# 🚀 Nuxt.js 项目部署指南

## 📋 快速开始

### 1. 安装依赖

```bash
# 安装 Node.js 依赖
npm install

# 或使用 pnpm（推荐）
pnpm install
```

### 2. 环境配置

创建环境变量文件：

```bash
# .env
VUE_APP_API_URL=https://api.chinadaji.com
VUE_APP_NATIONAL_TYPE=cn
VUE_APP_BASE_TYPE=new
VUE_APP_API_ENV=production
```

### 3. 开发模式

```bash
# 启动开发服务器
npm run dev

# 指定国家馆
npm run dev-uae  # 阿联酋
npm run dev-idn  # 印尼
```

### 4. 构建部署

```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview

# 生成静态站点（可选）
npm run generate
```

## 🐳 Docker 部署

### 1. 构建镜像

```bash
# 构建 Docker 镜像
docker build -f Dockerfile.nuxt -t trade-exhibition-nuxt .
```

### 2. 运行容器

```bash
# 单独运行
docker run -p 3000:3000 trade-exhibition-nuxt

# 使用 Docker Compose
docker-compose -f docker-compose.nuxt.yml up -d
```

### 3. 查看日志

```bash
# 查看应用日志
docker-compose -f docker-compose.nuxt.yml logs -f nuxt-app

# 查看所有服务日志
docker-compose -f docker-compose.nuxt.yml logs -f
```

## 🌐 生产环境配置

### 1. 环境变量

```bash
# .env.production
NODE_ENV=production
VUE_APP_API_URL=https://api.chinadaji.com
VUE_APP_NATIONAL_TYPE=cn
VUE_APP_BASE_TYPE=new
```

### 2. 性能优化

- 启用 gzip 压缩
- 配置 CDN
- 设置缓存策略
- 启用 HTTP/2

### 3. 监控配置

- 健康检查：`/api/health`
- 错误监控：集成 Sentry
- 性能监控：集成 APM

## 📊 SEO 优化验证

### 1. 检查 SEO 标签

```bash
# 检查页面 SEO
curl -H "User-Agent: Googlebot" http://localhost:3000/
```

### 2. 验证结构化数据

访问 Google 结构化数据测试工具验证

### 3. 检查 Sitemap

访问：`http://localhost:3000/sitemap.xml`

### 4. 检查 Robots.txt

访问：`http://localhost:3000/robots.txt`

## 🔧 故障排除

### 1. 常见问题

- **端口占用**：修改 `nuxt.config.ts` 中的端口配置
- **内存不足**：增加 Node.js 内存限制
- **构建失败**：检查依赖版本兼容性

### 2. 日志查看

```bash
# 查看应用日志
pm2 logs nuxt-app

# 查看 Docker 日志
docker logs trade-exhibition-nuxt
```

### 3. 性能调试

```bash
# 分析构建包大小
npm run analyze

# 性能测试
npx lighthouse http://localhost:3000
```

## 📚 相关文档

- [Nuxt.js 官方文档](https://nuxt.com)
- [部署指南](https://nuxt.com/docs/getting-started/deployment)
- [性能优化](https://nuxt.com/docs/guide/concepts/rendering)

## 🤝 技术支持

如有问题，请联系技术团队或查看项目文档。
