import { defineConfig, presetUno, presetIcons } from 'unocss'
import presetRemToPx from '@unocss/preset-rem-to-px'

export default defineConfig({
  presets: [
    presetUno(),
    presetIcons({
      scale: 1.2,
      warn: true,
      collections: {
        mdi: () => import('@iconify-json/mdi/icons.json').then(i => i.default)
      }
    }),
    presetRemToPx({
      baseFontSize: 4
    })
  ],
  shortcuts: {
    'btn': 'px-4 py-2 rounded-lg font-medium transition-all duration-200',
    'btn-primary': 'btn bg-blue-500 text-white hover:bg-blue-600',
    'btn-secondary': 'btn bg-gray-500 text-white hover:bg-gray-600',
    'btn-outline': 'btn border border-blue-500 text-blue-500 hover:bg-blue-500 hover:text-white',
    'container': 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'
  },
  theme: {
    colors: {
      primary: {
        50: '#eff6ff',
        500: '#667eea',
        600: '#5a67d8',
        700: '#4c51bf'
      }
    }
  }
})
