import { rumVitePlugin } from '@arms/rum-vite-plugin'
import { VantResolver } from '@vant/auto-import-resolver'
import legacy from '@vitejs/plugin-legacy'
import PluginVue from '@vitejs/plugin-vue'
import { fileURLToPath } from 'node:url'
import UnoCSS from 'unocss/vite'
import AutoImport from 'unplugin-auto-import/vite'
import { FileSystemIconLoader } from 'unplugin-icons/loaders'
import IconsResolver from 'unplugin-icons/resolver'
import Icons from 'unplugin-icons/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'
import { defineConfig } from 'vite'
import PluginVueDevTools from 'vite-plugin-vue-devtools'

const isProduction =
  process.env.npm_lifecycle_script.includes('vite build') &&
  (process.env.npm_lifecycle_script.includes('--mode=production') || !process.env.npm_lifecycle_script.includes('--mode='))

// html页面插入脚本
function _transformIndexHtml(html) {
  // 插入监控脚本
  const _env = isProduction ? 'prod' : 'daily'
  const scriptTag = `    <script>
      !(function(c,b,d,a){c[a]||(c[a]={});c[a]={
        "pid": "c67ee5ri5a@8b38ffec8cb1b03",
        "endpoint": "https://c67ee5ri5a-default-cn.rum.aliyuncs.com",
        "env": "${_env}",
      };
        with(b)with(body)with(insertBefore(createElement("script"),firstChild))setAttribute("crossorigin","",src=d)
      })(window, document, "https://c67ee5ri5a-sdk.rum.aliyuncs.com/v2/browser-sdk.js", "__rum");
    </script>`
  // 将脚本插入到 <body> 后的第一行位置
  html = html.replace(/<body[^>]*>/i, `$&\n${scriptTag}`)

  // 插入统计脚本
  if (isProduction) {
    let hmSrc = 'https://hm.baidu.com/hm.js?097a75cd4133c7a6989df2739f92ea97'

    // 阿联酋
    if (process.env.npm_lifecycle_event?.indexOf('uae') > -1) {
      hmSrc = 'https://hm.baidu.com/hm.js?a23e1b9b28fb2da199457a13fa88113d'
    }

    // 阿联酋
    if (process.env.npm_lifecycle_event?.indexOf('idn') > -1) {
      hmSrc = 'https://hm.baidu.com/hm.js?b771630e2981a5a985a4c7a9cb52eee0'
    }

    // 注意格式不需要缩进！！！
    const scriptTagAppendHead = `  <meta name="baidu-site-verification" content="codeva-ibDOkg3RWL" />
    <meta name="360-site-verification" content="156571fd25b1f293f573ab311f36ae9f" />
    <script>
        var _hmt = _hmt || [];
        (function() {
          var hm = document.createElement("script");
          hm.src = "${hmSrc}";
          var s = document.getElementsByTagName("script")[0];
          s.parentNode.insertBefore(hm, s);
        })();
    </script>
  </head>`
    html = html.replace('</head>', scriptTagAppendHead)
  }

  return html
}

// https://vitejs.dev/config/
export default defineConfig({
  base: '/',
  envPrefix: 'VUE_',
  define: {
    'import.meta.env.PLATFORM': JSON.stringify(process.env.PLATFORM),
  },
  server: {
    port: 5163,
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '@imgs/': fileURLToPath(new URL('./src/assets/imgs', import.meta.url)),
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        // 自动在所有 scss 文件开头引入公共 scss 文件
        additionalData: (resourceContent) => {
          return ["@import '@/common/scss/var.scss';", "@import '@/common/scss/mixin.scss';", resourceContent].join('\n')
        },
      },
    },
  },
  plugins: [
    UnoCSS(),
    PluginVue(),
    ...(process.env.PLATFORM === 'pc' ? [PluginVueDevTools()] : []),
    Icons({
      compiler: 'vue3',
      customCollections: {
        inside: FileSystemIconLoader('./src/assets/imgs/icon'),
      },
    }),
    AutoImport({
      include: [/src\/.*\.[tj]sx?$/, /src\/.*\.vue$/, /src\/.*\.vue\?vue/],
      imports: ['vue', 'vue-router'],
      dirs: ['src/hooks'], // 自动导入 hooks
      resolvers: [ElementPlusResolver(), VantResolver()],
      eslintrc: {
        enabled: true,
      },
    }),
    Components({
      resolvers: [
        ElementPlusResolver(),
        VantResolver(),
        IconsResolver({
          customCollections: ['inside'],
        }),
      ],
    }),
    {
      name: 'html-transform',
      transformIndexHtml(html) {
        return _transformIndexHtml(html)
      },
    },
    isProduction
      ? rumVitePlugin({
          pid: 'c67ee5ri5a@8b38ffec8cb1b03', // RUM 应用 ID
          accessKeyId: 'LTAI5t8hKhXJXepPMNhJmE2N',
          accessKeySecret: '******************************',
        })
      : null,
    legacy({
      targets: ['defaults', 'not IE 11'], // 根据需要调整目标浏览器
      modernPolyfills: true,
    }),
  ].filter(Boolean),
  build: {
    sourcemap: isProduction,
    target: 'es2021',
  },
})
