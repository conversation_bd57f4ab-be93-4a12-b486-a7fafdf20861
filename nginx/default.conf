server {
    listen       80;
    server_name  _;
    root   /usr/share/nginx/html/dist;
    
    gzip on;    #开启gzip压缩功能
    gzip_min_length 10k;    #设置允许压缩的页面最小字节数; 这里表示如果文件小于10个字节，就不用压缩，因为没有意义，本来就很小. 
    gzip_buffers 4 16k;    #设置压缩缓冲区大小，此处设置为4个16K内存作为压缩结果流缓存
    gzip_http_version 1.1;    #压缩版本
    gzip_comp_level 6;    #设置压缩比率，最小为1，处理速度快，传输速度慢；9为最大压缩比，处理速度慢，传输速度快; 这里表示压缩级别，可以是0到9中的任一个，级别越高，压缩就越小，节省了带宽资源，但同时也消耗CPU资源，所以一般折中为6
    gzip_types application/javascript application/json application/xml font/eot font/otf font/ttf image/svg+xml text/css text/javascript text/plain text/xml;    #制定压缩的类型,线上配置时尽可能配置多的压缩类型!
    gzip_disable "MSIE [1-6]\.";    #配置禁用gzip条件，支持正则。此处表示ie6及以下不启用gzip（因为ie低版本不支持）
    gzip_vary on;    #选择支持vary header；改选项可以让前端的缓存服务器缓存经过gzip压缩的页面; 这个可以不写，表示在传送数据时，给客户端说明我使用了gzip压缩

    location / {
        index index.html;
        try_files $uri $uri/ @router;
        proxy_set_header x-envoy-external-address $remote_addr;
        break;
    }

    # 对其他路径进行蜘蛛检测
    location ~ ^/(mall/goods-detail/|pages-sub/product-details/index) {
        index index.html;
        proxy_set_header X-Original-URL $scheme://$host$request_uri;
        proxy_set_header Host $host;
        try_files $uri $uri/ @router;
        proxy_set_header x-envoy-external-address $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_pass_request_headers on;
        # 当检测到是蜘蛛且不在排除路径时，返回403
        if ($is_spider = 1) {
        rewrite ^/(.*)$ /api/common-web/seo/crawler?host=$host&uri=$request_uri&args=$args break;

        # 代理到后端服务
        proxy_pass http://mall-common-web-svc:8080;
        }
    }
    location @router {
        rewrite ^.*$ /index.html last;
    }

    location ~* \.(html)?$ {
        # 不缓存 html 文件
        add_header Cache-Control no-cache;
    }

    location ~ .*\.(js|css|gif|jpg|jpeg|png|bmp|swf|woff|ttf)$ {
        # 设置缓存时间为两天(172800秒)
        add_header Cache-Control max-age=172800;
    }
}
