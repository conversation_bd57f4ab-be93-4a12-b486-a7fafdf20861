server {
    listen       80;
    server_name  _;
    root   /usr/share/nginx/html/dist;

    location / {
        index index.html;
        try_files $uri $uri/ @router;
    }

    location @router {
        rewrite ^/mobile/*  /mobile.html  last;
        rewrite pay\.html*  /pay.html  last;
        rewrite erp-ai-success\.html*  /erp-ai-success.html  last;
        rewrite erp-download\.html*  /erp-download.html  last;
        try_files $uri $uri/ /index.html;
    }

    location ~* \.(html)?$ {
        # 不缓存 html 文件 
        add_header Cache-Control no-cache;
    }

    location ~* \.(txt)$ {
        # txt 文件添加编码，避免预览的时候乱码
        add_header  Content-Type 'text/plain; charset=UTF-8';
    }

    location ~ .*\.(js|css)$ {
        # 设置不缓存
        add_header Cache-Control no-cache;
    }
}
