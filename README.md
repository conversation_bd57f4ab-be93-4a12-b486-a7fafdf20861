# 商城数科官网

## 启动项目

- 执行 `npm install` 安装依赖
- 执行 `npm run dev-pc` 启动pc项目
- 执行 `npm run dev-mb` 启动H5项目

## 打包（暂时没有区分生产还是测试 只有一个环境）

- `npm run build-pc`: pc端生产打包
- `npm run build-mb`: H5端生产打包

## 第三方库

- fabric: 抠图合成插件，可拖拽放大缩小
- bignumber.js: 数值计算

## 接口文档

- [yapi](https://yapi.comliq.net/project/87/interface/api)

## 设计稿

- [mastergo](https://mastergo.com/file/135116723237986?fileOpenFrom=project&page_id=5%3A1478)

## 需求文档

- [雨雀《临沂商城官网》 密码：dwgf](https://sdpjw-pm.yuque.com/kfeszs/dqsp90/riror1pdkzuevmss?singleDoc#)

## 线上访问地址

- 测试环境：

  - PC端：https://trade-exhibition.sdpjw.cn

- 线上环境：
  - PC端：https://www.chinamarket.ltd

## 开发规范

- js: 项目使用 eslint 来规范 js 代码，建议配合 vscode 插件 [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint) 来自动格式化，具体配置可参考 [《ESLint 规范》](https://liquidity.feishu.cn/docs/doccn3oJwYyupVD20YBZTdSxyvc)
- css: 项目使用 stylelint 来规范 css 代码，建议配合 vscode 插件 [Stylelint](https://marketplace.visualstudio.com/items?itemName=stylelint.vscode-stylelint) 来自动格式化，具体配置可参考 [《stylelint 配置使用》](https://liquidity.feishu.cn/docs/doccnowyTh5hVbTNSBaF9v8hpWf)

### 其他：

- 文件和文件夹命名统一使用小写+横杠
- `.vue` 入口文件名称与文件夹名称保持一致，比如创建一个 `icon` 组件，其路径为 `src\views\components\icon\icon.vue`
- 项目公共组件存放在 `src\components` 目录下，业务组件存放在 `src\views\components` 目录下，页面文件存放在 `src\views\pages` 目录下，页面的路径和文件名尽量与路由的路径保持一致
- 页面中需要抽取业务组件时，写在页面对应文件夹中的 `components` 目录下，如 `src\views\pages\business-manage\acceptor-label\components\common-dialog.vue`
- 公共 js 和 css 等存放在 `src\common` 目录下，尽量不引入其他的第三方库，如需引入先征求项目负责人的意见
- 组件库使用 element-plus，目前迭代比较频繁，更新经常伴随着 api 和样式变化，目前已锁定版本号，请谨慎更新
- 备注或类似功能（一个弹窗，弹窗中包含一个 textarea，底部是确定和取消按钮），使用 `src\views\components\remark` 组件，避免浪费时间写重复的逻辑和样式，还有可能导致样式和交互不一致的问题
- 权限控制，页面权限已通过 vue-router 的路由守卫功能统一处理，按钮权限需使用 `src\components\c-permission\c-permission.vue` 组件
- 新增页面可参考 [《深度后台项目新增页面》](https://liquidity.feishu.cn/docs/doccnFnBySUSBa2s4LpbSxDxtWe)

## 分支管理

## 问题日志

### 升级 element-plus

element-plus 从 2.2.0 升级到 2.3.8, 兼容版本修改点如下：

- el-dialog 属性 custom-class 废弃，改成 class。
- el-upload 动态更改 action 导致异步参数接收不到，修改了 upload-file-dialog.vue 组件实现方案。
- el-cascader 组件不支持[cascaderRef].value.inputValue 获取 label 值（使用出处：菜单管理），修改校验项。
- el-date-picker 组件中 datetimerange 类型日期的标签上与 el-range-editor css 类同级的 el-input**inner 类改成 el-input**wrapper。
