interface SeoOptions {
  title?: string
  description?: string
  keywords?: string
  image?: string
  url?: string
  type?: string
  siteName?: string
  locale?: string
  author?: string
  publishedTime?: string
  modifiedTime?: string
  section?: string
  tags?: string[]
}

/**
 * SEO 优化 composable
 * 提供统一的 SEO 配置管理
 */
export const useSeo = (options: SeoOptions = {}) => {
  const { locale } = useI18n()
  const route = useRoute()
  const config = useRuntimeConfig()
  
  // 默认 SEO 配置
  const defaultSeo = {
    siteName: '中国大集',
    type: 'website',
    locale: locale.value,
    author: '临沂商城',
    image: '/logo.png'
  }
  
  // 合并配置
  const seoConfig = { ...defaultSeo, ...options }
  
  // 构建完整的 URL
  const fullUrl = seoConfig.url || `https://www.chinadaji.com${route.path}`
  
  // 设置页面标题
  const pageTitle = seoConfig.title 
    ? `${seoConfig.title} - ${seoConfig.siteName}`
    : seoConfig.siteName
  
  // 设置 SEO meta 标签
  useSeoMeta({
    title: pageTitle,
    description: seoConfig.description,
    keywords: seoConfig.keywords,
    author: seoConfig.author,
    
    // Open Graph
    ogTitle: seoConfig.title || seoConfig.siteName,
    ogDescription: seoConfig.description,
    ogImage: seoConfig.image,
    ogUrl: fullUrl,
    ogType: seoConfig.type,
    ogSiteName: seoConfig.siteName,
    ogLocale: seoConfig.locale,
    
    // Twitter Card
    twitterCard: 'summary_large_image',
    twitterTitle: seoConfig.title || seoConfig.siteName,
    twitterDescription: seoConfig.description,
    twitterImage: seoConfig.image,
    
    // 文章相关 meta（如果是文章页面）
    ...(seoConfig.publishedTime && {
      articlePublishedTime: seoConfig.publishedTime,
      articleModifiedTime: seoConfig.modifiedTime,
      articleSection: seoConfig.section,
      articleTag: seoConfig.tags
    })
  })
  
  // 设置 canonical URL
  useHead({
    link: [
      {
        rel: 'canonical',
        href: fullUrl
      }
    ]
  })
  
  return {
    updateSeo: (newOptions: SeoOptions) => {
      const updatedConfig = { ...seoConfig, ...newOptions }
      useSeo(updatedConfig)
    }
  }
}

/**
 * 产品页面 SEO
 */
export const useProductSeo = (product: any) => {
  const { locale } = useI18n()
  
  return useSeo({
    title: product.name,
    description: product.description || `${product.name} - 优质商品，批发零售，欢迎选购`,
    keywords: `${product.name},${product.category},批发,零售,商品`,
    image: product.image || product.images?.[0],
    type: 'product',
    publishedTime: product.createdAt,
    modifiedTime: product.updatedAt
  })
}

/**
 * 商家页面 SEO
 */
export const useMerchantSeo = (merchant: any) => {
  return useSeo({
    title: merchant.name,
    description: merchant.description || `${merchant.name} - 专业商家，品质保证`,
    keywords: `${merchant.name},商家,供应商,批发商`,
    image: merchant.logo,
    type: 'profile'
  })
}

/**
 * 文章页面 SEO
 */
export const useArticleSeo = (article: any) => {
  return useSeo({
    title: article.title,
    description: article.summary || article.content?.substring(0, 160),
    keywords: article.tags?.join(','),
    image: article.coverImage,
    type: 'article',
    publishedTime: article.publishedAt,
    modifiedTime: article.updatedAt,
    section: article.category,
    tags: article.tags,
    author: article.author?.name
  })
}

/**
 * 分类页面 SEO
 */
export const useCategorySeo = (category: any) => {
  return useSeo({
    title: category.name,
    description: `${category.name}分类商品 - 海量优质商品，批发零售，一站式采购`,
    keywords: `${category.name},分类,商品,批发,采购`,
    image: category.image
  })
}

/**
 * 结构化数据 composable
 */
export const useStructuredData = () => {
  // 组织信息结构化数据
  const useOrganizationSchema = () => {
    useJsonld({
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: '中国大集',
      url: 'https://www.chinadaji.com',
      logo: 'https://www.chinadaji.com/logo.png',
      description: '中国大集是临沂商城官方B2B数字商贸综合服务平台',
      address: {
        '@type': 'PostalAddress',
        addressCountry: 'CN',
        addressRegion: '山东省',
        addressLocality: '临沂市'
      },
      contactPoint: {
        '@type': 'ContactPoint',
        telephone: '+86-400-xxx-xxxx',
        contactType: 'customer service'
      },
      sameAs: [
        'https://weibo.com/chinadaji',
        'https://www.facebook.com/chinadaji'
      ]
    })
  }
  
  // 产品结构化数据
  const useProductSchema = (product: any) => {
    useJsonld({
      '@context': 'https://schema.org',
      '@type': 'Product',
      name: product.name,
      description: product.description,
      image: product.images,
      brand: {
        '@type': 'Brand',
        name: product.brand
      },
      offers: {
        '@type': 'Offer',
        price: product.price,
        priceCurrency: 'CNY',
        availability: product.inStock ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock',
        seller: {
          '@type': 'Organization',
          name: product.seller?.name
        }
      }
    })
  }
  
  // 面包屑导航结构化数据
  const useBreadcrumbSchema = (breadcrumbs: Array<{name: string, url: string}>) => {
    useJsonld({
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement: breadcrumbs.map((item, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        name: item.name,
        item: item.url
      }))
    })
  }
  
  return {
    useOrganizationSchema,
    useProductSchema,
    useBreadcrumbSchema
  }
}
