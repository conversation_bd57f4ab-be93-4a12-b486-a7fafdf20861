import type { UseFetchOptions } from 'nuxt/app'

// API 基础配置
export const useApi = <T>(
  url: string,
  options: UseFetchOptions<T> = {}
) => {
  const config = useRuntimeConfig()
  
  // 默认配置
  const defaults: UseFetchOptions<T> = {
    baseURL: config.public.apiUrl,
    key: url,
    
    // 请求拦截器
    onRequest({ request, options }) {
      // 添加认证 token
      const token = useCookie('access_token')
      if (token.value) {
        options.headers = {
          ...options.headers,
          'Authorization': `Bearer ${token.value}`,
          'accessToken': token.value
        }
      }
      
      // 添加通用请求头
      options.headers = {
        ...options.headers,
        'Content-Type': 'application/json;charset=utf-8',
        'Channel': config.public.baseType === 'new' ? 'new_platform' : 'hn'
      }
    },
    
    // 响应拦截器
    onResponse({ response }) {
      // 处理响应数据
      if (response._data?.code === 200) {
        return response._data.data
      }
    },
    
    // 错误处理
    onResponseError({ response }) {
      const { status, _data } = response
      
      // 处理认证错误
      if ([401, 1005, 1006].includes(_data?.code || status)) {
        // 清除 token 并跳转到登录页
        const token = useCookie('access_token')
        token.value = null
        
        // 触发登录事件
        const { $event } = useNuxtApp()
        $event?.emit('TOKEN_EXPIRED')
        
        return navigateTo('/login')
      }
      
      // 处理权限错误
      if ([403].includes(_data?.code || status)) {
        const { $event } = useNuxtApp()
        $event?.emit('WITHOUT_PERMISSION')
      }
      
      // 显示错误信息
      if (process.client && _data?.msg) {
        ElMessage.error(_data.msg)
      }
    }
  }
  
  // 合并配置
  const params = defu(options, defaults)
  
  return $fetch(url, params)
}

// GET 请求
export const useGet = <T>(url: string, options: UseFetchOptions<T> = {}) => {
  return useApi<T>(url, { ...options, method: 'GET' })
}

// POST 请求
export const usePost = <T>(url: string, body?: any, options: UseFetchOptions<T> = {}) => {
  return useApi<T>(url, { ...options, method: 'POST', body })
}

// PUT 请求
export const usePut = <T>(url: string, body?: any, options: UseFetchOptions<T> = {}) => {
  return useApi<T>(url, { ...options, method: 'PUT', body })
}

// DELETE 请求
export const useDelete = <T>(url: string, options: UseFetchOptions<T> = {}) => {
  return useApi<T>(url, { ...options, method: 'DELETE' })
}

// 文件上传
export const useUpload = (url: string, file: File, options: UseFetchOptions<any> = {}) => {
  const formData = new FormData()
  formData.append('file', file)
  
  return useApi(url, {
    ...options,
    method: 'POST',
    body: formData,
    headers: {
      // 不设置 Content-Type，让浏览器自动设置
    }
  })
}

// 取消请求的 composable
export const useCancelableRequest = () => {
  const abortController = ref<AbortController | null>(null)
  
  const execute = async <T>(
    url: string,
    options: UseFetchOptions<T> = {}
  ) => {
    // 取消之前的请求
    if (abortController.value) {
      abortController.value.abort()
    }
    
    // 创建新的 AbortController
    abortController.value = new AbortController()
    
    return useApi<T>(url, {
      ...options,
      signal: abortController.value.signal
    })
  }
  
  const cancel = () => {
    if (abortController.value) {
      abortController.value.abort()
      abortController.value = null
    }
  }
  
  // 组件卸载时自动取消请求
  onUnmounted(() => {
    cancel()
  })
  
  return {
    execute,
    cancel
  }
}
