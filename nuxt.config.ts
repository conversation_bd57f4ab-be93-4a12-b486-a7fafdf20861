// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  devtools: { enabled: true },

  // 应用配置
  app: {
    head: {
      title: '中国大集-临沂商城一站式全域B2B数字商贸综合服务平台',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'description', content: '中国大集是临沂商城官方B2B数字商贸综合服务平台，提供一站式全域贸易服务，连接全球买家与卖家，助力中小企业数字化转型。' },
        { name: 'keywords', content: '中国大集,临沂商城,B2B,数字商贸,全域贸易,跨境电商,批发市场,供应链' },
        { name: 'author', content: '临沂商城' },
        { property: 'og:title', content: '中国大集-临沂商城一站式全域B2B数字商贸综合服务平台' },
        {
          property: 'og:description',
          content: '中国大集是临沂商城官方B2B数字商贸综合服务平台，提供一站式全域贸易服务，连接全球买家与卖家，助力中小企业数字化转型。',
        },
        { property: 'og:type', content: 'website' },
        { property: 'og:image', content: '/logo.png' },
        { name: 'twitter:card', content: 'summary_large_image' },
        { name: 'twitter:title', content: '中国大集-临沂商城一站式全域B2B数字商贸综合服务平台' },
        {
          name: 'twitter:description',
          content: '中国大集是临沂商城官方B2B数字商贸综合服务平台，提供一站式全域贸易服务，连接全球买家与卖家，助力中小企业数字化转型。',
        },
        { name: 'baidu-site-verification', content: 'codeva-ibDOkg3RWL' },
        { name: '360-site-verification', content: '156571fd25b1f293f573ab311f36ae9f' },
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
        { rel: 'canonical', href: 'https://www.chinadaji.com' },
      ],
      script: [
        // 百度统计脚本将在运行时动态添加
      ],
    },
  },

  // 运行时配置
  runtimeConfig: {
    // 私有配置（仅在服务端可用）
    apiSecret: '',

    // 公共配置（客户端和服务端都可用）
    public: {
      apiUrl: process.env.VUE_APP_API_URL || '',
      nationalType: process.env.VUE_APP_NATIONAL_TYPE || 'cn',
      baseType: process.env.VUE_APP_BASE_TYPE || 'old',
      apiEnv: process.env.VUE_APP_API_ENV || 'production',
    },
  },

  // CSS 配置
  css: ['vant/lib/index.css', 'normalize.css', '@/assets/scss/common.scss', '@/assets/scss/element-reset.scss', '@/assets/scss/reset.scss'],

  // 构建配置
  build: {
    transpile: ['element-plus'],
  },

  // Vite 配置
  vite: {
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `
            @import "@/assets/scss/var.scss";
            @import "@/assets/scss/mixin.scss";
          `,
        },
      },
    },
    define: {
      'import.meta.env.PLATFORM': JSON.stringify('pc'),
    },
  },

  // 模块配置
  modules: ['@nuxtjs/i18n', '@pinia/nuxt', '@unocss/nuxt', '@element-plus/nuxt', '@vant/nuxt'],

  // 国际化配置
  i18n: {
    locales: [
      { code: 'zh', name: '中文', file: 'zh.json' },
      { code: 'en', name: 'English', file: 'en.json' },
      { code: 'ar', name: 'العربية', file: 'ar.json' },
      { code: 'id', name: 'Bahasa Indonesia', file: 'id.json' },
    ],
    lazy: true,
    langDir: 'locales/',
    defaultLocale: 'zh',
    strategy: 'prefix_except_default',
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'i18n_redirected',
      redirectOn: 'root',
    },
  },

  // Element Plus 配置
  elementPlus: {
    icon: 'ElIcon',
    importStyle: 'scss',
  },

  // 服务端渲染配置
  ssr: true,

  // 预渲染配置（用于静态生成）
  nitro: {
    prerender: {
      routes: ['/', '/home', '/mall', '/market', '/merchants'],
    },
  },

  // 路由配置
  router: {
    options: {
      scrollBehaviorType: 'smooth',
    },
  },

  // 实验性功能
  experimental: {
    payloadExtraction: false,
  },

  // 兼容性配置
  compatibilityDate: '2024-04-03',
})
