version: '3.8'

services:
  # Nuxt.js 应用
  nuxt-app:
    build:
      context: .
      dockerfile: Dockerfile.nuxt
      args:
        NODE_ENV: production
    container_name: trade-exhibition-nuxt
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - VUE_APP_API_URL=${VUE_APP_API_URL:-https://api.chinadaji.com}
      - VUE_APP_NATIONAL_TYPE=${VUE_APP_NATIONAL_TYPE:-cn}
      - VUE_APP_BASE_TYPE=${VUE_APP_BASE_TYPE:-new}
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - app-network
    depends_on:
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: trade-exhibition-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    restart: unless-stopped
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: trade-exhibition-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    restart: unless-stopped
    networks:
      - app-network
    depends_on:
      - nuxt-app
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  app-network:
    driver: bridge

volumes:
  redis_data:
    driver: local
