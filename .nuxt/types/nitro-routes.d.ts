// Generated by nitro
import type { Serialize, Simplify } from "nitropack/types";
declare module "nitropack/types" {
  type Awaited<T> = T extends PromiseLike<infer U> ? Awaited<U> : T
  interface InternalApi {
    '/api/health': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/health').default>>>>
    }
    '/api/robots.txt': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/robots.txt').default>>>>
    }
    '/api/sitemap.xml': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/sitemap.xml').default>>>>
    }
    '/__nuxt_error': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../node_modules/.pnpm/nuxt@3.17.5_@parcel+watcher@2.5.1_@types+node@20.19.0_db0@0.3.2_eslint@8.57.1_ioredis@5_e75f833fce02ea9f7086e7fc82927ef6/node_modules/nuxt/dist/core/runtime/nitro/handlers/renderer').default>>>>
    }
    '/__nuxt_island/**': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/#internal/nuxt/island-renderer').default>>>>
    }
  }
}
export {}