
// @ts-nocheck


export const localeCodes =  [
  "zh",
  "en",
  "ar",
  "id"
]

export const localeLoaders = {
  "zh": [{ key: "../locales/zh.json", load: () => import("../locales/zh.json" /* webpackChunkName: "locale__Users_SDT_code_daji_trade_exhibition_locales_zh_json" */), cache: true }],
  "en": [{ key: "../locales/en.json", load: () => import("../locales/en.json" /* webpackChunkName: "locale__Users_SDT_code_daji_trade_exhibition_locales_en_json" */), cache: true }],
  "ar": [{ key: "../locales/ar.json", load: () => import("../locales/ar.json" /* webpackChunkName: "locale__Users_SDT_code_daji_trade_exhibition_locales_ar_json" */), cache: true }],
  "id": [{ key: "../locales/id.json", load: () => import("../locales/id.json" /* webpackChunkName: "locale__Users_SDT_code_daji_trade_exhibition_locales_id_json" */), cache: true }]
}

export const vueI18nConfigs = [
  
]

export const nuxtI18nOptions = {
  "experimental": {
    "localeDetector": "",
    "switchLocalePathLinkSSR": false,
    "autoImportTranslationFunctions": false
  },
  "bundle": {
    "compositionOnly": true,
    "runtimeOnly": false,
    "fullInstall": true,
    "dropMessageCompiler": false
  },
  "compilation": {
    "jit": true,
    "strictMessage": true,
    "escapeHtml": false
  },
  "customBlocks": {
    "defaultSFCLang": "json",
    "globalSFCScope": false
  },
  "vueI18n": "",
  "locales": [
    {
      "code": "zh",
      "name": "中文",
      "files": [
        "/Users/<USER>/code/daji/trade-exhibition/locales/zh.json"
      ]
    },
    {
      "code": "en",
      "name": "English",
      "files": [
        "/Users/<USER>/code/daji/trade-exhibition/locales/en.json"
      ]
    },
    {
      "code": "ar",
      "name": "العربية",
      "files": [
        "/Users/<USER>/code/daji/trade-exhibition/locales/ar.json"
      ]
    },
    {
      "code": "id",
      "name": "Bahasa Indonesia",
      "files": [
        "/Users/<USER>/code/daji/trade-exhibition/locales/id.json"
      ]
    }
  ],
  "defaultLocale": "zh",
  "defaultDirection": "ltr",
  "routesNameSeparator": "___",
  "trailingSlash": false,
  "defaultLocaleRouteNameSuffix": "default",
  "strategy": "prefix_except_default",
  "lazy": true,
  "langDir": "locales/",
  "detectBrowserLanguage": {
    "alwaysRedirect": false,
    "cookieCrossOrigin": false,
    "cookieDomain": null,
    "cookieKey": "i18n_redirected",
    "cookieSecure": false,
    "fallbackLocale": "",
    "redirectOn": "root",
    "useCookie": true
  },
  "differentDomains": false,
  "baseUrl": "",
  "dynamicRouteParams": false,
  "customRoutes": "page",
  "pages": {},
  "skipSettingLocaleOnNavigate": false,
  "types": "composition",
  "debug": false,
  "parallelPlugin": false,
  "multiDomainLocales": false,
  "i18nModules": []
}

export const normalizedLocales = [
  {
    "code": "zh",
    "name": "中文",
    "files": [
      {
        "path": "/Users/<USER>/code/daji/trade-exhibition/locales/zh.json"
      }
    ]
  },
  {
    "code": "en",
    "name": "English",
    "files": [
      {
        "path": "/Users/<USER>/code/daji/trade-exhibition/locales/en.json"
      }
    ]
  },
  {
    "code": "ar",
    "name": "العربية",
    "files": [
      {
        "path": "/Users/<USER>/code/daji/trade-exhibition/locales/ar.json"
      }
    ]
  },
  {
    "code": "id",
    "name": "Bahasa Indonesia",
    "files": [
      {
        "path": "/Users/<USER>/code/daji/trade-exhibition/locales/id.json"
      }
    ]
  }
]

export const NUXT_I18N_MODULE_ID = "@nuxtjs/i18n"
export const parallelPlugin = false
export const isSSG = false

export const DEFAULT_DYNAMIC_PARAMS_KEY = "nuxtI18n"
export const DEFAULT_COOKIE_KEY = "i18n_redirected"
export const SWITCH_LOCALE_PATH_LINK_IDENTIFIER = "nuxt-i18n-slp"
