{"compilerOptions": {"paths": {"nitropack/types": ["../node_modules/.pnpm/nitropack@2.11.12_webpack-sources@3.3.2_xml2js@0.6.2/node_modules/nitropack/types"], "nitropack/runtime": ["../node_modules/.pnpm/nitropack@2.11.12_webpack-sources@3.3.2_xml2js@0.6.2/node_modules/nitropack/runtime"], "nitropack": ["../node_modules/.pnpm/nitropack@2.11.12_webpack-sources@3.3.2_xml2js@0.6.2/node_modules/nitropack"], "defu": ["../node_modules/defu"], "h3": ["../node_modules/.pnpm/h3@1.15.3/node_modules/h3"], "consola": ["../node_modules/consola"], "ofetch": ["../node_modules/ofetch"], "@unhead/vue": ["../node_modules/.pnpm/@unhead+vue@2.0.10_vue@3.5.16_typescript@5.8.3_/node_modules/@unhead/vue"], "@vue/runtime-core": ["../node_modules/@vue/runtime-core"], "@vue/compiler-sfc": ["../node_modules/@vue/compiler-sfc"], "unplugin-vue-router/client": ["../node_modules/.pnpm/unplugin-vue-router@0.12.0_vue-router@4.5.1_vue@3.5.16_typescript@5.8.3___vue@3.5.16_typescript@5.8.3_/node_modules/unplugin-vue-router/client"], "@nuxt/schema": ["../node_modules/.pnpm/@nuxt+schema@3.17.5/node_modules/@nuxt/schema"], "nuxt": ["../node_modules/.pnpm/nuxt@3.17.5_@parcel+watcher@2.5.1_@types+node@20.19.0_db0@0.3.2_eslint@8.57.1_ioredis@5_e75f833fce02ea9f7086e7fc82927ef6/node_modules/nuxt"], "vite/client": ["../node_modules/.pnpm/vite@6.3.5_@types+node@20.19.0_jiti@2.4.2_sass@1.79.4_terser@5.39.2_tsx@4.19.1_yaml@2.8.0/node_modules/vite/client"], "~": [".."], "~/*": ["../*"], "@": [".."], "@/*": ["../*"], "~~": [".."], "~~/*": ["../*"], "@@": [".."], "@@/*": ["../*"], "#shared": ["../shared"], "assets": ["../assets"], "public": ["../public"], "public/*": ["../public/*"], "#app": ["../node_modules/.pnpm/nuxt@3.17.5_@parcel+watcher@2.5.1_@types+node@20.19.0_db0@0.3.2_eslint@8.57.1_ioredis@5_e75f833fce02ea9f7086e7fc82927ef6/node_modules/nuxt/dist/app"], "#app/*": ["../node_modules/.pnpm/nuxt@3.17.5_@parcel+watcher@2.5.1_@types+node@20.19.0_db0@0.3.2_eslint@8.57.1_ioredis@5_e75f833fce02ea9f7086e7fc82927ef6/node_modules/nuxt/dist/app/*"], "vue-demi": ["../node_modules/.pnpm/nuxt@3.17.5_@parcel+watcher@2.5.1_@types+node@20.19.0_db0@0.3.2_eslint@8.57.1_ioredis@5_e75f833fce02ea9f7086e7fc82927ef6/node_modules/nuxt/dist/app/compat/vue-demi"], "vue-i18n": ["../node_modules/.pnpm/vue-i18n@9.14.1_vue@3.5.11_typescript@5.8.3_/node_modules/vue-i18n/dist/vue-i18n"], "@intlify/shared": ["../node_modules/@intlify/shared/dist/shared"], "@intlify/message-compiler": ["../node_modules/@intlify/message-compiler/dist/message-compiler"], "@intlify/core-base": ["../node_modules/@intlify/core-base/dist/core-base"], "@intlify/core": ["../node_modules/.pnpm/@intlify+core@9.14.4/node_modules/@intlify/core/dist/core.node"], "@intlify/utils/h3": ["../node_modules/.pnpm/@intlify+utils@0.12.0/node_modules/@intlify/utils/dist/h3"], "ufo": ["../node_modules/ufo/dist/index"], "is-https": ["../node_modules/.pnpm/is-https@4.0.0/node_modules/is-https/dist/index"], "#i18n": ["../node_modules/.pnpm/@nuxtjs+i18n@8.5.6_magicast@0.3.5_rollup@4.43.0_vue@3.5.11_typescript@5.8.3__webpack-sources@3.3.2/node_modules/@nuxtjs/i18n/dist/runtime/composables/index"], "pinia": ["../node_modules/.pnpm/pinia@2.2.4_typescript@5.8.3_vue@3.5.11_typescript@5.8.3_/node_modules/pinia/dist/pinia"], "#vue-router": ["../node_modules/.pnpm/vue-router@4.4.5_vue@3.5.11_typescript@5.8.3_/node_modules/vue-router"], "#unhead/composables": ["../node_modules/.pnpm/nuxt@3.17.5_@parcel+watcher@2.5.1_@types+node@20.19.0_db0@0.3.2_eslint@8.57.1_ioredis@5_e75f833fce02ea9f7086e7fc82927ef6/node_modules/nuxt/dist/head/runtime/composables/v3"], "#imports": ["./imports"], "#app-manifest": ["./manifest/meta/cc2513d9-d410-4857-ba1f-d5706d3edf61"], "#components": ["./components"], "#build": ["."], "#build/*": ["./*"]}, "esModuleInterop": true, "skipLibCheck": true, "target": "ESNext", "allowJs": true, "resolveJsonModule": true, "moduleDetection": "force", "isolatedModules": true, "verbatimModuleSyntax": true, "strict": true, "noUncheckedIndexedAccess": false, "forceConsistentCasingInFileNames": true, "noImplicitOverride": true, "module": "preserve", "noEmit": true, "lib": ["ESNext", "dom", "dom.iterable", "webworker"], "jsx": "preserve", "jsxImportSource": "vue", "types": [], "moduleResolution": "<PERSON><PERSON><PERSON>", "useDefineForClassFields": true, "noImplicitThis": true, "allowSyntheticDefaultImports": true}, "include": ["../**/*", "../.config/nuxt.*", "./nuxt.d.ts", "../node_modules/.pnpm/@nuxtjs+i18n@8.5.6_magicast@0.3.5_rollup@4.43.0_vue@3.5.11_typescript@5.8.3__webpack-sources@3.3.2/node_modules/@nuxtjs/i18n/runtime", "../node_modules/.pnpm/@nuxtjs+i18n@8.5.6_magicast@0.3.5_rollup@4.43.0_vue@3.5.11_typescript@5.8.3__webpack-sources@3.3.2/node_modules/@nuxtjs/i18n/dist/runtime", "../node_modules/.pnpm/@pinia+nuxt@0.5.5_magicast@0.3.5_typescript@5.8.3_vue@3.5.11_typescript@5.8.3_/node_modules/@pinia/nuxt/runtime", "../node_modules/.pnpm/@pinia+nuxt@0.5.5_magicast@0.3.5_typescript@5.8.3_vue@3.5.11_typescript@5.8.3_/node_modules/@pinia/nuxt/dist/runtime", "../node_modules/.pnpm/@unocss+nuxt@0.62.4_magicast@0.3.5_postcss@8.5.4_rollup@4.43.0_vite@6.3.5_@types+node@2_26cdc847660293b9b0a92d2472455b96/node_modules/@unocss/nuxt/runtime", "../node_modules/.pnpm/@unocss+nuxt@0.62.4_magicast@0.3.5_postcss@8.5.4_rollup@4.43.0_vite@6.3.5_@types+node@2_26cdc847660293b9b0a92d2472455b96/node_modules/@unocss/nuxt/dist/runtime", "../node_modules/.pnpm/@element-plus+nuxt@1.1.3_@element-plus+icons-vue@2.3.1_vue@3.5.11_typescript@5.8.3___el_3d7b7c7b7a045387d0b76c7c17c984a1/node_modules/@element-plus/nuxt/runtime", "../node_modules/.pnpm/@element-plus+nuxt@1.1.3_@element-plus+icons-vue@2.3.1_vue@3.5.11_typescript@5.8.3___el_3d7b7c7b7a045387d0b76c7c17c984a1/node_modules/@element-plus/nuxt/dist/runtime", "../node_modules/.pnpm/@vant+nuxt@1.0.7_magicast@0.3.5_vant@4.9.7_vue@3.5.11_typescript@5.8.3__/node_modules/@vant/nuxt/runtime", "../node_modules/.pnpm/@vant+nuxt@1.0.7_magicast@0.3.5_vant@4.9.7_vue@3.5.11_typescript@5.8.3__/node_modules/@vant/nuxt/dist/runtime", "../node_modules/.pnpm/@nuxt+devtools@2.5.0_vite@6.3.5_@types+node@20.19.0_jiti@2.4.2_sass@1.79.4_terser@5.39._edca095eb3aa76d50a7abee142d3386e/node_modules/@nuxt/devtools/runtime", "../node_modules/.pnpm/@nuxt+devtools@2.5.0_vite@6.3.5_@types+node@20.19.0_jiti@2.4.2_sass@1.79.4_terser@5.39._edca095eb3aa76d50a7abee142d3386e/node_modules/@nuxt/devtools/dist/runtime", "../node_modules/.pnpm/@nuxt+telemetry@2.6.6_magicast@0.3.5/node_modules/@nuxt/telemetry/runtime", "../node_modules/.pnpm/@nuxt+telemetry@2.6.6_magicast@0.3.5/node_modules/@nuxt/telemetry/dist/runtime", ".."], "exclude": ["../dist", "../.data", "../node_modules", "../node_modules/.pnpm/nuxt@3.17.5_@parcel+watcher@2.5.1_@types+node@20.19.0_db0@0.3.2_eslint@8.57.1_ioredis@5_e75f833fce02ea9f7086e7fc82927ef6/node_modules/nuxt/node_modules", "../node_modules/.pnpm/@nuxtjs+i18n@8.5.6_magicast@0.3.5_rollup@4.43.0_vue@3.5.11_typescript@5.8.3__webpack-sources@3.3.2/node_modules/@nuxtjs/i18n/node_modules", "../node_modules/.pnpm/@pinia+nuxt@0.5.5_magicast@0.3.5_typescript@5.8.3_vue@3.5.11_typescript@5.8.3_/node_modules/@pinia/nuxt/node_modules", "../node_modules/.pnpm/@unocss+nuxt@0.62.4_magicast@0.3.5_postcss@8.5.4_rollup@4.43.0_vite@6.3.5_@types+node@2_26cdc847660293b9b0a92d2472455b96/node_modules/@unocss/nuxt/node_modules", "../node_modules/.pnpm/@element-plus+nuxt@1.1.3_@element-plus+icons-vue@2.3.1_vue@3.5.11_typescript@5.8.3___el_3d7b7c7b7a045387d0b76c7c17c984a1/node_modules/@element-plus/nuxt/node_modules", "../node_modules/.pnpm/@vant+nuxt@1.0.7_magicast@0.3.5_vant@4.9.7_vue@3.5.11_typescript@5.8.3__/node_modules/@vant/nuxt/node_modules", "../node_modules/.pnpm/@nuxt+devtools@2.5.0_vite@6.3.5_@types+node@20.19.0_jiti@2.4.2_sass@1.79.4_terser@5.39._edca095eb3aa76d50a7abee142d3386e/node_modules/@nuxt/devtools/node_modules", "../node_modules/.pnpm/@nuxt+telemetry@2.6.6_magicast@0.3.5/node_modules/@nuxt/telemetry/node_modules", "../node_modules/.pnpm/@nuxtjs+i18n@8.5.6_magicast@0.3.5_rollup@4.43.0_vue@3.5.11_typescript@5.8.3__webpack-sources@3.3.2/node_modules/@nuxtjs/i18n/runtime/server", "../node_modules/.pnpm/@nuxtjs+i18n@8.5.6_magicast@0.3.5_rollup@4.43.0_vue@3.5.11_typescript@5.8.3__webpack-sources@3.3.2/node_modules/@nuxtjs/i18n/dist/runtime/server", "../node_modules/.pnpm/@pinia+nuxt@0.5.5_magicast@0.3.5_typescript@5.8.3_vue@3.5.11_typescript@5.8.3_/node_modules/@pinia/nuxt/runtime/server", "../node_modules/.pnpm/@pinia+nuxt@0.5.5_magicast@0.3.5_typescript@5.8.3_vue@3.5.11_typescript@5.8.3_/node_modules/@pinia/nuxt/dist/runtime/server", "../node_modules/.pnpm/@unocss+nuxt@0.62.4_magicast@0.3.5_postcss@8.5.4_rollup@4.43.0_vite@6.3.5_@types+node@2_26cdc847660293b9b0a92d2472455b96/node_modules/@unocss/nuxt/runtime/server", "../node_modules/.pnpm/@unocss+nuxt@0.62.4_magicast@0.3.5_postcss@8.5.4_rollup@4.43.0_vite@6.3.5_@types+node@2_26cdc847660293b9b0a92d2472455b96/node_modules/@unocss/nuxt/dist/runtime/server", "../node_modules/.pnpm/@element-plus+nuxt@1.1.3_@element-plus+icons-vue@2.3.1_vue@3.5.11_typescript@5.8.3___el_3d7b7c7b7a045387d0b76c7c17c984a1/node_modules/@element-plus/nuxt/runtime/server", "../node_modules/.pnpm/@element-plus+nuxt@1.1.3_@element-plus+icons-vue@2.3.1_vue@3.5.11_typescript@5.8.3___el_3d7b7c7b7a045387d0b76c7c17c984a1/node_modules/@element-plus/nuxt/dist/runtime/server", "../node_modules/.pnpm/@vant+nuxt@1.0.7_magicast@0.3.5_vant@4.9.7_vue@3.5.11_typescript@5.8.3__/node_modules/@vant/nuxt/runtime/server", "../node_modules/.pnpm/@vant+nuxt@1.0.7_magicast@0.3.5_vant@4.9.7_vue@3.5.11_typescript@5.8.3__/node_modules/@vant/nuxt/dist/runtime/server", "../node_modules/.pnpm/@nuxt+devtools@2.5.0_vite@6.3.5_@types+node@20.19.0_jiti@2.4.2_sass@1.79.4_terser@5.39._edca095eb3aa76d50a7abee142d3386e/node_modules/@nuxt/devtools/runtime/server", "../node_modules/.pnpm/@nuxt+devtools@2.5.0_vite@6.3.5_@types+node@20.19.0_jiti@2.4.2_sass@1.79.4_terser@5.39._edca095eb3aa76d50a7abee142d3386e/node_modules/@nuxt/devtools/dist/runtime/server", "../node_modules/.pnpm/@nuxt+telemetry@2.6.6_magicast@0.3.5/node_modules/@nuxt/telemetry/runtime/server", "../node_modules/.pnpm/@nuxt+telemetry@2.6.6_magicast@0.3.5/node_modules/@nuxt/telemetry/dist/runtime/server", "../.output"]}