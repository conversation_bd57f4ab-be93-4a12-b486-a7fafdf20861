<template>
  <div class="layout-default">
    <!-- 页面头部 -->
    <AppHeader />

    <!-- 主要内容区域 -->
    <main class="main-content">
      <slot />
    </main>

    <!-- 页面底部 -->
    <AppFooter />

    <!-- 返回顶部按钮 -->
    <!-- <BackToTop /> -->

    <!-- 全局弹窗 -->
    <!-- <GlobalModals /> -->
  </div>
</template>

<script setup>
// 布局相关的逻辑
const route = useRoute()
const { locale } = useI18n()

// 监听路由变化，滚动到顶部
watch(() => route.path, () => {
  nextTick(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  })
})

// 设置页面语言属性
useHead({
  htmlAttrs: {
    lang: locale.value
  }
})
</script>

<style scoped>
.layout-default {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding-top: 80px;
  /* 为固定头部留出空间 */
}

@media (max-width: 768px) {
  .main-content {
    padding-top: 60px;
  }
}
</style>
