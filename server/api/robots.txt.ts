/**
 * 动态生成 robots.txt
 * 用于指导搜索引擎爬虫如何抓取网站
 */
export default defineEventHandler(async (event) => {
  // 设置响应头
  setHeader(event, 'content-type', 'text/plain')
  
  const baseUrl = 'https://www.chinadaji.com'
  
  // 根据环境生成不同的 robots.txt
  const isProduction = process.env.NODE_ENV === 'production'
  
  if (isProduction) {
    // 生产环境：允许所有爬虫
    return `User-agent: *
Allow: /

# 禁止爬取的路径
Disallow: /admin/
Disallow: /api/
Disallow: /login
Disallow: /register
Disallow: /user/
Disallow: /cart
Disallow: /checkout
Disallow: /search?*
Disallow: /*?*sort=
Disallow: /*?*filter=
Disallow: /*?*page=

# 特殊爬虫规则
User-agent: Baiduspider
Allow: /
Crawl-delay: 1

User-agent: Googlebot
Allow: /
Crawl-delay: 1

User-agent: bingbot
Allow: /
Crawl-delay: 1

# Sitemap 位置
Sitemap: ${baseUrl}/sitemap.xml

# 主机信息
Host: ${baseUrl}`
  } else {
    // 非生产环境：禁止所有爬虫
    return `User-agent: *
Disallow: /

# 这是测试环境，禁止搜索引擎索引`
  }
})
