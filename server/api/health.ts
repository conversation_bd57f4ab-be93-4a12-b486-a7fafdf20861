/**
 * 健康检查接口
 * 用于 Docker 健康检查和负载均衡器检查
 */
export default defineEventHandler(async (event) => {
  try {
    // 检查应用基本状态
    const status = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development'
    }

    // 可以添加更多健康检查项
    // 例如：数据库连接、Redis 连接、外部 API 状态等
    
    // 检查内存使用情况
    const memoryUsage = process.memoryUsage()
    const memoryUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100
    
    if (memoryUsagePercent > 90) {
      status.status = 'warning'
      status.warning = 'High memory usage'
    }

    // 设置响应状态码
    setResponseStatus(event, 200)
    
    return status
  } catch (error) {
    // 健康检查失败
    setResponseStatus(event, 503)
    
    return {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message
    }
  }
})
