/**
 * 动态生成 sitemap.xml
 * 用于 SEO 优化，帮助搜索引擎更好地索引网站
 */
export default defineEventHandler(async (event) => {
  // 设置响应头
  setHeader(event, 'content-type', 'application/xml')
  
  // 基础页面 URL
  const baseUrl = 'https://www.chinadaji.com'
  const staticPages = [
    { url: '/', changefreq: 'daily', priority: '1.0' },
    { url: '/mall', changefreq: 'daily', priority: '0.9' },
    { url: '/market', changefreq: 'daily', priority: '0.9' },
    { url: '/merchants', changefreq: 'weekly', priority: '0.8' },
    { url: '/ai-features', changefreq: 'weekly', priority: '0.7' },
    { url: '/about', changefreq: 'monthly', priority: '0.6' },
    { url: '/contact', changefreq: 'monthly', priority: '0.6' }
  ]
  
  // 动态页面 URL（这里可以从数据库获取）
  const dynamicPages: Array<{url: string, changefreq: string, priority: string, lastmod?: string}> = []
  
  try {
    // 获取商品页面
    // const products = await $fetch('/api/products/sitemap')
    // products.forEach((product: any) => {
    //   dynamicPages.push({
    //     url: `/products/${product.id}`,
    //     changefreq: 'weekly',
    //     priority: '0.8',
    //     lastmod: product.updatedAt
    //   })
    // })
    
    // 获取商家页面
    // const merchants = await $fetch('/api/merchants/sitemap')
    // merchants.forEach((merchant: any) => {
    //   dynamicPages.push({
    //     url: `/merchants/${merchant.id}`,
    //     changefreq: 'weekly',
    //     priority: '0.7',
    //     lastmod: merchant.updatedAt
    //   })
    // })
    
    // 获取分类页面
    // const categories = await $fetch('/api/categories/sitemap')
    // categories.forEach((category: any) => {
    //   dynamicPages.push({
    //     url: `/categories/${category.slug}`,
    //     changefreq: 'weekly',
    //     priority: '0.7'
    //   })
    // })
  } catch (error) {
    console.error('Error fetching dynamic pages for sitemap:', error)
  }
  
  // 合并所有页面
  const allPages = [...staticPages, ...dynamicPages]
  
  // 生成 XML
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${allPages.map(page => `  <url>
    <loc>${baseUrl}${page.url}</loc>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
    ${page.lastmod ? `<lastmod>${new Date(page.lastmod).toISOString()}</lastmod>` : ''}
  </url>`).join('\n')}
</urlset>`
  
  return sitemap
})
