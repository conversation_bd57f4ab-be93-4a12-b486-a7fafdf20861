export default defineNuxtPlugin(() => {
  // 客户端初始化逻辑
  
  // 初始化百度统计
  const initBaiduAnalytics = () => {
    const config = useRuntimeConfig()
    const nationalType = config.public.nationalType
    
    let hmSrc = 'https://hm.baidu.com/hm.js?097a75cd4133c7a6989df2739f92ea97'
    
    // 根据国家馆设置不同的统计代码
    if (nationalType === 'uae') {
      hmSrc = 'https://hm.baidu.com/hm.js?a23e1b9b28fb2da199457a13fa88113d'
    } else if (nationalType === 'idn') {
      hmSrc = 'https://hm.baidu.com/hm.js?b771630e2981a5a985a4c7a9cb52eee0'
    }
    
    // 只在生产环境加载百度统计
    if (process.env.NODE_ENV === 'production') {
      const script = document.createElement('script')
      script.src = hmSrc
      script.async = true
      document.head.appendChild(script)
      
      // 初始化 _hmt 数组
      window._hmt = window._hmt || []
    }
  }
  
  // 初始化阿里云 RUM 监控
  const initAliRUM = () => {
    if (process.env.NODE_ENV === 'production') {
      const rumConfig = {
        pid: 'c67ee5ri5a@8b38ffec8cb1b03',
        endpoint: 'https://c67ee5ri5a-default-cn.rum.aliyuncs.com',
        env: 'prod'
      }
      
      window.__rum = rumConfig
      
      const script = document.createElement('script')
      script.src = 'https://c67ee5ri5a-sdk.rum.aliyuncs.com/v2/browser-sdk.js'
      script.crossOrigin = 'anonymous'
      script.async = true
      document.head.appendChild(script)
    }
  }
  
  // 初始化翻译功能
  const initTranslate = () => {
    // 翻译功能初始化逻辑
    if (window.translate) {
      window.translate.execute()
    }
  }
  
  // 初始化 Vant Toast 配置
  const initVantConfig = () => {
    if (process.client) {
      import('vant').then(({ setToastDefaultOptions }) => {
        setToastDefaultOptions({
          zIndex: 9000
        })
      })
    }
  }
  
  // 页面加载完成后执行初始化
  onMounted(() => {
    initBaiduAnalytics()
    initAliRUM()
    initVantConfig()
    
    // 延迟执行翻译初始化
    setTimeout(() => {
      initTranslate()
    }, 300)
  })
  
  // 路由变化时重新执行翻译
  const router = useRouter()
  router.afterEach(() => {
    setTimeout(() => {
      initTranslate()
    }, 300)
  })
})
