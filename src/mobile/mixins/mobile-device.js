
const mobileDeviceMixins = {
  data() {
    return {
      // 是否是移动端,默认true: 移动端
      isMobile: true,
    }
  },
  created() {
    // eslint-disable-next-line no-magic-numbers
    this.isMobile = !(document.body.clientWidth > 768)
  },
  mounted() {
    window.addEventListener('resize', this.resize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resize)
  },
  methods: {
    // 屏幕宽度改变的时候触发
    resize() {
      // eslint-disable-next-line no-magic-numbers
      this.isMobile = !(document.body.clientWidth > 768)
    },
  }
}
export default mobileDeviceMixins
