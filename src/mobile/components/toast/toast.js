import SuccessIcon from '@/mobile/assets/imgs/icon/success.svg'
import ErrorIcon from '@/mobile/assets/imgs/icon/error.svg'
import { Toast } from 'vant'
import './toast.scss'

// 改写 success 方法，替换成功图标
Toast.setDefaultOptions('success', {
  icon: SuccessIcon,
  iconPrefix: 'custom-icon',
})
// 改写 fail 方法，替换失败图标
Toast.setDefaultOptions('fail', {
  icon: ErrorIcon,
  iconPrefix: 'custom-icon',
})

export default {
  install(Vue) {
    Vue.use(Toast)
    const Message = Toast
    // 此处仅为了兼容 axios 中的 $message.success 等显示消息相关的代码，移动端页面中建议通过 $toast.success 等方法来使用
    Vue.prototype.$message = {
      error(options) {
        Message.fail(options)
      },
      warning(options) {
        Message(options)
      },
      info(options) {
        Message(options)
      },
      success(options) {
        Message.success(options)
      },
      closeAll() {
        Message.clear(true)
      }
    }
  }
}
