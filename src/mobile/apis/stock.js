import { api } from '@/utils/axios.js'

const stockApi = {
  // 获取分享数据
  discernShareList(body) {
    return api.get('/api/platform/discern/shareList', body, {
      returnOrigin: true,
      mock: false,
      isCancelRequest: false,
    })
  },

  // 订单分享数据
  orderShareList(body) {
    return api.get('/api/platform/draft/order/shareList', body, {
      returnOrigin: true,
      mock: false,
      isCancelRequest: false,
    })
  },

  // 查询库存票据详情
  discernDetail(discernId) {
    return api.get(`/api/platform/discern/${discernId}`, null, {
      mock: false,
    })
  },

  // // 查询订单详情
  // queryDraftOrderInfo(orderNo) {
  //   return api.get(`/api/platform/draft/order/shareOrderDetail/${orderNo}`)
  // },
  queryDraftOrderInfo(orderNo) {
    return api.get(`/api/platform/draft/order/getOrderInfo/${orderNo}`)
  },
}

export default stockApi
