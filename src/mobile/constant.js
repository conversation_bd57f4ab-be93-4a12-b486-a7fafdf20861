import { WEB_NAME } from '@/constant'
// 获取 id 映射信息
// eslint-disable-next-line no-unused-vars
function getIdMap(nameMap) {
  let list = Object.values(nameMap)
  let result = {}
  list.forEach(item => {
    result[item.id] = item
  })
  return Object.freeze(result)
}

// 获取映射信息
export function getKeyToValueMap(nameMap, key = 'id', value = 'name') {
  let list = Object.values(nameMap)
  let result = {}
  list.forEach(item => {
    result[item[key]] = item[value]
  })
  return Object.freeze(result)
}

// 票据类型
export const DRAFT_TYPE = Object.freeze({
  ALL: {
    id: 0,
    name: '全部'
  },
  GUO_GU: {
    id: 1,
    name: '国股',
  },
  DA_SHANG: {
    id: 2,
    name: '大商',
  },
  CHEN_SHANG: {
    id: 3,
    name: '城商',
  },
  SAN_NONG: {
    id: 4,
    name: '三农',
  },
  CUN_ZHEN: {
    id: 5,
    name: '村镇',
  },
  WAI_ZI: {
    id: 6,
    name: '外资',
  },
  MIN_YING: {
    id: 7,
    name: '民营',
  },
  CAI_PIAO: {
    id: 8,
    name: '财票',
  },
  SHANG_PIAO: {
    id: 9,
    name: '商票',
  },
  JIANG_ZHE_HU: {
    id: 10,
    name: '江浙沪',
    key: ''
  },
})

// 票据类型 id 映射 名称
export const DRAFT_TYPE_VALUE_MAP = getKeyToValueMap(DRAFT_TYPE)

// 票据类型 id 映射 key
export const DRAFT_TYPE_ID_KEY_MAP = getKeyToValueMap(DRAFT_TYPE, 'id', 'key')

// 票面金额
export const FACE_VALUE = Object.freeze({
  ALL: {
    id: 0,
    name: '全部',
    range: {
      min: 0,
      max: 99999,
      left: true, // 左边是否闭合
      right: true, // 右边是否闭合
    }
  },
  LESS_5: {
    id: 1,
    name: '<5万',
    range: {
      min: 0,
      max: 5,
      left: true,
      right: false
    }
  },
  BETWEEN_5_10: {
    id: 2,
    name: '5-10W',
    range: {
      min: 5,
      max: 10,
      left: true,
      right: false,
    }
  },
  BETWEEN_10_50: {
    id: 3,
    name: '10-50W',
    range: {
      min: 10,
      max: 50,
      left: true,
      right: false,
    }
  },
  BETWEEN_50_100: {
    id: 4,
    name: '50-100W',
    range: {
      min: 50,
      max: 100,
      left: true,
      right: true,
    }
  },
  MORE_100: {
    id: 5,
    name: '>100W',
    range: {
      min: 100,
      max: 99999,
      left: false,
      right: true,
    }
  },
})

// 票据金额 id 映射 range
export const FACE_VALUE_ID_RANGE_MAP = getKeyToValueMap(FACE_VALUE, 'id', 'range')

// 票据期限
export const TERM_OF_BILL = Object.freeze({
  ALL: {
    id: 0,
    name: '全部',
    dueDays: {
      min: 0,
      max: 99999,
      left: true, // 左边是否闭合
      right: true, // 右边是否闭合
    }
  },
  LESS_30: {
    id: 1,
    name: '<30天',
    dueDays: {
      min: 0,
      max: 30,
      left: true,
      right: false,
    }
  },
  BETWEEN_31_90: {
    id: 2,
    name: '31-90天',
    dueDays: {
      min: 31,
      max: 90,
      left: true,
      right: false,
    }
  },
  BETWEEN_91_150: {
    id: 3,
    name: '91-150天',
    dueDays: {
      min: 91,
      max: 150,
      left: true,
      right: false
    }
  },
  MORE_151: {
    id: 5,
    name: '>151天',
    dueDays: {
      min: 151,
      max: 99999,
      left: false,
      right: true,
    }
  },
})

// 票据期限 id 映射 dueDays
export const TERM_OF_BILL_ID_DUEDAYS_MAP = getKeyToValueMap(TERM_OF_BILL, 'id', 'dueDays')

// 票据瑕疵类型
export const BACK_DEFECT_TYPE_NAME_MAP = Object.freeze({
  ALL: {
    id: 0,
    name: '全部',
    key: ''
  },
  NO: {
    id: -1,
    name: '无瑕疵',
    key: 'acceptDefectsWithout'
  },
  ABA: {
    id: 1,
    name: '回出票人aba',
    key: 'defectsAba'
  },
  ABCA: {
    id: 2,
    name: '回出票人abca',
    isShowNum: true,
    key: 'defectsAbca'
  },
  ABB: {
    id: 3,
    name: 'abb',
    key: 'defectsAbb'
  },
  HUI_SHOU_KUAN_REN: {
    id: 4,
    name: '回收款人',
    isShowNum: true,
    key: 'defectsReturnFront'
  },
  BEI_SHU_HUI_TOU: {
    id: 5,
    name: '背书回头',
    isShowNum: true,
    key: 'defectsTurnAround'
  },
  BEI_SHU_CHONG_FU: {
    id: 6,
    name: '背书重复',
    isShowNum: true,
    key: 'defectsDuplicated'
  },
  ZHI_YA: {
    id: 7,
    name: '质押',
    isShowNum: true,
    key: 'defectsPledgeEndorsement'
  },
  BAO_ZHENG: {
    id: 8,
    name: '保证',
    key: 'defectsPromise'
  },
  SHANG_XIA_BU_YI_ZHI: {
    id: 9,
    name: '不一致',
    key: 'defectsInconformity'
  },
  OTHER: {
    id: 10,
    name: '其他',
    key: 'defectsOther',
    descKey: 'defectsOtherDesc',
    isShowNum: true
  },
  HUI_GOU_TIE_XIAN: {
    id: 21,
    name: '回购式贴现',
    key: 'defectsRepoDiscount',
    isShowNum: true
  },
})

// 票据瑕疵 id 映射 key
export const BACK_DEFECT_TYPE_MAP = getKeyToValueMap(BACK_DEFECT_TYPE_NAME_MAP, 'id', 'key')

// 背书手数选项
export const ENDORES_OPTIONS_TYPE_LIST = Object.freeze([
  {
    name: '全选',
    id: null,
  },
  {
    name: '0手',
    id: 0,
  },
  {
    name: '1手',
    id: 1,
  },
  {
    name: '2手内',
    id: 2,
  },
  {
    name: '3手内',
    id: 3,
  },
  {
    name: '4手内',
    id: 4,
  },
])

// 票据瑕疵类型 id 映射 名称
export const BACK_DEFECT_TYPE_VALUE_MAP = getKeyToValueMap(BACK_DEFECT_TYPE_NAME_MAP)

// 分享来源
export const SHARE_FORM = Object.freeze({
  SHENDU: {
    id: 'shendu',
    name: '深度'
  },
  ERP: {
    id: 'erp',
    name: WEB_NAME,
    title: '票据分享列表'
  }
})
