import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)

const routes = [
  {
    path: '/stock',
    component: () => import(/* webpackChunkName: "mobile-stock" */ '@/mobile/views/pages/stock/stock.vue'),
  },
  {
    name: 'stockDetail',
    path: '/stock-detail',
    component: () => import(/* webpackChunkName: "mobile-stock-detail" */ '@/mobile/views/pages/stock/stock-detail.vue'),
  },
  {
    name: 'stockDetailPc',
    path: '/stock',
    component: () => import(/* webpackChunkName: "mobile-stock-detail" */ '@/mobile/views/pages/stock/stock.vue'),
  },
  {
    name: 'order',
    path: '/order',
    component: () => import(/* webpackChunkName: "mobile-stock-detail" */ '@/mobile/views/pages/order/order.vue')
  },
  {
    path: '/404',
    component: () => import(/* webpackChunkName: "mobile-404" */ '@/mobile/views/pages/404/404.vue'),
  },
  {
    path: '*',
    redirect: '/404',
  },
]

const router = new VueRouter({
  mode: 'history',
  base: 'mobile.html/',
  routes,
})

export default router
