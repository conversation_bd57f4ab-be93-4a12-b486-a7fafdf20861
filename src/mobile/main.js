import Vue from 'vue'
import App from './app.vue'
import router from './router'

import '@/common/scss/common.scss' // 公共样式
import '@/common/scss/reset.scss' // 重置默认样式
import '@/common/js/directives.js' // 注入全局指令
import Icon from '@/views/components/icon/icon.vue'
import { setVue as setVueForAxios } from '@/utils/axios'
import Toast from './components/toast/toast'
import { THEME_NAME } from '@/constant'
Vue.config.productionTip = false
Vue.component('icon', Icon)

const vue = new Vue({
  name: 'root',
  router,
  render: h => h(App),
})

setVueForAxios(vue)

// eslint-disable-next-line no-magic-numbers
const isMobile = /Android|webOS|iPhone|BlackBerry/i.test(navigator.userAgent) && (document.body.offsetWidth || window.innerWidth) <= 450
Vue.prototype.$isMobile = isMobile
Vue.prototype.$isPc = !isMobile
if (!isMobile) {
  // eslint-disable-next-line no-unused-expressions
  import('@/common/scss/element-reset.scss'); // element ui 样式
  (async() => {
    const ElementUI = await import('@shendu/element-ui')
    Vue.use(ElementUI)
    vue.$mount('#app')
  })()
} else {
  Vue.use(Toast)
  vue.$mount('#app')
}
// 设置手机端的title
document.title = THEME_NAME

// 开发环境启动 vconsole 调试
const isDevelopment = (process.env.VUE_APP_API_ENV || process.env.NODE_ENV) === 'development'
// 检查控制台是否有 vconsole=true 参数
const hasVConsoleQuery = /(\?|&)vconsole=true(&|$)/.test(window.location.href)
if ((isDevelopment || hasVConsoleQuery)) {
  // 按需加载 vConsole
  const loadVConsole = async() => {
    const VConsole = await import('vconsole')
    // eslint-disable-next-line new-cap,no-unused-vars
    const vConsole = new VConsole.default()
  }
  loadVConsole()
}
