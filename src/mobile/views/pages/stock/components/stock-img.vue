<!-- 图片分享-票据库存列表 -->
<style lang="scss" scoped>
.stock-img {
  margin: auto;
  color: $color-FFFFFF;
  background: $color-FFFFFF;
}

.img-header-box {
  position: relative;
  padding-top: 34px;
  width: 100%;
  height: 157px;
  background-size: cover;

  .bg-header {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .content {
    position: relative;
  }
}

.img-footer-box {
  position: relative;
  padding: 26px 0  0 16px;
  width: 100%;
  height: 90px;
  background-size: cover;

  .bg-footer {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .content {
    position: relative;
  }
}

.big-title {
  height: 25px;
  font-size: 18px;
  font-weight: 600;
  text-align: center;
  line-height: 25px;
}

.generation-time {
  margin-top: 8px;
  height: 20px;
  text-align: center;
  line-height: 20px;
}

.footer-desc {
  margin-bottom: 4px;
  height: 22px;
  font-size: 16px;
  line-height: 22px;
}

.weight {
  font-weight: 600;
}

.main {
  position: relative;
  padding: 0 16px;
  background: $color-FFFFFF;
}

@media screen and (min-width: 768px) {
  .stock-img {
    width: 750px;
  }

  .img-header-box {
    padding-top: 68px;
    width: 100%;
    height: 314px;
  }

  .img-footer-box {
    padding: 58px 0  0 32px;
    height: 180px;
  }

  .big-title {
    height: 50px;
    font-size: 36px;
    line-height: 50px;
  }

  .generation-time {
    margin-top: 16px;
    height: 40px;
    font-size: 28px;
    line-height: 40px;
  }

  .footer-desc {
    margin-bottom: 8px;
    height: 44px;
    font-size: 32px;
    line-height: 44px;
  }

  .main {
    padding: 0 32px;
  }
}
</style>

<style >
.stock-img {
  width: 100%;
}
</style>

<template>
  <div v-if="show && dataList" class="stock-img">
    <div class="img-header-box">
      <img
        ref="bgHeader"
        class="bg-header"
        src="https://oss.chengjie.red/web/imgs/mobile/stock-img-header.png"
        alt=""
      >
      <div class="content">
        <div class="big-title">{{ dataList.company }}</div>
        <div class="big-title">库存数据</div>
        <div class="generation-time">库存生成时间：{{ dataList.createAt }}</div>
      </div>
    </div>
    <div class="main">
      <StockItem
        v-for="item in dataList.data"
        :key="item.index"
        :is-mobile="isMobile"
        type-value="img"
        :data-item="item"
      />
    </div>
    <div class="img-footer-box">
      <img
        ref="bgFooter"
        class="bg-footer"
        src="https://oss.chengjie.red/web/imgs/mobile/stock-img-footer.png"
        alt=""
      >
      <div class="content">
        <div class="footer-desc">共<span class="weight"> {{ dataList.data.length }} </span>张，总金额<span class="weight"> {{ dataList.totalAmount }} </span>万</div>
        <div class="footer-desc">如需查看更多，敬请电话联系：<span class="weight">{{ dataList.mobile }}</span></div>
      </div>
    </div>
  </div>
</template>

<script>
import html2canvas from 'html2canvas'
import StockItem from './stock-item.vue'
import { getBase64Image } from '@/common/js/util'

export default {
  name: 'stock-img',
  components: { StockItem },
  props: {
    dataList: {
      type: [Array, Object],
      default: () => ({})
    },
    isMobile: { // 是否是移动端
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      show: true
    }
  },
  watch: {
    dataList() {
      if (this.dataList) {
        this.$nextTick().then(() => {
          this.setBackgroundBase64()
        })
      }
    }
  },
  created() {
    document.title = '票据库存列表'
  },

  methods: {
    // 转换图片素材并生成图片
    async setBackgroundBase64() {
      try {
        const h = this.setImageSrc(this.$refs.bgHeader)
        const f = this.setImageSrc(this.$refs.bgFooter)
        await h
        await f
        this.toImage()
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error(error)
      }
    },

    // 设置指定图片 src 为 base64
    async setImageSrc(imgRef) {
      const { src } = imgRef
      const base64 = await this.getSrc2Base64(src)
      imgRef.src = base64
    },

    // 根据图片 src 获取 base64
    getSrc2Base64(src) {
      return new Promise(resolve => {
        const img = new Image()
        img.src = src
        img.setAttribute('crossOrigin', 'Anonymous')
        img.onload = function() {
          const base64 = getBase64Image(img)
          resolve(base64)
        }
      })
    },

    // 生成分享图片
    toImage() {
      html2canvas(document.querySelector('#app')).then(canvas => {
        this.show = false
        let image = new Image()
        image.src = canvas.toDataURL('image/png')
        image.classList.add('stock-img')
        document.body.appendChild(image)
      })
    },
  }
}
</script>
