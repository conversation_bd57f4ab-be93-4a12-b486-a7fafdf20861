<!-- 单条票据 -->
<style lang="scss" scoped>
.stock-item {
  border-bottom: 1px  solid  $color-D9D9D9;
  padding: 16px 0 12px;
  font-size: 14px;
  color: $color-text-primary;
  background: $color-FFFFFF;

  &:last-child {
    border-bottom: 0;
  }
}

// 图片分享时候的web样式
.stock-item-dobule {
  border-bottom: 2px  solid  $color-D9D9D9;
  padding: 32px 0 24px;
  font-size: 28px;

  &:last-child {
    border-bottom: 0;
  }

  .gray-title {
    height: 40px;
    line-height: 40px;
    font-size: 32px;
  }

  .tag {
    margin-left: 8px;
    border-radius: 28px;
    padding: 2px 16px;
    height: 40px;
    font-size: 24px;
    line-height: 40px;
  }

  .margin-top-2 {
    margin-top: 4px;
  }

  .margin-top-10 {
    margin-top: 20px;
  }

  .right-icon {
    font-size: 40px;
  }

  .item {
    height: 82px;
  }

  .defect-tag {
    max-width: 470px;
  }

  .left-line-one {
    margin-bottom: 14px;
  }
}

// 深色标题字号
.gray-title {
  height: 20px;
  color: $color-text-secondary;
  line-height: 20px;
}

// 加粗字体
.weight {
  font-weight: 600;
  color: $color-text-primary;
}

// 右对齐
.text-right {
  text-align: right;
}

// 两端对齐布局
.space-between-layout {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: $color-text-primary;
}

.tag {
  margin-left: 4px;
  border-radius: 14px;
  padding: 1px 8px;
  height: 20px;
  font-size: 12px;
  font-weight: 600;
  line-height: 20px;
}

.green-tag {
  color: $font-color;
  background: $color-E6F3F3;
}

.red-tag {
  color: $--color-primary;
  background: $color-warning-sub;
}

.margin-top-2 {
  margin-top: 2px;
}

.margin-top-10 {
  margin-top: 10px;
}

.right-icon {
  font-size: 20px;
  color: $--color-primary;
}

.item {
  display: flex;
  justify-content: space-around;
  height: 42px;
  flex-direction: column;
}

.left-line-one {
  display: flex;
}

.defect-tag {
  display: inline-block;
  overflow: hidden;
  max-width: 230px;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@media screen and (min-width: 768px) {
  .stock-item {
    font-size: 16px;
  }

  .tag {
    font-size: 14px;
    line-height: 19px;
  }
}

.pc-left {
  display: flex;

  .info-left {
    margin-right: 10px;
    border-right: 1px solid $color-D9D9D9;
    width: 359px;
  }
}

.btn-detail {
  position: relative;
  top: -21px;
}
</style>

<template>
  <div
    v-if="dataItem"
    :class="[typeValue === 'img' && !isMobile ? 'stock-item-dobule' : 'stock-item']"
    @click="itemHandleToDetail"
  >
    <div class="space-between-layout">
      <div class="left">
        <div class="left-line-one">
          <span class="gray-title">承兑人</span>
          <span class="tag g-tag--hred ">{{ dataItem.acceptorType }}</span>

          <span v-if="typeValue === 'img' || isMobile" :class="['tag', 'defect-tag', dataItem.defects ? 'g-tag--hred' : 'g-tag--green']">{{ dataItem.defects ? dataItem.defects : '无瑕疵' }}</span>
        </div>
        <div class="weight margin-top-2">
          {{ dataItem.acceptorName }}<span class="tag g-tag--hred">{{ isNull(dataItem.endorseCount)
            ? "票方未提供"
            : dataItem.endorseCount + "手" }}</span>
        </div>
      </div>
      <icon v-if="typeValue === 'list' && isMobile" class="right-icon" type="chengjie-right" />
    </div>
    <!-- 移动端 -->
    <div v-if="typeValue === 'img' || isMobile" class="space-between-layout margin-top-10">
      <div class="item">
        <div class="gray-title">票面金额</div>
        <div class="weight margin-top-2">{{ dataItem.draftAmount }}万</div>
      </div>
      <div class="item">
        <div class="gray-title">到期日(剩 {{ dataItem.daysRemaining }} 天)</div>
        <div class=" margin-top-2">{{ dataItem.maturityDate }}</div>
      </div>
      <div class="text-right item">
        <div class="gray-title">每十万扣/利率</div>
        <div class="margin-top-2">
          <template v-if="dataItem.lakhFee || dataItem.annualInterest">
            {{ dataItem.lakhFee ? dataItem.lakhFee + '元' : '-' }} /{{ dataItem.annualInterest ? dataItem.annualInterest + '%' : '-' }}
          </template>
          <template v-else>
            -
          </template>
        </div>
      </div>
    </div>
    <!-- PC端 -->
    <div v-else class="space-between-layout">
      <div class="pc-left">
        <div class="info-left ">
          <div class="gray-title margin-top-10">到期日(剩 {{ dataItem.daysRemaining }} 天)</div>
          <div class=" margin-top-2">{{ dataItem.maturityDate }}</div>
          <div class="gray-title margin-top-10">票面金额</div>
          <div class="margin-top-2">{{ dataItem.draftAmount }}万</div>
        </div>
        <div>
          <div class="gray-title margin-top-10">瑕疵</div>
          <div :class="['margin-top-2', dataItem.defects ? 'red-text' : 'weight']">
            {{ dataItem.defects ? dataItem.defects : '无瑕疵' }}
          </div>
          <div class="gray-title margin-top-10">每十万扣/利率</div>
          <div class="margin-top-2">
            <template v-if="dataItem.lakhFee || dataItem.annualInterest">
              {{ dataItem.lakhFee ? dataItem.lakhFee + '元' : '-' }} /{{ dataItem.annualInterest ? dataItem.annualInterest + '%' : '-' }}
            </template>
            <template v-else>
              -
            </template>
          </div>
        </div>
      </div>
      <div v-if="typeValue === 'h5'" class="pc-right">
        <el-button
          class="btn-detail"
          width="120"
          height="42"
          border
          type="primary"
          @click="btnHandleToDetail"
        >
          查看详情
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { isNull } from '@/common/js/util'
export default {
  name: 'stock-item',
  props: {
    typeValue: {
      default: 'list',
      type: String
    },
    dataItem: {
      type: Object,
      default: () => ({})
    },
    isMobile: {
      type: Boolean
    }
  },

  data() {
    return {

    }
  },
  methods: {
    isNull,
    // 点击整个item-跳转详情
    itemHandleToDetail() {
      // 拦截当是pc端的时候不触发
      this.isMobile && this.$emit('on-detail')
    },

    // 点击查看详情按钮-跳转详情
    btnHandleToDetail() {
      this.$emit('on-detail')
    },
  }
}
</script>
