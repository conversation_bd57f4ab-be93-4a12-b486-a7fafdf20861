
<!-- 票据库存弹窗详情 -->
<style lang="scss" scoped>
// .stock-detail-dialog {

// }

::v-deep {
  .container {
    padding: 20px;
  }

  .content-image {
    min-height: 300px;
  }

  .el-dialog {
    &.stock-custom-dialog {
      width: 600px;
    }

    .el-dialog__body {
      padding: 0;
    }

    .panel .panel-item {
      margin-bottom: 4px;

      .value {
        font-size: 16px;
      }
    }
  }
}
</style>

<template>
  <div>
    <el-dialog
      class="stock-detail-dialog"
      title="票据详情"
      :visible.sync="dialogVisible"
      width="600"
      :before-close="handleClose"
      :close-on-click-modal="false"
      append-to-body
      center
      :lock-scroll="true"
      custom-class="stock-custom-dialog"
    >
      <div class="body">
        <StockDetail ref="stockDetail" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import StockDetail from '../stock-detail.vue'

export default {
  name: 'stock-detail-dialog',
  components: {
    StockDetail
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      dialogVisible: false, // 弹窗显示
    }
  },
  computed: {

  },

  methods: {
    // 初始化
    init() {
      this.$nextTick().then(() => {
        this.$refs.stockDetail.init()
      })
      this.dialogVisible = true
    },

    // 关闭弹窗(返回列表,删掉票据详情id表示列表页面,带上id表示打开票据详情弹窗)
    handleClose() {
      this.dialogVisible = false
      const query = { ...this.$route.query }
      delete query.id // 删除id,不会触发打开票据详情
      this.$router.replace({
        path: this.$route.path,
        query,
      })
    },

  },
}
</script>
