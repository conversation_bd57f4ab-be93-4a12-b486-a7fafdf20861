<!-- 文本分享-票据库存列表 -->
<style lang="scss" scoped>
.stock-text {
  padding: 16px 16px 0;
  height: 100%;
  color: $color-text-primary;
  background: $color-FFFFFF;
}

.company {
  font-size: 18px;
  font-weight: 600;
  line-height: 25px;
}

.generation-time {
  margin-top: 8px;
  height: 20px;
  font-size: 14px;
  line-height: 20px;
}

.stock-item {
  border-bottom: 1px  solid  $color-D9D9D9;
  padding: 12px 0;
  font-size: 16px;
  line-height: 22px;
}

.label {
  margin-top: 10px;

  &:first-child {
    margin-top: 0;
  }
}

.btn {
  margin: 12px auto 0;
  border-radius: 2px;
  width: 95%;
  height: 40px;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  color: $color-FFFFFF;
  background: $--color-primary;
  line-height: 40px;
}

.footer {
  padding: 12px 0 16px;
}

.footer-desc {
  height: 22px;
  font-size: 16px;
  line-height: 22px;

  &:first-child {
    margin-bottom: 4px;
  }
}
</style>

<template>
  <div v-if="dataList" class="stock-text">
    <div class="company">{{ dataList.company }}</div>
    <div class="generation-time">库存生成时间：{{ dataList.createAt }}</div>

    <div v-for="item in dataList.data" :key="item.index" class="stock-item">
      <div class="label">承兑人：{{ item.acceptorName }}</div>
      <div class="label">票面金额：{{ item.draftAmount }} 万</div>
      <div class="label">到期日：{{ item.maturityDate }}</div>
      <div class="label">每十万扣：{{ item.lakhFee || '-' }}</div>
      <div class="label">瑕疵：{{ item.defects }}</div>
    </div>
    <div class="footer">
      <div class="footer-desc">共 {{ dataList.data.length }} 张，总金额 {{ dataList.totalAmount }}万</div>
      <div class="footer-desc">如需查看更多，敬请电话联系：{{ dataList.mobile }}</div>
      <div v-copy="{ value: copyContent, onSuccess, onError }" class="btn">一键复制</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'stock-text',
  props: {
    dataList: {
      type: [Array, Object],
      default: () => ({})
    },
  },

  computed: {
    copyContent() {
      let content = ''
      this.dataList.data.forEach(item => {
        content = `${content}承兑人: ${item.acceptorName}\n票面金额： ${item.draftAmount ? `${item.draftAmount}万` : '-'}\n到期日： ${item.maturityDate}\n每十万扣：${item.lakhFee ? `${item.lakhFee}元` : '-'}\n瑕疵： ${item.defects}\n`
        content = `${content}\n` // 不同票据之间加换行
      })
      const headerContent = `${this.dataList.company}\n库存生成时间： ${this.dataList.createAt}\n\n`
      const footerContent = `共${this.dataList.data.length}张，总金额${this.dataList.totalAmount}万\n如需查看更多，敬请电话联系：${this.dataList.mobile}`
      return headerContent + content + footerContent
    }
  },

  created() {
    document.title = '票据库存列表'
  },

  methods: {
    // 复制成功
    onSuccess() {
      this.$toast.success('复制成功')
    },
    // 复制失败
    onError() {
      this.$toast.fail('复制失败')
    },
  }
}
</script>
