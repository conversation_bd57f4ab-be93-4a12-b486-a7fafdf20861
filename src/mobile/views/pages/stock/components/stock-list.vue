<!-- 列表分享-票据库存列表 -->
<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
  padding: 10px 16px;
}

.screen {
  font-size: 14px;
  line-height: 20px;
  color: $font-color;
}

.main {
  margin-bottom: 56px;
  padding: 0 16px;
  background: $color-FFFFFF;
}

.summary {
  height: 22px;
  font-size: 14px;
  color: $color-text-primary;
  line-height: 22px;
}

.red-text {
  color: $--color-primary;
}

.title-small {
  position: relative;
  display: flex;
  align-items: center;
  padding-left: 12px;
  font-size: 16px;
  font-weight: bold;
  line-height: 22px;

  &::before {
    position: absolute;
    top: 50%;
    left: 0;
    width: 4px;
    height: 16px;
    background: $--color-primary;
    transform: translateY(-50%);
    content: "";
  }

  &.isMobile {
    padding-left: 6px;
    font-size: 14px;
  }
}

.contact-btn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 2px;
  padding: 8px;
  width: 112px;
  height: 40px;
  background: $--color-primary;

  :hover {
    cursor: pointer;
  }

  .contact-text {
    font-size: 16px;
    font-weight: 600;
    color: $color-FFFFFF;
  }

  .contact-icon {
    font-size: 24px;
    color: $color-FFFFFF;
  }
}

.empty {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  width: 186px;
  height: 152px;
  text-align: center;

  .empty-img {
    width: 186px;
  }

  .empty-desc {
    font-size: 18px;
    color: $color-text-secondary;
    line-height: 26px;
  }
}

@media screen and (min-width: 768px) {
  .title-small {
    font-size: 18px;
  }

  .header {
    padding: 10px 16px 10px 0;
  }

  .empty {
    top: 250px;
  }
}
</style>

<template>
  <div v-if="showDataList" class="stock-list">
    <FilterBox
      :is-mobile="isMobile"
      :show-visible.sync="isShowFilterBox"
      :data-list="dataList"
      @filter-data="filterData"
    />
    <div class="header">
      <div :class="[isMobile && 'isMobile', 'title-small']">
        <!-- {{ showDataList.company }} -->
        <span>
          （共
          <span class="red-text">{{ showDataList.data.length }}</span>
          张,总金额
          <span class="red-text">{{ showDataList.totalAmount }}</span>
          万
          <template v-if="typeValue === 'text'">，电话：{{ dataList.mobile }}</template>
          ）
        </span>
      </div>
      <div v-if="isMobile && typeValue === 'h5'" class="screen" @click="FilterBoxShowChange">筛选</div>
      <!--
        <el-button
        v-if="!isMobile && typeValue === 'h5'"
        width="120"
        height="42"
        type="primary"
        @click="contactClick"
        >
        电话联系
        </el-button>
      -->
      <!-- 非移动端 且 文本分享 且筛选的数据的长度大于0才显示按钮 -->
      <el-button
        v-if="!isMobile && typeValue === 'text' && showDataList.data.length"
        v-copy="{ value: copyContent, onSuccess, onError }"
        width="120"
        height="42"
        type="primary"
      >
        一键复制
      </el-button>
    </div>
    <div v-if="showDataList.data.length" class="main">
      <StockItem
        v-for="item in showDataList.data"
        :key="item.index"
        :is-mobile="isMobile"
        :data-item="item"
        :type-value="typeValue"
        @on-detail="() => handleDetail(item)"
      />
    </div>
    <div v-else class="empty">
      <img src="https://oss.chengjie.red/web/imgs/mobile/empty-bg.png" class="empty-img">
      <div class="empty-desc">暂无符合条件的票据</div>
    </div>
    <!--
      <div v-if="isMobile" class="footer">
      <a :href="`tel:${showDataList.mobile}`" class="contact-btn">
      <icon class="contact-icon" type="chengjie-contact" />
      <span class="contact-text">电话联系</span>
      </a>
      </div>
    -->
  </div>
</template>

<script>
import FilterBox from './filter-box.vue'
import StockItem from './stock-item.vue'
export default {
  name: 'stock-list',
  components: { StockItem, FilterBox },
  props: {
    dataList: {
      type: [Array, Object],
      default: () => ({})
    },
    isMobile: { // 是否是移动端
      type: Boolean,
      default: false
    },
    typeValue: {
      default: 'h5',
      type: String
    },
  },

  data() {
    return {
      isShowFilterBox: false, // 是否展示筛选框
      showDataList: null, // 需要显示的数据
      currentIsMobile: true, // 目前是移动端
    }
  },

  computed: {
    copyContent() {
      let content = ''
      this.showDataList.data.forEach(item => {
        content = `${content}承兑人: ${item.acceptorName}\n票面金额： ${item.draftAmount ? `${item.draftAmount}万` : '-'}\n到期日： ${item.maturityDate}\n每十万扣： ${item.lakhFee ? `${item.lakhFee}元` : '-'}\n瑕疵： ${item.defects}\n`
        content = `${content}\n` // 不同票据之间加换行
      })
      const headerContent = `${this.dataList.company}\n库存生成时间： ${this.dataList.createAt}\n\n`
      const footerContent = `共${this.showDataList.data.length}张，总金额${this.showDataList.totalAmount}万\n如需查看更多，敬请电话联系：${this.dataList.mobile}`
      return headerContent + content + footerContent
    },
  },

  watch: {
    dataList(val) {
      this.showDataList = val
    },
    isMobile() {
      this.isShowFilterBox = !this.isMobile
    }
  },

  created() {
    document.title = '票据库存列表'
  },
  mounted() {
    this.$nextTick().then(() => {
      this.isShowFilterBox = !this.isMobile
    })
  },

  methods: {
    // 点击电话联系
    contactClick() {
      const h = this.$createElement
      this.$msgbox({
        title: '提示',
        dangerouslyUseHTMLString: true,
        message: h('p', null, `请使用手机拨打电话：${this.showDataList.mobile}`),
        confirmButtonText: '我知道了',
        type: 'warning',
      }).then(() => {
        // 确认
      })
    },

    FilterBoxShowChange() {
      this.isShowFilterBox = true
    },

    // 筛选数据
    filterData(val) {
      this.showDataList = val
    },

    // 详情处理
    handleDetail(item) {
      // 移动端跳转到详情页面,PC端打开详情弹窗
      if (this.isMobile) {
        this.toDetail(item)
      } else {
        this.openDetail(item)
      }
    },

    // 移动端跳转详情
    toDetail(item) {
      const { id, orderNo } = item
      const { shareKey, shareForm, type } = this.$route.query
      const { mobile } = this.dataList
      if (shareForm === 'order') { // 订单分享的
        this.$router.push({
          name: 'order', // 移动端票据详情页
          query: { id, shareKey, type, shareForm, orderNo },
        })
      } else { // 库存分享
        this.$router.push({
          name: 'stockDetail', // 移动端票据详情页
          query: { id, shareKey },
          params: {
            ...item,
            mobile,
          }
        })
      }
    },

    // PC打开详情弹窗--
    openDetail(item) {
      const { orderNo, id } = item
      const { shareKey, shareForm } = this.$route.query
      const { mobile } = this.dataList
      // 订单详情要用orderNo查询 库存用id查询
      const params = {}
      shareForm === 'order' ? params.orderNo = orderNo : params.id = id
      this.$router.push({
        name: 'stockDetailPc', // PC端下的票据详情页(实际上也是列表页面,只是带上id,表明需要打开票据详情弹窗)
        query: { ...this.$route.query, shareKey, ...params, shareForm: shareForm === 'order' ? shareForm : '' },
        params: {
          ...item,
          mobile,
        }
      })
    },

    // 复制成功
    onSuccess() {
      const message = '复制成功'
      this.isMobile ? this.$toast.success(message) : this.$message.success(message)
    },
    // 复制失败
    onError() {
      const message = '复制失败'
      this.isMobile ? this.$toast.fail(message) : this.$message.error(message)
    },

  }
}
</script>
