<!-- 筛选框-票据库存列表 -->
<style lang="scss" scoped>
.mantle {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 50;
  width: 100%;
  height: 100%;
  background-color: rgb(00 00 00 / 50%);

  .filter-box {
    position: fixed;
    padding: 16px;
    background: $color-FFFFFF;
  }

  .mobile-filter-box {
    position: fixed;
    padding: 16px 11px;
    height: 100%;
    background: $color-FFFFFF;
  }

  .select-group {
    display: flex;
    flex-wrap: wrap;

    ::v-deep {
      .select-item {
        margin: 0 7px 8px 0;
      }

      :nth-child(4n) {
        margin-right: 0;
      }
    }
  }
}

.question-icon {
  margin-top: 6px;
  margin-left: 10px;
  font-size: 20px;
}

.pc-bg {
  padding: 16px 5px 16px 16px;
  background: $color-FFFFFF;

  .pc-select {
    display: flex;
  }

  .select-group {
    display: flex;
    flex-wrap: wrap;

    ::v-deep {
      .select-item {
        margin: 0 8px 10px 0;
        padding: 0 12px;
        width: auto;
        height: 32px;
        font-size: 14px;
        line-height: 30px;

        &:hover {
          color: $--color-primary;
        }
      }
    }
  }

  // 深色标题字号
  .gray-title {
    margin-right: 16px;
    min-width: 66px;
    height: 20px;
    font-size: 16px;
    font-weight: 500;
    text-align: right;
    color: $color-text-secondary;
    line-height: 32px;
  }

  .input-append {
    width: 119px;
    height: 32px;

    .input {
      padding: 8px 12px;
      width: 82px;
      font-size: 12px;
      line-height: 22px;
    }

    .to {
      height: 30px;
      line-height: 30px;
    }

    .append {
      border-left: 1px solid $color-D9D9D9;
      width: 36px;
      height: 30px;
      text-align: center;
      background: $color-FAFAFA;
      line-height: 30px;
    }
  }
}

.title-small {
  position: relative;
  padding-left: 12px;
  font-size: 16px;
  font-weight: bold;
  line-height: 22px;

  &::before {
    position: absolute;
    top: 50%;
    left: 0;
    width: 4px;
    height: 16px;
    background: $--color-primary;
    transform: translateY(-50%);
    content: "";
  }
}

.select-box {
  margin: 10px 0 2px;
}

// 深色标题字号
.gray-title {
  margin: 2px 0;
  height: 20px;
  color: $color-text-secondary;
  line-height: 20px;
}

.btn-box {
  display: flex;
  justify-content: space-between;
  padding-top: 20px;
}

.btn-item {
  border: 1px solid $color-D9D9D9;
  border-radius: 2px;
  width: 168px;
  height: 40px;
  font-size: 16px;
  text-align: center;
  line-height: 40px;
}

.reset {
  border: 1px solid $--color-primary;
  color: $--color-primary;
}

.confirm {
  color: $color-FFFFFF;
  background-color: $--color-primary;
}

.select-slot {
  display: flex;
  justify-content: space-between;
  width: 256px;

  &.w {
    width: auto;
  }

  i {
    margin-left: 0;
  }
}

.input-append {
  display: flex;
  border: 1px solid $color-D9D9D9;
  border-radius: 1px;
  width: 119px;
  height: 38px;

  .input {
    padding: 8px 12px;
    width: 82px;
    font-size: 14px;
    line-height: 22px;
  }

  .append {
    border-left: 1px solid $color-D9D9D9;
    width: 36px;
    height: 36px;
    text-align: center;
    background: $color-FAFAFA;
    line-height: 36px;
  }
}

.input-append-mobile {
  display: flex;
  border: 1px solid $color-D9D9D9;
  border-radius: 1px;
  width: 119px;
  height: 30px;

  .input {
    padding: 8px 12px;
    width: 82px;
    font-size: 14px;
    line-height: 22px;
  }

  .append {
    border-left: 1px solid $color-D9D9D9;
    width: 36px;
    height: 28px;
    text-align: center;
    background: $color-FAFAFA;
    line-height: 28px;
  }
}

.to {
  height: 38px;
  line-height: 38px;
}

.mobile-box {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

// @media screen and (min-height: 992px) {
//   .mobile-box {
//     height: calc(100vh - 250px) !important;
//   }
// }

// @media screen and (max-height: 991px) {
//   .mobile-box {
//     height: calc(100vh - 237px) !important;
//   }
// }

// @media screen and (max-height: 767px) {
//   .mobile-box {
//     height: calc(100vh - 50px) !important;
//   }
// }

// @media screen and (max-height: 479px) {
//   .mobile-box {
//     height: calc(100vh - 900px) !important;
//   }
// }
</style>

<template>
  <div
    v-if="showVisible"
    :class="[isMobile ? 'mantle' : 'pc-bg']"
  >
    <div v-click-outside="{listen: () => showFilterBox, handler: hideFilterBox}" :class="[isMobile ? 'mobile-filter-box' : 'filter-box']">
      <div class="title-small">筛选</div>
      <div class="select-box">
        <div :class="[isMobile ? 'mobile-box' : '']" :style="{height: scrollerHeight}">
          <div :class="[isMobile ? '' : 'pc-select']">
            <div class="gray-title select-box">票据类型</div>
            <SelectGroup
              v-model="checkDraftType"
              :is-multiply="true"
              :data-list="draftTypeList"
              class="select-group"
            />
          </div>
          <div :class="[isMobile ? '' : 'pc-select']">
            <div class="gray-title">票面金额</div>
            <SelectGroup v-model="checkFaceValue" :data-list="faceValueList" class="select-group">
              <div class="select-slot">
                <div :class="[isMobile ? ' input-append-mobile' : 'input-append']">
                  <input
                    v-model="minAmount"
                    class="input"
                    placeholder="最小金额"
                    @input="amountInput($event, 'minAmount')"
                  ><div class="append">万</div>
                </div>
                <span class="to">-</span>
                <div :class="[isMobile ? ' input-append-mobile' : 'input-append']">
                  <input
                    v-model="maxAmount"
                    class="input"
                    placeholder="最大金额"
                    @input="amountInput($event, 'maxAmount')"
                  ><div class="append">万</div>
                </div>
              </div>
            </SelectGroup>
          </div>
          <div :class="[isMobile ? '' : 'pc-select']">
            <div class="gray-title">票据期限</div>
            <SelectGroup v-model="checkTermOfBill" :data-list="termOfBillList" class="select-group" />
          </div>
          <!-- 瑕疵开始 -->
          <div :class="[isMobile ? '' : 'pc-select']">
            <div class="gray-title">瑕疵</div>
            <SelectGroup
              v-model="checkDefectOfBill"
              :data-list="defectBillList"
              :is-multiply="true"
              class="select-group"
            >
              <div v-if="!isMobile" class="select-slot w">
                <el-tooltip placement="top-start" popper-class="defect-tooltip">
                  <div slot="content">
                    <p>背书回头：隔手背书中的被背书人名称(除出票人和收款人外)出现名称重复</p>
                    <p>回出票人aba：第一手转让背书中的被背书人为出票人</p>
                    <p>回出票人abca：除第一手转让背书外,后续转让背书中的被背书人中出现出票人</p>
                    <p>回收款人：除第一手转让背书外,后续转让背书中的被背书人为收款人</p>
                    <p>背书重复：除第一手转让背书外,同一手转让背书中的背书人名称与被背书人名称同名</p>
                    <p>abb：第一手转让背书中的背书人名称与被背书人名称同名,且为收款人</p>
                    <p>质押：质押背书</p>
                    <p>保证：票据正面有出票保证人或承兑保证人,或票据背面出现保证背书</p>
                    <p>不一致(银票) ：出票人开户行和承兑人全称不属于同一个银行系统</p>
                    <p>不一致(财票) ：出票人开户行全称和承兑人全称不一致</p>
                    <p>不一致(商票) ：出票人全称与承兑人全称不一致</p>
                    <p>回购式贴现：被背书人为银行,且在票据到期之前,可将该票从银行手里回购回来</p>
                    <p>其他：其他瑕疵</p>
                  </div>
                  <icon class="icon question-icon" type="chengjie-wenti" />
                </el-tooltip>
              </div>
            </SelectGroup>
          </div>
          <!-- 背书手数 -->
          <div :class="[isMobile ? '' : 'pc-select']">
            <div class="gray-title">背书手数</div>
            <SelectGroup v-model="checkEndoresBill" :data-list="endoresBillList" class="select-group">
              <div :class="[isMobile ? '' : 'pc-select']">
                <div :class="[isMobile ? ' input-append-mobile' : 'input-append']">
                  <input
                    v-model="endoresNum"
                    class="input"
                    placeholder="背书手数"
                    @input="endoresInput($event, 'endoresNum')"
                  ><div class="append">手内</div>
                </div>
              </div>
            </SelectGroup>
          </div>
          <div v-if="isMobile" class="btn-box">
            <div class="btn-item reset" @click="resetClick">重置</div>
            <div class="btn-item confirm" @click="confirmClick">确定</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable no-magic-numbers */

import { DRAFT_TYPE, FACE_VALUE, TERM_OF_BILL, DRAFT_TYPE_VALUE_MAP, TERM_OF_BILL_ID_DUEDAYS_MAP, FACE_VALUE_ID_RANGE_MAP, BACK_DEFECT_TYPE_NAME_MAP, ENDORES_OPTIONS_TYPE_LIST, BACK_DEFECT_TYPE_MAP } from '@/mobile/constant'
import BigNumber from 'bignumber.js'
import SelectGroup from '../../../components/select-group.vue'
import { debounce } from '@/common/js/util'
export default {
  name: 'filter-box',
  components: { SelectGroup },
  props: {
    isMobile: Boolean,
    showVisible: Boolean,
    dataList: Object
  },
  data() {
    return {
      DRAFT_TYPE_VALUE_MAP,
      FACE_VALUE_ID_RANGE_MAP,
      TERM_OF_BILL_ID_DUEDAYS_MAP,
      BACK_DEFECT_TYPE_NAME_MAP,
      BACK_DEFECT_TYPE_MAP,
      draftTypeList: DRAFT_TYPE, // 票据类型列表
      faceValueList: FACE_VALUE, // 票面金额
      termOfBillList: TERM_OF_BILL, // 票据期限
      defectBillList: BACK_DEFECT_TYPE_NAME_MAP, // 瑕疵
      endoresBillList: ENDORES_OPTIONS_TYPE_LIST, // 背书手数

      checkDraftType: [0], // 选择的票据类型
      checkFaceValue: [0], // 选择的票面金额
      checkTermOfBill: [0], // 选择的票据期限
      checkDefectOfBill: [0], // 选择的瑕疵
      checkEndoresBill: [null], // 选择的背书手数
      minAmount: '', // 最小金额
      maxAmount: '', // 最大金额
      endoresNum: '', // 自定义背书手数
      rawDataList: null, // 需要过滤的数据
    }
  },
  computed: {
    scrollerHeight() {
      return this.isMobile ? `${document.body.clientHeight - 55}px` : ''// 自定义高度需求
    },
    // 需要过滤的票据类型
    filterDraftType() {
      return this.checkDraftType.map(item => DRAFT_TYPE_VALUE_MAP[item])
    },

    // 需要过滤的票面金额
    filterValue() {
      return FACE_VALUE_ID_RANGE_MAP[this.checkFaceValue[0]]
    },

    // 需要过滤的票据期限
    filterDueDays() {
      return TERM_OF_BILL_ID_DUEDAYS_MAP[this.checkTermOfBill[0]]
    },

    // 需要过滤的瑕疵
    filterFlaw() {
      return this.checkDefectOfBill.map(item => BACK_DEFECT_TYPE_MAP[item])
    },

    // 需要过滤的背书手数
    filterEndores() {
      return this.checkEndoresBill[0]
    },

    // 过滤后的数据
    filterrawDataList() {
      const { filterDraftType, filterValue, filterDueDays, filterEndores, filterFlaw, endoresNum, rawDataList, maxAmount, minAmount } = this
      let { data } = rawDataList
      // 过滤票据类型
      if (filterDraftType.indexOf('全部') === -1) {
        data = data.filter(item => filterDraftType.indexOf(item.acceptorType) !== -1)
      }

      // 过滤票面金额
      if (!(filterValue.min === 0 && filterValue.max === 99999)) {
        const { min, max, left, right } = filterValue
        data = data.filter(item => (right ? item.draftAmount <= max : item.draftAmount < max) && (left ? item.draftAmount >= min : item.draftAmount > min))
      }
      if (maxAmount !== '' || minAmount !== '') {
        const min = minAmount || 0
        const max = maxAmount || 99999
        data = data.filter(item => item.draftAmount <= max && item.draftAmount >= min)
      }

      // 过滤票据期限
      if (!(filterDueDays.min === 0 && filterDueDays.max === 99999)) {
        const { min, max, left, right } = filterDueDays
        data = data.filter(item => (right ? item.daysRemaining <= max : item.daysRemaining < max) && (left ? item.daysRemaining >= min : item.daysRemaining > min))
      }

      // 过滤瑕疵   filterFlaw选中的key
      if (filterFlaw[0]) {
        data = data.filter(item => {
          // item.defectsDto
          let defectsDtoList = Object.keys(item.defectsDto).filter(key => item.defectsDto[key])
          return filterFlaw.some(v =>
            defectsDtoList.includes(v) || (filterFlaw.includes('acceptDefectsWithout') && defectsDtoList.length === 0))
        })
      }
      // 过滤背书手数
      if (filterEndores != null) {
        data = data.filter(item => item.endorseCount === filterEndores)
      }
      // 过滤背书手数输入
      if (endoresNum) {
        data = data.filter(item => item.endorseCount && item.endorseCount < endoresNum)
      }
      return data
    }
  },

  watch: {
    checkFaceValue: {
      handler(val) {
        if (val[0] !== 0) { // 如果选择的不是全部，就把最小金额和最大金额置空
          this.minAmount = ''
          this.maxAmount = ''
        }
        !this.isMobile && this.confirmClick()
      },

      deep: true
    },
    checkDraftType() {
      !this.isMobile && this.confirmClick()
    },
    checkTermOfBill() {
      !this.isMobile && this.confirmClick()
    },
    checkDefectOfBill() {
      !this.isMobile && this.confirmClick()
    },

    checkEndoresBill: {
      handler(val) {
        if (val[0] !== null) { // 如果选择的不是全部，背书手数输入框清空
          this.endoresNum = ''
        }
        !this.isMobile && this.confirmClick()
      }
    }
  },

  created() {
    this.rawDataList = this.dataList
    // 最大最小票面金额输入
    this.amountInput = debounce((e, name) => {
      this[name] = e.target.value
      this.checkFaceValue = [0] // 票面金额恢复到选中全部
    }, 500)

    // 背书手数输入输入
    this.endoresInput = debounce((e, name) => {
      this[name] = e.target.value
      this.checkEndoresBill = [null] // 票面金额恢复到选中全部
    }, 500)
  },

  methods: {
    // 点击确认按钮
    confirmClick() {
      const { minAmount, maxAmount } = this
      if (minAmount || maxAmount) { // 如果有输入金额的
        const min = minAmount || 0
        const max = maxAmount || 99999
        const message = '最小金额应不大于最大金额'
        if (+max < +min) {
          this.isMobile ? this.$toast.success(message) : this.$message.success(message)
          return
        }
      }
      // 构造过滤后的数据 start
      let showDataList = { ...this.rawDataList }
      showDataList.data = this.filterrawDataList
      let totalAmount = 0
      showDataList.data.forEach(elem => {
        totalAmount = new BigNumber(totalAmount).plus(elem.draftAmount)
      })
      showDataList.totalAmount = totalAmount
      // 构造过滤后的数据 end
      this.$emit('filter-data', showDataList)
      // 移动端就需要关闭筛选框
      this.isMobile && this.$emit('update:show-visible', false)
    },

    // eslint-disable-next-line no-empty-function
    showFilterBox() {},

    hideFilterBox() {
      this.isMobile && this.$emit('update:show-visible', false)
    },

    // 点击重置按钮
    resetClick() {
      this.checkDraftType = [0]
      this.checkFaceValue = [0]
      this.checkTermOfBill = [0]
      this.checkDefectOfBill = [0] // 选择的瑕疵
      this.checkEndoresBill = [null] // 选择的背书手数
      this.minAmount = ''
      this.maxAmount = ''
      this.endoresNum = ''
    }
  }
}
</script>
