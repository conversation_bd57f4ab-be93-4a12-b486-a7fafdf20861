<style lang="scss" scoped>
.container {
  padding: 12px 16px 32px;

  .g-title {
    margin-bottom: 10px;
    font-size: 16px;
    line-height: 22.4px;

    &::before {
      height: 16px;
    }
  }
}

.content-image {
  position: relative;
  margin: 0 auto;
  border-radius: 2px;
  padding: 12px 16px;

  // width: 560px;
  // height: 372px;
  background: $color-FFFFFF;
  box-sizing: border-box;

  .pic-title-margin {
    margin-bottom: 12px;
  }

  .turn-over {
    position: absolute;
    top: 60px;
    right: 28px;
    border-radius: 2px;
    width: 126px;
    height: 32px;
    font-size: 14px;
    text-align: center;
    color: $color-FFFFFF;
    background: $color-text-primary;
    opacity: .8;
    line-height: 32px;
    cursor: pointer;

    .icon-swap {
      margin-right: 10px;
    }
  }

  .market-draft-image {
    overflow: auto;

    // width: 560px;
    // height: 306px;
    cursor: pointer;
  }
}

.content-detail {
  margin: 12px auto 0;
  padding: 16px 16px 0;
  width: 100%;

  // height: 250px;
  background: $color-FFFFFF;
  box-sizing: border-box;
}

.defect-icon {
  cursor: pointer;
  margin-left: 5px;
  color: $--color-primary;
}

.g-copy {
  margin-right: 9px;
  color: $color-text-light;
  cursor: pointer;
}

.footer-button {
  display: block;
  margin-top: 12px;
  width: 100%;
  height: 40px;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  color: $color-FFFFFF;
  background: $--color-primary;
  line-height: 40px;
}

.draft-image {
  width: 100%;
}

.draft-image-bg {
  width: 100%;
  height: 154px;
  background-repeat: repeat-y;
  background-size: contain;
  background-image: url("https://oss.chengjie.red/web/imgs/draft/draft-bg.png");
}

.panel {
  .panel-item {
    margin-bottom: 12px;

    .label {
      margin-bottom: 2px;
      font-size: 14px;
      line-height: 22px;
      color: $color-text-secondary;
    }

    .value {
      width: 100%;
      font-size: 14px;
      color: $color-text-primary;
      line-height: 20px;

      @include ellipsis;

      &.bold {
        font-weight: 600;
      }

      &.red {
        color: $--color-primary;
      }

      .draft {
        white-space: break-spaces;
      }
    }
  }
}

.card-body {
  position: relative;
  display: flex;
  padding-bottom: 12px;

  .panel {
    flex: 1;
    max-width: 50%;

    &:first-child {
      margin-right: 16px;
      border-right: 1px solid $color-F0F0F0;
      padding-right: 16px;
    }

    .panel-item {
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.shareForm {
  cursor: pointer;

  img {
    width: 100%;
  }
}
</style>

<template>
  <div v-if="draftData" class="container">
    <div class="content-image">
      <div class="g-title pic-title-margin">票面截图</div>

      <!--
        <div class="turn-over" @click="turnOver">
        <icon type="chengjie-swap" class="icon-swap" />
        <span>切换至{{ type === 'front' ? '背面' : '正面' }}</span>
        </div>
      -->
      <!-- 票据截图生成组件 -->

      <div
        v-if="!draftData.frontImageUrl && draftData.draftJson && (draftData.draftJson.front || draftData.draftJson.back)"
        class="market-draft-image-box"
      >
        <MarketDraftImage
          v-if="!draftImageUrl"
          ref="marketDraftImage"
          class="market-draft-image"
          :is-small-window="false"
          :data="draftData.draftJson"
          :type="type"
          style="position: fixed; top: 100%; right: 0;"
          @mounted="toImage"
        />
      </div>

      <div v-if="draftData.frontImageUrl" class="shareForm">
        <img
          v-if="isMobile"
          :src="draftData.frontImageUrl"
          @click="ImagePreview([draftData.frontImageUrl])"
        >
        <div v-else>
          <el-image
            :src="draftData.frontImageUrl"
            :preview-src-list="[draftData.frontImageUrl]"
          />
        </div>
      </div>
    </div>

    <div class="content-detail">
      <div class="g-title pic-title-margin">票面详情</div>

      <div class="panel">
        <div class="panel-item">
          <div class="label">票号</div>
          <div class="value">{{ draftData.draftNo }}</div>
        </div>
        <div class="panel-item">
          <div class="label">承兑人</div>
          <div class="value bold">{{ draftData.acceptorName }}</div>
        </div>
      </div>

      <section class="card-body">
        <div class="panel">
          <div class="panel-item">
            <div class="label">到期日</div>
            <div class="value">{{ formatTime(draftData.maturityDate, 'YYYY-MM-DD') }}</div>
          </div>
          <div class="panel-item">
            <div class="label">票面金额</div>
            <div class="value">
              <span v-if="draftData.draftAmount" class="red">{{ draftData.draftAmount }} 万</span>
              <span v-else>暂无提供</span>
            </div>
          </div>
          <div class="panel-item">
            <div class="label">每十万扣/利率</div>
            <div class="value">
              {{ draftData.lakhFee || '' }}/{{ draftData.annualInterest || '' }}
            </div>
          </div>
          <div class="panel-item">
            <div class="label">背书企业</div>
            <div class="value">
              {{ draftData.lastEndorse || '暂无提供' }}
            </div>
          </div>
        </div>

        <div class="panel">
          <div class="panel-item">
            <div class="label">开票日期</div>
            <div class="value">{{ formatTime(draftData.issueDate, 'YYYY-MM-DD') }}</div>
          </div>
          <div class="panel-item">
            <div class="label">剩余天数</div>
            <div class="value">{{ draftData.interestDays }} 天</div>
          </div>
          <div class="panel-item">
            <div class="label">瑕疵</div>
            <div class="value">
              <div
                v-if="draftData.originalDefects"
                class="red draft"
              >
                {{ (draftData.originalDefects.split('|')).filter(item => item).map(item => {
                  return QIAN_SHOU_BACK_DEFECT_TYPE_VALUE_MAP[item.split('_')[0]] + (QIAN_SHOU_BACK_DEFECT_TYPE_SHOW_NUM_MAP[item.split('_')[0]] ? `(${item.split('_')[1]})` : '')
                }).join('，') }}
              </div>
              <div v-else>无瑕疵</div>
            </div>
          </div>
          <div class="panel-item">
            <div class="label">背书手数</div>
            <div class="value">
              {{ draftData.endorseCount ? `${draftData.endorseCount}手` : '票方未提供' }}
            </div>
          </div>
        </div>
      </section>
    </div>

    <a class="footer-button" :href="`tel:${draftData.mobile}`">电话联系</a>
  </div>
</template>

<script>
/* eslint-disable no-magic-numbers */

import {
  DRAFT_TYPE_VALUE_MAP, // 承兑人类型 id 映射 名称
  IDENTIFY_TYPE_MAP, // 识别类型 id 映射 名称
  IDENTIFY_TYPE_COLOR_MAP, // 识别类型 id 映射 标签颜色
  QIAN_SHOU_BACK_DEFECT_TYPE_VALUE_MAP, // 票据瑕疵类型 id 映射 名称
  QIAN_SHOU_BACK_DEFECT_TYPE_SHOW_NUM_MAP, // 票据瑕疵类型 id 映射 是否显示数字
  BACK_DEFECT_TYPE_VALUE_MAP,
} from '@/constant'
import { formatTime, getDateSpace } from '@/common/js/date'
// import BigNumber from 'bignumber.js'
import { ImagePreview } from 'vant'
import { yuan2wan } from '@/common/js/number'
// 票据正面和背面截图生成组件
import MarketDraftImage from '@/views/components/market-draft-image/market-draft-image.vue'
// import DefectsNotifyDialog from '@/views/components/defects-notify-dialog/defects-notify-dialog.vue'
import html2canvas from 'html2canvas'
import wx from '@/mobile/utils/wx'

import stockApi from '@/mobile/apis/stock'

export default {
  name: 'draft-detail',

  components: {
    MarketDraftImage,
    // DefectsNotifyDialog,
  },

  props: {
    dataList: {
      type: [Array, Object],
      default: () => ({})
    }
  },

  data() {
    return {
      visible: false,
      DRAFT_TYPE_VALUE_MAP, // 承兑人类型 id 映射 名称
      IDENTIFY_TYPE_MAP, // 识别类型 id 映射 名称
      IDENTIFY_TYPE_COLOR_MAP, // 识别类型 id 映射 标签颜色
      QIAN_SHOU_BACK_DEFECT_TYPE_VALUE_MAP, // 票据瑕疵类型 id 映射 名称
      QIAN_SHOU_BACK_DEFECT_TYPE_SHOW_NUM_MAP, // 票据瑕疵类型 id 映射 是否显示数字
      draftData: null, // 票据数据
      type: 'front', // 图片类型，正面或背面。front/back
      draftImageUrl: null,
      isMobile: document.body.clientWidth <= 768,
    }
  },
  created() {
    document.title = '票据库存详情'
    // PC端 不是弹框展示的的详情
    if (!this.isMobile && this.$route.path === '/stock-detail') {
      const { id, shareKey } = this.$route.query
      // pc端跳转到web地址
      window.location.href = `/mobile.html/stock?type=h5&shareKey=${shareKey}&id=${id}`
    }
  },

  mounted() {
    this.init()
  },

  methods: {
    ImagePreview,

    // 初始化
    async init() {
      let data = this.$route.params // 从列表获取详情信息
      if (!data.id) { // 详情页刷新或分享，信息丢失重新获取数据并根据id筛选数据
        data = await this.getData()
      }
      if (data.draftJson && (typeof data.draftJson === 'string')) {
        data.draftJson = JSON.parse(data.draftJson)
      }
      this.draftImageUrl = null
      this.draftData = data
    },

    // 时间格式化
    formatTime,

    // 将元为单位的金额转为以万为单位
    yuan2wan,

    // 翻面
    turnOver() {
      this.type = this.type === 'front' ? 'back' : 'front'
    },

    // 复制成功
    copySuccess(type = 1) {
      if (type === 1) {
        this.$message.success('已复制票号到剪贴板')
      } else {
        this.$message.success('已复制承兑人名称到剪贴板')
      }
    },

    // 显示瑕疵提示弹窗
    showDefectDialog(obj) {
      this.$refs.defectDialog.toggle(obj)
    },

    toImage() {
      this.$nextTick().then(() => {
        html2canvas(document.querySelector('.market-draft-image')).then(canvas => {
          this.draftImageUrl = canvas.toDataURL('image/png')
          this.draftData.frontImageUrl = this.draftImageUrl
        })
      })
    },

    // 获取数据
    async getData() {
      const { shareKey, id } = this.$route.query
      const res = await stockApi.discernShareList({ shareKey })
      // 格式化数据 start
      let { data, code } = res
      let currentItem = null
      if (code === 200 && data) {
        if (this.isMobile) {
          wx.init({
            title: `${data.company}`,
            desc: '在户票据列表寻低价出'
          })
        }
        currentItem = data.data.filter(item => item.id === Number(id))[0]
        // console.log(currentItem)
        currentItem.mobile = data.mobile

        currentItem.daysRemaining = getDateSpace(currentItem.maturityDate, new Date())
        currentItem.acceptorType = DRAFT_TYPE_VALUE_MAP[currentItem.acceptorType]
        currentItem.defects = currentItem.defects ? this.getDefects(currentItem.defects) : '无瑕疵'
        currentItem.draftAmount = yuan2wan(currentItem.draftAmount)
      } else if (code === 1000) {
        // code 1001 msg 分享链接已失效
        this.isInvalid = true
        this.$toast.fail('分享链接已失效')
      } else {
        this.$toast.fail('网络异常')
      }
      // 格式化数据 end

      return currentItem
    },

    // 获取瑕疵
    getDefects(str) {
      const arr = str.split('|')
      let newArr = arr.map(item => {
        const defectItemArr = item.split('_') // 以下划线分开成数组，下标[0]瑕疵类型,[1]瑕疵次数，如果没有[1]不显示
        const defectName = BACK_DEFECT_TYPE_VALUE_MAP[defectItemArr[0]] // 获取瑕疵类型名称
        item = `${defectName}${defectItemArr.length >= 2 ? `[${defectItemArr[1]}]` : ''}` // 构造瑕疵
        return item
      })
      return newArr.toString()
    },
  }
}
</script>
