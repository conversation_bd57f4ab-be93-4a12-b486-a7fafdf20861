<!-- 票据库存列表 -->
<style lang="scss" scoped>
.stock {
  .header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px 18px;
    height: 64px;
    background: $color-FFFFFF;

    .sd-icon {
      font-size: 156px;
    }

    .erp {
      display: flex;
      align-items: center;
      font-size: 16px !important;
      font-weight: 500 !important;
      color: #000000 !important;

      img {
        margin-right: 12px;
      }

      i {
        margin-right: 8px;
        width: 28px;
        height: 28px;
      }
    }
  }

  .main {
    margin: auto;
    max-width: 1200px;
  }
}
</style>

<template>
  <div class="stock">
    <div v-if="!isMobile" class="header">
      <div class="erp">
        <icon type="chengjie-logo" />
        {{ WEB_NAME }}
      </div>
    </div>
    <div class="main">
      <InvalidLink v-if="isInvalid" />
      <template v-else>
        <!-- 网页分享 -->
        <StockList
          v-if="type === 'h5' || (type === 'text' && !isMobile)"
          :type-value="type"
          :data-list="data"
          :is-mobile="isMobile"
        />
        <!-- 图片分享 -->
        <StockImg
          v-if="type === 'image'"
          :is-mobile="isMobile"
          :data-list="data"
        />
        <!-- 文字分享 -->
        <StockText
          v-if="type === 'text' && isMobile"
          :data-list="data"
        />

        <!-- PC端下使用的票据详情弹窗 -->
        <StockDetailDialog v-if="!isMobile" ref="stockDetailDialog" />
        <!-- 新的分享版本 -->
        <NewReceiveOrderDetail v-if="!isMobile" ref="newSdReceiveOrderDetail" />
      </template>
    </div>
  </div>
</template>

<script>
import {
  formatTime, // 格式化时间
  getDateSpace // 时间距离，相距多少天
} from '@/common/js/date'
import { DRAFT_TYPE_VALUE_MAP, BACK_DEFECT_TYPE_VALUE_MAP } from '@/mobile/constant'
import BigNumber from 'bignumber.js'
import { yuan2wan } from '@/common/js/number'
import InvalidLink from './components/invalid-link.vue'
import StockImg from './components/stock-img.vue'
import StockList from './components/stock-list.vue'
import StockText from './components/stock-text.vue'
import stockApi from '@/mobile/apis/stock'
import wx from '@/mobile/utils/wx'
import { WEB_NAME } from '@/constant'
import StockDetailDialog from './components/stock-detail-dialog.vue'
import NewReceiveOrderDetail from '@/views/pages/market/components/receive-order-detail/new-sd-receive-order-detail.vue' // 接单详情组件
export default {
  name: 'stock',
  components: {
    StockImg,
    StockList,
    StockText,
    InvalidLink,
    StockDetailDialog,
    NewReceiveOrderDetail
  },

  data() {
    return {
      WEB_NAME,
      DRAFT_TYPE_VALUE_MAP,
      BACK_DEFECT_TYPE_VALUE_MAP,
      type: null, // h5 | image | text
      data: null, // 获取回来的数据
      isInvalid: false, // 是否失效
      isMobile: false, // 是否是移动端
      isShowDetail: false, // 是否打开详情弹窗
      // shareForm: null, // 来源 order: 已发布订单分享  null  库存分享
    }
  },

  watch: {
    $route: {
      handler(to) {
        if (to) {
          const { orderNo, shareKey, shareForm, id } = to.query
          // 如果是订单分享出来的校验参数orderNo 库存的检验参数id
          const newId = shareForm === 'order' ? orderNo : id
          // 在PC端的情况下若stock页面url存在id参数,表明该页面需要打开票据详情弹窗
          if (document.body.clientWidth > 768 && newId) {
            this.$nextTick().then(() => {
              // 如果是订单分享出来的直接打开新的订单弹框  库存分享的弹框是老的弹框（pc）
              const refDome = shareForm === 'order' ? 'newSdReceiveOrderDetail' : 'stockDetailDialog'
              this.$refs[refDome].init(newId)
            })
          } else if (newId && shareForm === 'order') {
            const baseUrl = process.env.VUE_APP_BASE_URL
            // 如果是订单分享出来的直接打开新的订单详情  库存分享的页面不做处理 库存详情页面需要传item参数 无法直接打开（H5）
            window.location.href = `${baseUrl}/mobile.html/order?type=h5&shareKey=${shareKey}&shareForm=${shareForm}&orderNo=${newId}`
          }
        }
      },
      immediate: true,
    },
  },

  created() {
    // eslint-disable-next-line no-magic-numbers
    this.isMobile = !(document.body.clientWidth > 768)
    document.title = '票据分享列表'
  },

  mounted() {
    this.getQuery()
    window.addEventListener('resize', this.resize)
  },

  beforeDestroy() {
    window.removeEventListener('resize', this.resize)
  },
  methods: {

    // 屏幕宽度改变的时候触发
    resize() {
      // eslint-disable-next-line no-magic-numbers
      this.isMobile = !(document.body.clientWidth > 768)
    },

    // 获取分享参数
    getQuery() {
      const { type, shareKey } = this.$route.query
      this.type = type
      if (shareKey) {
        this.getData()
      }
    },

    // 获取瑕疵
    getDefects(str) {
      const arr = str.split('|')
      let newArr = arr.map(item => {
        const defectItemArr = item.split('_') // 以下划线分开成数组，下标[0]瑕疵类型,[1]瑕疵次数，如果没有[1]不显示
        const defectName = BACK_DEFECT_TYPE_VALUE_MAP[defectItemArr[0]] // 获取瑕疵类型名称
        item = `${defectName}${defectItemArr.length >= 2 ? `[${defectItemArr[1]}]` : ''}` // 构造瑕疵
        return item
      })
      return newArr.toString()
    },

    // 获取数据
    async getData() {
      try {
        const { shareKey, shareForm } = this.$route.query
        const res = shareForm === 'order' ? await stockApi.orderShareList({ shareKey }) : await stockApi.discernShareList({ shareKey })
        // 格式化数据 start
        let { data, code } = res
        if (code === 200 && data) {
          if (this.isMobile) {
            wx.init({
              title: `${data.company}`,
              desc: '在户票据列表寻低价出'
            })
          }

          let totalAmount = 0
          data.data.forEach(elem => {
            elem.daysRemaining = getDateSpace(elem.maturityDate, new Date())
            elem.acceptorType = DRAFT_TYPE_VALUE_MAP[elem.acceptorType]
            elem.defects = elem.defects ? this.getDefects(elem.defects) : '无瑕疵'
            elem.draftAmount = yuan2wan(elem.draftAmount)
            totalAmount = new BigNumber(totalAmount).plus(elem.draftAmount)
          })
          data.totalAmount = totalAmount
          data.createAt = formatTime(data.createAt)
        }
        data.data.sort((a, b) => b.discernTime - a.discernTime) // 票据按识别时间倒序排序
        // 格式化数据 end
        this.data = data
      } catch (error) {
        if (error.data.code === 1000) {
        // code 1001 msg 分享链接已失效
          this.isInvalid = true
        } else {
          this.$toast.fail('网络异常')
        }
        // console.log('error :>> ', error)
      }
    },

    // 打开详情弹窗
    openDetail(val) {
      const { isShow } = val
      this.isShowDetail = isShow
    },
  },

}
</script>
