<style lang="scss" scoped>
  .draft-front-info {
    padding: 16px;
    background: $color-FFFFFF;

    .item-content {
      display: flex;
      font-size: 14px;
      flex: 1;
    }

    .item-nowrap {
      display: flex;
      margin-top: 12px;
    }

    .acceptor {
      display: flex;
      flex: 1;
    }

    .acceptor-name {
      margin-right: 6px;
      max-width: 90%;
    }

    .acceptor-type-tag {
      border-radius: 14px;
      padding: 2px 8px;
      width: 40px;
      height: 21px;
      font-size: 12px;
      font-weight: 600;
      white-space: nowrap;
      color: $font-color;
      background: $--color-primary-hover;
      line-height: 18px;
      box-sizing: border-box;
    }
  }

  .draft-info {
    margin-top: 12px;
    padding: 16px;
    background: $color-FFFFFF;
  }

  .item-label {
    display: inline-block;
    margin-right: 8px;
    margin-bottom: 0;
    font-size: 12px;
    text-align: right;
    color: $color-text-secondary;
  }

  .items-bg {
    display: flex;
    margin-top: 12px;
    border-radius: 2px;
    padding: 12px;
    width: 100%;
    background-color: $color-F5F6F8;

    .item {
      margin-top: 0;
      border-left: 1px solid $color-D9D9D9;
      padding: 0 8px;

      &:first-child {
        border-left: none;
        padding-left: 0;
        width: 32%;
      }
    }

    .item-flex {
      flex: 1;
    }
  }

  .show-defect {
    // overflow: hidden;
    // text-overflow: ellipsis;
    // white-space: nowrap;
    color: $color-warning;

    // cursor: pointer;
  }

  .bold {
    font-weight: bold;
  }
</style>

<template>
  <section>
    <div class="draft-front-info">
      <div class="g-title-small title">票面信息</div>
      <div class="item-nowrap">
        <div class="item-label">承兑人</div>
        <div class="item-content acceptor">
          <span class="acceptor-name">{{
            draftInfo.acceptorName || "--"
          }}</span>
          <span v-if="!isNull(draftInfo.acceptorType)" class="acceptor-type-tag">{{
            DRAFT_TYPE_VALUE_MAP[draftInfo.acceptorType]
          }}</span>
        </div>
      </div>
      <div class="items-bg">
        <div class="item">
          <div class="item-label">
            <span>票面金额</span>
          </div>
          <div class="item-content bold">
            {{ yuan2wan(draftInfo.draftAmount) || "--" }} 万
          </div>
        </div>
        <div class="item item-flex">
          <div class="item-label">到期日</div>
          <div class="item-content">
            {{
              `${draftInfo.maturityDate || "--"}（剩${
                draftInfo.interestDays || "--"
              }天）`
            }}
          </div>
        </div>
        <div class="item item-flex">
          <div class="item-label">背书手数</div>
          <div class="item-content">
            {{
              isNull(draftInfo.endorseCount)
                ? "票方未提供"
                : draftInfo.endorseCount
            }}
          </div>
        </div>
      </div>

      <div class="item-nowrap">
        <div class="item-label">票号</div>
        <div class="item-content">
          {{ (draftInfo.draftNo) | formatDraftNo }}
        </div>
      </div>
      <div class="item-nowrap">
        <div class="item-label">瑕疵</div>
        <div v-if="showDefects" class="item-content show-defect">
          {{ showDefects }}
        </div>
        <div v-else>无瑕疵</div>
      </div>
    </div>
    <!-- 报价信息 -->
    <div class="draft-info">
      <div class="g-title-small title">报价信息</div>
      <div class="items-bg">
        <div class="item" style="width: 30%;">
          <div class="item-label">实付金额</div>
          <div class="item-content bold text-primary">
            {{ draftPaymentAmount || "--" }} 万
          </div>
        </div>
        <div class="item">
          <div class="item-label">每十万扣款 / 年利率</div>
          <div class="item-content bold">
            {{
              `${draftInfo.lakhFee || 0} 元 / ${
                draftInfo.annualInterest || 0
              }%`
            }}
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
// 判断是否非空值
import { isNull } from '@/common/js/util'
import {
  DRAFT_TYPE_VALUE_MAP,
  BACK_DEFECT_TYPE_NAME_MAP,
  BACK_DEFECT_TYPE_KEY_MAP
} from '@/constant'
import { yuan2wan } from '@/common/js/number'
import {
  lakhDeductionMath, // 每十万扣款计算 => 年化利率 和 到账金额
} from '@/common/js/draft-math'
import BigNumber from 'bignumber.js' // 交易凭证费用

export default {
  name: 'draft-info',
  filters: {

    /**
* 票号后N位替换为*
* @param {String} no 票号
* @param {Number} LENGTH 替换的长度
* @param {String} replaceStr 替换的符号
* @returns {String} no 替换后的票号
*/
    formatDraftNo(no = '', LENGTH = 9, replaceStr = '*') {
      if (no) {
        const reg = new RegExp(`(\\d{${LENGTH}})$`)
        while (LENGTH > 1) {
          replaceStr = `${replaceStr}*`
          LENGTH--
        }
        no = `${no.replace(reg, replaceStr)}`
      }
      return !no ? '--' : no
    }
  },
  props: {
    draftInfo: {
      type: Object
    }
  },
  data() {
    return {
      DRAFT_TYPE_VALUE_MAP,
      showDefects: '', // 瑕疵
    }
  },
  computed: {
    // 实付金额 (算上交易凭证费用，平台手续费)
    draftPaymentAmount() {
      const {
        draftAmount = 0, // 票据金额（元）
        lakhFee = 0, // 每十万扣款
        interestDays = 0 // 利息天数
      } = this.draftInfo
      const { tradeCertificate } = this // 是否需要交易凭证
      const tradeCertificateFee = 5 // 交易凭证费用
      const { receivedAmount } = lakhDeductionMath(draftAmount || 0, lakhFee || 0, interestDays || 0)

      return yuan2wan(
        (new BigNumber(receivedAmount || 0)
          .plus(tradeCertificate ? tradeCertificateFee : 0)),
        { digits: 6, parseNumber: false, mode: 'round' }
      )
    },
  },
  created() {
    // 处理瑕疵数据
    this.setDefect(this.draftInfo.defects || {})
  },
  methods: {
    isNull,
    yuan2wan,
    // 处理瑕疵
    setDefect(defects) {
      let showDefects = ''
      for (let i in defects) {
        if (defects[i] && BACK_DEFECT_TYPE_KEY_MAP[i]) {
          // 类型为 回出票人abca，回收款人，背书回头，背书重复，质押 时显示次数
          if (i === BACK_DEFECT_TYPE_NAME_MAP.ABCA.key
          || i === BACK_DEFECT_TYPE_NAME_MAP.HUI_SHOU_KUAN_REN.key
          || i === BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_HUI_TOU.key
          || i === BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_CHONG_FU.key
          || i === BACK_DEFECT_TYPE_NAME_MAP.ZHI_YA.key
          ) {
            showDefects = `${showDefects ? `${showDefects}，` : ''}${BACK_DEFECT_TYPE_KEY_MAP[i]}[${defects[i]}]`
            // 类型为其它时 显示其它瑕疵票描述
          } else if (i === BACK_DEFECT_TYPE_NAME_MAP.OTHER.key && defects.defectsOtherDesc) {
            // 只有在tooltips上才显示其他【】具体内容
            showDefects = `${showDefects ? `${showDefects}，` : ''}${BACK_DEFECT_TYPE_KEY_MAP[i]}[${defects.defectsOtherDesc}]`
          } else {
            showDefects = `${showDefects ? `${showDefects}，` : ''}${BACK_DEFECT_TYPE_KEY_MAP[i]}`
          }
        }
      }
      this.showDefects = showDefects
    }
  }
}
</script>
