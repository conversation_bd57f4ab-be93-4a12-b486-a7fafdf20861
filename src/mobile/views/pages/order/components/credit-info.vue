<!-- 对方信用信息 -->
<style lang="scss" scoped>
  .credit-info-container {
    padding: 16px;
    background: $color-FFFFFF;

    .icon-box {
      display: flex;
      align-items: flex-end;

      .icon-level {
        width: 94px;
        height: 26px;
      }

      .icon-tips {
        margin-left: 8px;
      }
    }

    .high-light {
      font-weight: 600;
    }

    ::v-deep .desc-list .row .item-column-2:last-child {
      padding-left: 16px;
    }
  }

  .credit-info-receive-order {
    .icon-box {
      .icon-level {
        width: 80px;
        height: 22px;
      }
    }

    .item-value {
      font-size: 14px;

      // font-weight: bold;
    }
  }

  .receive-order {
    margin-top: 12px;

    .level-item {
      display: flex;
      align-items: flex-end;
      flex-wrap: nowrap;

      .item-label {
        margin-right: 8px;
      }

      .icon-level {
        width: 94px;
        height: 26px;
      }
    }

    &-list {
      display: flex;
      margin-top: 12px;
      border-radius: 2px;
      padding: 12px;
      background-color: $color-F5F6F8;

      .item {
        border-left: 1px solid $color-D9D9D9;
        padding: 0 8px;

        &:first-child {
          border-left: none;
          padding-left: 0;
        }

        &:not(:first-child) {
          flex: 1;
        }

        &-label {
          font-size: 12px;
          color: $color-text-secondary;
        }

        &-value {
          margin-top: 4px;
          font-size: 14px;
        }
      }
    }
  }

  .bold {
    font-weight: bold;
  }
  </style>

<template>
  <div :class="['credit-info-container', `credit-info-${whereToUse}`]">
    <div class="g-title-small title">对方信用信息</div>

    <div v-if="whereToUse === 'receive-order'" class="receive-order">
      <!--
        <p class="level-item">
        <span class="item-label">信用分等级</span>
        <span class="item-value icon-box">
        <img :src="CREDIT_LEVEL_ICON_MAP[levelItem.value]" class="icon-level">
        <span class="icon-tips">(超过 <span class="high-light text-primary">{{ levelItem.userProportion }}%</span> 的用户) </span>
        </span>
        </p>
      -->
      <ul class="receive-order-list">
        <li
          v-for="item in list"
          :key="item.id"
          class="item"
        >
          <div class="item-label">
            {{ item.label }}
          </div>
          <div :class="['item-value', `${item.isBold ? 'bold' : ''}`]">
            {{ item.value }}
          </div>
        </li>
      </ul>
    </div>
    <div v-else class="main">
      <ul class="desc-list">
        <li
          v-for="row in list"
          :key="row.id"
          class="row"
        >
          <div
            v-for="item in row.list"
            :key="item.id"
            :class="['item', `item-column-${row.list.length || 0}`, item.itemClass ? item.itemClass : '']"
          >
            <div class="item-label">
              {{ item.label }}
            </div>
            <div v-if="item.key === 'level'" class="item-value icon-box">
              <el-image :src="CREDIT_LEVEL_ICON_MAP[item.value]" class="icon-level" />
              <span class="icon-tips">(超过 <span class="high-light text-primary">{{ item.userProportion }}%</span> 的用户) </span>
            </div>
            <div v-else class="item-value" :class="[`${item.isBold ? 'bold' : ''}`]">
              {{ item.value }}
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import {
  CREDIT_LEVEL_ICON_MAP, // 等级id映射图标
} from '@/constants/credit'
export default {
  name: 'credit-info',
  props: {
    draftInfo: Object, // 票据信息 只需要对方信用信息和统计信
    // 在哪里使用
    whereToUse: {
      default: 'draft-detail', // draft-detail: 在普通订单详情使用, agent-draft-detail: 定向订单详情， receive-order: 在接单弹窗
      type: String
    }
  },

  data() {
    return {
      // 信用等级图标
      CREDIT_LEVEL_ICON_MAP,
      // 排版列表
      list: [],
      levelItem: {}
    }
  },

  watch: {
    draftInfo: {
      handler(val) {
        this.setListData(val)
      },
      immediate: true,
      deep: true
    }
  },

  methods: {
    // 设置列表数据
    setListData(val) {
      const { currentCreditType, userProportion } = (val?.oppositeCreditDTO || {})
      const { confirmRate, payRate, endorseRate, signRate, lightBrokeTime, middleBrokeTime, heavyBrokeTime } = (val?.oppositeStatCorpTradeRecordDTO || {})
      const levelItem = {
        label: '信用分等级',
        key: 'level',
        value: currentCreditType || 0,
        userProportion: userProportion || 0
      }
      const confirmRateItem = {
        label: '确认率',
        value: `${confirmRate || 0}%`,
        key: 'confirmRate',
        isBold: true,
      }
      const payRateItem = {
        label: '打款率',
        value: `${payRate || 0}%`,
        key: 'payRate',
        isBold: true,
      }
      const endorseRateItem = {
        label: '背书率',
        value: `${endorseRate || 0}%`,
        key: 'endorseRate',
        isBold: true,
      }
      const signRateItem = {
        label: '签收率',
        value: `${signRate || 0}%`,
        key: 'signRate',
        isBold: true,
      }
      const brokeTimeItem = {
        label: '违约次数 (轻度 / 中度 / 重度)',
        key: 'brokeTime',
        value: `${lightBrokeTime || 0}次 / ${middleBrokeTime || 0}次 / ${heavyBrokeTime || 0}次`,
      }

      switch (this.whereToUse) {
        case 'receive-order':
          this.levelItem = levelItem
          this.list = [brokeTimeItem, confirmRateItem, endorseRateItem]
          break
        case 'agent-draft-detail':
          this.list = [
            {
              id: 1,
              list: [levelItem, confirmRateItem]
            }, {
              id: 2,
              list: [brokeTimeItem, endorseRateItem]
            }
          ]
          break
        case 'draft-detail':
          this.list = [
            {
              id: 1,
              list: [levelItem, payRateItem]
            }, {
              id: 2,
              list: [brokeTimeItem, signRateItem]
            }
          ]
          break
        default:
          break
      }
    }
  }
}
</script>
