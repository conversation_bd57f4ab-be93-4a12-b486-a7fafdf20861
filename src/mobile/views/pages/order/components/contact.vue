<style lang="scss" scoped>
.contact-wrapper {
  padding: 16px;
  background: $color-FFFFFF;

  .phone {
    margin-top: 12px;
    font-size: 14px;
  }

  .no-data {
    margin-top: 12px;
    padding: 6px 16px;
    font-size: 12px;
    color: $color-text-primary;
    background-color: #FFF4F5;
  }

  .warning-icon {
    color: $color-warning;
  }
}

.van-button--primary {
  margin-top: 10px;
  border: 1px solid $--color-primary;
}

.van-button--plain.van-button--primary {
  color: $--color-primary;
}

.corpinvite-code {
  margin-bottom: 16px;
}
</style>

<template>
  <div class="contact-wrapper">
    <div v-if="draftInfo.corpInviteCode" class="corpinvite-code">
      <div class="g-title-small title">定向码</div>
      <div>
        {{ draftInfo.corpInviteCode }}
        <Copy :content="draftInfo.corpInviteCode" />
      </div>
    </div>
    <!--
      <div class="g-title-small title">联系方式</div>
      <div v-if="draftInfo.mobileList && draftInfo.mobileList.length >= 1" class="phone">
      <div
      v-for="item in draftInfo.mobileList"
      :key="item.id"
      >
      <a :href="`tel:${item.mobile}`" class="contact-btn">
      {{ item.mobile }}
      <icon class="contact-icon" type="chengjie-contact" />
      </a>
      <Copy :content="item.mobile " />
      </div>
      <Button
      v-copy="{ value: copyCurrentItem(draftInfo), onSuccess, onError }"
      size="small"
      plain
      type="primary"
      @click="previewImage(imageList[0])"
      >
      复制联系方式
      </Button>
      </div>
      <div v-else class="no-data">
      <icon type="chengjie-exclamation-circle" class="warning-icon" />
      票方未开启展示联系方式，请联系您的客户经理！
      </div>
    -->
  </div>
</template>

<script>
import Copy from '@/views/components/common/copy/copy.vue'
// import { Button } from 'vant'
export default {
  name: 'contact-way',
  components: {
    Copy,
    // Button
  },
  props: {
    draftInfo: Object, // 票据信息 只需要对方信用信息和统计信
  },
  data() {
    return {
      copyMobile: ''
    }
  },
  computed: {
    // 复制内容
    copyCurrentItem() {
      return function(data) {
        // 处理复制手机号
        this.copyMobile = data.mobileList.length >= 1 ? data.mobileList.map(item => item.mobile).join(',') : ''
        return this.copyMobile
      }
    },

  },
  methods: {
    // 复制成功
    onSuccess() {
      this.$message.success('复制成功')
    },
    // 复制失败
    onError() {
      this.$message.error('复制失败，请重试')
    },
  }
}
</script>
