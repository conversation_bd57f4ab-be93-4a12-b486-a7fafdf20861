
<!-- 票面截图 -->
<style lang="scss" scoped>
  .draft-image {
    display: flex;
    justify-content: space-between;
    padding: 16px;

    // height: 389px;
    background: $color-FFFFFF;
    flex-direction: column;
    flex: auto;

    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      // font-size: 16px;
      // color: $color-text-primary;

      &::before {
        height: 16px;
      }

      .el-button {
        padding: 6px 7px;
        font-size: 14px;
      }
    }

    .front-image {
      position: relative;
      overflow: hidden;
      margin-top: 12px;
      width: 100%;

      // height: 299px;

      .options {
        position: absolute;
        top: 10px;
        right: 10px;
        display: flex;
      }

      .copy-image,
      .view-num {
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 2px;
        height: 34px;
        font-size: 14px;
        text-align: center;
        color: $color-FFFFFF;
        background: rgb(38 38 38 / 90%);
        cursor: pointer;

        &-text {
          margin-right: 8px;
          color: $color-warning;
        }
      }

      .copy-image {
        width: 108px;

        .sdicon-copy {
          margin-right: 10px;
          font-size: 18px;
        }
      }

      .view-num {
        margin-left: 10px;
        width: 158px;
      }

      .ticket-image {
        max-width: 100%;
        cursor: pointer;
      }

      .el-image {
        display: none;
      }
    }

    .ticket-holder-address {
      margin-top: 12px;
      font-size: 14px;
      text-align: center;
      color: $color-text-primary;

      .address {
        font-weight: 600;
        color: $color-text-primary;
      }
    }
  }

  .ml12 {
    margin-left: 12px;
  }

  .copy-img {
    position: fixed;
    top: -9999px;
    left: -9999px;
    z-index: -1;
    opacity: 0;
    pointer-events: none;
  }
</style>

<template>
  <div class="draft-image">
    <div class="g-title-small title">
      票面截图
      <Button
        size="mini"
        plain
        type="danger"
        @click="previewImage(imageList[0])"
      >
        全屏查看票面
      </Button>
    </div>
    <div class="front-image">
      <img
        ref="draftImage"
        class="ticket-image"
        :src="imageList[0]"
        alt="票面截图"
        crossorigin="Anonymous"
        @click="previewImage(imageList[0])"
      >

      <!--
        <el-image
        ref="previewImage"
        :src="imageList[0]"
        :preview-src-list="imageList"
        />
      -->

      <div class="options">
        <div class="copy-image" @click="copyImg">
          <icon class="icon sdicon-copy" type="chengjie-copy" />
          <span>复制票面</span>
        </div>

        <div v-if="showViewCount" class="view-num">
          <span class="view-num-text">{{ viewCount }}</span>人正在浏览此票
          <icon class="icon icon-close ml12" type="chengjie-close" @click="() => showViewCount = false" />
        </div>
      </div>
    </div>
    <div class="ticket-holder-address">
      <span>票方所在地：</span>
      <span class="address">{{ address }}</span>
    </div>

    <!-- 复制票图使用 -->
    <!-- TODO: 注意是否会有图片缓存问题 -->
    <img
      v-if="imageList[0]"
      ref="draftImageCopy"
      :src="imageList[0]"
      alt="票面截图"
      class="copy-img"
      crossorigin="Anonymous"
    >
  </div>
</template>

<script>
import { getBase64Image, base64ToFile } from '@/common/js/util'
import { browserType } from '@/common/js/env'
import { Button, ImagePreview } from 'vant'
export default {
  name: 'copy-image',
  components: { Button },
  props: {
    // 票面截图
    imageList: Array,
    // 正在浏览人数
    viewCount: {
      type: String,
      default: '1'
    },
    // 票方所在地
    address: {
      type: String,
      default: ''
    },
  },

  data() {
    return {
      showViewCount: true
    }
  },
  beforeDestroy() {
    this.showViewCount = true
  },

  methods: {
    // 预览图片
    previewImage(url) {
      // this.$refs.previewImage.openViewer()
      ImagePreview({ images: [url], closeable: true })
    },

    // 复制图片
    async copyImg() {
      try {
        const draftImage = this.$refs?.draftImageCopy
        if (!draftImage) return
        const isIe = browserType.indexOf('IE') !== -1
        const img = await getBase64Image(draftImage)
        if (!isIe) {
          // 除IE外的浏览器的复制方法
          if (navigator.clipboard) {
            const file = await base64ToFile(img)
            const blob = new Blob([file], { type: 'image/png' })
            await navigator.clipboard.write([
              // eslint-disable-next-line no-undef
              new ClipboardItem({
                [blob.type]: blob
              })
            ]).then(() => {
              this.$message.success('复制成功！')
            })
          } else if (window.require) {
            const { clipboard, nativeImage } = window.require('electron')
            const image = nativeImage.createFromDataURL(img) // res为base64图片数据
            clipboard.writeImage(image)
            this.$message.success('复制成功')
          } else {
            this.$message.warning('浏览器不支持复制票据，可在票据图片上点击右键复制')
          }
        } else {
          // IE浏览器不支持file方法，可以直接转blob
          // IE支持的方法
          // try {
          // // const blob = await this.dataURLtoBlob(img)
          // // console.log(window.clipboardData)
          // // console.log(window.clipboardData.setData('text', file))
          // } catch (err) {
          //   console.log(err)
          // }

          this.$message.warning('浏览器不支持复制票据，可在票据图片上点击右键复制')
        }
      } catch (err) {
        // console.log(err)
        this.$message.warning('浏览器不支持复制票据，可在票据图片上点击右键复制')
      }
    },

    dataURLtoBlob(dataurl) {
      let arr = dataurl.split(',')
      // 注意base64的最后面中括号和引号是不转译的
      let arr1 = arr[1].substring(0, arr[1].length - 2)
      let mime = arr[0].match(/:(.*?);/)[1]
      let bstr = atob(arr1)
      let n = bstr.length
      let u8arr = new Uint8Array(n)
      while (n) {
        n -= 1
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new Blob([u8arr], {
        type: mime
      })
    }
  },
}
</script>
