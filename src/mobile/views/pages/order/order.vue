<style lang="scss" scoped>
  .order-wrapper {
    height: 100vh;
    background: $color-FFFFFF;

    .header-title {
      border-bottom: 1px solid #EAEAEA;
      padding: 16px;
      font-size: 16px;
      font-weight: bold;
      color: $color-text-primary;
    }

    .tips-p {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 100px;
      font-size: 16px;
      color: $color-text-secondary;
    }
  }
</style>

<template>
  <div class="order-wrapper">
    <template v-if="draftInfo">
      <div class="header-title">
        <span>订单</span>
        <template v-if="orderNo">
          <span>{{ orderNo }}</span>
          <Copy :content="orderNo" />
        </template>
      </div>
      <!-- 票面截图 -->
      <DraftImage
        :image-list="imageList"
        :view-count="draftInfo.viewCount"
        :address="`${draftInfo.provinceName || ''}${draftInfo.cityName || ''}`"
      />
      <!-- 票面信息 -->
      <DraftInfo :draft-info="draftInfo" />
      <!-- 对方信用信息 -->
      <CreditInfoCard :draft-info="draftInfo" where-to-use="receive-order" />
      <!-- 联系方式 -->
      <Contact :draft-info="draftInfo" />
    </template>
    <!-- 暂无数据提示 -->
    <p v-else class="tips-p">{{ tipsText }}</p>
  </div>
</template>

<script>
import DraftImage from './components/draft-image.vue' // 票面截图组件
import DraftInfo from './components/draft-info.vue'
import CreditInfoCard from './components/credit-info.vue' // 对方信用信息
import Contact from './components/contact.vue' // 联系方式
import Copy from '@/views/components/common/copy/copy.vue'

// 票据正面和背面截图生成
import imgGenerator from '@/views/components/market-draft-image-generator/market-draft-image-generator.js'
import stockApi from '@/mobile/apis/stock.js'
import mobileDevice from '@/mobile/mixins/mobile-device'
import wx from '@/mobile/utils/wx'
export default {
  name: 'order',
  components: { DraftImage, DraftInfo, CreditInfoCard, Contact, Copy },
  mixins: [mobileDevice],
  data() {
    return {
      tipsText: '暂无数据',
      draftInfo: null, // 票据信息
      imageList: [], // 图片数组
    }
  },
  computed: {
    // 订单号
    orderNo() {
      return this.draftInfo.orderNo
    }
  },
  created() {
    // this.isMobile = !(document.body.clientWidth > 768)
    // console.log('this.isMobile', this.isMobile)
    if (this.isMobile) {
      // 移动端，请求数据
      this.getOrderData()
    } else {
      const { orderNo, shareKey } = this.$route.query
      // pc端跳转到web地址
      window.location.href = `/mobile.html/stock?type=h5&shareKey=${shareKey}&shareForm=order&orderNo=${orderNo}`
      // window.location.href = `/user-center/ticket-info?order?type=h5&shareKey=${shareKey}&shareForm=erp&orderNo=${orderNo}`
    }
  },
  methods: {
    // 数据格式化处理
    initData(data) {
      // 处理截图
      this.handleImageData(data)
    },

    // 生成票面图片
    async generateScreenshot(draftJson) {
      const recognitionData = typeof draftJson === 'string' ? JSON.parse(draftJson) : draftJson
      await this.$nextTick()
      let frontImage
      const frontPromise = imgGenerator.screenshot(recognitionData, 'front', 'canvas').then(frontData => {
        frontImage = frontData.toDataURL()
      })
      await Promise.all([frontPromise])
      this.imageList = [frontImage]
      return [frontImage]
    },
    // 处理图片数据
    handleImageData(data) {
      if (data.draftJson) {
        this.generateScreenshot(data.draftJson) // 票据截图
      } else {
        this.imageList = [data.frontImageUrl]
      }
    },
    // 请求订单详情数据
    async getOrderData() {
      const { orderNo } = this.$route.query
      try {
        let result = await stockApi.queryDraftOrderInfo(orderNo)
        if (this.isMobile) {
          wx.init({
            title: '您的好友分享的票据',
            desc: '在户票据寻低价出'
          })
        }
        this.draftInfo = result
        this.initData(result)
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log(error)
      }
    }
  }
}
</script>
