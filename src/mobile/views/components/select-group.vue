<!-- 筛选框组 -->
<style lang="scss" scoped>
.select-item {
  border: 1px solid $color-D9D9D9;
  border-radius: 2px;
  width: 80px;
  height: 30px;
  font-size: 12px;
  text-align: center;
  color: $color-text-primary;
  line-height: 28px;
  cursor: pointer;
}

.btn {
  border: 1px solid $color-D9D9D9;
  color: $color-text-primary;
}

.active {
  border: 1px solid $--color-primary;
  color: $--color-primary;
  background-color: $--color-primary-hover;
}
</style>

<template>
  <div>
    <div
      v-for="item in dataList"
      :key="item.id"
      :lable="item.id"
      :class="['select-item', {active: checkActive(item.id)}]"
      @click.stop="changeCheckList(item.id)"
    >
      {{ item.name }}
    </div>
    <slot />
  </div>
</template>

<script>
export default {
  name: 'select-group',
  model: {
    prop: 'currValArr',
    event: 'click'
  },
  props: {
    currValArr: Array,
    dataList: { // 传入可选择的数据
      type: [Array, Object],
      default: () => ({})
    },
    isMultiply: { // 是否是多选。默认为false：单选；true：多选
      type: Boolean,
      default: false
    },
  },

  data() {
    return {
      checkList: [], // 选择的数据
    }
  },

  computed: {
    checkActive() {
      return function(item) {
        return this.currValArr.indexOf(item) !== -1
      }
    }
  },

  watch: {
    checkList: {
      handler(val) {
        this.$emit('click', val)
      }
    },
  },

  methods: {
    // 供子组件操作选择的数据
    changeCheckList(item) {
      // 是多选 且 当前选项不是全部 且 选项中不包含全部 才走多选逻辑
      this.isMultiply && item !== 0 && this.currValArr.indexOf(0) === -1 ? this.multipleChoice(item) : this.singleChoice(item)
    },

    // 多选逻辑
    multipleChoice(item) {
      if (this.checkList.indexOf(item) === -1) {
        // 当前数组中没有该值则push到数组
        this.checkList.push(item)
      } else {
        // 当前数组中有该值，找到该值下标并删除
        this.checkList.splice(this.checkList.indexOf(item), 1)
        if (!this.checkList.length) {
          this.checkList = [0] // 多选 且 选项全部被取消时，应该默认回到选择【全部】
        }
      }
    },

    // 单选逻辑
    singleChoice(item) {
      this.checkList = [item] // 将该值设为当前数组的第一项
    },
  }
}
</script>
