/* global wx */
import commonApi from '../apis/common'

const imgUrl = 'https://oss.chengjie.red/web/imgs/mobile/share-thumbnail.jpg'
const jsApiList = [
  // 'updateAppMessageShareData', // 自定义“分享给朋友”及“分享到QQ”按钮的分享内容（1.4.0）
  // 'updateTimelineShareData', // 自定义“分享到朋友圈”及“分享到QQ空间”按钮的分享内容（1.4.0）
  'onMenuShareTimeline', // 获取“分享到朋友圈”按钮点击状态及自定义分享内容接口（即将废弃）
  'onMenuShareAppMessage', // 获取“分享给朋友”按钮点击状态及自定义分享内容接口（即将废弃）
  'onMenuShareQQ', // 获取“分享到QQ”按钮点击状态及自定义分享内容接口（即将废弃）
  'onMenuShareQZone', // 获取“分享到QQ空间”按钮点击状态及自定义分享内容接口（即将废弃）
]
const getOptions = (options, nema) => {
  const newOptions = Object.assign({
    title: document.title, // 分享标题
    desc: '', // 分享描述
    link: window.location.href, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
    imgUrl, // 分享图标
    success() {
      // eslint-disable-next-line no-console
      console.log(`${nema}成功`)
    },
    cancel() {
      // eslint-disable-next-line no-console
      console.log(`${nema}失败`)
    }
  }, options)
  return newOptions
}

// 通过 ready 接口处理成功验证
const onReady = options => {
  wx.ready(() => {
    wx.onMenuShareTimeline(getOptions(options, '分享到朋友圈'))
    wx.onMenuShareAppMessage(getOptions(options, '分享给朋友'))
    wx.onMenuShareQQ(getOptions(options, '分享到QQ'))
    wx.onMenuShareQZone(getOptions(options, '分享到QQ空间'))
  })
}
// 初始化签名
const init = async options => {
  const url = window.location.href.replace(/#.*/, '') // 当前网页的URL，不包含#及其后面部分
  const res = await commonApi.getJsSignature({ url })
  wx.config({
    debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
    appId: res.appId, // 必填，公众号的唯一标识
    timestamp: res.timestamp,
    nonceStr: res.nonceStr, // 必填，生成签名的随机串
    signature: res.signature, // 必填，签名
    jsApiList, // 必填，需要使用的JS接口列表
  })
  onReady(options)
}

export default {
  init
}
