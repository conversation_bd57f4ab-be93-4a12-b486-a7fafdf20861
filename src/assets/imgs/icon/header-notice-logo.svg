<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="12" cy="12" r="12" fill="url(#paint0_linear_308_1675)"/>
<circle cx="12" cy="12" r="11" fill="url(#paint1_angular_308_1675)"/>
<g filter="url(#filter0_b_308_1675)">
<circle cx="12" cy="12" r="9" fill="url(#paint2_linear_308_1675)" fill-opacity="0.4"/>
</g>
<mask id="mask0_308_1675" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="3" y="3" width="18" height="18">
<circle cx="12" cy="12" r="9" fill="url(#paint3_linear_308_1675)"/>
</mask>
<g mask="url(#mask0_308_1675)">
<g filter="url(#filter1_f_308_1675)">
<ellipse cx="11.5501" cy="4.35" rx="4.95" ry="1.35" fill="white"/>
</g>
</g>
<path d="M11.25 16.52H5.46V7.38H11.22V8.04H7.65V15.88H9.06V12.02C9.06 11.88 8.99 11.81 8.85 11.81H8.38V11.15H10.63C10.8033 11.15 10.95 11.2133 11.07 11.34C11.19 11.46 11.25 11.6067 11.25 11.78V16.52ZM12.169 16.52V7.37H17.349C17.5223 7.37 17.669 7.43333 17.789 7.56C17.909 7.68 17.969 7.82667 17.969 8V16.52H12.169ZM14.359 15.87H15.789V8.24C15.789 8.1 15.719 8.03 15.579 8.03H14.359V15.87Z" fill="#FCF6EE"/>
<defs>
<filter id="filter0_b_308_1675" x="-1" y="-1" width="26" height="26" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_308_1675"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_308_1675" result="shape"/>
</filter>
<filter id="filter1_f_308_1675" x="2.6001" y="-1" width="17.8999" height="10.7002" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="2" result="effect1_foregroundBlur_308_1675"/>
</filter>
<linearGradient id="paint0_linear_308_1675" x1="12" y1="0" x2="12" y2="24" gradientUnits="userSpaceOnUse">
<stop offset="0.210457" stop-color="#F7EDD8"/>
<stop offset="1" stop-color="#F2D9B6"/>
</linearGradient>
<radialGradient id="paint1_angular_308_1675" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(12 12) rotate(90) scale(11)">
<stop offset="0.210457" stop-color="#E39A58"/>
<stop offset="0.501733" stop-color="#D44F44"/>
<stop offset="0.656788" stop-color="#DE80B9"/>
<stop offset="0.983284" stop-color="#D64F3F"/>
</radialGradient>
<linearGradient id="paint2_linear_308_1675" x1="12" y1="3" x2="12" y2="18.75" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_308_1675" x1="12" y1="3" x2="12" y2="18.75" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
