import { createI18n } from 'vue-i18n'
// import { isNation } from '@/common/js/util'
import { LOCALES, storageKey } from './contants'
import en from './locales/en.json'
import zh from './locales/zh.json'
import { useStorageLocale } from './translatePlugin'

export { useStorageLocale, storageKey, LOCALES }

export const i18n = createI18n({
  legacy: false,
  globalInjection: true,
  locale: 'zh', // locale: localStorage.getItem(storageKey) ?? 'zh'
  fallbackLocale: localStorage.getItem(storageKey) ?? 'zh',
  messages: {
    en,
    zh,
  },
})

export default i18n
