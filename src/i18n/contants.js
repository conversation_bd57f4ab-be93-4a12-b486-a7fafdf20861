import { nationDefaultLangMap } from '@/constants/nation'

export const storageKey = 'LANG'
export const ignoreTranslateName = 'skip-translate'

export const defaultLocale = nationDefaultLangMap[import.meta.env.VUE_APP_NATIONAL_TYPE] || 'zh'

export const LOCALES = Object.freeze({
  outside_trade_serve: {
    zh: '外贸综合服务',
    en: 'Trade Service',
    ar: 'خدمات التجارة الخارجية الشاملة',
    thai: 'บริการการค้านานาชาติ',
    indonesian: '<PERSON><PERSON><PERSON> yang <PERSON>',
    ru: 'Услуги ВЭД',
    tr: 'Dış Ticaret Entegre Hizmetler',
  },
  usa_pavilion: {
    zh: '阿联酋国家馆',
    en: 'UAE Pavilion',
    ar: 'جناح الإمارات',
    thai: 'นิทรรศการอาหรับ',
    indonesian: 'Paviliun Arab',
    ru: 'Павильон ОАЭ',
    tr: 'BAE Millî Fuar Alanı',
  },
  sa_pavilion: {
    zh: '沙特阿拉伯国家馆',
    en: 'Saudi Arabia Pavilion',
    ar: 'جناح السعودية',
    thai: 'นิทรรศการซาอุดี',
    indonesian: 'Arab Saudi',
    ru: 'Павильон Саудовской Аравии',
    tr: 'SAU Millî Fuar Alanı',
  },
  thai_pavilion: {
    zh: '泰国国家馆',
    en: 'Thailand Pavilion',
    ar: 'جناح تايلاند',
    thai: 'นิทรรศการไทย',
    indonesian: 'Paviliun Thai',
    ru: 'Павильон ТАИЛАНД',
    tr: 'THA Millî Fuar Alanı',
  },
  indonesia_pavilion: {
    zh: '印度尼西亚国家馆',
    en: 'Indonesia Pavilion',
    ar: 'جناح اندونيسيا',
    thai: 'นิทรรศการอินโด',
    indonesian: 'Paviliun Indon',
    ru: 'Павильон ИНД',
    tr: 'IDN Millî Fuar Alanı',
  },
})
// 在线客服对应语言简称
// cn: '中文简体',
// tc: '中文繁體',
// en: 'English',
// vi: 'Việt Nam',
// th: 'ประเทศไทย',
// rus: 'Россия',
// id: 'Indonesia',
// jp: 'にほん',
// kr: '대한민국',
// es: 'España',
// fra: 'Français',
// it: 'Italian',
// de: 'Deutsch',
// pt: 'Português',
// ara: 'عربي',
// dan: 'Dansk',
// el: 'Ελληνικά',
// nl: 'Nederlands',
// pl: 'Polskie',
// fin: 'Suomi',
// 在线客服对应简称
export const CUSTOMER_SERVICE_LANGUAGE_MAP = {
  zh: 'cn',
  en: 'en',
  ar: 'ara',
  thai: 'th',
  indonesian: 'id',
  ru: 'rus',
  tr: 'tr',
}
