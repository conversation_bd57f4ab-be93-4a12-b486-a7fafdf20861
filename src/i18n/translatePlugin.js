import { ref } from 'vue'
import event from '@/event'
import { LANG_CHANGED } from '@/event/modules/site'
import { defaultLocale, ignoreTranslateName } from './contants'
import { langKey, langTypeMap } from './translateLang'

console.log(localStorage.getItem(langKey), 'translatePlugin')
// 使用 ref 创建响应式存储语言
const storageLocale = ref(langTypeMap[localStorage.getItem(langKey)] ?? defaultLocale)

function updateContentRender(targetDom, value) {
  const classNameArr = targetDom.className.split(' ')
  targetDom.textContent = value
  if (classNameArr.every((item) => item.trim() !== ignoreTranslateName)) {
    targetDom.className = `${targetDom.className} ${ignoreTranslateName}`
  }
}

export default {
  install(app) {
    // 监听 LANG_CHANGED 事件更新语言
    event.on(LANG_CHANGED, (key) => {
      storageLocale.value = key // 更新响应式值
      app.config.globalProperties.$storageLocale = key
    })
    // 将 storageLocale 挂载到 globalProperties
    app.config.globalProperties.$storageLocale = storageLocale.value

    app.directive('enMode', {
      mounted: (targetDom, binding) => {
        const updateContent = () => {
          if (storageLocale.value === 'en') {
            updateContentRender(targetDom, binding.value)
          }
        }
        // 初始化时调用一次
        updateContent()

        // 监听 storageLocale 的变化，动态更新 DOM
        watch(storageLocale, updateContent)
      },
    })

    app.directive('arMode', {
      mounted: (targetDom, binding) => {
        const updateContent = () => {
          if (storageLocale.value === 'ar') {
            updateContentRender(targetDom, binding.value)
          }
        }
        // 初始化时调用一次
        updateContent()

        // 监听 storageLocale 的变化，动态更新 DOM
        watch(storageLocale, updateContent)
      },
    })

    app.directive('mode', {
      mounted: (targetDom, binding) => {
        const updateContent = () => {
          const { value } = binding
          let newBindingValue = null

          // 根据绑定的值类型判断逻辑
          if (Array.isArray(value)) {
            // 数组形式 [语言, 显示内容]
            newBindingValue = storageLocale.value === value[0] ? value[1] : null
          } else if (typeof value === 'object' && value !== null) {
            // 对象形式 { 语言: 显示内容 }
            newBindingValue = value[storageLocale.value] || null
          } else {
            // 默认直接处理字符串 返回固定内容
            newBindingValue = value
          }

          if (newBindingValue) {
            updateContentRender(targetDom, newBindingValue)
          }
        }

        // 初始化时调用一次
        updateContent()

        // 监听 storageLocale 的变化，动态更新 DOM
        watch(storageLocale, updateContent)
      },
    })
  },
}

export function useStorageLocale() {
  return {
    storageLocale,
  }
}
