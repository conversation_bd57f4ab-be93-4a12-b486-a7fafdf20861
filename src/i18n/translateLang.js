// import { isNation } from '@/common/js/util'
import { nationDefaultLangMap } from '@/constants/nation'

// 插件翻译语言
export const I18nLanguage = {
  'zh-CN': 'chinese_simplified',
  'zh-TW': 'chinese_traditional',
  'zh-HK': 'chinese_traditional',
  co: 'corsican',
  gn: 'guarani',
  rw: 'kinyarwanda',
  ha: 'hausa',
  no: 'norwegian',
  nl: 'dutch',
  yo: 'yoruba',
  en: 'english',
  'en-US': 'english',
  kok: 'gongen',
  la: 'latin',
  ne: 'nepali',
  fr: 'french',
  cs: 'czech',
  haw: 'hawaiian',
  ka: 'georgian',
  ru: 'russian',
  fa: 'persian',
  bho: 'bhojpuri',
  hi: 'hindi',
  be: 'belarusian',
  sw: 'swahili',
  is: 'icelandic',
  yi: 'yiddish',
  tw: 'twi',
  ga: 'irish',
  gu: 'gujarati',
  km: 'khmer',
  sk: 'slovak',
  he: 'hebrew',
  kn: 'kannada',
  hu: 'hungarian',
  ta: 'tamil',
  ar: 'arabic',
  bn: 'bengali',
  az: 'azerbaijani',
  sm: 'samoan',
  af: 'afrikaans',
  id: 'indonesian',
  da: 'danish',
  sn: 'shona',
  bm: 'bambara',
  lt: 'lithuanian',
  vi: 'vietnamese',
  mt: 'maltese',
  tk: 'turkmen',
  as: 'assamese',
  ca: 'catalan',
  si: 'singapore',
  ceb: 'cebuano',
  gd: 'scottish-gaelic',
  sa: 'sanskrit',
  pl: 'polish',
  gl: 'galician',
  lv: 'latvian',
  uk: 'ukrainian',
  tt: 'tatar',
  cy: 'welsh',
  ja: 'japanese',
  fil: 'filipino',
  ay: 'aymara',
  lo: 'lao',
  te: 'telugu',
  ro: 'romanian',
  ht: 'haitian_creole',
  doi: 'dogrid',
  sv: 'swedish',
  mai: 'maithili',
  th: 'thai',
  hy: 'armenian',
  my: 'burmese',
  ps: 'pashto',
  hmn: 'hmong',
  dv: 'dhivehi',
  lb: 'luxembourgish',
  sd: 'sindhi',
  ku: 'kurdish',
  tr: 'turkish',
  mk: 'macedonian',
  bg: 'bulgarian',
  ms: 'malay',
  lg: 'luganda',
  mr: 'marathi',
  et: 'estonian',
  ml: 'malayalam',
  de: 'deutsch',
  sl: 'slovene',
  ur: 'urdu',
  pt: 'portuguese',
  ig: 'igbo',
  ckb: 'kurdish_sorani',
  om: 'oromo',
  el: 'greek',
  es: 'spanish',
  fy: 'frisian',
  so: 'somali',
  am: 'amharic',
  ny: 'nyanja',
  pa: 'punjabi',
  eu: 'basque',
  it: 'italian',
  sq: 'albanian',
  ko: 'korean',
  tg: 'tajik',
  fi: 'finnish',
  ky: 'kyrgyz',
  ee: 'ewe',
  hr: 'croatian',
  kri: 'creole',
  qu: 'quechua',
  bs: 'bosnian',
  mi: 'maori',
}

export const ReversedI18nLanguage = Object.entries(I18nLanguage).reduce((acc, [key, value]) => {
  acc[value] = key
  return acc
}, {})

export const langTypeMap = {
  ...ReversedI18nLanguage,
  chinese_simplified: 'zh',
  english: 'en',
  arabic: 'ar',
  thai: 'thai',
  indonesian: 'indonesian',
  russian: 'ru',
  turkish: 'tr',
}
export const langKey = 'to'
const translateToLang = {
  // 设置 translateToLang
  setTranslateToLang(key) {
    localStorage.setItem(langKey, key)
  },
  // 获取 translateToLang
  getTranslateToLang() {
    return langTypeMap[localStorage.getItem(langKey)] ?? (nationDefaultLangMap[import.meta.env.VUE_APP_NATIONAL_TYPE] || 'zh')
  },
  initTranslate() {
    const translateLocalValue = this.getTranslateToLang()
    const localLangValue = localStorage.getItem('LANG')
    if (!localLangValue || translateLocalValue !== localLangValue) {
      localStorage.setItem('LANG', translateLocalValue)
    }
    localLangValue === 'ar' && document.documentElement.setAttribute('dir', localLangValue === 'ar' ? 'rtl' : 'ltr')
  },
}

export default translateToLang
