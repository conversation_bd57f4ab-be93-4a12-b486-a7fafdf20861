# [translatePlugin.js](translatePlugin.js)

## 背景

- 使用了translate.js后i18n语言locale需固定为 'zh'
- 文件响应式失效
- 一些翻译需要自定义翻译


### js中使用 v-enMode

````
import { useStorageLocale } from '@/i18n'

const { storageLocale } = useStorageLocale()

// storageLocale.value === 'zh'
````

### template中使用 v-enMode

````
<div class="slogan" v-enMode="'Linyi Trade City · China Market'">{{ $t('slogan.main') }}</div>
````

### template中使用 v-arMode

````
<div class="slogan" v-arMode="'سوق الصين الكبرى'">{{ $t('slogan.main') }}</div>
````

### template中使用 v-mode

````
<!-- 数组形式 -->
<div v-mode="['en', 'This text is for English']"></div>

<!-- 对象形式 -->
<div v-mode="{ en: 'Hello', ar: 'مرحبا', zh: '你好' }"></div>

<!-- 字符串形式 固定内容 -->
<div v-mode="'Static text for all languages'"></div>
````



### template中使用 $storageLocale

````
<div v-if="$storageLocale === 'ar'">xxxxx</div>
````
