## 多语言翻译，修改步骤及相关文件

### 步骤：

    - 1、src/constants/mall.js

        LANG_TYPE 枚举添加相应语种，其中 key 的属性值与翻译插件的 to 的属性值需保持一致。

        subKey 属性值对应大集哥相关接口的参数 language (需与祥哥约定值)

    - 2、src/i18n/translateLang.js

        langTypeMap 枚举添加相应语种，其中 key 为插件 to 的属性值，value 为对应语种的简写

    - 3、src/constants/special-field.js

        LINYI_CHINA_MARKET(临沂商城 · 中国大集)
        AI_BROTHER(AI大集哥)
        GENERATE(生成中)
        SUSPENDED(已暂停)
        REBUILD(重新生成)
        STOP_GENERATION(停止生成)
        EXPORT_TO_DOMESTIC(临沂商城 官方平台 国企信誉 交易保障)
        PRODUCT_INQUIRY(咨询商品)
        以上枚举对应语种的翻译

    - 4、src/pc/views/pages/ai-chatbot/data-options.js

        AI大集哥对话框中下拉选择框：LANGUAGE_LIST 枚举添加对应的语种

    - 5、src/i18n/contants.js

        LOCALES 枚举添加对应的语种
    - 6、 src/i18n/contants.js
        CUSTOMER_SERVICE_LANGUAGE_MAP 在线客服增加对应缩写