import store from '@/store'
export const filterHtml = dom =>
  dom
    .replace(/<.+?>/g, '')
    .replace(/&nbsp;/gi, '')
    .replace(/&ldquo;/gi, '')
    .replace(/&rdquo;/gi, '')
    .replace(/\s/gi, '')

export function getWeek(week) {
  switch (week) {
    case 1:
      return '周一'
    case 2:
      return '周二'
    case 3:
      return '周三'
    case 4:
      return '周四'
    case 5:
      return '周五'
    case 6:
      return '周六'
    case 0:
      return '周日'
    default:
      return '周日'
  }
}

// 判断一张票是否为商票
export function isCommercialDraft(draftNumber) {
  return String(draftNumber).startsWith('2') || String(draftNumber).startsWith('6') || String(draftNumber).startsWith('7')
}

// 判断一张票是否为商票、财票
export function isWealthCommerceDraft(draftNumber, acceptorType) {
  return (
    String(draftNumber).startsWith('2') || String(draftNumber).startsWith('6') || String(draftNumber).startsWith('7') || acceptorType === 8
  )
}

/**
 * 阿里云Web Tracking日式上报方法 https://help.aliyun.com/document_detail/31752.html?spm=a2c4g.128134.0.0.1c104410zmf4sh
 * @export
 * @param {any | string} options 上报方法
 */
export function tracking(options = '') {
  // ${project}: k8s-log-c9b12bfc7a6c2411da4e2cf195a55f2bd
  // ${host}: cn-beijing.log.aliyuncs.com
  // ${logstore}: chengjie-web
  if (!options) return
  let params = typeof args === 'string' ? options : stringify(options)
  const { uid } = store.state.user.userInfo // 获取企业信息uid
  // const env = process.env.VUE_APP_API_ENV
  const trackUrl = 'http://k8s-log-c9b12bfc7a6c2411da4e2cf195a55f2bd.cn-beijing.log.aliyuncs.com/logstores/chengjie-web/track.gif'
  let url = `${trackUrl}?APIVersion=0.6.0&erpId=${uid}&${params}`
  let img = new Image()
  img.src = url
  document.body.appendChild(img)
  setTimeout(() => {
    document.body.removeChild(img) // 上传完成后移除标签
  }, 3000)
}

/**
 * 对象转参数
 * @param {object} params 提交到后台参数对象
 * @returns {string} 转换成拼接后的字符串
 */
export function stringify(params) {
  return Object.keys(params).map(k => {
    // 如果是对象或者数组  要转换成字符串
    let value = params[k]
    let v = typeof value === 'object' && value !== null ? JSON.stringify(value) : value
    return `${k}=${v}`
  })
    .join('&')
}

/**
  * @desc  获取两数之间的随机正数
  * @param {*} min
  * @param {*} max
  * @returns
*/

export function getRandomInt(min, max) {
  let delayMin = min || 200
  let delayMax = max || 180 * 1000 // 毫秒值
  delayMin = Math.ceil(delayMin)
  delayMin = Math.floor(delayMin)
  return Math.floor(Math.random() * (delayMax - delayMin)) + delayMin // 不含最大值，含最小值
}
