import { api } from '@/utils/axios.js'

// eslint-disable-next-line valid-jsdoc
/**
 * 创建Form表单跳转亿联直融的网关页面
 * @param {String} config.url form的Action，提交的后台地址
 * @param {String} config.method 使用POST还是GET提交表单
 * @param {String} config.target 当前页面还是新页面 _self/_blank
 * @param {object} config.params 文件对象
 */
function createFormToYl(config) {
  config = config || {}
  let form = document.createElement('form')
  form.action = config.url
  form.method = config.method || 'POST'
  form.target = config.target || '_blank'

  let params = config.params || {}
  for (let param in params) {
    let value = params[param]
    let input = document.createElement('input')
    input.type = 'hidden'
    input.name = param
    input.value = value

    form.appendChild(input)
  }
  document.body.appendChild(form)
  form.submit()

  // 跳转后再删除form
  setTimeout(() => {
    document.body.removeChild(form)
  }, 2000)
}

/**
 * 获取亿联银行网关页面的参数接口
 * @param {String} type 获取的网关API枚举类型 "101-设置密码 102-验证密码 103-修改密码 104-重置密码 201-对公客户注册 202-对公客户信息修改 203-客户绑定账户 204-打款验证 205-删除绑定账户301-客户提现"
 * @param {Object} Obj  可扩展参数 key按照接口文档字段传值
 */

export function getYlTokenAndUrl(type, Obj) {
  return new Promise((resolve, reject) => {
    api.post('/api/platform/bank/yilian/getTokenAndUrl', { tradeType: type, ...Obj }, {}).then(res => {
      const { url, body: params } = res
      if (url) {
        // 添加域名检验字段
        // params.webReferer = 'https://shendu-dev3.sdpjw.cn'
        createFormToYl({ url, params })
        resolve(res)
      }
    })
      .catch(err => {
        reject(err)
      })
  })
}

// 调用示例
// createFormToYl({
//   url: 'https://www.baidu.com',
//   params: {
//     a: 1,
//     b: 2,
//     c: 3,
//     d: 4,
//   }
// })

// getYlTokenAndUrl('202', {accountNo: '***********'})
