// import issueDraft<PERSON>pi from '@/apis/issue-draft'
import { PAYMENT_CHANNEL } from '@/constant'

// 规则限制包含的渠道
const INCLUSION_CHANNELS = [
  PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id, // E+
  PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id // 邦+
]

// 规则字段维护
const RULE_FIELD = {
  // E+渠道
  [PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id]: {
    todayIsHoliday: 'todayIsHoliday', // 是否工作日 0=>工作日 1=>节假日
    payAmountMax: 'payAmountMax', // 票方回款账户选择亿联一般户且订单应付金额≥payAmountMax万元时
    payAmountMin: 'payAmountMin', // 票方回款账户选择非亿联一般户且订单应付金额≥payAmountMin万元时
    holidayTime: 'holidayTime', // 节假日提示延迟到账时间范围
    workdayHintTime: 'workdayHintTime', // 工作日 提示延迟到账时间范围
    workdayTime: 'workdayTime', // 工作日 提示跨行转账限额时间范围
    fasterTradeOrderWorkdayLimitTime: 'fasterTradeOrderWorkdayLimitTime', // 光速订单 工作日 限制接单时间
    fasterTradeOrderHolidayLimitTime: 'fasterTradeOrderHolidayLimitTime', // 光速订单 节假日 限制接单时间
    fasterTradeOrderGeneralAccountLimitAmt: 'fasterTradeOrderGeneralAccountLimitAmt', // 光速订单 一般户 限制接单金额 万
    fasterTradeOrderLimitAmt: 'fasterTradeOrderLimitAmt', // 光速订单 非一般户 限制接单金额 万
    generalAccount: 'generalAccount', // 是否一般户
    workDayWorkTimeTakeOrderMin: 'ylWorkDayWorkTimeTakeOrderMin', // 跨行（非一般户）接单配置金额
    workDayWorkTimeConfirmOrderMin: 'ylWorkDayWorkTimeConfirmOrderMin', /// / 跨行（非一般户）确认订单配置金额
    cueQuotaAmt: 'ylCueQuotaAmt' // * E+提示限额
  },
  // 邦+渠道
  [PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id]: {
    todayIsHoliday: 'todayIsHoliday', // 是否工作日 0=>工作日 1=>节假日
    payAmountMax: 'payAmountMaxZb', // 票方回款账户选择亿联一般户且订单应付金额≥payAmountMax万元时
    payAmountMin: 'payAmountMinZb', // 票方回款账户选择非亿联一般户且订单应付金额≥payAmountMin万元时
    holidayTime: 'holidayTimeZb', // 节假日提示延迟到账时间范围
    workdayHintTime: 'workdayHintTimeZb', // 工作日 提示延迟到账时间范围
    workdayTime: 'workdayTimeZb', // 工作日 提示跨行转账限额时间范围
    fasterTradeOrderWorkdayLimitTime: 'fasterTradeOrderWorkdayLimitTimeZb', // 光速订单 工作日 限制接单时间
    fasterTradeOrderHolidayLimitTime: 'fasterTradeOrderHolidayLimitTimeZb', // 光速订单 节假日 限制接单时间
    fasterTradeOrderGeneralAccountLimitAmt: 'fasterTradeOrderGeneralAccountLimitAmtZb', // 光速订单 一般户 限制接单金额 万
    fasterTradeOrderLimitAmt: 'fasterTradeOrderLimitAmtZb', // 光速订单 非一般户 限制接单金额 万
    generalAccount: 'generalAccountZb', // 是否一般户
    workDayWorkTimeTakeOrderMin: 'zbWorkDayWorkTimeTakeOrderMin', // 跨行（非一般户）接单配置金额
    workDayWorkTimeConfirmOrderMin: 'zbWorkDayWorkTimeConfirmOrderMin', /// / 跨行（非一般户）确认订单配置金额
    cueQuotaAmt: 'zbCueQuotaAmt' // * 邦+提示限额
  }
}

// 检查E+是否存在超过6天未上传佐证材料的（限制接单提示）
// eslint-disable-next-line no-unused-vars
export function limitOrderTakingTips(payChannel, vm) {
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(resolve => {
    resolve('success') // 不限制接单

    // feat:下线隐藏佐证材料相关内容

    // if (payChannel === PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id) {

    //   const res = await issueDraftApi.checkZfyjLimitAcceptOrder()
    //   if (res) {
    //     vm.$confirm('<div style="font-size:16px;">您有交易完成后超过<span style="color:red;">6天</span>未上传佐证材料的订单，根据平台规则已限制当前账户使用<span style="font-weight: bold;">智付E+</span>渠道接单。完成佐证材料上传后可以继续接单</div>', '限制接单提示', {
    //       confirmButtonText: '上传佐证材料',
    //       dangerouslyUseHTMLString: true,
    //       showClose: true,
    //       showCancelButton: false, // 隐藏取消按钮
    //     }).then(() => {
    //       resolve('upload') // 去上传佐证
    //     })
    //       .catch()
    //   } else {
    //     // eslint-disable-next-line prefer-promise-reject-errors
    //     resolve('success') // 不限制接单
    //   }
    // } else {
    //   resolve('success') // 不限制接单
    // }
  })
}

/**
 * 时分秒格式
 * @param {传入的时间} time
 * @param {开始时间} startTime
 * @param {介绍时间} endTime
 * @returns
 */

function isTimeInRange(time, startTime, endTime) {
  const date = new Date()
  const [hour, minute, second] = time.split(':')
  date.setHours(hour)
  date.setMinutes(minute)
  date.setSeconds(second)
  const start = new Date(`${date.toDateString()} ${startTime}`)
  const end = new Date(`${date.toDateString()} ${endTime}`)
  return date >= start && date <= end
}

/**
 *判断传入的时间是否在工作日时间范围内
 * @param {工作日时间范围} workdayTime
 * @param {当前时间} time
 * @returns
 */

function weekday(workdayHintTime, time) {
  // workdayHintTime = '08:00:00-19:59:59'
  if (!workdayHintTime) return
  workdayHintTime = workdayHintTime.split('-')
  let startTime = workdayHintTime[0]
  let endTime = workdayHintTime[1]
  return isTimeInRange(time, startTime, endTime)
}

/**
 * 判断传入的时间是否在节假日时间范围内
 * @param {节假日时间范围} holidayTime
 * @param {当前时间} time
 * @returns
 */

function notWeekday(holidayTime, time) {
  if (!holidayTime) return
  holidayTime = holidayTime.split('-')
  let startTime = holidayTime[0]
  let endTime = holidayTime[1]
  return isTimeInRange(time, startTime, endTime)
}

// 获取当前时间 时分秒格式
function getCurrentTime() {
  const now = new Date()
  const hours = now.getHours()
  const minutes = now.getMinutes()
  const seconds = now.getSeconds()
  return `${hours}:${minutes}:${seconds}`
}

// 是否一般户票据金额判断
function isGeneralAccount(order) {
  // 一般户 0 且 票面金额>payAmountMax
  if (!order[RULE_FIELD[order.paymentChannel].generalAccount] && order.draftAmountWan > order.payAmountMax) {
    return true
  }
  // 非一般户 1 且 票面金额>payAmountMin
  if (order[RULE_FIELD[order.paymentChannel].generalAccount] && order.draftAmountWan > order.payAmountMin) {
    return true
  }
  return false
}

// 单笔确认/接单条件判断
function single(order, type) {
  if (type === 'orderTaking') { // 资方接单光速订单限制类型 （E+渠道 || 邦+） && 是光速订单 && (一般户 且 票面金额>payAmountMax ||  非一般户 且 票面金额>payAmountMin )
    return INCLUSION_CHANNELS.includes(order.paymentChannel)
            && order.fastTrade
      && isGeneralAccount(Object.assign(order, {
        // generalAccount: order[RULE_FIELD[order.paymentChannel].generalAccount],
        draftAmountWan: order.draftAmountWan,
        payAmountMax: order[RULE_FIELD[order.paymentChannel].fasterTradeOrderGeneralAccountLimitAmt],
        payAmountMin: order[RULE_FIELD[order.paymentChannel].fasterTradeOrderLimitAmt],
        paymentChannel: order.paymentChannel
      }))
  } else { // （E+渠道 || 邦+） && (一般户 且 票面金额>payAmountMax ||  非一般户 且 票面金额>payAmountMin )
    return INCLUSION_CHANNELS.includes(order.paymentChannel)
      && isGeneralAccount(Object.assign(order, {
        payAmountMax: order[RULE_FIELD[order.paymentChannel].payAmountMax],
        payAmountMin: order[RULE_FIELD[order.paymentChannel].payAmountMin],
      }))
  }
}

// 批量确认/接单条件判断
function batch(order, type) {
  const arr = order.list
  if (type === 'orderTaking') { // 资方接单光速订单限制类型 （E+渠道 || 邦+） && 是光速订单 && (一般户 且 票面金额>payAmountMax ||  非一般户 且 票面金额>payAmountMin )
    return arr.some(e => INCLUSION_CHANNELS.includes(e.paymentChannel)
                          && e.fastTrade
                          && isGeneralAccount(Object.assign(e, {
                            payAmountMax: order[RULE_FIELD[e.paymentChannel].fasterTradeOrderGeneralAccountLimitAmt],
                            payAmountMin: order[RULE_FIELD[e.paymentChannel].fasterTradeOrderLimitAmt],
                            paymentChannel: e.paymentChannel
                          })))
  } else { // （E+渠道 || 邦+）  && (一般户 且 票面金额>payAmountMax ||  非一般户 且 票面金额>payAmountMin )
    return arr.some(e => INCLUSION_CHANNELS.includes(e.paymentChannel)
                          && isGeneralAccount(Object.assign(e, {
                            payAmountMax: order[RULE_FIELD[e.paymentChannel].payAmountMax],
                            payAmountMin: order[RULE_FIELD[e.paymentChannel].payAmountMin],
                            paymentChannel: e.paymentChannel
                          })))
  }
}

// 智付E+渠道订单根据银行出入金额规则提示的处理  E+ 邦+
// 1、工作日17:00:00 - 19:59:59接单的订单
// 2、资方在工作日17:00:00 - 19:59:59使用智付E + 渠道完成接单，当票方回款账户选择亿联一般户且订单应付金额≥500万元时，票方确认订单时弹出对话框提示。——第一优先级弹出
// 3、资方在工作日17:00:00 - 19:59:59使用智付E + 渠道完成接单，当票方回款账户选择非亿联一般户且订单应付金额≥100万元时，票方确认订单时弹出对话框提示。——第一优先级弹出 workdayHintTime
// 4、非工作日8:00:00 - 19:59:59接单的订单——同上 holidayTime

export function confirmOrderTips(order, vm) {
  return new Promise(resolve => {
    // E+渠道 && (一般户票面金额>=500万 ||非亿联一般户票面金额>=100万)
    const status = order.hasAllOrder ? batch(order) : single(order) // 批量确认条件 or 单笔确认条件
    // 是否在规定时间内 区分工作日/节假日 todayIsHoliday 0=> 工作日 1=>节假日 批量(满足一条就提示) ｜ 单笔
    let isPrescribedTime
    if (order.hasAllOrder) {
      isPrescribedTime = order.list.some(e => ((order[RULE_FIELD[e.paymentChannel]?.todayIsHoliday] ? notWeekday(order[RULE_FIELD[e.paymentChannel]?.holidayTime], getCurrentTime()) : weekday(order[RULE_FIELD[e.paymentChannel]?.workdayHintTime], getCurrentTime()))))
    } else {
      isPrescribedTime = (order[RULE_FIELD[order.paymentChannel]?.todayIsHoliday] ? notWeekday(order[RULE_FIELD[order.paymentChannel]?.holidayTime], getCurrentTime()) : weekday(order[RULE_FIELD[order.paymentChannel]?.workdayHintTime], getCurrentTime()))
    }
    if (status && isPrescribedTime) {
      vm.$confirm(`<div style="font-size:16px;">${order.hasAllOrder ? '选中' : '该笔'}订单金额超过大小额系统转账限额，预计会延迟到账。<span style="font-weight: bold;">具体到账时间以银行为准，若您介意，请谨慎确认订单！</span></div>`, '提示', {
        confirmButtonText: '我知道了',
        dangerouslyUseHTMLString: true,
        showClose: true,
        showCancelButton: false, // 隐藏取消按钮
      }).then(() => {
        resolve('success') // 通过
      })
        .catch()
    } else {
      resolve('success') // 通过
    }
  })
}

// 光速订单限制接单规则 E+ 邦+
// 1、工作日17:00:00 - 19: 59: 59，资方选择智付E + 渠道，票方回款账户选择亿联一般户且订单应付金额≥500万元时限制接单。
// 2、工作日17:00:00 - 19: 59: 59，资方选择智付E + 渠道，票方回款账户选择非亿联一般户且订单应付金额≥100万元时限制接单。fasterTradeOrderWorkdayLimitTime
// 3、非工作日8:00:00 - 19: 59: 59，资方选择智付E + 渠道，票方回款账户选择亿联一般户且订单应付金额≥500万元时限制接单。
// 4、非工作日8:00:00 - 19: 59: 59，资方选择智付E + 渠道，票方回款账户选择非亿联一般户且订单应付金额≥100万元时限制接单。fasterTradeOrderWorkdayLimitTime
export function limitFastTradeOrderTaking(order) {
  if (order.hasAllOrder === undefined) { // 未传入是否批量 默认设置false
    order.hasAllOrder = false
  }
  // （单笔接单 并且 渠道没值）或者 (批量接单 并且 没有订单数据) return
  if ((!order.hasAllOrder && !order.paymentChannel) || (order.hasAllOrder && !order.list)) return

  const paymentChannel = order.hasAllOrder ? order.list[0].paymentChannel : order.paymentChannel

  // 非E+，邦+渠道 return
  if (!INCLUSION_CHANNELS.includes(paymentChannel)) return
  // 是否在规定时间内 区分工作日/节假日 todayIsHoliday 0=> 工作日 1=>节假日 区分邦+ E+
  const isPrescribedTime = order.todayIsHoliday ? notWeekday(order[RULE_FIELD[paymentChannel]?.fasterTradeOrderHolidayLimitTime], getCurrentTime()) : weekday(order[RULE_FIELD[paymentChannel]?.fasterTradeOrderWorkdayLimitTime], getCurrentTime())
  const status = order.hasAllOrder ? batch(order, 'orderTaking') : single(order, 'orderTaking') // 批量接单 or 单笔接单
  return !!(status && isPrescribedTime)
}

// function enjambmenBatch()

/**
 * 跨行回款户 确认订单提示
 * @param {工作日时间范围8:00:00-16:59:59} workdayTime
 * @param {非亿联一般户实付金额≥ylWorkDayWorkTimeConfirmOrderMin万元} workDayWorkTimeConfirmOrderMin
 * @param {非众邦一般户实付金额≥zbWorkDayWorkTimeConfirmOrderMin万元} workDayWorkTimeConfirmOrderMin
 * @returns
 */

export function enjambmentConfirmOrderTips(order, vm) {
  return new Promise(resolve => {
    // ( 非亿联一般户实付金额>=500万 || 非众邦一般户 >=400 )
    let status

    if (order.hasAllOrder) { // 批量
      status = order.list.some(e =>
        (INCLUSION_CHANNELS.includes(e.paymentChannel) // 规则限制包含的渠道内
        && e[RULE_FIELD[e.paymentChannel].generalAccount] // 是否非一般户
        && e.draftAmountWan > order[RULE_FIELD[e.paymentChannel]?.workDayWorkTimeConfirmOrderMin] // 实付金额
        && (order[RULE_FIELD[e.paymentChannel]?.todayIsHoliday] ? false : weekday(order[RULE_FIELD[e.paymentChannel]?.workdayTime], getCurrentTime())))) // 工作日是否在规定时间内 节假日false
    } else { // 单笔
      status = INCLUSION_CHANNELS.includes(order.paymentChannel) // 规则限制包含的渠道内
              && order[RULE_FIELD[order.paymentChannel]?.generalAccount] // 是否非一般户
              && order.draftAmountWan > order[RULE_FIELD[order.paymentChannel]?.workDayWorkTimeConfirmOrderMin] // 实付金额
              && (order[RULE_FIELD[order.paymentChannel]?.todayIsHoliday] ? false : weekday(order[RULE_FIELD[order.paymentChannel]?.workdayTime], getCurrentTime())) // 工作日是否在规定时间内 节假日false
    }

    if (status) {
      // const htmlStr = `<div style="line-height:30px;"><div style="font-size:16px;">${order.hasAllOrder ? '选中订单包含' : '该笔订单'}实际到账金额<span style="color:#EC3535;">≥${order[RULE_FIELD[order.paymentChannel || order.list[0].paymentChannel]?.workDayWorkTimeConfirmOrderMin]}万元</span>，预计将延迟到账。</div>
      //                 <div style="font-size:16px;">具体到账时间以银行为准，若您介意，请谨慎确认订单！</div>
      //                 <div style="color:#EC3535;"><div style="font-size:16px;">注：工作日8:00-17:00，跨行支付金额<500万，同行支付不限额</div>
      //                 <div style="font-size:16px;padding-left:30px;">其他时间，跨行支付金额<100万，同行支付不限额</div><div></div>`
      let htmlStr = ''
      if (order.hasAllOrder) {
        htmlStr = `<div style="line-height:30px;">
                    <div style="font-size:16px;">选中订单实际到账金额大于网银限额，预计将延迟到账。</div>
                    <div style="font-size:16px;">具体到账时间以银行为准，若您介意，请谨慎确认订单！</div>
                  </div>`
      } else {
        htmlStr = `<div style="line-height:30px;"><div style="font-size:16px;">${order.hasAllOrder ? '选中订单包含' : '该笔订单'}实际到账金额<span style="color:#EC3535;">>${order[RULE_FIELD[order.paymentChannel || order.list[0].paymentChannel]?.workDayWorkTimeConfirmOrderMin]}万元</span>，预计将延迟到账。</div>
                      <div style="font-size:16px;">具体到账时间以银行为准，若您介意，请谨慎确认订单！</div>
                      <div style="color:#EC3535;"><div style="font-size:16px;">注：工作日8:00-17:00，跨行支付金额≤${order[RULE_FIELD[order.paymentChannel || order.list[0].paymentChannel]?.workDayWorkTimeConfirmOrderMin]}万，同行支付依据网银实际额度</div>
                      <div style="font-size:16px;padding-left:30px;">其他时间，跨行支付金额≤100万，${order.paymentChannel === PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id ? '同行支付依据网银实际额度' : '同行支付不限额'}</div><div></div>`
      }

      vm.$confirm(`${htmlStr}`, '提示', {
        confirmButtonText: '确认',
        dangerouslyUseHTMLString: true,
        showClose: true,
        customClass: 'enjambment-confirm-Order-cls',
        showCancelButton: !!order.isCancel, // 取消按钮
      }).then(() => {
        resolve('success') // 通过
      })
        .catch()
    } else {
      resolve('success') // 通过
    }
  })
}

/**
 * 跨行回款户 接单提示
 * @param {工作日时间范围8:00:00-16:59:59} workdayTime
 * @param {非亿联一般户实付金额≥ylWorkDayWorkTimeTakeOrderMin万元} workDayWorkTimeTakeOrderMin
 * @param {非众邦一般户实付金额≥zbWorkDayWorkTimeTakeOrderMin万元} workDayWorkTimeTakeOrderMin
 * @returns
 */

export function enjambmentReceivingOrderTips(order, vm) {
  // 支付渠道 批量情况下取第一个
  const paymentChannel = order.hasAllOrder ? order.list[0].paymentChannel : order.paymentChannel

  // 非规则校验范围内渠道 return
  if (!INCLUSION_CHANNELS.includes(paymentChannel)) return

  // 工作日是否在规定时间内 todayIsHoliday 0=> 工作日 1=>节假日
  const isPrescribedTime = order[RULE_FIELD[paymentChannel]?.todayIsHoliday] ? false : weekday(order[RULE_FIELD[paymentChannel]?.workdayTime], getCurrentTime())
  return new Promise(resolve => {
    // ( E+ 非亿联一般户实付金额>=500万 || 邦+ 非众邦一般户 >400 )
    let status
    if (order.hasAllOrder) { // 批量
      status = order.list.some(e => e[RULE_FIELD[e.paymentChannel].generalAccount]
      // && (paymentChannel === PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id ? e.draftAmountWan > order[RULE_FIELD[e.paymentChannel]?.workDayWorkTimeTakeOrderMin] : e.draftAmountWan >= order[RULE_FIELD[e.paymentChannel]?.workDayWorkTimeTakeOrderMin]))

        // 固定限额10万提示
        && (paymentChannel === PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id ? e.draftAmountWan > order[RULE_FIELD[e.paymentChannel]?.cueQuotaAmt] : e.draftAmountWan >= order[RULE_FIELD[e.paymentChannel]?.cueQuotaAmt]))
    } else { // 单笔
      status = order[RULE_FIELD[order.paymentChannel].generalAccount]
      // && (paymentChannel === PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id ? order.draftAmountWan > order[RULE_FIELD[paymentChannel]?.workDayWorkTimeTakeOrderMin] : order.draftAmountWan >= order[RULE_FIELD[paymentChannel]?.workDayWorkTimeTakeOrderMin])

        // 固定限额10万提示
        && (paymentChannel === PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id ? order.draftAmountWan > order[RULE_FIELD[paymentChannel]?.cueQuotaAmt] : order.draftAmountWan >= order[RULE_FIELD[paymentChannel]?.cueQuotaAmt])
    }
    if (status && isPrescribedTime) {
      // const ylHtmlStr = `<div style="line-height:30px;"><div style="font-size:16px;">${order.hasAllOrder ? '选中订单包含' : '该笔订单'}实付金额<span style="color:#EC3535;">≥${order[RULE_FIELD[paymentChannel]?.workDayWorkTimeTakeOrderMin]}万元</span>，需银行人工审核，预计将影响您的付款时效。</div>
      //                 <div style="font-size:16px;">若因付款延误导致的违约将由您承担违约责任，若您介意，请谨慎接单！</div>
      //                 <div style="color:#EC3535;"><div style="font-size:16px;">注：工作日8:00-17:00，跨行支付金额<500万，同行支付不限额</div>
      //                 <div style="font-size:16px;padding-left:30px;">其他时间，跨行支付金额<100万，同行支付不限额</div><div></div>`

      // const zbHtmlStr=`<div style="line-height:30px;"><div style="font-size:16px;">${order.hasAllOrder ? '选中订单包含' : '该笔订单'}实付金额<span style="color:#EC3535;">>${order[RULE_FIELD[paymentChannel]?.workDayWorkTimeTakeOrderMin]}万元，请务必确认您的网银单笔转账限额。
      //                 </span>若超过单笔限额将影响您的付款时效。实付金额<span style="color:#EC3535;">≥500万元</span>请提前联系银行报备头寸。</div>
      //                 <div style="font-size:16px;">若因付款延误导致的违约将由您承担违约责任，若您介意，请谨慎接单！</div>
      //                 <div style="color:#EC3535;"><div style="font-size:16px;">注：工作日8:00-17:00，跨行支付金额≤500万，同行支付依据网银实际额度</div>
      //                 <div style="font-size:16px;padding-left:30px;">其他时间，跨行支付金额<100万，同行支付不限额</div><div></div>`

      // <div style="font-size:16px;">单日E+渠道总额度<span style="color:#EC3535;">${order[RULE_FIELD[paymentChannel]?.workDayWorkTimeTakeOrderMin]}万元</span>，请确认您的剩余额度是否≥订单实付金额。</div>
      const ylHtmlStr = `<div style="line-height:30px;">
      <div style="font-size:16px;">请依据您的E+渠道实际网银单日限额,确认剩余额度是否≥订单实付金额。</div>
                      <div style="font-size:16px;">若因额度不足导致的取消将由您承担违约责任，若您介意，请谨慎接单！</div>
                      <div style="color:#EC3535;"><div style="font-size:16px;">注：工作日8:00-17:00，跨行支付金额≤500万，同行支付依据网银实际额度</div>
                      <div style="font-size:16px;padding-left:30px;">其他时间，跨行支付金额≤100万，同行支付依据网银实际额度</div><div></div>`

      const zbHtmlStr = `<div style="line-height:30px;"><div style="font-size:16px;">单日邦+渠道总额度<span style="color:#EC3535;">${order[RULE_FIELD[paymentChannel]?.workDayWorkTimeTakeOrderMin]}万元，请确认您的剩余额度是否≥订单实付金额。</span></div>
                      <div style="font-size:16px;">若因额度不足导致的取消将由您承担违约责任，若您介意，请谨慎接单！</div>
                      <div style="color:#EC3535;"><div style="font-size:16px;">注：工作日8:00-17:00，跨行支付金额≤100万，同行支付不限额</div>
                      <div style="font-size:16px;padding-left:30px;">其他时间，跨行支付金额≤100万，同行支付不限额</div><div></div>`

      vm.$confirm(`${paymentChannel === PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id ? ylHtmlStr : zbHtmlStr}`, '提示', {
        confirmButtonText: '确认',
        dangerouslyUseHTMLString: true,
        showClose: true,
        customClass: 'enjambment-confirm-Order-cls',
        showCancelButton: true, // 隐藏取消按钮
        cancelButtonText: '取消'
      }).then(() => {
        resolve('success') // 通过
      })
        .catch()
    } else {
      resolve('success') // 通过
    }
  })
}
