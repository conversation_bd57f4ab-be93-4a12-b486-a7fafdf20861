import { Message } from '@shendu/element-ui'
import API from '@/apis/common'
const { origin } = window.location
const pageName = { // 调用SDK的name参数  可扩展
  1: 'zpm-ucenter', // 智付用户中心
  2: 'zpm-cashier' // 智付收银台
}

// 配置SDK环境 预发环境和生产一致，开发环境要配置环境参数
if (
  process.env.VUE_APP_API_ENV !== 'production' && process.env.VUE_APP_API_ENV !== 'staging') {
  window.jpmt = window.jpmt || {} // 客户端容错处理
  window.jpmt.env = 'development'
  window.jpmt.getDomain = () => 'https://piaoju-test.jd.com'
}

const webUrl = window.location.href

/**
   * 获取jd收银台页面地址 先从服务端获取到签名后调用SDK获取地址
   * @param {Object} item 页面调用传得参数{name： 1、用户中心， 2、收银台，platformOrderNo： 订单号数组 querys.accountType {百信： AIBANK，连连：LIANLIANPAY  合利宝：HELIPAY} 对应的支付tab页面}
   * @returns {Sting} url
   */
export const jumpJdPay = async(item = {}) => {
  const params = { url: origin }
  // eslint-disable-next-line no-empty-function
  const { code, data, msg } = await API.getSignature(params).catch(() => {}) || {}
  // eslint-disable-next-line no-console
  console.log(code, data, msg)
  if (code === 200 && data) {
    let { name, querys, platformOrderNo } = item
    let sendData = {
      ...data,
      name: name ? pageName[name] : pageName[1], // 页面名称 默认返回用户中心
      backUrl: webUrl, // 跳转异常的时候返回的地址 默认返回用户中心
      loginUrl: webUrl // 如果是未登录情况下 跳转到用户中心会自动触发登录弹框
    }
    sendData.querys
      = querys && Object.getOwnPropertyNames(querys).length > 0
        ? (sendData.querys = { ...querys })
        : {} // 如果有querys参数  把querys赋值给sendData.querys
    if (
      platformOrderNo
      && Object.prototype.toString.call(platformOrderNo) === '[object Array]'
      && platformOrderNo.length > 0
    ) {
      // 支付操作 platformOrderNo有数据且是数组时cb是querys对象中传入的回调地址
      sendData.querys.cb = encodeURIComponent(webUrl) // 支付成功页面回调地址，支付专用 cb有值的话直接编码，没有值就是当前页面
      if (platformOrderNo.length > 1) { // 判断length > 1 走批量支付逻辑 == 1 走单张支付
        // 获取jd批次号  批量支付(含2条及以上)需要调用该接口  单张支付(1张)直接传orderNo 不走接口
        // eslint-disable-next-line no-empty-function
        let res = await API.getBatchPayBatchNo({ platformOrderNo }).catch(() => {}) // 平台orderNo 集合
        // eslint-disable-next-line max-depth
        if (res.code === 200 && res.data) {
          sendData.querys.batchNo = res.data.batchNo
        } else {
          return Message.error(res.msg)
        }
      } else { // 单张支付
        sendData.querys.orderNo = platformOrderNo[0]
      }
    }
    // eslint-disable-next-line no-console
    console.log('调用SDK入参', sendData)
    let url = window.jpmt.computePageUrl(sendData)
    if (url) {
      // eslint-disable-next-line no-console
      console.log('SDK返回的的地址', url)
      window.open(url, '_blank')
    } else {
      Message.error('服务器异常，请稍后再试')
    }
  } else {
    Message.error(msg)
  }
}
