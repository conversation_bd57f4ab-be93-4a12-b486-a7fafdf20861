import user from '@/pc/utils/user'
import { stringify } from '@/utils/utils'

const project = 'k8s-log-cde5475d01dcb4b66b6cabeb8553efa22'
const host = 'cn-beijing.log.aliyuncs.com'
const logstore = 'mall-web'

/**
 * 阿里云Web Tracking日式上报方法 https://help.aliyun.com/document_detail/31752.html?spm=a2c4g.128134.0.0.1c104410zmf4sh
 * @export
 * @param {any | string} options 上报方法
 */
export function tracking(options) {
  if (!options) return
  let params = typeof options === 'string' ? options : stringify(options)
  const env = import.meta.env.MODE === 'production' ? 'prod' : 'daily'
  const trackUrl = `https://${project}.${host}/logstores/${logstore}/track.gif`
  let url = `${trackUrl}?APIVersion=0.6.0&env=${env}&${params}`
  let img = new Image()
  img.src = url
  document.body.appendChild(img)
  setTimeout(() => {
    document.body.removeChild(img) // 上传完成后移除标签
  }, 3000)
}

export function baseTracking(options) {
  if (typeof options !== 'object' || !options) return
  if (!options.isValidTrack && options.isValidTrack !== undefined) return
  delete options.isValidTrack
  tracking({
    device_type: 'PC',
    token: user.getToken() || '',
    ...options,
  })
}

// 曝光
export function trackExposure(options) {
  baseTracking({
    event: 'Exposure',
    ...options,
  })
}

// 点击
export function trackClick(options) {
  const module = options.module || options.m

  if (!module) {
    throw new TypeError('点击埋点module(点击模块)必填')
  }

  if (options.m) delete options.m

  baseTracking({
    event: 'Click',
    ...options,
    module,
  })
}

export default {
  install(app) {
    app.directive('track', {
      mounted(el, binding) {
        el.addEventListener(
          'click',
          () => {
            const data = binding.value
            trackClick({
              event: 'Click',
              ...data,
            })
          },
          false,
        )
      },
    })
  },
}
