import Vue from 'vue'
// v-dialogDrag: 弹窗拖拽
Vue.directive('dialogDrag', {
  // eslint-disable-next-line no-unused-vars
  bind(el, binding, vnode, oldVnode) {
    // 初始非全屏
    // eslint-disable-next-line no-unused-vars
    let isFullScreen = false

    // 当前宽高,用户放大缩小会到原始宽高
    let nowWidth = 0
    let nowHight = 0

    // 当前顶部高度
    // eslint-disable-next-line no-unused-vars
    let nowMarginTop = 0

    // 获取弹框头部（这部分可双击全屏）
    const dialogHeaderEl = el.querySelector('.el-dialog__header')
    // 弹窗
    const dragDom = el.querySelector('.el-dialog')

    // const bigBtn = el.querySelector('.bigbtn')
    const changeBig = el.querySelector('.changeBig') // 全屏
    const changeSmall = el.querySelector('.changeSmall') // 缩小

    // const minimizeBtn = el.querySelector('.minimizeBtn') // 最小化按钮
    // const recover = el.querySelector('.recover') // 恢复
    // const minimize = el.querySelector('.minimize') // 最小化
    // const isminimize = el.querySelector('.isminimize') // 不是最小化

    // 获取内容
    // const dialogBodyEl = document.getElementById('content')

    // 原始外边距高度
    // let orginMarginTop = dragDom.style.marginTop

    // 给弹窗加上overflow auto；不然缩小时框内的标签可能超出dialog；
    dragDom.style.overflow = 'auto'
    // 清除选择头部文字效果
    // dialogHeaderEl.onselectstart = new Function("return false");
    // 头部加上可拖动cursor
    dialogHeaderEl.style.cursor = 'move'
    // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);
    const sty = dragDom.currentStyle || window.getComputedStyle(dragDom, null)

    // 设置弹框的最小宽高
    let minWidth = 1200
    let minHeight = 687
    let originalWidth = 1200
    let originalHeight = 687
    let HeaderH = 70 // 头部高度

    let winWidth
    let winHeight
    // 可是区域窗口放大缩小的时候或者改变 非全屏状态下 修改窗口初始位置 和大小 避免出现位置便宜的情况
    function setBaseStyle() {
      changeBig.style.display = 'block'
      changeSmall.style.display = 'none'
      isFullScreen = false
      nowWidth = originalWidth
      nowHight = originalHeight
      dragDom.style.minWidth = `${minWidth}px`
      dragDom.style.minHeight = `${minHeight}px`
      dragDom.style.width = `${originalWidth}px`
      dragDom.style.height = `${originalHeight}px`

      // 获取当前可视窗口的宽度和高度
      winWidth = window.innerWidth
      winHeight = window.innerHeight

      dragDom.style.left = `${(winWidth - nowWidth) / 2}px`
      dragDom.style.top = `${(winHeight - nowHight) / 2}px`
    }
    // 初始设置弹窗大小
    setBaseStyle(true)

    // 窗口变化的时候 重新设置宽高
    const handleResize = () => {
      setBaseStyle()
    }
    window.addEventListener('resize', handleResize)

    el.resizeHandler = handleResize

    let moveDown = e => {
      // 鼠标按下，计算当前元素距离可视区的距离
      const disX = e.clientX - dialogHeaderEl.offsetLeft
      const disY = e.clientY - dialogHeaderEl.offsetTop

      // 获取到的值带px 正则匹配替换
      let styL
      let styT
      // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px
      if (sty.left.includes('%')) {
        styL = +document.body.clientWidth * (+sty.left.replace(/%/g, '') / 100)
        styT = +document.body.clientHeight * (+sty.top.replace(/%/g, '') / 100)
      } else {
        styL = +sty.left.replace(/\px/g, '')
        styT = +sty.top.replace(/\px/g, '')
      }

      document.onmousemove = emoveEvent => {
        // 通过事件委托，计算移动的距离
        const l = emoveEvent.clientX - disX
        const t = emoveEvent.clientY - disY

        // 移动当前元素，计算边界值
        if (l + styL < 0) {
          dragDom.style.left = '0px'
        } else if (l + styL > document.body.clientWidth - dragDom.clientWidth) {
          dragDom.style.left = `${document.body.clientWidth - dragDom.clientWidth}px`
        } else {
          dragDom.style.left = `${l + styL}px`
        }

        if (t + styT < 0) {
          dragDom.style.top = '0px'
        } else if (t + styT > document.body.clientHeight - dragDom.clientHeight) {
          dragDom.style.top = `${document.body.clientHeight - dragDom.clientHeight}px`
        } else {
          dragDom.style.top = `${t + styT}px`
        }
      }
      document.onmouseup = () => {
        document.onmousemove = null
        document.onmouseup = null
      }
    }
    dialogHeaderEl.onmousedown = moveDown

    // 拉伸(右边)效果不想要可以注释
    // let resizeElR = document.createElement('div')
    // dragDom.appendChild(resizeElR)
    // // 在弹窗右下角加上一个10-10px的控制块
    // resizeElR.style.cursor = 'w-resize'
    // resizeElR.style.position = 'absolute'
    // resizeElR.style.height = '100%'
    // resizeElR.style.width = '10px'
    // resizeElR.style.right = '0px'
    // resizeElR.style.top = '0px'
    // // 鼠标拉伸弹窗
    // resizeElR.onmousedown = e => {
    //   let elW = dragDom.clientWidth
    //   let EloffsetLeft = dragDom.offsetLeft
    //   // 记录初始x位置
    //   let { clientX } = e
    //   document.onmousemove = function(e) {
    //     e.preventDefault() // 移动时禁用默认事件
    //     // 右侧鼠标拖拽位置
    //     if (clientX > EloffsetLeft + elW - 10 && clientX < EloffsetLeft + elW) {
    //       // 往左拖拽
    //       if (clientX > e.clientX) {
    //         if (dragDom.clientWidth < minWidth) {
    //           console.log('111')
    //         } else {
    //           dragDom.style.width = `${elW - (clientX - e.clientX)}px`
    //         }
    //       }
    //       // 往右拖拽
    //       if (clientX < e.clientX) {
    //         dragDom.style.width = `${elW + (e.clientX - clientX)}px`
    //       }
    //     }
    //   }
    //   // 拉伸结束
    //   document.onmouseup = function(e) {
    //     document.onmousemove = null
    //     document.onmouseup = null
    //   }
    // }

    // 拉伸(左边)效果不想要可以注释
    // let resizeElL = document.createElement('div')
    // dragDom.appendChild(resizeElL)
    // // 在弹窗右下角加上一个10-10px的控制块
    // resizeElL.style.cursor = 'w-resize'
    // resizeElL.style.position = 'absolute'
    // resizeElL.style.height = '100%'
    // resizeElL.style.width = '10px'
    // resizeElL.style.left = '0px'
    // resizeElL.style.top = '0px'
    // // 鼠标拉伸弹窗
    // resizeElL.onmousedown = e => {
    //   let elW = dragDom.clientWidth
    //   let EloffsetLeft = dragDom.offsetLeft
    //   // 记录初始x位置
    //   let { clientX } = e
    //   document.onmousemove = function(e) {
    //     console.log('e', e)
    //     e.preventDefault() // 移动时禁用默认事件
    //     // 左侧鼠标拖拽位置
    //     if (clientX > EloffsetLeft && clientX < EloffsetLeft + 10) {
    //       // 往左拖拽
    //       if (clientX > e.clientX) {
    //         dragDom.style.width = `${elW + (clientX - e.clientX)}px`
    //       }
    //       // 往右拖拽
    //       if (clientX < e.clientX) {
    //         if (dragDom.clientWidth < minWidth) {
    //           console.log('111')
    //         } else {
    //           dragDom.style.width = `${elW - (e.clientX - clientX)}px`
    //         }
    //       }
    //     }
    //   }
    //   // 拉伸结束
    //   document.onmouseup = function(e) {
    //     document.onmousemove = null
    //     document.onmouseup = null
    //   }
    // }

    // 拉伸(下边)
    // let resizeElB = document.createElement('div')
    // dragDom.appendChild(resizeElB)
    // // 在弹窗右下角加上一个10-10px的控制块
    // resizeElB.style.cursor = 'n-resize'
    // resizeElB.style.position = 'absolute'
    // resizeElB.style.height = '10px'
    // resizeElB.style.width = '100%'
    // resizeElB.style.left = '0px'
    // resizeElB.style.bottom = '0px'
    // // 鼠标拉伸弹窗
    // resizeElB.onmousedown = e => {
    //   let EloffsetTop = dragDom.offsetTop
    //   let ELscrollTop = el.scrollTop
    //   let { clientY } = e
    //   let elH = dragDom.clientHeight
    //   // 鼠标按下，计算当前元素距离可视区的距离

    //   document.onmousemove = moveEvent => {
    //     moveEvent.preventDefault() // 移动时禁用默认事件
    //     // 底部鼠标拖拽位置
    //     console.log('EloffsetTop', EloffsetTop, ELscrollTop, elH, document.body.clientHeight)
    //     console.log(' moveEvent.clientY', moveEvent.clientY)
    //     if (EloffsetTop + moveEvent.clientY < document.body.clientHeight) {
    //       dragDom.style.height = `${elH - (clientY - moveEvent.clientY)}px`
    //     }

    //     // if (ELscrollTop + clientY > EloffsetTop + elH - 20 && ELscrollTop + clientY < EloffsetTop + elH) {
    //     //   // 往上拖拽

    //     //   // if (clientY > moveEvent.clientY) {
    //     //   //   if (dragDom.clientHeight < minHeight) {
    //     //   //     console.log('111')
    //     //   //   } else {
    //     //   //     dragDom.style.height = `${elH - (clientY - moveEvent.clientY)}px`
    //     //   //   }
    //     //   // }
    //     //   // // 往下拖拽
    //     //   // if (clientY < moveEvent.clientY) {
    //     //   //   dragDom.style.height = `${elH + (moveEvent.clientY - clientY)}px`
    //     //   // }
    //     // }
    //   }
    //   // 拉伸结束
    //   document.onmouseup = () => {
    //     document.onmousemove = null
    //     document.onmouseup = null
    //   }
    // }

    // 拉伸(右下方)
    let resizeEl = document.createElement('div')
    dragDom.appendChild(resizeEl)
    // 在弹窗右下角加上一个10-10px的控制块
    resizeEl.style.cursor = 'se-resize'
    resizeEl.style.position = 'absolute'
    resizeEl.style.height = '5px'
    resizeEl.style.width = '5px'
    resizeEl.style.right = '0px'
    resizeEl.style.bottom = '0px'
    resizeEl.style.zIndex = '99'
    // 鼠标拉伸弹窗
    resizeEl.onmousedown = e => {
      // 鼠标按下，计算当前元素距离可视区的距离
      let disX = e.clientX - resizeEl.offsetLeft
      let disY = e.clientY - resizeEl.offsetTop

      document.onmousemove = moveEvent => {
        moveEvent.preventDefault() // 移动时禁用默认事件

        // 通过事件委托，计算移动的距离
        let x = moveEvent.clientX - disX
        let y = moveEvent.clientY - disY

        // 超出边界值不允许拉伸
        if (disX + x < document.body.clientWidth) {
          // 比较是否小于最小宽高
          dragDom.style.width = x > minWidth ? `${x}px` : `${minWidth}px`
        }
        if (disY + y < document.body.clientHeight) {
          dragDom.style.height = y > minHeight ? `${y}px` : `${minHeight}px`
        }
      }

      // 拉伸结束
      document.onmouseup = () => {
        document.onmousemove = null
        document.onmouseup = null
      }
    }

    // 放大
    changeBig.onclick = () => {
      nowMarginTop = dragDom.style.marginTop
      changeBig.style.display = 'none'
      changeSmall.style.display = 'block'
      nowHight = dragDom.clientHeight
      nowWidth = dragDom.clientWidth

      dragDom.style.margin = 0
      dragDom.style.left = 0
      dragDom.style.top = 0
      dragDom.style.height = `${window.innerHeight - HeaderH}px`
      dragDom.style.width = '100%'
      isFullScreen = true
      // 将弹窗头部的鼠标指针样式设置为默认值
      dialogHeaderEl.style.cursor = 'initial'
      // 取消弹窗头部的鼠标按下事件。用户放大弹窗后，禁用拖拽功能，防止用户通过拖拽弹窗头部来移动弹窗的位置
      dialogHeaderEl.onmousedown = null
    }
    // 缩小
    changeSmall.onclick = () => {
      changeBig.style.display = 'block'
      changeSmall.style.display = 'none'
      dragDom.style.height = `${nowHight}px`
      dragDom.style.width = `${nowWidth}px`
      // 设置外边距
      dragDom.style.left = `${(winWidth - nowWidth) / 2}px`
      dragDom.style.top = `${(winHeight - nowHight) / 2}px`
      isFullScreen = false
      dialogHeaderEl.style.cursor = 'move'
      dialogHeaderEl.onmousedown = moveDown
    }
  },
  unbind(el) {
    if (el.resizeHandler) {
      window.removeEventListener('resize', el.resizeHandler)
      delete el.resizeHandler
    }
  }
})
