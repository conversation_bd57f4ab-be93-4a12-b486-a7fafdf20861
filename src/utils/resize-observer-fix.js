// 解决 ResizeObserver loop limit exceeded 这个报错

if (typeof window.ResizeObserver === 'function') {
  const OriginalResizeObserver = window.ResizeObserver
  // 重写 ResizeObserver
  window.ResizeObserver = function ResizeObserver(callback) {
    const newCallback = (entries, observer) => {
      // 将代码包在 requestAnimationFrame 中以防止报这个错：ResizeObserver loop limit exceeded
      // 据：https://stackoverflow.com/questions/49384120/resizeobserver-loop-limit-exceeded
      // 这个报错是良性的，相当于告诉我们 ResizeObserver 无法在一个单独的 animation frame 中处理所有的变动，并不会导致页面崩溃
      window.requestAnimationFrame(() => {
        if (!Array.isArray(entries) || !entries.length) {
          return
        }
        callback(entries, observer)
      })
    }
    return new OriginalResizeObserver(newCallback)
  }
}
