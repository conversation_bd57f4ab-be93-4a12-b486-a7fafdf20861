import userApi from '@/apis/user' // 用户接口
import openAccountApi from '@/apis/open-account' // 开户接口
import Storage from '@/common/js/storage' // 本地缓存对象
import { TOKEN, OPEN_ACCOUNT, USER_INFO, MY_ORDER_QUERY, MY_ORDER_PAGINATION, INQUITY_NOTICE_NUMBER, SENSITIVE_WORDS, MANAGE_QR_CODE } from '@/constant-storage'
import { SITE_OPEN_ACCOUNT, SITE_OPEN_CONTACT_SERVICE, OPEN_CUSTOMER_SERVICE_DIALOG, REAL_NAME_CERTIFICATION } from '@/event/modules/site'
import { REAL_NAME_AUTH_TYPE } from '@/constant' // 常量
import event from '@/event' // 事件订阅
import Cookies from 'js-cookie'
// import store from '@/store'

const user = {
  // 登录
  async postLogin(data) {
    const res = await userApi.postLogin(data)
    this.setToken(res.token)
    return res
  },

  // 验证码登录
  async codeLogin(data) {
    const res = await userApi.codeLogin(data)
    this.setToken(res.token)
    return res
  },

  // 注册
  async postRegister(data) {
    const res = await userApi.postRegisterUser(data)
    this.setToken(res.token)
    return res
  },

  /** 清除 token */
  clearToken() {
    Storage.remove(TOKEN)
  },

  // 清除客户经理二维码
  clearQrCode() {
    Storage.remove(MANAGE_QR_CODE)
  },

  // 设置 token
  setToken(token) {
    Storage.set(TOKEN, token)
  },

  // 获取 token
  getToken() {
    return Storage.get(TOKEN)
  },

  // 获取关联 id，埋点用
  getLinkId() {
    const keySuffix = process.env.VUE_APP_API_ENV === 'production' ? '' : `-${process.env.VUE_APP_API_ENV}`
    const value = Cookies.get(`linkId${keySuffix}`)
    return value && Number(value)
  },

  // 获取用户信息
  async getLoginUserInfo() {
    const res = await userApi.postLoginUserInfo()
    Storage.set(USER_INFO, res)
  },

  // 设置用户本地缓存开户信息
  setOpenAccount(obj) {
    const { uid } = Storage.get(USER_INFO) || {}
    if (!uid) {
      return
    }
    let temp = Storage.get(OPEN_ACCOUNT) || {}
    temp[uid] = Object.assign(temp[uid] || {}, obj)
    Storage.set(OPEN_ACCOUNT, temp)
  },

  // 获取用户本地缓存开户信息
  getOpenAccount() {
    const obj = Storage.get(OPEN_ACCOUNT)
    const { uid } = Storage.get(USER_INFO) || {}
    if (!uid) {
      return
    }
    return (obj && obj[uid]) || ''
  },

  // 删除用户开户信息
  removeOpenAccount() {
    const obj = Storage.get(OPEN_ACCOUNT)
    const { uid } = Storage.get(USER_INFO) || {}
    if (!uid) {
      return
    }
    delete obj[uid]
    Storage.set(OPEN_ACCOUNT, obj)
  },

  // 查询开户阶段 小额验证失败就只弹窗联系客服，其他弹窗开户窗口
  async checkAmountFailType(paymentChannel) {
    let isRealName = 0
    // 没有传paymentChannel 代表实名认证
    paymentChannel ? isRealName = 0 : isRealName = 1
    // 非实名认证情况（实名认证去掉了验证银行账户，故此处不需要小额验证判断，解决接口重复调用问题）
    if (isRealName === 0) {
      const res = await openAccountApi.getCorpOpenInfoStage({ isRealName, payChannel: paymentChannel })
      if (res.checkAmountFailType === 1) { // 验证失败超过上限
        event.emit(SITE_OPEN_CONTACT_SERVICE, {
          title: '今日输入小额验证失败次数已达上限！',
          contactServiceSubTitle: '请联系客服进行处理'
        })
        return
      } else if (res.checkAmountFailType === 2) { // 已过验证期
        event.emit(SITE_OPEN_CONTACT_SERVICE, {
          title: '小额打款已过验证期！',
          contactServiceSubTitle: '请联系客服进行处理'
        })
        return
      }
    }
    const { corpStatus } = Storage.get(USER_INFO) || {} // 获取企业状态

    if (corpStatus !== 2) { // 企业状态/亿连实名状态 未实名 实名失效
      event.emit(REAL_NAME_CERTIFICATION)
      return
    }
    // // if (corpInfo.paymentChannelOpenSuccess !== 1) {
    // //   event.emit(SITE_OPEN_ACCOUNT)
    // // }
    // event.emit(SITE_OPEN_ACCOUNT) // 未开通电子交易账户
    event.emit(SITE_OPEN_ACCOUNT, {
      realNameAuthType: REAL_NAME_AUTH_TYPE.FAIL,
      reAuthPaymentChannel: paymentChannel,
      startOver: true
    }) // 未开通电子交易账户
  },

  // 设置我的订单搜索条件
  setMyOrderQuery(obj) {
    const { uid } = Storage.get(USER_INFO) || {}
    if (!uid) {
      return
    }
    let temp = Storage.get(MY_ORDER_QUERY) || {}
    temp[uid] = Object.assign(temp[uid] || {}, obj)
    Storage.set(MY_ORDER_QUERY, temp)
  },

  // 获取我的订单搜索条件
  getMyOrderQuery() {
    const obj = Storage.get(MY_ORDER_QUERY) || {}
    const { uid } = Storage.get(USER_INFO) || {}
    if (!uid) {
      return
    }
    return (obj && obj[uid]) || {}
  },

  // 设置我的订单分页数据
  setMyOrderPagination(data) {
    Storage.set(MY_ORDER_PAGINATION, data)
  },

  // 获取我的订单分页数据
  getMyOrderPagination() {
    const obj = Storage.get(MY_ORDER_PAGINATION)
    return obj
  },

  // 清除我的订单分页数据
  clearMyOrderPagination() {
    Storage.remove(MY_ORDER_PAGINATION)
  },

  // 打开客服弹窗
  openCustomerServiceDialog() {
    event.emit(OPEN_CUSTOMER_SERVICE_DIALOG)
  },
  //
  getInquiryNoticeNumber() {
    const obj = Storage.get(INQUITY_NOTICE_NUMBER)
    return obj || {
      // 询单数量
      totalCount: 0, // 询单中总数
      sellerTotalCount: 0, // 作为票方询单中总数
      buyerTotalCount: 0, // 作为资金方询单中总数
    }
  },

  // 清除静态资源缓存
  clearStaticRequestCache() {
    Storage.remove(SENSITIVE_WORDS)
  }
}

export default user
