/* 组件注册 */
import Vue from 'vue'

// @/views/components/global 目录下的文件(组件命名需按 component-test 格式)自动挂载为全局组件
const subModuleList = require.context('@/views/components/global', false, /.vue$/)
subModuleList.keys().forEach(subRouter => {
  // eslint-disable-next-line no-magic-numbers
  let moduleName = subRouter.substring(2, subRouter.length - 4)
  Vue.component(moduleName, subModuleList(subRouter).default)
})
