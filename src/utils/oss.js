import commonApi from '@/apis/common'
import { randomString } from '@/common/js/util'
import { OSS_DIR, OSS_DIR_NAME } from '@/constant'
import axios from 'axios'

/**
 * 文件上传接口
 * @param {object} fileObj 文件对象
 * @param {string} directory 所属文件夹
 * @param {object} options 参数
 * @param {string} options.fileName 文件名
 * @param {string} options.prefix 文件名前缀
 * @param {function} options.onProgress 上传进度回调函数
 * @returns {Promise<any>} 操作后的promise回调
 */
export async function upload(fileObj, directory, options = {}) {
  if (!directory) {
    // eslint-disable-next-line no-console
    console.error('请指定文件夹')
  }
  const image = Object.keys(OSS_DIR).find(dir => OSS_DIR[dir] === directory)
  if (process.env.NODE_ENV === 'development' && !image) {
    const keys = Object
      .keys(OSS_DIR)
      .map(key => `OSS_DIR.${key}`)
      .join('、')
    throw new Error(`directory 属性必须为 ${keys} 其中一个`)
  }
  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line no-console
    console.log(`上传至【${OSS_DIR_NAME[directory]}】文件夹`)
  }
  // 获取policy信息
  const res = await commonApi.getUploadPolicy({ image })
  const { accessKeyId, dir, policy, signature, host } = res
  const getFileName = () => {
    const randomStringLength = 6
    let fileName = options.fileName || fileObj.name
    // 防止url超过数据库的255位限制
    if (fileName.length > 10) fileName = fileName.substring(fileName.length - 20, fileName.length)
    const randomName = `${randomString(randomStringLength, 'mix', options.prefix || '')}_${new Date().getTime()}_${fileName}`
    // 要上传到后端返回的指定路径下，否则报无权限
    return `${dir}/${randomName}`
  }
  const fileName = getFileName()
  // 拼接文件上传请求
  const fileReq = new FormData()
  fileReq.append('key', fileName)
  fileReq.append('policy', policy)
  fileReq.append('OSSAccessKeyId', accessKeyId)
  fileReq.append('success_action_status', '200')
  fileReq.append('signature', signature)
  fileReq.append('file', fileObj)
  await axios.post(
    host,
    fileReq,
    {
      headers: { 'Content-Type': 'multipart/form-data' }
    }
  )
  return `${host}/${encodeURIComponent(fileName)}` // encodeURIComponent  阿里云后台会二次转码 所以对应的前端也要转码
}

/**
 * [图片缩放](https://help.aliyun.com/document_detail/44688.html)
 * 通过 oss 的图片缩放功能返回图片缩略图，以优化缩略图的加载速度
 * @param {string} url oss 图片 url
 * @param {object} options oss 图片参数
 * @returns {string} 拼接后的 oss 图片 url
 */
export const getImageResizeUrl = (url, options) => {
  if (!url || typeof url !== 'string') {
    return url
  }
  if (!options || typeof options !== 'object') {
    return url
  }
  const keyMap = {
    // lfit（默认值）：等比缩放，缩放图限制为指定w与h的矩形内的最大图片。
    // mfit：等比缩放，缩放图为延伸出指定w与h的矩形框外的最小图片。
    // fill：将原图等比缩放为延伸出指定w与h的矩形框外的最小图片，之后将超出的部分进行居中裁剪。
    // pad：将原图缩放为指定w与h的矩形内的最大图片，之后使用指定颜色居中填充空白部分。
    // fixed：固定宽高，强制缩放。
    mode: 'm', // 指定缩放的模式。
    width: 'w', // 指定目标缩放图的宽度。
    height: 'h', // 指定目标缩放图的高度。
    longest: 'l', // 指定目标缩放图的最长边。
    smallest: 's', // 指定目标缩放图的最短边。
    limit: 'limit', // 指定当目标缩放图大于原图时是否进行缩放。
    color: 'color', // 当缩放模式选择为pad（缩放填充）时，可以设置填充的颜色。
  }
  const params = ['x-oss-process=image/resize']
  Object.entries(options).forEach(([key, value]) => {
    const realKey = keyMap[key] || key
    switch (key) {
      case 'width':
      case 'height':
        value = String(value).replace('px', '')
        break
      default:
        break
    }
    params.push(`${realKey}_${value}`)
  })
  const paramsString = params.join()
  return url.includes('?') ? `${url}&${paramsString}` : `${url}?${paramsString}`
}
