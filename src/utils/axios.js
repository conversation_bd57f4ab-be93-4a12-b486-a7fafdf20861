import axios from 'axios'
import { ElMessage } from 'element-plus'
import { showToast } from 'vant'
import { notBakHome } from '@/pc/constants/router-white-list'
import router from '@/pc/router'
import { useUserStore } from '@/pc/stores'
import { getMerchantsLoginRoutePath, isMerchantsBackend } from '@/pc/hooks/merchants'
import $event from '@/event/index'
import { OPEN_NEW_LOGIN } from '@/event/modules/site'
import i18n from '@/i18n'
import translateToLang from '@/i18n/translateLang'
import { deleteEmpty } from '@/common/js/util'
import '@/pc/utils/crypto-js'
import user from '@/pc/utils/user'
import { trackExposure } from '@/utils/tracking'
import { detectDeviceType, translateTextCustom } from '@/utils/utils'

/**
 * 处理 token 无效或会话过期的情况
 * @param {String} deviceType - 当前路由实例
 */
export async function handleTokenInvalid(deviceType) {
  try {
    const currentRoute = router.currentRoute.value
    const isMerchants = isMerchantsBackend(currentRoute?.meta)
    const isNotBakHome = !notBakHome(currentRoute)
    const isMobileResponsiveLayout = currentRoute?.path?.includes('/m/') // 响应式的几个h5页面
    const userStore = useUserStore()

    let path = null

    // 根据是否是商户后台，进行不同的跳转处理
    if (isMerchants) {
      path = getMerchantsLoginRoutePath(currentRoute) // 商户登录页跳转
    } else if (isMobileResponsiveLayout) {
      path = '/register-h5?type=login'
    } else {
      if (isNotBakHome) {
        path = deviceType === 'PC' ? '/mall' : '/home' // 跳转到商城首页
      }
      $event.emit(OPEN_NEW_LOGIN) // 弹出登录框
    }

    if (path) router.replace(path)
    // 执行登出操作
    userStore.logout()
  } catch (e) {
    console.log(e)
  }
}

function generateRequestId({ method, path, data }) {
  // 使用 MD5 哈希算法生成固定长度的请求ID
  const rawId = `${method}:${path}:${JSON.stringify(data)}`
  return window.CryptoJS?.MD5(rawId)?.toString() || rawId
}

/**
 * config 自定义配置项
 * @param withoutCheck 不使用默认的接口状态校验，直接返回 response
 * @param returnOrigin 是否返回整个 response 对象，为 false 只返回 response.data
 * @param showError 全局错误时，是否使用统一的报错方式
 * @param canEmpty 传输参数是否可以为空
 * @param timeout 接口请求超时时间，默认10秒
 * @param isCancelRequest 是否可以取消请求
 */
const customDefault = {
  showError: true,
  canEmpty: false,
  returnOrigin: false,
  withoutCheck: false,
  mock: false,
  timeout: 30000,
  isCancelRequest: true,
}

// 创建请求器
const service = axios.create(
  Object.assign(
    {
      baseURL: '',
      responseType: 'json',
      headers: {
        'Content-Type': 'application/json;charset=utf-8',
      },
    },
    customDefault,
  ),
)

// 添加请求拦截器
service.interceptors.request.use(
  (request) => {
    // 对请求参数进行处理，清除空参数
    if (!request.canEmpty) {
      request.data && (request.data = deleteEmpty(request.data))
      request.params && (request.params = deleteEmpty(request.params))
    }
    const token = user.getToken() || request.accessToken || ''
    const payPriceType = user.getPayPrice()
    request.headers.accessToken = token
    request.headers.Authorization = token
    request.headers.scene = user.getToken() ? 'sc' : 'agents'
    request.headers.channel = user.getToken() ? 'sc' : 'agents'
    request.headers.lang = i18n.global.locale.value
    request.headers.language = translateToLang.getTranslateToLang()
    request.headers.Website = import.meta.env.VUE_APP_NATIONAL_TYPE
    request.headers['price-type'] = payPriceType

    if (request.url.indexOf('http') === -1) {
      request.url = `${import.meta.env.VUE_APP_API_URL}/${request.url.replace(/^\//, '')}`
    }
    return request
  },
  (error) => {
    return Promise.reject(error)
  },
)

// 显示报错
const showError = (res) => {
  if (Object.prototype.toString.call(res) === '[object Error]') {
    // HTTP 请求失败
    console.error('服务器出错了，请刷新重试')
  } else {
    // HTTP 请求成功，但业务状态码为处理失败的情况
    console.log('res', res)
    console.error(res?.data?.msg || '业务处理失败')
  }
}
// 判断设备类型
const deviceType = detectDeviceType()

// object对象存放每次new CancelToken生成的方法
let source = new Map()

// 每次请求前都会把path放在此数组中，响应成功后清除此请求path
let requestList = []

// 定义取消方法
function cancelRequest(config, allCancel) {
  const requestId = generateRequestId(config)
  // 请求列表里存在此path，即发起重复请求，把之前的请求取消掉
  const isSameRequest = requestList.some((itemRequestId) => itemRequestId === requestId)
  if (isSameRequest) {
    source.get(requestId)(`终止请求: ${config.path}`)
  } else if (!config.path && allCancel) {
    // allCancel为true则请求列表里的请求全部取消
    requestList.forEach((el) => {
      source.get(el)('批量终止请求')
    })
  }
}

// 添加响应拦截器
service.interceptors.response.use(
  async (res) => {
    // 请求完成后，将此请求从请求列表中移除
    requestList = requestList.filter((requestId) => requestId === generateRequestId(res.config))
    // HTTP 状态码 2xx 状态入口，data.code 为 200 表示数据正确，无任何错误
    const SUCCESS_CODE = ['000000', '00000', 200, 0, '200']
    if (SUCCESS_CODE.includes(res.data.code)) {
      const { config } = res
      if (config?.webTracking) {
        const trackingObj = typeof config.webTracking === 'object' ? config.webTracking : {}
        let callbackParams = {}
        const resData = res.data.data || res.data || {}
        if (typeof trackingObj.callback === 'function') {
          callbackParams = trackingObj.callback(resData) || {}
          delete trackingObj.callback
        }
        trackExposure({
          rid: resData?.rid,
          ...trackingObj,
          ...callbackParams,
        })
      }
      // 接口响应为请求成功
      if (res.config.returnOrigin) {
        return res.data
      }
      if (res.data && res.data !== 0) {
        return res.data.data
      } else {
        return res.data || {}
      }
    } else if (res.config.withoutCheck) {
      return res.data
    } else {
      // 接口响应为请求失败
      const GATEWAY_TOKEN_ILLEGES_CODES = [
        'LOGIN_OUT', // token非法/已过期
        '01002', // token非法/已过期
        '04001', // token非法/已过期
        '011001', // token非法/已过期
        1005, // 认证失败
        1006, // 在其他地方登录，被踢下线
        401, // 权已过期,请重新登录
      ]
      if (GATEWAY_TOKEN_ILLEGES_CODES.includes(res.data?.code)) {
        handleTokenInvalid(deviceType)
      } else if (res.config.showError) {
        translateTextCustom(res?.data?.message || res?.data?.msg || '业务处理失败').then((textList) => {
          if (deviceType === 'PC') {
            ElMessage.error(textList[0])
          } else {
            showToast(textList[0]) // 非 pc 端提示用 Vant 组件
          }
        })
      }
      return Promise.reject(res)
    }
  },
  (error) => {
    // 非 2xx 状态入口
    // eslint-disable-next-line
    /**
     * config配置项
     * @param withoutCheck 不使用默认的接口状态校验，直接返回 response
     * @param returnOrigin 是否返回整个 response 对象，为 false 只返回 response.data
     * @param showError  全局错误时，是否使用统一的报错方式
     */
    const { config, response } = error
    // 判断是取消的请求不要弹出错误提示
    if (error.message.startsWith('终止请求')) {
      throw new Error(error.message)
    }
    if (config?.withoutCheck) {
      // 不进行状态状态检测
      return Promise.reject(response)
    }
    if (error.code === 'ECONNABORTED') {
      // 超时
      console.error('请求超时，请重试')
    } else if (!error.response) {
      // 前端异常
      console.error('请求失败，请检查您的网络连接')
    } else if (error.config.showError) {
      // 接口异常，且配置为使用统一错误提示
      showError(error)
    }
    return Promise.reject(error)
  },
)
// post、get封装
function requestFn(method, path, data = {}, options = {}) {
  // options = Object.assign(options, customDefault)
  options = {
    ...customDefault,
    ...options,
  }

  const requestId = generateRequestId({ method, path, data })
  // 取消上一次请求
  if (requestList.length) {
    cancelRequest({ method, path, data })
  }
  // 设置isCancelRequest为ture, 请求前将path推入requestList
  if (options.isCancelRequest) {
    requestList.push(requestId)
  }

  if (method === 'post') {
    return service.post(path, data, {
      cancelToken: new axios.CancelToken((c) => {
        source.set(requestId, c)
      }),
      ...options,
    })
  } else if (method === 'get') {
    return service.get(path, {
      params: data,
      cancelToken: new axios.CancelToken((c) => {
        source.set(requestId, c)
      }),
      ...options,
    })
  }
}
export const api = {
  axios: service, // 原始 axios 对象
  showError,

  // 重新封装 get 函数，统一使用方式
  get: (path, data, config) => requestFn('get', path, data, config),
  delete: (path, data, config) => service.delete(path, data, config),

  post: (path, data, config) => requestFn('post', path, data, config),
  put: (path, data, config) => service.put(path, data, config),
}

/**
 * 添加不同的 baseUrl
 * @param {string} baseUrl 需要添加的 baseUrl，默认为环境变量的 API 地址
 * @return {object} newApi 包含带 baseUrl 的 get, post, delete, put 方法
 */
export function apiAddBaseUrl(baseUrl) {
  baseUrl = (baseUrl || '').replace(/\/+$/, '')

  function _addBaseUrl(fn) {
    return function (url, ...args) {
      const fullUrl = `${baseUrl}/${url.replace(/^\/+/, '')}`
      return fn(fullUrl, ...args)
    }
  }

  function _init() {
    // 这里就是最终返回的值
    return {
      ...api,
      get: _addBaseUrl(api.get),
      post: _addBaseUrl(api.post),
      delete: _addBaseUrl(api.delete),
      put: _addBaseUrl(api.put),
    }
  }

  // 使用 Proxy 实现懒加载
  const handler = {
    get(target, prop) {
      if (!target._init) {
        target._init = _init()
      }
      return target._init[prop]
    },
  }

  // 使用 Proxy 实现懒加载
  return new Proxy({}, handler)
}

export default api
