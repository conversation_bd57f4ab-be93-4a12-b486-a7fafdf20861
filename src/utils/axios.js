/* eslint-disable max-depth */
import axios from 'axios'
import $event from '@/event/index'
import { SITE_TOKEN_EXPIRED, REAL_NAME_FAILURE_CODE } from '../event/modules/site'
import { deleteEmpty } from '@/common/js/util'
import user from './user'
import { browserType } from '@/common/js/env' // 判断浏览器类型
import { ISSUE_DRAFT_ERROR_CODE } from '@/constants/draft'
import { v4 as uuidv4 } from 'uuid'

let vueInstance = null
const isIe = browserType.indexOf('IE') !== -1
export function setVue(vue) {
  vueInstance = vue
}

// 接口前缀
const PREFIX = (id = '725') => `http://yapi.sdpjw.com/mock/${id}` // yapi mock 地址
const env = process.env.VUE_APP_API_ENV || process.env.NODE_ENV

/**
 * config 自定义配置项
 * @param withoutCheck 不使用默认的接口状态校验，直接返回 response
 * @param returnOrigin 是否返回整个 response 对象，为 false 只返回 response.data
 * @param showError 全局错误时，是否使用统一的报错方式
 * @param canEmpty 传输参数是否可以为空
 * @param mock 是否使用 mock 服务
 * @param mockId mock 服务对应的项目 id
 * @param timeout 接口请求超时时间，默认10秒
 * @param isCancelRequest 是否可以取消请求
 * @param isShowBatchError 是否统一显示批量操作的错误提示
 * @param isSdApi 是否用新的域名（深度项目调用海南的服务域名）
 */
const customDefault = {
  showError: true,
  canEmpty: false,
  returnOrigin: false,
  withoutCheck: false,
  mock: false,
  timeout: 15000,
  isCancelRequest: true,
  isShowBatchError: true
}

// 创建请求器
const service = axios.create(Object.assign({
  baseURL: '',
  responseType: 'json',
  withCredentials: true, // 容许跨域的时候在请求头里带上cookie waf拦截专用 禁止删除
  headers: {
    'Content-Type': 'application/json;charset=utf-8',
    'Client-Version': window.APP_VERSION || '',
    // 新客户端的Channel等于new_platform
    Channel: 'hn',
    scene: 'cj',
    'Device-Id': localStorage.getItem('DEVICE_UUID') || (() => {
      const uuid = uuidv4()
      localStorage.setItem('DEVICE_UUID', uuid)
      return uuid
    })()
  },
}, customDefault))

// 添加请求拦截器
service.interceptors.request.use(
  request => {
    const reqData = request.data || request.params
    if (reqData && !request.canEmpty) { // 对请求参数进行处理，清除空参数
      request.data = deleteEmpty(reqData)
    }
    if (request.params && !request.canEmpty) {
      request.params = deleteEmpty(request.params)
    }
    // 检测接口，根据环境自动切换前缀
    if (request.url.indexOf('http') === -1) {
      if (request.url[0] === '/') {
        request.url = request.url.substr(1)
      }

      request.url = `${env !== 'production' && request.mock ? PREFIX(request.mockId) : process.env.VUE_APP_API_URL}/${request.url}` // mock环境开发
    }

    // 若有做鉴权token，需要请求头自动加上token
    request.headers.accessToken = user.getToken()
    return request
  },
  error => {
    vueInstance.$message.error(error.toString())
    return Promise.reject(error)
  },
)

// 显示报错
export const showError = res => {
  if (Object.prototype.toString.call(res) === '[object Error]') {
    // HTTP 请求失败
    vueInstance.$message.error('服务器出错了，请刷新重试')
  } else {
    // HTTP 请求成功，但业务状态码为处理失败的情况
    vueInstance.$message.error(res?.data?.msg ?? '业务处理失败')
  }
}

// object对象存放每次new CancelToken生成的方法
let source = {}

// 每次请求前都会把path放在此数组中，响应成功后清除此请求path
let requestList = []

// 定义取消方法
function cancelRequest(path, allCancel) {
  // 请求列表里存在此path，即发起重复请求，把之前的请求取消掉
  if (path && requestList.includes(path) && typeof source[path] === 'function') {
    if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line no-console
      console.log('终止请求:', path)
    }
    source[path]('终止请求')
  } else if (!path && allCancel) {
    // allCancel为true则请求列表里的请求全部取消
    requestList.forEach(el => {
      source[el]('批量终止请求')
    })
  }
}

// 添加响应拦截器
service.interceptors.response.use(
  res => {
    // 获取请求的api
    const path = JSON.stringify(res.config.url)
    // 请求完成后，将此请求从请求列表中移除
    requestList = requestList.filter(item => !path.includes(item))

    // 深度首页接口请求处理
    if (res.data?.code === 0) {
      return res.data
    }

    // console.log(res)
    // HTTP 状态码 2xx 状态入口，data.code 为 200 表示数据正确，无任何错误
    const SUCCESS_CODE = 200
    if (res.data?.code === SUCCESS_CODE) {
      // 接口响应为请求成功
      if (res.config.returnOrigin) {
        res.config.isShowBatchError && res.data.data && handleBatchApiError(res.data.data)
        return res.data
      } else if (typeof res.data?.data !== 'undefined') {
        // 批量操作部分失败
        res.config.isShowBatchError && handleBatchApiError(res.data.data)
        return res.data.data
      } else {
        return res.data || {}
      }
    } else if (res.config?.withoutCheck) {
      return res.data
    } else {
      // 接口响应为请求失败
      const TOKEN_EXPIRED_CODE = 1005 // TOKEN 过期
      const TOKEN_NOLOGIN_CODE = 401 // TOKEN 过期
      const NO_ACCESS_PERMISSION = 403 // 无访问资源权限
      const LOGIN_DIFFERENT_PLACE_CODE = 1006 // 在其他地方登录，被踢下线
      const CLOSE_MARKET_CODE = 1019 // 闭市错误码
      const BROKE_LIMIT_RADAR_CODE = 3029 // 违约限制自动交易
      const BROKE_LIMIT_LIGHT_CODE = 3030 // 违约限制极速交易
      const CLOSE_RADAR_CODE = 3032 // 自动交易被后台关闭
      const CLOSE_FAST_CODE = 3034 // 极速交易被后台关闭
      if ([TOKEN_EXPIRED_CODE, LOGIN_DIFFERENT_PLACE_CODE, TOKEN_NOLOGIN_CODE, NO_ACCESS_PERMISSION].includes(res.data?.code)) {
        // 表示是否由 token 过期导致的请求失败
        res.$isTokenExpired = true
        // token 过期， 退出登录, 无权访问
        $event.emit(SITE_TOKEN_EXPIRED)
      } else if (res.data?.code === ISSUE_DRAFT_ERROR_CODE.REAL_NAME_EXPIRED) { // 实名失效
        $event.emit(REAL_NAME_FAILURE_CODE)
      } else if (res.config.showError) {
        // 避免错误提示提示多次
        vueInstance.$message.closeAll()
        showError(res)
      }
      if (res.data?.code === CLOSE_MARKET_CODE) {
        vueInstance.$store.dispatch('common/getCloseMarket')
        vueInstance.$store.dispatch('recognize-common/getCloseMarket')
      }
      if (res.data?.code === BROKE_LIMIT_RADAR_CODE) {
        vueInstance.$store.dispatch('common/getRadarLimit')
        vueInstance.$store.dispatch('recognize-common/getRadarLimit')
      }
      if (res.data?.code === BROKE_LIMIT_LIGHT_CODE) {
        vueInstance.$store.dispatch('common/getFastLimit')
        vueInstance.$store.dispatch('recognize-common/getFastLimit')
      }
      if ([CLOSE_RADAR_CODE, CLOSE_FAST_CODE].includes(res.data?.code)) {
        vueInstance.$store.dispatch('user/getCorpInfo')
        vueInstance.$store.dispatch('recognize-user/getCorpInfo')
      }
      return Promise.reject(res)
    }
  },
  error => { // 非 2xx 状态入口
    // eslint-disable-next-line
    /**
     * config配置项
     * @param withoutCheck 不使用默认的接口状态校验，直接返回 response
     * @param returnOrigin 是否返回整个 response 对象，为 false 只返回 response.data
     * @param showError  全局错误时，是否使用统一的报错方式
     */
    const { config, response } = error
    // 判断是取消的请求不要弹出错误提示
    if (error.message === '终止请求') {
      throw new Error('终止请求')
    }
    if (config?.withoutCheck) { // 不进行状态状态检测
      return Promise.reject(response)
    }
    // 避免错误提示提示多次
    vueInstance.$message.closeAll()
    if (error.message === 'Request aborted') { // 请求失败
      throw new Error(error.message)
    }

    if (error.code === 'ECONNABORTED') { // 超时
      vueInstance.$message.error('请求超时，请重试')
    } else if (!error.response) { // 前端异常
      vueInstance.$message.error('请求失败，请检查您的网络连接')
    } else if (error.config.showError) { // 接口异常，且配置为使用统一错误提示
      showError(error)
    }
    return Promise.reject(error)
  },
)

// 这里只做post封装演示，大家可以自己封装其他请求方法
function requestFn(method, path, data = {}, options = {}) {
  let newOptions = { ...customDefault, ...options } // 注意这里不是多层深拷贝
  // 取消上一次请求
  if (requestList.length) {
    cancelRequest(path)
  }
  // 设置isCancelRequest为ture, 请求前将path推入requestList
  if (newOptions.isCancelRequest) {
    requestList.push(path)
  }

  if (method === 'post') {
    return service.post(path, data, {
      cancelToken: new axios.CancelToken(c => {
        source[path] = c
      }),
      ...newOptions
    })
  } else if (method === 'get') {
    // 防止ie浏览器get请求使用缓存，在请求参数上加一个时间戳
    if (isIe) {
      data === null ? (data = { t: new Date().getTime() }) : (Object.assign(data, { t: new Date().getTime() }))
    }
    return service.get(path, {
      params: data,
      cancelToken: new axios.CancelToken(c => {
        source[path] = c
      }),
      ...newOptions
    })
  }
}

// 批量接口报错处理
function handleBatchApiError(data = {}) {
  try {
    // eslint-disable-next-line no-magic-numbers
    const isObject = Object.prototype.toString.call(data).slice(8, -1) === 'Object'
    if (!isObject) return
    if ('failNum' in data) {
      if (data.failNum) {
        vueInstance.$message.warning(`成功 ${data.successNum} 张，失败  ${data.failNum} 张`)
      }
    }
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log('handleBatchApiError :>> ', error)
  }
}

export const api = {
  axios: service, // 原始 axios 对象
  showError,

  // 重新封装 get 函数，统一使用方式
  get: (path, data, config) => requestFn('get', path, data, config),
  delete: (path, data, config) => service.delete(path, { data, ...config }),

  post: (path, data, config) => requestFn('post', path, data, config),
  put: (path, data, config) => service.put(path, data, config),
}

export default api
