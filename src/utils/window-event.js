// 监听storage事件
import eventMixin from '@/event/index'
import { MQTT_ORDER } from '@/event/modules/site'
// 在另一个页面退出登录，和登录的时候触发刷新其他页面
export const localStorageKey = 'loginOrLogout'
// 支付成功后，回调到成功页面，并且刷新资方页面
export const localStoragePaySuccessKey = 'paySuccess'

export const windowCommunication = {
  init: () => {
    // 窗口监听storage事件
    window.addEventListener('storage', e => {
      // IE 浏览器在当前窗口也会触发事件，跟其他浏览器行为不一致，这里做一下兼容
      if (document.hasFocus()) {
        return
      }
      // 登录、退出
      if (e.key === localStorageKey) {
        location.reload()
      }

      // 支付成功
      if (e.key === localStoragePaySuccessKey) {
        const { href } = window.location
        const flag = href.indexOf('user-center/buy-draft?tab=3') > -1 || (href.indexOf('/user-center/draft-detail') > -1 && href.indexOf('tab=3') > -1)
        flag && eventMixin.emit(MQTT_ORDER, { type: 'orderStatusRefresh', data: { orderNo: 'paySuccess' } })
      }
    })
  },
  // 触发storage事件
  trigger: (type = localStorageKey) => {
    localStorage.setItem(type, Math.random().toString())
  }
}
