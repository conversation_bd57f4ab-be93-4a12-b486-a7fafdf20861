// 中文标点
const zh = /[，‌。‌！‌？‌、‌；‌：`~…‌—_]/
// 英文标点
const en = /[,.!?`;~‌,:]/

// 图片
export const imgReg = /https?:\/\/[^"']*\.(?:png|jpg|jpeg|gif|webp|svg)/gi // 匹配图片格式

// 字符串是否包含标点符号
export const isPunctuation = (str) => {
  return zh.test(str) || en.test(str)
}

// 处理商品金额，小数点前面字体大，小数点后面字体小
export const priceFilter = {
  // 小数点前面
  radixPointBefore: (price) => {
    if (!price) return false
    price = price.toString()
    if (price && price.indexOf('.') !== -1) {
      return price.substring(0, price.indexOf('.'))
    } else {
      return price
    }
  },

  // 小数点后面
  radixPointAfter: (price) => {
    if (!price) return false
    price = price.toString()
    if (price && price.indexOf('.') !== -1) {
      return price.substring(price.indexOf('.'), price.length)
    } else {
      return '.00'
    }
  },
}

// 处理手机号中间4位为*
export const maskPhoneNumber = (phone) => {
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

// 节流
export function throttle(fn, delay) {
  let lastTime = 0
  return function (...args) {
    const now = Date.now()
    if (now - lastTime >= delay) {
      lastTime = now
      fn.apply(this, args)
    }
  }
}

// 防抖
export function debounce(fn, delay) {
  let timeoutId

  return function (...args) {
    // 如果已有定时器，则清除它
    if (timeoutId) clearTimeout(timeoutId)

    // 设置新的定时器，延迟执行函数
    timeoutId = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

export function isValidJSON(str) {
  if (!str) return false
  // 去除可能的首尾空白字符
  str = str.trim()

  // 正则检查是否是对象或数组格式的 JSON
  return (str.startsWith('{') && str.endsWith('}')) || (str.startsWith('[') && str.endsWith(']'))
}

// 设置翻译区域
export const translateDom = (className, delay = 10) => {
  setTimeout(() => {
    // 设置翻译区域
    window.translate.setDocuments(document.getElementsByClassName(className))
    // 执行翻译
    window.translate?.execute()
  }, delay)
}

// 手动翻译
export const translateTextCustom = (param) => {
  return new Promise((resolve) => {
    const isString = typeof param === 'string'
    try {
      if (!param || (!Array.isArray(param) && !isString)) {
        return ['']
      }
      const list = isString ? [param] : param
      // 如果不是中文
      if (localStorage.getItem('to') !== 'chinese_simplified') {
        window.translate?.request?.translateText(list, function (data) {
          resolve(data.text)
        })
      } else {
        resolve(list)
      }
    } catch (e) {
      resolve(isString ? [param] : param)
    }
  })
}

export function generateUUID() {
  return (Math.random().toString(36).substring(2) + Math.random().toString(36).substring(2)).substring(0, 16)
}

// 去除数组中相同的对象
export function filterDuplicatesByKey(array, key) {
  const seen = new Set()
  return array.filter((item) => {
    const value = JSON.stringify(item[key]) // 对对象或数组序列化
    if (seen.has(value)) {
      return false // 如果已存在，过滤掉
    }
    seen.add(value) // 如果不存在，添加到集合中
    return true
  })
}

export function simpleDeepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj
  return JSON.parse(JSON.stringify(obj))
}

// utils/copyToClipboard.js
export async function copyToClipboard(text) {
  if (!text) {
    return { success: false, message: '没有内容可复制！' }
  }

  // 优先使用 Clipboard API
  if (navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
    try {
      await navigator.clipboard.writeText(text)
      return { success: true, message: '复制成功！' }
    } catch (error) {
      console.error('Clipboard API 复制失败:', error)
      return { success: false, message: '复制失败，请重试。' }
    }
  }

  // 回退到 document.execCommand 方法
  try {
    const textarea = document.createElement('textarea')
    textarea.value = text
    textarea.style.position = 'fixed' // 防止页面跳动
    textarea.style.opacity = '0'
    document.body.appendChild(textarea)
    textarea.select()
    document.execCommand('copy')
    document.body.removeChild(textarea)
    return { success: true, message: '复制成功！' }
  } catch (error) {
    console.error('execCommand 复制失败:', error)
    return { success: false, message: '复制失败，请重试。' }
  }
}

// 检测设备类型
export const detectDeviceType = () => {
  const userAgent = navigator.userAgent || navigator.vendor || window.opera
  if (/windows phone/i.test(userAgent)) {
    return 'Mobile'
  } else if (/android/i.test(userAgent)) {
    return 'Mobile'
  } else if (/iPad|iPhone|iPod/i.test(userAgent) && !window.MSStream) {
    return 'iPad'
  } else if (/iPad/i.test(userAgent) && !window.MSStream) {
    return 'iPad'
  } else {
    return 'PC'
  }
}

// 去掉请求参数的前后空格
export const trimParams = (params) => {
  if (!params || typeof params !== 'object') return params

  return Object.keys(params).reduce((acc, key) => {
    const value = params[key]
    acc[key] = typeof value === 'string' ? value.trim() : value
    return acc
  }, {})
}

export const trimParamsChangeOrigin = (params) => {
  if (!params || typeof params !== 'object') return params

  return Object.keys(params).forEach((key) => {
    const value = params[key]
    if (typeof value === 'string') {
      params[key] = value.trim()
    }
  })
}

// oss 截图
export function formatVideoPosterUrl(url) {
  if (!url || typeof url !== 'string') return ''
  let concatStr = '?'
  if (url.includes('?')) {
    concatStr = '&'
  }
  return `${url}${concatStr}x-oss-process=video/snapshot,t_1000,f_jpg,m_fast,ar_auto`
}

/**
 * 对象转参数
 * @param {object} params 提交到后台参数对象
 * @returns {string} 转换成拼接后的字符串
 */
export function stringify(params) {
  return Object.keys(params)
    .map((k) => {
      // 如果是对象或者数组  要转换成字符串
      let value = params[k]
      let v = typeof value === 'object' && value !== null ? JSON.stringify(value) : value
      return `${k}=${v}`
    })
    .join('&')
}

export function setItemRIdRorList(rowList, rid) {
  if (rid) {
    rowList = rowList.map((item) => {
      return {
        ...item,
        rid,
      }
    })
  }
  return rowList
}
