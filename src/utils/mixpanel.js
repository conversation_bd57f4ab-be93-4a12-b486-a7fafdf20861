/* eslint-disable valid-jsdoc */
import Storage from '@/common/js/storage'
// import mixpanel from 'mixpanel-browser'
import { USER_INFO } from '@/constant-storage'
import store from '@/store/index'
import { THEME_NAME } from '@/constant'

// const TOKEN = '66000780ad538c0d3199f3fce883ea88' // 生产环境的 erp 专用token
// const QA_TOKEN = '71d9c12abb2dc02d4671b583f5f7742f' // qa 环境的token和深度公用

class Mixpanel {
  constructor() {
    // this.mixpanel = null
    this.mixpanel = {}
  }

  /**
   * 初始化
   * @returns {promise} 标识初始化是否完成的 promise
   */
  init() {
    if (this.mixpanel) {
      return Promise.resolve(this.mixpanel)
    } else {
      // const token = process.env.VUE_APP_API_ENV === 'production' ? TOKEN : QA_TOKEN
      return new Promise(resolve => {
        resolve(this.mixpanel)
        // mixpanel.init(token, {
        //   debug: process.env.VUE_APP_API_ENV !== 'production',
        //   loaded: () => {
        //     resolve(this.mixpanel)
        //   }
        // })
        // this.mixpanel = mixpanel
      })
    }
  }

  /**
   * 上报数据
   * @param {string} eventName 事件名
   * @param {any} properties 属性
   * @param  {...any} args 其他参数
   */
  async track(eventName, properties, ...args) {
    if (!this.mixpanel) {
      await this.init()
    }
    if (!properties) {
      properties = {}
    }
    const mobile = store.state.user.isLogined ? Storage.get(USER_INFO)?.mobile : null
    properties.用户ID = mobile || 'visitor'
    if (mobile) {
      properties.手机号 = mobile
    }
    properties.登录渠道 = `${THEME_NAME}-Web`
    const corpId = store.state.user.isLogined ? Storage.get(USER_INFO)?.corpId : null
    corpId && (properties.企业ID = corpId)
    // console.log('eventName', eventName, properties)
    // eslint-disable-next-line no-unused-vars
    let userCallback
    const userCallbackIndex = args.findIndex(arg => typeof arg === 'function')
    if (userCallbackIndex > -1) {
      userCallback = args[userCallbackIndex]
      args.splice(userCallbackIndex, 1)
    }
    return new Promise(resolve => {
      resolve()
      // 发送完成的回调
      // const callback = function(...callbackArgs) {
      //   // console.log('callback')
      //   userCallback && userCallback.apply(this, callbackArgs)
      //   resolve(callbackArgs)
      // }
      // this.mixpanel.track(eventName, properties, ...args.concat(callback))
    })
  }

  /**
   * 游客注册成功
   * @param {object} props 属性
   */
  async registerSuccess(props) {
    // await this.init()
    // const id = Mixpanel.getUserId(props.mobile)
    // this.mixpanel.alias(id)
    // this.mixpanel.people.set({
    //   手机号: props.mobile
    // })
    await this.track('注册_首页_等待注册结果_成功', {
      注册者姓名: props.realName,
      注册者性别: props.sex,
      注册者身份证号: props.idCard,
      注册者身份证有效期: props.idCardEndDate,
      注册者常用地址: props.detailedAddress,
    })
  }

  /**
   * 游客在首页点击注册
   */
  registerClick() {
    this.track('注册_首页_注册_点击')
  }

  /**
   * 游客在登录弹窗点击去注册
   */
  goRegisterClick() {
    this.track('注册_首页_去注册_点击')
  }

  /**
   * 深度票据网用户登录成功
   * @param {number} mobile 电话
   * @param {string} corpName 企业名称
   * @param {string} corpId 企业id
   */
  async login(mobile, corpName) {
    // await this.init()
    // const id = Mixpanel.getUserId(corpId || mobile)
    // this.mixpanel.identify(id)
    const props = {
      手机号: mobile,
      $name: mobile
    }
    if (corpName) {
      props['企业名称'] = corpName
    }
    // this.mixpanel.people.set(props)
    await this.track('登录_首页_登录弹窗_成功', {
      企业名称: corpName,
    })
  }

  /**
   * 登录失败
   */
  loginError(mobile, errorMsg) {
    this.track('登录_首页_登录弹窗_失败', {
      手机号: mobile,
      登录失败原因: errorMsg
    })
  }

  /**
   * 立即注册
   */
  registerImmediately(props) {
    this.track('注册_首页_立即注册_点击', {
      手机号: props.mobile,
      注册者姓名: props.realName,
      注册者性别: props.sex,
      注册者身份证号: props.idCard,
      注册者身份证有效期: props.idCardEndDate,
      注册者常用地址: props.detailedAddress,
    })
  }

  /**
   * 注册失败
   */
  registerError(props) {
    this.track('注册_首页_等待注册结果_失败', {
      手机号: props.mobile,
      注册失败原因: props.errorMsg,
      注册者姓名: props.realName,
      注册者性别: props.sex,
      注册者身份证号: props.idCard,
      注册者身份证有效期: props.idCardEndDate,
      注册者常用地址: props.detailedAddress,
    })
  }

  /**
   * 游客在注册弹窗点击获取验证码
   */
  getRegisterCode(mobile) {
    this.track('注册_首页_获取验证码_点击', {
      手机号: mobile
    })
  }

  /**
   * 游客获取验证码成功
   */
  getRegisterCodeSuccess(mobile) {
    this.track('注册_首页_获取验证码_成功', {
      手机号: mobile
    })
  }

  /**
   * 游客获取验证码失败
   */
  getRegisterCodeError(mobile, errorMsg) {
    this.track('注册_首页_获取验证码_失败', {
      手机号: mobile,
      验证码获取失败原因: errorMsg
    })
  }

  /**
   * 获取 userId，为了防止用户 id 重复，添加前缀区分
   * @param {number} userId 用户 id
   * @returns {string} 转换后的用户 id
   */
  static getUserId(userId) {
    return `shendu_${userId}`
  }

  /**
   * 用户在开户引导弹窗点击马上开户
   */
  goOpenAccountClick() {
    this.track('开通渠道_开户引导页面_马上开户_点击')
  }

  /**
   * 用户更新认证资料
   */
  updateCertificationInformationClick(props) {
    this.track('更新认证资料_基本信息页_更新认证资料_点击', props)
  }

  /**
   * 基本信息-渠道开通-重新开通
   */
  goOpenTradingAccountClick(props, isRetry = false) {
    this.track(`开通渠道_基本信息页_${isRetry ? '重新开通' : '立即开通'}_点击`, {
      手机号: props?.mobile,
      企业名称: props?.corpName,
      渠道名称: props?.paymentChannelName,
    })
  }
}

export default new Mixpanel()
