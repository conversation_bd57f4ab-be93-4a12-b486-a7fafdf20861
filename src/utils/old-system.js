// 旧系统（原深度票据网）相关代码

// qa 环境域名
export const DOMAIN_QA = 'https://shendu-dev1.sdpjw.cn'
// 生产环境域名
export const DOMAIN_PROD = 'https://sdpjw.cn'

const envDomainMap = {
  // development: 'https://shendu-dev1.sdpjw.cn',
  // staging: 'https://shendu-test1.sdpjw.cn',
  // production: 'https://sdpjw.cn'
  development: 'https://dev-discount.sdpjw.cn', // 开发/测试
  staging: 'https://staging-discount.sdpjw.cn', // 预发
  production: 'https://discount.sdpjw.cn' // 线上
}

// 当前环境对应域名
export const DOMAIN = envDomainMap[process.env.VUE_APP_API_ENV]
// export const DOMAIN = (process.env.VUE_APP_API_ENV || process.env.NODE_ENV) === 'development' ? DOMAIN_QA : DOMAIN_PROD

// 发送的事件
export const SEND_EVENTS = {
  PARENT_DOMAIN: 'PARENT_DOMAIN', // 当前域名，用于通知子系统当前域名是多少
  MT_SCROLLTOP: 'MT_SCROLLTOP' // 秒贴页面滚动高度监测
}

let messageCallback = null // message 事件对应的回调函数
const messageCallbacks = []
// 监听消息
export const onMessage = (cb, vm) => {
  if (!vm) {
    // eslint-disable-next-line no-console
    console.error('请传入组件实例（this）')
  }
  messageCallbacks.push({
    vm,
    cb
  })
  if (!messageCallback) {
    messageCallback = event => {
      if (event.origin === DOMAIN) {
        messageCallbacks.forEach(callbackObj => callbackObj.cb(event.data))
      }
    }
    window.addEventListener('message', messageCallback)
  }
  // 销毁前取消事件监听
  vm.$on('hook:beforeDestroy', () => {
    const callbacks = messageCallbacks.filter(callbackObj => callbackObj.vm === vm)
    callbacks.forEach(callbackObj => {
      const index = messageCallbacks.indexOf(callbackObj)
      index > -1 && messageCallbacks.splice(index, 1)
    })
    if (!messageCallbacks.length) {
      window.removeEventListener('message', messageCallback)
      messageCallback = null
    }
  })
}
// 发送消息
export const postMessage = (iframeRef, data) => {
  iframeRef.contentWindow.postMessage(data, '*')
}

// 监听秒贴页面滚动的高度，用于解决秒贴iframe 引入页面弹框定位的问题
export const onScrollFn = (iframeRef, vm) => {
  parent.window.addEventListener('scroll', () => {
    // 对有DOCTYPE申明的页面 可以使用：document.documentElement.scrollTop  对没有DOCTYPE申明的页面 可以使用：document.body.scrollTop safari 比较特殊 可以使用 window.pageYOffset
    const scrollTop = parent.window.document.documentElement.scrollTop || parent.window.document.body.scrollTop || parent.window.pageYOffset
    postMessage(iframeRef, {
      event: SEND_EVENTS.MT_SCROLLTOP,
      data: scrollTop
    })
  })
  vm.$on('hook:beforeDestroy', () => {
    parent.window.removeEventListener('scroll', () => {
      // 对有DOCTYPE申明的页面 可以使用：document.documentElement.scrollTop  对没有DOCTYPE申明的页面 可以使用：document.body.scrollTop safari 比较特殊 可以使用 window.pageYOffset
      const scrollTop = parent.window.document.documentElement.scrollTop || parent.window.document.body.scrollTop || parent.window.pageYOffset
      postMessage(iframeRef, {
        event: SEND_EVENTS.MT_SCROLLTOP,
        data: scrollTop
      })
    })
  })
}
