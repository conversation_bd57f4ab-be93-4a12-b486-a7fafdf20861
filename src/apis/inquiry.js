// 询单模块接口
import { api } from '@/utils/axios.js'

const inquiryApi = {
  // 订单咨询
  inquiry(body) {
    return api.post('/api/platform/draft/consult/inquiry', body)
  },
  // 批量询单
  inquiryBatch(body) {
    return api.post('/api/platform/draft/consult/batchInquiry', body)
  },

  // 资金方询单列表
  buyerConsultOrders(body) {
    return api.post('/api/platform/draft/consult/buyerConsultOrders', body)
  },
  // 删除询单记录
  buyerDeleteOrders(id) {
    return api.delete(`/api/platform/draft/consult/delete?id=${id}`, null)
  },

  // 订单咨询问题情况
  inquiryState(orderNo) {
    return api.get(`/api/platform/draft/consult/inquiryState?orderNo=${orderNo}`)
  },

  // 询单回复
  inquiryReply(body) {
    return api.post('/api/platform/draft/consult/inquiryReply', body)
  },
  // 询单回复后修改
  inquiryReplyUpdate(body) {
    return api.post('/api/platform/draft/consult/inquiryReplyUpdate', body)
  },

  // 票方询单列表
  sellerConsultOrders(body) {
    return api.post('/api/platform/draft/consult/sellerConsultOrders', body)
  },

  // 询单催回
  consultUrgeToReply(id) {
    return api.get(`/api/platform/draft/consult/consultUrgeToReply?billConsultOrderDetailId=${id}`)
  },

  // 背书手数-历史回复
  historyInquiryEndorse(id) {
    return api.get(`/api/platform/draft/consult/historyInquiryEndorse?billOrderId=${id}`)
  },

  // 询单模板列表
  consultTemplateList() {
    return api.get('/api/platform/draft/consultTemplate/list')
  },

  // 查询单个询单模板
  consultTemplateSingle(id) {
    return api.get(`/api/platform/draft/consultTemplate/getOne?id=${id}`)
  },

  // 询单单个模板删除
  consultTemplateDelete(id) {
    return api.get(`/api/platform/draft/consultTemplate/delete?id=${id}`)
  },

  // 询单模板保存
  consultTemplateSave(data) {
    return api.post('/api/platform/draft/consultTemplate/save', data)
  },

  // 询单模板更新
  consultTemplateUpdate(data) {
    return api.post('/api/platform/draft/consultTemplate/update', data)
  },

  // 资金方手动结束询单
  finishConsultOrder(id) {
    return api.get(`/api/platform/draft/consult/finishConsultOrder?billConsultOrderId=${id}`)
  },

  // // 询单模板数量
  // consultTemplateCount() {
  //   return api.get('/api/platform/draft/consultTemplate/count')
  // },

  // 查询询单数量
  consultTabNum(body) {
    return api.post('/api/platform/draft/consult/consultTabNum', body, {
      mock: false,
      isCancelRequest: false
    })
  },
}

export default inquiryApi
