// 议价模块接口
import { api } from '@/utils/axios.js'

const bargainApi = {

  // 申请议价
  postBargain(body) {
    return api.post('/api/platform/draft/bargain/postBargain', body, {
      mock: false,
    })
  },

  // 撤销议价
  revokeBargain(body) {
    return api.post('/api/platform/draft/bargain/revokeBargain', body, {
      mock: false
    })
  },

  // 同意议价
  confirmBargain(body) {
    return api.post('/api/platform/draft/bargain/confirmBargain', body, {
      mock: false
    })
  },

  // 取消议价
  cancelBargain(body) {
    return api.post('/api/platform/draft/bargain/cancelBargain', body, {
      mock: false
    })
  },

  // 还价
  dickerBargain(body) {
    return api.post('/api/platform/draft/bargain/dickerBargain', body, {
      mock: false
    })
  },

  // 查询议价列表
  traderCorpBargainList(body) {
    return api.post('/api/platform/draft/bargain/traderCorpBargainList', body, {
      mock: false,
      isCancelRequest: true
    })
  },

  // 撤销还价
  revokeDickerBargain(body) {
    return api.post('/api/platform/draft/bargain/revokeDickerBargain', body, {
      mock: false
    })
  },

  // 同意还价
  confirmDickerBargain(body) {
    return api.post('/api/platform/draft/bargain/confirmDickerBargain', body, {
      mock: false
    })
  },

  // 取消还价
  cancelDickerBargain(body) {
    return api.post('/api/platform/draft/bargain/cancelDickerBargain', body, {
      mock: false
    })
  },

  // 重新发起议价
  rePostBargain(body) {
    return api.post('/api/platform/draft/bargain/rePostBargain', body, {
      mock: false
    })
  },

  // 查询议价数量
  bargainTabNum(body) {
    return api.post('/api/platform/draft/bargain/bargainTabNum', body, {
      mock: false,
      isCancelRequest: false
    })
  },
}

export default bargainApi
