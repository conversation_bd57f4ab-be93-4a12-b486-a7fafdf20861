import { api, apiAddBaseUrl } from '@/utils/axios.js'

const newApi = apiAddBaseUrl(import.meta.env.VUE_APP_MALL_API_URL)

/************************************ 数字人模块 ************************************/

// 获取验证码
export const getSmsCode = (data) => api.get('/user/login/smsCode', data)
// 登录接口
export const postLoginApi = (data) => api.get('/user/login', data)

// 全局搜索
export const search = (params) => api.get('/index/search/tips', params)
// 外贸搜索
export const outsideTradeSearch = (params) => api.get('/index/foreign/search/tips', params)
// 获取用户信息
export const getLoginUserInfo = (data) => api.get('/user/loginInfo', data)

/************************************ 信息；录入接口块 ************************************/
// 首页--申请报名加盟
export const applyJoin = (body) => api.post('/req/zh/join', body)

// 申请入驻门店
export const applyForeignJoin = (body) => api.post('/req/foreign/join', body)

/************************************ 买家、卖家中心模块 ************************************/
// 商城严选-获取OSS临时凭证
export const getScyxUploadPolicy = (params) => newApi.get('/api/user-web/oss/stsToken', params)

// 获取验证码
export const smsSend = (body) => newApi.post('/api/user-web/sms/sendCode', body)

// 卖家/买家注册
export const userRegister = (body) => newApi.post('/api/scyx/user/register', body, { returnOrigin: true })

// 密码登录接口
export const scyxPwdLogin = (data) => newApi.post('/api/scyx/user/pwdLogin', data)

// 验证码登录接口
export const scyxCodeLogin = (data) => newApi.post('/api/scyx/user/codeLogin', data)

// 重置密码登录接口
export const scyxResetPwdLogin = (data) => newApi.post('/api/scyx/user/resetPwd', data)

// 获取商城严选用户信息
export const getScyxUserInfo = (data) => newApi.get('/api/scyx/user/userInfo', data)

// 留言
export const leaveMessage = (data) => newApi.post('/api/user-web/client/leaveMessage', data)

// 留言
export const putError = (data) => newApi.post('/api/user-web/putError', data)

// 查询国家列表
export const getCountryList = (data) => newApi.get('/api/common-web/client/country/getList', data)

// 根据国家code,上级code查询地区
export const getRegionList = (data) => newApi.get('/api/common-web/client/region/getList', data)
