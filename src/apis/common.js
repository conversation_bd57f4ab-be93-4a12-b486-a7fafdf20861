import { api } from '@/utils/axios.js'

const commonApi = {
  // 获取服务器当前时间戳
  getSysCurrentTime(params) {
    return api.get('/api/platform/operation/common/getSysCurrentTime', params, {
      mock: false,
      showError: false
    })
  },
  // 获取京东jsTicket及Signature签名
  getSignature(params) {
    return api.post('/api/platform/jdSignature/getSignature', params, {
      mock: false,
      returnOrigin: true,
      withoutCheck: true,
      showError: false
    })
  },
  // 获取批量单收银台批次号
  getBatchPayBatchNo(params) {
    return api.post('/api/platform/jdSignature/getBatchPayBatchNo', params, {
      mock: false,
      returnOrigin: true,
      withoutCheck: true,
      showError: false
    })
  },
  // 文件上传-获取policy授权信息
  getUploadPolicy(body) {
    return api.get('/api/platform/operation/common/upload/policy', body, {
      mock: false
    })
  },
  // 获取未来所有假期(不包含双休)
  getHolidays(params) {
    return api.get('/api/platform/operation/common/getHoliday', params, {
      mock: false,
      isCancelRequest: false
    })
  },
  // 获取识票助手支持的银行列表
  getBankInfo(params) {
    return api.get('/api/platform/noauth/qs/listLogo', params, {
      mock: false,
    })
  },

  // 渠道合作页面填写联系方式
  addCustomer(params) {
    return api.post('/api/platform/channelCustomer/save', params, {
      mock: false,
    })
  },

  // 经济+页面填写联系方式
  brokerBankOpenAppointSave(params) {
    return api.post('/api/platform/brokerBankOpenAppoint/save', params, {
      mock: false,
    })
  },

  // 获取运营闭市配置信息
  getCloseMarket() {
    return api.get('/api/platform/operation/common/getCloseMarket', {}, {
      mock: false,
    })
  },
  // 获取违约限制雷达交易
  getRadarLimit() {
    return api.get('/api/platform/punish/order/limit/accept/radar', {}, {
      mock: false,
    })
  },
  // 获取违约限制光速交易
  getFastLimit() {
    return api.get('/api/platform/punish/order/limit/publish/light', {}, {
      mock: false,
    })
  },

  // 获取交易奖励数据
  getActivityCountAndAmt() {
    return api.get('/api/platform/activity/activityCountAndAmt', {}, {
      mock: false,
    })
  },

  // 获取服务费收取规则
  getServicesRule() {
    return api.get('/api/platform/order/billOrderPlatformServiceFee/config', {}, {
      mock: false
    })
  },

  // 获取发布权限开关设置
  querySwitchAuth() {
    return api.get('/api/platform/operation/controlSwitch/getSwitch', {}, {
      mock: false
    })
  },

  // 通过授权码通知后端开始下载客户端
  qqlDownload(params) {
    return api.get('/api/platform/noauth/qs/qqlDownload', params, {
      mock: false
    })
  },
  // 投诉建议提交
  postComplainSuggest(body) {
    return api.post('/api/platform/operation/complaintAdvice/submit', body, { mock: false })
  },
  // 查询投诉记录数量
  queryCountByCorpId() {
    return api.post('/api/platform/operation/complaintAdvice/countByCorpId', {
      mock: false,
    })
  },
  // 更改投诉记录数量
  updateCountByCorpId() {
    return api.post('/api/platform/operation/complaintAdvice/updateByCorpId', {
      mock: false,
    })
  },
  // 查询投诉记录
  queryListByCorpId(body) {
    return api.post('/api/platform/operation/complaintAdvice/listByCorpId', body, {
      mock: false,
    })
  },

  // 获取新一代配置
  getNewVersionDraftConfig(params) {
    return api.post('/api/platform/newDraft/getConfiguration', params, {
      mock: false,
      showError: false
    })
  },
  // 后端解析excl接口
  parseExcel(body) {
    return api.post('/api/platform/basic/parseExcel', body, {
      mock: false
    })
  },
  // 获取配置类接口（新）
  getNewConfig(body) {
    return api.get('/api/platform/loginConfig/getConfig', body, {
      mock: false,
    })
  },
  // 查询更新迭代内容
  queryPublishContent(body) {
    return api.post('/api/platform/content/getUpdateIterativeContent', body, {
      mock: false,
    })
  },
  // 更新迭代内容标记已读
  postReadPublishContent(body) {
    return api.post('/api/platform/content/updateIterativeContentRead', body, {
      mock: false,
    })
  },
  // 信息驾驶舱---获取
  getInfoCockpitToken(params) {
    return api.post('/api/platform/sdsk/getToken', params, {
      mock: false,
    })
  },
}

export default commonApi
