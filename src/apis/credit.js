// 我的信用相关接口
import { api } from '@/utils/axios.js'

const creditApi = {
  // 获取我的信用信息
  getMyCredit(body) {
    return api.get('/api/platform/credit/myCredit', body, {
      mock: false,
      isCancelRequest: false
    })
  },

  // 信用分明细列表
  getCreditList(body) {
    return api.post('/api/platform/credit/list/detail', body, {
      mock: false,
    })
  },

  // 根据企业id查询信用数据
  getByCorpId(body) {
    return api.get('/api/platform/credit/getByCorpId', body, {
      mock: false,
    })
  },

  // 判断是否提示信用分
  getCreditRemind() {
    return api.get('/api/platform/credit/judge/remind', {}, {
      mock: false,
      showError: false
    })
  },

  // 关闭信用分提示
  closeCreditRemind() {
    return api.get('/api/platform/credit/closeCreditRemind', {}, {
      mock: false,
      showError: false
    })
  },

}

export default creditApi
