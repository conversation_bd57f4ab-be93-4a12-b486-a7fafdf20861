import { api } from '@/utils/axios.js'

const openAccountApi = {
  // 身份证正面oc r
  postOcrIdentityCardFront(body) {
    return api.get('/api/platform/corpOpenInfo/ocrIdentityCardFront', body, {
      mock: false
    })
  },
  // 身份证背面ocr
  postOcrIdentityCardBack(body) {
    return api.get('/api/platform/corpOpenInfo/ocrIdentityCardBack', body, {
      mock: false
    })
  },
  // 营业执照ocr
  postOcrBusinessLicense(body) {
    return api.get('/api/platform/corpOpenInfo/ocrBusinessLicense', body, {
      mock: false
    })
  },
  // 查询企业行业列表
  getCompanyIndustryList(body) {
    return api.get('/api/platform/corpOpenInfo/companyIndustryList', body, {
      mock: false
    })
  },
  // 保存商户开户信息
  postCorpOpenInfoSave(body) {
    return api.post('/api/platform/corpOpenInfo/save', body, {
      mock: false,
      canEmpty: true
    })
  },
  // 更新商户开户信息
  corpOpenInfoUpdate(body) {
    return api.post('/api/platform/corpOpenInfo/update', body, {
      mock: false,
      canEmpty: true
    })
  },
  // 查询开户流程阶段
  // getCorpOpenInfoStage(type) {
  //   return api.get(`/api/platform/corpOpenInfo/stage${type ? `?type=${type}` : ''}`, null, {
  //     mock: false
  //   })
  // },
  getCorpOpenInfoStage(body) {
    return api.post('/api/platform/corpOpenInfo/stage', body, {
      mock: false
    })
  },
  // 保存商户绑定的银行卡信息
  postSaveBank(body) {
    return api.post('/api/platform/corpOpenInfo/saveBank', body, {
      mock: false
    })
  },
  // 小额打款验证
  putVerifyJdAmount(verifyAmount) {
    return api.put(`/api/platform/corpOpenInfo/${verifyAmount}/verifyCheckAmount`, null, {
      mock: false,
    })
  },
  // 查询商户开户信息
  getCorpOpenInfo(body) {
    return api.get('/api/platform/corpOpenInfo', body, {
      mock: false
    })
  },
  // 查询商户开户的银行信息
  getCorpOpenInfoBank(body) {
    return api.get('/api/platform/corpOpenInfo/bankAccount', body, {
      mock: false
    })
  },
  // 模糊查询银行信息
  getBankSearch(body) {
    return api.get('/api/platform/operation/bank/search', body, {
      mock: false
    })
  },
  // 根据银行行号查询
  getBankInfo(body) {
    return api.get('/api/platform/operation/bank/getBankInfo', body, {
      mock: false
    })
  },
  // 更新开户信息的银行账号信息
  updateAccountBank(body) {
    return api.get('/api/platform/corpOpenInfo/updateAccountBank', body, {
      mock: false
    })
  },
  // 查询支付渠道账户列表
  getPaymentAccountList(body) {
    return api.get('/api/platform/corp/paymentAccountList', body, {
      mock: false
    })
  },
  // 判断企业是否重复
  getJudgeRepeatCorp(body) {
    return api.post('/api/platform/corpOpenInfo/judgeRepeatCorp', body, {
      mock: false
    })
  },
  // 获取人脸识别的url
  postFaceUrl(body) {
    return api.post('/api/platform/corpOpenInfo/faceUrl', body, {
      mock: false
    })
  },
  // 查询人脸识别认证结果
  postQueryFaceResult(body) {
    return api.post('/api/platform/corpOpenInfo/queryFaceResult', body, {
      mock: false
    })
  },
  // 开通未开通电子交易账户
  paymentAccountOpenApply(paymentChannel) {
    return api.post(`/api/platform/corp/paymentAccountOpenApply/${paymentChannel}`, null, {
      mock: false
    })
  },
  // 判断企业是否有风险
  judgeRiskCorp(body) {
    return api.get('/api/platform/corpOpenInfo/judgeRiskCorp', body, {
      mock: false
    })
  },
  // 推送未处理的预警通知
  pushUnHandleWarnNotice(body) {
    return api.post('/api/platform/corp/pushUnHandleWarnNotice', body, {
      mock: false
    })
  },
  // 启信宝企业信息查询校验
  getQiXinBaoCorpInfo(body) {
    return api.post('/api/platform/corp/queryTraderCorpBusiness', body, {
      mock: false
    })
  },
  // 经办人三要素判断
  agentUserCheck(body) {
    return api.post('/api/platform/corpOpenInfo/agentUserCheck', body, {
      mock: false
    })
  },
  // 风控-企业基本信息
  checkBasicCorpInfo(body) {
    return api.post('/api/platform/corpOpenInfo/risk/basicCorpInfo', body, {
      mock: false
    })
  },
  // 风控-企业法人信息
  checkLegalInfo(body) {
    return api.post('/api/platform/corpOpenInfo/risk/legalInfo', body, {
      mock: false
    })
  },
  // 风控-企业股东信息
  checkStockholderInfo(body) {
    return api.post('/api/platform/corpOpenInfo/risk/stockholderInfo', body, {
      mock: false
    })
  },
  // 风控-企业实控人信息
  checkControllerInfo(body) {
    return api.post('/api/platform/corpOpenInfo/risk/controllerInfo', body, {
      mock: false
    })
  },
  // 风控-企业受益人信息
  checkBeneficiaryInfo(body) {
    return api.post('/api/platform/corpOpenInfo/risk/beneficiaryInfo', body, {
      mock: false
    })
  },
  // 打款失败修改银行卡密码
  pateBank(body) {
    return api.post('/api/platform/corpOpenInfo/upateBank', body, {
      mock: false
    })
  },
  // 渠道开通/实名认证是否存在在途申请
  getExistOnPassageApply(body) {
    return api.post('/api/platform/corpOpenInfo/existOnPassageApply', body, {
      mock: false
    })
  },
  // E签宝标准协议签署认证
  verifyESignProtocol(body) {
    return api.post('/api/platform/contract/signProtocol', body, {
      mock: false
    })
  },
  // 法人三要素校验
  verifyLegalThreeElements(body) {
    return api.post('api/platform/corpOpenInfo/verifyLegalThreeElements', body, {
      mock: false
    })
  },
}

export default openAccountApi
