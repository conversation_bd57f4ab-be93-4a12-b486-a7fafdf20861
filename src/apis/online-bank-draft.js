import { api } from '@/utils/axios.js'

const onlineBankDraftApi = {
  // 验证新网网银授权
  validateXwBankSign() {
    return api.post('/api/platform/bill/order/bankDraft/validateXwBankSign', null, {
      mock: false,
      showError: false
    })
  },

  // 银行数据同步
  synchronizeBankData() {
    return api.post('/api/platform/bill/order/bankDraft/synchronizeBankData', null, {
      mock: false,
      timeout: 30000
    })
  },

  // 网银票据列表
  getBankDraftPageList(body) {
    return api.post('/api/platform/bill/order/bankDraft/bankDraftPageList', body, {
      mock: false,
    })
  },

  // 通过网银票据id查询详情
  queryDetailsByBankDraftId(body) {
    return api.post('/api/platform/bill/order/bankDraft/queryDetailsByBankDraftId', body, {
      mock: false,
    })
  },

  // 请求新网授权
  xwBankSign(body) {
    return api.post('/api/platform/bill/order/bankDraft/xwBankSign', body, {
      mock: false,
    })
  },

  // 上传 base64 票面
  uploadBase64DraftImg(body) {
    return api.post('/api/platform/base64UploadImgToOss', body, {
      mock: false
    })
  }
}

export default onlineBankDraftApi
