import { api } from '@/utils/axios.js'

const avatarUrl = import.meta.env.VUE_APP_AVATAR_API_URL
const aiUrl = import.meta.env.VUE_APP_AI_API_URL
const readyToken = import.meta.env.VUE_APP_READY_TOKEN

const mattingUrl = import.meta.env.VUE_APP_MATTING_API_URL

export const getUploadPolicy = (params) => api.get(avatarUrl + '/api/aiHuman/common/upload/policy', params)
// 生成视频
export const generateVideo = (data) => api.post(avatarUrl + '/api/aiHuman/core/generateVideo', data)
// 背景模版列表
export const listBackgroundTemplate = () => api.get(avatarUrl + '/api/aiHuman/core/listBackgroundTemplate')
// 数字人模版列表
export const listAiHumanModel = () => api.get(avatarUrl + '/api/aiHuman/core/listAiHumanModel')
// 短视频管理列表
export const pageListWorksTask = (data) => api.post(avatarUrl + '/api/aiHuman/core/pageListWorksTask', data)
// 重新生成视频
export const reGenerateVideo = (data) => api.post(avatarUrl + '/api/aiHuman/core/reGenerate', data)
// 短视频详情
export const worksTaskDetail = (id) => api.get(avatarUrl + `/api/aiHuman/core/worksTaskDetail/${id}`)
// 试听
export const auditionAudioByText = (data) => api.post(avatarUrl + '/api/aiHuman/core/auditionAudioByText', data)
// 视频翻译
export const transVideo = (data) => api.post(avatarUrl + '/api/aiHuman/core/transVideo', data)
// 图片生成视频
export const picToVideo = (data) => api.post(avatarUrl + '/api/aiHuman/core/picToVideo', { data })
// 语音变声列表
export const listVoiceModel = (data) => api.post(avatarUrl + '/api/aiHuman/core/listVoiceModel', data)
// 提交语音变声文件
export const uploadVoiceFile = (data) => api.post(avatarUrl + '/api/aiHuman/core/uploadVoiceFile', { data })
// 智能优化接口‘
export const optimizeDescription = (params) => api.post(avatarUrl + '/api/aiHuman/chat/optimizeDescription', params)

// ai 聊天推流
export const login = (body) => api.post(avatarUrl + '/sso/login', body)

// #######################################  以下是AI 服务  ##############################################
// ai 聊天推流
export const getsuggested = (body) => api.post(`${aiUrl}/api/chatrobot/agents/suggested`, body)

export const historyChats = (body) => api.post(`${aiUrl}/api/chatrobot/agents/history`, body, { accessToken: readyToken })

export const stopSse = (body) => api.post(`${aiUrl}/api/chatrobot/agents/stopChat`, body, { accessToken: readyToken })

export const isLike = (body) => api.post(`${aiUrl}/api/chatrobot/agents/like`, body, { accessToken: readyToken })
// 翻译
export const translate = (body) => api.post(`${aiUrl}/api/business/translate/translate`, body, { accessToken: readyToken, timeout: 50 * 1000 })

export const plan = (body) => api.post(`${aiUrl}/api/chatrobot/agents/plan`, body, { returnOrigin: true, accessToken: readyToken })

export const handlePicture = (body) => api.post(`${mattingUrl}/matting/upload`, body)
