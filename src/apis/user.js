import { api } from '@/utils/axios.js'

const userApi = {
  // 企业成员手机号码+密码登录
  postLogin(body) {
    return api.post('/api/platform/user/login/passwordLogin', body, {
      mock: false,
      showError: false
    })
  },

  // 快捷登录前检查是否需要补充三要素
  beforeCodeLogin(body) {
    return api.post('/api/platform/user/register/verificationBasicInfoQuick', body, {
      mock: false
    })
  },

  // 企业成员手机号码+验证码登录
  codeLogin(body) {
    return api.post('/api/platform/user/login/mobileVerifyCodeLogin', body, {
      mock: false
    })
  },

  // 验证码登录，发送手机验证码
  loginSendPhoneVerifyCode(body) {
    return api.post('/api/platform/user/login/sendPhoneVerifyCode', body, {
      mock: false
    })
  },

  // 用户退出登录
  postUserLogout() {
    return api.post('/api/platform/user/logout', null, {
      mock: false
    })
  },

  // 注册用户，发送手机验证码
  postSendPhoneVerifyCode(body) {
    return api.post('/api/platform/user/register/sendPhoneVerifyCode', body, {
      mock: false
    })
  },

  // 注册前信息核验
  beforePostRegisterUser(body) {
    return api.post('/api/platform/user/register/verificationBasicInfo', body, {
      mock: false,
    })
  },

  // 企业成员用户注册
  postRegisterUser(body) {
    return api.post('/api/platform/user/register/registerUser', body, {
      mock: false,
    })
  },

  // 忘记密码，发送手机验证码
  postRetrievePasswordVerifyCode(body) {
    return api.post('/api/platform/user/login/resetPassword/sendPhoneVerifyCode', body, {
      mock: false
    })
  },

  // 获取新网白名单
  getXwWhite() {
    return api.get('/api/platform/bill/order/bankDraft/isItWhitelisted', null, {
      mock: false,
      showError: false
    })
  },

  // 忘记密码，重置密码
  postResetPassword(body) {
    return api.post('/api/platform/user/login/resetPassword', body, {
      mock: false,
      showError: false
    })
  },

  // 获取当前登录用户信息
  postLoginUserInfo(body) {
    return api.post('/api/platform/user/getLoginUserInfo', body, {
      mock: false
    })
  },

  // 修改密码，发送手机验证码
  postFixPasswordVerifyCode() {
    return api.post('/api/platform/user/resetPassword/sendPhoneVerifyCode', null, {
      mock: false
    })
  },

  // 修改密码
  postFixPassword(body) {
    return api.post('/api/platform/user/resetPassword', body, {
      mock: false
    })
  },

  // 修改手机号，发送手机验证 码
  postFixMobileVerifyCode(body) {
    return api.post('/api/platform/user/updateCorpMemberMobile/sendPhoneVerifyCode', body, {
      mock: false
    })
  },

  // 修改手机号
  postFixMobile(body) {
    return api.post('/api/platform/user/updateCorpMemberMobile', body, {
      mock: false,
      showError: false
    })
  },

  // 重新认证前校验
  getRreAuthCheck() {
    return api.get('/api/platform/corpOpenInfo/reAuthCheck', null, {
      mock: false,
      showError: false
    })
  },

  // 查询电子交易账户列表
  getPaymentAccountList(body) {
    return api.get('/api/platform/corp/paymentAccountList', body, {
      mock: false,
      isCancelRequest: false
    })
  },

  // 查询电子交易账户的余额
  getPaymentAccountFund(paymentChannel) {
    return api.get(`/api/platform/corp/paymentAccountFund/${paymentChannel}`, null, {
      mock: false,
      // eslint-disable-next-line no-magic-numbers
      timeout: 1000 * 30
    })
  },
  // 查询电子交易账户的余额-奖励明细
  getQueryAmount(paymentChannel) {
    return api.get(`/api/platform/corp/paymentAccountFund/queryAmount/${paymentChannel}`, null, {
      mock: false,
      // eslint-disable-next-line no-magic-numbers
      timeout: 1000 * 30
    })
  },
  // 用户退出登录
  postLogout() {
    return api.post('/api/platform/user/logout')
  },

  // 获取用户信息
  getUserInfo() {
    return api.post('/api/platform/user/getLoginUserInfo')
  },

  // 查询企业信息
  getCorpInfo() {
    return api.get('/api/platform/corp/corpInfo', null, {
      mock: false,
      isCancelRequest: false
    })
  },

  // 获取企业统计信息
  getCorpStatData(body) {
    return api.get('/api/platform/statistic/getCorpStatData', body, {
      mock: false,
    })
  },

  // 根据企业id获取最近90天的企业统计信息
  getCorpStatDataByCorpId(body) {
    return api.get('/api/platform/statistic/getCorpStatDataByCorpId', body, {
      mock: false,
    })
  },

  // 查询背书账户列表
  getBankCardList(body) {
    return api.get('/api/platform/corp/bankCardList', body, {
      mock: false,
    })
  },

  // 查询背书账户
  getBankCardId(bankCardId) {
    return api.get(`/api/platform/corp/bankCard/${bankCardId}`, null, {
      mock: false,
    })
  },

  // 查询背书账户开通流程阶段
  getbankCorpBankCardStage(bankCardId) {
    return api.get(`/api/platform/corp/corpBankCardStage/${bankCardId}`, null, {
      mock: false,
    })
  },

  // 保存背书账户
  postSaveCorpBankCard(body) {
    return api.post('/api/platform/corp/saveCorpBankCard', body, {
      mock: false
    })
  },

  // 更新背书账户
  updateCorpBankCard(body) {
    return api.post('/api/platform/corp/updateCorpBankCard', body, {
      mock: false
    })
  },

  // 背书账户验证小额打款
  putCheckBankCardAmount(body) {
    return api.put('/api/platform/corp/checkBankCardAmount', body, {
      mock: false
    })
  },

  // 删除背书账户
  deleteCorpBankCard(body) {
    return api.post('/api/platform/corp/deleteCorpBankCard', body, {
      mock: false
    })
  },

  // 更新银行背书账户是否支持新一代票据
  updateCorpNewDraftFlag(params) {
    return api.post('/api/platform/corp/updateBankCard/newDraftFlag', params, {
      mock: false
    })
  },

  // 获取智付的用户中心地址
  getPaymentAccountUserCenterUrl(type, paymentChannel) {
    return api.get(`/api/platform/corp/userCenterUrl?type=${type}-${paymentChannel}`, null, {
      mock: false
    })
  },

  // 设置定时上下架
  postTraderCorpConfig(body) {
    return api.post('/api/platform/order/config/saveBillOrderTraderCorpConfig', body, {
      mock: false,
    })
  },

  // 获取定时上下架配置列表
  getBillOrderTraderCorpConfig(body) {
    return api.get('/api/platform/order/config/getBillOrderTraderCorpConfig', body, {
      mock: false,
    })
  },

  // 获取用户提醒配置
  getRemindSetting(body) {
    return api.get('/api/platform/demand/user/remind/get', body, {
      mock: false
    })
  },

  // 保存用户提醒配置
  saveRemindSetting(body) {
    return api.post('/api/platform/demand/user/remind/save', body, {
      mock: false,
    })
  },

  // 客户反馈
  postCorpFeedback(body) {
    return api.post('/api/platform/operation/corpFeedback', body, {
      mock: false,
      showError: false
    })
  },

  // 关注服务号 发送生成二维码的验证码
  sendQrcodePhoneVerifyCode(body) {
    return api.post('/api/platform/operation/wechat/sendQrcodePhoneVerifyCode', body, {
      mock: false
    })
  },

  // 关注服务号 生成微信二维码
  createQrcode(body) {
    return api.post('/api/platform/operation/wechat/createQrcode', body, {
      mock: false
    })
  },

  // 取消关注服务号
  unfollowWechat(body) {
    return api.post('/api/platform/operation/wechat/updateWechatUnsubscribe', body, {
      mock: false
    })
  },

  // 修改关注服务号昵称
  updateWechatNickname(body) {
    return api.post('/api/platform/operation/wechat/modifyNickName', body, {
      mock: false
    })
  },

  // 京东vip信息
  getVipCorp(body) {
    return api.get('/api/platform/activity/jd/vipCorp', body, {
      mock: false
    })
  },

  // 获取 充值/提现 账户列表
  getTranslateAccountBankList(body) {
    return api.post('/api/platform/corp/translateAccountBankList', body, {
      mock: false
    })
  },

  // 充值/提现账户同步背书账户
  syncToEndorseBank(body) {
    return api.post('/api/platform/corp/syncToEndorseBank', body, {
      mock: false
    })
  },

  // 获取用户识票助手端配置
  getDiscernConfig() {
    const os = navigator.platform.indexOf('Win') > -1 ? 'window' : 'mac'
    return api.get(`/api/discern/systemConfig/getCommonConfig?os=${os}`, null, {
      mock: false
    })
  },
  // 更新用户识票助手端配置
  updateDiscernConfig(body) {
    return api.post('/api/discern/systemConfig/updateCommonConfig', body, {
      mock: false
    })
  },
  // 券开关
  couponAllow() {
    return api.get('/api/platform/assets/allow', {}, {
      mock: false
    })
  },
  // 活动进度
  couponProgress(body) {
    return api.post('/api/platform/activity/marketing/coupon/currentProgress', body, {
      mock: false,
      isCancelRequest: false
    })
  },
  // 持票方汇款账户列表
  translateBankList(body) {
    return api.post('/api/platform/corp/translateBankList', body, {
      mock: false,
    })
  },
  // 获取客户经理二维码
  getManageQRCode() {
    return api.get('api/platform/config/customerQRCodeConfig', {}, {
      mock: false
    })
  },
  // 渠道签约
  channelSign(body) {
    return api.post('api/platform/account/sign', body, {
      mock: false
    })
  },
  // 渠道签约步骤
  channelSignStage(body) {
    return api.post('api/platform/account/signStage', body, {
      mock: false
    })
  },
  // 签约银行信息
  getSignBankInfo(body) {
    return api.post('api/platform/account/getSignBankInfo', body, {
      mock: false
    })
  },
  // 银行账户同步接口
  setBankCardSync(body) {
    return api.post('/api/platform/corp/bankCardSync', body, {
      mock: false
    })
  },
  // 批量银行背书账户同步接口
  batchBankCardBackAccountSync(body) {
    return api.post('/api/platform/corp/batchBankCardBackAccountSync', body, {
      mock: false
    })
  },

  // 修改默认回款户
  modifyDefaultBackAccount(body) {
    return api.post('/api/platform/corp/modifyDefaultBackAccount', body, {
      mock: false
    })
  },

  // 银行账户-上传凭证
  uploadCertificate(body) {
    return api.post('/api/platform/corp/uploadCertificate', body, {
      mock: false
    })
  },

  // 渠道签约-上传凭证
  channelSignUploadCertificate(body) {
    return api.post('/api/platform/account/uploadCertificate', body, {
      mock: false
    })
  },
  // 渠道签约-同步银行账户至回款账户
  signSuccessSyncBank(body) {
    return api.post('/api/platform/account/signSuccessSyncBank', body, {
      mock: false
    })
  },
  // 获取渠道签约-验证小额次数
  signVerifyAmountTimes(body) {
    return api.post('/api/platform/account/signVerifyAmountTimes', body, {
      mock: false
    })
  },
  // 渠道签约-验证小额
  signVerifyCheckAmount(body) {
    return api.post('/api/platform/account/verifyCheckAmount', body, {
      mock: false
    })
  },
  // 前台-查询异常名单消息
  getAbnormalNoticeMsg(body) {
    return api.post('/api/platform/risk/getAbnormalNoticeMsg', body, {
      mock: false
    })
  },
  // e++签约获取银行账户列表
  getEPlusSignBankList(body) {
    return api.post('/api/platform/bankCard/querySignBanks', body, {
      mock: false
    })
  },
  // E++校验爬虫方案用户是否处于登录状态
  checkLoginCrawlerStatus(body) {
    return api.get('/api/platform/disConnection/crawler/userLoginStatus', body, {
      mock: false
    })
  },
  // E++校验爬虫方案用户登录接口
  loginCrawler(body) {
    return api.post('/api/platform/disConnection/crawler/userLogin', body, {
      mock: false
    })
  },
  // 修改支付密码，发送手机验证码
  paymentMobileVerifyCode(body) {
    return api.post('/api/platform/order/defaultConfig/sendPhoneVerifyCode', body, {
      mock: false,
      showError: false
    })
  },

  // 修改支付密码
  setPaymentPassword(body) {
    return api.post('/api/platform/order/defaultConfig/updatePassword', body, {
      mock: false,
      showError: false
    })
  },
}

export default userApi
