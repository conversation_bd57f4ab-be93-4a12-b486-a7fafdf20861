import { api } from '@/utils/axios.js'
// pc
export default {
  bannerPC(param) {
    return api.get('/api/platform/content/banner/pc', param, {
      mock: false,
      isSdApi: true
    })
  },
  noticeAndNews(param) {
    return api.post('/api/platform/content/listPage', param, {
      mock: false,
      isSdApi: true
    })
  },
  // 暂无首页
  // shcpeMarketOverview() {
  //   return api.get('/api/datacollection/shcpeMarketOverview/get', null, {
  //     // domainType: 'shendu',
  //     mock: false,
  //   })
  // },
  // 获取带token的商票信评url链接
  getSpxpTokenUrl(param) {
    return api.post('/api/platform/spb/getXinpingToken', param, {
      // domainType: 'shendu',
      mock: false,
    })
  },
  // 深度行情-查询实时成交价
  getRealtimeTransactionPrice(params) {
    return api.post('/api/platform/content/getRealtimeTransactionPrice', params, {
      mock: false,
    })
  },
  // 深度行情-热门商票成交价
  getCommercialTransactionPriceTrend(params) {
    return api.post('/api/platform/content/getCommercialTransactionPriceTrend', params, {
      mock: false,
    })
  },
  // 深度行情-查询近7天成交价走势图
  getTransactionPriceTrend(params) {
    return api.get('/api/platform/content/getTransactionPriceTrend', params, {
      mock: false,
    })
  },
  // 新闻资讯
  newsList(param) {
    return api.post('/api/platform/content/homePageList', param, {
      mock: false,
      isSdApi: true
    })
  },
  getCorpInfoSync(param) {
    return api.post('/api/platform/spb/corpInfoSync', param, {
      mock: false,
    })
  },
  newBannerPC(param) {
    return api.post('/api/platform/operation/banner/list', param, {
      mock: false,
      isSdApi: true
    })
  },
}
