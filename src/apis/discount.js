import { api } from '@/utils/axios.js'

const discountApi = {
  // 获取单点登录token
  getDiscountToken() {
    return api.get('/api/platform/discount/getDiscountToken', null, {
      mock: false,
      mockId: '119'
    })
  },

  // 银票 发布页面秒贴最优报价（废弃）
  postDraftBankRate(body) {
    return api.post('/api/platform/discount/postDraftBankRate', body, {
      // mock: true,
      mockId: '119'
    })
  },

  // 银票 发布页面秒贴最优报价(新版)
  postDraftBankRateNew(body) {
    return api.post('/api/platform/discount/postDraftBankRateNew', body, {
      // mock: true,
    })
  },

  // 获取银票秒贴签约地址
  getBankCreditUrl(body) {
    return api.post('/api/platform/discount/getBankCreditUrl', body, {
      // mock: true,
      mockId: '119',
      withoutCheck: true,
    })
  },
}

export default discountApi
