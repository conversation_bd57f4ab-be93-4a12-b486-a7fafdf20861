import { api } from '@/utils/axios.js'
import { apiAddBaseUrl } from '@/utils/axios.js'

const serviceApi = apiAddBaseUrl(import.meta.env.VUE_APP_MALL_API_URL)

// 外贸资讯
// export const getForeignTradeInfo = (params) => api.get('/foreignTrade/news/page', params)
export const getForeignTradeInfo = (params) => api.get('/api/goods-web/client/foreignTrade/news/page', params)

// 内外资源融合
export const getExternalResource = (params) => api.get('/foreignTrade/international/resource/news/page', params)

// 税务协调
export const getTaxInfo = (params) => api.get('/foreignTrade/taxCoord/news/page', params)

// 采购商签证
export const getVisaInfo = (params) => api.get('/foreignTrade/buyer/visa/page', params)

// 外汇交易
export const getforexTrade = (params) => api.get('/foreignTrade/internation/order/news/page', params)

// 地方外贸
export const getLocalInfo = (params) => api.get('/foreignTrade/local/news/page', params)

// 财会事项
export const getFinanceInfo = (params) => api.get('/foreignTrade/finance/news/page', params)

// 美食 - 列表
export const foodsList = (params) => api.get('/foreignTrade/foods/list', params)
// 美食 - 分类
export const foodsCategory = (params) => api.get('/foreignTrade/foods/category', params)
// 美食 - 区域
export const foodsRegion = (params) => api.get('/foreignTrade/foods/region', params)
// 住宿 - 列表
export const hotelList = (params) => api.get('/foreignTrade/hotels/list', params)
// 住宿 - 区域
export const hotelArea = (params) => api.get('/foreignTrade/hotels/region', params)
// 娱乐 - 列表
export const entertainmentList = (params) => api.get('/foreignTrade/plays/list', params)
// 娱乐 - 分类
export const entertainmentCategory = (params) => api.get('/foreignTrade/plays/category', params)
// 娱乐 - 区域
export const entertainmentArea = (params) => api.get('/foreignTrade/plays/region', params)

// 国际展会列表
// export const getForeignTrade = (params) => api.get('/foreignTrade/zc/page', params)
export const getForeignTrade = (params) => api.get('/api/goods-web/client/foreignTrade/zc/page', params)

// 跨境贸易
// export const getCrossBorderTrade = (params) => api.get('/foreignTrade/internation/trade/page', params)
export const getCrossBorderTrade = (params) => api.get('/api/goods-web/client/foreignTrade/internation/trade/page', params)

/************************************ 服务商中心模块 ************************************/

// 公共-服务商-服务类别
export const getServiceType = () => serviceApi.get('/api/scyx/serviceProvider/serviceType/info')

// 服务商中心-服务商-资料详情
export const getServiceProviderInfo = () => serviceApi.get('/api/scyx/serviceProvider/info')

// 服务商中心-服务商-资料更新
export const updateServiceProviderInfo = (body) => serviceApi.post('/api/scyx/serviceProvider/update', body)

// 服务商中心-服务-列表
export const getServiceList = (body) => serviceApi.post('/api/scyx/service/list', body)

// 服务商中心-服务-上下架
export const updateServiceStatus = (body) => serviceApi.put('/api/scyx/service/status', body)

// 服务商中心-服务-新增
export const createService = (body) => serviceApi.post('/api/scyx/service/create', body)

// 服务商中心-服务-详情
export const getServiceInfo = (params) => serviceApi.get(`/api/scyx/service/info?id=${params}`)

// 服务商中心-服务-修改
export const updateService = (body) => serviceApi.put('/api/scyx/service/update', body)

export const getWebServiceList = (body) => serviceApi.post('/api/scyx/web/service/list', body)

export const getWebServiceInfo = (body) => serviceApi.get('/api/scyx/web/service/info', body)
