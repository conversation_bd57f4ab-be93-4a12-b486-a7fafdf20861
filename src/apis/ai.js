/* eslint-disable */
import { api } from '@/utils/axios.js'
let host = process.env.VUE_APP_BASE_GPT_URL
const gptApi = {
  // gpt 提问
  chatrobot(params) {
    return api.post(host + '/api/chatrobot/chat', params)
  },
  chatSse(params) {
    return api.post(host + '/api/chatrobot/chat/sse', params)
  },
  // 获取历史聊天
  historyChats(params) {
    return api.post(host + '/api/chatrobot/chat/history', params)
  },
  // 队列清单
  chatrobotStr(params) {
    return api.post(host + '/api/chatrobot/chat/status', params)
  },
  // 获取erp侧token
  loginErp(params) {
    return api.post(host + '/api/chatrobot/user/loginErp', params)
  },
  // 查询订单状态
  orderStatus(params) {
    return api.post(host + '/api/chatrobot/chat/orderStatus', params)
  },
  // 差评
  evaluateBad(params) {
    return api.post(host + '/api/chatrobot/chat/bad', params)
  },
  // 好评
  evaluateGood(params) {
    return api.post(host + '/api/chatrobot/chat/good', params)
  },
  // 查询模板列表
  getTemplate(params) {
    return api.post(host + '/api/chatrobot/chat/template/list', params)
  },
  // 新增模板
  addTemplate(params) {
    return api.post(host + '/api/chatrobot/chat/template', params)
  },
  // 编辑模板
  editTemplate(params) {
    return api.put(host + '/api/chatrobot/chat/template', params)
  },
  // 删除模板
  delTemplate(id) {
    return api.delete(host + `/api/chatrobot/chat/template/${id}`, null)
  },
  // 设置头像
  setUserAvatar(params) {
    return api.post(host + '/api/chatrobot/userAvatar/update', params)
  },
  // 获取头像
  getUserAvatar(params) {
    return api.post(host + '/api/chatrobot/userAvatar/get', params)
  },
  // 停止生成sse流
  stopSse(params) {
    return api.post(host + '/api/chatrobot/chat/stopSse', params)
  },
  // 获取是否有权限查看AI
  getAiState() {
    return api.get('/api/platform/corp/functionSwitch?functionType=4', null, {
      mock: false,
      showError: false
    })
  },
  // 清空历史记录
  cleanHistory() {
    return api.post(host + '/api/chatrobot/chat/cleanHistory', null, {
      mock: false,
      showError: false
    })
  },
  // 评价
  isLike(params) {
    return api.post(host + '/api/chatrobot/chat/like', params, {
      mock: false,
      showError: false
    })
  },
  // 获取系统聊天模板
  systemTemplate(params) {
    return api.post(host + '/api/chatrobot/chat/template/system', params, {
      mock: false,
      showError: false
    })
  },
  // 订单是否可用
  checkAcceptOrder(params) { return api.post(host + '/api/chatrobot/chat/checkAcceptOrder', params) },
}

export default gptApi
