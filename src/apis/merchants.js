import { apiAddBaseUrl } from '@/utils/axios.js'

const api = apiAddBaseUrl(import.meta.env.VUE_APP_MALL_API_URL + '/api/user-web')

// 注册
export const userRegister = (data) => api.post('/client/user/register', data)
// 登录
export const userLogin = (data) => api.post('/client/user/login', data)
// 找回密码
export const forgotPassword = (data) => api.post('/client/user/forgotPassword', data)
// 退出
export const loginOut = (data) => api.post('/client/user/loginOut', data)
// 获取用户最外层信息，用户名、用户类型、审核状态及邀请信息
export const getUserBaseInfo = (data) => api.get('/client/user/getUserLoginInfo', data)
// 获取用户信息
export const getUserInfo = (data) => api.get('/client/user/info/get', data)
// 获取用户审核信息
export const getUserAuditInfo = (data) => api.get('/client/user/audit/get', data)
// 商家入驻
export const userInsert = (data) => api.post('/client/user/audit/add', data, { returnOrigin: true })
// 更新用户信息
export const updateUserInfo = (data) => api.post('/client/user/info/update', data, { canEmpty: true })
// 获取字典
export const getDictListByKey = (data) => api.get('/dict/getListByKey', data)
// 实力商家
export const getSellerPage = (data) => api.get('/client/user/getSellerPage', data)
// 查询商家
export const getSellerById = (data) => api.get('/client/user/getSellerById', data)
// 卖家服务邀请人管理
export const getUserInviteePage = (data) => api.get('/client/user/getUserInviteePage', data)
// 卖家服务邀请人管理解除绑定
export const inviteeRemove = (data) => api.post('/client/user/inviteeRemove', data)
// 卖家服务邀请人管理-用户列表
export const getUserAccountList = (data) => api.get('/client/account/record/getPage', data)
// 卖家服务邀请人管理-用户信息
export const getUserAccountInfo = (data) => api.get('/client/account/getUserAccount', data)
// 外贸综合服务列表
export const getServiceList = (data) => api.post('/client/service/list', data)
// 外贸综合服务新增
export const getServiceCreate = (data) => api.post('/client/service/create', data)
// 外贸综合服务详情
export const getServiceInfo = (data) => api.get('/client/service/info', data)
// 外贸综合服务修改
export const updateServiceInfo = (data) => api.put('/client/service/update', data)
// 外贸综合服务上下架
export const updateServiceStatus = (data) => api.put('/client/service/status', data)
// 手机号前缀
export const getPhonePrefixIds = (data) => api.get('/prefix/getList', data)

// 验证码
export const smsSend = (data) => api.post('/sms/sendCode', data)

// 获取用户设置
export const getUserConfig = (data) => api.get('/client/user/config/getUserConfig', data)

// 设置用户语言、币种
export const setUserConfig = (data) => api.post('/client/user/config/saveWebsiteConfig', data)

// 获取用户收货地址分页
export const getUserDeliveryAddress = (data) => api.get('/client/user/delivery/address/getPage', data)
// 保存用户收货地址
export const saveUserDeliveryAddress = (data) => api.post('/client/user/delivery/address/save', data)
// 获取默认地址
export const getUserDefaultDeliveryAddress = (data) => api.get('/client/user/delivery/address/getDefault', data)
// 删除用户收货地址
export const deleteUserDeliveryAddress = (data) => api.post('/client/user/delivery/address/delete', data)
// 根据id查询地址
export const getUserDeliveryAddressById = (data) => api.get('/client/user/delivery/address/getById', data)
// 设置默认收货地址
export const setDefaultDeliveryAddress = (data) => api.post('/client/user/delivery/address/setDefault', data)
// 平台联系人列表
export const getPlatformContact = (data) => api.get('/client/platformContactPerson/getPage', data)
