// 消息提醒模块接口
import { api } from '@/utils/axios.js'

const notificationApi = {

  // 消息列表
  getNotificationList(body) {
    return api.post('/api/platform/corp/message/messageList', body, {
      mock: false,
    })
  },

  // 消息列表未读数量
  getMsgUnReadCount() {
    return api.get('/api/platform/corp/message/tabNum', null, {
      mock: false,
    })
  },

  // 修改为已读
  putNotificationCount(id) {
    return api.put(`/api/platform/corp/message/updateRead/${id}`, null, {
      mock: false,
    })
  },

  // 根据msgTab修改为全部已读
  updateAllRead() {
    return api.put('/api/platform/content/updateAllRead', null, {
      mock: false,
      isSdApi: true
    })
  },

  // 通知提醒-消息全部已读(我是票方｜我是资方)
  orderMessageAllRead(data) {
    return api.post('/api/platform/corp/message/updateReadAll', data, {
      mock: false,
    })
  },

  // 修改为已读（平台公告）
  putNotificationCountPlatform(id) {
    return api.put(`/api/platform/content/updateRead/${id}`, null, {
      mock: false,
      isSdApi: true
    })
  },
  // 异常风险提示通知修改为已读
  postRiskMsgNoticeRead() {
    return api.put('/api/platform/operation/abnormal/notice/updateRead', null, {
      mock: false,
    })
  }

}

export default notificationApi
