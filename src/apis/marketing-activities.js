import { api } from '@/utils/axios.js'

const marketingActivitiesApi = {
  // 获取活动列表
  getActivityList(body) {
    return api.get('/api/platform/activity/list', body, {
      mock: false,
    })
  },

  // 分页查询活动奖励明细列表
  postActivityRewardDetail(body) {
    return api.post('/api/platform/activity/activityRewardDetail/pageList', body, {
      mock: false,
    })
  },

  // 领取奖励-头部的活动奖励统计金额
  getRewardDetailTotalAmt(body) {
    return api.get('/api/platform/activity/activityRewardDetail/getRewardDetailTotalAmt', body, {
      mock: false,
    })
  },

  // 领取奖励前验证码获取
  getMobileCaptcha(body) {
    return api.post('/api/platform/activity/activityRewardDetail/sendReadPaperVerifyCode', body, {
      mocK: false
    })
  },

  // 一键领取交易奖励
  postRewardTotalAmtEx(body) {
    return api.post('/api/platform/activity/activityRewardDetail/rewardTotalAmtEx', body, {
      mock: false,
    })
  },

  // 奖励明细-京东余额奖励明细
  postJdGain(body) {
    return api.post('/api/platform/activity/jd/gainDetail/listPage', body, {
      mock: false,
    })
  },

  // 奖励明细-提现记录
  postWithdraw(body) {
    return api.post('/api/platform/activity/jd/gainWithdraw/listPage', body, {
      mock: false,
    })
  },

  // 获取企业本周成交数据和邀请企业数
  getCurrentWeekCorpTradeData(body) {
    return api.get('/api/platform/statistic/getCurrentWeekCorpTradeData', body, {
      mock: false,
    })
  },

  // 获取用户邀请列表
  postInviteRecordList(body) {
    return api.post('/api/platform/corp/inviteRecord/list', body, {
      mock: false,
    })
  },

  // 获取用户邀请列表统计数据
  getInviteRecordTotalData() {
    return api.get('/api/platform/corp/inviteRecord/totalData', null, {
      mock: false,
    })
  },

  // 获取我的昨日信用分
  getMyCreditByYesterday() {
    return api.get('/api/platform/credit/myCreditByYesterday', null, {
      mock: false,
    })
  },

}

export default marketingActivitiesApi
