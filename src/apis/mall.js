import { apiAddBaseUrl } from '@/utils/axios.js'

const api = apiAddBaseUrl(import.meta.env.VUE_APP_MALL_API_URL)

// 获取首页商品列表
export const getHomeGoodsList = (data) => api.post('/api/scyx/web/goods/list', data)
// 为你推荐、商详页店铺热销
export const getRecommendList = (data, config) => api.post('/api/goods-web/client/index/recommend', data, { webTracking: config })

// 获取商品列表 - new
export const getNewHomeGoodsList = (params) => api.get('/api/goods-web/client/spu/getSearchPage', params)

// 逛市场 - 为你推荐列表
export const getGoodsPage = (data) => api.post('/api/scyx/market/goods/page', data)

// 获取实力商家列表
export const getSellerList = (data) => api.get('/api/scyx/seller/suggest', data)

// 获取首页商品分类列表
export const getProductCateList = (parentId) => api.get(`/home/<USER>/${parentId}`)
// 获取首页轮播推荐
export const getHomeContent = () => api.get('/home/<USER>')
// 获取首页人气推荐
export const getHomeHotProductList = () => api.get('/home/<USER>')
// 获取首页新品推荐
export const getHomeNewProductList = () => api.get('/home/<USER>')

// 根据商品分类获取商品列表
export const searchProduct = (params) => api.get('/esProduct/search/simple', params)
// 获取商品详情信息
export const getProductDetail = (id) => api.get(`/api/scyx/web/goods/${id}`)

// 商品详情获取店铺信息
export const getProductDetailShopInfo = (params) => api.get('/api/scyx/seller/shop/info', params)

// 添加商品收藏
export const addProductCollection = (body) => api.post('/member/productCollection/add', body)
// 取消商品收藏
export const delProductCollection = (body) => api.pos('/member/productCollection/delete', body)

// 添加购物车
export const addCart = (body) => api.post('/cart/add', body)
// 生成订单
export const generateOrder = (body) => api.post('/order/generateOrder', body)

// 获取订单列表
export const getOrderList = () => api.get('/order/list')

// 获取验证码
export const getAuthCode = () => api.get('/sso/getAuthCode')
// 登录
export const login = (body) => api.post('/sso/login', body)

/************************************ 买家、卖家中心模块 ************************************/

// 买家中心-获取买家信息
export const getBuyerInfo = (body) => api.get('/api/scyx/buyer/info', body)

// 买家中心-更新买家信息
export const updateBuyerInfo = (body) => api.post('/api/scyx/buyer/updateInfo', body, { canEmpty: true })

// 买家中心-获取店铺类型数据
export const shopTypeList = (body) => api.get('/api/scyx/buyer/shopTypes/list', body)

// 买家中心-买家商品收藏列表
export const getBuyerCollectionList = (body) => api.post('/api/scyx/buyer/goods/collection/list', body)

// 买家中心-买家商品收藏
export const buyerCollection = (body) => api.post('/api/scyx/buyer/goods/collection', body)

// 买家中心-买家取消商品收藏
export const deletedBuyerCollection = (body) => api.post('/api/scyx/buyer/goods/collection/deleted', body)

// 卖家中心-获取卖家信息
export const getSellerInfo = () => api.get('/api/scyx/seller/info')

// 卖家中心-获取经营类别
export const getBusinessCategoryList = () => api.get('/api/scyx/seller/businessCategory/list')

// 卖家中心-更新卖家信息
export const updateSellerInfo = (body) => api.post('/api/scyx/seller/updateInfo', body)

// 卖家中心-商品列表
export const getSellerGoodsList = (body) => api.post('/api/scyx/seller/goods/list', body)

// 卖家中心-更新商品上架、下架
export const updateSellerGoodsStatus = (body) => api.put('/api/scyx/seller/goods/status', body)

// 卖家中心-新增商品
export const createSellerGoods = (body) => api.post('/api/scyx/seller/goods', body)

// 卖家中心-更新商品
export const updateSellerGoods = (body) => api.put('/api/scyx/seller/goods', body)

// 卖家中心-根据id查询商品类目子树
export const getGoodsCategoryTree = (params) => api.get(`/api/scyx/goodsCategory/tree/${params}`)

// 卖家中心-根据id查询商品详情
export const getSellerGoodsDetail = (goods_id) => api.get(`/api/scyx/seller/goods/${goods_id}`)

// 根据id获取 省市区 节点完整路径信息
export const getAreaRoute = (params) => api.get(`/api/scyx/area/route/${params}`)

// 根据id获取 商品类目 节点完整路径信息
export const getGoodsCategoryRoute = (params) => api.get(`/api/scyx/goodsCategory/route/${params}`)

// 获取品类类目
export const getCategoryTree = (body) => api.get(`/api/scyx/goodsCategory/tree/${body.parentId}`, {})

// 查询品类类目列表
export const queryCategoryList = (body) => api.post(`/api/scyx/goodsCategory/list`, body)

// 收藏商品
export const collectionGoods = (body) => api.post('/api/scyx/buyer/goods/collection', body)

// 查询收藏商品
export const queryCollectionGoods = (body) => api.post('/api/scyx/buyer/goods/collection/list', body)

// 卖家店铺信息-自己店铺
export const getMerchantSelf = () => api.get('/api/scyx/seller/shop/self', {})

// 卖家店铺信息-修改自己店铺
export const merchantUpdate = (body) => api.post('/api/scyx/seller/shop/update', body)

// 店铺主页类目获取 merchantId
export const getShopTopCategory = (body) => api.get('/api/scyx/seller/shop/topCategory', body)

// 店铺主页列表
export const getShopHomeGoods = (body) => api.post('/api/scyx/seller/shop/category/goods', body)

// 店铺主页列表
export const getShopGoods = (body) => api.post('/api/scyx/seller/shop/goods/list', body)

// 卖家服务商-卖家管理
export const servicesMerchantManage = (body) => api.post('/api/scyx/sellerServiceProvider/seller/list', body)

// 卖家服务商-商品管理
export const servicesProductManage = (body) => api.post('/api/scyx/sellerServiceProvider/goods/list', body)

// 卖家服务商-订单管理
export const servicesOrderManage = (body) => api.post('/api/scyx/sellerServiceProvider/order/list', body)

// 卖家服务商-账户管理
export const servicesAccountManage = (body) => api.get('/api/scyx/sellerServiceProvider/getInfo', body)

// 卖家服务商-账户管理
export const servicesAccountList = (body) => api.get('/api/scyx/accountRecord/getPage', body)

// 卖家服务商-账户管理
export const servicesMerchantUnbind = (body) => api.post('/api/scyx/sellerServiceProvider/seller/unbind', body)

// 卖家服务商-账户管理
export const servicesUpdateUserInfo = (body) => api.post('/api/scyx/sellerServiceProvider/updateBankInfo', body)
