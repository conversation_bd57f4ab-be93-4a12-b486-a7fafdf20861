// 商票交易权限相关
import { api } from '@/utils/axios.js'

const spTradeApi = {
  // 商票准入权限校验
  accessCorpCheck(corpId) {
    return api.get(`/api/platform/operation/commercialBillWhiteList/accessCorpCheck/${corpId}`, {}, {
      mock: false
    })
  },
  // 商票用户白名单申请
  entitledCorpApply(body) {
    return api.post('/api/platform/operation/commercialBillWhiteList/entitledCorpApply', body, {
      mock: false
    })
  },
  // 商票白名单权限校验
  whiteListCheck(body) {
    return api.post('/api/platform/operation/commercialBillWhiteList/whiteListCheck', body, {
      mock: false
    })
  },
  // 商票白名单申请在途状态查询
  existUnderReviewAccessCorpApply(corpId) {
    return api.get(`/api/platform/operation/commercialBillWhiteList/existUnderReviewAccessCorpApply/${corpId}`, {}, {
      mock: false
    })
  }
}

export default spTradeApi
