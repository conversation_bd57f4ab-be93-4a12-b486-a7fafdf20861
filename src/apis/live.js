import { apiAddBaseUrl } from '@/utils/axios.js'

const api = apiAddBaseUrl(import.meta.env.VUE_APP_MALL_API_URL + '/api/goods-web')

// 未入直播商品池列表
export const getSpufreeList = (data) => api.post('/client/live/spu/freeList', data)

// 已入直播商品池列表
export const getLiveSpuList = (data) => api.post('/client/live/spu/list', data)

// 直播商品详情
export const getLiveSpuDetail = (data) => api.get('/client/live/spu/getInfo', data)

// 新增直播商品、编辑直播商品
export const saveLiveSpu = (data) => api.post('/client/live/spu/save', data)

// 直播商品上下架
export const updatePublishStatus = (data) => api.post('/client/live/spu/updatePublishStatus', data)

// 直播商品通过id删除
export const delSpu = (data) => api.delete(`/client/live/spu/del`, { params: data })
