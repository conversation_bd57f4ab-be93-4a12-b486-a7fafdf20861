import { api } from '@/utils/axios.js'
import { apiAddBaseUrl } from '@/utils/axios.js'

const api2 = apiAddBaseUrl(import.meta.env.VUE_APP_MALL_API_URL + '/api/goods-web')

// 商城简介
export const marketList = () => api2.get('/guideMarket/market/list')

// 商城市场简介
export const marketInfo = (data) => api2.get('/guideMarket/market/detail/info', data)

// 热销商品
export const hotProductList = () => api.get('/guideMarket/market/product/hot/list')
