import Storage from '@/common/js/storage'
import { api } from '@/utils/axios.js'
import { SENSITIVE_WORDS, SENSITIVE_WORDS_DATE } from '@/constant-storage'
import { formatTime } from '@/common/js/date'

const marketApi = {
  // 获取风险关键字选项列表
  getSensitiveWords(val) {
    // 检查是否存在缓存，直接返回
    const cacheDate = Storage.get(SENSITIVE_WORDS_DATE)
    const cacheData = Storage.get(SENSITIVE_WORDS)
    if (val !== 'setting' && cacheDate && cacheDate === formatTime(Date.now(), 'YYYY-MM-DD') && cacheData) return Promise.resolve(cacheData)
    return api.get('/api/platform/order/billOrderRiskKeywordList/all', {}, {
      mock: false
    }).then(data => {
      Storage.set(SENSITIVE_WORDS, data)
      Storage.set(SENSITIVE_WORDS_DATE, formatTime(Date.now(), 'YYYY-MM-DD'))
      return data
    })
    // return api.get('/api/platform/order/billOrderRiskKeywordList/all', {}, {
    //   mock: false
    // })
  },

  // 获取票据订单列表
  getDraftList(body, type) {
    return api.post(`/api/platform/draft/order/searchDraftOrderList${type ? `?type=${type}` : ''}`, body, {
      mock: false,
      isCancelRequest: true
    })
  },

  // 获取票据订单列表--我的关注
  getFollowDraftList(body, type) {
    return api.post(`/api/platform/draft/order/searchFollowOrderList${type ? `?type=${type}` : ''}`, body, {
      mock: false,
      isCancelRequest: true
    })
  },

  // 获取票据订单列表--智能助手
  getAssistantOrderList(body, type) {
    return api.post(`/api/platform/draft/order/searchAssistantDraftOrderList${type ? `?type=${type}` : ''}`, body, {
      mock: false,
      isCancelRequest: true
    })
  },
  // 一键分享接口
  getShareUrl(orderNo) {
    return api.get(`/api/platform/draft/order/getShareLink/${orderNo}`)
  },
  // 获取联系方式
  getOrderContact(body) {
    return api.post('/api/platform/contact/getOrderContact', body, {
      mock: false,
      isCancelRequest: true
    })
  },

  // 获取订单的浏览次数
  getOrderViewsByListOrderNo(body) {
    return api.post('/api/platform/draft/order/getOrderViewsByListOrderNo', body, {
      mock: false,
      isCancelRequest: true
    })
  },

  // 获取我的订单票据
  getMyDraftList(body) {
    return api.post('/api/platform/draft/order/searchTraderCorpOrderList', body, {
      mock: false,
      isCancelRequest: true
    })
  },

  // 获取订单的浏览次数和议价人数
  getOrderCntByOrderNos(body) {
    return api.post('/api/platform/draft/order/getOrderCntByOrderNos', body, {
      mock: false,
      isCancelRequest: true
    })
  },

  // 查询我的所有收藏 然后和票据订单列表对比，判断哪个单已被收藏
  getAllMyCollect(body) {
    return api.post('/api/platform/draft/order/getAllMyCollect', body, {
      mock: false,
      isCancelRequest: true
    })
  },
  // 查询我的所有收藏 然后和票据订单列表对比，判断哪个单已被收藏--关注
  getAllMyFollowCollect(body) {
    return api.post('/api/platform/draft/order/getAllMyFollowCollect', body, {
      mock: false,
      isCancelRequest: true
    })
  },

  // 获取票据详情
  getDraftOrderInfo(orderNo) {
    return api.get(`/api/platform/draft/order/getDraftOrderInfo/${orderNo}`)
  },
  // 获取票据详情--通过分享
  getDraftOrderInfoShare(orderNo) {
    return api.get(`/api/platform/draft/order/getOrderInfo/${orderNo}`)
  },

  // 获取用户收票偏好
  getPreferencesList(body) {
    return api.get('/api/platform/demand/search/condition/list', body, {
      mock: false
    })
  },
  // 获取数量
  getTradingCenterPreference(body) {
    return api.post('/api/platform/draft/order/getTradingCenterPreference', body, {
      mock: false
    })
  },
  // 删除数量
  deletePreference(body) {
    return api.post('/api/platform/draft/order/deletePreference', body, {
      mock: false
    })
  },
  // 增加收票偏好
  addPreferences(body) {
    return api.post('/api/platform/demand/search/condition/save', body, {
      mock: false,
      showError: false, // 因为提示的图标不一样，所以在外面捕获
    })
  },

  // 修改收票偏好
  editPreferences(body) {
    return api.put('/api/platform/demand/search/condition/update', body, {
      mock: false,
      showError: false, // 因为提示的图标不一样，所以在外面捕获
    })
  },

  // 修改收票偏好排序
  setPreferencesSort(body) {
    return api.put('/api/platform/demand/search/condition/updatePriority', body, {
      mock: false
    })
  },

  // 删除收票偏好
  deletePreferences(id) {
    return api.delete(`/api/platform/demand/search/condition/delete/${id}`, {
      mock: false
    })
  },

  // 执行交易流程（单个接单）
  postSingleAcceptOrder(body) {
    return api.post('/api/platform/draft/order/singleAcceptOrder', body, {
      mock: false,
      showError: false,
    })
  },

  // 执行交易流程（单个新票接单）
  postSingleAcceptNewOrder(body) {
    return api.post('/api/platform/newDraft/singleAcceptNewDraft', body, {
      mock: false,
      showError: false,
    })
  },

  // 执行交易流程（定向接单）
  postSingleAcceptAgentOrder(body) {
    return api.post('/api/platform/draft/order/singleAcceptAgentOrder', body, {
      mock: false,
      showError: false,
    })
  },

  // 批量接单：批量接指定数量连号票的订单
  postBatchAcceptSerialOrderLimitNumber(body) {
    return api.post('/api/platform/draft/order/batchAcceptSerialOrderLimitNumber', body, {
      mock: false,
      isShowBatchError: false, // 因为提示的UI不一样，所以在外面捕获
      timeout: 30000
    })
  },

  // 验证报价是否修改了（接单前调用）
  validateQuotedPriceDiff(body) {
    return api.post('/api/platform/draft/order/validateQuotedPriceDiff', body, {
      mock: false
    })
  },

  // 票据订单正在浏览人数
  viewCount(orderId) {
    return api.get(`/api/platform/draft/order/viewCount/${orderId}`, null, {
      mock: false
    })
  },

  // 批量接单弹窗查询
  postAcceptDialogDetail(body) {
    return api.post('/api/platform/draft/order/listOrderInfoForAccept', body, {
      mock: false
    })
  },

  // 批量接单
  postBatchAcceptOrder(body) {
    return api.post('/api/platform/draft/order/batchAcceptOrder', body, {
      mock: false,
      showError: false,
      isShowBatchError: body.isShowBatchError,
      timeout: 30000
    })
  },

  // 批量接单(只用于批量定向)
  postBatchAcceptAgentOrder(body) {
    return api.post('/api/platform/draft/order/batchAcceptAgentOrder', body, {
      mock: false,
      showError: false,
      isShowBatchError: body.isShowBatchError,
      timeout: 30000
    })
  },

  // 交易大厅票据期限月份转天数查询接口
  getMarketBillTerm(params) {
    return api.get('/api/platform/operation/common/billTerm', params, {
      mock: false
    })
  },

  // 根据承兑人名称，搜索承兑人列表
  getAccepterList(params) {
    return api.get('/api/platform/operation/acceptor/search', params, {
      mock: false,
    })
  },

  // 我的收藏列表
  getMyCollectList(body) {
    return api.post('/api/platform/demand/user/collection/userCollectList', body, {
      mock: false,
    })
  },
  // 我的收藏列表--关注
  getMyCollectFocusList(body) {
    return api.post('/api/platform/demand/order/follow/userCollectList', body, {
      mock: false,
    })
  },
  // 用户批量收藏订单
  batchCollect(body) {
    return api.post('/api/platform/demand/user/collection/save', body, {
      mock: false,
    })
  },
  // 用户批量收藏订单--关注
  batchFocusCollect(body) {
    return api.post('/api/platform/demand/order/follow/save', body, {
      mock: false,
    })
  },

  // 批量取消收藏订单
  deleteCollect(body) {
    return api.post('/api/platform/demand/user/collection/deleteByOrderNos', body, {
      mock: false,
      timeout: 30000
    })
  },
  // 批量取消收藏订单--关注
  deleteFocusCollect(body) {
    return api.post('/api/platform/demand/order/follow/deleteByOrderNos', body, {
      mock: false,
      timeout: 30000
    })
  },

  // 我的意向订单列表
  getMyIntentionList(body) {
    return api.post('/api/platform/demand/orderIntention/orderIntentionList', body, {
      mock: false,
    })
  },

  // 新增我的意向订单
  saveIntention(body) {
    return api.post('/api/platform/demand/orderIntention/save', body, {
      mock: false,
    })
  },

  // 批量删除我的意向订单
  deleteIntention(body) {
    return api.post('/api/platform/demand/orderIntention/deleteByOrderNos', body, {
      mock: false,
      timeout: 30000
    })
  },

  // 意向订单详情
  getIntentionDetail(orderNo, body) {
    return api.get(`/api/platform/demand/orderIntention/getIntentionDetail/${orderNo}`, body, {
      mock: false,
    })
  },

  // 大厅底部获取客户logo列表
  getCustomLogoConf(body) {
    return api.get('/api/platform/logoCarousel/getClientLogoCarousel', body, {
      mock: false,
      isSdApi: true
    })
  },

  // 服务大厅我的订单 更新发布时间
  refreshPublishTime(body) {
    return api.put('/api/platform/draft/order/refreshPublishTime', body, {
      mock: false,
      showError: false,
    })
  },
  // 查询商票风险测评结果
  getRiskEvaluationResult(body) {
    return api.post('/api/platform/protocolRemember/checkRemember', body, {
      mock: false
    })
  },
  // 商票风险测评提交
  saveRiskEvaluationInfo(body) {
    return api.post('/api/platform/protocolRemember/save', body, {
      mock: false
    })
  },
  // 温馨提示-是否展示
  getWarmPrompt(body) {
    return api.post('/api/platform/newDraft/getWarmPrompt', body, {
      mock: false
    })
  },
  // 根据票据信息计算订单报价信息及服务费信息
  calculateSplitOrderFee(body) {
    return api.post('/api/platform/newDraft/getOrderFee', body, {
      mock: false,
      showError: false,
    })
  },
  // 偏好数量
  getPreferenceConfig() {
    return api.get('/api/platform/demand/search/condition/getPreferenceConfig', null, {
      mock: false,
      isCancelRequest: false
    })
  },
}

export default marketApi
