import { apiAddBaseUrl } from '@/utils/axios.js'

const api = apiAddBaseUrl(import.meta.env.VUE_APP_MALL_API_URL + '/api/user-web')
/* 我的收货地址 */
// 地址列表
export const getAddressList = (data) => api.get('/client/user/delivery/address/getPage', data)
// 根据 id 获取地址
export const getAddressById = (data) => api.get('/client/user/delivery/address/getById', data)
// 保存地址
export const saveAddress = (data) => api.post('/client/user/delivery/address/save', data)
// 获取默认地址
export const getDefaultAddress = () => api.get('/client/user/delivery/address/getDefault')
// 删除地址
export const deleteAddress = (data) => api.post('/client/user/delivery/address/delete', data)
