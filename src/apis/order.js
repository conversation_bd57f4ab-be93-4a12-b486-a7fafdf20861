import { apiAddBaseUrl } from '@/utils/axios.js'

const api = apiAddBaseUrl(import.meta.env.VUE_APP_MALL_API_URL + '/api/order-web')

// 供应商订单列表
export const getSellerPageList = (data) => api.get('/client/order/getSellerPage', data)

// 订单详情
export const getOrderInfo = (data) => api.get('/client/order/getOrderInfo', data)

// 修改价格
export const updatePrice = (data) => api.post('/client/order/updatePrice', data)

// 确认订单
export const confirmOrder = (data) => api.post('/client/order/confirm', data)

// 发货
export const sendDelivery = (data) => api.post('/client/order/sendDelivery', data)

// 以下为移动端购物车接口------------------

// 计价
export const calculate = (data) => api.post(`/client/order/calculateV1`, data)

// 提交订单
export const submitOrder = (data) => api.post(`/client/order/submitV1`, data)

// 进货单（采购车）
export const getUserCartList = (data) => api.post(`/client/shopcart/shopcartV1`, data)

// 加入购物车
export const addCartOrder = (data) => api.post(`/client/shopcart/add`, data)

// 购物车数量
export const getCount = (data) => api.get(`/client/shopcart/getCount`, data)

// 买家订单
export const getBuyerPage = (data) => api.get(`/client/order/getBuyerPage`, data)

// 订单详情 getOrderInfo --重命名为--> getBuyerOrderInfo
export const getBuyerOrderInfo = (data) => api.get(`/client/order/getBuyerOrderInfo`, data)

// 取消订单
export const cancelOrder = (data) => api.post(`/client/order/cancel`, data)

// 修改收货地址
export const updateOrderDeliveryAddress = (data) => api.post(`/client/order/delivery/address/updateAddress`, data)

// 确认收货
export const takeDelivery = (data) => api.post(`/client/order/takeDelivery`, data)

// 我的订单数量
export const getMineOrderCount = (data) => api.get(`/client/order/getBuyerCount`, data)

// 支付
export const payOrder = (data) => api.post(`/client/order/pay`, data)

// 查询支付渠道
export const getPayChannel = (data) => api.get(`/client/pay/channel/getList`, data)

/**
 * 获取sku价格
 * @param {*} data.spuId 商品id
 * @param {*} data.payPriceType 价格类型
 * @returns
 */
export const getGoodsSkuPriceInfo = (data) => api.post(`/api/goods-web/client/spu/getGoodsSkuPriceInfo`, data)

// 前置检查
export const getCheck = (data) => api.post(`/client/shopcart/check`, data)
// 以上为移动端购物车接口------------------
