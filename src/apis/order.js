import { api } from '@/utils/axios.js'

const orderApi = {

  // 获取我的订单列表
  getMyDraftList(params) {
    return api.get('/api/platform/draft/order/listTraderCorpOder', params, {
      mock: false,
      isCancelRequest: true
    })
  },

  // 获取已完成笔数和金额
  finishOrderStatistics(params) {
    return api.post('/api/platform/draft/order/finishOrderStatistics', params, {
      mock: false,
      isCancelRequest: true
    })
  },

  // 资方tab待办
  getBuyerTabNum(params) {
    return api.get('/api/platform/draft/order/buyerTabNum', params, {
      mock: false
    })
  },

  // 持票tab待办
  getSellerTabNum(params) {
    return api.get('/api/platform/draft/order/sellerTabNum', params, {
      mock: false
    })
  },

  // 导出我的订单
  exportList(params) {
    return api.get('/api/platform/draft/order/exportOssListTraderCorpOrder', params, {
      mock: false,
    })
  },

  // 获取我的历史订单列表
  getMyHistoryOrderList(params) {
    return api.get('/api/platform/draft/order/listHistoricalBillOrder', params, {
      mock: false,
      isCancelRequest: true
    })
  },

  // 资方历史订单tab待办
  getBuyerHistoryTabNum(params) {
    return api.get('/api/platform/draft/order/buyerHistoricalTabNum', params, {
      mock: false
    })
  },

  // 持票历史订单tab待办
  getSellerHistoryTabNum(params) {
    return api.get('/api/platform/draft/order/sellerHistoricalTabNum', params, {
      mock: false
    })
  },

  // 导出我的历史订单
  exportHistoryList(params) {
    return api.get('/api/platform/draft/order/exportOssListHistoricalBillOrder', params, {
      mock: false,
    })
  },

  // 单个-执行交易流程 canEmpty 为是否可以传空字段
  postSingleExecuteOrderTradeStep(body, canEmpty = false) {
    return api.post(`/api/platform/draft/order/singleExecuteOrderTradeStep?orderNo=${body.orderNoList[0]}&type=${body.type}`, body, {
      mock: false,
      canEmpty,
      showError: body.showError,
    })
  },

  // 多个-执行交易流程 canEmpty 为是否可以传空字段
  // eslint-disable-next-line no-magic-numbers
  postExecuteOrderTradeStep(body, canEmpty = false, timeout = 30000) {
    return api.post(`/api/platform/draft/order/executeOrderTradeStep?orderNo=${body.orderNoList[0]}&type=${body.type}`, body, {
      mock: false,
      canEmpty,
      isShowBatchError: body.isShowBatchError,
      showError: body.showError,
      timeout
    })
  },

  // 改价接口 包含单张和批量
  postUpdateQuotation(body) {
    return api.post('/api/platform/order/post/updateQuotation', body, {
      mock: false,
      canEmpty: false,
      isShowBatchError: false,
      showError: false,
      timeout: 30000
    })
  },

  // 查询我的订单详情
  getTraderCorpOrderInfo(orderNo) {
    return api.get(`/api/platform/draft/order/getTraderCorpOrderInfo/${orderNo}`, null, {
      mock: false,
      isCancelRequest: false
    })
  },

  // 查询我的历史订单详情
  getHistoricalTraderCorpOrderInfo(orderNo) {
    return api.get(`/api/platform/draft/order/getHistoricalTraderCorpOrderInfo/${orderNo}`, null, {
      mock: false,
      isCancelRequest: false
    })
  },

  // 取消订单验证码
  getCancelVerifyCode(params) {
    return api.get('/api/platform/draft/order/cancelOrder/sendPhoneVerifyCode', params, {
      mock: false
    })
  },

  // 签收解付验证码
  getSignPaymentVerifyCode(params) {
    return api.get('/api/platform/draft/order/signOrder/sendPhoneVerifyCode', params, {
      mock: false
    })
  },

  // 批量-签收解付验证码
  postAllSignPaymentVerifyCode(body) {
    return api.post('/api/platform/draft/order/batchSignOrder/sendPhoneVerifyCode', body, {
      mock: false,
      timeout: 30000
    })
  },

  // 批量签收
  batchSign(body) {
    return api.post(`/api/platform/draft/order/batchSign?orderNo=${body.orderNoList[0]}`, body, {
      mock: false,
      isShowBatchError: false,
      timeout: 30000
    })
  },

  // 修改背书账户
  putUpdateEndorseBankCard(body) {
    return api.put(`/api/platform/draft/order/updateEndorseBankCard/${body.traderCorpBankCardId}/${body.orderNo}`, null, {
      mock: false
    })
  },

  // 订单操作记录
  getBillorderOperateLog(orderNo) {
    return api.get(`/api/platform/order/operate/getBillOrderOperateLog/${orderNo}`, null, {
      mock: false
    })
  },

  // 历史订单操作记录
  getHistoryOrderOperateLog(orderNo) {
    return api.get(`/api/platform/order/operate/getBillOrderOperateLogHistory/${orderNo}`, null, {
      mock: false
    })
  },

  // 根据订单号获取企业背书账户信息
  getBillOrderEndorseAccount(orderNo) {
    return api.get(`/api/platform/draft/order/getBillOrderEndorseAccount/${orderNo}`, null, {
      mock: false
    })
  },

  // 催单
  postRemindersOrder(body) {
    return api.post(`/api/platform/bill/order/accelerateLog/reminders?orderNo=${body.orderNo}`, body, {
      mock: false
    })
  },

  // 获取收银台地址
  getCashierDeskUrl(body, timeout = 10000) {
    return api.post('/api/platform/draft/order/getCashierDeskUrl', body, {
      mock: false,
      timeout
    })
  },

  // 客服介入
  postEvidence(body) {
    return api.post('/api/platform/order/dispute/saveEvidence', body, {
      mock: false
    })
  },

  // 错号背书申请
  wrongNumberApply(params) {
    return api.post('/api/platform/order/wrongNumberApply', params, {})
  },

  // 订单流程签署合同
  postSignOrderProtocol(params) {
    return api.post('/api/platform/contract/order/sign', params, { mock: false })
  },

  // 下载错号背书
  downloadWrongNumber(params) {
    return api.post('/api/platform/order/downloadWrongNumber', params, {})
  },

  // 合同预览
  contractPreview(orderNo, params) {
    return api.get(`/api/platform/draft/order/contractPreview/${orderNo}`, params, {
      mock: false
    })
  },

  // 补充凭证
  postReplenishEvidence(body) {
    return api.post('/api/platform/order/dispute/replenishEvidence', body, {
      mock: false
    })
  },

  // 查询凭证列表
  getEvidenceList(orderNo) {
    return api.get(`/api/platform/order/dispute/evidence/${orderNo}`, null, {
      mock: false
    })
  },

  // 查询争议订单列表
  getDisputeList(body) {
    return api.post('/api/platform/order/dispute/list', body, {
      mock: false
    })
  },

  // 查询争议的操作日志
  getDisputeLogs(params) {
    return api.get('/api/platform/order/dispute/list/operation/logs', params, {
      mock: false
    })
  },

  // 获取光/非光 订单
  getFastTradeOrderList(body) {
    return api.post('/api/platform/draft/order/fastTradeOrder', body, {
      mock: false
    })
  },

  // 光/非光 转换
  fastTradeOrderTransfer(body) {
    return api.post('/api/platform/draft/order/fastTradeOrderTransfer', body, {
      mock: false,
      showError: true,
    })
  },

  // 订单失败后：批量重新发布
  batchRepublishAfterFail(body) {
    return api.post('/api/platform/draft/order/batchRepublishAfterFail', body, {
      mock: false,
      showError: false,
      isShowBatchError: false,
      timeout: 30000
    })
  },

  // 待接单下架后：批量重新发布
  batchRepublishAfterReview(body) {
    return api.post('/api/platform/draft/order/batchRepublishAfterReview', body, {
      mock: false,
      showError: false,
      isShowBatchError: false,
      timeout: 30000
    })
  },

  // 批量更新订单发布时间
  refreshOrderPublishTime(body) {
    return api.put('/api/platform/draft/order/refreshOrderPublishTime', body, {
      mock: false,
      isShowBatchError: false,
      timeout: 30000
    })
  },

  // 获取订单的自动接单规则
  getBillOrderNeedRule(orderNo, params) {
    return api.get(`/api/platform/order/needRule/getBillOrderNeedRule/${orderNo}`, params, {
      mock: false,
    })
  },

  // 获取事后交易凭证提交
  postDealVoucherSubmit(body) {
    return api.post('/api/platform/certificate/backup', body, {
      mock: false
    })
  },
  // 批量获取事后交易凭证提交
  postBatchDealVoucherSubmit(body) {
    return api.post('/api/platform/certificate/beachBackup', body, {
      mock: false
    })
  },
  // 获取事后交易凭证提交--获取验证码
  getVerificationCode(body) {
    return api.post('/api/platform/certificate/getVerificationCode', body, {
      mock: false
    })
  },
  // 获取分享码
  getShareNo(body) {
    return api.post('/api/platform/draft/order/getShareNo', body, {
      mock: false
    })
  },

  //  订单提醒消息推送 (我发布的，我收到的红点提醒)
  getOrderRemindNum(orderNo, params) {
    return api.get('/api/platform/draft/order/orderRemind', params, {
      mock: false,
    })
  },
  // 优惠券-查询券类型集合
  getCouponList() {
    return api.get('/api/platform/assets/coupon/listCouponType', null, { mock: false })
  },
  // 单个订单支付策略
  queryPayStrategy(body) {
    return api.post('/api/platform/assets/showPaymentStrategy', body, { mock: false })
  },
  // 拆分订单支付策略
  querySplitPayStrategy(body) {
    return api.post('/api/platform/assets/getPaymentStrategy', body, { mock: false })
  },
  // 批量接单支付策略
  queryBatchPayStrategy(body) {
    return api.post('/api/platform/assets/batchShowPaymentStrategy', body, { mock: false })
  },
  // 发起/修改/撤销变更票号
  updateTicketNumber(body) {
    return api.post('/api/platform/order/changeDraftNo', body, { mock: false })
  },
  // 同意变更票号
  // agreeChangeTicketNumber(body) {
  //   return api.post('/api/platform/agreeChangeDraftNo', body, { mock: false })
  // },
  // 查询变更票号
  queryChangeTicketNumber(body) {
    return api.post('/api/platform/order/getChangeDraftNo', body, { mock: false })
  },

  // 智能验票，查询票据识别结果
  getDiscernByDraftNo(body) {
    return api.post('/api/platform/draft/order/getDiscernByDraftNo', body, {
      mock: false
    })
  },
  // 查询上一次历史变更票号记录
  getChangeDraftNoHistory(body) {
    return api.post('/api/platform/order/getChangeDraftNoHistory', body, {
      mock: false
    })
  },
  // 已完成订单上传佐证材料
  uploadEvidence(body) {
    return api.post('/api/platform/evidence/uploadEvidence', body, {
      mock: false
    })
  },
  // 获取上一次佐证材料上传的数据
  getLastRejectEvidenceDetail(body) {
    return api.post('/api/platform/evidence/getLastRejectEvidenceDetail', body, {
      mock: false
    })
  },

  // 是否智能验票过
  getDiscernRecordByList(body) {
    return api.post('/api/discern/risk/existDiscernRecordByOrderNoList', body, {
      mock: false
    })
  },
  // 是否有准入风险
  getDiscernRiskByList(body) {
    return api.post('/api/discern/risk/existRiskByOrderNoList', body, {
      mock: false,
      showError: false,
    })
  },
  // 批量设置联系人
  beachUpdateMobile(body) {
    return api.post('/api/platform/contact/beachUpdateMobile', body, {
      mock: false,
    })
  },
  // 购买查看联系套餐--获取验证码
  getContactVerificationCode(body) {
    return api.post('/api/platform/contact/sendVerificationCode', body, {
      mock: false
    })
  },
  // 购买平台套餐
  purchaseContactMeal(body) {
    return api.post('/api/platform/contact/buyPackage', body, {
      mock: false
    })
  },
  // 购买套餐-订单价格列表分页查询
  pageOrderPriceList(body) {
    return api.post('/api/platform/contact/pageOrderPriceList', body, {
      mock: false
    })
  },
  // 购买套餐-订单价格详情
  purchaseOrderPriceDetail() {
    return api.get('/api/platform/contact/purchaseOrderPriceDetail', null, {
      mock: false
    })
  },
  // E++ 订单文件下载
  createYlOrderFile(body) {
    return api.post('/api/platform/draft/order/createYlOrderFile', body, {
      mock: false
    })
  }
}

export default orderApi
