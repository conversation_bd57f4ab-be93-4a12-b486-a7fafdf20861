import { api } from '@/utils/axios.js'

const issueDraftApi = {

  // 获取计息天数
  getInterestDay(params) {
    return api.get('/api/platform/operation/common/getInterestDay', params, {
      mock: false
    })
  },

  // 发布票据
  issueDraft(body) {
    return api.post('/api/platform/draft/order/postOrder', body, {
      mock: false,
      showError: false
    })
  },
  // 定向发布时校验地区黑名单
  validateAgentOrderArea(body) {
    return api.post('/api/platform/draft/order/agentLimit', body, {
      mock: false,
      showError: true
    })
  },

  // 发布票据
  issueNewDraft(body) {
    return api.post('/api/platform/newDraft/postNewDraftOrder', body, {
      mock: false,
      showError: false
    })
  },

  // 连号票发布
  postSerialDraftOrder(body) {
    return api.post('/api/platform/draft/order/postSerialDraftOrder', body, {
      mock: false,
      showError: false
    })
  },

  // 票容易发布
  postRongyiTicketDraft(body) {
    return api.post('/api/platform/draft/order/postSerialWithMarginDraftOrder', body, {
      mock: false,
      showError: false
    })
  },

  // 票容易发布开关状态
  getRongyiTicketDraftState() {
    return api.get('/api/platform/corp/functionSwitch?functionType=1', null, {
      mock: false,
      showError: false
    })
  },

  // 票据ocr
  ocrDraft(body) {
    return api.post('/api/platform/draft/order/ocrDraft', body, {
      mock: false,
      showError: false
    })
  },

  // 新票ocr
  ocrNewDraft(body) {
    return api.post('/api/platform/draft/order/ocrNewDraft', body, {
      mock: false,
      showError: false
    })
  },

  // 模糊查询承兑人
  getAcceptorSearch() {
    return api.get('/api/platform/operation/acceptor/search', null, {
      mock: false
    })
  },

  // 承兑人通用标签列表根据票据类型搜索
  getAcceptorCommonLabel(body) {
    return api.post('/api/platform/acceptorCommonLabel/list', body, {
      mock: false
    })
  },

  // 承兑人商票标签列表根据承兑人名称搜索
  getAcceptorCommercialLabel(body) {
    return api.post('/api/platform/acceptorCommercialLabel/list', body, {
      mock: false
    })
  },

  // 报价行情参考
  enquiryPrice(body) {
    return api.post('/api/platform/draft/order/enquiryPrice', body, {
      mock: false
    })
  },
  // 获取票方支付的服务费
  getPostOrderFee(body) {
    return api.post('/api/platform/order/release/getPostOrderFee', body, { mock: false })
  },
  // 新票批量发布
  beachPostNewDraftOrder(body) {
    return api.post('/api/platform/draft/order/beachPostNewDraftOrder', body, { mock: false })
  },
  // 确认接单-检测智付E+ 限制接单,检测“交易完成后超过,6天未上传佐证材料的订单”
  checkZfyjLimitAcceptOrder(body) {
    return api.get('/api/platform/evidence/checkZfyjLimitAcceptOrder', body, { mock: false })
  },
  // 发布票据优化校验-票据承兑人与西部信托承兑人匹配且承兑人融资利率＜订单报价年化利率
  validateInterestRate(body) {
    return api.post('/api/platform/draft/order/validatePostOrders', body, { mock: false })
  },
  // 获取计息天数（新）
  getInterestDayNew(params) {
    return api.get('/api/platform/operation/common/getInterestDayNew', params, {
      mock: false
    })
  },
  // 获取秒贴报价
  getMtQuotation(body) {
    return api.post('/api/platform/draft/order/queryDiscountQuotation', body, { mock: false })
  }
}

export default issueDraftApi
