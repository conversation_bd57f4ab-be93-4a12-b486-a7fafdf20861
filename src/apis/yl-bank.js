// 亿联银行支付渠道交易流程关键节点校验
import { api } from '@/utils/axios.js'

export default {
  // 是否已签收
  isSignPaymentSuccess(body) {
    return api.post('/api/platform/draft/order/getYlSignStatus', body, {
      mock: false
    })
  },

  // 获取亿联银行状态
  identifyYlBankStatus(body) {
    return api.post('/api/platform/orderHtmlParse/parseHtml', body, {
      mock: false,
      showError: false,
      headers: {
        'is-check-attack': false
      }
    })
  },
  // 识单助手查询网银订单状态
  queryYlBankOrderStatus(body) {
    return api.post('/api/platform/disConnection/discern/bankOrderStatus', body, {
      mock: false,
    })
  }
}
