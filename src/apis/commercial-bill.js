// 商票模块接口
import { api } from '@/utils/axios.js'

const commercialBillApi = {

  // 分页搜索商票交易列表
  queryCommercialOrderList(body) {
    return api.post('/api/platform/draft/commercialOrder/searchList', body, {
      mock: false,
    })
  },

  // 商票交易记录导出excel表格
  exportCommercialOrderList(body) {
    return api.post('/api/platform/draft/commercialOrder/searchList/export', body, {
      mock: false
    })
  },

  // 获取已经出账的奖励记录
  queryCommercialOrderRewardRecord(body) {
    return api.get('/api/platform/draft/commercialOrder/rewardRecord', body, {
      mock: false,
    })
  },

}

export default commercialBillApi
