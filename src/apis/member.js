import { api } from '@/utils/axios.js'

const memberApi = {
  // 我的会员
  getMyMemberInfo(body) {
    return api.get('/api/platform/member/myMemberInfo', body, {
      mock: false,
    })
  },
  // 会员等级详情
  getGradeList(body) {
    return api.get('/api/platform/member/getGradeList', body, {
      mock: false,
    })
  },
  // 近几个月交易数据概览
  generalView(body) {
    return api.get('/api/platform/member/generalView', body, {
      mock: false,
    })
  },
  // 额外奖励规则
  getExtraRewardConfig(body) {
    return api.get('/api/platform/member/getExtraRewardConfig', body, {
      mock: false,
    })
  },
  // 赠送明细
  getGiveDetail(body) {
    return api.post('/api/platform/member/getGiveDetail', body, {
      mock: false,
    })
  },
}

export default memberApi
