import { api } from '@/utils/axios.js'

const breachApi = {
  // 获取我的违约列表
  getMyBreachList(body) {
    return api.post('/api/platform/order/broke/listOneselfBroke', body, {
      mock: false
    })
  },

  // 获取对方违约列表
  getOtherBreachList(body) {
    const data = JSON.parse(JSON.stringify(body))
    delete data.brokeTab
    return api.post('/api/platform/order/broke/listOthersBroke', data, {
      mock: false
    })
  },

  // 导出我的违约
  exportMyBreach(body) {
    return api.post('/api/platform/order/broke/exportListOneselfBroke', body, {
      mock: false
    })
  },

  // 导出对方违约
  exportOtherBreach(body) {
    const data = JSON.parse(JSON.stringify(body))
    delete data.brokeTab
    return api.post('/api/platform/order/broke/exportListOthersBroke', data, {
      mock: false
    })
  },

  // 发起申诉
  appealOrder(body) {
    return api.post('/api/platform/order/broke/appealOrderBroke', body, {
      mock: false,
    })
  },

  // 获取tab数量
  getBreachTabNum(body) {
    return api.post('/api/platform/order/broke/listOneselfOrderBrokeTabNum', body, {
      mock: false
    })
  },

  // 违约记录数 v0.1.8
  countBrokeRecords(body) {
    return api.get('/api/platform/order/broke/countBrokeRecords', body, {
      mock: false
    })
  },

  // 列表查询违约数据 v0.1.8
  listBrokeRecords(body) {
    return api.get('/api/platform/order/broke/listBrokeRecords', body, {
      mock: false
    })
  },
}

export default breachApi
