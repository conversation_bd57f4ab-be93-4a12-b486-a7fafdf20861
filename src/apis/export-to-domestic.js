import { apiAddBaseUrl } from '@/utils/axios.js'

const api = apiAddBaseUrl(import.meta.env.VUE_APP_MALL_API_URL + '/api/goods-web')
const newApi = apiAddBaseUrl(import.meta.env.VUE_APP_MALL_API_URL)

// 获取轮播图列表
export const getSlideImageList = (data) => api.post('/client/ad/getSlideImageList', data)

// 出口转销-TOP商品列表
export const getExportResaleTop10List = (data) => api.get('/client/spu/getExportResaleTop10List', data)

// 出口转销-查看更多
export const getExportResaleList = (data) => api.get('/client/spu/getExportResaleList', data)

// 出口转内销报名接口
export const exportResaleSignUp = (body) => newApi.post('/api/user-web/client/leaveMessage/exportResale/signUp', body)
