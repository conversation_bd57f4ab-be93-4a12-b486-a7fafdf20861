import axios from 'axios'
import { ossUrl } from '@/constants/common'
import { apiAddBaseUrl } from '@/utils/axios.js'

const api = apiAddBaseUrl(import.meta.env.VUE_APP_MALL_API_URL + '/api/goods-web')

// 商品列表
export const getGoodsList = (data) => api.get('/client/spu/getSearchPage', data)

// 获取商品详情
export const getGoodsDetail = (data) => api.get('/client/spu/getGoodsInfo', data)

// 获取卖家商品详情
export const getSellerGoodsInfo = (data) => api.get('/client/spu/getInfo', data)

// 获取卖家商品列表
export const getUserGoodsList = (data) => api.get('/client/spu/getSellerPage', data)

// 商品上下架
export const updatePublishStatus = (data) => api.post('/client/spu/updatePublishStatus', data)

// 获取卖家服务商商品列表
export const getSellerServiceGoodsList = (data) => api.get('/client/spu/getSellerServicePage', data)

// 获取类目
export const getCategoryTree = (data) => api.post('/client/category/tree', data)

// 获取类目对应属性
export const getCategoryAttrData = (data) => api.post('/client/category/listAttr', data)

// 搜索属性
export const searchAttrList = (data) => api.post('/client/attr/allAttr', data)

// 添加属性
export const addAttr = (data) => api.post('/client/attr/add', data)

// 添加属性值
export const addAttrItem = (data) => api.post('/client/attr/item/add', data)

// 获取品牌
export const getCategoryBrand = (data) => api.get(`/client/category/brand/${data.id}`, data)

// 保存商品
export const goodsSubmit = (data) => api.post('/client/spu/save', data, { canEmpty: true })

// 商品收藏
export const goodsCollect = (data) => api.post('/client/user/spu/collect', data)

// 商品收藏取消
export const goodsCollectCancel = (data) => api.post('/client/user/spu/collect/cancel', data)

// 商品收藏
export const getCollectList = (data) => api.post('/client/user/spu/collect/getPage', data)

// 商品是否收藏
export const getUserCollect = (data) => api.get('/client/user/spu/collect/getUserCollect', data)

// 获取地址
export const getAreaList = () => axios.get(ossUrl + '/json/region-code.json?v=1')

// 获取类目绑定的广告
export const getCategoryAd = (data) => api.get('/client/category/ad', data)

// 获取字典
export const getDictListByKey = (data) => api.get('/dict/getListByKey', data)
// 获取销售字典
export const getSaleTypeList = () => api.get('/dict/getSalePriceType')

// 获取商机中心列表
export const getOpportunityList = (data) => api.post('/client/opportunity/list', data)

// 添加商机
export const opportunityAdd = (data) => api.post('/client/opportunity/add', data)

// 获取商机中心详情
export const getOpportunityDetail = (data) => api.get(`/client/opportunity/${data.id}`, {})

// 获取商机中心详情
export const getOpportunityInfo = (data) => api.get(`/client/opportunity/stat`, data)

// 店铺类目搜索
export const getShopCategoryInfo = (data) => api.get(`/client/category/shopTree`, data)

// 以图搜品
export const getCategoryByImage = (data) => api.get(`/client/category/getCategoryByImage`, data)

// market 市场临时跳转链接（可能是临时接口）
export const getMarketUrlInfo = (data) => api.get(`/client/market/temp/urls`, data)

// 推荐类目
export const getCategoryPreference = (data) => api.get(`/client/category/preference`, data)

// 通过参数查询标签信息
export const getListLabel = (data) => api.post(`/api/label/listLabel`, data)

// 价格类型
export const getPayPrice = () => api.get('/dict/getPayPriceTypeByWebsite')
