// 工具栏接口
import { api } from '@/utils/axios.js'

const toolbarApi = {

  // 新增投诉举报
  postAcceptorRisk(body) {
    return api.post('/api/platform/operation/riskComplaintsReport', body, {
      mock: false,
    })
  },

  // 查询自己举报的风险承兑人列表
  getOwnReportList(body) {
    return api.get('/api/platform/operation/riskComplaintsReport/ownReportList', body, {
      mock: false,
    })
  },
  // 切换账户-查询用户分组
  getUserGroup() {
    return api.get('/api/platform/switchGroup/corpMember/corpMemberList', null, {
      mock: false
    })
  },
  // 切换账户-移除用户分组
  removeUserGroup(uid) {
    return api.delete(`/api/platform/switchGroup/removeCorpMember/${uid}`, null, {
      mock: false
    })
  },
  // 切换账户-切换账户
  signinAccount(body) {
    return api.post('/api/platform/switchGroup/switchCorpMember', body, {
      mock: false
    })
  },
  // 切换账户-绑定账户
  bindingAccount(body) {
    return api.post('/api/platform/switchGroup/bindCorpMember', body, {
      mock: false
    })
  },
}

export default toolbarApi
