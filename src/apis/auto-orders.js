import { api } from '@/utils/axios.js'

// 自动接单 api
const autoOrdersApi = {
  // 创建自动接单规则
  saveBillOrderNeedRule(body) {
    return api.post('/api/platform/order/needRule/saveBillOrderNeedRule', body, {
      mock: false,
    })
  },

  // 修改自动接单规则
  updateBillOrderNeedRule(body) {
    return api.put('/api/platform/order/needRule/updateBillOrderNeedRule', body, {
      mock: false,
    })
  },

  // 修改自动接单规则名称
  updateBillOrderNeedRuleName(body) {
    return api.put('/api/platform/order/needRule/updateBillOrderNeedRuleName', body, {
      mock: false,
    })
  },

  // 删除自动接单规则
  deleteBillOrderNeedRule(billOrderNeedRuleId) {
    return api.delete(`/api/platform/order/needRule/deleteBillOrderNeedRule/${billOrderNeedRuleId}`, null, {
      mock: false,
    })
  },

  // 查询自动接单规则详情
  getBillOrderNeedRuleInfo(billOrderNeedRuleId) {
    return api.get(`/api/platform/order/needRule/getBillOrderNeedRuleInfo/${billOrderNeedRuleId}`, null, {
      mock: false,
    })
  },

  // 启动自动接单规则
  startBillOrderNeedRule(billOrderNeedRuleId) {
    return api.put(`/api/platform/order/needRule/startBillOrderNeedRule/${billOrderNeedRuleId}`, null, {
      mock: false,
    })
  },

  // 停止全部自动接单规则
  stopBillOrderNeedRule(billOrderNeedRuleId) {
    return api.put(`/api/platform/order/needRule/stopBillOrderNeedRule/${billOrderNeedRuleId}`, null, {
      mock: false,
    })
  },

  // 查询自动接单规则列表
  listBillOrderNeedRule() {
    return api.get('/api/platform/order/needRule/listBillOrderNeedRule', null, {
      mock: false,
    })
  },

  // 启动全部自动接单规则
  startAllBillOrderNeedRule(body) {
    return api.put('/api/platform/order/needRule/startAllBillOrderNeedRule', body, {
      mock: false,
      showError: false,
    })
  },

  // 停止全部自动接单规则
  stopAllBillOrderNeedRule() {
    return api.put('/api/platform/order/needRule/stopAllBillOrderNeedRule', null, {
      mock: false,
    })
  },

  // 获取企业已成功开通的签收账户信息 (该接口只返回签收账户列表数据，目前只用于创建修改扫票签收账户列表获取)
  getTraderCorpSuccessAccount() {
    return api.get('/api/platform/corp/getTraderCorpSuccessAccount', null, {
      mock: false,
    })
  },

  // 查询自动接单订单列表,v25
  postSearchOrderListByNeedRule(body) {
    return api.post('/api/platform/order/needRule/searchOrderListByNeedRule', body, {
      mock: false,
    })
  },
  // 自动扫票修改截止日期
  updateBillOrderNeedRuleStopTime(body) {
    return api.put('/api/platform/order/needRule/updateBillOrderNeedRuleStopTime', body, {
      mock: false,
    })
  }

}

export default autoOrdersApi
