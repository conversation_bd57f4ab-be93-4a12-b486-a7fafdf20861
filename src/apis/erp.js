import { api } from '@/utils/axios.js'

const erpApi = {
  // 商品入库记录
  getGoodsInStorageRecord(body) {
    return api.post('/api/platform/customer/inbound/frontList', body)
  },
  // 商品入库 新增/编辑
  goodsInStorage(body) {
    return api.post('/api/platform/customer/inbound/saveOrUpdate', body)
  },
  // 商品入库信息删除
  goodsInStorageDelete(body) {
    return api.post('/api/platform/customer/inbound/batchDel', body)
  },
  // 商品入库 批量
  goodsInStorageBatch(body) {
    return api.post('/api/platform/customer/inbound/import', body)
  },
  // 商品入库信息 导出
  goodsInStorageExport(body) {
    return api.post('/api/platform/customer/inbound/export', body)
  },

  // 商品出库 列表
  getGoodsOutStorageRecord(body) {
    return api.post('/api/platform/customer/outbound/frontList', body)
  },
  // 商品出库 新增/编辑
  goodsOutStorage(body) {
    return api.post('/api/platform/customer/outbound/saveOrUpdate', body)
  },
  // 商品出库信息删除
  goodsOutStorageDelete(body) {
    return api.post('/api/platform/customer/outbound/batchDel', body)
  },
  // 商品出库 批量
  goodsOutStorageBatch(body) {
    return api.post('/api/platform/customer/outbound/import', body)
  },
  // 商品出库信息 导出
  goodsOutStorageExport(body) {
    return api.post('/api/platform/customer/outbound/export', body)
  },

  // 商品库存 列表
  getGoodsStockRecord(body) {
    return api.post('/api/platform/customer/inventory/stat/frontList', body)
  },
  // 商品库存 导出
  goodsStockExport(body) {
    return api.post('/api/platform/customer/inventory/stat/export', body)
  },

  // 发票 列表
  getInvoiceRecord(body) {
    return api.post('/api/platform/customer/invoice/frontList', body)
  },
  // 发票 新增/编辑
  invoice(body) {
    return api.post('/api/platform/customer/invoice/saveOrUpdate', body)
  },
  // 发票信息删除
  invoiceDelete(body) {
    return api.post('/api/platform/customer/invoice/batchDel', body)
  },
  // 发票 批量导入
  invoiceBatch(body) {
    return api.post('/api/platform/customer/invoice/import', body)
  },
  // 发票信息 导出
  invoiceExport(body) {
    return api.post('/api/platform/customer/invoice/export', body)
  },
}

export default erpApi
