// 米相关接口
import { api } from '@/utils/axios.js'

const sdmApi = {

  // 获取米账号信息
  getSdmInfo(params) {
    return api.get('/api/platform/margin/queryByCorpId', params, {
      isCancelRequest: false
    })
  },

  // 获取米汇总表
  querySummaryForList(body) {
    return api.post('/api/platform/margin/querySummaryForList', body, {
      isCancelRequest: true,
    })
  },

  // 导出米汇总表
  querySummaryForExport(body) {
    return api.post('/api/platform/margin/querySummaryForExport', body)
  },

  // 获取米明细列表
  queryDetailedForList(body) {
    return api.post('/api/platform/margin/queryDetailedForList', body, {
      isCancelRequest: true,
      // mock: true,
      // mockId: 119
    })
  },

  // 导出米明细列表
  queryForListExport(body) {
    return api.post('/api/platform/margin/queryForListExport', body)
  },

  // 查询明细类型
  queryForDetailType(params) {
    return api.get('/api/platform/margin/queryForDetailType', params, {
      isCancelRequest: true
    })
  },

  // 获取充值记录
  getRechargeList(body) {
    return api.post('/api/platform/margin/rechargeList', body, {
      isCancelRequest: true
    })
  },

  // 导出充值记录
  rechargeExport(body) {
    return api.post('/api/platform/margin/rechargeExport', body)
  },

  // 获取米兑换记录列表
  queryExchangeOrderForList(body) {
    return api.post('/api/platform/margin/queryExchangeOrderForList', body, {
      isCancelRequest: true
    })
  },

  // 导出兑换记录
  exportExchangeOrder(body) {
    return api.post('/api/platform/margin/exportExchangeOrder', body)
  },

  // 米兑换详情
  queryExchangeDetail(body) {
    return api.post('/api/platform/margin/queryExchangeDetail', body, {
      isCancelRequest: true
    })
  },

  // 米兑换取消
  cancelExchange(params) {
    return api.post('/api/platform/margin/cancelExchange', params)
  },

  // 米兑换倒计时 即重新领取
  queryExchangeOrderLastSecond(body) {
    return api.post('/api/platform/margin/queryExchangeOrderLastSecond', body)
  },

  // 兑换商品列表
  getExchangeForList(body) {
    return api.post('/api/platform/margin/queryExchangeForList', body)
  },

  // 米兑换红包 兑换类型 1奖励米 3充值米 返回微信二维码领取url
  wechatExchange(body) {
    return api.post('/api/platform/margin/exchange', body)
  },

  // 历史提现人
  getHistoricalCashier(body) {
    return api.post('/api/platform/margin/getHistoricalCashier', body)
  },

  // 兑换到公司户 选择账户
  getAccountBankList(params) {
    return api.get('/api/platform/corp/listExchangeBankCard', params)
  },

  // 兑换 获取银行列表 开户行
  queryChildBanks(params) {
    return api.get('/api/platform/operation/bank/search', params)
  },

  // 兑换 提现到个人户
  personalExchange(body) {
    return api.post('/api/platform/margin/personalExchange', body)
  },
  // 未转入米历史沉淀资金奖励对私提现
  personalExchangeHistory(body) {
    return api.post('/api/platform/activity/activityRewardDetail/historyWithdrawToPerson', body)
  },
  // 兑换 提现到公司户
  enterpriseExchange(body) {
    return api.post('/api/platform/margin/enterpriseExchange', body)
  },
  // 未转入米历史沉淀资金奖励对公提现
  enterpriseExchangeHistory(body) {
    return api.post('/api/platform/activity/activityRewardDetail/historyWithdrawToBusiness', body)
  },

  // 充值-获取银行列表
  getBankList(body) {
    return api.post('/api/platform/margin/bankList', body)
  },

  // 充值-获取费率
  getServiceFee(body) {
    return api.post('/api/platform/margin/getServiceFeeByPayDictType', body)
  },

  // 充值-充值接口
  recharge(body) {
    return api.post('/api/platform/margin/recharge', body)
  },

  // 赠送豆
  calculateGivingAmt(body) {
    return api.post('/api/platform/margin/calculateGivingAmt', body, {
      isCancelRequest: false
    })
  },

  // 充值-获取订单支付结果
  getResultBySerialNo(body) {
    return api.post('/api/platform/margin/getResultBySerialNo', body)
  },

  // 线下转账-提交
  postOfflinePaySubmit(body) {
    return api.post('/api/platform/margin/submit', body)
  },

  // 获取验证码
  getExchangeSms(body) {
    return api.post('/api/platform/margin/personalExchangeSms', body)
  },

  // 查询限制兑换奖励信息
  queryLimitRewardExchange(body) {
    return api.post('/api/platform/margin/queryLimitRewardExchange', body)
  },
  // 获取承接贝配置类接口
  getMarginConfiguration(body) {
    return api.post('/api/platform/margin/getMarginConfiguration', body)
  }
}

export default sdmApi
