import { api } from '@/utils/axios.js'

const draftStockApi = {
  // 票据库存列表查询
  getStockList(body) {
    return api.post('/api/platform/discern/list', body, {
      mock: false
    })
  },

  // 票据库存列表查询
  getStockSummary(body) {
    return api.post('/api/platform/discern/discernSummary', body, {
      mock: false
    })
  },

  // 获取票据库存总数量
  getStockTabNum(body) {
    return api.post('/api/platform/discern/tabNum', body, {
      mock: false
    })
  },

  // 导出库存列表数据
  discernExportList(body) {
    return api.post('/api/platform/discern/exportList', body, {
      mock: false
    })
  },

  // 分享库存列表数据
  discernShareUrl(body) {
    return api.post('/api/platform/discern/shareUrl', body, {
      mock: false,
    })
  },

  // 获取分享数据
  discernShareList(body) {
    return api.post('/api/platform/discern/shareList', body, {
      mock: false,
    })
  },

  // 查询库存票据详情
  discernDetail(discernId) {
    return api.get(`/api/platform/discern/${discernId}`, null, {
      mock: false,
    })
  },

  // 票据出库
  discernOutStock(body) {
    return api.post('/api/platform/discern/outStock', body, {
      mock: false,
    })
  },

  // 批量出库
  postBatchOutStock(body) {
    return api.post('/api/platform/discern/batchOutStock', body, {
      mock: false,
    })
  },

  // 删除库存
  discernBatchDelete(body, timeout = 10000) {
    return api.post('/api/platform/discern/batchDelete', body, {
      mock: false,
      withoutCheck: true,
      timeout
    })
  },

  // 修改备注
  updateRemark(body) {
    return api.post('/api/platform/draft/addRemark', body, {
      mock: false,
    })
  },

}

export default draftStockApi
