// 米相关接口
import { api } from '@/utils/axios.js'

const couponApi = {

  // 我的消费券列表
  listCouponGroupByCorp(params) {
    return api.post('/api/platform/assets/coupon/listCouponGroupByCorp', params, {
      isCancelRequest: false
    })
  },
  // 查询券类型集合
  listCouponType(params) {
    return api.get('/api/platform/assets/coupon/listCouponType', params, {
      isCancelRequest: false
    })
  },
  // 查询券类型集合 这里边不包含 3块和6块的
  listCouponTypeForSplit(params) {
    return api.get('/api/platform/activity/marketing/coupon/listCouponTypeForSplit', params, {
      isCancelRequest: false
    })
  },
  // 领取明细
  createPageList(params) {
    return api.post('/api/platform/assets/couponGroupDetail/createPageList', params, {
      isCancelRequest: false
    })
  },
  // 领取明细-导出
  createListExport(params) {
    return api.post('/api/platform/assets/couponGroupDetail/createListExport', params, {
      isCancelRequest: false
    })
  },
  // 领取明细-历史数据导出
  exportReceiveHistoryList(params) {
    return api.post('/api/platform/assets/couponGroupDetail/historyCreateListExport', params, {
      isCancelRequest: false
    })
  },
  // 使用明细
  transactionPageList(params) {
    return api.post('/api/platform/assets/couponGroupDetail/transactionPageList', params, {
      isCancelRequest: false
    })
  },
  // 使用明细-导出
  transactionListExport(params) {
    return api.post('/api/platform/assets/couponGroupDetail/transactionListExport', params, {
      isCancelRequest: false
    })
  },
  // 使用明细-历史数据导出
  exportUsageHistoryList(params) {
    return api.post('/api/platform/assets/couponGroupDetail/historyTransactionListExport', params, {
      isCancelRequest: false
    })
  },
  // 失效明细
  expirePageList(params) {
    return api.post('/api/platform/assets/couponGroupDetail/expirePageList', params, {
      isCancelRequest: false
    })
  },
  // 失效明细-导出
  expireListExport(params) {
    return api.post('/api/platform/assets/couponGroupDetail/expireListExport', params, {
      isCancelRequest: false
    })
  },
  // 活动-领券中心
  receiveCenter() {
    return api.get('/api/platform/activity/marketing/coupon/receiveCenter', {}, {
      isCancelRequest: false
    })
  },
  // 渠道活动信息展示
  currentActivityData() {
    return api.get('/api/platform/activity/agile/paymentChannel/currentActivityData', {}, {
      isCancelRequest: false
    })
  },
  // 活动-领券中心-历史数据
  activityHistory() {
    return api.get('/api/platform/activity/marketing/coupon/activityHistory', {}, {
      isCancelRequest: false
    })
  },
  // 活动-领券中心-历史数据--渠道活动
  listActivityHistory() {
    return api.get('/api/platform/activity/agile/paymentChannel/listActivityHistory', {}, {
      isCancelRequest: false
    })
  },
  // 活动-拆分领取（充值云豆 + 完成笔数）
  splitReceiveRewards(params) {
    return api.post('api/platform/activity/marketing/coupon/splitReceiveRewardsCoupon', params, {
      isCancelRequest: false
    })
  },
  // 拆分领取--活动渠道
  receiveSplit(params) {
    return api.post('/api/platform/activity/agile/receiveSplit', params, {
      isCancelRequest: false
    })
  },
  // 活动-每日登录ERP领券
  loginRewards() {
    return api.post('/api/platform/activity/marketing/coupon/loginRewards', {}, {
      isCancelRequest: false
    })
  },
  // 获取待领取的数量
  getUnReceivedNum() {
    return api.get('api/platform/activity/marketing/coupon/getUnReceivedNum', {}, {
      isCancelRequest: false
    })
  },
  // 活动等级划分获取
  listActivityClassification() {
    return api.get('/api/platform/activity/marketing/coupon/listActivityClassification', {}, {
      isCancelRequest: false
    })
  },
  // 查询月度交易数据
  queryMonthTradeData() {
    return api.get('/api/platform/statistic/getMonthTradeData', {}, {
      isCancelRequest: false
    })
  },
  // 查询
  getActivityConfig() {
    return api.get('/api/platform/activity/activityConfig', {}, {
      isCancelRequest: false
    })
  },
}

export default couponApi
