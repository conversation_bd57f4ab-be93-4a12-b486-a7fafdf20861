// 文章模块接口
import { api } from '@/utils/axios.js'

const articleApi = {
  // 新闻动态-分页列表（不用登录）
  postContentListPage(body) {
    return api.post('/api/platform/content/listPageByCorp', body, {
      // mock: true,
      isSdApi: true
    })
  },
  // 新闻动态-详情
  getContentDetail(id) {
    return api.get(`/api/platform/content/detail/${id}`, null, {
      // mock: true,
      showError: false,
      isSdApi: true
    })
  },
  // 关于深度-详情
  getAboutDepthDetail(type) {
    return api.get(`/api/platform/aboutDepth/detail/${type}`, null, {
      // mock: true,
      isSdApi: true
    })
  },
  // 风险承兑人列表
  postAcceptorRiskList(body) {
    return api.post('/api/platform/operation/riskComplaintsReport/passedList', body, {
      // mock: true
    })
  },
  // 常见问题列表
  postCommonQuestionListPage(body) {
    return api.post('/api/platform/commonQuestion/listPage', body, {
      // mock: true
      isSdApi: true
    })
  },
  // 相关下载、业务规则
  postRuleOrDownloadListPage(body) {
    return api.post('/api/platform/ruleOrDownload/listPage', body, {
      // mock: true
      isSdApi: true,
      isCancelRequest: false
    })
  },
  // 业务规则详情
  getRuleOrDownloadDetail(id) {
    return api.get(`/api/platform/ruleOrDownload/detail/${id}`, null, {
      // mock: true
      isSdApi: true
    })
  },
  // 新闻动态-分页列表（需要登录，用于消息中心列表里的平台公告）
  postContentListPageLogin(body) {
    return api.post('/api/platform/content/listPageContent', body, {
      // mock: true,
      isSdApi: true
    })
  },
  // 新闻动态-分页列表（需要登录，用于消息中心列表里的平台公告）
  getGeneralArticlesList(body) {
    return api.get('/api/platform/generalArticles/getGeneralArticlesList', body)
  },
  // 消息通知全部已读
  updateAllRead() {
    return api.put('api/platform/content/updateAllRead', null, {
      returnOrigin: true
    })
  },
  // 承兑人预期列表
  listByAcceptorName(body) {
    return api.post('/api/platform/operation/acceptor/listByAcceptorName', body)
  },
  // 异常准入名单列表
  getCompanyBlacklist(body) {
    return api.post('/api/platform/risk/opening/blacklist/frontList', body)
  },
  // 违反软件安全规则名单
  getViolationFrontList(body) {
    return api.post('/api/platform/risk/software/security/violation/frontList', body)
  },
}
export default articleApi
