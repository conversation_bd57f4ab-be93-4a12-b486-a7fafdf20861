// 用户设置接口
import { api } from '@/utils/axios.js'

const settingApi = {

  // 查询企业设置
  getCorpConfig() {
    return api.get('/api/platform/corp/config', null, {
      mock: false,
      isCancelRequest: false
    })
  },

  // 保存企业设置
  postCorpConfig(params) {
    return api.post('/api/platform/corp/config', params, {
      mock: false,
      canEmpty: true
    })
  },

  // 查询电子交易账户列表
  getPaymentAccountList(params) {
    return api.get('/api/platform/corp/paymentAccountList', params, {
      mock: false,
      isCancelRequest: false
    })
  },

  // 查询背书账户
  getBankCard(bankCardId) {
    return api.get(`/api/platform/corp/bankCard/${bankCardId}`, null, {
      mock: false,
      isCancelRequest: false
    })
  },

  // 查询已通过的背书账户列表
  getPassedBankCardList(params) {
    return api.get('/api/platform/corp/passedBankCardList', params, {
      mock: false,
      isCancelRequest: false
    })
  },

  // 保存支付渠道默认背书账户
  saveDefaultBankCard(body) {
    return api.post('/api/platform/corp/paymentChannel/defaultBankCard', body, {
      mock: false,
      isCancelRequest: false
    })
  },
  // 保存支付渠道默认支付账户
  saveDefaultPayBankCard(body) {
    return api.post('api/platform/corp/paymentChannel/defaultPayBankCard', body, {
      mock: false,
      isCancelRequest: false
    })
  },

  // 保存支付渠道排序
  savePaymentChannelSort(body) {
    return api.post('/api/platform/corp/paymentChannel/sort', body, {
      mock: false,
      isCancelRequest: false
    })
  },

  // 查询企业黑名单列表
  getBlackList(blackType, params) {
    return api.get(`/api/platform/corp/blackConfig/list/${blackType}`, params, {
      mock: false,
    })
  },

  // 保存企业黑名单设置
  saveBlackSetting(body) {
    return api.post('/api/platform/corp/blackConfig', body, {
      mock: false,
    })
  },

  // 删除企业黑名单设置
  deleteBlackSetting(id, body) {
    return api.delete(`/api/platform/corp/blackConfig/${id}`, body, {
      mock: false,
    })
  },

  // 批量删除企业黑名单
  batchDeleteCompanyBlackList(body) {
    return api.delete('/api/platform/corp/blackConfig/batchDelete', body, {
      mock: false
    })
  },
  // 批量删除地区黑名单
  batchDeleteAreaBlackList(body) {
    return api.delete('/api/platform/corp/areaBlackConfig/batchDelete', body, {
      mock: false
    })
  },
  // TODO:更新企业黑名单设置, 目前无用
  updateBlackSetting(body) {
    return api.put('/api/platform/corp/blackConfig', body, {
      mock: false,
    })
  },

  // 查询企业地区黑名单列表
  getAreaBlackList(params) {
    return api.get('/api/platform/corp/areaBlackConfig/list', params, {
      mock: false,
    })
  },

  // 保存企业地区黑名单设置
  saveAreaBlackSetting(body) {
    return api.post('/api/platform/corp/areaBlackConfig', body, {
      mock: false,
    })
  },

  // 更新企业地区黑名单设置
  updateAreaBlackSetting(body) {
    return api.put('/api/platform/corp/areaBlackConfig', body, {
      mock: false,
    })
  },

  // 删除企业地区黑名单设置
  deleteAreaBlackSetting(id, body) {
    return api.delete(`/api/platform/corp/areaBlackConfig/${id}`, body, {
      mock: false,
    })
  },

  // 查询对方企业id
  searchCorp(params) {
    return api.get('/api/platform/corp/blackConfig/searchCorp', params, {
      mock: false,
    })
  },

  // 查询所有发布设置
  getPostConfig() {
    return api.get('/api/platform/corp/getPostOrderConfig', null, {
      mock: false,
      isCancelRequest: false
    })
  },

  // 查询所有风险关键字 按修改时间倒排
  searchKeywordList(body) {
    return api.post('/api/platform/order/billOrderRiskKeywordList/all', body, {
      mock: false,
    })
  },

  // 添加关键字
  addKeyword(body) {
    return api.post('/api/platform/order/billOrderRiskKeywordList/add', body, {
      mock: false,
    })
  },
  // 修改关键字接口
  editKeyword(body) {
    return api.post('/api/platform/order/billOrderRiskKeywordList/update', body, {
      mock: false,
    })
  },
  // 删除关键字
  delKeyword(body) {
    return api.post('/api/platform/order/billOrderRiskKeywordList/del', body, {
      mock: false,
    })
  },
  // 获取联系方式列表
  getMobileList() {
    return api.get('/api/platform/order/mobile/getMobileList', null, {
      mock: false,
    })
  },
  // 新增联系方式列表
  saveOrUpdate(body) {
    return api.post('/api/platform/order/mobile/saveOrUpdate', body, {
      mock: false,
    })
  },
  // 删除联系方式列表
  deleteMobile(body) {
    return api.post('/api/platform/order/mobile/deleteMobile', body, {
      mock: false,
    })
  },
  // 询户数据查询
  queryAccountAskData(params) {
    return api.get(`/api/platform/inquiryCorp/inquiry/${params}`, null, { mock: false })
  },
  // 接单方黑名单设置-批量导入
  importBlackListPerson(body) {
    return api.post('/api/platform/corp/uploadCorpBlackConfigRequest', body, {
      mock: false
    })
  },
  // 温馨提示-勾选
  warmPromptSave(body) {
    return api.post('/api/platform/newDraft/warmPromptSave', body, {
      mock: false
    })
  },
  // 获取交易大厅引导配置
  tradingHallConfig() {
    return api.get('/api/platform/config/tradingHallConfig', {
      mock: false
    })
  },
  // 修改回款账户
  updateSellerBankAccount(body) {
    return api.post('/api/platform/draft/order/updateSellerBankAccount', body, {
      mock: false,
    })
  },
}

export default settingApi
