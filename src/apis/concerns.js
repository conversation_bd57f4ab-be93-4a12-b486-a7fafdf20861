// 我关注的

import { api } from '@/utils/axios.js'

const concernsApi = {
  // 我关注的企业列表
  queryMyCompanyConcernsList(body) {
    return api.post('/api/platform/corp/follow/queryCorpFollowPageList', body, {
      mock: false
    })
  },
  // 关注
  postConcernsCompany(body) {
    return api.post('/api/platform/corp/follow/follow', body, {
      mock: false
    })
  },
  // 取消关注
  cancelConcernsCompany(body) {
    return api.post('/api/platform/corp/follow/unFollow', body, {
      mock: false
    })
  },
  // 获取关注的企业数据
  queryConcernsCompanyInfo() {
    return api.get('/api/platform/corp/follow/getFollowConfig', null, { mock: false })
  },
  // 获取关注的企业数量
  queryFollowingByCache() {
    return api.get('/api/platform/corp/follow/queryFollowingByCache', null, { mock: false })
  },
  // 获取手机验证码
  getMobileCheckCode(params) {
    return api.get('/api/platform/corp/follow/sendFollowCode', params, { mock: false })
  },
  // 关注文件上传
  postFileUpload(body) {
    return api.post('/api/platform/corp/follow/upload', body, { mock: false })
  }

}

export default concernsApi
