// 金额单位转换
import { yuan2wan, keep2Decimals, wan2yuan } from '@/common/js/number'
// 时间格式化
import { formatTime, getDateSpace } from '@/common/js/date'
import BigNumber from 'bignumber.js'

/**
 * 每十万扣款计算 => 年化利率 和 到账金额
 * @param {String} draftAmount 票面金额（元）
 * @param {String} lakhDeduction 十万直扣值
 * @param {String} interestDays 计息天数
 * @returns {Object}
 * {
 *  @param annualInterest 年化利率
 *  @param receivedAmount 到账金额（元）
 * }
 */
export function lakhDeductionMath(draftAmount, lakhDeduction, interestDays) {
  let obj = {
    annualInterest: '0.00', // 年化利率
    receivedAmount: '' // 到账金额（元）
  }
  if (draftAmount && lakhDeduction && interestDays) {
    // 年化利率 = 每十万扣款 / 10万 / 计息天数 * 360 * 100
    obj.annualInterest = keep2Decimals(
      new BigNumber(lakhDeduction)
        .dividedBy(1e5)
        .dividedBy(interestDays)
        .multipliedBy(360)
        .multipliedBy(100)
      , 'round', 4
    )

    // 到账金额 = 票面金额 - 利息（ 每十万扣款 ╳ 票面金额（万元）/10万 ）
    obj.receivedAmount = yuan2wan(new BigNumber(draftAmount * 1e4).minus(new BigNumber(lakhDeduction).multipliedBy(draftAmount / 10)))
  }

  return obj
}

/**
 * 以利率计算 => 每十万扣款 年化利率 和 到账金额
 * @param {String} draftAmount 票面金额（元）
 * @param {String} annualInterestInput 输入利率
 * @param {String} serviceCharge 每十万手续费
 * @param {String} interestDays 计息天数
 * @returns {Object}
 * {
 *  @param annualInterest 年化利率
 *  @param lakhDeduction 每十万扣款
 *  @param receivedAmount 到账金额（元）
 * }
 */
export function interestRateMath (draftAmount, annualInterestInput, serviceCharge, interestDays) {
  let obj = {
    annualInterest: '0.00',  // 年化利率
    lakhDeduction: '', // 每十万扣款
    receivedAmount: ''  // 到账金额（元）
  }

  // TODO:利率应该是可以为空的，为空就默认为0去计算
  if (draftAmount && (annualInterestInput || serviceCharge) && interestDays) {
    // 年化利率 = (利率 / 100 * 100000 * 计息天数 / 360取小数点两位小数 + 手续费）/ 100000 / 计息天数 * 360 * 100
    obj.annualInterest = keep2Decimals(new BigNumber(keep2Decimals(
      new BigNumber(annualInterestInput || 0)
        .dividedBy(100)
        .multipliedBy(1e5)
        .multipliedBy(interestDays) // 计息天数
        .dividedBy(360)
        .plus(serviceCharge || 0) // 手续费
      , 'round', 4
    ))
      .dividedBy(1e5)
      .dividedBy(interestDays)
      .multipliedBy(360)
      .multipliedBy(100), 'round', 4)

    // 每十万扣 = 利率 / 100 * 100000 * 计息天数 / 360取小数点两位小数 + 每十万手续费
    obj.lakhDeduction = keep2Decimals(
      new BigNumber(annualInterestInput || 0)
        .dividedBy(100)
        .multipliedBy(1e5)
        .multipliedBy(interestDays)
        .dividedBy(360)
        .plus(serviceCharge || 0)
      , 'round'
    )
    draftAmount = yuan2wan(draftAmount)
    // 到账金额 = 票面金额（元） - 利息（ 每十万扣款 ╳ 票面金额（万元）/10万 ） 这里的每十万扣款不能先取两位小数点，所以重新计算一遍
    obj.receivedAmount = keep2Decimals(new BigNumber(draftAmount * 1e4)
      .minus(new BigNumber(annualInterestInput || 0)
      .dividedBy(100)
      .multipliedBy(1e5)
      .multipliedBy(interestDays)
      .dividedBy(360)
      .plus(serviceCharge || 0).multipliedBy(draftAmount / 10)),'round', 2)
  }

  return obj
}

/**
 * （带议价）每十万扣款计算 => 年化利率
 * @param {String} lakhDeduction 十万直扣值
 * @param {String} interestDays 计息天数
 * @param {String} Bargain 议价值
 * @returns {String} 年化利率
 */
export function lakhDeductionMathWithBargain (lakhDeduction, interestDays, Bargain) {
  //  年化利率 = （每十万扣款 + 议价上限 ） / 10万 / 计息天数 * 360 *
  let annualInterest = 0
  if (lakhDeduction && interestDays) {
    annualInterest = keep2Decimals(
      new BigNumber(new BigNumber(lakhDeduction).plus(+Bargain))
        .dividedBy(1e5)
        .dividedBy(interestDays)
        .multipliedBy(360)
        .multipliedBy(100)
      , 'round', 4
    )
  }
  return annualInterest
}

/**
 * （带议价）以利率计算 => 年化利率
 * @param {String} annualInterestInput 输入利率
 * @param {String} serviceCharge 每十万手续费
 * @param {String} interestDays 计息天数
 * @param {String} Bargain 议价值
 * @returns {String} 年化利率
 */
export function interestRateMathWithBargain (annualInterestInput, serviceCharge, interestDays, Bargain) {
  //  年化利率 = (利率 / 1 00 * 100000 * 计息天数 / 360取小数点两位小数 + （手续费+议价上限））/ 100000 / 计息天数 * 360 * 100
  let annualInterest = 0
  if (annualInterestInput && interestDays) {
    annualInterest = keep2Decimals(new BigNumber(keep2Decimals(
      new BigNumber(annualInterestInput)
        .dividedBy(100)
        .multipliedBy(1e5)
        .multipliedBy(interestDays) // 计息天数
        .dividedBy(360)
        .plus(new BigNumber(+serviceCharge).plus(+Bargain) || 0) // 手续费
      , 'round', 4
    ))
      .dividedBy(1e5)
      .dividedBy(interestDays)
      .multipliedBy(360)
      .multipliedBy(100), 'round', 4)
  }

  return annualInterest
}
/**
 * 计算贴息金额
 * @param {String} draftAmount 票面金额
 * @param {String} receivedAmount 到账金额
 * @param {String} draftAmountUnit 金额单位，默认为万，可传值'wan','yuan'
 * @returns {String} 贴息金额（元）
 */

export function deductionAmountMath(draftAmount, receivedAmount, draftAmountUnit = 'wan') {
  if(!draftAmount) {
    return ''
  }
  const draftAmountYuan = draftAmountUnit === 'wan' ? wan2yuan(draftAmount) : draftAmount
  const receivedAmountYuan = draftAmountUnit === 'wan' ? wan2yuan(receivedAmount) : receivedAmount
  // 贴息金额 = 票面金额 - 到账金额
  const amount = keep2Decimals(new BigNumber(draftAmountYuan).minus(receivedAmountYuan),'round', 2)
  return amount
}

/**
 * 计算调整天数
 * @param {Object|String} maturityDate 到期日
 * @param {Array} holidays 节假日列表
 * @returns {String|Number} 调整天数
 */
 export function getAdjustDays(maturityDate, holidays = []) {
  let addDay = '' // 需要加多少天
  // new Date的参数兼容问题 ，和-部分浏览器不兼容 需要装换成/
  if(typeof(maturityDate) === 'string') maturityDate.replace(/-|\,/g, '/')
  let addDayAfterDate = new Date(maturityDate)
  let addDayAfter = formatTime(maturityDate, 'YYYY-MM-DD')
  // 查找循环开始的index
  let index = holidays.findIndex( v => v?.civicHoliday === addDayAfter)
  // 如果查询当天不在节假日列表 或者节假日列表的当天是上班的 则直接返回空
  if(index === -1 || holidays[index]?.isHoliday === 0){
    return ''
  }
  for (let i = index; i < holidays.length; i++) {
    const item = holidays[i];
    if (item?.civicHoliday === addDayAfter) {
      addDay === '' ? addDay = item.isHoliday : addDay += item.isHoliday
      addDayAfter = formatTime(addDayAfterDate.setDate(addDayAfterDate.getDate() + 1), 'YYYY-MM-DD')
    } else if(addDay !== '') {
      break
    }
    
  }
  // 如果调整天数为0 则返回'' 
  return addDay === 0 ? '' : addDay
}

/**
 * 计算计息天数
 * @param {Object|String} discountDate 贴现日期
 * @param {Object|String} maturityDate 到期日期
 * @param {String|Number} adjustDays 调整天数
 * @returns {String|Number} 计息天数
 */
export function getInterestAccrualDay(discountDate, maturityDate, adjustDays) {
  if(!discountDate || !maturityDate) {
    return ''
  }
  // new Date的参数兼容问题 ，和-部分浏览器不兼容 需要装换成/
  if(typeof(maturityDate) === 'string') maturityDate.replace(/-|\,/g, '/')
  let afterDate = new Date(maturityDate)
  const addDayAfter = formatTime(afterDate.setDate(afterDate.getDate() + Number(adjustDays)), 'YYYY-MM-DD')
  return getDateSpace(addDayAfter, discountDate)
}

/**
 * 计算保证金
 * @param {String|Number} draftAmount 票面金额
 * @param {String} draftAmountUnit 金额单位，默认为元，可传值'wan','yuan'
 * @returns {Number} 保证金金额
 */
 export function marginAmountMath(draftAmount, draftAmountUnit = 'yuan') {
  if(!draftAmount) {
    return 0
  }
  // 保证金=万分之3*票面金额（最低 10 元，最高 600 元）
  let marginAmount = new BigNumber(draftAmount).multipliedBy(draftAmountUnit === 'wan' ? 1e4 : 1)
          .multipliedBy(0.0003)
          .toFixed(2)
  if (marginAmount < 10) {
    marginAmount = 10
  } else if (marginAmount > 600) {
    marginAmount = 600
  }
  return +marginAmount
}
