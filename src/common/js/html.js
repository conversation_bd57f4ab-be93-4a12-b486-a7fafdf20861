import { isIOS, isInWeChat } from './env'

/**
 * 加载JS文件
 * @param {string} url JS路径
 * @returns {Promise<any>} 加载成功的回调
 */
export function loadScript(url) {
  return new Promise((resolve, reject) => {
    const $script = document.createElement('script')
    $script.src = url
    $script.async = true
    $script.addEventListener('error', (e) => {
      reject(e)
    })
    $script.addEventListener('load', () => {
      resolve()
    })
    document.body.appendChild($script)
  })
}

/**
 * 获取图片的长宽，也可用于校验图片的URL是否有效
 * @param {string} url img的路径
 * @returns {Promise<any>} 有效返回图片宽高，无效返回false
 */
export function getImgSize(url) {
  return new Promise((resolve) => {
    const $img = document.createElement('img')
    $img.src = url
    $img.style.opacity = 0
    $img.addEventListener('error', () => {
      document.body.removeChild($img)
      resolve(false)
    })
    $img.addEventListener('load', () => {
      const data = {
        width: $img.naturalWidth,
        height: $img.naturalHeight,
      }
      document.body.removeChild($img)
      resolve(data)
    })
    document.body.appendChild($img)
  })
}

/**
 * 根据给出的文字内容，获取宽度自适应时的宽度
 * @param {string} content 文字内容
 * @param {object} targetDom|styleObj 文字样式继承某dom，或者定制该文字的style
 * @returns {number} number clientWidth 宽度
 */
export function getAutoWidth(content, targetDom) {
  const $dom = document.createElement('div')
  $dom.innerHTML = content
  $dom.style.position = 'absolute'
  $dom.style.opacity = '0'
  $dom.style.padding = '0'
  $dom.style.marrgin = '0'
  if (targetDom.nodeType) {
    // 如果是添加到特定的Dom中，继承其字体特性
    ;['font-size', 'font-weight', 'border', 'padding'].forEach((key) => {
      $dom.style[key] = window.getComputedStyle(targetDom, null)[key]
    })
  } else {
    // 如果这是个样式对象
    const styleObj = targetDom
    for (const key in styleObj) {
      $dom.style[key] = styleObj[key]
    }
  }
  document.body.appendChild($dom)
  const clientWidth = $dom.clientWidth + 1
  document.body.removeChild($dom)
  return clientWidth
}

/**
 * 根据给出的文字内容，获取文字自动换行后的高度
 * @param {string} content 文字内容
 * @param {object} targetDom|styleObj 文字样式继承某dom，或者定制该文字的style
 * @returns {number} clientHeight 高度
 */
export function getAutoHeight(content, targetDom) {
  const $dom = document.createElement('div')
  $dom.innerHTML = content
  $dom.style.position = 'absolute'
  $dom.style.opacity = '0'
  $dom.style.padding = '0'
  $dom.style.marrgin = '0'
  console.log(targetDom)
  if (targetDom.nodeType) {
    // 如果是添加到特定的Dom中，继承其字体特性
    ;['font-size', 'font-weight', 'line-height', 'width', 'border', 'padding'].forEach((key) => {
      $dom.style[key] = window.getComputedStyle(targetDom, null)[key]
    })
  } else {
    // 如果这是个样式对象
    const styleObj = targetDom
    for (const key in styleObj) {
      $dom.style[key] = styleObj[key]
    }
  }
  document.body.appendChild($dom)
  const { clientHeight } = $dom
  document.body.removeChild($dom)
  return clientHeight
}

/**
 * 兼容设置页面title
 * @param {string} titleText title文案
 */
export function setTitle(titleText) {
  document.title = titleText
  if (isIOS || isInWeChat) {
    const $iframe = document.createElement('iframe')
    $iframe.title = titleText
    $iframe.style.opacity = '0'
    document.body.appendChild($iframe)
    document.body.removeChild($iframe)
  }
}

/**
 * 拷贝内容到剪贴板（必须由用户主动触发）
 * @method copyToClipboard
 * @param {String} content 要拷贝的内容
 * @return {Boolean} 拷贝是否成功
 */
export function copyToClipboard(content) {
  if (!document.queryCommandSupported('copy')) {
    return false
  }

  let $input = document.createElement('input')
  $input.style.opacity = '0'
  $input.value = content
  document.body.appendChild($input)
  $input.select()

  const result = document.execCommand('copy')
  document.body.removeChild($input)
  $input = null

  return result
}

/**
 * 根据任意个图片img，合并成一张canvas
 * @param {number} width canvas的长度
 * @param {number} height canvas的高度
 * @param {array} imgDataList img信息，包括img的URL，及该img绘制在canvas中的配置参数，即canvas.drawImage的参数
 * @returns {Promise<[any]>} 全部图片加载成功且描绘成功后的promise
 */
export function createCanvasFromURL(width, height, imgDataList) {
  const promiseLise = []
  const $canvas = document.createElement('canvas')
  $canvas.width = width
  $canvas.height = height
  for (let i = 0, { length } = imgDataList; i < length; i++) {
    const imgItem = imgDataList[i]
    imgItem.config = imgItem.config || [0, 0]
    promiseLise.push(
      new Promise((resolve) => {
        const $img = document.createElement('img')
        $img.src = imgItem.url
        $img.style.opacity = '0'
        $img.addEventListener('error', () => {
          document.body.removeChild($img)
          resolve(false)
        })
        $img.addEventListener('load', () => {
          $canvas.getContext('2d').drawImage($img, ...imgItem.config)
          document.body.removeChild($img)
          resolve($canvas)
        })
        document.body.appendChild($img)
      }),
    )
  }
  return Promise.all(promiseLise)
}

/**
 * 获取当前光标偏移值
 * @returns {number} 当前光标偏移值
 */
export function getCursorOffset() {
  const selectObj = getSelection() // 获取选中对象
  if (selectObj && selectObj.rangeCount > 0) {
    // 如果有选择范围
    const selectRange = selectObj.getRangeAt(0) // 获取选中对象的第一个选择范围
    return selectRange.startOffset // 记录选项开始位置
  }
  return -1
}

/**
 * 设置光标位置
 * @param {object} dom 光标所在dom节点
 * @param {number} offset 光标偏移值
 */
export function setCursorOffset(dom, offset) {
  const selectObj = getSelection() // 获取选中对象
  const selectRange = selectObj.getRangeAt(0) // 获取选中对象的第一个选择范围
  selectRange.setStart(dom, offset) // 设置光标位置
  selectRange.collapse(true) // 光标开始和光标结束重叠
  selectObj.removeAllRanges() // 清除选定对象的所有光标对象
  selectObj.addRange(selectRange) // 插入新的光标对象
}

/**
 * 设置光标位于某元素后面
 * @param {object} dom 光标所在dom节点
 */
export function setCursorAfter(dom) {
  const selectObj = getSelection() // 获取选中对象
  const selectRange = document.createRange()
  selectRange.setStartAfter(dom)
  selectRange.collapse(true) // 光标开始和光标结束重叠
  selectObj.removeAllRanges() // 清除选定对象的所有光标对象
  selectObj.addRange(selectRange) // 插入新的光标对象
}

/**
 * 判断是否支持某CSS样式
 * @param {string} attr 样式名
 * @param {string} value 样式值
 * @returns {boolean} 是否支持某CSS样式
 */
export function cssSupport(attr, value) {
  const element = document.createElement('div')
  if (attr in element.style) {
    element.style[attr] = value
    return element.style[attr] === value
  } else {
    return false
  }
}

/**
 * 容器滚动函数
 * @param {string} $target 需要滚动的目标dom
 * @param {number} space 滚动距离
 * @param {number} time 分多少次执行完成
 * @param {function} endHandler 执行完成回调
 */
// eslint-disable-next-line
export function scrollTo($target, space, time = 30, endHandler) {
  const requestAnimFrame = (() =>
    window.requestAnimationFrame ||
    window.webkitRequestAnimationFrame ||
    window.mozRequestAnimationFrame ||
    function (callback) {
      window.setTimeout(callback, 1000 / 60)
    })()

  const eachSpace = space / time // 单次滚动的距离
  function move(moveSpace, remainTime) {
    requestAnimFrame(() => {
      remainTime--
      $target.scrollTop += moveSpace
      if (remainTime <= 0) {
        if (endHandler && typeof endHandler === 'function') {
          endHandler()
        }
      }
      remainTime > 0 && move(moveSpace, remainTime)
    })
  }

  move(eachSpace, time)
}

/**
 * 获取电脑安装的flash信息
 * @returns {*} flash信息
 */
export function getFlashData() {
  if (window.ActiveXObject) {
    return new window.ActiveXObject('ShockwaveFlash.ShockwaveFlash')
  } else {
    return navigator.plugins['Shockwave Flash']
  }
}
