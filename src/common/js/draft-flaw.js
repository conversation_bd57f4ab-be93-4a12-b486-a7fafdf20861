// 票据瑕疵相关
import {
  BACK_DEFECT_TYPE_NAME_MAP, // 票据瑕疵类型
  // BACK_DEFECT_TYPE_VALUE_MAP, // 票据瑕疵类型 id 映射 是否显示数字
  // BACK_DEFECT_TYPE_SHOW_NUM_MAP,// 票据瑕疵类型 id 映射 名称
  QIAN_SHOU_BACK_DEFECT_TYPE_VALUE_MAP, // 识票助手票据瑕疵类型 id 映射 名称
  QIAN_SHOU_BACK_DEFECT_TYPE_SHOW_NUM_MAP,// 识票助手票据瑕疵类型 id 映射 是否显示数字
} from '@/constant'
const ticketFlawList = [
  {
    name: BACK_DEFECT_TYPE_NAME_MAP.ABA.name, // 回出票人aba
    id: BACK_DEFECT_TYPE_NAME_MAP.ABA.id,
    keyName: BACK_DEFECT_TYPE_NAME_MAP.ABA.key,
  },
  {
    name: BACK_DEFECT_TYPE_NAME_MAP.ABCA.name, // 回出票人abca
    id: BACK_DEFECT_TYPE_NAME_MAP.ABCA.id,
    keyName: BACK_DEFECT_TYPE_NAME_MAP.ABCA.key,
  },
  {
    name: BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_HUI_TOU.name, // 背书回头
    id: BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_HUI_TOU.id,
    keyName: BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_HUI_TOU.key,
  },
  {
    name: BACK_DEFECT_TYPE_NAME_MAP.HUI_SHOU_KUAN_REN.name, // 回收款人
    id: BACK_DEFECT_TYPE_NAME_MAP.HUI_SHOU_KUAN_REN.id,
    keyName: BACK_DEFECT_TYPE_NAME_MAP.HUI_SHOU_KUAN_REN.key,
  },
  {
    name: BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_CHONG_FU.name, // 背书重复
    id: BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_CHONG_FU.id,
    keyName: BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_CHONG_FU.key
  },
  {
    name: BACK_DEFECT_TYPE_NAME_MAP.ABB.name, // abb
    id: BACK_DEFECT_TYPE_NAME_MAP.ABB.id,
    keyName: BACK_DEFECT_TYPE_NAME_MAP.ABB.key
  },
  {
    name: BACK_DEFECT_TYPE_NAME_MAP.ZHI_YA.name, // 质押
    id: BACK_DEFECT_TYPE_NAME_MAP.ZHI_YA.id,
    keyName: BACK_DEFECT_TYPE_NAME_MAP.ZHI_YA.key,
  },
  {
    name: BACK_DEFECT_TYPE_NAME_MAP.BAO_ZHENG.name, // 保证
    id: BACK_DEFECT_TYPE_NAME_MAP.BAO_ZHENG.id,
    keyName: BACK_DEFECT_TYPE_NAME_MAP.BAO_ZHENG.key,
  },
  {
    name: BACK_DEFECT_TYPE_NAME_MAP.SHANG_XIA_BU_YI_ZHI.name, // 不一致
    id: BACK_DEFECT_TYPE_NAME_MAP.SHANG_XIA_BU_YI_ZHI.id,
    keyName: BACK_DEFECT_TYPE_NAME_MAP.SHANG_XIA_BU_YI_ZHI.key,
  },
  {
    name: BACK_DEFECT_TYPE_NAME_MAP.OTHER.name, // 其他
    id: BACK_DEFECT_TYPE_NAME_MAP.OTHER.id,
    keyName: BACK_DEFECT_TYPE_NAME_MAP.OTHER.key,
  },
  {
    name: BACK_DEFECT_TYPE_NAME_MAP.HUI_GOU_TIE_XIAN.name, // 回购式贴现
    id: BACK_DEFECT_TYPE_NAME_MAP.HUI_GOU_TIE_XIAN.id,
    keyName: BACK_DEFECT_TYPE_NAME_MAP.HUI_GOU_TIE_XIAN.key
  }
]
const ticketFlawMap = {
  [BACK_DEFECT_TYPE_NAME_MAP.ABA.key]: 0,
  [BACK_DEFECT_TYPE_NAME_MAP.ABCA.key]: 0,
  [BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_HUI_TOU.key]: 0,
  [BACK_DEFECT_TYPE_NAME_MAP.HUI_SHOU_KUAN_REN.key]: 0,
  [BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_CHONG_FU.key]: 0,
  [BACK_DEFECT_TYPE_NAME_MAP.ABA.key]: 0,
  [BACK_DEFECT_TYPE_NAME_MAP.ZHI_YA.key]: 0,
  [BACK_DEFECT_TYPE_NAME_MAP.BAO_ZHENG.key]: 0,
  [BACK_DEFECT_TYPE_NAME_MAP.SHANG_XIA_BU_YI_ZHI.key]: 0,
  [BACK_DEFECT_TYPE_NAME_MAP.OTHER.key]: 0,
  [BACK_DEFECT_TYPE_NAME_MAP.OTHER.descKey]: '',
  [BACK_DEFECT_TYPE_NAME_MAP.HUI_GOU_TIE_XIAN.key]: 0
}

/**
 * 将瑕疵字符串转为对象
 * @param {String} str 票据瑕疵字符串 "1_1|2_1"
 * @returns {Object} 请求对象
 */

export function defectStrToObj(str) {
  const res = JSON.parse(JSON.stringify(ticketFlawMap))
  if (!str) {
    return res
  }
  const list = str.split('|')
  list.forEach(flaw => {
    const item = flaw.split('_')
    const id = item[0]
    const num = item[1]
    const one = ticketFlawList.find(tk => tk.id === parseInt(id))
    if (one.keyName === BACK_DEFECT_TYPE_NAME_MAP.OTHER.key) {
      res[BACK_DEFECT_TYPE_NAME_MAP.OTHER.key] = 1
      res[BACK_DEFECT_TYPE_NAME_MAP.OTHER.descKey] = num
    } else {
      res[one.keyName] = num
    }
  })
  return res
}

/**
 * 将原始瑕疵字符串转为渲染字符串
 * @param {String} str 票据瑕疵字符串 "1_1|2_1"
 * @returns {String} 渲染字符串
 */

export function toDefectStr(str) {
  if (!str) {
    return ''
  }
  let res = str.split('|').map(item => {
    let isOtherItem = Number(item.split('_')[0]) === BACK_DEFECT_TYPE_NAME_MAP.OTHER.id
    return QIAN_SHOU_BACK_DEFECT_TYPE_VALUE_MAP[item.split('_')[0]] + (QIAN_SHOU_BACK_DEFECT_TYPE_SHOW_NUM_MAP[item.split('_')[0]] && item.split('_')[1] ? isOtherItem?`[${item.split('_')[1]}]`:`(${item.split('_')[1]})` : '')
  })
    .join('，')
  return res || ''
}

const titleMap = {
  ownCompanyList: '自有户',
  endorseDefect: '背书手数',
  acceptBlackList: '承兑人黑名单',
  payeeBlackList: '收款人黑名单',
  sensitiveIndustryList: '敏感行业',
  endorserBlackList: '背书人黑名单',
  riskCorps: '异常企业',
  abnormalAccessRiskCorps: '异常准入企业',
  violateSoftwareSecurityCorps: '违反软件安全规则企业',
}

/**
 * 将瑕疵提示json字符串转为渲染对象列表
 * @param {String} str 瑕疵提示json字符串
 * @returns {Array} 渲染对象列表
 */

export function toDefectsNotifyTemp(str) {
  let obj = JSON.parse(str)
  let res = {}
  let list = []
  console.log(obj);
  Object.keys(obj).forEach(key => {
    if (key === 'endorseDefect') {
      let endorseDefectList = Object.keys(obj.endorseDefect).map(innerKey => obj.endorseDefect[innerKey].replace(/%(\d*)%/g, '<span style="color:red"> $1 </span>'))
        .filter(v => v)
      res.endorseDefect = endorseDefectList.length ? `${endorseDefectList.join('；')}。` : ''
    } else if (obj[key].filter(v => v).length) {
      list.push({
        title: titleMap[key],
        list: obj[key]
      })
    }
  })
  res.other = list
  return res
}

/**
 * 将瑕疵提示json字符串转为渲染对象列表(新版)
 * @param {String} str 瑕疵提示json字符串
 * @returns {Array} 渲染对象列表
 */
export function toDefectsNotifyTempNew(str, fontSelfOwn, backSelfOwn) {
  let obj = JSON.parse(str)
  let res = []
  Object.keys(titleMap).forEach(key => {
    let title = titleMap[key]
    let list = []

    if (key === 'endorseDefect') {
      if (!obj.endorseDefect || Object.keys(obj.endorseDefect).length === 0) {
        return
      }
      for (const [_, innerValue] of Object.entries(obj.endorseDefect)) {
        const one = innerValue.split(/(%\d*%)/g)
        const oneList = one.map(v => ({
          style: v.indexOf('%') > -1 ? { color: '#ffb253' } : null,
          content: v.replace(/%(\d*)%/g, '$1')
        }))
        list.push(oneList)
      }
    } else {
      if (!obj[key]?.filter(v => v)?.length) {
        return
      }
      list = obj[key]
    }

    if (key === 'ownCompanyList') {
      title += ` (${fontSelfOwn ? '正' : ''}${backSelfOwn ? '背' : ''}面)`
    } else if (key === 'riskCorps') {
      title += `（${list.length}）`
      list = list.map(i => `${i} 已被平台限制`)
    } 
    if (key === 'abnormalAccessRiskCorps' || key === 'violateSoftwareSecurityCorps' ){
      //这两个需要置顶
      title += `（${list.length}）`
      const data = {
        key,
        title,
        list
      }
      if (key === 'abnormalAccessRiskCorps') {
        res.unshift(data)
      } else {
        if (res.some(item => item.key === 'violateSoftwareSecurityCorps')) {
          res.splice(1,0,data)
        } else {
          res.unshift(data)
        }
      }
    } else {
      res.push({
        key,
        title,
        list
      })
    }
  })
  return res
}