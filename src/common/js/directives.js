export default {
  install(app) {
    // 仅输入数字
    // <input v-only-number>copy</div>>
    app.directive('onlyNumber', {
      mounted: (targetDom, binding) => {
        if (typeof binding.value === 'boolean' && binding.value.toString() === 'false') {
          return
        }
        const options = binding.value && typeof binding.value === 'object' ? binding.value : {}
        // 是否允许输入负数
        const allowNegative = typeof options.negative === 'boolean' ? options.negative : true
        // 判断一个值是否负数
        const isNegative = (value) => /^\s*-/.test(String(value))
        targetDom.addEventListener(
          'input',
          (event) => {
            event = event || window.event
            const $dom = event.target
            const newValue = ($dom.value || '').trim()
            if (newValue === '' || (!isNaN(newValue) && (!isNegative(newValue) || allowNegative))) {
              $dom.dataset.preValue = newValue.trim()
              $dom.value = newValue.trim()
            } else {
              $dom.value = $dom.dataset.preValue || ''
            }
          },
          { capture: true },
        )
      },
    })

    // 输入长度
    // <input v-length='18'>
    app.directive('length', {
      mounted: (targetDom, binding) => {
        const length = binding.value
        targetDom.addEventListener(
          'input',
          (event) => {
            event = event || window.event
            const $dom = event.target
            $dom.value = $dom.value.substr(0, length)
          },
          { capture: true },
        )
      },
    })

    // 自动化聚焦
    // <input v-focus>
    app.directive('focus', {
      mounted: (el) => el.focus(),
    })

    // 复制
    // <div v-copy="number">copy</div>
    // <div v-copy="{ value: '111', onSuccess: () => {}, onError: () => {} }">copy</div>
    app.directive('copy', {
      mounted: (targetDom, binding) => {
        let onSuccess = () => {}
        let onError = () => {}
        if (binding.value && typeof binding.value === 'object') {
          targetDom.dataset.copyValue = binding.value.value
          // eslint-disable-next-line prefer-destructuring
          onSuccess = binding.value.onSuccess
          // eslint-disable-next-line prefer-destructuring
          onError = binding.value.onError
        } else {
          targetDom.dataset.copyValue = binding.value
        }
        targetDom.addEventListener('click', async () => {
          try {
            // 使用最新clipboard API
            await navigator.clipboard.writeText(targetDom.dataset.copyValue)
            onSuccess()
          } catch (err) {
            let $input = document.createElement('textarea')
            $input.style.opacity = '0'
            $input.value = targetDom.dataset.copyValue
            document.body.appendChild($input)
            $input.select()

            const isSuccess = document.execCommand('copy')
            isSuccess ? onSuccess() : onError()
            document.body.removeChild($input)
            $input = null
          }
        })
      },

      // 更新存储的值，存储在 dom 的 dataset 中
      updated: (el, binding) => {
        el.dataset.copyValue = binding.value.value
      },
    })

    // <div v-shortcut="{'27': key1}">copy</div>

    app.directive('shortcut', {
      mounted: (targetDom, binding) => {
        // 往 dom 对象中挂载函数，以便卸载时，消除消息监听，keyCode 编码映射表：https://www.bejson.com/othertools/keycodes/
        targetDom.shortcutFun = function (event) {
          Object.keys(binding.value).forEach((key) => {
            event.keyCode.toString() === key && binding.value[key]()
          })
        }
        window.addEventListener('keyup', targetDom.shortcutFun)
      },

      // 指令被卸载，消除消息监听
      unmounted: (targetDom) => {
        window.removeEventListener('keyup', targetDom.shortcutFun)
      },
    })

    // <div v-click-outside="{listen: () => showSettings, handler: hideSetting}">
    //   <div>copy</div>
    //   <div>copy2</div>
    // </div>
    app.directive('clickOutside', {
      mounted: (targetDom, binding) => {
        targetDom.clickOutsideFun = function (event) {
          try {
            const { handler, isListen } = binding.value
            isListen() && event.target !== targetDom && !targetDom.contains(event.target) && handler()
          } catch (e) {
            // eslint-disable-next-line no-console
            console.error('全局指令，请输入正确配置，v-click-outside="{listen: () => showSettings, handler: hideSetting}"')
          }
        }
        window.addEventListener('click', targetDom.clickOutsideFun, { capture: true })
        window.addEventListener('touchend', targetDom.clickOutsideFun, { capture: true })
      },

      // 指令被卸载，消除消息监听
      unmounted: (targetDom) => {
        window.removeEventListener('click', targetDom.clickOutsideFun, { capture: true })
        window.removeEventListener('touchend', targetDom.clickOutsideFun, { capture: true })
      },
    })
  },
}
