/* eslint-disable no-empty-function */
import Vue from 'vue'

// 仅输入数字
// <input v-only-number>copy</div>>
Vue.directive('onlyNumber', {
  inserted: (targetDom, binding) => {
    if (typeof binding.value === 'boolean' && binding.value.toString() === 'false') {
      return
    }
    const options = binding.value && typeof binding.value === 'object' ? binding.value : {}
    // 是否允许输入负数
    const allowNegative = typeof options.negative === 'boolean' ? options.negative : true
    // 判断一个值是否负数
    const isNegative = value => /^\s*-/.test(String(value))
    targetDom.addEventListener('input', event => {
      event = event || window.event
      const $dom = event.target
      const newValue = ($dom.value || '').trim()
      if (newValue === '' || (!isNaN(newValue) && (!isNegative(newValue) || allowNegative))) {
        $dom.dataset.preValue = newValue.trim()
        $dom.value = newValue.trim()
      } else {
        $dom.value = $dom.dataset.preValue || ''
      }
    }, { capture: true })
  }
})

// 输入长度
// <input v-length='18'>
Vue.directive('length', {
  inserted: (targetDom, binding) => {
    const length = binding.value
    targetDom.addEventListener('input', event => {
      event = event || window.event
      const $dom = event.target
      $dom.value = $dom.value.substr(0, length)
    }, { capture: true })
  }
})

// 自动化聚焦
// <input v-focus>
Vue.directive('focus', {
  inserted: el => el.focus()
})

// 元素dataset属性兼容性很差,IE11以上才支持,所以写了个方法兼容下
function getDataset(ele){
  if(ele.dataset) {
      return ele.dataset
  }
  else {
      let attrs = ele.attributes, // 元素的属性集合
          dataset = {},
          name,
          matchStr;
      for(let i = 0; i < attrs.length; i++) {
          // 是否是data- 开头
          matchStr = attrs[i].name.match(/^data-(.+)/);
          if(matchStr) {
              // data-auto-play 转成驼峰写法 autoPlay
              name = matchStr[1].replace(/-([\da-z])/gi,function(all,letter){
                  return letter.toUpperCase();
              });
              dataset[name] = attrs[i].value;
          }
      }
      return dataset;
  }
}

// 设置 属性
function setDataset(ele, value){
  ele.setAttribute('data-copy-value', value)
}

// 复制
// <div v-copy="number">copy</div>
// <div v-copy="{ value: '111', onSuccess: () => {}, onError: () => {} }">copy</div>
Vue.directive('copy', {
  bind: (targetDom, binding) => {
    let onSuccess = () => {}
    let onError = () => {}
    if (binding.value && typeof binding.value === 'object') {
      setDataset(targetDom, binding.value.value)
      onSuccess = binding.value.onSuccess
      onError = binding.value.onError
    } else {
      setDataset(targetDom, binding.value?.value)
    }
    targetDom.addEventListener('click', async() => {
      try {
        // 使用最新clipboard API
        await navigator.clipboard.writeText(getDataset(targetDom).copyValue)
        onSuccess()
      } catch (err) {
        let $input = document.createElement('textarea')
        $input.style.opacity = '0'
        $input.value = getDataset(targetDom).copyValue
        document.body.appendChild($input)
        $input.select()

        const isSuccess = document.execCommand('copy')
        isSuccess ? onSuccess() : onError()
        document.body.removeChild($input)
        $input = null
      }
    })
  },

  // 更新存储的值，存储在 dom 的 dataset 中
  update: (el, binding) => {
    if(binding.value) {
      let value = typeof binding.value === 'object' ? binding.value.value : binding.value
      setDataset(el, value)
    }
  }
})

// <div v-shortcut="{'27': key1}">copy</div>

Vue.directive('shortcut', {
  bind: (targetDom, binding) => {
    // 往 dom 对象中挂载函数，以便卸载时，消除消息监听，keyCode 编码映射表：https://www.bejson.com/othertools/keycodes/
    targetDom.shortcutFun = function(event) {
      Object.keys(binding.value).forEach(key => {
        event.keyCode.toString() === key && binding.value[key]()
      })
    }
    window.addEventListener('keyup', targetDom.shortcutFun)
  },

  // 指令被卸载，消除消息监听
  unbind: targetDom => {
    window.removeEventListener('keyup', targetDom.shortcutFun)
  }

})

// <div v-click-outside="{listen: () => showSettings, handler: hideSetting}">
//   <div>copy</div>
//   <div>copy2</div>
// </div>
// 点击外部执行事件
Vue.directive('clickOutside', {
  bind: (targetDom, binding) => {
    targetDom.clickOutsideFun = function(event) {
      try {
        const { handler, listen } = binding.value
        listen() && event.target !== targetDom && !targetDom.contains(event.target) && handler()
      } catch (e) {
        // eslint-disable-next-line no-console
        console.error('全局指令，请输入正确配置，v-click-outside="{listen: () => showSettings, handler: hideSetting}"')
      }
    }
    window.addEventListener('click', targetDom.clickOutsideFun, { capture: true })
    window.addEventListener('touchend', targetDom.clickOutsideFun, { capture: true })
  },

  // 指令被卸载，消除消息监听
  unbind: targetDom => {
    window.removeEventListener('click', targetDom.clickOutsideFun, { capture: true })
    window.removeEventListener('touchend', targetDom.clickOutsideFun, { capture: true })
  }
})
// <div v-drag="{dragParentClass: 'parentClassName'}">
//   <div>drag</div>
// </div>
// 拖拽事件 dragParentClass 为父级dom，不设置则拖拽当前元素
Vue.directive('drag', {
  inserted: (el, binding) => {
    let dragDom = el //默认拖拽元素为当前元素
    // 设置了拖动当前元素的父级
    let dragParentClass = (binding.value || {}).dragParentClass
    if(dragParentClass) {
      let targetParent = el.parentNode;
      while(targetParent.className !== dragParentClass) {
        targetParent = targetParent.parentNode
      }
      dragDom = targetParent //甚至拖拽元素
    }
    el.onmousedown = (e) => {
        e.preventDefault(); //拖动时阻住文本被选中
        //算出鼠标相对元素的位置
        let disX = e.clientX - dragDom.offsetLeft;
        let disY = e.clientY - dragDom.offsetTop;
        
        document.onmousemove = (e)=>{
            //用鼠标的位置减去鼠标相对元素的位置，得到元素的位置
            let left = e.clientX - disX;    
            let top = e.clientY - disY;
            top = top < 60 ? 60 : top
            //移动元素
            dragDom.style.left = left + 'px';
            dragDom.style.top = top + 'px';
        };
        document.onmouseup = (e) => {
            document.onmousemove = null;
            document.onmouseup = null;
        };
    };
  }
})

/** 监听 ajax，自动为匹配到的 dom 元素添加点击约束  **/
// eslint-disable-next-line
// <div id="1" v-waiting="['get::waiting::/test/users?pageIndex=2', 'get::/test/users?pageIndex=1']" @click="test"></div>
// <div id="2" v-waiting="'get::loading::http://www.baidu.com/mock/50/test/users?pageIndex=2'" @click="test">copy</div>
// <div id="3" v-waiting="'get::disable::http://www.baidu.com/mock/50/test/users?pageIndex=2'" @click="test">copy</div>
// Vue.directive('waiting', {
//   bind: (targetDom, binding) => {
//     // 注入全局方法
//     (function() {
//       if (window.hadResetAjaxForWaiting) { // 如果已经重置过，则不再进入。解决开发时局部刷新导致重新加载问题
//         return
//       }
//       window.hadResetAjaxForWaiting = true
//       window.waittingAjaxMap = {} // 接口映射 'get::http://www.baidu.com/mock/50/test/users?pageIndex=1': dom

//       let OriginXHR = window.XMLHttpRequest
//       let originOpen = OriginXHR.prototype.open

//       // 重置 XMLHttpRequest
//       window.XMLHttpRequest = function() {
//         let targetDomList = [] // 存储本 ajax 请求，影响到的 dom 元素
//         let realXHR = new OriginXHR() // 重置操作函数，获取请求数据

//         realXHR.open = function(method, url, asyn) {
//           Object.keys(window.waittingAjaxMap).forEach(key => {
//             let [targetMethod, type, targetUrl] = key.split('::')
//             if (!targetUrl) { // 设置默认类型
//               targetUrl = type
//               type = 'v-waiting-waiting'
//             } else { // 指定类型
//               type = `v-waiting-${type}`
//             }
//             if (targetMethod.toLocaleLowerCase() === method.toLocaleLowerCase() && (url.indexOf(targetUrl) > -1 || new RegExp(targetUrl).test(url))) {
//               targetDomList = [...window.waittingAjaxMap[key], ...targetDomList]
//               window.waittingAjaxMap[key].forEach(dom => {
//                 // 如果当前是一个 vue 组件实例对应的 dom，且 data 中存在 vWaiting 属性，则将其设为 true，组件中则自己根据 vWaiting 属性来实现 loading 效果
//                 const vm = dom.__vue__
//                 if (vm && vm._data && typeof vm._data.vWaiting === 'boolean') {
//                   vm.vWaiting = true
//                 } else if (!dom.classList.contains(type)) {
//                   dom.classList.add('v-waiting', type)
//                   if (window.getComputedStyle(dom).position === 'static') { // 如果是 static 定位，则修改为 relative，为伪类的绝对定位做准备
//                     dom.style.position = 'relative'
//                   }
//                 }
//                 dom.waitingAjaxNum = dom.waitingAjaxNum || 0 // 不使用 dataset，是应为 dataset 并不实时，在同一个时间内，上一次存储的值不能被保存
//                 dom.waitingAjaxNum++
//               })
//             }
//           })
//           originOpen.call(realXHR, method, url, asyn)
//         }

//         // 监听加载完成，清除 waiting
//         realXHR.addEventListener('loadend', () => {
//           targetDomList.forEach(dom => {
//             dom.waitingAjaxNum--
//             if (dom.waitingAjaxNum !== 0) return
//             dom.classList.remove(
//               'v-waiting',
//               'v-waiting-loading',
//               'v-waiting-waiting',
//               'v-waiting-disable',
//             )
//             const vm = dom.__vue__
//             if (vm && vm._data && typeof vm._data.vWaiting === 'boolean') {
//               vm.vWaiting = false
//             }
//           })
//         }, false)
//         return realXHR
//       }
//     })();

//     // 注入全局 css
//     (() => {
//       if (!document.getElementById('v-waiting')) {
//         let code = `
//        .v-waiting {
//           pointer-events: none;
//           /*cursor: not-allowed; 与 pointer-events: none 互斥，设置 pointer-events: none 后，设置鼠标样式无效 */
//         }
//         .v-waiting::before {
//           position: absolute;
//           content: '';
//           left: 0;
//           top: 0;
//           width: 100%;
//           height: 100%;
//           opacity: 0.7;
//           z-index: 999;
//           background-color: #ffffff;
//         }
//         .v-waiting-waiting::after {
//           position: absolute;
//           content: '数据加载中';
//           top: 50%;
//           left: 0;
//           width: 100%;
//           max-width: 100vw;
//           color: #666666;
//           font-size: 20px;
//           text-align: center;
//           transform: translateY(-50%);
//           z-index: 999;
//           animation: v-waiting-v-waiting-keyframes 1.8s infinite linear;
//         }
//         @keyframes v-waiting-v-waiting-keyframes {
//           20% {
//             content: '数据加载中.';
//           }
//           40% {
//             content: '数据加载中..';
//           }
//           60% {
//             content: '数据加载中...';
//           }
//           80% {
//             content: '数据加载中...';
//           }
//         }  
//         .v-waiting-loading::after {
//           position: absolute;
//           content: '';
//           left: 50%;
//           top: 50%;
//           width: 30px;
//           height: 30px;
//           z-index: 9999;
//           cursor: not-allowed;
//           animation: v-waiting-v-loading-keyframes 1.1s infinite linear;
//           background-position: center;
//           background-size: 30px 30px;
//           background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAMAAABg3Am1AAAAWlBMVEUAAABmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZJqDNWAAAAHXRSTlMAgOKKPykV1K5JDbIf9OzNvGHGpZ5lNi8Hl3RVbc989bAAAAE8SURBVEjH5ZRZcsMgEEQR2li0WbuXvv81k5ARTllAQZV/Un5fAnWbYdwj9iaKXM9Zgr7EDzxav9cw5LGGB4gq0iBArEFZtTb0lIEoQ3oNoN/MoyQ93wP62lb9rOnil9sqxO9y4YCW9mXfnxo2gVC0sannyxZoq9MN/PdsXPs56WtPm8dTT8lwYy5W6YiPadOdxbM/RL6x/4sqk+SNBupb0jxS0sLITNp5NJhlOJ4ZJSVmgiub/gLEENKTrPh7QvjaqgPQmcyPMLSBXFDYaup+fZwWRhXKNmDsppJ9InLu9JKgzwL/9jLPp2iu8Gf2jm+ml80rGbg7ducPygCi8MQOmfuEznuCfLkXGa40tTkf7E/mVKuzJtLT4nBw7piuS9/abXGUHQuHQaQapmiDTiyJWt8rFu8YWy4q9g6+AGYbJ4l/4YQUAAAAAElFTkSuQmCC);
//         }
//         @keyframes v-waiting-v-loading-keyframes {
//           from {
//             transform: translate(-50%, -50%) rotate(0deg);
//           }
//           to {
//             transform: translate(-50%, -50%) rotate(360deg);
//           }
//         }`
//         let style = document.createElement('style')
//         style.id = 'v-waiting'
//         style.type = 'text/css'
//         style.rel = 'stylesheet'
//         style.appendChild(document.createTextNode(code))
//         let head = document.getElementsByTagName('head')[0]
//         head.appendChild(style)
//       }
//     })()

//     // 添加需要监听的接口，注入对应的 dom
//     const targetUrlList = Array.isArray(binding.value) ? binding.value : [binding.value]
//     targetUrlList.forEach(targetUrl => {
//       window.waittingAjaxMap[targetUrl] = [targetDom, ...(window.waittingAjaxMap[targetUrl] || [])]
//     })
//   },

//   // 参数变化
//   update: (targetDom, binding) => {
//     if (binding.oldValue !== binding.value) {
//       const preTargetUrlList = Array.isArray(binding.oldValue) ? binding.oldValue : [binding.oldValue]
//       preTargetUrlList.forEach(targetUrl => {
//         const index = (window.waittingAjaxMap[targetUrl] || []).indexOf(targetDom)
//         index > -1 && window.waittingAjaxMap[targetUrl].splice(index, 1)
//       })

//       // 添加需要监听的接口，注入对应的 dom
//       const targetUrlList = Array.isArray(binding.value) ? binding.value : [binding.value]
//       targetUrlList.forEach(targetUrl => {
//         window.waittingAjaxMap[targetUrl] = [targetDom, ...(window.waittingAjaxMap[targetUrl] || [])]
//       })
//     }
//   },

//   // 指令被卸载，消除消息监听
//   unbind: (targetDom, binding) => {
//     const targetUrlList = typeof binding.value === 'object' ? binding.value : [binding.value]
//     targetUrlList.forEach(targetUrl => {
//       const index = window.waittingAjaxMap[targetUrl].indexOf(targetDom)
//       index > -1 && window.waittingAjaxMap[targetUrl].splice(index, 1)
//       if (window.waittingAjaxMap[targetUrl].length === 0) {
//         delete window.waittingAjaxMap[targetUrl]
//       }
//     })
//   }
// })
