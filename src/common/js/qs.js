/**
 * 获取URL中的文件后缀
 * @param {string} URL 数据源的URL
 * @returns {string} 解析后的文件后缀
 */
export function getSuffix(URL) {
  try {
    return URL.split('/').pop().split('?')[0].split('.').pop()
  } catch (e) {
    console.warn('URL is invalid')
  }
}

/**
 * 获取URL中的文件名
 * @param {string} URL 数据源的URL
 * @returns {string} 解析后的文件名
 */
export function getFileName(URL) {
  try {
    const fullName = URL.split('/').pop().split('?')[0]
    return fullName.substr(0, fullName.lastIndexOf('.'))
  } catch (e) {
    console.warn('URL is invalid')
    return null
  }
}

/**
 * 解析URL参数，并进行URL参数解码
 * @param {string} [url] 解析对象
 * @param {string} [key] 需要获取的参数名，不传则返回整个解析的对象
 * @returns {string|object} 返回整个解决后的对象，或者特定Key的值
 */
export function getParams(url = location.href, key) {
  const params = {}
  url = decodeURIComponent(url)
  url.replace(/[?|&](\w+)=([^&^?]*)/g, (matchStr, $1, $2) => {
    params[$1] = $2
  })
  return key ? params[key] : params
}

/**
 * URL参数串行化 / 添加参数，并进行URL参数编码
 * @param {string} [baseURL] 原URL
 * @param {object} params URL参数
 * @param {boolean} isEncode 是否解码
 * @returns {string} 解析组合好的字符串
 */
export function serialize(baseURL, params, isEncode) {
  let paramsStr = ''
  if (baseURL) {
    params = Object.assign({}, getParams(baseURL), params)
  }
  for (let key in params) {
    paramsStr += `${key}=${params[key]}&`
  }
  paramsStr = paramsStr.slice(0, -1)
  if (isEncode) {
    paramsStr = encodeURIComponent(paramsStr)
  }

  if (baseURL) {
    return `${baseURL.split('?')[0]}?${paramsStr}`
  } else {
    return paramsStr
  }
}

/**
 * 获取URL中的路径信息
 * @param {string} url 解析的URL
 * @returns {string} 路径字符串
 */
export function getPath(url) {
  url = url || location.href
  const elemList = url.split('/')
  elemList.shift() // 去除http:/
  elemList.shift() // 去除//
  elemList.shift() // 去除域名
  return elemList.join('/')
}
