import BigNumber from 'bignumber.js'

// 获取小数位数
const getDecimalsLength = number => {
  const matched = String(number || 0).match(/\.(\d+)$/)
  return matched ? matched[1].length : 0
}

/**
 * 格式化数字，每隔三位添加逗号
 * @param {number} num 数值
 * @returns {string} 格式化后的数值字符串
 */
 export function parseNum(num) {
    const numberString = (num || 0).toString()
    // 在转换一些以万为单位的金额时，小数点位数会超过3位，小数点中的数字暂不解析
    const decimalPointIndex = numberString.indexOf('.')
    let intString = decimalPointIndex > -1 ? numberString.slice(0, decimalPointIndex) : numberString
    let decimalString = decimalPointIndex > -1 ? numberString.slice(decimalPointIndex) : ''
    let reg1 = new RegExp('(\\d)(?=(?:\\d{3})+($|\\.))','g')
    return intString.replace(reg1, "$1,") + decimalString
}

/**
 * 保留两位小数
 * @param {number|string} number 保留两位小数
 * @param {string} [mode=floor] 舍入模式，floor 是向下取整，round 是四舍五入，ceil 是向上进一位
 */
export const keep2Decimals = (number, mode = 'floor', decimals = 2) => {
  const times = Math.pow(10, decimals)
  const oneHundredTimes = new BigNumber(number).times(times)
  // https://mikemcl.github.io/bignumber.js/#constructor-properties
  const roundModes = {
    ceil: BigNumber.ROUND_CEIL, // 向正无限大方向舍入，始终不会减少计算值
    floor: BigNumber.ROUND_FLOOR, // 向负无限大方向舍入，始终不会增加计算值
    round: BigNumber.ROUND_HALF_UP, // 四舍五入
  }
  const roundMode = roundModes[mode]
  if (!Object.prototype.hasOwnProperty.call(roundModes, mode)) {
    throw new Error(`暂不支持 ${mode} 舍入模式`)
  }
  const integer = oneHundredTimes.integerValue(roundMode)
  const newNumber = new BigNumber(integer).dividedBy(times)
  return newNumber.toNumber()
}

/** 将元为单位的金额转为以万为单位 */
export function yuan2wan(price, options = { digits : 6, parseNumber : false, mode : 'floor'}) {
  let bigNumber = new BigNumber(price)
  let { digits, parseNumber, mode } = options
  if (digits === 4) {
    bigNumber = new BigNumber(bigNumber.integerValue(BigNumber.ROUND_FLOOR))
  } else if (digits === 6 && getDecimalsLength(bigNumber.toString()) > 2) {
    bigNumber = new BigNumber(keep2Decimals(bigNumber,mode))
  }
  const result = bigNumber.dividedBy(1e4).toNumber()
  if (!parseNumber) {
    return result
  }
  return parseNum(result)
}

/** 将万为单位的金额转为以元为单位 */
export function wan2yuan(price, parseNumber = false) {
  const bigNumber = new BigNumber(price)
  const result = bigNumber.times(1e4).toNumber()
  return parseNumber ? parseNum(result) : result
}

/** 将元为单位的金额转为以分为单位 */
export function yuan2fen(price, parseNumber = false) {
  const bigNumber = new BigNumber(price)
  const result = bigNumber.times(1e2).toNumber()
  return parseNumber ? parseNum(result) : result
}

/** 将分为单位的金额转为以元为单位 */
export function fen2yuan(price, parseNumber = false) {
  const bigNumber = new BigNumber(price)
  const result = bigNumber.dividedBy(1e2).toNumber()
  return parseNumber ? parseNum(result) : result
}

/**
 * 计算两位的差额，可设置保留小数点位数
 * @param {number} firstNumber 第一个数字
 * @param {number} lastNumber 第二个数字
 * @param {number} decimals 保留的小数点位数，默认为0
 * @returns {number} 结果
 */
export function getDifference(firstNumber, lastNumber, decimals = 0) {
  return Number(new BigNumber(firstNumber || 0)
        .minus(lastNumber || 0)
        .toFixed(decimals))
}