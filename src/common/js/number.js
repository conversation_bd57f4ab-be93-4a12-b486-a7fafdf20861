/* eslint-disable */
import BigNumber from 'bignumber.js'

// 获取小数位数
export const getDecimalsLength = (number) => {
  const matched = String(number || 0).match(/\.(\d+)$/)
  return matched ? matched[1].length : 0
}

/**
 * 格式化数字，每隔三位添加逗号
 * @param {number} num 数值
 * @returns {string} 格式化后的数值字符串
 */
export function parseNum(num) {
  const numberString = (num || 0).toString()
  // 在转换一些以万为单位的金额时，小数点位数会超过3位，小数点中的数字暂不解析
  const decimalPointIndex = numberString.indexOf('.')
  let intString = decimalPointIndex > -1 ? numberString.slice(0, decimalPointIndex) : numberString
  let decimalString = decimalPointIndex > -1 ? numberString.slice(decimalPointIndex) : ''
  return intString.replace(/(\d)(?=(?:\d{3})+($|\.))/g, '$1,') + decimalString
}

/**
 * 保留两位小数
 * @param {number|string} number 保留两位小数
 * @param {string} [mode=floor] 舍入模式，floor 是向下取整，round 是四舍五入，ceil 是向上进一位
 */
export const keep2Decimals = (number, mode = 'floor', decimals = 2) => {
  const times = Math.pow(10, decimals)
  const oneHundredTimes = new BigNumber(number).times(times)
  // https://mikemcl.github.io/bignumber.js/#constructor-properties
  const roundModes = {
    ceil: BigNumber.ROUND_CEIL, // 向正无限大方向舍入，始终不会减少计算值
    floor: BigNumber.ROUND_FLOOR, // 向负无限大方向舍入，始终不会增加计算值
    round: BigNumber.ROUND_HALF_UP, // 四舍五入
  }
  const roundMode = roundModes[mode]
  if (!Object.prototype.hasOwnProperty.call(roundModes, mode)) {
    throw new Error(`暂不支持 ${mode} 舍入模式`)
  }
  const integer = oneHundredTimes.integerValue(roundMode)
  const newNumber = new BigNumber(integer).dividedBy(times)
  return newNumber.toNumber()
}

/**
 * 保留n位小数  不够失望添加0占位
 * @param {number} num 数值 默认2位
 * @returns {string} 格式化后的数值字符串
 */
export const formatnumber = (value, num = 2) => {
  let a
  let b
  let c
  let i
  a = value.toString()
  b = a.indexOf('.')
  c = a.length
  if (num == 0) {
    if (b != -1) {
      a = a.substring(0, b)
    }
  } else {
    // 如果没有小数点
    if (b == -1) {
      a = `${a}.`
      for (i = 1; i <= num; i++) {
        a = `${a}0`
      }
    } else {
      // 有小数点，超出位数自动截取，否则补0
      a = a.substring(0, b + num + 1)
      for (i = c; i <= b + num; i++) {
        a = `${a}0`
      }
    }
  }
  return a
}

/** 将元为单位的金额转为以万为单位 */
export function yuan2wan(price, digits = 6, parseNumber = false) {
  let bigNumber = new BigNumber(price)
  if (digits === 4) {
    bigNumber = new BigNumber(bigNumber.integerValue(BigNumber.ROUND_FLOOR))
  } else if (digits === 6 && getDecimalsLength(bigNumber.toString()) > 2) {
    bigNumber = new BigNumber(keep2Decimals(bigNumber))
  }
  const result = bigNumber.dividedBy(1e4).toNumber()
  if (!parseNumber) {
    return result
  }
  return parseNum(result)
}

/** 将万为单位的金额转为以元为单位 */
export function wan2yuan(price, parseNumber = false) {
  const bigNumber = new BigNumber(price)
  const result = bigNumber.times(1e4).toNumber()
  return parseNumber ? parseNum(result) : result
}

/** 将元为单位的金额转为以分为单位 */
export function yuan2fen(price, parseNumber = false) {
  const bigNumber = new BigNumber(price)
  const result = bigNumber.times(1e2).toNumber()
  return parseNumber ? parseNum(result) : result
}

// 分转元
export function fen2yuan(price, parseNumber = false) {
  if (!price) {
    return '0'
  }
  const bigNumber = new BigNumber(price)
  const result = bigNumber.div(1e2).toNumber()
  return parseNumber ? parseNum(result) : result
}
