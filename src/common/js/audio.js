// 音频类库
export default class Audio {
  // 静态属性
  static audio = new Audio()

  // 预加载
  static preload(srcList) {
    srcList = Array.isArray(srcList) ? srcList : [srcList]
    srcList.forEach((src) => {
      const $audio = document.createElement('audio')
      $audio.src = src
      $audio.addEventListener('canplaythrough', () => console.log(`${src} loaded`))
    })
  }

  // 自动队列播放
  static play(src) {
    Audio.audio.debouncePlay(src)
  }

  // 实例属性
  audio = null

  // 播放间隔
  timeout = 0

  // 当前是否正在播放
  playing = false

  // 当前是否可以继续播放，当未播放过音频或音频播放 timeout 毫秒后为 true
  canPlay = true

  // 构造器
  constructor(src, timeout = 5000) {
    this.audio = document.createElement('audio')
    this.timeout = timeout
    if (src) {
      this.audio.src = src
    }
    this.audio.addEventListener('ended', () => {
      // 播放完成
      this.playing = false
      // 如果队列中有待播放的音频，5 秒后播放
      setTimeout(() => {
        this.canPlay = true
      }, this.timeout)
    })
  }

  // 播放音乐
  play(src, { loop, volume } = {}) {
    if (src) {
      this.audio.src = src
    }
    if (loop) {
      this.audio.loop = loop
    }
    if (volume) {
      this.audio.volume = volume || 1
    }
    this.playing = true
    this.canPlay = false
    this.audio.play()
  }

  // 节流播放音乐，音频播放完成 timeout 毫秒内不播放其他音频
  debouncePlay(src, options) {
    if (this.canPlay) {
      this.play(src, options)
    }
  }

  // 恢复播放
  resume() {
    this.audio.play()
  }

  // 暂停播放
  pause() {
    this.audio.pause()
  }

  // 终止播放
  stop() {
    this.audio.load()
  }
}
