// 封装 localStorage 逻辑
class Storage {
  /**
   * 生成存储对象的构造函数
   * @param {string} key localStorage 中的 key 值
   * @param {string} type 数据类型，如 Object
   * @param {any} fallback 解析失败时返回的默认值
   */
  constructor(key, type = '', fallback) {
    this.key = key
    this.type = type.replace(/^[a-z]/, (char) => char.toUpperCase()) // 将第一个字母转为大写
    this.fallback = fallback
  }
  /**
   * 获取 localStorage 中的数据
   */
  get() {
    const localStorageStr = window.localStorage.getItem(this.key) // 对象 json
    if (localStorageStr !== null) {
      try {
        const result = JSON.parse(localStorageStr)
        if (!this.type) {
          return result
        } else if (Object.prototype.toString.call(result) === `[object ${this.type}]`) {
          return result
        }
      } catch (e) {
        console.log('字符串解析失败')
      }
    }
    return typeof this.fallback === 'function' ? this.fallback(localStorageStr) : this.fallback
  }
  /**
   * 将数据存储到 localStorage 中
   * @param {any} value 需要保存到 localStorage 的数据
   */
  set(value) {
    window.localStorage.setItem(this.key, JSON.stringify(value))
  }

  /**
   * 删除key对应的数据
   * @param {string} key 保存数据的键
   */
  remove() {
    window.localStorage.removeItem(this.key)
  }

  // 静态暴露 获取数据
  static get(key, type, fallback) {
    const storage = new Storage(key, type, fallback)
    return storage.get()
  }

  // 静态暴露 设置数据
  static set(key, value) {
    const storage = new Storage(key)
    return typeof value === 'function' ? storage.set(value(this.get(key))) : storage.set(value)
  }

  // 静态暴露 删除数据
  static remove(key) {
    const storage = new Storage(key)
    return storage.remove()
  }
}

export default Storage
