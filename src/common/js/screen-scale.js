/* eslint-disable no-magic-numbers */
/** 屏幕自适应缩放相关 */
import { debounce } from '@/common/js/util'
// 白名单
const whites = ['/market']

/**
 * 元素样式
 * 设置元素样式钱会存储默认样式，用于还原样式
 */
class DomStyle {
  constructor(dom) {
    this.dom = dom
    this.initStyle = {}
  }

  // private 保存初始样式
  saveInitStyle(styleKey) {
    const val = this.dom.style[styleKey]
    this.initStyle[styleKey] = val
  }

  // public 设置样式
  setStyle(styles) {
    for (let key in styles) {
      const keys = Object.keys(this.initStyle)

      // 未保存过初始样式的保存初始样式
      if (!keys.includes(key)) {
        this.saveInitStyle(key)
      }
      this.dom.style[key] = styles[key]
    }
  }

  // public 还原样式
  resetStyle() {
    Object.entries(this.initStyle).forEach(([key, value]) => {
      this.dom.style[key] = value
    })
  }
}

const htmlDomStyle = new DomStyle(document.documentElement)

// 特殊处理 个人中心中一些页面的样式，通过zoom来缩放页面，所以页面的用到vh的地方值也跟着缩放了，所以传入scale,来回推之前vh高度
const setFullHeight = scale => {
  if (!document.getElementById('setFullHeightStyleSheet')) {
    const styleSheet = document.createElement('style')
    styleSheet.id = 'setFullHeightStyleSheet'

    // 表格缺省 css样式
    let tableEmptyCss = `.empty-container{height: calc((100vh /${scale} - 287px - 75px - 43px - 10px)) !important}
    @media only screen and (max-width: 1920px) {
      .empty-container{height: calc((100vh /${scale} - 287px - 123px - 43px - 10px)) !important}
    }
    `
    styleSheet.innerHTML = tableEmptyCss
    document.head.appendChild(styleSheet)
  }
}

const resetStyle = () => {
  htmlDomStyle.resetStyle()
  if (document.getElementById('setFullHeightStyleSheet')) {
    document.head.removeChild(document.getElementById('setFullHeightStyleSheet'))
  }
}

// 缩放变化后的回调
const callbacks = []
// 监听缩放
export const onZoom = callback => {
  callbacks.push(callback)
}

// 设置缩放
export const setScale = () => {
  const { innerWidth } = window // 屏幕缩放比, 网页宽度
  const maxWidth = 1480 // 适配的最大宽度
  const minWidth = 1280 // 适配的最小宽度
  let zoomScale = 1 // 页面缩放比例
  // 判断是否移动端，移动端页面不要缩放，因为缩放后滑块滑动的距离要超出真实的滑块区域，用些用户反馈滑块拉不到最右边
  const isMobile = /Mobi|Android|iPhone/i.test(navigator.userAgent)

  if (!isMobile && innerWidth < maxWidth) {
    // 浏览器页面宽度小于1480的时候，缩放比例，最小缩放比例是 1280宽度的时候
    zoomScale = Math.max(innerWidth, minWidth) / maxWidth
  }
  // 只缩小，不放大，放大会导致滑块无法拖到最右边！
  zoomScale = zoomScale > 1 ? 1 : zoomScale
  // 只在 0.85-1 之间缩放，防止跟其他网站相差太大
  // 理论上只需要适配 1280 的话，最小值是 0.8648648648648649，这里主要是为了防止后面改逻辑之后导致出现小于 0.85 的情况
  zoomScale = zoomScale < 0.85 ? 0.85 : zoomScale
  if (zoomScale !== 1) {
    setFullHeight(zoomScale)
    htmlDomStyle.setStyle({
      zoom: zoomScale,
    })
  } else {
    resetStyle()
  }
  callbacks.forEach(cb => (cb(zoomScale)))
  return zoomScale
}

const debounceSetBodyZoom = debounce(setScale, 300)

// 监听页面变化作出动态调整
setScale()
window.addEventListener('resize', () => {
  const path = window.location.pathname
  if (!whites.includes(path)) {
    debounceSetBodyZoom()
  }
})
