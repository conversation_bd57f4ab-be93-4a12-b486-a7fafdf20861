/**
 * isPhone 手机号检测
 * @param {string} text 待检测字符串
 * @returns {boolean} 是否手机号
 */
export function isPhone(text) {
  return /^1[3-9]\d{9}$/.test(text.toString().trim())
}

/**
 * isMoney 金钱检测
 * @param {string} text 待检测字符串
 * @returns {boolean} 是否金钱
 */
export function isMoney(text) {
  return /^(\d+\.)?\d+$/.test(text.toString().trim())
}

/**
 * isPassword 密码检测（仅支持 8-18位数字/字母/符号，至少2种组合)
 * @param {string} text 待检测字符串
 * @returns {boolean} 密码是否正确
 */
export function isPassword(text) {
  // return /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,}$/.test(text.toString().trim())
  let str = text.toString().trim()
  // 前面规则是匹配 8-18位数字/字母/符号，至少2种组合， 后面规则是不能是中文
  return (/^(?![A-Za-z]+$)(?!\d+$)(?![\W_]+$)\S{8,18}$/).test(str) && /^[^\u4e00-\u9fa5]{0,}$/.test(str)
}

/**
   * 校验是否为18位统一信用代码
   *
   * @param  {String}   uscCode   待校验的统一信用代码
   * @return {Boolean}            校验结果，合法为 true，反之则为 false
   */
 export function validateUnifiedSocialCreditCode (uscCode) {
  return uscCode.length === 18
}

 /**
   * 校验身份证号码
   *
   * @param  {String}   userName  待校验的身份证号码字符串
   * @return {Boolean}            校验结果，合法为 true，反之则为 false
   */
  export function validateIDNumber (IDNumber) {
    const city = {11: '北京', 12: '天津', 13: '河北', 14: '山西', 15: '内蒙古', 21: '辽宁', 22: '吉林', 23: '黑龙江 ', 31: '上海', 32: '江苏', 33: '浙江', 34: '安徽', 35: '福建', 36: '江西', 37: '山东', 41: '河南', 42: '湖北 ', 43: '湖南', 44: '广东', 45: '广西', 46: '海南', 50: '重庆', 51: '四川', 52: '贵州', 53: '云南', 54: '西藏 ', 61: '陕西', 62: '甘肃', 63: '青海', 64: '宁夏', 65: '新疆', 71: '台湾', 81: '香港', 82: '澳门', 91: '国外'}
    const birthday = IDNumber.substr(6, 4) + '/' + Number(IDNumber.substr(10, 2)) + '/' + Number(IDNumber.substr(12, 2))
    const d = new Date(birthday);
    const newBirthday = d.getFullYear() + '/' + Number(d.getMonth() + 1) + '/' + Number(d.getDate())
    const currentTime = new Date().getTime()
    const time = d.getTime()
    const arrInt = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    const arrCh = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']

    if (!/^\d{17}(\d|x)$/i.test(IDNumber)) return false
    if (city[IDNumber.substr(0, 2)] === undefined) return false
    if (time >= currentTime || birthday !== newBirthday) return false

    let sum = 0
    for (let i = 0; i < 17; i++) {
      sum = sum + IDNumber.substr(i, 1) * arrInt[i]
    }
    const residue = arrCh[sum % 11];
    if (residue !== IDNumber.substr(17, 1).toUpperCase()) return false

    return true
  }

  /**
   * 校验电子邮件地址是否合法
   *
   * @param  {String}   email   待校验的电子邮件地址
   * @return {Boolean}          校验结果，合法为 true，反之则为 false
   */
   export function validateEmail (email) {
    if (!email) {
      return false
    }

    const isAscii = (str) => str.split('').every((x) => x.charCodeAt(0) <= 127)

    const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
    return isAscii(email) && reg.test(String(email).toLowerCase())
  }