import { $api } from '@/utils/axios'
import { loadScript } from './html'


// 基础的微信JSAPI
const apiList = [
  'checkJsApi',
  'onMenuShareTimeline',
  'onMenuShareAppMessage',
  'onMenuShareQQ',
  'onMenuShareWeibo',
  'onMenuShareQZone',
]

/**
 * 增加要调用的微信API
 * @param {Array|String} newAPIList 新增的API名
 */
export function addWXAPIs(newAPIList) {
  if (typeof newAPIList === 'string') {
    newAPIList = [newAPIList]
  }
  newAPIList.forEach(apiName => {
    if (apiList.indexOf(apiName) !== -1) {
      apiList.push(apiName)
    }
  })
}


// 请求微信JSSDK的脚本文件
function getSDK() {
  if (!window.wx) {
    loadScript('//res.wx.qq.com/open/js/jweixin-1.2.0.js').then(() => window.wx)
  }
  return window.wx
}


// 默认获取微信验证配置的函数
function getWXConfig(url) {
  return $api.get(url)
}

/**
 * 获取已配置好的wx对象
 * @param {string} url 请求微信配置的ULR
 * @returns {Promise<any>} 操作成功的Promise
 */
export function getWX(url = '') {
  return new Promise((resolve, reject) => {
    Promise.all([getSDK(), getWXConfig(url)]).then(res => {
      const wx = res[0]
      const data = res[1]

      wx.config({
        debug: false,
        appId: data.appId,
        timestamp: data.timestamp,
        nonceStr: data.nonceStr,
        signature: data.signature,
        jsApiList: apiList.slice(),
      })
      wx.ready(() => resolve(wx))
    }, reject)
  })
}
