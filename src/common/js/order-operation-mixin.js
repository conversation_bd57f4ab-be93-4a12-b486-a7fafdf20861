import Vue from 'vue'
import store from '@/store/index'

// 弹窗组件实例 map
const componentInstanceMap = new Map()
// 弹窗组件对应的父组件（按钮组件）列表map
const componentParentsMap = new Map()

export default function createMixin(Component) {
  // @vue/component
  // eslint-disable-next-line vue/require-name-property
  const mixinOptions = {
    props: {
      // 订单对象或列表
      order: {
        type: [Object, Array],
        required: true
      },
      // 是否是历史记录
      isHistory: {
        type: Boolean,
        default: false
      },
    },
    // 销毁前
    beforeDestroy() {
      for (let [ComponentObj, parents] of componentParentsMap.entries()) {
        let index = parents.indexOf(this)
        if (index > -1) {
          parents.splice(index, 1)
          if (!parents.length) {
            const instance = componentInstanceMap.get(ComponentObj)
            instance.$el.parentNode.removeChild(instance.$el)
            instance.$destroy()
            componentInstanceMap.delete(ComponentObj)
            componentParentsMap.delete(ComponentObj)
          }
          break
        }
      }
    },
    methods: {
      // 初始化
      init() {
        let instance = componentInstanceMap.get(Component)
        if (!instance) {
          const Constructor = Vue.extend(Component)
          instance = new Constructor({
            router: this.$router,
            store
          })
          instance.$mount()
          document.body.appendChild(instance.$el)
          componentInstanceMap.set(Component, instance)
        }
        // 重新监听 success 事件
        instance.$off('success')
        instance.$on('success', (...args) => {
          this.$emit('success', ...args)
        })
        instance.order = this.order
        instance.isHistory = this.isHistory
        instance.init(this.$options.propsData)
        const parents = componentParentsMap.get(Component) || []
        if (!parents.includes(this)) {
          parents.push(this)
        }
        componentParentsMap.set(Component, parents)
      }
    },
  }
  return mixinOptions
}
