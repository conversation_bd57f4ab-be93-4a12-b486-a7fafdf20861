import { fillZero,compareFillZero } from './util'

/**
 * 时间类型兼容，兼容「时间戳数字」「时间戳字符串」和「时间对象」
 * @param {string|number|object} time 时间内容，可传入时间对象，或时间戳
 * @param {string} type 期望返回的数据类型
 * @returns {string|object} 时间对象或时间戳
 */
export function dealTime(time, type = 'string') {
  if(!time) {
    return type = 'string' ? "" : null
  }
  if (time.toString().indexOf('/') > 0 || time.toString().indexOf('-') > 0) {
    time = new Date(time)
  } else if (typeof time === 'string' || typeof time === 'number') {
    const standTimeStampLength = 13 // 标准时间戳长度
    time = fillZero(time, standTimeStampLength, true) // 转化为标准的13位时间戳
    time = new Date(parseInt(time))
  }
  if (type === 'object') {
    return time
  } else if (type === 'string') {
    return time.getTime()
  }
}

/**
 * 格式化时间
 * @param {string|number|object} date 时间内容，可传入时间对象，或时间戳
 * @param {string} formatStr 格式模板
 * @returns {string} 处理后的字符串
 */
export function formatTime(date, formatStr = 'YYYY-MM-DD hh:mm:ss') {
  if(!date) {
    return ""
  }
  date = dealTime(date, 'object')
  const formatType = {
    Y: date.getFullYear(),
    M: date.getMonth() + 1,
    D: date.getDate(),
    h: date.getHours(),
    m: date.getMinutes(),
    s: date.getSeconds(),
  }
  return formatStr.replace(
    /Y+|M+|D+|h+|m+|s+/g,
    target => (new Array(target.length).join('0') + formatType[target[0]]).substr(-target.length)
  )
}


/**
 * 判断给出的时间是否已经过期
 * @param {string|number|object} timestamp 给出的时间戳或时间对象
 * @returns {boolean} 是否过期
 */
export function isOverdue(timestamp) {
  timestamp = dealTime(timestamp, 'string')
  return timestamp < new Date().getTime()
}

/**
 * 测试函数，测试没有被import的函数是否会被打包
 * 实践证明，该代码并不会被打包到源文件中
 * 只会被打包到.map文件中
 * 而.map文件在正常浏览的情况下是不会被加载的，只会在打开调试工具时才被加载，所以不用担心流量加载请求
 * 但这也侧面反映一个问题，在模块文件中，不能通过字符串去调用某函数，因为不会被打包，不能被调用
 */
export function momoTestFun() {
  console.log('momoTestFun')
}


/**
 * 获取最近几天的时间范围
 * @param {number} length 天数，最近的几天
 * @param {string|number|object} [date] 截止时间，默认为今天
 * @returns {array} 解析好之后的时间戳范围数组
 */
export function getLastSomeDay(length, date = new Date()) {
  date = dealTime(date, 'object')
  const startTime = new Date(date.getFullYear(), date.getMonth(), date.getDate() - length)
  const endTime = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1, 0, 0, -1)
  return [startTime.getTime(), endTime.getTime()]
}

/**
 * 获取某月的时间戳范围
 * @param {string|number|object} [date] 特定格式化的时间
 * @returns {array} 解析好之后的时间戳范围
 */
 export function getMonthRange(date = new Date()) {
  date = dealTime(date, 'object')
  const startTime = new Date(date.getFullYear(), date.getMonth(), 1)
  const endTime = new Date(date.getFullYear(), date.getMonth() + 1, 1, 0, 0, -1)
  return [startTime.getTime(), endTime.getTime()]
}

/**
 * 获取某周的时间戳范围
 * @param {string|number|object} [date] 特定格式化的时间
 * @returns {array} 解析好之后的时间戳范围
 */
export function getWeekRange(date = new Date()) {
  date = dealTime(date, 'object')
  const daysInWeek = 7
  const day = date.getDay()
  // Date.prototype.getDay() 的返回值为 0-6，0 表示周日
  const week = day === 0 ? 6 : day - 1
  const startTime = new Date(date.getFullYear(), date.getMonth(), date.getDate() - week)
  const endTime = new Date(
    date.getFullYear(),
    date.getMonth(),
    (daysInWeek - week) + date.getDate(),
    0,
    0,
    -1,
  )
  return [startTime.getTime(), endTime.getTime()]
}

/**
 * 获取某日的时间戳范围
 * @param {string|number|object} [date] 特定格式化的时间
 * @returns {array} 解析好之后的时间戳范围
 */
export function getDayRange(date = new Date()) {
  date = dealTime(date, 'object')
  const startTime = new Date(date.getFullYear(), date.getMonth(), date.getDate())
  const endTime = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1, 0, 0, -1)
  return [startTime.getTime(), endTime.getTime()]
}

/**
 * 获取时间提示信息
 * @param {string|number|object} timestamp 需要转换的时间戳
 * @returns {string} 解析好之后时间提示
 */
export function getDateTips(timestamp) {
  timestamp = dealTime(timestamp, 'string')
  const date = new Date()
  const now = date.getTime()
  // eslint-disable-next-line
  const fiveMinuteBefore = new Date().setMinutes(date.getMinutes() - 5)
  const oneHourBefore = new Date().setHours(date.getHours() - 1)
  const today = new Date(date.getFullYear(), date.getMonth(), date.getDate()).getTime()
  const yesterday = new Date(date.getFullYear(), date.getMonth(), date.getDate() - 1).getTime()
  const twoDaysBefore = new Date(date.getFullYear(), date.getMonth(), date.getDate() - 2).getTime()
  if (timestamp > fiveMinuteBefore) { // 5分钟内
    return '刚刚'
  } else if (timestamp > oneHourBefore) { // 60分钟内
    return `${Math.floor((now - timestamp) / 1000 / 60)}分钟前`
  } else if (timestamp > today) { // 今天
    return `今天 ${formatTime(timestamp, 'hh时mm分')}`
  } else if (timestamp > yesterday) { // 昨天
    return `昨天 ${formatTime(timestamp, 'hh时mm分')}`
  } else if (timestamp > twoDaysBefore) { // 前天
    return `前天 ${formatTime(timestamp, 'hh时mm分')}`
  } else { // 更久之前
    return formatTime(timestamp, 'MM月DD日 hh时mm分')
  }
}

/**
 * 是否是今天
 * @param {string|number|object} timestamp 给出的时间戳或时间对象
 * @returns {boolean} 是否是今天
 */
export function isToday(timestamp) {
  timestamp = dealTime(timestamp, 'string')
  const date = new Date()
  const timeStart = new Date(date.getFullYear(), date.getMonth(), date.getDate()).getTime()
  const timeEnd = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1).getTime()
  return timeStart <= timestamp && timestamp < timeEnd
}

/**
 * 是否是明天
 * @param {string|number|object} timestamp 给出的时间戳或时间对象
 * @returns {boolean} 是否是明天
 */
export function isTomorrow(timestamp) {
  timestamp = dealTime(timestamp, 'string')
  const date = new Date()
  const timeStart = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1).getTime()
  const timeEnd = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 2).getTime()
  return timeStart <= timestamp && timestamp < timeEnd
}

/**
 * 是否是后天
 * @param {string|number|object} timestamp 给出的时间戳或时间对象
 * @returns {boolean} 是否是后天
 */
export function isTheDayAfterTomorrow(timestamp) {
  timestamp = dealTime(timestamp, 'string')
  const date = new Date()
  const timeStart = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 2).getTime()
  // eslint-disable-next-line
  const timeEnd = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 3).getTime()
  return timeStart <= timestamp && timestamp < timeEnd
}

/**
 * 是否是昨天
 * @param {string|number|object} timestamp 给出的时间戳或时间对象
 * @returns {boolean} 是否是昨天
 */
export function isYesterday(timestamp) {
  timestamp = dealTime(timestamp, 'string')
  const date = new Date()
  const timeStart = new Date(date.getFullYear(), date.getMonth(), date.getDate() - 1).getTime()
  const timeEnd = new Date(date.getFullYear(), date.getMonth(), date.getDate()).getTime()
  return timeStart <= timestamp && timestamp < timeEnd
}

/**
 * 是否是前天
 * @param {string|number|object} timestamp 给出的时间戳或时间对象
 * @returns {boolean} 是否是前天
 */
export function isTheDayBeforeYesterday(timestamp) {
  timestamp = dealTime(timestamp, 'string')
  const date = new Date()
  const timeStart = new Date(date.getFullYear(), date.getMonth(), date.getDate() - 2).getTime()
  const timeEnd = new Date(date.getFullYear(), date.getMonth(), date.getDate() - 1).getTime()
  return timeStart <= timestamp && timestamp < timeEnd
}


/**
 * 获取日期对应的星期数
 * @param {string|number|object} date 给出的时间戳或时间对象
 * @returns {string} 日期
 */
export function getWeek(date) {
  date = dealTime(date, 'object')
  return ['日', '一', '二', '三', '四', '五', '六'][date.getDay()]
}


/**
 * 是否在同一个时间段内
 * @param {string|number|object} time1 时间1
 * @param {string|number|object} time2 时间2
 * @param {string} timeType 时间段类型
 * @returns {boolean} 是否在同一个时间段内
 */
export function isSame(time1, time2, timeType) {
  const formatStr = {
    year: 'YYYY',
    month: 'YYYY:MM',
    day: 'YYYY:MM:DD',
    hour: 'YYYY:MM:DD hh',
    minute: 'YYYY:MM:DD hh:mm',
    second: 'YYYY:MM:DD hh:mm:ss',
  }[timeType]
  return formatTime(time1, formatStr) === formatTime(time2, formatStr)
}

/**
 * 转化时间长度
 * @param {number} time 时间长度
 * @param {string} type 转换类型
 * @returns {number} 转换后的数值
 */
export function getTimeLength(time, type) {
  time = parseInt(time)
  return time / {
    day: 24 * 60 * 60 * 1000,
    hour: 60 * 60 * 1000,
    minute: 60 * 1000,
    second: 1000,
  }[type]
}

/**
 * 获取当前月共有多少天
 * @param {Date} date 日期对象
 */
export function getMonthDays(date = new Date()) {
  const dateString = dealTime(date, 'object').toISOString()
  const clonedDate = new Date(dateString)
  clonedDate.setDate(1)
  clonedDate.setMonth(clonedDate.getMonth() + 1)
  clonedDate.setDate(0)
  return clonedDate.getDate()
}

/**
 * 获取多少个月后的今天
 * @param {Date} date 当前日期
 * @param {number} months 月数，表示多少个月后
 */
export function afterMonths(date, months) {
  const dateString = dealTime(date, 'object').toISOString()
  date = new Date(dateString) // 防止直接修改外部传进来的 Date 对象
  const dayOfMonth = date.getDate()
  date.setDate(1)
  date.setMonth(date.getMonth() + months)
  const newDayOfMonth = Math.min(dayOfMonth, getMonthDays(date))
  date.setDate(newDayOfMonth)
  return date
}

/**
 * 时间距离，相距多少天
 * @param {string|number|object} time1 时间1
 * @param {string|number|object} time2 时间2
 * @param {string} timeType 时间段类型
 * @returns {number} 时间距离
 */
export function getDateSpace(time1, time2, timeType = 'day') {
  let formatStr = {
    day: 'YYYY/MM/DD ',
    hour: 'YYYY/MM/DD hh',
    minute: 'YYYY/MM/DD hh:mm',
    second: 'YYYY/MM/DD hh:mm:ss',
  }[timeType]
  time1 = new Date(formatTime(time1, formatStr))
  time2 = new Date(formatTime(time2, formatStr))
  return getTimeLength(time1.getTime() - time2.getTime(), timeType)
}

/**
 * 毫秒转时间 formatDuration(34325055574); // '397天,6小时,44分钟,15秒
 * @param {string|number} ms 时间(毫秒)
 * @param {string} unit 时间间隔符号 , -
 * @param {string} lang 时间单位语言 zh/en
 * @returns {string} 时间段
 */
export function formatDuration(ms, unit = '',lang="zh") {
  if (ms < 0) ms = -ms
  const langMap = {
    day:{
      zh:"天",
      en:"day",
    },
    hour:{
      zh:"时",
      en:"hour",
    },
    minute:{
      zh:"分",
      en:"minute",
    },
    second:{
      zh:"秒",
      en:"second",
    },
    millisecond:{
      zh:"毫秒",
      en:"millisecond",
    },
  }
  const time = {
    [langMap.day[lang]]: Math.floor(ms / 86400000),
    [langMap.hour[lang]]: Math.floor(ms / 3600000) % 24,
    [langMap.minute[lang]]: Math.floor(ms / 60000) % 60,
    [langMap.second[lang]]: Math.floor(ms / 1000) % 60,
    // [langMap.millisecond[lang]]: Math.floor(ms) % 1000
  }
  return Object.entries(time)
    .filter(val => {
      const isSecond  = val[0]===langMap.second[lang]
      const isMin  = val[0]===langMap.minute[lang]
      // 保留0秒的情况
      if(val[1] !== 0 || isSecond){
        return val
      }
      // 保留0分的情况
      if(val[1] !== 0 || isMin){
        return val
      }
    })
    .map(([key, val]) => `${compareFillZero(val)}${key}${(val !== 1 && lang==='en') ? 's' : ''}`)
    .join(unit)
}

/**
 * 格式化倒计时 大于一天显示“1天”，小于一天的显示'23:00:00'格式
 * @param {string|number} ms 时间(毫秒)
 * @param {string} showFormat 显示格式，number: 12:12:10, chinese: 12时12分12秒
 * @returns {string} 时间段
 */
 export function formatCountDown(ms,showFormat = "number") {
  if (ms < 0) {
    ms = -ms
  }
  const oneDayTime = 8.64e7 // 一天的时间毫秒
  if (ms > oneDayTime) {
    // 向下取整，24小时~48小时内都算1天
    return `${Math.floor(ms / oneDayTime)}天`
  } else {
    let hour = Math.floor(ms / 3600000)
    let minute = Math.floor(ms / 60000) % 60
    let second = Math.floor(ms / 1000) % 60

    hour = hour < 10 ? `0${hour}` : hour
    minute = minute < 10 ? `0${minute}` : minute
    second = second < 10 ? `0${second}` : second
    return showFormat === 'number' ? `${hour}:${minute}:${second}` : `${hour} 时 ${minute} 分 ${second} 秒`
  }
}

/**
 * 判断time是否在时间范围（时分）内
 * @param {String} time 判断的时间戳
 * @param {Array} 时间区间数组
 *        {String} beginTime 开始时间 19:40
 *        {String} endTime 结束时间 23:59 （当天） | 08:00 （次日）
 * @returns {Boolean}
 */
 export function inTimeRange(time,[beginTime, endTime]){
  let strb = beginTime.split(':')
  if (strb.length !== 2) {
    return false
  }
  let stre = endTime.split(':')
  if (stre.length !== 2) {
    return false
  }
  let b = new Date(time)
  let e = new Date(time)
  let n = new Date(time)
  // 开始时间
  b.setHours(strb[0])
  b.setMinutes(strb[1])
  b.setSeconds(0)
  // 结束时间
  e.setHours(stre[0])
  e.setMinutes(stre[1])
  e.setSeconds(0)

  return b.getTime() < e.getTime() ?
      n.getTime() > b.getTime() && n.getTime()  < e.getTime() :  // 开始 < 结束，取区间之内
      n.getTime() > b.getTime() || n.getTime()  < e.getTime()    // 开始 > 结束，取区间之外
}

/**
 * 获取上个月第一天
 * @returns {string} 处理后的字符串
 */
export function getLastMonthBegin() {
  const date = new Date()
  if (date.getMonth() === 0) {
    date.setFullYear(date.getFullYear() - 1)
    date.setMonth(11)
  } else {
    date.setMonth(date.getMonth() - 1)
  }
  date.setDate(1)
  return formatTime(date, 'YYYY-MM-DD')
}

/**
 * 毫秒转时间，只显示小时 formatDuration(34325055574); // 100:34:12格式
 * @param {string|number} ms 时间(毫秒)
 * @param {string} showFormat 显示格式，number: 12:12:10, chinese: 12时12分12秒
 * @returns {string} 时间段
 */
export function formatDurationHour (ms, showFormat = 'number') {
  if (ms < 0) {
    ms = -ms
  }

  let hour = Math.floor(ms / 3600000)
  let minute = Math.floor(ms / 60000) % 60
  let second = Math.floor(ms / 1000) % 60

  hour = hour < 10 ? `0${hour}` : hour
  minute = minute < 10 ? `0${minute}` : minute
  second = second < 10 ? `0${second}` : second
  return showFormat === 'number' ? `${hour}:${minute}:${second}` : `${hour}时${minute}分${second}秒`
}

/**
 * 获取今天前多少天的时间戳
 * @param {number} days 天数
 * @returns  时间戳
 */
export function getTimestampBeforeDays(days) {
  // 获取当前时间
  const currentDate = new Date()
  // 将当前时间减去指定的天数，得到几天前的时间
  currentDate.setDate(currentDate.getDate() - days)
  return currentDate.getTime()
}

