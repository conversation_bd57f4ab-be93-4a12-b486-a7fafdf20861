const { userAgent } = window.navigator


/**
 * 是否在微信内
 * @property isInWeChat
 * @type {Boolean}
 */
export const isInWeChat = /\bMicroMessenger\b/.test(userAgent)

/**
 * 是否在QQ或iPad QQ内
 * @property isInQQ
 * @type {Boolean}
 */
export const isInQQ = /\b(IPad)?QQ\b/.test(userAgent)

/**
 * 是否在QQ浏览器内
 * @property isInQQBrowser
 * @type {Boolean}
 */
export const isInQQBrowser = /\bMQQBrowser\b/.test(userAgent)

/**
 * 是否在微博内
 * @property isInWeibo
 * @type {Boolean}
 */
export const isInWeibo = /\bWeibo\b/.test(userAgent)

/**
 * 是否在非浏览器的APP内（目前只能判断一部分APP）
 * @property isInApp
 * @type {Boolean}
 */
export const isInApp = isInWeChat || isInQQ || isInWeibo


/**
 * 是否在iOS设备下
 * @property isIOS
 * @type {Boolean}
 */
export const isIOS = /\b(?:iPad|iPod|iPhone)\b/.test(userAgent)
  && /\bOS(?:\s([\d_.]+))?\slike\sMac\sOS\sX\b/.test(userAgent)


/**
 * 是否在Android设备下
 * @property isAndroid
 * @type {Boolean}
 */
export const isAndroid = /\b(?:Android|Adr|YunOS)\b/.test(userAgent)


/**
 * 是否移动端
 * @type {Boolean}
 */
export const isMobile = isAndroid || isIOS


/**
 * 获取运行环境, local、test、pre或prod
 * @returns {string} 运行环境标识
 */
function getRuntimeEnv() {
  const envObj = {
    dev: 'learning-d.tanzhouedu.com',
    test: 'learning-t.tanzhouedu.com',
    pre: 'learning-p.tanzhouedu.com',
    prod: 'learning.tanzhouedu.com',
  }
  for (let key in envObj) {
    if (location.href.indexOf(envObj[key]) !== -1) {
      return key
    }
  }
  return 'local'
}


/**
 * 运行环境, local、test、pre或prod
 * @property runtimeEnv
 * @type {string}
 */
export const runtimeEnv = getRuntimeEnv()

// 判断当前浏览类型
function getBrowserType() {
  if (window.ActiveXObject || 'ActiveXObject' in window) { // IE
    const reIE = new RegExp('MSIE (\\d+\\.\\d+)')
    reIE.test(userAgent)
    const fIEVersion = parseFloat(RegExp.$1)
    return `IE${fIEVersion || '低版本'}`
  } else if (userAgent.indexOf('Firefox') !== -1) {
    return 'Firefox(火狐)'
  } else if (userAgent.indexOf('Opera') !== -1) {
    return 'Opera'
  } else if (userAgent.indexOf('Edge') !== -1) {
    return 'Edge'
  } else if (userAgent.indexOf('QQ') !== -1 && userAgent.indexOf('QQBrowser') === 1) {
    return '手机QQ'
  } else if (userAgent.indexOf('QQBrowser') !== -1) {
    return 'QQ浏览器'
  } else if (userAgent.indexOf('Safari') !== -1 && userAgent.indexOf('MetaSr') !== -1) {
    return '搜狗浏览器'
  } else if (userAgent.indexOf('MicroMessenger') !== -1) {
    return '微信浏览器'
  } else if (userAgent.indexOf('LBBROWSER') !== -1) {
    return '猎豹浏览器'
  } else if (userAgent.indexOf('Maxthon') !== -1) {
    return '遨游浏览器'
  } else if (userAgent.indexOf('TheWorld') !== -1) {
    return '世界之窗浏览器'
  } else if (userAgent.indexOf('bWeibo') !== -1) {
    return '微博'
  } else if (userAgent.indexOf('ubrowser') !== -1) {
    return 'UC'
  } else if (userAgent.indexOf('bidubrowser') !== -1) {
    return '百度'
  } else if (userAgent.indexOf('Safari') !== -1 && userAgent.indexOf('Chrome') === -1) {
    return 'Safari'
  } else if (userAgent.indexOf('Chrome') !== -1) {
    return 'Chrome'
  } else {
    return '未知'
  }
}

export const browserType = getBrowserType()
