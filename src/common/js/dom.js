/**
 * 添加class
 * @param {object} dom dom节点
 * @param {string} className class名
*/

export function addClass(dom, className) {
  if (dom.classList) {
    dom.classList.add(className)
  } else {
    dom.className += ` ${className}`
  }
}

/**
 * 添加class
 * @param {object} dom dom节点
 * @param {string} className class名
*/

export function removeClass(dom, className) {
  if (dom.classList) {
    dom.classList.remove(className)
  } else {
    dom.className = dom.className.replace(new RegExp(`(^|\\b)${className.split(' ').join('|')}(\\b|$)`, 'gi'), ' ')
  }
}


/**
 * 判断某个 dom 上层是否存在一个符合 parentSelector 选择器的节点
 * @param {object} dom dom 节点
 * @param {string} parentSelectorOrDom 父节点选择器
 * @returns {boolean} 是否属于某个选择器对应的节点
 */
 export function isContainIn (dom, parentSelectorOrDom) {
  const parent = dom && dom.parentElement
  if (!parent) {
    return false
  } else if (typeof parentSelectorOrDom === 'string' ? parent.matches(parentSelectorOrDom) : parent === parentSelectorOrDom) {
    return true
  } else {
    return isContainIn(parent, parentSelectorOrDom)
  }
}

/**
 * 获取css属性值
 * @param {object} dom dom节点
 * @param {style} style 需要获取的dom对象的属性的值
 * @returns 返回当前相关需要获取的css值
*/

export function getStyle(dom, style) {
  return window.getComputedStyle(dom, null)[style]
}

/**
 * 获取dom元素绝对位置的横坐标
 * @param {object} dom dom节点
 * @returns 返回横坐标
*/

export function getElementLeft(element) {
  let actualLeft = element.offsetLeft
  let current = element.offsetParent

  while (current !== null) {
    actualLeft += current.offsetLeft
    current = current.offsetParent
  }

  return actualLeft
}

/**
 * 获取dom元素绝对位置的纵坐标
 * @param {object} dom dom节点
 * @returns 返回纵坐标
*/

export function getElementTop(element) {
  let actualTop = element.offsetTop
  let current = element.offsetParent

  while (current !== null) {
    actualTop += current.offsetTop
    current = current.offsetParent
  }

  return actualTop
}

// 获取当前距离顶部的滚动值
export function getScrollTop() {
  let scrollTop = 0
  if (document.documentElement && document.documentElement.scrollTop) {
    // eslint-disable-next-line prefer-destructuring
    scrollTop = document.documentElement.scrollTop
  } else if (document.body) {
    // eslint-disable-next-line prefer-destructuring
    scrollTop = document.body.scrollTop
  }
  return scrollTop
}

/**
 * 封装一个回到底部或者顶部的函数
 * @param {object} dom dom节点
 * @param {string} position 距离顶部的距离 0表示顶部
 * @param {object} scrollTopDistance 当前元素滚动的距离
*/
export function smoothScroll(dom, position, scrollTopDistance) {
  // 如果你要滚到顶部，那么position传过来的就是0，下面这个distance肯定就是负值。
  let distance = position - scrollTopDistance
  // 每次滚动的距离要不一样，制造一个缓冲效果
  scrollTopDistance = scrollTopDistance + distance / 5
  // 判断条件
  if (Math.abs(distance) < 1) {
    dom.scrollTo({
      left: 0,
      top: position
    })
  } else {
    dom.scrollTo({
      left: 0,
      top: scrollTopDistance
    })
    window.requestAnimationFrame(() => smoothScroll(dom, position, scrollTopDistance))
  }
}

/**
 * 封装一个回到底部或者顶部的函数
 * @param {object} dom dom节点
 * @param {string} distance 距离底部的距离 0表示底部
*/
export function scrollToTop(dom, distance) {
  const position = dom.offsetHeight - distance
  // 使用requestAnimationFrame，如果没有则使用setTimeOut
  if (!window.requestAnimationFrame) {
    window.requestAnimationFrame = function(callback) {
      return setTimeout(callback, 20)
    }
  }

  // 获取当前元素滚动的距离
  let scrollTopDistance = document.documentElement.scrollTop || document.body.scrollTop
  smoothScroll(dom, position, scrollTopDistance)
}



