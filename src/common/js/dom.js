/**
 * 添加class
 * @param {object} dom dom节点
 * @param {string} className class名
 */
export function addClass(dom, className) {
  if (dom.classList) {
    dom.classList.add(className)
  } else {
    dom.className += ` ${className}`
  }
}

/**
 * 添加class
 * @param {object} dom dom节点
 * @param {string} className class名
 */
export function removeClass(dom, className) {
  if (dom.classList) {
    dom.classList.remove(className)
  } else {
    dom.className = dom.className.replace(new RegExp(`(^|\\b)${className.split(' ').join('|')}(\\b|$)`, 'gi'), ' ')
  }
}

/**
 * 判断某个 dom 上层是否存在一个符合 parentSelector 选择器的节点
 * @param {object} dom dom 节点
 * @param {string} parentSelectorOrDom 父节点选择器
 * @returns {boolean} 是否属于某个选择器对应的节点
 */
export function isContainIn(dom, parentSelectorOrDom) {
  return !!findParent(dom, parentSelectorOrDom)
}

/**
 * 寻找某个 dom 上层符合 parentSelector 选择器的节点
 * @param {object} dom dom 节点
 * @param {string} parentSelector 父节点选择器
 * @returns {object} 找到的父节点，如果没有，则返回 null
 */
export function findParent(dom, parentSelector) {
  const parent = dom && dom.parentElement
  if (!parent) {
    return null
  } else if (typeof parentSelector === 'string' ? parent.matches(parentSelector) : parent === parentSelector) {
    return parent
  } else {
    return findParent(parent, parentSelector)
  }
}
