import { getSuffix, getFileName, serialize } from "@/common/js/qs";
import axios from "axios";
import user from '@/utils/user'

/**
 * 获取随机字符串
 * @param {number} length 字符串长度
 * @param {string} [type] 随机字符串类型，letter纯字母，number纯数字，mix字母与数字混合
 * @param {string} [prefix] 字符串前缀
 * @returns {string} 生成的随机字符串
 */
export function randomString(length, type = "letter", prefix = "") {
  const numbers = "0123456789";
  const letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

  let chars = "";
  let result = prefix;

  if (type === "number" || type === "mix") {
    chars += numbers;
  }

  if (type === "letter" || type === "mix") {
    chars += letters;
  }

  for (let i = 0; i < length; i++) {
    result += chars[Math.floor(Math.random() * chars.length)];
  }
  return result;
}

/**
 * 字符串填充「0」
 * @param {string} origin 原始字符串
 * @param {number} length 目标字符串长度
 * @param {boolean} isFillBack 是否从尾部跳槽
 * @returns {string} 处理后的字符串
 */
export function fillZero(origin, length, isFillBack) {
  length = origin.length > length ? origin.length : length; // 若原长度比目标长度更长
  if (isFillBack) {
    return (origin.toString() + new Array(length).join("0")).substr(0, length);
  } else {
    return (new Array(length).join("0") + origin.toString()).substr(-length);
  }
}

/**
 * 除去多余参数
 * @param {object} obj 需要处理的对象
 * @param {boolean} isFillEmptyStr 是否填充为空字符串
 * @returns {object} 处理完成后的对象
 */
export function deleteEmpty(obj, isFillEmptyStr = false) {
  obj = JSON.parse(JSON.stringify(obj));
  Object.keys(obj).forEach((key) => {
    if (typeof obj[key] === "string") {
      obj[key] = obj[key].trim();
    }
    if (obj[key] === undefined || obj[key] === null || obj[key] === "") {
      if (isFillEmptyStr) {
        obj[key] = "";
      } else {
        delete obj[key];
      }
    }
    if (typeof obj[key] === "object" && obj[key].constructor !== Array) {
      obj[key] = deleteEmpty(obj[key]);
    }
  });
  return obj;
}

/**
 * 获取img标签图片的base64数据
 * @param {object} img img标签
 * @returns {string} base64数据
 */
export function getBase64Image(img) {
  const canvas = document.createElement("canvas");
  canvas.width = img.width;
  canvas.height = img.height;
  const ctx = canvas.getContext("2d");
  ctx.drawImage(img, 0, 0, img.width, img.height);
  return canvas.toDataURL("image/png");
}

/**
 * 将base64数据转化为file对象
 * @param {string} base64Str base64数据
 * @param {string} fileName 文件名
 * @returns {File} 文件对象
 */
export function base64ToFile(base64Str, fileName) {
  const params = base64Str.split(",");
  const mime = params[0].match(/:(.*?)/)[1];
  const fileData = atob(params[1]); // 解码Base64
  let { length } = fileData;
  const uint8Array = new Uint8Array(length);
  while (length) {
    length -= 1;
    uint8Array[length] = fileData.charCodeAt(length);
  }
  return new File([uint8Array], fileName, { type: mime });
}

/**
 * 将blob数据转化为buffer对象
 * @param {object} blob blob对象
 */
export function blobToBuffer(blob) {
  return new Promise((resolve) => {
    let reader = new FileReader();
    reader.onload = (result) => {
      resolve(result);
    };
    reader.readAsArrayBuffer(blob);
  });
}

/**
 * 文件下载
 * @param {string} url 获取文件对应的URL
 * @param {object} data 文件内容
 * @param {string} fileName 文件名
 * @param {string} fileType 文件类型
 */
export function downloadFile(url, data = {}, fileName, fileType) {
  fileName = fileName || getFileName(url);
  fileType = fileType || getSuffix(url);
  data = Object.assign({}, data, { token: localStorage.getItem("token") });

  axios({
    url,
    method: "get",
    responseType: "blob",
    params: data,
    headers: {
      accessToken: user.getToken()
    }
  }).then((res) => {
    const fileBlob = new Blob([res.data]); // 创建一个Blob对象
    const a = document.createElement("a");
    a.download = `${fileName}.${fileType}`;
    a.href = URL.createObjectURL(fileBlob);
    a.style.display = "none";
    document.body.appendChild(a);
    a.click();
    a.remove();
  });
}

/**
 * 根据 blob 或 url 直接下载文件
 * @param {string|Blob} url 获取文件对应的URL
 * @param {string} fileName 文件名
 * @param {string} fileType 文件类型
 */
export function download(url, fileName, fileType) {
  const isBlob = url instanceof Blob;
  if (isBlob) {
    url = URL.createObjectURL(url);
  }
  const a = document.createElement("a");
  a.download = `${fileName}.${fileType}`;
  a.href = url;
  a.style.display = "none";
  document.body.appendChild(a);
  a.click();
  a.parentNode.removeChild(a);
  if (isBlob) {
    URL.revokeObjectURL(url);
  }
}

/**
 * 获取完整的URL绝对，解决相对路径转变为绝对路径的问题
 * @param {string} path 原路径
 * @returns {string} 解析后的路径
 */
export function getURL(path) {
  const $a = document.createElement("a");
  $a.href = path;
  return $a.href;
}

/**
 * 获取阿里OSS云存储的图片裁剪功能后的图片，减少图片体积，节约流量
 * @param {string} url 图片URL
 * @param {number} width 图片宽度 / 短边长度
 * @param {number} [height] 图片高度
 * @param {string} type 图片缩放方式，默认m_fill为自动裁剪，m_mfit，短边优先，长边自动扩充。详情参考https://help.aliyun.com/document_detail/44688.html?spm=a2c4g.11186623.4.1.rGBEsn
 * @returns {string} 处理后的图片链接
 */
export function getFitURL(url, width, height, type = "m_fill") {
  height = height || width;
  return url
    ? serialize(url, {
        "x-oss-process": `image/resize,${type},h_${parseInt(
          height
        )},w_${parseInt(width)}`,
      })
    : "";
}

/**
 * 获取首字母大写的字符串
 * @param {string} text 待替换字符串
 * @returns {string} 转化后的字符串
 */
export function getWord(text) {
  return text[0].toUpperCase() + text.substr(1);
}

/**
 * 将横线命名转化为驼峰命名法
 * @param {string} text 待替换字符串
 * @param {string} char 单词间隔符
 * @returns {string} 转化后的字符串
 */
export function getCamelCase(text, char) {
  // const reg = new RegExp('-[a-z]+?', 'g')
  const reg = new RegExp(`${char}[a-z]+?`, "g");
  return text.replace(reg, (matchStr) => matchStr[1].toUpperCase());
}

/**
 * 将驼峰命名法转化为横线命名
 * @param {string} text 待替换字符串
 * @returns {string} 转化后的字符串
 */
export function getUnderScoreCase(text) {
  return text.replace(/[A-Z]+?/g, (matchStr) => `-${matchStr.toLowerCase()}`);
}

/**
 * 获取符合条件的所有对象
 * @param {array} list 候选数组
 * @param {object} condition 对象特征
 * @returns {array}符合的对象数组
 */
export function getMatchObjList(list, condition) {
  const matchList = [];
  list.forEach((item, index) => {
    for (const key in condition) {
      if (condition[key] !== item[key]) {
        return;
      }
    }
    matchList.push({
      index,
      content: item,
    });
  });
  return matchList;
}

/**
 * 获取符合条件的对象索引
 * @param {array} list 候选数组
 * @param {object} condition 对象特征
 * @returns {number} 符合条件的对象索引
 */
export function getObjIndex(list, condition) {
  const result = getMatchObjList(list, condition);
  return result.length > 0 ? result[0].index : -1;
}

/**
 * 获取符合条件的对象
 * @param {array} list 候选数组
 * @param {object} condition 对象特征
 * @returns {object} 符合条件的对象
 */
export function getObj(list, condition) {
  return (getMatchObjList(list, condition)[0] || {}).content;
}

/**
 * 解析对象字符串
 * @param {object} targetObj 目标对象
 * @param {string} str 字符串表达式
 * @param {string} splitWord 字符串表达式分隔符
 */
export function parseObjStr(targetObj, str, splitWord = ".") {
  return str.split(splitWord).reduce((obj, key) => {
    return obj[key];
  }, targetObj);
}

/**
 * 格式化大小
 * @param {number} value 文件大小(单位为Bytes)
 * @returns {string} 文件大小字符串
 */
export function formatSize(value) {
  if (value) {
    // eslint-disable-next-line no-magic-numbers
    const index = Math.floor(Math.log(value) / Math.log(1024));
    // eslint-disable-next-line no-magic-numbers
    const size = (value / 1024 ** index).toFixed(2);
    return (
      size + ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"][index]
    );
  }
  return "0 Bytes";
}

/**
 * 替换对象的key
 * @param {object} target 目标对象
 * @param {object} keyMap key值转换映射，如将a转化为b，则{a:b}
 * @returns {any} 转化后的对象
 */
export function changeObjKey(target, keyMap) {
  target = JSON.stringify(target);
  for (const key in keyMap) {
    target = target.replace(new RegExp(`"${key}":`, "g"), `"${keyMap[key]}":`);
  }
  return JSON.parse(target);
}

/**
 * 计算页码，从第几个页开始显示
 * @param {number} pageIndex 当前第几页
 * @param {number} pageNum 一共有几页
 * @param {number} pageShowNum 显示多少页码
 * @returns {number} 从第几页开始显示
 */
export function getPageStart(pageIndex, pageNum, pageShowNum) {
  let startPageIndex = 0;
  pageShowNum = pageShowNum > pageNum ? pageNum : pageShowNum; // 页面总数，与现显示页数总数比较
  pageShowNum = pageShowNum > 0 ? pageShowNum : 1; // 0判断
  if (pageIndex < pageNum / 2) {
    // 最左边
    startPageIndex = 0;
  } else if (pageIndex > Math.floor(pageNum - pageShowNum / 2 + 1)) {
    // 最右边
    startPageIndex = pageNum - pageShowNum;
  } else {
    // 中间
    startPageIndex = pageIndex - Math.floor(pageShowNum / 2) - 1;
  }
  startPageIndex = startPageIndex > -1 ? startPageIndex : 0;
  return startPageIndex;
}

/**
 * 防抖
 * @param {function} func 回调函数
 * @param {number} waitTime 毫秒
 * @returns
 */
export function debounce(func, waitTime = 500) {
  let timeout;
  return function () {
    let _this = this,
      args = arguments;
    if (timeout) {
      clearTimeout(timeout);
    }

    timeout = setTimeout(function () {
      func.apply(_this, args);
    }, waitTime);
  };
}

/**
 * 比较数字大小补0
 * *compareFillZero(5) => '05'
 * *compareFillZero(25,1000) => '025'
 * @param {Number|String} num 数字
 * @param {number|String} maxNum 比较的数字
 * @param {String} fillUnit 默认0，可其他字符串*，-等
 * @returns {string} 补0后的字符串
 */
export function compareFillZero(num, maxNum = 100, fillUnit = "0") {
  if (!num && +num !== 0) return "";
  let numString = `${num}`;
  if (+num < +maxNum) {
    for (let i = 1; i < String(maxNum).length - String(num).length; i++) {
      numString = fillUnit + numString;
    }
  }
  return numString;
}

/**
 * 传入数据,返回该数据的数据类型
 * *用例: dataType(null) 'null'
 * *用例: dataType({}) => 'object'
 * @param {any} data 任何的数据
 * @returns {string} 该数据的数据类型
 */
export function dataType(data) {
  if (data instanceof Element) {
    return "element";
  }
  const map = {
    "[object Boolean]": "boolean",
    "[object Number]": "number",
    "[object String]": "string",
    "[object Object]": "object",
    "[object Null]": "null",
    "[object Undefined]": "undefined",
    "[object RegExp]": "regExp",
    "[object Date]": "date",
    "[object Function]": "function",
    "[object Array]": "array",
    "[object Math]": "math",
    "[object Symbol]": "symbol",
    "[object Error]": "error",
  };
  let res = map[Object.prototype.toString.call(data)];
  if (res === "number" && data !== data) {
    res = "NaN";
  }
  return res;
}

/**
 * 判断是否为非空值（null undefined ''）
 * *用例: isNotVoid(null) false
 * *用例: isNotVoid(undefined) => false
 * *用例: isNotVoid(0) => false
 * *用例: isNotVoid(0,false) => true
 * *用例: isNotVoid('') => false
 * @param {any} data 任何的数据
 * @param {Boolean} isIncludeZero 是否需要判断0
 * @returns {Boolean} 该数据是否空值
 */
export function isNotVoid(data, isIncludeZero = true) {
  if (isIncludeZero) {
    return !!data;
  } else {
    return data === 0 || !!data;
  }
}

/**
 * 判断是否为Null（不为0，undefined的情况下）
 * *用例: isNull(null) true
 * *用例: isNull(undefined) => false
 * *用例: isNull(0) => false
 * @param {any} data 任何的数据
 * @returns {Boolean} 该数据是否为Null
 */
export function isNull(data) {
  return !data && typeof data !== "undefined" && data !== 0;
}

/**
 * url参数对象转为拼接好的字符串
 * *用例: paramObjToQueryString({ orderNo: 123, id: 1 }) => ?orderNo=123&id=1
 * @param {Object} paramObj url参数对象
 * @returns {String} queryStr 拼接好的字符串
 */
export function paramObjToQueryString(paramObj) {
  let queryStr = "";
  Object.entries(paramObj).forEach((item, index) => {
    if (queryStr[0] != "?") {
      queryStr = `?${item[0]}=${item[1]}`;
    } else {
      queryStr = `${queryStr}&${item[0]}=${item[1]}`;
    }
  });
  return queryStr;
}

/**
 * 打开新窗口
 * @param {any} val 传入的是url字符串，或者是异步函数(返回 url)
 */
export async function openWindow(val) {
  let url = "";
  if (typeof val === "string") {
    // 传入字符串
    window.open(val, "_blank");
  } else if (
    Object.prototype.toString.call(val) === "[object Function]" ||
    Object.prototype.toString.call(val) === "[object AsyncFunction]"
  ) {
    // 传入函数
    try {
      url = val();
      if (url && typeof url.then === "function") {
        url = await url;
      }
      window.open(url, "_blank");
    } catch (error) {
    }
  }
}

/**
 * 获取oss上的文件路径 - 每日更新一次
 * @param {string} url 文件所在路径
 * @returns 文件完整路径
 */
export function getOSSImgrl(url) {
  const day = new Date().getDate();
  return "https://cdn.sdpjw.cn/static/" + url + "?v1=" + day;
}

/**
 * 返回两个数组相同部分的数据 
 * @param {array} list 候选数组较短的
 * @param {array} listLong 候选数组较长的
 * @returns
 */
 export function duplicatedData(list,listLong) {
	let tempList = []
  list.forEach(item => {
    if (listLong.includes(item)) {
      tempList.push(item)
    }
  })
  return tempList
}

//下载
export function downloadClick(url, name) {
  let aDom = document.createElement('a');
  aDom.style.display = 'none';
  aDom.href = url;
  aDom.setAttribute('download', name);
  document.body.appendChild(aDom);
  aDom.click();
  document.body.removeChild(aDom);
}


// 订单类型label
export function  handleOrderTypeLabel(row, sign = '|') {
  // TODO 订单列表的订单类型标识'光'暂未改为左上角悬浮, 故先隐藏光
  const OrderTypeLabelMap = {
    fastTrade: {
      label: '极速'
    },
    radarType: {
      label: '自动'
    },
  }
  let res = ''
  const reg = new RegExp(` \\${sign} $`, 'g')
  for (const key in OrderTypeLabelMap) {
    const item = OrderTypeLabelMap[key]
    if (row[key]) {
      res = `${res}${item.label} ${sign} `
    }
  }
  res = res.replace(reg, '')
  return res
}

// 订单类型label左上角动态位置
export function handleOrderTypeLabelPosition(fastTrade, radarType, type) {
  // 如果是极速自动的票 并且是自动的票就是往后移动一下
  // if (fastTrade && radarType && type !== 'jisu') {
  //   // eslint-disable-next-line no-magic-numbers
  //   return `-${document.getElementsByClassName('acceptor-column')[0].offsetLeft - 32 || 0}px`
  // } else {
  //   return `-${document.getElementsByClassName('acceptor-column')[0].offsetLeft || 0}px`
  // }
  return `-${document.getElementsByClassName('acceptor-column')[0].offsetLeft || 0}px`
}

/**
 * 节流
 * @param {Function} func 要执行的回调函数
 * @param {Number} wait 延时的时间
 * @param {Boolean} immediate 是否立即执行
 * @return null
 */
let timer; let flag
export function throttle(func, wait = 300, immediate = false) {
  if (immediate) {
    if (!flag) {
      flag = true
      // 如果是立即执行，则在wait毫秒内开始时执行
      typeof func === 'function' && func()
      timer = setTimeout(() => {
        flag = false
      }, wait)
    }
  } else if (!flag) {
    flag = true
    // 如果是非立即执行，则在wait毫秒内的结束处执行
    timer = setTimeout(() => {
      flag = false
      typeof func === 'function' && func()
    }, wait)
  }
}