// 省略号样式
@mixin ellipsis($lineNum: '') {
  overflow: hidden;
  text-overflow: ellipsis;

  @if $lineNum != '' {
    /* stylelint-disable-next-line */
    display: -webkit-box;
    /* stylelint-disable-next-line */
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $lineNum;
  } @else {
    // display: inline-block;
    white-space: nowrap;
  }
}

// flex 布局
@mixin flex($obj) {
  display: flex;

  @if map-get($obj, 'justify') {
    justify-content: map-get($obj, 'justify');
  }

  @if map-get($obj, 'align') {
    align-items: map-get($obj, 'align');
  }
}

// flex 水平居中
@mixin flex-c {
  @include flex(
    (
      justify: center,
    )
  );
}

// flex 垂直居中
@mixin flex-vc {
  @include flex(
    (
      align: center,
    )
  );
}

// flex 水平两端对齐
@mixin flex-sb {
  @include flex(
    (
      justify: space-between,
    )
  );
}

// flex 水平垂直居中
@mixin flex-cc {
  @include flex(
    (
      justify: center,
      align: center,
    )
  );
}

// flex 水平两端对齐，垂直居中
@mixin flex-sbc {
  @include flex(
    (
      justify: space-between,
      align: center,
    )
  );
}
