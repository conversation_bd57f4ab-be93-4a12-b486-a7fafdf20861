// 承让erp样式变量页面

//  主题色
$--color-primary: #0076F6;

/* ==================================== 基础变量-定义在这里 START =============================================== */
// 主题色系的移入颜色
$--color-primary-hover: rgba($color: $--color-primary, $alpha: 10%);

/* 辅助色 */
$color-assist2: #37C187;
$color-assist3: #EE9D25;
$color-warning: #EC3535; //  警示色 用于文字 错误 提示 金额等 全局只有一个红色
$color-warning-sub: #FFE4E6; // 浅色警示色
$color-FFFFFF: #FFFFFF;
$color-black: #000000;

/* 渐变色 */
// $color-linear-primary: linear-gradient(180deg, #417FE9 0%, #5FA3FB 100%);
$color-linear-primary: $--color-primary;

/* 字体色值 */
$color-text-primary: #333333; // 主要文本色值
$color-text-regular: #666666; // 常用文本色值
$color-text-secondary: #999999; // 次要文本色值
$color-text-light: #BFBFBF; // 表单控件占位文本颜色/禁用颜色
$color-EBEBEB: #EBEBEB;

// 重点文字样式
$--color-font-main: $color-warning;

// 常规文字按钮颜色
$font-color: $--color-primary;

// 常规文字按钮移入颜色
$font-color-hover: mix($color-FFFFFF, $--color-primary, 20%);

// 常规文字按钮点击颜色
$font-color-active: mix($color-black, $--color-primary, 20%);

// 左侧菜单背景颜色
$--color-menu-aside: $--color-primary;

// layout-aside的两个渐变颜色
$--color-header-2A60C2: #2A60C2;
$--color-header-5287D6: #5287D6;

/* 字体大小 */
$font-size-small: 12px;
$font-size-base: 14px;
$font-size-medium: 16px;
$font-size-large: 18px;
$font-size-extra-large: 20px;

/* 边框颜色 */
$border-color: #CBCBCB; // 边框基础色值
$border-color-lighter: #EBEDF0; // 列表边框 输入框单位后缀
$color-F0F0F0: #F0F0F0;
$color-D9D9D9: #D9D9D9; // 分割线 边框

/* 背景 */
$background-color-base: #FBFBFB; // 模块背景
$background-color-dark: #F0F4FF;
$background-color-disabled: #F5F5F5; // 禁用背景
$background-color-prepend: #EBEBEB; // 输入框单位背景
$success-icon-bg: #E6F8EE;
$--color-sub: #EBF1FF;
$color-F4F5F6: #F4F5F6;

/* 其他颜色 */
$color-FAFAFA: #FAFAFA;
$color-F8F8F8: #F8F8F8;
$color-F2F2F2: #F2F2F2;
$color-CCCDD0: #CCCDD0;
$color-EAF0FE: #EAF0FE;
$color-E6F3F3: #ECF7FF;
$color-1890FF: #1890FF;
$color-40A9FF: #40A9FF;
$color-008489: #0476EA;  // tag --purple
$color-914669: #914669;
$color-FFEAE1: #FFEAE1;
$color-FFF7E6: #FFF7E6;
$color-FF7645: #FF7645;
$color-AF772D: #AF772D;
$color-9254DE: #9254DE;
$color-E6F7FF: #E6F7FF;
$color-F5F6F8: #F5F6F8;
$color-FFFAFA: #FFFAFA;
$color-FCE6E7: #FCE6E7;
$color-FFF8F9: #FFF8F9;
$color-FF5A5F: #366AF0;
$color-13C2C2: #13C2C2;

/* ====================================== 基础变量-定义在这里 END ============================================ */

/* ==================================== ElementUI变量-定义在这里 START ======================================= */
$--color-warning: $color-assist3;
$--color-danger: $color-warning;
$--color-info: $color-assist3;
$--color-success: #37C187;
$--border-color-base: rgba($color:$border-color, $alpha: 50%); // 输入框，按钮禁用边框等;
$--table-row-hover-background-color: $--color-primary-hover; // 表格hover状态

/* ===================================== ElementUI变量-定义在这里 END ========================================= */

/* ==================================== 模块变量-定义在这里 START ============================================= */

/* 表格 */
// 表头渐变色
$table-header-background: #2C8EF9;
$table-header-color-text: $color-FFFFFF; // 表头字体颜色值

/* button */
$button-background: #0076F6; // 按钮背景色

/* 输入框等表单控件添加box-shadow */
$box-shadow-form: none;

/* tabs头部标签样式 */
$tabs-header-background-base: #E5EEFC;
$tabs-header-background-active: $--color-primary;

/* driver.js 引导插件变量覆盖 */
$popover-z-index: 33005;
$overlay-z-index: 33002;
$stage-z-index: 33003;
$highlighted-element-z-index: 33004;

/* 标签装饰的竖线条宽度 */
$line-bar-width: 2px;

/* 服务大厅按钮商米角标颜色 */
$color-market-mi-tag: $--color-primary;

/* 订单详情的步骤条icon颜色 */
$steps-icon: $--color-primary;

/* 订单详情的步骤条icon颜色 */
$steps-icon: $--color-primary;

/* 左侧菜单颜色 */
$menu-aside-left: $--color-menu-aside;

/* header 颜色 */
// $layout-header-color: linear-gradient(180deg, $--color-header-2A60C2, $--color-header-5287D6 100%);
$layout-header-color: $--color-primary;

/* =================================== 模块变量-定义在这里 END ============================================ */
