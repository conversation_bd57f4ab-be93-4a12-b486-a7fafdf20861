// 框架主题色
$primary-color: #d8131a;

// 辅助色
$assist-base: #008489; // 基础辅助色
$assist-danger: #f5222d; // 风险
$assist-success: #35b576; // 成功
$assist-warning: #ffa940; // 警告

// 文本色
$base-text: #262626; // 基础中性色
$regular-text: #666666;
$darker-text: #8c8c8c; // 较深色字体
$light-text: #bfbfbf; // 浅色文本色

$basic-black: #000000; // 黑色
$basic-white: #ffffff; // 白色
$primary-text-hover: #5c79d1; // 主题字的hover色 $primary-text-hover
$actived-text: #3250a8; // 激活后文本色
$color-999999: #999999; // 浅色辅助色
$color-f9f9fa: #f9f9fa; // 浅色辅助色
$color-ff4000: #ff4000; // 红色辅助色
$color-333333: #333333; // 黑色
$color-F5F6F7: #f5f6f7;
$color-666666: #666666;
$color-EDD1A8: #edd1a8;
$color-1D5DDC: #1d5ddc;
$color-D7D8D9: #d7d8d9;
$color-B8A9AA: #b8a9aa;

// 背景色
$base-background: #f6f6f6; // 基础
$overlay-background: #fff1f0; // 覆盖物背景色(表单校验报错)
$darker-background: #f2f2f2; // 弹窗填充色

// 填充色
$blank-fill: #ffffff; // 空白色
$base-fill: #f3f6ff; // 基调色填充 #FFEFEF
$dark-fill: #d3deff;

// 边框颜色
$base-border: #d9d9d9;
$light-border: #f6f6f6;
$lighter-border: #f0f0f0;
$color-dcdee3: #dcdee3;
$color-f5f5f5: #f5f5f5;
$color-e9e9e9: #e9e9e9;
$color-FFE7E7: #ffe7e7;
$color-F1F6FF: #f1f6ff;
$color-E6E9ED: #e6e9ed;
$color-B3B3B3: #b3b3b3;

// 圆角
$border-radius-small: 2px;

// 主内容区宽度
$main-width: 1260px;
$main-height: calc(100vh - 64px - 326px);

$color-C4C4C4: #c4c4c4;
$color-F5F5F5: #f5f5f5;
$color-EEEEEE: #eeeeee;

$width-small: 1140px;
$width-medium: 1260px;
$width-large: 1440px;

.g-content-warp {
  margin: 0 auto;
  width: $width-small;

  @media (min-width: 1550px) and (max-width: 1620px) {
    width: $width-medium;
  }

  @media (min-width: 1620px) {
    width: $width-large;
  }
}
