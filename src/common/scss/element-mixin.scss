@mixin el-button-variant($background-disabled,$button-background) {
  // border-color: transparent;
  background: $button-background;

  &:hover,
  &:focus {
    background: mix($color-FFFFFF, $--color-primary, 20%);
  }

  &:active {
    background: mix($color-black, $--color-primary, 20%);
    outline: none;
  }

  &.is-active {
    background: mix($color-black, $--color-primary, 20%);
  }

  &.is-disabled {
    &,
    &:hover,
    &:focus,
    &:active {
      border-color: transparent;
      color: $color-FFFFFF;
      background-color: $background-disabled;
    }
  }
}

@mixin el-button-border($border-color,$color-text,$background-disabled) {
  border-color: $--color-primary;
  color: $--color-primary;

  &:hover,
  &:focus {
    border-color: mix($color-FFFFFF, $--color-primary, 20%);
    background-color: mix($color-FFFFFF, $--color-primary, 90%);
  }

  &:active {
    border-color: mix($color-black, $--color-primary, 20%);
    background: mix($color-FFFFFF, $--color-primary, 80%);
    outline: none;
  }

  &.is-disabled {
    &,
    &:hover,
    &:focus,
    &:active {
      border: 1px solid $border-color;
      color: $color-text;
      background: $background-disabled;
    }
  }
}

@mixin button-border-none($paddingVertical) {
  border: none;
  padding-top: $paddingVertical + 1px;
  padding-bottom: $paddingVertical + 1px;
}

@mixin el-table-cell($table-cell-padding-horizontal,$table-cell-height,$color-text) {
  & .cell {
    display: flex;
    align-items: center;
    padding: 0 $table-cell-padding-horizontal;
    height: $table-cell-height;

    @include font(14px, $color-text, 400);
  }
}
