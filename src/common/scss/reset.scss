// 设置默认滚动条样式
/* stylelint-disable-next-line selector-pseudo-element-no-unknown */
::input-placeholder {
  color: $color-C4C4C4;
}

::-webkit-resizer {
  background-position: bottom right;
  background-repeat: no-repeat;
}
::placeholder {
  color: $color-C4C4C4;
}
:placeholder {
  color: $color-C4C4C4;
}
/* stylelint-disable-next-line selector-pseudo-class-no-unknown */
:input-placeholder {
  color: $color-C4C4C4;
}

body {
  color: $base-text;
  /* stylelint-disable-next-line max-line-length */
  font-size: 14px;
  line-height: 1.4;
  font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
  position: relative;
}

// 去除input标签的默认样式
input,
textarea {
  appearance: none;
  outline: none;
  cursor: pointer;
}

// 重置各标签的默认样式
body,
div,
span,
header,
footer,
nav,
section,
aside,
article,
th,
td,
ul,
dl,
dt,
dd,
li,
a,
p,
h1,
h2,
h3,
h4,
h5,
h6,
i,
b,
textarea,
button,
input,
select,
figure,
figcaption {
  margin: 0;
  border: none;
  padding: 0;
  /* stylelint-disable-next-line max-line-length */
  font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  text-decoration: none;
  list-style: none;
  font-style: normal;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  -webkit-font-smoothing: antialiased;

  &:hover,
  &:focus {
    outline: none;
  }
}

// 清除IE下图片的边框
img {
  border-style: none;
  font-size: 0;
}

// 解决chrome浏览器默认黄色背景问题
input:-webkit-autofill,
textarea:-webkit-autofill,
select:-webkit-autofill {
  box-shadow: 0 0 0 1000px $basic-white inset;
}

// 重置点击按钮类型的input
input[type='button'] {
  display: block;
}

input::-webkit-inner-spin-button {
  display: none;
}

a {
  color: #333;
}

.van-toast {
  background: rgba(0, 0, 0, 0.7) !important;
}

[dir='rtl'] {
  .el-button + .el-button {
    margin-left: 0;
    margin-right: 12px;
  }

  .el-checkbox__label {
    padding-right: 8px;
    padding-left: 0;
  }

  .el-form-item__label {
    padding-right: 0;
    padding-left: 12px;
    &::before {
      margin-right: 0;
      margin-left: 4px;
    }
  }
  .el-form-item__error {
    right: 0;
  }
  .el-pagination {
    .btn-prev,
    .btn-next {
      transform: rotate(-180deg);
    }
  }

  .el-pagination__classifier {
    margin-left: 0;
    margin-right: 8px;
  }
  .el-message-box__headerbtn {
    left: 0;
    right: auto;
  }
  .el-message-box__header {
    text-align: right;
  }
  .el-popconfirm__main {
    align-items: flex-start;
  }
  .el-popconfirm__icon {
    margin-left: 4px;
  }

  .el-select__selection {
    text-align: right;
  }

  .el-radio {
    margin-right: 0;
    margin-left: 30px;
  }

  .el-radio__label {
    padding-left: 0;
    padding-right: 8px;
  }

  .el-input-group--prepend > .el-input__wrapper,
  .el-input-group__append {
    border-radius: 4px 0 0 4px;
  }

  .el-input-group--prepend .el-input-group__prepend .el-select .el-select__wrapper {
    border-radius: 0 4px 4px 0;
  }

  .el-input-group__prepend {
    border-left: 0;
    border-radius: 0 4px 4px 0;
    box-shadow:
      -1px 0 0 0 var(--el-input-border-color) inset,
      0 1px 0 0 var(--el-input-border-color) inset,
      0 -1px 0 0 var(--el-input-border-color) inset;
  }

  .mr-1 {
    margin-left: 4px;
    margin-right: 0;
  }

  .mr-2 {
    margin-left: 8px;
    margin-right: 0;
  }

  .mr-4 {
    margin-left: 16px;
    margin-right: 0;
  }

  .ml-3 {
    margin-right: 12px;
    margin-left: 0;
  }
}

.g-dropdown {
  .el-scrollbar,
  .el-scrollbar__wrap {
    overflow: visible;
  }
}
