// 单行文字超出显示省略号
.g-ellipsis {
  @include ellipsis;
}

// 带左边绿色柱子的标题
.g-title {
  position: relative;
  padding-left: 12px;
  font-size: 20px;
  font-weight: bold;
  line-height: 30px;

  @include flex-vc;

  &::before {
    position: absolute;
    top: 50%;
    left: 0;
    width: $line-bar-width;
    height: 24px;
    background: $--color-primary;
    content: "";
    transform: translateY(-50%);
  }
}

// 小一号的标题
.g-title-small {
  position: relative;
  padding-left: 12px;
  font-size: 16px;
  font-weight: bold;
  line-height: 22px;

  @include flex-vc;

  &::before {
    position: absolute;
    top: 50%;
    left: 0;
    width: $line-bar-width;
    height: 16px;
    background: $--color-primary;
    transform: translateY(-50%);
    content: "";
  }
}

// 左边红色必填标注
.g-required {
  position: relative;
  margin-bottom: 4px;
  padding-left: 10px;
  height: 22px;
  color: $color-text-secondary;
  line-height: 22px;

  &::before {
    position: absolute;
    top: 1px;
    left: 0;
    font-size: 14px;
    font-weight: bold;
    color: $color-warning;
    content: "*";
    line-height: 20px;
  }
}

// 椭圆标签样式 一般是两三个字的宽度
.g-tag {
  &--blue {
    @include tag-icon($color-1890FF, "fill", "type", $color-E6F7FF );
  }

  &--green {
    @include tag-icon(#32BB74, "fill", "type", #E6FFEC );
  }

  &--red {
    @include tag-icon($color-warning, "fill", "type", $color-warning-sub );
  }

  &--hred {
    @include tag-icon($color-warning, "fill", "type", $color-warning-sub );
  }

  &--purple {
    @include tag-icon($color-914669, "fill", "type", $color-warning-sub );
  }

  &--orange {
    @include tag-icon($color-AF772D, "fill", "type", #F9ECD9 );
  }

  &--orange-risk {
    @include tag-icon(#FA8C16, "fill", "type", #FFEDD8 );
  }
}

/* 订单标签样式 */
// 蓝色标签：连，光，定，签,识，容，网
.g-tag-blue {
  @include tag-icon;
}

// 红色标签：违
.g-tag-red {
  @include tag-icon($color-warning);
}

// 绿色标签：单，批，同
.g-tag-green {
  @include tag-icon($--color-success);
}

// 橘色标签：豆，米，保
.g-tag-orange {
  @include tag-icon(#FF6B2B);
}

// 黄色标签：商，银，财
.g-tag-yellow {
  @include tag-icon($color-assist3);
}

// 蓝色填充标签：验
.g-tag-fill-blue {
  @include tag-icon($--color-primary,"fill");
}

// 红色填充标签：验
.g-tag-fill-red {
  @include tag-icon($color-warning,"fill");
}

// 红色填充标签：验
.g-tag-fill-green {
  @include tag-icon($--color-success,"fill");
}

// flex 垂直居中
.g-flex-vc {
  @include flex-vc;
}

// 操作弹窗
.order-operation-dialog {
  .el-dialog__footer {
    .el-button {
      font-size: 18px;
    }

    .el-button--default {
      color: $color-text-primary;
    }
  }
}

.g-copy {
  color: $color-text-light;
  cursor: pointer;

  &:hover {
    color: $color-FF5A5F;
  }
}

// 弹窗重点文字样式
.text-primary {
  color: $--color-font-main;
}

// 常规主题色文字
.text-color {
  color: $font-color;
}

.text-link {
  @include example-underline;

  margin-right: 8px;
}

// 加粗字体
.text-bold {
  font-weight: 600;
}

// 鼠标小手样式
.c-p {
  cursor: pointer;
}

.line-block {
  display: inline-block;
}

.va-m {
  vertical-align: middle;
}

.g-link-underline {
  @include example-underline;
}

.g-mb-s {
  margin-bottom: 8px;
}

.g-ml-s {
  margin-left: 8px;
}

// 全局红色提示文本
.g-warning-text {
  margin: 0 4px;
  font-size: 14px;
  color: $color-warning;
}

.rect-font-icon {
  border-radius: 2px;
  width: 20px;
  height: 20px;
  line-height: 20px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

.g-tag-shi {
  @extend .rect-font-icon;

  color: $color-FFFFFF;
  background: $color-assist2;
}

// 极速
.g-tag-jisu {
  @include new-tag-icon(#FF6B2B);
}

// 自动
.g-tag-zidong {
  @include new-tag-icon($--color-success);
}

// 隐 / 前
.g-tag-yin {
  // @include new-tag-icon($color-assist3);
  @extend .rect-font-icon;

  color: $color-FFFFFF;
  background: $color-assist3;
}

// 新票
.g-tag-xinpiao {
  border-radius: 0 0 4px;
  padding: 0 2px;
  height: 16px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  color: $color-FFFFFF;
  background: $--color-success;
  line-height: 16px;
}

.g-xinpiao {
  margin-left: 8px;
  border-radius: 10px 10px 10px 0;
  padding: 1px 5px;
  width: 35px;
  height: 20px;
  font-size: 12px;
  text-align: center;
  color: $color-FFFFFF;
  background: #37C187;
  line-height: 20px;
}

.order-tag {
  position: absolute;
  top: 0;
  left: -240px;
  display: flex;
  min-width: 56px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;

  // 不确定是否冗余，暂时注释
  // .el-button {
  //   font-size: 16px;
  // }
}

// 预览大图最大宽度
.el-image-viewer__img {
  max-width: 1000px !important;
}

// iconfont 全局颜色 问号
.svg-icon.chengjie-wenti {
  font-size: 20px;
  color: $--color-primary;
  cursor: pointer;
}

// 消息弹框上的new标识
.svg-icon.icon-new {
  color: $color-warning;
}

// iconfont 全局颜色 感叹号 ！
.svg-icon.sdicon-info-circle,
.svg-icon.sdicon-exclamation-circle1,
.svg-icon.cw-tixing1 {
  font-size: 16px;
  color: $--color-primary !important;
}

// 修改带有 .handle-column类的操作栏样式
.handle-column .cell {
  justify-content: unset !important;
}

// 一些样式只有边距
.mr-twelve {
  margin-right: 12px;
}

.mr-four {
  margin-right: 4px;
}

// 字体 浅灰色
.text-gray {
  color: $color-text-secondary;
}

// 字体 深灰色
.text-dark-gray {
  color: $color-text-regular;
}

// mac icon 不居中
.icon-m {
  .svg-icon {
    vertical-align: sub;
  }
}

.tag-fan {
  margin-left: 4px;

  @include new-icon(50px, 20px);
}

.tag-tong {
  display: inline-block;
  margin: 0 2px;
  border: 1px solid #37C187;
  border-radius: 4px;
  width: 14px;
  height: 14px;
  font-size: 10px;
  text-align: center;
  color: #37C187;
  line-height: 12px;
}

// 智付渠道 返利tag 大小 统一控制
.channel-tag-fan-w {
  @include new-icon(45px, 20px);
}

// 引导组件层级全局设置
body {
  div#driver-page-overlay,
  div#driver-highlighted-element-stage,
  div#driver-popover-item {
    z-index: 1800 !important;
  }

  .driver-highlighted-element {
    z-index: 1820 !important;
  }
}

// 跨行回款户的确认订单提示框样式设置
.enjambment-confirm-Order-cls {
  width: 650px !important;
}

// 全局主题色样式类名定义
.customize-primary-color {
  color: $--color-primary;
}

// 沉默用户交易订单提醒样式
.silent_user_trade-remind {
  margin-bottom: 10px;

  span {
    font-weight: 600;
    color: $color-warning;
  }
}
