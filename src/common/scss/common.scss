body {
  overflow-x: auto;
  min-width: $main-width;
}

// 内宽 1260px 样式
.w-1260 {
  width: $main-width;
  margin: 0 auto;
}

.scrollbar {
  /* 滚动条整体部分 */
  &::-webkit-scrollbar {
    background: #f2f2f2;
    height: 6px;
    width: 6px;
  }

  &::-webkit-scrollbar-track-piece {
    background-color: #fff;
  }

  &::-webkit-scrollbar-thumb,
  ::-webkit-scrollbar-thumb:vertical {
    background-color: #b4bbc5;
    -webkit-border-radius: 10px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb:hover,
  ::-webkit-scrollbar-thumb:vertical:hover {
    background-color: #909090;
  }
}

.w-e-scroll {
  overflow-y: auto; /* 确保有滚动条 */
}
/* Windows 平台上的滚动条样式 */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .w-e-scroll::-webkit-scrollbar {
    background: #f2f2f2;
    height: 6px;
    width: 6px;
  }

  .w-e-scroll::-webkit-scrollbar-track {
    background: #fff; /* 滚动条轨道背景色 */
  }

  .w-e-scroll::-webkit-scrollbar-thumb {
    background: #b4bbc5; /* 滚动条滑块背景色 */
    border-radius: 6px;
  }

  .w-e-scroll::-webkit-scrollbar-thumb:hover {
    background: #909090; /* 滚动条滑块悬停背景色 */
  }
}

#translate {
  display: none;
}

/** v-html 样式重置会浏览器初始化样式 **/
.custom-html,
.re-reset {
  * {
    all: revert; /* 清除所有默认样式 */
    display: revert; /* 恢复部分必要的 display 样式 */
  }

  img {
    max-width: 100%;
  }
}

.text-ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.text-ellipsis-1 {
  text-overflow: ellipsis;
  overflow: hidden;
  /* stylelint-disable-next-line */
  display: -webkit-box;
  /* stylelint-disable-next-line */
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  word-break: break-all;
}

.text-ellipsis-2 {
  text-overflow: ellipsis;
  overflow: hidden;
  /* stylelint-disable-next-line */
  display: -webkit-box;
  /* stylelint-disable-next-line */
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  word-break: break-all;
}

.text-ellipsis-3 {
  text-overflow: ellipsis;
  overflow: hidden;
  /* stylelint-disable-next-line */
  display: -webkit-box;
  /* stylelint-disable-next-line */
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  word-break: break-all;
}
.el-message-box.g-message-box {
  --el-messagebox-title-color: #1a1a1a;
  --el-messagebox-border-radius: 8px;
  --el-messagebox-padding-primary: 24px;
  --el-messagebox-width: 545px;
  .el-message-box__header {
    .el-message-box__title {
      padding-left: 30px;
    }
  }
  .el-message-box__container {
    .el-message-box__status {
      position: absolute;
      top: 24px;
    }
  }
  .el-message-box__header {
    --el-messagebox-padding-primary: 16px;
  }
  .el-message-box__btns {
    --el-messagebox-padding-primary: 16px;
  }
}
.shop-tag {
  max-width: 80px;
  height: 18px;
  margin-right: 8px;
  border-radius: 4px;
  padding: 2px 4px;
  font-size: 10px;
  line-height: 18px;
  font-weight: 500;
  color: #ffeedc;
  line-height: normal;
  display: flex;
  align-items: center;
  justify-content: center;
}
.shop-tab-jlb {
  color: #ffeedc;
  background: linear-gradient(270deg, #898179 0%, #363636 100%);
}
.shop-tab-sl {
  color: #ffeedc;
  background: #ff4753;
}
.shop-tab-gc {
  color: #ffffff;
  background: #f7b662;
}
.shop-tab-default {
  //border: 1px solid #888b94;
  color: #888b94;
  background: #dfe0e4;
}
