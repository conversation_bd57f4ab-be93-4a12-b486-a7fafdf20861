/* 改变主题色变量 */
$--color-primary: $--color-primary;

// 修改按钮等默认字体粗细为 400
$--font-weight-primary: 400;
$--button-default-font-color: $color-text-primary;

// 默认边框圆角 颜色
$--border-base: 1px solid $--border-color-base;
$--border-radius-base: 2px;
$--border-radius-small: 0;

// font-color
$--color-text-primary: $color-text-primary;
$--color-text-regular: $color-text-regular;
$--color-text-secondary: $color-text-secondary;

// Table
$--table-border-color: $border-color-lighter;
$--table-border: 1px solid $--table-border-color;
$--table-header-background-color: $color-F2F2F2;
$table-cell-padding-horizontal: 16px;
$table-cell-height: 40px;
$table-row-height: 56px;
$table-sort-caret-border: $table-header-color-text; // 排序图标色值

// Pagination
$--pagination-background-color: $color-FFFFFF;

// Dialog
$--dialog-background-color: $color-F2F2F2;
$--dialog-padding-primary: 20px; // 暂留待用
$--dialog-title-font-size: 16px;

// Button-size可选项有medium/small/mini, 不指定size默认高度40px
// 默认按钮设置
$--button-padding-vertical: 12px;
$--button-padding-horizontal: 18px;
$--button-font-size: 14px;
$button-default-height: 40px; // 项目自定义按钮高度，防止按钮中添加其他图标导致高度不统一；
// 中等按钮
$--button-medium-padding-vertical: 10px;
$--button-medium-padding-horizontal: 20px;
$--button-medium-font-size: 14px;

// 禁用按钮
$--button-disabled-border-color: $color-D9D9D9;
$--button-disabled-background-color: $color-F4F5F6;
$--button-disabled-font-color: $color-text-light;

// 大按钮-只有type="text"可使用large(特殊场景),其他不要使用，因为新的文档已经取消了large。
$--button-large-padding-vertical: 12px;
$--button-large-padding-horizontal: 18px;
$--button-large-font-size: $font-size-medium;

// 小按钮
$--button-small-padding-vertical: 8px; // 5px;
$--button-small-padding-horizontal: 15px; // 23px;
$--button-small-font-size: 14px;
$--button-mini-padding-vertical: 7px;
$--button-mini-border-radius: 2px;

// Message
// $--message-success-font-color: $--color-success;
// $--message-danger-font-color: $color-warning;
// $--message-info-font-color: $--color-info;
// $--message-warning-font-color: $color-assist3;

// Message Box
$--messagebox-warning-color1: $color-assist3;

// Tooltip
$--tooltip-font-size: 14px;

// Input
$--input-font-color: $color-text-primary;
$--input-placeholder-color: $color-text-light;
$--input-mini-height: 32px;
$--input-border: 1px solid $--border-color-base;
$--input-disabled-fill: $background-color-disabled;
$--input-disabled-border: $color-D9D9D9;
$--input-disabled-color: $color-text-regular;
$--input-disabled-placeholder-color: $color-text-light;
$autocomplete-suggestion-font-size: $font-size-medium;
$input-inner-padding: 12px;

// Select
$--select-option-hover-background: $--color-primary-hover;
$--select-option-height: 40px;
$--select-option-color: $color-text-primary;
$--select-font-size: $font-size-base;
$--select-option-selected-font-color: $color-text-primary;
$--select-dropdown-padding: 0;

// Checkbox
$--checkbox-input-width: 16px;
$--checkbox-input-height: 16px;
$--checkbox-input-border: 1px solid $--border-color-base;

// 未选中的禁用状态
$--checkbox-disabled-border-color: $color-D9D9D9;
$--checkbox-disabled-input-fill: $background-color-disabled;
$--checkbox-disabled-icon-color: $color-text-light;

// 选中的禁用状态
$--checkbox-disabled-checked-input-fill: $background-color-disabled;
$--checkbox-disabled-checked-input-border-color: $color-D9D9D9;
$--checkbox-disabled-checked-icon-color: $color-text-light;

// Radio
$--radio-input-width: 16px;
$--radio-input-height: 16px;
$--radio-disabled-input-border-color: $--border-color-base;
$--radio-disabled-checked-input-border-color: $--border-color-base;
$--radio-disabled-input-fill: $background-color-disabled;
$--radio-disabled-checked-input-fill: $background-color-disabled;

// radio-button
$--radio-button-checked-background-color: $--color-primary;
$radio-button-small-padding: 8px 15px;
$radio-button-default-background-color: $color-F8F8F8;

// Switch
$--switch-off-color: $color-text-light;

// Tabs
$tabs-item-font-size: $font-size-base;
$tabs-item-color: $color-text-primary;
$tabs-height: 56px;

/* 改变 icon 字体路径变量，必需 */
$--font-path: "~@shendu/element-ui/lib/theme-chalk/fonts";

/* 优化弹窗黑屏问题 */
$--popup-modal-background-color: rgb(38 38 38 / 50%);
$--popup-modal-opacity: initial;

@import "~@shendu/element-ui/packages/theme-chalk/src/index";
@import "./element-mixin.scss";

/* ========================= 重置Input START ========================== */
.el-input {
  .el-input__inner {
    padding: 0 $input-inner-padding;
  }

  &:not(.is-disabled) .el-input__inner {
    box-shadow: $box-shadow-form;
  }

  .el-input__count .el-input__count-inner {
    background-color: transparent;
  }

  &.el-input--suffix .el-input__inner {
    padding-right: 24px;
  }

  &.el-input--prefix .el-input__inner {
    padding-left: 30px;
  }
}

// 输入框不可点击
// .el-input.is-disabled .el-input__inner {
//   border-color: $color-D9D9D9 !important;
//   color: $color-text-light !important;
//   background: $color-F4F5F6 !important;
// }

// input autocomplete 输入建议
.el-autocomplete-suggestion li {
  font-size: $autocomplete-suggestion-font-size;

  &:hover {
    font-size: $autocomplete-suggestion-font-size;
    font-weight: 600;
  }
}

// ie10 placeholder start
.el-range-input:-ms-input-placeholder,
.el-input__inner:-ms-input-placeholder,
.el-textarea__inner:-ms-input-placeholder {
  color: $--input-placeholder-color;
}

// ie10 placeholder end

// 覆盖有后置元素的输入框
.el-input-group--append {
  .el-input__inner {
    text-align: right;
  }

  .el-input-group__append {
    padding: 0 8px;
    font-size: 14px;
    color: $color-text-secondary;
    background-color: $background-color-prepend;
  }
}

/* =========================== Input END ================================ */

/* =========================== DatePicker START =========================== */
.el-range-editor.el-input__inner {
  box-shadow: $box-shadow-form;
}

.el-date-editor .el-range-separator {
  width: 26px;
}

/* =========================== DatePicker END ============================= */

/* ======================== 重置分页样式 START ============================ */
.footer-pagination {
  text-align: center;
}

.el-pagination {
  padding: 22px 0;
  font-weight: normal;
}

.el-pagination.is-background {
  .btn-prev,
  .btn-next,
  .el-pager li {
    margin: 0 4px;
    border: 1px solid $--pagination-background-color;
    min-width: 32px;
    height: 32px;
    font-size: 14px;
    background-color: $--pagination-background-color;
    line-height: 31px;

    &.more {
      border: none !important;
      background-color: transparent !important;
      line-height: 36px;
    }
  }

  .el-pager li:not(.disabled).active {
    border-color: $--color-primary;
    font-weight: 600;
    color: $--color-primary;
    background-color: $--color-primary-hover;
  }

  .btn-prev:disabled,
  .btn-next:disabled {
    color: $color-text-light;
    background: $color-FFFFFF;
  }

  span:not([class*="suffix"]) {
    height: $--input-mini-height;
    line-height: $--input-mini-height;
  }

  .el-pagination__jump {
    margin-left: 0;

    .el-input__inner {
      height: $--input-mini-height;
      font-size: 14px;
    }
  }
}

/* =========================== 分页样式 END ============================== */

/* ======================= 重置checkbox多选框 START ======================= */
.el-checkbox {
  &:not(.is-disabled) .el-checkbox__inner {
    box-shadow: $box-shadow-form;
  }

  .el-checkbox__inner {
    &::after {
      left: 5px;
      height: 8px;
    }
  }

  // 没全选时出现的横杆
  .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
    top: 6px;
  }

  .el-checkbox__label {
    padding-left: 6px;
    color: $color-text-primary;
  }

  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: $--color-primary;
  }
}

// 多选框有边框的边框颜色
.el-checkbox.is-bordered {
  &:hover:not(.is-disabled) {
    border-color: $--color-primary;

    .el-checkbox__label {
      color: $--color-primary;
    }
  }

  .el-checkbox__label {
    @include flex-cc;

    display: inline-flex;
    height: 100%;
  }
}

// 多选框选中背景色
.el-checkbox.is-bordered.is-checked {
  background-color: rgba($color: $--color-primary, $alpha: 10%);
}

// 多选框有边框的不可点击状态
.el-checkbox.is-bordered.is-disabled {
  border-color: $color-D9D9D9 !important;
  color: $color-text-light !important;
  background: $color-F4F5F6 !important;
}

/* ========================== checkbox多选框 END ========================== */

/* ========================== 重置模态框Dialog样式 START =================== */
.el-dialog {
  background: $--dialog-background-color;

  .el-dialog__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid $--dialog-background-color;
    padding: 0 16px;
    height: 46px;
    font-size: 18px;
    font-weight: 600;
    background: $color-FFFFFF;
    line-height: 46px;

    .el-dialog__headerbtn {
      position: static;
    }
  }

  .el-dialog__body {
    padding: $--dialog-padding-primary;
    color: $color-text-primary;
  }

  .el-dialog__footer {
    padding: 0 $--dialog-padding-primary $--dialog-padding-primary;
  }
}

/* ========================== 模态框Dialog样式 END ========================= */

/* ========================== 重置Tooltip样式 START ======================== */

.el-tooltip__popper {
  max-width: 480px;
  line-height: 1.5;
}

/* ============================== Tooltip样式 END ========================== */

/* ============================== Select样式 START ========================== */
// .el-select {
// .el-input__inner {
//   padding-left: $input-inner-padding;
//   font-size: $--select-font-size;
// }

// ::placeholder {
//   font-size: $--select-font-size;
//   font-weight: normal;
//   color: $color-text-light;
// }

// TODO:添加default，避免影响全局，后面全局都需要修改的话，可以删除
// &.default {
//   .el-input__inner {
//     font-size: 14px;
//   }

//   ::placeholder {
//     font-size: 14px;
//   }
// }
// }

/* ============================== Select样式 END ========================== */

/* ============================= Dropdown样式 START ========================= */

// 覆盖select下拉框样式
.el-select-dropdown__item {
  padding: 0 12px;
}

.el-select-dropdown__item.selected {
  font-weight: 600;
}

// TODO:添加default，避免影响全局，后面全局都需要修改的话，可以删除
.default {
  .el-select-dropdown__item {
    padding: 0 12px;
    height: 36px;
    line-height: 36px;
    font-size: 14px;
  }

  .el-select-dropdown__item.selected {
    font-size: 14px;
  }
}

/* ============================== Dropdown样式 END ========================== */

/* =============================== Table样式 START =========================== */
.el-table {
  thead {
    th.el-table__cell {
      padding: 0;
      background: $table-header-background;

      @include el-table-cell($table-cell-padding-horizontal,$table-cell-height,$table-header-color-text);

      &.has-tags-column .cell {
        padding-left: 36px !important;
      }
    }
  }

  tbody {
    .el-table__cell {
      padding: 8px 0;

      @include el-table-cell($table-cell-padding-horizontal,$table-cell-height,$color-text-primary);
    }
  }

  &.el-table--border .el-table__cell:first-child {
    .cell {
      padding-left: $table-cell-padding-horizontal;
    }
  }

  // 固定列时高度超出问题
  .el-table__fixed-right {
    height: calc(100% - 11px) !important;
  }

  .el-table__fixed-right::before {
    height: 0;
  }

  .sort-caret.ascending {
    top: 4px;
    border-bottom-color: $table-sort-caret-border;
  }

  .sort-caret.descending {
    bottom: 3px;
    border-top-color: $table-sort-caret-border;
  }

  .el-table__empty-text {
    line-height: 68px;
    font-size: $font-size-base;
  }

  // 排序样式修改
  .caret-wrapper {
    margin-left: 4px;
    width: 8px;
    height: 30px;
  }

  .sort-caret {
    left: 0;
  }
}

// 斑马线样式
// .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
// background: $--table-row-hover-background-color;
// }

/* ================================= Table样式 END ============================ */

/* =========================== Message/Message-Box样式 START ======================= */

// message重复覆盖 固定位置
.el-message {
  top: 30px !important;
  z-index: 5000 !important;
}

// 重置message-box 样式
.el-message-box__wrapper .el-message-box {
  overflow: auto;
  max-height: 80%;
}

/* =========================== Message/Message-Box样式 END ======================== */

/* ================================= Button 样式 START ============================ */

// 所有按钮不管type 不可点击状态样式都一样
// .el-button.is-disabled,
// .el-button.is-disabled:hover {
//   border-color: $--button-disabled-border-color !important;
//   font-weight: 400 !important;
//   color: $--button-disabled-font-color !important;
//   background: $--button-disabled-background-color !important;
// }

// 主要按钮的字体统一加粗
// .el-button--primary:not(.is-plain):not(.is-border) {
//   font-weight: 600;
// }
.el-button {
  transition: none;
}

.el-button--primary {
  // 渐变按钮清除border
  &:not(.is-border) {
    @include button-border-none($--button-padding-vertical);

    &.el-button--medium {
      @include button-border-none($--button-medium-padding-vertical);
    }

    &.el-button--small {
      @include button-border-none($--button-small-padding-vertical);
    }

    &.el-button--mini {
      @include button-border-none($--button-mini-padding-vertical);
    }
  }

  @include el-button-variant($color-text-light,$button-background);

  &.is-border {
    @include el-button-border($--border-color-base,$color-text-light,$background-color-disabled);
  }
}

.el-button + .el-button {
  margin-left: 8px;
}

.el-button--text {
  padding: 2px 0 !important;

  @include example-underline;
}

.el-button.is-disabled.el-button--text {
  background: unset !important;

  &::after {
    border-color: $color-text-light;
  }
}

// 小按钮-暂时不确定是否使用
// .el-button--small {
//   span {
//     line-height: 20px;
//     display: inline-block;
//   }
// }

.el-button--secondary {
  border-color: $--color-primary;
  background-color: $--color-primary;

  &:hover,
  &:focus {
    border-color: rgba($color: $--color-primary, $alpha: 70%);
    background-color: rgba($color: $--color-primary, $alpha: 70%);
  }
}

/* ================================== Button样式 END ============================= */

/* ================================== Radio/Radio-Button样式 START ======================= */

// 单选框样式
.el-radio {
  &:not(.is-disabled) .el-radio__inner {
    box-shadow: $box-shadow-form;
  }

  .el-radio__inner::after {
    width: 6px;
    height: 6px;
  }

  .el-radio__label,
  .el-radio__input.is-checked + .el-radio__label {
    color: $color-text-primary;
  }
}

// 单选框不可点击状态
.el-radio.is-disabled.is-bordered {
  border: $--border-base !important;
  color: $color-text-light !important;
  background: $color-F4F5F6 !important;
}

.el-radio.is-bordered:not(.is-disabled) {
  &:hover {
    .el-radio__label {
      color: $color-text-primary; // $--color-primary;
    }
  }
}

// 单选框默认线性图标选中样式 el-radio-group标签添加类line
.line .el-radio-button__orig-radio:checked + .el-radio-button__inner {
  border-color: $--color-primary;
  font-weight: 600;
  color: $--color-primary;
  background: $--color-primary-hover;
}

// 单选框移入状态
// .el-radio-button {
//   &:not(.is-active):not(.is-disabled) .el-radio-button__inner:hover {
//     color: $--color-primary;
//   }
// }

.el-radio-button--small {
  .el-radio-button__inner {
    padding: $radio-button-small-padding;
  }
}

// 解决键盘方向键操作造成的阴影
.el-radio-button:focus:not(.is-focus):not(:active):not(.is-disabled) {
  box-shadow: none;
}

// 单选按钮组件
.el-radio-group .el-radio-button .el-radio-button__inner {
  background: $radio-button-default-background-color;
  user-select: none;

  @include flex-cc;
}

.el-radio-button__orig-radio:checked + .el-radio-button__inner {
  font-weight: 500;
  background: $--color-primary;
}

// 给默认的radio-button设置一个高度，防止按钮添加图标等内容导致高度不统一；如果其他尺寸按钮也需要可以另外设置高度
.el-radio-button:not(.el-radio-button--medium,.el-radio-button--small,.el-radio-button--mini) {
  .el-radio-button__inner {
    height: $button-default-height;
  }
}

// 重置radio按钮大尺寸的高度-旧代码不确定是否有效，暂时删除
// .el-radio-group .el-radio-button--large .el-radio-button__inner {
//   height: 40px;

//   @include flex-cc;
// }

/* ================================== Radio/Radio-Button样式 END =========================== */

/* =================================== Form/Form-Item样式 START ============================ */

// 表单元素错误提示
.el-form-item__error {
  padding-top: 6px;
  color: $color-warning;
}

.el-form-item.is-error .el-input__inner,
.el-form-item.is-error .el-input__inner:focus,
.el-form-item.is-error .el-textarea__inner,
.el-form-item.is-error .el-textarea__inner:focus {
  border-color: $color-warning;
  background-color: #FFF3F4;
}

/* ================================== Form/Form-Item样式 START ========================= */

/* ==================================== Tabs样式 START ================================= */
.el-tabs {
  &__active-bar {
    transition: transform .3s cubic-bezier(0, .78, .58, 1);
  }

  &__header {
    margin-bottom: 12px;
  }

  &__nav-wrap {
    background-color: $color-FFFFFF;

    &::after {
      height: 1px;
      background-color: transparent;
    }
  }

  &__item {
    font-size: $tabs-item-font-size;
    color: $tabs-item-color;

    &.is-active {
      font-weight: 600;
    }

    &:focus.is-active.is-focus:not(:active) {
      border-radius: inherit;
      box-shadow: none;
    }
  }

  // 全局页面中二级导航样式统一
  &.g-tabs--nav {
    .el-tabs__nav-wrap {
      padding: 0 16px;
    }

    .el-tabs__item {
      height: $tabs-height;
      line-height: $tabs-height;
    }
  }
}

.el-tabs__content {
  position: inherit;
}

/* ====================================== Tabs样式 END ============================== */

/* ====================================== progress样式 START ============================== */

.el-progress-bar__outer {
  border: 1px solid rgb(156 162 185 / 50%);
  background-color: #F4F4F4;
  box-shadow: 2px 2px 4px 0 rgb(156 162 185 / 32%) inset;
}

.text-inner .el-progress-bar__innerText {
  margin-left: 19px;
  color: $color-text-primary;
}

.text-inner .el-progress {
  text-align: unset;
}

.el-progress-bar .el-progress-bar__inner {
  background-image: linear-gradient(180deg, rgb(255 255 255 / 50%) 0%, rgb(255 255 255 / 0%) 100%);
}

/* ====================================== progress样式 END ============================== */

/* ====================================== el-checkbox el-radio-button 特殊样式 START ============================== */
// 发布票据 和智能助手多选/单选框实心
.special-box {
  // 选中的label 颜色修改
  .el-checkbox.is-bordered.is-checked .el-checkbox__label {
    color: $color-FFFFFF !important;
  }

  // 多选框选中背景色
  .el-checkbox.is-bordered.is-checked {
    border: none;
    vertical-align: bottom;
    color: $color-FFFFFF !important;
    background: $button-background;
  }

  // 选中修改背景颜色
  .el-radio.is-bordered.is-checked {
    border: none;
    vertical-align: bottom;
    background: $button-background;
  }

  .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    border: none;
    box-shadow: none;
  }
}

// tabs 背景需要渐变 其它使用tabs 的地方不变
.special-tabs {
  .el-radio-group .el-radio-button .el-radio-button__inner {
    background: $radio-button-default-background-color;
    box-shadow: none;
    transition: none;
    user-select: none;

    @include flex-cc;
  }

  .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    border: none !important;
    font-weight: 500;
    background: $button-background;// $--color-primary;
  }

  .el-radio-button--medium {
    .el-radio-button__inner {
      height: $button-default-height;
    }
  }
}

// 渠道选中但是警用样式
.channel-special.el-checkbox.is-bordered.is-disabled.is-checked {
  border: 1px solid $--color-primary !important;
  font-weight: normal !important;
  background: $--color-primary-hover !important;
}

// 渠道设置中  未开通会选中  改变他的样式
.channel-special-uncheck.el-checkbox.is-bordered.el-checkbox--medium .el-checkbox__label {
  color: $color-text-secondary !important;
}

/* ======================================  el-checkbox el-radio-button 特殊样式 END ============================== */
