:root:root {
  --el-color-primary: #d8131a;
}

body {
  .el-button {
    --el-button-hover-bg-color: #ffeded;
    --el-button-hover-border-color: #ffeded;
    --el-button-active-bg-color: #d8131a;
    --el-button-active-border-color: #d8131a;
  }
  .el-button--primary {
    --el-button-hover-bg-color: #d8131a;
    --el-button-hover-border-color: #d8131a;
    --el-button-disabled-bg-color: #fcc2c4;
    --el-button-disabled-border-color: #fcc2c4;
  }
  .el-dropdown__popper {
    --el-dropdown-menuItem-hover-fill: #ffeded;
  }

  .el-button--default,
  .el-button--small {
    --el-button-border-color: #d8131a;
    --el-button-text-color: #d8131a;
    &:active {
      background-color: #ffeded;
    }
  }

  .el-input {
    --el-input-text-color: #333;
  }

  .el-skeleton {
    --el-skeleton-color: #f5f5f5;
  }
  .el-popconfirm {
    .el-popconfirm__action {
      .el-button--primary span {
        color: #fff;
      }
    }
  }
}

/* ========================== Pagination END =========================== */

.el-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px 20px 0;
  font-weight: normal;

  .el-input__suffix {
    right: 12px;
    transform: none;
  }

  .el-select .el-input {
    line-height: 32px;
    width: 112px;
    font-size: 14px;

    .el-input__inner {
      height: 32px;
    }
  }

  .el-pagination__goto {
    margin-left: 10px;
  }

  .el-pagination__sizes {
    margin: 0 8px 0 16px;
  }
}

.el-pagination__editor .el-input__wrapper.is-focus,
.el-pagination__sizes .el-select__wrapper.is-focused,
.el-input__wrapper.is-focus,
.el-select__wrapper.is-focused {
  box-shadow: 0 0 0 1px #d9d9d9 inset !important;
}

.el-select-dropdown__item.is-selected {
  color: #d8131a !important;
}

.el-pagination .el-select {
  width: 105px !important;
}

.el-pagination.is-background {
  .btn-prev,
  .btn-next {
    padding: 0 10px;
  }

  .btn-prev,
  .btn-next,
  .el-pager li {
    margin: 0 4px;
    border: 1px solid $base-border;
    min-width: 32px;
    height: 32px;
    font-size: 14px;
    color: $base-text;
    background-color: $basic-white !important;
    line-height: 31px;

    &.more {
      border: none !important;
      background-color: transparent !important;
      line-height: 36px;
    }
  }

  .el-pager li:not(.disabled).active {
    border-color: $primary-color;
    font-weight: 600;
    color: $primary-color !important;
    background-color: $basic-white;
  }

  .btn-prev:disabled,
  .btn-next:disabled {
    color: $color-B3B3B3;
    background: $basic-white;
  }

  .el-input--mini .el-input__inner {
    height: 32px;
    line-height: 32px;
  }

  .el-pagination__editor.el-input .el-input__inner {
    height: 32px;
  }

  .el-pagination__jump {
    margin-left: 0;
  }

  .btn-next.is-active,
  .btn-prev.is-active,
  .el-pager li.is-active {
    background-color: $primary-color !important;
    border-radius: 2px;
  }
}

.el-table {
  th.el-table__cell.is-leaf {
    background: #fafafa;
    padding: 10px 0;
    font-size: 14px;
    font-weight: 500;
    color: #333333;
    &.el-table-fixed-column--right,
    .el-table-fixed-column--left {
      background: #fafafa;
    }
    .cell {
      padding: 0 16px;
    }
  }
  td.el-table__cell,
  th.el-table__cell.is-leaf {
    border-bottom: 1px solid #edeef1 !important;
    border-right: 1px solid #edeef1 !important;
  }
}
.el-table__cell span {
  color: #666666;
}
.el-table__cell {
  .el-button {
    &.edit span {
      color: #257bfb;
    }
    span {
      color: #d8131a;
    }
  }
}

.el-dialog {
  .el-dialog__close {
    font-size: 26.6px !important;
  }
}
.el-message-box__btns {
  .el-button:focus {
    outline: none;
  }
}

/* ========================== Pagination END ============================== */

/* ========================== form表单样式 Start ============================== */
.en-ellipsis {
  .el-form-item__label {
    display: inline-block;
    text-align: right;
  }
}
.el-form .el-form-item__label {
  display: inline-block;
  text-align: right;
  color: $color-999999;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* ========================== form表单样式 End ============================== */

/* ========================== step 样式 Start ============================== */
.el-step {
  &__head,
  &__title {
    font-size: 14px !important;
    &.is-success {
      border-color: $primary-color !important;
      color: $primary-color !important;
    }
    &.is-process,
    &.is-wait {
      border-color: #d9d9d9 !important;
      color: #999 !important;
      font-weight: normal !important;
    }
  }
  &__title {
    line-height: normal !important;
    margin-top: 8px !important;
  }
  &__icon {
    width: 32px !important;
    height: 32px !important;
    &-inner {
      width: 16px !important;
      height: 16px !important;
      font-weight: normal !important;
    }
  }
  &.is-center .el-step__line {
    left: 72% !important;
    right: -25% !important;
    top: 15px !important;
  }
}

/* ========================== step 表单样式 End ============================== */

/* ========================== ElMessage 样式 Start ========================== */
.el-message {
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.2);
  border-radius: 4px;

  .el-message__content {
    font-size: 18px;
  }

  .el-message__icon {
    font-size: 18px;
  }

  &.el-message--error {
    background-color: #fef5f5;
    border: 1px solid #fa3e3e;
    .el-message__icon {
      color: #fa3e3e;
    }
    .el-message__content {
      color: #fa3e3e;
    }
  }
}
/* ========================== ElMessage 样式 End =========================== */
