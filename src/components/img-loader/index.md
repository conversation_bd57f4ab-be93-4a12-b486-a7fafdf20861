# ImgLoader 组件

## 组件功能

1. **添加ossDomain**：默认添加oss域名不用频繁的写ossDomain。
2. **图片懒加载**：lazy属性开启。
3. **picture**：html5默认标签，可以优先加载更高效的格式（如 webp），以节省流量和加快加载速度;可以根据设备像素比、视窗宽度等条件提供不同的图片，优化移动端和桌面端体验。
4. **后期扩展**：后期可对图片压缩等功能扩展。

## 不支持 `Intersection Observer` 的处理

如果浏览器不支持 `Intersection Observer`，直接加载图片，无懒加载

#### 示例代码

#### 通用版
```javascript
<template>
  <img-loader
    src="home/partner.png"
    alt=""
    lazy
    />
</template>
```

#### 完整版
```javascript
<template>
  <img-loader
    src="home/partner.png"
    alt=""
    lazy
    isOss
    img-class="xxx"
    loading-img="xxx"
    error-img="xxx"
    oss-domain="xxxx"
    />
</template>
```

#### 注意
1. **isOss 默认为true**
2.  **ossDomain 默认为 ossUrl** 来自：import { ossUrl } from '@/constants/common'
