<template>
  <picture class="img-loader">
    <source :srcset="webpImageSrc" type="image/webp" />
    <img v-bind="$attrs" :src="imageSrc" :class="imgClass" ref="image" @load="onLoad" @error="onError" />
  </picture>
</template>

<script setup>
import { ossUrl } from '@/constants/common'

// 设置 inheritAttrs 为 false
defineOptions({
  inheritAttrs: false,
})

const props = defineProps({
  src: {
    type: String,
    required: true,
  },
  imgClass: {
    type: String,
    default: '',
  },
  ossDomain: {
    type: String,
    default: ossUrl, // 可选的 OSS 域名前缀
  },
  isOss: {
    type: Boolean,
    default: true,
  },
  lazy: {
    type: Boolean,
    default: true,
  },
  loadingImg: {
    type: String,
    default: '',
  },
  errorImg: {
    type: String,
    default: '',
  },
  disabledWebp: {
    type: Boolean,
    default: false,
  },
})

const formatUrl = (url) => {
  // 处理本地图片
  if (url.startsWith('@/')) {
    return url.replace('@/', '/src/')
  }
  // 处理本地图片
  if (url.startsWith('../')) {
    return `/src/${url.replace(/(\.\.\/)/g, '')}`
  }

  if (!props.isOss) {
    return url
  }

  if (!props.ossDomain) {
    return url
  }

  const wxFileReg = /^(http(s)?:\/\/|wxfile:).*$/
  const base64Reg = /^data:image\/[a-z]+;base64,/
  const videoSnapshotReg = /x-oss-process=video/

  if (wxFileReg.test(url)) {
    return url
  }

  if (base64Reg.test(url)) {
    return url
  }

  if (videoSnapshotReg.test(url)) {
    return url
  }

  const ossDomain = props.ossDomain.endsWith('/') ? props.ossDomain : `${props.ossDomain}/`

  if (url.startsWith('/')) {
    return `${ossDomain}${url.slice(1)}`
  }

  return `${ossDomain}${url}`
}

/**
 * 动态添加 WebP 参数到图片 URL
 * @param {string} url - 原始图片 URL
 * @returns {string} - 处理后的图片 URL
 */
function addWebpToUrl(url) {
  if (!url) return ''

  const webpParam = 'format,webp'
  const ossProcessKey = 'x-oss-process'
  const imagePrefix = 'image/' // 确保加上 image/ 前缀
  const urlObj = new URL(url)

  // 获取查询参数
  const searchParams = urlObj.searchParams

  // 检查是否已经包含 x-oss-process 参数
  if (searchParams.has(ossProcessKey)) {
    // 获取当前的 x-oss-process 参数值
    let currentProcess = searchParams.get(ossProcessKey)

    // 如果没有包含 format,webp，追加该参数
    if (!currentProcess.includes(webpParam)) {
      currentProcess = `${currentProcess}/${webpParam}`
    }

    // 更新参数
    searchParams.set(ossProcessKey, currentProcess)
  } else {
    // 如果没有 x-oss-process 参数，直接新增，并加上 image/ 前缀
    searchParams.set(ossProcessKey, `${imagePrefix}${webpParam}`)
  }

  // 返回修改后的 URL
  return urlObj.toString()
}

const image = ref(null)
const imageSrc = ref('')
const webpImageSrc = computed(() => {
  if (props.disabledWebp) {
    return imageSrc.value
  }
  return addWebpToUrl(imageSrc.value)
})
const isError = ref(false)
let lazyComplete = false
let loadingComplete = false

const emitEvent = defineEmits(['load', 'error'])

// 是loading图片在加载
const isLoadingImg = () => props.loadingImg && imageSrc.value === formatUrl(props.loadingImg) && formatUrl(props.src) !== imageSrc.value && !loadingComplete

const loadImg = (value) => {
  isError.value = false
  loadingComplete = false
  imageSrc.value = formatUrl(props.loadingImg || value || props.src)
}

const onLoad = (event) => {
  if (isLoadingImg()) {
    isError.value = false
    loadingComplete = true
    imageSrc.value = formatUrl(props.src)
    return
  }

  emitEvent('load', event)
}

const onError = (event) => {
  if (props.lazy && !lazyComplete && !('loading' in HTMLImageElement.prototype)) return

  if (isLoadingImg()) {
    isError.value = false
    loadingComplete = true
    imageSrc.value = formatUrl(props.src)
    return
  }

  if (isError.value) return

  isError.value = true

  if (props.errorImg) {
    imageSrc.value = formatUrl(props.errorImg)
  }

  emitEvent('error', event)
}

const onIntersect = (entries) => {
  const entry = entries[0]
  if (entry.isIntersecting) {
    lazyComplete = true
    loadImg()
    observer.unobserve(entry.target)
  }
}

let observer
onMounted(() => {
  if ('loading' in HTMLImageElement.prototype && props.lazy) {
    image.value.loading = 'lazy'
    loadImg()
  } else if ('IntersectionObserver' in window && image.value && props.lazy) {
    observer = new IntersectionObserver(onIntersect, {
      rootMargin: '100px',
      threshold: 0.1,
    })
    observer.observe(image.value)
  } else {
    loadImg()
  }
})

watch(
  () => props.src,
  (value) => {
    loadImg(value)
  },
)

onUnmounted(() => {
  observer?.disconnect()
})
</script>
