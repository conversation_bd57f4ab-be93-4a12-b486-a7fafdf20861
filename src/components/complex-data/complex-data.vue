<style lang="scss" scoped>
.goods-card {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  width: 192px;
  border: 1px solid #eeeeee;
  border-radius: 8px;
  cursor: pointer;

  img {
    width: 100%;
    height: 160px;
    object-fit: cover;
  }
  .goods-desc {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 8px;
    border-top: 1px solid #eeeeee;
    color: #333;
    font-weight: 600;
    font-size: 14px;
  }
}
.props-data-item + .props-data-item {
  margin-top: 16px;
  border-top: 1px dashed #cccccc;
  padding-top: 12px;
  flex: 100%;
}
span {
  display: inline-block;
}
.cur-p {
  cursor: pointer;
}
::v-deep {
  .el-image-viewer__close {
    background-color: #f5f1f1;
    color: #000;
  }
}
</style>

<template>
  <div class="props-data-item">
    <!-- 标题 -->
    <h3 :class="props.data.link && 'cur-p underline text-#d8131a'" class="mt-3" v-if="props.data.title" @click="openLink(props.data)">
      {{ props.data.title }}
    </h3>
    <!-- 图片 -->
    <span class="mt-2" v-if="props.data.desc">{{ props.data.desc }}</span>
    <div v-if="props.data.imgs?.length > 0" class="flex flex-wrap gap-2 mt-2">
      <el-image
        class="h-150px"
        :src="`${props.data.imgs[0]}?x-oss-process=image/resize,h_300`"
        :zoom-rate="1.2"
        :preview-src-list="[props.data.imgs]"
        fit="cover"
      ></el-image>
    </div>
    <!-- 商品名称  -->
    <div v-if="props.data.shopName" class="mt-2">
      <!-- <span class="font-600">商品名称：</span> -->
      <icon type="icon-shangpinmingcheng"></icon>
      <span>{{ props.data.shopName }}</span>
    </div>
    <!-- 价格  -->
    <div v-if="props.data.price" class="mt-2">
      <!-- <span class="font-600">价格：</span> -->
      <icon type="icon-jiage"></icon>
      <span>{{ props.data.price }}</span>
    </div>
    <!-- 标签 -->
    <div v-if="props.data.label" class="mt-2">
      <!-- <span class="font-600">标签：</span> -->
      <icon type="icon-biaoqian"></icon>
      <span>{{ props.data.label }}</span>
    </div>
    <!-- 位置 -->
    <div v-if="props.data.loc" class="mt-2">
      <!-- <span class="font-600">位置：</span> -->
      <a
        class="underline text-#d8131a"
        :href="`https://map.baidu.com/search/${props.data.loc}/@13166924.375,4156211.47,19z?querytype=s&da_src=shareurl&wd=${props.data.loc}&c=234&src=0&pn=0&sug=0&l=19&from=webmap`"
        target="_blank"
      >
        <icon type="icon-map"></icon>
        {{ props.data.loc }}
      </a>
    </div>
    <!-- 电话 -->
    <div v-if="props.data.mobile" class="mt-2 flex">
      <!-- <span class="font-600">联系方式：</span> -->
      <icon type="icon-lianxifangshi"></icon>
      <span class="ml-1">{{ props.data.mobile }}</span>
    </div>

    <!-- 营业时间 -->
    <div v-if="props.data.businessTime" class="mt-2 flex">
      <!-- <span class="font-600">营业时间：</span> -->
      <icon type="icon-yingyeshijian"></icon>
      <span class="ml-1">{{ props.data.businessTime }}</span>
    </div>
    <!-- 旅游线路说明 -->
    <div v-if="props.data.routeDesc" class="mt-2">
      <!-- <span class="font-600">线路：</span> -->
      <icon type="icon-xianlu"></icon>
      <span>{{ props.data.routeDesc }}</span>
    </div>
    <!-- 景点说明 -->
    <div v-if="props.data.scenicSpotDesc" class="mt-2">
      <!-- <span class="font-600">景点介绍：</span> -->
      <icon type="icon-jingdianjieshao"></icon>
      <span>{{ props.data.scenicSpotDesc }}</span>
    </div>
    <!-- 美食说明 -->
    <div v-if="props.data.foodDesc" class="mt-2">
      <!-- <span class="font-600">美食介绍：</span> -->
      <icon type="icon-meishijieshao"></icon>
      <span>{{ props.data.foodDesc }}</span>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})

const openLink = (data) => {
  const urlObj = data?.link && new URL(data?.link)
  if (data?.link?.indexOf('http') === 0) {
    const origin = import.meta.env.VUE_APP_WEB_URL // 当前环境域名地址
    const pathName = '/mall/goods-list' // 商品列表路由
    const href = origin + pathName + urlObj?.search // 组装成PC端的商品列表链接，避免data.link是移动端的历史记录链接
    window.open(href)
  }
}
</script>
