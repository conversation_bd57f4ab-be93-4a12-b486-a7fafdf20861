import { getKeyToValueMap, getMapToArray } from './utils'

// 商品推荐类型
export const PRODUCT_SUGGEST_TYPE = Object.freeze({
  UN_SUGGEST: {
    id: 0,
    name: '未推荐',
  },
  HOT: {
    id: 1,
    name: '热门爆款',
  },
  GOODS: {
    id: 2,
    name: '本地好货',
  },
  SUGGEST: {
    id: 3,
    name: '为您推荐',
  },
  NATIONAL_UAE: {
    id: 10,
    name: '阿联酋馆商品推荐',
  },
  NATIONAL_SA: {
    id: 20,
    name: '沙特阿拉伯馆商品推荐',
  },
  THAILAND_NATIONAL_PAVILION: {
    id: 30,
    name: '泰国国家馆',
  },
  INDONESIA_ARABIA_NATIONAL_PAVILION: {
    id: 40,
    name: '印度尼西亚国家馆',
  },
})

// 商品推荐类型 id 映射 名称
export const PRODUCT_SUGGEST_TYPE_MAP = getKeyToValueMap(PRODUCT_SUGGEST_TYPE)
export const PRODUCT_SUGGEST_TYPE_ARRAY = getMapToArray(PRODUCT_SUGGEST_TYPE)

// 销售币种
export const PRICE_TYPE = Object.freeze({
  CNY: {
    id: 1,
    name: '￥',
    label: '人民币',
  },
  USD: {
    id: 2,
    name: '$',
    label: '美元',
  },
  AED: {
    id: 3,
    name: 'د.إ',
    label: '阿联酋迪拉姆',
  },
  SAR: {
    id: 4,
    name: 'ر.س',
    label: '沙特里亚尔',
  },
})

// 商品推荐类型 id 映射 名称
export const PRICE_TYPE_MAP = getKeyToValueMap(PRICE_TYPE)
export const PRICE_TYPE_ARRAY = getMapToArray(PRICE_TYPE)

// 商机中心-发布商机-是否定制
export const Customized = Object.freeze({
  YES: {
    id: 1,
    name: '是',
    label: '是',
  },
  NO: {
    id: 0,
    name: '否',
    label: '否',
  },
})
// 商机中心-发布商机-是否定制
export const CustomizedMap = getKeyToValueMap(Customized)
// 商机中心-发布商机-是否定制
export const CustomizedArray = getMapToArray(Customized)

// 商机中心-发布商机-交货周期
export const DeliveryType = Object.freeze({
  Days7: {
    id: 0,
    name: '7天',
    label: '7天',
  },
  Days15: {
    id: 1,
    name: '15天',
    label: '15天',
  },
  Days30: {
    id: 2,
    name: '30天',
    label: '30天',
  },
  Days60: {
    id: 3,
    name: '2个月',
    label: '2个月',
  },
  Days180: {
    id: 4,
    name: '6个月',
    label: '6个月',
  },
  Days365: {
    id: 5,
    name: '1年',
    label: '1年',
  },
})
// 商机中心-发布商机-交货周期
export const DeliveryTypeMap = getKeyToValueMap(DeliveryType)
// 商机中心-发布商机-交货周期
export const DeliveryTypeArray = getMapToArray(DeliveryType)

// 商机中心-数据状态 0-待接单 1-沟通中 2-已成交 3-已失效
export const OpportunityStatus = Object.freeze({
  wait: {
    id: 0,
    name: '待接单',
    label: '待接单',
  },
  progress: {
    id: 1,
    name: '沟通中',
    label: '沟通中',
  },
  done: {
    id: 2,
    name: '已成交',
    label: '已成交',
  },
  invalid: {
    id: 3,
    name: '已失效',
    label: '已失效',
  },
  check: {
    id: 4,
    name: '审核中',
    label: '审核中',
  },
})
// 商机中心-数据状态
export const OpportunityStatusMap = getKeyToValueMap(OpportunityStatus)
// 商机中心-数据状态
export const OpportunityStatusArray = getMapToArray(OpportunityStatus)

// 商品视频语言-数据状态 spuVideo-中 spuVideoEn-英文 spuVideoAr-阿拉伯
export const GOODS_LANG_VIDEO = Object.freeze({
  spuVideo: {
    id: 'spuVideo',
    name: '中文',
    zhName: '中文',
  },
  spuVideoEn: {
    id: 'spuVideoEn',
    name: 'English',
    zhName: '英语',
  },
  spuVideoAr: {
    id: 'spuVideoAr',
    name: 'العربية',
    zhName: '阿拉伯语',
  },
  spuVideoIdn: {
    id: 'spuVideoIdn',
    name: 'INA',
    zhName: '印尼语',
  },
})

// 商机中心-数据状态
export const GOODS_LANG_VIDEO_MAP = getKeyToValueMap(GOODS_LANG_VIDEO)
// 商机中心-数据状态
export const GOODS_LANG_VIDEO_ARRAY = getMapToArray(GOODS_LANG_VIDEO).map((item) => ({ ...item, ...GOODS_LANG_VIDEO[item.id] }))

// 运费设置
export const SHIPPING_TYPE = Object.freeze({
  DISCUSSED: {
    id: 0,
    name: '运费待议',
  },
  TEMPLATE: {
    id: 1,
    name: '运费模版',
  },
})
// 商机中心-数据状态
export const SHIPPING_TYPE_AMP = getKeyToValueMap(SHIPPING_TYPE)
// 商机中心-数据状态
export const SHIPPING_TYPE_ARRAY = getMapToArray(SHIPPING_TYPE)

// 发货实效
export const DELIVERY_LIMIT = Object.freeze({
  DAY1: {
    id: 0,
    name: '24小时',
  },
  DAY2: {
    id: 1,
    name: '48小时',
  },
  DAY3: {
    id: 2,
    name: '72小时',
  },
  DAY7: {
    id: 3,
    name: '7天',
  },
  DAY15: {
    id: 4,
    name: '15天',
  },
})
// 商机中心-数据状态
export const DELIVERY_LIMIT_AMP = getKeyToValueMap(DELIVERY_LIMIT)
// 商机中心-数据状态
export const DELIVERY_LIMIT_ARRAY = getMapToArray(DELIVERY_LIMIT)
export const GOODS_LANG_VIDEO_DETAIL = Object.freeze({
  zh: {
    id: 'spuVideo',
    name: '中文',
  },
  en: {
    id: 'spuVideoEn',
    name: 'English',
  },
  ar: {
    id: 'spuVideoAr',
    name: 'العربية',
  },
})
// 商品视频语言-数据状态
export const GOODS_LANG_VIDEO_DETAIL_MAP = getKeyToValueMap(GOODS_LANG_VIDEO_DETAIL)
// 商品视频语言-数据状态
export const GOODS_LANG_VIDEO_DETAIL_ARRAY = getMapToArray(GOODS_LANG_VIDEO_DETAIL)
