// 议价
import {
  // eslint-disable-next-line no-unused-vars
  getIdMap, // 获取 id 映射信息
  getKeyToValueMap // 获取映射信息
} from './utils'

// 议价方
export const BARGAIN_TAB_LIST = Object.freeze({
  SALE: {
    id: 2,
    label: '票方',
    name: 'sale'
  },
  BUY: {
    id: 1,
    label: '资方',
    name: 'buy'
  },
})

// 议价状态
export const BARGAIN_STATUS = Object.freeze({
  BARGAINING: {
    id: 0,
    name: '议价中',
    label: '议价中', // 左侧栏label
    title: '议价中', // 议价消息卡片左上标题
    icon: 'chengjie-filet-ext',
    isShow: true // 是否在左侧栏展示
  },
  BARGAIN_SUCCESS: {
    id: 1,
    name: '议价成功',
    label: '议价成功', // 左侧栏label
    title: '议价成功', // 议价消息卡片左上标题
    icon: 'chengjie-file-done',
    isShow: true // 是否在左侧栏展示
  },
  BARGAIN_FAIL: {
    id: 2,
    name: '议价失败',
    label: '议价关闭', // 左侧栏label
    title: '议价中', // 议价消息卡片左上标题
    icon: 'chengjie-file-excel',
    isShow: true // 是否在左侧栏展示
  },
  BARGAIN_FINISH: {
    id: 3,
    name: '议价结束',
    label: '议价关闭',
  },
})

// 议价中状态
export const BARGAINING_STATUS = Object.freeze({
  BUY_BARGAIN_APPLY: {
    id: 0,
    name: '买方发起议价'
  },
  BUY_BARGAIN_RECALL: {
    id: 1,
    name: '买方撤销议价'
  },
  SALE_BARGAIN_CONFIRM: {
    id: 2,
    name: '卖方同意议价'
  },
  SALE_BARGAIN_REJECT: {
    id: 3,
    name: '卖方拒绝议价'
  },
  SALE_DICKER_APPLY: {
    id: 4,
    name: '卖方还价'
  },
  SALE_DICKER_RECALL: {
    id: 5,
    name: '卖方撤销还价'
  },
  BUY_DICKER_CONFIRM: {
    id: 6,
    name: '买方同意还价'
  },
  BUY_DICKER_REJECT: {
    id: 7,
    name: '买方拒绝还价'
  },
  BARGAIN_FAIL_OVERTIME: {
    id: 8,
    name: '超时议价失败'
  },
  ORDER_STATUS_CANCELED: {
    id: 9,
    name: '订单状态变更取消'
  },
})

// 议价状态 id 映射 名称
export const BARGAIN_STATUS_VALUE_MAP = getKeyToValueMap(BARGAIN_STATUS)

// 议价交易方 id 映射 名称
export const BARGAIN_TAB_LIST_VALUE_MAP = getKeyToValueMap(BARGAIN_TAB_LIST)
