import {
  // eslint-disable-next-line no-unused-vars
  getKeyToValueMap,
  getIdMap, // 获取 id 映射信息
} from './utils'

export const INQUIRYBARGAIN_TAB_LIST = Object.freeze({
  YIJIA: {
    id: '0', // 为什么不是number呢，因为我用了tabs，他的绑定值为string
    label: '议价',
    name: '议价',
  },
  XUNDAN: {
    id: '1',
    label: '询单',
    name: '询单',
  },
})

export const INQUIRYBARGAIN_TEXT = Object.freeze({
  ZIXUN: {
    id: 0,
    label: '咨询',
  },
  YIZIXUN: {
    id: 1,
    label: '已咨询',
  },
})

export const INQUIRYBARGAIN_DISCERNTYPE = Object.freeze({
  DANZHANG: {
    id: 1,
    label: '单张识别',
  },
  PILIANG: {
    id: 2,
    label: '批量识别',
  },
  ZIDONG: {
    id: 3,
    label: '自动同步',
  },
})

// 询单类型
export const INQUITY_TYPES = Object.freeze({
  IMG: {
    id: 0,
    name: '票面信息',
  },
  BEISHU: {
    id: 1,
    name: '背书手数',
  },
  KEYWORD: {
    id: 2,
    name: '敏感信息',
  },
  ONE: {
    id: 3,
    name: '个体户',
  },
})

export const INQUITY_TYPES_LIST = getIdMap(INQUITY_TYPES)

// 询单类型 id 映射 名称
export const INQUIRY_TYPES_VALUE_MAP = getKeyToValueMap(INQUITY_TYPES)

export const INQUIRY_TAB_LIST = Object.freeze({
  SALE: {
    id: '2',
    label: '票方',
    name: 'sale',
  },
  BUY: {
    id: '1',
    label: '资方',
    name: 'buy',
  },
})

export const INQUIRY_TEM = Object.freeze({
  ADD: {
    id: 1,
    label: '新增',
  },
  UPDATE: {
    id: 2,
    label: '修改',
  },
})

export const INQUIRYREFRESH = 'INQUIRYREFRESH'

export const INQUIRYTEMMAX = 5 // 模版最大数量

export const INQUIRYBARGAIN_NAME = Object.freeze({
  YIJIA: {
    id: 2,
    name: '议价',
    icon: 'chengjie-bargaining'
  },
  XUNDAN: {
    id: 3,
    name: '询单',
    icon: 'chengjie-icon-xundan'
  },
  ALL: {
    id: 1,
    name: '议价&询单'
  }
})

export const INQUIRY_NAMES_VALUE_MAP = getKeyToValueMap(INQUIRYBARGAIN_NAME)
