const DOMAIN = 'https://oss.chengjie.red/'
const PATH = `${DOMAIN}web/pdf/`

// 需要处理的
// 关注企业-导入模板文件地址
export const CONCERNS_TEMPLATE_URL = `${DOMAIN}web/template/concerns_company/我关注的企业下载模板.xlsx`
export const BLACKLIST_TEMPLATE_URL = `${DOMAIN}web/template/concerns_company/黑名单导入模板.xlsx`
export const BLACKLIST_PANEL_TEMPLATE_URL = `${DOMAIN}web/template/名单导入模版.xlsx`
// 新票批量发布-（子票区间、票号）导入模板文件地址
export const BATCH_NEW_TICKETS_DRFAT = `${DOMAIN}web/template/新票批量票模板(子票区间相同).xlsx` // 票号导入模版
export const BATCH_NEW_TICKETS_SUB_TICKET = `${DOMAIN}web/template/新票批量票模板(票号相同).xlsx` // 子票区间导入模版

// 关注企业-服务协议
export const CONCERNS_COMPANY_PROTOCOL_URL = `${PATH}信息增值服务协议.pdf?v=20240401` // 经纪商没有关注
export const PLATFORM_DEFAULT_RULES_URL = `${PATH}违约规则.pdf` // 项目里面没有使用 在文件中引用
export const PLATFORM_DEFAULT_RULESNEW_URL = `${PATH}订单违约规则.pdf?v=${new Date().getDate()}`

/* 说明：陈聪需求-新票订单违约规则不要了，和普通订单违约规则一样 */
export const PLATFORM_DEFAULT_RULESNEW_URL_NEW_DRAFT = `${PATH}订单违约规则.pdf?v=${new Date().getDate()}`

export const FASTTRADE_URL = `${PATH}极速出票规则.pdf?v=${new Date().getDate()}` // 光速自动
export const RADAR_URL = `${PATH}自动扫票规则.pdf?v=${new Date().getDate()}` // 光速自动

// 自动接单操作流程说明视频--暂无
export const RADAR_VIDEO_RUL = ''

export const TRANSFER_OUT_APPLICATION_DESCRIPTION = `${DOMAIN}web/template/转出申请说明.docx?v = ${new Date().getDate()}` // 智付E渠道余额转出申请说明

// 智付E+操作指引
export const ZHI_FU_YI_LIAN_PLUS_GUIDE_URL = `${PATH}${encodeURIComponent('智付E+电子账户操作指引.pdf')}?v=${new Date().getDate()}`
// 智付邦+操作指引
export const ZHI_FU_ZHONG_BANG_PLUS_GUIDE_URL = `${PATH}${encodeURIComponent('智付邦+电子账户操作指引.pdf')}?v=${new Date().getDate()}`
// 新票拆分设置按拆分金额*张数 导入模版
export const SPLIT_SETTING_IMPORT_URL = `${DOMAIN}web/template/拆分设置模板.xlsx?v=${new Date().getDate()}`

// E++模拟登录企业信息授权书
export const E_PLUS_SIGN_COMPANY_INFO_AUTH_URL = `${PATH}${encodeURIComponent('模拟登录企业信息授权书.pdf')}?v=${new Date().getDate()}`
// E++邮箱开通企业信息授权书
export const E_PLUS_SIGN_EMAIL_COMPANY_INFO_AUTH_URL = `${PATH}${encodeURIComponent('邮箱开通企业信息授权书.pdf')}?v=${new Date().getDate()}`
// E++识单助手启用企业信息授权书
export const E_PLUS_SIGN_DISCOVERY_HELPER_COMPANY_INFO_AUTH_URL = `${PATH}${encodeURIComponent('识单助手启用企业信息授权书.pdf')}?v=${new Date().getDate()}`
// E++操作指引
export const E_PLUS_GUIDE_URL = `${PATH}${encodeURIComponent('E++电子账户操作指引.pdf')}?v=${new Date().getDate()}`
// erp协议
const OSS_FILES_URL = {
  RADAR_ORDER_AUTHORIZATION: `${PATH}“自动扫票”授权委托书.pdf?v=20240401`,
  LIGHT_SPEED_ORDER_AUTHORIZATION: `${PATH}“极速出票”授权委托书.pdf?v=20240401`,
  // 注册登录
  REGISTER_SERVICE_URL: `${PATH}承接ERP注册服务协议.pdf?v=20240401`,
  PRIVSCY_POLICY_URL: `${PATH}承接ERP隐私协议.pdf?v=20240401`,
  // 认证开户
  EQIANBAO_SERVICE_PROTOCOL_URL: `${PATH}e签宝服务协议与数字证书申请协议.pdf`,
  EQIANBAO_PRIVACY_PROTOCOL_URL: `${PATH}e签宝隐私协议.pdf`,
  REAL_NAME_AUTHENTICATION_URL: `${PATH}承接ERP实名认证服务协议.pdf?v=20240407`,
  YILIAN_OPEN_PROTOCOL_URL: 'https://ftcms.jd.com/p/page/woofb0d8updj.htm', // 亿联银行虚户开通电子协议
  HELIBAO_OPEN_PROTOCOL_URL: 'https://ftcms.jd.com/p/page/bb4jco5lyixp.htm', // 智付合利宝虚户开通电子协议
  ZHONGBANG_OPEN_PROTOCOL_URL: 'https://ftcms.jd.com/p/page/jmde2ti81wi4.htm', // 《众邦银行E账通系统开户三方协议》
  ZHONGBANGPULS_OPEN_PROTOCOL_URL: `${PATH}武汉众邦银行股份有限公司B2B支付服务三方协议V231031.pdf?v=20240401`, // 《众邦E+开户三方协议》
  // 云豆
  RECHARGE_PROTOCOL_URL: `${PATH}承接贝充值协议.pdf?v=20240401`,
  CONVERSION_PROTOCOL_URL: `${PATH}承接贝红包兑换协议.pdf?v=20240401`,
  UNDERTAKE_PROTOCOL_URL: `${PATH}小英领活个人自由经营者承揽协议.pdf`,
  // 交易
  AGREEMENT_OF_TRANSFER: `${PATH}票据收益权转让合同-1213.pdf`,
  AGREEMENT_OF_TRANSFER_NEW_DRAFT: `${PATH}新一代票据收益权转让合同.pdf?v=20240401`,
  NEW_DRAFT_GUIDE: `${PATH}新一代票据指南.pdf`,
  SP_RISK: `${PATH}商票风险揭示书.pdf?v=20240401`,
  REFERENCE_TABLE: `${PATH}参考表.pdf`,
  ASSET_TRANSFER_AUTHORIZATION: `${PATH}资产转让补充授权书.pdf`,
  // 上传佐证风险说明
  RISK_STATEMENT: `${PATH}风险业务情况说明函.pdf`

}

export { OSS_FILES_URL }
