// 文章相关常量
import {
  getKeyToValueMap // 获取映射信息
} from './utils'

/**
 * 新闻动态
 * hideInBottom 页脚信息中是否展示当前入口
 */
export const NEWS = {
  ANNOUNCEMENT: {
    id: 1,
    name: 'announcement',
    label: '平台公告'
  },
  INFORMATION: {
    id: 2,
    name: 'information',
    label: '平台资讯',
    isHNShow: true,
  },
  DYNAMICS: {
    id: 3,
    name: 'dynamics',
    label: '行业动态',
    isHNShow: true,
  },
  CLASSROOM: {
    id: 4,
    name: 'classroom',
    label: '深度益课堂',
    isHNShow: true,
  },
  // COMMEND: {
  //   id: 5,
  //   name: 'commend',
  //   label: '诚信企业表彰榜',
  //   hideInBottom: true,
  // },
  REPLY: {
    id: 8,
    name: 'reply',
    label: '政府职能部门批复',
    isHNShow: true,
  },
}
// 添加 path 属性，表示对应菜单所在路径
Object.values(NEWS).forEach(obj => {
  obj.path = `/news/${obj.name}`
})
// 新闻动态数组
export const NEWS_ARRAY = Object.values(NEWS)
// key-对象 map
export const NEWS_NAME_VALUE_MAP = getKeyToValueMap(NEWS, 'name', null)

// 关于深度
export const ABOUT = {
  INTRO: {
    id: 1,
    name: 'intro',
    label: '平台介绍'
  },
  BUSINESS: {
    id: 2,
    name: 'business',
    label: '业务合规'
  },
  CONTACT: {
    id: 3,
    name: 'contact',
    label: '联系我们'
  },
  JOIN: {
    id: 4,
    name: 'join',
    label: '加入我们'
  },
}
// 添加 path 属性，表示对应菜单所在路径
Object.values(ABOUT).forEach(obj => {
  obj.path = `/about/${obj.name}`
})
// 关于深度数组
export const ABOUT_ARRAY = Object.values(ABOUT)
// key-对象 map
export const ABOUT_NAME_VALUE_MAP = getKeyToValueMap(ABOUT, 'name', null)

// 帮助中心
export const HELP = {
  GUIDE: {
    id: 6,
    name: 'guide',
    label: '新手指引'
  },
  NOTICE: {
    id: 1,
    name: 'notice',
    label: '通知公告',
    icon: 'chengjie-tongzhigonggao'
  },
  RULES: {
    id: 2,
    name: 'rules',
    label: '业务规则',
    icon: 'chengjie-pingtaiguize1'
  },
  RISK_ACCEPTOR: {
    id: 3,
    name: 'risk-acceptor',
    label: '风险承兑人',
    hideInBottom: true,
  },
  FAQ: {
    id: 4,
    name: 'faq',
    label: '常见问题',
    icon: 'chengjie-changjianwenti'
  },
  DOWNLOAD: {
    id: 1,
    name: 'download',
    label: '相关下载',
    icon: 'chengjie-xiangguanxiazai'
  },
}
// 添加 path 属性，表示对应菜单所在路径
Object.values(HELP).forEach(obj => {
  obj.path = `/help/${obj.name}`
})
// 帮助中心数组
export const HELP_ARRAY = Object.values(HELP)
// key-对象 map
export const HELP_NAME_VALUE_MAP = getKeyToValueMap(HELP, 'name', null)

// 创新产品
export const INNOVATIVE_PRODUCT = {
  TGPRO: {
    id: 1,
    name: 'tgpro',
    label: '推广大使',
    path: 'https://tgpro.sdpjw.cn/#/front/home/<USER>',
    hideInBottom: true
  },
  OPEN_PLATFORM: {
    id: 2,
    name: 'open_platform',
    label: '开放平台',
    path: 'https://open.spbbank.cn/home'
  },
  JSTPJ: {
    id: 3,
    name: 'jstpj',
    label: '聚速贴',
    path: 'https://www.jstpj.cn/index'
  },
  SPBBANK: {
    id: 4,
    name: 'spbbank',
    label: '商票板',
    path: 'https://www.spbbank.cn/'
  },
  // DA_PIAO_QUAN: {
  //   id: 5,
  //   name: 'da_piao_quan',
  //   label: '定向+',
  //   path: 'https://www.dapiaoquan.com/#/home'
  // },
  TICKET_RATING: {
    id: 6,
    name: 'ticket_rating',
    label: '商票信评',
    path: '',
  }
}
// 创新产品数组
export const INNOVATIVE_PRODUCT_ARRAY = Object.values(INNOVATIVE_PRODUCT)
