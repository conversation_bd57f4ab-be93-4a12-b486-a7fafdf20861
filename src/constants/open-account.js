// 开户相关常量
import {
  getKeyToValueMap // 获取映射信息
} from './utils'

// 企业最新开户记录申请单状态：0-未提交，1-提交失败，2-待京东审核，3-京东审核失败，4-待京东打款，5-京东打款成功，6-京东打款失败，7-打款验证失败，8-已通过
export const APPLICATION_STATUS = Object.freeze({
  NOT_SUBMIT: {
    id: 0,
    name: '未提交'
  },
  SUBMIT_FAIL: {
    id: 1,
    name: '提交失败'
  },
  TO_AUDIT: {
    id: 2,
    name: '待京东审核'
  },
  AUDIT_FAIL: {
    id: 3,
    name: '京东审核失败'
  },
  TO_PLAY_WITH: {
    id: 4,
    name: '待京东打款'
  },
  PLAY_WITH_SUCCESS: {
    id: 5,
    name: '京东打款成功'
  },
  PLAY_WITH_FAIL: {
    id: 6,
    name: '京东打款失败'
  },
  PLAY_VERIFICATION_FAIL: {
    id: 7,
    name: '打款验证失败'
  },
  HAVE_BEEN_THROUGH: {
    id: 8,
    name: '已通过'
  },
  PENDING_SIGN: {
    id: 9,
    name: '待签署协议'
  },
  SIGN_FAIL: {
    id: 10,
    name: '协议签署失败'
  }
})

// 企业最新开户记录申请单状态 id 映射 名称
export const APPLICATION_STATUS_VALUE_MAP = getKeyToValueMap(APPLICATION_STATUS)

// 企业状态
export const CORP_STATUS = Object.freeze({
  NO_REAL_NAME: {
    id: 0,
    name: '未实名'
  },
  IN_THE_REAL_NAME: {
    id: 1,
    name: '实名中'
  },
  HAVE_REAL_NAME: {
    id: 2,
    name: '已实名'
  },
  REAL_NAME_FAILURE: {
    id: 3,
    name: '实名失效'
  }
})

export const BAN_STATUS = Object.freeze({
  ENABLE: {
    id: 0,
    name: '启用'
  },
  DISABLE: {
    id: 1,
    name: '禁用'
  }
})
