// 信用分相关常量
import {
  getKeyToValueMap // 获取映射信息
} from './utils'

// 信用等级
export const CREDIT_LEVEL_CODE = Object.freeze({
  NORMAL: {
    id: 0,
    icon: 'https://oss.chengjie.red/web/imgs/credit-svg/erp-xinyongyiban.svg',
    name: '一般',
    score: 500,
    stage: '0≤信用分<500',
    type: 'erp-xinyongyiban',
    color: '#9DA1A9'
  },
  MIDDLE: {
    id: 1,
    icon: 'https://oss.chengjie.red/web/imgs/credit-svg/erp-xinyongzhongdeng.svg',
    name: '中等',
    score: 600,
    stage: '500≤信用分<600',
    type: 'erp-xinyongzhongdeng',
    color: '#936141'
  },
  GOOD: {
    id: 2,
    icon: 'https://oss.chengjie.red/web/imgs/credit-svg/erp-xinyonglianghao.svg',
    name: '良好',
    score: 700,
    stage: '600≤信用分<700',
    type: 'erp-xinyonglianghao',
    color: '#18A1FF'
  },
  GREAT: {
    id: 3,
    icon: 'https://oss.chengjie.red/web/imgs/credit-svg/erp-xinyongyouxiu.svg',
    name: '优秀',
    score: 850,
    stage: '700≤信用分<850',
    type: 'erp-xinyongyouxiu',
    color: '#FFB734'
  },
  PERFECT: {
    id: 4,
    icon: 'https://oss.chengjie.red/web/imgs/credit-svg/erp-xinyongjihao.svg',
    name: '极好',
    score: 999,
    stage: '850≤信用分≤999',
    type: 'erp-xinyongjihao',
    color: '#F74B2C'
  },
})
// 信用等级 id 映射 name
export const CREDIT_LEVEL_NAME_MAP = getKeyToValueMap(CREDIT_LEVEL_CODE)
// 信用等级 id 映射 icon
export const CREDIT_LEVEL_ICON_MAP = getKeyToValueMap(CREDIT_LEVEL_CODE, 'id', 'icon')
// 信用等级 id 映射 score
export const CREDIT_LEVEL_SCORE_MAP = getKeyToValueMap(CREDIT_LEVEL_CODE, 'id', 'score')
// 信用等级 id 映射 type
export const CREDIT_LEVEL_TYPE_MAP = getKeyToValueMap(CREDIT_LEVEL_CODE, 'id', 'type')
// 信用等级 id 映射 color
export const CREDIT_LEVEL_COLOR_MAP = getKeyToValueMap(CREDIT_LEVEL_CODE, 'id', 'color')
