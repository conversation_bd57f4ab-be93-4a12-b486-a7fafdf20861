// 特殊文案翻译
import { getKeyToValueMap } from './utils'

// 临沂商城 · 中国大集
export const LINYI_CHINA_MARKET = Object.freeze({
  CHINESE: {
    id: 'zh',
    name: '临沂商城 · 中国大集',
  },
  ENGLISH: {
    id: 'en',
    name: 'Linyi Trade City · Chinamarket',
  },
  ARABIC: {
    id: 'ar',
    name: 'مدينة ليني التجارية · سوق الصين الكبرى',
  },
  THAI: {
    id: 'thai',
    name: 'ร้านค้าหลินอี๋ · ตลาดใหญ่ของประเทศจีน',
  },
  INDONESIAN: {
    id: 'indonesian',
    name: 'Linyi Trade City · Chinamarket',
  },
  RUSSIAN: {
    id: 'ru',
    name: 'Большая серия линьи · коммерческого города китая',
  },
  TURKISH: {
    id: 'tr',
    name: '<PERSON><PERSON> Merkez · <PERSON><PERSON> Büyük Pazarı',
  },
})
// 临沂商城 · 中国大集
export const LINYI_CHINA_MARKET_MAP = getKeyToValueMap(LINYI_CHINA_MARKET)

// PC端 AI大集哥
export const AI_BROTHER = Object.freeze({
  CHINESE: {
    id: 'zh',
    name: 'AI大集哥',
  },
  ENGLISH: {
    id: 'en',
    name: 'AI Brother',
  },
  ARABIC: {
    id: 'ar',
    name: 'مجموعة رقمية كبيرة',
  },
  THAI: {
    id: 'thai',
    name: 'บรรยายไทยโดย AI',
  },
  INDONESIAN: {
    id: 'indonesian',
    name: 'AI Brother',
  },
  RUSSIAN: {
    id: 'ru',
    name: 'Большой эл',
  },
  TURKISH: {
    id: 'tr',
    name: 'AI Daji Ge​',
  },
})
// AI大集哥
export const AI_BROTHER_MAP = getKeyToValueMap(AI_BROTHER)

// 移动端 AI大集哥
export const MOBILE_AI_BROTHER = Object.freeze({
  CHINESE: {
    id: 'zh',
    name: '大集哥',
  },
  ENGLISH: {
    id: 'en',
    name: 'AI Bro',
  },
})
// AI大集哥
export const MOBILE_AI_BROTHER_MAP = getKeyToValueMap(MOBILE_AI_BROTHER)

/***************************  AI 数字人对话 start  ****************************/
// 生成中
export const GENERATE = Object.freeze({
  CHINESE: {
    id: 'zh',
    name: '生成中',
  },
  ENGLISH: {
    id: 'en',
    name: 'Generating',
  },
  ARABIC: {
    id: 'ar',
    name: 'توليد',
  },
  THAI: {
    id: 'thai',
    name: 'กำลังสร้าง',
  },
  INDONESIAN: {
    id: 'indonesian',
    name: 'Menimbulkan',
  },
  RUSSIAN: {
    id: 'ru',
    name: 'Здание',
  },
  TURKISH: {
    id: 'tr',
    name: 'Oluşturuluyor',
  },
})
// 生成中
export const GENERATE_MAP = getKeyToValueMap(GENERATE)

// 已暂停
export const SUSPENDED = Object.freeze({
  CHINESE: {
    id: 'zh',
    name: '已暂停',
  },
  ENGLISH: {
    id: 'en',
    name: 'Suspended',
  },
  ARABIC: {
    id: 'ar',
    name: 'متوقف',
  },
  THAI: {
    id: 'thai',
    name: 'หยุดชั่วคราว',
  },
  INDONESIAN: {
    id: 'indonesian',
    name: 'Telah berhenti',
  },
  RUSSIAN: {
    id: 'ru',
    name: 'Приостановлена',
  },
  TURKISH: {
    id: 'tr',
    name: 'Duraklatıldı',
  },
})
// 已暂停
export const SUSPENDED_MAP = getKeyToValueMap(SUSPENDED)

// 重新生成
export const REBUILD = Object.freeze({
  CHINESE: {
    id: 'zh',
    name: '重新生成',
  },
  ENGLISH: {
    id: 'en',
    name: 'Rebuild',
  },
  ARABIC: {
    id: 'ar',
    name: 'إعادة بناء',
  },
  THAI: {
    id: 'thai',
    name: 'สร้างใหม่',
  },
  INDONESIAN: {
    id: 'indonesian',
    name: 'Regeneratif',
  },
  RUSSIAN: {
    id: 'ru',
    name: 'Перестраивать',
  },
  TURKISH: {
    id: 'tr',
    name: 'Yeniden Oluştur',
  },
})
// 重新生成
export const REBUILD_MAP = getKeyToValueMap(REBUILD)

// 停止生成
export const STOP_GENERATION = Object.freeze({
  CHINESE: {
    id: 'zh',
    name: '停止生成',
  },
  ENGLISH: {
    id: 'en',
    name: 'Stop generation',
  },
  ARABIC: {
    id: 'ar',
    name: 'وقف توليد',
  },
  THAI: {
    id: 'thai',
    name: 'หยุดการสร้าง',
  },
  INDONESIAN: {
    id: 'indonesian',
    name: 'Berhenti menghasilkan',
  },
  RUSSIAN: {
    id: 'ru',
    name: 'Остановка сборки',
  },
  TURKISH: {
    id: 'tr',
    name: 'Oluşturmayı Durdur',
  },
})
// 停止生成
export const STOP_GENERATION_MAP = getKeyToValueMap(STOP_GENERATION)

/***************************  AI 数字人对话 end  ****************************/
// 停止生成
export const EXPORT_TO_DOMESTIC = Object.freeze({
  CHINESE: {
    id: 'zh',
    name: '临沂商城 官方平台 国企信誉 交易保障',
  },
  ENGLISH: {
    id: 'en',
    name: 'Linyi Trade city, Official Platform State Enterprise, Trade Guaranteed',
  },
})
// 停止生成
export const EXPORT_TO_DOMESTIC_MAP = getKeyToValueMap(EXPORT_TO_DOMESTIC)

/***************************  出口转内销申请页面 end ****************************/

// 大集哥-咨询商品
export const PRODUCT_INQUIRY = Object.freeze({
  CHINESE: {
    id: 'zh',
    name: '咨询商品',
  },
  ENGLISH: {
    id: 'en',
    name: 'Product Inquiry',
  },
  ARABIC: {
    id: 'ar',
    name: '​استفسار عن المنتج',
  },
  THAI: {
    id: 'thai',
    name: 'สอบถามข้อมูลสินค้า',
  },
  INDONESIAN: {
    id: 'indonesian',
    name: 'Inquiry Produk',
  },
  RUSSIAN: {
    id: 'ru',
    name: 'Консультировать товар',
  },
  TURKISH: {
    id: 'tr',
    name: 'Çevrimiçi Ürün Danışmanlık​',
  },
})
// 咨询商品
export const PRODUCT_INQUIRY_MAP = getKeyToValueMap(PRODUCT_INQUIRY)

// 产品详情
export const PRODUCT_DETAIL_ADDCART = Object.freeze({
  CHINESE: {
    id: 'zh',
    name: '加入进货单',
  },
  ENGLISH: {
    id: 'en',
    name: 'Add to Purchase',
  },
  ARABIC: {
    id: 'ar',
    name: 'أضف إلى أمر الشراء',
  },
  THAI: {
    id: 'thai',
    name: 'เพิ่มใบสั่งซื้อ',
  },
  INDONESIAN: {
    id: 'indonesian',
    name: '+Keranjang',
  },
  RUSSIAN: {
    id: 'ru',
    name: 'купить',
  },
  TURKISH: {
    id: 'tr',
    name: 'Siparişe Ekle​',
  },
})
//  产品详情
export const PRODUCT_DETAIL_ADDCART_MAP = getKeyToValueMap(PRODUCT_DETAIL_ADDCART)
export const PRODUCT_DETAIL_BUY = Object.freeze({
  CHINESE: {
    id: 'zh',
    name: '立即购买',
  },
  ENGLISH: {
    id: 'en',
    name: 'Buy Now',
  },
  ARABIC: {
    id: 'ar',
    name: 'اشتري الآن',
  },
  THAI: {
    id: 'thai',
    name: 'ซื้อเลย',
  },
  INDONESIAN: {
    id: 'indonesian',
    name: 'Beli Sekarang',
  },
  RUSSIAN: {
    id: 'ru',
    name: 'купить',
  },
  TURKISH: {
    id: 'tr',
    name: 'Hemen Satın Al​',
  },
})
//  产品详情
export const PRODUCT_DETAIL_BUY_MAP = getKeyToValueMap(PRODUCT_DETAIL_BUY)
export const PRODUCT_DETAIL_SERVICE = Object.freeze({
  CHINESE: {
    id: 'zh',
    name: '咨询客服',
  },
  ENGLISH: {
    id: 'en',
    name: 'Service',
  },
  ARABIC: {
    id: 'ar',
    name: 'استشر',
  },
  THAI: {
    id: 'thai',
    name: 'ปรึกษา',
  },
  INDONESIAN: {
    id: 'indonesian',
    name: 'Layanan',
  },
  RUSSIAN: {
    id: 'ru',
    name: 'Сервис',
  },
  TURKISH: {
    id: 'tr',
    name: 'Müşteri Hizmetleri​',
  },
})
//  产品详情
export const PRODUCT_DETAIL_SERVICE_MAP = getKeyToValueMap(PRODUCT_DETAIL_SERVICE)
