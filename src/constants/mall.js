// 存放项目常量
import { ossUrl } from '@/constants/common'
import { getKeyToValueMap, getMapToArray } from './utils'

// 注册类型：买家、卖家
export const REGISTER_TYPE = Object.freeze({
  BUYER: {
    id: 1,
    name: { zh: '注册为买家', en: 'Register as a buyer' },
    title: { zh: '我要找货', en: 'Looking for goods' },
    subNavTitle: { zh: '买家中心', en: 'Buyer center' },
    path: '/buyer-center',
  },
  SELLER: {
    id: 2,
    name: { zh: '注册为卖家', en: 'Register as a seller' },
    title: { zh: '我有货源', en: 'I have a supply' },
    subNavTitle: { zh: '卖家中心', en: 'Seller center' },
    path: '/seller-center',
  },
  SERVICE: {
    id: 3,
    name: { zh: '注册为贸易服务商', en: 'Register as a servicer' },
    title: { zh: '我是贸易服务商', en: 'I am a trade servicer' },
    subNavTitle: { zh: '贸易服务商中心', en: 'Trade servicer center' },
    path: '/service-center',
  },
  MERCHANT_SERVICE: {
    id: 4,
    name: { zh: '注册为商户服务商', en: 'Register as a merchant servicer' },
    title: { zh: '我是商户服务商', en: 'I am a merchant servicer' },
    subNavTitle: { zh: '商户服务商中心', en: 'Merchant servicer center' },
    path: '/merchant-services-center',
  },
})
// 注册类型数组
export const REGISTER_TYPE_ARRAY = getMapToArray(REGISTER_TYPE)

// 多语言
export const LANG_TYPE = Object.freeze({
  CHINESE: {
    id: 1,
    key: 'chinese_simplified',
    name: '简体中文',
    mobileName: '中文',
    subKey: 'zh',
  },
  ENGLISH: {
    id: 2,
    key: 'english',
    name: 'English',
    mobileName: 'English',
    subKey: 'en',
  },
  ARABIC: {
    id: 3,
    key: 'arabic',
    name: 'العربية',
    mobileName: 'العربية',
    subKey: 'ar',
  },
  THAI: {
    id: 4,
    key: 'thai',
    name: 'ภาษาไทย',
    mobileName: 'ภาษาไทย',
    subKey: 'thai',
  },
  INDONESIAN: {
    id: 5,
    key: 'indonesian',
    name: 'Bahasa Indonesia',
    mobileName: 'Indonesian',
    subKey: 'indonesian',
  },
  RUSSIAN: {
    id: 6,
    key: 'russian',
    name: 'Pусский язык',
    mobileName: 'Russian',
    subKey: 'ru',
  },
  TURKISH: {
    id: 7,
    key: 'turkish',
    name: 'Türkçe',
    mobileName: 'Turkish',
    subKey: 'tr',
  },
})

export const BANNER_CONFIG = {
  CHUKOU_ZHUAN_NEIXIAO: {
    lang: {
      zh: `${ossUrl}/mall/new-banner/出口转内销banner-中文版.png`,
      en: `${ossUrl}/mall/new-banner/出口转内销banner-英文版.png`,
      ar: `${ossUrl}/mall/new-banner/出口转内销banner-阿拉伯语.png`,
      thai: `${ossUrl}/mall/new-banner/出口转内销banner-泰语.png`,
      indonesian: `${ossUrl}/mall/new-banner/出口转内销banner-印尼语.png`,
    },
    shopId: 'exportToDomestic',
  },
  CHAIN_MARKET: {
    lang: {
      zh: `${ossUrl}/mall/new-banner/中国大集banner-中文版.jpg`,
      en: `${ossUrl}/mall/new-banner/中国大集banner-英文版.jpg`,
      ar: `${ossUrl}/mall/new-banner/中国大集banner-阿拉伯语.jpg`,
      thai: `${ossUrl}/mall/new-banner/中国大集banner-泰语.jpg`,
      indonesian: `${ossUrl}/mall/new-banner/中国大集banner-印尼语.jpg`,
      ru: `${ossUrl}/mall/new-banner/中国大集banner-俄语版.jpg`,
    },
    shopId: '1855828852119502850',
  },
  XIN_MING_HUI: {
    lang: {
      zh: `${ossUrl}/mall/new-banner/新明辉-中文版.jpg`,
      en: `${ossUrl}/mall/new-banner/新明辉-英语.jpg`,
      ar: `${ossUrl}/mall/new-banner/新明辉-阿拉伯语.jpg`,
      thai: `${ossUrl}/mall/new-banner/新明辉-泰语.jpg`,
      indonesian: `${ossUrl}/mall/new-banner/新明辉-印尼语.jpg`,
      ru: `${ossUrl}/mall/new-banner/新明辉-俄语版.jpg`,
    },
    shopId: '1857004354461098412',
  },
  HAO_DUO_BAO: {
    lang: {
      zh: `${ossUrl}/mall/new-banner/好多宝-中文版.jpg`,
      en: `${ossUrl}/mall/new-banner/好多宝-英语.jpg`,
      ar: `${ossUrl}/mall/new-banner/好多宝-阿拉伯语.jpg`,
      thai: `${ossUrl}/mall/new-banner/好多宝-泰语.jpg`,
      indonesian: `${ossUrl}/mall/new-banner/好多宝-印尼语.jpg`,
      ru: `${ossUrl}/mall/new-banner/好多宝-俄语版.jpg`,
    },
    shopId: '1857004354461098386',
  },
  XIN_MEI_JIE: {
    lang: {
      zh: `${ossUrl}/mall/new-banner/鑫美捷-中文版.jpg`,
      en: `${ossUrl}/mall/new-banner/鑫美捷-英语.jpg`,
      ar: `${ossUrl}/mall/new-banner/鑫美捷-阿拉伯语.jpg`,
      thai: `${ossUrl}/mall/new-banner/鑫美捷-泰语.jpg`,
      indonesian: `${ossUrl}/mall/new-banner/鑫美捷-印尼语.jpg`,
      ru: `${ossUrl}/mall/new-banner/鑫美捷-俄语版.jpg`,
    },
    shopId: '1857004354461098340',
  },
  YOU_DIAN_MEN_CHUANG: {
    lang: {
      zh: `${ossUrl}/mall/new-banner/优典门窗-中文版.jpg`,
      en: `${ossUrl}/mall/new-banner/优典门窗-英文版.jpg`,
      ar: `${ossUrl}/mall/new-banner/优典门窗-阿拉伯语.jpg`,
      thai: `${ossUrl}/mall/new-banner/优典门窗-泰语.jpg`,
      indonesian: `${ossUrl}/mall/new-banner/优典门窗-印尼语.jpg`,
      ru: `${ossUrl}/mall/new-banner/优典门窗-俄语版.jpg`,
    },
    shopId: '1857004354461099047',
  },
}

// 多语言
export const PC_LANG_TYPE_MAP = getKeyToValueMap(LANG_TYPE, 'subKey', 'name') // pc端
export const PC_LANG_TYPE_MAP_KEY = getKeyToValueMap(LANG_TYPE, 'subKey', 'key') // pc端
export const LANG_TYPE_ARRAY = getMapToArray(LANG_TYPE)

export const BANNER_CONFIG_AMP = getKeyToValueMap(BANNER_CONFIG, 'shopId', 'lang')

export const BANNER_CONFIG_ARRAY = getMapToArray(BANNER_CONFIG)
