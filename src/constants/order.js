// 存放项目常量
import { getKeyToValueMap, getMapToArray } from '@/constants/utils'

// 订单状态
export const ORDER_STATUS = {
  ALL: {
    name: '全部',
    buyerOrderStatus: '',
    orderTips: '',
    id: 1,
    translateName: {
      zh: '全部',
      en: 'All',
      ar: 'الكل',
      thai: 'ทั้งหมด',
      indonesian: 'Semua',
      ru: 'Все',
      tr: 'Tümü',
    },
  },
  WAIT_CONFIRM: {
    name: '待确认',
    buyerOrderStatus: '待供应商确认',
    orderTips: '待确认',
    stepTitle: '买家提交订单',
    id: 100,
    translateName: {
      zh: '待确认',
      en: 'Pending Confirmation',
      ar: 'قيد التأكيد',
      thai: 'กำลังรอการยืนยัน',
      indonesian: 'Menunggu Konfirmasi',
      ru: 'Ожидает подтверждения',
      tr: '<PERSON><PERSON>',
    },
  },
  WAIT_PAY: {
    name: '待付款',
    buyerOrderStatus: '待买家付款',
    orderTips: '待付款',
    stepTitle: '供应商确认',
    id: 200,
    translateName: {
      zh: '待付款',
      en: 'Pending',
      ar: 'قيد الدفع',
      thai: 'กำลังชำระเงิน',
      indonesian: 'Belum Bayar',
      ru: 'Ожидает оплаты',
      tr: 'Ödeme Bekliyor',
    },
  },
  WAIT_AUDIT: {
    name: '付款待审核',
    buyerOrderStatus: '待平台付款审核',
    orderTips: '已上传支付凭证，待平台审核',
    stepTitle: '买家支付',
    id: 300,
    translateName: {
      zh: '付款待审核',
      en: 'Payment Pending Review',
      ar: 'قيد المراجعة',
      thai: 'กำลังตรวจสอบการชำระเงิน',
      indonesian: 'Menunggu Verifikasi',
      ru: 'Платеж ожидает проверки',
      tr: 'Ödeme İnceleme Bekliyor',
    },
  },
  AUDIT_FAILD: {
    name: '付款审核未通过',
    buyerOrderStatus: '平台付款审核未通过',
    orderTips: '付款审核未通过',
    stepTitle: '买家支付',
    id: 350,
    translateName: {
      zh: '付款审核未通过',
      en: 'Payment review not approved',
      ar: 'تحقق الدفع لم يتم الموافقة عليه',
      thai: 'การตรวจสอบการชำระเงินไม่ได้รับการยืนยัน',
      indonesian: 'Pembayaran Ditolak',
      ru: 'Платеж не одобрен',
      tr: 'Ödeme onaylanmadı',
    },
  },
  WAIT_SENT: {
    name: '待发货',
    buyerOrderStatus: '待供应商发货',
    orderTips: '等待卖家发货',
    stepTitle: '平台付款审核',
    id: 400,
    translateName: {
      zh: '待发货',
      en: 'Waiting for Delivery',
      ar: 'إنتظار الشحن',
      thai: 'การสั่งส่งสินค้า',
      indonesian: 'Menunggu Kirim',
      ru: 'Ожидает доставки',
      tr: 'Kargo Bekliyor',
    },
  },
  WAIT_RECEIVE: {
    name: '待收货',
    buyerOrderStatus: '待买家收货',
    orderTips: '待收货',
    stepTitle: '供应商发货',
    id: 500,
    translateName: {
      zh: '待收货',
      en: 'Waiting for Receiving',
      ar: 'إنتظار الاستلام',
      thai: 'การรอรับสินค้า',
      indonesian: 'Menunggu Diterima',
      ru: 'Ожидает получения',
      tr: 'Teslim Bekliyor',
    },
  },
  COMPLETED: {
    name: '已完成',
    buyerOrderStatus: '已完成',
    orderTips: '交易成功',
    stepTitle: '买家确认收货',
    id: 1000,
    translateName: {
      zh: '已完成',
      en: 'Completed',
      ar: 'تم الاستلام',
      thai: 'เสร็จสิ้น',
      indonesian: 'Selesai',
      ru: 'Завершено',
      tr: 'Tamamlandı',
    },
  },
  CLOSED: {
    name: '交易关闭',
    buyerOrderStatus: '交易关闭',
    orderTips: '交易关闭',
    stepTitle: '交易关闭',
    id: -1,
    translateName: {
      zh: '交易关闭',
      en: 'Transaction Closed',
      ar: 'تم الإغلاق',
      thai: 'การปิดการทำธุรกรรม',
      indonesian: 'Transaksi Ditutup',
      ru: 'Транзакция закрыта',
      tr: 'İşlem Kapatıldı',
    },
  },
}
export const ORDER_STATUS_ARRAY = getMapToArray(ORDER_STATUS)
export const ORDER_STATUS_MAP = getKeyToValueMap(ORDER_STATUS)
export const ORDER_STATUS_ORDER_TIPS_MAP = getKeyToValueMap(ORDER_STATUS, 'id', 'orderTips')
export const BUYER_ORDER_STATUS_MAP = getKeyToValueMap(ORDER_STATUS, 'id', 'buyerOrderStatus')

// 支付方式
export const PAY_TYPE = {
  OFFLINE: {
    name: '线下转账',
    id: 1,
  },
}
export const PAY_TYPE_ARRAY = getMapToArray(PAY_TYPE)
export const PAY_TYPE_MAP = getKeyToValueMap(PAY_TYPE)

// 审核状态
export const REVIEW_TYPE = {
  WAIT_AUDIT: {
    name: '付款待审核',
    color: '#F99703',
    id: 0,
  },
  AUDIT_FAILD: {
    name: '付款审核未通过',
    color: '#D8131A',
    id: -1,
  },
  WAIT_SENT: {
    name: '付款审核通过',
    color: '',
    id: 1,
  },
}
export const REVIEW_TYPE_ARRAY = getMapToArray(REVIEW_TYPE)
export const REVIEW_TYPE_MAP = getKeyToValueMap(REVIEW_TYPE)
export const REVIEW_TYPE_MAP_COLOR = getKeyToValueMap(REVIEW_TYPE, 'id', 'color')
