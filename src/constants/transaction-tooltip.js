import { getKeyToValueMap } from '@/constants/utils'
// 交易限制提示类型
export const TRANSACTION_TOOLTIP_TYPE = Object.freeze({
  HOLIDAY: 'holiday', // 节假日闭市
  DAILY: 'daily', // 日常闭市
  RADAR: 'radar', // 自动接单
  FAST: 'fast', // 极速订单
  CREDIT: 'credit', // 信用分
})

// 订单交易完成-发起方签署合同状态
export const INITIATOR_SIGN_STATUS = Object.freeze({
  APPLY_SIGN: {
    id: 0,
    name: '发起签署申请',
    tipText: '若需要《票据收益权转让合同》，请向对方发起签署合同申请并等待处理结果。'
  },
  PENDING_HANDLE: {
    id: 1,
    name: '等待对方处理',
    tipText: '您已向对方发起《票据收益权转让合同》的签署申请，请耐心等待处理结果。'
  },
  OTHER_SIGNING: {
    id: 2,
    name: '对方签署中',
    tipText: '您已向对方发起《票据收益权转让合同》的签署申请，请耐心等待处理结果。'
  },
  SIGNING: {
    id: 3,
    name: '签署中',
    tipText: '对方已完成《票据收益权转让合同》的签署。您可按签署指引继续进行签署。'
  },
  SIGN_FINISH: {
    id: 4,
    name: '下载合同',
    tipText: '《票据收益权转让合同》已签署，您可点击下方按钮下载合同。'
  },
})

export const INITIATOR_SIGN_STATUS_MAP = getKeyToValueMap(INITIATOR_SIGN_STATUS)
export const INITIATOR_SIGN_STATUS_TIPTEXT = getKeyToValueMap(INITIATOR_SIGN_STATUS, 'id', 'tipText')

// 订单交易完成-接收方签署合同状态
export const RECIPIENT_SIGN_STATUS = Object.freeze({
  APPLY_SIGN: {
    id: 0,
    name: '发起签署申请',
    tipText: '若需要《票据收益权转让合同》，请向对方发起签署合同申请并等待处理结果。'
  },
  AGREE_SIGN: {
    id: 1,
    name: '同意签署',
    tipText: '对方发起签署合同申请，您可按签署指引完成《票据收益权转让合同》的签署操作。'
  },
  SIGNING: {
    id: 2,
    name: '签署中',
    tipText: '对方发起签署合同申请，您可按签署指引完成《票据收益权转让合同》的签署操作。'
  },
  OTHER_SIGNING: {
    id: 3,
    name: '对方签署中',
    tipText: '您已完成《票据收益权转让合同》的签署，请等待对方完成签署操作。'
  },
  SIGN_FINISH: {
    id: 4,
    name: '下载合同',
    tipText: '《票据收益权转让合同》已签署，您可点击下方按钮下载合同。'
  },
})

export const RECIPIENT_SIGN_STATUS_MAP = getKeyToValueMap(RECIPIENT_SIGN_STATUS)
export const RECIPIENT_SIGN_STATUS_TIPTEXT = getKeyToValueMap(RECIPIENT_SIGN_STATUS, 'id', 'tipText')
