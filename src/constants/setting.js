// 用户角色
export const USER_ROLE = Object.freeze({
  SALE: {
    id: 0,
    label: '票方',
    name: 'sale'
  },
  BUY: {
    id: 1,
    label: '资方',
    name: 'buy'
  },
})

// 用户设置选项
export const SETTING_USER = Object.freeze({
  SALE: {
    id: 1,
    label: '票方设置',
  },
  BUY: {
    id: 2,
    label: '资方设置',
  },
  NOTIFICATION: {
    id: 3,
    label: '消息通知设置',
  },
  // CONTACT: {
  //   id: 4,
  //   label: '联系方式设置'
  // },
  // ASK: {
  //   id: 5,
  //   label: '账户询户设置'
  // }
})

// 票方设置选项
export const SALE_SETTING_OPTIONS = Object.freeze({
  ISSUE_DEFAULT: {
    id: 0,
    label: '发布默认设置',
  },
  REGULAR_PUT_OFF_SHELF: {
    id: 1,
    label: '定时上下架设置',
  },
  BUY_AREA_BLACKLIST: {
    id: 2,
    label: '资方地区黑名单设置',
  },
  BUY_BLACKLIST: {
    id: 3,
    label: '资方黑名单设置',
  },
  // CONTACT_PHONE: {
  //   id: 4,
  //   label: '联系方式设置',
  // },
})

// 资方设置选项
export const BUY_SETTING_OPTIONS = Object.freeze({
  DEFAULT: {
    id: 0,
    label: '默认设置',
  },
  SALE_BLACKLIST: {
    id: 1,
    label: '票方黑名单设置',
  },
  SALE_AREA_BLACKLIST: {
    id: 4,
    label: '票方地区黑名单设置'
  },
  INQUIRY: {
    id: 2,
    label: '询单模版设置',
  },
  TICKET_KEYWORD: {
    id: 3,
    // label: '票面关键字设置',
    label: '风险关键字设置'
  }
})

// 星期列表
export const WEEK_LIST = Object.freeze([
  {
    value: 1,
    label: '周一',
  },
  {
    value: 2,
    label: '周二'
  },
  {
    value: 3,
    label: '周三'
  },
  {
    value: 4,
    label: '周四'
  },
  {
    value: 5,
    label: '周五'
  },
  {
    value: 6,
    label: '周六'
  },
  {
    value: 7,
    label: '周日'
  }
])
