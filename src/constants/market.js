import { getKeyToValueMap, getMapToArray } from '@/constants/utils'

// 后端接口顺序不变，前端id对应接口返回数据的 key 值
export const MARKET_TYPE = {
  1: {
    name: { zh: '整车机械车品', en: 'New Materials' },
    id: 5,
  },
  2: {
    name: { zh: '五金机电建材', en: 'Modern Grocery Store' },
    id: 1,
  },
  3: {
    name: { zh: '日用家具百货', en: 'Tech Devices' },
    id: 2,
  },
  4: {
    name: { zh: '服饰鞋包配件', en: 'Industrial Equipment' },
    id: 3,
  },
  5: {
    name: { zh: '农副产品食品', en: 'Agricultural Products' },
    id: 4,
  },
  6: {
    name: { zh: '数码家电电器', en: 'Building Materials' },
    id: 6,
  },
}

export const MARKET_TYPE_MAP = getKeyToValueMap(MARKET_TYPE)
export const MARKET_TYPE_ARRAY = getMapToArray(MARKET_TYPE)

export const ANCHOR_POINT_NAV = {
  1: {
    name: { zh: '市场介绍', en: 'Introduction' },
    id: '1',
  },
  // 2: {
  //   name: { zh: '实力商家', en: 'Sellers' },
  //   id: '2',
  // },
  3: {
    name: { zh: '热销商品', en: 'Products' },
    id: '3',
  },
  // 4: {
  //   name: { zh: '交通指引', en: 'Traffic Guide' },
  //   id: '4',
  // },
  // 5: {
  //   name: { zh: '生活指引', en: 'Life Guide' },
  //   id: '5',
  // },
  6: {
    name: { zh: '联系我们', en: 'contact' },
    id: '6',
  },
}
export const ANCHOR_POINT_NAV_MAP = getKeyToValueMap(ANCHOR_POINT_NAV)
export const ANCHOR_POINT_NAV_ARRAY = getMapToArray(ANCHOR_POINT_NAV)
