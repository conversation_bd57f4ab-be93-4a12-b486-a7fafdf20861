// 违约相关常量
import {
  getKeyToValueMap // 获取映射信息
} from './utils'

// 违约状态
export const BREACH_STATUS = Object.freeze({
  ORDER_ING: {
    id: 0,
    name: '订单进行中'
  },
  CAN_APPEAL: {
    id: 1,
    name: '可申诉',
  },
  PENDING_APPEAL: {
    id: 2,
    name: '处理中',
  },
  REJECT: {
    id: 3,
    name: '已驳回',
  },
  REVISION: {
    id: 4,
    name: '已改判',
  },
})
// 违约状态 id 映射 name
export const BREACH_STATUS_NAME_MAP = getKeyToValueMap(BREACH_STATUS)
