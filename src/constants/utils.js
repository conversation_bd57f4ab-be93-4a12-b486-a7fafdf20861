// 获取 id 映射信息
export function getIdMap(nameMap) {
  let list = Object.values(nameMap)
  let result = {}
  list.forEach(item => {
    result[item.id] = item
  })
  return Object.freeze(result)
}

// 获取映射信息
export function getKeyToValueMap(nameMap, key = 'id', value = 'name') {
  let list = Object.values(nameMap)
  let result = {}
  list.forEach(item => {
    result[item[key]] = value ? item[value] : item
  })
  return Object.freeze(result)
}
