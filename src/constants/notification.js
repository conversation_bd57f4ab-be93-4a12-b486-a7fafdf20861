// 消息提醒常量

// 消息提醒类型
export const MSG_TYPE = Object.freeze({
  ORDER_REFRESH: {
    id: 1,
    notificationTitle: '交易提醒',
    cardTitle: '交易提醒',
  },
  INTENTION: {
    id: 2,
    notificationTitle: '消息提醒',
    cardTitle: '意向价格通知',
  },
  BROKE_CONTRACT_RECORD_NOTICE: {
    id: 3,
    notificationTitle: '违约行为提醒',
    cardTitle: '违约情况',
  },
  BROKE_CONTRACT_RECORD_PUNISH_NOTICE: {
    id: 4,
    notificationTitle: '违约判罚提醒',
    cardTitle: '违约情况',
  },
  ISSUE: {
    id: 5,
    notificationTitle: '发布提醒',
    cardTitle: '发布提醒',
  },
  // 由于消息中心的平台公告不属于后端规划的消息提醒接口范围内，故需前端自动义msgType值
  PLATFORM: {
    id: -1,
    notificationTitle: '消息提醒',
    cardTitle: '通知公告',
  },
  COMMERCIAL_AUDIT_NOTICE: {
    id: 6,
    notificationTitle: '消息提醒',
    cardTitle: '审核结果提醒',
  },
  SIGN_PROTOCOL: {
    id: 8,
    notificationTitle: '签署提醒',
    cardTitle: '签署提醒',
  }
})

// 平台消息通知的消息类型
export const OPERATION_MESSAGE_MSGTYPE = {
  // 更新票据库存通知
  update_inventory: {
    id: 'update_inventory',
    showCloseTime: true, // 是否显示弹窗关闭倒计时
    isDelayRandom: true, // 是否需要随机延迟弹出通知窗
  }
}
// 违约等级
export const BREAK_CONTRACT_RANK = Object.freeze({
  NORMAL: {
    id: 0,
    label: '轻',
  },
  MID: {
    id: 1,
    label: '中',
  },
  HARD: {
    id: 2,
    label: '重',
  },
  MIDS: {
    id: 3,
    label: '中',
  },
})

// 常量-接口节流时间
export const MQTT_REFRESH_TIME = 100
// 常量-消息弹窗频率
export const MQTT_ALERT_TIME = 5 * 1000
