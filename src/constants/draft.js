// 发布票据、定向操作常量
// import {
//   getKeyToValueMap // 获取映射信息
// } from './utils'

// 极速订单code 0-非光订单  1-极速订单
export const DRAFT_FAST_TRADE_CODE = Object.freeze({
  FAST_TRADE: 1, // 非光订单
  NOT_FAST_TRADE: 0 // 极速订单
})

// 发布票据错误code
export const ISSUE_DRAFT_ERROR_CODE = Object.freeze({
  REPEAT_REGISTERED: 1010, // 手机号码已经注册
  BUYER_NOT_EXITS: 1011, // 对方不存在
  MARGIN_NOT_SUPPORT: 1012, // 米余额不足
  BUYER_NOT_OPEN: 1013, // 对方未开户
  REAL_NAME_EXPIRED: 1014, // 实名已失效
  ORDER_EXIT_ACCEPT_RISK: 1015, // 存在承兑风险
  PAY_CHANNEL_BUYER_NOT_SUPPORT: 1016, // 支付方式买方不支持
  SELLER_LIMIT_TRADE_ON_CANCEL_ORDER_PENDING_CONFIRM: 3018, // 您已在确认环节取消此票据三次,今日该票在此户限制发布。
  CREDIT_TYPE_GENERAL: 3026, // 信用等级一般,
  OPPOSITE_CREDIT_TYPE_GENERAL: 3027, // 对方信用等级一般,
  PAY_CHANNEL_FAIL: 1018, // 支付渠道失效
  GAO_MAI_DI_MAI: 5036, // 高买低卖
  MAX_LIMIT_REACHED: 3038, // 出票量已经达到限额
  OTHER_MAX_LIMIT_REACHED: 3039, // 对方接单量已达到限额
  RECEIVE_MAX_LIMIT_REACHED: 3040, // 接单达到限额
})

// 票据类型
export const DRAFT_TYPE_CODE = Object.freeze({
  SILVER_NOTE: 1, // 银票
  COMMERCIAL_TICKET: 2, // 商票
  FINANCIAL_TICKET: 3, // 财票
})

export const INTEGER_SPLIT_AMOUNT = [
  { id: null, label: '--' },
  { id: '1', label: '1万' },
  { id: '5', label: '5万' },
  { id: '10', label: '10万' }
]
