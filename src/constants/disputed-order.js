// 争议订单
import {
  getKeyToValueMap // 获取映射信息
} from './utils'

// 争议订单来源
export const DISPUTE_SOURCE = Object.freeze({
  SALE: {
    id: 1,
    name: '票方',
  },
  BUY: {
    id: 2,
    name: '资方',
  },
  SERVICE: {
    id: 3,
    name: '客服',
  },
})

export const DISPUTE_SOURCE_VALUE_MAP = getKeyToValueMap(DISPUTE_SOURCE)

// 违约方类型
export const DUTY_PARTY_TYPE = Object.freeze({
  SALE: {
    id: 2,
    name: '票方',
  },
  BUY: {
    id: 1,
    name: '资方',
  },
  BOTH_DUTY: {
    id: 3,
    name: '双方违约',
  },
  BOTH_NOT_DUTY: {
    id: 4,
    name: '双方无责',
  },
})

export const DUTY_PARTY_TYPE_VALUE_MAP = getKeyToValueMap(DUTY_PARTY_TYPE)

// 违约原因类型
export const DUTY_REASON_TYPE = Object.freeze({
  NO_ENDORSE: {
    id: 1,
    name: '超时未背书',
  },
  BEFORE_COMPLETE_ENDORSE: {
    id: 2,
    name: '票方提前操作完成背书',
  },
  FALSE_ENDORSE: {
    id: 3,
    name: '票方实际未背书，坚持声称已背书',
  },
  DRAFT_DEFECT_DISCREPANCY: {
    id: 4,
    name: '票面瑕疵不符',
  },
  ENDORSE_ERROR_DRAFT_NO: {
    id: 5,
    name: '背错票号',
  },
  DRAFT_INFO_ERROR: {
    id: 6,
    name: '票面信息错误',
  },
  ACCOUNT_ENDORSE_ERROR: {
    id: 7,
    name: '错户背书',
  },
  ENDORSE_ERROR_BANK: {
    id: 8,
    name: '背错银行',
  },
  ENDORSE_ERROR_ACCOUNT: {
    id: 9,
    name: '背错账户',
  },
  NON_NEGOTIABLE: {
    id: 10,
    name: '不可转让',
  },
  TIMEOUT_AND_DRAFT_DEFECT_DISCREPANCY: {
    id: 11,
    name: '超时未签收，票面瑕疵不符',
  },
  MALICIOUS_FORGERY: {
    id: 12,
    name: '恶意伪造',
  },
  TIMEOUT_AND_DRAFT_DEFECT_MATCH: {
    id: 13,
    name: '超时未签收，票面瑕疵相符',
  },
  OUTSTANDING_PAYMENT: {
    id: 14,
    name: '签收未解付',
  },
  REFUSAL_WITHOUT_REASON: {
    id: 15,
    name: '票面瑕疵相符，无理由拒签',
  },
  NON_COMPLIANCE_WITH_RULES: {
    id: 16,
    name: '蓄意不遵守业务规则',
  },
  UNJUST_ENRICHMENT: {
    id: 17,
    name: '不当得利',
  },
  USING_PLUG_INS: {
    id: 18,
    name: '使用外挂',
  },
  OTHER: {
    id: 19,
    name: '其他',
  },
})
export const DUTY_REASON_TYPE_VALUE_MAP = getKeyToValueMap(DUTY_REASON_TYPE)

// 争议订单处理状态
export const DUTY_HANDLE_STATUS = Object.freeze({
  UN_DEAL: {
    id: 0,
    name: '未处理',
  },
  DEALING: {
    id: 1,
    name: '处理中',
  },
  JD_DEALING: {
    id: 2,
    name: '处理中', // 京东处理中,前端统一显示处理中
  },
  DEAL_DONE: {
    id: 3,
    name: '已处理',
  },
})

export const DUTY_HANDLE_STATUS_VALUE_MAP = getKeyToValueMap(DUTY_HANDLE_STATUS)

// 争议订单处理结果
export const DISPUTED_HANDLE_RESULT = Object.freeze({
  RECOVERY_TRANSACTION: {
    id: 9,
    name: '恢复交易',
  },
  RECOVERY_CANCEL: {
    id: 10,
    name: '恢复取消',
  },
  FORCE_CANCEL: {
    id: 11,
    name: '强制撤单',
  },
  FORCE_PAYMENT: {
    id: 12,
    name: '强制解付',
  },
})
export const DISPUTED_HANDLE_RESULT_VALUE_MAP = getKeyToValueMap(DISPUTED_HANDLE_RESULT)

// 争议订单操作类型
export const DISPUTED_HANDLE_TYPE = Object.freeze({
  BUYER_UPLOAD_VOUCHER: {
    id: 3,
    name: '(资方)上传凭证',
  },
  SELLER_UPLOAD_VOUCHER: {
    id: 4,
    name: '(票方)上传凭证',
  },
  SYSTEM_UPLOAD_VOUCHER: {
    id: 5,
    name: '(客服)上传凭证',
  },
  BUYER_APPLY_DISPUTED: {
    id: 6,
    name: '(资方)申请客服介入',
  },
  SELLER_APPLY_DISPUTED: {
    id: 7,
    name: '(票方)申请客服介入',
  },
  SYSTEM_LAUNCH_DISPUTED: {
    id: 8,
    name: '(客服)强推争议',
  }
})
export const DISPUTED_HANDLE_TYPE_VALUE_MAP = getKeyToValueMap(DISPUTED_HANDLE_TYPE)
