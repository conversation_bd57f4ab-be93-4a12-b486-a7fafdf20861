// 存放项目常量
import { ossUrl } from '@/constants/common'
import { getKeyToValueMap, getMapToArray } from './utils'

// 语言
export const LANGUAGE_DATA = {
  CN: {
    name: { zh: '中文', en: 'Chinese' },
    id: '1',
  },
  ENGLISH: {
    name: { zh: '英文', en: 'English' },
    id: '2',
  },
}
// 语言映射成数组
export const LANGUAGE_DATA_ARRAY = getMapToArray(LANGUAGE_DATA)

// 视频管理状态
export const VIDEO_STATUS = {
  ALL: {
    name: { zh: '全部', en: 'All' },
    id: '-1',
  },
  GENERATING: {
    name: { zh: '生成中', en: 'Generating' },
    id: '0',
  },
  COMPLETED: {
    name: { zh: '已完成', en: 'Completed' },
    id: '1',
  },
  FAILED: {
    name: { zh: '生成失败', en: 'Failed' },
    id: '2',
  },
  WAITING: {
    name: { zh: '等待中', en: 'Waiting' },
    id: '3',
  },
}
export const VIDEO_STATUS_ARRAY = getMapToArray(VIDEO_STATUS)
export const VIDEO_STATUS_MAP = getKeyToValueMap(VIDEO_STATUS)

// 文本类型
export const INFER_TYPE = {
  TEXT: {
    name: { zh: '文字输入', en: 'Text Input' },
    id: 1,
  },
  VOICE: {
    name: { zh: '上传语音', en: 'Upload Voice' },
    id: 2,
  },
}
export const INFER_TYPE_ARRAY = getMapToArray(INFER_TYPE)

// 位置
export const NUMBER_HUMAN_POSITION = {
  CENTER: {
    name: { zh: '居中', en: 'Center' },
    id: 1,
  },
  LEFT: {
    name: { zh: '居左', en: 'Left' },
    id: 2,
  },
  RIGHT: {
    name: { zh: '居右', en: 'Right' },
    id: 3,
  },
}
export const NUMBER_HUMAN_POSITION_ARRAY = getMapToArray(NUMBER_HUMAN_POSITION)
export const NUMBER_HUMAN_POSITION_MAP = getKeyToValueMap(NUMBER_HUMAN_POSITION)

// 登录方式
export const LOGIN_WAY = Object.freeze({
  PASSWORD: {
    name: { zh: '账号登录', en: 'Account login' },
    id: 0,
  },
  CODE: {
    name: { zh: '验证码登录', en: 'Verification code login' },
    id: 1,
  },
  RESET_PASSWORD: {
    name: { zh: '重置密码登录', en: '' },
    id: 2,
  },
})
// 登录方式
export const LOGIN_WAY_ARRAY = getMapToArray(LOGIN_WAY)

// 验证码发送类型
export const SMS_SEND_WAY = Object.freeze({
  REGISTER: {
    name: '前台注册',
    id: 1,
  },
  LOGIN: {
    name: '前台登陆',
    id: 2,
  },
  FIND_PASSWORD: {
    name: '前台找回密码',
    id: 3,
  },
  LEAVE_MESSAGE: {
    name: '逛市场留言',
    id: 5,
  },
})
// 验证码发送类型
export const SMS_SEND_WAY_ARRAY = getMapToArray(SMS_SEND_WAY)

// 临沂商城 · 中国大集
export const LINYI_SHANGCHENG_CHINA_MARKET = {
  ZH: {
    name: '临沂商城 · 中国大集',
    id: 'zh',
  },
  EN: {
    name: 'Linyi Trade City · China Market',
    id: 'en',
  },
  AR: {
    name: 'مدينة ليني التجارية · سوق الصين الكبرى',
    id: 'ar',
  },
}
export const LINYI_SHANGCHENG_CHINA_MARKET_MAP = getKeyToValueMap(LINYI_SHANGCHENG_CHINA_MARKET)

//商品详情图片
export const EXPORT_TO_DOMESTIC_IMG = {
  zh: `${ossUrl}/export-to-domestic/product-detail-中文.png`,
  en: `${ossUrl}/export-to-domestic/product-detail-英文.png`,
  ar: `${ossUrl}/export-to-domestic/product-detail-阿拉伯.png`,
  thai: `${ossUrl}/export-to-domestic/product-detail-泰.png`, //泰语
  indonesian: `${ossUrl}/export-to-domestic/product-detail-印度.png`, //印度尼西亚
  ru: `${ossUrl}/export-to-domestic/product-detail-俄语.png`, //泰语
  tr: `${ossUrl}/export-to-domestic/product-detail-土耳其语.png`, //土耳其语
}
////出口转内销商品小标志
export const BANNARIMG = {
  zh: `${ossUrl}/export-to-domestic/product-detail-logo-中文.png`,
  en: `${ossUrl}/export-to-domestic/product-detail-logo-英文.png`,
  ar: `${ossUrl}/export-to-domestic/product-detail-logo-阿拉伯.png`,
  thai: `${ossUrl}/export-to-domestic/product-detail-logo-泰语.png`,
  indonesian: `${ossUrl}/export-to-domestic/product-detail-logo-印度尼西亚.png`,
  ru: `${ossUrl}/export-to-domestic/product-detail-logo-俄语.png`,
  tr: `${ossUrl}/export-to-domestic/product-detail-logo-土耳其语.png`,
}
//出口转内销文字图片
export const EXPORT_TO_DOMESTIC_FONT_IMG = {
  zh: `${ossUrl}/export-to-domestic/出口转内销文字-中文.png`,
  en: `${ossUrl}/export-to-domestic/出口转内销文字-英文.png`,
  ar: `${ossUrl}/export-to-domestic/出口转内销文字-阿拉伯.png`,
  thai: `${ossUrl}/export-to-domestic/出口转内销文字-泰.png`, //泰语
  indonesian: `${ossUrl}/export-to-domestic/出口转内销文字-印度尼西亚.png`, //印度尼西亚
  ru: `${ossUrl}/export-to-domestic/出口转内销文字-俄语.png`,
  tr: `${ossUrl}/export-to-domestic/出口转内销文字-土耳其语.png`,
}
