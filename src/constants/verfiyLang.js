export const LANG_ENUM_ARRAY = [
  { 'zh-CN': '简体中文' },
  { 'zh-TW': '台湾繁体中文' },
  { 'zh-HK': '香港繁体中文' },
  { 'en-US': '美式英文' },
  { 'en-GB': '英式英文' },
  { es: '西班牙语（欧洲）' },
  { pt: '葡萄牙语（欧洲）' },
  { fr: '法语' },
  { de: '德语' },
  { ru: '俄语' },
  { it: '意大利语' },
  { ja: '日语' },
  { 'es-la': '拉美西语' },
  { 'pt-br': '巴西葡语' },
  { sv: '瑞典语' },
  { no: '挪威语' },
  { da: '丹麦语' },
  { cs: '捷克语' },
  { hu: '匈牙利语' },
  { sk: '斯洛伐克语' },
  { pl: '波兰语' },
  { ro: '罗马尼亚语' },
  { el: '希腊语' },
  { sr: '塞尔维亚语（拉丁文）' },
  { bs: '波斯尼亚语' },
  { mk: '马其顿语' },
  { bg: '保加利亚语' },
  { fi: '芬兰语' },
  { et: '爱沙尼亚语' },
  { lv: '拉脱维亚语' },
  { lt: '立陶宛语' },
  { sl: '斯洛文尼亚语' },
  { hr: '克罗地亚语' },
  { uk: '乌克兰语' },
  { tr: '土耳其语' },
  { vi: '越南语' },
  { id: '印尼语' },
  { ar: '阿拉伯语' },
  { fa: '波斯语' },
  { nl: '荷兰语' },
  { th: '泰语' },
  { ms: '马来西亚语' },
  { ca: '加泰罗尼亚语' },
  { hi: '印地语' },
  { my: '缅甸语' },
  { ko: '韩语' },
  { he: '希伯来语' },
  { gl: '加利西亚语' },
  { eu: '巴斯克语' },
  { ka: '格鲁吉亚语' },
  { az: '阿塞拜疆语' },
  { uz: '乌孜别克语' },
  { km: '高棉语' },
  { si: '僧伽罗语' },
  { ur: '乌尔都语' },
  { bo: '藏语' },
  { be: '白俄罗斯语' },
  { kk: '哈萨克语（西里尔文）' },
  { bn: '孟加拉语' },
  { lo: '老挝语' },
  { fil: '菲律宾语' },
  { jv: '爪哇语' },
  { ne: '尼泊尔语' },
  { sw: '斯瓦西里语' },
  { mi: '毛利语' },
  { am: '阿姆哈拉语' },
  { te: '泰卢固语' },
  { mr: '马拉地语' },
  { ta: '泰米尔语' },
  { gu: '古吉拉特语' },
  { kn: '卡纳达语' },
  { ml: '马来亚拉姆语' },
  { or: '欧里亚语' },
  { pa: '旁遮普语' },
  { as: '阿萨姆语' },
  { mai: '迈蒂利语' },
  { mn: '蒙古语（西里尔文）' },
  { ug: '维吾尔语' },
]

export const LANG_ENUM = LANG_ENUM_ARRAY.reduce((prev, cur) => {
  Object.keys(cur).map((key) => {
    prev[key] = {
      id: key,
      value: key,
      label: cur[key],
    }
  })
  return prev
}, {})
