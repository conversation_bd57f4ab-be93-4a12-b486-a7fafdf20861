import { getKeyToValueMap, getMapToArray } from '@/constants/utils'

export const MERCHANTS_TYPE = Object.freeze({
  BUYER: {
    id: 1,
    hide: true,
    name: {
      zh: '采购商工作台',
      en: 'Purchaser',
      ar: 'المشتري',
      thai: 'ผู้ซื้อ',
      indonesian: 'Pembeli',
    },
    title: {
      zh: '采购商',
      en: 'Purchaser',
      ar: 'المشتري',
      thai: 'ผู้ซื้อ',
      indonesian: 'Pembeli',
    },
    path: '/buyer-center',
  },
  SELLER: {
    id: 2,
    name: {
      zh: '供应商工作台',
      en: 'Supplier',
      ar: 'المورد',
      thai: 'ผู้ขาย',
      indonesian: 'Penjual',
    },
    title: {
      zh: '供应商',
      en: 'Supplier',
      ar: 'المورد',
      thai: 'ผู้ขาย',
      indonesian: 'Penjual',
    },
    path: '/seller-center',
  },
  BUYER_SERVICE: {
    id: 5,
    name: {
      zh: '采购商服务商工作台',
      en: 'Purchaser Service',
      ar: 'خدمة المشتري',
      thai: 'บริการผู้ซื้อ',
      indonesian: 'Layanan Pembeli',
    },
    title: {
      zh: '采购商服务商',
      en: 'Purchaser Service',
      ar: 'مقدم الخدمة للمشتري',
      thai: 'ผู้ให้บริการสำหรับผู้ซื้อ',
      indonesian: 'Penyedia Layanan Pembeli',
    },
    path: '/buyer-service-center',
  },
  MERCHANT_SERVICE: {
    id: 4,
    name: {
      zh: '供应商服务商工作台',
      en: 'Supplier Service',
      ar: 'خدمة المورد',
      thai: 'บริการผู้ขาย',
      indonesian: 'Layanan Penjual',
    },
    title: {
      zh: '供应商服务商',
      en: 'Supplier Service',
      ar: 'مقدم الخدمة للمورد',
      thai: 'ผู้ให้บริการสำหรับผู้ขาย',
      indonesian: 'Penyedia Layanan Penjual',
    },
    path: '/merchant-services-center',
  },
  FOREIGN_TRADE_SERVICE: {
    id: 3,
    name: {
      zh: '外综服服务商工作台',
      en: 'Trade Service',
      ar: 'خدمات التجارة الخارجية الشاملة',
      thai: 'บริการการค้านานาชาติ',
      indonesian: 'Layanan Perdagangan Komprehensif',
    },
    title: {
      zh: '外综服服务商',
      en: 'Trade Service Provider',
      ar: 'مقدم خدمات التجارة الخارجية الشاملة',
      thai: 'ผู้ให้บริการการค้าระหว่างประเทศ',
      indonesian: 'Penyedia Layanan Perdagangan Komprehensif',
    },
    path: '/service-center',
  },
})
export const MERCHANTS_TYPE_MAP = getKeyToValueMap(MERCHANTS_TYPE)
// 注册类型数组
export const MERCHANTS_TYPE_ARRAY = getMapToArray(MERCHANTS_TYPE)

export const IS_MERCHANTS_BACKEND = 'isMerchantsBackend'
