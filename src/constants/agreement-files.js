import { ossUrl } from '@/constants/common'

// 注册服务协议oss地址
export const REGISTER_FILE_ADDRESS = {
  zh: `${ossUrl}/file/注册服务协议-中国大集.pdf`,
  en: `${ossUrl}/file/【英文】注册服务协议.pdf`,
  ar: `${ossUrl}/file/【Arabic】注册服务协议.pdf`,
  thai: `${ossUrl}/file/【Thai】注册服务协议.pdf`,
  indonesian: `${ossUrl}/file/【Indonesian】注册服务协议.pdf`,
  ru: `${ossUrl}/file/【英文】注册服务协议.pdf`,
  tr: `${ossUrl}/file/【英文】注册服务协议.pdf`,
}

// 隐私政策oss地址
export const PRIVACY_FILE_ADDRESS = {
  zh: `${ossUrl}/file/隐私政策-中国大集.pdf`,
  en: `${ossUrl}/file/【英文】隐私政策.pdf`,
  ar: `${ossUrl}/file/【Arabic】隐私政策.pdf`,
  thai: `${ossUrl}/file/【Thai】隐私政策.pdf`,
  indonesian: `${ossUrl}/file/【Indonesian】隐私政策.pdf`,
  ru: `${ossUrl}/file/【英文】隐私政策.pdf`,
  tr: `${ossUrl}/file/【英文】隐私政策.pdf`,
}
