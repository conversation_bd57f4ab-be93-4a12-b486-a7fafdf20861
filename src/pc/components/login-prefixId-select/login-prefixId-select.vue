<style lang="scss" scoped>
.search-input {
  border-radius: 20px;
  border: 1px solid #eee;
  background: #f6f6f6;
  overflow: hidden;

  :deep() {
    .el-input__wrapper {
      box-shadow: none !important;
      background: #f6f6f6;
    }
  }
}

.pb-safe {
  padding-bottom: calc(12px + constant(safe-area-inset-bottom));
  padding-bottom: calc(12px + env(safe-area-inset-bottom));
}
</style>

<template>
  <van-action-sheet v-model:show="show" title="选择国家或地区">
    <template #description>
      <el-input v-model="searchVal" clearable class="search-input" placeholder="搜索">
        <template #prefix>
          <el-icon class="el-input__icon"><search /></el-icon>
        </template>
      </el-input>
    </template>
    <div class="h-[60vh] flex flex-col overflow-hidden">
      <div class="px-[10px] overflow-auto pb-safe">
        <div
          v-for="(item, i) in actions"
          :key="i"
          @click="handleSelect(item)"
          class="h-[40px] flex items-center justify-between border-b-[1px] border-b-[#eee] border-b-solid last:border-none"
          :class="item.id === modelValue ? 'text-[#D33232] font-600' : 'text-[#666]'"
        >
          <div>{{ item.desc }}</div>
          <div>+{{ item.prefix }}</div>
        </div>
      </div>
      <el-empty v-if="actions.length === 0"></el-empty>
    </div>
  </van-action-sheet>
</template>

<script setup>
import { Search } from '@element-plus/icons-vue'
import { computed, ref } from 'vue'

const props = defineProps({
  prefixIdList: {
    type: Array,
    default: () => [],
  },
  modelValue: {
    type: String,
    require: true,
  },
})

const emits = defineEmits(['update:modelValue'])
const show = ref(false)
const searchVal = ref('')
const actions = computed(() => {
  return props.prefixIdList.filter((item) => {
    const val = searchVal.value?.trim()?.toLowerCase()
    if (val) {
      return String(item?.prefix).includes(val) || item?.desc?.toLowerCase()?.includes(val)
    }
    return item
  })
})
const handleSelect = (item) => {
  emits('update:modelValue', item.id)
  show.value = false
}
const showModal = () => {
  show.value = true
}
defineExpose({
  showModal,
})
</script>
