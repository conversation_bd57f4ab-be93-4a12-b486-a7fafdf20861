<!-- 二维码客服 -->
<style lang="scss" scoped>
.code {
  position: fixed;
  top: 85vh;
  right: 17px;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  user-select: none;
  cursor: pointer;
  .chant-logo {
    // width: 36px;
    // height: 40px;
    background: #fff;
    width: 64px;
    height: 64px;
    border-radius: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .chant-msg {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 24px;
    border-radius: 200px;
    background: #fff;
    padding: 0px 6px;
    font-size: 14px;
    letter-spacing: 0em;
    color: #333;
  }
}
.code-box {
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>
<style lang="scss">
.chat-dialog {
  .el-dialog {
    width: 260px;
    //height: 274px;
    border-radius: 8px;
    background: linear-gradient(0deg, #faf5f5 0%, #ffeded 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 0;

    // justify-content: end;
  }
  .code-cont {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .code-tip {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 12px;
    .code-tip-msg {
      padding: 4px 16px;
      background: #d8131a;
      font-size: 16px;
      font-weight: 600;
      line-height: normal;
      text-align: center;
      letter-spacing: 0em;
      color: #fff;
      border-radius: 20px;
      z-index: 10;
    }
    .code-tip-arrow {
      width: 12px;
      height: 12px;
      background: #d8131a;
      box-sizing: border-box;
      content: ' ';
      transform: rotate(45deg);
      border-bottom-right-radius: 2px;
      position: absolute;
      bottom: -6px;
      z-index: 1;
    }
  }
}
</style>

<template>
  <div>
    <div class="code" @click="openChat">
      <div class="chant-logo">
        <img src="https://static.chinamarket.cn/static/trade-exhibition/chat-logo.png" class="w-[36px] h-[40px] object-cover" alt="" />
      </div>
      <div class="chant-msg">联系客服</div>
    </div>
    <el-dialog v-model="dialogVisible" width="260px" title="" :show-close="false" modal-class="chat-dialog">
      <div class="code-cont">
        <div class="code-box">
          <div class="code-tip">
            <div class="code-tip-msg">微信扫码添加直播客服</div>
            <div class="code-tip-arrow"></div>
          </div>
          <div class="code_img">
            <img-loader
              src="https://static.chinamarket.cn/static/trade-exhibition/qr-code1.png?v=1.0"
              img-class="w-[176px] h-[176px] object-cover"
              alt=""
            ></img-loader>
          </div>
        </div>
        <Icon type="icon-guanbi" :size="16" @click="onCloseDialog"></Icon>
      </div>
    </el-dialog>
  </div>
  <main class="preview-video-dialog"></main>
</template>

<script setup lang="jsx">
const dialogVisible = ref(false)
const openChat = () => {
  dialogVisible.value = true
}
const onCloseDialog = () => {
  dialogVisible.value = false
}

defineExpose({})
</script>
