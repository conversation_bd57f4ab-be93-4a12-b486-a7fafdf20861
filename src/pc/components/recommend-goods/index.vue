<!--
 * @Author: 李兵 <EMAIL>
 * @Date: 2024-08-13 15:15:17
 * @LastEditors: your name
 * @LastEditTime: 2024-12-21 10:32:07
 * @FilePath: /trade-exhibition/src/pc/components/recommend-goods/index.vue
 * @Description: 为您推荐
-->
<template>
  <div class="space-index-offerlist" :class="`m-${scene}`">
    <div class="main-title relative" v-if="showTitle">
      <span class="title-text text-[24px] font-600">
        <slot name="title">{{ $t('mall.recommended') }}</slot>
      </span>
      <!--      <div class="look-more-btn" @click="onMore">-->
      <!--        <span class="look-more">{{ $t('market.more') }}</span>-->
      <!--        <div class="icon-box">-->
      <!--          <icon type="icon-lujing" :size="6" />-->
      <!--          <icon type="icon-lianji" :size="12" />-->
      <!--        </div>-->
      <!--      </div>-->
      <slot name="content"></slot>
    </div>
    <div class="bg-white rounded-4 p-4 goods-list-content">
      <RecommendGoodsTab @uploadData="queryGoodsList" v-if="isShowtab"></RecommendGoodsTab>
      <div class="goods-list" :class="{ 'no-data': goodsList.length === 0 }">
        <GoodsDetailNewCard v-for="(item, index) in goodsList" :key="item.id + index" :goodsInfo="item" :showSaleNum="true" :scene="scene"></GoodsDetailNewCard>
        <el-empty v-if="!goodsList.length && !isLoading && showEmpty" :image-size="200"></el-empty>
      </div>
    </div>
    <div v-if="isLoading" class="py-[24px] text-center color-[#999] text-[24px] w-[100%] h-[400px]">
      <el-icon class="loading-icon"><Loading></Loading></el-icon>
    </div>
    <div v-else-if="goodsList.length && isEnd" class="text-center color-[#999] mb-[24px]">- {{ t('noMore') }} -</div>
  </div>
</template>

<script setup>
import { Loading } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import GoodsDetailNewCard from '@/pc/components/goods-detail-card/goods-detail-card-new.vue'
import RecommendGoodsTab from '@/pc/components/recommend-goods-tab/recommend-goods-tab.vue'
import { PRODUCT_SUGGEST_TYPE } from '@/constants/goods'
import { getDictListByKey } from '@/apis/goods'
import * as API from '@/apis/mall'
import { setItemRIdRorList } from '@/utils/utils'

const { t } = useI18n({
  messages: {
    zh: {
      noMore: '没有更多',
    },
    en: {
      noMore: 'No More',
    },
  },
})

const props = defineProps({
  suggest: {
    type: Number,
    default: PRODUCT_SUGGEST_TYPE.SUGGEST.id,
  },
  showEmpty: {
    type: Boolean,
    default: true,
  },
  scene: {
    type: String,
    default: '',
  },
  isMall: {
    type: Boolean,
    default: false,
  },
  showTitle: {
    type: Boolean,
    default: true,
  },
  // 自定义参数
  paramsObj: {
    type: Object,
    default: () => {},
  },
})
const goodsList = ref([])
const pagination = reactive({
  pageNum: 1,
  pageSize: 20,
  total: 0,
})
const isLoading = ref(false)
const isEnd = ref(false)
const newRid = ref(null)
const tabParams = ref({})
const queryGoodsList = (params = {}) => {
  console.log('queryGoodsList', params)
  newRid.value = null
  Object.assign(pagination, {
    pageNum: 1,
  })
  isEnd.value = false
  tabParams.value = params
  getGoodsList()
}

const getGoodsList = async () => {
  if (isEnd.value) return
  isLoading.value = true
  let paramsData = {}
  if (props.scene === 'cart_success_page') {
    paramsData = {
      pageSize: pagination.pageSize,
      pageNum: pagination.pageNum,
      orderAsc: 1,
      labelIds: [],
    }
  } else if (isShowtab.value) {
    paramsData = {
      pageSize: pagination.pageSize,
      pageNum: pagination.pageNum,
      rid: newRid.value,
      ...tabParams.value,
      ...props.paramsObj,
    }
  } else {
    paramsData = {
      pageSize: pagination.pageSize,
      pageNum: pagination.pageNum,
      labelType: props.suggest,
      ...props.paramsObj,
    }
  }
  console.log('paramsData', paramsData, tabParams.value)
  //const res = await API.getNewHomeGoodsList(paramsData)
  const callbackParams = (data) => {
    const rowList = typeof data === 'object' && data?.rowList ? data.rowList : []
    return {
      goods_ids: JSON.stringify(rowList.map((item) => item.id)),
    }
  }
  const res =
    isShowtab.value || props.scene === 'cart_success_page'
      ? await API.getRecommendList(paramsData, { scene: props.scene, callback: callbackParams })
      : await API.getNewHomeGoodsList(paramsData)

  if (res) {
    const { rowList, rid, totalRecord } = res

    newRid.value = rid || ''
    if (pagination.pageNum === 1) {
      goodsList.value.length = 0
    }
    const arr = setItemRIdRorList(rowList, rid) || []
    goodsList.value.push(...arr)
    isLoading.value = false
    if (isShowtab.value) {
      let newtotal = ref(0)
      if (!rowList || rowList.length < 20) {
        newtotal.value = goodsList.value.length
      }
    }

    if (totalRecord > goodsList.value.length) {
      Object.assign(pagination, {
        pageNum: pagination.pageNum + 1,
      })
    } else {
      isEnd.value = true
    }
  }
}

const doScroll = () => {
  const scrollTop = window.scrollY || document.documentElement.scrollTop || document.body.scrollTop
  const scrollHeight = window.scrollHeight || document.documentElement.scrollHeight || document.body.scrollHeight
  const clientHeight = window.clientHeight || document.documentElement.clientHeight || document.body.clientHeight

  if (scrollTop + clientHeight >= scrollHeight - 280) {
    !isLoading.value && getGoodsList()
  }
}
//增加开关，判断是否显示为你推荐的tab
const isShowtab = ref(false)
const getShowTab = async () => {
  let res = await getDictListByKey({ dictKey: 'config_switch' })
  console.log('data', res)
  if (res && res[0] && res[0].al_recommend_switch == '1' && props.isMall) {
    isShowtab.value = true
  }
}
onMounted(async () => {
  await getShowTab()
  getGoodsList()

  document.addEventListener('scroll', doScroll)
})

onUnmounted(() => {
  document.removeEventListener('scroll', doScroll)
})

defineExpose({
  queryGoodsList,
})
</script>

<style lang="scss" scoped>
.goods-list-content {
  width: $main-width;
  min-height: 400px;
}
.m-cart_success_page {
  .bg-white {
    background: none;
  }
  .goods-list-content {
    width: 100%;
  }
  .p-4 {
    padding: 0;
  }
}
.goods-list {
  // overflow: auto;
  width: 100%;
  margin: 0 auto 24px;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 10px;

  & > div {
    max-width: 244px;
  }

  &.no-data {
    display: flex;
    justify-content: center;
  }
}

.main-title {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24px 0;
  font-family: PingFang SC;

  &::before,
  &::after {
    content: '';
    display: block;
    width: 45px;
    height: 16px;
    margin: 0 12px;
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAAAgCAYAAACSEW+lAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAK7SURBVGiB7ZmxTttQFIb/c2+MQ6hCY0ETGEAqrpAqeI8sWSt1ZGPtG+QNWNkYkViz8B6gSiihUjoAKchpoxIcnHtPBxKIAjK+DikM9xuv/uP/+sjy9fFPGFAFxHaxOHt2Tbls1s0AQBj2+stz3N1ttW6qgMYLUIfvhoXOuhRyDYI8AIDmQGl1mm3nTz6h0XsRH8/LK+FWwKIM8NrdKp2C9KHUvdqnIOi8hE9zfqXQdaItYvoKYGOwfMzE+7nI2Vv987MNAAQADMhGqeSpvuM8dTGZiSL/4iIgQE2yqV+Li+8CJcs8bPAYpDnwpDr8cHn5dxKf7wsLS0LP7IDgPylgNLS4/fb56up8Ep8f75dWbwVqINp82oePZjQqH3+fN0UVEHFNBgDVd5xGqeRVAZF2U3X4blyTAYAFeYGS5Tp8N7WP5+VjmwwABF/omZ265+XT+jTnVwqxTQYAos1bgVpzfqUgtovF2bgmD1F9x9kuFmfTbiwsdNbjmjyEBXlhobOe1kcJtxLb5CEEXwm3ktan60RbsU2+96HNrhNtibNryiW9uIl2HCnk2jS0j2BRnop2jME7ObFWDA++JJhoR2GAkOBpvsdEO8IBIB8OviSYaEeqAImHgy8JG6nfuRYzRBj2+knFJtpRCGBoDhIXmGhH+AIogE6TV5hoR6ruvr6ODUqOxfIcd5OqTbTjKK0S35SJ9hGkD6eiHYOJ9020YrfVupGZKHpOLDNRtNtq3aTdWLadP6EETyppDrLt/ElaH6l7NTAazwoZDal7tbQ+ucjZA/PR8z58lIucPTuwTIDJwELDtaodwVORdAR/VRggHvwGmCYHgLz79JsuDEj+Dz4Wi8VisVgsllfHDiwTYjPDNzaC28wwJTYztJmhzQxtZmiIzQzfMDYzTIHNDAGbGdrMEHZgSYvNDPH2RvB/dk+ggddFU7AAAAAASUVORK5CYII=')
      no-repeat center center/100% 100%;
  }

  &::after {
    transform: rotate(180deg);
  }
}

.look-more-btn {
  position: absolute;
  right: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  color: $color-999999;
  .icon-box {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: $color-999999;
    color: $basic-white;
    text-align: center;
    margin-left: 4px;
    // line-height: 12px;
    transition: width 0.5s ease-in-out;

    .icon-lujing {
      display: block;
      margin: 5px auto 0;
      transition: all 0.5s ease-in-out;
    }
    .icon-lianji {
      display: none;
      transition: width 0.5s ease-in-out;
    }
  }
  &:hover {
    color: $primary-color;

    .icon-box {
      width: 24px;
      border-radius: 8px;
      background: $primary-color;

      .icon-lujing {
        display: none;
      }
      .icon-lianji {
        display: block;
        margin: 2px auto 0;
      }
    }
  }
}

[dir='rtl'] .look-more-btn {
  direction: rtl;
  left: 0;
  right: auto;

  .icon-box {
    transform: rotateZ(-180deg) translateX(8px);
  }
}
</style>
