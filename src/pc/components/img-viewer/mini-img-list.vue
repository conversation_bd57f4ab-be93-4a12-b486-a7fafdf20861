<style lang="scss" scope>
.mini-img-list {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.mini-img-box {
  margin: 0 12px;
  border: 4px solid $basic-white;
  width: 125px;
  height: 80px;
  background: $basic-white;
  box-shadow: 0 0 0 0 $primary-color;
  transition: box-shadow 0.1s;
  cursor: pointer;

  &.active,
  &:hover {
    box-shadow: 0 0 0 6px $primary-color;
  }

  .img {
    width: auto;
    height: 100%;
  }
}
</style>

<template>
  <div class="mini-img-list">
    <div
      v-for="(url, idx) in props.urlList"
      :key="url"
      class="mini-img-box"
      :class="{ active: props.activeIndex === idx }"
      @click="
        () => {
          handleSelect(idx)
        }
      "
    >
      <el-image style="width: 117px; height: 72px" :src="url" fit="cover" />
    </div>
  </div>
</template>

<script setup lang="jsx">
const props = defineProps({
  // 图片数组 ['a.jpg', 'b.png']
  urlList: {
    type: Array,
    default: () => [],
  },
  // 层级
  zIndex: {
    type: Number,
    default: 2000,
  },
  activeIndex: {
    type: Number,
    default: 0,
  },
})

const emit = defineEmits(['index-change'])

// 点击图片
const handleSelect = (idx) => {
  emit('index-change', idx)
}
</script>
