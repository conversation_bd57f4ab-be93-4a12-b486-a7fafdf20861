<style lang="scss" scoped>
.image-viewer-container {
  position: relative;
}

$elImageViewerActionsHeight: 44px; // ElImageView 底部控件高度
$elImageViewerActionsBottom: 48px; // ElImageView 底部控件bottom
$miniImageListMarginBottom: 32px; // miniImageList 距离 ElImageView 底部控件距离
$miniImageListHeight: 80px; // miniImageList 高度
$largeImageMarginBottom: 40px; // 大图可视区域距离下方元素距离
$largeImageMarginHeight: 800px; // 大图可视区域设计高度(非实际可视高度)

// miniImageListBottom bottom值
$miniImageListBottom: $elImageViewerActionsBottom + $elImageViewerActionsHeight + $miniImageListMarginBottom;

// 存在 miniImageList 时 可视区域的 bottom 值
$hasMiniImageListLargeImageBottom: $miniImageListBottom + $miniImageListHeight + $largeImageMarginBottom;

// 不村子 miniImageList 时 可视区域的 bottom 值
$largeImageBottom: $largeImageMarginBottom + $elImageViewerActionsHeight + $elImageViewerActionsBottom;

.mini-img-container {
  position: absolute;
  right: 0;
  bottom: $miniImageListBottom;
  left: 0;
}

:deep() {
  $width: 960px;

  // 有缩略图时的样式
  .show-mini-image {
    .el-image-viewer__canvas {
      bottom: $hasMiniImageListLargeImageBottom;

      @media screen and (min-height: ($largeImageMarginHeight + $hasMiniImageListLargeImageBottom)) {
        top: 200px;
      }
    }
  }

  .el-image-viewer__close {
    top: 32px;
    left: 50%;
    margin-left: calc($width / 2 + 24px);

    @media screen and (min-height: ($largeImageMarginHeight + $largeImageBottom)) {
      top: 160px;
    }
  }

  // 样式修改
  .el-image-viewer__canvas {
    margin-top: 110px;
    margin-left: calc(50vw - ($width / 2));
    width: $width;
    height: auto;
    height: calc(100vh - 332px);

    @media screen and (min-height: ($largeImageMarginHeight + $largeImageBottom)) {
      margin-top: 200px;
    }
  }

  .el-image-viewer__actions {
    bottom: $elImageViewerActionsBottom;
  }

  .no-prev {
    .el-image-viewer__prev {
      display: none;
    }
  }

  .no-next {
    .el-image-viewer__next {
      display: none;
    }
  }
}
</style>

<template>
  <teleport to="body">
    <div
      v-show="props.visible"
      class="image-viewer-container"
      :class="{
        'show-mini-image': props.showMiniImage,
        'no-prev': !props.prevBtn,
        'no-next': !props.nextBtn,
      }"
      :style="{ zIndex: props.zIndex }"
    >
      <ElImageViewer
        v-if="visible"
        ref="elImageViewer"
        :url-list="filterPic"
        :z-index="props.zIndex"
        :initial-index="props.initialIndex"
        :infinite="props.infinite"
        :hide-on-click-modal="props.hideOnClickModal"
        @close="onImageViewerClose"
        @switch="onImageViewerSwitch"
      >
        <div v-if="props.showMiniImage" class="mini-img-container">
          <MiniImgList :active-index="miniIndex" :url-list="filterPic" @index-change="setImageIndex" />
        </div>
      </ElImageViewer>
    </div>
  </teleport>
</template>

<script setup lang="jsx">
import { ElImageViewer } from 'element-plus'
import { computed, ref, watch } from 'vue'
import MiniImgList from './mini-img-list.vue'

const props = defineProps({
  // 图片数组 ['a.jpg', 'b.png']
  urlList: {
    type: Array,
    default: () => [],
  },

  // 层级
  zIndex: {
    type: Number,
    // fix: 解决普通弹窗隐藏预览弹窗的问题，层级不能固定写死
    // default: 2000,
  },

  // 初始展示图片位置
  initialIndex: {
    type: Number,
    default: 0,
  },

  // 是否允许无限循环
  infinite: {
    type: Boolean,
    default: true,
  },

  // 是否允许点击遮罩关闭
  hideOnClickModal: {
    type: Boolean,
    default: false,
  },

  // 显示关闭
  visible: {
    type: Boolean,
    default: false,
  },

  // 上一张按钮
  prevBtn: {
    type: Boolean,
    default: true,
  },

  // 下一张按钮
  nextBtn: {
    type: Boolean,
    default: true,
  },

  // 是否使用迷你缩略图
  showMiniImage: {
    type: Boolean,
    default: false,
  },
})

// 对外抛出的事件
const emit = defineEmits(['update:visible', 'el-image-viewer-mounted'])

// 过滤图片是否为null
const filterPic = computed(() => props.urlList.filter((v) => v))

// 关闭事件
const onImageViewerClose = () => {
  emit('update:visible', false)
  // fix: 重置选中的下标索引。解决多张图片选中某一张关闭弹窗后再打开，展示图片与索引不一致的情况。
  currentIndex.value = props.initialIndex
  miniIndex.value = props.initialIndex
}

// 当前显示图片下标
let currentIndex = ref(props.initialIndex)
// 切换事件
const onImageViewerSwitch = (idx) => {
  currentIndex.value = idx
  miniIndex.value = idx
}

// 小图显示下标
let miniIndex = ref(props.initialIndex)
// elImageViewer 实例
const elImageViewer = ref(null)
// ElImageViewer 拓展方法，设置图片下标
const setImageIndex = (idx) => {
  const prevBtnELe = document.getElementsByClassName('el-image-viewer__prev')[0]
  const nextBtnELe = document.getElementsByClassName('el-image-viewer__next')[0]
  const clickNum = Math.abs(idx - currentIndex.value)
  for (let i = 0; i < clickNum; i++) {
    idx > currentIndex.value ? nextBtnELe.click() : prevBtnELe.click()
  }
  miniIndex.value = idx
}
// 监听 currentIndex 变化 切换 elImageViewer 图片
watch(currentIndex, (idx) => {
  setImageIndex(idx)
})

watch(
  () => props.initialIndex,
  (idx) => {
    currentIndex.value = idx
  },
)

// 阻止事件监听
const handleStopEventListener = (e) => {
  if (!props.visible) return
  if ((e.type === 'keydown' && e.code === 'Escape') || e.type === 'click') {
    onImageViewerClose()
    e.stopImmediatePropagation()
  }
}

// 重写elImageViewer内部的close按钮click事件，只执行关闭弹窗，不执行原来的click事件里的注销事件
// 还有键盘的esc事件
// eslint-disable-next-line no-unused-vars
const handleImageViewerClose = () => {
  // elImageViewer内部的close按钮
  const closeBtn = document.querySelector('.image-viewer-container .el-image-viewer__close')

  // elImageViewer遮罩层
  // const maskWrap = document.querySelector('.image-viewer-container .el-image-viewer__mask')

  // 会触发弹窗关闭的dom元素
  const closeArr = []

  closeBtn && closeArr.push(closeBtn)
  // maskWrap && closeArr.push(maskWrap)

  if (closeArr.length) {
    closeArr.forEach((elm) => {
      elm.addEventListener('click', handleStopEventListener, true)
    })
  }

  // 键盘的esc事件
  document.addEventListener('keydown', handleStopEventListener, true)
}

// 监听elImageViewer的mounted事件
// const onImageViewerMounted = () => {
//   // handleImageViewerClose()
//   emit('el-image-viewer-mounted')
// }

// onBeforeUnmount(() => {
//   document.removeEventListener('keydown', handleStopEventListener)
// })

defineExpose({
  setImageIndex,
})
</script>
