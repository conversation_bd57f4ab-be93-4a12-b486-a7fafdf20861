<!--
 * @Author: 李兵 <EMAIL>
 * @Date: 2024-08-13 14:59:44
 * @LastEditors: your name
 * @LastEditTime: 2024-12-20 11:51:57
 * @FilePath: /trade-exhibition/src/pc/components/search-input/search-input.vue
 * @Description: 搜索组件
-->
<template>
  <div class="search-box-wrap flex">
    <div v-if="isShowGif" class="mr-4 w-[236px] h-[41px] gif-box"></div>
    <div class="alisearch-box flex-1">
      <input v-model.trim="inputValue" :placeholder="t('mall.searchPlaceholder')" type="text" @keyup.enter="onSearch" />
      <div class="search-button" @click="onSearch">
        <i-inside-search style="color: red"></i-inside-search>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'

const { t } = useI18n({
  messages: {
    zh: {
      mall: {
        searchPlaceholder: '搜索商品',
      },
    },
    en: {
      mall: {
        searchPlaceholder: 'Search for goods',
      },
    },
  },
})

const props = defineProps({
  isSearchPage: {
    type: Boolean,
    default: false,
  },
  isShowGif: {
    type: Boolean,
    default: true,
  },
})
const emit = defineEmits(['on-search'])
const inputValue = ref('')
const router = useRouter()
const onSearch = () => {
  // 如果在搜索结果页直接搜索
  if (props.isSearchPage) {
    // 调用父组件方法
    emit('on-search', inputValue.value)
    return
  }
  // 跳转到搜索结果页
  handleToSearchPage()
}
const handleAutoSearch = (val) => {
  inputValue.value = val
  onSearch()
}
// 去搜索结果页（goodsList页面）搜索
const handleToSearchPage = () => {
  if (!inputValue.value || !inputValue.value.trim()) {
    return
  }
  const url = router.resolve({
    path: '/mall/goods-list',
    query: {
      keyword: inputValue.value,
    },
  })
  window.open(url.href, '_blank')
}
const clear = () => {
  inputValue.value = ''
}
defineExpose({
  inputValue,
  handleAutoSearch,
  clear,
})
</script>

<style lang="scss" scoped>
.search-box-wrap {
  width: $main-width;
  .alisearch-box {
    border: none;
    height: 100%;
    position: relative;
    input {
      box-sizing: border-box;
      width: 100%;
      height: 40px;
      border-radius: 8px;
      font-size: 18px;
      outline: 0;
      border: 1px solid $primary-color;
      background-color: #fff;
      font-weight: 400;
      padding-left: 24px;
      padding-right: 181.5px;
      text-overflow: ellipsis;
      color: #333;
    }
    .search-button {
      cursor: pointer;
      position: absolute;
      right: 8px;
      top: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 24px;
      height: 24px;
      border-radius: 4px;
      color: red;
      // background-image: linear-gradient(270deg, #d8131a 0%, #fd441f 100%);

      ::v-deep {
        path {
          fill: #d8131a;
        }
      }
    }
  }
}

.gif-box {
  background: url('https://static.chinamarket.cn/static/trade-exhibition/mall/mall-header-logo.gif') no-repeat;
  background-size: cover;
}
[dir='rtl'] {
  .search-box-wrap {
    .alisearch-box {
      input {
        padding-right: 24px;
        padding-left: 181.5px;
      }

      .search-button {
        left: 8px;
        right: auto;
      }
    }
  }
}
</style>
