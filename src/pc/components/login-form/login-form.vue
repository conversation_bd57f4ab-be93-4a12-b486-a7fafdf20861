<style lang="scss" scoped>
.decoration-wrapper {
  width: 100%;
  height: 8px;
  border-radius: 8px 8px 0 0;
  background: $primary-color;
  margin-bottom: 24px;
}
.logo-wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
  .logo {
    height: 60px;
    margin-right: 8px;
  }
}
.login-content-wrapper {
  padding: 0 40px 40px;

  .title {
    text-align: center;
    font-size: 16px;
    font-weight: 600;
    color: $color-999999;
    margin-bottom: 40px;

    .title-item {
      position: relative;

      &::after {
        position: absolute;
        bottom: -10px;
        left: 50%;
        margin-left: -16px;
        display: none;
        content: '';
        width: 32px;
        border-top: 2px solid $primary-color;
      }

      &.active-item {
        color: $primary-color;
        font-weight: 600;

        &::after {
          display: block;
        }
      }
    }
  }
  .register-active {
    background: #ffeded;
    border: 1px solid #d8131a;
    color: #d8131a;
  }
}
.login-wrapper {
  position: relative;
  border-radius: 8px;
  background-color: #fff;
  .header {
    width: 100%;
    height: 26px;
  }
}

.login-btn {
  width: 100%;
  height: 48px;
  background: $primary-color;
  text-align: center;
  line-height: 48px;
  font-size: 16px;
  font-weight: 600;
  color: $basic-white;
  margin-bottom: 16px;
  cursor: pointer;
  padding: 0;
}

:deep() {
  .el-form-item--label-top .el-form-item__label {
    line-height: 20px;
    margin-bottom: 4px;
    color: $color-999999;
  }
  .el-form-item {
    margin-bottom: 16px;
  }

  .el-select__suffix .el-input__validateIcon {
    display: none;
  }

  .el-input-group__prepend {
    background: #fff;
    padding: 0;

    .el-select {
      margin: 0;
    }

    .el-select__wrapper {
      border: none;
      box-shadow: none !important;
      padding: 0 8px 0 15px !important;
    }

    .el-select__wrapper.is-disabled .el-select__selected-item {
      color: rgb(51, 51, 51);
    }
  }

  .el-checkbox {
    &.is-checked {
      .el-checkbox__inner {
        background: $primary-color;
        border-color: $primary-color;
      }
      .el-checkbox__label {
        color: $primary-color;
      }
    }

    &.el-checkbox--large {
      height: max-content;
    }

    .el-checkbox__label {
      white-space: pre-wrap;
      word-break: break-all;
      line-height: 1.3;
    }
  }
}
[dir='rtl'] .login-content-wrapper {
  :deep(.el-form-item__label) {
    text-align: right;
    &::before {
      margin-right: 0;
      margin-left: 4px;
    }
  }
}
.get-code-wrapper {
  .get-code-btn {
    padding: 0 5px;
    min-width: 120px;
    height: 40px;
    font-size: 14px;
    font-weight: 600;
    text-align: center;
    line-height: 0;
    color: $basic-white;
    background: $primary-color;
    border: none;

    &.is-disabled {
      background: $color-C4C4C4 !important;
    }
  }
}
.link-text {
  font-size: 14px;
  color: $primary-color;
}
</style>

<template>
  <div class="login-wrapper" :key="storageLocale">
    <template v-if="!isForm">
      <div class="decoration-wrapper"></div>
      <div class="header flex justify-between items-center mb-2 px-10">
        <div class="cursor-pointer text-base c-#999">
          <span v-if="isResetPassword" @click="goBack"
            ><icon v-if="storageLocale !== 'ar'" class="mr-1" type="icon-fh" :size="16" /><icon v-else class="mr-1" type="icon-xiala1" :size="16" />{{
              t('backToLogin')
            }}</span
          >
        </div>
        <slot name="headerRight"></slot>
      </div>
    </template>
    <slot name="headerLogo" :isRegister="isRegister" :isResetPassword="isResetPassword"></slot>
    <div class="login-content-wrapper">
      <div class="title" v-if="(!isRegister || isNormal) && !isForm">
        <template v-if="isRegister">
          <span></span>
        </template>
        <template v-else-if="isResetPassword">
          <span class="c-#333333">{{ t('retrievePassword') }}</span>
        </template>
        <template v-else>
          <span
            v-for="(item, index) in LOGIN_WAY_ARRAY.slice(0, 2)"
            :key="index"
            v-mode="{ ru: 'Вход по коду' }"
            class="cursor-pointer title-item"
            :class="{ 'active-item': currentIndex === index, 'mr-16px': index === 0, 'mx-2': storageLocale === 'ar' }"
            @click="loginTypeClick(index)"
          >
            {{ item.name[$i18n.locale] }}
          </span>
        </template>
      </div>
      <div class="title" v-if="isForm && !isRegister && !isResetPassword">
        <span
          v-for="(item, index) in LOGIN_WAY_ARRAY.slice(0, 2)"
          :key="index"
          :class="{ 'active-item': currentIndex === index, 'mr-16px': index === 0, 'mx-2': storageLocale === 'ar', 'text-14px': storageLocale === 'ar' }"
          class="cursor-pointer title-item"
          @click="loginTypeClick(index)"
        >
          {{ item.name[$i18n.locale] }}
        </span>
      </div>
      <template v-if="!isRegister">
        <div class="form-wrapper">
          <el-form ref="loginFormRef" :model="formData" :rules="rules" label-position="top" size="large">
            <!-- 选择邮箱手机号码 -->
            <el-form-item class="email" label="账号类型" label-position="left" label-width="auto">
              <el-radio-group v-model="accountType" size="small" @change="handleAccountTypeChange">
                <el-radio :value="item.value" size="large" v-for="item in loginOptions" :key="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <!-- 手机号 -->
            <el-form-item v-if="!accountType" class="mobile" :label="t('field1')" prop="mobile">
              <el-input v-model.trim="formData.mobile" :placeholder="t('filed1Placeholder')" clearable>
                <template #prepend>
                  <div class="flex items-center" @click="handleLoginPrefixIdSelect">
                    <el-select :placeholder="''" v-model="formData.prefixId" style="width: 76px" :disabled="isH5" filterable :filter-method="filterPrefix">
                      <el-option :label="`+${item.prefix}`" :value="item.id" v-for="item in prefixOptions" :key="item.id">
                        <div class="flex justify-between">
                          <span class="mr-4">+{{ item.prefix }}</span>
                          <span class="text-[13px]">{{ item.desc }}</span>
                        </div>
                      </el-option>
                    </el-select>
                  </div>
                </template>
              </el-input>
            </el-form-item>
            <!-- 邮箱 -->
            <el-form-item v-else class="email" :label="t('fieldEmail')" prop="mobile">
              <el-input v-model.trim="formData.mobile" :placeholder="t('fieldEmailPlaceholder')" clearable />
            </el-form-item>
            <template v-if="currentIndex === 1 || isResetPassword">
              <!-- 滑块验证 -->
              <el-form-item>
                <WangyiVerify ref="wangyiVerify" ncwidth="100%" @success="verifySuccess" @error="verifyFail" />
              </el-form-item>
              <!-- 验证码 -->
              <el-form-item class="get-code-wrapper" :label="t('field2')" prop="smsCode">
                <div class="flex w-full">
                  <el-input
                    v-model.trim="formData.smsCode"
                    name="sd-code"
                    class="code-input flex-1 mr-4"
                    :placeholder="t('filed2Placeholder')"
                    maxlength="6"
                    clearable
                    @input="(e) => handleInput(e, 'smsCode')"
                  />
                  <el-button
                    element-loading-cover
                    class="get-code-btn shrink-0"
                    :class="{ 'skip-translate': leftSeconds > 0 }"
                    :disabled="leftSeconds > 0"
                    :loading="codeLoading"
                    @click="getCode"
                  >
                    {{ leftSeconds > 0 ? t('countdown', { countdown: leftSeconds }) : t('sendCaptcha') }}
                  </el-button>
                </div>
              </el-form-item>
            </template>
            <!-- 密码 -->
            <el-form-item v-if="currentIndex === 0 || isResetPassword" class="get-code-wrapper" :label="t('field3')" prop="password">
              <el-input v-model.trim="formData.password" type="password" show-password :placeholder="t('filed3Placeholder')" clearable />
            </el-form-item>
            <!-- 确认密码 -->
            <el-form-item v-if="isResetPassword" class="get-code-wrapper" :label="t('field4')" prop="confirmPassword">
              <el-input v-model.trim="formData.confirmPassword" type="password" show-password :placeholder="t('filed4Placeholder')" clearable />
            </el-form-item>
            <!-- 协议 -->
            <el-form-item label="" prop="isAgree">
              <el-checkbox-group v-model="formData.isAgree">
                <el-checkbox value="Online activities" name="isAgree">
                  <span class="mx-1">{{ t('agree') }}</span>
                  <a :href="REGISTER_FILE_ADDRESS[$i18n.fallbackLocale]" target="_blank" class="link-text">{{ t('servicAgree') }}</a>
                  <span class="mx-1">{{ t('and') }}</span>
                  <a :href="PRIVACY_FILE_ADDRESS[$i18n.fallbackLocale]" target="_blank" class="link-text">{{ t('secretAgree') }}</a>
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-form>
          <el-button type="primary" class="login-btn" @click="onLogin" :loading="submitLoading">{{ isResetPassword ? t('confirm') : t('submit') }}</el-button>
          <div class="text-base text-center c-#D8131A">
            <span class="cursor-pointer" @click="registerClick">{{ t('registerImmediately') }}</span>
            <span v-if="!isResetPassword" class="mx-2">|</span>
            <span v-if="!isResetPassword" class="cursor-pointer" @click="retrievePassword">{{ t('retrievePassword') }}</span>
          </div>
        </div>
      </template>
      <template v-else>
        <LoginRegister
          :isNormal="isNormal"
          :isH5="isH5"
          :userType="userType"
          :I18nConfig="I18nConfig"
          :rulesFn="rulesFn"
          :invite-code="referralCode"
          @goBack="goBack"
          @loginSuccess="loginSuccess"
        />
      </template>
    </div>
    <LoginPrefixIdSelect v-model="formData.prefixId" ref="loginPrefixIdSelectRef" :prefix-id-list="prefixIdList" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import LoginPrefixIdSelect from '@/pc/components/login-prefixId-select/login-prefixId-select.vue'
import LoginRegister from '@/pc/components/login-register/login-register.vue'
import WangyiVerify from '@/pc/components/verify/wangyi-verify.vue'
import { SMS_SEND_WAY } from '@/constants'
import { PRIVACY_FILE_ADDRESS, REGISTER_FILE_ADDRESS } from '@/constants/agreement-files'
import { LOGIN_WAY_ARRAY } from '@/constants/index'
import { useUserStore } from '@/pc/stores'
import useLoginPrefix from '@/pc/hooks/useLoginPrefix'
import useLoginSuccess from '@/pc/hooks/useLoginSuccess'
import useShowToast from '@/pc/hooks/useShowToast'
import { forgotPassword, smsSend, userLogin } from '@/apis/merchants'
import { useEvent } from '@/event'
import { LOGIN_SUCCESS } from '@/event/modules/site'
import { useStorageLocale } from '@/i18n'
import user from '@/pc/utils/user.js'
import { detectDeviceType } from '@/utils/utils'

const I18nConfig = {
  messages: {
    zh: {
      backToLogin: '返回登录',
      register: '注册',
      retrievePassword: '找回密码',
      registerImmediately: '立即注册',
      existingAccount: '已有账号？',
      field1: '手机号',
      filed1Placeholder: '请输入手机号码',
      filed1Validate: '请输入正确的手机号',
      fieldEmail: '邮箱',
      fieldEmailPlaceholder: '请输入邮箱',
      fieldEmailValidate: '请输入正确的邮箱',
      field2: '验证码',
      filed2Placeholder: '请输入验证码',
      field3: '密码',
      filed3Placeholder: '请输入密码',
      field4: '密码确认',
      filed4Placeholder: '请再次输入密码',
      filed4Validate: '两次输入的密码不一致',
      field5: '企业名称',
      filed5Placeholder: '请输入企业名称',
      field6: '推荐码',
      filed6Placeholder: '请输入推荐码',
      sendCaptcha: '获取验证码',
      sendCaptchaSuccess: '验证码发送成功',
      countdown: '{countdown}s',
      submit: '立即登录',
      verify: '请滑动验证',
      explain: '未注册的手机号码验证后将自动创建新账号',
      agree: '我已阅读并同意',
      servicAgree: '《注册服务协议》',
      and: '和',
      secretAgree: '《隐私协议》',
      readAgree: '请阅读并同意服务协议',
      servicesLink: 'https://static.chinamarket.cn/static/trade-exhibition/file/注册服务协议-中国大集.pdf',
      privacyLink: 'https://static.chinamarket.cn/static/trade-exhibition/file/隐私政策-中国大集.pdf',
      loginSuccess: '登录成功',
      registerSuccess: '注册成功',
      confirm: '确定',
    },
    en: {
      backToLogin: 'Back to login',
      register: 'Register',
      retrievePassword: 'Retrieve password',
      registerImmediately: 'Register immediately',
      existingAccount: 'Existing account？',
      field1: 'Phone or email',
      filed1Placeholder: 'Please input your phone number',
      filed1Validate1: 'Please enter a valid phone number',
      field2: 'Captcha',
      filed2Placeholder: 'please input the captcha',
      field3: 'Password',
      filed3Placeholder: 'Please input your password',
      field4: 'Password confirmation',
      filed4Placeholder: 'Please enter your password again to confirm',
      filed4Validate: 'The two passwords are different',
      field5: 'Company name',
      filed5Placeholder: 'Please enter company name',
      field6: 'Invite Code',
      filed6Placeholder: 'Please enter Invite Code',
      sendCaptcha: 'Send captcha',
      sendCaptchaSuccess: 'Captcha sent successfully',
      countdown: '{countdown} second left',
      submit: 'Sing in',
      verify: 'Please slide to verify',
      explain: 'Unregistered phone numbers will automatically create a new account after verification.',
      agree: 'I have read and agree',
      servicAgree: '《Services Statement》',
      and: 'and',
      secretAgree: '《Privacy Statement》',
      readAgree: 'Please read and agree to the service agreement',
      servicesLink:
        'https://static.chinamarket.cn/static/trade-exhibition/file/%E3%80%90%E8%8B%B1%E6%96%87%E3%80%91%E6%B3%A8%E5%86%8C%E6%9C%8D%E5%8A%A1%E5%8D%8F%E8%AE%AE.pdf',
      privacyLink: 'https://static.chinamarket.cn/static/trade-exhibition/file/%E3%80%90%E8%8B%B1%E6%96%87%E3%80%91%E9%9A%90%E7%A7%81%E6%94%BF%E7%AD%96.pdf',
      loginSuccess: 'Login successfully',
      registerSuccess: 'Register successfully',
      confirm: 'Confirm',
    },
  },
}
const { t } = useI18n(I18nConfig)
let userStore = useUserStore() // pinia

const event = useEvent()

const { storageLocale } = useStorageLocale()

const { showMessage } = useShowToast()

const props = defineProps({
  isShowLogin: {
    type: Boolean,
    default: false,
  },
  openLoginParams: {
    type: Object,
    default: null,
  },
  isNormal: {
    // 采购商登录注册
    type: Boolean,
    default: true,
  },
  isForm: {
    // 只保留form样式
    type: Boolean,
    default: false,
  },
  isH5: {
    // 只保留form样式
    type: Boolean,
    default: false,
  },
  showRegister: {
    type: Boolean,
    default: false,
  },
  userType: {
    type: Number,
    default: 1,
  },
  referralCode: {
    type: [String, Number],
    default: null,
  },
  loginSuccessRedirectPath: {
    type: String,
    default: '',
  },
})
const Emits = defineEmits(['hideLogin', 'loginSuccessCallback'])
const handleHideLogin = () => {
  Emits('hideLogin')
}

const route = useRoute()
const currentIndex = ref(0) // 登录方式： 0 密码登录； 1 验证码登录
const isResetPassword = ref(false) // 重置密码
const isRegister = ref(props.showRegister || !!props.referralCode || +route?.query?.loginType === 2) // 注册
const loginFormRef = ref(null)
// 是否禁用router
let routerDisabled = false

// 登录表单
const formData = reactive({
  mobile: '', // 手机号或邮箱
  smsCode: '', // 验证码
  password: '', // 密码
  isAgree: [],
  prefixId: '2',
})
// 0 手机号 1 邮箱
const accountType = ref(0)
const loginOptions = ref([
  { label: '手机号', value: 0 },
  { label: '邮箱', value: 1 },
])
const { prefixIdList } = useLoginPrefix()
const handleAccountTypeChange = () => {
  if (formData.mobile) formData.mobile = ''
}
// h5
const loginPrefixIdSelectRef = ref(null)
const handleLoginPrefixIdSelect = () => {
  if (props.isH5) {
    loginPrefixIdSelectRef.value.showModal()
  }
}

const rulesFn = (formData, accountTypeValue) => {
  const validateConfirmPassword = (rule, value, callback) => {
    if (value !== formData.password) {
      callback(new Error(t('filed4Validate')))
    } else {
      callback()
    }
  }
  const validateMobile = (rule, value, callback) => {
    if (!value) {
      return callback(new Error(accountTypeValue === 0 ? t('filed1Placeholder') : t('fieldEmailPlaceholder')))
    }
    if (accountTypeValue) {
      if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value)) {
        return callback(new Error(t('fieldEmailValidate')))
      }
    } else {
      if (!/^\d+(?:-\d+)*(?!.{15,})$/.test(value)) {
        return callback(new Error(t('filed1Placeholder')))
      }
    }
    callback()
  }
  const validatePassword = (rule, value, callback) => {
    if (!value) {
      return callback(new Error('请输入密码'))
    }
    if (!/^(?=.*[a-zA-Z])(?=.*\d).{6,20}$/.test(value) && (isResetPassword.value || isRegister.value)) {
      return callback(new Error('密码要求6-20位，至少包含字母和数字'))
    }
    callback()
  }
  return {
    mobile: [
      {
        required: true,
        message: computed(() => (accountTypeValue === 0 ? t('filed1Placeholder') : t('fieldEmailPlaceholder'))),
        trigger: ['blur'],
      },
      { validator: validateMobile, trigger: 'blur' },
    ],
    smsCode: [
      {
        required: true,
        pattern: /^[0-9]{4,6}$/,
        message: computed(() => t('filed2Placeholder')),
        trigger: ['blur'],
      },
    ],
    password: [
      {
        required: true,
        message: computed(() => t('filed3Placeholder')),
        trigger: ['blur'],
      },
      {
        validator: validatePassword,
        trigger: 'blur',
      },
    ],
    confirmPassword: [
      {
        required: true,
        message: computed(() => t('filed4Placeholder')),
        trigger: ['blur'],
      },
      { validator: validateConfirmPassword, trigger: 'blur' },
    ],
    isAgree: [
      {
        type: 'array',
        required: true,
        message: computed(() => t('readAgree')),
        trigger: 'change',
      },
    ],
  }
}

const rules = computed(() => rulesFn(formData, accountType.value))

// const registerIndex = ref(0)
// const registerOptionClick = (item, index) => {
//   registerIndex.value = index
//   router.push(`/register?userType=${item.id}`)
//   isShowLogin.value = false
// }
// 返回登录
const goBack = () => {
  currentIndex.value = 0
  isRegister.value = false
  isResetPassword.value = false
  resetFormClearValidate()
}
// 找回密码
const retrievePassword = () => {
  isResetPassword.value = true
  resetFormClearValidate()
}
// 立即注册
const registerClick = () => {
  isRegister.value = true
  isResetPassword.value = false
}

// 登录方式切换
const loginTypeClick = (index) => {
  currentIndex.value = index
  resetFormClearValidate()
}
// 重置表单，清除校验提示
const resetFormClearValidate = () => {
  verifyReset()
  verifyFail()
  loginFormRef?.value?.resetFields()
}
// 滑动验证信息
const verifyInfo = reactive({
  verifyData: null,
  verifyStatus: null,
})

// 滑块验证成功后触发
const verifySuccess = (data) => {
  verifyInfo.verifyData = data
  verifyInfo.verifyStatus = true
}

// 滑块组件 ref
const wangyiVerify = ref()
// 滑块重置
const verifyReset = () => wangyiVerify.value && wangyiVerify.value.ncRefresh()
// 滑块验证失败后触发
const verifyFail = () => {
  verifyInfo.verifyStatus = false
}

// 限制只能输入数字
const handleInput = (val, key) => {
  formData[key] = val.replace(/\D/g, '')
}

// 倒计时
const leftSeconds = ref(0)
const timer = ref(null)
const startCountDown = () => {
  leftSeconds.value = 60
  const countDown = () => {
    timer.value = setTimeout(() => {
      leftSeconds.value -= 1
      leftSeconds.value > 0 && countDown()
    }, 1000)
  }
  countDown()
}

const codeLoading = ref(false)
// 点击获取验证码
const sendCode = async () => {
  const body = {
    account: formData.mobile,
    prefixId: accountType.value || formData.prefixId,
    type: isResetPassword.value ? SMS_SEND_WAY.FIND_PASSWORD.id : SMS_SEND_WAY.LOGIN.id,
    userType: props.userType,
  }
  try {
    codeLoading.value = true
    await smsSend(body)
    showMessage({
      message: t('sendCaptchaSuccess'),
      type: 'success',
    })
    startCountDown()
  } catch (error) {
    verifyReset() // 将滑动验证重置为初始状态
    verifyInfo.verifyStatus = false
    formData.smsCode = ''
  } finally {
    codeLoading.value = false
  }
}

// 获取验证码
const getCode = () => {
  loginFormRef.value.validateField('mobile', (isValid) => {
    if (isValid) {
      if (verifyInfo.verifyStatus) {
        // 校验否滑动成功滑块
        sendCode()
      } else {
        showMessage({
          message: t('verify'),
          type: 'warning',
        })
      }
    }
  })
}

// 点击登录
const submitLoading = ref(false)
const onLogin = async () => {
  if (!verifyInfo.verifyStatus && currentIndex.value !== 0) {
    showMessage({
      message: t('verify'),
      type: 'warning',
    })
    return
  }
  await loginFormRef.value.validate()
  submitLoading.value = true
  try {
    let data = {}
    if (isResetPassword.value) {
      data = await forgotPassword({
        userType: props.userType,
        userName: formData.mobile,
        code: formData.smsCode,
        password: formData.password,
        prefixId: accountType.value || formData.prefixId,
      })
    } else {
      data = await userLogin({
        userType: props.userType,
        loginType: currentIndex.value ? 2 : 1,
        userName: formData.mobile,
        password: currentIndex.value ? null : formData.password,
        code: currentIndex.value ? formData.smsCode : null,
        prefixId: accountType.value || formData.prefixId,
      })
    }
    await loginSuccess(data, isResetPassword.value ? 'forgetPassword' : 'login')
    loginFormRef.value.resetFields()
  } catch (error) {
    console.log(error)
  } finally {
    verifyInfo.verifyStatus = false
    verifyReset() // 重置滑动验证
    submitLoading.value = false
  }
}

const { loginSuccessToPage } = useLoginSuccess(props.isH5 || detectDeviceType() !== 'PC')
const loginSuccess = async (data, loginType) => {
  const { token } = data

  //  设置基础用户信息
  user.setToken(token)
  userStore.setIsLogined(true)
  userStore.setUserInfo(data)
  let message = t('loginSuccess')
  switch (loginType) {
    case 'forgetPassword':
      message = t('loginSuccess')
      break
    case 'register':
      message = t('registerSuccess')
      break
    default:
      break
  }
  showMessage({
    message,
    type: 'success',
  })
  await loginSuccessToPage(data, routerDisabled, props.loginSuccessRedirectPath)
  Emits('loginSuccessCallback')
  handleHideLogin()
  event.emit(LOGIN_SUCCESS, {})
}

let referralCode = ref(props.referralCode)
const open = (params) => {
  routerDisabled = typeof params === 'object' ? !!params?.routerDisabled : false
  if (params.referralCode) {
    referralCode.value = params.referralCode
    isRegister.value = true
  }
  // 用户store数据
  // userStore.logout()
}

const filterVal = ref('')
const prefixOptions = computed(() => {
  const val = filterVal?.value?.trim()?.toLowerCase()
  return prefixIdList.value.filter((item) => {
    return String(item?.prefix).includes(val) || item?.desc?.toLowerCase()?.includes(val)
  })
})
const filterPrefix = (val) => {
  filterVal.value = val
}

watch(
  () => props.openLoginParams,
  (val) => {
    if (val) open(val)
  },
  {
    immediate: true,
  },
)

defineExpose({ open, registerClick, retrievePassword, goBack })
</script>
