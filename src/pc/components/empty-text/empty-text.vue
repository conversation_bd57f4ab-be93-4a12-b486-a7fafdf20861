<template>
  <div class="empty-text" :class="{ absolute: isAbsolute }">{{ $t('emptyText') }}</div>
</template>

<script setup>
defineProps({
  // 是否需要定位，在表格中需要定位
  isAbsolute: {
    type: Boolean,
    default: false,
  },
})
</script>

<style lang="scss" scoped>
.empty-text {
  text-align: center;
  padding: 16px 0;
  color: $color-999999;
  font-size: 14px;
}

.absolute {
  position: absolute;
  left: 0;
  right: 0;
  margin: auto;
}
</style>
