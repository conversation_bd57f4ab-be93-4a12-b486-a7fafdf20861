<!-- 网易滑块验证 -->
<style lang="scss" scoped>
.captch-box {
  width: 100%;
  height: 40px;
}
:deep() {
  .yidun_tips {
    height: 40px;
  }
  .yidun.yidun--light .yidun_control {
    border: none;
  }
  .yidun.yidun--light .yidun_slider {
    box-shadow: none;
    background: #ced4dd;
    top: -1px;

    .yidun_slider__icon {
      background: url('https://static.chinamarket.cn/static/trade-exhibition/hua-kuai.png') no-repeat;
      background-size: 16px 16px;
      background-position: center center;
    }
  }
  .yidun.yidun--light .yidun_slider.yidun_slider--hover:hover {
    background: #ced4dd;

    .yidun_slider__icon {
      background: url('https://static.chinamarket.cn/static/trade-exhibition/hua-kuai.png') no-repeat;
      background-size: 16px 16px;
      background-position: center center;
    }
  }
  .yidun.yidun--light.yidun--success.yidun--jigsaw .yidun_control .yidun_slide_indicator {
    border: none;
    height: 40px;
  }
}
</style>

<template>
  <div class="captch-box">
    <div :id="ncId" />
  </div>
</template>

<script setup>
import '/public/yidun-captcha.js'
import { nextTick, onBeforeUnmount, onMounted } from 'vue'
import { LANG_ENUM } from '@/constants/verfiyLang'
import { useStorageLocale } from '@/i18n'

const emits = defineEmits(['success', 'error'])

const props = defineProps({
  ncwidth: {
    type: String,
    default: '418px',
  },
  mode: {
    type: String,
    default: 'float',
  },
  // 验证类型 login：登录Key contact：获取联系人Key
  ncType: {
    type: String,
    default: 'login',
  },
})

// 网易滑块配置项
const loginKey = '3c00342b7eba45ac92ea46601462beb0' // 网易滑块验证 key
const ncId = `captcha-${new Date().getTime()}` // 动态 id
let instance = null // 验证实例

const { storageLocale } = useStorageLocale()
const langMap = {
  zh: 'zh-CN',
  en: 'en-US',
  indonesian: 'id',
  ru: 'ru',
  thai: 'th',
}
// 初始化
const init = () => {
  const { ncwidth, mode } = props
  const langKey = langMap[storageLocale.value] || storageLocale.value
  // eslint-disable-next-line no-undef
  initNECaptchaWithFallback(
    {
      captchaId: loginKey,
      element: `#${ncId}`,
      width: ncwidth,
      apiVersion: 2,
      lang: LANG_ENUM[langKey] ? langKey : 'en-US',
      mode,
      onVerify: (err, data) => {
        if (err) return
        emits('success', data)
      },
    },
    (onload = (v) => {
      instance = v
    }),
    (onerror = (err) => {
      emits('error', err)
    }),
  )
}

// 刷新验证
const ncRefresh = () => {
  instance && instance.refresh()
}

onMounted(() => {
  nextTick(() => {
    init()
  })
})

onBeforeUnmount(() => {
  instance && instance.destroy()
})

defineExpose({
  ncRefresh,
})
</script>
