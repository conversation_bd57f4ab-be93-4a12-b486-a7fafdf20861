<template>
  <el-upload
    ref="uploadRef"
    action="/"
    v-bind="attrs"
    :accept="accept"
    :multiple="false"
    :auto-upload="false"
    :show-file-list="showFileList"
    :on-change="handleUploadChange"
    v-loading="loading"
  >
    <div class="relative" :class="hoverRemove ? 'hover-remove-wrap' : ''">
      <span v-if="preview && modelValue" @click.stop="showImageViewer = true">
        <slot :url="modelValue"></slot>
      </span>
      <span v-else>
        <slot :url="modelValue"></slot>
      </span>

      <span @click.stop="onRemove" v-if="modelValue" class="remove-wrap">
        <slot name="remove"></slot>
      </span>
    </div>
  </el-upload>

  <!-- 图片预览组件 -->
  <ElImageViewer
    v-if="showImageViewer"
    :urlList="previewUrlList"
    :z-index="99"
    :initialIndex="imgViewerIndex"
    hideOnClickModal
    teleported
    @close="showImageViewer = false"
  />
</template>
<script setup>
// 上传
import { ElImageViewer, ElMessage } from 'element-plus'
import { OSS_DIR } from '@/constants/oss-dir'
import Arms from '@/pc/utils/arms'
import { upload } from '@/utils/oss'

const attrs = useAttrs()

const props = defineProps({
  // 文件的 src，没有图片时为空字符串
  modelValue: {
    type: [Array, String],
    default: '',
  },
  // 文件类型
  accept: {
    type: String,
    default: 'image/jpg,image/jpeg,image/png',
  },
  // 文件大小限制
  sizeLimit: {
    type: Number,
    default: 10,
  },
  showFileList: {
    type: Boolean,
    default: false,
  },
  hoverRemove: {
    type: Boolean,
    default: true,
  },
  preview: {
    type: Boolean,
    default: true,
  },
  // 上传文件夹
  dir: {
    // required: true,
    type: String,
    validator(dir) {
      const isValid = Object.values(OSS_DIR).some((d) => d === dir)
      if (import.meta.env.DEV && !isValid) {
        const keys = Object.keys(OSS_DIR)
          .map((d) => `OSS_DIR.${d}`)
          .join('、')
        // eslint-disable-next-line no-console
        console.error(`[img-upload]dir 属性必须为 ${keys} 其中一个`)
      }
      return isValid
    },
  },
})

const emits = defineEmits(['update:modelValue', 'change'])

const loading = ref(false)
const handleUploadChange = async (file) => {
  if (!beforeUploadValid(file)) {
    return
  }
  try {
    loading.value = true
    const data = await upload(file.raw, props.dir)
    emits('update:modelValue', data)
    emits('change', data)
  } catch (e) {
    Arms.sendCustom('uploadError', 'imgUploadEl上传失败', {
      error: e,
    })
  } finally {
    loading.value = false
  }
}
const beforeUploadValid = (file) => {
  if (!props.accept.includes(file.raw.type)) {
    ElMessage.warning('不支持该文件类型')
    return false
  }

  if (props.sizeLimit && typeof props.sizeLimit === 'number') {
    const sizeLimit = props.sizeLimit * 1024 * 1024
    const fileSize = file.size
    if (fileSize > sizeLimit) {
      ElMessage.error(`上传的文件大小不能超过${props.sizeLimit}M`)
      return false
    }
  }
  return true
}
const onRemove = () => {
  if (props.modelValue) {
    const val = typeof props.modelValue === 'string' ? '' : []
    emits('update:modelValue', val)
  }
}

// 图片预览组件
const showImageViewer = ref(false)
const imgViewerIndex = ref(0)
// 预览图片 url 数组
const previewUrlList = computed(() => {
  if (typeof props.modelValue === 'string') {
    return props.modelValue ? [props.modelValue] : []
  } else {
    return props.modelValue
  }
})

defineExpose({
  onRemove,
})
</script>

<style scoped lang="scss">
.hover-remove-wrap {
  &:hover {
    .remove-wrap {
      display: block;
    }
  }

  .remove-wrap {
    display: none;
  }
}
</style>
