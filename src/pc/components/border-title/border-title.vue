<template>
  <div class="border-title flex items-center mb-4">
    <span v-if="showLeftBorder" class="w-0.75 h-4 mr-2 bg-[#D8131A]"></span>
    <span class="text-base c-#333">{{ title }}</span>
    <slot name="icon"></slot>
  </div>
</template>

<script setup>
defineProps({
  // 标题
  title: {
    type: String,
    default: '',
  },
  showLeftBorder: {
    type: Boolean,
    default: true,
  },
})
</script>

<style lang="scss" scoped>
[dir='rtl'] .mr-2 {
  margin-left: 8px;
  margin-right: 0;
}
</style>
