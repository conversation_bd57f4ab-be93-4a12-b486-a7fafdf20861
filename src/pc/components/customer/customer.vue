<template>
  <Dialog v-model="visible" width="260">
    <template #default>
      <div class="p-4 rounded-1 text-center" style="background-image: linear-gradient(0deg, #faf5f5 0%, #ffeded 100%)">
        <div
          class="flex items-center justify-center relative mb-1 text-white text-16px font-semibold bg-[#D8131A] w-[186px] m-auto rounded-40px px-[16px] py-[6px]"
        >
          <div>{{ t('pleaseContact') }}</div>
          <div class="w-[12px] h-[12px] bg-[#D8131A] absolute left-0 right-0 bottom-[-6px] m-auto" style="transform: rotate(-45deg)"></div>
        </div>
        <div class="text-[#333] text-16px font-semibold py-[4px]"></div>
        <div class="code-warp m-auto">
          <img-loader class="qrcode" :src="qrCode || '/wechat-work-qrcode.png'" alt="" />
        </div>
        <Icon class="cursor-pointer mt-1" type="icon-guanbi" :size="16" @click="visible = false" />
      </div>
    </template>
  </Dialog>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import Dialog from '@/pc/components/dialog/dialog.vue'
import { CUSTOMER_SERVICE_PHONE_NUMBER_86 } from '@/pc/constant'

const { t } = useI18n({
  messages: {
    zh: {
      pleaseContact: '请加微信',
    },
    en: {
      pleaseContact: 'Please add WeChat',
    },
  },
})

const visible = ref(false)
const qrCode = ref('')
const mobile = ref(CUSTOMER_SERVICE_PHONE_NUMBER_86)
const init = (item) => {
  if (item?.qrCode) qrCode.value = item.qrCode
  if (item?.mobile) mobile.value = item.mobile
  visible.value = true
}
defineExpose({ init })
</script>

<style lang="scss" scoped>
.code-warp {
  width: 176px;
  height: 176px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-size: cover;
  background-image: url('https://static.chinamarket.cn/static/trade-exhibition/code-warp.png');
  :deep(.qrcode) {
    width: 160px;
    height: 160px;
  }
}
</style>
