<template>
  <div class="right-side-nav">
    <!-- 悬浮导航按钮 -->
    <div class="nav-buttons">
      <!-- 找货按钮（仅在mall路由下显示） -->
      <div class="nav-item" v-if="showMallTools" @click="openPage({ type: 'router', path: '/buyer-center/my-opportunity-add', needLogined: true })">
        <Icon type="icon-new-zhaohuo" :size="30" />
        <span v-if="$storageLocale === 'zh'" class="nav-text">找货</span>
      </div>

      <!-- 进货单按钮（仅在mall路由下显示） -->
      <div class="nav-item" v-if="showMallTools" @click="openPage({ type: 'router', path: '/cart/list', needLogined: true })">
        <Icon type="icon-new-gouwuche" :size="28" />
        <span v-if="$storageLocale === 'zh'" class="nav-text">进货单</span>
      </div>
      <!-- 订单按钮（仅在mall路由下显示） -->
      <div class="nav-item" v-if="showMallTools" @click="openPage({ type: 'router', path: '/buyer-center/order-manage', needLogined: true })">
        <Icon type="icon-new-dingdan" :size="26" />
        <span v-if="$storageLocale === 'zh'" class="nav-text">订单</span>
      </div>
      <!-- 客服按钮 -->
      <div v-if="!inBlack" class="nav-item" @click="openPage(serviceOptions)">
        <Icon type="icon-new-kefu" :size="26" />
        <span v-if="$storageLocale === 'zh'" class="nav-text">客服</span>
      </div>
    </div>
    <el-backtop :right="0" :bottom="-110" :visibility-height="600" class="back-top">
      <div class="nav-item">
        <Icon type="icon-new-huidingbu" :size="27" />
        <span v-if="$storageLocale === 'zh'" class="nav-text">回顶部</span>
      </div>
    </el-backtop>

    <!-- 返回顶部按钮 -->
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/pc/stores'
import { useEvent } from '@/event'
import { OPEN_NEW_LOGIN } from '@/event/modules/site'
import { useStorageLocale } from '@/i18n'
import { CUSTOMER_SERVICE_LANGUAGE_MAP } from '@/i18n/contants'
import { debounce } from '@/common/js/util'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const event = useEvent()
const { storageLocale } = useStorageLocale()

const windowFeatures = 'height=800,width=950,top=50,left=200,status=yes,toolbar=no,menubar=no,resizable=yes,scrollbars=no,location=no,titlebar=no'
const serviceOptions = computed(() => {
  const lang = CUSTOMER_SERVICE_LANGUAGE_MAP[storageLocale.value]
  return {
    type: 'windowOpen',
    path: `https://support.chinamarket.cn/index/index/home?visiter_id=&visiter_name=&avatar=&business_id=1&groupid=0&special=0&width=100&lang=${lang}`,
    windowOptions: {
      target: '_blank',
      features: windowFeatures,
    },
  }
})
// 判断是否在mall路由下，决定是否显示找货、进货单、订单按钮
const showMallTools = computed(() => {
  return route.path.includes('/mall')
})
const inBlack = computed(() => {
  const list = []
  return list.includes(route.path)
})
/**
 * @param options.type: router vue路由（默认） | link 外部地址 （可拓展，默认新标签页）
 * @param options.path: 跳转地址
 * @param options.needLogined: 是否需要登录 默认：否
 * @param options.immediately: 是否立即执行 默认：否
 * @param loginOptions.routerDisabled: true 跳转指定路由， false 根据用户type跳转相应页
 * @param loginOptions.loginSuccessRedirectPath: routerDisabled为true时需要指定跳转路由
 * @param loginOptions.callback: 登录成功的回调触发函数
 * @param loginOptions.windowOptions.target: 新窗口打开方式 默认：_blank
 * @param loginOptions.windowOptions.features: 新窗口打开配置 默认：''
 */
const openPage = debounce((options) => {
  const defaultOptions = {
    type: 'router',
    path: '',
    needLogined: false,
    immediately: true,
  }
  const defaultLoginOptions = {
    routerDisabled: true,
    path: options.path,
    loginSuccessRedirectPath: '',
    callback: null,
  }
  const _loginOptions = {
    ...defaultLoginOptions,
    ...options.loginOptions,
  }
  const _windowOptions = {
    target: '_blank',
    features: '',
    ...options.windowOptions,
  }
  const _options = {
    ...defaultOptions,
    ...options,
  }
  if (_options.needLogined) {
    if (!userStore.isLogined) {
      event.emit(OPEN_NEW_LOGIN, _loginOptions)
      return false
    }
  }
  if (!_options.path) {
    console.error('path is required')
    return
  }
  if (_options.type === 'router') {
    router.push({
      path: _options.path,
    })
  }
  if (_options.type === 'link') {
    location.href = _options.path
  }
  if (_options.type === 'windowOpen') {
    window.open(_options.path, _windowOptions.target, _windowOptions.features)
  }
})
</script>

<style scoped lang="scss">
.right-side-nav {
  position: fixed;
  right: 0px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 999;
  border-radius: 8px 0px 0px 8px;
  background: #ffffff;
  box-sizing: border-box;
  border-width: 1px 0px 1px 1px;
  border-style: solid;
  border-color: #f0f0f0;
}

.nav-buttons {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.nav-item {
  min-width: 48px;
  min-height: 48px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  padding: 10px 0;
  &:first-child {
    padding-top: 20px;
  }
  &:last-child {
    padding-bottom: 20px;
  }
}

.nav-item:hover {
  background: #f5f5f5;
}

.nav-icon {
  width: 24px;
  height: 24px;
  color: #333;
}

.nav-text {
  font-size: 12px;
  color: #333;
  margin-top: 4px;
}

.back-top {
  color: #000;
  .nav-item {
    border-radius: 8px 0px 0px 8px;
    background: #ffffff;
    box-sizing: border-box;
    border-width: 1px 0px 1px 1px;
    border-style: solid;
    border-color: #f0f0f0;
    padding: 20px 0;
  }
}
</style>
