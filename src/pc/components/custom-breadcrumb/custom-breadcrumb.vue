<!--
 * @Author: 李兵 <EMAIL>
 * @Date: 2024-08-24 15:27:51
 * @LastEditors: your name
 * @LastEditTime: 2024-08-26 16:03:47
 * @FilePath: /trade-exhibition/src/components/custom-breadcrumb/custom-breadcrumb.vue
 * @Description: 自定义二级导航
-->
<style scoped lang="scss">
.custom-breadcrumb {
  height: 62px;

  @include flex-sbc;

  .back {
    width: 100%;
    font-size: 20px;
    font-weight: 500;
    color: #666666;
    line-height: 28px;
    cursor: pointer;

    .icon {
      margin-right: 16px;
      color: #d9d9d9;
    }
  }
}
</style>
<style scoped lang="scss">
:deep() {
  .el-breadcrumb__inner.is-link {
    font-size: 16px;
    color: $regular-text;
    font-weight: normal;
  }
  .el-breadcrumb__item:last-child .el-breadcrumb__inner {
    font-size: 16px;
    color: $primary-color;
  }
}

[dir='rtl'] .custom-breadcrumb {
  .el-breadcrumb {
    display: flex;
  }

  :deep(.el-breadcrumb__separator) {
    transform: rotateZ(-180deg);
  }
}
</style>

<template>
  <div class="custom-breadcrumb">
    <el-breadcrumb separator-class="el-icon-arrow-right" :separator-icon="ArrowRight">
      <el-breadcrumb-item v-for="(item, index) in breadcrumbList" :key="index" :to="item.path">{{ item.name[$i18n.locale] }}</el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>
<script setup>
import { ArrowRight } from '@element-plus/icons-vue'

const route = useRoute()
const breadcrumbList = ref([])

const props = defineProps({
  beforeBreadcrumb: {
    type: Function,
    default: null,
  },
})

// 监听路由变化，添加对应 tab
watch(
  route,
  (val) => {
    let arr = val.matched.map((item) => {
      return {
        name: item.meta.title,
        path: item.path,
      }
    })
    if (typeof props.beforeBreadcrumb === 'function') {
      const newArr = props.beforeBreadcrumb(arr)
      if (Array.isArray(newArr)) {
        arr = newArr
      }
    }
    breadcrumbList.value = arr
  },
  {
    immediate: true,
    deep: true,
  },
)
</script>
