<template>
  <table :class="{ 'has-border': border }">
    <thead>
      <tr :style="{ background: theadBgColor, color: theadTextColor }">
        <th v-for="item in cols" :key="item.prop" :style="{ width: item.minWidth + 'px', textAlign: item.align || 'left' }">
          <icon v-if="item.icon" :type="item.icon" :size="iconSize" />
          {{ item.label }}
        </th>
      </tr>
    </thead>
    <template v-if="data.length">
      <tbody>
        <tr v-for="(row, index) in data" :key="index" :class="{ 'striped-row': index % 2 === 0 }">
          <td v-for="col in cols" :key="col.prop" :style="{ textAlign: col.align || 'left' }">
            <template v-if="col.prop === 'index'">
              {{ index + 1 }}
            </template>
            <template v-else>
              {{ row[col.prop] || '-' }}
            </template>
          </td>
        </tr>
      </tbody>
    </template>
    <!-- 暂无数据 -->
    <EmptyText isAbsolute v-else />
  </table>
</template>

<script setup>
import EmptyText from '@/pc/components/empty-text/empty-text.vue'

defineProps({
  cols: {
    type: Array,
    required: true,
  },
  data: {
    type: Array,
    required: true,
  },
  border: {
    type: Boolean,
    default: false,
  },
  // 表格表头背景颜色
  theadBgColor: {
    type: String,
    default: '#ffffff',
  },
  theadTextColor: {
    type: String,
    default: '#ffffff',
  },
})
</script>

<style scoped>
table {
  width: 100%;
  border-collapse: collapse;
  position: relative;
  /* border: none; */
}
.has-border {
  border-collapse: collapse;
}
th {
  text-align: left;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 600;
}
td {
  padding: 10px 16px;
  font-size: 14px;
  color: #333333;
}
.has-border th,
.has-border td {
  border: 1px solid #ddd;
  padding: 8px;
}
.striped-row {
  background-color: #f9f9f9;
}
</style>
