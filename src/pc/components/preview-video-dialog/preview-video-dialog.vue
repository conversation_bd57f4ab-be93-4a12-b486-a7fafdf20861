<!-- 预览视频弹窗 -->
<style lang="scss" scoped>
.video-box {
  max-height: 600px;

  .video {
    display: block;
    width: 100%;
    height: 100%;
    max-height: 600px;
  }
}
</style>

<style lang="scss">
.preview-video-dialog {
  .el-dialog__body {
    padding: 20px;
  }
}
</style>

<template>
  <main class="preview-video-dialog">
    <el-dialog
      v-model="dialogVisible"
      width="800px"
      :title="t('videoPreview')"
      :append-to-body="true"
      custom-class="preview-video-dialog"
      :close-on-click-modal="false"
      @close="closeCallBack"
    >
      <div class="video-box">
        <video ref="videoRef" class="video" :src="url" controls disablePictureInPicture />
      </div>
    </el-dialog>
  </main>
</template>

<script setup lang="jsx">
import { useI18n } from 'vue-i18n'

const { t } = useI18n({
  messages: {
    zh: {
      videoPreview: '视频预览',
    },
    en: {
      videoPreview: 'Video Preview',
    },
  },
})

const dialogVisible = ref(false)
const url = ref('')

const init = (data) => {
  dialogVisible.value = true
  url.value = data.url
}
const videoRef = ref(null)
// 弹窗关闭回调
const closeCallBack = () => {
  videoRef.value.pause()
}

defineExpose({
  init,
})
</script>
