<style lang="scss">
@import './rich-text';

.rich-text-editor {
  border: 1px solid $base-border;
  border-radius: 2px;

  .toolbar {
    border-bottom: 1px solid $base-border;

    button {
      padding: 0 5px;

      // 隐藏默认图标
      &:not(.select-button) {
        svg:first-child {
          display: block;
          width: 16px;
          height: 16px;
          background-repeat: no-repeat;
          background-size: 16px 16px;
          content: '';

          path {
            display: none;
          }
        }
      }
    }

    .w-e-bar-item {
      &:nth-child(6) button svg:first-child {
        background-image: url('./icon/bold.svg');
      } // 粗体
      &:nth-child(7) button svg:first-child {
        background-image: url('./icon/italic.svg');
      } // 斜体
      &:nth-child(8) button svg:first-child {
        background-image: url('./icon/under-line.svg');
      }
      &:nth-child(9) button svg:first-child {
        background-image: url('./icon/fontcolors.svg');
      }
      &:nth-child(10) button svg:first-child {
        background-image: url('./icon/font-background-colors.svg');
      }

      &:nth-child(12) button svg:first-child {
        background-image: url('./icon/align-left.svg');
      }
      &:nth-child(13) button svg:first-child {
        background-image: url('./icon/align-center.svg');
      }
      &:nth-child(14) button svg:first-child {
        background-image: url('./icon/align-right.svg');
      }
      &:nth-child(15) button svg:first-child {
        background-image: url('./icon/ordered-list.svg');
      }
      &:nth-child(16) button svg:first-child {
        background-image: url('./icon/unordered-list.svg');
      }
      &:nth-child(17) button svg:first-child {
        background-image: url('./icon/textindent.svg');
      }
      &:nth-child(18) button svg:first-child {
        background-image: url('./icon/decreaseindent.svg');
      }

      &:nth-child(19) button svg:first-child {
        background-image: url('./icon/picture.svg');
      }
      &:nth-child(20) button svg:first-child {
        background-image: url('./icon/videocamera-add.svg');
      }
      &:nth-child(21) button svg:first-child {
        background-image: url('./icon/link.svg');
      }
      &:nth-child(22) button svg:first-child {
        background-image: url('./icon/table.svg');
      }
    }
  }

  .editor {
    .w-e-text-placeholder {
      font-style: normal;
      line-height: normal;
    }
  }
}
</style>

<template>
  <div class="rich-text-editor">
    <i class="iconfont icon-picture" />
    <div :id="toolbarId" class="toolbar" />
    <div :id="editorId" class="editor rich-text" :style="{ height: props.height }" />
  </div>
</template>

<script setup lang="jsx">
import { createEditor, createToolbar } from '@wangeditor/editor'
import '@wangeditor/editor/dist/css/style.css'
import { onBeforeUnmount, onMounted, ref, watch } from 'vue'
// import { ElMessage } from '@/components/el-message/index'
import { OSS_DIR } from '@/constants/oss-dir'
import { upload } from '@/utils/oss'

const props = defineProps({
  // 富文本内容
  content: {
    type: String,
    default: '',
  },
  // 文本内容
  text: {
    type: String,
    default: '',
  },
  // 图片存储路径
  ossPicDir: {
    type: String,
    default: '',
  },
  // 视频存储路径
  ossVideoDir: {
    type: String,
    default: '',
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false,
  },
  // 编辑器高度
  height: {
    type: String,
    default: '300px',
  },
  // 限制的图片大小，单位为 M
  imageSizeLimit: {
    type: Number,
    default: 10,
  },
  // 限制的视频大小，单位为 M
  videoSizeLimit: {
    type: Number,
    default: 50,
  },
})

const emit = defineEmits(['update:content', 'update:text', 'blur'])

// 实例挂载id
const baseId = `${new Date().getTime()}${Math.random()}`.replace(/\./g, '') // 随机数
const editorId = ref(`editor-${baseId}`) // editor 的 id
const toolbarId = ref(`toolbar-${baseId}`) // toolbar 的 id

let editor = null // 编辑器实例
// eslint-disable-next-line no-unused-vars
let toolbar = null // 工具栏

onMounted(() => {
  init()
  setDisabled(props.disabled)
})
// 初始化编辑器
const init = () => {
  initEditor()
  initToolbar()
}

// 编辑器初始化
const initEditor = () => {
  // 输入框配置
  const editorConfig = { MENU_CONF: {} }
  editorConfig.autoFocus = false
  editorConfig.placeholder = '请输入内容'
  editorConfig.onChange = (editorObj) => {
    const newHtml = editorObj.getHtml()
    emit('update:content', newHtml)
  }
  editorConfig.onBlur = () => {
    emit('blur')
  }

  // 图片上传
  editorConfig.MENU_CONF.uploadImage = {
    async customUpload(file, insertFn) {
      // console.log({ file, insertFn })
      const FILE_SIZE = props.imageSizeLimit * 1024 * 1024 // 10 * 1024 * 1024
      if (file.size > FILE_SIZE) {
        ElMessage.error(`所选图片大小超过 ${props.imageSizeLimit} M，请重新上传`)
        return
      }
      const url = await upload(file, OSS_DIR[props.ossPicDir]) // 路径需要修改
      // console.log(url)
      insertFn(url)
    },
  }

  // 视频上传
  editorConfig.MENU_CONF.uploadVideo = {
    // 自定义上传
    async customUpload(file, insertFn) {
      // console.log({ file })
      const FILE_SIZE = props.videoSizeLimit * 1024 * 1024 // 50 * 1024 * 1024
      if (file.size > FILE_SIZE) {
        ElMessage.error(`所选视频大小超过 ${props.videoSizeLimit} M，请重新上传`)
        return
      }
      const url = await upload(file, OSS_DIR[props.ossVideoDir]) // 路径需要修改
      insertFn(url)
    },
  }

  // 创建输入框实例
  editor = createEditor({
    selector: `#${editorId.value}`,
    html: props.content,
    config: editorConfig,
    mode: 'default',
  })
}

// 工具栏初始化
const initToolbar = () => {
  // 工具栏配置
  const toolbarConfig = {}
  toolbarConfig.toolbarKeys = [
    'headerSelect',
    'fontFamily', // 字体
    'fontSize', // 字号
    'lineHeight', // 行高
    '|',
    'bold', // 粗体
    'italic', // 斜体
    'underline', // 下划线
    'color', // 颜色
    'bgColor', // 字体背景色
    '|',
    'justifyLeft', // 居左
    'justifyCenter', // 居中
    'justifyRight', // 居右
    'numberedList', // 有序列表
    'bulletedList', // 无序列表
    'indent', // 缩进
    'delIndent', // 取消缩进
    // '|',
    // {
    //   key: 'group-image', // 以 group 开头
    //   title: '图片',
    //   menuKeys: ['insertImage', 'uploadImage'],
    // },
    'uploadImage',
    // {
    //   key: 'group-video', // 以 group 开头
    //   title: '视频',
    //   menuKeys: ['insertVideo', 'uploadVideo'],
    // },
    'uploadVideo',
    'insertLink', // 链接
    'insertTable', // 表格
  ]
  // toolbarConfig.modalAppendToBody = true
  // 工具栏实例
  toolbar = createToolbar({
    editor,
    selector: `#${toolbarId.value}`,
    config: toolbarConfig,
    mode: 'default',
  })
}

// 设置默认内容
// 该方法会报一个 Uncaught (in promise) Error: Cannot resolve a DOM node from Slate node 的错误，貌似没有什么影响
const setHtmlContent = (htmlContent) => {
  if (htmlContent) {
    // editor.restoreSelection() // 恢复最近一次非 null 选区。如编辑器 blur 之后，再重新恢复选区
    editor.select([]) // 全选
    editor.deleteFragment() // 删除选中内容
    editor.dangerouslyInsertHtml(htmlContent) // 设置富文本
    return
  }
  editor.select([]) // 全选
  editor.deleteFragment() // 删除选中内容
}
// 获取 html 内容
const getHtmlContent = () => {
  const richText = editor.getHtml()
  return richText
}
// 获取文本内容
const getTextContent = () => {
  const richText = editor.getText()
  return richText
}

// 禁用 editor
const setDisabled = (disabled) => {
  if (disabled) {
    editor.disable()
  } else {
    editor.enable()
  }
}

watch(
  () => props.disabled,
  (disabled) => {
    setDisabled(disabled)
  },
)

watch(
  () => props.content,
  (newContent, oldContent) => {
    if (!newContent) {
      editor.clear()
    }
    if (newContent && (editor.isEmpty() || !oldContent)) {
      setHtmlContent(newContent)
    }
  },
)

// 退出页面销毁
onBeforeUnmount(() => {
  // console.log('die')
  editor.destroy()
  editor = null
  toolbar = null
})

defineExpose({
  setHtmlContent,
  getHtmlContent,
  getTextContent,
  setDisabled,
})
</script>
