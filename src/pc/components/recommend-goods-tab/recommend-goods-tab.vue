<template>
  <div class="recommend-tab">
    <el-tabs v-model="categoryId" class="demo-tabs" @tab-click="updateDateFun">
      <el-tab-pane label="为你推荐" name=""></el-tab-pane>
      <el-tab-pane v-for="item in tabList" :key="item.id" :label="item.categoryName" :name="item.id"></el-tab-pane>
    </el-tabs>
    <div class="sort-label-list">
      <div class="sort-label-left">
        <div class="sort-label" :class="sortLabel == '综合' ? 'isActive' : ''" @click="changeSort('综合')">综合</div>
        <div class="sort-label" :class="sortLabel == '销量' ? 'isActive' : ''" @click="changeSort('销量')">销量</div>
        <div class="sort-label" :class="sortLabel == '价格' ? 'isActive' : ''" @click="changeSort('价格')">
          价格
          <div class="arrow-up-down">
            <icon type="icon-xiala" class="down" :class="sortLabel == '价格' && orderAsc == 1 ? 'isActiveSvg' : ''"></icon>
            <icon type="icon-xiala" :class="sortLabel == '价格' && orderAsc == 2 ? 'isActiveSvg' : ''"></icon>
          </div>
        </div>
      </div>
      <el-radio-group v-model="labelRadio" size="large">
        <el-radio v-for="(item, index) in lablist" @click.prevent="changeRadio($event, item)" :key="index" :value="item.id">{{ item.labelName }}</el-radio>
      </el-radio-group>
    </div>
  </div>
</template>
<script setup>
import { onMounted } from 'vue'
import { getCategoryPreference, getListLabel } from '@/apis/goods'

const emitEvent = defineEmits(['uploadData'])
const categoryId = ref('')
const tabList = ref([])
const lablist = ref([])
const labelRadio = ref()
const sortLabel = ref('综合')

const orderAsc = ref(1)
const getSearchParams = () => {
  let params = {
    categoryId: categoryId.value,
    orderBy: '',
    orderAsc: orderAsc.value,
    labelIds: labelRadio.value ? [labelRadio.value] : null,
  }
  if (sortLabel.value == '综合') {
    params.orderBy = ''
  }
  if (sortLabel.value == '销量') {
    params.orderBy = 'sale'
  }
  if (sortLabel.value == '价格') {
    params.orderBy = 'price'
  }
  return params
}
const changeRadio = (e, item) => {
  if (item.id == labelRadio.value) {
    labelRadio.value = ''
  } else {
    labelRadio.value = item.id
  }
  updateDateFun()
}
const updateDateFun = () => {
  scrollToTop()
  setTimeout(() => {
    emitEvent('uploadData', getSearchParams())
  }, 500)
}

const getData = async () => {
  let res = await getCategoryPreference()

  tabList.value = res || []
  console.log('getData', tabList)
}
const getLabel = async () => {
  let res = await getListLabel({ labelType: 1, labelNames: ['趋势力', '品牌力', '价格力'] })
  lablist.value = []
  //价格力=工厂直销  趋势力=网红爆款 品牌力=品牌馆

  res.map((item) => {
    if (item.labelName == '价格力') {
      lablist.value.push({ ...item, labelName: '工厂直销' })
    } else if (item.labelName == '趋势力') {
      lablist.value.push({ ...item, labelName: '网红爆款' })
    } else if (item.labelName == '品牌力') {
      lablist.value.push({ ...item, labelName: '品牌馆' })
    } else {
      lablist.value.push({ ...item })
    }
  })

  console.log('getLabel', lablist)
}
//从低到高1
const changeSort = (sort) => {
  if (sortLabel.value == sort) {
    if (orderAsc.value == '1') {
      orderAsc.value = '2'
    } else {
      orderAsc.value = '1'
    }
  } else if (sort == '销量') {
    sortLabel.value = sort
    orderAsc.value = '2'
  } else {
    sortLabel.value = sort
    orderAsc.value = '1'
  }
  updateDateFun()
}

const scrollToTop = () => {
  let scrollTop = document.documentElement.scrollTop || document.body.scrollTop

  const inner = document.getElementsByClassName('goods-list-content')
  const topbannarH = inner[0].offsetTop - 128
  if (scrollTop < topbannarH) {
    return
  }
  // 获取元素的计算后的样式inner[0].offsetTop - parseInt(style.paddingTop) - parseInt(inner[0].offsetHeight)
  const style = window.getComputedStyle(inner[0])
  window.scrollTo({
    top: inner[0].offsetTop - parseInt(style.paddingTop) - 128,
    behavior: 'smooth',
  })
}

onMounted(() => {
  getData()
  getLabel()
})
</script>
<style lang="scss" scoped>
:deep() {
  .el-tabs--bottom > .el-tabs__header .el-tabs__item:nth-child(2),
  .el-tabs--top > .el-tabs__header .el-tabs__item:nth-child(2) {
    padding-left: 20px;
  }
  .arrow-up-down {
    svg {
      color: #d8d8d8;
    }
    .isActiveSvg {
      svg {
        color: $primary-color;
      }
    }
  }
  .el-checkbox.el-checkbox--large .el-checkbox__label {
    font-size: 16px;
    color: #505259;
    font-size: 16px;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 0px;
  }
  .el-tabs__header {
    margin-bottom: 10px;
  }
  .el-tabs__active-bar {
    height: 0px !important;
  }
  .el-tabs__nav-wrap.is-scrollable {
    padding: 0 60px;
  }
  .el-tabs__nav-next,
  .el-tabs__nav-prev {
    width: 22px;
    height: 22px;
    top: 14px;
    line-height: 22px;
  }
  .el-tabs__nav-wrap .is-disabled {
    .el-icon {
      border: 1px solid #999;
      color: #999;
    }
    cursor: not-allowed;
    &:hover {
      .el-icon {
        border: 1px solid #999;
        color: #999;
      }
    }
  }
  .el-tabs__nav-next,
  .el-tabs__nav-prev {
    &:hover {
      .el-icon {
        border: 1px solid $primary-color;
        color: $primary-color;
      }
    }
    .el-icon {
      border: 1px solid #3d3d3d;
      color: #3d3d3d;
      border-radius: 50%;
      font-size: 20px;
    }
  }
  .el-tabs__nav-wrap:after {
    background-color: transparent;
  }
  .el-tabs__item {
    font-size: 20px;
    font-weight: normal;
    line-height: normal;
    letter-spacing: 0em;
    color: #333333;
    height: 48px;
    line-height: 48px;
    // margin-top: 10px;
    // margin-bottom: 10px;
  }
  .el-tabs__item.is-active,
  .el-tabs__item:hover {
    color: $primary-color;
    background: #f7f7f7;
    border-radius: 74px;
  }
  .el-tabs__active-bar {
    height: 4px;
    border-radius: 33px;
  }
}
.recommend-tab {
  position: sticky;
  top: 128px;
  z-index: 10;
  background: #fff;
  padding-bottom: 10px;
  padding-top: 10px;
}
.el-tabs {
  padding: 0 13px;
}
.sort-label-list {
  height: 42px;
  display: flex;
  justify-content: left;
  padding: 0 12px;

  align-items: center;
  .sort-label-left {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #3d3d3d;
  }
  .sort-label {
    font-size: 16px;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 0em;

    margin-right: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
  .isActive {
    color: $primary-color;
  }
}

.arrow-up-down {
  margin-left: 10px;
  display: flex;
  flex-direction: column;
  font-size: 11px;

  i {
    cursor: pointer;
  }
  .down {
    rotate: 180deg;
  }
}
</style>
