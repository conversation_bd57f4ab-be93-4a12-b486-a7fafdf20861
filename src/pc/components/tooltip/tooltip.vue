<style lang="scss" scoped>
.reference-text {
  width: 100%;
  cursor: pointer;
  display: inline-block;
  @include ellipsis;
}

.content-box {
  max-width: 1200px;
  background: rgb(24 24 24 / 0.8);
  color: $basic-white;
  border-radius: 6px;
  padding: 10px;
  position: relative;
  font-size: 14px;
}
</style>

<template>
  <Popover ref="popoverRef" :placement="placement">
    <template #reference>
      <span class="reference-text" :style="tooltipTextStyle">{{ tooltipText }}</span>
    </template>
    <div class="content-box" v-if="!disabled">
      {{ tooltipText }}
    </div>
  </Popover>
</template>

<script setup>
import Popover from '@/pc/components/popover/popover.vue'

defineProps({
  // 要提示的文案
  tooltipText: {
    type: String,
    default: '',
  },
  // 提示文案样式
  tooltipTextStyle: {
    type: Object,
    default: () => ({
      color: '#FFFFFF',
      fontSize: '14px',
    }),
  },
  // 方向
  placement: {
    type: String,
    default: 'top',
  },
  // 是否禁用 tooltip
  disabled: {
    Boolean,
    default: false,
  },
})
</script>
