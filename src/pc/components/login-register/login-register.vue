<style lang="scss" scoped>
.login-btn {
  width: 100%;
  height: 48px;
  background: $primary-color;
  text-align: center;
  line-height: 48px;
  font-size: 16px;
  font-weight: 600;
  color: $basic-white;
  margin-bottom: 16px;
  cursor: pointer;
  padding: 0;
}

.title {
  text-align: center;
  font-size: 16px;
  font-weight: 600;
  color: $color-999999;
  margin-bottom: 40px;

  .title-item {
    position: relative;

    &::after {
      position: absolute;
      bottom: -10px;
      left: 50%;
      margin-left: -16px;
      display: none;
      content: '';
      width: 32px;
      border-top: 2px solid $primary-color;
    }

    &.active-item {
      color: $primary-color;
      font-weight: 600;

      &::after {
        display: block;
      }
    }
  }
}

:deep() {
  .el-form-item__label {
    line-height: 20px;
    margin-bottom: 4px;
    color: $color-999999;
  }
  .el-form-item {
    margin-bottom: 16px;
  }

  .el-select__suffix .el-input__validateIcon {
    display: none;
  }

  .el-input-group__prepend {
    background: #fff;
    padding: 0;

    .el-select {
      margin: 0;
    }

    .el-select__wrapper {
      border: none;
      box-shadow: none !important;
      padding: 0 8px 0 15px !important;
    }

    .el-select__wrapper.is-disabled .el-select__selected-item {
      color: rgb(51, 51, 51);
    }
  }

  .el-checkbox {
    line-height: 20px;
    height: 20px;

    &.is-checked {
      .el-checkbox__inner {
        background: $primary-color;
        border-color: $primary-color;
      }
      .el-checkbox__label {
        color: $primary-color;
      }
    }
  }
}

.get-code-wrapper {
  .get-code-btn {
    padding: 0 5px;
    min-width: 120px;
    height: 40px;
    font-size: 14px;
    font-weight: 600;
    text-align: center;
    line-height: 0;
    color: $basic-white;
    background: $primary-color;
    border: none;

    &.is-disabled {
      background: $color-C4C4C4 !important;
    }
  }
}
.link-text {
  font-size: 14px;
  color: $primary-color;
}

.h5-small-text .link-text {
  font-size: 12px;
}
</style>

<template>
  <el-form ref="registerRef" :model="formData" :rules="rules" label-position="top" size="large">
    <div class="title flex items-center justify-center">
      <div
        v-for="item in loginOptions"
        :key="item.value"
        :class="{ 'active-item': accountType === item.value }"
        class="cursor-pointer title-item mx-[20px]"
        @click="handleAccountTypeChange(item.value)"
      >
        {{ item.label }}注册
      </div>
    </div>
    <!-- 手机号 -->
    <el-form-item v-if="!accountType" class="mobile" :label="t('field1')" prop="mobile">
      <el-input v-model.trim="formData.mobile" :placeholder="t('filed1Placeholder')" clearable>
        <template #prepend>
          <div class="flex items-center" @click="handleLoginPrefixIdSelect">
            <el-select :placeholder="''" v-model="formData.prefixId" style="width: 76px" :disabled="isH5" filterable :filter-method="filterPrefix">
              <el-option :label="`+${item.prefix}`" :value="item.id" v-for="item in prefixOptions" :key="item.id">
                <div class="flex justify-between">
                  <span class="mr-4">+{{ item.prefix }}</span>
                  <span class="text-[13px]">{{ item.desc }}</span>
                </div>
              </el-option>
            </el-select>
          </div>
        </template>
      </el-input>
    </el-form-item>
    <!-- 邮箱 -->
    <el-form-item v-else class="email" :label="t('fieldEmail')" prop="mobile">
      <el-input v-model.trim="formData.mobile" :placeholder="t('fieldEmailPlaceholder')" clearable />
    </el-form-item>
    <!-- 滑块验证 -->
    <el-form-item>
      <WangyiVerify ref="wangyiVerify" ncwidth="100%" @success="verifySuccess" @error="verifyFail" />
    </el-form-item>
    <!-- 验证码 -->
    <el-form-item class="get-code-wrapper" :label="t('field2')" prop="smsCode">
      <div class="flex w-full">
        <el-input
          v-model.trim="formData.smsCode"
          name="sd-code"
          class="code-input flex-1 mr-4"
          :placeholder="t('filed2Placeholder')"
          maxlength="6"
          clearable
          @input="(e) => handleInput(e, 'smsCode')"
        />
        <el-button
          element-loading-cover
          class="get-code-btn shrink-0"
          :class="{ 'skip-translate': leftSeconds > 0 }"
          :disabled="leftSeconds > 0"
          :loading="codeLoading"
          @click="getCode"
        >
          {{ leftSeconds > 0 ? t('countdown', { countdown: leftSeconds }) : t('sendCaptcha') }}
        </el-button>
      </div>
    </el-form-item>
    <!-- 密码 -->
    <el-form-item :label="t('field3')" prop="password">
      <el-input v-model.trim="formData.password" type="password" show-password clearable :placeholder="t('filed3Placeholder')" />
    </el-form-item>
    <!-- 确认密码 -->
    <el-form-item :label="t('field4')" prop="confirmPassword">
      <el-input v-model.trim="formData.confirmPassword" type="password" show-password clearable :placeholder="t('filed4Placeholder')" />
    </el-form-item>
    <template v-if="isNormal">
      <!-- 企业名称 -->
      <el-form-item :label="t('field5')" prop="companyName">
        <el-input v-model="formData.companyName" maxlength="50" clearable :placeholder="t('filed5Placeholder')" />
      </el-form-item>
    </template>
    <!-- 邀请码 -->
    <el-form-item :label="t('field6')" prop="inviteCode" v-if="isNormal || userType === MERCHANTS_TYPE.SELLER.id">
      <el-input
        v-model.trim="formData.inviteCode"
        :value="formData.inviteCode"
        clearable
        :placeholder="t('filed6Placeholder')"
        :disabled="!!props.inviteCode"
      />
    </el-form-item>
    <!-- 协议 -->
    <el-form-item label="" prop="isAgree">
      <el-checkbox-group v-model="formData.isAgree">
        <el-checkbox value="Online activities" name="isAgree">
          <span :class="isH5 ? 'text-[12px] h5-small-text' : ''">
            <span class="mx-1">{{ t('agree') }}</span>
            <a :href="REGISTER_FILE_ADDRESS[$i18n.fallbackLocale]" target="_blank" class="link-text">{{ t('servicAgree') }}</a>
            <span class="mx-1">{{ t('and') }}</span>
            <a :href="PRIVACY_FILE_ADDRESS[$i18n.fallbackLocale]" target="_blank" class="link-text">{{ t('secretAgree') }}</a>
          </span>
        </el-checkbox>
      </el-checkbox-group>
    </el-form-item>
    <el-button type="primary" class="login-btn" @click="handleRegister" :loading="submitLoading">注册</el-button>
    <div class="text-base text-center c-#D8131A" v-if="!(isH5 && isNormal)">
      <span class="cursor-pointer" @click="handleBackLogin">{{ t('backToLogin') }}</span>
    </div>

    <LoginPrefixIdSelect v-model="formData.prefixId" ref="loginPrefixIdSelectRef" :prefix-id-list="prefixIdList" />
  </el-form>
</template>

<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import LoginPrefixIdSelect from '@/pc/components/login-prefixId-select/login-prefixId-select.vue'
import WangyiVerify from '@/pc/components/verify/wangyi-verify.vue'
import { SMS_SEND_WAY } from '@/constants'
import { PRIVACY_FILE_ADDRESS, REGISTER_FILE_ADDRESS } from '@/constants/agreement-files'
import { ExtendKey, ExtendValue } from '@/constants/common'
import { MERCHANTS_TYPE } from '@/constants/merchants'
import useLoginPrefix from '@/pc/hooks/useLoginPrefix'
import useShowToast from '@/pc/hooks/useShowToast'
import { smsSend, userRegister } from '@/apis/merchants'
import { trimParamsChangeOrigin } from '@/utils/utils'

const { showMessage } = useShowToast()

const props = defineProps({
  rulesFn: {
    type: Function,
    default: null,
  },
  I18nConfig: {
    type: Object,
    default: () => ({}),
  },
  isNormal: {
    type: Boolean,
    default: false,
  },
  isH5: {
    type: Boolean,
    default: false,
  },
  userType: {
    type: Number,
    default: null,
  },
  inviteCode: {
    type: [String, Number],
    default: null,
  },
})
const Emits = defineEmits(['goBack', 'loginSuccess'])

const { t } = useI18n(props.I18nConfig)
const route = useRoute()
const formData = reactive({
  mobile: '', // 手机号或邮箱
  smsCode: '', // 验证码
  password: '', // 密码
  confirmPassword: '', // 密码
  isAgree: [],
  inviteCode: route?.query?.referralCode,
  companyName: null,
  prefixId: '2',
})
// 0 手机号 1 邮箱
const accountType = ref(0)
const loginOptions = ref([
  { label: '手机号', value: 0 },
  { label: '邮箱', value: 1 },
])
const { prefixIdList, getPrefixIdList } = useLoginPrefix(false)
const handleAccountTypeChange = (value) => {
  accountType.value = value
  if (formData.mobile) formData.mobile = ''
}
// h5
const loginPrefixIdSelectRef = ref(null)
const handleLoginPrefixIdSelect = () => {
  if (props.isH5) {
    loginPrefixIdSelectRef.value.showModal()
  }
}
const registerRef = ref(null)

const rules = computed(() => (typeof props.rulesFn === 'function' ? props.rulesFn(formData, accountType.value) || {} : {}))

// 滑块组件 ref
const wangyiVerify = ref()
// 滑动验证信息
const verifyInfo = reactive({
  verifyData: null,
  verifyStatus: null,
})
// 滑块验证成功后触发
const verifySuccess = (data) => {
  verifyInfo.verifyData = data
  verifyInfo.verifyStatus = true
}
// 滑块重置
const verifyReset = () => wangyiVerify.value && wangyiVerify.value.ncRefresh()
// 滑块验证失败后触发
const verifyFail = () => {
  verifyInfo.verifyStatus = false
}

// 限制只能输入数字
const handleInput = (val, key) => {
  formData[key] = val.replace(/\D/g, '')
}

// 倒计时
const leftSeconds = ref(0)
const timer = ref(null)
const startCountDown = () => {
  leftSeconds.value = 60
  const countDown = () => {
    timer.value = setTimeout(() => {
      leftSeconds.value -= 1
      leftSeconds.value > 0 && countDown()
    }, 1000)
  }
  countDown()
}
const codeLoading = ref(false)
// 点击获取验证码
const sendCode = async () => {
  const body = {
    account: formData.mobile,
    prefixId: accountType.value || formData.prefixId,
    type: SMS_SEND_WAY.REGISTER.id,
    userType: props.userType,
  }
  try {
    codeLoading.value = true
    await smsSend(body)
    showMessage({
      message: '验证码发送成功',
      type: 'success',
    })
    startCountDown()
  } catch (error) {
    verifyReset() // 将滑动验证重置为初始状态
    verifyInfo.verifyStatus = false
    formData.smsCode = ''
  } finally {
    codeLoading.value = false
  }
}
// 获取验证码
const getCode = () => {
  registerRef.value.validateField('mobile', (isValid) => {
    if (isValid) {
      if (verifyInfo.verifyStatus) {
        // 校验否滑动成功滑块
        sendCode()
      } else {
        showMessage({
          message: t('verify'),
          type: 'warning',
        })
      }
    }
  })
}

const submitLoading = ref(false)
// 注册
const handleRegister = async () => {
  try {
    if (!verifyInfo.verifyStatus) {
      showMessage({
        message: t('verify'),
        type: 'warning',
      })
      return
    }
    trimParamsChangeOrigin(formData)
    await registerRef.value.validate()
    submitLoading.value = true
    const formDataVal = {
      userName: formData.mobile,
      code: formData.smsCode,
      password: formData.password,
      inviteCode: formData.inviteCode,
      companyName: formData.companyName,
      userType: props.userType,
      prefixId: accountType.value || formData.prefixId,
      uniqueCode: route?.query?.uniqueCode,
      source: +route?.query?.uniqueType || null,
      extendValue: route?.query?.extendValue || localStorage.getItem(ExtendKey) || null,
      extendKey: route?.query?.extendKey || localStorage.getItem(ExtendValue) || null,
    }
    const data = await userRegister(formDataVal)
    await Emits('loginSuccess', data, 'register')
    registerRef.value.resetFields()
  } catch (e) {
    console.log(e)
  } finally {
    submitLoading.value = false
  }
}
// 返回登录
const handleBackLogin = () => {
  Emits('goBack')
}

const filterVal = ref('')
const prefixOptions = computed(() => {
  const val = filterVal?.value?.trim()?.toLowerCase()
  return prefixIdList.value.filter((item) => {
    return String(item?.prefix).includes(val) || item?.desc?.toLowerCase()?.includes(val)
  })
})
const filterPrefix = (val) => {
  filterVal.value = val
}

onMounted(() => {
  setTimeout(() => {
    getPrefixIdList()
  }, 500)
})

watch(
  () => props.inviteCode,
  (val) => {
    if (val && !formData.inviteCode) {
      formData.inviteCode = props.inviteCode
    }
  },
  {
    immediate: true,
  },
)
</script>
