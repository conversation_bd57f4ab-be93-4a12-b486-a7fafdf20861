<template>
  <div>
    <div v-if="showSaleNum">
      <el-tooltip :content="saleStr" placement="top"> {{ saleStr }}</el-tooltip>
    </div>
    <div v-else-if="goodsInfo.collectCount" :title="goodsInfo.collectCount + $t('mall.onSale')" dir="ltr">
      {{ goodsInfo.collectCount }}<span v-mode="localEnum.onSale">想买</span>
    </div>
  </div>
</template>
<script setup>
const props = defineProps({
  showSaleNum: {
    type: Boolean,
    default: false,
  },
  goodsInfo: {
    type: Object,
    default: () => {},
  },
})
const localEnum = {
  onSale: {
    zh: '想买',
    en: ' Like',
    ar: 'أريد',
    thai: 'ซื้อ',
    indonesian: ' Beli',
    ru: 'Хочу',
  },
}
const saleStr = ref('')
const insertCharacter = (str, char, index) => {
  if (str.slice(index) == '0') {
    return str.slice(0, index)
  }
  return str.slice(0, index) + char + str.slice(index)
}
const getSaleNum = (data) => {
  if (!data?.saleNum) {
    return
  }
  let str = ''
  if (data?.saleNum < 50) {
    str = `${data.saleMinNum}件起批`
  } else if (data.saleNum >= 50 && data.saleNum < 100) {
    if (data.saleNum == parseInt(data.saleNum / 10) * 10) {
      str = `已售${data.saleNum}件`
    } else {
      str = `已售${parseInt(data.saleNum / 10) * 10}+件`
    }
  } else if (data.saleNum >= 100 && data.saleNum < 10000) {
    if (data.saleNum == parseInt(data.saleNum / 100) * 100) {
      str = `已售${data.saleNum}件`
    } else {
      str = `已售${parseInt(data.saleNum / 100) * 100}+件`
    }
  } else if (data.saleNum >= 10000) {
    if (data.saleNum == parseInt(data.saleNum / 1000) * 1000) {
      let numstr = parseInt(data.saleNum / 1000) + ''

      str = `已售` + insertCharacter(numstr, '.', numstr.length - 1) + `万件`
    } else {
      let numstr = parseInt(data.saleNum / 1000) + ''
      str = `已售` + insertCharacter(numstr, '.', numstr.length - 1) + `万+件`
    }
  }
  return str
}
watch(
  () => props.goodsInfo,
  (value) => {
    saleStr.value = getSaleNum(value)
  },
  { immediate: true },
)
</script>
