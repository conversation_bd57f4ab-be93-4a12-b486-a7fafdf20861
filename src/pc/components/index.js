import ImgLoader from '@/components/img-loader/index.vue'
import BorderTitle from './border-title/border-title.vue'
import CSymbol from './c-symbol/c-symbol.vue'
import CTable from './c-table/table.vue'
import Icon from './icon/icon.vue'
import ImgUpload from './img-upload/img-upload.vue'
import ImgUploads from './img-upload/img-uploads.vue'
import Popover from './popover/popover.vue'
import Tooltip from './tooltip/tooltip.vue'

const components = {
  Icon,
  Popover,
  CTable,
  Tooltip,
  ImgUpload,
  ImgUploads,
  BorderTitle,
  ImgLoader,
  CSymbol,
}

export default {
  install: (app) => {
    Object.keys(components).forEach((comName) => {
      app.component(comName, components[comName])
    })
  },
}
