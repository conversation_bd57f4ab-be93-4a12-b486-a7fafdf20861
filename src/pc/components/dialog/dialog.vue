<style lang="scss">
// 手动混淆
.dialog-DLmzNhtq {
  --el-dialog-padding-primary: 0;
  --el-bg-color: transparent;

  .el-dialog__header {
    display: none;
  }
}
</style>

<template>
  <el-dialog
    class="dialog-DLmzNhtq"
    :model-value="props.modelValue"
    :width="width"
    append-to-body
    align-center
    @close="emits('update:modelValue', false)"
    @closed="closed"
  >
    <slot />
  </el-dialog>
</template>

<script setup>
const props = defineProps({
  modelValue: {
    required: true,
    type: Boolean,
  },
  width: {
    type: [String, Number],
    default: '1200',
  },
})
const emits = defineEmits(['update:modelValue', 'closed'])

const closed = () => {
  emits('closed')
}
</script>
