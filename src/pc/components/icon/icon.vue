<!-- 图标组件 -->
<style lang="scss" scoped>
.svg-icon {
  display: inline-block;
  width: 1em;
  height: 1em;
  line-height: 1em;
  vertical-align: -0.15em;
}

.svg-icon-inner {
  overflow: hidden;
  width: 100%;
  height: 100%;
  fill: currentcolor;
  stroke: currentcolor;
}
</style>

<template>
  <i class="svg-icon" :class="type" :style="style" @click="handleClick">
    <svg class="svg-icon-inner" aria-hidden="true">
      <use :xlink:href="`#${type}`"></use>
    </svg>
  </i>
</template>

<script>
export default {
  name: 'rr-icon',
  props: {
    // 类型，图标名
    type: {
      type: String,
      require: true,
    },
    // 大小，px 值，如 16
    size: [Number, String],
  },
  emits: ['click'],
  computed: {
    // svg 的样式
    style() {
      const style = {}
      if (this.size) {
        style.fontSize = `${this.size}px`
      }
      return style
    },
  },
  methods: {
    // 点击图标时触发
    handleClick(e) {
      this.$emit('click', e)
    },
  },
}
</script>
