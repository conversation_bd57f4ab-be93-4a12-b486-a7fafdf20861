<!-- 用户中心页面 -->
<style lang="scss" scoped>
.icon-test {
  padding: 20px 0;
}

.icon-list {
  color: $primary-color;
}
</style>

<template>
  <div class="icon-test">
    <el-button type="primary" :loading="loading" @click="loading = !loading"> 测试按钮 </el-button>
    <div class="icon-list">
      <Icon type="icon-sd-logo" :size="200" />
    </div>
  </div>
</template>

<script>
import Icon from './icon.vue'

export default {
  name: 'icon-test',
  components: {
    Icon,
  },
  data() {
    return {
      loading: false,
    }
  },
}
</script>
