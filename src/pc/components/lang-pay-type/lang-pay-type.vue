<!--
 * @Date: 2025-05-23 15:20:00
 * @FilePath: /trade-exhibition/src/pc/components/lang-pay-type/lang-pay-type.vue
 * @Description: 切换币种与切换语言 头部组件
-->
<template>
  <div class="flex items-center mr-16px" v-bind="$attrs" v-if="payPriceOptions.length > 0">
    <el-dropdown :hide-on-click="false" :hide-timeout="800" popper-class="g-dropdown">
      <div class="flex items-center cursor-pointer text-white">
        <Icon type="icon-duoyuyan1" :size="22" />
        <div class="text-white skip-translate ml-8px" :data-name="langAndPayTypeName">{{ langAndPayTypeName }}</div>
      </div>
      <template #dropdown>
        <div class="px-24px py-16px text-[#505259] text-14px w-375px">
          <div class="text-[#1A1A1A] text-16px font-500">设置语言和货币</div>
          <div class="font-500 my-16px">请选择您的首选语言和货币。您可以随时更新设置。</div>
          <div class="mb-4px">语言</div>
          <el-select class="skip-translate" v-model="langType" @change="onChangeLang" :teleported="false">
            <el-option v-for="item in langArr" :key="item.id" :label="item.name" :value="item.key" />
          </el-select>
          <div class="mt-16px mb-4px">货币</div>
          <el-select v-model="payPriceType" @change="onChangePayPrice" :teleported="false">
            <el-option v-for="item in payPriceOptions" :key="item.id" :label="item.label" :value="item.id">
              <span class="skip-translate">{{ item.currency }}</span
              >-<span>{{ item.value }}</span>
            </el-option>
            <template #label>
              <span class="skip-translate">{{ fitPayPriceOptions().currency }}</span
              >-<span>{{ fitPayPriceOptions().value }}</span>
            </template>
          </el-select>
          <el-button type="primary" class="w-full mt-16px" size="large" @click="confirmHandle">保存</el-button>
        </div>
      </template>
    </el-dropdown>
  </div>
</template>
<script setup>
import { LANG_TYPE_ARRAY, PC_LANG_TYPE_MAP, PC_LANG_TYPE_MAP_KEY } from '@/constants/mall'
import { useDictStore } from '@/pc/stores'
import { useNation } from '@/hooks/useNation'
import { useEvent } from '@/event'
import { LANG_CHANGED } from '@/event/modules/site.js'
import { storageKey, useStorageLocale } from '@/i18n'
import user from '@/pc/utils/user'

const event = useEvent()
const { isNation } = useNation()
const { storageLocale } = useStorageLocale()

const langArr = computed(() => {
  if (isNation('uae')) {
    return LANG_TYPE_ARRAY.filter((item) => ['zh', 'en'].includes(item.subKey))
  }
  if (isNation('idn')) {
    return LANG_TYPE_ARRAY.filter((item) => ['zh', 'en', 'indonesian'].includes(item.subKey))
  }
  return LANG_TYPE_ARRAY
})

const payPriceType = ref(user.getPayPrice())
const langType = ref(PC_LANG_TYPE_MAP_KEY[storageLocale.value || 'zh'])
const dictStore = useDictStore()
const fitPayPriceOptions = () => {
  const item = dictStore.payPriceOptions.filter((el) => el.id === payPriceType.value) || []
  if (item.length > 0) {
    return {
      currency: item[0].currency || '',
      value: item[0].value || '',
    }
  }
  return {
    currency: '',
    value: '',
  }
}
try {
  dictStore.getPayPrice()
  dictStore.getPriceTypeList()
} catch (e) {
  console.log(e)
}

const payPriceOptions = computed(
  () =>
    dictStore.payPriceOptions.map((el) => {
      return {
        label: `${el.currency}-${el.value}`,
        ...el,
      }
    }) || [],
)

function getLangAndPayTypeName() {
  const item = payPriceOptions.value.find((i) => i.id === payPriceType.value)
  let preLang = ''
  // 为了解决设置后，语言会展示undefined的问题
  if (PC_LANG_TYPE_MAP[storageLocale.value]) {
    preLang = PC_LANG_TYPE_MAP[storageLocale.value] || ''
  }
  return `${preLang}-${item?.currency || ''}`
}
// 获取语言和货币名称
let langAndPayTypeName = ref('')
// 只监听一次币种的变化
const unwatch = watchEffect(
  () => {
    langAndPayTypeName.value = getLangAndPayTypeName()
    if (payPriceOptions.value?.length > 0) {
      unwatch()
    }
  },
  {
    flush: 'post', // 控制执行时机
  },
)

const onChangePayPrice = async (item) => {
  payPriceType.value = item
}

// 切换语言
const onChangeLang = async (item) => {
  langType.value = item
}
// const userStore = useUserStore()
const confirmHandle = async () => {
  window.translate?.changeLanguage(langType.value)
  let subKey = ''
  langArr.value.forEach((el) => {
    if (langType.value === el.key) {
      subKey = el.subKey
    }
  })
  localStorage.setItem(storageKey, subKey)
  event.emit(LANG_CHANGED, subKey)
  user.setPayPrice(payPriceType.value)
  // 如果登录了 再设置
  // if (userStore.isLogined) {
  //   await setUserConfig({
  //     priceType: payPriceType.value,
  //   })
  // }
  window.location.reload()
  document.documentElement.setAttribute('dir', subKey === 'ar' ? 'rtl' : 'ltr')
}
</script>
<style lang="scss" scoped>
.white-bg {
  .text-white {
    color: #000;
  }
}
</style>
