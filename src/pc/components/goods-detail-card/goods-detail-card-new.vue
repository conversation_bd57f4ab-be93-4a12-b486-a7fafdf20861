<!-- 新的卡片页面，修改字段名，适应新接口 -->
<template>
  <div class="rec-offer" @click="GoodsDetail(goodsInfo)" v-track="{ rid: goodsInfo?.rid, goods_id: goodsInfo?.id, scene, m: 'goods', isValidTrack: !!scene }">
    <a :href="`${baseAddress}`">
      <div class="goods-img-wrapper relative" :style="imgStyle">
        <img-loader
          :src="`${goodsPic}?x-oss-process=image/resize,h_478`"
          alt="暂无图片"
          class="w-full h-full object-cover rounded-t-2"
          loadingImg="/mall/errorImg.png"
          errorImg="/mall/errorImg.png"
        ></img-loader>
        <slot name="tag"></slot>
      </div>
      <div class="goods-content-wrapper">
        <slot>
          <div class="offer-title">{{ goodsInfo.spuName }}</div>

          <span class="offer-tag s"></span>
          <div class="flex justify-between items-end">
            <span class="offer-price">
              <!-- 数字价格 -->
              <i v-if="typeof goodsPrice === 'number'" class="price" :title="`${PRICE_TYPE_MAP[goodsInfo.priceType]}${goodsPrice}`">
                <em class="symbol"><c-symbol /></em>
                <em class="number n-b">{{ priceFilter.radixPointBefore(goodsPrice) }}</em>
                <em class="number">{{ priceFilter.radixPointAfter(goodsPrice) }}</em>
              </i>
              <!-- 字符串价格 -->
              <i v-else class="price" :title="`${goodsPrice}`">
                <em :class="`number n-b ${goodsPrice && goodsPrice.length > 3 ? 'number-small-text' : ''}`">{{ goodsPrice }}</em>
              </i>
            </span>
            <div v-if="showSaleNum" class="color-[#999] font-[12px] max-w-[90px] whitespace-nowrap text-ellipsis overflow-hidden ml-2px">
              <el-tooltip :content="getSaleNum(goodsInfo)" placement="top"> {{ getSaleNum(goodsInfo) }}</el-tooltip>
            </div>
            <div
              v-else-if="goodsInfo.collectCount"
              class="color-[#999] font-[12px] whitespace-nowrap text-ellipsis overflow-hidden ml-2px"
              :title="goodsInfo.collectCount + $t('mall.onSale')"
              dir="ltr"
            >
              {{ goodsInfo.collectCount }}<span v-mode="localEnum.onSale">想买</span>
            </div>
          </div>
        </slot>
      </div>
    </a>
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router'
import { PRICE_TYPE_MAP } from '@/constants/goods'
import { priceFilter } from '@/utils/utils.js'

const localEnum = {
  onSale: {
    zh: '想买',
    en: ' Like',
    ar: 'أريد',
    thai: 'ซื้อ',
    indonesian: ' Beli',
    ru: 'Хочу',
  },
}

const route = useRoute()
const baseAddress = computed(() => `${import.meta.env.VUE_APP_WEB_URL}${route.fullPath}`)

const router = useRouter()
const props = defineProps({
  goodsInfo: {
    type: Object,
    default: () => ({}),
  },
  customClick: {
    type: Boolean,
    default: false,
  },
  pic: {
    type: String,
    default: '',
  },
  imgStyle: {
    type: String,
    default: '',
  },
  shopId: {
    type: String,
    default: '',
  },
  showSaleNum: {
    type: Boolean,
    default: false,
  },
  scene: {
    type: String,
    default: '',
  },
})

const goodsPic = computed(() => props.pic || props.goodsInfo?.spuImages?.split(',')[0] || '')
const goodsPrice = computed(() => props.goodsInfo?.minPrice || '')

const GoodsDetail = (goodsInfo) => {
  if (props.customClick) return
  if (goodsInfo?.origin === 1) {
    window.open(goodsInfo.goodsUrl)
    return
  }

  const query = props.shopId ? { shopId: props.shopId } : {}
  const url = router.resolve({ path: `/mall/goods-detail/${goodsInfo.id}`, query })
  window.open(url.href, '_blank')
  // router.push({
  //   name: 'mall-goods-detail',
  //   params: {
  //     id,
  //   },
  // })
}

// const showQRCode = ref(false)
const insertCharacter = (str, char, index) => {
  if (str.slice(index) == '0') {
    return str.slice(0, index)
  }
  return str.slice(0, index) + char + str.slice(index)
}
const getSaleNum = (data) => {
  let str = ''
  if (data.saleNum < 50) {
    str = `${data.saleMinNum}件起批`
  } else if (data.saleNum >= 50 && data.saleNum < 100) {
    if (data.saleNum == parseInt(data.saleNum / 10) * 10) {
      str = `已售${data.saleNum}件`
    } else {
      str = `已售${parseInt(data.saleNum / 10) * 10}+件`
    }
  } else if (data.saleNum >= 100 && data.saleNum < 10000) {
    if (data.saleNum == parseInt(data.saleNum / 100) * 100) {
      str = `已售${data.saleNum}件`
    } else {
      str = `已售${parseInt(data.saleNum / 100) * 100}+件`
    }
  } else if (data.saleNum >= 10000) {
    if (data.saleNum == parseInt(data.saleNum / 1000) * 1000) {
      let numstr = parseInt(data.saleNum / 1000) + ''

      str = `已售` + insertCharacter(numstr, '.', numstr.length - 1) + `万件`
    } else {
      let numstr = parseInt(data.saleNum / 1000) + ''
      str = `已售` + insertCharacter(numstr, '.', numstr.length - 1) + `万+件`
    }
  }
  return str
}
</script>

<style lang="scss" scoped>
.rec-offer {
  position: relative;
  // padding: 12px;
  background-color: #fff;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-sizing: border-box;
  border: 1px solid #edeef1;

  .offer-title {
    transition: all 0.3s ease;
  }

  &:hover {
    border-color: $primary-color;

    :deep(img) {
      border-radius: 8px 8px 0 0;
      // transform: scale(1.01);
    }

    .offer-title {
      color: $primary-color;
    }
  }

  .goods-img-wrapper {
    overflow: hidden;
    height: 244px;
    border-bottom: 1px solid #edeef1;

    :deep(img) {
      width: 100%;
      height: 100%;
      -o-object-fit: cover !important;
      object-fit: cover !important;
      -o-object-position: center !important;
      object-position: center !important;
      transition: all 0.3s ease;
    }
  }
  .qr-code-wrap {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    border-radius: 8px;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
    background: rgba(0, 0, 0, 0.6);
    img {
      width: 216px;
      height: 216px;
      margin-bottom: 10px;
    }
  }
  .goods-content-wrapper {
    overflow: hidden;
    // height: 88px;
    padding: 12px 10px;
    box-sizing: border-box;
  }
  .offer-title {
    height: 40px;
    line-height: 20px;
    font-size: 14px;
    color: $color-333333;
    @include ellipsis(2);
  }
  .label-wrapper {
    display: flex;
    flex-wrap: wrap;

    span {
      padding: 2px 8px;
      margin-right: 8px;
      font-size: 12px;

      &.free-label {
        background: $color-FFE7E7;
        color: $primary-color;
      }

      &.deliver-goods-time {
        background: $color-F1F6FF;
        color: $color-1D5DDC;
      }
    }
  }
  .offer-price {
    display: flex;
    margin-top: 6px;
    flex-direction: row;
    flex-wrap: nowrap;
    white-space: nowrap;
    justify-content: space-between;

    .price {
      display: block;
      color: $primary-color;
      font-size: 24px;
      // line-height: 24px;
      .symbol {
        font-size: 14px;
        // line-height: 22px;
        font-style: normal;
      }
      .number {
        font-size: 16px;
        font-style: normal;
        &.n-b {
          font-size: 24px;
          line-height: 32px;
        }

        &.number-small-text {
          font-size: 20px;
        }
      }
    }
    .slot {
      line-height: 32px;
      color: $color-999999;
      font-size: 12px;
    }
  }
  .business-wrapper {
    .business-name {
      margin-bottom: 6px;
      width: 100%;
      font-size: 14px;
      color: $color-666666;
      @include ellipsis;
    }
    .business-address {
      width: 100%;
      font-size: 14px;
      color: $color-999999;
      @include ellipsis;
    }
  }
}
</style>
