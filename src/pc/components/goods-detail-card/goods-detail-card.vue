<!--
 * @Author: 李兵 <EMAIL>
 * @Date: 2024-08-13 14:59:44
 * @LastEditors: your name
 * @LastEditTime: 2024-12-21 10:59:08
 * @FilePath: /trade-exhibition/src/pc/components/goods-detail-card/goods-detail-card.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%A
-->
<template>
  <div class="rec-offer" @click="GoodsDetail(goodsInfo)">
    <a :href="`${baseAddress}`">
      <div class="goods-img-wrapper relative" :style="imgStyle" :class="imgModeClass">
        <img-loader
          :src="`${goodsPic}?x-oss-process=image/resize,h_478`"
          alt="暂无图片"
          class="w-full h-full object-cover rounded-t-2"
          loadingImg="/mall/errorImg.png"
          errorImg="/mall/errorImg.png"
        />
        <slot name="tag"></slot>
      </div>
      <!--    <div v-show="goodsInfo.origin !== 1 && showQRCode" class="qr-code-wrap flex flex-col justify-center items-center">-->
      <!--      <img :src="`${ossUrl}/wechat-work-qrcode.png`" alt="" srcset="" />-->
      <!--      <div>联系客服</div>-->
      <!--    </div>-->
      <div class="goods-content-wrapper">
        <slot>
          <div class="offer-title">{{ goodsInfo.goodsTitle || goodsInfo.goodsName }}</div>

          <span class="offer-tag s"></span>
          <div class="flex justify-between items-end">
            <span class="offer-price">
              <!-- 数字价格 -->
              <i v-if="typeof goodsPrice === 'number'" class="price" :title="`¥${goodsPrice}`">
                <em class="symbol">￥</em>
                <em class="number n-b">{{ priceFilter.radixPointBefore(goodsPrice) }}</em>
                <em class="number">{{ priceFilter.radixPointAfter(goodsPrice) }}</em>
              </i>
              <!-- 字符串价格 -->
              <i v-else class="price" :title="`${goodsPrice}`">
                <em :class="`number n-b ${goodsPrice && goodsPrice.length > 3 ? 'number-small-text' : ''}`">{{ goodsPrice }}</em>
              </i>
            </span>

            <div
              v-if="goodsInfo.followNum"
              class="color-[#999] font-[12px] whitespace-nowrap text-ellipsis overflow-hidden"
              :title="goodsInfo.followNum + $t('mall.onSale')"
              dir="ltr"
            >
              {{ goodsInfo.followNum }}<span v-mode="localEnum.onSale">想买</span>
            </div>
          </div>
        </slot>
        <!-- <div class="business-wrapper">
        <div class="business-name">{{ goodsInfo.shopName }}</div>
        <div class="business-address">山东省临沂市兰山区深度票据网山东省临沂市兰山区深度票据网山东省临沂市兰山区深度票据网</div>
      </div> -->
      </div>
    </a>
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router'
import { priceFilter } from '@/utils/utils.js'

// import { ossUrl } from '@/constants/common'

const route = useRoute()
const baseAddress = computed(() => `${import.meta.env.VUE_APP_WEB_URL}${route.fullPath}`)

const localEnum = {
  onSale: {
    zh: '想买',
    en: ' Like',
    ar: 'أريد',
    thai: 'ซื้อ',
    indonesian: ' Beli',
  },
}

const router = useRouter()
const props = defineProps({
  goodsInfo: {
    type: Object,
    default: () => ({}),
  },
  customClick: {
    type: Boolean,
    default: false,
  },
  pic: {
    type: String,
    default: '',
  },
  imgStyle: {
    type: String,
    default: '',
  },
  imgModeClass: {
    type: String,
    default: '',
  },
})

const goodsPic = computed(() => props.pic || props.goodsInfo?.goodsPictureUrls?.[0] || '')
const goodsPrice = computed(() => parseFloat(props.goodsInfo?.skuList?.[0]?.price) || props.goodsInfo?.skuList?.[0]?.price)

const GoodsDetail = (goodsInfo) => {
  if (props.customClick) return
  if (goodsInfo?.origin === 1) {
    window.open(goodsInfo.goodsUrl)
    return
  }

  const url = router.resolve({ path: `/mall/goods-detail/${goodsInfo.id}` })
  window.open(url.href, '_blank')
  // router.push({
  //   name: 'mall-goods-detail',
  //   params: {
  //     id,
  //   },
  // })
}

// const showQRCode = ref(false)
</script>

<style lang="scss" scoped>
.rec-offer {
  position: relative;
  // padding: 12px;
  background-color: #fff;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-sizing: border-box;
  border: 1px solid #edeef1;

  .offer-title {
    transition: all 0.3s ease;
  }

  &:hover {
    border-color: $primary-color;

    :deep(img) {
      border-radius: 8px 8px 0 0;
      // transform: scale(1.01);
    }

    .offer-title {
      color: $primary-color;
    }
  }

  .goods-img-wrapper {
    overflow: hidden;
    height: 244px;
    border-bottom: 1px solid #edeef1;

    &.img-contain {
      :deep(img) {
        -o-object-fit: contain !important;
        object-fit: contain !important;
      }
    }

    :deep(img) {
      width: 100%;
      height: 100%;
      -o-object-fit: cover !important;
      object-fit: cover !important;
      -o-object-position: center !important;
      object-position: center !important;
      transition: all 0.3s ease;
    }
  }
  .qr-code-wrap {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    border-radius: 8px;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
    background: rgba(0, 0, 0, 0.6);
    img {
      width: 216px;
      height: 216px;
      margin-bottom: 10px;
    }
  }
  .goods-content-wrapper {
    overflow: hidden;
    // height: 88px;
    padding: 12px 10px;
    box-sizing: border-box;
  }
  .offer-title {
    height: 40px;
    line-height: 20px;
    font-size: 14px;
    color: $color-333333;
    @include ellipsis(2);
  }
  .label-wrapper {
    display: flex;
    flex-wrap: wrap;

    span {
      padding: 2px 8px;
      margin-right: 8px;
      font-size: 12px;

      &.free-label {
        background: $color-FFE7E7;
        color: $primary-color;
      }

      &.deliver-goods-time {
        background: $color-F1F6FF;
        color: $color-1D5DDC;
      }
    }
  }
  .offer-price {
    display: flex;
    margin-top: 6px;
    flex-direction: row;
    flex-wrap: nowrap;
    white-space: nowrap;
    justify-content: space-between;

    .price {
      max-width: 110px;
      display: block;
      color: $primary-color;
      font-size: 24px;
      // line-height: 24px;
      .symbol {
        font-size: 14px;
        // line-height: 22px;
        font-style: normal;
      }
      .number {
        font-size: 16px;
        font-style: normal;
        &.n-b {
          font-size: 24px;
          line-height: 32px;
        }

        &.number-small-text {
          font-size: 20px;
        }
      }
    }
    .slot {
      line-height: 32px;
      color: $color-999999;
      font-size: 12px;
    }
  }
  .business-wrapper {
    .business-name {
      margin-bottom: 6px;
      width: 100%;
      font-size: 14px;
      color: $color-666666;
      @include ellipsis;
    }
    .business-address {
      width: 100%;
      font-size: 14px;
      color: $color-999999;
      @include ellipsis;
    }
  }
}
</style>
