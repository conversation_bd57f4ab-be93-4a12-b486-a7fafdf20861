<!--
 * @Date: 2025-05-23 15:20:00
 * @FilePath: /trade-exhibition/src/pc/components/cart-symbol/cart-symbol.vue
 * @Description: 购物车头部组件
-->
<template>
  <div class="flex items-center h-full" v-bind="$attrs">
    <div class="text-white mr-16px cursor-pointer hover:text-opacity-80 flex items-center" @click="toCart" :data-count="count">
      <Icon type="icon-new-gouwuche" :size="21" />
      <!-- {{ count }} -->
    </div>
    <div class="text-white mr-16px cursor-pointer hover:text-opacity-80 flex items-center" @click="toOrder">
      <Icon type="icon-new-dingdan" :size="20" />
    </div>
  </div>
</template>
<script setup>
import { useCartStore } from '@/pc/stores'
import { useUserStore } from '@/pc/stores'
import { useEvent } from '@/event'
import { OPEN_NEW_LOGIN } from '@/event/modules/site'

const router = useRouter()
const event = useEvent()

const userStore = useUserStore()
const toCart = () => {
  if (!userStore.isLogined) {
    event.emit(OPEN_NEW_LOGIN, {
      routerDisabled: true,
      path: '/cart/list',
    })
    return false
  } else {
    router.push('/cart/list')
  }
}

const toOrder = () => {
  if (!userStore.isLogined) {
    event.emit(OPEN_NEW_LOGIN, {
      routerDisabled: true,
      path: '/buyer-center/order-manage',
    })
  } else {
    router.push('/buyer-center/order-manage')
  }
}
const cartStore = useCartStore()
const count = computed(() => {
  return cartStore.cartCount
})
</script>
<style lang="scss" scoped>
.white-bg {
  .text-white {
    color: #000;
  }
}
</style>
