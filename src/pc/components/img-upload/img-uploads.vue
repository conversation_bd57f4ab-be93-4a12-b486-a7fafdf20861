<!-- 多图片上传组件 -->
<style lang="scss" scoped>
.img-uploads {
  display: flex;
  flex: 1;
}

.img-upload-input {
  display: none;
}

.img-upload-empty {
  display: flex;
  align-items: center;
  margin-right: 8px;
  border: 2px dashed #edeef1;
  border-radius: 2px;
  height: 100%;
  text-align: center;
  background: #f7f7f7;
  cursor: pointer;
}

.img-upload-empty-disabled {
  cursor: not-allowed;
  background-color: $color-F5F5F5;
}

.img-upload-empty-dragover {
  border-width: 2px;
  background: rgba($primary-color, 0.1);
}

.img-upload-empty-content {
  flex: 1;
  line-height: initial;
}

.img-upload-icon-plus {
  color: $primary-color;
}

.img-upload-empty-text {
  margin-top: 4px;
  font-size: 14px;
  line-height: 16px;
  color: $darker-text;
}

.img-upload-preview-container {
  position: relative;
  margin-right: 12px;
  border: 1px solid $base-border;
  border-radius: 2px;
  height: 100%;
  background: $basic-white;
}

.img-upload-preview {
  width: 100%;
  height: 100%;
}

.preview-mask-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  &:hover .preview-mask {
    display: flex;
  }
}

.preview-mask {
  display: none;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  color: $basic-white;
  background: rgba($basic-black, 0.5);
  box-shadow: 0 0 4px rgb(0 0 0 / 10%);
}

.img-upload-preview-icon,
.img-upload-delete-icon {
  cursor: pointer;
}

.img-upload-delete-icon {
  margin-left: 20px;
}
</style>

<style lang="scss">
.el-form-item.is-error .img-upload-empty {
  border-color: $primary-color;
  background: #ffeded;
  .img-upload-empty-text {
    color: $primary-color;
  }
}
</style>

<template>
  <div class="img-uploads">
    <!-- input -->
    <input ref="inputRef" :key="inputKey" type="file" class="img-upload-input" :accept="accept" @change="handleInputChange" multiple />
    <!-- 已上传文件 -->
    <div v-for="(item, index) in previewUrlList" :key="index" class="img-upload-preview-container" :style="{ height: `${height}px`, width: `${width}px` }">
      <!-- 视频类型 -->
      <div v-if="isVideo" class="img-upload-preview">
        <video ref="videoRef" class="object-cover img-upload-preview" :src="item" controls disablePictureInPicture />
      </div>
      <!-- 图片类型 -->
      <el-image ref="imageRef" class="img-upload-preview" fit="cover" :src="item" :preview-src-list="previewUrlList" v-else />
      <div class="preview-mask-container">
        <div class="preview-mask hover:bg-[rgba(0,0,0,.5)] text-white group-hover:block">
          <Icon class="img-upload-preview-icon cursor-pointer" type="icon-yulan" size="32" :title="t('preview')" @click="preview(index)" />
          <Icon
            v-if="isDelete || !disabled"
            class="img-upload-delete-icon cursor-pointer ml-[16px]"
            type="icon-shanchu"
            size="32"
            :title="t('delete')"
            @click="removeFile(index)"
          />
          <!-- <icon class="img-upload-preview-icon" type="icon-eye" size="32" title="预览" @click="preview(index)" />
          <icon v-if="isDelete || !disabled" class="img-upload-delete-icon" type="icon-delete" size="32" title="删除" @click="removeFile(index)" /> -->
        </div>
      </div>
    </div>

    <!-- 未上传文件 -->
    <div
      v-if="(previewUrlList && previewUrlList.length) < props.imgNumber"
      v-loading="uploading"
      :style="{ height: `${height}px`, width: `${width}px` }"
      class="img-upload-empty"
      :class="{
        'img-upload-empty-dragover': dragover,
        'img-upload-empty-disabled': !uploadable,
      }"
      @click="handleClick"
      @drop.prevent="onDrop"
      @dragover.prevent="onDragover"
      @dragleave.prevent="dragover = false"
    >
      <div class="img-upload-empty-content">
        <!-- <icon class="img-upload-icon-plus" type="icon-plus" :size="plusSize" /> -->
        <div class="img-upload-empty-text">
          <slot name="empty">
            {{ emptyText }}
          </slot>
        </div>
      </div>
    </div>
  </div>

  <!-- 视频预览 -->
  <PreviewVideoDialog v-if="isVideo" ref="previewVideoDialogRef" />

  <!-- 放大预览图片组件 -->
  <ImgViewer v-model:visible="isShowImageViewer" :url-list="previewUrlList" :initial-index="imgViewerIndex" :show-mini-image="true" :z-index="9999" />
</template>

<script setup lang="jsx">
import { ElMessageBox, useDisabled, useFormItem } from 'element-plus'
import { computed, onBeforeUnmount, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import ImgViewer from '@/pc/components/img-viewer/img-viewer.vue'
import PreviewVideoDialog from '@/pc/components/preview-video-dialog/preview-video-dialog.vue'
import { OSS_DIR } from '@/constants/oss-dir'
import Arms from '@/pc/utils/arms'
import { upload } from '@/utils/oss'
import { generateUUID } from '@/utils/utils'

const { t } = useI18n({
  messages: {
    zh: {
      preview: '预览',
      delete: '删除',
      unSupport: '不支持该文件类型',
      exceed: '上传的文件大小不能超过',
      uploadFailed: '上传失败',
      confirmDelete: '确定要删除吗?',
      prompt: '提示',
      confirm: '确定',
      cancel: '取消 ',
      deletedSuccessfully: '删除成功 ',
    },
    en: {
      preview: 'Preview',
      delete: 'Delete',
      unSupport: 'File type not supported',
      exceed: 'Uploaded file size cannot exceed',
      uploadFailed: 'Upload failed',
      confirmDelete: 'Are you sure you want to delete?',
      prompt: 'Prompt',
      confirm: 'Confirm',
      cancel: 'Cancel ',
      deletedSuccessfully: 'Deleted successfully ',
    },
  },
})

const props = defineProps({
  // 图片的 src，没有图片时为空字符串
  modelValue: {
    type: [String, Array],
    require: true,
  },
  imgNumber: {
    // 支持上传几张图片
    type: Number,
    default: 1,
  },
  // 未上传文件时的提示文字，也可用 empty slot
  emptyText: {
    type: String,
    default: '点击或拖拽图片至此上传图片',
  },
  disabled: Boolean, // 是否禁用
  // 是否显示删除按钮
  isDelete: {
    type: Boolean,
    default: false,
  },
  // 接受的文件类型
  accept: {
    type: String,
    // https://developer.mozilla.org/en-US/docs/Web/Media/Formats/Image_types
    default: 'image/jpeg,image/png',
  },
  beforeUpload: Function, // 上传前的回调函数, 返回 false 可以阻止上传
  onSuccess: Function, // 上传成功的回调函数
  sizeLimit: Number, // 限制的文件大小，单位为 M
  // 宽度
  width: {
    type: [Number, String],
    default: 160,
  },
  // 高度
  height: {
    type: [Number, String],
    default: 100,
  },
  plusSize: {
    type: Number,
    default: 30,
  },
  // 上传文件夹
  dir: {
    required: true,
    type: String,
    validator(dir) {
      const isValid = Object.values(OSS_DIR).some((d) => d === dir)
      if (import.meta.env.DEV && !isValid) {
        const keys = Object.keys(OSS_DIR)
          .map((d) => `OSS_DIR.${d}`)
          .join('、')
        // eslint-disable-next-line no-console
        console.error(`[img-upload]dir 属性必须为 ${keys} 其中一个`)
      }
      return isValid
    },
  },
  // 是否是视频
  isVideo: {
    type: Boolean,
    default: false,
  },
})

// data
const dragover = ref(false) // 是否鼠标悬浮
const tempUrl = ref('') // 通过 URL.createObjectURL 创建的临时 url
const uploading = ref(false) // 是否正在上传
const inputKey = ref(0) // input 的 key

const emit = defineEmits(['update:modelValue', 'change'])

// 销毁组件前，回收 url
onBeforeUnmount(() => {
  tempUrl.value && URL.revokeObjectURL(tempUrl.value)
})

// 表单 item
const elFormItem = useFormItem()

// 是否禁用
// eslint-disable-next-line vue/no-dupe-keys
const disabled = useDisabled()

// 预览图片url数组
const previewUrlList = computed(() => {
  if (typeof props.modelValue === 'string') {
    if (props.modelValue || tempUrl.value) {
      return [props.modelValue || tempUrl.value]
    }
    return []
  } else {
    return props.modelValue || []
  }
})
// 当前是否可上传
const uploadable = computed(() => !disabled.value && !uploading.value)

// input 元素的 ref
const inputRef = ref()
// 点击
const handleClick = () => {
  if (uploadable.value) {
    inputRef.value.click()
  }
}
// 清空 input 的值
const clearFile = () => {
  inputRef.value && (inputRef.value.value = '')
  // IE10 下 input.value = '' 无效，故在此将其重新渲染
  if (inputRef.value?.value) {
    inputKey.value++
  }
}
watch(
  () => props.modelValue,
  (val) => {
    // 触发表单校验
    elFormItem.formItem && elFormItem.formItem.validate('change')
    if (!val) {
      clearFile()
    }
  },
)

// 验证文件
const validateFiles = (files) => {
  if (props.accept) {
    files = [].slice.call(files).filter((file) => {
      const { type, name } = file
      const extension = name.indexOf('.') > -1 ? `.${name.split('.').pop()}` : ''
      const baseType = type.replace(/\/.*$/, '')
      return props.accept
        .split(',')
        .map((t) => t.trim())
        .filter(Boolean)
        .some((acceptedType) => {
          if (/\..+$/.test(acceptedType)) {
            return extension === acceptedType
          }
          if (/\/\*$/.test(acceptedType)) {
            return baseType === acceptedType.replace(/\/\*$/, '')
          }
          if (/^[^/]+\/[^/]+$/.test(acceptedType)) {
            return type === acceptedType
          }
          return false
        })
    })
    if (!files.length) {
      ElMessage.warning(t('unSupport'))
      return
    }
  }
  if (!files || !files.length) return
  const file = files[0]
  if (props.sizeLimit) {
    // eslint-disable-next-line no-magic-numbers
    const fileSize = file.size / 1024 / 1024
    if (fileSize >= props.sizeLimit) {
      ElMessage.error(`${t('exceed')} ${props.sizeLimit}M`)
      clearFile() // 上传完清空,解决重复上传文件不触发change事件
      return
    }
  } else if (typeof props.beforeUpload === 'function' && !props.beforeUpload(file)) {
    return
  }
  return file
}
// 上传文件
const uploadFiles = async (files) => {
  try {
    // 初始化文件数组
    const urls = []
    let urlTemp

    // 转换 files 为数组
    let uploadFiles = Array.from(files)

    // 根据条件过滤需要上传的文件数量
    const remainingSlots = props.imgNumber - (Array.isArray(props.modelValue) ? props.modelValue.length : 0)
    uploadFiles = uploadFiles.slice(0, Math.min(remainingSlots, props.imgNumber))
    uploadFiles = uploadFiles.filter((item) => !!validateFiles([item]))

    if (!uploadFiles.length) return

    for (const file of uploadFiles) {
      try {
        const truncatedFileName = generateUUID()

        // 创建一个新的文件对象，使用截取后的文件名
        const newFile = new File([file], truncatedFileName, { type: file.type })
        const url = await uploadFile([newFile])
        urls.push(url)
      } catch (e) {
        console.log(e)
      }
    }

    // 根据 modelValue 类型设置 urlTemp
    urlTemp = typeof props.modelValue === 'string' && props.imgNumber === 1 ? urls[0] : [...props.modelValue, ...urls]

    // 调用 onSuccess 并更新组件的 modelValue 和 change 事件
    if (typeof props.onSuccess === 'function') {
      await props.onSuccess(urlTemp)
    }
    emit('update:modelValue', urlTemp)
    emit('change', urlTemp)
  } catch (e) {
    console.log(e, '文件上传失败')
  }
}
const uploadFile = async (files) => {
  const fileToUpload = validateFiles(files)
  if (!fileToUpload) {
    return
  }
  uploading.value = true
  elFormItem.formItem && elFormItem.formItem.clearValidate()
  try {
    return await upload(fileToUpload, props.dir)
  } catch (e) {
    ElMessage.error(t('uploadFailed'))
    Arms.sendCustom('uploadError', 'imgUploads上传失败', {
      error: e,
    })
  } finally {
    uploading.value = false
    clearFile() // 上传完清空,解决重复上传文件不触发change事件
  }
}
// 点击上传文件
const handleInputChange = (e) => {
  const { files } = e.target
  if (!files || files.length === 0) return
  uploadFiles(files)
}
// 拖拽是鼠标悬浮
const onDragover = () => {
  if (uploadable.value) {
    dragover.value = true
  }
}
// 拖拽释放鼠标
const onDrop = (e) => {
  if (!uploadable.value) return
  dragover.value = false
  uploadFiles(e.dataTransfer.files)
}

const isShowImageViewer = ref(false)
const imgViewerIndex = ref(0)

// 视频
const previewVideoDialogRef = ref(null)
// 打开视频预览
const previewVideo = (info) => {
  previewVideoDialogRef.value.init({
    url: info,
  })
}

// 打开图片预览框
const preview = (index) => {
  if (props.isVideo) {
    previewVideo(previewUrlList.value[index])
    return
  }
  imgViewerIndex.value = index
  isShowImageViewer.value = true
}
// 删除文件
const removeFile = (index) => {
  ElMessageBox.confirm(t('confirmDelete'), t('prompt'), {
    confirmButtonText: t('confirm'),
    cancelButtonText: t('cancel'),
    type: 'warning',
  })
    .then(() => {
      clearFile()
      let urlTemp
      if (typeof props.modelValue === 'string' && props.imgNumber === 1) {
        urlTemp = ''
      } else {
        urlTemp = [...props.modelValue]
        urlTemp.splice(index, 1)
      }
      emit('update:modelValue', urlTemp)
      emit('change', urlTemp)
      ElMessage.success(t('deletedSuccessfully'))
    })
    .catch(() => {
      // 取消删除
    })
}

const getUpdateLoading = () => {
  return uploading.value
}

defineExpose({
  getUpdateLoading,
})
</script>
