<style lang="scss" scoped>
.input-number-wrapper {
  display: flex;
  border: 1px solid #ededed;
  width: 130px;
  input {
    width: 74px;
    border: none;
    text-align: center;
    &:focus-visible {
      outline: none;
    }
  }
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }
  input[type='number'] {
    -moz-appearance: textfield;
  }
  button {
    width: 28px;
    height: 28px;
    border: none;
    display: inline-block;
    cursor: pointer;
    background: transparent;
  }
}
</style>

<template>
  <div class="input-number-wrapper">
    <button :disabled="modelValue <= min" @click="handleDecrease">-</button>
    <input type="number" :value="modelValue" step="0.01" @input="handleInput" @change="handleChange" @blur="handleBlur" />
    <button :disabled="modelValue >= max" @click="handleIncrease">+</button>
  </div>
</template>

<script setup>
import { watch } from 'vue'

const props = defineProps({
  // 输入框值
  modelValue: {
    type: Number,
    default: 0,
  },
  // 最小值
  min: {
    type: Number,
    default: 0,
  },
  // 最大值
  max: {
    type: Number,
    default: 0,
  },
  step: {
    type: Number,
    default: 1,
  },
})
const emit = defineEmits(['update:modelValue', 'change', 'on-blur'])

// 点击减号
const handleDecrease = () => {
  const value = props.modelValue - props.step
  emit('update:modelValue', value)
  handleBlur()
}
// 点击加号
const handleIncrease = () => {
  const value = props.modelValue + props.step
  emit('update:modelValue', value)
  handleBlur()
}

// input 事件
const handleInput = (event) => {
  const value = parseInt(event.target.value, 10)
  if (!Number.isNaN(value)) {
    emit('update:modelValue', value)
    event.target.value = value
  } else {
    emit('update:modelValue', 0)
    event.target.value = 0
  }
}

// input change事件
const handleChange = (event) => {
  const value = parseInt(event.target.value, 10)
  if (!Number.isNaN(value)) {
    emit('change', value)
  }
}
// input blur事件
const handleBlur = () => {
  emit('on-blur')
}

watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal < props.min) {
      emit('update:modelValue', props.min)
    } else if (newVal > props.max) {
      emit('update:modelValue', props.max)
    }
  },
)
</script>
