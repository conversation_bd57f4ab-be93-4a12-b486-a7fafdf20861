<template>
  <span v-if="showType === 'labelSymbol'" class="skip-translate">{{ currentLabelSymbol }}</span>
  <span v-else-if="showCnAndEn" class="skip-translate">{{ currencyEn }}（{{ currency }}）</span>
  <span v-else-if="showType === 'enCurrency'" class="skip-translate">{{ enCurrency }}</span>
  <span v-else-if="showType === 'symbol'" class="skip-translate">{{ showCurrency ? currency : symbol }}</span>
  <span v-else class="skip-translate">{{ showCurrency ? currency : symbol }}</span>
</template>

<script setup>
import { useDictStore } from '@/pc/stores'
import { useNation } from '@/hooks/useNation'
import user from '@/pc/utils/user'

const props = defineProps({
  priceType: {
    type: Number,
    default: null,
  },
  showCurrency: {
    type: Boolean,
    default: false,
  },
  // 显示类型 默认显示符号($) 可选值 labelSymbol 显示符号和币种(USD$)
  showType: {
    type: String,
    default: 'symbol',
  },
  // 展示简称和中文 CNY（人民币）
  showCnAndEn: {
    type: Boolean,
    default: false,
  },
})

const dictStore = useDictStore()
const userPriceType = user.getPayPrice() // 用户设置币种
const { isNation } = useNation()

const priceTypeMap = computed(() => dictStore.priceTypeMap)
// 站点默认币种和默认符号
let siteDefaultPriceTypeSymbol = '¥'
let defaultCurrency = 'RMB'
if (isNation('uae')) {
  siteDefaultPriceTypeSymbol = 'AED'
  defaultCurrency = 'AED'
}
if (isNation('idn')) {
  siteDefaultPriceTypeSymbol = 'IDR'
  defaultCurrency = 'IDR'
}
const symbol = computed(() => {
  const item = priceTypeMap?.value && priceTypeMap?.value[props?.priceType || userPriceType]
  return item?.symbol || siteDefaultPriceTypeSymbol
})
const shortSymbol = computed(() => {
  const item = priceTypeMap?.value && priceTypeMap?.value[props?.priceType || userPriceType]
  return item?.currency || defaultCurrency
})
// 显示币种的英文简称+币种符号，如 CNY￥、USD$
const currentLabelSymbol = computed(() => {
  if (shortSymbol.value === symbol.value) {
    return shortSymbol.value
  }
  return `${shortSymbol.value}${symbol.value}`
})
// 显示币种的英文简称，如 CNY、USD。AED、IDR
const enCurrency = computed(() => {
  return `${shortSymbol.value}`
})
const currency = computed(() => {
  const item = priceTypeMap?.value && priceTypeMap?.value[props?.priceType || userPriceType]
  return item?.value || '人民币'
})
const currencyEn = computed(() => {
  const item = priceTypeMap?.value && priceTypeMap?.value[props.priceType]
  return item?.currency || 'CNY'
})
</script>

<style scoped lang="scss"></style>
