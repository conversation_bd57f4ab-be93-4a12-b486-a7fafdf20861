<!-- 气泡卡片 -->
<style lang="scss" scoped>
.popover-wrapper {
  position: relative;
}

.popover-content-wrapper {
  position: absolute;
  padding: 4px;
  z-index: 1000;
}

.slide-enter-active,
.slide-leave-active {
  transition:
    opacity 0.3s,
    transform 0.3s;
}
.slide-enter-from,
.slide-leave-to {
  opacity: 0;
  transform: translateY(20px);
}
</style>

<template>
  <div class="popover-wrapper" @click="onOpenPopover" @mouseenter="onMouseenter" @mouseleave="onMouseleave">
    <div ref="referenceRef">
      <!-- 触发弹窗的 dom -->
      <slot name="reference" />
    </div>
    <!-- 气泡展示 -->
    <transition name="slide">
      <div ref="contentRef" v-show="isViable" class="popover-content-wrapper" :style="contentStyle">
        <!-- 弹窗中的内容 -->
        <slot />
      </div>
    </transition>
  </div>
</template>

<script setup>
import { nextTick, ref, watch } from 'vue'
import { DELAY_TIME, POSITION_ENUM } from './options'

const props = defineProps({
  // 出发弹窗的方式
  trigger: {
    type: String,
    default: 'hover',
  },
  // 弹窗出现的位置
  placement: {
    type: String,
    default: POSITION_ENUM.TOP_START,
    validator(val) {
      const result = Object.values(POSITION_ENUM).includes(val)
      if (!result) {
        throw new Error(`placement 属性值必须是 ${Object.values(POSITION_ENUM).join('、')} 中的一个`)
      }
      return result
    },
  },
  leftDistance: {
    type: Number,
    default: 0,
  },
  // 自定义的 left 值
  customLeft: {
    type: Number,
    default: 0,
  },
})

// 是否展示弹窗
const isViable = ref(false)

// 点击打开弹窗
const onOpenPopover = () => {
  if (props.trigger !== 'click') return
  isViable.value = true
}

// 鼠标移入
let timeout = null
const onMouseenter = () => {
  if (props.trigger !== 'hover') return
  console.log('mouseenter')
  isViable.value = true
  if (timeout) clearTimeout(timeout)
}
// 鼠标移出
const onMouseleave = () => {
  if (props.trigger !== 'hover') return
  timeout = setTimeout(() => {
    isViable.value = false
    timeout = null
  }, DELAY_TIME)
}

// 手动关闭弹窗
const onClose = () => {
  isViable.value = false
}

// 计算元素的尺寸
const referenceRef = ref(null)
const contentRef = ref(null)
const getElementSize = (target) => {
  if (!target) return {}
  return {
    width: target.offsetWidth,
    height: target.offsetHeight,
  }
}

// 弹窗的位置
const contentStyle = ref({
  top: 0,
  left: 0,
})

// 计算位置
watch(isViable, (val) => {
  if (!val) return
  nextTick(() => {
    switch (props.placement) {
      // 正上
      case POSITION_ENUM.TOP:
        contentStyle.value.top = -getElementSize(contentRef.value).height + 'px'
        // @ts-ignore
        contentStyle.value.left = 0
        break
      // 左上
      case POSITION_ENUM.TOP_START:
        contentStyle.value.top = -getElementSize(contentRef.value).height + 'px'
        // @ts-ignore
        contentStyle.value.left = -getElementSize(contentRef.value).width + 'px'
        break
      // 右上
      case POSITION_ENUM.TOP_END:
        // @ts-ignore
        contentStyle.value.top = 0
        // @ts-ignore
        contentStyle.value.left = getElementSize(referenceRef.value).width + 'px'
        break
      // 左下
      case POSITION_ENUM.BOTTOM_START:
        contentStyle.value.top = 0
        // @ts-ignore
        contentStyle.value.top = getElementSize(referenceRef.value).height + 'px'
        // @ts-ignore
        contentStyle.value.left = -getElementSize(contentRef.value).width + 'px'
        break
      // 右下
      case POSITION_ENUM.BOTTOM_END:
        // @ts-ignore
        contentStyle.value.top = getElementSize(referenceRef.value).height + 'px'
        // @ts-ignore
        contentStyle.value.left = getElementSize(referenceRef.value).width + 'px'
        break
      // 正下方
      case POSITION_ENUM.BOTTOM:
        // @ts-ignore
        contentStyle.value.top = getElementSize(referenceRef.value).height + 8 + 'px'
        // @ts-ignore
        contentStyle.value.left = props.customLeft ? props.customLeft + 'px' : 0
        break
    }
  })
})

defineExpose({
  onClose,
})
</script>
