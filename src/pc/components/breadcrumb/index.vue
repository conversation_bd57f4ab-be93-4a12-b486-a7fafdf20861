<template>
  <div class="custom-breadcrumb" v-if="breadcrumbList.length">
    <el-breadcrumb separator-class="el-icon-arrow-right" :separator-icon="ArrowRight">
      <el-breadcrumb-item v-for="(item, index) in breadcrumbList" :key="index" :to="item.path">{{ item.name[$i18n.locale] }}</el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<script setup>
import { ArrowRight } from '@element-plus/icons-vue'

defineProps({
  breadcrumbList: {
    type: Array,
    default: () => [],
  },
})
</script>

<style scoped lang="scss">
.custom-breadcrumb {
  height: 62px;

  @include flex-sbc;

  .back {
    width: 100%;
    font-size: 20px;
    font-weight: 500;
    color: #666666;
    line-height: 28px;
    cursor: pointer;

    .icon {
      margin-right: 16px;
      color: #d9d9d9;
    }
  }
}
</style>
<style scoped lang="scss">
:deep() {
  .el-breadcrumb__inner.is-link {
    font-size: 16px;
    color: $regular-text;
    font-weight: normal;
  }
  .el-breadcrumb__item:last-child .el-breadcrumb__inner {
    font-size: 16px;
    color: $primary-color;
  }
}

[dir='rtl'] .custom-breadcrumb {
  .el-breadcrumb {
    display: flex;
  }

  :deep(.el-breadcrumb__separator) {
    transform: rotateZ(-180deg);
  }
}
</style>
