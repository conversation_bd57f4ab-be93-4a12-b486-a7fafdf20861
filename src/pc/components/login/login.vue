<!--
 * @Author: 王俊杰
 * @Date: 2024-08-29 17:21:25
 * @LastEditors: your name
 * @LastEditTime: 2024-09-09 14:03:18
 * @Description: 登录注册
 * @FilePath: /trade-exhibition/src/pc/components/login/login.vue
-->
<style lang="scss" scoped>
.decoration-wrapper {
  width: 100%;
  height: 8px;
  border-radius: 8px 8px 0 0;
  background: $primary-color;
  margin-bottom: 40px;
}
.login-content-wrapper {
  padding: 0 40px 40px;

  .logo-wrapper {
    display: flex;
    justify-content: center;
    margin-bottom: 16px;
    .logo {
      height: 60px;
      margin-right: 8px;
    }
  }
  .title {
    text-align: center;
    font-size: 24px;
    font-weight: 600;
    color: $color-333333;
    margin-bottom: 40px;
  }
}
.login-wrapper {
  position: relative;
  border-radius: 8px;
  background-color: #fff;

  .icon-guanbi {
    position: absolute;
    top: 36px;
    right: 44px;
    cursor: pointer;
  }
}

.login-btn {
  width: 100%;
  height: 48px;
  background: $primary-color;
  text-align: center;
  line-height: 48px;
  font-size: 16px;
  font-weight: 600;
  color: $basic-white;
  margin-bottom: 16px;
  cursor: pointer;
}

.tips-text {
  font-size: 16px;
  color: $color-999999;
  text-align: center;
}

:deep() {
  .el-form-item__label {
    line-height: 20px;
    margin-bottom: 4px;
    color: $color-999999;
  }
  .el-form-item {
    margin-bottom: 16px;
  }

  .el-checkbox {
    line-height: 20px;
    height: 20px;

    &.is-checked {
      .el-checkbox__inner {
        background: $primary-color;
        border-color: $primary-color;
      }
      .el-checkbox__label {
        color: $primary-color;
      }
    }
  }
}

.get-code-wrapper {
  :deep(.el-form-item__content) {
    justify-content: space-between;

    &::after {
      display: none;
    }

    &::before {
      display: none;
    }
  }

  .code-input {
    width: 290px;
  }

  .get-code-btn {
    float: right;
    padding-right: 0;
    padding-left: 0;
    width: 120px;
    height: 40px;
    font-size: 14px;
    font-weight: 600;
    text-align: center;
    line-height: 0;
    color: $basic-white;
    background: $primary-color;
    border: none;

    &.is-disabled {
      background: $color-C4C4C4 !important;
    }
  }
}
.agreement-box {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 40px;
  line-height: 20px;
  color: #333;
}
.green-link-text {
  font-size: 14px;
  color: $primary-color;
}
</style>

<template>
  <Dialog v-model="isShowLogin" width="500">
    <template #default>
      <div class="login-wrapper">
        <icon type="icon-guanbi" :size="16" @click="isShowLogin = false" />
        <div class="decoration-wrapper"></div>
        <div class="login-content-wrapper">
          <div class="logo-wrapper">
            <img class="logo" src="https://static.chinamarket.cn/static/trade-exhibition/logo/logo-login.png" draggable="false" />
          </div>
          <div class="title">{{ t('title') }}</div>
          <div class="form-wrapper">
            <el-form ref="loginFormRef" :model="formData" :rules="rules" label-position="top" size="large">
              <el-form-item :label="t('field1')" prop="mobile">
                <el-input v-model="formData.mobile" :placeholder="t('filed1Placeholder')" maxlength="11" @input="(e) => handleInput(e, 'mobile')" />
              </el-form-item>
              <!-- 滑块验证 -->
              <el-form-item>
                <WangyiVerify ref="wangyiVerify" @success="verifySuccess" @error="verifyFail" />
              </el-form-item>
              <el-form-item class="get-code-wrapper" :label="t('field2')" prop="smsCode">
                <el-input
                  v-model="formData.smsCode"
                  name="sd-code"
                  class="code-input"
                  :placeholder="t('filed2Placeholder')"
                  maxlength="6"
                  @input="(e) => handleInput(e, 'smsCode')"
                />
                <el-button element-loading-cover class="get-code-btn secondary-button" :disabled="leftSeconds > 0" @click="getCode">
                  {{ leftSeconds > 0 ? t('countdown', { countdown: leftSeconds }) : t('sendCaptcha') }}
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          <div class="agreement-box">
            <el-checkbox v-model="isAgree" />
            <span class="ml-1">{{ t('agree') }}</span>
            <a :href="t('servicesLink')" target="_blank" class="green-link-text">{{ t('servicAgree') }}</a>
            <span>{{ t('and') }}</span>
            <a :href="t('privacyLink')" target="_blank" class="green-link-text">{{ t('secretAgree') }}</a>
          </div>
          <div class="login-btn" @click="onLogin">{{ t('submit') }}</div>
          <div class="tips-text">{{ t('explain') }}</div>
        </div>
      </div>
    </template>
  </Dialog>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import Dialog from '@/pc/components/dialog/dialog.vue'
import WangyiVerify from '@/pc/components/verify/wangyi-verify.vue'
import { useUserStore } from '@/pc/stores'
import { getUserInfo } from '@/pc/hooks/user.js'
import { getSmsCode } from '@/apis/common.js'
import { useEvent } from '@/event'
import { LOGIN_SUCCESS, OPEN_LOGIN } from '@/event/modules/site'
import user from '@/pc/utils/user.js'

const { t } = useI18n({
  messages: {
    zh: {
      title: '登录注册',
      field1: '手机号',
      filed1Placeholder: '请输入手机号码',
      filed1Validate1: '请输入正确的手机号',
      field2: '验证码',
      filed2Placeholder: '请输入验证码',
      sendCaptcha: '获取验证码',
      sendCaptchaSuccess: '验证码发送成功',
      countdown: '还剩{countdown}秒',
      submit: '立即登录',
      verify: '请滑动验证',
      explain: '未注册的手机号码验证后将自动创建新账号',
      agree: '我已阅读并同意',
      servicAgree: '《注册服务协议》',
      and: '和',
      secretAgree: '《隐私协议》',
      readAgree: '请阅读并同意服务协议',
      servicesLink: 'https://static.chinamarket.cn/static/trade-exhibition/file/注册服务协议-中国大集.pdf',
      privacyLink: 'https://static.chinamarket.cn/static/trade-exhibition/file/隐私政策-中国大集.pdf',
      loginSuccess: '登录成功',
    },
    en: {
      title: 'Sign in',
      field1: 'Phone',
      filed1Placeholder: 'Please input your phone number',
      filed1Validate1: 'Please enter a valid phone number',
      field2: 'Captcha',
      filed2Placeholder: 'please input the captcha',
      sendCaptcha: 'Send captcha',
      sendCaptchaSuccess: 'Captcha sent successfully',
      countdown: '{countdown} second left',
      submit: 'Sing in',
      verify: 'Please slide to verify',
      explain: 'Unregistered phone numbers will automatically create a new account after verification.',
      agree: 'I have read and agree',
      servicAgree: '《Services Statement》',
      and: 'and',
      secretAgree: '《Privacy Statement》',
      readAgree: 'Please read and agree to the service agreement',
      servicesLink:
        'https://static.chinamarket.cn/static/trade-exhibition/file/%E3%80%90%E8%8B%B1%E6%96%87%E3%80%91%E6%B3%A8%E5%86%8C%E6%9C%8D%E5%8A%A1%E5%8D%8F%E8%AE%AE.pdf',
      privacyLink: 'https://static.chinamarket.cn/static/trade-exhibition/file/%E3%80%90%E8%8B%B1%E6%96%87%E3%80%91%E9%9A%90%E7%A7%81%E6%94%BF%E7%AD%96.pdf',
      loginSuccess: 'Login successfully',
    },
  },
})
let userStore = useUserStore() // pinia

const event = useEvent()
const isShowLogin = ref(false)
const loginFormRef = ref(null)
const isAgree = ref(false)

const rules = {
  mobile: [
    {
      required: true,
      message: computed(() => t('filed1Placeholder')),
      trigger: ['change', 'blur'],
    },
    {
      required: true,
      pattern: /^1[3456789]\d{9}$/,
      message: computed(() => t('filed1Validate1')),
      trigger: 'blur',
    },
  ],
  smsCode: [
    {
      required: true,
      pattern: /^[0-9]{6}$/,
      message: computed(() => t('filed2Placeholder')),
      trigger: ['blur'],
    },
  ],
}

// 滑动验证信息
const verifyInfo = reactive({
  verifyData: null,
  verifyStatus: null,
})

// 滑块验证成功后触发
function verifySuccess(data) {
  verifyInfo.verifyData = data
  verifyInfo.verifyStatus = true
}

// 滑块组件 ref
const wangyiVerify = ref()
// 重置
const verifyReset = () => wangyiVerify.value && wangyiVerify.value.ncRefresh()
// 滑块验证失败后触发
function verifyFail() {
  verifyInfo.verifyStatus = false
}

// 登录表单
const formData = reactive({
  mobile: '',
  smsCode: '',
})

// 限制只能输入数字
const handleInput = (val, key) => {
  formData[key] = val.replace(/\D/g, '')
}

// 倒计时
const leftSeconds = ref(0)
const timer = ref(null)
const startCountDown = () => {
  leftSeconds.value = 60
  const countDown = () => {
    timer.value = setTimeout(() => {
      leftSeconds.value -= 1
      leftSeconds.value > 0 && countDown()
    }, 1000)
  }
  countDown()
}

// 点击获取验证码
const sendCode = async () => {
  const body = {
    mobile: formData.mobile,
    validate: verifyInfo.verifyData.validate,
  }
  try {
    await getSmsCode(body)
    ElMessage({
      message: t('sendCaptchaSuccess'),
      type: 'success',
    })
    startCountDown()
  } catch (error) {
    verifyReset() // 将滑动验证重置为初始状态
    verifyInfo.verifyStatus = false
    formData.smsCode = ''
  }
}

// 获取验证码
const getCode = () => {
  loginFormRef.value.validateField('mobile', (isValid) => {
    if (isValid) {
      if (verifyInfo.verifyStatus) {
        // 校验否滑动成功滑块
        sendCode()
      } else {
        ElMessage({
          message: t('verify'),
          type: 'warning',
        })
      }
    }
  })
}

// 点击登录
const onLogin = async () => {
  if (!verifyInfo.verifyStatus) {
    ElMessage({
      message: t('verify'),
      type: 'warning',
    })
    return
  }
  if (!isAgree.value) {
    ElMessage({
      message: t('readAgree'),
      type: 'warning',
    })
    return
  }
  await loginFormRef.value.validate()
  const body = {
    mobile: formData.mobile,
    smsCode: formData.smsCode,
  }
  try {
    await user.postLogin(body, 'AI')
    userStore.setIsLogined(true)
    // userStore.setUserInfo(res.userInfo || {})
    getUserInfo()
    ElMessage({
      message: t('loginSuccess'),
      type: 'success',
    })
    event.emit(LOGIN_SUCCESS, {})
    isShowLogin.value = false
  } catch (error) {
    verifyReset() // 重置滑动验证
    verifyInfo.verifyStatus = false
  }
}

const init = () => {
  isShowLogin.value = true
}
event.on(OPEN_LOGIN, () => {
  // 用户store数据
  userStore.logout()
  init()
})

defineExpose({ init })
</script>
