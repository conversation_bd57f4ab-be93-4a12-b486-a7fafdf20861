<style lang="scss" scoped>
.decoration-wrapper {
  width: 100%;
  height: 8px;
  border-radius: 8px 8px 0 0;
  background: $primary-color;
  margin-bottom: 24px;
}
.logo-wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
  .logo {
    height: 60px;
    margin-right: 8px;
  }
}
</style>

<template>
  <Dialog v-model="isShowLogin" :width="512" @closed="closed">
    <template #default>
      <LoginForm
        ref="loginFormRef"
        :loginSuccessRedirectPath="loginSuccessRedirectPath"
        :openLoginParams="openLoginParams"
        @hideLogin="hideModal"
        @loginSuccessCallback="loginSuccessCallback"
      >
        <template #headerRight>
          <Icon type="icon-guanbi" class="cursor-pointer" :size="24" @click="hideModal" />
        </template>
        <template #headerLogo="{ isRegister }">
          <div class="mb-4 text-#333333">
            <div class="flex items-center justify-center px-2">
              <div class="text-[32px] font-bold mr-2 text-ellipsis overflow-hidden whitespace-nowrap">{{ title }}{{ isRegister ? '注册' : '' }}</div>
              <template v-if="$storageLocale !== 'ru'">
                <div
                  v-if="!isRegister"
                  class="text-[20px] font-bold min-w-[120px] h-[32px] bg-cover color-[#fff] flex items-center justify-center text-ellipsis overflow-hidden whitespace-nowrap px-[12px] shrink-0"
                  :style="`background-image: url('${ossUrl}/merchants/login-title-bg.png')`"
                >
                  欢迎登录
                </div>
              </template>
            </div>
          </div>
        </template>
      </LoginForm>
    </template>
  </Dialog>
</template>

<script setup>
import Dialog from '@/pc/components/dialog/dialog.vue'
import LoginForm from '@/pc/components/login-form/login-form.vue'
import { ossUrl } from '@/constants/common'
import { useEvent } from '@/event'
import { OPEN_NEW_LOGIN } from '@/event/modules/site'

const title = ref('采购商')
const isShowLogin = ref(false)
const loginFormRef = ref(null)
const openLoginParams = ref(null)

const hideModal = () => {
  isShowLogin.value = false
}

const closed = () => {
  loginFormRef.value.goBack()
}

const event = useEvent()
// 登录成功回调，仅执行一次
let loginSuccessCallback = () => {
  if (callback && typeof callback === 'function') {
    callback()
    callback = null
  }
}
let callback = null
const loginSuccessRedirectPath = ref('')
event.on(OPEN_NEW_LOGIN, async (params) => {
  callback = null
  if (params?.callback && typeof params.callback === 'function') {
    callback = params.callback
  }
  if (params?.path) {
    loginSuccessRedirectPath.value = params.path
  }
  isShowLogin.value = true
  openLoginParams.value = params || {}
})
</script>
