/**
 * 下载视频
 */
export function downloadVideo(url, fileName = '') {
  const xhr = new XMLHttpRequest()
  xhr.open('GET', url, true)
  xhr.responseType = 'blob'
  // 监听请求完成事件
  xhr.onload = () => {
    if (xhr.readyState === 4 && xhr.status === 200) {
      let blob = xhr.response
      // 转换一个 blob 连接
      let u = window.URL.createObjectURL(new Blob([blob], { type: 'video/mp4' }))
      let a = document.createElement('a')
      a.download = fileName
      a.href = u
      a.style.display = 'none'
      document.body.appendChild(a)
      a.click()
      a.remove()
    }
  }
  xhr.send()
}
