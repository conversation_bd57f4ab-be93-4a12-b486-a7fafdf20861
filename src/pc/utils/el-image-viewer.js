// 修改图片预览组件关闭按钮的样式
import { findParent } from '@/common/js/dom'

// 关闭按钮的大小
const CLOSE_ICON_SIZE = 44
// 允许距离视口边框的最近距离
const CORNER_DISTANCE = 40

// 图片移动后跟着调整关闭按钮的位置
const handleImageMove = (img) => {
  // console.log('move', img)
  if (!img || img.style.display === 'none') {
    return
  }
  const imageViewerWrapper = findParent(img, '.el-image-viewer__wrapper')
  if (!imageViewerWrapper) {
    return
  }
  const imgRect = img.getBoundingClientRect()
  const { top, right } = imgRect
  if (!imgRect.width || !imgRect.height) {
    return
  }
  let windowWidth = window.innerWidth
  const { zoom } = document.documentElement.style
  if (zoom) {
    windowWidth = windowWidth / parseFloat(zoom)
  }
  const closeIcon = imageViewerWrapper.querySelector('.el-image-viewer__close')
  const iconRect = closeIcon && closeIcon.getBoundingClientRect()
  const { width, height } = iconRect || { width: CLOSE_ICON_SIZE, height: CLOSE_ICON_SIZE }
  const iconLeft = Math.min(windowWidth - width - CORNER_DISTANCE, right + 24)
  const iconTop = Math.max(CORNER_DISTANCE, top - height)
  const closeIconStyle = {
    left: `${iconLeft}px`,
    right: 'auto',
    top: `${iconTop}px`,
  }
  Object.assign(closeIcon.style, closeIconStyle)
  if (img.style.transition && !img._hasTransitionEndListener) {
    // 当前有动画时，需等动画结束后再获取位置
    img.addEventListener('transitionend', () => {
      handleImageMove(img)
    })
    img._hasTransitionEndListener = true
  }
}

// 图片预览弹窗出现后的 MutationObserver 回调
const imageViewerCallback = (mutationList) => {
  for (const mutation of mutationList) {
    if (mutation.target.tagName === 'IMG') {
      const img = mutation.target
      handleImageMove(img)
    }
  }
}

// body 下监听到 dom 创建或销毁的回调
const bodyCallback = (mutationList) => {
  for (const mutation of mutationList) {
    for (let i = 0; i < mutation.addedNodes.length; i++) {
      const node = mutation.addedNodes[i]
      // 判断图片预览组件是否是在 src\components\img-viewer\img-viewer.vue 组件中，是则固定关闭按钮位置，不需要跟随
      const isInImageViewerContainer = node.parentNode && node.parentNode.classList.contains('image-viewer-container')
      if (node.classList && node.classList.contains('el-image-viewer__wrapper') && !isInImageViewerContainer) {
        const imageViewerObserver = new MutationObserver(imageViewerCallback)
        imageViewerObserver.observe(node, {
          subtree: true,
          childList: true,
          attributes: true,
          attributeFilter: ['style'],
        })
        ;[...node.querySelectorAll('img')].forEach((img) => {
          img.addEventListener('load', () => {
            handleImageMove(img)
          })
        })
      }
    }
  }
}

if (window.MutationObserver) {
  // 监听 body 下所有 dom 的插入和删除事件，当有图片预览弹窗出现时，修改关闭按钮的位置
  const bodyObserver = new MutationObserver(bodyCallback)
  bodyObserver.observe(document.body, {
    subtree: true,
    childList: true,
  })
}
