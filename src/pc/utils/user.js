import { useNation } from '@/hooks/useNation'
import { postLoginApi } from '@/apis/common.js'
import { scyxCodeLogin, scyxPwdLogin, scyxResetPwdLogin } from '@/apis/common.js'
import Storage from '@/common/js/storage'

const { isNation } = useNation()
// 站点默认币种
let defaultPayPriceType = 1
if (isNation('uae')) {
  defaultPayPriceType = 3
}
if (isNation('idn')) {
  defaultPayPriceType = 4
}
// 本地缓存
const TOKEN = 'token'
const USER_INFO = 'userInfo'
const PAY_PRICE_TYPE = 'payPriceType'
const LOGIN_METHOD = () => ({
  0: scyxPwdLogin,
  1: scyxCodeLogin,
  2: scyxResetPwdLogin,
})
const user = {
  // 登录 ⚠️ 新的登录不再使用该方法，留在看AI是否使用
  async postLogin(data, type) {
    let func = null
    let res = null
    if (type === 'AI') {
      res = await postLoginApi(data) // 数字人模块登录
      this.setToken(res.token)
      this.setUserInfo(res.userInfo)
    } else {
      func = LOGIN_METHOD()[type] // type: 0 密码登录, 1 验证码登录，2: 重置密码登录
      res = await func(data)
      this.setToken(res.accessToken)
      this.setUserInfo(res)
    }
    return res
  },

  // 设置用户信息
  setUserInfo(data) {
    Storage.set(USER_INFO, data)
  },

  // 清除 userInfo
  clearUserInfo() {
    Storage.remove(USER_INFO)
  },

  // 获取 userInfo
  getUserInfo() {
    return Storage.get(USER_INFO)
  },

  // 清楚 token
  clearToken() {
    Storage.remove(TOKEN)
  },

  // 设置 token
  setToken(token) {
    Storage.set(TOKEN, token)
  },

  // 获取 token
  getToken() {
    return Storage.get(TOKEN)
  },

  // 设置 payPrice
  setPayPrice(data) {
    Storage.set(PAY_PRICE_TYPE, data)
  },

  // 获取 payPrice
  getPayPrice() {
    return Storage.get(PAY_PRICE_TYPE) || defaultPayPriceType
  },
}

export default user
