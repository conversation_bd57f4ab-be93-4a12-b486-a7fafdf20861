import user from '@/pc/utils/user'

// 环境枚举
export const envEnum = {
  development: 'local',
  test: 'daily',
  staging: 'pre',
  production: 'prod',
}

export default {
  init() {
    const userInfo = user.getUserInfo()
    if (userInfo && userInfo.id) {
      const { userName, id, userType } = userInfo
      this.setUserConfig({
        name: userName,
        tags: id,
        userType,
      })
    }
  },

  setConfig(customConfig = {}) {
    const ArmsRum = window?.RumSDK?.default
    const currentConfig = ArmsRum?.getConfig()
    ArmsRum?.setConfig({
      ...currentConfig,
      ...customConfig,
    })
  },

  setUserConfig(nameOrConfig = null, tags = null, additionalConfig = {}) {
    let userConfig = {}

    if (typeof nameOrConfig === 'object' && nameOrConfig !== null) {
      userConfig = {
        name: nameOrConfig.name || null,
        tags: nameOrConfig.tags || null,
        ...nameOrConfig,
      }
    } else {
      userConfig = {
        name: nameOrConfig,
        tags,
        ...additionalConfig,
      }
    }

    const ArmsRum = window?.RumSDK?.default
    const currentConfig = ArmsRum?.getConfig()
    ArmsRum?.setConfig({
      ...currentConfig,
      user: {
        ...currentConfig.user,
        ...userConfig,
      },
    })
  },

  // 异常上报
  sendException(name = '', message = '', properties = {}, options = {}) {
    if (!name || !message) {
      console.warn('sendException 参数 name/message必填')
      return
    }
    const ArmsRum = window?.RumSDK?.default
    ArmsRum?.sendException({
      // 必选
      name,
      message,
      // 可选
      ...options,
      properties,
    })
  },

  // 自定义上报
  sendCustom(type, name, properties = {}, options = {}) {
    if (!name || !type) {
      console.warn('sendCustom 参数 name/type必填')
      return
    }
    const ArmsRum = window?.RumSDK?.default
    ArmsRum?.sendCustom({
      // 必选
      type,
      name,
      // 可选
      ...options,
      properties,
    })
  },

  // 上报自定义资源
  sendResource(name, type, duration, properties = {}, options = {}) {
    if (!name || !type || !duration) {
      console.warn('sendCustom 参数 name/type/duration必填')
      return
    }
    const ArmsRum = window?.RumSDK?.default
    ArmsRum?.sendResource({
      // 以下必选
      name, // 资源名
      type, // 资源类型，例如：css、javascript、xmlhttprequest、fetch、api、image、font、other
      duration, // 请求耗时
      // 以下可选
      ...options,
      properties,
    })
  },
}
