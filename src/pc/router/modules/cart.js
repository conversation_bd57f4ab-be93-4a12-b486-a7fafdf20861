import { setCommonMeta } from '../index'

// 添加全局配置信息
const commonMeta = { requiredLogin: true }
const ignore = [] // 无需登录即可访问的页面名称

const cartConfigs = [
  {
    path: '/cart/list',
    component: () => import('@/pc/views/pages/cart/cart.vue').catch(() => location.reload()),
    meta: {
      title: '中国大集-临沂商城一站式全域B2B数字商贸综合服务平台',
    },
  },
  {
    path: '/cart/confirm',
    component: () => import('@/pc/views/pages/cart/cart-confirm.vue').catch(() => location.reload()),
    meta: {
      title: '中国大集-临沂商城一站式全域B2B数字商贸综合服务平台',
    },
  },
  {
    path: '/cart/success',
    component: () => import('@/pc/views/pages/cart/cart-confirm-success.vue').catch(() => location.reload()),
    meta: {
      title: '中国大集-临沂商城一站式全域B2B数字商贸综合服务平台',
    },
  },
]

setCommonMeta(cartConfigs, commonMeta, ignore)

export default cartConfigs
