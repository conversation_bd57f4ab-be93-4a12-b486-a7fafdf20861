import MerchantsBuyerCenter from '@/pc/views/pages/merchants/buyer-center/buyer-center.vue'
import MerchantsBuyerServiceCenter from '@/pc/views/pages/merchants/buyer-service-center/buyer-service-center.vue'
import MerchantServicesCenter from '@/pc/views/pages/merchants/merchant-services-center/index.vue'
import MerchantsSellerCenter from '@/pc/views/pages/merchants/seller-center/seller-center.vue'
import ForeignTradeServiceCenter from '@/pc/views/pages/merchants/service-center/service-center.vue'
import { IS_MERCHANTS_BACKEND, MERCHANTS_TYPE } from '@/constants/merchants'
import { setCommonMeta } from '@/pc/router'

const merchantsConfigs = [
  {
    path: '/buyer-center',
    component: MerchantsBuyerCenter,
    name: 'buyerCenter',
    redirect: '/buyer-center/welcome',
    meta: {
      title: { zh: '采购商中心', en: 'Buyer center' },
      parentRouter: true,
    },
    children: [
      {
        path: 'welcome',
        component: () => import('@/pc/views/pages/merchants/buyer-center/welcome/welcome.vue').catch(() => location.reload()),
        name: 'buyerWelcome',
        meta: {
          title: { zh: '欢迎中心', en: 'Welcome' },
        },
      },
      {
        path: 'my-info',
        component: () => import('@/pc/views/pages/merchants/buyer-center/my-info/my-info.vue').catch(() => location.reload()),
        name: 'myInfo',
        redirect: '/buyer-center/my-info/base-info',
        meta: {
          title: { zh: '我的资料', en: 'My Profile' },
        },
        parentRouter: true,
        children: [
          {
            path: 'base-info',
            component: () => import('@/pc/views/pages/merchants/buyer-center/my-info/base-info/base-info.vue').catch(() => location.reload()),
            name: 'baseInfo',
            meta: {
              title: { zh: '基础资料', en: '' },
            },
          },
          {
            path: 'address-manage',
            component: () => import('@/pc/views/pages/merchants/buyer-center/my-info/address-manage/address-manage.vue').catch(() => location.reload()),
            name: 'addressManage',
            meta: {
              title: { zh: '收货地址管理', en: '' },
            },
          },
        ],
      },
      {
        path: 'my-collection',
        component: () => import('@/pc/views/pages/merchants/buyer-center/my-collection/my-collection.vue').catch(() => location.reload()),
        name: 'myCollection',
        meta: {
          title: { zh: '我的商品收藏', en: 'My favorites' },
        },
      },
      {
        path: 'order-manage',
        component: () => import('@/pc/views/pages/merchants/buyer-center/order-manage/order-manage.vue').catch(() => location.reload()),
        name: 'buyOrderManage',
        meta: {
          title: { zh: '订单管理', en: 'Order Manage' },
        },
      },
      {
        path: 'order-detail',
        component: () => import('@/pc/views/pages/merchants/buyer-center/order-detail/order-detail.vue').catch(() => location.reload()),
        name: 'buyOrderDetail',
        meta: {
          title: { zh: '订单详情', en: 'Order Detail' },
        },
      },
      {
        path: 'my-opportunity',
        component: () => import('@/pc/views/pages/merchants/buyer-center/my-opportunity/my-opportunity.vue').catch(() => location.reload()),
        name: 'myOpportunity',
        meta: {
          title: { zh: '商机中心', en: 'Opportunity Hub' },
        },
      },
      {
        path: 'my-opportunity-detail',
        component: () => import('@/pc/views/pages/merchants/buyer-center/my-opportunity-detail/my-opportunity-detail.vue').catch(() => location.reload()),
        name: 'myOpportunityDetail',
        meta: {
          title: { zh: '商机中心详情', en: 'Opportunity Hub Details' },
        },
      },
      {
        path: 'my-opportunity-add',
        component: () => import('@/pc/views/pages/merchants/buyer-center/my-opportunity-add/my-opportunity-add.vue').catch(() => location.reload()),
        name: 'myOpportunityAdd',
        meta: {
          title: { zh: '发布商机', en: 'Publish Opportunity' },
        },
      },
    ],
  },
  {
    path: '/buyer-service-center',
    component: MerchantsBuyerServiceCenter,
    name: 'MerchantsBuyerServiceCenter',
    redirect: '/buyer-service-center/welcome',
    meta: {
      title: { zh: '采购商服务商', en: 'Buyer Service center' },
      parentRouter: true,
    },
    children: [
      {
        path: 'welcome',
        component: () => import('@/pc/views/pages/merchants/buyer-service-center/welcome/welcome.vue').catch(() => location.reload()),
        name: 'buyerServiceCenter',
        meta: {
          title: { zh: '欢迎中心', en: 'Welcome' },
        },
      },
      {
        path: 'buyer-manage',
        component: () => import('@/pc/views/pages/merchants/buyer-service-center/buyer-manage/buyer-manage.vue').catch(() => location.reload()),
        name: 'buyerServiceManage',
        meta: {
          title: { zh: '采购商管理', en: 'Buyer Manage' },
        },
      },
      {
        path: 'my-opportunity',
        component: () => import('@/pc/views/pages/merchants/buyer-center/my-opportunity/my-opportunity.vue').catch(() => location.reload()),
        name: 'buyerServiceOpportunity',
        meta: {
          title: { zh: '商机中心', en: 'Opportunity Hub' },
        },
      },
      {
        path: 'my-opportunity-detail',
        component: () => import('@/pc/views/pages/merchants/buyer-center/my-opportunity-detail/my-opportunity-detail.vue').catch(() => location.reload()),
        name: 'buyerServiceOpportunityDetail',
        meta: {
          title: { zh: '商机中心详情', en: 'Opportunity Hub Details' },
        },
      },
      {
        path: 'my-opportunity-add',
        component: () => import('@/pc/views/pages/merchants/buyer-center/my-opportunity-add/my-opportunity-add.vue').catch(() => location.reload()),
        name: 'buyerServiceOpportunityAdd',
        meta: {
          title: { zh: '发布商机', en: 'Publish Opportunity' },
        },
      },
    ],
  },
  {
    path: '/seller-center',
    component: MerchantsSellerCenter,
    name: 'MerchantsSellerCenter',
    redirect: '/seller-center/welcome',
    meta: {
      title: { zh: '供应商中心', en: 'Seller center' },
      parentRouter: true,
    },
    children: [
      {
        path: 'welcome',
        component: () => import('@/pc/views/pages/merchants/seller-center/welcome/welcome.vue').catch(() => location.reload()),
        name: 'sellerWelcome',
        meta: {
          title: { zh: '欢迎中心', en: 'Welcome' },
        },
      },
      {
        path: 'merchant-info-manage',
        component: () => import('@/pc/views/pages/merchants/seller-center/merchant-info-manage/merchant-info-manage.vue').catch(() => location.reload()),
        name: 'merchantInfoManage',
        meta: {
          title: { zh: '商家资料管理', en: 'Merchant Information Management' },
        },
      },
      {
        path: 'product-manage',
        component: () => import('@/pc/views/pages/merchants/seller-center/product-manage/product-manage.vue').catch(() => location.reload()),
        name: 'productManage',
        meta: {
          title: { zh: '商品管理', en: 'Product Manage' },
        },
      },
      {
        path: 'add-product',
        component: () => import('@/pc/views/pages/merchants/seller-center/add-product-new/add-product-new.vue').catch(() => location.reload()),
        name: 'addProduct',
        meta: {
          title: { zh: '商品新增', en: 'Add Product' },
        },
      },
      {
        path: 'store-manage',
        component: () => import('@/pc/views/pages/merchants/seller-center/store-manage/store-manage.vue').catch(() => location.reload()),
        name: 'storeManage',
        meta: {
          title: { zh: '店铺管理', en: 'Store Manage' },
        },
      },
      {
        path: 'order-manage',
        component: () => import('@/pc/views/pages/merchants/seller-center/order-manage/order-manage.vue').catch(() => location.reload()),
        name: 'orderManage',
        meta: {
          title: { zh: '订单管理', en: 'Order Manage' },
        },
      },
      {
        path: 'order-detail',
        component: () => import('@/pc/views/pages/merchants/seller-center/order-detail/order-detail.vue').catch(() => location.reload()),
        name: 'orderDetail',
        meta: {
          title: { zh: '订单详情', en: 'Order Detail' },
        },
      },
      {
        path: 'live-product-manage',
        component: () => import('@/pc/views/pages/merchants/seller-center/live-product-manage/live-product-manage.vue').catch(() => location.reload()),
        name: 'liveProductManage',
        redirect: '/seller-center/live-product-manage/uninclude-live-pool',
        meta: {
          title: { zh: '直播商品管理', en: 'Live Product Manage' },
        },
        parentRouter: true,
        children: [
          {
            path: 'uninclude-live-pool',
            component: () =>
              import('@/pc/views/pages/merchants/seller-center/live-product-manage/uninclude-live-pool/uninclude-live-pool.vue').catch(() => location.reload()),
            name: 'unincludeLivePool',
            meta: {
              title: { zh: '未入直播选品池', en: 'uninclude Live Pool' },
            },
          },
          {
            path: 'include-live-pool',
            component: () =>
              import('@/pc/views/pages/merchants/seller-center/live-product-manage/include-live-pool/include-live-pool.vue').catch(() => location.reload()),
            name: 'includeLivePool',
            meta: {
              title: { zh: '已入直播选品池', en: 'include Live Pool' },
            },
          },

          {
            path: 'add-live-product',
            component: () =>
              import('@/pc/views/pages/merchants/seller-center/live-product-manage/add-live-product/add-live-product.vue').catch(() => location.reload()),
            name: 'addLiveProduct',
            meta: {
              title: { zh: '新增直播商品', en: 'Add Live Product' },
            },
          },
        ],
      },
      {
        path: 'platform-contacts',
        component: () => import('@/pc/views/pages/merchants/seller-center/platformContacts/platformContacts.vue').catch(() => location.reload()),
        name: 'platformContacts',
        meta: {
          title: { zh: '平台通讯录', en: 'platform Contacts' },
        },
      },

      //
    ],
  },
  {
    path: '/service-center',
    component: ForeignTradeServiceCenter,
    name: 'ForeignTradeServiceCenter',
    redirect: '/service-center/welcome',
    meta: {
      title: { zh: '外综服服务商后台', en: 'Foreign Trade Service center' },
      parentRouter: true,
    },
    children: [
      {
        path: 'welcome',
        component: () => import('@/pc/views/pages/merchants/seller-center/welcome/welcome.vue').catch(() => location.reload()),
        name: 'foreignTradeServiceWelcome',
        meta: {
          title: { zh: '欢迎中心', en: 'Welcome' },
        },
      },
      {
        path: 'service-info-manage',
        component: () => import('@/pc/views/pages/merchants/service-center/service-info-manage/service-info-manage.vue').catch(() => location.reload()),
        name: 'serviceInfoManage',
        meta: {
          title: { zh: '服务商资料管理', en: 'Service Information Management' },
        },
      },
      {
        path: 'service-manage',
        component: () => import('@/pc/views/pages/merchants/service-center/service-manage/service-manage.vue').catch(() => location.reload()),
        name: 'serviceManage',
        meta: {
          title: { zh: '服务管理', en: 'Service Manage' },
        },
      },
      {
        path: 'add-service',
        component: () => import('@/pc/views/pages/merchants/service-center/add-service/add-service.vue').catch(() => location.reload()),
        name: 'addService',
        meta: {
          title: { zh: '服务新增', en: 'Add Service' },
        },
      },
      {
        path: 'contacts',
        component: () => import('@/pc/views/pages/merchants/service-center/contacts/contacts.vue').catch(() => location.reload()),
        name: 'contacts',
        meta: {
          title: { zh: '平台通讯录', en: 'Contacts' },
        },
      },
    ],
  },
  {
    path: '/merchant-services-center',
    component: MerchantServicesCenter,
    name: 'merchantServicesCenter',
    redirect: '/merchant-services-center/welcome',
    meta: {
      title: { zh: '供应商服务商', en: 'Supplier Service center' },
      parentRouter: true,
    },
    children: [
      {
        path: 'welcome',
        component: () => import('@/pc/views/pages/merchants/seller-center/welcome/welcome.vue').catch(() => location.reload()),
        name: 'merchantServicesWelcome',
        meta: {
          title: { zh: '欢迎中心', en: 'Welcome' },
        },
      },
      {
        path: 'merchant-manage',
        component: () => import('@/pc/views/pages/merchants/merchant-services-center/merchant-manage/merchant-manage.vue').catch(() => location.reload()),
        name: 'merchantManage',
        meta: {
          title: { zh: '供应商管理', en: 'Merchant Management' },
        },
      },
      {
        path: 'merchant-product-manage',
        component: () =>
          import('@/pc/views/pages/merchants/merchant-services-center/merchant-product-manage/merchant-product-manage.vue').catch(() => location.reload()),
        name: 'merchantProductManage',
        meta: {
          title: { zh: '商品管理', en: 'Product Management' },
        },
      },
      {
        path: 'merchant-order-manage',
        component: () =>
          import('@/pc/views/pages/merchants/merchant-services-center/merchant-order-manage/merchant-order-manage.vue').catch(() => location.reload()),
        name: 'merchantOrderManage',
        meta: {
          title: { zh: '订单管理', en: 'Order Management' },
        },
      },
      {
        path: 'my-info',
        component: () => import('@/pc/views/pages/merchants/merchant-services-center/my-info/my-info.vue').catch(() => location.reload()),
        name: 'merchantServicesInfo',
        meta: {
          title: { zh: '账户管理', en: 'Account Management' },
        },
      },
    ],
  },
  {
    path: '/buyer-center/login',
    name: 'buyerCenterLogin',
    redirect: `/merchants-login/${MERCHANTS_TYPE.BUYER.id}`,
    meta: {},
  },
  {
    path: '/seller-center/login',
    name: 'sellerCenterLogin',
    redirect: `/merchants-login/${MERCHANTS_TYPE.SELLER.id}`,
    meta: {},
  },
  {
    path: '/merchant-services-center/login',
    name: 'merchantServicesCenterLogin',
    redirect: `/merchants-login/${MERCHANTS_TYPE.MERCHANT_SERVICE.id}`,
    meta: {},
  },
  {
    path: '/buyer-service-center/login',
    name: 'buyerServiceCenterLogin',
    redirect: `/merchants-login/${MERCHANTS_TYPE.BUYER_SERVICE.id}`,
    meta: {},
  },
  {
    path: '/service-center/login',
    name: 'serviceCenterLogin',
    redirect: `/merchants-login/${MERCHANTS_TYPE.FOREIGN_TRADE_SERVICE.id}`,
    meta: {},
  },
  {
    path: '/merchants-audit',
    component: () => import('@/pc/views/pages/merchants/merchants-audit/merchants-audit.vue').catch(() => location.reload()),
    name: 'merchantsAudit',
    meta: {},
  },
  {
    path: '/merchants-login-audit',
    component: () => import('@/pc/views/pages/merchants/merchants-login-audit/merchants-login-audit.vue').catch(() => location.reload()),
    name: 'merchantsLoginAudit',
    meta: {},
  },
  {
    path: '/merchants-login/:id',
    component: () => import('@/pc/views/pages/merchants/merchants-login/merchants-login.vue').catch(() => location.reload()),
    name: 'merchantsLogin',
    meta: {
      title: { zh: '商家中心', en: 'Merchants center' },
    },
  },
  {
    path: '/merchants-enter',
    component: () => import('@/pc/views/pages/merchants/enter/enter.vue').catch(() => location.reload()),
    name: 'merchantsEnter',
    meta: {
      title: { zh: '商家入驻', en: 'Merchants Enter' },
    },
  },
]

const ignore = ['merchantsLogin', 'sellerCenterLogin', 'merchantServicesCenterLogin', 'buyerServiceCenterLogin', 'serviceCenterLogin', 'buyerCenterLogin'] // 无需登录即可访问的页面名称
setCommonMeta(merchantsConfigs, { requiredLogin: true, code: 'merchants' }, ignore)

const buyerCenterIgnoreArr = merchantsConfigs.find((item) => item.name === 'buyerCenter').children.map((item) => item.name)
setCommonMeta(merchantsConfigs, { [IS_MERCHANTS_BACKEND]: true }, ['buyerCenter', ...buyerCenterIgnoreArr])

export default merchantsConfigs
