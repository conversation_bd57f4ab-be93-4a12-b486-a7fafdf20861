const aiFeaturesConfigs = [
  {
    path: '/ai-features',
    redirect: '/ai-features/video-translation',
    component: () => import('@/pc/views/pages/ai-features/layout.vue').catch(() => location.reload()),
    children: [
      {
        path: 'video-translation',
        component: () => import('@/pc/views/pages/ai-features/video-translation/index.vue').catch(() => location.reload()),
        meta: {
          title: '视频翻译',
        },
      },
      {
        path: 'template-manage',
        component: () => import('@/pc/views/pages/ai-features/template-manage/index.vue').catch(() => location.reload()),
        meta: {
          title: '模版管理',
        },
      },
      {
        path: 'person-square',
        component: () => import('@/pc/views/pages/ai-features/person-square/index.vue').catch(() => location.reload()),
        meta: {
          title: '数字代言人',
        },
      },
      {
        path: 'product-picture',
        component: () => import('@/pc/views/pages/ai-features/product-picture/index.vue').catch(() => location.reload()),
        meta: {
          title: 'AI 商品图',
        },
      },
      {
        path: 'video-create',
        component: () => import('@/pc/views/pages/ai-features/video-create/index.vue').catch(() => location.reload()),
        meta: {
          title: '自主创作',
        },
      },
      {
        path: 'short-video',
        component: () => import('@/pc/views/pages/ai-features/short-video/index.vue').catch(() => location.reload()),
        meta: {
          title: '内容管理',
        },
      },
    ],
  },
]

export default aiFeaturesConfigs
