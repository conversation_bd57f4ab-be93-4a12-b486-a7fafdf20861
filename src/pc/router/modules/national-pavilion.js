const nationalPavilionConfigs = [
  {
    path: '/uae-pavilion',
    component: () => import('@/pc/views/pages/national-pavilion/united-arab-emirates.vue').catch(() => location.reload()),
    name: 'uaePavilion',
    meta: {
      title: { zh: '阿联酋国家馆', en: 'UAE Pavilion' },
      constantsKey: 'usa_pavilion',
      flag: '/national-store/uae.png',
    },
  },
  {
    path: '/indonesia-pavilion',
    component: () => import('@/pc/views/pages/national-pavilion/indonesia-pavilion.vue').catch(() => location.reload()),
    name: 'indonesiaPavilion',
    meta: {
      title: { zh: '印尼馆', en: 'Indonesia Pavilion' },
      constantsKey: 'indonesia_pavilion',
      flag: '/national-store/yinni_flag.png',
    },
  },
  {
    path: '/sa-pavilion',
    component: () => import('@/pc/views/pages/national-pavilion/saudi-arabia.vue').catch(() => location.reload()),
    name: 'saPavilion',
    meta: {
      title: { zh: '沙特阿拉伯国家馆', en: 'Saudi Arabia Pavilion' },
      constantsKey: 'sa_pavilion',
      flag: '/national-store/sa.png',
    },
  },
  {
    path: '/thailand-pavilion',
    component: () => import('@/pc/views/pages/national-pavilion/thailand-pavilion.vue').catch(() => location.reload()),
    name: 'thailandPavilion',
    meta: {
      title: { zh: '泰国馆', en: 'Thailand Pavilion' },
      constantsKey: 'thai_pavilion',
      flag: '/national-store/taiguo_flag.png',
    },
  },
]

export default nationalPavilionConfigs
