import MobileComponent from '@/pc/views/mobile/layout/index.vue'

const mobileConfigs = [
  {
    path: '/m',
    component: MobileComponent,
    name: 'mobile',
    redirect: '/m/enter-audit',
    meta: {
      title: { zh: '商家入驻', en: 'Merchants Enter' },
    },
    children: [
      {
        path: 'enter-audit',
        component: () => import('@/pc/views/mobile/pages/enter-audit/index.vue').catch(() => location.reload()),
        name: 'mEnterAudit',
        meta: {
          title: { zh: '供应商入驻', en: 'Merchants Enter' },
        },
      },
      {
        path: 'enter',
        component: () => import('@/pc/views/mobile/pages/enter/index.vue').catch(() => location.reload()),
        name: 'enter',
        meta: {
          title: { zh: '供应商入驻', en: 'Supplier Enter' },
        },
      },
    ],
  },
]

export default mobileConfigs
