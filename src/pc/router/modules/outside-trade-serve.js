import layoutSecondLevel from '@/pc/views/layout/layout-secondLevel.vue'

const outsideTradeServeConfigs = [
  {
    path: '/outside-trade-serve',
    component: () => import('@/pc/views/pages/outside-trade-serve/outside-trade-serve.vue').catch(() => location.reload()),
    name: 'outsideTradeServe',
    meta: {
      title: { zh: '外贸综合服务', en: 'Trade Service' },
    },
  },
  {
    path: '/outside-trade/serve-detail/:id',
    name: 'serveDetail',
    component: () => import('@/pc/views/pages/outside-trade-serve/pages/serve-detail/index.vue').catch(() => location.reload()),
    meta: {
      title: { zh: '外贸综合服务', en: 'Trade Service' },
    },
  },
  {
    path: '/outside-trade',
    component: layoutSecondLevel,
    name: 'outsideTrade',
    redirect: '/outside-trade-serve',
    meta: {
      title: { zh: '外贸综合服务', en: 'Trade Service' },
    },
    children: [
      {
        path: 'country-exhibition',
        component: () => import('@/pc/views/pages/outside-trade-serve/pages/country-exhibition/country-exhibition.vue').catch(() => location.reload()),
        name: 'outsideTradeServeCountryExhibition',
        meta: {
          title: { zh: '国际物流', en: 'International Logistics' },
        },
      },
      {
        path: 'overseas-warehous',
        component: () => import('@/pc/views/pages/outside-trade-serve/pages/overseas-warehous/overseas-warehous.vue').catch(() => location.reload()),
        name: 'outsideTradeServeOverseasWarehous',
        meta: {
          title: { zh: '海外仓储', en: 'Overseas Warehouse' },
        },
      },
      {
        path: 'customs-introduce',
        component: () => import('@/pc/views/pages/outside-trade-serve/pages/customs-introduce/customs-introduce.vue').catch(() => location.reload()),
        name: 'outsideTradeServeCustomsIntroduce',
        meta: {
          title: { zh: '海关窗口', en: 'Customs' },
        },
      },
      {
        path: 'open-company',
        component: () => import('@/pc/views/pages/outside-trade-serve/pages/open-company/open-company.vue').catch(() => location.reload()),
        name: 'outsideTradeServeOpenCompany',
        meta: {
          title: { zh: '开设公司', en: 'Company Registration' },
        },
      },
      {
        path: 'country-expo',
        component: () => import('@/pc/views/pages/outside-trade-serve/pages/country-expo/country-expo.vue').catch(() => location.reload()),
        name: 'outsideTradeServeCountryExpo',
        meta: {
          title: { zh: '国际展会', en: 'International Expo' },
        },
      },
      {
        path: 'restaurant',
        component: () => import('@/pc/views/pages/outside-trade-serve/pages/restaurant/restaurant.vue').catch(() => location.reload()),
        name: 'restaurant',
        meta: {
          title: { zh: '餐饮', en: 'Dining' },
        },
      },
      {
        path: 'accommodation',
        component: () => import('@/pc/views/pages/outside-trade-serve/pages/restaurant/restaurant.vue').catch(() => location.reload()),
        name: 'accommodation',
        meta: {
          title: { zh: '住宿', en: 'Accommodation' },
        },
      },
      {
        path: 'entertainment',
        component: () => import('@/pc/views/pages/outside-trade-serve/pages/restaurant/restaurant.vue').catch(() => location.reload()),
        name: 'entertainment',
        meta: {
          title: { zh: '娱乐', en: 'Entertainment' },
        },
      },
      {
        path: 'foreing-info',
        name: 'foreingInfo',
        component: () => import('@/pc/views/pages/outside-trade-serve/pages/information-list/information-list.vue').catch(() => location.reload()),
        meta: {
          title: { zh: '外贸资讯', en: 'Foreign Trade Information' },
        },
      },
      {
        path: 'external-reaource',
        name: 'externalReaource',
        component: () => import('@/pc/views/pages/outside-trade-serve/pages/information-list/information-list.vue').catch(() => location.reload()),
        meta: {
          title: { zh: '内外资源融合', en: 'Integration of Internal and External Resources' },
        },
      },
      {
        path: 'taxation',
        name: 'taxation',
        component: () => import('@/pc/views/pages/outside-trade-serve/pages/information-list/information-list.vue').catch(() => location.reload()),
        meta: {
          title: { zh: '税务协调', en: 'Tax Coordinate' },
        },
      },
      {
        path: 'buy-visa',
        name: 'buyVisa',
        component: () => import('@/pc/views/pages/outside-trade-serve/pages/information-list/information-list.vue').catch(() => location.reload()),
        meta: {
          title: { zh: '采购商签证', en: 'Visa' },
        },
      },
      {
        path: 'forex-trading',
        name: 'forexTrading',
        component: () => import('@/pc/views/pages/outside-trade-serve/pages/information-list/information-list.vue').catch(() => location.reload()),
        meta: {
          title: { zh: '外汇交易', en: 'Foreign Exchange Trading' },
        },
      },
      {
        path: 'financial-matter',
        name: 'financialMatter',
        component: () => import('@/pc/views/pages/outside-trade-serve/pages/information-list/information-list.vue').catch(() => location.reload()),
        meta: {
          title: { zh: '财会事项', en: 'Financial Matters' },
        },
      },
      {
        path: 'local-trade',
        name: 'localTrade',
        component: () => import('@/pc/views/pages/outside-trade-serve/pages/information-list/information-list.vue').catch(() => location.reload()),
        meta: {
          title: { zh: '地方外贸资讯', en: 'Local Trade Information' },
        },
      },
      {
        path: 'trade-fund',
        name: 'tradeFund',
        component: () => import('@/pc/views/pages/outside-trade-serve/pages/trade-fund/trade-fund.vue').catch(() => location.reload()),
        meta: {
          title: { zh: '外贸基金', en: 'Trade Fund' },
        },
      },
      {
        path: 'cost-finance',
        component: () => import('@/pc/views/pages/outside-trade-serve/pages/cost-finance/cost-finance.vue').catch(() => location.reload()),
        name: 'costFinance',
        meta: {
          title: { zh: '低成本融资', en: 'Low-cost Financing' },
        },
      },
      {
        path: 'cross-border-trade',
        name: 'trcrossBorderTradede',
        component: () => import('@/pc/views/pages/outside-trade-serve/pages/cross-border-trade/cross-border-trade.vue').catch(() => location.reload()),
        meta: {
          title: { zh: '跨境贸易', en: 'Cross-border Trade' },
        },
      },
    ],
  },
]

export default outsideTradeServeConfigs
