import { setCommonMeta } from '../index'

// 添加全局配置信息
const commonMeta = { requiredLogin: true }
const ignore = ['mall', 'goodsList', 'goodsDetail', 'onlineMall', 'onlineMallRegister', 'shop'] // 无需登录即可访问的页面名称

const mallConfigs = [
  {
    path: '/mall',
    name: 'mall',
    component: () => import('@/pc/views/pages/mall/home/<USER>').catch(() => location.reload()),
    meta: {
      title: '中国大集-临沂商城一站式全域B2B数字商贸综合服务平台',
    },
  },
  {
    path: '/mall/goods-list',
    name: 'goodsList',
    component: () => import('@/pc/views/pages/mall/goods-list/goods-list.vue').catch(() => location.reload()),
    meta: {
      title: '中国大集-临沂商城一站式全域B2B数字商贸综合服务平台',
    },
  },
  {
    path: '/mall/goods-detail/:id',
    name: 'goodsDetail',
    component: () => import('@/pc/views/pages/mall/goods-detail/index.vue').catch(() => location.reload()),
    meta: {
      title: '中国大集-临沂商城一站式全域B2B数字商贸综合服务平台',
    },
  },
  {
    path: '/mall/shop/:id',
    name: 'shop',
    component: () => import('@/pc/views/pages/mall/shop/index.vue').catch(() => location.reload()),
    meta: {
      title: '中国大集-临沂商城一站式全域B2B数字商贸综合服务平台',
    },
  },
  // {
  //   path: '/mall/my-order',
  //   component: () => import('@/pc/views/pages/mall/my-order/my-order.vue').catch(() => location.reload()),
  //   meta: {
  //     title: '中国大集-临沂商城一站式全域B2B数字商贸综合服务平台',
  //   },
  // },
]

setCommonMeta(mallConfigs, commonMeta, ignore)

export default mallConfigs
