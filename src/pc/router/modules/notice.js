const noticeConfigs = [
  {
    path: '/notice',
    component: () => import('@/pc/views/pages/notice/list/list.vue').catch(() => location.reload()),
    name: 'noticeList',
    meta: {
      title: { zh: '通知公告', en: 'Notice' },
    },
  },
  {
    path: '/notice-detail/:id',
    component: () => import('@/pc/views/pages/notice/detail/detail.vue').catch(() => location.reload()),
    name: 'noticeDetail',
    meta: {
      title: { zh: '通知公告详情', en: 'Notice Details' },
    },
  },
]

export default noticeConfigs
