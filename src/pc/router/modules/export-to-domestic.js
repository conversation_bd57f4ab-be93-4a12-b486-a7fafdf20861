import { setCommonMeta } from '../index'

// 添加全局配置信息
const commonMeta = { requiredLogin: true }
const ignore = ['exportToDomestic', 'categoryList', 'application'] // 无需登录即可访问的页面名称

const exportToDomesticConfigs = [
  {
    path: '/export-to-domestic',
    name: 'exportToDomestic',
    component: () => import('@/pc/views/pages/export-to-domestic/export-to-domestic.vue').catch(() => location.reload()),
    meta: {
      title: '出口转内销',
    },
  },
  {
    path: '/export-to-domestic/category-list/:id',
    name: 'categoryList',
    component: () => import('@/pc/views/pages/export-to-domestic/category-list/category-list.vue').catch(() => location.reload()),
    meta: {
      title: '分类页',
    },
  },
  {
    path: '/export-to-domestic/application',
    name: 'application',
    component: () => import('@/pc/views/pages/export-to-domestic/application/application.vue').catch(() => location.reload()),
    meta: {
      title: '报名表',
    },
  },
]

setCommonMeta(exportToDomesticConfigs, commonMeta, ignore)

export default exportToDomesticConfigs
