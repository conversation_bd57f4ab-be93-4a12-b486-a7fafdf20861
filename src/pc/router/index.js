import { ElMessageBox } from 'element-plus'
import { createRouter, createWebH<PERSON><PERSON> } from 'vue-router'
import { ExtendKey, ExtendValue } from '@/constants/common'
import { LINYI_CHINA_MARKET_MAP } from '@/constants/special-field'
import { getMerchantsLoginRoutePath, isMerchantsBackend } from '@/pc/hooks/merchants'
// import { useUserStore } from '@/pc/stores'
import event from '@/event'
import { OPEN_NEW_LOGIN } from '@/event/modules/site.js'
import { useStorageLocale } from '@/i18n'
import user from '@/pc/utils/user'
import daJiConfigs from './modules/ai-daji'
import aiFeaturesConfigs from './modules/ai-features'
import cart from './modules/cart'
import exportToDomesticConfigs from './modules/export-to-domestic'
import globalShop from './modules/global-shop'
import mall from './modules/mall'
import market from './modules/market'
import merchants from './modules/merchants'
import mobileConfigs from './modules/mobile'
import pavilion from './modules/national-pavilion'
import noticeConfigs from './modules/notice'
import outsideTradeServe from './modules/outside-trade-serve'
import rulesConfigs from './modules/rules'
import visitExhibitionConfigs from './modules/visit-exhibition'

// function getUserInfo() {
//   const userStore = useUserStore()
//   return userStore.userInfo
// }

const routes = [
  {
    path: '/',
    redirect: '/home',
  },
  {
    path: '/home',
    name: 'home',
    component: () => import('@/pc/views/pages/home/<USER>').catch(() => location.reload()),
  },
  {
    path: '/redirect',
    component: () => import('@/pc/views/pages/redirect/redirect.vue').catch(() => location.reload()),
  },
  {
    path: '/skip-page',
    component: () => import('@/pc/views/pages/skip-page/skip-page.vue').catch(() => location.reload()),
  },
  {
    path: '/register-h5',
    component: () => import('@/pc/views/pages/register-h5/register-h5.vue').catch(() => location.reload()),
  },
  {
    path: '/register',
    component: () => import('@/pc/views/pages/register/register.vue').catch(() => location.reload()),
  },
  /* 政府服务站群 */
  {
    path: '/government-services',
    component: () => import('@/pc/views/pages/government-services/government-services.vue').catch(() => location.reload()),
    meta: {
      title: '政府服务站群',
    },
  },
  ...market,
  ...outsideTradeServe,
  ...globalShop,
  ...aiFeaturesConfigs,
  ...mall,
  ...merchants,
  ...daJiConfigs,
  ...visitExhibitionConfigs,
  ...rulesConfigs,
  ...noticeConfigs,
  ...pavilion,
  ...mobileConfigs,
  ...exportToDomesticConfigs,
  ...cart,
  // 不存在的路由跳转
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/home',
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

const { storageLocale } = useStorageLocale()

const saveExtendInfo = (to) => {
  const { extendKey, extendValue } = to?.query || {}

  if (extendKey && extendValue) {
    localStorage.setItem(ExtendKey, extendKey)
    localStorage.setItem(ExtendValue, extendValue)
  }
}

router.beforeEach(async (to, from, next) => {
  // saveExtendInfo
  saveExtendInfo(to)
  // 设置title
  document.title = storageLocale.value === 'zh' ? '中国大集-临沂商城一站式全域B2B数字商贸综合服务平台' : LINYI_CHINA_MARKET_MAP[storageLocale.value]
  const isAuthenticated = !!user.getToken()
  if (!isAuthenticated && to.meta.requiredLogin) {
    if (isMerchantsBackend(to.meta)) {
      next(getMerchantsLoginRoutePath(to))
    } else {
      event.emit(OPEN_NEW_LOGIN, {})
      next('/mall')
    }
  } else {
    next()
  }
})

router.afterEach(() => {
  setTimeout(() => window?.translate?.execute(), 300)
  // 切换路由时，让页面返回顶部
  window.scrollTo(0, 0)
})

/**
 * 为路由配置对象设置全局参数
 * @param {object[]} routerObj 路由对象数组
 * @param {object} commonMeta 需要统一设置的 meta
 * @param {boolean} commonMeta.requiredLogin 访问当前页面是否需要登录
 * @param {object} commonMeta.title 标题
 * @param {string} commonMeta.code 唯一标识
 * @param {object} commonMeta.showInMenu 是否显示在菜单中
 * @param {boolean} commonMeta.isMerchantsBackend 只显示用户菜单
 * @param {string[]} ignore 需要忽略的路由名称
 */
export function setCommonMeta(routerObj, commonMeta, ignore) {
  routerObj.forEach((item) => {
    const isIgnored = ignore && ignore.length && item.name && ignore.includes(item.name)
    if (!isIgnored) {
      item.meta = Object.assign({}, commonMeta, item.meta)
    }
    if (item.children && item.children.length > 0) {
      setCommonMeta(item.children, commonMeta, ignore)
    }
  })
}

// 系统发布导致的报错弹窗提醒
let hasChunkLoadError = false
router.onError((error) => {
  const isChunkLoadFailed = /Loading chunk [\w-]+ failed|Failed to fetch dynamically imported module/.test(error.message)

  if (isChunkLoadFailed && !hasChunkLoadError) {
    hasChunkLoadError = true

    ElMessageBox.alert('很抱歉，页面资源加载失败，可能由于网络问题或页面更新。请点击“重新加载”按钮。', '页面加载失败', {
      type: 'warning',
      confirmButtonText: '重新加载',
      callback: (action) => {
        if (action === 'confirm') {
          location.reload()
        }
      },
    })
  }
})

export default router
