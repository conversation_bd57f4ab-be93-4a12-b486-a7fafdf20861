// 路由白名单

// 这个白名单维护的是顶部导航是否遮盖住页面的一部分，如果在下面的数组中，就会遮挡住
export const PAGE_HEADER_NAV_WHITH_LIST = [
  '/home',
  '/market',
  '/outside-trade-serve',
  '/uae-pavilion',
  '/sa-pavilion',
  '/cart/success',
  '/cart/confirm',
  '/cart/list',
  'mall',
  'goodsList',
  'goodsDetail',
  'shop',
  'serveDetail',
  'thailandPavilion',
  'indonesiaPavilion',
  'merchantsLogin',
  'exportToDomestic',
  'categoryList',
  'application',
]

// 未登录不去home页
export const NOT_BACK_HOME_WHITE_LIST = ['/ai-features', 'goodsDetail', '/uae-pavilion']

export const notBakHome = ({ path, name }) => {
  const pathArr = NOT_BACK_HOME_WHITE_LIST.filter((item) => item.startsWith('/'))
  const nameArr = NOT_BACK_HOME_WHITE_LIST.filter((item) => !item.startsWith('/'))
  return pathArr.some((item) => path.startsWith(item)) || nameArr.some((item) => name === item)
}
