import { createPinia } from 'pinia'
import { useCartStore } from './modules/cart'
import { useCategoryStore } from './modules/category'
import { useCategoryFilterStore } from './modules/category-filter'
import { useCategoryAdStore } from './modules/categoryAd'
import { useDictStore } from './modules/dict'
import { useRegionCodeStore } from './modules/region-code'
import { useUserStore } from './modules/user'

const pinia = createPinia()

export { useUserStore, useCategoryStore, useRegionCodeStore, useCategoryAdStore, useDictStore, useCategoryFilterStore, useCartStore }

export default pinia
