import { isValidJSON } from '@/utils/utils'

// 定义 Pinia 插件
export default function createPiniaPersist() {
  return ({ store, options }) => {
    const { persist, expire } = options || {}
    // 检查 store 是否需要持久化
    if (!persist) {
      return
    }

    // 检查 store 是否需要持久化
    const Storage = window[options.persist === 'localStorage' ? 'localStorage' : 'localStorage']
    const storageId = `pinia_${store.$id}`
    let storageValue = Storage.getItem(storageId)
    // 在 store 初始化时从 localStorage 恢复状态
    if (storageValue) {
      // 如果存的是对象
      if (isValidJSON(storageValue)) {
        storageValue = JSON.parse(storageValue)
        const { __expire__time, valueStr } = storageValue

        if (__expire__time && valueStr) {
          const isExpired = Date.now() > __expire__time
          storageValue = isExpired ? null : JSON.parse(valueStr)

          if (isExpired) Storage.removeItem(storageId)
        }
      }

      if (storageValue) {
        store.$patch(storageValue)
      }
    }

    // 监听 store 的变化，将变化后的状态保存到 localStorage
    store.$subscribe((mutation, state) => {
      let stateStr = JSON.stringify(state)
      if (expire && typeof expire === 'number') {
        stateStr = JSON.stringify({
          __expire__time: expire + Date.now(),
          valueStr: stateStr,
        })
      }
      Storage.setItem(storageId, stateStr)
    })
  }
}
