import { useBroadcastChannel } from '@vueuse/core'
import { onMounted, ref, watch } from 'vue'
import { debounce } from '@/common/js/util'

// 广播同步插件用于多页面tab更新store
export default function createPiniaBroadcastSyncPlugin() {
  return ({ store, options }) => {
    const { broadcastSync } = options || {}
    if (!broadcastSync) return

    // 初始化广播频道
    const { channel, isSupported, close } = useBroadcastChannel({
      name: options.name,
    })

    if (!isSupported.value) {
      console.warn('Broadcast Channel API is not supported in this browser.')
      return
    }

    const prevState = ref(JSON.stringify(store.$state)) // 用来存储之前的状态

    onMounted(() => {
      console.log('频道初始化完毕，准备监听消息...')
      channel.value.onmessage = debounce((message) => {
        try {
          const parsedMessage = JSON.parse(message.data)
          if (parsedMessage.type === 'state_update') {
            if (JSON.stringify(parsedMessage.payload) === prevState.value) {
              return
            }
            console.log('接收到更新的数据，正在更新store...')
            store.$patch(parsedMessage.payload)
            prevState.value = JSON.stringify(parsedMessage.payload) // 更新之前的状态
          }
        } catch (error) {
          console.error('广播消息处理失败:', error)
        }
      }, 100)
    })

    // 当状态发生变化时发送广播通知
    watch(
      () => store.$state,
      (newState) => {
        // 检查状态是否真的有变化
        if (JSON.stringify(newState) !== prevState.value) {
          if (channel.value) {
            channel.value.postMessage(JSON.stringify({ type: 'state_update', payload: newState }))
            prevState.value = JSON.stringify(newState) // 更新之前的状态
          }
        }
      },
      { deep: true },
    )

    // 提供清理方法
    store.closeBroadcastChannel = () => {
      close()
    }
  }
}
