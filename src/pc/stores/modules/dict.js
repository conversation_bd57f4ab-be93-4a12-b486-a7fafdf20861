import { defineStore } from 'pinia'
import { getDictListByKey, getPayPrice, getSaleTypeList } from '@/apis/goods'

export const useDictStore = defineStore('dict', {
  state: () => ({
    priceTypeOptions: [], // 前台类目绑定的广告图数组
    priceTypeMap: JSON.parse(localStorage.getItem('priceTypeMap')) || {}, // 币种字典
    saleTypeOptions: [], // 销售字典
    saleTypeMap: JSON.parse(localStorage.getItem('saleTypeMap')) || {}, // 销售字典
    payPriceOptions: JSON.parse(localStorage.getItem('payPriceOptions')) || [], // 支付币种字典
  }),
  getters: {},
  actions: {
    async getPriceTypeList() {
      const data = await getDictListByKey({ dictKey: 'price_type' })
      this.priceTypeOptions = data || []
      this.priceTypeMap = computed(() =>
        data.reduce((prev, cur) => {
          prev[cur.id] = cur
          return prev
        }, {}),
      )
      localStorage.setItem('priceTypeMap', JSON.stringify(this.priceTypeMap))
    },
    async getPayPrice() {
      const data = await getPayPrice()
      this.payPriceOptions = data || []
      localStorage.setItem('payPriceOptions', JSON.stringify(this.payPriceOptions))
    },
    async getSaleTypeList() {
      const data = await getSaleTypeList()
      this.saleTypeOptions = data || []
      this.saleTypeMap.value = computed(() =>
        data.reduce((prev, cur) => {
          prev[cur.id] = cur
          return prev
        }, {}),
      )
      localStorage.setItem('saleTypeMap', JSON.stringify(this.saleTypeMap.value))
    },
  },
})
