import { defineStore } from 'pinia'
import { useDictStore } from '@/pc/stores'
import { getUserConfig } from '@/apis/merchants'
// import { setUserConfig } from '@/apis/merchants'
import Storage from '@/common/js/storage'
import Arms from '@/pc/utils/arms'
import user from '@/pc/utils/user'

export const useUserStore = defineStore('user', {
  state: () => ({
    isLogined: !!user.getToken(), // 是否登录
    userInfo: user.getUserInfo(),
  }),
  actions: {
    // 登录状态
    setIsLogined(payload) {
      this.isLogined = payload
    },
    // 用户信息
    async setUserInfo(data) {
      console.log('setUserInfo 被调用，传入的 data:', data)
      console.log('当前 this.userInfo:', this.userInfo)

      this.userInfo = data
      Arms.setUserConfig(data.userName, data.id)
      // 往用户信息里追加货币语言字段
      const { language, priceType } = await this.getUserConfigInfo()
      console.log('getUserConfigInfo 返回:', { language, priceType })

      if (language) this.userInfo.language = language
      const payPrice = user.getPayPrice()
      // 本地有币种，但是和用户配置的币种不一致，则更新远端币种
      // if (payPrice && priceType !== payPrice) {
      //   setUserConfig({ priceType: payPrice })
      // }
      // 本地没有币种，则设置本地币种
      if (!payPrice && priceType) {
        this.setUserInfoPriceType(priceType)
      }

      console.log('最终设置的 userInfo:', this.userInfo)
      user.setUserInfo(this.userInfo)
      try {
        // 在获取用户信息的同时，获取货币语言字段
        const dictStore = await useDictStore()
        dictStore.getPayPrice()
      } catch (e) {
        console.log(e)
      }
    },
    setUserInfoPriceType(priceType) {
      user.setPayPrice(priceType)
    },
    // 获取用户配置信息
    async getUserConfigInfo() {
      try {
        return await getUserConfig()
      } catch (e) {
        console.log(e)
        return {}
      }
    },
    // 退出登录
    logout() {
      user.clearToken()
      user.clearUserInfo()
      Storage.remove('priceTypeMap') // 清除币种字典
      this.userInfo = null
      this.isLogined = false
    },
  },
  broadcastSync: true, // 广播同步
})
