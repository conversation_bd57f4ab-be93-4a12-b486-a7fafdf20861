import { defineStore } from 'pinia'
import { getCountryList } from '@/apis/common'
import { getAreaList } from '@/apis/goods'
import { simpleDeepClone } from '@/utils/utils'

const targetValueToTop = (data, targetValue, targetValue1, targetValue2) => {
  const arr = Array.isArray(data) ? simpleDeepClone(data) : []

  // 查找目标节点
  const topItem = arr.find((item) => item.value === targetValue)

  // 如果有子级且提供了 targetValue1，则递归处理 children
  if (topItem?.children?.length && targetValue1) {
    topItem.children = targetValueToTop(topItem.children, targetValue1, targetValue2)
  }

  console.log(topItem)
  // 构造新数组：先放 topItem（如果存在），再放其他非目标项
  return [...(topItem ? [topItem] : []), ...arr.filter((item) => item.value !== targetValue)]
}

export const useRegionCodeStore = defineStore('regionCode1', {
  state: () => ({
    areaList: [],
    countryLists: [],
  }),
  getters: {
    areaListTopSD: (state) => {
      const TOP_VALUE = '370000'
      const TOP_VALUE_LEVEL2 = '371300'
      const arr = state.areaList || []
      return targetValueToTop(arr, TOP_VALUE, TOP_VALUE_LEVEL2)
    },
  },
  actions: {
    async queryAreaList() {
      const { data } = await getAreaList()
      this.areaList = data || []
    },
    async queryCountryList() {
      const res = await getCountryList()
      this.countryLists = res || []
    },
  },
  persist: true, // 添加这个标志
})
