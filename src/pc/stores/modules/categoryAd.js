import { defineStore } from 'pinia'
import { getCategoryAd } from '@/apis/goods'

export const useCategoryAdStore = defineStore('categoryAd', {
  state: () => ({
    categoryAdList: [], // 前台类目绑定的广告图数组
  }),
  getters: {},
  actions: {
    async getCategoryAdList() {
      const data = await getCategoryAd()
      this.categoryAdList = data || []
    },
  },
  persist: false, // 持久化缓存
  expire: 10 * 60 * 1000,
})
