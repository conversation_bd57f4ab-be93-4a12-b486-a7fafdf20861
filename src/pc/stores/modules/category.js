import { defineStore } from 'pinia'
import { getCategoryTree } from '@/apis/goods'

function _getChildren(parentList) {
  return parentList.reduce((prev, cur) => {
    if (cur?.subList?.length) {
      prev = prev.concat(cur.subList)
    }
    return prev
  }, [])
}

export const useCategoryStore = defineStore('category', {
  state: () => ({
    categoryTree: [],
  }),
  getters: {
    categoryLevel1: (state) => state.categoryTree,
    categoryLevel2: (state) => _getChildren(state.categoryTree),
    categoryLevel3: (state) => _getChildren(_getChildren(state.categoryTree)),
  },
  actions: {
    async getCategoryList() {
      const body = { categoryType: 2 } // 前台类目传 2
      const data = await getCategoryTree(body)
      this.categoryTree = data || []
    },
  },
  persist: false, // 添加这个标志
  expire: 10 * 60 * 1000,
})
