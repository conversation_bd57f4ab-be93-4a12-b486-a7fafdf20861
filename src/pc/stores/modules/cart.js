import { defineStore } from 'pinia'
import { useUserStore } from '@/pc/stores'
import { getCount } from '@/apis/order'

export const useCartStore = defineStore(
  'cart',
  () => {
    const orderSubmitParams = ref(null)
    const cartCount = ref(0)
    const userStore = useUserStore()

    const setOrderSubmitParams = (params) => {
      orderSubmitParams.value = params
    }

    const getCartCount = async () => {
      if (!userStore.isLogined) {
        cartCount.value = 0
        return
      }
      const data = await getCount()
      cartCount.value = typeof data === 'number' ? data : 0
    }

    const updateCartCount = (val) => {
      if (val === cartCount.value) return
      cartCount.value = val
    }

    watch(
      () => userStore.isLogined,
      () => {
        getCartCount()
      },
      {
        immediate: true,
      },
    )

    return {
      orderSubmitParams,
      setOrderSubmitParams,
      getCartCount,
      cartCount,
      updateCartCount,
    }
  },
  {
    persist: true,
  },
)
