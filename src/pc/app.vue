<style lang="scss"></style>

<template>
  <el-config-provider :locale="elementLang" :message="messageConfig">
    <LayoutHeader v-if="!isSkipPage"></LayoutHeader>
    <router-view></router-view>
    <LayoutFooter v-if="!isSkipPage" v-show="!isLoading"></LayoutFooter>
    <!-- 大集AI入口 -->
    <!-- <AiTalkEntry v-if="!isSkipPage"></AiTalkEntry> -->
    <chatDialog v-if="isLivePage"></chatDialog>
    <!-- 大集AI弹窗 -->
    <AiDialog></AiDialog>
    <!-- 登录/注册弹窗 -->
    <LoginDialog></LoginDialog>
    <!-- 买家卖家登录/注册弹窗 -->
    <NewLoginDialog></NewLoginDialog>
    <!-- 右侧侧边栏 -->
    <RightSideNav v-if="!isSkipPage"></RightSideNav>
  </el-config-provider>
</template>

<script setup>
import en from 'element-plus/dist/locale/en'
import zhCn from 'element-plus/dist/locale/zh-cn'
import { useI18n } from 'vue-i18n'
import chatDialog from '@/pc/components/chat-dialog/chat-dialog.vue'
import LoginDialog from '@/pc/components/login/login.vue'
import NewLoginDialog from '@/pc/components/login/new-login.vue'
import RightSideNav from '@/pc/components/right-side-nav/right-side-nav.vue'
// import AiTalkEntry from '@/pc/views/pages/home/<USER>/ai-talk-entry.vue'
import LayoutFooter from './views/layout/layout-footer.vue'
import LayoutHeader from './views/layout/layout-header.vue'
import AiDialog from './views/pages/ai-chatbot/ai-dialog.vue'

const { locale } = useI18n()
const elementLang = computed(() => {
  return {
    en,
    zh: zhCn,
  }[locale.value]
})
const messageConfig = reactive({
  offset: 220,
})
const router = useRouter()
const route = useRoute()
const isSkipPage = computed(() => ['/skip-page', '/register-h5', '/', '/m/enter-audit', '/m/enter'].includes(route.path))
const isLivePage = ref(false)

watch(
  () => route.path,
  (newVal) => {
    if (newVal && newVal.includes('live-product-manage')) {
      isLivePage.value = true
    } else {
      isLivePage.value = false
    }
  },
)
const isLoading = ref(false)
onMounted(() => {
  router.beforeEach(() => {
    isLoading.value = true
  })

  router.afterEach(() => {
    isLoading.value = false
  })

  window.translate?.listener.start()
  nextTick(() => {
    window?.translate?.execute()
  })
})
</script>
