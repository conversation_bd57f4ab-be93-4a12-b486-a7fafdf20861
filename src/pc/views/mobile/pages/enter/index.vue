<template>
  <div :key="storageLocale" class="enter-wrap bg-white">
    <div class="fixed w-full h-[44px] flex justify-between items-center bg-white text-[18px] font-600 z-10 px-4">
      <div class="w-4 flex items-center justify-center" @click="goBack">
        <span><icon v-if="storageLocale !== 'ar'" class="mr-1" type="icon-fh" :size="16" /><icon v-else class="mr-1" type="icon-xiala1" :size="16" /></span>
      </div>
      <div class="w-full text-center">填写入驻信息</div>
      <div class="w-4"></div>
    </div>
    <div class="h-[44px]"></div>
    <el-form
      ref="enterFormRef"
      :model="enterForm"
      :rules="enterFormRules"
      class="enter-form p-5"
      label-position="top"
      scroll-to-error
      :scroll-into-view-options="{ block: 'start', behavior: 'instant' }"
      size="large"
    >
      <div class="form-title">企业信息</div>
      <!-- 营业执照 -->
      <el-form-item class="business-license tips-item" label="" prop="companyLicenseUrl">
        <template #label>
          <span>营业执照</span>
          <span class="text-3 mx-1 text-[#D33232]">营业执照原件照片（清晰且露出4个角）</span>
        </template>
        <div class="w-full">
          <ImageUploadEl class="relative" v-model="enterForm.companyLicenseUrl" :dir="OSS_DIR.CERT" :hoverRemove="false">
            <template #default="{ url }">
              <div class="w-[130px] h-[78px] img-upload-wrap">
                <img v-if="url" :src="url" class="w-full h-full object-contain bg-white" alt="" />
                <div class="text-[12px] text-[#999]" v-else>
                  <img :src="`${ossUrl}/mall/business-license.png`" class="w-[40px] h-[29px]" alt="" />
                  <div>营业执照</div>
                </div>
              </div>
            </template>
            <template #remove>
              <img src="@/assets/imgs/mall/small-close-icon.png" alt="" class="w-[18px] h-[18px] cursor-pointer absolute right-[-7px] top-[-7px] z-1" />
            </template>
          </ImageUploadEl>
        </div>
      </el-form-item>
      <!-- 公司名称 -->
      <el-form-item label="公司名称" prop="companyName">
        <el-input v-model="enterForm.companyName" clearable placeholder="请输入公司名称" />
      </el-form-item>
      <!-- 统一社会信用代码 -->
      <el-form-item label="统一社会信用代码" prop="companyLicenseId">
        <el-input v-model="enterForm.companyLicenseId" clearable placeholder="请输入统一社会信用代码" />
      </el-form-item>
      <!-- 营业执照有效期 -->
      <el-form-item v-if="userType === 5" label="营业执照有效期" prop="companyLicenseDate">
        <div class="flex items-center">
          <el-date-picker
            v-model="enterForm.companyLicenseDate"
            class="date mr-2"
            clearable
            end-placeholder="结束时间"
            format="YYYY/MM/DD"
            range-separator="-"
            start-placeholder="开始时间"
            type="daterange"
            value-format="x"
            @change="handleDateChange"
          />
          <div class="long">
            <el-checkbox v-model="enterForm.companyLicenseDateForever" @change="handleCheckboxChange">长期</el-checkbox>
          </div>
        </div>
      </el-form-item>
      <!-- 经营地址 -->
      <el-form-item label="经营地址" prop="address">
        <el-input readonly placeholder="请选择省/市/区" :value="addressStr" :suffix-icon="ArrowDown" @click="areaShow = true"></el-input>
      </el-form-item>
      <!-- 详细地址 -->
      <el-form-item label="详细地址" prop="companyAddress">
        <el-input v-model="enterForm.companyAddress" clearable placeholder="请输入详细地址"></el-input>
      </el-form-item>
      <!-- 联系人手机号 -->
      <el-form-item label="联系人手机号" prop="contactPhone">
        <el-input v-model.trim="enterForm.contactPhone" type="tel" clearable placeholder="请输入联系人手机号" />
      </el-form-item>
      <!-- 邮箱 -->
      <el-form-item v-if="[4, 5].includes(userType)" label="邮箱" prop="contactEmail">
        <el-input v-model.trim="enterForm.contactEmail" clearable placeholder="请输入邮箱" />
      </el-form-item>
      <!-- 微信 -->
      <el-form-item label="微信" prop="contactWechat">
        <el-input v-model="enterForm.contactWechat" clearable placeholder="请输入微信" />
      </el-form-item>
      <!-- 所属市场 -->
      <el-form-item v-if="userType === 2" label="所属市场" prop="market">
        <!--        <el-select v-model="enterForm.market" clearable filterable placeholder="请选择所属市场">-->
        <!--          <el-option v-for="item in marketLists" :key="item" :label="item" :value="item" />-->
        <!--        </el-select>-->
        <div @click="show = true" class="w-full relative">
          <el-input readonly placeholder="请选择所属市场" :value="marketListsMap[enterForm.marketId] || enterForm.marketId">
            <template #suffix>
              <el-icon v-if="enterForm.marketId"><CircleClose /></el-icon>
              <el-icon v-else><ArrowDown /></el-icon>
            </template>
          </el-input>
          <div class="icon-right" @click.stop="handleClearMarket"></div>
        </div>
      </el-form-item>

      <!-- 法人信息 -->
      <div class="form-title">法人信息</div>
      <div>
        <div class="mb-1">
          <span class="text-[#f56c6c] mr-1">*</span>
          <span class="text-[#666]">法人身份证</span>
          <span class="text-3 mx-1 text-[#D33232]">法人身份证照片（清晰且露出4个角）</span>
        </div>
        <div class="flex">
          <div class="w-[146px]">
            <el-form-item prop="companyPersonIdFrontUrl">
              <div class="w-full">
                <ImageUploadEl class="relative" v-model="enterForm.companyPersonIdFrontUrl" :dir="OSS_DIR.ID_CARD" :hoverRemove="false">
                  <template #default="{ url }">
                    <div class="w-[130px] h-[78px] img-upload-wrap">
                      <img v-if="url" :src="url" class="w-full h-full object-contain bg-white" alt="" />
                      <div class="text-[12px] text-[#999]" v-else>
                        <img :src="`${ossUrl}/mall/id-card-front.png`" class="w-[40px] h-[29px]" alt="" />
                        <div>反面（人像面）</div>
                      </div>
                    </div>
                  </template>
                  <template #remove>
                    <img src="@/assets/imgs/mall/small-close-icon.png" alt="" class="w-[18px] h-[18px] cursor-pointer absolute right-[-7px] top-[-7px] z-1" />
                  </template>
                </ImageUploadEl>
              </div>
            </el-form-item>
          </div>
          <div class="w-[146px]">
            <el-form-item class="tips-item" prop="companyPersonIdBackUrl">
              <div class="w-full">
                <ImageUploadEl class="relative" v-model="enterForm.companyPersonIdBackUrl" :dir="OSS_DIR.ID_CARD" :hoverRemove="false">
                  <template #default="{ url }">
                    <div class="w-[130px] h-[78px] img-upload-wrap">
                      <img v-if="url" :src="url" class="w-full h-full object-contain bg-white" alt="" />
                      <div class="text-[12px] text-[#999]" v-else>
                        <img :src="`${ossUrl}/mall/id-card-back.png`" class="w-[40px] h-[29px]" alt="" />
                        <div>正面（国徽面）</div>
                      </div>
                    </div>
                  </template>
                  <template #remove>
                    <img src="@/assets/imgs/mall/small-close-icon.png" alt="" class="w-[18px] h-[18px] cursor-pointer absolute right-[-7px] top-[-7px] z-1" />
                  </template>
                </ImageUploadEl>
              </div>
            </el-form-item>
          </div>
        </div>
      </div>
      <el-form-item label="法人姓名" prop="companyPersonName">
        <el-input v-model="enterForm.companyPersonName" clearable placeholder="请输入法人姓名" @blur="handleNameBlur" />
      </el-form-item>
      <el-form-item label="法人身份证号码" prop="companyPersonId">
        <el-input v-model.trim="enterForm.companyPersonId" clearable maxlength="18" placeholder="请输入法人身份证号码" @blur="handleBlur" />
      </el-form-item>

      <!-- 银行账户信息 -->
      <div class="form-title">银行账户信息</div>
      <!-- 银行卡号 -->
      <el-form-item label="银行卡号" prop="bankAccountNo">
        <el-input v-model.trim="enterForm.bankAccountNo" clearable placeholder="请输入银行卡号" type="tel" />
      </el-form-item>
      <!-- 银行账户名称 -->
      <el-form-item label="银行账户名称" prop="bankAccountName">
        <el-input v-model="enterForm.bankAccountName" clearable placeholder="请输入银行账户名称" />
      </el-form-item>
      <!-- 开户行行号 -->
      <el-form-item label="开户行行号" prop="bankBranchNo">
        <el-input v-model="enterForm.bankBranchNo" clearable placeholder="请输入开户行行号" />
      </el-form-item>
      <!-- 开户行名称 -->
      <el-form-item label="开户行名称" prop="bankBranchName">
        <el-input v-model="enterForm.bankBranchName" clearable placeholder="请输入开户行名称" />
      </el-form-item>
      <div class="submit-wrap fixed bottom-0 left-0 z-10">
        <el-button :loading="submitLoading" class="login-btn" type="primary" @click="submitForm">提交</el-button>
      </div>
      <div class="submit-wrap-placeholder"></div>
    </el-form>
    <!-- 省市区弹框 -->
    <van-popup v-model:show="areaShow" destroy-on-close round position="bottom">
      <van-picker
        :model-value="pickerValue"
        :columns="cityListView"
        :columns-field-names="{ text: 'label', value: 'value', children: 'children' }"
        @cancel="areaShow = false"
        @confirm="onConfirm"
      >
        <template #columns-top>
          <div class="px-16px bg-white">
            <el-input v-model="searchCityVal" clearable class="search-input" placeholder="搜索">
              <template #prefix>
                <el-icon class="el-input__icon"><search /></el-icon>
              </template>
            </el-input>
          </div>
        </template>
      </van-picker>
    </van-popup>
    <van-action-sheet v-model:show="show" title="选择市场">
      <template #description>
        <el-input v-model="searchVal" clearable class="search-input" placeholder="搜索">
          <template #prefix>
            <el-icon class="el-input__icon"><search /></el-icon>
          </template>
        </el-input>
      </template>
      <div class="h-[60vh] flex flex-col overflow-hidden">
        <div class="px-[10px] overflow-auto pb-safe">
          <div
            v-for="(item, i) in actions"
            :key="i"
            @click="handleSelect(item)"
            class="h-[40px] flex items-center justify-center border-b-[1px] border-b-[#eee] border-b-solid last:border-none"
            :class="item.id === enterForm.marketId ? 'text-[#D33232] font-600' : ''"
          >
            {{ item.name }}
          </div>
        </div>
        <el-empty v-if="actions.length === 0"></el-empty>
      </div>
    </van-action-sheet>
  </div>
</template>

<script setup>
import { ArrowDown, CircleClose, Search } from '@element-plus/icons-vue'
import { computed, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import ImageUploadEl from '@/pc/components/img-upload-el/img-upload-el.vue'
import { ossUrl } from '@/constants/common'
import { MERCHANTS_TYPE } from '@/constants/merchants'
import { OSS_DIR } from '@/constants/oss-dir'
import { useRegionCodeStore } from '@/pc/stores'
import { marketList } from '@/apis/market'
import { getUserAuditInfo, userInsert } from '@/apis/merchants'
import { useStorageLocale } from '@/i18n'
import { validateIDNumber } from '@/common/js/validator.js'
import { simpleDeepClone, trimParamsChangeOrigin } from '@/utils/utils'

const { storageLocale } = useStorageLocale()

/**
 * userType: 商家类型
 * 1 = 采购商
 * 2 = 供应商
 * 3 = 外综服服务商
 * 4 = 供应商服务商
 * 5 = 采购商服务商
 */
const userType = ref(null)
const route = useRoute()
const router = useRouter()
userType.value = Number(route.query?.userType || MERCHANTS_TYPE.SELLER.id)

const enterFormRules = computed(() => {
  return {
    companyLicenseUrl: [{ required: true, message: '请上传营业执照', trigger: 'change' }],
    companyName: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
    companyLicenseId: [{ required: true, message: '请输入统一社会信用代码', trigger: 'blur' }],
    companyLicenseDate: [
      {
        required: true,
        message: '请选择营业执照有效期',
        trigger: 'change',
      },
    ],
    companyAddress: [{ required: true, message: '请输入经营地址', trigger: 'blur' }],
    address: [{ required: true, message: '请选择省市区', trigger: 'blur' }],
    contactPhone: [
      { required: true, message: '请输入联系人手机号', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' },
    ],
    contactEmail: [
      { required: false, message: '请输入邮箱', trigger: 'blur' },
      {
        type: 'email',
        pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
        message: '邮箱格式不正确',
        trigger: 'blur',
      },
    ],
    companyPersonIdFrontUrl: [{ required: true, message: '请上传法人身份证正面', trigger: 'change' }],
    companyPersonIdBackUrl: [{ required: true, message: '请上传法人身份证反面', trigger: 'change' }],
    companyPersonName: [{ required: true, message: '请输入法人姓名', trigger: 'blur' }],
    companyPersonId: [
      { required: true, message: '请输入法人身份证号码', trigger: 'blur' },
      {
        validator: (rule, value, callback) => {
          if (value && !validateIDNumber(value)) {
            callback(new Error('身份证号码格式不正确'))
          } else {
            callback()
          }
        },
        trigger: 'blur',
      },
    ],
  }
})

// 城市
const regionCodeStore = useRegionCodeStore()
const filterArrForVal = (val = '', arr = []) => {
  return arr.reduce((prev, cur) => {
    const label = cur?.label
    const children = cur?.children

    if (label && label.includes(val)) {
      prev.push(cur)
    } else if (children) {
      const filteredChildren = filterArrForVal(val, children)
      if (filteredChildren.length > 0) {
        cur.children = filteredChildren
        prev.push(cur)
      }
    }

    return prev
  }, [])
}
// 城市
const searchCityVal = ref('')
const cityList = computed(() => regionCodeStore.areaListTopSD || [])
const cityListView = computed(() => {
  const val = searchCityVal.value?.trim()
  if (val) {
    return filterArrForVal(val, simpleDeepClone(cityList.value))
  }
  return cityList.value
})
// 城市
if (!cityList.value.length) {
  regionCodeStore.queryAreaList()
}
const addressStr = computed(() => {
  const list = enterForm.address || []
  let cityArr = cityList.value || []
  return (
    list
      ?.map((item) => {
        const cur = cityArr?.find((_item) => _item.value === item.value)
        cityArr = cur?.children
        if (item.value && !item.label) {
          return cur.label
        }
        return item.label
      })
      ?.join('-') || ''
  )
})

// 入驻表单
const enterFormRef = ref(null)
const enterForm = reactive({})
// 省市区code数组
const pickerValue = ref([])
// 获取用户入驻信息
const getUserInfos = async () => {
  try {
    const filterCode = {
      id: null,
      auditStatus: null,
      auditMessage: null,
      auditDateTime: null,
      auditUserId: null,
      auditUserName: null,
      createId: null,
      createName: null,
      createTime: null,
      updateId: null,
      updateName: null,
      updateTime: null,
      deleted: null,
      companyLicenseUrlOcrStatus: null,
    }
    const {
      provinceCode,
      cityCode,
      areaCode,
      provinceName,
      cityName,
      areaName,
      companyLicenseDateForever,
      companyLicenseStartDate,
      companyLicenseEndDate,
      ...rest
    } = await getUserAuditInfo()
    pickerValue.value = [provinceCode, cityCode, areaCode]
    // 过滤掉 filterCode 中的字段
    Object.keys(filterCode).forEach((key) => {
      delete rest[key]
    })
    Object.assign(enterForm, {
      ...rest,
      address: provinceCode
        ? [
            {
              value: provinceCode,
              label: provinceName,
            },
            {
              value: cityCode,
              label: cityName,
            },
            {
              value: areaCode,
              label: areaName,
            },
          ]
        : null,
      companyLicenseDate: [companyLicenseStartDate, companyLicenseDateForever ? new Date('2099-12-31').getTime() : companyLicenseEndDate],
      companyLicenseDateForever: !!companyLicenseDateForever,
    })
    userType.value = rest?.userType
  } catch (error) {
    console.log(error)
  }
}
// 重新入驻时需要调用接口获取原有资料，进行回显
route.query?.from === 'audit' && getUserInfos()

// 营业执照有效期事件
const handleDateChange = () => {
  enterForm.companyLicenseDateForever = false
}
// 处理长期勾选变化
const handleCheckboxChange = () => {
  if (enterForm.companyLicenseDateForever) {
    enterForm.companyLicenseDate?.length && (enterForm.companyLicenseDate[1] = new Date('2099-12-31').getTime())
  }
}
// 失去焦点方法将带有小写x字母的转成大写X
const handleBlur = () => {
  enterForm.companyPersonId = enterForm.companyPersonId?.replace(/x/g, 'X')
}

// 省市区
const areaShow = ref(false)
const onConfirm = ({ selectedValues, selectedOptions }) => {
  areaShow.value = false
  pickerValue.value = selectedValues
  enterForm.address = selectedOptions
  enterFormRef.value.validateField('address')
}

// 为姓名去空格，防止英文状态无法输入空格
const handleNameBlur = () => {
  enterForm.companyPersonName = enterForm.companyPersonName?.trim()
}
// 所属市场列表
const marketLists = ref([])
const marketListsMap = computed(() => {
  return marketLists.value.reduce((prev, cur) => {
    prev[cur.id] = cur.name
    return prev
  }, {})
})
const getMarketList = async () => {
  try {
    const res = await marketList()
    marketLists.value = Object.values(res).reduce((prev, cur) => {
      prev = prev.concat(cur.marketList)
      return prev
    }, [])
  } catch (error) {
    console.log(error)
  }
}

watchEffect(() => {
  if (!enterForm.marketId && enterForm.market) {
    enterForm.marketId = marketLists.value?.find((item) => item.name === enterForm.market)?.id
  }
})

onMounted(() => {
  userType.value === 2 && getMarketList()
})

// 登录提交
const submitLoading = ref(false)
const submitForm = async () => {
  if (submitLoading.value) return
  trimParamsChangeOrigin(enterForm)
  await enterFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      submitLoading.value = true
      try {
        const {
          address,
          companyPersonIdFrontUrl,
          companyPersonIdBackUrl,
          companyLicenseUrl,
          companyLicenseDate,
          companyLicenseDateForever,
          marketId,
          ...params
        } = {
          ...enterForm,
          userType: userType.value,
        }
        const body = {
          ...params,
          provinceCode: Array.isArray(address) ? address?.[0]?.value : null,
          cityCode: Array.isArray(address) ? address?.[1]?.value : null,
          areaCode: Array.isArray(address) ? address?.[2]?.value : null,
          provinceName: Array.isArray(address) ? address?.[0]?.label : null,
          cityName: Array.isArray(address) ? address?.[1]?.label : null,
          areaName: Array.isArray(address) ? address?.[2]?.label : null,
          companyLicenseUrl: Array.isArray(companyLicenseUrl) ? companyLicenseUrl[0] : companyLicenseUrl,
          companyLicenseDateForever: userType.value === 5 ? (companyLicenseDateForever ? 1 : 0) : null,
          companyLicenseStartDate: companyLicenseDate?.length ? companyLicenseDate[0] : null,
          companyLicenseEndDate: companyLicenseDateForever ? '' : companyLicenseDate?.length ? companyLicenseDate[1] : null,
          companyPersonIdFrontUrl: Array.isArray(companyPersonIdFrontUrl) ? companyPersonIdFrontUrl[0] : companyPersonIdFrontUrl,
          companyPersonIdBackUrl: Array.isArray(companyPersonIdBackUrl) ? companyPersonIdBackUrl[0] : companyPersonIdBackUrl,
          marketId,
          market: marketListsMap.value[marketId] || null,
        }
        const res = await userInsert(body)
        if (res?.code === '200') {
          router.push(`/m/enter-audit?userType=${userType.value}`)
          setTimeout(() => {
            submitLoading.value = false
          }, 2000)
          return
        }
        submitLoading.value = false
      } catch (error) {
        console.log(error)
        submitLoading.value = false
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

// 所属市场
const show = ref(false)
const searchVal = ref('')
const actions = computed(() => {
  return marketLists.value.filter((item) => {
    if (searchVal.value) {
      return item.name.includes(searchVal.value)
    }
    return item
  })
})
const handleSelect = (item) => {
  enterForm.marketId = item.id
  show.value = false
}

const handleClearMarket = () => {
  enterForm.marketId = null
  enterForm.market = null
}

const goBack = () => {
  router.back()
}

onMounted(() => {
  // 解决不翻译问题
  setTimeout(() => {
    window?.translate?.execute()
  }, 500)
})
</script>

<style lang="scss" scoped>
.form-title {
  line-height: 30px;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #000;
}

:deep() {
  .el-form-item--label-top .el-form-item__label {
    margin-bottom: 4px;
    color: #666;
  }

  .el-form-item--large {
    margin-bottom: 8px;

    &.is-error {
      margin-bottom: 20px;

      .img-upload-wrap {
        border-color: #f56c6c;
      }
    }
  }

  .img-upload-wrap {
    width: 130px;
    height: 78px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    border: 1px dashed #e7e1e0;
    line-height: 1.5;
  }
}

.submit-wrap,
.submit-wrap-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: calc(100% - 40px);
  height: 64px;
  padding: 0 20px;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  box-sizing: content-box;
  background: #fff;

  .login-btn {
    width: 100%;
    font-size: 16px;
  }
}

.submit-wrap {
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.1);
}

.search-input {
  border-radius: 20px;
  border: 1px solid #eee;
  background: #f6f6f6;
  overflow: hidden;

  :deep() {
    .el-input__wrapper {
      box-shadow: none !important;
      background: #f6f6f6;
    }
  }
}

.business-license {
  :deep(.el-form-item__label) {
    white-space: pre-wrap;
  }
}

.pb-safe {
  padding-bottom: calc(12px + constant(safe-area-inset-bottom));
  padding-bottom: calc(12px + env(safe-area-inset-bottom));
}

.icon-right {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 30px;
  background: transparent;
  z-index: 1;
}
</style>
