<template>
  <div v-loading="loading">
    <div v-if="loading" class="h-[100vh]"></div>
    <template v-else>
      <div class="fixed w-full h-[44px] flex justify-between items-center bg-white text-[18px] font-600 z-10 px-4">
        <div class="w-4 flex items-center justify-center" @click="goBack">
          <span><icon v-if="storageLocale !== 'ar'" class="mr-1" type="icon-fh" :size="16" /><icon v-else class="mr-1" type="icon-xiala1" :size="16" /></span>
        </div>
        <div class="w-full text-center">入驻审核</div>
        <div class="w-4"></div>
      </div>
      <div class="h-[44px]"></div>
      <div class="text-center py-[62px] leading-[1.5]">
        <div v-if="auditStatus === 1">
          <img-loader src="/mall/audit-success-status.png" alt="" class="w-[120px] h-[91px] object-contain"></img-loader>
          <div class="text-[16px] font-600">您已成功入驻中国大集</div>
          <div class="text-[#999] mt-[10px]">
            请在电脑端登录供应商后台售卖商品。 <br />供应商后台：
            <div class="text-[#0256FF]">{{ sellerUrl }}</div>
          </div>
          <el-button class="submit-btn copy-btn" v-copy="{ value: sellerUrl, onSuccess, onError }">复制链接</el-button>
        </div>
        <div v-else-if="auditStatus === 2">
          <img-loader src="/mall/audit-error-status.png" alt="" class="w-[120px] h-[91px] object-contain"></img-loader>
          <div class="text-[16px] font-600">商家入驻审核不通过</div>
          <div class="text-[#FE9914] mt-[10px]">原因：{{ remark }}</div>
          <el-button type="primary" class="submit-btn" @click="handleToRegister">重新入驻</el-button>
        </div>
        <div v-else>
          <img-loader src="/mall/audit-wait-status.png" alt="" class="w-[120px] h-[91px] object-contain"></img-loader>
          <div class="text-[16px] font-600">入驻审核中，请等待</div>
          <el-button type="primary" class="submit-btn" @click="handleRefresh">刷新查看结果</el-button>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { MERCHANTS_TYPE } from '@/constants/merchants'
import { useUserStore } from '@/pc/stores'
import useShowToast from '@/pc/hooks/useShowToast'
import { getUserAuditInfo, getUserBaseInfo } from '@/apis/merchants'
import { useStorageLocale } from '@/i18n'

const { showMessage } = useShowToast()
const { storageLocale } = useStorageLocale()

const auditStatus = ref(0)
const remark = ref('')
const router = useRouter()

const sellerUrl = `${window.location.origin}/seller-center/welcome`

let userTypeVal = null
const handleToRegister = async () => {
  if (!userTypeVal) await getUserType()
  router.push({
    path: `/m/enter`,
    query: {
      userType: userTypeVal,
      from: 'audit',
    },
  })
}

const handleRefresh = async () => {
  await getAuditStatus()
  showMessage('刷新成功')
}

const onSuccess = () => {
  showMessage('复制成功')
}
const onError = () => {
  showMessage('复制失败')
}

const loading = ref(false)
const userInfo = useUserStore()
const getUserType = async () => {
  try {
    const data = await getUserBaseInfo()
    userInfo.setUserInfo(data)
    userTypeVal = data.userType
    return data.userType
  } catch (e) {
    console.log(e)
  }
  return null
}
const getAuditStatus = async () => {
  try {
    loading.value = true
    const data = await getUserAuditInfo()
    // 如果没有入驻
    if (!data || typeof data === 'boolean') {
      const userType = await getUserType()
      if (!userType) return
      // if (userType !== +route.query.userType && route.query.userType) {
      //   router.replace(`/register-h5/${route.query.userType}`)
      //   return
      // }

      // 如果是买家的登录
      if (userType !== MERCHANTS_TYPE.SELLER.id) {
        router.replace(`/register-h5`)
        return
      }

      router.replace({
        path: '/m/enter',
        query: {
          userType,
        },
      })
      return
    }

    // 提交过了
    const { auditStatus: status, auditMessage } = data

    switch (status) {
      // 审核通过
      case 1:
        auditStatus.value = status
        break
      // 审核中
      case 0:
        auditStatus.value = status
        break
      // 审核不通过
      case 2:
        remark.value = auditMessage || ''
        auditStatus.value = 2
        break
      default:
        break
    }
  } catch (e) {
    console.log(e)
  } finally {
    setTimeout(() => {
      loading.value = false
    }, 500)
  }
}
getAuditStatus()

const goBack = () => {
  router.back()
}
</script>

<style scoped lang="scss">
.submit-btn {
  border-radius: 40px;
  background: linear-gradient(270deg, #d8131a 0%, #ff7345 100%);
  font-size: 17px;
  border: none;
  margin-top: 38px;
  width: 335px;
  height: 50px;
  color: #fff;

  &.copy-btn {
    color: $primary-color;
    background: #fff;
    border: 1px solid $primary-color;
  }
}
</style>
