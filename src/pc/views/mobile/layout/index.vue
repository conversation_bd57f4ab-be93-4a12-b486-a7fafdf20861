<template>
  <router-view />
</template>

<script setup>
import { detectDeviceType } from '@/utils/utils'

const isH5 = ref(null)
const isH5Fn = () => {
  const deviceType = detectDeviceType()
  return deviceType !== 'PC'
}

onBeforeMount(() => {
  isH5.value = isH5Fn()
  if (isH5.value) {
    document.body.style.minWidth = '100vw'
    document.body.style.overflowX = 'hidden'
  }
})

onBeforeUnmount(() => {
  if (isH5.value) {
    document.body.style.minWidth = ''
    document.body.style.overflowX = ''
  }
})
</script>

<style scoped lang=""></style>
