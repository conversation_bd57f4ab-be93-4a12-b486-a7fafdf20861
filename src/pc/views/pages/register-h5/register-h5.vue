<template>
  <div class="h5-wrap" v-if="isH5">
    <div class="form-wrap">
      <LoginForm
        ref="loginFormRef"
        :isNormal="!isSeller"
        isH5
        isForm
        showRegister
        :userType="userType"
        :referralCode="referralCode"
        @loginSuccess="loginSuccess"
      >
        <template #headerLogo="{ isRegister, isResetPassword }">
          <div class="fixed w-full h-[44px] flex justify-between items-center bg-white text-[18px] font-600 z-10 px-4">
            <div class="w-4 flex items-center justify-center" @click="goBack" v-if="isSeller">
              <span v-if="isResetPassword || isRegister"
                ><icon v-if="storageLocale !== 'ar'" class="mr-1" type="icon-fh" :size="16" /><icon v-else class="mr-1" type="icon-xiala1" :size="16"
              /></span>
            </div>
            <div class="w-full text-center" v-if="isResetPassword">找回密码</div>
            <div class="w-full text-center" v-else>
              {{ isSeller && MERCHANTS_TYPE_MAP[userType]?.zh ? MERCHANTS_TYPE_MAP[userType].zh : '' }}{{ isRegister ? t('register') : '登录' }}
            </div>
            <div class="w-4" @click="show = true">
              <icon :type="actionsMap[langVal]?.iconName || actions[0].iconName" :size="18"></icon>
            </div>
          </div>
          <div class="h-[44px]"></div>
          <div class="logo-wrapper mt-4 pb-6">
            <img class="logo" :src="`${ossUrl}/logo/logo-login.png`" draggable="false" alt="" />
          </div>
        </template>
      </LoginForm>
      <!--      <Register-->
      <!--        :I18nConfig="I18nConfig"-->
      <!--        :rulesFn="rulesFn"-->
      <!--        isH5-->
      <!--        :isNormal="!isSeller"-->
      <!--        :inviteCode="referralCode"-->
      <!--        @loginSuccess="loginSuccess"-->
      <!--        :userType="isSeller ? MERCHANTS_TYPE.SELLER.id : MERCHANTS_TYPE.BUYER.id"-->
      <!--      />-->
    </div>

    <van-action-sheet v-model:show="show" title="">
      <div class="flex flex-col overflow-hidden pb-safe">
        <div class="px-[10px] overflow-auto">
          <div
            v-for="(item, i) in actions"
            :key="i"
            @click="handleSelect(item)"
            class="h-[44px] flex items-center justify-center border-b-[1px] border-b-[#eee] border-b-solid text-[16px]"
            :class="item.key === langVal ? 'text-[#D33232] font-600' : 'text-[#333]'"
          >
            <div v-mode="item.name">{{ item.name }}</div>
          </div>
        </div>
        <div class="px-[12px] py-[12px]">
          <el-button class="cancel-btn" @click="show = false">取消</el-button>
        </div>
      </div>
    </van-action-sheet>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import LoginForm from '@/pc/components/login-form/login-form.vue'
import { ossUrl } from '@/constants/common'
import { LANG_TYPE } from '@/constants/mall'
// import Register from '@/pc/components/login-register/login-register.vue'
import { MERCHANTS_TYPE, MERCHANTS_TYPE_MAP } from '@/constants/merchants'
import event from '@/event'
import { OPEN_NEW_LOGIN } from '@/event/modules/site'
import { useStorageLocale } from '@/i18n'
import { detectDeviceType } from '@/utils/utils'

const I18nConfig = {
  messages: {
    zh: {
      backToLogin: '返回登录',
      register: '注册',
      retrievePassword: '找回密码',
      registerImmediately: '立即注册',
      existingAccount: '已有账号？',
      field1: '手机号或邮箱',
      filed1Placeholder: '请输入手机号码或邮箱',
      filed1Validate: '请输入正确的手机号或邮箱',
      fieldEmail: '邮箱',
      fieldEmailPlaceholder: '请输入邮箱',
      fieldEmailValidate: '请输入正确的邮箱',
      field2: '验证码',
      filed2Placeholder: '请输入验证码',
      field3: '密码',
      filed3Placeholder: '请输入密码',
      field4: '密码确认',
      filed4Placeholder: '请再次输入密码',
      filed4Validate: '两次输入的密码不一致',
      field5: '企业名称',
      filed5Placeholder: '请输入企业名称',
      field6: '推荐码',
      filed6Placeholder: '请输入推荐码',
      sendCaptcha: '获取验证码',
      sendCaptchaSuccess: '验证码发送成功',
      countdown: '{countdown}s',
      submit: '立即登录',
      verify: '请滑动验证',
      explain: '未注册的手机号码验证后将自动创建新账号',
      agree: '我已阅读并同意',
      servicAgree: '《注册服务协议》',
      and: '和',
      secretAgree: '《隐私协议》',
      readAgree: '请阅读并同意服务协议',
      servicesLink: 'https://static.chinamarket.cn/static/trade-exhibition/file/注册服务协议-中国大集.pdf',
      privacyLink: 'https://static.chinamarket.cn/static/trade-exhibition/file/隐私政策-中国大集.pdf',
      loginSuccess: '登录成功',
    },
    en: {
      backToLogin: 'Back to login',
      register: 'Register',
      retrievePassword: 'Retrieve password',
      registerImmediately: 'Register immediately',
      existingAccount: 'Existing account？',
      field1: 'Phone or email',
      filed1Placeholder: 'Please input your phone number or email',
      filed1Validate1: 'Please enter a valid phone number or email',
      field2: 'Captcha',
      filed2Placeholder: 'please input the captcha',
      field3: 'Password',
      filed3Placeholder: 'Please input your password',
      field4: 'Password confirmation',
      filed4Placeholder: 'Please enter your password again to confirm',
      filed4Validate: 'The two passwords are different',
      field5: 'Company name',
      filed5Placeholder: 'Please enter company name',
      field6: 'Invite Code',
      filed6Placeholder: 'Please enter Invite Code',
      sendCaptcha: 'Send captcha',
      sendCaptchaSuccess: 'Captcha sent successfully',
      countdown: '{countdown} second left',
      submit: 'Sing in',
      verify: 'Please slide to verify',
      explain: 'Unregistered phone numbers will automatically create a new account after verification.',
      agree: 'I have read and agree',
      servicAgree: '《Services Statement》',
      and: 'and',
      secretAgree: '《Privacy Statement》',
      readAgree: 'Please read and agree to the service agreement',
      servicesLink:
        'https://static.chinamarket.cn/static/trade-exhibition/file/%E3%80%90%E8%8B%B1%E6%96%87%E3%80%91%E6%B3%A8%E5%86%8C%E6%9C%8D%E5%8A%A1%E5%8D%8F%E8%AE%AE.pdf',
      privacyLink: 'https://static.chinamarket.cn/static/trade-exhibition/file/%E3%80%90%E8%8B%B1%E6%96%87%E3%80%91%E9%9A%90%E7%A7%81%E6%94%BF%E7%AD%96.pdf',
      loginSuccess: 'Login successfully',
    },
  },
}
const { t } = useI18n(I18nConfig)
const { storageLocale } = useStorageLocale()
// const rulesFn = (formData) => {
//   const validateConfirmPassword = (rule, value, callback) => {
//     if (value !== formData.password) {
//       callback(new Error(t('filed4Validate')))
//     } else {
//       callback()
//     }
//   }
//   return {
//     mobile: [
//       {
//         required: true,
//         message: storageLocale.value === 'zh' ? t('filed1Placeholder') : t('fieldEmailPlaceholder'),
//         trigger: ['change', 'blur'],
//       },
//       {
//         pattern:
//           storageLocale.value === 'zh'
//             ? /^1[3456789]\d{9}$|^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
//             : /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, // 手机号或邮箱, 邮箱
//         message: storageLocale.value === 'zh' ? t('filed1Validate') : t('fieldEmailValidate'),
//         trigger: 'blur',
//       },
//     ],
//     smsCode: [
//       {
//         required: true,
//         pattern: /^[0-9]{6}$/,
//         message: computed(() => t('filed2Placeholder')),
//         trigger: ['blur'],
//       },
//     ],
//     password: [
//       {
//         required: true,
//         message: computed(() => t('filed3Placeholder')),
//         trigger: ['blur'],
//       },
//     ],
//     confirmPassword: [
//       {
//         required: true,
//         message: computed(() => t('filed4Placeholder')),
//         trigger: ['blur'],
//       },
//       { validator: validateConfirmPassword, trigger: 'blur' },
//     ],
//     isAgree: [
//       {
//         type: 'array',
//         required: true,
//         message: computed(() => t('readAgree')),
//         trigger: 'change',
//       },
//     ],
//   }
// }

const router = useRouter()
const route = useRoute()
const referralCode = computed(() => route.query?.referralCode || null)
const uniqueCode = computed(() => route.query?.uniqueCode || null)
const uniqueType = computed(() => route.query?.uniqueType || null)
const userType = computed(() => {
  const type = +route.query?.userType
  if (type === MERCHANTS_TYPE.MERCHANT_SERVICE.id || type === MERCHANTS_TYPE.SELLER.id) {
    return MERCHANTS_TYPE.SELLER.id
  }
  return 1
})
// 是卖家服务商分享的
const isSeller = computed(() => userType.value === MERCHANTS_TYPE.SELLER.id && userType.value)
const isH5 = ref(false)
const isH5Fn = () => {
  const deviceType = detectDeviceType()
  return deviceType === 'Mobile' || deviceType === 'iPad'
}
const loginSuccess = () => {
  if (isH5.value) {
    if (isSeller.value) {
      router.push(`/m/enter-audit?userType=${userType.value}`)
    } else {
      window.location.href = import.meta.env.VUE_APP_MOBILE_URL
    }
    return
  }
  if (isSeller.value) {
    router.push('/seller-center')
  } else {
    router.push('/mall')
  }
}

const loginFormRef = ref(null)
const goBack = () => {
  loginFormRef.value.goBack()
}

const show = ref(false)
const actions = [
  {
    ...LANG_TYPE.CHINESE,
    iconName: 'icon-yuyanqiehuan1',
  },
  {
    ...LANG_TYPE.ENGLISH,
    iconName: 'icon-yuyanqiehuan1',
  },
]
const actionsMap = computed(() => {
  return actions.reduce((prev, cur) => {
    prev[cur.key] = cur
    return prev
  }, {})
})
const defaultVal = actions.find((item) => item.subKey === storageLocale.value)?.key
const defaultQueryVal = actions.find((item) => item.subKey === route?.query?.lang)?.key
const langVal = ref(defaultQueryVal || defaultVal || LANG_TYPE.CHINESE.key)

const handleSelect = (item) => {
  langVal.value = item.key
  show.value = false
  const { fullPath } = router.resolve({
    ...route,
    query: {
      ...route.query,
      lang: item.key,
    },
  })
  window.location.replace(fullPath)
}

onBeforeMount(() => {
  isH5.value = isH5Fn()
  if (isH5.value) {
    document.body.style.minWidth = '100vw'
    document.body.style.overflowX = 'hidden'
  } else {
    const loginType = route?.query?.loginType || ''
    const params = route?.query || {}
    if (isSeller.value) {
      router.replace({
        path: `/merchants-login/${MERCHANTS_TYPE.SELLER.id}`,
        query: {
          ...params,
          loginType,
          referralCode: referralCode.value,
          uniqueCode: uniqueCode.value,
          uniqueType: uniqueType.value,
        },
      })
      return
    }
    router.replace({
      path: '/mall',
      query: {
        ...params,
        loginType,
        referralCode: referralCode.value,
        uniqueCode: uniqueCode.value,
        uniqueType: uniqueType.value,
      },
    })
    event.emit(OPEN_NEW_LOGIN, {
      ...params,
      referralCode: referralCode.value,
      uniqueCode: uniqueCode.value,
      uniqueType: uniqueType.value,
    })
  }
})

onBeforeUnmount(() => {
  if (isH5.value) {
    document.body.style.minWidth = ''
    document.body.style.overflowX = ''
  }
})
</script>

<style scoped lang="scss">
.h5-wrap {
  max-width: 768px;
  min-height: 100vh;
  margin: 0 auto;
  background: #fff;
  padding-bottom: 100px;

  :deep() {
    .login-content-wrapper {
      padding: 0 24px;
    }
  }
}

/** 安全距离 **/
.form-wrap {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.logo-wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
  .logo {
    height: 60px;
    margin-right: 8px;
  }
}

.title {
  text-align: center;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 40px;
}

:deep(.el-checkbox.el-checkbox--large .el-checkbox__label) {
  font-size: 12px;
}

:deep(.link-text) {
  font-size: 12px;
}

.cancel-btn {
  width: 100%;
  height: 40px;
  border-radius: 20px;
  border-color: #eee;
  font-size: 16px;
  color: #666;
  background: #fff;
}

.pb-safe {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

:deep(.el-form-item__error) {
  position: initial;
  margin-bottom: -15px;
}
</style>
