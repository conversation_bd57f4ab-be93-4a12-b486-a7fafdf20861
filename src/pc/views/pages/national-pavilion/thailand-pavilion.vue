<!-- 泰国馆 -->
<style lang="scss" scoped>
.goods-list {
  // overflow: auto;
  width: $main-width;
  margin: 0 auto 24px;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 10px;

  & > div {
    max-width: 244px;
  }

  &.no-data {
    display: flex;
    justify-content: center;
  }
}
</style>

<template>
  <div>
    <Banner :imgList="bannerList" :nationalName="nationalName" :pavilionNameEnum="pavilionNameEnum" flag="/national-store/taiguo_flag.png">
      <template #content0>
        <BannerText :title="`海外数字商城—${nationalName}馆`" />
      </template>
    </Banner>
    <div class="w-1260">
      <NationalDetails>
        <template #default>
          <div class="indent-[2em] text-[16px]">
            泰王国（The Kingdom of
            Thailand），简称“泰国”，旧称为“暹罗”。首都曼谷，位于中南半岛中南部，东南临太平洋泰国湾，西南临印度洋安达曼海。西部及西北部与缅甸交界，东北部与老挝毗邻，东连柬埔寨，南接马来西亚。属热带季风气候，地势北高南低。总面积513000平方千米，海岸线2705千米，全国分为五个地区，共有77个府。全国共有30多个民族，泰族为主要民族，其余为老挝族、华族、马来族等。90%以上的民众信仰佛教，泰语为国语。
          </div>
          <div class="indent-[2em] text-[16px]">
            泰国是新兴工业国家和市场经济体之一，实行自由经济政策，属外向型经济。是东盟成员国和创始国，位于东盟中心位置，社会总体较为稳定，政策透明度和贸易自由化程度较高，营商环境开放包容，是东盟第二大经济体，对周边国家具有较强辐射能力，经济增长前景良好。
          </div>
        </template>
      </NationalDetails>
      <CategoryTable :tableFromData="tableFromData" :tableData="tableData" :nationalName="nationalName" />
      <template v-if="goodsList.length">
        <MarketTitle title="中国出口泰国商品推荐" />
        <div class="goods-list bg-white rounded-4 p-4">
          <GoodsDetailNewCard v-for="item in goodsList" :key="item.id" :goodsInfo="item" />
        </div>
      </template>
      <CooperativePartner entry="THAILAND_NATIONAL_PAVILION" />
      <CardList :nationalName="nationalName" :companyList="companyList" :outsideCompanyList="outsideCompanyList" />
    </div>
  </div>
</template>

<script setup>
import GoodsDetailNewCard from '@/pc/components/goods-detail-card/goods-detail-card-new.vue'
import MarketTitle from '@/pc/views/pages/market/components/market-title.vue'
import BannerText from '@/pc/views/pages/national-pavilion/components/banner-text/index.vue'
import Banner from './components/banner/index.vue'
import CardList from './components/card-list/index.vue'
import CategoryTable from './components/category-table/index.vue'
import CooperativePartner from './components/cooperative-partner/index.vue'
import NationalDetails from './components/national-details/index.vue'
import { ossUrl } from '@/constants/common'
import { PRODUCT_SUGGEST_TYPE } from '@/constants/goods'
import * as API from '@/apis/mall'
import { LOCALES } from '@/i18n'

// 中文
const imgList = ['/outside-trade/taiguo_banner.jpg', '/outside-trade/taiguo_banner_zh2.jpg']
// 英文
const imgListEN = ['/outside-trade/taiguo_banner.jpg', '/outside-trade/taiguo_banner_en2.jpg']
// 阿拉伯语
const imgListArabic = ['/outside-trade/taiguo_banner.jpg', '/outside-trade/taiguo_banner_ar2.jpg']
// 泰国
const imgListThai = ['/outside-trade/taiguo_banner.jpg', '/outside-trade/taiguo_banner_thai2.jpg']
// 印尼
const imgListIndonesian = ['/outside-trade/taiguo_banner.jpg', '/outside-trade/taiguo_banner_indonesian2.jpg']

const i18nEnum = {
  zh: imgList,
  en: imgListEN,
  ar: imgListArabic,
  thai: imgListThai,
  indonesian: imgListIndonesian,
  empty: [],
}
const lang = ref(window.localStorage.getItem('LANG') || 'zh')

const bannerList = computed(() => {
  const imgArr = i18nEnum[lang.value] || imgList
  return imgArr.map((item) => item)
})

const nationalName = '泰国国家'
const pavilionNameEnum = {
  ...LOCALES.usa_pavilion,
  zh: `${nationalName}馆`,
}

// 进口
const tableFromData = [
  {
    categoryLevel1: '电机、电气设备及其零件；录音机及放声机、电视图像、声音的录制和重放设备及其零件',
    categoryLevel2: '集成电路，无线电广播、电视机发送设备，电话机，半导体器件，电动机及发动机等',
    categoryLevel3: '用作处理器及控制器的集成电路，用作存储器的集成电路，二极管，彩色电视接收机零件，起动活塞式发动机',
  },
  {
    categoryLevel1: '橡胶及其制品',
    categoryLevel2: '合成橡胶及从油类提取的油膏，天然橡胶，硫化橡胶线及绳，薪柴，充气橡胶轮胎等',
    categoryLevel3: '硫化橡胶线及绳，天然橡胶乳，技术分类天然橡胶（TSNR），再生橡胶，初级形状或板、片、带，烟胶片，充气橡胶轮胎',
  },
  {
    categoryLevel1: '食用水果及坚果；甜瓜或柑橘属水果的果皮',
    categoryLevel2: '鲜或干的水果及坚果，冷冻水果及坚果，未去壳的水果或坚果',
    categoryLevel3: '鲜榴莲，鲜或干的山竹果，鲜龙眼，未列名冷冻水果及坚果，未去内壳（内果皮）的椰子',
  },
  {
    categoryLevel1: '核反应堆、锅炉、机器、机械器具及零件',
    categoryLevel2: '自动数据处理设备及其部件，空气泵或真空泵，滚动轴承，印刷机器，龙头、旋塞、阀门及类似装置等',
    categoryLevel3: '硬盘驱动器，深沟球滚珠轴承，具有打复印及传真两种及以上功能的机器，音频扩大器，自动数据处理设备的部件，油压传动阀',
  },
  {
    categoryLevel1: '铜及其制品',
    categoryLevel2: '铜废料及碎料，未锻轧的精炼铜及铜合金，铜锍；沉积铜（泥铜），未精炼铜，电解精炼用的铜阳极铜箔',
    categoryLevel3:
      '非绝缘铜丝绞线、缆、编带及布、网、格栅等，铜制垫圈（包括弹簧垫圈），未锻轧铜含量＞99.9935%的精炼铜阴极，铜锍、沉积铜（泥铜），未精炼铜、电解精炼用的铜阳极，印制电路用覆铜板，铜制或钢铁制带铜头的各种钉',
  },
  {
    categoryLevel1: '塑料及其制品',
    categoryLevel2: '初级形状的乙烯聚合物，初级形状的聚缩醛，初级形状的聚碳酸酯、醇酸树脂、聚烯丙基酯，其他非泡沫塑料的板、片、膜、箔及扁条等',
    categoryLevel3:
      '初级形状的线型低密度聚乙烯（比重＜0.94），初级形状的乙烯-α-烯烃共聚物（比重＜0.94），初级形状的聚丙烯，初级形状的聚乙烯（比重≥0.94），初级形状的乙烯-乙酸乙烯酯共聚物',
  },
  {
    categoryLevel1: '木及木制品，木炭',
    categoryLevel2: '木材，薪柴，碎料板，原木，其他木制品等',
    categoryLevel3:
      '温带非针叶木制饰面用单板（厚≤6mm），温带非针叶木制胶合板用单板（厚≤6mm），木制胶合板用单板（厚≤6mm），多层已装拼的木地板，中密度板0.5g/cucm＜密度≤0.8g/cucm（厚＞9mm）',
  },
  {
    categoryLevel1: '糖及糖食',
    categoryLevel2: '不含可可的糖食（包括白巧克力），固体甘蔗糖、甜菜糖及化学纯蔗糖，其他固体糖等',
    categoryLevel3: '口香糖，人造蜜及焦糖，甘蔗糖，葡糖糖，砂糖',
  },
]

// 出口
const tableData = [
  {
    categoryLevel1: '电机、电气设备及其零件；录音机及放声机、电视图像、声音的录制和重放设备及其零件',
    categoryLevel2: '电话机，变压器，绝缘电导体，印刷电路，热水器，蓄电池等',
    categoryLevel3: '二极管、晶体管，局域网或广域网，碳电极、碳刷、灯碳棒，点火磁电机、永磁直流发电机，快速热水器、储存式热水器等',
  },
  {
    categoryLevel1: '核反应堆、锅炉、机器、机械器具及零件',
    categoryLevel2: '空气泵，空气调节器，升降、搬运、装卸机械，自动数据处理设备及其部件等',
    categoryLevel3: '风机，风扇，升降机，自动梯，液体提升机，磁性或光学阅读机',
  },
  {
    categoryLevel1: '塑料及其制品',
    categoryLevel2: '供运输或包装货物用的塑料制品，塑料制的餐具、厨房用具，塑料制的管子，其他塑料板、片、膜、箔、扁条等',
    categoryLevel3: '塑料浴缸、淋浴盘、洗涤槽，塑料板、片、膜、箔、扁条，初级形状的聚硅氧烷等',
  },
  {
    categoryLevel1: '车辆及其零件',
    categoryLevel2: '牵引车、拖拉机，客运机动车辆，，货运机动车辆，特殊用途的机动车辆，装有发动机的机动车辆底盘，机动车的零件等',
    categoryLevel3: '抢修车，起重车，救火车，混凝土搅拌车，婴儿车等',
  },
  {
    categoryLevel1: '家具，寝具，灯具及照明装置，活动房屋',
    categoryLevel2: '坐具及其零件，医疗、外科、牙科或兽用家具，寝具及类似用品，活动房屋',
    categoryLevel3: '椅子、牙科用椅，手术台，检查台，弹簧床，探照灯、活动房屋等',
  },
  {
    categoryLevel1: '钢铁制品',
    categoryLevel2: '钢铁板桩，铁道及电车道铺轨用钢铁材料，无缝钢铁管及空心异型材，钢铁链及其零件，钢铁制弹簧及弹簧片等',
    categoryLevel3: '钢轨、护轨、齿轨、道岔尖轨，焊、铆及用类似方法接合的管，接头、肘管、管套，螺钉、螺栓、螺母，烤肉架等',
  },
  {
    categoryLevel1: '钢铁',
    categoryLevel2: '铁合金，不锈钢丝，办公室用机器，钢铁废料及碎料；供再熔的碎料钢铁锭，生铁、镜铁及钢铁的颗粒和粉末',
    categoryLevel3: '胶版复印机，油印机，地址机写机，不规则盘卷的铁及非合金钢的热轧条、杆，宽度≥600mm的铁或非合金钢平板轧材等',
  },
  {
    categoryLevel1: '特殊交易品及未分类商品',
    categoryLevel2: '未分类商品，低值简易通关商品',
    categoryLevel3: '未分类商品，低值简易通关商品',
  },
]

const companyList = [
  {
    companyName: '山东优典门窗有限公司',
    companyLogo: ossUrl + '/national-store/companylogo1/%E5%B1%B1%E4%B8%9C%E4%BC%98%E5%85%B8%E9%97%A8%E7%AA%97%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '玻璃、五金配件、系统门窗等',
    mobile: '18005390633',
  },
  {
    companyName: '晶硕光电科技（山东）有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo1/%E6%99%B6%E7%A1%95%E5%85%89%E7%94%B5%E7%A7%91%E6%8A%80%EF%BC%88%E5%B1%B1%E4%B8%9C%EF%BC%89%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '动力电池、节能电源等',
    mobile: '13583955878',
  },
  {
    companyName: '山东诺米拓包装制品有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo1/%E5%B1%B1%E4%B8%9C%E8%AF%BA%E7%B1%B3%E6%8B%93%E5%8C%85%E8%A3%85%E5%88%B6%E5%93%81%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '一次性餐饮具等',
    mobile: '17860542337',
  },
  {
    companyName: '临沂浩昌商贸有限公司',
    companyLogo: ossUrl + '/national-store/companylogo1/%E4%B8%B4%E6%B2%82%E6%B5%A9%E6%98%8C%E5%95%86%E8%B4%B8%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '高中低档办公家具等',
    mobile: '19953917085',
  },
  {
    companyName: '悦洋科技',
    companyLogo: ossUrl + '/national-store/companylogo1/%E6%82%A6%E6%B4%8B%E7%A7%91%E6%8A%80%402x.png',
    category: '塑料包装制品等',
    mobile: '18669639301',
  },
  {
    companyName: '山东恒森卫生用品有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo1/%E5%B1%B1%E4%B8%9C%E6%81%92%E6%A3%AE%E5%8D%AB%E7%94%9F%E7%94%A8%E5%93%81%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '个人卫生护理产品等',
    mobile: '15853852818',
  },
  {
    companyName: '山东龙立电子有限公司',
    companyLogo: ossUrl + '/national-store/companylogo1/%E5%B1%B1%E4%B8%9C%E9%BE%99%E7%AB%8B%E7%94%B5%E5%AD%90%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '线束连接器，新能源汽车充电桩等',
    mobile: '18669978879',
  },
  {
    companyName: '山东大自然新材料科技有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo1/%E5%B1%B1%E4%B8%9C%E5%A4%A7%E8%87%AA%E7%84%B6%E6%96%B0%E6%9D%90%E6%96%99%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '水龙头、阀门、管道等',
    mobile: '13181222028',
  },
]

const outsideCompanyList = [
  {
    companyName: '山东八达通汽车超市有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo1/%E5%B1%B1%E4%B8%9C%E5%85%AB%E8%BE%BE%E9%80%9A%E6%B1%BD%E8%BD%A6%E8%B6%85%E5%B8%82%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '物流、报关等',
    mobile: '18669639301',
  },
  {
    companyName: '临沂港有限公司',
    companyLogo: ossUrl + '/national-store/companylogo1/%E4%B8%B4%E6%B2%82%E6%B8%AF%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '货运、货代、报关等',
    mobile: '18815398313',
  },
  {
    companyName: '燧石跨境电商物流有限公司',
    companyLogo: ossUrl + '/national-store/companylogo/%E7%87%A7%E7%9F%B3%E8%B7%A8%E5%A2%83.png',
    category: '物流、报关、国际贸易代理等',
    mobile: '18053900515',
  },
  {
    companyName: '封神国际贸易（临沂）有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo1/%E5%B0%81%E7%A5%9E%E5%9B%BD%E9%99%85%E8%B4%B8%E6%98%93%EF%BC%88%E4%B8%B4%E6%B2%82%EF%BC%89%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '综合服务等',
    mobile: '17861406838',
  },
]

const goodsList = ref([])
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0,
})
const getGoodsList = async () => {
  const res = await API.getNewHomeGoodsList({
    pageSize: pagination.pageSize,
    pageNum: pagination.pageNum,
    labelType: PRODUCT_SUGGEST_TYPE.THAILAND_NATIONAL_PAVILION.id,
  })
  if (res) {
    const { rowList, totalRecord } = res
    goodsList.value = rowList
    pagination.total = totalRecord
  }
}

getGoodsList()
</script>
