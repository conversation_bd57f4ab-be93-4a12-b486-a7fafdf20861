<!-- 印尼馆 -->
<style lang="scss" scoped>
.goods-list {
  // overflow: auto;
  width: $main-width;
  margin: 0 auto 24px;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 10px;

  & > div {
    max-width: 244px;
  }

  &.no-data {
    display: flex;
    justify-content: center;
  }
}

.category-select__inner {
  display: flex;
  align-items: center;
  border: 1px solid #333333;
  border-radius: 8px;
  height: 46px;
  width: 176px;
}

[dir='rtl'] .category-select__inner {
  // direction: ltr;
  padding-right: 10px;
  .search-icon {
    margin-left: 10px;
  }
}

.search-input {
  width: 100%;
  height: 100%;
  background: inherit;
  outline: none;
  border: none;
  box-shadow: none;
  color: #333;
  padding-left: 8px;
}

.select-list {
  width: 176px;
  position: absolute;
  z-index: 9;
  top: 75px;
  right: 0;
  background: #fff;
  border: 4px;
  box-sizing: border-box;
  // min-width: 100%;
  max-height: 300px;
  overflow: auto;
  box-shadow: 0 4px 10px 0 rgba(49, 0, 0, 0.1);

  .select-item {
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 14px;
    color: #333;
    transition: all 0.1s;
    cursor: pointer;

    @include ellipsis;

    &:hover {
      color: $primary-color;
      background-color: #ffeded;
    }
  }
}
</style>

<template>
  <div>
    <Banner :imgList="bannerList" :nationalName="nationalName" :pavilionNameEnum="pavilionNameEnum" flag="/national-store/yinni_flag.png">
      <template #content0>
        <BannerText :title="`海外数字商城—${nationalName}馆`" />
      </template>
    </Banner>
    <div class="w-1260">
      <NationalDetails>
        <template #default>
          <div class="indent-[2em] text-[16px]">
            印度尼西亚共和国（Republic of Indonesia），简称“印尼”，是东南亚国家，首都努山塔拉
            。与巴布亚新几内亚、东帝汶和马来西亚等国家相接。印度尼西亚国土面积1913578.68平方公里
            ，由约17508个岛屿组成，是全世界最大的群岛国家，疆域横跨亚洲及大洋洲，也是多火山多地震的国家。面积较大的岛屿有加里曼丹岛、苏门答腊岛、伊里安岛、苏拉威西岛和爪哇岛，全国共有3个地方特区和31个省，是世界第四人口大国
            。有数百个民族，其中爪哇族占人口45%，巽他族14%，马都拉族和马来族分别占7.5%
            。民族语言共有200多种，官方语言为印尼语。约87%的人口信奉伊斯兰教，是世界上穆斯林人口最多的国家。
          </div>
          <div class="indent-[2em] text-[16px]">
            根据世界银行标准划分，属于中高等收入国家。印尼是东盟最大的经济体，农业、工业、服务业均在国民经济中发挥重要作用。
          </div>
        </template>
      </NationalDetails>
      <!-- 找货 -->
      <Opportunity />
      <CategoryTable v-if="!isNation('idn')" :tableFromData="tableFromData" :tableData="tableData" :nationalName="nationalName" />
      <!-- <IdnContent /> -->
      <!-- <CooperativePartner entry="INDONESIA_ARABIA_NATIONAL_PAVILION" title="寻找印度尼西亚国家馆独立站合作伙伴 " /> -->
      <CardList :nationalName="nationalName" :companyList="companyList" :outsideCompanyList="outsideCompanyList" />
      <!-- <template v-if="goodsList.length">
        <MarketTitle title="热销商品" />
        <div class="goods-list bg-white rounded-4 p-4">
          <GoodsDetailNewCard v-for="item in goodsList" :key="item.id" :goodsInfo="item" />
        </div>
      </template> -->
      <RecommendGoods :suggest="PRODUCT_SUGGEST_TYPE.INDONESIA_ARABIA_NATIONAL_PAVILION.id" :paramsObj="{ search: inputValue }" ref="commendRef">
        <template #title>
          {{ t('market.hotGoods') }}
        </template>
        <template #content>
          <div class="absolute right-0" v-if="isNation('idn')">
            <div class="category-select__inner">
              <input
                v-model.trim="inputValue"
                type="text"
                class="search-input"
                :placeholder="t('queryCategory')"
                @blur="searchGoods"
                @keyup.enter="$event.target.blur()"
              />
              <el-icon v-if="inputValue" class="search-icon-after" @click="inputValue = ''" @mousedown.prevent="clear">
                <CircleClose></CircleClose>
              </el-icon>
              <el-icon class="search-icon" :size="24"><Search></Search></el-icon>
            </div>
          </div>
        </template>
      </RecommendGoods>
    </div>
  </div>
</template>

<script setup>
// import GoodsDetailNewCard from '@/pc/components/goods-detail-card/goods-detail-card-new.vue'
// import MarketTitle from '@/pc/views/pages/market/components/market-title.vue'
import { CircleClose, Search } from '@element-plus/icons-vue'
import { nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import RecommendGoods from '@/pc/components/recommend-goods/index.vue'
import BannerText from '@/pc/views/pages/national-pavilion/components/banner-text/index.vue'
import Banner from './components/banner/index.vue'
import CardList from './components/card-list/index.vue'
import CategoryTable from './components/category-table/index.vue'
// import IdnContent from './components/idn-content/index.vue'
// import CooperativePartner from './components/cooperative-partner/index.vue'
import NationalDetails from './components/national-details/index.vue'
import Opportunity from './components/opportunity/index.vue'
import { ossUrl } from '@/constants/common'
import { PRODUCT_SUGGEST_TYPE } from '@/constants/goods'
// import * as API from '@/apis/mall'
import { LOCALES } from '@/i18n'

const { isNation } = useNation()
const { t } = useI18n({
  messages: {
    zh: {
      queryCategory: '快速检索',
    },
    en: {
      queryCategory: 'Search',
    },
  },
})
// 中文
const imgList = ['/outside-trade/yinni_banner.jpg']
// 英文
const imgListEN = ['/outside-trade/yinni_banner.jpg']
// 阿拉伯语
const imgListArabic = ['/outside-trade/yinni_banner.jpg']
// 泰国
const imgListThai = ['/outside-trade/yinni_banner.jpg']
// 印尼
const imgListIndonesian = ['/outside-trade/yinni_banner.jpg']

const i18nEnum = {
  zh: imgList,
  en: imgListEN,
  ar: imgListArabic,
  thai: imgListThai,
  indonesian: imgListIndonesian,
  empty: [],
}
const lang = ref(window.localStorage.getItem('LANG') || 'zh')

const bannerList = computed(() => {
  const imgArr = i18nEnum[lang.value] || imgList
  return imgArr.map((item) => item)
})

const nationalName = '印度尼西亚国家'
const pavilionNameEnum = {
  ...LOCALES.usa_pavilion,
  zh: `${nationalName}馆`,
}

// 进口
const tableFromData = [
  {
    categoryLevel1: '矿物燃料、矿物油及其蒸馏产品，沥青物质，矿物蜡',
    categoryLevel2: '褐煤，煤砖、煤球及用煤制成的类似固体燃料，石油气及其他烃类气，石油原油及从沥青矿物提取的原油，石油及从沥青矿物提取的油类等',
    categoryLevel3: '褐煤，液化天然气，炼焦煤，石油原油及从沥青矿物提取的原油，5~7号燃料油，不含有生物柴油等',
  },
  {
    categoryLevel1: '钢铁',
    categoryLevel2: '铁合金，不锈钢平板轧材，不锈钢半制品生铁及镜铁，锭、块，不锈钢丝，其他合金钢丝等',
    categoryLevel3:
      '镍铁，冷轧不锈钢板材（1mm＜厚＜3mm），合金生铁、镜铁锭、块或其他初级形状，冷轧不锈钢板材（0.5mm≤厚≤1mm），冷轧不锈钢板材（600mm≤宽度＜1800mm,3mm≤厚＜4.75mm），镀或涂锌的普通钢铁丝等',
  },
  {
    categoryLevel1: '动、植物或微生物油、脂及其分解产品，精制的食用油脂，动、植物蜡',
    categoryLevel2: '棕榈油及其分离品，人造黄油；动、植物或微生物油、脂及其分离品，椰子油、棕榈仁油或巴巴苏棕榈果油及其分离品，甘油水及甘油碱液等',
    categoryLevel3: '棕榈液油（熔点19℃-24℃），起酥油，氢化酯化或反油酸化的植物油、脂及其分离品，棕榈硬脂（熔点44℃-56℃），粗甘油；甘油水及甘油碱液等',
  },
  {
    categoryLevel1: '木浆及其他纤维状纤维素浆，回收（废碎）纸及纸板',
    categoryLevel2: '化学木浆、溶解级，烧碱木浆或硫酸盐木浆，从回收（废料）纸或纸板提取的纤维浆其他纤维状纤维素浆等',
    categoryLevel3: '从回收（废碎）纸或纸板提取的纤维浆，半漂白或漂白非针叶木烧碱木浆或硫酸盐木浆，化学木浆、溶解级等',
  },
  {
    categoryLevel1: '杂项化学产品',
    categoryLevel2: '工业用单羧脂肪酸，粘合剂，杀虫剂、杀鼠剂、杀菌剂、等，松香和树脂酸及衍生物，松香精及松香油，再熔胶，活性炭等',
    categoryLevel3:
      '表面包覆钴化物的氢氧化镍（掺杂碳），除墨剂、蜡纸改正液及类似品，非耐火的灰泥及混凝土，水泥、灰泥及混凝土用添加剂，高效减水剂，工业用脂肪醇等',
  },
  {
    categoryLevel1: '镍及其制品',
    categoryLevel2: '镍锍、氧化镍烧结物及镍冶炼的其他中间产品，未锻轧镍，镍管及管子附件（例如， 接头、肘管、管套），其他镍制品等',
    categoryLevel3: '镍管子附件，其他未锻轧非合金镍，其他氧化镍烧结物及镍冶炼的其他中间产品，镍湿法冶炼中间产品，镍锍等',
  },
  {
    categoryLevel1: '铜及其制品',
    categoryLevel2: '未锻轧的精炼铜及铜合金，铜丝，铜废料及碎料，铜锍；沉积铜（泥铜）、铜条、杆、型材及异型材等',
    categoryLevel3:
      '铸造、模压、冲压或锻造的工业用铜制品，铜制卫生器具及其零件，餐桌、厨房或其他家用铜制器具及其零件，铜制其他螺钉、螺栓及螺母，铜制垫圈（包括弹簧垫圈），盘卷的铜锌合金（黄铜）管等',
  },
  {
    categoryLevel1: '矿砂、矿渣及矿灰',
    categoryLevel2: '铜矿砂及其精矿，贵金属矿砂及其精矿，铌、钽、钒或锆矿砂及其精矿，钛矿砂及其精矿，锌矿砂及其精矿等',
    categoryLevel3: '铜矿砂及其精矿，锆矿砂及其精矿，钛矿砂及其精矿，锌矿砂及其精矿，其他贵金属矿砂及其精矿等',
  },
]

// 出口
const tableData = [
  {
    categoryLevel1: '核反应堆、锅炉、机器、机械器具及零件',
    categoryLevel2: '煤气发生器，发动机，水轮机、燃气轮机，空气调节器等',
    categoryLevel3: '排液泵，炼焦炉，垃圾焚烧炉，太阳能热水器，喷雾式干燥器，提净塔，精馏塔，热水器零件，船用洗舱机，龙门式起重机，门式装卸桥',
  },
  {
    categoryLevel1: '电机、电气设备及其零件，录音机及放声机、电视图像、声音的录制和重放设备及其零件',
    categoryLevel2: '电动机及发电机，蓄电池，家用电动器具，热水器、电话机、传声器，绝缘材料',
    categoryLevel3: '锂离子蓄电池，移动通信基站，集成电路，多喇叭音箱，电锅、烧烤炉等，路由器，光导纤维，火花塞，音频扩大器等',
  },
  {
    categoryLevel1: '钢铁',
    categoryLevel2: '铁合金、钢铁废料及碎料，铁或非合金钢平板轧材，办公室用钢铁机器等',
    categoryLevel3: '锰铁，硅铁，硅锰铁，铬铁，钼铁，钛铁及硅钛铁，不锈钢制螺纹肘管、弯管及管套，不锈钢制对焊件，电钻，链锯等',
  },
  {
    categoryLevel1: '车辆及其零件',
    categoryLevel2: '牵引车、拖拉机，客运机动车辆，货运机动车辆，特殊用途的机动车辆，机动车的零件，摩托车',
    categoryLevel3:
      '装有柴油发动机,摩托车零件，装有驱动电动机的摩托车及脚踏车，机动混凝土搅拌车，山地自行车，单轴拖拉机，电动牵引车，竞赛型自行车，机动小客车的车身，残疾人车辆零件，发动机罩',
  },
  {
    categoryLevel1: '钢铁制品',
    categoryLevel2: '钢铁板桩，铁道及电车道铺轨用钢铁材料，铸铁管及空心异型材，钢铁管子附件钢铁槽、罐、桶及类似容器',
    categoryLevel3: '不锈钢制无缝套管、导管，钢制钻探石油天然气钻管，钢铁道岔尖轨、辙叉、尖轨拉杆等叉道段体，钢轨，焊接的钢铁角材、型材及异型材，钢铁板桩',
  },
  {
    categoryLevel1: '塑料及其制品',
    categoryLevel2: '初级形状的乙烯聚合物、丙烯或其他烯烃聚合物，初级形状的苯乙烯聚合物，塑料的废料、下脚料及碎料等',
    categoryLevel3: '塑料制小雕塑品及其他装饰品，塑料制家具，塑料制手套，百叶窗，塑料塞子、盖子及类似品等',
  },
  {
    categoryLevel1: '家具，寝具，灯具及照明装置，活动房屋',
    categoryLevel2: '坐具及其零件，医疗、外科、牙科或兽医用家具，寝具及类似用品，灯具及照明装置,',
    categoryLevel3: '皮革或再生皮革面的坐具，竹制的坐具，藤制的坐具，办公室用家具，海绵橡胶或泡沫塑料制褥垫，被子、床罩，探照灯等',
  },
  {
    categoryLevel1: '有机化学品',
    categoryLevel2: '无环烃，环烃，烃的卤化衍生物，天然或合成再制的生物碱及其盐、醚、酯和其他衍生物，天然或合成再制的激素、，包括主要用作激素的改性链多肽等',
    categoryLevel3: '谷氨酸钠，乙酸乙酯，甲苯，谷氨酸，乙烯，前列腺素、血栓烷、白细胞三烯及其衍生物和结构类似物等',
  },
]

const companyList = [
  {
    companyName: '山东宠云行供应链管理有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo/%E4%B8%B4%E6%B2%82%E5%8D%8E%E4%B8%9C%E5%AE%A0%E7%89%A9%E7%94%A8%E5%93%81%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '宠物产品集散中心',
    mobile: '18801080390',
  },
  {
    companyName: '山东博朗国际贸易有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo/%E5%B1%B1%E4%B8%9C%E5%8D%9A%E6%9C%97%E5%9B%BD%E9%99%85%E8%B4%B8%E6%98%93%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '五金机电工具',
    mobile: '15853976370',
  },
  {
    companyName: '山东大自然新材料科技有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo1/%E5%B1%B1%E4%B8%9C%E5%A4%A7%E8%87%AA%E7%84%B6%E6%96%B0%E6%9D%90%E6%96%99%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '水龙头、阀门、管道等',
    mobile: '13181222028',
  },
  {
    companyName: '临沂商城新商业发展股份有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo1/%E4%B8%B4%E6%B2%82%E5%95%86%E5%9F%8E%E6%96%B0%E5%95%86%E4%B8%9A%E5%8F%91%E5%B1%95%E8%82%A1%E4%BB%BD%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '小商品、劳保、五金、板材、机械等',
    mobile: '18866995354',
  },
  {
    companyName: '山东诺米拓包装制品有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo1/%E5%B1%B1%E4%B8%9C%E8%AF%BA%E7%B1%B3%E6%8B%93%E5%8C%85%E8%A3%85%E5%88%B6%E5%93%81%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '一次性餐饮具等',
    mobile: '17860542337',
  },
  {
    companyName: '悦洋科技',
    companyLogo: ossUrl + '/national-store/companylogo1/%E6%82%A6%E6%B4%8B%E7%A7%91%E6%8A%80%402x.png',
    category: '塑料包装制品等',
    mobile: '18669639301',
  },
  {
    companyName: '山东恒森卫生用品有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo1/%E5%B1%B1%E4%B8%9C%E6%81%92%E6%A3%AE%E5%8D%AB%E7%94%9F%E7%94%A8%E5%93%81%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '个人卫生护理产品等',
    mobile: '15853852818',
  },
  {
    companyName: '山东龙立电子有限公司',
    companyLogo: ossUrl + '/national-store/companylogo1/%E5%B1%B1%E4%B8%9C%E9%BE%99%E7%AB%8B%E7%94%B5%E5%AD%90%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '线束连接器，新能源汽车充电桩等',
    mobile: '18669978879',
  },
]

const outsideCompanyList = [
  {
    companyName: '封神国际贸易（临沂）有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo1/%E5%B0%81%E7%A5%9E%E5%9B%BD%E9%99%85%E8%B4%B8%E6%98%93%EF%BC%88%E4%B8%B4%E6%B2%82%EF%BC%89%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '对外贸易综合服务等',
    mobile: '17861406838',
  },
  {
    companyName: '临沂内陆港报关代理有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo1/%E4%B8%B4%E6%B2%82%E5%86%85%E9%99%86%E6%B8%AF%E6%8A%A5%E5%85%B3%E4%BB%A3%E7%90%86%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '报关、物流服务',
    mobile: '15266697711',
  },
  {
    companyName: '燧石跨境电商物流有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo1/%E7%87%A7%E7%9F%B3%E8%B7%A8%E5%A2%83%E7%94%B5%E5%95%86%E7%89%A9%E6%B5%81%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '物流、报关、国际贸易代理等',
    mobile: '18053900515',
  },
  {
    companyName: '山东瑞飞仕国际供应链管理有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo1/%E5%B1%B1%E4%B8%9C%E7%91%9E%E9%A3%9E%E4%BB%95%E5%9B%BD%E9%99%85%E4%BE%9B%E5%BA%94%E9%93%BE%E7%AE%A1%E7%90%86%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '物流、国际贸易代理等',
    mobile: '19553913069',
  },
]

/* 搜索商品 */
// const goodsList = ref([])
// const pagination = reactive({
//   pageNum: 1,
//   pageSize: 10,
//   total: 0,
// })
// const getGoodsList = async () => {
//   const res = await API.getNewHomeGoodsList({
//     pageSize: pagination.pageSize,
//     pageNum: pagination.pageNum,
//     labelType: PRODUCT_SUGGEST_TYPE.INDONESIA_ARABIA_NATIONAL_PAVILION.id,
//   })
//   if (res) {
//     const { rowList, totalRecord } = res
//     goodsList.value = rowList
//     pagination.total = totalRecord
//   }
// }

// getGoodsList()
const inputValue = ref('')
const commendRef = ref(null)

const searchGoods = () => {
  commendRef.value.queryGoodsList()
}

const clear = () => {
  inputValue.value = ''
  nextTick(() => {
    commendRef.value.queryGoodsList()
  })
}
</script>
