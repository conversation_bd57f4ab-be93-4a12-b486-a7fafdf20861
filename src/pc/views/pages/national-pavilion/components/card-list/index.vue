<template>
  <div class="mb-[40px]">
    <div class="bg-[#fff] rounded-[8px] mb-[24px]" :class="{ 'pl-[24px] py-[20px]': nationalName !== '阿联酋国家', 'pt-20px': nationalName === '阿联酋国家' }">
      <div class="list-title" :class="{ 'pl-[24px] best-seller': nationalName === '阿联酋国家' }">
        <div class="relative z-2 text-5">中国出口{{ nationalName }}<span :class="`color-[#333] ${$storageLocale === 'zh' ? '' : 'ml-3'}`">商家推荐</span></div>
      </div>
      <div class="flex flex-wrap w-[100%]">
        <BestSeller v-if="nationalName === '阿联酋国家'" />
        <template v-else>
          <!-- <CardItem v-for="(item, i) in companyList" :key="i" :company-info="item" /> -->
          <Recommend />
        </template>
      </div>
    </div>

    <div class="bg-[#fff] pl-[24px] py-[20px] rounded-[8px]">
      <div class="list-title">
        <div class="relative z-2">外贸服务商</div>
      </div>
      <div class="flex flex-wrap w-[100%]">
        <CardItem v-for="(item, i) in outsideCompanyList" :key="i" :company-info="item" @handleShowCustomer="showCustomer" />
      </div>
    </div>
    <Customer ref="customerRef" />
  </div>
</template>

<script setup>
import Customer from '@/pc/components/customer/customer.vue'
import BestSeller from '../best-seller/index.vue'
import CardItem from '../card-item/index.vue'
import Recommend from '../recommend/index.vue'

defineProps({
  companyList: {
    type: Array,
    default: () => [],
  },
  outsideCompanyList: {
    type: Array,
    default: () => [],
  },
  nationalName: {
    type: String,
    default: '',
  },
})

const customerRef = ref(null)
const showCustomer = () => {
  customerRef.value.init()
}
</script>

<style scoped lang="scss">
.list-title {
  position: relative;
  font-family: CKTKingKong;
  font-size: 24px;
  font-weight: bold;
  line-height: normal;
  color: $primary-color;
  margin-bottom: 16px;

  &::after {
    position: absolute;
    bottom: 0;
    left: 0;
    content: '';
    display: block;
    width: 160px;
    height: 10px;
    background: linear-gradient(90deg, rgba(216, 19, 26, 0.4) 0%, rgba(216, 19, 26, 0) 100%);
  }
  &.best-seller {
    &::after {
      left: 24px;
    }
  }
}

[dir='rtl'] .list-title {
  direction: rtl;
  padding-right: 24px;

  &::after {
    left: auto;
    right: 24px;
    transform: rotateZ(-180deg);
  }
}
</style>
