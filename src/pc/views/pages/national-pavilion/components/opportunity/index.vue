<template>
  <div class="text-[#fff] w-full opportunity-wrap" :style="`background-image: url(${ossUrl}/national-store/pavilion-op-bg.png)`">
    <div class="text-[#fff]" :class="['zh', 'ar'].includes($storageLocale) ? 'text-[40px]' : 'text-[28px]'">
      临沂商城是中国最大的批发市场集群 海量商品任您选购
    </div>
    <el-button type="primary" class="submit-btn" @click="handleOpportunityAdd">立即发布找货需求</el-button>
  </div>
</template>

<script setup>
import { ossUrl } from '@/constants/common'
import { useUserStore } from '@/pc/stores'
import { useEvent } from '@/event'
import { OPEN_NEW_LOGIN } from '@/event/modules/site'

const userStore = useUserStore()
const router = useRouter()
const event = useEvent()

const handleOpportunityAdd = () => {
  if (!userStore.isLogined) {
    onLogin()
  } else if (userStore.userInfo?.userType === 1) {
    router.push({
      name: 'myOpportunityAdd',
    })
  } else {
    event.emit(OPEN_NEW_LOGIN, {})
  }
}

const onLogin = () => {
  event.emit(OPEN_NEW_LOGIN, {})
}
</script>

<style lang="scss" scoped>
.opportunity-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 378px;
  background-size: 100% auto;
  background-position: center center;
  margin-bottom: 20px;
  border-radius: 8px;
  flex-direction: column;
  padding: 0 40px;
  box-sizing: border-box;
}

.submit-btn {
  border-radius: 4px;
  /* 主要样式/渐变按钮 */
  background: linear-gradient(270deg, #d8131a 0%, #fd441f 100%);
  font-size: 18px;
  font-weight: 600;
  padding: 0 24px;
  height: 65px;
  border: none;
  margin-top: 50px;
}
</style>
