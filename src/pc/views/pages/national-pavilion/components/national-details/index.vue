<template>
  <div class="banner-bottom color-[#333] px-[30px] py-[30px] bg-[#fff] rounded-[8px] relative z-1" ref="bannerBottom" :style="style">
    <div class="flex mb-[16px]">
      <img-loader
        src="/national-store/title-prefix.png"
        loading-img="/national-store/title-prefix.png?x-oss-process=image/resize,h_1"
        width="38px"
        height="38px"
      ></img-loader>
      <div v-if="isNation('idn')" class="text-[24px] font-600 pt-[14px] mx-[4px]">国家馆介绍</div>
      <div v-else class="text-[24px] font-600 pt-[14px] mx-[4px]">国家介绍</div>
    </div>
    <div class="color-[#666] banner-bottom-content pb-[10px]">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
const bannerBottom = ref(null)

const { isNation } = useNation()

const style = computed(() => {
  const { clientHeight } = bannerBottom.value || {}
  const marginTop = clientHeight ? clientHeight * 0.8 : 150
  return `margin-top: -${marginTop}px;`
})
</script>

<style scoped lang="scss">
.banner-bottom {
  margin-bottom: 40px;

  &-content {
    line-height: 24px;
  }
}

[dir='rtl'] .img-loader {
  transform: rotateZ(-180deg) translateY(10%);
}
</style>
