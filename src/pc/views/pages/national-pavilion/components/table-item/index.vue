<template>
  <div class="w-[50%] mx-[8px]">
    <div class="flex items-center mb-[8px]">
      <img :src="`${ossUrl}/national-store/title-icon.png`" class="w-[20px]" alt="" />
      <div class="color-[#fff] font-600 text-[20px] mx-[8px] text-ellipsis-1" :style="{ fontSize: $storageLocale === 'ru' ? '14px' : '' }" :title="title">
        {{ title }}
      </div>
    </div>
    <table class="w-[100%] text-left custom-table">
      <tr class="bg-[#FAF4F4]">
        <th v-for="row in thOptions" :key="row.prop" class="px-[12px] py-[10px]" :style="row.style">{{ row.label }}</th>
      </tr>
      <tr
        v-for="(item, i) in tableData"
        :key="item"
        class="bg-[#fff]"
        :class="{ 'h-[72px]': $storageLocale === 'en', 'h-[55px]': $storageLocale !== 'en' && $storageLocale !== 'zh' }"
      >
        <td v-for="row in thOptions" :key="row.prop" class="px-[12px] py-[10px] truncate" :style="row.style">
          <span v-if="row.prop === 'index'">
            <img v-if="i < 3" :src="`${ossUrl}/national-store/rank${i + 1}.png`" class="w-[24px]" alt="" />
            <span v-else>{{ i + 1 }}</span>
          </span>
          <el-tooltip v-else popper-class="tooltip-box" effect="dark" :content="item[row.prop]" :disabled="item[row.prop].length < 14" placement="bottom">
            <div class="truncate text-ellipsis overflow-hidden ... max-w-120px">{{ item[row.prop] }}</div>
          </el-tooltip>
        </td>
      </tr>
    </table>
  </div>
</template>
<script setup>
import { ossUrl } from '@/constants/common'

const thOptions = [
  {
    label: '序号',
    prop: 'index',
    style: 'width: 40px;text-align: center; white-space: nowrap;padding: 5px 10px;',
  },
  {
    label: '品类',
    prop: 'categoryLevel1',
    style: '',
  },
  {
    label: '细分',
    prop: 'categoryLevel2',
  },
  {
    label: '实例',
    prop: 'categoryLevel3',
  },
]

defineProps({
  tableData: {
    type: Array,
    default: () => [],
  },
  title: {
    type: String,
    default: '',
  },
})
</script>

<style>
.tooltip-box {
  max-width: 300px;
}
</style>

<style scoped lang="scss">
.custom-table {
  border-spacing: initial;
  border-color: #edeef1;
  font-size: 12px;
  font-weight: normal;
  color: #333;

  td,
  th {
    box-sizing: border-box;
    border-bottom: 1px solid #edeef1;
  }
}

[dir='rtl'] {
  .custom-table {
    text-align: right;
  }
}
</style>
