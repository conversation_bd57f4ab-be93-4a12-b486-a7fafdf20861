<template>
  <div class="banner relative">
    <div class="fixed top-0 left-0 w-[100%] h-[71px] z-10 banner-top" :class="y > 0 ? 'active' : ''" v-if="!nationalTypeUae && !isNation('idn')">
      <div class="flex items-center justify-between w-1260 h-full">
        <div
          class="flex items-center national-item"
          :class="active === item.name ? 'active' : ''"
          v-for="(item, i) in nationalPavilionConfigs"
          :key="i"
          @click="handleItemClick(item)"
        >
          <img :src="`${ossUrl}${item.meta.flag}`" class="flag-img" width="59px" height="39px" alt="" />
          <div
            class="text-[22px] font-600 national-title mx-[16px]"
            :style="{ fontSize: $storageLocale === 'ru' ? '17px' : '' }"
            v-mode="LOCALES[item.meta.constantsKey]"
          >
            {{ LOCALES[item.meta.constantsKey] }}
          </div>
        </div>
      </div>
    </div>
    <swiper
      v-if="imgList.length"
      :loop="true"
      :autoplay="{
        delay: 5000,
        disableOnInteraction: false,
      }"
      :pagination="true"
      :navigation="true"
      :modules="modules"
      class="w-[100%] h-[100vh] min-h-[640px] bg-[#fff]"
    >
      <swiper-slide v-for="(item, i) in imgList" :key="i">
        <div class="w-[100%] h-[100%] flex justify-center items-center banner-item relative">
          <img-loader :src="item" :loading-img="`${item}?x-oss-process=image/resize,h_320`" img-class="largeImg" class="largeImg" disabledWebp />
          <slot :name="`content${i}`"></slot>
        </div>
      </swiper-slide>
    </swiper>
  </div>
</template>
<script setup>
// Import Swiper Vue.js components
// Import Swiper styles
import { useWindowScroll } from '@vueuse/core'
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'
// import required modules
import { Autoplay, Navigation, Pagination } from 'swiper/modules'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { ossUrl } from '@/constants/common'
import nationalPavilionConfigs from '@/pc/router/modules/national-pavilion'
import { LOCALES } from '@/i18n'

const { y } = useWindowScroll()

const nationalType = inject('$nationalType')
const nationalTypeUae = nationalType === 'uae'
const { isNation } = useNation()

const modules = [Autoplay, Pagination, Navigation]

const route = useRoute()
const router = useRouter()

const active = ref(route.name)

const handleItemClick = (item) => {
  if (item.name === 'uaePavilion') {
    window.open(`${import.meta.env.VUE_APP_WEB_URL_UAE}`, '__blank')
    // return
  }
  if (item.name === 'indonesiaPavilion') {
    window.open(`${import.meta.env.VUE_APP_WEB_URL_IDN}`, '__blank')
    // return
  }
  active.value = item.name
  router.push(item.path)
}

defineProps({
  imgList: {
    type: Array,
    default: () => [],
  },
  flag: {
    type: String,
    default: '',
  },
  nationalName: {
    type: String,
    default: '',
  },
  pavilionNameEnum: {
    type: Object,
    default: null,
  },
})
</script>

<style scoped lang="scss">
.banner-item {
  max-width: 2560px;
  margin: 0 auto;
  .img-loader {
    width: 100%;
    height: 100%;
    object-fit: cover; /* 保持图片的宽高比 */
    object-position: center top; /* 图片居中 */

    :deep(img) {
      width: 100%;
      height: 100%;
      object-fit: cover; /* 保持图片的宽高比 */
      object-position: center top; /* 图片居中 */
    }
  }
}

.banner-top {
  margin-top: 64px;
  background: rgba(255, 255, 255, 0.2);

  .flag-img {
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  }

  &.active {
    background: #fff;

    .national-item {
      color: #333;

      &.active {
        color: $primary-color;
        background: rgba(0, 0, 0, 0.1);
      }
    }
  }
}

:deep() {
  .swiper-horizontal > .swiper-pagination-bullets,
  .swiper-pagination-bullets.swiper-pagination-horizontal {
    top: 80%;
  }
  .swiper-pagination-bullet {
    background: #fff;
    width: 24px;
    height: 6px;
    border-radius: 3px;
    opacity: 1;

    &.swiper-pagination-bullet-active {
      width: 48px;
      background: #d8131a;
    }
  }
}

.national-item {
  width: 100%;
  cursor: pointer;
  color: #fff;
  height: 100%;
  padding: 0 24px;

  &.active {
    color: $primary-color;
    background: rgba(255, 255, 255, 0.5);
  }
}

@media screen and (max-width: 1480px) {
  .national-title {
    font-size: 18px;
  }

  .banner-top {
    height: 60px;
  }
}
</style>
