<template>
  <div class="bg-[#fff] w-[25%] pr-[24px] mb-[24px] overflow-hidden">
    <div class="w-[100%] h-[140px] img-wrap mb-[12px]">
      <img-loader
        :src="companyInfo.companyLogo"
        class="max-w-[100%] max-h-[100%] object-contain"
        img-class="max-w-[100%] max-h-[100%] object-contain"
      ></img-loader>
    </div>
    <div class="mb-[8px] whitespace-nowrap overflow-hidden text-ellipsis text-[14px] color-[#333]" :title="companyInfo.companyName">
      {{ companyInfo.companyName }}
    </div>
    <div class="mb-[8px] whitespace-nowrap overflow-hidden text-ellipsis text-[12px] color-[#666]" :title="companyInfo.category">
      经营品类：{{ companyInfo.category }}
    </div>
    <div class="flex items-center overflow-hidden">
      <div class="whitespace-nowrap overflow-hidden text-ellipsis text-[12px] color-[#999] shrink-0" :title="companyInfo.mobile">
        联系方式: {{ companyInfo.mobile }}
      </div>
      <div class="btn cursor-pointer" @click="handleShowCustomer">联系客服</div>
    </div>
  </div>
</template>
<script setup>
defineProps({
  companyInfo: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['handleShowCustomer'])
const handleShowCustomer = () => {
  emit('handleShowCustomer')
}
</script>

<style scoped lang="scss">
.btn {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  color: $primary-color;
  border: thin solid $primary-color;
  border-radius: 2px;
  padding: 0 2px;
  line-height: 20px;
  font-size: 12px;
  text-align: center;
  margin-left: 8px;
}

[dir='rtl'] .btn {
  direction: rtl;
  margin-right: 8px;
}

.img-wrap {
  .img-loader {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }
}
</style>
