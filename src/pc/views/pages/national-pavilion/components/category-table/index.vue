<template>
  <div class="flex category-list w-[100%] px-[12px] py-[20px] mb-[40px] rounded-[8px]">
    <TableItem :tableData="tableData" :title="`中国出口${nationalName}品类排名`" />
    <TableItem :tableData="tableFromData" :title="`${nationalName}出口中国品类排名`" />
  </div>
</template>

<script setup>
import TableItem from '../table-item/index.vue'

defineProps({
  tableFromData: {
    type: Array,
    default: () => [],
  },
  tableData: {
    type: Array,
    default: () => [],
  },
  nationalName: {
    type: String,
    required: true,
  },
})
</script>

<style scoped lang="scss">
.category-list {
  background: linear-gradient(180deg, #f91912 6%, #ffffff 58%);
}
</style>
