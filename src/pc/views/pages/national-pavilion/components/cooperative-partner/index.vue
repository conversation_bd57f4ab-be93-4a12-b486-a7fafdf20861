<style lang="scss" scoped>
.linear-box {
  background: linear-gradient(270deg, #d9ae7e 0%, #f9ecdb 48%, #e5b36f 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.tips-box {
  position: relative;
  &::before,
  &::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    margin: auto;
    width: 100%;
    width: 285px;
    height: 1px;
    background: linear-gradient(270deg, rgba(217, 174, 126, 0) 0%, #f9ecdb 48%, rgba(229, 179, 111, 0) 100%);
  }
  &::before {
    top: 0;
  }
  &::after {
    bottom: 0px;
  }
}
:deep() {
  .el-textarea__inner,
  .el-input__wrapper {
    box-shadow: none;
    border-radius: 2px 0px 0px 2px;
  }
}

.taiguo_bg {
  background-image: url('https://static.chinamarket.cn/static/trade-exhibition/taiguo_bg.png');
  background-size: cover;
}
.yinni_bg {
  background-image: url('https://static.chinamarket.cn/static/trade-exhibition/yinni_bg.png');
  background-size: cover;
}
</style>

<template>
  <div class="mb-10">
    <div class="w-1260 rounded-2 bg-[#999] px-6 pt-[94px] pb-15 bg-cover" :class="[bgClass]">
      <div class="text-center mb-5 text-8 font-black linear-box">{{ title }}</div>
      <div class="tips-box text-center py-[10px] text-4 linear-box mb-[30px]">{{ tipsText }}</div>
      <div class="flex">
        <div class="bg-[#f9f9fa]/[.8] flex-1 p-5 mr-6">
          <div>
            <div class="text-4 font-semibold text-[#333333] mb-2">留言</div>
            <el-input v-model="formData.leaveMessage" class="mb-5" :rows="6" show-word-limit maxlength="200" type="textarea" placeholder="请输入您的需求" />
          </div>
          <div class="flex mb-5">
            <div class="mr-[10px] flex-1">
              <div class="text-4 font-semibold text-[#333333] mb-2">手机号</div>
              <el-input v-model="formData.tel" size="large" placeholder="请输入手机号码" />
            </div>
            <div class="flex-1">
              <div class="text-4 font-semibold text-[#333333] mb-2">邮箱</div>
              <el-input v-model="formData.email" class="mr-2" style="width: 339px" size="large" placeholder="请输入邮箱" />
            </div>
          </div>
          <div class="bg-[#D8131A] rounded-[2px] text-center text-white text-4 font-semibold leading-12 w-50 m-auto cursor-pointer" @click="onSubmit">提交</div>
        </div>
        <div class="w-[440px] h-362px">
          <img class="w-full" :src="shopTechImg" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { leaveMessage } from '@/apis/common.js'

const props = defineProps({
  title: {
    type: String,
    default: '寻找泰国国家馆独立站合作伙伴 ',
  },
  tipsText: {
    type: String,
    default: '共享中国制造出海时代机遇 ',
  },
  // 留言提交入口
  entry: {
    type: String,
    default: '',
  },
})

const i18nEnum = {
  zh: 'https://static.chinamarket.cn/static/trade-exhibition/shop-pc/shop-tech-ch.png',
  en: 'https://static.chinamarket.cn/static/trade-exhibition/shop-pc/shop-tech-en.png',
  ar: 'https://static.chinamarket.cn/static/trade-exhibition/shop-pc/shop-tech-ar.png?v-1',
  thai: 'https://static.chinamarket.cn/static/trade-exhibition/shop-pc/shop-tech-thai.png',
  indonesian: 'https://static.chinamarket.cn/static/trade-exhibition/shop-pc/shop-tech-indonesian.png?v-1',
}

const lang = ref(window.localStorage.getItem('LANG') || 'zh')

const shopTechImg = computed(() => {
  return i18nEnum[lang.value] || ''
})

const bgClass = computed(() => {
  let className = 'taiguo_bg'
  if (props.entry === 'THAILAND_NATIONAL_PAVILION') {
    className = 'taiguo_bg'
  } else {
    className = 'yinni_bg'
  }
  return className
})

const formData = ref({
  leaveMessage: '',
  tel: '',
  email: '',
})

// 点击提交
const onSubmit = async () => {
  await leaveMessage({ entry: props.entry, ...formData.value })
  ElMessage.success('提交成功')
  formData.value.leaveMessage = ''
  formData.value.tel = ''
  formData.value.email = ''
}
</script>
