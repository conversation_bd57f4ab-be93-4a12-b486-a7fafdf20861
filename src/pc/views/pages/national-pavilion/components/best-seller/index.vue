<template>
  <div class="partners">
    <div class="scroll-wrapper">
      <!-- <div class="partners-title">
        <div class="partners-title-text">{{ t('partners') }}</div>
      </div> -->
      <div class="img-wrapper">
        <div class="mask left_mask"></div>
        <div class="mask right_mask"></div>
        <div class="mb-16px">
          <ImgScroll :content="oneList" style="height: 80px" direction="right">
            <template v-slot="{ item }">
              <div class="one-img">
                <img :src="item" alt="" />
              </div>
            </template>
          </ImgScroll>
        </div>
        <div class="mb-16px">
          <ImgScroll :content="twoList" style="height: 80px" direction="right">
            <template v-slot="{ item }">
              <div class="two-img">
                <img :src="item" alt="" />
              </div>
            </template>
          </ImgScroll>
        </div>
        <div class="mb-24px">
          <ImgScroll :content="threeList" style="height: 80px" direction="right">
            <template v-slot="{ item }">
              <div class="two-img">
                <img :src="item" alt="" />
              </div>
            </template>
          </ImgScroll>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import ImgScroll from './img-scroll.vue'

const oneList = [
  'https://static.chinamarket.cn/static/trade-exhibition/national-store/national-pavilion/bun-ker.jpg',
  'https://static.chinamarket.cn/static/trade-exhibition/national-store/national-pavilion/hui-ning.jpg',
  'https://static.chinamarket.cn/static/trade-exhibition/national-store/national-pavilion/hao-de.jpg',
  'https://static.chinamarket.cn/static/trade-exhibition/national-store/national-pavilion/hao-duo-bao.jpg',
  'https://static.chinamarket.cn/static/trade-exhibition/national-store/national-pavilion/huan-dun.jpg',
]

const twoList = [
  'https://static.chinamarket.cn/static/trade-exhibition/national-store/national-pavilion/hu-rui.jpg',
  'https://static.chinamarket.cn/static/trade-exhibition/national-store/national-pavilion/hui-yi-jia.jpg',
  'https://static.chinamarket.cn/static/trade-exhibition/national-store/national-pavilion/hai-ding.jpg',
  'https://static.chinamarket.cn/static/trade-exhibition/national-store/national-pavilion/mei-rong-jiao.jpg',
  'https://static.chinamarket.cn/static/trade-exhibition/national-store/national-pavilion/mu-feng.jpg',
]

const threeList = [
  'https://static.chinamarket.cn/static/trade-exhibition/national-store/national-pavilion/lan-bang.jpg',
  'https://static.chinamarket.cn/static/trade-exhibition/national-store/national-pavilion/zhi-zhi-hu.jpg',
  'https://static.chinamarket.cn/static/trade-exhibition/national-store/national-pavilion/yong-qiang.jpg',
  'https://static.chinamarket.cn/static/trade-exhibition/national-store/national-pavilion/yi-de-le.jpg',
  'https://static.chinamarket.cn/static/trade-exhibition/national-store/national-pavilion/ou-shi-ke.jpg',
]
</script>

<style lang="scss" scoped>
.partners {
  position: relative;
  text-align: center;
  background: #fff;
  // height: 575px;
  &-title {
    margin: 0 auto;
    width: 500px;
    padding: 80px 0 20px;
    background: #fff;

    &-text {
      text-align: center;
      font-family: PingFang SC;
      font-size: 48px;
      font-weight: 600;
      line-height: normal;
      letter-spacing: 0;
      background: linear-gradient(270deg, #d8131a 0%, #e96f18 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }
  }

  .scroll-wrapper {
    width: $main-width;
    margin: 0 auto;
    overflow: hidden;
  }
}

.img-item {
  width: 200px;
  margin-right: 10px;
}

:deep() {
  .loop-item {
    margin-right: 20px;
  }
}

.one-img {
  width: 233px;
}

.two-img {
  width: 233px;
}
.one-img,
.two-img {
  height: 80px;
  box-sizing: border-box;
  border: 1px solid #eef0f1;
  border-radius: 2px;
  box-shadow: 0px 6px 24px 0px rgba(0, 0, 0, 0.08);
  img {
    width: 100%;
    height: 100%;
  }
}

.mb-25 {
  margin-bottom: 25px;
}

.img-wrapper {
  position: relative;

  .mask {
    position: absolute;
    z-index: 10;
    width: 200px;
    height: 100%;

    &.left_mask {
      left: 0;
      background: linear-gradient(270deg, rgba(249, 249, 250, 0) 0%, #ffffff 100%);
    }
    &.right_mask {
      right: 0;
      background: linear-gradient(270deg, #ffffff 0%, rgba(249, 249, 250, 0) 100%);
    }
  }
}
</style>
