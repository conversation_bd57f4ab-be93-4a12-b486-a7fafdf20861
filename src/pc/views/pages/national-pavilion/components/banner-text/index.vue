<template>
  <div class="banner-inner">
    <div class="large-text mb-2" :class="{ 'whitespace-nowrap': $storageLocale !== 'ru' }">{{ title }}</div>
    <div>{{ subTitle }}</div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    default: '',
  },
  subTitle: {
    type: String,
    default: '携手共建“一带一路”，让贸易变通途，让天涯若比邻',
  },
})
</script>

<style scoped lang="scss">
.banner-inner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  margin-top: -60px;
  color: #fff;
  text-align: center;
  z-index: 1;
  font-size: 24px;

  .large-text {
    font-size: 48px;
  }
}

@media screen and (min-width: 1680px) {
  .banner-inner {
    font-size: 36px;

    .large-text {
      font-size: 68px;
    }
  }
}
</style>
