<template>
  <div ref="scrollBox" class="scroll-box" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave">
    <!-- mask: `linear-gradient(${degList[direction]}, #000 70%, transparent)`, -->
    <div class="scroll-content" ref="scrollContent">
      <template v-if="Array.isArray(content)">
        <!-- :slides-per-view="5"
          :space-between="30"
          :slides-per-group="5"
          :cssMode="true"
          :loop="true"
          :autoplay="{
            delay: 2500,
            disableOnInteraction: false,
          }"
          :loop-fill-group-with-blank="true"
          :navigation="{ prevEl: prevButton, nextEl: nextButton }"
          :modules="[Autoplay, Navigation]" -->
        <swiper
          ref="swiperRef"
          :slides-per-view="5"
          :space-between="10"
          :slides-per-group="5"
          :cssMode="true"
          :loop="true"
          :autoplay="{
            delay: 4000,
            disableOnInteraction: false,
            waitForTransition: false,
            reverseDirection: true,
          }"
          :navigation="{
            prevEl: `#${uniqueId}-prev`,
            nextEl: `#${uniqueId}-next`,
          }"
          :modules="[Autoplay, Navigation]"
          class="swiper"
          @swiper="onSwiper"
        >
          <swiper-slide v-for="(item, index) in content" :key="'arrayFirst' + index" class="loop-item">
            <slot :item="item" :index="index"></slot>
          </swiper-slide>
        </swiper>

        <!-- 左按钮 -->
        <div :id="`${uniqueId}-prev`" class="lt-btn">
          <div class="btn-inner">
            <el-icon><ArrowLeft /></el-icon>
          </div>
        </div>
        <!-- 右按钮 -->
        <div :id="`${uniqueId}-next`" class="rt-btn">
          <div class="btn-inner">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import 'swiper/css'
import 'swiper/css/navigation'
import { Autoplay, Navigation } from 'swiper/modules'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { defineProps, ref } from 'vue'

// 生成唯一ID
const uniqueId = ref(`swiper-${Math.random().toString(36).substr(2, 9)}`)
const swiperRef = ref(null)
const swiperInstance = ref(null)

const onSwiper = (swiper) => {
  swiperInstance.value = swiper
}

const handleMouseEnter = () => {
  if (swiperInstance.value) {
    swiperInstance.value.autoplay.stop()
  }
}

const handleMouseLeave = () => {
  if (swiperInstance.value) {
    swiperInstance.value.autoplay.start()
  }
}

defineProps({
  direction: {
    type: String,
    default: 'right',
    validate: (value) => ['left', 'top', 'right', 'bottom'].findIndex(value) !== -1,
  },

  content: {
    type: [String, Array],
    default: '',
  },

  mask: {
    type: Boolean,
    default: true,
  },
})
</script>

<style lang="scss" scoped>
.scroll-content {
  box-sizing: border-box;
  padding: 2px 0px;
  &:hover .lt-btn,
  &:hover .rt-btn {
    display: block;
    opacity: 1;
  }
}

.scroll-box {
  position: relative;

  .lt-btn,
  .rt-btn {
    display: none;
    opacity: 0;
    top: calc(50% - 22px / 2);
    position: absolute;
    color: #fff;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    background-color: rgba(31, 45, 61, 0.11);
    z-index: 99;
    transition: all 0.5s;
    transition-behavior: allow-discrete;

    .btn-inner {
      width: inherit;
      height: inherit;
      border-radius: inherit;
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }
  }
  .lt-btn {
    left: 0;
  }
  .rt-btn {
    right: 0;
  }
}

.swiper {
  width: 100%;
  height: 100%;
}

.swiper-slide {
  text-align: center;
  font-size: 18px;
  background: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  padding: 10px;
}

.swiper-slide img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style>
