<template>
  <div class="recommend pr-20px">
    <div class="scroll-wrapper">
      <!-- <div class="recommend-title">
        <div class="recommend-title-text">{{ t('recommend') }}</div>
      </div> -->
      <template v-if="isNation('idn')">
        <div class="img-wrapper wrapper-idn">
          <!-- <div class="mask left_mask"></div>
        <div class="mask right_mask"></div> -->
          <div>
            <ImgSwier :content="oneList" direction="right">
              <template v-slot="{ item }">
                <div class="one-img" @click="jumpToStore(item)">
                  <img :src="item.logo" alt="" />
                </div>
              </template>
            </ImgSwier>
          </div>
          <div>
            <ImgSwier :content="twoList" direction="right">
              <template v-slot="{ item }">
                <div class="two-img" @click="jumpToStore(item)">
                  <img :src="item.logo" alt="" />
                </div>
              </template>
            </ImgSwier>
          </div>
          <div>
            <ImgSwier :content="threeList" direction="right">
              <template v-slot="{ item }">
                <div class="two-img" @click="jumpToStore(item)">
                  <img :src="item.logo" alt="" />
                </div>
              </template>
            </ImgSwier>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="img-wrapper">
          <div class="mask left_mask"></div>
          <div class="mask right_mask"></div>
          <div class="mb-6">
            <CarouselComponent :content="oneList" style="height: 140px" direction="right">
              <template v-slot="{ item }">
                <div class="one-img" @click="jumpToStore(item)">
                  <img :src="item.logo" alt="" />
                </div>
              </template>
            </CarouselComponent>
          </div>
          <div class="mb-6">
            <CarouselComponent :content="twoList" style="height: 140px" direction="right">
              <template v-slot="{ item }">
                <div class="two-img" @click="jumpToStore(item)">
                  <img :src="item.logo" alt="" />
                </div>
              </template>
            </CarouselComponent>
          </div>
          <div class="mb-6">
            <CarouselComponent :content="threeList" style="height: 140px" direction="right">
              <template v-slot="{ item }">
                <div class="two-img" @click="jumpToStore(item)">
                  <img :src="item.logo" alt="" />
                </div>
              </template>
            </CarouselComponent>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import ImgScroll from './img-scroll.vue'
import ImgSwier from './img-swiper.vue'
import { ossUrl } from '@/constants/common'

const { isNation } = useNation()

const CarouselComponent = isNation('idn') ? ImgSwier : ImgScroll

const oneList = [
  {
    logo: `${ossUrl}/idn-store/store-1.png`,
    address: 'https://www.chinamarket.cn/mall/shop/1857004354461099150',
  },
  {
    logo: `${ossUrl}/idn-store/store-2.png`,
    address: 'https://www.chinamarket.cn/mall/shop/1857004354461099146',
  },
  {
    logo: `${ossUrl}/idn-store/store-3.png`,
    address: 'https://www.chinamarket.cn/mall/shop/1855896228689149954',
  },
  {
    logo: `${ossUrl}/idn-store/store-4.png`,
    address: 'https://www.chinamarket.cn/mall/shop/1857004354461098308',
  },
  {
    logo: `${ossUrl}/idn-store/store-5.png`,
    address: 'https://www.chinamarket.cn/mall/shop/1856979836090834946',
  },
  {
    logo: `${ossUrl}/idn-store/store-16.png`,
    address: 'https://www.chinamarket.cn/mall/shop/4050064232810000091',
  },
  {
    logo: `${ossUrl}/idn-store/store-17.png`,
    address: 'https://www.chinamarket.cn/mall/shop/4050082022460600052',
  },
  {
    logo: `${ossUrl}/idn-store/store-18.png`,
    address: 'https://www.chinamarket.cn/mall/shop/1856956789610897410',
  },
  {
    logo: `${ossUrl}/idn-store/store-19.png`,
    address: 'https://www.chinamarket.cn/mall/shop/1856597734002327554',
  },
  {
    logo: `${ossUrl}/idn-store/store-20.png`,
    address: 'https://www.chinamarket.cn/mall/shop/1857004354461098567',
  },
  {
    logo: `${ossUrl}/idn-store/store-31.png`,
    address: 'https://www.chinamarket.cn/mall/shop/4050063820468400005',
  },
  {
    logo: `${ossUrl}/idn-store/store-32.png`,
    address: 'https://www.chinamarket.cn/mall/shop/4050083828246800090',
  },
  {
    logo: `${ossUrl}/idn-store/store-33.png`,
    address: 'https://www.chinamarket.cn/mall/shop/4050064022825200018',
  },
]

const twoList = [
  {
    logo: `${ossUrl}/idn-store/store-6.png`,
    address: 'https://www.chinamarket.cn/mall/shop/4050064220004000026',
  },
  {
    logo: `${ossUrl}/idn-store/store-7.png`,
    address: 'https://www.chinamarket.cn/mall/shop/4050082828527400059',
  },
  {
    logo: `${ossUrl}/idn-store/store-8.png`,
    address: 'https://www.chinamarket.cn/mall/shop/1857004354461098708',
  },
  {
    logo: `${ossUrl}/idn-store/store-9.png`,
    address: 'https://www.chinamarket.cn/mall/shop/4050061022989400042',
  },
  {
    logo: `${ossUrl}/idn-store/store-10.png`,
    address: 'https://www.chinamarket.cn/mall/shop/4050063834170600071',
  },
  {
    logo: `${ossUrl}/idn-store/store-21.png`,
    address: 'https://www.chinamarket.cn/mall/shop/4050063828325400071',
  },
  {
    logo: `${ossUrl}/idn-store/store-22.png`,
    address: 'https://www.chinamarket.cn/mall/shop/4050061024004800059',
  },
  {
    logo: `${ossUrl}/idn-store/store-23.png`,
    address: 'https://www.chinamarket.cn/mall/shop/1857262017968521217',
  },
  {
    logo: `${ossUrl}/idn-store/store-24.png`,
    address: 'https://www.chinamarket.cn/mall/shop/1857004354461098057',
  },
  {
    logo: `${ossUrl}/idn-store/store-25.png`,
    address: 'https://www.chinamarket.cn/mall/shop/1857004354461098075',
  },
  {
    logo: `${ossUrl}/idn-store/store-34.png`,
    address: 'https://www.chinamarket.cn/mall/shop/4050064024701400033',
  },
  {
    logo: `${ossUrl}/idn-store/store-35.png`,
    address: 'https://www.chinamarket.cn/mall/shop/1856966851758776322',
  },
  {
    logo: `${ossUrl}/idn-store/store-36.png`,
    address: 'https://www.chinamarket.cn/mall/shop/4050062232091400090',
  },
]

const threeList = [
  {
    logo: `${ossUrl}/idn-store/store-11.png`,
    address: 'https://www.chinamarket.cn/mall/shop/1856215667989086210',
  },
  {
    logo: `${ossUrl}/idn-store/store-12.png`,
    address: 'https://www.chinamarket.cn/mall/shop/4050064024701400033',
  },
  {
    logo: `${ossUrl}/idn-store/store-13.png`,
    address: 'https://www.chinamarket.cn/mall/shop/4050084230591200040',
  },
  {
    logo: `${ossUrl}/idn-store/store-14.png`,
    address: 'https://www.chinamarket.cn/mall/shop/4050061625162600041',
  },
  {
    logo: `${ossUrl}/idn-store/store-15.png`,
    address: 'https://www.chinamarket.cn/mall/shop/4050082628681000063',
  },
  {
    logo: `${ossUrl}/idn-store/store-26.png`,
    address: 'https://www.chinamarket.cn/mall/shop/1856893711900860418',
  },
  {
    logo: `${ossUrl}/idn-store/store-27.png`,
    address: 'https://www.chinamarket.cn/mall/shop/1856266553394266113',
  },
  {
    logo: `${ossUrl}/idn-store/store-28.png`,
    address: 'https://www.chinamarket.cn/mall/shop/1857004354461098374',
  },
  {
    logo: `${ossUrl}/idn-store/store-29.png`,
    address: 'https://www.chinamarket.cn/mall/shop/4050063832207800005',
  },
  {
    logo: `${ossUrl}/idn-store/store-30.png`,
    address: 'https://www.chinamarket.cn/mall/shop/4050064218222200021',
  },
  {
    logo: `${ossUrl}/idn-store/store-37.png`,
    address: 'https://www.chinamarket.cn/mall/shop/4050064020509200031',
  },
  {
    logo: `${ossUrl}/idn-store/store-38.png`,
    address: 'https://www.chinamarket.cn/mall/shop/4050064032569400093',
  },
  {
    logo: `${ossUrl}/idn-store/store-39.png`,
    address: 'https://www.chinamarket.cn/mall/shop/4050064018061000056',
  },
  {
    logo: `${ossUrl}/idn-store/store-40.png`,
    address: 'https://www.chinamarket.cn/mall/shop/4050085020183400089',
  },
]

const jumpToStore = (item) => {
  window.open(item.address)
}
</script>

<style lang="scss" scoped>
.recommend {
  position: relative;
  text-align: center;
  background: #fff;
  width: 100%;
  box-sizing: border-box;
  // height: 575px;
  &-title {
    margin: 0 auto;
    width: 500px;
    padding: 80px 0 20px;
    background: #fff;

    &-text {
      text-align: center;
      font-family: PingFang SC;
      font-size: 48px;
      font-weight: 600;
      line-height: normal;
      letter-spacing: 0;
      background: linear-gradient(270deg, #d8131a 0%, #e96f18 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }
  }

  .scroll-wrapper {
    width: 100%;
    margin: 0 auto;
    overflow: hidden;
  }
}

.img-item {
  width: 200px;
  margin-right: 10px;
}

:deep() {
  .loop-item {
    margin-right: 20px;
  }
}

.one-img {
  width: 233px;
}

.two-img {
  width: 233px;
}
.one-img,
.two-img {
  height: 140px;
  box-sizing: border-box;
  border: 1px solid #eef0f1;
  border-radius: 6px;
  padding: 20px;
  box-shadow: 0px 6px 24px 0px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease-in-out;
  cursor: pointer;

  &:hover {
    transform: scale(1.08);
  }
  img {
    width: 100%;
    height: 100%;
  }
}

.wrapper-idn .one-img,
.wrapper-idn .two-img {
  box-shadow: 0px 3px 24px 0px rgba(0, 0, 0, 0.08);
}

.mb-25 {
  margin-bottom: 25px;
}

.img-wrapper {
  position: relative;
  box-sizing: border-box;
  padding: 14px 0 24px 0;
  .mask {
    position: absolute;
    z-index: 10;
    width: 200px;
    height: 100%;

    &.left_mask {
      left: 0;
      background: linear-gradient(270deg, rgba(249, 249, 250, 0) 0%, #ffffff 100%);
    }
    &.right_mask {
      right: 0;
      background: linear-gradient(270deg, #ffffff 0%, rgba(249, 249, 250, 0) 100%);
    }
  }
}
</style>
