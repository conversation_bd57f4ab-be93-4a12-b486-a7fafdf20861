<template>
  <div class="flex justify-between px-[20px] py-[20px] overflow-hidden bg-white rounded-4 mb-5">
    <div class="language-wrap h-[468px]">
      <div class="language bg-[#F5F6F7] mb-[20px] p-[4px] rounded-2">
        <span
          v-for="(item, index) in languageList"
          :key="index"
          :class="{ active: currentIndex === index }"
          class="py-[10px] px-[16px] text-[14px] skip-translate"
          @click="currentIndex = index"
          >{{ item.label }}</span
        >
      </div>
      <video ref="videoRef" class="mb-[40px]" width="720" height="468" controls preload="auto" :poster="formatVideoPosterUrl(videoUrl)" :src="videoUrl"></video>
    </div>

    <div class="h-[468px] overflow-auto text-[#333]">
      <div class="text-[30px] mb-[25px]">馆长寄语</div>
      <div class="leading-[1.46]">
        尊敬的各位来宾：<br />
        大家好！我是迪拜谷雨集团董事长毛安业，同时也是迪拜中国大集阿联酋馆的馆长。今天，我诚挚地欢迎大家来到阿联酋馆，共同见证中阿贸易合作的崭新平台。<br />
        迪拜，作为全球贸易的枢纽，凭借其优越的地理位置、开放的经济政策以及成熟的转口贸易体系，为全球商业合作提供了得天独厚的条件。中国大集阿联酋馆正是在这样的环境下应运而生。我们依托迪拜龙城的战略要地，深度整合资源，致力于打造一个集贸易、展示、文化交流于一体的综合性平台。<br />
        我们的阿联酋馆采用双边贸易供采模式，通过数字化技术赋能传统贸易，为交易双方提供更加便捷、高效的服务。无论是线上独立站的便捷交易，还是线下综合大卖场展厅的实地体验，我们都致力于让采购商直观感受到商品的品质与交易的保障。
        我们的宗旨是：以诚信为本，以服务为先，促进中阿贸易的繁荣发展，推动双边经济合作迈向更高层次。在这里，您将找到优质的产品、可靠的合作伙伴以及全方位的贸易支持。<br />
        最后，我再次欢迎大家来到迪拜中国大集阿联酋馆，让我们携手共进，共创美好未来！<br />
        谢谢大家！
      </div>
    </div>
  </div>
</template>
<script setup>
import { ossUrl } from '@/constants/common'
import { formatVideoPosterUrl } from '@/utils/utils'

const languageList = [
  { label: 'English', lang: 'en', video: `${ossUrl}/national-store/video/pavilion-video-en.mp4` },
  { label: '中文', lang: 'zh', video: `${ossUrl}/national-store/video/pavilion-video-zh.mp4` },
  { label: 'العربية', lang: 'arabic', video: `${ossUrl}/national-store/video/pavilion-video-ar.mp4` },
]

const currentIndex = ref(0)
const videoUrl = computed(() => {
  return languageList[currentIndex.value].video
})

const videoRef = ref(null)

onBeforeUnmount(() => {
  videoRef.value.pause()
})
</script>

<style scoped lang="scss">
.language-wrap {
  width: 720px;
  flex-shrink: 0;
  overflow: hidden;
  margin-right: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .language {
    span {
      display: inline-block;
      cursor: pointer;
      &.active {
        font-weight: 600;
        color: #ffffff;
        border-radius: 4px;
        background: linear-gradient(270deg, #d8131a 0%, #fd441f 100%);
      }
    }
  }
}

[dir='rtl'] {
  .language-wrap {
    margin-left: 32px;
  }
}
</style>
