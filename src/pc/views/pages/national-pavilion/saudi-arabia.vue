<template>
  <div>
    <Banner :imgList="imgList" :nationalName="nationalName" :pavilionNameEnum="pavilionNameEnum" flag="/national-store/sa.png">
      <template #content0>
        <BannerText :title="`海外数字商城—${nationalName}馆`" />
      </template>
    </Banner>

    <div class="w-1260">
      <NationalDetails>
        <template #default>
          <div class="indent-[2em] text-[16px]">
            沙特阿拉伯王国（阿拉伯语：لمملكة العربية السعودية
            ），通称沙特阿拉伯（台译沙乌地阿拉伯），简称沙特。位于阿拉伯半岛。东濒波斯湾，西临红海，同约旦、伊拉克、科威特、阿联酋、阿曼、也门等国接壤，并经法赫德国王大桥与巴林相接。
            国土面积225万平方公里，首都利雅得。人口3218万（截至2023年6月），其中沙特公民约占58.4%。全国分为13个省， 省下设一级县和二级县。
          </div>
          <div class="indent-[2em] text-[16px]">
            沙特是君主制国家，
            实行自由经济政策。沙特阿拉伯是全球最大的石油出口国之一，石油和天然气产业占国内生产总值的大部分。原油探明储量382亿吨，占世界储量的17.3％，居世界第二位。
            出口以原油和石油产品为主，约占出口总额的90％。 是世界上最大的淡化海水生产国，其海水淡化量占世界总量的21%左右。
          </div>
        </template>
      </NationalDetails>
      <CategoryTable :tableFromData="tableFromData" :tableData="tableData" :nationalName="nationalName" />
      <CardList :nationalName="nationalName" :companyList="companyList" :outsideCompanyList="outsideCompanyList" />
      <RecommendGoods :suggest="PRODUCT_SUGGEST_TYPE.NATIONAL_SA.id">
        <template #title>
          {{ t('market.hotGoods') }}
        </template>
      </RecommendGoods>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import RecommendGoods from '@/pc/components/recommend-goods/index.vue'
import BannerText from '@/pc/views/pages/national-pavilion/components/banner-text/index.vue'
import Banner from './components/banner/index.vue'
import CardList from './components/card-list/index.vue'
import CategoryTable from './components/category-table/index.vue'
import NationalDetails from './components/national-details/index.vue'
import { ossUrl } from '@/constants/common'
import { PRODUCT_SUGGEST_TYPE } from '@/constants/goods'
import { LOCALES } from '@/i18n'

const { t } = useI18n({})

const imgList = ['/outside-trade/%E9%98%BF%E6%8B%89%E4%BC%AF.png']

const nationalName = '沙特阿拉伯国家'
const pavilionNameEnum = {
  ...LOCALES.sa_pavilion,
  zh: `${nationalName}馆`,
}

// 进口
const tableFromData = [
  {
    categoryLevel1: '石油及石油制品',
    categoryLevel2: '原油、成品油、液化天然气等',
    categoryLevel3: '原油、汽油、柴油、LNG等',
  },
  {
    categoryLevel1: '塑料及橡胶制品',
    categoryLevel2: '塑料制品、橡胶制品等',
    categoryLevel3: '塑料颗粒、橡胶制品等',
  },
  {
    categoryLevel1: '化工产品',
    categoryLevel2: '各类化工原料和化学品',
    categoryLevel3: '有机化学品、无机化学品、塑料原料等',
  },
  {
    categoryLevel1: '金属及其制品',
    categoryLevel2: '钢铁、铝材、铜材及其制品',
    categoryLevel3: '钢铁板材、铝合金门窗、铜管等',
  },
  {
    categoryLevel1: '宝石和贵金属',
    categoryLevel2: '黄金、钻石、珠宝首饰等',
    categoryLevel3: '黄金饰品、钻石原石、珠宝首饰等',
  },
  {
    categoryLevel1: '食品和农产品',
    categoryLevel2: '各类食品和农产品',
    categoryLevel3: '水果、蔬菜、坚果、海鲜等',
  },
  {
    categoryLevel1: '机械设备',
    categoryLevel2: '工程机械、农业机械、建筑机械等',
    categoryLevel3: '挖掘机、拖拉机、起重机等',
  },
  {
    categoryLevel1: '光学和医疗设备',
    categoryLevel2: '光学仪器、医疗设备等',
    categoryLevel3: '显微镜、X光机、医疗耗材等',
  },
]

// 出口
const tableData = [
  {
    categoryLevel1: '机电产品',
    categoryLevel2: '机械设备、电子设备、通信设备等',
    categoryLevel3: '手机、电脑、家电、工业机械等',
  },
  {
    categoryLevel1: '纺织品和服装',
    categoryLevel2: '各类纺织品、服装、鞋帽等',
    categoryLevel3: '棉布、丝绸、成衣、运动鞋等',
  },
  {
    categoryLevel1: '塑料及橡胶制品',
    categoryLevel2: '塑料制品、橡胶制品等',
    categoryLevel3: '塑料包装材料、橡胶轮胎等',
  },
  {
    categoryLevel1: '金属及其制品',
    categoryLevel2: '钢铁、铝材、铜材及其制品',
    categoryLevel3: '钢铁板材、铝合金门窗、铜管',
  },
  {
    categoryLevel1: '化工产品',
    categoryLevel2: '各类化工原料和化学品',
    categoryLevel3: '有机化学品、无机化学品、塑料原料等',
  },
  {
    categoryLevel1: '建筑材料',
    categoryLevel2: '各类建筑材料和装饰材料',
    categoryLevel3: '瓷砖、石材、玻璃、灯具等',
  },
  {
    categoryLevel1: '汽车及零部件',
    categoryLevel2: '汽车及零部件',
    categoryLevel3: '乘用车、商用车、汽车配件等',
  },
  {
    categoryLevel1: '光学和医疗设备',
    categoryLevel2: '光学仪器、医疗设备等',
    categoryLevel3: '显微镜、X光机、医疗耗材等',
  },
]

const companyList = [
  {
    companyName: '山东宠云行供应链管理有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo/%E4%B8%B4%E6%B2%82%E5%8D%8E%E4%B8%9C%E5%AE%A0%E7%89%A9%E7%94%A8%E5%93%81%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '宠物产品集散中心',
    mobile: '18801080390',
  },
  {
    companyName: '山东博朗国际贸易有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo/%E5%B1%B1%E4%B8%9C%E5%8D%9A%E6%9C%97%E5%9B%BD%E9%99%85%E8%B4%B8%E6%98%93%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '五金机电工具',
    mobile: '15853976370',
  },
  {
    companyName: '山东大自然新材料科技有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo1/%E5%B1%B1%E4%B8%9C%E5%A4%A7%E8%87%AA%E7%84%B6%E6%96%B0%E6%9D%90%E6%96%99%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '水龙头、阀门、管道等',
    mobile: '13181222028',
  },
  {
    companyName: '临沂商城新商业发展股份有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo1/%E4%B8%B4%E6%B2%82%E5%95%86%E5%9F%8E%E6%96%B0%E5%95%86%E4%B8%9A%E5%8F%91%E5%B1%95%E8%82%A1%E4%BB%BD%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '小商品、劳保、五金、板材、机械等',
    mobile: '18866995354',
  },
  {
    companyName: '山东诺米拓包装制品有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo1/%E5%B1%B1%E4%B8%9C%E8%AF%BA%E7%B1%B3%E6%8B%93%E5%8C%85%E8%A3%85%E5%88%B6%E5%93%81%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '一次性餐饮具等',
    mobile: '17860542337',
  },
  {
    companyName: '悦洋科技',
    companyLogo: ossUrl + '/national-store/companylogo1/%E6%82%A6%E6%B4%8B%E7%A7%91%E6%8A%80%402x.png',
    category: '塑料包装制品等',
    mobile: '18669639301',
  },
  {
    companyName: '山东恒森卫生用品有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo1/%E5%B1%B1%E4%B8%9C%E6%81%92%E6%A3%AE%E5%8D%AB%E7%94%9F%E7%94%A8%E5%93%81%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '个人卫生护理产品等',
    mobile: '15853852818',
  },
  {
    companyName: '山东龙立电子有限公司',
    companyLogo: ossUrl + '/national-store/companylogo1/%E5%B1%B1%E4%B8%9C%E9%BE%99%E7%AB%8B%E7%94%B5%E5%AD%90%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '线束连接器，新能源汽车充电桩等',
    mobile: '18669978879',
  },
]

const outsideCompanyList = [
  {
    companyName: '封神国际贸易（临沂）有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo1/%E5%B0%81%E7%A5%9E%E5%9B%BD%E9%99%85%E8%B4%B8%E6%98%93%EF%BC%88%E4%B8%B4%E6%B2%82%EF%BC%89%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '对外贸易综合服务等',
    mobile: '17861406838',
  },
  {
    companyName: '临沂内陆港报关代理有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo1/%E4%B8%B4%E6%B2%82%E5%86%85%E9%99%86%E6%B8%AF%E6%8A%A5%E5%85%B3%E4%BB%A3%E7%90%86%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '报关、物流服务',
    mobile: '15266697711',
  },
  {
    companyName: '燧石跨境电商物流有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo1/%E7%87%A7%E7%9F%B3%E8%B7%A8%E5%A2%83%E7%94%B5%E5%95%86%E7%89%A9%E6%B5%81%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '物流、报关、国际贸易代理等',
    mobile: '18053900515',
  },
  {
    companyName: '山东瑞飞仕国际供应链管理有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo1/%E5%B1%B1%E4%B8%9C%E7%91%9E%E9%A3%9E%E4%BB%95%E5%9B%BD%E9%99%85%E4%BE%9B%E5%BA%94%E9%93%BE%E7%AE%A1%E7%90%86%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '物流、国际贸易代理等',
    mobile: '19553913069',
  },
]
</script>
