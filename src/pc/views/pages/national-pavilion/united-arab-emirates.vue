<template>
  <div>
    <Banner :imgList="imgList" :nationalName="nationalName" :pavilionNameEnum="pavilionNameEnum" flag="/national-store/uae.png">
      <template #content0>
        <BannerText :title="`海外数字商城—${nationalName}馆`" />
      </template>
    </Banner>
    <div class="w-1260">
      <NationalDetails>
        <template #default>
          <div class="indent-[2em] text-[16px]">
            阿拉伯联合酋长国（阿拉伯语：امارات عربية
            متحدة‎），简称阿联酋，首都阿布扎比。位于阿拉伯半岛东部，西和南与沙特阿拉伯交界，东和东北与阿曼毗连，北临波斯湾，与伊朗隔海相望，属热带沙漠气候，主要由平原、山地和洼地组成，石油和天然气资源非常丰富。总面积83600平方千米，海岸线长734千米，由7个酋长国组成。截至2024年7月，阿联酋总人口为1024万，外籍人占88%，居民大多信奉伊斯兰教，多数属逊尼派。阿拉伯语为官方语言，通用英语。
          </div>
          <div class="indent-[2em] text-[16px]">
            阿联酋是石油输出国组织（OPEC）成员国，国家经济以石油生产和石油化工工业为主，此外还大力发展以信息技术为核心的知识经济，同时注重可再生能源研发，首都阿布扎比于2009年6月获选国际可再生能源署总部所在地。
            阿联酋同时还是阿拉伯国家联盟、海湾合作委员会、及大阿拉伯自由贸易区成员国。
          </div>
        </template>
      </NationalDetails>
      <Opportunity />
      <CategoryTable :tableFromData="tableFromData" :tableData="tableData" :nationalName="nationalName" v-if="!nationalTypeUae" />
      <PavilionContent />
      <CardList :nationalName="nationalName" :companyList="companyList" :outsideCompanyList="outsideCompanyList" />
      <RecommendGoods :suggest="PRODUCT_SUGGEST_TYPE.NATIONAL_UAE.id">
        <template #title>
          {{ t('market.hotGoods') }}
        </template>
      </RecommendGoods>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import RecommendGoods from '@/pc/components/recommend-goods/index.vue'
import BannerText from './components/banner-text/index.vue'
import Banner from './components/banner/index.vue'
import CardList from './components/card-list/index.vue'
import CategoryTable from './components/category-table/index.vue'
import NationalDetails from './components/national-details/index.vue'
import Opportunity from './components/opportunity/index.vue'
import PavilionContent from './components/pavilion-content/index.vue'
import { ossUrl } from '@/constants/common'
import { PRODUCT_SUGGEST_TYPE } from '@/constants/goods'
import { LOCALES } from '@/i18n'

const { t } = useI18n({})

const nationalType = inject('$nationalType')
const nationalTypeUae = nationalType === 'uae'

const imgList = ['/outside-trade/%E9%98%BF%E8%81%94%E9%85%8B2.png']

const nationalName = '阿联酋国家'
const pavilionNameEnum = {
  ...LOCALES.usa_pavilion,
  zh: `${nationalName}馆`,
}

// 进口
const tableFromData = [
  {
    categoryLevel1: '石油及石油制品',
    categoryLevel2: '原油、成品油、液化天然气等',
    categoryLevel3: '原油、汽油、柴油、LNG等',
  },
  {
    categoryLevel1: '塑料及橡胶制品',
    categoryLevel2: '塑料制品、橡胶制品等',
    categoryLevel3: '塑料颗粒、橡胶制品等',
  },
  {
    categoryLevel1: '化工产品',
    categoryLevel2: '各类化工原料和化学品',
    categoryLevel3: '有机化学品、无机化学品、塑料原料等',
  },
  {
    categoryLevel1: '金属及其制品',
    categoryLevel2: '钢铁、铝材、铜材及其制品',
    categoryLevel3: '钢铁板材、铝合金门窗、铜管等',
  },
  {
    categoryLevel1: '宝石和贵金属',
    categoryLevel2: '黄金、钻石、珠宝首饰等',
    categoryLevel3: '黄金饰品、钻石原石、珠宝首饰等',
  },
  {
    categoryLevel1: '食品和农产品',
    categoryLevel2: '各类食品和农产品',
    categoryLevel3: '水果、蔬菜、坚果、海鲜等',
  },
  {
    categoryLevel1: '机械设备',
    categoryLevel2: '工程机械、农业机械、建筑机械等',
    categoryLevel3: '挖掘机、拖拉机、起重机等',
  },
  {
    categoryLevel1: '光学和医疗设备',
    categoryLevel2: '光学仪器、医疗设备等',
    categoryLevel3: '显微镜、X光机、医疗耗材等',
  },
]

// 出口
const tableData = [
  {
    categoryLevel1: '机电产品',
    categoryLevel2: '机械设备、电子设备、通信设备等',
    categoryLevel3: '手机、电脑、家电、工业机械等',
  },
  {
    categoryLevel1: '纺织品和服装',
    categoryLevel2: '各类纺织品、服装、鞋帽等',
    categoryLevel3: '棉布、丝绸、成衣、运动鞋等',
  },
  {
    categoryLevel1: '塑料及橡胶制品',
    categoryLevel2: '塑料制品、橡胶制品等',
    categoryLevel3: '塑料包装材料、橡胶轮胎等',
  },
  {
    categoryLevel1: '金属及其制品',
    categoryLevel2: '钢铁、铝材、铜材及其制品',
    categoryLevel3: '钢铁板材、铝合金门窗、铜管',
  },
  {
    categoryLevel1: '化工产品',
    categoryLevel2: '各类化工原料和化学品',
    categoryLevel3: '有机化学品、无机化学品、塑料原料等',
  },
  {
    categoryLevel1: '建筑材料',
    categoryLevel2: '各类建筑材料和装饰材料',
    categoryLevel3: '瓷砖、石材、玻璃、灯具等',
  },
  {
    categoryLevel1: '汽车及零部件',
    categoryLevel2: '汽车及零部件',
    categoryLevel3: '乘用车、商用车、汽车配件等',
  },
  {
    categoryLevel1: '光学和医疗设备',
    categoryLevel2: '光学仪器、医疗设备等',
    categoryLevel3: '显微镜、X光机、医疗耗材等',
  },
]

const companyList = [
  {
    companyName: '山东优典门窗有限公司',
    companyLogo: ossUrl + '/national-store/companylogo1/%E5%B1%B1%E4%B8%9C%E4%BC%98%E5%85%B8%E9%97%A8%E7%AA%97%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '玻璃、五金配件、系统门窗等',
    mobile: '18005390633',
  },
  {
    companyName: '晶硕光电科技（山东）有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo1/%E6%99%B6%E7%A1%95%E5%85%89%E7%94%B5%E7%A7%91%E6%8A%80%EF%BC%88%E5%B1%B1%E4%B8%9C%EF%BC%89%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '动力电池、节能电源等',
    mobile: '13583955878',
  },
  {
    companyName: '山东诺米拓包装制品有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo1/%E5%B1%B1%E4%B8%9C%E8%AF%BA%E7%B1%B3%E6%8B%93%E5%8C%85%E8%A3%85%E5%88%B6%E5%93%81%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '一次性餐饮具等',
    mobile: '17860542337',
  },
  {
    companyName: '临沂浩昌商贸有限公司',
    companyLogo: ossUrl + '/national-store/companylogo1/%E4%B8%B4%E6%B2%82%E6%B5%A9%E6%98%8C%E5%95%86%E8%B4%B8%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '高中低档办公家具等',
    mobile: '19953917085',
  },
  {
    companyName: '悦洋科技',
    companyLogo: ossUrl + '/national-store/companylogo1/%E6%82%A6%E6%B4%8B%E7%A7%91%E6%8A%80%402x.png',
    category: '塑料包装制品等',
    mobile: '18669639301',
  },
  {
    companyName: '山东恒森卫生用品有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo1/%E5%B1%B1%E4%B8%9C%E6%81%92%E6%A3%AE%E5%8D%AB%E7%94%9F%E7%94%A8%E5%93%81%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '个人卫生护理产品等',
    mobile: '15853852818',
  },
  {
    companyName: '山东龙立电子有限公司',
    companyLogo: ossUrl + '/national-store/companylogo1/%E5%B1%B1%E4%B8%9C%E9%BE%99%E7%AB%8B%E7%94%B5%E5%AD%90%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '线束连接器，新能源汽车充电桩等',
    mobile: '18669978879',
  },
  {
    companyName: '山东大自然新材料科技有限公司',
    companyLogo:
      ossUrl +
      '/national-store/companylogo1/%E5%B1%B1%E4%B8%9C%E5%A4%A7%E8%87%AA%E7%84%B6%E6%96%B0%E6%9D%90%E6%96%99%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%402x.png',
    category: '水龙头、阀门、管道等',
    mobile: '13181222028',
  },
]

const outsideCompanyList = [
  {
    companyName: '青岛惠润达国际供应链管理有限公司',
    companyLogo: ossUrl + '/national-store/national-pavilion/青岛惠润达国际供应链管理有限公司.png',
    category: '代理报关 海运、空运代理 多式联运等',
    mobile: '18663951687',
  },
  {
    companyName: '山东欧亚铁路物流有限公司 ',
    companyLogo: ossUrl + '/national-store/national-pavilion/山东欧亚铁路物流有限公司.png',
    category: '俄罗斯、中亚五国铁路运输 国际联运等',
    mobile: '18769915222',
  },
  {
    companyName: '山东华亮国际物流有限公司',
    companyLogo: ossUrl + '/national-store/national-pavilion/山东华亮国际物流有限公司.png',
    category: '国际道路货物运输',
    mobile: '15069909809',
  },
  {
    companyName: '青岛中亮国际供应链有限公司',
    companyLogo: ossUrl + '/national-store/national-pavilion/青岛中亮国际供应链有限公司.png',
    category: '普货、冷鲜、罐式、集装箱运输',
    mobile: '17661697095',
  },
  {
    companyName: '鑫中驰(山东)国际供应链管理有限公司',
    companyLogo: ossUrl + '/national-store/national-pavilion/鑫中驰(山东)国际供应链管理有限公司.png',
    category: '国际货物运输服务、双清到门、仓储服务',
    mobile: '18369336193',
  },
  {
    companyName: '多发货物流科技（山东）有限公司 ',
    companyLogo: ossUrl + '/national-store/national-pavilion/多发货物流科技（山东）有限公司.png',
    category: '经营品类:FBA头程、海外仓、保险、商标财税服务等',
    mobile: '15165556699',
  },
  {
    companyName: '青岛海兴达物流科技有限公司',
    companyLogo: ossUrl + '/national-store/national-pavilion/青岛海兴达物流科技有限公司.png',
    category: '海、空、铁、危化品进出口运输和报关报检',
    mobile: '15898879636',
  },
  {
    companyName: '深圳速猫供应链科技有限公司',
    companyLogo: ossUrl + '/national-store/national-pavilion/深圳速猫供应链科技有限公司.png',
    category: '空海物流清关 中东空、海、快递门到门等',
    mobile: '13148786772',
  },
]
</script>
