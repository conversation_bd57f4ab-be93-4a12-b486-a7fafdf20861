<template>
  <div class="ai-daji">
    <Header ref="headerRef" :isNeedAutoPlay="false" />
    <div class="main-content">
      <div class="center">
        <video
          ref="introduceVideoRef"
          class="center-video"
          width="720"
          height="400"
          controls
          preload="auto"
          :poster="`${ossUrl}/ai-daji/video-bg/quan-taiguo-bg.png?v=4`"
          :src="`${ossUrl}/ai-daji/video/quan-taiguo.mp4?v=4.1`"
          @play="introduceVideoClick"
        ></video>
        <div class="center-right">
          <div class="title">
            <span v-mode="LINYI_CHINA_MARKET_MAP">{{ t('aiDaJi.introduce') }}</span>
          </div>
          <div class="center-right-message scrollbar">{{ t('aiDaJi.message') }}</div>
          <!-- <div class="center-right-bottom">
            <span @click="router.push('/ai-features/video-create')">{{ t('aiDaJi.independentCreation') }}</span>
            <span>{{ t('aiDaJi.btnTips') }}</span>
          </div> -->
        </div>
      </div>
      <div class="bottom skip-translate">
        <AreaTitle class="mb-[20px]" :from="'home'" :title="t('aiDaJi.gatherLinyi')" />
        <div class="video-wrap">
          <div class="box flex flex-col" v-for="(item, index) in videoList" :key="index" @click="videoClick(index)">
            <img class="video-bg" :src="`${ossUrl}/ai-daji/video-bg/${item.videoName}.png?v=1.2`" alt="" srcset="" />
            <div class="video-bottom flex flex-col justify-center items-center">
              <div class="video-name">{{ $storageLocale === 'zh' ? item.name.zh : item.name.en }}</div>
              <div class="line-wrap">
                <span></span>
                <span></span>
                <span></span>
              </div>
              <div class="video-title">{{ $storageLocale === 'zh' ? item.title.zh : item.title.en }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import AreaTitle from './components/area-title.vue'
import Header from './components/header.vue'
import { ossUrl } from '@/constants/common'
import { LINYI_CHINA_MARKET_MAP } from '@/constants/special-field'
import { useEvent } from '@/event'
import { AI_DAJI_VIDEO_LIST } from './ai-daji-video'

const { t } = useI18n({
  messages: {
    zh: {
      aiDaJi: {
        introduce: '临沂商城 · 中国大集',
        message: `中国大集，临沂商城打造的数字商贸综合服务平台，是传统市场转型的重要数字引擎。立足中国最大商贸物流城、国际文化城等产业基础。以区域禀赋，科技赋能，聚焦地产品供应链，整合商仓、物流、会展、资金、贸易服务为一体，提供品质和惠商的严选产品，推动链属企业降本增效，促进临沂经济稳步增长。\n       我们搭建中国大集数字商贸综合服务平台，提供外贸入口和服务体系，成为国际商城发展的新基建。我们打造数字选品中心，凭专业品类实现跨境布局，推动传统市场高质量发展；我们鼓励特色产业带上平台，推动三区九县融入商城数字货盘，成为极具特色的北方产业带；我们搭建线下场景样板，配套直播、网红、购物、娱乐一站式管理服务，让市场商户不必多头对接专心经营；我们深度挖掘临沂商城特色，携手星级商户打造爆款计划，让供需双方的合作赢在起点。`,
        independentCreation: '自主创作',
        btnTips: '点击可跳转AI创新页面进行数字人或多语言翻译的自主创作',
        gatherLinyi: '聚临沂商城 · 赶中国大集',
      },
    },
    en: {
      aiDaJi: {
        introduce: 'Linyi Trade City · Chinamarket',
        message: `The Chinamarket,the official portal of Digital Commerce Comprehensive Service Platform by Linyi Mall, is an crucial digital engine for the transformation of traditional markets. Based on the industrial foundation of China’s largest commercial and logistics city, international cultural city, etc.and empowered by regional resources and technology, it focuses on the supply chain of local products,integrates business, warehouse, logistics, park, exhibition, finance, and trade services,provides high-quality and cost-effective selected products for merchants,promotes cost reduction and efficiency increase for the chain-related enterprises, and enhances the steady growth of the Linyi economy.\n       By building the official portal of the Chinamarket,we provide Digital Commerce Comprehensive Service Platform,becoming the new infrastructure for the development of the international mall.By establishing a digital product selection center.we realize cross-border layout with professional categories.promoting the high-quality development of traditional markets.By encouraging characteristic industrial cluster to go online,we accelerate the integration of three districts and nine counties into the digital goods trading platform of the mall.becoming a highly characteristic northern industrial belt.By setting up an offline scene model,we provide one-stop management services such as live streaming, internet celebrities, shopping, and entertainment.enabling market merchants to focus on business without red tapes.By deeply exploring the characteristics of Linyi Mall,and working with star merchants to create a hit product plan,we make successful cooperation between supply and demand sides at the starting point.`,
        independentCreation: 'Self-Creation',
        btnTips: 'Click to jump to the AI innovation page for independent creation of digital humans or multilingual translation',
        gatherLinyi: 'Gather at Linyi trade city · Join the Aggregation',
      },
    },
  },
})

// 过滤掉临沂商城的介绍视频
const videoList = computed(() => {
  return AI_DAJI_VIDEO_LIST.slice(0, 12)
})

// const router = useRouter()
const headerRef = ref(null)
const videoClick = (index) => {
  headerRef.value.onPause()
  introduceVideoRef.value.pause()
  window.open(`/ai-daji-detail?index=${index}`, '_blank')
}

// 点击数字人视频，暂停临沂商城介绍视频
const event = useEvent()
event.on('digitalVideoClick', () => {
  introduceVideoRef.value.pause()
})

// 点击临沂商城介绍视频，暂停数字人视频
const introduceVideoRef = ref(null)
const introduceVideoClick = () => {
  headerRef.value.onPause()
}
</script>

<style lang="scss" scoped>
.ai-daji {
  background-color: #ffffff;
  .main-content {
    width: $main-width;
    margin: 20px auto 0;
    .center {
      display: flex;
      justify-content: space-between;
      height: 480px;
      &-video {
        width: 720px;
        height: 400px;
        border-radius: 8px;
        object-fit: cover;
      }
      &-right {
        margin-left: 40px;
        .title {
          display: flex;
          align-items: center;
          height: 56px;
          margin-bottom: 16px;
          span:first-child {
            font-size: 28px;
            font-weight: 600;
            color: #333333;
          }
        }
        &-message {
          width: 500px;
          height: 325px;
          line-height: 28px;
          font-size: 14px;
          color: #333333;
          text-indent: 2em;
          white-space: pre-wrap;
          overflow-y: auto;
          &:disabled {
            background-color: #ffffff;
          }
        }
        &-bottom {
          display: flex;
          align-items: center;
          margin-top: 28px;
          span:first-child {
            flex-shrink: 0;
            height: 40px;
            line-height: 40px;
            padding: 0px 40px;
            margin-right: 12px;
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
            border-radius: 4px;
            background: linear-gradient(270deg, #d8131a 0%, #fd441f 100%);
            cursor: pointer;
          }
          span:last-child {
            font-size: 12px;
            color: #999999;
          }
        }
      }
    }
    .bottom {
      display: flex;
      flex-direction: column;
      align-items: center;
      .video-wrap {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 20px;
        margin-bottom: 80px;
        .box {
          width: 406px;
          cursor: pointer;
          .video-bg {
            width: 100%;
            height: 228px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
          }
          .video-bottom {
            height: 120px;
            text-align: center;
            background: #d8131a url('https://static.chinamarket.cn/static/trade-exhibition/ai-daji/video-bottom-bg.png') no-repeat;
            background-size: 100%;
            color: #ffffff;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            .video-name {
              font-size: 20px;
              font-weight: 600;
            }
            .line-wrap {
              display: flex;
              justify-content: center;
              align-items: center;
              margin: 12px 0;
              span {
                display: inline-block;
                height: 0.5px;
                width: 142px;
                background-color: #ffffff;
              }
              span:nth-child(2) {
                width: 3px;
                height: 3px;
                transform: rotate(-135deg);
                margin: 0 12px;
              }
            }
            .video-title {
              font-size: 14px;
              padding: 0 10px;
            }
          }
        }
      }
    }
  }
}

[dir='rtl'] .main-content .center {
  &-right {
    margin-right: 24px;
    margin-left: 0;

    span:first-child {
      font-size: 32px !important;
    }

    &-bottom span:first-child {
      margin-left: 12px;
      margin-right: 0;
    }
  }
}
</style>
