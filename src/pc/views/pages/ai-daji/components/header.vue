<template>
  <div class="header">
    <div class="header-content">
      <div class="top">
        <div class="left">
          <div class="title" v-mode="AI_BROTHER_MAP">{{ t('aiDaJi.title') }}</div>
          <div class="light"></div>
          <div class="desc">{{ t('aiDaJi.desc') }}</div>
          <div class="start-btn" @click="openAiWindow">{{ t('aiDaJi.useNow') }}</div>
        </div>
        <div class="right">
          <video
            ref="digitalVideoRef"
            class="right-video"
            width="350"
            height="auto"
            controls
            :autoplay="isNeedAutoPlay"
            disablePictureInPicture
            :poster="formatVideoPosterUrl(`${ossUrl}/ai-daji/digital-intro-online.mp4?v=1.2`)"
            :src="`${ossUrl}/ai-daji/digital-intro-online.mp4?v=1.2`"
            @play="digitalVideoClick"
          />
        </div>
      </div>
      <img class="triangle" :src="`${ossUrl}/ai-daji/triangle.png`" dragable="false" />
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { ossUrl } from '@/constants/common'
import { AI_BROTHER_MAP } from '@/constants/special-field'
import { useEvent } from '@/event'
import { OPEN_AI_WINDOW } from '@/event/modules/site'
import { formatVideoPosterUrl } from '@/utils/utils'

defineProps({
  isNeedAutoPlay: {
    type: Boolean,
    default: false,
  },
})

const { t } = useI18n({
  messages: {
    zh: {
      aiDaJi: {
        title: '数字大集哥',
        desc: '大集AI，贸语无界限',
        useNow: '点击马上使用',
      },
    },
    en: {
      aiDaJi: {
        title: 'AI Brother',
        desc: 'Daji AI makes trade language without boundaries',
        useNow: 'Click to Use Now',
      },
    },
  },
})

const event = useEvent()
const openAiWindow = () => {
  event.emit(OPEN_AI_WINDOW)
}

const digitalVideoRef = ref(null)
const onPause = () => {
  digitalVideoRef.value.pause()
}
const digitalVideoClick = () => {
  event.emit('digitalVideoClick')
}

defineExpose({
  onPause,
})
</script>

<style lang="scss" scoped>
.header {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 800px;
  background: url('https://static.chinamarket.cn/static/trade-exhibition/ai-daji/ai-daji-bg.png') no-repeat center;
  background-size: cover;
  &-content {
    width: $main-width;
    margin: 0 auto;
    .top {
      display: flex;
      justify-content: space-between;
      .left {
        display: flex;
        flex-direction: column;
        justify-content: center;
        color: #ffffff;
        .title {
          font-size: 60px;
          font-weight: bold;
        }
        .light {
          width: 138px;
          height: 4px;
          margin-top: 10px;
          background: linear-gradient(90deg, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
        }
        .desc {
          font-size: 30px;
          margin: 17px 0 50px;
        }
        .start-btn {
          width: fit-content;
          padding: 12px 36px;
          border-radius: 4px;
          border: 1px solid #ffffff;
          font-size: 16px;
          font-weight: 600;
          cursor: pointer;
        }
      }
      .right {
        margin-top: 90px;
        margin-right: 160px;
        position: relative;
        width: 504px;
        height: 693px;
        background-size: 100%;
        flex-shrink: 0;
        border-radius: 24px 24px 0 0;
        background: linear-gradient(180deg, rgba(216, 216, 216, 0.075) 0%, rgba(216, 216, 216, 0.5) 97%);
        box-sizing: border-box;
        border: 1px solid rgba(255, 255, 255, 0.5);
        backdrop-filter: blur(20px);
        padding-left: 40px;

        &-video {
          position: absolute;
          bottom: 120px;
          left: 80px;
          border-radius: 8px;
        }
      }
    }
    .triangle {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 162px;
    }
  }
}

[dir='rtl'] .header-content .top .left .title {
  font-size: 56px;
}
</style>
