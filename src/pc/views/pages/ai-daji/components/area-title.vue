<template>
  <div class="title">
    <img :src="`${ossUrl}/ai-daji/more-left.svg`" dragable="false" />
    <span class="skip-translate" v-if="from !== 'home'">{{ $storageLocale === 'zh' ? titleObj.zh : titleObj.en }}</span>
    <span v-else v-mode="LINYI_CHINA_MARKET_MAP">{{ title }}</span>
    <img :src="`${ossUrl}/ai-daji/more-right.svg`" dragable="false" />
  </div>
</template>

<script setup>
import { ossUrl } from '@/constants/common'
import { LINYI_CHINA_MARKET_MAP } from '@/constants/special-field'

defineProps({
  title: {
    type: String,
    default: '',
  },
  titleObj: {
    type: Object,
    default: () => ({}),
  },
  from: {
    type: String,
    default: '',
  },
})
</script>

<style lang="scss" scoped>
.title {
  display: flex;
  align-items: center;
  font-size: 40px;
  font-weight: 600;
  text-align: center;
  color: #333333;
  img {
    width: 45px;
    height: 16px;
  }
  span {
    margin: 0 16px;
  }
}
</style>
