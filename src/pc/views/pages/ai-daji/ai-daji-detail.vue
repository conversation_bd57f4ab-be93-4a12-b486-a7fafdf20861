<template>
  <div class="ai-daji-detail">
    <Header ref="headerRef" />
    <div class="ai-daji-detail-bottom bg-white">
      <AreaTitle class="mb-[12px]" :titleObj="videoIndex.name" />
      <div v-if="route.query.index !== 12" class="representative-title skip-translate mb-[20px] text-[26px] color-[#9E9E9E]">
        {{ $storageLocale === 'zh' ? videoIndex.title.zh : videoIndex.title.en }}
      </div>
      <div class="language bg-[#F5F6F7] mb-[20px] p-[4px]">
        <span
          v-for="(item, index) in list"
          :key="index"
          :class="{ active: currentIndex === index }"
          class="py-[10px] px-[16px] text-[14px] skip-translate"
          @click="currentIndex = index"
          >{{ item.label }}</span
        >
      </div>
      <video
        ref="presidentVideoRef"
        class="mb-[40px]"
        width="1260"
        height="708"
        controls
        preload="auto"
        :poster="`${ossUrl}/ai-daji/video-bg/${videoIndex.videoName}-big.png?v=4`"
        :src="`${ossUrl}/ai-daji/video/${videoIndex.videoName}-${languageList[currentIndex]?.lang}.mp4?v=4`"
        @play="presidentVideoClick"
      ></video>
    </div>
  </div>
</template>

<script setup>
import { useWindowScroll } from '@vueuse/core'
import { useI18n } from 'vue-i18n'
import AreaTitle from './components/area-title.vue'
import Header from './components/header.vue'
import { ossUrl } from '@/constants/common'
import { useEvent } from '@/event'
import { LANG_CHANGED } from '@/event/modules/site'
import { AI_DAJI_VIDEO_LIST } from './ai-daji-video'

const languageList = [
  { label: '中文', lang: 'zh' },
  { label: 'English', lang: 'en' },
  { label: 'العربية', lang: 'arabic' },
  { label: 'ภาษาไทย', lang: 'thai' },
  { label: 'Bahasa Indonesia', lang: 'indonesian' },
  { label: '日本語', lang: 'japan' },
  { label: '한국어', lang: 'korean' },
  { label: 'pycck', lang: 'russian' },
]
const list = ref([])

const route = useRoute()
const videoIndex = AI_DAJI_VIDEO_LIST[route.query.index]

const { y } = useWindowScroll()
const { locale } = useI18n()
const currentIndex = ref(null)
const langObj = { zh: 0, en: 1 }
onMounted(() => {
  if (route.query.index == 12) {
    list.value = languageList.slice(0, 2)
  } else {
    list.value = languageList
  }
  currentIndex.value = langObj[locale.value]
  y.value = 500
})

const event = useEvent()
event.on(LANG_CHANGED, (lang) => {
  currentIndex.value = langObj[lang]
})

const presidentVideoRef = ref(null)
// 点击数字人视频，暂停代表视频
event.on('digitalVideoClick', () => {
  presidentVideoRef.value.pause()
})

// 点击代表视频，暂停数字人视频
const headerRef = ref(null)
const presidentVideoClick = () => {
  headerRef.value.onPause()
}
</script>

<style lang="scss" scoped>
.ai-daji-detail {
  &-bottom {
    display: flex;
    flex-direction: column;
    align-items: center;
    .language {
      span {
        display: inline-block;
        cursor: pointer;
        &.active {
          font-weight: 600;
          color: #ffffff;
          border-radius: 4px;
          background: linear-gradient(270deg, #d8131a 0%, #fd441f 100%);
        }
      }
    }
  }
}
</style>
