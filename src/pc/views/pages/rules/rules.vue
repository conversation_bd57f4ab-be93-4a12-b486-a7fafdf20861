<template>
  <div class="w-1260">
    <div class="rules-center flex">
      <div class="left py-6 px-4 bg-white w-56 mr-4 mb-5 overflow-y-scroll h-full scrollbar">
        <BorderTitle class="font-600" :title="t('rules')" />
        <div class="sub-nav" :class="{ active: currentTab === item.id }" v-for="(item, index) in subTabs" :key="index" @click="subTabClick(item)">
          {{ item.title[$i18n.locale] }}
        </div>
      </div>
      <div class="right flex-1">
        <iframe :src="`/protocol/${currentTab}.html`" frameborder="0" style="width: 100%; height: 100%" ref="iframe" :key="lang"></iframe>
      </div>
    </div>
  </div>
</template>

<script setup lang="jsx">
import { useI18n } from 'vue-i18n'
import { useEvent } from '@/event'
import { LANG_CHANGED } from '@/event/modules/site'

const { t } = useI18n({
  messages: {
    zh: {
      rules: '平台规则',
    },
    en: {
      rules: 'Platform Rules',
    },
  },
})

// import * as API from '@/apis/outside-trade-serve'
const route = useRoute()
const subTabs = [
  { title: { zh: '大集平台信息发布标准', en: 'Platform Posting Standards' }, id: 'standards' },
  { title: { zh: '中国大集禁售商品细则', en: 'Prohibited Products Guidelines' }, id: 'prohibited' },
  { title: { zh: '中国大集商家管理规则', en: 'Merchant Management Rules' }, id: 'merchantmanagement' },
  { title: { zh: '中国大集卖家服务协议', en: 'Seller Service Agreement' }, id: 'sellerrule' },
  { title: { zh: '个人店-个人身份入驻所需材料', en: 'Individual Registration Materials' }, id: 'userjoining' },
  { title: { zh: '个体户-个体店入驻所需材料', en: 'Sole Proprietor Registration Materials' }, id: 'Individualshop' },
  { title: { zh: '企业店-企业公司入驻所需材料', en: 'Company Registration Materials' }, id: 'companyshop' },
  { title: { zh: '中国大集店铺类型及命名规范', en: 'Store Type and Naming Guidelines' }, id: 'shoptypename' },
  { title: { zh: '中国大集服务商入驻规范', en: 'Seller Service Agreement' }, id: 'servicejoinrules' },
  { title: { zh: '中国大集服务商入驻所需材料', en: 'Service Provider Registration Guidelines' }, id: 'servicefiles' },
  { title: { zh: '中国大集服务商入驻协议', en: 'Service Provider Registration Agreement"' }, id: 'servicejoining' },
  { title: { zh: '中国大集服务商资质管理规范', en: 'Business Qualification Management Guidelines' }, id: 'serviceinfomanage' },
  { title: { zh: '中国大集平台店铺服务协议', en: 'Store Service Agreement' }, id: 'platformshop' },
  { title: { zh: '中国大集平台法律声明', en: 'Platform Legal Disclaimer' }, id: 'platformlaw' },
  { title: { zh: '中国大集商家资质管理规范', en: 'Merchant Qualification Management Guidelines' }, id: 'companymanage' },
  { title: { zh: '中国大集招商标准及入驻规范', en: 'Recruitment Standards and Registration Guidelines' }, id: 'attractinvestment' },
]

const currentTab = ref('standards')
const event = useEvent()

onMounted(() => {
  if (route.query?.id) {
    currentTab.value = route.query.id
  }
})

const subTabClick = ({ id }) => {
  currentTab.value = id
}

const iframe = ref(null)
const lang = ref('zh')
event.on(LANG_CHANGED, async (key) => {
  lang.value = key
})
</script>

<style lang="scss" scoped>
.rules-center {
  width: 100%;
  height: calc(100vh - 64px);
  padding: 20px 0;
  box-sizing: border-box;

  .left {
    flex-shrink: 0;

    .sub-nav {
      padding: 9px 20px;
      line-height: 20px;
      margin-bottom: 12px;
      font-size: 14px;
      border: 1px solid #fff;
      color: #999;
      cursor: pointer;
      &.active {
        color: $primary-color;
        border: 1px solid $primary-color;
        background: #ffeded;
        border-radius: 4px;
      }
    }
  }
  .right {
    height: 100%;
    box-sizing: border-box;
    padding-bottom: 24px;
    border-radius: 4px;
    background: #fff;
    max-width: 1064px;
    margin-bottom: 24px;
    overflow: hidden;

    .rule-wrap {
      padding: 24px;
      height: 100%;
      overflow: auto;
      box-sizing: border-box;
    }
  }
}
</style>
