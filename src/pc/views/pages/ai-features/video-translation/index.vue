<!--
 * @Author: 王俊杰 “<EMAIL>”
 * @Date: 2024-03-20 11:48:19
 * @LastEditors: your name
 * @LastEditTime: 2024-09-09 10:34:07
 * @FilePath: /trade-exhibition/src/pc/views/pages/ai-features/video-translation/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="video-translation">
    <div class="left grid content-between">
      <div>
        <div class="text-[16px] text-[#333333] font-bold mb-3">{{ t('field1') }}</div>
        <el-input class="mb-4" v-model="name" :placeholder="t('field1Placeholder')"></el-input>
        <div class="text-[16px] text-[#333333] font-bold mb-3">{{ t('field2') }}</div>
        <div class="border rounded-sm p-[2px] mb-4">
          <ImgUpload
            class="video"
            v-model="videoUrl"
            :dir="OSS_DIR.AI_HUMAN_VIDEO_TRANSLATION"
            :tipsText="t('field2Placeholder')"
            accept="video/mp4"
            :isVideo="true"
            height="157px"
            videoHeight="117px"
          ></ImgUpload>
        </div>
        <div>
          <div class="mb-[4px] text-[16px] text-[#333333] font-bold">{{ t('field3') }}</div>
          <div class="mb-2 text-[12px] text-[#999999]">{{ t('field3Placeholder') }}</div>
          <el-select v-model="language">
            <el-option key="2" :label="t('lang[0]')" value="2"></el-option>
          </el-select>
        </div>
      </div>
      <div class="text-center">
        <el-button
          class="w-[100%] h-14 bg-gradient-to-r from-purple-500 to-pink-500"
          type="primary"
          :disabled="videoUrl.length === 0 || !name"
          @click="onTransVideo"
        >
          {{ t('submit') }}
        </el-button>
      </div>
    </div>

    <div class="right grid place-content-center relative">
      <!-- 没有上传视频 -->
      <div v-if="videoUrl.length === 0" class="text-center text-[24px]">
        <div class="mb-3 text-[#333333] font-bold">{{ t('tip') }}</div>
        <div style="width: 100%; height: 440px">
          <video
            muted
            autoplay="autoplay"
            loop="loop"
            src="https://static.chinamarket.cn/static/trade-exhibition/ai-feature-intro/video-translate-new.mp4"
            style="width: 100%; height: 100%; object-fit: fill"
            disablePictureInPicture
          ></video>
        </div>
      </div>
      <!-- 上传了视频 -->
      <div v-else class="text-center text-[20px]">
        <div class="absolute top-4 right-4 cursor-pointer" @click="operateGuide">
          <icon type="icon-caozuozhiyin" size="48"></icon>
          <div class="mt-1 text-sm text-#666">{{ t('guide') }}</div>
        </div>
        <div v-if="isTranslated">
          <i18n-t class="mb-4 text-[#999999]" keypath="jumpTip" tag="div">
            <span class="text-[#333333]">{{ t('jumpTo') }}</span>
          </i18n-t>
          <el-button class="h-14 mb-[60px]" type="primary" @click="goPage">{{ t('jump') }}</el-button>
        </div>
        <div class="max-h-[440px]">
          <template v-for="v in videoUrl" :key="v">
            <video class="max-h-[440px]" controls :src="v"></video>
          </template>
        </div>
      </div>
    </div>
  </div>

  <OperateGuide ref="operateGuideRef"></OperateGuide>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import OperateGuide from './operate-guide-dialog.vue'
import { OSS_DIR } from '@/constants/oss-dir'
import { useUserStore } from '@/pc/stores'
import * as API from '@/apis/ai-features'
import { useEvent } from '@/event'
import { OPEN_NEW_LOGIN } from '@/event/modules/site'

const { t } = useI18n({
  messages: {
    zh: {
      field1: '视频名称',
      field1Placeholder: '请输入视频名字',
      field2: '视频',
      field2Placeholder: '从本地上传视频，仅支持MP4文件',
      field3: '选择语种',
      field3Placeholder: '选择语种后，可识别目标语种翻译成选择的语种翻译完成的视频在内容管理处显示',
      lang: ['英文'],
      submit: '立即合成视频',
      submitSuccess: '操作成功！',
      tip: '视频翻译步骤',
      guide: '操作指引',
      jumpTip: '生成内容可前往“{0}”页面查看及预览',
      jumpTo: '内容管理',
      jump: '立即跳转',
    },
    en: {
      field1: 'Video Name',
      field1Placeholder: 'Please enter the video name',
      field2: 'Video File',
      field2Placeholder: 'Upload video from local, only MP4 files are supported.',
      field3: 'Select language',
      field3Placeholder:
        'After selecting the language, the target language will be recognized and translated into the chosen language. The translated video will be displayed in the content management section once completed.',
      lang: ['English'],
      submit: 'Generate Now',
      submitSuccess: 'Operation successful!',
      tip: 'Video translation steps',
      guide: 'Guide',
      jumpTip: 'Generated content can be viewed and previewed in the "{0}" page',
      jumpTo: 'Content Management',
      jump: 'Jump Now',
    },
  },
})
const router = useRouter()
const event = useEvent()

const userStore = useUserStore()
if (!userStore.isLogined) {
  event.emit(OPEN_NEW_LOGIN)
}

const videoUrl = ref([])

const language = ref('2')
const name = ref('')
// 翻译成功
const isTranslated = ref(false)

const onTransVideo = async () => {
  try {
    const params = {
      languageType: language.value,
      videoUrl: videoUrl.value[0],
      name: name.value,
    }
    await API.transVideo(params)
    isTranslated.value = true
    ElMessage({ message: t('submitSuccess'), type: 'success' })
  } catch (error) {
    isTranslated.value = false
  }
}

const goPage = () => {
  router.push('/ai-features/short-video')
}

const operateGuideRef = ref(false)
const operateGuide = () => {
  operateGuideRef.value.init('translate')
}
</script>

<style lang="scss" scoped>
.video-translation {
  display: grid;
  grid-template-columns: 356px 1fr;
  gap: 16px;
  height: 100%;

  .left {
    padding: 16px;
  }

  .left,
  .right {
    background: $basic-white;
  }

  .el-button--primary {
    background: linear-gradient(270deg, #d8131a 0%, #e96f18 100%);
    height: 42px;
    border: 0;
  }

  .el-button.is-disabled,
  .el-button.is-disabled:hover {
    background: linear-gradient(270deg, #d8131a 0%, #e96f18 100%), rgba(255, 255, 255, 0.5);
    cursor: not-allowed;
    opacity: 0.5;
  }
}

::v-deep {
  .video-box {
    text-align: center;
  }
}
</style>
