<style lang="scss" scoped>
.operate-guide-dialog {
  :deep() {
    .el-dialog__title {
      font-weight: 600;
      font-size: 16px;
      color: #333;
    }
    .el-dialog__headerbtn {
      top: 18px;
      right: 18px;
      width: 22px;
      height: 22px;
    }
    .el-dialog__close {
      font-size: 22px;
      color: #333;
    }
  }
}
</style>

<template>
  <div class="operate-guide-dialog">
    <el-dialog v-model="dialogVisible" :title="t('guide')" width="848px" destroy-on-close>
      <video class="w-full" muted autoplay loop :src="audioSrc" disablePictureInPicture></video>
    </el-dialog>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'

const { t } = useI18n({
  messages: {
    zh: {
      guide: '操作指引',
    },
    en: {
      guide: 'Guide',
    },
  },
})
const dialogVisible = ref(false)
const audioSrc = ref(null)

const init = (val) => {
  dialogVisible.value = true
  switch (val) {
    // 视频翻译
    case 'translate':
      audioSrc.value = 'https://static.chinamarket.cn/static/trade-exhibition/ai-feature-intro/视频翻译.mp4'
      break
    case 'productPicture':
      audioSrc.value = 'https://static.chinamarket.cn/static/trade-exhibition/ai-feature-intro/更换图片.mp4'
      break
    case 'numberHuman':
      audioSrc.value = 'https://static.chinamarket.cn/static/trade-exhibition/ai-feature-intro/数字代言人.mp4'
      break
    default:
      break
  }
}

defineExpose({ init })
</script>
