<style lang="scss" scoped>
.create-human-dialog {
  .el-dialog__header {
    padding-bottom: 0 !important;

    .el-tabs__header {
      margin-bottom: 0;
    }
  }
}
</style>

<template>
  <el-dialog v-model="dialogVisible" title="数字人视频创建" class="create-human-dialog" :show-close="false" width="870px">
    <template #header>
      <div class="text-center text-[20px] font-bold">数字人视频创建</div>
    </template>
    <div class="bg-[#f7f7f8] rounded-sm p-[32px]">
      <el-form ref="ruleFormRef" :model="ruleForm" label-width="120px">
        <el-form-item label="数字人模版">
          <el-image class="w-[160px] h-[90px] rounded-[4px]" :src="ruleForm.template" />
        </el-form-item>
        <el-form-item label="视频尺寸">
          <el-select v-model="ruleForm.size" disabled placeholder="请选择视频尺寸">
            <el-option label="PC版（1920*1080px）" value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="数字人语言">
          <el-select v-model="ruleForm.language" placeholder="请选择数字人语言">
            <el-option label="Chinese" value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="数字人">
          <el-avatar :size="100" :src="ruleForm.microHuman" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="text-center">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="dialogVisible = false"> 立即合成视频 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
const dialogVisible = ref(false)

const ruleForm = reactive({
  template: 'https://aigc-1301563501.cos.ap-shanghai.myqcloud.com/ttttt.png',
  size: '1',
  language: '1',
  microHuman: 'https://assets.chinagoods.com/libs/cgf/static/images/preview_talk_6.png',
})

// 打开弹窗
const open = () => {
  dialogVisible.value = true
}

defineExpose({
  open,
})
</script>
