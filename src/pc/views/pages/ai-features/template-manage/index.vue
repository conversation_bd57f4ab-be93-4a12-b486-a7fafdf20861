<template>
  <div>
    <div class="mb-1 text-[20px] font-bold">模版管理</div>
    <el-row class="flex mt-[38px] mx-[-10px]">
      <el-col
        v-for="item in templateImageData"
        :key="item.name"
        :span="4"
        class="w-[240px] h-[134px] mr-[10px] mt-[16px] text-center px-[10px] cursor-pointer"
        @click="onCreateVideo(item)"
      >
        <el-image :src="item.image" class="h-[112px] rounded-[10px] hover:shadow-xl" />
      </el-col>
    </el-row>
    <!-- 创建弹窗 -->
    <CreateVideo ref="createVideoRef" />
  </div>
</template>

<script setup>
import CreateVideo from './components/create-video.vue'
import { templateImageData } from '@/constants/mock-data'

const createVideoRef = ref(null)
const onCreateVideo = () => {
  createVideoRef.value.open()
}
</script>

<style lang="scss" scoped></style>
