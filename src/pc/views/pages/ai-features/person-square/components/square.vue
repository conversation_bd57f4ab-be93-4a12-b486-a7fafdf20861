<!--
 * @Author: 王俊杰 “<EMAIL>”
 * @Date: 2024-02-19 10:59:54
 * @LastEditors: 王俊杰 “<EMAIL>”
 * @LastEditTime: 2024-03-22 11:59:00
 * @FilePath: /sdt-micro-human/src/views/person-square/components/square.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<style lang="scss" scoped>
.image-item {
  &:hover {
    font-weight: 600;
    box-shadow: 0px 4px 8px 0px rgba(49, 0, 0, 0.06);
  }
}
</style>

<template>
  <div class="p-[16px] bg-[#FFFFFF] text-[#333333] h-[100%]">
    <div class="mb-3 text-[16px] font-semibold">{{ t('title') }}</div>
    <div class="grid grid-cols-3 gap-2">
      <div v-for="item in humanList" :key="item.modelId" class="image-item text-center">
        <div class="flex items-end justify-center h-[101px] w-[101px] bg-[#F7F7F7] p-[21px 10px 0px 10px]">
          <el-image class="cursor-pointer h-[80px]" :src="item.modelPic" fit="cover" @click="toVideoCreate(item)"></el-image>
        </div>
        <div class="text-[14px] mt-1 text-[#333333]">{{ item.modelName }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useHumanModel } from '@/pc/hooks/aiHumanModel'

const { t } = useI18n({
  messages: {
    zh: {
      title: '数字人',
    },
    en: {
      title: 'Digital human',
    },
  },
})

const { getHumanList, humanList } = useHumanModel()
getHumanList()

const router = useRouter()

// 跳转视频创作
const toVideoCreate = () => {
  router.push('/ai-features/video-create')
}
</script>
