<style lang="scss" scoped></style>

<template>
  <div>
    <div v-if="showCreate" class="flex flex-col items-center">
      <el-image src="https://assets.chinagoods.com/libs/cgf/static/images/蒙版组 792@2x_1690273977854.png" style="width: 300px; height: 300px" fit="cover" />
      <p class="text-[16px] mb-2">您还没有创建您的数字人</p>
      <el-button type="primary" round @click="onCreatePerson">创建数字人</el-button>
    </div>

    <!-- 创建数字人人像 -->
    <template v-else>
      <div class="mb-1 text-[20px] font-bold">创建数字人人像</div>
      <div class="flex">
        <!-- 左边 -->
        <div class="w-[56%]">
          <div class="border rounded-sm border-dashed p-[2px] mb-1">
            <Upload v-model="videoBgUrl" height="450px" :plusSize="64" dir="ai_human_video_creation" />
          </div>
          <div class="flex items-center">
            <div class="text-[14px] w-[120px]">数字人像名称</div>
            <el-input v-model="name" placeholder="请输入数字人名称，建议10个汉字以内" />
          </div>
        </div>
        <!-- 右边 -->
        <div class="flex-1 ml-5 border rounded-sm border-dashed px-[20px] py-[20px]">示例图片展示区域</div>
      </div>
      <div class="text-center pt-[30px]">
        <el-button round @click="showCreate = true">取消</el-button>
        <el-button type="primary" round @click="determineCreatePerson">开始创建</el-button>
      </div>
    </template>
  </div>
</template>

<script setup>
const showCreate = ref(true)

const videoBgUrl = ref([])

const name = ref('')

// 创建数字人
const onCreatePerson = () => {
  showCreate.value = false
}

// 确定创建数字人
const determineCreatePerson = () => {
  showCreate.value = true
}
</script>
