<style lang="scss" scoped>
.person-square {
  display: grid;
  grid-template-columns: 356px 1fr;
  gap: 16px;
  height: 100%;
}
</style>

<style lang="scss" scoped></style>

<template>
  <div class="person-square">
    <Square></Square>
    <div class="grid bg-[#FFFFFF] place-content-center">
      <div class="text-center">
        <img class="w-40 h-40 object-cover" src="https://static.chinamarket.cn/static/trade-exhibition/stay-tuned.png" />
        <div class="mt-4 text-[#333333] text-[24px]">
          <span v-if="$i18n.locale === 'zh'">正在开发中，敬请期待....</span>
          <span v-if="$i18n.locale === 'en'">In development....</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import Square from './components/square.vue'
</script>
