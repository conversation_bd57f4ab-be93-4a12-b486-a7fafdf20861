<!-- 选择模版背景 -->
<style lang="scss">
.select-bg-box {
  .el-dialog__header {
    padding: 0;
    text-align: center;
    margin: 0;
  }

  .el-image__inner {
    object-fit: contain;
  }
}
</style>

<template>
  <el-dialog v-model="dialogVisible" class="select-bg-box" :close-on-click-modal="false" :show-close="false" title="选择背景模版" width="46%" @close="close">
    <div class="flex max-h-[400px] ml-[10px] overflow-auto text-center flex-wrap">
      <div v-for="(item, index) in templateBgData" :key="item.id" class="w-[18%] px-[12px] mb-[50px]" @click="selectBgTemp(item, index)">
        <el-image class="cursor-pointer h-[140px] rounded-[10px]" :class="{ 'shadow-2xl': currentTempleIndex === index }" :src="item.url" />
        <div class="mt-[12px] text-center truncate text-[14px]">{{ item.name }}</div>
      </div>
    </div>
    <template #footer>
      <div class="text-center">
        <el-button round @click="close">取消</el-button>
        <el-button type="primary" round @click="onDetermineTemp">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import * as videoApi from '@/apis/ai-features'

const emits = defineEmits('send-template-img')

const { t } = useI18n({
  messages: {
    zh: {
      tips: '请选择模版',
    },
    en: {
      tips: 'Please select a template',
    },
  },
})

const dialogVisible = ref(false)

// 当前选中的模版
const currentTempleIndex = ref(null)

const templateImg = ref('')

// 获取背景模版
const templateBgData = reactive([])
const getBgTemplate = async () => {
  let res = await videoApi.listBackgroundTemplate()
  templateBgData.length = 0
  templateBgData.push(...res)
}

// 选择模版
const selectBgTemp = (item, index) => {
  currentTempleIndex.value = index
  templateImg.value = item.url
}

// 打开弹窗
const open = () => {
  getBgTemplate()
  dialogVisible.value = true
}

// 关闭弹窗
const close = () => {
  dialogVisible.value = false
  currentTempleIndex.value = null
  templateImg.value = ''
}

// 点击确定
const onDetermineTemp = () => {
  if (!templateImg.value) {
    ElMessage.error(t('tips'))
    return
  }
  emits('send-template-img', templateImg.value)
  close()
}

defineExpose({
  open,
})
</script>
