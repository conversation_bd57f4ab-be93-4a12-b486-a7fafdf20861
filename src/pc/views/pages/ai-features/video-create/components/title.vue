<style lang="scss" scoped></style>

<template>
  <div class="text-base font-semibold leading-snug mb-[12px]">
    {{ title }}
    <span v-if="rightTips" class="text-[12px] font-normal text-[#999999] align-text-bottom ml-[4px] vertical-base">{{ rightTips }}</span>
    <div v-if="bottomTips" class="text-[12px] font-normal text-[#999999]">{{ bottomTips }}</div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    default: '',
  },
  rightTips: {
    type: String,
    default: '',
  },
  bottomTips: {
    type: String,
    default: '',
  },
})
</script>
