<!-- 选择数字人弹窗 -->
<style scoped lang="scss">
.number-wrapper {
  overflow: auto;
  scrollbar-width: none;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  width: 100%;
  max-height: 180px;
  margin-bottom: 8px;
  &::-webkit-scrollbar {
    display: none; /* 对于Webkit浏览器，隐藏滚动条 */
  }
}

.list-item {
  border-width: 1px;
  border-style: solid;
  border-color: transparent;
  cursor: pointer;
}
.active {
  border-radius: 8px;
  overflow: hidden;
  border-image: linear-gradient(270deg, #d8131a 0%, #e96f18 100%) 1;
}
</style>

<template>
  <div class="number-wrapper">
    <div v-for="(item, index) in humanList" :key="item.modelId" class="list-item" :class="{ active: humanIndex === index }" @click="onSelectHuman(item, index)">
      <div class="relative pt-full bg-#f7f7f7 rounded-1">
        <img class="absolute inset-0 w-full h-full object-cover" :src="item.modelPic" />
      </div>
      <div class="text-center text-[14px] leading-normal mt-[6px]" :class="{ 'font-semibold': humanIndex === index }">{{ item.modelName }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const emits = defineEmits(['on-select-avatar'])

defineProps({
  humanList: {
    type: Array,
    default: () => [],
  },
})

// 点击展开/收起
// const isToggle = ref(false)
// const onToggle = () => {
//   isToggle.value = !isToggle.value
// }

const dialogVisible = ref(false)

// 打开弹窗
const open = () => {
  dialogVisible.value = true
}

const humanIndex = ref(0)
const onSelectHuman = (item, index) => {
  humanIndex.value = index
  emits('on-select-avatar', item)
}

defineExpose({
  open,
})
</script>
