<style lang="scss" scoped>
.el-select {
  width: 200px !important;
  margin-right: 20px;
}
</style>

<template>
  <div>
    <!-- <div class="mb-2 text-[20px] font-bold">视频创作</div> -->
    <div class="flex">
      <div class="border rounded-sm border-dashed p-[12px] w-[25%] mr-[20px]">
        <div class="text-[16px] font-bold mb-[16px]">制作流程:</div>
        <div class="text-[14px] font-bold leading-[38px]">
          <p>1、一张上半身正面清晰照片。</p>
          <p>2、选择“文字创作”或者“上传语音”。</p>
          <p>3、如果选择“文字创作”，输入文字文案，选择设置“语言”，选择“语音变声”。</p>
          <p>4、点击“立即合成视频”按钮，几分钟即可生成视频。</p>
        </div>
      </div>
      <div class="w-[70%]">
        <el-form ref="ruleFormRef" :model="ruleForm" label-width="120px" size="small">
          <el-form-item label="名称：">
            <el-input v-model="ruleForm.name" placeholder="请输入视频名称" />
          </el-form-item>
          <el-form-item label="照片：">
            <div class="border rounded-sm border-dashed p-[2px] mb-1 w-[345px]">
              <Upload
                v-model="ruleForm.portraitUrl"
                width="340px"
                height="260px"
                imgWidth="auto"
                :plusSize="64"
                tipsText="支持格式PNG、JPG，请使用五官清晰的单人正脸照片"
                :dir="OSS_DIR.AI_HUMAN_VIDEO_CREATION"
              />
            </div>
          </el-form-item>
          <el-form-item label="文字/语音：">
            <div class="rounded-sm p-[2px] w-[100%]">
              <el-tabs type="border-card" v-model="ruleForm.inferType">
                <el-tab-pane label="文字输入" :name="1">
                  <div class="p-[2px] mb-[8px]">
                    <el-input
                      v-model="ruleForm.inferText"
                      maxlength="300"
                      type="textarea"
                      show-word-limit
                      placeholder="想要照片说写什么内容？请写在这里吧～"
                      :rows="5"
                      resize="none"
                    />
                  </div>
                  <div class="text-right">
                    <el-button class="text-[14px]" :loading="optimizeScriptLoading" link @click="onOptimizeScript">智能优化文字</el-button>
                  </div>
                  <div>
                    <el-select class="mb-[12px]" v-model="ruleForm.languageType" placeholder="请选择语言">
                      <el-option v-for="item in LANGUAGE_DATA_ARRAY" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                    <div class="flex">
                      <audio ref="audioPlayer" @loadedmetadata="getDuration" />
                      <div class="text-[16px] w-[120px]">选择语音变声：</div>
                      <ul class="flex flex-wrap flex-1">
                        <li
                          class="cursor-pointer border-2 min-w-[70px] text-center rounded-[4px] py-[2px] px-[4px] mr-[4px] mb-[4px] text-[14px]"
                          :class="{ 'border-[#2b2953]': currentTrackIndex === index }"
                          v-for="(item, index) in playList"
                          :key="item.voiceModelId"
                          @click="playTrack(item, index)"
                        >
                          {{ item.voiceModelName }}
                        </li>
                      </ul>
                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="上传语音" name="2">
                  <upload
                    v-model="ruleForm.audioFilePath"
                    :isAudio="true"
                    accept="audio/mp3,audio/wav,audio/mpeg"
                    dir="ai_human_video_translation"
                    tipsText="支持格式MP3/WAV格式，大小限制在10M以为"
                  />
                </el-tab-pane>
              </el-tabs>
              <div class="pt-[10px] text-right">
                <el-button v-if="ruleForm.inferType === 1" class="text-[14px]" link :loading="audioLoading" @click="onAuditionAudioByText">试听</el-button>
              </div>
            </div>
          </el-form-item>
        </el-form>
        <div class="text-center">
          <el-button :loading="hcLoading" class="text-[14px]" type="primary" @click="onSynthesisVideo">立即合成视频</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { LANGUAGE_DATA_ARRAY } from '@/constants/index'
import { OSS_DIR } from '@/constants/oss-dir'
import { useVideoStore } from '@/pc/stores'
import gptApi from '@/apis/gpt'
import videoApi from '@/apis/video'
import voiceApi from '@/apis/voice'

const router = useRouter()
const route = useRoute()

const videoStore = useVideoStore()

// 右侧表单
const ruleForm = reactive({
  name: '',
  portraitUrl: [],
  inferType: 1, // tabs切换，1：文字驱动，2：音频驱动
  inferText: '', // 数字人文字值
  audioFilePath: '', // 上传语音 url 连接
  languageType: '1', // 选择语言
  radioSound: new URL('@/assets/audio/default.mp3', import.meta.url).href,
})

// 数字人语音变声列表
const playList = reactive([])
const getPlayList = async () => {
  let res = await voiceApi.listVoiceModel({ templateType: null })
  voiceModelId.value = res[0].voiceModelId
  playList.length = 0
  playList.push(...res)
}
getPlayList()
// 当前选中的语音变声
const currentTrackIndex = ref(0)

// 点击播放
const audioPlayer = ref(null)
const voiceModelId = ref('') // 存储选中语音变声的信息
const playTrack = (item, index) => {
  currentTrackIndex.value = index
  audioPlayer.value.src = playList[index].demoAudioUrl
  audioPlayer.value.play()
  voiceModelId.value = item.voiceModelId
}

// 获取音频信息
const getDuration = (e) => {
  console.log(e)
}

// 点击智能优化文字
const optimizeScriptLoading = ref(false)
const onOptimizeScript = async () => {
  if (!ruleForm.inferText) {
    ElMessage.warning('请输入内容')
    return
  }
  if (!ruleForm.languageType) {
    ElMessage.warning('请选择选择语言')
    return
  }
  optimizeScriptLoading.value = true
  let res = await gptApi.optimizeDescription({ message: ruleForm.inferText })
  ruleForm.inferText = res
  optimizeScriptLoading.value = false
}

// 合成视频 loading
const hcLoading = ref(false)

const message = ref(null)

// 点击合成视频
const onSynthesisVideo = async () => {
  if (message.value) {
    message.value.close()
  }
  if (!ruleForm.name) {
    ElMessage.warning('请输入视频名称')
    return
  }
  if (ruleForm.portraitUrl.length === 0) {
    ElMessage.warning('请上传背景图片')
    return
  }
  if (ruleForm.inferType === 1) {
    if (!ruleForm.inferText) {
      ElMessage.warning('请输入文字')
      return
    }
    if (!ruleForm.languageType) {
      ElMessage.warning('请选择选择语言')
      return
    }
    if (!voiceModelId.value) {
      ElMessage.warning('请选择语音变声')
      return
    }
  } else {
    if (!ruleForm.audioFilePath) {
      ElMessage.warning('上传语音')
      return
    }
  }
  hcLoading.value = true
  const params = {
    name: ruleForm.name,
    portraitUrl: ruleForm.portraitUrl[0],
    inferType: ruleForm.inferType,
    inferText: ruleForm.inferText,
    languageType: ruleForm.languageType,
    audioFilePath: ruleForm.audioFilePath[0],
    modelId: ruleForm.modelId,
    voiceModelId: voiceModelId.value,
  }
  try {
    await videoApi.picToVideo(params)
    router.push('/ai-features/short-video')
  } finally {
    hcLoading.value = false
  }
}

// 点击试听
const audioLoading = ref(false)
const onAuditionAudioByText = async () => {
  if (!ruleForm.inferText) {
    ElMessage.warning('请输入文字')
    return
  }
  if (!ruleForm.languageType) {
    ElMessage.warning('请选择选择语言')
    return
  }
  if (!voiceModelId.value) {
    ElMessage.warning('请选择语音变声')
    return
  }
  audioLoading.value = true
  const params = {
    inferText: ruleForm.inferText,
    languageType: ruleForm.languageType,
    voiceModelId: voiceModelId.value,
  }
  try {
    const res = await videoApi.auditionAudioByText(params)
    audioPlayer.value.src = res
    audioPlayer.value.play()
  } finally {
    audioLoading.value = false
  }
}

// 获取视频详情
const getWorksTaskDetail = async () => {
  let res = await videoApi.worksTaskDetail(route.query.videoId)
  Object.assign(ruleForm, res)
}

onUnmounted(() => {
  videoStore.setScriptText('')
})

onMounted(() => {
  if (route.query.videoId) {
    getWorksTaskDetail()
  }
})
</script>

<style lang="scss" scoped></style>
