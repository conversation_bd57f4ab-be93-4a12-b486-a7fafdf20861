<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 16px;
}

.tabsActive {
  background: linear-gradient(270deg, #d8131a 0%, #fd441f 100%);
}
.borderActive {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  border-image: linear-gradient(270deg, #d8131a 0%, #e96f18 100%) 1;
}
:deep() {
  .el-textarea__inner {
    padding: 22px 12px 12px;
  }
  .text-input {
    .el-input__count {
      bottom: -17px;
    }
  }
}
.form-box {
  height: 100%;
  overflow: hidden;
  overflow: auto;
  padding-bottom: 40px;
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none; /* 对于Webkit浏览器，隐藏滚动条 */
  }
}

.el-button--primary {
  background: linear-gradient(270deg, #d8131a 0%, #e96f18 100%);
  height: 42px;
  border: 0;
}

.video-create-wrapper {
  display: grid;
  grid-template-columns: 356px 1fr;
  gap: 16px;
  height: 100%;
}
</style>

<template>
  <div class="video-create-wrapper" v-loading="hcLoading" :element-loading-text="t('loading')">
    <div class="content-between p-4 bg-white overflow-hidden relative">
      <div class="form-box">
        <el-form ref="ruleFormRef" :rules="rules" :model="ruleForm">
          <!-- 视频名称 -->
          <el-form-item prop="name">
            <Title :title="t('field1')" />
            <el-input v-model="ruleForm.name" :placeholder="t('field1Placeholder')" />
          </el-form-item>
          <!-- 背景图 -->
          <el-form-item prop="backgroundFileUrl">
            <Title :title="t('field2')" />
            <ImgUpload
              v-model="ruleForm.backgroundFileUrl"
              :tipsText="t('field2Placeholder')"
              :dir="OSS_DIR.AI_HUMAN_VIDEO_CREATION"
              height="151px"
              width="324px"
              :isWHSize="true"
            >
            </ImgUpload>
          </el-form-item>
          <!-- 数字人 -->
          <el-form-item prop="modelId">
            <Title :title="t('field3')" :rightTips="t('field3Placeholder')" />
            <NumberHuman ref="numberHumanDialogRef" :humanList="humanList" @on-select-avatar="onSelectAvatar" />
            <el-select v-model="ruleForm.metaHumanLocation" placeholder="请选择位置">
              <el-option v-for="item in NUMBER_HUMAN_POSITION_ARRAY" :key="item.id" :label="item.name[$i18n.locale]" :value="item.id" />
            </el-select>
          </el-form-item>
          <!-- 选择语种 -->
          <el-form-item prop="inferTextLanguage">
            <Title :title="t('field4')" :bottomTips="t('field4Placeholder')"></Title>
            <el-select v-model="ruleForm.inferTextLanguage" placeholder="请选择语种">
              <el-option v-for="item in LANGUAGE_DATA_ARRAY" :key="item.id" :label="item.name[$i18n.locale]" :value="item.id" />
            </el-select>
          </el-form-item>
          <!-- 文本创建 -->
          <el-form-item :prop="ruleForm.inferType === 1 ? 'inferText' : 'audioFilePath'">
            <Title :title="t('field5')" />
            <div class="w-full p-[4px] bg-[#F5F6F7] flex rounded mb-[12px]">
              <div
                v-for="item in INFER_TYPE_ARRAY"
                :key="item.id"
                class="flex-1 text-center cursor-pointer text-[14px] text-[#666666]"
                :class="{
                  'tabsActive text-white font-semibold rounded-[2px]': ruleForm.inferType === item.id,
                }"
                @click="handleInferChange(item.id)"
              >
                {{ item.name[$i18n.locale] }}
              </div>
            </div>
            <!-- 文字输入框 -->
            <div v-if="ruleForm.inferType === 1" class="relative w-full text-input">
              <el-input
                v-model="ruleForm.inferText"
                maxlength="300"
                type="textarea"
                show-word-limit
                :placeholder="t('field5Placeholder')"
                :rows="5"
                resize="none"
              />
              <div
                class="absolute right-0 top-0 bg-[#257BFB] rounded-tr-[4px] rounded-bl-[4px] px-[8px] py-[2px] text-white cursor-pointer text-[12px] leading-[20px]"
                v-loading="optimizeScriptLoading"
                @click="onOptimizeScript"
              >
                {{ t('field5Tip') }}
              </div>
            </div>
            <!-- 上传语音 -->
            <template v-else>
              <ImgUpload
                v-model="ruleForm.audioFilePath"
                :isAudio="true"
                :tipsText="t('field5Upload')"
                accept="audio/mp3,audio/wav,audio/mpeg"
                dir="ai_human_video_translation"
              />
            </template>
          </el-form-item>
          <!-- 语音变声 -->
          <el-form-item prop="voiceModelId">
            <audio ref="audioPlayer" @loadedmetadata="getDuration" />
            <Title :title="t('field6')" />
            <ul class="flex flex-wrap">
              <li
                v-for="(item, index) in playList"
                :key="item.voiceModelId"
                class="cursor-pointer border border-solid border-[#EDEEF1] min-w-[56px] text-center rounded-[4px] px-[12px] mr-[4px] mb-[4px] text-[14px]"
                :class="{ 'borderActive rounded-[4px] text-[#D8131A]': currentTrackIndex === index }"
                @click="playTrack(item, index)"
              >
                {{ item.voiceModelName }}
              </li>
            </ul>
          </el-form-item>
        </el-form>
      </div>
      <div class="absolute bottom-0 left-0 right-0 m-auto w-[324px] text-center">
        <el-button class="w-[100%] h-14 bg-gradient-to-r from-purple-500 to-pink-500" type="primary" @click="onSynthesisVideo">{{ t('submit') }}</el-button>
      </div>
    </div>
    <div class="bg-white grid place-content-center relative">
      <!-- 没有上传视频 -->
      <div v-if="ruleForm.backgroundFileUrl.length === 0" class="text-center text-[24px]">
        <div class="mb-3 text-[#333333] font-bold">{{ t('tip') }}</div>
        <div class="max-w-[700px]">
          <video
            class="w-[100%]"
            muted
            autoplay="autoplay"
            loop="loop"
            src="https://static.chinamarket.cn/static/trade-exhibition/ai-feature-intro/digital-spokesperson-new.mp4"
            disablePictureInPicture
          ></video>
        </div>
      </div>
      <!-- 上传是后展示 -->
      <div v-else class="text-center text-[20px]">
        <div class="absolute top-4 right-4 cursor-pointer" @click="operateGuide">
          <icon type="icon-caozuozhiyin" size="48"></icon>
          <div class="mt-1 text-sm text-#666">{{ t('guide') }}</div>
        </div>
        <div class="w-200 max-h-[440px] relative">
          <img class="w-full max-h-[440px]" :src="ruleForm.backgroundFileUrl[0]" />
          <img
            class="w-[300px] h-[300px] absolute bottom-[10px] z-10"
            :class="{
              'left-0': ruleForm.metaHumanLocation === 2,
              'right-0': ruleForm.metaHumanLocation === 3,
              'left-[50%] translate-x-[-50%]': ruleForm.metaHumanLocation === 1,
            }"
            :src="ruleForm.avatar"
            alt=""
          />
        </div>
      </div>
    </div>
    <OperateGuide ref="operateGuideRef" />
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import OperateGuide from '@/pc/views/pages/ai-features/video-translation/operate-guide-dialog.vue'
import NumberHuman from './components/number-human.vue'
import Title from './components/title.vue'
import { INFER_TYPE_ARRAY, LANGUAGE_DATA_ARRAY, NUMBER_HUMAN_POSITION_ARRAY } from '@/constants/index'
import { OSS_DIR } from '@/constants/oss-dir'
import { useHumanModel } from '@/pc/hooks/aiHumanModel'
import * as videoApi from '@/apis/ai-features'
import { useEvent } from '@/event'
import { LOGIN_SUCCESS } from '@/event/modules/site'

const { t } = useI18n({
  messages: {
    zh: {
      field1: '视频名称',
      field1Placeholder: '请输入视频名字',
      field2: '背景图',
      field2Placeholder: '支持格式PNG、JPG，最多上传1张图片，图片尺寸大于等于1080*1080，图片大小限制在10M以内',
      field2Tip: '请上传背景',
      field3: '数字人',
      field3Placeholder: '选择数字人后，自定义选择数字人所在的区域',
      field4: '选择语种',
      field4PlaceholderShort: '请选择要翻译的目标语种',
      field4Placeholder: '请选择要翻译的目标语种，待下方输入文本后可生成语音翻译',
      field5: '文本创建',
      field5Options: ['文字输入', '上传语音'],
      field5Tip: '智能优化文字',
      field5Placeholder: '请输入数字人将要说出的文字',
      field5Upload: '支持格式MP3/WAV格式，大小限制在10M以内',
      field5UploadTip: '请上传语音文件',
      field6: '语音变声',
      submit: '立即合成视频',
      tip: '视频创作步骤',
      loading: '数字人制作中...',
      guide: '操作指引',
    },
    en: {
      field1: 'Video Name',
      field1Placeholder: 'Please enter the video name',
      field2: 'Background Image',
      field2Placeholder: 'Supported formats: PNG, JPG. You can upload up to 1 image, with a resolution of at least 1080 pixels.',
      field2Tip: 'Please upload background image',
      field3: 'Digital human',
      field3Placeholder: 'After selecting the virtual avatar, customize and select the region where the avatar will be placed.',
      field4: 'Select language',
      field4PlaceholderShort: 'Please select the target language for translation',
      field4Placeholder: 'Please select the target language for translation. Once the text is entered below, a voice translation can be generated.',
      field5: 'Script',
      field5Tip: 'Intelligent text optimization',
      field5Placeholder: 'Please enter the text that the virtual avatar will speak.',
      field5Upload: 'Supported formats: MP3/WAV, with a size limit of 10MB.',
      field5UploadTip: 'Please upload the audio file.',
      field6: 'Voice modulation',
      submit: 'Generate Now',
      tip: 'Video creation steps',
      loading: 'Generating...',
      guide: 'Guide',
    },
  },
})
const event = useEvent()

const { getHumanList, humanList } = useHumanModel()

const rules = {
  name: [{ required: true, message: computed(() => t('field1Placeholder')), trigger: 'blur' }],
  backgroundFileUrl: { required: true, message: computed(() => t('field2Tip')), trigger: 'blur' },
  modelId: [{ required: true, message: '请选择数字人', trigger: 'blur' }],
  voiceModelId: [{ required: true, message: '请选择语音变声', trigger: 'blur' }],
  inferText: [{ required: true, message: computed(() => t('field5Placeholder')), trigger: 'blur' }],
  audioFilePath: [{ required: true, message: computed(() => t('field5UploadTip')), trigger: 'blur' }],
}

const router = useRouter()
const route = useRoute()

// 文本创建 tabs 切换
const handleInferChange = (id) => {
  ruleForm.inferType = id
}

// 语言变声
const currentTrackIndex = ref(0)

// 左侧表单
const ruleForm = reactive({
  name: '', // 视频名称
  backgroundFileUrl: '', // 视频背景
  modelId: '', // 数字人 id
  metaHumanLocation: 1, // 数字人位置
  inferTextLanguage: '1', // 选择语种
  inferType: 1, // 文本创建 tabs切换，1：文字输入，2：上传语音
  inferText: '', // 文字输入
  audioFilePath: '', // 上传语音 url 连接
  voiceModelId: '', // 语音变声 id
  radioSound: new URL('@/assets/audio/default.mp3', import.meta.url).href,
})
// 数字人语音变声列表
const playList = reactive([])
const getPlayList = async () => {
  try {
    let res = await videoApi.listVoiceModel({ templateType: null })
    Object.assign(ruleForm, {
      voiceModelId: res[0].voiceModelId,
    })
    playList.length = 0
    playList.push(...res)
  } catch (error) {
    console.log(error)
  }
}
getPlayList()

// 点击播放
const audioPlayer = ref(null)
const playTrack = (item, index) => {
  currentTrackIndex.value = index
  audioPlayer.value.src = playList[index].demoAudioUrl
  audioPlayer.value.play()
  Object.assign(ruleForm, { voiceModelId: item.voiceModelId })
}

// 获取音频信息
const getDuration = (e) => {
  console.log(e)
}

// 确定切换数字人
const onSelectAvatar = (info) => {
  Object.assign(ruleForm, { avatar: info.modelPic, modelId: info.modelId })
}

// 点击智能优化文字
const optimizeScriptLoading = ref(false)
const onOptimizeScript = async () => {
  if (!ruleForm.inferText) {
    ElMessage.warning(t('field5Placeholder'))
    return
  }
  if (!ruleForm.inferTextLanguage) {
    ElMessage.warning(t('field4PlaceholderShort'))
    return
  }
  optimizeScriptLoading.value = true
  try {
    let res = await videoApi.optimizeDescription({ message: ruleForm.inferText })
    ruleForm.inferText = res
    optimizeScriptLoading.value = false
  } catch (error) {
    console.log(error)
  } finally {
    optimizeScriptLoading.value = false
  }
}

// 合成视频 loading
const hcLoading = ref(false)

// 点击合成视频
const ruleFormRef = ref(null)
const onSynthesisVideo = async () => {
  await ruleFormRef.value.validate(async (valid) => {
    if (!valid) return
    hcLoading.value = true
    try {
      const params = {
        ...ruleForm,
        backgroundFileUrl: ruleForm.backgroundFileUrl[0],
        audioFilePath: ruleForm.audioFilePath[0],
      }

      await videoApi.generateVideo(params)
      router.push('/ai-features/short-video')
    } finally {
      hcLoading.value = false
    }
  })
}

// 点击试听
// const audioLoading = ref(false)
// const onAuditionAudioByText = async () => {
//   if (!ruleForm.inferText) {
//     ElMessage.warning('请输入文字')
//     return
//   }
//   if (!ruleForm.inferTextLanguage) {
//     ElMessage.warning('请选择选择语言')
//     return
//   }
//   if (!voiceModelId.value) {
//     ElMessage.warning('请选择语音变声')
//     return
//   }
//   audioLoading.value = true
//   const params = {
//     inferText: ruleForm.inferText,
//     inferTextLanguage: ruleForm.inferTextLanguage,
//     voiceModelId: voiceModelId.value,
//   }
//   try {
//     const res = await videoApi.auditionAudioByText(params)
//     audioPlayer.value.src = res
//     audioPlayer.value.play()
//   } finally {
//     audioLoading.value = false
//   }
// }

// 获取视频详情
const getWorksTaskDetail = async () => {
  try {
    let res = await videoApi.worksTaskDetail(route.query.videoId)
    if (typeof res.backgroundFileUrl === 'string') {
      res.backgroundFileUrl = [res.backgroundFileUrl]
    }
    Object.assign(ruleForm, res)
  } catch (error) {
    console.log(error)
  }
}

onUnmounted(() => {
  // videoStore.setScriptText('')
})

onMounted(async () => {
  if (route.query.videoId) {
    getWorksTaskDetail()
  }
  // 获取数字人模版
  await getHumanList()
  // 默认选中第一个数字人
  Object.assign(ruleForm, {
    avatar: humanList[0]?.modelPic || '',
    modelId: humanList[0]?.modelId || '',
  })
})
// 操作指引弹窗
const operateGuideRef = ref(false)
const operateGuide = () => {
  operateGuideRef.value.init('numberHuman')
}

event.on(LOGIN_SUCCESS, async () => {
  await getHumanList()
})
</script>

<style lang="scss" scoped></style>
