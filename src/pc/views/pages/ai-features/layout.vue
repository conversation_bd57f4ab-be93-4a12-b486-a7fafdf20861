<style lang="scss" scoped>
.ai-features {
  height: calc(100vh - 64px);
  min-height: 680px;
}
.feature-list {
  overflow-y: auto;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 260px;
  height: 100%;
  padding: 20px;
  background-color: #fff;
}
.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border: 1px solid #edeef1;
  border-radius: 8px;
  background-image: linear-gradient(to right, #ffeded 0%, #fffcfc 50%, #ffffff 100%);
  background-size: 200%;
  background-position: 100%;
  transition: all 0.3s;
  cursor: pointer;
  user-select: none;

  &.hl {
    border-color: #d8131a;
    background-position: 0;

    .menu-label,
    .menu-desc,
    .icon {
      color: #d8131a;
    }
  }

  .menu-label {
    font-weight: 600;
    font-size: 16px;
    color: #333;
  }
  .menu-desc {
    margin-top: 8px;
    font-size: 12px;
    color: #999;
  }

  .icon {
    height: 48px;
  }
}
</style>

<template>
  <div class="ai-features flex">
    <div class="feature-list">
      <div
        v-for="menu in menusList"
        :key="menu.label"
        class="feature-item"
        :class="{
          hl: menu.path === route.path,
        }"
        @click="menuClick(menu)"
      >
        <img class="icon" :src="`https://static.chinamarket.cn/static/trade-exhibition/ai-features-menu/${menu.icon}`" draggable="false" />
        <div>
          <div class="menu-label">{{ menu.label[$i18n.locale] }}</div>
          <div class="menu-desc">{{ menu.desc[$i18n.locale] }}</div>
        </div>
      </div>
    </div>
    <div class="h-full grow p-4 bg-#F6F7FA">
      <router-view />
    </div>
  </div>
</template>

<script setup>
import { useEvent } from '@/event'
import { OPEN_AI_WINDOW } from '@/event/modules/site'

const router = useRouter()
const route = useRoute()
const event = useEvent()

const menusList = reactive([
  {
    label: { zh: '视频翻译', en: 'Video Translation' },
    desc: { zh: '提供多国语言视频翻译', en: 'Provides multilingual video translation' },
    path: '/ai-features/video-translation',
    icon: '视频翻译.png',
  },
  {
    label: { zh: '数字代言人', en: 'Digital Spokesperson' },
    desc: { zh: '系统自带模特数字人', en: 'Built-in digital human models' },
    path: '/ai-features/person-square',
    icon: '数字代言人.png',
  },
  {
    label: { zh: '自主创作', en: 'Self-Creation' },
    desc: { zh: '数字人模特+文案=视频', en: 'Digital human models + script = video' },
    path: '/ai-features/video-create',
    icon: '自主创作.png',
  },
  {
    label: { zh: 'AI商品图', en: 'AI Product Images' },
    desc: { zh: '抠图', en: 'Background removal' },
    icon: 'AI商品图.png',
    path: '/ai-features/product-picture',
  },
  {
    label: { zh: 'AI翻译官', en: 'AI Translator' },
    desc: { zh: '支持翻译多国语言', en: 'Supports multilingual translation' },
    icon: 'AI翻译官.png',
    onClick() {
      event.emit(OPEN_AI_WINDOW, {
        agent: 'SC_TRANSLATION_BOT',
      })
    },
  },
  {
    label: { zh: '内容管理', en: 'Content Management' },
    desc: { zh: '管理生成的内容', en: 'Manage generated content' },
    icon: '内容管理.png',
    path: '/ai-features/short-video',
  },
])

const menuClick = (menu) => {
  if (typeof menu.onClick === 'function') {
    return menu.onClick()
  }
  router.push(menu.path)
}
</script>
