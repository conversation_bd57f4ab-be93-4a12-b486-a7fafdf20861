<template>
  <div class="w-full">
    <!-- input -->
    <input class="hidden" ref="inputRef" :key="inputKey" type="file" :accept="accept" @change="handleInputChange" />
    <!-- 已经上传文件 -->
    <div v-for="(item, index) in filesList" :key="item.id" :style="{ height: height, width: width }" class="relative mr-[8px] rounded-[2px] p-[10px] bg-white">
      <!-- 图片类型 -->
      <el-image :ref="setImageRef" class="h-full" :style="{ width: imgWidth }" fit="cover" :src="item.url" :preview-src-list="[item.url]" />
      <div class="absolute top-0 left-0 p-[10px] z-20 w-full h-full group">
        <div class="hidden w-full h-full text-center leading-[150px] hover:bg-[rgba(0,0,0,.5)] text-white group-hover:block">
          <Icon class="img-upload-preview-icon cursor-pointer" type="icon-yulan" size="32" title="预览" @click="preview(item, index)" />
          <Icon
            v-if="isDelete || !disabled"
            class="img-upload-delete-icon cursor-pointer ml-[16px]"
            type="icon-shanchu"
            size="32"
            title="删除"
            @click="removeFile(item)"
          />
        </div>
      </div>
      <slot name="people" />
    </div>

    <!-- 未上传文件 -->
    <div
      v-if="(!modelValue || (modelValue && modelValue.length < limit)) && showUploadButton"
      v-loading="uploading"
      class="rounded-[4px] border border-dashed border-[#EDEEF1] flex items-center py-[24px] text-center cursor-pointer bg-[#F7F7F7]"
      :class="{
        'border-[2px]': dragover,
        'cursor-not-allowed': !uploadable,
      }"
      :style="{ height: height, width: width }"
      @click="handleClick"
      @dragover.prevent="onDragover"
      @drop.prevent="onDrop"
      @dragleave.prevent="dragover = false"
    >
      <div>
        <Icon type="icon-shangchuan" :size="plusSize" />
        <slot name="empty">
          <div class="text-[14px] text-[#333333] h-[20px] leading-5 mb-[4px]">{{ emptyText ?? $t('imgUpload.emptyText') }}</div>
        </slot>
        <slot name="tips">
          <div class="text-[12px] px-[60px] text-[#999999] leading-normal">{{ tipsText ?? $t('imgUpload.tipsText') }}</div>
        </slot>
      </div>
    </div>
  </div>

  <!-- 图片预览组件 -->
  <ImgViewer v-model:visible="showImageViewer" :url-list="previewUrlList" :initial-index="imgViewerIndex" :show-mini-image="true" :z-index="9999" />
</template>

<script setup>
import { ElMessage, ElMessageBox, useDisabled, useFormItem } from 'element-plus'
import { useI18n } from 'vue-i18n'
import ImgViewer from '@/pc/components/img-viewer/img-viewer.vue'
import { OSS_DIR } from '@/constants/oss-dir'
import { randomString } from '@/common/js/util'
import { upload } from '@/utils/oss'

const { t } = useI18n({
  messages: {
    zh: {
      delConfirm: '确定要删除吗？',
      delTip: '删除',
      delConfirmBtn: '确定',
      delCancelBtn: '取消',
      delSuccess: '删除成功',
      error: {
        pixelSize: '请上传至少为 {wSize}x{wSize} 尺寸的图片',
        sizeLimit: '上传的文件大小不能超过 {sizeLimit}M',
        unsupport: '不支持该文件类型',
        uploadFail: '上传失败',
      },
    },
    en: {
      delConfirm: 'Are you sure you want to delete it?',
      delTip: 'Delete',
      delConfirmBtn: 'Confirm',
      delCancelBtn: 'Cancel',
      delSuccess: 'Delete success',
      error: {
        pixelSize: 'Please upload at least {wSize}x{wSize} size image',
        sizeLimit: 'The file size cannot exceed {sizeLimit}M',
        unsupport: 'Unsupported file type',
        uploadFail: 'Upload failed',
      },
    },
  },
})

// 表单 item
const elFormItem = useFormItem()
// 是否禁用
const disabled = useDisabled()
// 传给父组件的事件
const emits = defineEmits(['update:modelValue', 'change'])

// 图片预览组件
const showImageViewer = ref(false)
const imgViewerIndex = ref(0)
// 预览图片 url 数组
const previewUrlList = computed(() => {
  if (typeof props.modelValue === 'string') {
    return props.modelValue ? [props.modelValue] : []
  } else {
    return props.modelValue
  }
})

const props = defineProps({
  // 文件的 src，没有图片时为空字符串
  modelValue: {
    type: [Array, String],
    require: true,
  },
  // 未上传文件时的提示文字，也可用 empty slot
  emptyText: {
    type: String,
  },
  // 未上传提示的支持上传文件文案，也可以使用 tips slot
  tipsText: {
    type: String,
  },
  // 文件类型
  accept: {
    type: String,
    default: 'image/jpe,image/jpeg,image/png',
  },
  // 是否显示删除按钮
  isDelete: {
    type: Boolean,
    default: false,
  },
  // 文件大小限制
  sizeLimit: Number,
  // 上传前的回调函数，返回 false 可以阻止上传
  beforeUpload: Function,
  onSuccess: Function,
  // 限制文件上传数量
  limit: {
    type: Number,
    default: 1,
  },
  // 上传按钮显示,详情回现可以隐藏
  showUploadButton: {
    type: Boolean,
    default: true,
  },
  width: {
    type: [Number, String],
    default: '100%',
  },
  height: {
    type: [Number, String],
    default: '100%',
  },
  // icon 样式大小
  plusSize: {
    type: Number,
    default: 20,
  },
  // 上传文件夹
  dir: {
    // required: true,
    type: String,
    validator(dir) {
      const isValid = Object.values(OSS_DIR).some((d) => d === dir)
      if (import.meta.env.DEV && !isValid) {
        const keys = Object.keys(OSS_DIR)
          .map((d) => `OSS_DIR.${d}`)
          .join('、')
        // eslint-disable-next-line no-console
        console.error(`[img-upload]dir 属性必须为 ${keys} 其中一个`)
      }
      return isValid
    },
  },
  // 是否验证文件宽高尺寸
  isWHSize: {
    type: Boolean,
    default: false,
  },
  // 验证文件宽高尺寸
  wSize: {
    type: Number,
    default: 1080,
  },
  // 图片的宽度
  imgWidth: {
    type: [Number, String],
    default: '100%',
  },
})

const dragover = ref(false) // 是否鼠标悬浮
const tempUrl = ref('') // 通过 URL.createObjectURL 创建的临时 url
const inputKey = ref(0) // input 的 key
const inputRef = ref(null) // input 元素的 ref
const uploading = ref(false) // 是否正在上传

// 销毁组件前，回收 url
onBeforeUnmount(() => {
  tempUrl.value && URL.revokeObjectURL(tempUrl.value)
})

// 当前是否可以上传
const uploadable = computed(() => !disabled.value && !uploading.value)

// 上传文件地址
const filesList = computed(() => {
  if (!props.modelValue) return
  let tempList = props.modelValue
  // 如果是字符串，转换为数组
  if (typeof props.modelValue === 'string') {
    tempList = tempList.split(',')
  }
  const list = tempList.map((url) => {
    const markIndex = url.lastIndexOf('_') || url.lastIndexOf('/')
    const lastIndex = markIndex + 1
    const fileName = url.substring(lastIndex)
    return {
      url,
      name: fileName,
      id: randomString(8),
    }
  })
  return list
})

watch(
  () => props.modelValue,
  (val) => {
    // 触发表单校验
    elFormItem.formItem && elFormItem.formItem.validate('change')
    if (!val) {
      clearFile()
    }
  },
)

// 验证图片尺寸
const checkImageSize = async (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    const img = new Image()

    reader.onload = () => {
      img.src = reader.result
      img.onload = () => {
        const width = img.width
        const height = img.height
        if (width < props.wSize || height < props.wSize) {
          // 错误消息应该明确指出是尺寸太小
          ElMessage.error(
            t('error.pixelSize', {
              wSize: props.wSize,
            }),
          )
          reject(new Error('图片尺寸太小'))
        } else {
          resolve(true)
        }
      }

      img.onerror = () => {
        // 处理图片加载错误
        reject(new Error('图片加载失败'))
      }
    }

    reader.onerror = () => {
      // 处理文件读取错误
      reject(new Error('文件读取失败'))
    }

    reader.readAsDataURL(file)
  })
}

// 验证文件
const validateFiles = (files) => {
  if (props.accept) {
    files = [].slice.call(files).filter((file) => {
      const { type, name } = file
      const extension = name.indexOf('.') > -1 ? `.${name.split('.').pop()}` : ''
      const baseType = type.replace(/\/.*$/, '')
      return props.accept
        .split(',')
        .map((t) => t.trim())
        .filter(Boolean)
        .some((acceptedType) => {
          if (/\..+$/.test(acceptedType)) {
            return extension === acceptedType
          }
          if (/\/\*$/.test(acceptedType)) {
            return baseType === acceptedType.replace(/\/\*$/, '')
          }
          if (/^[^/]+\/[^/]+$/.test(acceptedType)) {
            return type === acceptedType
          }
          return false
        })
    })
    if (!files.length) {
      ElMessage.warning(t('error.unsupport'))
    }
  }
  if (!files || !files.length) return
  const file = files[0]
  if (props.sizeLimit) {
    const fileSize = file.size / 1024 / 1024
    if (fileSize >= props.sizeLimit) {
      ElMessage.error(
        t('error.sizeLimit', {
          sizeLimit: props.sizeLimit,
        }),
      )
      clearFile() // 上传完情况，解决重复上传文件不出发change事件
      return
    }
  } else if (typeof props.beforeUpload === 'function' && !props.beforeUpload(file)) {
    return
  }
  return file
}

// 点击上传文件
const handleInputChange = async (e) => {
  const { files } = e.target
  if (!files || files.length === 0) return
  // 验证图片宽高尺寸
  if (props.isWHSize) {
    let res = await checkImageSize(files[0])
    if (!res) return
  }
  uploadFiles(files)
}

// 上传文件方法
const uploadFiles = async (files) => {
  const fileToUpload = validateFiles(files)
  if (!fileToUpload) {
    return
  }
  uploading.value = true
  elFormItem.formItem && elFormItem.formItem.clearValidate()
  try {
    const url = await upload(fileToUpload, props.dir)
    try {
      // onSuccess 可以返回一个 promise，如果返回 promise，上传结果会取决于 promise 最终状态
      typeof props.onSuccess === 'function' && (await props.onSuccess(url, files[0]))
      let fileDataArr = []
      if (props.modelValue && props.modelValue.length > 0) {
        let list = JSON.parse(JSON.stringify(props.modelValue))
        fileDataArr = [...list, url]
      } else {
        fileDataArr = [url]
      }
      emits('update:modelValue', fileDataArr)
      emits('change', fileDataArr)
    } catch (e) {
      // onSuccess 函数报错
    }
  } catch (e) {
    ElMessage.error(t('error.uploadFail'))
  } finally {
    uploading.value = false
    clearFile() // 上传完情况，解决重复上传文件不出发change事件
  }
}

// 点击触发原生的 input click 事件
const handleClick = () => {
  if (uploadable.value) {
    inputRef.value.click()
  }
}

// 清除上传
const clearFile = () => {
  inputRef.value && (inputRef.value.value = '')
  // IE10 下 input.value = '' 无效，故在此将其重新渲染
  if (inputRef.value?.value) {
    inputKey.value++
  }
}

// 拖拽时鼠标悬浮
const onDragover = () => {
  if (uploadable.value) {
    dragover.value = true
  }
}

// 拖拽释放鼠标
const onDrop = async (e) => {
  // 验证图片宽高尺寸
  if (props.isWHSize) {
    let res = await checkImageSize(e.dataTransfer.files[0])
    if (!res) return
  }
  if (!uploadable.value) return
  dragover.value = false
  uploadFiles(e.dataTransfer.files)
}

let imageRefs = []
// 动态设置el-image组件的ref
const setImageRef = (el) => {
  let srcArr = imageRefs.map((item) => item.src)
  if (el && !srcArr.includes(el.src)) {
    imageRefs.push(el)
  }
}

// 打开图片预览
const preview = (info, i) => {
  imgViewerIndex.value = i
  showImageViewer.value = true
}

// 删除文件
const removeFile = (info) => {
  ElMessageBox.confirm(t('delConfirm'), t('delTip'), {
    confirmButtonText: t('delConfirmBtn'),
    cancelButtonText: t('delCancelBtn'),
    type: 'warning',
  })
    .then(() => {
      clearFile()
      const list = JSON.parse(JSON.stringify(filesList.value))
      const temp = list.filter((item) => item.id !== info.id)
      const tempUrls = temp.map((item) => item.url)
      emits('update:modelValue', tempUrls)
      emits('change', tempUrls)
      ElMessage.success(t('delSuccess'))
    })
    .catch(() => {
      // 取消删除
    })
}
</script>
