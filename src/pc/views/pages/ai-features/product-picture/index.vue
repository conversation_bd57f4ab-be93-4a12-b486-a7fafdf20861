<template>
  <div class="product-picture">
    <div class="shrink-0 basis-[356px] flex flex-col justify-between p-4 bg-white">
      <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules">
        <!-- 图片名称 -->
        <el-form-item prop="name">
          <Title :title="t('field1')" />
          <el-input v-model="ruleForm.name" :placeholder="t('field1Placeholder')" prop="name"></el-input>
        </el-form-item>
        <el-form-item prop="productPicture">
          <Title :title="t('field2')" :rightTips="t('field2Tip')" />
          <ProductUpload
            v-model="ruleForm.productPicture"
            :tipsText="t('field2Placeholder')"
            :dir="OSS_DIR.AI_HUMAN_VIDEO_CREATION"
            height="151px"
            :on-success="imageMatting"
          >
          </ProductUpload>
        </el-form-item>
        <el-form-item prop="backgroundPicture">
          <Title :title="t('field3')" :rightTips="t('field3Tip')" />
          <ProductUpload
            v-model="ruleForm.backgroundPicture"
            :tipsText="t('field2Placeholder')"
            :dir="OSS_DIR.AI_HUMAN_VIDEO_CREATION"
            height="151px"
            :on-success="addBackgroundImage"
          >
          </ProductUpload>
        </el-form-item>
      </el-form>
      <el-button class="h-14 bg-gradient-to-r from-purple-500 to-pink-500" type="primary" @click="exportImage">
        {{ t('submit') }}
      </el-button>
    </div>

    <div class="grow bg-white">
      <!-- 没有上传图片 -->
      <div v-show="ruleForm.productPicture.length > 0 || ruleForm.backgroundPicture.length > 0" class="h-full flex flex-col items-center">
        <div class="self-end my-4 mr-4 text-center" @click="operateGuide">
          <icon type="icon-caozuozhiyin" size="48"></icon>
          <div class="mt-1 text-sm text-#666">{{ t('guide') }}</div>
        </div>
        <canvas ref="canvasContainer" width="512" height="512"></canvas>
      </div>
      <!-- 上传了视频 -->
      <div v-show="ruleForm.productPicture.length === 0 && ruleForm.backgroundPicture.length === 0" class="h-full flex flex-col justify-center">
        <div class="mb-3 text-[#333333] font-bold text-center text-[20px]">{{ t('title') }}</div>
        <video
          class="w-full"
          style="height: 512px"
          muted
          autoplay
          loop
          src="https://static.chinamarket.cn/static/trade-exhibition/ai-feature-intro/更换图片.mp4"
          disablePictureInPicture
        ></video>
      </div>
    </div>

    <OperateGuide ref="operateGuideRef"></OperateGuide>
  </div>
</template>
<script setup>
import axios from 'axios'
import { Canvas, FabricImage, Rect } from 'fabric'
import { useI18n } from 'vue-i18n'
import Title from '../video-create/components/title.vue'
import OperateGuide from '../video-translation/operate-guide-dialog.vue'
import ProductUpload from './product-upload.vue'
import { OSS_DIR } from '@/constants/oss-dir'

const { t } = useI18n({
  messages: {
    zh: {
      title: 'AI商品图',
      field1: '图片名称',
      field1Placeholder: '请输入图片名称',
      field2: '商品图',
      field2Tip: '自动扣除商品之外的背景',
      field2Placeholder: '支持格式PNG、JPG，最多上传1张图片，图片尺寸大于等于1080*1080，图片大小限制在10M以内',
      field2RuleTip: '请上传商品图',
      field3: '背景图',
      field3Tip: '上传即可拼接图片',
      field3Placeholder: '支持格式PNG、JPG，最多上传1张图片，图片尺寸大于等于1080*1080，图片大小限制在10M以内',
      field3RuleTip: '请上传背景图',
      submit: '下载',
      downloadSuccess: '下载成功！',
      guide: '操作指引',
    },
    en: {
      title: 'Ai Product Image',
      field1: 'Image Name',
      field1Placeholder: 'Please enter the image name',
      field2: 'Product Image',
      field2Tip: 'Automatically remove the background apart from the product.',
      field2Placeholder: 'Supported formats: PNG, JPG. You can upload up to 1 image, with a resolution of at least 1080 pixels.',
      field2RuleTip: 'Please upload product image',
      field3: 'Background Image',
      field3Tip: 'Upload to instantly stitch images together.',
      field3RuleTip: 'Please upload background image',
      submit: 'Download',
      downloadSuccess: 'Download successful!',
      guide: 'Guide',
    },
  },
})

const operateGuideRef = ref(false)
const operateGuide = () => {
  operateGuideRef.value.init('productPicture')
}

// 左侧表单
const ruleForm = reactive({
  name: '', // 图片名称
  productPicture: '', // 商品图
  backgroundPicture: '', // 背景图
})

const rules = {
  name: [{ required: true, message: computed(() => t('field1Placeholder')), trigger: 'blur' }],
  productPicture: { required: true, message: computed(() => t('field2RuleTip')), trigger: 'change' },
  backgroundPicture: [{ required: true, message: computed(() => t('field3RuleTip')), trigger: 'change' }],
}

// 上传完毕回调 传入 一个 form-data 格式的图片 给后台处理
const imageMatting = async (val, file) => {
  try {
    const mattingUrl = import.meta.env.VUE_APP_MATTING_API_URL
    const res = await axios.post(
      `${mattingUrl}/matting/upload`,
      { file },
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    )
    // 生成canvas
    addProductImage(res.data.file_url)
  } catch (error) {
    console.error(error)
  }
}

//------------------------------------- canvans-------------------------------------
const canvasContainer = ref(null)
let fabricCanvas = null
let backgroundImage = null
let productImage = null

onMounted(() => {
  initCanvas()
})

const initCanvas = () => {
  // 创建 Fabric.js 画布
  fabricCanvas = new Canvas(canvasContainer.value, {
    width: 512,
    height: 512,
    selection: false,
  })
  // 添加栅格
  addGrid()
}

// 栅格背景图
const addGrid = () => {
  const gridSize = 20
  const numCols = Math.ceil(fabricCanvas.width / gridSize)
  const numRows = Math.ceil(fabricCanvas.height / gridSize)

  for (let i = 0; i < numCols; i++) {
    for (let j = 0; j < numRows; j++) {
      const isGray = (i + j) % 2 === 0 // 交替灰色和白色
      const rect = new Rect({
        left: i * gridSize,
        top: j * gridSize,
        fill: isGray ? '#ccc' : '#fff', // 灰色和白色交替
        width: gridSize,
        height: gridSize,
        selectable: false,
        evented: false,
        name: 'grid', // 标记栅格背景
      })
      fabricCanvas.add(rect)
    }
  }
}
// 添加商品图
const loadImage = (src) => {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => resolve(img)
    img.onerror = reject
    img.src = src
    // 设置跨域  不然保存不下来
    img.setAttribute('crossOrigin', 'anonymous')
  })
}
const addProductImage = async (proUrl) => {
  const img = await loadImage(proUrl)
  productImage = new FabricImage(img, {
    left: 0,
    top: 0,
    scaleX: 0.5,
    scaleY: 0.5,
    hasControls: true,
    hasBorders: true,
    lockUniScaling: true,
  })
  fabricCanvas.add(productImage)
  fabricCanvas.setActiveObject(productImage)
}
const addBackgroundImage = async (proUrl) => {
  const img = await loadImage(proUrl)
  const isVerticalRect = img.height >= img.width
  const scale = 512 / (isVerticalRect ? img.width : img.height)
  const offset = ((isVerticalRect ? img.height : img.width) * scale - 512) / 2

  backgroundImage = new FabricImage(img, {
    left: isVerticalRect ? 0 : -offset,
    top: isVerticalRect ? -offset : 0,
    scaleX: scale,
    scaleY: scale,
    evented: false,
  })
  fabricCanvas.add(backgroundImage)
  fabricCanvas.bringObjectToFront(productImage)
}

watch(
  () => ruleForm.productPicture,
  (val) => {
    if (val.length === 0 && productImage) {
      fabricCanvas.remove(productImage)
      productImage = null
    }
  },
)

watch(
  () => ruleForm.backgroundPicture,
  (val) => {
    if (val.length === 0 && backgroundImage) {
      fabricCanvas.remove(backgroundImage)
      backgroundImage = null
    }
  },
)

const ruleFormRef = ref(null)
const exportImage = () => {
  if (!ruleFormRef.value) return
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      if (!fabricCanvas) return
      try {
        // 临时将栅格背景设为不可见
        const gridItems = fabricCanvas.getObjects('rect').filter((rect) => rect.name === 'grid')
        gridItems.forEach((rect) => rect.set({ visible: false }))

        // 生成包含 Canvas 内容的 Data URL
        const dataURL = fabricCanvas.toDataURL({
          format: 'png',
          quality: 1.0,
        })
        // 恢复栅格背景的可见性
        gridItems.forEach((rect) => rect.set({ visible: true }))
        // 创建一个链接元素并点击以下载图像
        const link = document.createElement('a')
        link.href = dataURL
        link.download = ruleForm.name
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        ElMessage.success(t('downloadSuccess'))
      } catch (e) {
        console.error('Failed to export image:', e)
      }
    } else {
      return false
    }
  })
}
</script>

<style lang="scss" scoped>
.product-picture {
  display: flex;
  gap: 16px;
  height: 100%;

  .el-button--primary {
    background: linear-gradient(270deg, #d8131a 0%, #e96f18 100%);
    height: 42px;
    border: 0;
  }

  .el-button.is-disabled,
  .el-button.is-disabled:hover {
    background: linear-gradient(270deg, #d8131a 0%, #e96f18 100%), rgba(255, 255, 255, 0.5);
    cursor: not-allowed;
    opacity: 0.5;
  }
}
</style>
