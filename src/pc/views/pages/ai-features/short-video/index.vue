<!-- 短视频管理 -->
<style scoped lang="scss">
:deep() {
  .el-button.el-button--primary.is-link {
    span {
      color: #257bfb !important;
    }
  }
}

.short-table-wrapper {
  max-height: 70vh !important;
  overflow: hidden;
  overflow: auto;
}

.tabsActive {
  background: linear-gradient(270deg, #d8131a 0%, #fd441f 100%);
}
</style>

<template>
  <div class="bg-white p-4 w-full h-full">
    <div class="w-fit p-[4px] bg-[#F5F6F7] flex gap-2 rounded mb-[16px]">
      <div
        v-for="item in statusBtnList"
        :key="item.id"
        class="min-w-[58px] px-2 text-center leading-[32px] text-[14px] text-[#666666] cursor-pointer"
        :class="{
          'tabsActive text-white font-semibold rounded-[2px]': activeName === item.id,
        }"
        @click="handleClick(item)"
      >
        {{ item.name[$i18n.locale] }}
      </div>
    </div>
    <el-table border :data="videoList" style="width: 100%" max-height="500">
      <el-table-column prop="ruleForm" :label="t('col1')">
        <template #default="{ row }">
          <span>{{ row.name || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" :label="t('col2')">
        <template #default="{ row }">
          <span :style="{ color: statusColor[row.status] }">{{ VIDEO_STATUS_MAP[row.status][$i18n.locale] }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="createdAt" :label="t('col3')">
        <template #default="{ row }">
          <span>{{ formatTime(row.createdAt) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="t('col4')">
        <template #default="{ row }">
          <el-button v-if="row.status === 1" link type="primary" @click="previewVideo(row)">{{ t('operate[0]') }}</el-button>
          <el-button v-if="row.status === 1" link type="primary" @click="exportData(row)">{{ t('operate[1]') }}</el-button>
          <el-button v-if="row.status === 2" link type="primary" @click="onRegenerate(row)">{{ t('operate[2]') }}</el-button>
          <span v-if="row.status === 0 || row.status === 3">-</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="pt-[20px] flex justify-center">
      <el-pagination
        background
        v-model:current-page="pageNum"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        :page-sizes="[10, 20, 30, 40]"
        popper-class="aaaaa"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
  <!-- 视频预览 -->
  <PreviewVideoDialog ref="previewVideoDialogRef" />
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import PreviewVideoDialog from '@/pc/components/preview-video-dialog/preview-video-dialog.vue'
import { VIDEO_STATUS_ARRAY, VIDEO_STATUS_MAP } from '@/constants/index'
import * as videoApi from '@/apis/ai-features'
import { useEvent } from '@/event'
import { LANG_CHANGED } from '@/event/modules/site'
import { formatTime } from '@/common/js/date'
import { downloadVideo } from '@/pc/utils'

const { t } = useI18n({
  messages: {
    zh: {
      col1: '视频名称',
      col2: '状态',
      col3: '创建时间',
      col4: '操作',
      operate: ['查看', '导出', '重新生成'],
      noExportLink: '暂无导出链接',
    },
    en: {
      col1: 'Video name',
      col2: 'Status',
      col3: 'Generate Datetime',
      col4: 'Operate',
      operate: ['Preview', 'Export', 'Regenerate'],
      noExportLink: 'No export link',
    },
  },
})
const event = useEvent()

const activeName = ref('-1')

const statusColor = ref({
  0: '#F99703',
  1: '#06AD0E',
  2: '#D8131A',
})

const statusBtnList = computed(() => {
  return VIDEO_STATUS_ARRAY.slice(0, 4)
})

const handleClick = (item) => {
  activeName.value = item.id
  pageNum.value = 1
  pageSize.value = 10
  getVideoList()
}

const pageNum = ref(1)
const pageSize = ref(10)
const total = ref(0)

const handleCurrentChange = (val) => {
  pageNum.value = val
  pageSize.value = 10
  getVideoList()
}

// 分页条数变化
const handleSizeChange = (val) => {
  pageSize.value = val
  pageNum.value = 1
  getVideoList()
}

// 获取列表
const videoList = reactive([])
const getVideoList = async () => {
  const params = {
    status: activeName.value,
    pageNum: pageNum.value,
    pageSize: pageSize.value,
  }
  try {
    let { rowList = [], totalRecord = 0 } = await videoApi.pageListWorksTask(params)
    videoList.length = 0
    videoList.push(...rowList)
    total.value = totalRecord
  } catch (e) {
    ElMessage.error(e.data.msg)
  }
}
getVideoList()

const previewVideoDialogRef = ref(null)
// 打开视频预览
const previewVideo = (row) => {
  previewVideoDialogRef.value.init({
    url: row.outputUrl,
  })
}

// 导出视频
const exportData = (row) => {
  if (!row.outputUrl) {
    ElMessage.warning(t('noExportLink'))
    return
  }
  downloadVideo(row.outputUrl, row.name)
}

// 点击重新生成
const onRegenerate = async (row) => {
  try {
    await videoApi.reGenerateVideo({ id: row.id })
    getVideoList()
  } catch (e) {
    ElMessage.error(e.data.msg)
  }
}
event.on(LANG_CHANGED, async () => {
  await getVideoList()
})
</script>
