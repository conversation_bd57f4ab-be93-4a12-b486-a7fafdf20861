<style lang="scss" scoped>
.goods-card {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  width: 192px;
  border: 1px solid #eeeeee;
  border-radius: 8px;
  cursor: pointer;

  img {
    width: 100%;
    height: 160px;
    object-fit: cover;
  }
  .goods-desc {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 8px;
    border-top: 1px solid #eeeeee;
    color: #333;
    font-weight: 600;
    font-size: 14px;
  }
}
.local-life + .local-life {
  margin-top: 16px;
  border-top: 1px dashed #cccccc;
  padding-top: 12px;
}
span {
  display: inline-block;
}
</style>

<template>
  <!-- 地图卡片 && props.data.lat && props.data.lng && props.data.title -->
  <div v-if="props.data.dataType === 'market'" class="w-full">
    <h3 v-if="props.data.title">{{ props.data.title }}</h3>
    <span class="mt-2" v-if="props.data.desc">{{ props.data.desc }}</span>
    <div v-if="props.data.imgs?.length > 0" class="flex flex-wrap gap-2 mt-2">
      <!-- <img v-for="url in props.data.imgs" :key="url" class="h-150px rounded-8px" :src="`${url}?x-oss-process=image/resize,m_fill,h_300`" /> -->
      <el-image
        class="h-150px"
        :src="`${props.data.imgs[0]}?x-oss-process=image/resize,h_300`"
        :zoom-rate="1.2"
        :preview-src-list="[props.data.imgs]"
        fit="cover"
      ></el-image>
    </div>
    <template v-if="props.data.loc">
      <span class="font-600 mt-2">位置：</span>
      <a
        v-if="props.data.lat && props.data.lng"
        class="underline text-#d8131a"
        :href="`http://api.map.baidu.com/marker?location=${props.data.lat},${props.data.lng}&output=html&title=${props.data.loc}`"
        target="_blank"
      >
        <icon type="icon-map"></icon>
        {{ props.data.loc }}
      </a>
      <span v-else>{{ props.data.loc }}</span>
    </template>
  </div>
  <!-- 商品卡片 -->
  <div v-else-if="props.data.dataType === 'goods'" class="goods-card" @click="openLink(props.data.link)">
    <!-- <img :src="props.data.imgs[0]" /> -->
    <img :src="`${props.data.imgs[0]}?x-oss-process=image/resize,m_fill,h_300`" />
    <div class="goods-desc">{{ props.data.title }}</div>
  </div>
  <!-- 本地生活 -->
  <div v-else-if="props.data.dataType === 'localLife'" class="w-full local-life">
    <div v-if="props.data.imgs" class="flex flex-wrap gap-2">
      <img v-for="url in props.data.imgs" :key="url" class="h-150px rounded-8px" :src="`${url}?x-oss-process=image/resize,m_fill,h_300`" />
    </div>
    <div @click="openLink(props.data.link)" class="mt-2 cursor-pointer">
      <span class="font-600">店铺名称：</span>
      <span>{{ props.data.title }}</span>
    </div>
    <div v-if="props.data.label" class="mt-1">
      <span class="font-600">标签：</span>
      <span>{{ props.data.label }}</span>
    </div>
    <div v-if="props.data.mobile" class="mt-1">
      <span class="font-600">联系方式：</span>
      <span>{{ props.data.mobile }}</span>
    </div>
    <div v-if="props.data.loc" class="mt-1">
      <span class="font-600">位置：</span>
      <span>{{ props.data.loc }}</span>
    </div>
    <div v-if="props.data.desc" class="mt-1">
      <span class="font-600">路线：</span>
      <span>{{ props.data.desc }}</span>
    </div>
  </div>
  <!-- 图片 -->
  <div v-else-if="props.data.dataType === 'IMG'">
    <!-- <img class="h-150px" :src="props.data.url" /> -->
    <!-- <el-image class="h-150px" :src="props.data.url" :zoom-rate="1.2" :preview-src-list="[props.data.url]" fit="cover"></el-image> -->
    <el-image
      class="h-150px"
      :src="`${props.data.url}?x-oss-process=image/resize,h_300`"
      :zoom-rate="1.2"
      :preview-src-list="[props.data.url]"
      fit="cover"
    ></el-image>
  </div>
</template>

<script setup>
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})

const openLink = (url) => {
  window.open(url)
}
</script>
