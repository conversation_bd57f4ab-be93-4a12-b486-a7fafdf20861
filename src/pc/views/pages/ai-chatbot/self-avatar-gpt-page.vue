<style lang="scss" scoped>
.gpt-wrapper {
  height: 80vh;
  min-height: 585px;
  max-height: 800px;
}
.gpt-page {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.gpt-page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 58px;
  padding: 0 20px;
  border-bottom: 1px solid #d8d8d8;
  background-color: #f5f4f6;
}
.gpt-page-body {
  display: flex;
  flex-grow: 1;
  background: linear-gradient(0deg, #ffeded 0%, #f6f4f4 100%);
}

.layout-sider {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  width: 160px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.6);

  .agent-title {
    padding-left: 30px;
    font-weight: 600;
    font-size: 16px;
    line-height: 22px;
    color: #333;
    background-image: url('@/assets/imgs/ai-talk-agent-title.png');
    background-repeat: no-repeat;
    background-size: 22px;
  }
  .agent-list {
    flex-grow: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    // margin-top: 20px;
  }

  .agent-disabled {
    cursor: not-allowed !important;
  }

  .agent-item {
    padding: 10px;
    padding-left: 18px;
    font-size: 14px;
    color: #333;
    user-select: none;
    cursor: pointer;
  }
  .agent-activty {
    color: $primary-color;
    background: #ffeded;
  }
}

.layout-content {
  flex-grow: 1;
  width: 740px;
  display: flex;
  flex-direction: column;
  padding-bottom: 12px;
}

.qa-list {
  overflow-y: auto;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 0;
  padding: 20px 20px 0;
  color: $color-333333;

  .qa-item {
    display: flex;
    gap: 8px;
    max-width: calc(100% - 32px);
    white-space: pre-wrap;

    &.posl {
      flex-direction: row;
      align-self: flex-start;

      .avatar {
        background-image: url('https://static.chinamarket.cn/static/trade-exhibition/ai-daji/dajigeduihua.png?v=1');
        background-size: 32px 32px;
      }
      .qa-text-content {
        background-color: #fff;
        position: relative;
        .think {
          color: #8b8b8b;
          padding: 0 0 0 13px;
          border-left: 2px solid #e5e5e5;
        }
      }
    }
    &.posr {
      flex-direction: row-reverse;
      align-self: flex-end;

      .avatar {
        background-color: #fddddd;
        background-image: url('@/assets/imgs/ai-talk-avatar-user.png');
      }
      .qa-text-content {
        background-color: #f6e3e1;
      }
    }

    .avatar {
      flex-shrink: 0;
      width: 32px;
      height: 32px;
      margin-top: 6px;
      border-radius: 50%;
      // background-color: #ff8275;
      background-size: 80%;
      background-position: center;
      background-repeat: no-repeat;
    }

    .qa-content {
      flex-grow: 1;
    }

    .qa-text-content {
      font-size: 14px;
      padding: 12px;
      border-radius: 4px;
      word-break: break-all;
    }
  }

  .translate-text {
    border-radius: 4px;
    margin-top: 2px;
    padding-top: 4px;
    border-top: 1px solid $color-EEEEEE;
    color: $regular-text;
  }

  .generate-btn {
    font-size: 12px;
    margin-top: 8px;
    letter-spacing: 2px;
    width: fit-content;
    height: 25px;
    padding: 0 12px;
    border-radius: 4px;
    color: $basic-white;
    background: linear-gradient(270deg, #d8131a 0%, #e96f18 100%);
  }

  .stop-generate {
    text-align: center;
    line-height: 25px;
    font-size: 12px;
    margin-top: 8px;
    letter-spacing: 2px;
    width: fit-content;
    height: 25px;
    padding: 0 12px;
    border-radius: 4px;
    color: $basic-white;
    border: 1px solid transparent;

    color: $primary-color;

    border-image: linear-gradient(270deg, #d8131a 0%, #e96f18 100%) 1;
    background: linear-gradient(270deg, #ffb7ba 0%, #ffe3cf 100%);

    border: 1px solid $primary-color; /* 透明边框 */
    border-radius: 4px;
  }
  .appraise {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;

    .icon-zan-dianji,
    .icon-cha-dianji {
      color: $primary-color;
    }
  }
}

.shortcut-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 16px 20px 0;
  padding-left: 40px;
  background-image: url('@/assets/imgs/ai-talk-avatar.png');
  background-repeat: no-repeat;
  background-size: 32px 24px;

  .option {
    padding: 2px 12px;
    border-radius: 2px;
    border: 1px solid $primary-color;
    color: $primary-color;
    font-size: 12px;
    user-select: none;
    cursor: pointer;
  }
}

.input-box {
  display: flex;
  flex-direction: column;
  height: 104px;
  margin: 16px 20px 0;
  padding: 12px;
  border: 1px solid #fd441f;
  border-radius: 8px;
  // border-image: linear-gradient(270deg, #d8131a 0%, #fd441f 100%) 1;
  background-color: #fff;

  .input-search {
    flex-grow: 1;
    height: 100%;
    padding: 0;
  }
  .send-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px 12px;
    border-radius: 20px;
    color: $basic-white;
    font-size: 14px;
    background: linear-gradient(270deg, #d8131a 0%, #fd441f 100%);
  }
}
.disclaimer {
  margin-top: 4px;
  font-size: 12px;
  color: #bcbcbc;
  text-align: center;
}

.disabled {
  color: $basic-white !important;
  background: #cacaca !important;
  border: none;
  cursor: not-allowed !important;
}

.lange-box {
  margin-bottom: 8px;

  .icon-qiehuan {
    margin: 0 12px;
    cursor: pointer;
  }
}
.lange-select {
  padding: 0 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
  outline: none;
}

.translating {
  color: $primary-color !important;
}
/* 设置动画 */
@keyframes donghua {
  0% {
    transform: translateY(0px);
  }
  20% {
    transform: translateY(-5px);
  }
  40%,
  100% {
    transform: translateY(0px);
  }
}
.translating span {
  display: inline-block;
  animation: donghua 1.5s ease-in-out infinite;
  animation-delay: calc(0.1s * var(--i));
}
.avatar-box {
  width: 305px;
  height: 100%;
  position: relative;
  background: #ffffff;
}
.avatar-box .mp4 {
  width: 305px;
  height: 466px;
  overflow: hidden;
  position: relative;
  top: 20px;
}
#avatar {
  width: 305px;
  height: 512px;
  position: absolute;
  z-index: 2;
  top: -40px;
}
#video {
  width: 305px;
  height: 512px;
  position: absolute;
  z-index: 1;
  top: -40px;
}
.stop-button {
  position: absolute;
  bottom: 80px;
  left: 50%;
  transform: translateX(-50%);
  padding: 8px 15px;
  border-radius: 8px;
  color: #ffffff;
  background: $primary-color;
  cursor: pointer;
}

@media (max-height: 860px) {
  .avatar-box {
    top: 0;
    .mp4 {
      top: 0;
      height: 390px;
    }
    .stop-button {
      bottom: 60px;
    }
    #avatar,
    #video {
      top: -110px;
    }
  }
}

.generate-box {
  margin-top: 10px;

  .avatar {
    margin: 0px 8px 0 0 !important;
  }
}
</style>

<template>
  <div class="gpt-wrapper">
    <div class="gpt-page">
      <div class="gpt-page-header">
        <img class="h-full" src="https://static.chinamarket.cn/static/trade-exhibition/logo/logo-header1.png" draggable="false" />
        <i-inside-close class="h-5 cursor-pointer" @click="emits('close')" />
      </div>

      <div class="gpt-page-body">
        <div class="layout-sider">
          <!-- <div class="agent-title">AI智能体</div> -->
          <div class="agent-list">
            <div
              class="agent-item"
              :class="[item.code === agentCode && 'agent-activty']"
              @click="changeAgent(item.code)"
              v-for="item in ANGENT_CODE"
              :key="item"
            >
              <span class="skip-translate" v-if="item.code === 'LYDJ_BOT'">{{ $storageLocale === 'zh' ? item.name.zh : item.name.en }}</span>
              <span v-else>{{ item.name[$i18n.locale] }}</span>
            </div>
          </div>
        </div>

        <div class="layout-content">
          <div ref="scrollToBottomRef" class="qa-list skip-translate">
            <div v-for="(qa, i) in qaList" :key="i" class="qa-item" :class="{ posl: qa.locate === 'left', posr: qa.locate === 'right' }">
              <div class="avatar"></div>
              <div class="qa-content">
                <div class="qa-text-content">
                  <div v-if="nlpStatusText && qa.locate === 'left' && i === qaList.length - 1">{{ nlpStatusText }}</div>
                  <!-- 思考中的内容 -->
                  <div v-if="qa.locate === 'left'" class="think">{{ qa.think }}</div>
                  <div v-if="qa.dataType === 'text' || qa.locate === 'right'">
                    <!-- 针对有图片类型的text需要展示image标签 -->
                    <span v-html="qa.text" />
                    <div class="translate-text" v-if="qa.translatorText">
                      <div v-if="translating && translatingIndex === i">
                        <InGenerating></InGenerating>
                      </div>
                      <div v-else>{{ qa.translatorText }}</div>
                    </div>
                  </div>
                  <el-image
                    v-else-if="qa.dataType === 'IMG'"
                    :ref="`imageRef_${i}`"
                    :src="qa.text"
                    :zoom-rate="1.2"
                    :preview-src-list="[`${qa.text}?x-oss-process=image/resize,h_300`]"
                    fit="cover"
                  ></el-image>
                  <!-- <img v-else-if="qa.dataType === 'IMG'" :ref="`imageRef_${i}`" :src="qa.text" /> -->
                  <div v-else-if="qa.dataType === 'json' && qa.json" class="mt-20px">
                    {{ qa.json.tip }}
                    <!-- JSON 只翻译 tip -->
                    <div id="translate" class="translate-text" v-if="qa.translatorText">
                      <div v-if="translating && translatingIndex === i">
                        <InGenerating></InGenerating>
                      </div>
                      <div v-else>{{ qa.translatorText }}</div>
                    </div>
                    <div class="flex flex-wrap gap-2 mt-2">
                      <ComplexData v-for="(complex, complexIndex) in qa.json.data" :key="complexIndex" :data="complex"></ComplexData>
                    </div>
                  </div>
                  <!-- 翻译没有停止生成和重新生成 -->
                  <div v-if="agentCode !== 'SC_TRANSLATION_BOT'">
                    <div class="stop-generate" v-if="qa.isManualStop && qa.locate === 'left'" v-mode="SUSPENDED_MAP">
                      {{ $t('ai.suspended') }}
                    </div>
                    <template v-if="qaList.length - 1 === i && qa.locate === 'left' && qa.isNoFirst">
                      <button v-if="!isTasking" v-mode="REBUILD_MAP" class="generate-btn" @click="onSend(qa.conversationId)">
                        {{ $t('ai.rebuild') }}
                      </button>
                      <button
                        v-else
                        type="text"
                        v-mode="STOP_GENERATION_MAP"
                        v-arMode="'وقف توليد'"
                        class="generate-btn"
                        @click="disconnectSSE(qa.conversationId, i)"
                      >
                        {{ $t('ai.stopGeneration') }}
                      </button>
                    </template>
                  </div>
                </div>
                <div class="qa-operation flex" :class="{ 'justify-right': qa.locate === 'right' }">
                  <template v-if="qa.locate === 'left'">
                    <div v-if="!isTasking" class="appraise">
                      <SelectLanguage :cur-info="qa" :index="i" @change-language="changeLang"></SelectLanguage>
                      <icon
                        v-if="qa.dataType === 'IMG'"
                        title="复制"
                        class="icon-appraises icon-copy"
                        type="icon-fuzhi"
                        size="16"
                        @click="copyImg(`imageRef_${i}`)"
                      ></icon>
                      <icon
                        v-if="qa.dataType === 'text'"
                        v-copy="{ value: qa.text, onSuccess, onError }"
                        title="复制"
                        class="icon-appraises icon-copy"
                        type="icon-fuzhi"
                        size="16"
                      ></icon>

                      <icon v-if="qa.goodOrBad === 1" @click="onLike(qa, LIKE_STATUS.GOOD, i)" type="icon-zan-dianji" size="16"></icon>
                      <!-- <icon v-else class="icon-appraises" type="icon-zan" size="16" @click="onLike(qa, LIKE_STATUS.GOOD, i)"></icon> -->
                      <icon v-if="qa.goodOrBad === 2" @click="onLike(qa, LIKE_STATUS.BAD, i)" type="icon-cha-dianji" size="16"></icon>
                      <!-- <icon v-else class="icon-appraises" type="icon-cha" size="16" @click="onLike(qa, LIKE_STATUS.BAD, i)"></icon> -->
                    </div>
                  </template>
                  <template v-if="qa.locate === 'right'">
                    <div class="appraise">
                      <icon v-copy="{ value: qa.text, onSuccess, onError }" title="复制" class="icon-appraises icon-copy" type="icon-fuzhi" size="16"></icon>
                    </div>
                  </template>
                </div>
              </div>
            </div>
            <div v-if="nlpStatus" class="generate-box qa-item posl">
              <div class="flex items-center">
                <div class="avatar"></div>
                <!-- 等待动画 三个小点闪动 -->
                <InGenerating></InGenerating>
              </div>
            </div>
          </div>
          <!-- 快捷键 -->
          <!-- <div class="shortcut-options" v-if="agentCode !== 'SC_TRANSLATION_BOT'">
            <div class="option" v-for="item in AI_SHORTCU_KEY" :key="item.id" @click="onShortcut(item)">{{ item.value[$i18n.locale] }}</div>
          </div> -->
          <div class="input-box">
            <div class="lange-box skip-translate" v-if="agentCode === 'SC_TRANSLATION_BOT'">
              <select class="lange-select" v-model="sourceTranslateLg">
                <option v-for="item in LANGUAGE_LIST" :key="item.id" :selected="item.lable === '中文'" :value="item.code">{{ item.value }}</option>
              </select>
              <icon class="icon-qiehuan" type="icon-qiehuan1" @click="handover"></icon>
              <select class="lange-select" v-model="translateLg">
                <option v-for="item in LANGUAGE_LIST" :key="item.id" :selected="item.lable === '阿拉伯语'" :value="item.code">{{ item.value }}</option>
              </select>
            </div>
            <div class="flex items-end grow gap-2">
              <textarea
                v-model="input"
                class="input-search"
                :placeholder="$t('ai.inputPlaceholder')"
                clearable
                resize="none"
                type="textarea"
                maxlength="300"
                show-word-limit
                @keydown="onHandleKey"
              ></textarea>
              <div :class="isTasking && 'disabled'" class="send-btn" @click="onSend()">
                <icon class="text-white" type="icon-fasong" size="16" />
                {{ $t('ai.send') }}
              </div>
            </div>
          </div>
          <div class="disclaimer">{{ $t('ai.warning') }}</div>
        </div>

        <div class="avatar-box" v-if="false">
          <div class="mp4">
            <!-- 空闲视频 -->
            <video id="avatar" width="100%" height="100%" disablePictureInPicture src="@/assets/mp4/avatar-bg.mp4" muted autoplay loop />
            <!-- 视屏 -->
            <video id="video" width="100%" height="100%" disablePictureInPicture src="@/assets/mp4/avatar1.mp4" muted autoplay loop />
          </div>
          <div class="stop-button" @click="interruptAvatar">{{ $t('ai.stopPlay') }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { useI18n } from 'vue-i18n'
import ComplexData from '@/components/complex-data/complex-data.vue'
import InGenerating from './components/in-generating.vue'
import SelectLanguage from './components/select-languages.vue'
import { REBUILD_MAP, STOP_GENERATION_MAP, SUSPENDED_MAP } from '@/constants/special-field'
// import useBasic from '@/pc/hooks/useSelfAvatar'
import * as API from '@/apis/ai-features'
import { browserType } from '@/common/js/env'
import { base64ToFile, debounce, getBase64Image } from '@/common/js/util'
import user from '@/pc/utils/user'
import { imgReg } from '@/utils/utils'
// import { AI_SHORTCU_KEY, ANGENT_CODE, LANGUAGE_LIST } from './data-options.js'
import { ANGENT_CODE, LANGUAGE_LIST } from './data-options.js'

const { fallbackLocale, t } = useI18n({
  messages: {
    zh: {
      tip: {
        noInput: '请输入您想了解的内容',
        translateError: '翻译失败，请稍后再试！',
        copySuccess: '复制成功！',
        copyError: '复制失败',
        unsupportCopy: '浏览器不支持复制图片，可在图片上点击右键复制',
      },
    },
    en: {
      tip: {
        noInput: 'Please enter the content you want to know',
        translateError: 'Translation failed, please try again later!',
        copySuccess: 'Copy success!',
        copyError: 'Copy failed',
        unsupportCopy: 'Browser does not support copying images. You can copy the image by right-clicking on it.',
      },
    },
  },
})

// const router = useRouter()
// const { initSDK, closeWebSocket, writeText } = useBasic()
// 当前提问是否停止了
const isStop = ref(false)

const props = defineProps({
  presetAgent: {
    type: String,
  },
  presetQuestion: {
    type: String,
  },
})
const emits = defineEmits(['close'])

const translateLg = ref('en')
const sourceTranslateLg = ref('zh')

const handover = () => {
  let lang = translateLg.value
  translateLg.value = sourceTranslateLg.value
  sourceTranslateLg.value = lang
}

const LIKE_STATUS = {
  DEFAULT: 0, // 未评价
  GOOD: 1, // 好评
  BAD: 2, // 差评
}

// 停止播报
const interruptAvatar = () => {
  isStop.value = true
  // closeWebSocket()
}

// 处理键盘Enter 事件
const onHandleKey = (e) => {
  // 只输入enter 发送消息
  if (!e.shiftKey && e.keyCode === 13) {
    e.returnValue = false
    e.cancelBubble = true // ie阻止冒泡行为
    e.stopPropagation() // Firefox阻止冒泡行为
    e.preventDefault() // 取消事件的默认动作*换行
    onSend() // 执行发送逻辑
  }
}

// 大模型请求地址
const NLP_URL = `${import.meta.env.VUE_APP_AI_API_URL}/api/chatrobot/agents/plan`

// 聊天内容区域
const scrollToBottomRef = ref(null)
// 自动滚动
const setScroll = () => {
  nextTick().then(() => {
    if (scrollToBottomRef.value) {
      scrollToBottomRef.value.scroll({
        top: scrollToBottomRef.value.scrollHeight,
        // behavior: 'smooth', // 可选，平滑滚动
      })
    }
  })
}
// 滚动防抖 - 防抖包装
const setScrollDebounce = debounce(() => {
  setScroll()
}, 50)

const agentCode = ref(props.presetAgent || 'LYDJ_BOT')

// 提问消息的数据
const qaList = ref([])

// 模型处理状态
const nlpStatus = ref(false)
const nlpStatusText = ref('')
// 是否在回答，回答中不给用户再次提问
const isTasking = ref(false)

// 提问框的值
const input = ref('')
const token = user.getToken()
const isProduction = import.meta.env.VUE_APP_API_ENV === 'production'
const isDevAgent = computed(() => agentCode.value === 'LYDJ_BOT')
// 确定 agentCode 开发测试环境为：LYDJ_BOT_DEV, 生产：LYDJ_BOT
const effectiveAgentCode = computed(() => (isProduction ? agentCode.value : isDevAgent.value ? `${agentCode.value}_DEV` : agentCode.value))
// 获取历史数据
const getHistoryChats = async (F) => {
  // 只有大集哥且登录状态有token时才需要获取历史聊天记录
  const historyList = token && agentCode.value.includes('LYDJ_BOT') ? await API.historyChats({ agentCode: effectiveAgentCode.value }) : []
  const reg = /[*#]/g // 匹配特殊字符*#-
  if (historyList && historyList.length) {
    qaList.value = historyList.map((item) => {
      if (item?.dataType === 'json' && Array.isArray(item?.text)) {
        const { text, ...rest } = item
        rest.think = rest.think?.replace(/^\n/, '')
        let tip = ''
        const arr = []
        text?.forEach((data) => {
          tip += data?.tip?.replace(reg, '') ?? ''
          Array.isArray(data.data) && arr.push(data.data[0])
        })
        return {
          ...rest,
          json: {
            data: arr,
            tip,
          },
        }
      } else if (item?.dataType === 'text') {
        item.text = item.text.replace(reg, '')
        const urls = item.text.match(imgReg) || [] // 如果text中有图片类型链接，则需要处理并加上图片标签
        if (urls.length) {
          item.text = item.text.replace(imgReg, `<image src="${urls[0]}" alt="" style="max-width: 100%;" />`)
        }
        return item
      } else {
        return item
      }
    })
  }

  //  如果有预设问题 发送消息 并且是首次打开弹窗
  if (props.presetQuestion && F) {
    input.value = props.presetQuestion
    // onSend()
  } else {
    createSseConnect(null, '', F)
  }

  setScrollDebounce()
}

// 切换智能体
const changeAgent = (val) => {
  if (nlpStatus.value) return
  qaList.value = []
  ctrl.value && ctrl.value.abort()
  isTasking.value = false
  input.value = ''
  agentCode.value = val
  getHistoryChats()
}

// 快捷键
// const onShortcut = async (item) => {
//   if (item.path) {
//     // emits('close')
//     window.open(item.path)
//   }
// }

// 发送消息
const onSend = async (msgId) => {
  // msgId 有值 说明是 重新生成
  if (nlpStatus.value || isTasking.value) return

  if (!msgId && !input.value.trim()) {
    return ElMessage.warning(t('tip.noInput'))
  }

  let qustion = input.value

  try {
    nlpStatus.value = true

    if (!msgId) {
      qaList.value.push({
        locate: 'right',
        text: qustion,
      })
    } else if (qaList.value.length > 0 && qaList.value[qaList.value.length - 1].locate === 'left' && msgId) {
      // 重新生成 需要删除最后一条
      qaList.value.pop()
      // 找到最后一条right 数据 重新生成的时候传给后台
      qustion = qaList.value
        .slice()
        .reverse()
        .find((item) => item.locate === 'right').text
    }

    input.value = ''
    await createSseConnect(msgId, qustion)
  } catch (error) {
    nlpStatus.value = false
  }
}

const reg = /[*#]/g // 匹配特殊字符*#-
// 发送消息  F 手动切换还是首次进入的时候默认的选择
const ctrl = ref(null)
const conversationId = ref(null)
const createSseConnect = async (msgId, val, F) => {
  isTasking.value = true

  // msgId 说明重新生成 重新生成需要 问题 和  conversationId
  let params = {
    conversationId: msgId ? msgId : conversationId.value,
    scene: token ? 'sc' : 'agents',
    agentCode: effectiveAgentCode.value, // 开发测试环境为 LYDJ_BOT_DEV, 其他智能体保持不变：agentCode.value
    question: val,
    language: fallbackLocale.value,
  }

  // 本地生活服务和商品服务需要传JSON
  if (['SC_LIFE_BOT', 'SC_GOODS_BOT'].includes(agentCode.value)) {
    params.dataType = 'json'
  }
  // 语种编码
  // let code = 'zh'

  interruptAvatar()
  isStop.value = false

  // 在线翻译走专用接口
  if (agentCode.value === 'SC_TRANSLATION_BOT') {
    // 翻译专用接口
    translationApi(val, translateLg.value, F)
    return
  }
  // 首次默认找不到数据，为新增的数据
  let index = -1
  let tip = ''
  // let isHttp = false
  let isImgOver = false
  ctrl.value = new AbortController()
  await fetchEventSource(NLP_URL, {
    method: 'POST',
    headers: {
      Accept: 'text/event-stream, application/json, text/plain, */*',
      'Cache-Control': 'no-cache',
      'Client-Version': '',
      'Content-Type': 'application/json;charset=UTF-8',
      pragma: 'no-cache',
      scene: token ? 'sc' : 'agents',
      channel: token ? 'sc' : 'agents',
      accessToken: token || import.meta.env.VUE_APP_READY_TOKEN,
    },
    body: JSON.stringify(params),
    openWhenHidden: true,

    onmessage: (msg) => {
      nlpStatus.value = false
      isTasking.value = true

      let answer
      if (index === -1) {
        answer = {
          isNoFirst: val ? true : false,
          dataType: 'text',
          text: '',
          think: '', // 思考中的内容
          goodOrBad: null,
          isManualStop: false,
          locate: 'left',
        }
        qaList.value.push(answer)
        index = qaList.value.length - 1
      } else {
        answer = qaList.value[qaList.value.length - 1]
      }

      let { data } = msg

      data = JSON.parse(data)
      data.type = data.type.toUpperCase()
      // 保存会话id 和 msg id
      if (!answer) return
      answer.messageId = data?.messageId || null
      answer.conversationId = data?.conversationId || null
      conversationId.value = data.conversationId || null

      nlpStatusText.value = ''
      if (data.type === 'EVENT') {
        nlpStatusText.value = data.text
      } else if (data.type === 'IMG') {
        // TODO:
      } else if (data.type === 'TEXT') {
        // 结尾一句发送两个句号
        // if (data.text === '。。') {
        // return !F && writeText('。', data.messageId, code)
        // }
        if (data?.think) {
          // 思考中的内容, 去除第一个换行符
          answer.think += data?.think?.replace(/^\n/, '')
          answer.dataType = 'think'
        } else {
          answer.dataType = 'text'
          if (!isImgOver) {
            answer.text += data.text.replace(reg, '')
            const urls = answer.text.match(imgReg) || []
            if (urls.length) {
              answer.text = answer.text.replace(imgReg, `<img src="${urls[0]}" alt="" style="max-width: 100%;" />`)
              isImgOver = true
            }
          }

          if (data.text.replace(reg, '').includes('http')) {
            // !F && !isHttp && writeText(isStop.value ? '。' : '下图。', data.messageId, code)
            // isHttp = true
          }
        }

        // 数字人
        // !F && !isHttp && writeText(isStop.value ? '。' : data.text.replace(reg, ''), data.messageId, code)
      } else if (data.type === 'JSON') {
        answer.dataType = 'json'
        data.text = data.text ?? {}
        try {
          // 结尾一句发送两个句号
          // if (data.text?.tip === '。。') {
          // return !F && writeText('。', data.messageId, code)
          // }
          tip += data.text?.tip?.replace(reg, '') ?? ''
          data.text.data = Array.isArray(data.text?.data) ? data.text.data : []
          data.text.data.unshift(...(Array.isArray(answer.json?.data) ? answer.json.data : []))
          answer.json = {
            ...data.text,
            tip,
          }
          // 数字人
          // !F && writeText(isStop.value ? '。' : (data.text?.tip.replace(reg, '') ?? ''), data.messageId, code)
        } catch (e) {
          console.error(data.text)
          console.log(e)
        }
      }
      !isImgOver && setScrollDebounce()
    },
    onclose: () => {
      isTasking.value = false
      nlpStatus.value = false
      index = -1
      setScrollDebounce()
    },
    onerror: (err) => {
      ctrl.value && ctrl.value.abort()
      isTasking.value = false
      nlpStatus.value = false
      index = -1
      throw err
    },
  })
}
// 停止生成
const disconnectSSE = async (conversationId, i) => {
  try {
    await API.stopSse({
      conversationId,
    })
    // let index = qaList.value.findIndex((item) => item.conversationId === conversationId && item.locate === 'left')
    // 停止生成 修改当前停止的标识符
    qaList.value[i].isManualStop = true
  } catch (error) {
    console.error(error)
  }
}

// 翻译接口
const translationApi = async (val, code, F) => {
  if (!val) {
    nlpStatus.value = false
    isTasking.value = false
    return
  }
  try {
    nlpStatus.value = !!F
    const res = await API.translate({
      q: val,
      to: code,
    })
    qaList.value.push({
      isNoFirst: val ? true : false,
      dataType: 'text',
      text: res.text,
      goodOrBad: null,
      isManualStop: false,
      locate: 'left',
    })
    // !F && !isStop.value && writeText(res.text + '。', new Date().getTime(), code)
    isTasking.value = false
    nlpStatus.value = false
  } catch (error) {
    nlpStatus.value = false
    isTasking.value = false
  }
}

// 切换语言
const changeLang = (val, curInfo, index, code) => {
  console.log(val, code, 'kdgjiosdghiodshviudovhuid')

  // JSON 类型 只处理 tip 的文本翻译
  const textToTranslate = curInfo.dataType === 'json' ? curInfo?.json?.tip : curInfo.text
  // handleTranslator(`${textToTranslate}${val}`, index, code)
  handleTranslator(textToTranslate, index, code)
}

// 是否是翻译中 用于生产中的动画处理
const translating = ref(false)
// 当前翻译的第几条 用于显示生成动画
const translatingIndex = ref(null)
const handleTranslator = async (val, index, code) => {
  try {
    translating.value = true
    translatingIndex.value = index
    qaList.value[index].translatorText = true

    // let params = { scene: 'sc', agentCode: 'SC_TRANSLATION_INLINE_BOT', question: val, streaming: false }
    // let res = await API.plan(params)
    const res = await API.translate({
      q: val,
      to: code,
    })
    console.log(res, '翻译的res')
    translating.value = false
    qaList.value[index].translatorText = res.text
    // 数字人
    // writeText(res.text + '。', new Date().getTime(), code)
  } catch (error) {
    qaList.value[index].translatorText = ''
    translating.value = false
    ElMessage.error(t('tip.translateError'))
  }
}

// 评价
const onLike = async (msg, status) => {
  let params = {
    messageId: msg.messageId,
    like: 0,
  }
  // 一样说明是取消
  if (status === msg.goodOrBad) {
    params.like = LIKE_STATUS.DEFAULT
  } else {
    params.like = status
  }

  API.isLike(params)
  msg.goodOrBad = params.like
}

// 复制图片
const copyImg = async (ref) => {
  try {
    const draftImage = this.$refs?.[ref][0]
    if (!draftImage) return
    const isIe = browserType.indexOf('IE') !== -1
    const img = await getBase64Image(draftImage)
    if (!isIe) {
      // 除IE外的浏览器的复制方法
      if (navigator.clipboard) {
        const file = await base64ToFile(img)
        const blob = new Blob([file], { type: 'image/png' })
        await navigator.clipboard
          .write([
            // eslint-disable-next-line no-undef
            new ClipboardItem({
              [blob.type]: blob,
            }),
          ])
          .then(() => {
            ElMessage.success(t('tip.copySuccess'))
          })
      } else if (window.require) {
        const { clipboard, nativeImage } = window.require('electron')
        const image = nativeImage.createFromDataURL(img) // res为base64图片数据
        clipboard.writeImage(image)
        ElMessage.success(t('tip.copySuccess'))
      } else {
        ElMessage.warning(t('tip.unsupportCopy'))
      }
    } else {
      ElMessage.warning(t('tip.unsupportCopy'))
    }
  } catch (err) {
    // console.log(err)
    ElMessage.warning(t('tip.unsupportCopy'))
  }
}

// 复制成功
const onSuccess = () => {
  return ElMessage.success(t('tip.copySuccess'))
}
// 复制失败
const onError = () => {
  return ElMessage.error(t('tip.copyError'))
}

onMounted(() => {
  getHistoryChats(true)
  // initSDK()
})
// onUnmounted(() => {
// closeWebSocket()
// })
defineExpose({
  interruptAvatar,
})
</script>
