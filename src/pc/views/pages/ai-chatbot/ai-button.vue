<!-- 机器人按钮 入口 -->
<style lang="scss" scoped>
.floating-ball {
  @keyframes blink {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0;
    }
  }

  .blink {
    animation: blink 1.5s linear 2; /* 总时长1秒，闪烁两次 */
  }

  .logo {
    position: fixed;
    top: 347px;
    right: 36px;
    z-index: 99;
    z-index: 99999;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .icon-close {
      cursor: pointer;
      position: absolute;
      top: -10px;
      right: -10px;
      width: 16px;
      height: 16px;
      color: #d8d8d8;
    }

    .robot-image {
      width: 64px;
      height: 64px;
      cursor: pointer;
    }
  }

  .gpt {
    position: relative;
    margin-right: 16px;
    width: 78px;
    color: $basic-white;

    .text {
      font-size: 12px;
      font-weight: 500;
      line-height: 18px;
      cursor: pointer;

      &:hover {
        border-bottom: 1px solid $basic-white;
      }
    }

    .icon {
      margin-left: 2px;
    }

    .svg-icon {
      vertical-align: top;
    }

    .new-tag1 {
      position: absolute;
      top: -12px;
      right: -12px;
      width: 25px;
    }
  }
}
</style>

<template>
  <div class="floating-ball">
    <div id="startDiv" class="logo">
      <button @click="openAiDialog">点这个 打开 AI</button>
    </div>
    <AiDialog ref="aiDialogRef"></AiDialog>
  </div>
</template>

<script setup>
import AiDialog from './ai-dialog.vue'

const aiDialogRef = ref(null)

const openAiDialog = () => {
  aiDialogRef.value.init()
}
</script>
