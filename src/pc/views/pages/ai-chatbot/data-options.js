export const ANGENT_CODE = Object.freeze([
  {
    code: 'LYDJ_BOT',
    name: { zh: '大集哥', en: 'AI Bro' },
  },
  // {
  //   code: 'SC_POLICY_BOT',
  //   name: { zh: '临沂政策', en: 'Linyi Policy' },
  // },
  // {
  //   code: 'SC_LIFE_BOT',
  //   name: { zh: '本地生活服务', en: 'Local Life' },
  // },
  // {
  //   code: 'SC_HUIYI_BOT',
  //   name: { zh: '展会服务', en: 'Exhibition' },
  // },
  // {
  //   code: 'SC_GOODS_BOT',
  //   name: { zh: '商品服务', en: 'Goods' },
  // },
  {
    code: 'SC_TRANSLATION_BOT',
    name: { zh: '在线翻译', en: 'Translation' },
  },
])

export const AI_SHORTCU_KEY = Object.freeze([
  { id: '1', value: { zh: '找商品', en: 'Find Products' }, lable: '商品', path: '/mall' },
  // { id: '2', value: { zh: '找服务', en: 'Find Services' }, lable: '服务' },
  // { id: '3', value: { zh: '找AI工具', en: 'Find AI Tools' }, lable: 'AI工具' },
  { id: '4', value: { zh: '视频翻译', en: 'Video Translation' }, lable: '视频翻译', path: '/ai-features/video-translation' },
  { id: '5', value: { zh: '数字代言人', en: 'Digital Spokesperson' }, lable: '数字代言人', path: '/ai-features/person-square' },
  { id: '6', value: { zh: 'AI商品图', en: 'AI Product Images' }, lable: 'AI商品图', path: '/ai-features/product-picture' },
  { id: '7', value: { zh: '自主创作', en: 'Self Creation' }, lable: '自主创作', path: '/ai-features/video-create' },
])

export const LANGUAGE_LIST = Object.freeze([
  { id: '1', value: '中文', lable: '中文', code: 'zh' },
  { id: '2', value: 'English', lable: '英语', code: 'en' },
  { id: '3', value: 'العربية', lable: '阿拉伯语', code: 'ar' },
  { id: '4', value: 'ภาษาไทย', lable: '泰语', code: 'thai' },
  { id: '5', value: 'Bahasa Indonesia', lable: '印尼语', code: 'indonesian' },
  { id: '6', value: 'Pусский язык', lable: '俄语', code: 'ru' },
  { id: '7', value: 'Türkçe', lable: '土耳其语', code: 'tr' },
  // { id: '4', value: '日本語', lable: '日语', code: 'jp' },
  // { id: '5', value: '한국어', lable: '韩语', code: 'kor' },
  // { id: '6', value: 'русск', lable: '俄语', code: 'ru' },
  // { id: '7', value: 'Монгол', lable: '蒙古语', code: 'MN' },
])
