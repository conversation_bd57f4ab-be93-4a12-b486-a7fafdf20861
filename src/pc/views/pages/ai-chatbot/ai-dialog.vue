<template>
  <Dialog v-model="visible" width="1200">
    <template #default>
      <GptPage ref="pageRef" :preset-agent="presetAgent" :preset-question="presetQuestion" @close="visible = false"></GptPage>
    </template>
  </Dialog>
</template>

<script setup>
import Dialog from '@/pc/components/dialog/dialog.vue'
// import GptPage from './gpt-page.vue'
import GptPage from './self-avatar-gpt-page.vue'
import { PRODUCT_INQUIRY_MAP } from '@/constants/special-field'
import { useEvent } from '@/event'
import { OPEN_AI_WINDOW } from '@/event/modules/site'
import { useStorageLocale } from '@/i18n'

const event = useEvent()
const { storageLocale } = useStorageLocale()

const pageRef = ref()

const visible = ref(false)
const init = () => {
  visible.value = true
}
const presetAgent = ref('')
const presetQuestion = ref('')
event.on(OPEN_AI_WINDOW, (args) => {
  presetAgent.value = args?.agent ?? ''
  // 商品详情页咨询大集哥
  if (args?.path?.includes('mall/goods-detail')) {
    presetQuestion.value = `${PRODUCT_INQUIRY_MAP[storageLocale.value]}: ${window?.location?.href}`
  } else {
    presetQuestion.value = args?.question ?? ''
  }
  init()
})

// 监听关闭事件
watch(visible, (val) => {
  if (!val) {
    pageRef.value?.interruptAvatar()
  }
})

defineExpose({ init })
</script>
