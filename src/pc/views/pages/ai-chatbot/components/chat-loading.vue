<style lang="scss" scoped>
.chat-uikit-chat-loading {
  overflow: hidden;
  margin: 10px 0 0 14px;
  width: 30px;
  height: 20px;
  font-size: 12px;
  color: #999999;
  line-height: 25px;
  transform-origin: 0 0;

  .chat-loading-spinner-ellipsis {
    display: inline-block;
    overflow: hidden;
    width: 200px;
    height: 200px;
    background: none;
    transform-origin: 0 -5px;
    transform: scale(0.15);

    .chat-ldio-item {
      position: relative;
      width: 100%;
      height: 100%;
      transform: translateZ(0) scale(1);
      backface-visibility: hidden;
      transform-origin: 0 0;

      div {
        position: absolute;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        background: rgba($color: #808bf5, $alpha: 40%);
        transform: translate(80px, 80px) scale(1);
        animation: chat-ldio-common 2.272s cubic-bezier(0, 0.5, 0.5, 1) infinite;

        &:nth-child(2) {
          animation-delay: -0.5682s;
          background: rgba($color: #808bf5, $alpha: 70%);
        }

        &:nth-child(3) {
          animation-delay: -1.1364s;
          background: rgba($color: #808bf5, $alpha: 100%);
        }

        &:nth-child(4) {
          animation-delay: -1.7045s;
          background: rgba($color: #808bf5, $alpha: 70%);
        }

        &:nth-child(5) {
          animation-delay: -2.2727s;
          background: rgba($color: #808bf5, $alpha: 40%);
        }
      }
    }
  }
}

@keyframes chat-ldio-common {
  0% {
    transform: translate(12px, 80px) scale(0);
  }

  25% {
    transform: translate(12px, 80px) scale(0);
  }

  50% {
    transform: translate(12px, 80px) scale(1);
  }

  75% {
    transform: translate(80px, 80px) scale(1);
  }

  100% {
    transform: translate(148px, 80px) scale(1);
  }
}
</style>

<template>
  <div class="chat-uikit-chat-loading">
    <div class="chat-loading-spinner-ellipsis">
      <div class="chat-ldio-item">
        <div v-for="v in 5" :key="v" />
      </div>
    </div>
  </div>
</template>
