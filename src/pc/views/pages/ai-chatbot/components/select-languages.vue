<template>
  <el-popover style="width: 100px" v-model:visible="visible" width="120" trigger="click" popper-class="lang-box skip-translate">
    <template #reference>
      <div class="reference-text" :class="{ 'bg-white': visible }">
        <icon type="icon-yuyanqiehuan" size="16"></icon>
        <icon type="icon-xiala" size="6"></icon>
      </div>
    </template>
    <div class="content-box" ref="popoverContent">
      <div class="list" v-for="item in LANGUAGE_LIST" :key="item.id" @click.stop="changeLag(item)">{{ item.value }}</div>
    </div>
  </el-popover>
</template>

<script setup lang="jsx">
import { LANGUAGE_LIST } from '../data-options.js'

const emit = defineEmits(['changeLanguage'])
const props = defineProps({
  // 输入框值
  curInfo: {
    type: Object,
    default: () => {},
  },
  index: {
    type: Number,
    default: null,
  },
})

const visible = ref(false)
// 关闭当前popover
const onCancel = () => {
  visible.value = false
}

const changeLag = (val) => {
  // 需要空格
  emit('changeLanguage', ` 翻译成${val.lable}`, props.curInfo, props.index, val.code)
  onCancel()
}
</script>

<style lang="scss" scoped>
.reference-text {
  margin-right: 17px;
  font-size: 14px;
  padding: 2px;
  border-radius: 1px;
  color: $color-333333;
  cursor: pointer;
  display: flex;
  align-items: center;
  position: relative;
  &::before {
    position: absolute;
    top: 50%;
    right: -20px;
    margin-right: 8px;
    width: 2px;
    height: 16px;
    background: #e0e0e0;
    transform: translateY(-50%);
    content: '';
  }
}

.content-box {
  width: 96px;
  background: $basic-white;
  border-radius: 6px;

  .list {
    padding: 4px 12px;
    cursor: pointer;
    &:hover {
      background: #f7f7f7;
    }
  }
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}
</style>

<style>
.lang-box.el-popper {
  min-width: 120px !important;
}
</style>
