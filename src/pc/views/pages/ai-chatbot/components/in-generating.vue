<style lang="scss" scoped>
.generate-text {
  border-radius: 4px;
  margin-top: 0px;
  padding-top: 4px;
  color: $regular-text;
}

.generating {
  color: $primary-color !important;
}
/* 设置动画 */
@keyframes donghua {
  0% {
    transform: translateY(0px);
  }
  20% {
    transform: translateY(-5px);
  }
  40%,
  100% {
    transform: translateY(0px);
  }
}
.generating span {
  display: inline-block;
  animation: donghua 1.5s ease-in-out infinite;
  animation-delay: calc(0.1s * var(--i));
}
</style>

<template>
  <div class="generate-text">
    <div class="generating">
      <span v-mode="GENERATE_MAP" style="--i: 1" class="mx-1">生成中</span>
      <!-- <span style="--i: 1">生</span>
      <span style="--i: 2">成</span>
      <span style="--i: 3">中</span> -->
      <span style="--i: 4">.</span>
      <span style="--i: 5">.</span>
      <span style="--i: 6">.</span>
    </div>
  </div>
</template>

<script setup>
import { GENERATE_MAP } from '@/constants/special-field'
</script>
