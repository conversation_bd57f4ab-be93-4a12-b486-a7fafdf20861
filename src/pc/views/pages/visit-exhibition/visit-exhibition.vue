<script setup>
import { useI18n } from 'vue-i18n'

const { t } = useI18n({
  messages: {
    zh: {
      visitExhibition: {
        time: '对洽会举办时间',
        contact: '联络人',
        product: '主导产业/产品',
        address: '专场地点',
        phone: '联系方式',
        exhibitionArea: '展区规划图',
        exhibitionAreaDistribution: '展区分布',
      },
    },
    en: {
      visitExhibition: {
        title: 'XXX',
        contact: 'XXX',
        product: 'XXX',
        address: 'XXX',
        phone: 'XXX',
        exhibitionArea: 'Exhibition Area Layout Plan',
        exhibitionAreaDistribution: 'Exhibition Area Distribution',
      },
    },
  },
})
const baseLists = [
  {
    title: { zh: '兰山区-商城集团专场', en: 'XXX' },
    x: '466px',
    y: '150px',
    titleTop: '-42px',
    activeW: '71px',
    activeH: '40px',
    activeLeft: '5px',
    activeTop: '102px',
  },
  { title: { zh: '兰华集团专场', en: 'XXX' }, x: '390px', y: '185px', titleTop: '0', activeW: '123px', activeH: '49px', activeLeft: '-3px', activeTop: '90px' },
  {
    title: { zh: '兰山区-华苑集团专场', en: 'XXX' },
    x: '560px',
    y: '205px',
    titleTop: '-20px',
    activeW: '124px',
    activeH: '35px',
    activeLeft: '-51px',
    activeTop: '97px',
  },
  {
    title: { zh: '2024临沂商城国际采购商大会河东分会场', en: 'XXX' },
    x: '1002px',
    y: '266px',
    titleTop: '0',
    activeW: '91px',
    activeH: '52px',
    activeLeft: '-10px',
    activeTop: '94px',
  },
  {
    title: { zh: '好多宝专场', en: 'XXX' },
    x: '127px',
    y: '329px',
    titleTop: '-16px',
    activeW: '129px',
    activeH: '92px',
    activeLeft: '-22px',
    activeTop: '87px',
  },
  {
    title: { zh: '商城控股集团专场', en: 'XXX' },
    x: '320px',
    y: '403px',
    titleTop: '-20px',
    activeW: '72px',
    activeH: '100px',
    activeLeft: '-7px',
    activeTop: '68px',
  },
  {
    title: { zh: '兰田集团专场', en: 'XXX' },
    x: '698px',
    y: '355px',
    titleTop: '-6px',
    activeW: '58px',
    activeH: '124px',
    activeLeft: '15px',
    activeTop: '47px',
  },
  {
    title: { zh: '批发商联合市场', en: 'XXX' },
    x: '864px',
    y: '386px',
    titleTop: '-5px',
    activeW: '57px',
    activeH: '114px',
    activeLeft: '14px',
    activeTop: '57px',
  },
  {
    title: { zh: '高新区-远通二手车专场', en: 'XXX' },
    x: '58px',
    y: '577px',
    titleTop: '-40px',
    activeW: '166px',
    activeH: '113px',
    activeLeft: '-42px',
    activeTop: '59px',
  },
  { title: { zh: '华丰集团专场', en: 'XXX' }, x: '561px', y: '550px', titleTop: '0', activeW: '74px', activeH: '85px', activeLeft: '6px', activeTop: '79px' },
  {
    title: { zh: '2024沂河新区国际合作及优势产业采购商大会', en: 'XXX' },
    x: '1063px',
    y: '642px',
    titleTop: '-20px',
    activeW: '129px',
    activeH: '116px',
    activeLeft: '-23px',
    activeTop: '52px',
  },
  {
    title: { zh: '货满堂专场', en: 'XXX' },
    x: '986px',
    y: '804px',
    titleTop: '-20px',
    activeW: '135px',
    activeH: '155px',
    activeLeft: '1px',
    activeTop: '21px',
  },
  {
    title: { zh: '临沂商城国际采购商大会罗庄区分会场', en: 'XXX' },
    x: '-233px',
    y: '694px',
    titleTop: '-40px',
    activeW: '123.5px',
    activeH: '70.5px',
    activeLeft: '-29px',
    activeTop: '82px',
  },
]

const currentIndex = ref(null)

onMounted(() => {
  document.body.style.minWidth = '1920px'
})
onUnmounted(() => {
  document.body.style.minWidth = ''
})

const details = ref({
  title: '兰华集团专场',
  time: '10月18日下午16:00',
  contact: 'XXX',
  product: '酒店用品，五金土杂、装饰板材、机电用品、塑料制品酒店用品，五金土杂、装饰板材、机电用品、塑料制品',
  address: 'XXX',
  phone: 'XXX',
})

const areaDetail = ref({
  title: '电池及其零部件',
  exhibitionAreaDistribution: 'AT005、AT005、AT005、AT005、AT005、AT005、AT005、AT005、AT005、AT005、AT005、AT005、AT005、AT005、AT005、AT005、AT005、AT005、',
})
</script>

<template>
  <div class="visit-exhibition">
    <div class="header">
      <div class="header-content">
        <div
          class="base"
          :class="{ active: currentIndex === index }"
          v-for="(item, index) in baseLists"
          :key="index"
          :style="{ left: item.x, top: item.y }"
          @click="currentIndex = index"
        >
          <div class="title" :style="{ 'position-anchor': '--anchor-' + index, top: item.titleTop }">{{ item.title[$i18n.locale] }}</div>
          <img src="https://static.chinamarket.cn/static/trade-exhibition/visit-exhibition/light.png" alt="" :style="{ 'anchor-name': '--anchor-' + index }" />
          <img
            v-show="currentIndex === index"
            class="active-img"
            :src="`https://static.chinamarket.cn/static/trade-exhibition/visit-exhibition/active${index}.png`"
            alt=""
            :style="{ width: item.activeW, height: item.activeH, left: item.activeLeft, top: item.activeTop }"
          />
        </div>
      </div>
    </div>
    <div v-show="currentIndex !== null" class="detail-wrap w-[1200px] h-[144px] bg-white m-auto mt-[-144px] px-[40px] py-[20px]">
      <div class="title-wrap flex items-center pb-[10px] mb-[12px]">
        <div class="icon-wrap w-[26px] h-[26px] mr-[12px]">
          <img src="https://static.chinamarket.cn/static/trade-exhibition/visit-exhibition/company.svg" dragable="false" />
        </div>
        <span>{{ details.title }}</span>
      </div>
      <div class="content flex">
        <div class="box">
          <div class="item">
            <span class="label">{{ t('visitExhibition.time') }}: </span>
            <span class="value">{{ details.time }}</span>
          </div>
          <div class="item">
            <span class="label">{{ t('visitExhibition.address') }}: </span>
            <span class="value">{{ details.address }}</span>
          </div>
        </div>
        <div class="box">
          <div class="item">
            <span class="label">{{ t('visitExhibition.contact') }}: </span>
            <span class="value">{{ details.contact }}</span>
          </div>
          <div class="item">
            <span class="label">{{ t('visitExhibition.phone') }}: </span>
            <span class="value">{{ details.phone }}</span>
          </div>
        </div>
        <div class="box">
          <div class="item">
            <span class="label">{{ t('visitExhibition.product') }}: </span>
            <span class="value">{{ details.product }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="bottom my-[40px] px-[20px]">
      <div class="title">
        <img src="https://static.chinamarket.cn/static/trade-exhibition/ai-daji/more-left.svg" dragable="false" />
        <span>{{ t('visitExhibition.exhibitionArea') }}</span>
        <img src="https://static.chinamarket.cn/static/trade-exhibition/ai-daji/more-right.svg" dragable="false" />
      </div>
      <div class="bottom-content p-[20px] bg-white rounded-3">
        <div class="exhibition-area-detail">
          <div class="title-wrap flex items-center pb-[10px] mb-[12px]">
            <div class="icon-wrap w-[26px] h-[26px] mr-[12px]">
              <img src="https://static.chinamarket.cn/static/trade-exhibition/visit-exhibition/area-title-icon.svg" dragable="false" />
            </div>
            <span>{{ areaDetail.title }}</span>
          </div>
          <div class="box flex">
            <div class="item flex">
              <span class="label mr-[10px]">{{ t('visitExhibition.exhibitionAreaDistribution') }}: </span>
              <span class="value flex-1">{{ areaDetail.exhibitionAreaDistribution }}</span>
            </div>
          </div>
        </div>
        <img class="w-[1840px] h-[1288px]" src="https://static.chinamarket.cn/static/trade-exhibition/visit-exhibition/exhibition-area.webp" alt="" srcset="" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.visit-exhibition {
  background: #f4f4f4;
  .header {
    height: 1080px;
    background: url('https://static.chinamarket.cn/static/trade-exhibition/visit-exhibition/visit-exhibition-bg.webp') no-repeat center;
    background-size: cover;
    &-content {
      position: relative;
      width: $main-width;
      margin: 0 auto;
      .base {
        position: absolute;
        cursor: pointer;
        .title {
          position: absolute;
          width: max-content;
          justify-self: anchor-center;
          padding: 10px 13px;
          font-size: 16px;
          font-weight: 600;
          color: #ffffff;
          border: 1px solid #ffc1c1;
          border-radius: 21px;
          text-shadow: 0px 2px 10px rgba(216, 0, 0, 0.5);
        }
        img {
          width: 81px;
          height: 141px;
          position: relative;
          z-index: 2;
        }
        &.active {
          .title {
            background: linear-gradient(270deg, rgba(255, 169, 169, 0) 0%, rgba(255, 82, 82, 0.8) 31%, rgba(255, 82, 82, 0.8) 71%, rgba(255, 169, 169, 0) 100%);
          }
          .active-img {
            position: absolute;
            z-index: 1;
          }
        }
      }
    }
  }
  .detail-wrap {
    position: relative;
    border-radius: 12px;
    z-index: 4;
    .content {
      gap: 51px;
      .box {
        flex-shrink: 0;
        font-size: 14px;
        color: #000000;
        .item:first-child {
          margin-bottom: 8px;
        }
      }
      .box:not(:first-child) {
        margin-left: 30px;
        padding-left: 16px;
        border-left: 1px solid #edf0f5;
      }
    }
  }
  .title-wrap {
    border-bottom: 1px solid #edeef1;
    .icon-wrap {
      text-align: center;
      line-height: 26px;
      border-radius: 34px;
      background: linear-gradient(270deg, #d8131a 0%, #e96f18 100%);
    }
    span {
      font-size: 26px;
      font-weight: 600;
      line-height: 26px;
      text-align: justify;
      background: linear-gradient(270deg, #d8131a 0%, #e96f18 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }
  .bottom {
    display: flex;
    flex-direction: column;
    align-items: center;
    .title {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      font-size: 40px;
      font-weight: 600;
      text-align: center;
      color: #333333;
      img {
        width: 45px;
        height: 16px;
      }
      span {
        margin: 0 16px;
      }
    }
    .exhibition-area-detail {
      display: flex;
      flex-direction: column;
      width: 1200px;
      margin: 20px auto;
      padding: 20px 40px;
      box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
      .box {
        font-size: 20px;
      }
    }
  }
}
</style>
