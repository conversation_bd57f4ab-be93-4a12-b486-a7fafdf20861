<template>
  <div class="banner-wrapper relative min-h-[640px]">
    <div class="banner-text-wrapper absolute top-[40%] left-[0px] z-2 w-[100%]">
      <div class="w-1260">
        <div class="banner-text font-600 text-[56px] text-white">
          <div class="text">不出去 眼前就是世界</div>
          <div class="text">走出去 世界就在眼前</div>
        </div>
      </div>
    </div>
    <swiper
      v-if="imgList.length"
      :loop="true"
      :autoplay="{
        delay: 2500,
        disableOnInteraction: false,
      }"
      :pagination="true"
      :modules="modules"
      class="w-[100%] h-[100%] bg-[#fff]"
    >
      <swiper-slide v-for="(item, i) in imgList" :key="i" class="h-[100%] swiper-slide-normal">
        <div class="w-[100%] h-[100%] flex justify-center items-center banner-item relative">
          <img-loader :src="item" :loading-img="`${item}?x-oss-process=image/resize,h_100`" img-class="largeImg" class="largeImg"></img-loader>
        </div>
      </swiper-slide>
      <swiper-slide v-for="(item, i) in imgLargeList" :key="i" class="h-[100%] swiper-slide-large">
        <div class="w-[100%] h-[100%] flex justify-center items-center banner-item relative">
          <img-loader :src="item" :loading-img="`${item}?x-oss-process=image/resize,h_100`" img-class="largeImg" class="largeImg"></img-loader>
        </div>
      </swiper-slide>
    </swiper>
  </div>
</template>

<script setup>
import { Autoplay } from 'swiper/modules'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { ossUrl } from '@/constants/common'

const modules = [Autoplay]

const imgList = [`${ossUrl}/government-services-banner3.jpg`]
const imgLargeList = [`${ossUrl}/government-services-banner2560-new.jpg`]
</script>

<style lang="scss" scoped>
.banner-wrapper {
  width: 100%;
  height: 100vh;
  min-height: 640px;
  margin-top: -64px;

  .banner-text {
    margin-bottom: 32px;
    .text {
      letter-spacing: 4px;
      text-shadow: 0px 0px 20px rgba(0, 0, 0, 0.3);
    }

    .red-text {
      font-size: 28px;
      width: max-content;
      font-family: PingFang SC;
      font-weight: 600;
      line-height: normal;
      background: linear-gradient(270deg, #d8131a 0%, #e96f18 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }
  }
}

.banner-item {
  .img-loader {
    width: 100%;
    height: 100%;
    object-fit: cover; /* 保持图片的宽高比 */
    object-position: center top; /* 图片居中 */

    :deep(img) {
      width: 100%;
      height: 100%;
      object-fit: cover; /* 保持图片的宽高比 */
      object-position: center top; /* 图片居中 */
    }
  }
}

:deep(.swiper-wrapper) {
  height: 100%;
}

.swiper-slide-large {
  display: none;
}

@media screen and (max-width: 1480px) {
  .banner-text {
    font-size: 44px;
  }
}

@media screen and (min-width: 2560px) {
  .banner-text {
    display: none;
  }

  .swiper-slide-normal {
    display: none;
  }

  .swiper-slide-large {
    display: block;
  }
}
</style>
