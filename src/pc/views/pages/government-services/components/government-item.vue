<template>
  <div class="government-item">
    <!-- 左侧数据 -->
    <div class="item-lt min-h-[300px] max-h-[300px] w-[600px] relative rounded-[8px] overflow-hidden shrink-0">
      <img-loader :src="serveInfo.logo" class="w-[600px] h-[100%] min-h-[300px] max-h-[300px]" style="box-sizing: border-box; object-fit: contain" />
      <!-- <div :class="`serve-tag flex justify-center items-center`" :style="{ backgroundColor: serveInfo.bgClass }">{{ serveInfo.serveType }}</div> -->
    </div>
    <!-- 右侧数据 -->
    <div class="min-h-[300px] w-[620px] flex flex-col justify-between">
      <div
        :class="`${(['en', 'thai', 'indonesian'].includes($storageLocale) ? 'text-[14px]' : 'text-[16px]', $storageLocale === 'ru' ? 'break-all' : '')} mb-[12px] text-#333333`"
      >
        <span class="text-[-20px] font-600">{{ serveInfo.companyName }}</span> {{ serveInfo.companyDetails }}
      </div>
      <div class="flex">
        <div class="w-[50%] shrink-0 pd-[24px]">
          <div class="tag-title">服务介绍</div>
          <ul :class="`${['thai', 'indonesian'].includes($storageLocale) ? 'text-[12px]' : 'text-[14px]'} tag-ul text-[#666]`">
            <li
              v-for="item in serveInfo.serveIntroduce"
              :class="`${['en', 'thai', 'indonesian'].includes($storageLocale) ? 'mb-[3px]' : 'mb-[10px]'}`"
              :key="item"
            >
              {{ item }}
            </li>
          </ul>
        </div>
        <div class="w-[50%] shrink-0">
          <div class="tag-title">服务优势</div>
          <ul :class="`${['thai', 'indonesian'].includes($storageLocale) ? 'text-[12px]' : 'text-[14px]'} tag-ul text-[#666]`">
            <li
              v-for="item in serveInfo.serveFeatures"
              :class="`${['en', 'thai', 'indonesian'].includes($storageLocale) ? 'mb-[3px]' : 'mb-[10px]'}`"
              :key="item"
            >
              {{ item }}
            </li>
          </ul>
        </div>
      </div>
      <div class="address-btn">
        <el-button class="btn" @click="jumpTo()">进入官网</el-button>
      </div>
    </div>
  </div>
</template>
<script setup>
const props = defineProps({
  serveInfo: {
    type: Object,
    default: () => ({}),
  },
})

// const emit = defineEmits(['handleShowCustomer'])
// const handleShowCustomer = (item) => {
//   emit('handleShowCustomer', item)
// }

const jumpTo = () => {
  window.open(props.serveInfo.address)
}
</script>

<style scoped lang="scss">
.government-item {
  display: flex;
  gap: 40px;
  & + .government-item {
    margin-top: 40px;
  }
}

.tag-title {
  width: max-content;
  font-size: 14px;
  padding: 2px 8px;
  background: linear-gradient(90deg, #6c5354 0%, #4a4e66 100%);
  color: #edd1a8;
  margin-bottom: 10px;
}

.static-btn {
  width: max-content;
  color: $primary-color;
  font-size: 12px;
  padding: 0 4px;
  border-radius: 2px;
  border: 1px solid $primary-color;
  cursor: pointer;
}

.serve-tag {
  position: absolute;
  top: 0;
  left: 0;
  min-width: 108px;
  height: 32px;
  border-radius: 0 0 8px 0;
  padding: 0 12px;
  z-index: 1;
  font-size: 16px;
  font-weight: bold;
  color: #fff;
}

.address-btn {
  height: 42px;
  min-height: 42px;
  display: inline-flex;
  align-items: flex-end;
  .btn {
    border: 2px solid #d8131a;
    color: #d8131a;
    padding: 10px 20px;
    &:hover {
      background-color: #d8131a;
      color: #fff;
    }
  }
}
</style>
