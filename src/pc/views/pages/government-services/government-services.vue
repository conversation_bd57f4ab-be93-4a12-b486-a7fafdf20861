<template>
  <div class="government">
    <!-- banner -->
    <banner></banner>
    <!-- 标题 -->
    <div class="g-title">
      <div class="g-tabs flex justify-center w-1260 h-130px bg-white w-1260px flex rounded-tr-8px rounded-tl-8px">
        <div class="title-header">
          <div class="title-text" :style="{ fontSize: $storageLocale === 'ru' ? '32px' : '' }">政府服务站群</div>
        </div>
      </div>
    </div>
    <!-- 内容 -->
    <div class="content">
      <div class="wrap w-[1260px]">
        <!-- <div class="wrap-item" v-for="(item, index) in wrapList" :key="index">
          <div class="item-lt">
            <el-image class="item-img w-full h-full" :src="item.img" />
          </div>
          <div class="item-rt">
            <div class="info">
              <span class="info-title">{{ item.title }}</span>
              <span class="info-content">
                <span>{{ item.content }}</span>
              </span>
            </div>
            <div class="address">
              <el-button class="btn" @click="jumpTo(item)">进入官网</el-button>
            </div>
          </div>
        </div> -->
        <ServeStaticItem v-for="(item, i) in governmentData" :key="i" :serveInfo="item"></ServeStaticItem>
      </div>
    </div>
  </div>
</template>

<script setup>
// import { useWindowScroll } from '@vueuse/core'
import banner from './components/banner.vue'
import ServeStaticItem from './components/government-item.vue'
import { governmentData } from './data-options.js'

// const { y } = useWindowScroll()
// const isFixed = computed(() => {
//   return y.value > 0
// })
</script>

<style lang="scss" scoped>
.government {
  min-height: $main-height;
  .g-title {
    margin-top: -130px;
    position: relative;
    z-index: 1;
  }

  // .g-tabs {
  // }
}

.title-header {
  padding: 24px 0;
  .title-text {
    font-size: 40px;
    color: #191919;
    font-weight: 600;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    &::before,
    &::after {
      content: '';
      display: block;
      width: 45px;
      height: 16px;
      margin: 0 12px;
      background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAAAgCAYAAACSEW+lAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAK7SURBVGiB7ZmxTttQFIb/c2+MQ6hCY0ETGEAqrpAqeI8sWSt1ZGPtG+QNWNkYkViz8B6gSiihUjoAKchpoxIcnHtPBxKIAjK+DikM9xuv/uP/+sjy9fFPGFAFxHaxOHt2Tbls1s0AQBj2+stz3N1ttW6qgMYLUIfvhoXOuhRyDYI8AIDmQGl1mm3nTz6h0XsRH8/LK+FWwKIM8NrdKp2C9KHUvdqnIOi8hE9zfqXQdaItYvoKYGOwfMzE+7nI2Vv987MNAAQADMhGqeSpvuM8dTGZiSL/4iIgQE2yqV+Li+8CJcs8bPAYpDnwpDr8cHn5dxKf7wsLS0LP7IDgPylgNLS4/fb56up8Ep8f75dWbwVqINp82oePZjQqH3+fN0UVEHFNBgDVd5xGqeRVAZF2U3X4blyTAYAFeYGS5Tp8N7WP5+VjmwwABF/omZ265+XT+jTnVwqxTQYAos1bgVpzfqUgtovF2bgmD1F9x9kuFmfTbiwsdNbjmjyEBXlhobOe1kcJtxLb5CEEXwm3ktan60RbsU2+96HNrhNtibNryiW9uIl2HCnk2jS0j2BRnop2jME7ObFWDA++JJhoR2GAkOBpvsdEO8IBIB8OviSYaEeqAImHgy8JG6nfuRYzRBj2+knFJtpRCGBoDhIXmGhH+AIogE6TV5hoR6ruvr6ODUqOxfIcd5OqTbTjKK0S35SJ9hGkD6eiHYOJ9020YrfVupGZKHpOLDNRtNtq3aTdWLadP6EETyppDrLt/ElaH6l7NTAazwoZDal7tbQ+ucjZA/PR8z58lIucPTuwTIDJwELDtaodwVORdAR/VRggHvwGmCYHgLz79JsuDEj+Dz4Wi8VisVgsllfHDiwTYjPDNzaC28wwJTYztJmhzQxtZmiIzQzfMDYzTIHNDAGbGdrMEHZgSYvNDPH2RvB/dk+ggddFU7AAAAAASUVORK5CYII=')
        no-repeat center center/100% 100%;
    }
    &::after {
      background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAAAgCAYAAACSEW+lAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAK7SURBVGiB7ZmxTttQFIb/c2+MQ6hCY0ETGEAqrpAqeI8sWSt1ZGPtG+QNWNkYkViz8B6gSiihUjoAKchpoxIcnHtPBxKIAjK+DikM9xuv/uP/+sjy9fFPGFAFxHaxOHt2Tbls1s0AQBj2+stz3N1ttW6qgMYLUIfvhoXOuhRyDYI8AIDmQGl1mm3nTz6h0XsRH8/LK+FWwKIM8NrdKp2C9KHUvdqnIOi8hE9zfqXQdaItYvoKYGOwfMzE+7nI2Vv987MNAAQADMhGqeSpvuM8dTGZiSL/4iIgQE2yqV+Li+8CJcs8bPAYpDnwpDr8cHn5dxKf7wsLS0LP7IDgPylgNLS4/fb56up8Ep8f75dWbwVqINp82oePZjQqH3+fN0UVEHFNBgDVd5xGqeRVAZF2U3X4blyTAYAFeYGS5Tp8N7WP5+VjmwwABF/omZ265+XT+jTnVwqxTQYAos1bgVpzfqUgtovF2bgmD1F9x9kuFmfTbiwsdNbjmjyEBXlhobOe1kcJtxLb5CEEXwm3ktan60RbsU2+96HNrhNtibNryiW9uIl2HCnk2jS0j2BRnop2jME7ObFWDA++JJhoR2GAkOBpvsdEO8IBIB8OviSYaEeqAImHgy8JG6nfuRYzRBj2+knFJtpRCGBoDhIXmGhH+AIogE6TV5hoR6ruvr6ODUqOxfIcd5OqTbTjKK0S35SJ9hGkD6eiHYOJ9020YrfVupGZKHpOLDNRtNtq3aTdWLadP6EETyppDrLt/ElaH6l7NTAazwoZDal7tbQ+ucjZA/PR8z58lIucPTuwTIDJwELDtaodwVORdAR/VRggHvwGmCYHgLz79JsuDEj+Dz4Wi8VisVgsllfHDiwTYjPDNzaC28wwJTYztJmhzQxtZmiIzQzfMDYzTIHNDAGbGdrMEHZgSYvNDPH2RvB/dk+ggddFU7AAAAAASUVORK5CYII=')
        no-repeat center center/100% 100%;
      transform: rotate(180deg);
    }
  }
}

// .text-ellipsis {
//   text-overflow: ellipsis;
//   overflow: hidden;
//   white-space: nowrap;
// }

// .text-ellipsis-1 {
//   text-overflow: ellipsis;
//   overflow: hidden;
//   /* stylelint-disable-next-line */
//   display: -webkit-box;
//   /* stylelint-disable-next-line */
//   -webkit-box-orient: vertical;
//   -webkit-line-clamp: 1;
//   word-break: break-all;
// }

.content {
  background-color: #fff;
  min-height: 800px;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  .wrap {
    padding: 0 40px;
    padding-bottom: 70px;
  }
  .wrap-item {
    height: 300px;
    display: flex;
    gap: 40px;
    & + .wrap-item {
      margin-top: 40px;
    }

    .item-lt {
      width: 600px;
      min-width: 600px;
      border-radius: 12px;
      .item-img {
        border-radius: inherit;
      }
    }

    .item-rt {
      flex: 1;
      display: flex;
      flex-direction: column;
      .info {
        height: calc(100% - 42px);
        letter-spacing: 1px;
        overflow-y: auto;

        .info-title {
          font-size: 20px;
          font-weight: 600;
          margin-right: 10px;
        }
        .info-content {
          font-size: 16px;
        }
      }
      .address {
        height: 42px;
        min-height: 42px;
        display: inline-flex;
        align-items: flex-end;
        .btn {
          border: 2px solid #257bfb;
          color: #257bfb;
          padding: 10px 20px;
          &:hover {
            background-color: #257bfb;
            color: #fff;
          }
        }
      }
    }
  }
}
</style>
