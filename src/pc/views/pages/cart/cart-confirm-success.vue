<template>
  <div class="h-[158px] bg-white"></div>
  <div class="logo-nav">
    <SearchInput />
  </div>
  <div class="bg-white pb-300px">
    <!-- 订单提交成功容器 -->
    <div class="g-content-warp bg-[#F7F8FC] rounded-lg p-10 flex flex-col items-center gap-10">
      <!-- 成功图标 -->
      <div class="relative flex flex-col justify-center items-center">
        <!-- 主文档图标 -->
        <img class="w-200px" :src="`${ossUrl}/cart/cart_success.png`" />
      </div>
      <!-- 标题和说明文字 -->
      <div class="flex flex-col items-center gap-2 -mt-10">
        <!-- 主标题 -->
        <div class="flex justify-center">
          <h2 class="text-[18px] font-semibold text-[#1A1A1A] leading-[25px]">已提交订单</h2>
        </div>

        <!-- 说明文字 -->
        <div class="w-[420px] text-center">
          <p class="text-[14px] text-[#888B94] leading-[20px]">
            订单处理中，正在为您对接供应商，稍后与您联系，或拨打客服热线<br />
            +86 198 6095 9205 咨询进度
          </p>
        </div>
      </div>

      <!-- 操作按钮组 -->
      <div class="flex gap-6 text-[16px] font-semibold">
        <!-- 返回首页按钮 -->
        <div
          class="cursor-pointer px-40px py-8px border border-solid border-[#D8131A] rounded text-[#D8131A] hover:bg-[#D8131A] hover:text-white transition-colors duration-200"
          @click="goHome"
        >
          返回首页
        </div>

        <!-- 查看订单按钮 -->
        <div class="cursor-pointer px-40px py-8px bg-[#D8131A] rounded text-white hover:bg-[#B8111A] transition-colors duration-200" @click="viewOrder">
          查看订单
        </div>
      </div>
    </div>
    <recommend />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import SearchInput from '@/pc/components/search-input/search-input.vue'
import recommend from './components/recommend.vue'
import { ossUrl } from '@/constants/common'

const router = useRouter()

const orderId = ref('')

// 返回首页
const goHome = () => {
  router.push('/')
}
// 获取订单id
const getOrderId = () => {
  orderId.value = router.currentRoute.value.query.id
}
getOrderId()
// 查看订单
const viewOrder = () => {
  router.push(`/buyer-center/order-detail?id=${orderId.value}`)
}
</script>

<style scoped lang="scss">
/* 主题红色变量 */
:root {
  --theme-red: #d8131a;
  --white: #ffffff;
}
$width-small: 1140px;
$width-medium: 1260px;
$width-large: 1440px;

.g-content-warp {
  margin: 0 auto;
  width: $width-small;

  @media (min-width: 1550px) and (max-width: 1620px) {
    width: $width-medium;
  }

  @media (min-width: 1620px) {
    width: $width-large;
  }
}
.logo-nav {
  position: fixed;
  top: 64px;
  z-index: 49;
  width: 100%;
  height: 64px;
  margin: 0 auto 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.1);
  background-color: #fff;
}
</style>
