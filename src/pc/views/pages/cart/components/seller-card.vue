<template>
  <el-card class="seller-card" shadow="never">
    <!-- 商家信息头部 -->
    <div class="flex items-center justify-between border-b border-[#F0F0F0] pb-8px">
      <div class="flex items-center gap-3">
        <el-checkbox v-if="isSelect" :model-value="isSellerAllSelected" @change="handleSellerSelectAll" size="large" />
        <el-avatar :src="seller.shopLogo" :size="24" class="bg-[#D8131A]">
          <el-icon><Shop /></el-icon>
        </el-avatar>
        <span class="text-base font-medium text-[#1A1A1A]">{{ seller.companyName }}</span>
      </div>
    </div>

    <!-- 商品列表 -->
    <div class="bg-white rounded-8px px-16px py-24px">
      <SellerItem
        v-for="item in seller.goodsList"
        :key="item.skuId"
        :item="item"
        :is-lose-goods="isLoseGoods"
        :is-select="isSelect"
        :choose-currency="chooseCurrency"
        @select-change="handleItemSelectChange"
        @quantity-change="handleItemQuantityChange"
        @delete-item="handleDeleteItem"
      />
    </div>
    <div v-if="!isSelect" class="text-14px text-[#1A1A1A] font-500 flex flex-col gap-8px mt-16px">
      <div class="flex items-center gap-8px" v-if="deliveryAddress?.addressType === 1">
        <div class="w-130px flex-shrink-0">运费</div>
        <el-input
          :model-value="seller.deliveryPrice"
          @input="(value) => updateSellerPrice('deliveryPrice', value)"
          type="number"
          placeholder="与商家协商一致后填写"
          class="flex-1 rounded-8px m-input"
        />
      </div>
      <div class="flex items-center gap-8px" v-else-if="deliveryAddress?.addressType === 0">
        <div class="w-130px flex-shrink-0">外贸综合服务费</div>
        <el-input
          :model-value="seller.servicePrice"
          @input="(value) => updateSellerPrice('servicePrice', value)"
          type="number"
          placeholder="选填，请先和商家协商一致后填写"
          class="flex-1 rounded-8px m-input"
        />
      </div>
      <div class="flex items-center gap-8px">
        <div class="w-130px flex-shrink-0">留言</div>
        <el-input
          :model-value="seller.remarks"
          @input="(value) => updateSellerRemarks('remarks', value)"
          placeholder="选填，请先和商家协商一致"
          class="flex-1 rounded-8px m-input"
        />
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { Shop } from '@element-plus/icons-vue'
import { computed } from 'vue'
import SellerItem from './seller-item.vue'

// 组件属性
const props = defineProps({
  seller: {
    type: Object,
    required: true,
  },
  // 是否为失效商品
  isLoseGoods: {
    type: Boolean,
    default: false,
  },
  // 是否需要选中
  isSelect: {
    type: Boolean,
    default: true,
  },
  deliveryAddress: {
    type: Object,
    default: () => {},
  },
  // 选择的币种
  chooseCurrency: {
    type: Number,
    default: 1,
  },
})

// 组件事件
const emit = defineEmits([
  'seller-select-all',
  'delete-seller',
  'item-select-change',
  'item-quantity-change',
  'delete-item',
  'update-seller-price',
  'update-seller-remarks',
])

// 检查商家是否全选
const isSellerAllSelected = computed(() => {
  if (!props.seller.goodsList || props.seller.goodsList.length === 0) return false
  return props.seller.goodsList.every((item) => item.select)
})

// 商家全选/取消全选
const handleSellerSelectAll = (checked) => {
  emit('seller-select-all', props.seller, checked)
}

// 商品选择状态改变
const handleItemSelectChange = (item, checked) => {
  emit('item-select-change', item, checked)
}

// 商品数量变化
const handleItemQuantityChange = (item, newCount) => {
  emit('item-quantity-change', item, newCount)
}

// 删除商品
const handleDeleteItem = (item) => {
  emit('delete-item', item)
}

// 更新商家价格
const updateSellerPrice = (key, value) => {
  if (String(value).length > 15) {
    return
  }
  emit('update-seller-price', props.seller, key, value)
}

// 更新商家备注
const updateSellerRemarks = (key, value) => {
  emit('update-seller-remarks', props.seller, key, value)
}
</script>

<style lang="scss" scoped>
/* Element UI Plus 样式自定义 */
.seller-card {
  background-color: #f7f8fc;
  border: none;
  border-radius: 8px;
  margin-bottom: 20px;
}

.seller-card :deep(.el-card__body) {
  padding: 20px;
}

.checked-color {
  color: #000;
  &:hover {
    color: #d8131a;
  }
}

/* 复选框样式 */
:deep(.el-checkbox) {
  .el-checkbox__inner {
    border: 1px solid #000;
    width: 16px;
    height: 16px;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #d8131a;
    border-color: #d8131a;
  }
  .el-checkbox__inner:after {
    width: 4px;
    height: 8px;
  }
  .el-checkbox__label {
    color: #1a1a1a;
    font-weight: 500;
  }
  .el-checkbox__inner:hover {
    border-color: #d8131a;
  }
}

/* 头像样式 */
:deep(.el-avatar) {
  background-color: #d8131a;
}

/* 输入框样式 */
:deep(.m-input) {
  .el-input__wrapper {
    box-shadow: none !important;
    padding: 3px 11px;
  }
}
</style>
