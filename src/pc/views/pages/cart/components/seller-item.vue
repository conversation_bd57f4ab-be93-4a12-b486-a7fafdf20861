<template>
  <div class="flex items-center gap-16px border-0 border-b border-solid border-[#F0F0F0] last:border-b-0">
    <!-- 商品选择框 -->
    <template v-if="isSelect">
      <el-checkbox v-if="!isLoseGoods" :model-value="item.select" @change="handleItemSelect" size="large" />
      <el-checkbox v-else :disabled="isLoseGoods" @change="handleItemSelect" size="large" />
    </template>

    <!-- 商品信息 -->
    <div class="flex items-center gap-4 flex-1 py-8px">
      <!-- 商品图片 -->
      <div @click="handleGoodsDetail" class="cursor-pointer size-80px bg-white flex-shrink-0 relative overflow-hidden" :class="{ grayscale: isLoseGoods }">
        <img :src="item.goodsImage" :alt="item.goodsName" class="w-full h-full object-cover" />
        <!-- 出口转内销 -->
        <div class="absolute left-0 top-0 z-10 w-[60px] h-[24px] img-logo" v-if="item?.showType === 1">
          <img-loader
            :src="bannarImgNow"
            class="w-[100%] h-[100%] object-cover cursor-pointer"
            :loading-img="`${bannarImgNow}?x-oss-process=image/resize,h_100`"
          ></img-loader>
        </div>
        <!-- 失效标记 -->
        <div v-if="isLoseGoods" class="absolute inset-0 flex items-center justify-center">
          <div class="text-12px text-white bg-black bg-opacity-70 rounded-full py-8px px-4px">失效</div>
        </div>
      </div>

      <!-- 商品详情 -->
      <div @click="handleGoodsDetail" class="cursor-pointer flex-1">
        <h3 class="text-[#1A1A1A] font-normal line-clamp-2 leading-5">
          {{ item.goodsName }}
        </h3>
        <div class="text-[#888B94] my-4px">
          {{ getSpecText(item.spec) }}
        </div>
        <div class="font-medium text-[#D8131A]">
          <c-symbol v-if="isSelect" />
          <c-symbol v-else :price-type="chooseCurrency" />
          {{ item.price }}
        </div>
      </div>

      <!-- 数量控制 -->
      <div class="flex items-center relative" v-if="isSelect">
        <el-input-number
          :disabled="isLoseGoods"
          :model-value="item.count"
          @update:model-value="handleQuantityChange"
          :min="1"
          :max="item.stock"
          class="m-number"
        />
        <div class="absolute -bottom-25px w-full text-[#D8131A] text-12px whitespace-nowrap" v-if="item.errorMessage && !isLoseGoods">
          {{ item.errorMessage }}
        </div>
      </div>
      <div v-else class="text-[#1A1A1A] text-14px skip-translate min-w-100px">X{{ item.count }}</div>

      <!-- 小计 -->
      <div class="flex items-center justify-end w-28 px-4">
        <span class="font-medium text-[#D8131A] whitespace-nowrap">
          <c-symbol v-if="isSelect" />
          <c-symbol v-else :price-type="chooseCurrency" />
          {{ item.finalPrice.toFixed(2) }}
        </span>
      </div>

      <!-- 删除按钮 -->
      <div class="flex items-center px-4" v-if="isSelect">
        <div @click="handleDeleteItem" class="text-[#888B94] hover:text-[#D8131A] cursor-pointer" :class="{ 'checked-color': item.select && !isLoseGoods }">
          <Icon type="icon-shanchu1" :size="16" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineEmits, defineProps } from 'vue'
import { useRouter } from 'vue-router'
import { BANNARIMG } from '@/constants/index'
import { useStorageLocale } from '@/i18n/translatePlugin'
import { debounce } from '@/common/js/util'

// 组件属性
const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
  // 是否失效
  isLoseGoods: {
    type: Boolean,
    default: false,
  },
  // 是否需要选中
  isSelect: {
    type: Boolean,
    default: true,
  },
  // 选择的币种
  chooseCurrency: {
    type: Number,
    default: 1,
  },
})

const { storageLocale } = useStorageLocale()
const bannarImgNow = computed(() => {
  const img = BANNARIMG[storageLocale.value] || BANNARIMG.zh
  return img
})
// 组件事件
const emit = defineEmits(['select-change', 'quantity-change', 'delete-item'])

// 解析规格信息
const getSpecText = (specJson) => {
  try {
    if (!specJson) return ''
    const specs = JSON.parse(specJson)
    return specs.map((spec) => `${spec.spec}: ${spec.item.value}`).join(', ')
  } catch (e) {
    return specJson || ''
  }
}

const router = useRouter()
const handleGoodsDetail = debounce(() => {
  if (props.isLoseGoods) {
    emit('delete-item', props.item)
    return
  }
  router.push(`/mall/goods-detail/${props.item.spuId}`)
}, 300)
// 商品选择状态改变
const handleItemSelect = (checked) => {
  emit('select-change', props.item, checked)
}

// 数量变化
const handleQuantityChange = (newValue) => {
  if (newValue) {
    emit('quantity-change', props.item, newValue)
  } else {
    emit('quantity-change', props.item, 1)
  }
}

// 删除商品
const handleDeleteItem = () => {
  emit('delete-item', props.item)
}
</script>

<style lang="scss" scoped>
/* 文本行数限制 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.grayscale {
  filter: grayscale(1);
}

/* 数量输入框样式 */
:deep(.el-input-number) {
  .el-input__inner {
    text-align: center;
    border-radius: 4px;
  }
}

:deep(.m-number.el-input-number) {
  line-height: 24px;
  .el-input-number__decrease,
  .el-input-number__increase {
    background: none;
  }
  .el-input__wrapper {
    box-shadow: 0 0 0 1px #f0f0f0 inset;
  }
}

/* 复选框样式 */
:deep(.el-checkbox) {
  .el-checkbox__inner {
    border: 1px solid #000;
    width: 16px;
    height: 16px;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #d8131a;
    border-color: #d8131a;
  }
  .el-checkbox__inner:after {
    width: 4px;
    height: 8px;
  }
  .el-checkbox__label {
    color: #1a1a1a;
    font-weight: 500;
  }
  .el-checkbox__inner:hover {
    border-color: #d8131a;
  }
}

.checked-color {
  color: #000;
  &:hover {
    color: #d8131a;
  }
}
</style>
