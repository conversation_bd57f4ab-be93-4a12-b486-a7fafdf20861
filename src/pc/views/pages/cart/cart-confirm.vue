<template>
  <div class="h-[158px] bg-white"></div>
  <div class="logo-nav">
    <SearchInput />
  </div>
  <div class="bg-white pb-30px">
    <div class="g-content-warp" v-loading="calculateLoading">
      <!-- 页面标题 -->
      <div class="flex items-center justify-between mb-16px">
        <h1 class="text-24px font-semibold text-[#1A1A1A]">提交订单</h1>
      </div>
      <div class="flex">
        <div class="flex-1">
          <div class="text-18px font-600 text-[#1A1A1A] mb-8px flex items-center justify-between">
            收货地址
            <el-divider class="flex-1 mx-32px! my-0!" />
            <div class="text-14px font-normal text-[#505259] cursor-pointer flex items-center" @click="editDeliveryAddressClick">
              修改收货地址
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
          <div
            v-if="deliveryAddressInit"
            class="text-14px font-500 text-[#1A1A1A] mb-8px flex items-center gap-4 justify-center bg-[#f7f8fc] p-16px rounded-8px cursor-pointer select-none"
          >
            <div class="hover:text-[#D8131A]" v-if="!deliveryAddress" @click="handleAddAddress">新建收货地址</div>
            <div v-else class="flex items-start justify-between w-full">
              <div class="mr-16px mt-2px">
                <Icon type="icon-dizhi1" :size="20" />
              </div>
              <div class="flex-1">
                <div class="text-14px font-500 text-[#1A1A1A] flex items-center break-all">
                  {{ addressStr }}
                  <div v-if="!deliveryAddress?.status" class="text-12px font-400 text-[#D8131A] ml-16px flex items-center break-keep">
                    <Icon class="mr-4px" type="icon-jinggaoAlert1" :size="14" />
                    请完善收货地址
                    <span
                      class="text-12px font-500 text-[#D8131A] ml-8px border border-[#F0F0F0] bg-white rounded-4px px-8px py-4px flex items-center"
                      @click="handleEditAddress"
                    >
                      <Icon class="mr-4px" type="icon-bianji1" :size="16" />
                      编辑
                    </span>
                  </div>
                </div>
                <div class="text-14px text-[#505259] mt-8px">{{ deliveryAddress?.deliveryName || '' }} {{ deliveryAddress?.deliveryPhone || '' }}</div>
              </div>
            </div>
          </div>
          <!-- 订单信息 -->
          <div class="text-18px font-600 text-[#1A1A1A] mb-8px">商品信息</div>
          <!-- 加载状态 -->
          <div class="min-h-300px">
            <!-- 购物车商品列表 -->
            <div v-if="!calculateLoading && orderList.length > 0" class="space-y-5">
              <!-- 按商家分组的商品 -->
              <SellerCard
                v-for="seller in orderList"
                :key="seller.id"
                :seller="seller"
                :is-select="false"
                @update-seller-price="handleUpdateSellerPrice"
                @update-seller-remarks="handleUpdateSellerRemarks"
                :delivery-address="deliveryAddress"
                :choose-currency="lazyChooseCurrency"
              />
            </div>
            <!-- 空购物车状态 -->
            <div v-if="!initLoading && !calculateLoading && orderList.length === 0" class="text-center py-20">
              <el-empty description="选品车空空如也">
                <el-button type="primary">去选品</el-button>
              </el-empty>
            </div>
          </div>
        </div>
        <div class="w-48px"></div>
        <!-- 订单汇总和结算 -->
        <div v-if="orderList.length > 0" class="my-20px w-33% -mt-50px relative" id="fixedPanelBox">
          <el-card class="checkout-panel" id="fixedPanel">
            <!-- 汇总标题 -->
            <div class="border-0 border-solid border-b border-[#F0F0F0] pb-16px mb-16px">
              <h3 class="text-lg font-semibold text-[#1A1A1A]">订单小结 ({{ getTotalItemCount }}件商品)</h3>
            </div>

            <!-- 价格信息 -->
            <div class="space-y-4">
              <div class="text-16px font-500 text-[#1A1A1A]">价格明细</div>
              <div class="flex items-center justify-between">
                <span class="text-[#505259]">产品价格</span>
                <span class="text-[#505259]">
                  <c-symbol :price-type="lazyChooseCurrency" />
                  {{ formatPrice(calculateData.orderPrice) }}
                </span>
              </div>
              <div class="flex items-center justify-between" v-if="deliveryAddress?.addressType === 1">
                <span class="text-[#505259]">运费</span>
                <span class="text-[#505259]">
                  <c-symbol :price-type="lazyChooseCurrency" />
                  {{ formatPrice(calculateData.deliveryPrice) }}
                </span>
              </div>
              <div class="flex items-center justify-between" v-if="deliveryAddress?.addressType === 0">
                <span class="text-[#505259]">外贸综合服务费</span>
                <span class="text-[#505259]">
                  <c-symbol :price-type="lazyChooseCurrency" />
                  {{ formatPrice(calculateData.servicePrice) }}
                </span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-[#505259]">合计</span>
                <span class="text-lg font-medium text-[#D8131A]">
                  <c-symbol :price-type="lazyChooseCurrency" />
                  {{ formatPrice(calculateData.finalPrice) }}
                </span>
              </div>
            </div>

            <div class="border-0 border-solid border-b border-t border-[#F0F0F0] py-16px mb-16px flex items-center justify-between">
              <h3 class="text-16px font-500 text-[#1A1A1A]">支付币种</h3>
              <div class="flex items-center justify-between flex-1">
                <el-select class="m-select" placement="bottom-end" v-model="chooseCurrency" @change="onChangePayPrice">
                  <el-option v-for="item in payPriceOptions" :key="item.id" :label="item.label" :value="item.id">
                    <span class="skip-translate">{{ item.currency }}</span
                    >-<span>{{ item.value }}</span>
                  </el-option>
                  <template #label>
                    <span class="skip-translate">{{ fitPayPriceOptions().currency }}</span
                    >-<span>{{ fitPayPriceOptions().value }}</span>
                  </template>
                </el-select>
              </div>
            </div>

            <div class="text-16px font-500 text-[#1A1A1A]">支付方式</div>
            <!-- 本地支付: AED、IDR -->
            <template v-if="chooseCurrency === 3 || chooseCurrency === 4">
              <el-radio>本地支付</el-radio>
              <div class="text-12px text-[#888B94]">请根据收款账户进行线下转账，并上传付款凭证</div>
            </template>
            <!-- T/T电汇 -->
            <template v-else-if="chooseCurrency === 2">
              <el-radio>T/T电汇</el-radio>
              <div class="text-12px text-[#888B94]">请根据收款账户通过您当地的银行进行电汇，并上传付款凭证</div>
            </template>
            <!-- 对公转账 -->
            <template v-else>
              <el-radio>对公转账</el-radio>
              <div class="text-12px text-[#888B94]">请根据收款账户进行线下转账，并上传付款凭证</div>
            </template>

            <!-- 结算按钮 -->
            <div class="mt-6">
              <el-button
                type="primary"
                size="large"
                @click="handleCheckout"
                :loading="calculateLoading || submitLoading"
                class="w-full bg-[#D8131A] border-[#D8131A] hover:bg-[#B8111A] hover:border-[#B8111A]"
              >
                {{ submitLoading ? '计算中...' : '结算' }}
              </el-button>
            </div>
          </el-card>
        </div>
      </div>
    </div>
    <SelectDeliveryAddress ref="selectDeliveryAddressRef" :is-wait-submit-order="true" @confirm-click="setAddress" />
    <DeliveryAddress ref="deliveryAddressRef" @confirm-click="setAddress" />
  </div>
</template>
<script setup>
import { ArrowRight } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import SearchInput from '@/pc/components/search-input/search-input.vue'
import DeliveryAddress from '@/pc/views/pages/merchants/buyer-center/components/delivery-address.vue'
import SelectDeliveryAddress from '@/pc/views/pages/merchants/buyer-center/components/select-delivery-address.vue'
import SellerCard from './components/seller-card.vue'
import { useCartStore } from '@/pc/stores'
import { useDictStore } from '@/pc/stores'
import { getAddressById, getAddressList, getDefaultAddress } from '@/apis/mine'
import { calculate, getUserCartList, submitOrder } from '@/apis/order'
import { debounce, sleepFn } from '@/common/js/util'
import user from '@/pc/utils/user'

const localType = useRoute().query?.from === 'sku'
const cartList = ref([])
const finalPrice = ref(0)
const calculateLoading = ref(false)
const deliveryAddressInit = ref(false)
const submitLoading = ref(false)
const cartStore = useCartStore()
const dictStore = useDictStore()
const calculateData = ref({})
const orderList = ref([])
let currentPriceType = user.getPayPrice()

const chooseCurrency = ref(user.getPayPrice())
const lazyChooseCurrency = ref(user.getPayPrice())

const initLoading = ref(true)
watch(calculateLoading, (newVal) => {
  if (!newVal && orderList.value.length === 0) {
    initLoading.value = false
  }
})
const payPriceOptions = computed(
  () =>
    dictStore.payPriceOptions.map((el) => {
      return {
        label: `${el.currency}（${el.value}）`,
        ...el,
      }
    }) || [],
)
const fitPayPriceOptions = () => {
  const item = payPriceOptions.value.filter((el) => el.id === chooseCurrency.value) || []
  if (item.length > 0) {
    return {
      currency: item[0].currency || '',
      value: item[0].value || '',
    }
  }
  return {
    currency: '',
    value: '',
  }
}
const onChangePayPrice = (currency) => {
  chooseCurrency.value = currency
  handleCalculate()
}
const formatPrice = (price) => {
  return Number(price) ? Number(price).toFixed(2) : '0.00'
}

// 获取总商品数量
const getTotalItemCount = computed(() => {
  if (localType) {
    return orderList.value.reduce((acc, seller) => {
      return acc + seller.goodsList.length || 0
    }, 0)
  }
  return orderList.value.reduce((acc, seller) => {
    return acc + seller.goodsList?.filter((item) => item.select).length || 0
  }, 0)
})

const deliveryAddressRef = ref(null)
// 新增地址
const handleAddAddress = () => {
  deliveryAddressRef.value.init()
}
const handleEditAddress = () => {
  deliveryAddressRef.value.init(deliveryAddress.value?.id)
}
// 修改收货地址
const selectDeliveryAddressRef = ref(null)
const editDeliveryAddressClick = () => {
  selectDeliveryAddressRef.value.init(deliveryAddress.value, 'id')
}
const addressStr = computed(() => {
  if (!deliveryAddress.value) return ''
  const { addressType = '', provinceName = '', cityName = '', areaName = '', address = '', countryName = '', postalCode = '' } = deliveryAddress.value || {}
  if (addressType) {
    return `${addressType ? '中国' : '海外'} | ${provinceName} ${cityName}  ${areaName} ${address}`
  } else {
    return `${addressType ? '中国' : '海外'} | ${address} ${cityName} ${provinceName} ${postalCode} ${countryName}`
  }
})
const setAddress = (item) => {
  deliveryAddress.value = item
  handleCalculate()
}

const priceFormat = (val) => {
  if ((+val && val > 0) || val === '0') {
    const newVal = Math.round(val * 100) / 100
    if (newVal === 0) return 0
    // 保留两位小数
    return `${Math.round(val * 100) / 100}`
  } else {
    return ''
  }
}
// 更新商家价格
const handleUpdateSellerPrice = (seller, key, value) => {
  const index = orderList.value.findIndex((item) => item.id === seller.id)
  if (index !== -1) {
    orderList.value[index][key] = priceFormat(value)
  }
  // 计算价格，1秒内，如果有新价格，则取消之前的请求，请求方法为：handleCalculate
  handleCalculate()
}

// 更新商家备注
const handleUpdateSellerRemarks = (seller, key, value) => {
  const index = orderList.value.findIndex((item) => item.id === seller.id)
  if (index !== -1) {
    orderList.value[index][key] = value
  }
}
// 获取购物车列表
const getList = async () => {
  try {
    const data = await getUserCartList({ init: true })
    cartList.value = (data.sellerList || [])
      .filter((item) => {
        return item.goodsList.some((_item) => _item.select)
      })
      .map((item) => ({
        ...item,
        servicePrice: item.servicePrice || null,
        deliveryPrice: item.deliveryPrice || null,
        remarks: item.remarks || null,
        goodsList: item.goodsList.filter((_item) => _item.select),
      }))
    if (cartList.value.length <= 0) {
      jumpNoOrderList()
      return
    }
    handleCalculate()
    finalPrice.value = data.finalPrice || 0
    cartStore.updateCartCount(data.goodsCount || 0)
  } catch (error) {
    console.error('获取购物车失败:', error)
    cartList.value = []
    finalPrice.value = 0
  }
}
const getLocalList = (sellerList) => {
  if (!Array.isArray(sellerList)) return
  cartList.value = (sellerList || []).map((item) => ({
    ...item,
    servicePrice: item.servicePrice || null,
    deliveryPrice: item.deliveryPrice || null,
    remarks: item.remarks || null,
    goodsList: item.goodsList,
  }))
  handleCalculate()
}
const validFormMessage = (list) => {
  if (!deliveryAddress.value.status) {
    ElMessage.warning('请完善收货地址')
    return false
  }
  if (!list?.length) {
    ElMessage.warning('请选择要结算的商品')
    return false
  }
  let message = ''
  for (let item of list) {
    if (message) break
    const { errorMessage } = item.goodsList.find((_item) => _item.errorMessage) || {}
    if (errorMessage) {
      message = errorMessage
    }
  }
  if (message) {
    ElMessage.warning(message)
    return false
  }
  return true
}
const router = useRouter()
// 结算
const handleCheckout = async () => {
  if (calculateLoading.value || submitLoading.value) {
    ElMessage.warning('请稍后再试')
    return
  }
  submitLoading.value = true
  await sleepFn(350)
  try {
    const calculateFormData = getCalculateFormData()
    const valid = validFormMessage(calculateFormData.sellerList)
    if (!valid) return
    const data = await submitOrder({
      ...calculateFormData,
    })
    if (data && data.length) {
      localStorage.removeItem('CART_CONFIRM_DATA')
      router.replace(`/cart/success?id=${data[0] || ''}`)
    }
  } catch (e) {
    console.log(e)
  } finally {
    submitLoading.value = false
  }
}
const deliveryAddress = ref(null)
// 地址参数
const getAddressFormData = () => {
  if (!deliveryAddress.value || !deliveryAddress.value.deliveryName) return null
  // const { addressType, deliveryName, deliveryPhone, provinceCode, provinceName, cityCode, cityName, areaCode, areaName, address } = deliveryAddress.value || {}
  // if (addressType === 1) {
  //   return { addressType, deliveryName, deliveryPhone, provinceCode, provinceName, cityCode, cityName, areaCode, areaName, address }
  // }
  return { ...deliveryAddress.value, userDeliveryAddressId: deliveryAddress.value?.id || '' }
}
// 计算价格参数
const getCalculateFormData = () => {
  const addressFormData = getAddressFormData()
  const sellerList = orderList.value.length ? orderList.value : cartList.value
  return {
    sellerList: sellerList.map((item) => {
      const sellerItem = {
        id: item.id,
        deliveryPrice: addressFormData?.addressType === 1 ? item.deliveryPrice : null,
        servicePrice: addressFormData?.addressType === 0 ? item.servicePrice : null,
        remarks: item.remarks,
        goodsList: item.goodsList.map((_item) => {
          return {
            select: _item.select,
            spuId: _item.spuId,
            skuId: _item.skuId,
            count: _item.count,
            errorMessage: _item.errorMessage,
          }
        }),
      }
      if (sellerItem.deliveryPrice || sellerItem.servicePrice) {
        sellerItem.feePriceType = currentPriceType
      }
      return sellerItem
    }),
    deliveryAddress: addressFormData,
    priceType: chooseCurrency.value,
  }
}
// 重新计算价格数据的核心逻辑
const calculatePrice = async () => {
  if (calculateLoading.value) return
  calculateLoading.value = true
  try {
    const formData = getCalculateFormData()
    const res = await calculate(formData)
    orderList.value = res?.sellerList || []
    calculateData.value = res
    if (formData?.priceType) {
      currentPriceType = formData?.priceType
    }
    lazyChooseCurrency.value = chooseCurrency.value
    console.log('lazyChooseCurrency.value', lazyChooseCurrency.value)
  } catch (e) {
    console.log(e)
  } finally {
    calculateLoading.value = false
  }
}

// 重新计算价格数据, 延迟1秒触发，1秒内有其他请求进来则取消之前的请求
const handleCalculate = debounce(calculatePrice, 300)
const getUserAddress = async () => {
  try {
    if (deliveryAddress.value) return
    const data = await getDefaultAddress()
    if (data && typeof data === 'object' && data.deliveryName) {
      deliveryAddress.value = data
    } else {
      const { rowList } = await getAddressList({ pageSize: 1, pageNum: 1 })
      if (rowList && rowList[0]) {
        deliveryAddress.value = rowList[0]
      }
    }
  } catch (e) {
    console.log(e)
  } finally {
    deliveryAddressInit.value = true
  }
}

const checkAddress = async (id) => {
  try {
    const data = await getAddressById({ id })
    if (!data || data?.deleted !== 0) {
      deliveryAddress.value = null
    }
  } catch (e) {
    console.log(e)
  }
}
// 根据国内外地址类型，转换地址
// const address = computed(() => {
//   if (!deliveryAddress?.value) return ''
//   const { addressType, provinceName, cityName, areaName, address } = deliveryAddress?.value || {}
//   return addressType ? `${provinceName}${cityName}${areaName}${address}` : address
// })
// 没有获取到确认订单，跳转到首页
const jumpNoOrderList = () => {
  router.replace('/')
}
// 页面初始化
onMounted(() => {
  if (localType) {
    const sellerListItem = localStorage.getItem('CART_CONFIRM_DATA')
    if (sellerListItem) {
      const sellerList = JSON.parse(sellerListItem)?.sellerList || []
      if (sellerList.length <= 0) {
        jumpNoOrderList()
      } else {
        getLocalList(sellerList)
      }
    } else {
      jumpNoOrderList()
    }
  } else {
    getList()
  }
  getUserAddress()
  if (deliveryAddress.value?.id) {
    checkAddress(deliveryAddress.value.id)
  }
})
</script>

<style lang="scss" scoped>
$width-small: 1140px;
$width-medium: 1260px;
$width-large: 1440px;
#fixedPanel {
  position: sticky;
  top: 160px;
}
.g-content-warp {
  margin: 0 auto;
  width: $width-small;

  @media (min-width: 1550px) and (max-width: 1620px) {
    width: $width-medium;
  }

  @media (min-width: 1620px) {
    width: $width-large;
  }
}

.logo-nav {
  position: fixed;
  top: 64px;
  z-index: 49;
  width: 100%;
  height: 64px;
  margin: 0 auto 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

/* 文本行数限制 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 动画效果 */
.animate-pulse {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Element UI Plus 样式自定义 */
.invalid-goods-card {
  background-color: #f7f8fc;
  border: none;
  border-radius: 8px;
}

.invalid-goods-card :deep(.el-card__body) {
  padding: 20px;
}

.checkout-panel {
  border-radius: 8px;
  box-shadow: none;
  border-color: #f0f0f0;
}

.checkout-panel :deep(.el-card__body) {
  padding: 16px;
}

/* 数量输入框样式 */
:deep(.el-input-number) {
  .el-input__inner {
    text-align: center;
    border-radius: 4px;
  }
}
:deep(.el-checkbox__input + .el-checkbox__label) {
  font-size: 16px;
  font-weight: 500;
  color: #1a1a1a;
}
/* 复选框样式 */
:deep(.el-checkbox) {
  .el-checkbox__inner {
    border: 1px solid #000;
    width: 16px;
    height: 16px;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #d8131a;
    border-color: #d8131a;
  }
  .el-checkbox__inner:after {
    width: 4px;
    height: 8px;
  }
  .el-checkbox__label {
    color: #1a1a1a;
    font-weight: 500;
  }
  .el-checkbox__inner:hover {
    border-color: #d8131a;
  }
}

/* 按钮样式 */
:deep(.el-button--primary) {
  background-color: #d8131a;
  border-color: #d8131a;

  &:hover {
    background-color: #b8111a;
    border-color: #b8111a;
  }

  &:active {
    background-color: #a0101a;
    border-color: #a0101a;
  }
}

:deep(.m-number.el-input-number) {
  line-height: 24px;
  .el-input-number__decrease,
  .el-input-number__increase {
    background: none;
  }
  .el-input__wrapper {
    box-shadow: 0 0 0 1px #f0f0f0 inset;
  }
}

/* 购物车项过渡效果 */
.cart-item-transition {
  transition: all 0.3s ease;
}

.cart-item-transition:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 失效商品特殊样式 */
.invalid-item {
  position: relative;
  opacity: 0.6;
}

.invalid-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.02);
  pointer-events: none;
  border-radius: 8px;
}

/* 按钮悬停效果 */
.btn-hover {
  transition: all 0.2s ease;
}

.btn-hover:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(216, 19, 26, 0.3);
}

/* 固定结算面板的响应式处理 */
@media (max-width: 1200px) {
  .fixed-checkout-panel {
    position: static;
    width: 100%;
    margin: 20px 0 0 0;
  }
}
.checked-color {
  color: #000;
  &:hover {
    color: #d8131a;
  }
}
/* 骨架屏效果增强 */
.skeleton-loader {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
:deep(.m-select) {
  .el-select__wrapper,
  .is-focused {
    border: none;
    box-shadow: none !important;
    .el-select__selected-item {
      text-align: right;
      padding-right: 0;
    }
  }
  .el-select__caret {
    transform: rotate(-90deg);
    font-size: 18px;
  }
}
:deep(.el-divider) {
  --el-border-color: #f0f0f0;
}
</style>
