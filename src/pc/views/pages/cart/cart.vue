<template>
  <div class="h-[158px] bg-white"></div>
  <div class="logo-nav">
    <SearchInput />
  </div>
  <div class="bg-white pb-30px">
    <div class="g-content-warp" v-loading="initLoading">
      <!-- 页面标题 -->
      <div class="flex items-center justify-between mb-16px">
        <h1 class="text-24px font-semibold text-[#1A1A1A]">进货单</h1>
      </div>
      <div class="flex">
        <div class="flex-1">
          <!-- 全选和删除操作栏 -->
          <div v-if="cartList.length > 0" class="flex items-center justify-between border-b border-[#F0F0F0] mb-16px">
            <div class="flex items-center font-500">
              <el-checkbox :label="`选择所有商品（${getTotalItemCount}）`" v-model="selectAll" @change="handleSelectAll" />
            </div>
            <el-divider class="flex-1 mx-32px! my-0!" />
            <div
              class="text-14px text-[#888B94] cursor-pointer hover:text-[#D8131A]"
              :class="{ 'checked-color': hasSelectedItems }"
              @click="handleDeleteSelected"
            >
              <Icon type="icon-shanchu1" :size="16" />
            </div>
          </div>

          <!-- 加载状态 -->
          <div class="min-h-300px">
            <!-- 选品车商品列表 -->
            <div v-if="!initLoading && cartList.length > 0" class="space-y-5">
              <!-- 按商家分组的商品 -->
              <SellerCard
                v-for="seller in cartList"
                :key="seller.id"
                :seller="seller"
                @seller-select-all="handleSellerSelectAll"
                @delete-seller="handleDeleteSeller"
                @item-select-change="handleItemSelectChange"
                @item-quantity-change="handleItemQuantityChange"
                @delete-item="handleDeleteItem"
              />
            </div>

            <!-- 失效商品区域 -->
            <div v-if="errorList.length > 0" class="mt-20px">
              <!-- 失效商品标题 -->
              <div class="flex items-center justify-between mb-4">
                <h2 class="text-18px font-600 text-[#1A1A1A]">失效商品</h2>
                <el-divider class="flex-1 mx-32px! my-0!" />
                <div @click="handleClearInvalidItems" class="text-14px text-[#1A1A1A] hover:text-[#D8131A] cursor-pointer">一键清理失效商品</div>
              </div>

              <!-- 失效商品列表 -->
              <div class="bg-[#f7f8fc] p-16px rounded-8px">
                <div v-if="!initLoading && errorList.length > 0" class="space-y-5 bg-white px-16px py-24px">
                  <!-- 按商家分组的商品 -->
                  <SellerItem
                    v-for="seller in errorList"
                    :key="seller.id"
                    :item="seller"
                    :is-lose-goods="true"
                    @seller-select-all="handleSellerSelectAll"
                    @delete-seller="handleDeleteSeller"
                    @item-select-change="handleItemSelectChange"
                    @item-quantity-change="handleItemQuantityChange"
                    @delete-item="handleDeleteErrorItem"
                  />
                </div>
              </div>
            </div>

            <!-- 空选品车状态 -->
            <div v-if="!initLoading && cartList.length === 0 && errorList.length === 0" class="text-center py-20">
              <el-empty description="选品车空空如也">
                <el-button @click="openPage" type="primary">去选品</el-button>
              </el-empty>
            </div>
          </div>
        </div>
        <div class="w-48px"></div>
        <!-- 订单汇总和结算 -->
        <div v-if="cartList.length > 0 || errorList.length > 0" class="my-20px w-33% -mt-50px relative" id="fixedPanelBox">
          <el-card class="checkout-panel" id="fixedPanel">
            <!-- 汇总标题 -->
            <div class="border-0 border-solid border-b border-[#F0F0F0] pb-4 mb-4">
              <h3 class="text-lg font-semibold text-[#1A1A1A]">订单汇总 ({{ getTotalItemCount }}件商品)</h3>
            </div>

            <!-- 价格信息 -->
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <span class="text-[#505259]">产品价格</span>
                <span class="text-[#505259]">
                  <c-symbol />
                  {{ calculateSelectedPrice().toFixed(2) }}
                </span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-[#505259]">合计</span>
                <span class="text-lg font-medium text-[#D8131A]">
                  <c-symbol />
                  {{ finalPrice.toFixed(2) }}
                </span>
              </div>
            </div>

            <!-- 结算按钮 -->
            <div class="mt-6">
              <el-button
                type="primary"
                size="large"
                @click="handleCheckout"
                :disabled="selectedItemsCount === 0"
                :loading="calculateLoading"
                class="w-full bg-[#D8131A] border-[#D8131A] hover:bg-[#B8111A] hover:border-[#B8111A]"
              >
                {{ calculateLoading ? '计算中...' : '结算' }}
              </el-button>
            </div>
          </el-card>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ElMessage } from 'element-plus'
import { computed, onMounted, ref, watchEffect } from 'vue'
import { useRouter } from 'vue-router'
import SearchInput from '@/pc/components/search-input/search-input.vue'
import SellerCard from './components/seller-card.vue'
import SellerItem from './components/seller-item.vue'
import { useCartStore } from '@/pc/stores'
import { getUserCartList } from '@/apis/order'
import { debounce, sleepFn } from '@/common/js/util'

const initLoading = ref(true)
const cartList = ref([])
const errorList = ref([])
const finalPrice = ref(0)
const selectAll = ref(false)
const calculateLoading = ref(false)
const cartStore = useCartStore()

const router = useRouter()
const openPage = () => {
  router.push('/market')
}

// 计算选中商品数量
const selectedItemsCount = computed(() => {
  let count = 0
  cartList.value.forEach((seller) => {
    seller.goodsList?.forEach((item) => {
      if (item.select) count += item.count
    })
  })
  return count
})

// 检查是否有选中的商品
const hasSelectedItems = computed(() => {
  return cartList.value.some((seller) => seller.goodsList?.some((item) => item.select))
})

// 获取总商品数量
const getTotalItemCount = computed(() => {
  return cartList.value.reduce((acc, seller) => {
    return acc + seller.goodsList?.filter((item) => item.select).length || 0
  }, 0)
})

// 计算选中商品的总价
const calculateSelectedPrice = () => {
  let total = 0
  cartList.value.forEach((seller) => {
    seller.goodsList?.forEach((item) => {
      if (item.select) {
        total += item.finalPrice
      }
    })
  })
  return total
}

// 检查商家是否全选
const isSellerAllSelected = (seller) => {
  if (!seller.goodsList || seller.goodsList.length === 0) return false
  return seller.goodsList.every((item) => item.select)
}

// 更新商家的全选状态
const updateSellerSelectAllStatus = () => {
  cartList.value.forEach((seller) => {
    seller.selectAll = isSellerAllSelected(seller)
  })
}

// 监听全选状态
watchEffect(() => {
  if (cartList.value.length === 0) {
    selectAll.value = false
    return
  }

  let allSelected = true
  let hasItems = false

  cartList.value.forEach((seller) => {
    if (seller.goodsList && seller.goodsList.length > 0) {
      hasItems = true
      seller.goodsList.forEach((item) => {
        if (!item.select) {
          allSelected = false
        }
      })
    }
  })

  selectAll.value = hasItems && allSelected
})

// 构建请求数据
const buildRequestData = (sellerList = []) => {
  // 如果提供了sellerList，则直接使用
  if (sellerList.length > 0) {
    return {
      init: false,
      sellerList,
    }
  }

  // 否则构建请求数据，包含失效商品信息
  return {
    init: false,
    sellerList: getEditCartParams(errorList.value, true),
  }
}

// 获取选品车列表
const getList = async (isInit = true, sellerList = []) => {
  try {
    if (isInit) {
      initLoading.value = true
    }
    calculateLoading.value = true

    const requestData = isInit ? { init: true } : buildRequestData(sellerList)
    const data = await getUserCartList(requestData)
    cartList.value = data.sellerList || []
    errorList.value = data.errorGoodsList || []

    finalPrice.value = data.finalPrice || 0
    cartStore.updateCartCount(data.goodsCount || 0)

    // 为每个商家添加selectAll字段并更新状态
    cartList.value.forEach((seller) => {
      seller.selectAll = false // 初始化
    })
    updateSellerSelectAllStatus()
  } catch (error) {
    console.error('获取选品车失败:', error)
    cartList.value = []
    errorList.value = []
    finalPrice.value = 0
  } finally {
    calculateLoading.value = false
    if (isInit) {
      initLoading.value = false
    }
  }
}

// 全选/取消全选
const handleSelectAll = () => {
  // 如果有未选中的 则全选
  if (cartList.value.some((seller) => seller.goodsList?.some((item) => !item.select))) {
    cartList.value.forEach((seller) => {
      seller.goodsList?.forEach((item) => {
        item.select = true
      })
    })
  } else {
    // 如果已经全选 则取消全选
    cartList.value.forEach((seller) => {
      seller.goodsList?.forEach((item) => {
        item.select = false
      })
    })
  }

  // 更新到服务器，确保保留失效商品
  updateCartData()
  // 更新商家全选状态
  updateSellerSelectAllStatus()
}

// 商家全选/取消全选
const handleSellerSelectAll = (seller, checked) => {
  seller.goodsList?.forEach((item) => {
    item.select = checked
  })

  // 更新该商家的全选状态
  seller.selectAll = checked

  // 更新到服务器
  updateCartData()
}

// 单个商品选择状态改变 - 来自CartItem组件的事件
const handleItemSelectChange = (item, checked) => {
  item.select = checked
  // 更新商家全选状态
  updateSellerSelectAllStatus()
  // 更新到服务器
  updateCartData()
}

const handleItemQuantityChange = (item, newCount) => {
  item.count = newCount
  // 修改数量的时候，如果商品未选中，则选中
  if (!item.select) {
    item.select = true
  }
  // 数量实时增加，接口延迟调用
  handleItemQuantityDebuonceChange()
}

// 单个商品数量变化 - 来自CartItem组件的事件
const handleItemQuantityDebuonceChange = debounce(() => {
  // 更新商家全选状态
  updateSellerSelectAllStatus()
  // 更新到服务器，确保保留失效商品
  updateCartData()
}, 400)

const handleDeleteErrorItem = (item) => {
  handleDeleteItem(item, 'error')
}

const getEditCartParams = (newErrorList, notFilter = false) => {
  const errorArr = Array.isArray(newErrorList) ? newErrorList : errorList.value
  const errorShopObj = errorArr.reduce((prev, cur) => {
    if (!prev[cur.sellerId]) prev[cur.sellerId] = []
    prev[cur.sellerId].push(cur)
    return prev
  }, {})
  const invalidShopList = Object.keys(errorShopObj)
    .filter((key) => cartList.value.every((_item) => _item.id !== key))
    .map((key) => ({ id: key, goodsList: errorShopObj[key] }))

  return cartList.value
    .map((item) => {
      const errorItemArr = errorShopObj[item.id] || []
      // 过滤逻辑 保留非编辑下的所有数据
      return {
        id: item.id,
        goodsList: item.goodsList
          .filter((_item) => notFilter || !_item.delSelected)
          .map((_item) => {
            return {
              select: !!_item.select,
              spuId: _item.spuId,
              skuId: _item.skuId,
              count: _item.count,
              errorMessage: _item.errorMessage,
            }
          })
          .concat(errorItemArr),
      }
    })
    .concat(invalidShopList)
}

// 删除单个商品
const handleDeleteItem = (item, type = 'cart') => {
  // 展示 确认删除该商品吗？
  ElMessageBox.confirm('确认删除商品吗？', '删除商品', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    customClass: 'g-message-box',
  }).then((res) => {
    if (res === 'confirm') {
      // 从当前列表中移除该商品
      if (type === 'cart') {
        cartList.value.forEach((seller) => {
          if (seller.goodsList) {
            seller.goodsList = seller.goodsList.filter((good) => good.skuId !== item.skuId)
          }
        })
        // 更新购物车数据
        updateCartData()
      } else if (type === 'error') {
        const newErrorList = errorList.value.filter((error) => error.skuId !== item.skuId)
        const sellerList = getEditCartParams(newErrorList, true)
        getList(false, sellerList)
      }
    }
  })
}

// 删除选中的商品
const handleDeleteSelected = () => {
  if (!hasSelectedItems.value) {
    ElMessage.warning('请选择要删除的商品')
    return
  }
  // 展示 确认删除该商品吗？
  ElMessageBox.confirm('确认删除已选中的商品吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    customClass: 'g-message-box',
  }).then((res) => {
    if (res === 'confirm') {
      // 将选中商品标记为删除
      cartList.value.forEach((seller) => {
        if (seller.goodsList) {
          seller.goodsList = seller.goodsList.filter((item) => !item.select)
        }
      })
      // 使用getEditCartParams确保保留失效商品
      const sellerList = getEditCartParams(errorList.value, true)
      getList(false, sellerList)
    }
  })
}

// 删除商家的所有商品 TODO: 暂时不使用，商家全删功能，UI侧已删除
const handleDeleteSeller = () => {
  // 展示 确认删除该商家吗？
  ElMessageBox.confirm('确认删除该商家吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    customClass: 'g-message-box',
  })
}

// 清理失效商品
const handleClearInvalidItems = () => {
  // 展示 确认删除该商家吗？
  ElMessageBox.confirm('确认要删除所有失效商品吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    customClass: 'g-message-box',
  }).then((res) => {
    if (res === 'confirm') {
      const sellerList = getEditCartParams([], true)
      getList(false, sellerList).then(() => {
        // 删除成功
        ElMessage.success('清除成功！')
      })
    }
  })
}

// 更新选品车数据到服务器
const updateCartData = async () => {
  if (cartList.value.length === 0) {
    // 如果选品车为空，重新获取初始数据
    await getList(true)
    return
  }

  // 构建请求数据，确保保留失效商品信息
  const sellerList = getEditCartParams(errorList.value, true)
  await getList(false, sellerList)

  // 更新商家全选状态
  updateSellerSelectAllStatus()
}

const validFormMessage = () => {
  if (!cartList.value?.length || cartList.value.every((item) => item.goodsList.every((_item) => !_item.select))) {
    ElMessage.warning('请选择要结算的商品')
    return false
  }
  let message = ''
  for (let item of cartList.value) {
    if (message) break
    const { errorMessage } = item.goodsList.find((_item) => _item.errorMessage && _item.select) || {}
    if (errorMessage) {
      message = errorMessage
    }
  }
  if (message) {
    ElMessage.warning(message)
    return false
  }
  return true
}

// 结算
const handleCheckout = async () => {
  if (calculateLoading.value) {
    ElMessage.warning('请稍后再试')
    return
  }
  calculateLoading.value = true
  const valid = validFormMessage()
  await sleepFn(350)
  if (!valid) {
    calculateLoading.value = false
    return
  }
  calculateLoading.value = false
  // 这里可以跳转到结算页面
  router.push({
    path: '/cart/confirm',
  })
}

// 页面初始化
onMounted(() => {
  getList(true)
})
</script>

<style lang="scss" scoped>
$width-small: 1140px;
$width-medium: 1260px;
$width-large: 1440px;
#fixedPanel {
  position: sticky;
  top: 160px;
}
.g-content-warp {
  margin: 0 auto;
  width: $width-small;

  @media (min-width: 1550px) and (max-width: 1620px) {
    width: $width-medium;
  }

  @media (min-width: 1620px) {
    width: $width-large;
  }
}

.logo-nav {
  position: fixed;
  top: 64px;
  z-index: 49;
  width: 100%;
  height: 64px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

/* 文本行数限制 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 动画效果 */
.animate-pulse {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Element UI Plus 样式自定义 */
.invalid-goods-card {
  background-color: #f7f8fc;
  border: none;
  border-radius: 8px;
}

.invalid-goods-card :deep(.el-card__body) {
  padding: 20px;
}

.checkout-panel {
  border-radius: 8px;
  box-shadow: none;
  border-color: #f0f0f0;
}

.checkout-panel :deep(.el-card__body) {
  padding: 16px;
}

/* 数量输入框样式 */
:deep(.el-input-number) {
  .el-input__inner {
    text-align: center;
    border-radius: 4px;
  }
}
:deep(.el-checkbox__input + .el-checkbox__label) {
  font-size: 16px;
  font-weight: 500;
  color: #1a1a1a;
}
/* 复选框样式 */
:deep(.el-checkbox) {
  .el-checkbox__inner {
    border: 1px solid #888b94;
    width: 16px;
    height: 16px;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #d8131a;
    border-color: #d8131a;
  }
  .el-checkbox__inner:after {
    width: 4px;
    height: 8px;
  }
  .el-checkbox__label {
    color: #1a1a1a;
    font-weight: 500;
  }
  .el-checkbox__inner:hover {
    border-color: #d8131a;
  }
}

/* 按钮样式 */
:deep(.el-button--primary) {
  background-color: #d8131a;
  border-color: #d8131a;

  &:hover {
    background-color: #b8111a;
    border-color: #b8111a;
  }

  &:active {
    background-color: #a0101a;
    border-color: #a0101a;
  }
}

:deep(.m-number.el-input-number) {
  line-height: 24px;
  .el-input-number__decrease,
  .el-input-number__increase {
    background: none;
  }
  .el-input__wrapper {
    box-shadow: 0 0 0 1px #f0f0f0 inset;
  }
}

/* 选品车项过渡效果 */
.cart-item-transition {
  transition: all 0.3s ease;
}

.cart-item-transition:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 失效商品特殊样式 */
.invalid-item {
  position: relative;
  opacity: 0.6;
}

.invalid-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.02);
  pointer-events: none;
  border-radius: 8px;
}

/* 按钮悬停效果 */
.btn-hover {
  transition: all 0.2s ease;
}

.btn-hover:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(216, 19, 26, 0.3);
}

/* 固定结算面板的响应式处理 */
@media (max-width: 1200px) {
  .fixed-checkout-panel {
    position: static;
    width: 100%;
    margin: 20px 0 0 0;
  }
}
.checked-color {
  color: #000;
  &:hover {
    color: #d8131a;
  }
}
/* 骨架屏效果增强 */
.skeleton-loader {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
:deep(.el-divider) {
  --el-border-color: #f0f0f0;
}
</style>
