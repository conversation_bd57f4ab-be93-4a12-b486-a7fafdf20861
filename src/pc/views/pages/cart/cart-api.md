## 商品选中

请求接口：/api/order-web/client/shopcart/shopcartV1

请求方式：POST
```json
{"init":false,"sellerList":[{"id":"1856880272728748216","goodsList":[{"select":true,"spuId":"4050045430680600004","skuId":"4050045430680600489","count":48},{"select":false,"spuId":"4050045430680600004","skuId":"4050045430680600390","count":49}]},{"id":"4050083818443800087","goodsList":[{"select":true,"spuId":"4050083822323400052","skuId":"4050083822*********","count":1},{"select":true,"spuId":"4050083822323400052","skuId":"4050083822323400059","count":1},{"select":true,"spuId":"4050083822323400052","skuId":"4050083822323400007","count":1}]}]}
```
请求成功返回值：
```json
{
    "success": true,
    "code": "200",
    "traceId": "e5e191f1-06ab-4e58-afcf-f9f04f3b8dc9",
    "data": {
        "userId": "4050103821064600030",
        "priceType": 2,
        "sellerList": [
            {
                "id": "1856880272728748216",
                "companyName": "极易净水科技（上海）有限公司",
                "shopLogo": "https://ly-marketshop-dev.oss-cn-beijing.aliyuncs.com/merchant/logo/2025/01/21/y61CR6_1737442522091_79np4aw2kwq18f1l",
                "goodsList": [
                    {
                        "select": true,
                        "sellerId": "1856880272728748216",
                        "spuId": "4050045430680600004",
                        "goodsName": "香舍里四季相伴车载香薰",
                        "goodsImage": "https://static.chinamarket.cn/seller/goods/pic/2025/02/27/ZCtOzo_1740641509723_f81i6m5rirk7nlvp",
                        "skuId": "4050045430680600489",
                        "spec": "[{\"id\": \"4050045430643200090\", \"item\": {\"id\": \"4050045430649800013\", \"value\": \"炫彩缤纷\"}, \"spec\": \"四季相伴\"}]",
                        "price": 1.52,
                        "count": 48,
                        "stock": 297,
                        "finalPrice": 72.96,
                        "showType": 1
                    },
                    {
                        "select": false,
                        "sellerId": "1856880272728748216",
                        "spuId": "4050045430680600004",
                        "goodsName": "香舍里四季相伴车载香薰",
                        "goodsImage": "https://static.chinamarket.cn/seller/goods/pic/2025/02/27/ZCtOzo_1740641509723_f81i6m5rirk7nlvp",
                        "skuId": "4050045430680600390",
                        "spec": "[{\"id\": \"4050045430643200090\", \"item\": {\"id\": \"4050045430663800036\", \"value\": \"栀子花\"}, \"spec\": \"四季相伴\"}]",
                        "price": 1.52,
                        "count": 49,
                        "stock": 266,
                        "finalPrice": 74.48,
                        "showType": 1
                    }
                ],
                "orderPrice": 72.96,
                "finalPrice": 72.96
            },
            {
                "id": "4050083818443800087",
                "companyName": "厚礼谢",
                "shopLogo": "https://ly-marketshop-dev.oss-cn-beijing.aliyuncs.com/merchant/logo/2025/04/19/spU6Ha_1745049190479_twc1q06hjjddheh4",
                "goodsList": [
                    {
                        "select": true,
                        "sellerId": "4050083818443800087",
                        "spuId": "4050083822323400052",
                        "goodsName": "厚礼谢",
                        "goodsImage": "https://ly-marketshop-dev.oss-cn-beijing.aliyuncs.com/seller/goods/pic/2025/04/19/u3NqMu_1745031499619_f9avwha4qdwltb1j",
                        "skuId": "4050083822*********",
                        "spec": "[{\"id\": \"1899308586268982476\", \"item\": {\"id\": \"1899308586268982483\", \"value\": \"XL\"}, \"spec\": \"尺寸大小\"}]",
                        "price": 4.16,
                        "count": 1,
                        "stock": 2969,
                        "finalPrice": 4.16,
                        "showType": 1
                    },
                    {
                        "select": true,
                        "sellerId": "4050083818443800087",
                        "spuId": "4050083822323400052",
                        "goodsName": "厚礼谢",
                        "goodsImage": "https://ly-marketshop-dev.oss-cn-beijing.aliyuncs.com/seller/goods/pic/2025/04/19/u3NqMu_1745031499619_f9avwha4qdwltb1j",
                        "skuId": "4050083822323400059",
                        "spec": "[{\"id\": \"1899308586268982476\", \"item\": {\"id\": \"1899308586268982479\", \"value\": \"M\"}, \"spec\": \"尺寸大小\"}]",
                        "price": 2.77,
                        "count": 1,
                        "stock": 1990,
                        "finalPrice": 2.77,
                        "showType": 1
                    },
                    {
                        "select": true,
                        "sellerId": "4050083818443800087",
                        "spuId": "4050083822323400052",
                        "goodsName": "厚礼谢",
                        "goodsImage": "https://ly-marketshop-dev.oss-cn-beijing.aliyuncs.com/seller/goods/pic/2025/04/19/u3NqMu_1745031499619_f9avwha4qdwltb1j",
                        "skuId": "4050083822323400007",
                        "spec": "[{\"id\": \"1899308586268982476\", \"item\": {\"id\": \"1899308586268982481\", \"value\": \"L\"}, \"spec\": \"尺寸大小\"}]",
                        "price": 1.39,
                        "count": 1,
                        "stock": 971,
                        "finalPrice": 1.39,
                        "showType": 1
                    }
                ],
                "orderPrice": 8.32,
                "finalPrice": 8.32
            }
        ],
        "errorGoodsList": [],
        "goodsCount": 5,
        "orderPrice": 81.28,
        "deliveryPrice": 0,
        "servicePrice": 0,
        "finalPrice": 81.28
    }
}
```
## 库存加减

请求接口：/api/order-web/client/shopcart/shopcartV1

请求方式：POST

请求参数：
```json
{"init":false,"sellerList":[{"id":"1856880272728748216","goodsList":[{"select":true,"spuId":"4050045430680600004","skuId":"4050045430680600489","count":48},{"select":true,"spuId":"4050045430680600004","skuId":"4050045430680600390","count":49}]},{"id":"4050083818443800087","goodsList":[{"select":true,"spuId":"4050083822323400052","skuId":"4050083822*********","count":1},{"select":true,"spuId":"4050083822323400052","skuId":"4050083822323400059","count":1},{"select":true,"spuId":"4050083822323400052","skuId":"4050083822323400007","count":1}]}]}
```

请求成功返回值：
```json
{
    "success": true,
    "code": "200",
    "traceId": "ec2303fd-4187-4a46-a296-cd868a450e9b",
    "data": {
        "userId": "4050103821064600030",
        "priceType": 2,
        "sellerList": [
            {
                "id": "1856880272728748216",
                "companyName": "极易净水科技（上海）有限公司",
                "shopLogo": "https://ly-marketshop-dev.oss-cn-beijing.aliyuncs.com/merchant/logo/2025/01/21/y61CR6_1737442522091_79np4aw2kwq18f1l",
                "goodsList": [
                    {
                        "select": true,
                        "sellerId": "1856880272728748216",
                        "spuId": "4050045430680600004",
                        "goodsName": "香舍里四季相伴车载香薰",
                        "goodsImage": "https://static.chinamarket.cn/seller/goods/pic/2025/02/27/ZCtOzo_1740641509723_f81i6m5rirk7nlvp",
                        "skuId": "4050045430680600489",
                        "spec": "[{\"id\": \"4050045430643200090\", \"item\": {\"id\": \"4050045430649800013\", \"value\": \"炫彩缤纷\"}, \"spec\": \"四季相伴\"}]",
                        "price": 1.52,
                        "count": 48,
                        "stock": 297,
                        "finalPrice": 72.96,
                        "showType": 1
                    },
                    {
                        "select": true,
                        "sellerId": "1856880272728748216",
                        "spuId": "4050045430680600004",
                        "goodsName": "香舍里四季相伴车载香薰",
                        "goodsImage": "https://static.chinamarket.cn/seller/goods/pic/2025/02/27/ZCtOzo_1740641509723_f81i6m5rirk7nlvp",
                        "skuId": "4050045430680600390",
                        "spec": "[{\"id\": \"4050045430643200090\", \"item\": {\"id\": \"4050045430663800036\", \"value\": \"栀子花\"}, \"spec\": \"四季相伴\"}]",
                        "price": 1.52,
                        "count": 49,
                        "stock": 266,
                        "finalPrice": 74.48,
                        "showType": 1
                    }
                ],
                "orderPrice": 147.44,
                "finalPrice": 147.44
            },
            {
                "id": "4050083818443800087",
                "companyName": "厚礼谢",
                "shopLogo": "https://ly-marketshop-dev.oss-cn-beijing.aliyuncs.com/merchant/logo/2025/04/19/spU6Ha_1745049190479_twc1q06hjjddheh4",
                "goodsList": [
                    {
                        "select": true,
                        "sellerId": "4050083818443800087",
                        "spuId": "4050083822323400052",
                        "goodsName": "厚礼谢",
                        "goodsImage": "https://ly-marketshop-dev.oss-cn-beijing.aliyuncs.com/seller/goods/pic/2025/04/19/u3NqMu_1745031499619_f9avwha4qdwltb1j",
                        "skuId": "4050083822*********",
                        "spec": "[{\"id\": \"1899308586268982476\", \"item\": {\"id\": \"1899308586268982483\", \"value\": \"XL\"}, \"spec\": \"尺寸大小\"}]",
                        "price": 4.16,
                        "count": 1,
                        "stock": 2969,
                        "finalPrice": 4.16,
                        "showType": 1
                    },
                    {
                        "select": true,
                        "sellerId": "4050083818443800087",
                        "spuId": "4050083822323400052",
                        "goodsName": "厚礼谢",
                        "goodsImage": "https://ly-marketshop-dev.oss-cn-beijing.aliyuncs.com/seller/goods/pic/2025/04/19/u3NqMu_1745031499619_f9avwha4qdwltb1j",
                        "skuId": "4050083822323400059",
                        "spec": "[{\"id\": \"1899308586268982476\", \"item\": {\"id\": \"1899308586268982479\", \"value\": \"M\"}, \"spec\": \"尺寸大小\"}]",
                        "price": 2.77,
                        "count": 1,
                        "stock": 1990,
                        "finalPrice": 2.77,
                        "showType": 1
                    },
                    {
                        "select": true,
                        "sellerId": "4050083818443800087",
                        "spuId": "4050083822323400052",
                        "goodsName": "厚礼谢",
                        "goodsImage": "https://ly-marketshop-dev.oss-cn-beijing.aliyuncs.com/seller/goods/pic/2025/04/19/u3NqMu_1745031499619_f9avwha4qdwltb1j",
                        "skuId": "4050083822323400007",
                        "spec": "[{\"id\": \"1899308586268982476\", \"item\": {\"id\": \"1899308586268982481\", \"value\": \"L\"}, \"spec\": \"尺寸大小\"}]",
                        "price": 1.39,
                        "count": 1,
                        "stock": 971,
                        "finalPrice": 1.39,
                        "showType": 1
                    }
                ],
                "orderPrice": 8.32,
                "finalPrice": 8.32
            }
        ],
        "errorGoodsList": [],
        "goodsCount": 5,
        "orderPrice": 155.76,
        "deliveryPrice": 0,
        "servicePrice": 0,
        "finalPrice": 155.76
    }
}
```


## 商品删除
请求接口：/api/order-web/client/shopcart/shopcartV1

请求方式：POST

请求参数：
```json
{"init":false,"sellerList":[{"id":"1856880272728748216","goodsList":[{"select":true,"spuId":"4050045430680600004","skuId":"4050045430680600489","count":48},{"select":false,"spuId":"4050045430680600004","skuId":"4050045430680600390","count":49}]},{"id":"4050083818443800087","goodsList":[{"select":true,"spuId":"4050083822323400052","skuId":"4050083822323400059","count":1},{"select":true,"spuId":"4050083822323400052","skuId":"4050083822323400007","count":1}]}]}
```

请求成功返回值：
```json
{
    "success": true,
    "code": "200",
    "traceId": "2b180292-0821-4354-ae90-6325e965be7c",
    "data": {
        "userId": "4050103821064600030",
        "priceType": 2,
        "sellerList": [
            {
                "id": "1856880272728748216",
                "companyName": "极易净水科技（上海）有限公司",
                "shopLogo": "https://ly-marketshop-dev.oss-cn-beijing.aliyuncs.com/merchant/logo/2025/01/21/y61CR6_1737442522091_79np4aw2kwq18f1l",
                "goodsList": [
                    {
                        "select": true,
                        "sellerId": "1856880272728748216",
                        "spuId": "4050045430680600004",
                        "goodsName": "香舍里四季相伴车载香薰",
                        "goodsImage": "https://static.chinamarket.cn/seller/goods/pic/2025/02/27/ZCtOzo_1740641509723_f81i6m5rirk7nlvp",
                        "skuId": "4050045430680600489",
                        "spec": "[{\"id\": \"4050045430643200090\", \"item\": {\"id\": \"4050045430649800013\", \"value\": \"炫彩缤纷\"}, \"spec\": \"四季相伴\"}]",
                        "price": 1.52,
                        "count": 48,
                        "stock": 297,
                        "finalPrice": 72.96,
                        "showType": 1
                    },
                    {
                        "select": false,
                        "sellerId": "1856880272728748216",
                        "spuId": "4050045430680600004",
                        "goodsName": "香舍里四季相伴车载香薰",
                        "goodsImage": "https://static.chinamarket.cn/seller/goods/pic/2025/02/27/ZCtOzo_1740641509723_f81i6m5rirk7nlvp",
                        "skuId": "4050045430680600390",
                        "spec": "[{\"id\": \"4050045430643200090\", \"item\": {\"id\": \"4050045430663800036\", \"value\": \"栀子花\"}, \"spec\": \"四季相伴\"}]",
                        "price": 1.52,
                        "count": 49,
                        "stock": 266,
                        "finalPrice": 74.48,
                        "showType": 1
                    }
                ],
                "orderPrice": 72.96,
                "finalPrice": 72.96
            },
            {
                "id": "4050083818443800087",
                "companyName": "厚礼谢",
                "shopLogo": "https://ly-marketshop-dev.oss-cn-beijing.aliyuncs.com/merchant/logo/2025/04/19/spU6Ha_1745049190479_twc1q06hjjddheh4",
                "goodsList": [
                    {
                        "select": true,
                        "sellerId": "4050083818443800087",
                        "spuId": "4050083822323400052",
                        "goodsName": "厚礼谢",
                        "goodsImage": "https://ly-marketshop-dev.oss-cn-beijing.aliyuncs.com/seller/goods/pic/2025/04/19/u3NqMu_1745031499619_f9avwha4qdwltb1j",
                        "skuId": "4050083822323400059",
                        "spec": "[{\"id\": \"1899308586268982476\", \"item\": {\"id\": \"1899308586268982479\", \"value\": \"M\"}, \"spec\": \"尺寸大小\"}]",
                        "price": 2.77,
                        "count": 1,
                        "stock": 1990,
                        "finalPrice": 2.77,
                        "showType": 1
                    },
                    {
                        "select": true,
                        "sellerId": "4050083818443800087",
                        "spuId": "4050083822323400052",
                        "goodsName": "厚礼谢",
                        "goodsImage": "https://ly-marketshop-dev.oss-cn-beijing.aliyuncs.com/seller/goods/pic/2025/04/19/u3NqMu_1745031499619_f9avwha4qdwltb1j",
                        "skuId": "4050083822323400007",
                        "spec": "[{\"id\": \"1899308586268982476\", \"item\": {\"id\": \"1899308586268982481\", \"value\": \"L\"}, \"spec\": \"尺寸大小\"}]",
                        "price": 1.39,
                        "count": 1,
                        "stock": 971,
                        "finalPrice": 1.39,
                        "showType": 1
                    }
                ],
                "orderPrice": 4.16,
                "finalPrice": 4.16
            }
        ],
        "errorGoodsList": [],
        "goodsCount": 4,
        "orderPrice": 77.12,
        "deliveryPrice": 0,
        "servicePrice": 0,
        "finalPrice": 77.12
    }
}
```

请求成功返回值：
```json
{
    "success": true,
    "code": "200",
    "traceId": "ec2303fd-4187-4a46-a296-cd868a450e9b",
    "data": {
        "userId": "4050103821064600030",
        "priceType": 2,
        "sellerList": [
            {
                "id": "1856880272728748216",
                "companyName": "极易净水科技（上海）有限公司",
                "shopLogo": "https://ly-marketshop-dev.oss-cn-beijing.aliyuncs.com/merchant/logo/2025/01/21/y61CR6_1737442522091_79np4aw2kwq18f1l",
                "goodsList": [
                    {
                        "select": true,
                        "sellerId": "1856880272728748216",
                        "spuId": "4050045430680600004",
                        "goodsName": "香舍里四季相伴车载香薰",
                        "goodsImage": "https://static.chinamarket.cn/seller/goods/pic/2025/02/27/ZCtOzo_1740641509723_f81i6m5rirk7nlvp",
                        "skuId": "4050045430680600489",
                        "spec": "[{\"id\": \"4050045430643200090\", \"item\": {\"id\": \"4050045430649800013\", \"value\": \"炫彩缤纷\"}, \"spec\": \"四季相伴\"}]",
                        "price": 1.52,
                        "count": 48,
                        "stock": 297,
                        "finalPrice": 72.96,
                        "showType": 1
                    },
                    {
                        "select": true,
                        "sellerId": "1856880272728748216",
                        "spuId": "4050045430680600004",
                        "goodsName": "香舍里四季相伴车载香薰",
                        "goodsImage": "https://static.chinamarket.cn/seller/goods/pic/2025/02/27/ZCtOzo_1740641509723_f81i6m5rirk7nlvp",
                        "skuId": "4050045430680600390",
                        "spec": "[{\"id\": \"4050045430643200090\", \"item\": {\"id\": \"4050045430663800036\", \"value\": \"栀子花\"}, \"spec\": \"四季相伴\"}]",
                        "price": 1.52,
                        "count": 49,
                        "stock": 266,
                        "finalPrice": 74.48,
                        "showType": 1
                    }
                ],
                "orderPrice": 147.44,
                "finalPrice": 147.44
            },
            {
                "id": "4050083818443800087",
                "companyName": "厚礼谢",
                "shopLogo": "https://ly-marketshop-dev.oss-cn-beijing.aliyuncs.com/merchant/logo/2025/04/19/spU6Ha_1745049190479_twc1q06hjjddheh4",
                "goodsList": [
                    {
                        "select": true,
                        "sellerId": "4050083818443800087",
                        "spuId": "4050083822323400052",
                        "goodsName": "厚礼谢",
                        "goodsImage": "https://ly-marketshop-dev.oss-cn-beijing.aliyuncs.com/seller/goods/pic/2025/04/19/u3NqMu_1745031499619_f9avwha4qdwltb1j",
                        "skuId": "4050083822*********",
                        "spec": "[{\"id\": \"1899308586268982476\", \"item\": {\"id\": \"1899308586268982483\", \"value\": \"XL\"}, \"spec\": \"尺寸大小\"}]",
                        "price": 4.16,
                        "count": 1,
                        "stock": 2969,
                        "finalPrice": 4.16,
                        "showType": 1
                    },
                    {
                        "select": true,
                        "sellerId": "4050083818443800087",
                        "spuId": "4050083822323400052",
                        "goodsName": "厚礼谢",
                        "goodsImage": "https://ly-marketshop-dev.oss-cn-beijing.aliyuncs.com/seller/goods/pic/2025/04/19/u3NqMu_1745031499619_f9avwha4qdwltb1j",
                        "skuId": "4050083822323400059",
                        "spec": "[{\"id\": \"1899308586268982476\", \"item\": {\"id\": \"1899308586268982479\", \"value\": \"M\"}, \"spec\": \"尺寸大小\"}]",
                        "price": 2.77,
                        "count": 1,
                        "stock": 1990,
                        "finalPrice": 2.77,
                        "showType": 1
                    },
                    {
                        "select": true,
                        "sellerId": "4050083818443800087",
                        "spuId": "4050083822323400052",
                        "goodsName": "厚礼谢",
                        "goodsImage": "https://ly-marketshop-dev.oss-cn-beijing.aliyuncs.com/seller/goods/pic/2025/04/19/u3NqMu_1745031499619_f9avwha4qdwltb1j",
                        "skuId": "4050083822323400007",
                        "spec": "[{\"id\": \"1899308586268982476\", \"item\": {\"id\": \"1899308586268982481\", \"value\": \"L\"}, \"spec\": \"尺寸大小\"}]",
                        "price": 1.39,
                        "count": 1,
                        "stock": 971,
                        "finalPrice": 1.39,
                        "showType": 1
                    }
                ],
                "orderPrice": 8.32,
                "finalPrice": 8.32
            }
        ],
        "errorGoodsList": [],
        "goodsCount": 5,
        "orderPrice": 155.76,
        "deliveryPrice": 0,
        "servicePrice": 0,
        "finalPrice": 155.76
    }
}
```


## 商品全选
请求接口：/api/order-web/client/shopcart/shopcartV1

请求方式：POST

请求参数：
```json
{"init":false,"sellerList":[{"id":"1856880272728748216","goodsList":[{"select":true,"spuId":"4050045430680600004","skuId":"4050045430680600489","count":48},{"select":true,"spuId":"4050045430680600004","skuId":"4050045430680600390","count":49}]},{"id":"4050083818443800087","goodsList":[{"select":true,"spuId":"4050083822323400052","skuId":"4050083822323400059","count":1},{"select":true,"spuId":"4050083822323400052","skuId":"4050083822323400007","count":1}]}]}
```
请求成功返回值：
```json
{
    "success": true,
    "code": "200",
    "traceId": "0d0e6cbe-f678-486d-aae7-f73593ce3035",
    "data": {
        "userId": "4050103821064600030",
        "priceType": 2,
        "sellerList": [
            {
                "id": "1856880272728748216",
                "companyName": "极易净水科技（上海）有限公司",
                "shopLogo": "https://ly-marketshop-dev.oss-cn-beijing.aliyuncs.com/merchant/logo/2025/01/21/y61CR6_1737442522091_79np4aw2kwq18f1l",
                "goodsList": [
                    {
                        "select": true,
                        "sellerId": "1856880272728748216",
                        "spuId": "4050045430680600004",
                        "goodsName": "香舍里四季相伴车载香薰",
                        "goodsImage": "https://static.chinamarket.cn/seller/goods/pic/2025/02/27/ZCtOzo_1740641509723_f81i6m5rirk7nlvp",
                        "skuId": "4050045430680600489",
                        "spec": "[{\"id\": \"4050045430643200090\", \"item\": {\"id\": \"4050045430649800013\", \"value\": \"炫彩缤纷\"}, \"spec\": \"四季相伴\"}]",
                        "price": 1.52,
                        "count": 48,
                        "stock": 297,
                        "finalPrice": 72.96,
                        "showType": 1
                    },
                    {
                        "select": true,
                        "sellerId": "1856880272728748216",
                        "spuId": "4050045430680600004",
                        "goodsName": "香舍里四季相伴车载香薰",
                        "goodsImage": "https://static.chinamarket.cn/seller/goods/pic/2025/02/27/ZCtOzo_1740641509723_f81i6m5rirk7nlvp",
                        "skuId": "4050045430680600390",
                        "spec": "[{\"id\": \"4050045430643200090\", \"item\": {\"id\": \"4050045430663800036\", \"value\": \"栀子花\"}, \"spec\": \"四季相伴\"}]",
                        "price": 1.52,
                        "count": 49,
                        "stock": 266,
                        "finalPrice": 74.48,
                        "showType": 1
                    }
                ],
                "orderPrice": 147.44,
                "finalPrice": 147.44
            },
            {
                "id": "4050083818443800087",
                "companyName": "厚礼谢",
                "shopLogo": "https://ly-marketshop-dev.oss-cn-beijing.aliyuncs.com/merchant/logo/2025/04/19/spU6Ha_1745049190479_twc1q06hjjddheh4",
                "goodsList": [
                    {
                        "select": true,
                        "sellerId": "4050083818443800087",
                        "spuId": "4050083822323400052",
                        "goodsName": "厚礼谢",
                        "goodsImage": "https://ly-marketshop-dev.oss-cn-beijing.aliyuncs.com/seller/goods/pic/2025/04/19/u3NqMu_1745031499619_f9avwha4qdwltb1j",
                        "skuId": "4050083822323400059",
                        "spec": "[{\"id\": \"1899308586268982476\", \"item\": {\"id\": \"1899308586268982479\", \"value\": \"M\"}, \"spec\": \"尺寸大小\"}]",
                        "price": 2.77,
                        "count": 1,
                        "stock": 1990,
                        "finalPrice": 2.77,
                        "showType": 1
                    },
                    {
                        "select": true,
                        "sellerId": "4050083818443800087",
                        "spuId": "4050083822323400052",
                        "goodsName": "厚礼谢",
                        "goodsImage": "https://ly-marketshop-dev.oss-cn-beijing.aliyuncs.com/seller/goods/pic/2025/04/19/u3NqMu_1745031499619_f9avwha4qdwltb1j",
                        "skuId": "4050083822323400007",
                        "spec": "[{\"id\": \"1899308586268982476\", \"item\": {\"id\": \"1899308586268982481\", \"value\": \"L\"}, \"spec\": \"尺寸大小\"}]",
                        "price": 1.39,
                        "count": 1,
                        "stock": 971,
                        "finalPrice": 1.39,
                        "showType": 1
                    }
                ],
                "orderPrice": 4.16,
                "finalPrice": 4.16
            }
        ],
        "errorGoodsList": [],
        "goodsCount": 4,
        "orderPrice": 151.60,
        "deliveryPrice": 0,
        "servicePrice": 0,
        "finalPrice": 151.60
    }
}
```


## 选品车初始化
请求接口：/api/order-web/client/shopcart/shopcartV1

请求方式：POST

请求参数：
```json
{"init":true}
```
请求成功返回值：
```json
{
    "success": true,
    "code": "200",
    "traceId": "6f9bc46a-1976-46c5-8760-54cb07875d5d",
    "data": {
        "userId": "4050103821064600030",
        "priceType": 2,
        "sellerList": [
            {
                "id": "1856880272728748216",
                "companyName": "极易净水科技（上海）有限公司",
                "shopLogo": "https://ly-marketshop-dev.oss-cn-beijing.aliyuncs.com/merchant/logo/2025/01/21/y61CR6_1737442522091_79np4aw2kwq18f1l",
                "goodsList": [
                    {
                        "select": true,
                        "sellerId": "1856880272728748216",
                        "spuId": "4050045430680600004",
                        "goodsName": "香舍里四季相伴车载香薰",
                        "goodsImage": "https://static.chinamarket.cn/seller/goods/pic/2025/02/27/ZCtOzo_1740641509723_f81i6m5rirk7nlvp",
                        "skuId": "4050045430680600489",
                        "spec": "[{\"id\": \"4050045430643200090\", \"item\": {\"id\": \"4050045430649800013\", \"value\": \"炫彩缤纷\"}, \"spec\": \"四季相伴\"}]",
                        "price": 1.52,
                        "count": 10,
                        "stock": 297,
                        "finalPrice": 15.20,
                        "showType": 1
                    },
                    {
                        "select": true,
                        "sellerId": "1856880272728748216",
                        "spuId": "4050045430680600004",
                        "goodsName": "香舍里四季相伴车载香薰",
                        "goodsImage": "https://static.chinamarket.cn/seller/goods/pic/2025/02/27/ZCtOzo_1740641509723_f81i6m5rirk7nlvp",
                        "skuId": "4050045430680600390",
                        "spec": "[{\"id\": \"4050045430643200090\", \"item\": {\"id\": \"4050045430663800036\", \"value\": \"栀子花\"}, \"spec\": \"四季相伴\"}]",
                        "price": 1.52,
                        "count": 48,
                        "stock": 266,
                        "finalPrice": 72.96,
                        "showType": 1
                    }
                ],
                "orderPrice": 88.16,
                "finalPrice": 88.16
            },
            {
                "id": "4050083818443800087",
                "companyName": "厚礼谢",
                "shopLogo": "https://ly-marketshop-dev.oss-cn-beijing.aliyuncs.com/merchant/logo/2025/04/19/spU6Ha_1745049190479_twc1q06hjjddheh4",
                "goodsList": [
                    {
                        "select": true,
                        "sellerId": "4050083818443800087",
                        "spuId": "4050083822323400052",
                        "goodsName": "厚礼谢",
                        "goodsImage": "https://ly-marketshop-dev.oss-cn-beijing.aliyuncs.com/seller/goods/pic/2025/04/19/u3NqMu_1745031499619_f9avwha4qdwltb1j",
                        "skuId": "4050083822323400059",
                        "spec": "[{\"id\": \"1899308586268982476\", \"item\": {\"id\": \"1899308586268982479\", \"value\": \"M\"}, \"spec\": \"尺寸大小\"}]",
                        "price": 2.77,
                        "count": 3,
                        "stock": 1990,
                        "finalPrice": 8.31,
                        "showType": 1
                    },
                    {
                        "select": false,
                        "sellerId": "4050083818443800087",
                        "spuId": "4050083822323400052",
                        "goodsName": "厚礼谢",
                        "goodsImage": "https://ly-marketshop-dev.oss-cn-beijing.aliyuncs.com/seller/goods/pic/2025/04/19/u3NqMu_1745031499619_f9avwha4qdwltb1j",
                        "skuId": "4050083822323400007",
                        "spec": "[{\"id\": \"1899308586268982476\", \"item\": {\"id\": \"1899308586268982481\", \"value\": \"L\"}, \"spec\": \"尺寸大小\"}]",
                        "price": 1.39,
                        "count": 1,
                        "stock": 971,
                        "finalPrice": 1.39,
                        "showType": 1
                    }
                ],
                "orderPrice": 8.31,
                "finalPrice": 8.31
            }
        ],
        "errorGoodsList": [],
        "goodsCount": 4,
        "orderPrice": 96.47,
        "deliveryPrice": 0,
        "servicePrice": 0,
        "finalPrice": 96.47
    }
}
```
