<style lang="scss" scoped>
[dir='rtl'] {
  .register-wrap .register {
    .tabs-wrap {
      span {
        &:nth-child(2) {
          border-right: 1px solid $primary-color;
          border-left: 1px solid $primary-color;
        }
        &:last-child {
          border-left: none;
        }
      }
    }
    .register-form {
      .border-title {
        :deep(span:nth-child(2)) {
          margin-right: 6px;
        }
      }
      :deep(.el-checkbox__label) {
        margin-right: 6px;
      }
    }
  }
}
.register-wrap {
  width: $main-width;
  margin: 0 auto 20px;
  .register {
    background-color: #fff;
    min-height: calc(100vh - 64px - 226px);
    .tabs-wrap {
      width: max-content;
      color: $primary-color;
      border: 1px solid $primary-color;
      span {
        display: inline-block;
        height: 26px;
        padding: 4px 16px;
        font-size: 14px;
        cursor: pointer;
        &.active {
          color: #fff;
          background-color: $primary-color;
        }
        &:not(:first-child) {
          border-left: 1px solid $primary-color;
        }
      }
    }
    .register-form {
      .title {
        border-left: 4px solid #d8131a;
        font-size: 16px;
        height: 18px;
        line-height: 18px;
      }
      .get-code-wrapper {
        :deep(.el-form-item__content) {
          flex-wrap: nowrap;

          &::after {
            display: none;
          }

          &::before {
            display: none;
          }
        }
        .code-input {
          width: 240px;
        }
        .get-code-btn {
          float: right;
          padding: 0 5px;
          height: 40px;
          margin-left: 10px;
          border: 1px solid #d8131a;
          font-size: 14px;
          text-align: center;
          line-height: 0;
          color: $primary-color;

          &.is-disabled {
            font-weight: 600;
            background: $color-C4C4C4 !important;
            color: $basic-white;
            border: none;
          }
          &:active {
            color: $basic-white;
          }
        }
      }
      .tips-item {
        margin-bottom: 10px;
        &.is-error {
          margin-bottom: 20px;
        }
      }

      .agree {
        border-top: 1px solid #edf0f5;
        :deep() {
          .el-form-item {
            display: flex;
            justify-content: center;
            align-items: center;
            padding-top: 7px;
            margin-left: 0 !important;
          }
        }
      }
      .link-text {
        font-size: 14px;
        color: $primary-color;
        margin: 0 4px;
      }
      .login-btn {
        width: max-content;
        padding: 8px 32px;
        font-size: 14px;
        line-height: 20px;
        text-align: center;
        border-radius: 2px;
        background: $primary-color;
        color: $basic-white;
        cursor: pointer;
        margin: 0 auto;
      }
      ::v-deep() {
        .has-empty-icon {
          .empty-icon img {
            width: 80px;
            height: 58px;
          }
        }
      }
    }
  }
}

[dir='rtl'] {
  .get-code-btn {
    margin-left: 0;
    margin-right: 10px;
  }
}
</style>
<template>
  <div class="register-wrap flex flex-col" :key="storageLocale">
    <div class="h-14.5 leading-17 color-[#666] text-base">注册</div>
    <div class="register flex flex-col p-5">
      <div class="tabs-wrap mb-5">
        <span v-for="item in tabs" :key="item.id" :class="{ active: userType === item.id }" @click="tabClick(item.id)">
          {{ item.name[$i18n.locale] }}
        </span>
      </div>
      <el-form ref="registerFormRef" :model="registerForm" :rules="registerFormRules" label-width="180px" size="large" class="register-form">
        <BorderTitle :title="storageLocale === 'zh' ? t('title1') : '邮箱验证'" />
        <el-row :gutter="24">
          <el-col :span="12">
            <!-- 中文时显示手机号或邮箱 -->
            <el-form-item v-if="storageLocale === 'zh'" :label="t('field1')" prop="mobile">
              <el-input v-model.trim="registerForm.mobile" :placeholder="t('filed1Placeholder')" />
            </el-form-item>
            <!-- 非中文时时显示邮箱 -->
            <el-form-item v-else :label="t('fieldEmail')" prop="mobile">
              <el-input v-model.trim="registerForm.mobile" :placeholder="t('fieldEmailPlaceholder')" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 验证码 -->
            <el-form-item class="get-code-wrapper" :label="t('field2')" prop="verifyCode">
              <el-input
                v-model.trim="registerForm.verifyCode"
                name="sd-code"
                maxlength="6"
                @input="(e) => handleInput(e, 'verifyCode')"
                class="code-input"
                :placeholder="t('filed2Placeholder')"
              />
              <el-button
                element-loading-cover
                class="get-code-btn secondary-button"
                :class="{ 'skip-translate': leftSeconds > 0, 'w-20': leftSeconds > 0, 'mr-2': storageLocale === 'ar' }"
                :disabled="leftSeconds > 0"
                @click="getCode"
              >
                {{ leftSeconds > 0 ? t('countdown', { countdown: leftSeconds }) : t('sendCaptcha') }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <!-- 密码 -->
            <el-form-item :label="t('field3')" prop="password">
              <el-input v-model.trim="registerForm.password" type="password" maxlength="30" show-password :placeholder="t('filed3Placeholder')" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 确认密码 -->
            <el-form-item :label="t('field4')" prop="confirmPassword">
              <el-input v-model.trim="registerForm.confirmPassword" type="password" maxlength="30" show-password :placeholder="t('filed4Placeholder')" />
            </el-form-item>
          </el-col>
        </el-row>
        <div v-if="userType !== 1">
          <!-- 店铺信息 -->
          <BorderTitle :title="t('title2')" />
          <el-row :gutter="24">
            <el-col :span="12">
              <!-- 公司名 -->
              <el-form-item prop="companyName">
                <template #label>
                  <span class="items-center">
                    <span class="mr-1">{{ t('companyName') }}</span>
                    <el-tooltip :content="t('companyNameTip')" placement="top">
                      <icon type="icon-tishi" :size="16" />
                    </el-tooltip>
                  </span>
                </template>
                <el-input v-model.trim="registerForm.companyName" :placeholder="t('companyNamePlaceholder')" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <!-- 地址 -->
              <el-form-item :label="t('address')" prop="address">
                <el-input v-model.trim="registerForm.address" :placeholder="t('addressPlaceholder')" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <!-- 微信 -->
              <el-form-item :label="t('wechat')" prop="wechat">
                <el-input v-model.trim="registerForm.wechat" :placeholder="t('wechatPlaceholder')" />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="userType === 4 && storageLocale === 'zh'">
              <!-- 邮箱 -->
              <el-form-item :label="t('fieldEmail')" prop="email">
                <el-input v-model.trim="registerForm.email" :placeholder="t('fieldEmailPlaceholder')" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <!-- 证件及个人信息 -->
        <BorderTitle :title="userType !== 1 ? t('title3') : '个人信息'" />
        <template v-if="userType !== 1">
          <el-row :gutter="24">
            <el-col :span="9">
              <el-form-item :label="t('idCardImg')" prop="idFrontUrl" class="tips-item">
                <div class="flex flex-col justify-center items-center has-empty-icon">
                  <ImgUpload
                    v-model="registerForm.idFrontUrl"
                    :emptyIcon="`${ossUrl}/mall/id-card-front.png`"
                    :emptyText="''"
                    :tipsText="t('idCardFrontPlaceholder')"
                    :dir="OSS_DIR.ID_CARD"
                    :size-limit="10"
                    height="122px"
                    width="244px"
                  >
                  </ImgUpload>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item prop="idBackUrl" label-width="0" class="tips-item ml-3">
                <div class="flex flex-col justify-center items-center has-empty-icon">
                  <ImgUpload
                    v-model="registerForm.idBackUrl"
                    :emptyIcon="`${ossUrl}/mall/id-card-back.png`"
                    :emptyText="''"
                    :tipsText="t('idCardBackPlaceholder')"
                    :dir="OSS_DIR.ID_CARD"
                    :size-limit="10"
                    height="122px"
                    width="244px"
                  >
                  </ImgUpload>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item style="margin-bottom: 5px">
            <div class="h-5 leading-5">{{ userType !== 1 ? t('idCardSellerTips') : t('idCardBuyerTips') }}</div>
          </el-form-item>
        </template>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item :label="t('userName')" prop="userName">
              <el-input v-model="registerForm.userName" :placeholder="t('userNamePlaceholder')" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="userType !== 1">
            <el-form-item :label="t('idNo')" prop="idNo">
              <el-input v-model.trim="registerForm.idNo" maxlength="18" :placeholder="t('idNoPlaceholder')" />
            </el-form-item>
          </el-col>
        </el-row>
        <template v-if="userType !== 1">
          <!-- 营业执照 -->
          <el-form-item :label="t('certUrl')" prop="certUrl" class="business-license tips-item">
            <div class="flex flex-col justify-center items-center has-empty-icon">
              <ImgUpload
                v-model="registerForm.certUrl"
                :emptyIcon="`${ossUrl}/mall/business-license.png`"
                :emptyText="''"
                :tipsText="t('certUrlTipsPlaceholder')"
                :dir="OSS_DIR.CERT"
                :size-limit="10"
                height="122px"
                width="244px"
              >
              </ImgUpload>
            </div>
          </el-form-item>
          <el-form-item>
            <div class="h-5 leading-5">{{ t('certUrlTips') }}</div>
          </el-form-item>
        </template>
        <el-row :gutter="24" v-if="userType === 2">
          <el-col :span="12">
            <!-- 推荐码 -->
            <el-form-item :label="t('referralCode')" prop="referralCode">
              <el-input v-model.trim="registerForm.referralCode" :disabled="!!localReferralCode" maxlength="30" :placeholder="t('referralCodePlaceholder')" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 所属市场 -->
            <el-form-item :label="t('market')" prop="market">
              <el-select v-model="registerForm.market" filterable placeholder="请选择所属市场">
                <el-option v-for="item in marketLists" :key="item" :label="item" :value="item" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" v-if="userType !== 1">
          <el-col :span="12">
            <!-- 开户行 -->
            <el-form-item :label="t('depositBank')" prop="depositBank">
              <el-input v-model.trim="registerForm.depositBank" maxlength="30" :placeholder="t('depositBankPlaceholder')" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 银行账号 -->
            <el-form-item :label="t('bankNo')" prop="bankNo">
              <el-input v-model.trim="registerForm.bankNo" type="number" maxlength="30" :placeholder="t('bankNoPlaceholder')" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-col :span="12" v-if="userType === 2">
          <!-- 账户名 -->
          <el-form-item :label="t('accountName')" prop="accountName">
            <el-input v-model.trim="registerForm.accountName" maxlength="30" :placeholder="t('accountNamePlaceholder')" />
          </el-form-item>
        </el-col>
        <!-- 协议 -->
        <div class="flex justify-center agree">
          <el-form-item label="" prop="isAgree" label-width="0">
            <el-checkbox-group v-model="registerForm.isAgree">
              <el-checkbox value="" name="isAgree">
                <span class="ml-1">{{ t('agree') }}</span>
                <a :href="REGISTER_FILE_ADDRESS[$i18n.fallbackLocale]" target="_blank" class="link-text">{{ t('servicAgree') }}</a>
                <span class="mx-1">{{ t('and') }}</span>
                <a :href="PRIVACY_FILE_ADDRESS[$i18n.fallbackLocale]" target="_blank" class="link-text">{{ t('secretAgree') }}</a>
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>
        <div class="flex justify-center">
          <el-button type="primary" class="login-btn" @click="submitForm" :loading="submitLoading">{{ t('submit') }}</el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { computed, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import { SMS_SEND_WAY } from '@/constants'
import { PRIVACY_FILE_ADDRESS, REGISTER_FILE_ADDRESS } from '@/constants/agreement-files'
import { ossUrl } from '@/constants/common'
import { REGISTER_TYPE_ARRAY } from '@/constants/mall'
import { OSS_DIR } from '@/constants/oss-dir'
import { useUserStore } from '@/pc/stores'
import { smsSend, userRegister } from '@/apis/common.js'
import { marketList } from '@/apis/market'
import { useStorageLocale } from '@/i18n'
import { validateIDNumber } from '@/common/js/validator.js'
import user from '@/pc/utils/user'

const { t } = useI18n({
  messages: {
    zh: {
      title1: '手机号或邮箱验证',
      title2: '店铺信息',
      title3: '证件及个人信息',
      field1: '手机号或邮箱',
      filed1Placeholder: '请输入手机号或邮箱',
      filed1Validate: '请输入正确的手机号或邮箱',
      fieldEmail: '邮箱',
      fieldEmailPlaceholder: '请输入邮箱',
      fieldEmailValidate: '请输入正确的邮箱',
      field2: '验证码',
      filed2Placeholder: '请输入验证码',
      field3: '密码',
      filed3Placeholder: '请输入密码',
      field4: '确认密码',
      filed4Placeholder: '请再次输入密码',
      filed4Validate: '两次输入的密码不一致',
      sendCaptcha: '获取验证码',
      sendCaptchaSuccess: '验证码发送成功',
      countdown: '{countdown}s',
      companyName: '公司名', // 公司名
      companyNamePlaceholder: '请输入公司名',
      companyNameTip: '公司名称即为店铺名称，请选择合适的营业执照上传',
      address: '地址', // 地址
      addressPlaceholder: '请填写详细到门牌号的具体地址',
      wechat: '微信', // 微信
      wechatPlaceholder: '请输入微信',
      idCardImg: '身份证照片', // 身份证照片
      idCardFrontPlaceholder: '正面（个人照片面）',
      idCardSellerTips: '法人身份证照片（清晰且露出4个角）',
      idCardBuyerTips: '身份证照片（清晰且露出4个角）',
      idCardFrontValidate: '请上传身份证正面照片',
      idCardBackPlaceholder: '反面（国徽面）',
      idCardBackTips: '反面（国徽面）',
      idCardBackValidate: '请上传身份证反面照片',
      userName: '姓名', // 姓名
      userNamePlaceholder: '请输入姓名',
      idNo: '身份证号码', // 身份证号码
      idNoPlaceholder: '请输入身份证号码',
      idNoValidate: '请输入正确的身份证号',
      certUrl: '营业执照',
      certUrlTipsPlaceholder: '营业执照照片',
      certUrlTips: '营业执照原件照片（清晰且露出4个角）',
      certUrlPlaceholder: '请上传营业执照',
      referralCode: '推荐码',
      referralCodePlaceholder: '请输入推荐码',
      market: '所属市场',
      marketPlaceholder: '请输入所属市场',
      accountName: '账户名',
      accountNamePlaceholder: '请输入账户名',
      submit: '确认注册并登录',
      verify: '请滑动验证',
      explain: '未注册的手机号码验证后将自动创建新账号',
      agree: '我已阅读并同意',
      servicAgree: '《注册服务协议》',
      and: '和',
      secretAgree: '《隐私协议》',
      readAgree: '请阅读并同意服务协议',
      servicesLink: 'https://static.chinamarket.cn/static/trade-exhibition/file/注册服务协议-中国大集.pdf',
      privacyLink: 'https://static.chinamarket.cn/static/trade-exhibition/file/隐私政策-中国大集.pdf',
      registerSuccess: '注册成功',
      certFail: '营业执照审核失败！',
      depositBank: '开户行',
      depositBankPlaceholder: '请输入开户行',
      bankNo: '银行账号',
      bankNoPlaceholder: '请输入银行账号',
    },
    en: {
      title1: 'Phone number verification',
      title2: 'Shop information',
      title3: 'Documents and personal information',
      field1: 'Phone or email',
      filed1Placeholder: 'Please input your phone number or email',
      filed1Validate: 'Please enter a valid phone number or email',
      field2: 'Captcha',
      filed2Placeholder: 'please input the captcha',
      field3: 'Password',
      filed3Placeholder: 'Please input your password',
      field4: 'Password confirmation',
      filed4Placeholder: 'Please enter your confirmation password',
      filed4Validate: 'The two passwords are different',
      sendCaptcha: 'Send captcha',
      sendCaptchaSuccess: 'Captcha sent successfully',
      countdown: '{countdown} second left',
      companyName: 'CompanyName', // 公司名
      companyNamePlaceholder: 'Please enter the company name',
      companyNameTip: 'The company name is the shop name, please upload an appropriate business license',
      address: 'Address', // 地址
      addressPlaceholder: 'Please enter your address',
      wechat: 'Wechat', // 微信
      wechatPlaceholder: 'Please enter wechat',
      idCardImg: 'idCardImg', // 身份证照片
      idCardFrontPlaceholder: 'Front (personal photo side)',
      idCardSellerTips: `Legal Representative's ID Card Photo（Clear photo showing all four corners of the license）`,
      idCardBuyerTips: 'ID Card Photo（Clear photo showing all four corners of the license）',
      idCardFrontValidate: 'Please upload the front photo of your ID card',
      idCardBackPlaceholder: 'Reverse (national emblem)',
      idCardBackTips: 'Reverse (national emblem)',
      idCardBackValidate: 'Please upload the reverse photo of your ID card',
      userName: 'UserName', // 姓名
      userNamePlaceholder: 'Please enter your name',
      idNo: 'IDCard No', // 身份证号码
      idNoPlaceholder: 'Please enter your ID number',
      idNoValidate: 'Please enter the correct ID number',
      certUrl: 'Business license',
      certUrlTipsPlaceholder: 'Business license photo',
      certUrlTips: 'Business License Original Photo（Clear photo showing all four corners of the license）',
      certUrlPlaceholder: 'Please upload business license',
      submit: 'Confirm registration and login',
      verify: 'Please slide to verify',
      explain: 'Unregistered phone numbers will automatically create a new account after verification.',
      agree: 'I have read and agree',
      servicAgree: '《Services Statement》',
      and: 'and',
      secretAgree: '《Privacy Statement》',
      readAgree: 'Please read and agree to the service agreement',
      servicesLink:
        'https://static.chinamarket.cn/static/trade-exhibition/file/%E3%80%90%E8%8B%B1%E6%96%87%E3%80%91%E6%B3%A8%E5%86%8C%E6%9C%8D%E5%8A%A1%E5%8D%8F%E8%AE%AE.pdf',
      privacyLink: 'https://static.chinamarket.cn/static/trade-exhibition/file/%E3%80%90%E8%8B%B1%E6%96%87%E3%80%91%E9%9A%90%E7%A7%81%E6%94%BF%E7%AD%96.pdf',
      registerSuccess: 'Register successfully',
      certFail: 'Business license verification failed!',
      depositBank: 'Bank Name',
      depositBankPlaceholder: 'Please enter your bank name',
      bankNo: 'Bank Account Number',
      bankNoPlaceholder: 'Please enter your bank number',
    },
  },
})
const { storageLocale } = useStorageLocale()

// 限制只能输入数字
const handleInput = (val, key) => {
  registerForm[key] = val.replace(/\D/g, '')
}

const tabs = [REGISTER_TYPE_ARRAY[1], REGISTER_TYPE_ARRAY[0], REGISTER_TYPE_ARRAY[3], REGISTER_TYPE_ARRAY[2]]

const userType = ref(1)
const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
userType.value = Number(route.query?.userType)

// 校验规则
const validateConfirmPassword = (rule, value, callback) => {
  if (value !== registerForm.password) {
    callback(new Error(t('filed4Validate')))
  } else {
    callback()
  }
}

const registerFormRules = computed(() => {
  return {
    mobile: [
      {
        required: true,
        message: storageLocale.value === 'zh' ? t('filed1Placeholder') : t('fieldEmailPlaceholder'),
        trigger: ['change', 'blur'],
      },
      {
        pattern:
          storageLocale.value === 'zh'
            ? /^1[3456789]\d{9}$|^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
            : /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, // 手机号或邮箱, 邮箱
        message: storageLocale.value === 'zh' ? t('filed1Validate') : t('fieldEmailValidate'),
        trigger: 'blur',
      },
    ],
    verifyCode: [{ required: true, pattern: /^[0-9]{6}$/, message: computed(() => t('filed2Placeholder')), trigger: 'blur' }],
    password: [{ required: true, message: computed(() => t('filed3Placeholder')), trigger: 'blur' }],
    confirmPassword: [
      { required: true, message: computed(() => t('filed4Placeholder')), trigger: 'blur' },
      { validator: validateConfirmPassword, trigger: 'blur' },
    ],
    address: [{ required: userType.value !== 4, message: computed(() => t('addressPlaceholder')), trigger: 'blur' }],
    userName: [{ required: true, message: computed(() => t('userNamePlaceholder')), trigger: 'blur' }],
    depositBank: [{ required: userType.value === 4, message: computed(() => t('depositBankPlaceholder')), trigger: 'blur' }],
    bankNo: [{ required: userType.value === 4, message: computed(() => t('bankNoPlaceholder')), trigger: 'blur' }],
    isAgree: [{ type: 'array', required: true, message: computed(() => t('readAgree')), trigger: 'change' }],
    companyName: [{ required: true, message: computed(() => t('companyNamePlaceholder')), trigger: 'blur' }],
    wechat: [{ required: false, message: computed(() => t('wechatPlaceholder')), trigger: 'blur' }],
    idFrontUrl: [{ required: true, message: computed(() => t('idCardFrontValidate')), trigger: 'change' }],
    idBackUrl: [{ required: true, message: computed(() => t('idCardBackValidate')), trigger: 'change' }],
    idNo: [
      { required: true, message: computed(() => t('idNoPlaceholder')), trigger: 'blur' },
      {
        validator: (rule, value, callback) => {
          if (value && !validateIDNumber(value)) {
            callback(new Error(t('idNoValidate')))
          } else {
            callback()
          }
        },
        trigger: 'blur',
      },
    ],
    certUrl: [{ required: true, message: computed(() => t('certUrlPlaceholder')), trigger: 'change' }],
  }
})

// 登录表单
const registerFormRef = ref(null)
const registerForm = reactive({
  mobile: '', // 手机号
  verifyCode: '', // 验证码
  password: '', // 密码
  confirmPassword: '', // 确认密码
  email: '',
  companyName: '', // 公司名
  address: '', // 地址
  wechat: '', // 微信
  idFrontUrl: '', // 身份证照片
  idBackUrl: '',
  userName: '', // 姓名
  idNo: '', // 身份证号码
  certUrl: '', // 营业执照
  referralCode: '', // 推荐码
  market: '', // 所属市场
  depositBank: '', // 开户行
  bankNo: '', // 银行账号
  accountName: '', // 账户名
  isAgree: [],
})

const localReferralCode = ref(null)
const getShareCode = () => {
  const { referralCode } = route.query || {}
  const localKey = '_share_code'
  if (referralCode) {
    console.log(referralCode, 'referralCode')
    window.localStorage.setItem(localKey, referralCode)
    registerForm.referralCode = referralCode
    localReferralCode.value = referralCode
    return
  }
  const localReferralCodeStr = window.localStorage.getItem(localKey) || ''
  registerForm.referralCode = localReferralCodeStr
  localReferralCode.value = localReferralCodeStr
}

onMounted(() => {
  getShareCode()
})

// 注册身份切换
const tabClick = (id) => {
  userType.value = id
  setTimeout(() => {
    registerFormRef.value.resetFields()
  }, 1)
}

// 所属市场列表
let marketLists = ref([])
const getMarketList = async () => {
  const res = await marketList()
  const list = Object.values(res)
    .flat()
    .map((item) => {
      return item.name
    })
  marketLists.value = ['其他', ...list]
}
onMounted(() => {
  userType.value === 2 && getMarketList()
})
// 监视 userType 的变化, 为卖家时需要请求
watch(userType, (newValue) => {
  if (newValue === 2) {
    getMarketList()
  }
})

// 倒计时
const leftSeconds = ref(0)
const timer = ref(null)
const startCountDown = () => {
  leftSeconds.value = 60
  const countDown = () => {
    timer.value = setTimeout(() => {
      leftSeconds.value -= 1
      leftSeconds.value > 0 && countDown()
    }, 1000)
  }
  countDown()
}

// 点击获取验证码
const sendCode = async () => {
  const body = {
    mobile: registerForm.mobile,
    type: SMS_SEND_WAY.REGISTER.id,
  }
  try {
    await smsSend(body)
    ElMessage({
      message: t('sendCaptchaSuccess'),
      type: 'success',
    })
    startCountDown()
  } catch (error) {
    registerForm.verifyCode = ''
  }
}

// 获取验证码
const getCode = () => {
  registerFormRef.value.validateField('mobile', (isValid) => {
    if (isValid) {
      sendCode()
    }
  })
}

// 登录提交
const submitLoading = ref(false)
const submitForm = async () => {
  registerForm.userName = registerForm.userName.trim() // 此处为姓名去空格，防止英文状态无法输入空格
  await registerFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      submitLoading.value = true
      try {
        // eslint-disable-next-line
        const { confirmPassword, isAgree, idFrontUrl, idBackUrl, certUrl, ...params } = { ...registerForm, userType: userType.value }
        const body = { idFrontUrl: idFrontUrl[0] ?? '', idBackUrl: idBackUrl[0] ?? '', certUrl: certUrl[0] ?? '', ...params }
        const res = await userRegister(body)
        if (res?.code === 3018) {
          ElMessage({ message: t('certFail'), type: 'error' })
        }
        if (res?.code === 200) {
          userStore.setIsLogined(true)
          user.setToken(res?.data?.accessToken)
          userStore.setUserInfo(res?.data)
          router.push(REGISTER_TYPE_ARRAY[userType.value - 1]?.path)

          ElMessage({
            message: t('registerSuccess'),
            type: 'success',
          })
        }
      } catch (error) {
        console.log(error)
      } finally {
        submitLoading.value = false
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}
</script>
