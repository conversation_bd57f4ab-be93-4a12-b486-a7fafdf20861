<template>
  <swiper
    v-if="imgList.length"
    :loop="true"
    :autoplay="{
      delay: 2500,
      disableOnInteraction: false,
    }"
    :pagination="true"
    :modules="modules"
    class="w-[100%] h-[350px] bg-[#fff]"
  >
    <swiper-slide v-for="(item, i) in imgList" :key="i">
      <div class="w-[100%] h-[100%] flex justify-center items-center">
        <img-loader :src="item" :loading-img="`${item}?x-oss-process=image/resize,h_50`" class="object-cover" />
      </div>
    </swiper-slide>
  </swiper>
</template>
<script setup>
// Import Swiper Vue.js components
// Import Swiper styles
import 'swiper/css'
import 'swiper/css/pagination'
// import required modules
import { Autoplay } from 'swiper/modules'
import { Swiper, SwiperSlide } from 'swiper/vue'

const modules = [Autoplay]

const props = defineProps({
  shopInfo: {
    type: Object,
    default: () => ({}),
  },
})

const imgList = computed(() => props.shopInfo?.shopMainImage?.split(',')?.filter((item) => !!item) || [])
</script>

<style scoped lang="scss">
.img-loader {
  width: 100%;
  height: 100%;
  object-fit: cover; /* 保持图片的宽高比 */
  object-position: center; /* 图片居中 */

  :deep(img) {
    width: 100%;
    height: 100%;
    object-fit: cover; /* 保持图片的宽高比 */
    object-position: center; /* 图片居中 */
  }
}
</style>
