<template>
  <div class="text-[20px] font-600 mb-[12px] pt-[8px]" v-if="title">{{ title }}</div>
  <div :class="['goods-list', { 'no-data': goodsList.length === 0 }]">
    <GoodsDetailCardNew v-for="item in goodsList" :key="item.id" :goodsInfo="item" :shopId="shopId" />
    <el-empty v-if="showEmpty && goodsList.length === 0 && !loading" :image-size="200" />
  </div>
  <div v-if="loading" class="py-[24px] text-center color-[#999] text-[24px] w-[100%]">
    <el-icon class="loading-icon"><Loading /></el-icon>
  </div>
</template>

<script setup>
import { Loading } from '@element-plus/icons-vue'
import GoodsDetailCardNew from '@/pc/components/goods-detail-card/goods-detail-card-new.vue'

defineProps({
  goodsList: {
    type: Array,
    default: () => [],
  },
  showEmpty: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '',
  },
  loading: {
    type: <PERSON>olean,
    default: false,
  },
  isNewGoods: {
    type: Boolean,
    default: false,
  },
})

const route = useRoute()
const shopId = computed(() => route?.params?.id)
</script>

<style scoped lang="scss">
.goods-list {
  // overflow: auto;
  width: $main-width;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 10px;
  margin: 0 auto 24px;

  &.no-data {
    display: flex;
    justify-content: center;
  }
}

.loading-icon {
  animation: spin 1s linear infinite; /* 动画属性：1秒转一圈，线性，循环无限次 */
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
