<template>
  <div class="category-wrap relative" v-if="categoryList && categoryList.length">
    <div class="flex items-center overflow-hidden h-[38px] bg-[#fff] rounded-[8px] mb-[16px] flex-wrap static">
      <div :class="`main-tab shrink-0 static ${!activeId ? 'active' : ''}`" @mouseenter.stop="handleMouseenter" @mouseleave="handleMouseleave">
        <div class="w-[100%] h-[100%] flex items-center justify-center" @click="handleSelect(null)">
          {{ t('allCategory') }}
          <div class="line"></div>
          <el-icon :class="`icon ${visible ? 'extend' : ''}`"><ArrowDown /></el-icon>
        </div>

        <!-- 下拉类目 -->
        <div :class="`category-list ${visible ? 'visible' : ''}`">
          <div v-for="item in categoryList" :key="item.id" class="category-list-item">
            <div :class="`cursor-pointer item ${activeId === item.id ? 'active' : ''}`" @click="handleSelect(item)">{{ item.categoryName }}</div>
          </div>
        </div>
      </div>
      <div v-for="item in categoryList" :key="item.id" :class="`item-tab ${activeId === item.id ? 'active' : ''}`" @click="handleSelect(item)">
        {{ item.categoryName }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ArrowDown } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n({
  messages: {
    zh: {
      allCategory: '全部类目',
    },
    en: {
      allCategory: 'All Categories',
    },
  },
})

const emit = defineEmits(['handleSelect'])
const visible = ref(false)

const handleSelect = (item) => {
  emit('handleSelect', item?.id)
  visible.value = false
}

const handleMouseenter = () => {
  nextTick(() => {
    if (!visible.value) visible.value = true
  })
}
const handleMouseleave = () => {
  if (visible.value) visible.value = false
}

defineProps({
  categoryList: {
    type: Array,
    default: () => [],
  },
  activeId: {
    type: String,
    default: null,
  },
})
</script>

<style scoped lang="scss">
.main-tab {
  height: 100%;
  cursor: pointer;
  border-radius: 8px;
  font-size: 16px;
  min-width: 140px;
  background: #fff;
  padding: 0 12px;
  box-sizing: border-box;
  color: #fff;
  background: linear-gradient(270deg, #d8131a 0%, #e96f18 100%);

  .icon {
    transition: transform 0.3s ease;

    &.extend {
      transform: rotateZ(-180deg);
    }
  }
}

.line {
  height: 22px;
  border-right: 1px solid #fff;
  margin: 0 12px;
}

.item-tab {
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 140px;
  padding: 0 8px;
  height: 100%;
  white-space: nowrap;
  cursor: pointer;
  font-size: 16px;

  &.active {
    color: $primary-color;
  }
}

.category-list {
  display: none;
  flex-wrap: wrap;
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.1);
  max-height: 320px;
  z-index: 3;
  padding: 16px 20px;
  box-sizing: border-box;
  overflow: auto;
  color: #333;

  &.visible {
    display: flex;
  }

  .category-list-item {
    width: 14%;
    min-height: 38px;

    .item {
      display: flex;
      align-items: center;
      min-height: 38px;
      padding: 6px 12px;
      width: max-content;
      max-width: 100%;

      &:hover {
        color: $primary-color;
      }

      &.active {
        border-radius: 24px;
        background: #ffe2e2;
        color: $primary-color;
      }
    }
  }
}
</style>
