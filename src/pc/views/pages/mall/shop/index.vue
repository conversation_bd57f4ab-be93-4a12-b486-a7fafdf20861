<template>
  <div class="home-wrap pd-[24px]">
    <div class="h-[140px]" />
    <div class="logo-nav">
      <SearchInput ref="searchRef" is-search-page @on-search="onSearch" />
    </div>
    <div class="home-content w-1260">
      <Breadcrumb :breadcrumbList="breadcrumbList" />
      <store-info :shopInfo="shopInfo" />
      <category-list :categoryList="category" @handleSelect="handelCategory" :activeId="form.categoryId" />
      <template v-if="!form.keyword && !form.categoryId">
        <shop-banner :shopInfo="shopInfo" class="mb-[12px]" />
        <shop-goods v-for="(item, i) in homeGoodsList" :key="i" :goodsList="item.goodsList" :title="item.categoryName" isNewGoods />
        <shop-goods :goodsList="goodsList" :title="goodsList.length ? t('otherProducts') : ''" :loading="isLoading" show-empty isNewGoods />
      </template>
      <shop-goods v-else :goodsList="goodsList" show-empty :loading="isLoading" />
      <div v-if="goodsList.length && isEnd" class="text-center color-[#999] mb-[24px]">- {{ t('noMore') }} -</div>
    </div>
    <!-- <BackTop /> -->
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
// import BackTop from '@/pc/components/back-top/back-top.vue'
import Breadcrumb from '@/pc/components/breadcrumb/index.vue'
import SearchInput from '@/pc/components/search-input/search-input.vue'
import StoreInfo from '@/pc/views/pages/mall/components/store-info/index.vue'
import CategoryList from './components/category-list/index.vue'
import ShopBanner from './components/shop-banner/index.vue'
import ShopGoods from './components/shop-goods/index.vue'
import { getGoodsList, getShopCategoryInfo } from '@/apis/goods'
import { getSellerById } from '@/apis/merchants'

const { t } = useI18n({
  messages: {
    zh: {
      otherProducts: '其他商品',
      noMore: '没有更多',
    },
    en: {
      otherProducts: 'Other Products',
      noMore: 'No More',
    },
  },
})

const breadcrumbList = reactive([
  {
    path: '/mall',
    name: { zh: '在线商城', en: 'Online Store' },
  },
  {
    name: { zh: '店铺主页', en: 'Shop Homepage' },
  },
])

const route = useRoute()
const merchantId = route.params.id
const category = ref([])
const getTopCategory = async () => {
  category.value = (await getShopCategoryInfo({ userId: merchantId })) || []
  await getHomeGoodsList()
}

const homeGoodsList = ref([])
const getListRequest = async (item) => {
  try {
    const { rowList } = await getGoodsList({ pageSize: 10, pageNum: 1, categoryId: item.id, userId: merchantId })
    return {
      goodsList: rowList || [],
      ...item,
    }
  } catch (e) {
    console.log(e)
    return null
  }
}
const getShopHomeGoods = () => {
  return Promise.all(
    category.value.map((item) => {
      return getListRequest(item)
    }),
  )
}
const getHomeGoodsList = async () => {
  try {
    const arr = (await getShopHomeGoods()) || []
    homeGoodsList.value = arr.filter((item) => item)
  } catch (e) {
    console.log(e)
  }
}

const form = reactive({
  search: null,
  categoryId: null,
})
const searchRef = ref(null)
const onSearch = (keyword) => {
  queryList({ keyword })
}
const handelCategory = (categoryId) => {
  queryList({ categoryId })
}

const queryList = ({ keyword, categoryId } = {}) => {
  form.keyword = keyword
  form.categoryId = categoryId
  if (!keyword) {
    searchRef.value.clear()
  }
  pagination.pageNum = 1
  isEnd.value = false
  getShopGoodsList()
}

const goodsList = ref([])
const isLoading = ref(false)
const isEnd = ref(false)
const pagination = reactive({
  pageNum: 1,
  pageSize: 20,
})
const getShopGoodsList = async () => {
  if (isEnd.value) return
  isLoading.value = true
  try {
    const res = await getGoodsList({
      search: form.keyword,
      categoryId: form.categoryId,
      userId: merchantId,
      ...pagination,
    })
    const { rowList, totalRecord } = res
    if (pagination.pageNum === 1) {
      goodsList.value.length = 0
    }
    goodsList.value.push(...rowList)
    isLoading.value = false
    if (totalRecord > goodsList.value.length) {
      Object.assign(pagination, {
        pageNum: pagination.pageNum + 1,
      })
    } else {
      isEnd.value = true
    }
  } catch (e) {
    console.log(e)
    isLoading.value = false
  }
}

const shopInfo = ref(null)
const getShopInfo = async () => {
  try {
    shopInfo.value = await getSellerById({ id: merchantId })
  } catch (e) {
    console.log(e)
  }
}

getShopInfo()
getTopCategory()
getShopGoodsList()

const doScroll = () => {
  const scrollTop = window.scrollY || document.documentElement.scrollTop || document.body.scrollTop
  const scrollHeight = window.scrollHeight || document.documentElement.scrollHeight || document.body.scrollHeight
  const clientHeight = window.clientHeight || document.documentElement.clientHeight || document.body.clientHeight

  if (scrollTop + clientHeight >= scrollHeight - 280) {
    !isLoading.value && getShopGoodsList()
  }
}
onMounted(() => {
  document.addEventListener('scroll', doScroll)
})

onUnmounted(() => {
  document.removeEventListener('scroll', doScroll)
})
</script>

<style lang="scss" scoped>
.home-wrap {
  min-height: calc(100vh - 64px - 162px);
}

.logo-nav {
  position: fixed;
  top: 64px;
  z-index: 49;
  width: 100%;
  height: 64px;
  margin: 0 auto 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.1);
  background-color: #fff;
}
</style>
