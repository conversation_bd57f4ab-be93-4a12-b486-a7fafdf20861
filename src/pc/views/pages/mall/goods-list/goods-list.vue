<!-- 商品列表页面 -->
<template>
  <div class="goods">
    <div class="h-[160px]" />
    <div class="logo-nav">
      <SearchInput ref="searchRef" is-search-page @on-search="onSearch" />
    </div>
    <SearchCategory ref="searchCategoryRef" @on-search="handleCategorySearch" />
    <div :class="['goods-list', { 'no-data': goodsList.length === 0 }]">
      <GoodsDetailNewCard v-for="item in goodsList" :key="item.id" :goodsInfo="item" />
      <!-- <GoodsDetailCard v-for="item in goodsList" :key="item.id" :goodsInfo="item" /> -->
      <el-empty v-if="goodsList.length === 0 && !isLoading" :image-size="200" />
    </div>
    <div v-if="isLoading" class="py-[24px] text-center color-[#999] text-[24px] w-[100%]">
      <el-icon class="loading-icon"><Loading /></el-icon>
    </div>
    <div v-else-if="goodsList.length && isEnd" class="text-center color-[#999] mb-[24px]">- {{ t('noMore') }} -</div>
    <!-- 客服二维码 -->
    <!-- <div class="service-qrcode">
      <img src="https://static.chinamarket.cn/static/trade-exhibition/wechat-work-qrcode.png" alt="" />
      <div class="tips">联系客服</div>
    </div> -->
    <!-- 返回顶部 -->
    <!-- <BackTop /> -->
  </div>
</template>

<script setup>
import { Loading } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
// import BackTop from '@/pc/components/back-top/back-top.vue'
import GoodsDetailNewCard from '@/pc/components/goods-detail-card/goods-detail-card-new.vue'
import SearchInput from '@/pc/components/search-input/search-input.vue'
import SearchCategory from '@/pc/views/pages/mall/goods-list/components/search-category/index.vue'
import * as API from '@/apis/goods'
import { useEvent } from '@/event'
import { LANG_CHANGED } from '@/event/modules/site'

const { t } = useI18n({
  messages: {
    zh: {
      noMore: '没有更多',
    },
    en: {
      noMore: 'No More',
    },
  },
})

const route = useRoute()
const isLoading = ref(false) // 是否正在加载
const isEnd = ref(false)
const goodsList = ref([])
const pagination = reactive({
  pageSize: 20,
  pageNum: 1,
})
const searchRef = ref(null)
const searchCategoryRef = ref(null)
const form = reactive({})

const onSearch = (keyword) => {
  form.keyword = keyword
  form.categoryId = null
  form.parentCategoryId = null
  searchCategoryRef.value.clear()
  queryGoodsList()
}

const handleCategorySearch = ({ categoryId, parentCategoryId, keyword }) => {
  form.categoryId = null
  form.parentCategoryId = null
  form.keyword = null
  if (keyword) form.keyword = keyword
  if (categoryId) form.categoryId = categoryId
  if (parentCategoryId) form.parentCategoryId = parentCategoryId
  queryGoodsList()
}

const queryGoodsList = () => {
  Object.assign(pagination, {
    pageNum: 1,
  })
  isEnd.value = false
  getGoodsList()
}

const getGoodsList = async () => {
  if (isEnd.value) return
  isLoading.value = true
  const res = await API.getGoodsList({
    search: form.keyword,
    categoryId: form.categoryId,
    parentCategoryId: form.parentCategoryId,
    ...pagination,
  })
  const { rowList, totalRecord } = res
  if (pagination.pageNum === 1) {
    goodsList.value.length = 0
  }
  goodsList.value.push(...rowList)
  isLoading.value = false
  if (totalRecord > goodsList.value.length) {
    Object.assign(pagination, {
      pageNum: pagination.pageNum + 1,
    })
  } else {
    isEnd.value = true
  }
}

const doScroll = () => {
  const scrollTop = window.scrollY || document.documentElement.scrollTop || document.body.scrollTop
  const scrollHeight = window.scrollHeight || document.documentElement.scrollHeight || document.body.scrollHeight
  const clientHeight = window.clientHeight || document.documentElement.clientHeight || document.body.clientHeight

  if (scrollTop + clientHeight >= scrollHeight - 280) {
    !isLoading.value && getGoodsList()
  }
}
onMounted(() => {
  const { keyword, categoryId, categoryName } = route.query
  if (keyword) {
    searchRef.value.handleAutoSearch(keyword)
  } else if (categoryId || categoryName) {
    searchCategoryRef.value.handleAutoSearch(categoryId, categoryName)
  } else {
    getGoodsList()
  }
  document.addEventListener('scroll', doScroll)
})

onUnmounted(() => {
  document.removeEventListener('scroll', doScroll)
})

const event = useEvent()
event.on(LANG_CHANGED, () => {
  onSearch(searchRef.value.inputValue)
})
</script>

<style lang="scss" scoped>
.goods {
  min-height: calc(100vh - 64px - 162px);

  .logo-nav {
    position: fixed;
    top: 64px;
    z-index: 49;
    width: 100%;
    height: 64px;
    margin: 0 auto 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
    background-color: #fff;
  }
  .affix {
    height: 120px;
    width: 100%;
  }
  .goods-list {
    // overflow: auto;
    width: $main-width;
    margin: 0 auto 24px;
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 10px;

    & > div {
      max-width: 244px;
    }

    &.no-data {
      display: flex;
      justify-content: center;
    }
  }

  // .service-qrcode {
  //   position: fixed;
  //   top: 238px;
  //   left: 50%;
  //   transform: translateX(calc($main-width / 2 + 10px));
  //   width: 168px;
  //   height: 206px;
  //   border-radius: 8px;
  //   background: #ffffff;
  //   padding: 24px;
  //   img {
  //     width: 120px;
  //     height: 120px;
  //     margin-bottom: 16px;
  //   }
  //   .tips {
  //     font-size: 16px;
  //     color: #333333;
  //   }
  // }

  // // 媒体查询
  // @media screen and (max-width: 1660px) {
  //   .goods-list {
  //     width: 1006px;
  //     transform: translateX(-122px);
  //   }
  //   .service-qrcode {
  //     transform: translateX(calc(1006px / 2 + 10px - 122px));
  //   }
  // }
}

.loading-icon {
  animation: spin 1s linear infinite; /* 动画属性：1秒转一圈，线性，循环无限次 */
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
