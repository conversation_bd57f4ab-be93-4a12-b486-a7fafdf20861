<template>
  <div class="category-select">
    <div class="category-select__inner">
      <el-icon class="search-icon"><Search /></el-icon>
      <input v-model.trim="inputValue" type="text" class="search-input" :placeholder="t('queryCategory')" />
      <el-icon v-if="inputValue" class="search-icon-after" @click="inputValue = ''"><CircleClose /></el-icon>
    </div>
    <div class="select-list" v-if="searchResultList.length">
      <div v-for="item in searchResultList" :key="item.id" @click="handleItemClick(item)" class="select-item">
        {{ item.categoryName }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { CircleClose, Search } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'

// import { queryCategoryList } from '@/apis/mall'
// import { debounce } from '@/utils/utils'

const { t } = useI18n({
  messages: {
    zh: {
      queryCategory: '快速检索品类',
    },
    en: {
      queryCategory: 'Category Search',
    },
  },
})

const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
})
const inputValue = ref('')
const searchResultList = computed(() => {
  const val = inputValue.value
  return val ? props.list.filter((item) => item.categoryName.includes(val)) : []
})

// const queryList = async () => {
//   const categoryName = inputValue.value?.trim()
//   if (!categoryName) {
//     categoryList.value = []
//     return
//   }
//   try {
//     const data = await queryCategoryList({ categoryName, pageNum: 1, pageSize: 50 })
//     categoryList.value = data || []
//   } catch (e) {
//     console.log(e)
//   }
// }
//
// const handleInput = debounce(() => {
//   queryList()
// }, 500)

const emit = defineEmits(['handleSelect'])
const handleItemClick = (item) => {
  inputValue.value = ''
  emit('handleSelect', item.id)
}
</script>

<style scoped lang="scss">
.category-select {
  position: relative;

  &__inner {
    display: flex;
    align-items: center;
    border-radius: 14px;
    width: 118px;
    height: 25px;
    padding: 4px 4px 4px 12px;
    background: #faf9f9;
    overflow: hidden;
  }

  .search-icon {
    flex-shrink: 0;
    font-size: 12px;
    color: #666;
    margin-right: 8px;

    &-after {
      font-size: 12px;
      color: #999;
      margin-left: 4px;
      cursor: pointer;
    }
  }

  .search-input {
    width: 100%;
    height: 100%;
    background: inherit;
    outline: none;
    border: none;
    box-shadow: none;
    color: #333;
    font-size: 12px;
  }

  .select-list {
    position: absolute;
    z-index: 9;
    top: 30px;
    left: 0;
    background: #fff;
    border: 4px;
    box-sizing: border-box;
    min-width: 100%;
    max-height: 300px;
    overflow: auto;
    box-shadow: 0 4px 10px 0 rgba(49, 0, 0, 0.1);

    .select-item {
      padding: 4px 12px;
      border-radius: 4px;
      font-size: 14px;
      color: #333;
      transition: all 0.1s;
      cursor: pointer;

      &:hover {
        color: $primary-color;
        background-color: #ffeded;
      }
    }
  }
}

[dir='rtl'] .category-select {
  .search-icon {
    margin-right: 0;
    margin-left: 8px;
  }
}
</style>
