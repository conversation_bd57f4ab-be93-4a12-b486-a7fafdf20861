<template>
  <div class="w-1260">
    <!--    <div class="breadcrumb mb-[12px] flex items-center">-->
    <!--      <span v-for="item in activeCategoryPropList" :key="item.id" class="flex items-center">-->
    <!--        <span-->
    <!--          :class="['category-breadcrumb-btn', 'cursor-pointer', item.id === active ? 'active' : '']"-->
    <!--          @click="handleSelect(item.id)"-->
    <!--        >{{ item.categoryName }}</span>-->
    <!--        <el-icon><ArrowRight /></el-icon>-->
    <!--      </span>-->
    <!--      <span-->
    <!--        v-if="activeCategoryItem"-->
    <!--        :class="['category-breadcrumb-btn', 'cursor-pointer', activeCategoryItem.id === active ? 'active' : '']"-->
    <!--        @click="handleSelect(activeCategoryItem.id)"-->
    <!--      >{{ activeCategoryItem.categoryName }}</span-->
    <!--      >-->
    <!--    </div>-->
    <div class="category-form">
      <div class="category-form-label text-[14px]">{{ $t('mall.category') }}</div>
      <div class="category-form-list">
        <div
          v-for="item in categoryStore.categoryLevel1"
          :key="item.id"
          class="category-form-item"
          @click.stop="handleSelect(item.id)"
          @mouseenter.stop="(e) => handleMouseenter(e, item)"
          @mouseleave="hoverActiveCategory = {}"
          :data-id="item.id"
        >
          <div
            :class="[
              'category-form-item__inner',
              active === item.id || activeLevel1.id === item.id ? 'active' : '',
              hoverActiveCategory.id === item.id ? 'hover-text' : '',
            ]"
          >
            {{ item.categoryName }}
          </div>

          <div
            v-if="hoverActiveCategory && hoverActiveCategory.id === item.id"
            :class="['category-tree flex flex-wrap w-[100%]', activePosition.x > 600 ? 'position-left' : '']"
            @click.stop
          >
            <div class="tree-inner flex flex-wrap w-[100%]">
              <div class="level2-item mb-[4px]">
                <span
                  :class="[
                    'mb-[4px] cursor-pointer level2-item-title mb-[8px] active-text',
                    active === hoverActiveCategory.id || activeLevel1.id === hoverActiveCategory.id ? 'active' : '',
                  ]"
                  @click.stop="handleSelect(hoverActiveCategory.id)"
                >
                  {{ hoverActiveCategory.categoryName }}
                </span>

                <div class="flex flex-wrap pt-[8px] level3-item">
                  <div v-for="level2 in hoverActiveCategory.subList" :key="level2.id">
                    <div
                      :class="['shrink-0 mr-[10px] mb-[8px] cursor-pointer color-[#999] active-text level3-item-text', active === level2.id ? 'active' : '']"
                      @click.stop="handleSelect(level2.id)"
                    >
                      {{ level2.categoryName }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!--        <div class="category-form-item">-->
        <!--          <categorySelect :list="allList" @handleSelect="handleSelect" />-->
        <!--        </div>-->
      </div>
    </div>
  </div>
</template>

<script setup>
// import categorySelect from './category-select.vue'
import { useCategoryFilterStore } from '@/pc/stores'

function getTargetItem(list, targetId, deep = false) {
  // 如果不进行深层查找，直接在当前层级中查找目标元素
  if (!deep) {
    return list.find((item) => item.id === targetId) || null
  }

  // 深度查找模式：递归在子项中查找目标元素
  for (const item of list) {
    if (item.id === targetId) {
      return item // 找到目标元素直接返回
    }
    if (item.subList?.length) {
      // 在子级递归查找
      const found = getTargetItem(item.subList, targetId, deep)
      if (found) {
        return found // 找到目标元素后立即返回
      }
    }
  }

  // 未找到目标元素，返回 null
  return null
}

const categoryStore = useCategoryFilterStore()
const active = ref(null)
const activeCategoryItem = computed(() => {
  const value = active.value
  if (!value) return null
  return getTargetItem(categoryStore.categoryLevel1, value, true)
})

const activeCategoryPropList = computed(() => {
  const { categoryParentId } = activeCategoryItem.value || {}
  if (categoryParentId) {
    return [categoryParentId].map((id, i) => {
      if (i) return getTargetItem(categoryStore.categoryLevel2, id)
      return getTargetItem(categoryStore.categoryLevel1, id)
    })
  }
  return []
})
const activeLevel1 = computed(() => activeCategoryPropList.value?.[0] || {})
// const activeLevel2 = computed(() => activeCategoryPropList.value?.[1] || {})
// const allList = computed(() => categoryStore.categoryLevel1.concat(categoryStore.categoryLevel2.concat(categoryStore.categoryLevel3)))

const hoverActiveCategory = ref({})
const activePosition = reactive({
  x: 0,
  y: 0,
})
const handleMouseenter = (e, item) => {
  const { offsetLeft, dataset } = e.target
  if (!dataset?.id) return
  activePosition.x = offsetLeft
  if (item?.subList?.length) {
    hoverActiveCategory.value = item
  }
}

onMounted(() => {
  if (!categoryStore.categoryLevel2.length) {
    categoryStore.getCategoryList()
  }
})

const emit = defineEmits(['on-search'])
let autoSearchParams = null

const handleSelect = (id) => {
  // if (active.value === id) {
  //   active.value = null
  //   emit('on-search', {})
  //   hoverActiveCategory.value = {}
  //   return
  // }
  autoSearchParams = null
  active.value = id
  hoverActiveCategory.value = {}
  // if (activeCategoryItem.value?.subList?.length) {
  //   emit('on-search', { parentCategoryId: id })
  //   return
  // }
  emit('on-search', { categoryId: id }) // 新版商品中心统一用categoryId
}

const handleAutoSearch = (val, categoryName) => {
  if (!categoryStore.categoryLevel1.length) {
    autoSearchParams = {
      id: val,
      categoryName,
    }
    return
  }

  autoSearch(val, categoryName)
}

const autoSearch = (val, categoryName) => {
  if (val) {
    handleSelect(val)
    return
  }

  if (categoryName) {
    const item = categoryStore.categoryLevel1.find((item) => item.categoryName === categoryName)
    if (item) {
      handleSelect(item.id)
      return
    }
  }

  handleSelect(null)
}

watchEffect(() => {
  if (categoryStore.categoryLevel1.length && autoSearchParams) {
    autoSearch(autoSearchParams.id, autoSearchParams.categoryName)
    autoSearchParams = null
  }
})

const clear = () => {
  active.value = null
}

defineExpose({
  handleAutoSearch,
  clear,
})
</script>

<style scoped lang="scss">
.category-form {
  display: flex;
  padding: 20px 20px 8px;
  box-sizing: border-box;
  margin: 0 auto 24px;
  background: #fff;
  font-family: PingFang SC;
  font-size: 14px;
  line-height: 1.5;

  &-label {
    flex-shrink: 0;
    margin-right: 26px;
    line-height: 25px;
    color: #999;
  }

  &-list {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    white-space: nowrap;
  }

  &-item {
    position: relative;
    margin: 0 6px 12px;

    .category-tree {
      position: absolute;
      top: 100%;
      left: 0;
      max-width: 600px;
      min-width: 40px;
      width: max-content;
      box-sizing: border-box;
      z-index: 3;
      padding-top: 8px;

      .tree-inner {
        box-sizing: border-box;
        padding: 8px 4px 4px 20px;
        background: #fff;
        border: 1px solid $primary-color;
        border-radius: 8px;
        color: #333;
        font-size: 14px;
      }

      &.position-left {
        left: auto;
        right: 0;
      }

      .level2-item {
        width: 100%;

        &-title {
          &.active {
            font-weight: bold;
          }
        }
      }

      .active-text {
        &:hover {
          color: $primary-color;
          text-decoration: underline;
        }

        &.active {
          color: $primary-color;
        }
      }
    }

    &__inner {
      line-height: 25px;
      padding: 0 12px;
      background: #faf9f9;
      border-radius: 14px;
      color: #666;
      cursor: pointer;

      &:hover,
      &.hover-text {
        color: $primary-color;
      }

      &.active {
        background: linear-gradient(270deg, #d8131a 0%, #e96f18 100%);
        color: #fff;
      }
    }
  }
}

.breadcrumb {
  font-size: 16px;
  color: $regular-text;

  &-link {
    color: $regular-text;
    font-weight: normal;
  }

  .category-breadcrumb-btn {
    &.active,
    &:hover {
      color: $primary-color;
    }
  }
}

.cascade-wrap {
  width: 138px;
  height: 25px;
  border-radius: 14px;
  background: #faf9f9;
  padding-left: 12px;
  box-sizing: border-box;

  .cascade-icon {
    color: #666;
    font-size: 14px;
  }

  :deep(.el-cascader) {
    border-color: transparent;
  }

  :deep(.el-input__wrapper) {
    background: transparent;
    -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
    box-shadow: none !important;
  }

  :deep(.el-input__suffix .icon-arrow-down) {
    display: none;
  }
}

[dir='rtl'] .category-form {
  &-label {
    margin-left: 26px;
    margin-right: 0;
  }

  .category-form-item .category-tree {
    .tree-inner {
      padding-left: 4px;
      padding-right: 20px;
    }

    .level3-item-text {
      margin-right: 0;
      margin-left: 10px;
    }

    .level2-item-title {
      padding-right: 0;
    }

    &.position-left {
      .tree-inner {
        .level3-item {
          justify-content: flex-start;
        }
      }
    }
  }
}
</style>
