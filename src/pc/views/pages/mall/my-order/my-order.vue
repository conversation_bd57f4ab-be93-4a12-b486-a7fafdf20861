<!-- 我的订单页面 -->
<template>
  <div class="my-order">
    <div class="my-order-content">
      <div class="batch-operate-bar">
        <div class="flex items-center gap-2">
          <div class="o-button">
            <label class="flex gap-2"><input type="checkbox" />全选</label>
          </div>
          <div class="o-button-group">
            <div class="o-button">合并付款</div>
            <div class="o-button">批量导出</div>
          </div>
          <label class="flex gap-1">
            <input type="checkbox" />
            <span>隐藏交易关闭的订单</span>
          </label>
        </div>
        <div class="flex items-center gap-2">
          <div class="o-button">上一页</div>
          <div>1 / 1</div>
          <div class="o-button">下一页</div>
        </div>
      </div>

      <div class="order-table mt">
        <div class="table-header">
          <div class="grow pl-4xl text-left">货品</div>
          <div class="w-100px text-center">发布价（元）</div>
          <div class="w-60px text-center">数量</div>
          <div class="w-108px text-center">总金额（元）</div>
          <div class="w-108px text-center">订单状态</div>
          <div class="w-108px text-center">交易操作</div>
          <div class="w-108px text-center">其他</div>
        </div>
        <div class="table-content">
          <div class="per-order" v-for="i in 3" :key="i">
            <div class="order-origin">
              <input type="checkbox" />
              <div class="order-no">订单号：3987428871301474516</div>
              <div class="order-date">2024-07-30 09:01:22</div>
              <div class="order-shop">江西半球家用品实业有限公司</div>
              <div class="order-del">删除订单</div>
            </div>
            <div class="order-goods">
              <div class="goods-item" v-for="i in 3" :key="i">
                <div class="grow flex items-center gap-4">
                  <img class="goods-img" src="https://cbu01.alicdn.com/img/ibank/O1CN01E93aVk1Bs2xn3ONNA_!!0-0-cib.80x80.jpg?_=2020" />
                  <div class="flex flex-col gap-2">
                    <div class="goods-name">床下柜收纳箱整理箱扁平超薄超扁牛津布床底鞋子神器布艺宿舍学生</div>
                    <div class="goods-sku">颜色:白色; 尺寸:大号(93x55x19 cm)</div>
                    <div class="flex gap-2">
                      <div class="goods-serve-tag">7天无理由退货</div>
                      <div class="goods-serve-tag">48小时发货</div>
                      <div class="goods-serve-tag">极速退款</div>
                    </div>
                  </div>
                </div>
                <div class="w-100px flex justify-center items-center">42.80</div>
                <div class="w-60px flex justify-center items-center">6</div>
                <div class="w-108px flex flex-col justify-center items-center gap-2">
                  <div class="goods-original-price">256.80</div>
                  <div class="goods-discounted-price">249.80</div>
                </div>
                <div class="w-108px flex flex-col justify-center items-center gap-2">
                  <div>交易成功</div>
                  <div>查看物流</div>
                </div>
                <div class="w-108px flex flex-col justify-center items-center gap-2">
                  <div class="o-button">评价</div>
                </div>
                <div class="w-108px flex flex-col justify-center items-center gap-2">
                  <div>再次购买</div>
                  <div>申请售后</div>
                  <div>申请理赔</div>
                  <div>投诉</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="order-pagination">
        <label class="flex gap-2"><input type="checkbox" />全选</label>
        <div class="flex gap-2 items-center">
          <div class="flex items-center">
            <div>每页显示：</div>
            <input class="o-input" type="number" />
          </div>
          <div class="flex gap-2 items-center">
            <div class="o-button">上一页</div>
            <div class="pageno">1</div>
            <div class="o-button">下一页</div>
          </div>
          <div>1/1</div>
          <div>1条</div>
          <div class="flex gap-1 items-center">到<input class="o-input" type="number" />页</div>
          <div class="o-button">确定</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineComponent({ name: 'my-order' })
</script>

<style lang="scss" scoped>
.my-order {
  background-color: #f2f2f2;
  font-size: 12px;
}
.my-order-content {
  width: $main-width;
  margin: 0 auto;
  padding: 20px 0;
}

.batch-operate-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-table {
  .table-header {
    display: flex;
    padding: 14px 10px;
    border-bottom: 1px solid #ededed;
    background-color: #f8f8f8;
    color: $color-999999;
  }
  .table-content {
    padding: 10px;
    background-color: #fff;
  }
  .order-origin {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 8px;
    background-color: #f5f5f5;

    .order-date {
      color: #888;
    }
    .order-shop {
      flex-grow: 1;
    }
  }
  .goods-item {
    display: flex;
    padding: 12px 0;

    .goods-img {
      width: 76px;
      height: 76px;
      border: 1px solid #e5e5e5;
    }
    .goods-name {
      &:hover {
        color: $primary-color;
      }
    }
    .goods-sku {
      color: #888;
    }
    .goods-serve-tag {
      border: 1px solid $primary-color;
      color: $primary-color;
      padding: 2px;
    }
    .goods-original-price {
      text-decoration: line-through;
      color: #666;
    }
    .goods-discounted-price {
      font-size: 14px;
      font-weight: bolder;
    }
  }
}

.order-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
  padding: 14px;
  background-color: #fff;
}

.o-button-group {
  display: flex;

  .o-button {
    border-right: none;
    border-radius: 0;
    &:first-child {
      border-top-left-radius: 2px;
      border-bottom-left-radius: 2px;
    }
    &:last-child {
      border-right: 1px solid #ddd;
      border-top-right-radius: 2px;
      border-bottom-right-radius: 2px;
    }
  }
}
.o-button {
  padding: 0 16px;
  border: 1px solid #ddd;
  border-radius: 2px;
  background-color: #fff;
  color: #666;
  font-size: 12px;
  line-height: 26px;
  cursor: pointer;

  &:hover {
    background-color: $primary-color;
    color: #fff;
  }
}
.o-input {
  width: 40px;
  padding: 0 4px;
  line-height: 26px;
  border: 1px solid #ddd;
  text-align: center;
}
</style>
