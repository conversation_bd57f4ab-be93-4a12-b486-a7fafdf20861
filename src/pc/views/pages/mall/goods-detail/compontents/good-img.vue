<template>
  <div class="pic-item-container">
    <!-- 左侧缩略图区域 -->
    <div class="thumbnail-container">
      <!-- 向上滚动按钮 -->
      <div class="scroll-btn scroll-up" :class="{ disabled: scrollTop <= 0 }" @click="scrollUp">
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
          <circle cx="16" cy="16" r="16" fill="rgba(255, 255, 255, 0.7)"></circle>
          <path d="M11.25 18.75L16 14L20.75 18.75" stroke="#1A1A1A" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
        </svg>
      </div>

      <!-- 缩略图列表 -->
      <div class="thumbnail-list" ref="thumbnailList">
        <div class="thumbnail-wrapper" :style="{ transform: `translateY(-${scrollTop}px)` }">
          <div
            v-for="(image, index) in imgList"
            :key="index"
            class="thumbnail-item"
            :class="{ active: currentIndex === index }"
            @click="selectImage(index)"
            @mouseenter="selectImage(index)"
          >
            <img :src="image.imgUrl" :alt="`缩略图 ${index + 1}`" />
            <!-- 播放图标 -->
            <div v-if="image.type === 'video'" class="play-icon">
              <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                <circle cx="9" cy="9" r="9" fill="rgba(0, 0, 0, 0.5)"></circle>
                <path d="M7 6L12 9L7 12V6Z" fill="white"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- 向下滚动按钮 -->
      <div class="scroll-btn scroll-down" :class="{ disabled: scrollTop >= maxScrollTop }" @click="scrollDown">
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
          <circle cx="16" cy="16" r="16" fill="rgba(255, 255, 255, 0.7)"></circle>
          <path d="M11.25 13.25L16 18L20.75 13.25" stroke="#1A1A1A" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
        </svg>
      </div>
    </div>

    <!-- 右侧主图区域 -->
    <div class="main-image-container">
      <div class="main-image-wrapper">
        <!-- 上一张按钮 -->
        <div class="action-btn prev" :class="{ disabled: currentIndex <= 0 }" @click="prevImage">
          <icon type="icon-fh" size="16"></icon>
        </div>
        <!-- 主图片/视频 -->
        <div class="main-image">
          <img v-if="currentImage?.type === 'image'" :src="currentImage.url" @click="visible = true" :alt="`主图 ${currentIndex + 1}`" />
          <!-- <video v-else-if="currentImage.type === 'video'" :src="currentImage.url" controls :poster="currentImage.imgUrl"></video> -->

          <video
            v-else-if="currentImage?.type === 'video'"
            ref="videoPlayer"
            :src="videoUrlItem.url"
            controls
            controlslist="nodownload"
            webkit-playsinline="webkit-playsinline"
            playsinline="playsinline"
            :autoplay="false"
            :poster="videoUrlItem.imgUrl"
            :muted="false"
            style="width: 100%; height: 100%"
            preload="auto"
            @play="onPlay"
            @pause="onPause"
            @ended="onVideoEnded"
          ></video>
        </div>

        <!-- 播放按钮 (视频) -->
        <div v-if="currentImage?.type === 'video' && !playing" class="main-play-btn" @click="toggleClick">
          <svg width="64" height="64" viewBox="0 0 64 64" fill="none">
            <circle cx="32" cy="32" r="32" fill="rgba(0, 0, 0, 0.5)"></circle>
            <path d="M24 20L44 32L24 44V20Z" fill="white"></path>
          </svg>
        </div>

        <!-- 语言切换 -->
        <div class="language-switcher" v-if="videoArr?.length > 1">
          <div
            v-for="(item, i) in videoArr"
            :key="i"
            @click="changeVideo(i)"
            class="lang-item"
            :class="currentVideoIndex === i ? 'active' : ''"
            v-mode="item.name"
          >
            {{ item.name }}
          </div>
        </div>
        <!-- 下一张按钮 -->
        <div class="action-btn next" :class="{ disabled: currentIndex >= imgList.length - 1 }" @click="nextImage">
          <icon type="icon-xiala1" size="16"></icon>
        </div>

        <!-- 收藏按钮 -->
        <!-- <div class="action-btn flex-col favorite text-12px" @click="toggleFavorite">
          <icon type="icon-shoucang-moren" v-if="!isFavorite" size="22"></icon>
          <icon type="icon-shoucang-dianji1" v-else size="22"></icon>
          <div class="px-5px" v-if="!isFavorite">收藏</div>
          <div class="px-5px" v-else>已收藏</div>
        </div> -->
      </div>
    </div>
    <ElImageViewer
      v-if="visible"
      :urlList="urlList"
      :z-index="99"
      :initialIndex="initialIndex"
      hideOnClickModal
      teleported
      @close="visible = false"
    ></ElImageViewer>
  </div>
</template>

<script setup>
import { ElImageViewer } from 'element-plus'
import { computed, nextTick, onMounted, ref } from 'vue'
// 清理事件监听器
import { onUnmounted } from 'vue'
import { GOODS_LANG_VIDEO } from '@/constants/goods'
import { useStorageLocale } from '@/i18n'
import { formatVideoPosterUrl } from '@/utils/utils'

const { storageLocale } = useStorageLocale()

const props = defineProps({
  detailInfo: {
    type: Object,
    default: () => ({}),
  },
})
// 响应式数据
const currentIndex = ref(0)
const scrollTop = ref(0)
const thumbnailList = ref(null)
const visible = ref(false)

const videoArr = computed(() => {
  return (
    props.detailInfo?.videoList?.map((item) => {
      return {
        ...item,
        name: GOODS_LANG_VIDEO[item.id]?.name,
        url: item.videoUrl,
        type: 'video',
        imgUrl: formatVideoPosterUrl(item.videoUrl),
      }
    }) || []
  )
})

const imgList = computed(() => {
  const goodsPictureUrls = props.detailInfo?.spuImages?.split(',') || []
  console.log('imglist---', goodsPictureUrls)
  const videoList = videoArr.value.length ? [videoArr.value[0]] : []
  return videoList.concat(
    goodsPictureUrls.map((item) => {
      return {
        url: item,
        imgUrl: item,
        type: 'image',
      }
    }),
  )
})
const getInitIndex = () => {
  const initIndex = videoArr.value.findIndex((item) => item.languageType === storageLocale.value)
  return initIndex > 0 ? initIndex : 0
}
const currentVideoIndex = ref(getInitIndex())
const videoUrlItem = computed(() => videoArr.value[currentVideoIndex.value] || {})
const changeVideo = (i) => {
  if (currentVideoIndex.value === i) return // 如果已经是当前视频，不做切换
  currentVideoIndex.value = i
  nextTick(() => {
    // console.log(videoPlayer.value, 9999)
    // videoPlayer.value.seek(0)
    playVideo()
  })
}

// 计算属性
const currentImage = computed(() => {
  if (currentIndex.value == -1 && activeUrlItem.value) {
    return activeUrlItem.value
  } else {
    return imgList.value[currentIndex.value]
  }
})
const maxScrollTop = computed(() => {
  const itemHeight = 80 // 60px + 20px gap
  const visibleItems = 5
  const totalItems = imgList.value.length
  return Math.max(0, (totalItems - visibleItems) * itemHeight)
})

// 方法
const selectImage = (index) => {
  currentIndex.value = index
}

const nextImage = () => {
  if (currentIndex.value < imgList.value.length - 1) {
    currentIndex.value++
    scrollToCurrentImage()
  }
}

const prevImage = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--
    scrollToCurrentImage()
  }
}

const scrollUp = () => {
  if (scrollTop.value > 0) {
    scrollTop.value = Math.max(0, scrollTop.value - 80)
  }
}

const scrollDown = () => {
  if (scrollTop.value < maxScrollTop.value) {
    scrollTop.value = Math.min(maxScrollTop.value, scrollTop.value + 80)
  }
}

const scrollToCurrentImage = () => {
  const itemHeight = 80
  const visibleItems = 5
  const currentPosition = currentIndex.value * itemHeight

  if (currentPosition < scrollTop.value) {
    scrollTop.value = currentPosition
  } else if (currentPosition >= scrollTop.value + (visibleItems - 1) * itemHeight) {
    scrollTop.value = currentPosition - (visibleItems - 1) * itemHeight
  }
}

const urlList = computed(() => {
  const arr = props.detailInfo?.spuImages?.split(',') || []
  if (currentImage.value && !arr.includes(currentImage.value.imgUrl)) return [currentImage.value.imgUrl]
  return arr
})

const activePreview = computed(() => {
  return currentImage.value?.imgUrl || urlList.value[0]
})
const initialIndex = computed(() => urlList.value.indexOf(activePreview.value))

const videoPlayer = ref(null)
const playing = ref(false)
const playVideo = () => {
  if (videoPlayer.value) {
    videoPlayer.value.play()
  }
}
const onPlay = () => {
  playing.value = true
}
const onPause = () => {
  playing.value = false
}
const onVideoEnded = () => {
  playing.value = false
}
const pauseVideo = () => {
  if (videoPlayer.value) {
    videoPlayer.value.pause()
  }
}
const toggleClick = () => {
  if (playing.value) {
    pauseVideo()
  } else {
    playVideo()
  }
}

// 键盘事件
const handleKeydown = (event) => {
  switch (event.key) {
    case 'ArrowLeft':
      prevImage()
      break
    case 'ArrowRight':
      nextImage()
      break
    case 'ArrowUp':
      scrollUp()
      break
    case 'ArrowDown':
      scrollDown()
      break
  }
}
const activeUrlItem = ref(null)
const handleMouseenter = (item) => {
  currentIndex.value = -1
  activeUrlItem.value = item
}

const handleCustomImg = (item) => {
  handleMouseenter(item)
}
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
watch(
  props.detailInfo,
  (val) => {
    if (val) {
      // currentImage.value = imgList.value[0]
    }
  },
  {
    immediate: true,
  },
)
defineExpose({
  handleCustomImg,
})
</script>

<style lang="scss" scoped>
/* 主容器样式 */
.pic-item-container {
  display: flex;
  gap: 20px;
  //   width: 702px;
  //   height: 464px;
  padding: 20px 0;
}

/* 左侧缩略图容器 */
.thumbnail-container {
  display: flex;
  flex-direction: column;
  width: 64px;
  gap: 20px;
  position: relative;
}

/* 滚动按钮 */
.scroll-btn {
  width: 32px;
  height: 32px;
  cursor: pointer;
  background: rgba(255, 255, 255, 1);
  align-self: center;
  position: absolute;
  border-radius: 50%;
  overflow: hidden;
  z-index: 2;
  /* top: 0; */
}
.scroll-down {
  bottom: 0;
}
.scroll-up {
  top: 0;
}
.scroll-btn:hover {
  opacity: 0.7;
}

.scroll-btn.disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

/* 缩略图列表 */
.thumbnail-list {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.thumbnail-wrapper {
  transition: transform 0.3s ease;
}

/* 缩略图项 */
.thumbnail-item {
  width: 64px;
  height: 64px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  position: relative;
  margin-bottom: 20px;
  border: 2px solid transparent;
  transition: border-color 0.3s ease;
}

.thumbnail-item:hover {
  //border-color: #d8131a;
  border: 2px solid #d8131a;
}

.thumbnail-item.active {
  //   border-color: #d8131a;
  //   border-style: dashed;
  border: 2px solid #d8131a;
}

.thumbnail-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 播放图标 */
.play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

/* 右侧主图容器 */
.main-image-container {
  flex: 1;
  width: 720px;
  //height: 720px;
  background-color: #f7f8fc;
  border-radius: 8px;
  position: relative;
}

.main-image-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 主图片 */
.main-image {
  width: 500px;
  height: 500px;
  margin: 0 auto;
  position: relative;
}

.main-image img,
.main-image video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 主播放按钮 */
.main-play-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.main-play-btn:hover {
  opacity: 0.8;
}

/* 语言切换器 */
.language-switcher {
  position: absolute;
  bottom: 58px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  display: flex;
  align-items: center;
  border-radius: 30px;
  height: 28px;
  overflow: hidden;
}

.lang-item {
  min-width: 60px;
  white-space: nowrap;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  background: rgba(238, 238, 238, 0.7);
  font-size: 12px;
  cursor: pointer;
  padding: 0 8px;

  &.active {
    color: $primary-color;
    font-weight: 600;
  }
}

.lang-item.active {
  color: $primary-color;
  font-weight: 600;
}

// .lang-item:first-child {
//   border-radius: 200px 0 0 200px;
// }

// .lang-item:last-child {
//   border-radius: 0 200px 200px 0;
// }

/* 右侧操作按钮 */
.action-buttons {
  position: absolute;
  right: 16px;
  top: 16px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.action-btn {
  width: 32px;
  height: 32px;
  cursor: pointer;
  transition: transform 0.3s ease;
  background: rgba(255, 255, 255, 1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-btn.disabled {
  opacity: 0.3;
  cursor: not-allowed;
}
.prev {
  position: absolute;
  left: 16px;
  top: 50%;
  margin-top: -16px;
}
.next {
  position: absolute;
  right: 16px;
  top: 50%;
  margin-top: -16px;
}
.favorite {
  position: absolute;
  right: 16px;
  top: 16px;
  height: auto;
  width: auto;
  min-width: 56px;
  user-select: none;
  padding: 12px 5px;
  flex-wrap: wrap;
  word-break: keep-all;
  border-radius: 200px;
  background: rgba(255, 255, 255, 0.7);
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.1);
}

.action-btn:hover {
  transform: scale(1.1);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  /* 主图片 */
  .main-image {
    width: 424px;
    height: 424px;
  }
  /* 右侧主图容器 */
  .main-image-container {
    width: 600px;
    // height: 600px;
  }
  /* 缩略图项 */
  .thumbnail-item {
    width: 54px;
    height: 54px;
  }
  /* 左侧缩略图容器 */
  .thumbnail-container {
    width: 56px;
  }
}
</style>
