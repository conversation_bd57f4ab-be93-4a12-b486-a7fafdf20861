<!-- 热销商品 -->
<style lang="scss" scoped>
.hot-title {
  font-size: 20px;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.goods-list-wrapper {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;

  background: #ffffff;

  & > div {
    // max-width: 237px;
  }
}

:deep() {
  .el-carousel__arrow {
    background-color: rgba(255, 255, 255, 0.7);
    i {
      font-size: 16px;
      color: #262626;
    }
  }
  .rec-offer {
    border: 1px solid #edeef1;
    // border-radius: 0;
    position: relative;
    padding: 0;
    .goods-img-wrapper {
      border: none;
    }
    .offer-title {
      height: 35px;
    }
    .goods-content-wrapper {
      padding: 8px;
    }
    &::before {
      content: '';
      width: 100%;
      height: 1px;
      background: #edeef1;
      position: absolute;
      left: 0;
      top: 240px;
    }
  }
}
.el-carousel__item {
  //  line-height: 322px;
}
</style>
<template>
  <div class="mb-[32px]" v-if="goodsList.length">
    <div class="hot-title">店铺热销</div>
    <el-carousel height="345px" :arrow="goodsList?.length > 3 ? 'always' : 'never'">
      <el-carousel-item v-for="i in Math.ceil(goodsList.length / 3)" :key="i">
        <div class="goods-list-wrapper" v-if="i >= Math.ceil(goodsList.length / 3)">
          <div v-for="(item, ii) in goodsList.slice((i - 1) * 3, goodsList.length)" :key="ii">
            <GoodsDetailNewCard :goodsInfo="item" :showSaleNum="true"></GoodsDetailNewCard>
          </div>
        </div>
        <div class="goods-list-wrapper" v-else>
          <div v-for="(item, ii) in goodsList.slice((i - 1) * 3, (i - 1) * 3 + 3)" :key="ii">
            <GoodsDetailNewCard :goodsInfo="item" :showSaleNum="true"></GoodsDetailNewCard>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>

    <!-- <div class="goods-list-wrapper">
      <template v-for="(item, index) in goodsList" :key="index">
        <GoodsDetailNewCard :goodsInfo="item"></GoodsDetailNewCard>
      </template>
    </div> -->
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import GoodsDetailNewCard from '@/pc/components/goods-detail-card/goods-detail-card-new.vue'
import { getRecommendList } from '@/apis/mall'

const props = defineProps({
  detailInfo: {
    type: Object,
    default: () => ({}),
  },
})

const goodsList = ref([])

const getGoodsList = async () => {
  if (!props.detailInfo) {
    return
  }
  let paramsData = {
    pageSize: 10,
    pageNum: 1,
    userId: props.detailInfo.userId,
    spuId: props.detailInfo.id,
  }

  const { rowList } = await getRecommendList(paramsData)
  if (rowList && rowList.length > 8) {
    goodsList.value = rowList.slice(0, 8)
  } else {
    goodsList.value = rowList
  }
}

onMounted(() => {
  getGoodsList()
})
</script>
