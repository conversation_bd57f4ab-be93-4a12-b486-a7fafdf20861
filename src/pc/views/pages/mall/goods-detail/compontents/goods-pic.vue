<template>
  <div v-if="detailInfo">
    <div class="main-video-wrap bg-black" v-show="showVideo" v-if="videoArr.length">
      <video
        ref="videoPlayer"
        :src="videoUrlItem.url"
        controls
        controlslist="nodownload"
        webkit-playsinline="webkit-playsinline"
        playsinline="playsinline"
        :autoplay="false"
        :poster="videoUrlItem.imgUrl"
        :muted="false"
        style="width: 100%; height: 100%"
        preload="auto"
        @play="onPlay"
        @pause="onPause"
        @ended="onVideoEnded"
      ></video>
      <div class="lang-tab-wrap" v-if="videoArr.length > 1">
        <div
          v-for="(item, i) in videoArr"
          :key="i"
          @click="changeVideo(i)"
          class="lang-tab-item"
          :class="currentVideoIndex === i ? 'active' : ''"
          v-mode="item.name"
        >
          {{ item.name }}
        </div>
      </div>
      <div v-if="showVideo && !playing" class="video-play-btn flex justify-center items-center" @click="toggleClick">
        <el-icon><CaretRight /></el-icon>
      </div>
    </div>
    <div class="main-pic-wrap" v-if="!showVideo">
      <zoom-pic :thumbnail-img="activePreview" :large-img="activePreview" :box-size="500" />
      <div class="preview-btn" @click="visible = true">
        <el-icon><Search /></el-icon>
      </div>
    </div>

    <div class="spec-wrap">
      <button ref="prevButton" class="spec-button">
        <el-icon><CaretLeft /></el-icon>
      </button>
      <swiper
        direction="horizontal"
        class="spec-list"
        :cssMode="true"
        :navigation="{ prevEl: prevButton, nextEl: nextButton }"
        :mousewheel="true"
        :modules="modules"
      >
        <swiper-slide v-for="(itemList, i) in imgList" :key="i">
          <div class="spec-item">
            <div v-for="(item, i1) in itemList" :key="i1" class="relative">
              <img
                @mouseenter="handleMouseenter(item)"
                :class="['spec-item-pic', activeUrlItem && JSON.stringify(activeUrlItem) === JSON.stringify(item) ? 'active' : '', 'block']"
                :src="item.imgUrl"
                alt=""
              />
              <div v-if="item.type === 'video'" class="video-play-btn small flex justify-center items-center">
                <el-icon><CaretRight /></el-icon>
              </div>
            </div>
          </div>
        </swiper-slide>
      </swiper>
      <button ref="nextButton" class="spec-button">
        <el-icon><CaretRight /></el-icon>
      </button>
    </div>
  </div>
  <ElImageViewer v-if="visible" :urlList="urlList" :z-index="99" :initialIndex="initialIndex" hideOnClickModal teleported @close="visible = false" />
</template>
<script setup>
import { CaretLeft, CaretRight, Search } from '@element-plus/icons-vue'
import { ElImageViewer } from 'element-plus'
// Import Swiper styles
import 'swiper/css'
import 'swiper/css/navigation'
// import required modules
import { Mousewheel, Navigation } from 'swiper/modules'
import { Swiper, SwiperSlide } from 'swiper/vue'
import ZoomPic from '@/pc/views/pages/mall/goods-detail/compontents/zoom-pic.vue'
import { GOODS_LANG_VIDEO } from '@/constants/goods'
import { useStorageLocale } from '@/i18n'
import { formatVideoPosterUrl } from '@/utils/utils'

const props = defineProps({
  detailInfo: {
    type: Object,
    default: () => ({}),
  },
})

const { storageLocale } = useStorageLocale()
const videoArr = computed(() => {
  return (
    props.detailInfo?.videoList?.map((item) => {
      return {
        ...item,
        name: GOODS_LANG_VIDEO[item.id]?.name,
        url: item.videoUrl,
        type: 'video',
        imgUrl: formatVideoPosterUrl(item.videoUrl),
      }
    }) || []
  )
})

const modules = [Navigation, Mousewheel]
const prevButton = ref(null)
const nextButton = ref(null)
const visible = ref(false)
const splitLen = 5
const mediaList = computed(() => {
  const goodsPictureUrls = props.detailInfo?.spuImages?.split(',') || []
  const videoList = videoArr.value.length ? [videoArr.value[0]] : []
  return videoList.concat(
    goodsPictureUrls.map((item) => {
      return {
        url: item,
        imgUrl: item,
        type: 'image',
      }
    }),
  )
})
const imgList = computed(() => {
  const newArrLen = Math.ceil(mediaList.value.length / splitLen)
  const newArr = []
  for (let i = 0; i < newArrLen; i++) {
    newArr[i] = mediaList.value.slice(i * splitLen, (i + 1) * splitLen)
  }
  return newArr
})
const urlList = computed(() => {
  const arr = props.detailInfo?.spuImages?.split(',') || []
  if (activeUrlItem.value && !arr.includes(activeUrlItem.value.imgUrl)) return [activeUrlItem.value.imgUrl]
  return arr
})
const activeUrlItem = ref(null)
const activePreview = computed(() => {
  return activeUrlItem.value?.imgUrl || urlList.value[0]
})
const initialIndex = computed(() => urlList.value.indexOf(activePreview.value))

const showVideo = computed(() => activeUrlItem.value?.type === 'video')
const videoPlayer = ref(null)
const playing = ref(false)
const playVideo = () => {
  if (videoPlayer.value) {
    videoPlayer.value.play()
  }
}
const onPlay = () => {
  playing.value = true
}
const onPause = () => {
  playing.value = false
}
const onVideoEnded = () => {
  playing.value = false
}
const pauseVideo = () => {
  if (videoPlayer.value) {
    videoPlayer.value.pause()
  }
}
const toggleClick = () => {
  if (playing.value) {
    pauseVideo()
  } else {
    playVideo()
  }
}
const getInitIndex = () => {
  const initIndex = videoArr.value.findIndex((item) => item.languageType === storageLocale.value)
  return initIndex > 0 ? initIndex : 0
}
const currentVideoIndex = ref(getInitIndex())
const videoUrlItem = computed(() => videoArr.value[currentVideoIndex.value] || {})
const changeVideo = (i) => {
  if (currentVideoIndex.value === i) return // 如果已经是当前视频，不做切换
  currentVideoIndex.value = i
  nextTick(() => {
    // console.log(videoPlayer.value, 9999)
    // videoPlayer.value.seek(0)
    playVideo()
  })
}

const handleMouseenter = (item) => {
  activeUrlItem.value = item
}

const handleCustomImg = (item) => {
  handleMouseenter(item)
}

onBeforeUnmount(() => {
  pauseVideo()
})

watch(
  props.detailInfo,
  (val) => {
    if (val) {
      activeUrlItem.value = mediaList.value[0]
    }
  },
  {
    immediate: true,
  },
)

// 停止播放
watch(showVideo, (val) => {
  if (!val) {
    pauseVideo()
  }
})

defineExpose({
  handleCustomImg,
})
</script>

<style lang="scss" scoped>
.main-pic-wrap,
.main-video-wrap {
  width: 100%;
  height: 500px;
  margin-bottom: 16px;
  position: relative;

  .preview-btn {
    width: 30px;
    line-height: 30px;
    text-align: center;
    position: absolute;
    bottom: 0;
    right: 0;
    z-index: 1;
    font-size: 16px;
    color: #fff;
    background: #8a8a8a;
    cursor: pointer;
  }
}

.spec-wrap {
  display: flex;
  justify-content: space-between;
  width: 500px;
  height: 78px;
  overflow: hidden;
  background: #fff;

  .spec-button {
    display: block;
    flex-shrink: 0;
    width: 19px;
    height: 100%;
    background: #f9f9f9;
    cursor: pointer;
  }

  :deep(.swiper-button-disabled) {
    cursor: not-allowed;
  }
}

.spec-list {
  width: 450px;
  height: 100%;

  .spec-item {
    display: flex;
    height: 100%;

    &-pic {
      width: 78px;
      height: 78px;
      margin: 0 6px;
      border: 1px solid transparent;
      box-sizing: border-box;
      object-fit: cover;

      &.active {
        border-color: #d8131a;
      }
    }
  }
}

.video-play-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 9;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background: rgba(0, 0, 0, 0.2);
  border: thin solid #fff;
  border-radius: 50%;
  text-align: center;
  color: #fff;
  font-size: 40px;
  cursor: pointer;

  &.small {
    width: 32px;
    height: 32px;
    font-size: 20px;
  }

  &:hover {
    background: rgba(0, 0, 0, 0.5);
  }
}

.lang-tab-wrap {
  position: absolute;
  bottom: 58px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  display: flex;
  align-items: center;
  border-radius: 30px;
  height: 28px;
  overflow: hidden;

  .lang-tab-item {
    min-width: 60px;
    white-space: nowrap;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    background: rgba(238, 238, 238, 0.7);
    font-size: 12px;
    cursor: pointer;
    padding: 0 8px;

    &.active {
      color: $primary-color;
      font-weight: 600;
    }
  }
}

[dir='rtl'] {
  .main-pic-wrap,
  .main-video-wrap {
    .preview-btn {
      right: auto;
      left: 0;
    }
  }

  .spec-wrap {
    .spec-button {
      transform: rotateZ(-180deg);
    }
  }
}
</style>
