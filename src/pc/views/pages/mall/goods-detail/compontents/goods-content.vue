<template>
  <div class="content-wrap" v-if="detailInfo">
    <div class="w-full h-[45px] mb-[8px]" v-if="detailInfo.showType">
      <img-loader :src="bannarImgNow" class="w-[100%] h-[100%] object-cover" :loading-img="`${bannarImgNow}?x-oss-process=image/resize,h_68`"></img-loader>
    </div>
    <!-- <div class="content-title">{{ detailInfo.spuName }}</div> -->
    <div class="content-detail">
      <div class="flex items-center content-detail-item">
        <div class="content-row-label min-w-[70px] mr-[8px] color-[#505259]">最低起订量</div>
        <div class="content-row-value color-[#505259]">{{ detailInfo?.saleMinNum ? detailInfo?.saleMinNum + '件' : '-' }}</div>
      </div>
      <div class="content-price">
        <c-symbol></c-symbol>
        <span v-if="!detailInfo.maxPrice || detailInfo.minPrice == detailInfo.maxPrice">{{ formatPrice(detailInfo.minPrice) }}</span>
        <span v-else> {{ formatPrice(detailInfo.minPrice) }} ~ <c-symbol></c-symbol>{{ formatPrice(detailInfo.maxPrice) }}</span>
      </div>
      <div class="flex items-center content-detail-item">
        <div class="content-row-label min-w-[70px] mr-[8px] color-[#505259]">发货地</div>
        <div class="content-row-value color-[#505259]">{{ deliveryFromText }}</div>
      </div>
      <!-- <div v-for="item in detailOptions" :key="item.key" class="flex items-center content-detail-item">
        <div class="content-row-label min-w-[56px] mr-[24px] color-[#999]">{{ t(item.labelName) }}</div>
        <div class="content-row-value">
          <span v-if="item.prefix">{{ item.prefix }}</span>
          <span v-if="item.parentKey === 'deliveryFrom'">{{ deliveryFromText || '-' }}</span>
          <span v-else-if="item.parentKey">{{ detailInfo[item.parentKey] ? detailInfo[item.parentKey][item.key] : '-' }}</span>
          <span v-else>{{ detailInfo[item.key] || '-' }}</span>
        </div>
      </div> -->
    </div>
    <div class="good-sub-title">商品规格</div>
    <div class="content-attr flex overflow-hidden content w-full">
      <slot></slot>
    </div>

    <Customer ref="customerRef"></Customer>
  </div>
</template>

<script setup>
//import { useI18n } from 'vue-i18n'
import Customer from '@/pc/components/customer/customer.vue'
import { EXPORT_TO_DOMESTIC_IMG } from '@/constants/index'
import { useStorageLocale } from '@/i18n/translatePlugin'

// import user from '@/pc/utils/user'

const { storageLocale } = useStorageLocale()
// const { t } = useI18n({
//   messages: {
//     zh: {
//       category: '商品类目',
//       goodsNo: '商品编码',
//       brand: '品牌',
//       deliveryFrom: '发货城市',
//       price: '价格',
//       stock: '库存',
//       addToFavorites: '加入收藏',
//       sampleRequest: '联系我们',
//       favorited: '已收藏',
//       saleMinNum: '最低起订量',
//       attr: '产品规格',
//     },
//     en: {
//       category: 'Category',
//       goodsNo: 'Goods No',
//       brand: 'Brand',
//       deliveryFrom: 'Delivery From',
//       price: 'Price',
//       stock: 'Stock',
//       addToFavorites: 'Add to Favorites',
//       sampleRequest: 'Contact Us',
//       favorited: 'Favorited',
//       saleMinNum: 'Minimum Order Quantity',
//       attr: 'attributes',
//     },
//   },
// })

const props = defineProps({
  detailInfo: {
    type: Object,
    default: () => ({}),
  },
})
// const detailOptions = [
//   // {
//   //   labelName: 'category',
//   //   parentKey: 'category',
//   //   key: 'categoryName',
//   // },
//   {
//     labelName: 'brand',
//     key: 'brandName',
//   },
//   {
//     labelName: 'deliveryFrom',
//     parentKey: 'deliveryFrom',
//     key: 'areaName',
//   },
//   {
//     labelName: 'saleMinNum',
//     key: 'saleMinNum',
//     prefix: '',
//   },
// ]

const bannarImgNow = computed(() => {
  const img = EXPORT_TO_DOMESTIC_IMG[storageLocale.value] || EXPORT_TO_DOMESTIC_IMG.zh
  return img
})

const customerRef = ref(null)

const deliveryFromText = computed(() => {
  return [props.detailInfo?.deliveryProvinceName, props.detailInfo?.deliveryCityName, props.detailInfo?.deliveryAreaName].join('')
})
const formatPrice = (price) => {
  return Number(price) ? Number(price).toFixed(2) : '0.00'
}
</script>

<style lang="scss" scoped>
.good-sub-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}
.content-price {
  color: $primary-color;
  font-size: 20px;
  font-weight: 500;
  padding-bottom: 8px;
}
.el-button + .el-button {
  margin-left: 8px;
}
.content {
  max-height: calc(100vh - 240px);
  &-detail {
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
  }
  &-wrap {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 100%;
    color: #333;
  }

  &-title {
    font-family: PingFang SC;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 18px;
    white-space: pre-wrap;
    word-break: break-all;
  }

  &-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    margin-right: 16px;

    &-label {
      //color: #999;
    }
  }

  &-footer {
    // flex-shrink: 0;
    // height: 40px;
    display: flex;
    justify-content: space-between;
  }

  &-btn {
    height: 40px;
    width: 100%;

    border-radius: 4px;
    background: #fff;
    border: 1px solid $primary-color;
    color: $primary-color;
    font-family: PingFang SC;
    font-size: 16px;
    font-weight: 600;

    &.is-disabled {
      opacity: 0.6;
    }

    &:hover {
      border: 1px solid $primary-color;
      color: $primary-color;
    }

    &.primary {
      color: #fff;
      background: $primary-color;
    }
  }
}

[dir='rtl'] .content {
  &-detail {
    .content-row-label {
      margin-right: 0;
      margin-left: 24px;
    }
  }

  &-row {
    margin-right: 0;
    margin-left: 16px;
  }

  .large-label {
    margin-right: 0;
    margin-left: 24px;
    padding-right: 12px;
  }

  &-btn {
    &.primary {
      margin-right: 12px;
    }
  }
}

.content-detail-item {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}
</style>
