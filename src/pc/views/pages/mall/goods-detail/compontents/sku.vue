<template>
  <div class="h-full w-full flex flex-col overflow-hidden relative">
    <div class="overflow-y-auto scrollbar" :class="detailInfo.showType ? 'good-sub-cont-live' : 'good-sub-cont'">
      <div class="h-full w-full flex flex-col">
        <div v-for="(attrItem, index) in attrList" :key="attrItem.id" class="attr-item" :class="`attr-item_${index}_${attrList.length}`">
          <div
            class="shrink-0 text-ellipsis overflow-hidden whitespace-nowrap label-wrap"
            :class="index === attrList.length - 1 ? 'leading-[22px]' : 'leading-[22px]'"
            :title="attrItem.spec"
          >
            {{ attrItem.spec }}
          </div>
          <div class="h-full w-full value-wrap" v-if="index === attrList.length - 1">
            <table class="w-full">
              <tr v-for="spec in attrItem.items" :key="spec.id" class="tr-wrap">
                <td class="px-[8px] py-[8px] max-w-[120px] overflow-hidden">
                  <div class="flex items-center">
                    <img :src="spec.image" class="w-[28px] h-[28px] mr-[4px] cursor-pointer" v-if="spec.image" @click="handleTableImgClick(spec)" />
                    <div class="text-ellipsis overflow-hidden whitespace-nowrap font-600" :title="spec.value">
                      <span v-if="spec.value.indexOf(',') === 0">{{ spec.value.replace(',', '') }}</span>
                      <span v-else>{{ spec.value }}</span>
                    </div>
                  </div>
                </td>
                <td class="px-[8px] py-[8px] font-600">
                  <span v-if="goodsSkuData[preActiveId + spec.id]?.price || goodsSkuData[preActiveId + spec.id]?.price === 0">
                    <c-symbol></c-symbol>

                    {{ formatPrice(goodsSkuData[preActiveId + spec.id]?.price) }}</span
                  >
                </td>
                <td class="px-[8px] py-[8px] text-gray">
                  <span v-if="goodsSkuData[preActiveId + spec.id]?.stock || goodsSkuData[preActiveId + spec.id]?.stock === 0"
                    >{{ goodsSkuData[preActiveId + spec.id]?.stock }}件可售</span
                  >
                </td>
                <td class="pl-[8px] py-[8px] text-right">
                  <el-input-number
                    v-model="goodsNumData[preActiveId + spec.id]"
                    :max="formatNum(goodsSkuData[preActiveId + spec.id]?.stock, 999999)"
                    :min="0"
                    :precision="0"
                    :controls-position="$storageLocale === 'ar' ? 'right' : ''"
                    @change="handleCalculate"
                  ></el-input-number>
                </td>
              </tr>
            </table>
          </div>
          <div class="flex flex-wrap value-wrap" v-else>
            <div class="mr-[12px] mb-[12px]" v-for="spec in attrItem.items" :key="spec.id">
              <el-badge :value="'×' + getBadgeNum(index, spec)" v-if="!index" :hidden="!getBadgeNum(index, spec)" color="#D8131A">
                <el-button class="attr-item-btn" :type="preActiveIdList[index] === spec.id ? 'primary' : ''" @click="handleItemClick(index, spec)">
                  <img :src="spec.image" class="w-[36px] h-[36px] mr-[8px]" v-if="spec.image" />
                  <span class="inline-block max-w-[240px] overflow-hidden text-ellipsis whitespace-nowrap" :title="spec.value">{{ spec.value }}</span>
                </el-button>
                <template #content="{ value }">
                  <span>{{ value }}</span>
                </template>
              </el-badge>
              <el-button class="attr-item-btn" :type="preActiveIdList[index] === spec.id ? 'primary' : ''" @click="handleItemClick(index, spec)" v-else>
                <span class="inline-block max-w-[240px] overflow-hidden text-ellipsis whitespace-nowrap" :title="spec.value">{{ spec.value }}</span>
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <div class="list-wrap" v-if="goodsSkuDataViewList.length && showListFlag">
        <table class="w-full border-table">
          <tr v-for="item in goodsSkuDataViewList" :key="item.id">
            <td v-for="(attrItem, i) in attrList" :key="attrItem.id" class="px-[8px] py-[8px]">{{ attrMapList[i][item.customSkuId?.split(',')?.[i]] }}</td>
            <td class="px-[8px] py-[8px]">{{ item.goodsNum }}件</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="w-full shrink-0 z-2">
      <div class="flex items-center h-full color-[#666] my-[16px] h-[25px] justify-end">
        <div class="sku-total">
          <em>共计</em>
          <em class="text-[18px] text-[#D8131A] mx-1">{{ totalNum }}</em>
          <em class="mr-[6px]">件</em>
          <em class="mx-1">商品金额</em>

          <c-symbol class="mx-[1px] text-[#D8131A]"></c-symbol>
          <em class="text-[18px] text-[#D8131A]" v-loading="calculateLoading">{{ formatPrice(totalPrice) }}</em>
        </div>
        <!-- <div class="text-[#333] cursor-pointer" @click="showListFlag = !showListFlag" v-if="goodsSkuDataViewList.length">
          <span class="mr-[2px]">已选清单</span><icon type="icon-shangjiantou" v-if="showListFlag"></icon><icon type="icon-xiajiantou" v-else></icon>
        </div> -->
      </div>
    </div>
    <div class="content-footer">
      <el-button class="content-btn primary" type="primary" :loading="addCartLoading" @click="handleAddCartClick"
        ><span v-mode="PRODUCT_DETAIL_ADDCART_MAP">{{ PRODUCT_DETAIL_ADDCART_MAP['zh'] }}</span></el-button
      >
      <el-button class="content-btn" :loading="buyLoading" @click="handleBuyClick">
        <span v-mode="PRODUCT_DETAIL_BUY_MAP">{{ PRODUCT_DETAIL_BUY_MAP['zh'] }}</span>
      </el-button>
      <el-button class="content-btn" v-mode="PRODUCT_DETAIL_SERVICE_MAP" @click="handleReceive">
        <span v-mode="PRODUCT_DETAIL_SERVICE_MAP">{{ PRODUCT_DETAIL_SERVICE_MAP['zh'] }}</span>
      </el-button>
      <div class="flex flex-col items-center text-12px text-center mt-3px cursor-pointer pl-5px letter-spacing-1px" @click="toggleFavorite">
        <icon :type="isFavorite ? 'icon-shoucang-dianji1' : 'icon-shoucang-moren'" size="18"></icon>
        <div class="max-w-200px min-w-40px">{{ isFavorite ? '已收藏' : '收藏' }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import BigNumber from 'bignumber.js'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { PRODUCT_DETAIL_ADDCART_MAP, PRODUCT_DETAIL_BUY_MAP, PRODUCT_DETAIL_SERVICE_MAP } from '@/constants/special-field'
import { useUserStore } from '@/pc/stores'
import { useCartStore } from '@/pc/stores'
import { getUserCollect, goodsCollect, goodsCollectCancel } from '@/apis/goods'
import { addCartOrder, calculate, getCheck } from '@/apis/order'
import { useEvent } from '@/event'
import { OPEN_NEW_LOGIN } from '@/event/modules/site'
import { CUSTOMER_SERVICE_LANGUAGE_MAP } from '@/i18n/contants'
import { useStorageLocale } from '@/i18n/translatePlugin'
import { sleepFn } from '@/common/js/util'
import user from '@/pc/utils/user'

const router = useRouter()
const cartStore = useCartStore()
const totalPrice = ref(0)
const chooseCurrency = ref(user.getPayPrice())
const { storageLocale } = useStorageLocale()
const props = defineProps({
  detailInfo: {
    type: Object,
    default: null,
  },
})
const Emits = defineEmits(['goodsPicChange', 'updateDetail'])

// 选择的sku规格 用于选中交互
const preActiveIdList = ref([])
// 所有sku规格
const attrList = ref([])
// sku 数据对象id为键
const skuMap = reactive({})
// 进货数量对象
const goodsNumData = reactive({})
const showListFlag = ref(false)

// 用户交互的sku数据对象
const goodsSkuData = computed(() => {
  return Object.keys(goodsNumData).reduce((prev, cur) => {
    let id = cur
      .split(',')
      .sort((a, b) => {
        const num1 = new BigNumber(a)
        const num2 = new BigNumber(b)
        return num1.minus(num2)
      })
      .join(',')
    if (attrList.value.length === 1) id = id.replace(',', '')
    if (skuMap[id]) prev[cur] = { ...skuMap[id], goodsNum: goodsNumData[cur], customSkuId: cur }
    return prev
  }, {})
})
// 用于展示的进货单列表
const goodsSkuDataViewList = computed(() => Object.values(goodsSkuData.value).filter((item) => item.goodsNum))
// 总件数
const totalNum = computed(() => {
  return Object.values(goodsSkuData.value).reduce((prev, cur) => {
    const num1 = new BigNumber(prev)
    const num2 = new BigNumber(cur.goodsNum)
    prev = num1.plus(num2)
    return prev
  }, 0)
})
// 总价
// const totalPrice = computed(() => {
//   return Object.values(goodsSkuData.value).reduce((prev, cur) => {
//     const goodsNum = new BigNumber(cur.goodsNum)
//     const price = new BigNumber(cur.price)
//     // 执行乘法操作
//     const totalPrice = goodsNum.multipliedBy(price)
//     const num1 = new BigNumber(totalPrice)
//     const num2 = new BigNumber(prev)
//     prev = num1.plus(num2)
//     return Math.round(prev * 100) / 100
//   }, 0)
// })
// 选中的skuIds，最后一个除外
const preActiveId = computed(() => {
  const arr = preActiveIdList.value
  const id = arr.filter((item) => item).join(',')
  return id ? `${id},` : ''
})
// 用于已选清单回显
const attrMapList = computed(() => {
  return attrList.value.map((item) => {
    return item.items.reduce((prev, cur) => {
      prev[cur.id] = cur.value
      return prev
    }, {})
  })
})
const formatPrice = (price) => {
  return Number(price).toFixed(2) || '0.00'
}
const formatNum = (val, nextVal) => {
  return val || val === 0 ? val : nextVal
}

// 同步goodsNumData里的数据，用于不同skuItem数量的双向绑定
watchEffect(() => {
  const prevId = preActiveId.value
  const lastAttrData = attrList.value[attrList.value.length - 1]
  lastAttrData?.items?.forEach((item) => {
    const activeId = `${prevId}${item.id}`
    goodsNumData[activeId] = goodsNumData[activeId] || 0
  })
})

// 同步图片
watch(
  () => preActiveIdList.value[0],
  (imgActiveId) => {
    if (imgActiveId) {
      const { items } = attrList.value[0] || {}
      const { image } = items.find((item) => item.id === imgActiveId) || {}
      Emits('goodsPicChange', image || '')
    } else {
      Emits('goodsPicChange', '')
    }
  },
)

const handleTableImgClick = (item) => {
  const { image } = item || {}
  if (!image) return
  Emits('goodsPicChange', image || '')
}

// 获取数据
const init = () => {
  const { spec, skuList } = props.detailInfo
  attrList.value = JSON.parse(spec)
  // 选中的id
  preActiveIdList.value = attrList.value
    .filter((item, i) => i < attrList.value.length - 1)
    .map((item) => {
      return item?.items?.[0]?.id || ''
    })
  // sku map
  skuList.forEach((item) => {
    skuMap[item.specValue] = item
  })
  totalPrice.value = 0
}

watchEffect(() => {
  if (props.detailInfo?.skuList?.length && !attrList.value.length) {
    init()
  }
})

// 选中sku逻辑
const handleItemClick = (index, spec) => {
  if (preActiveIdList.value[index] === spec.id) {
    preActiveIdList.value[index] = null
    return
  }

  preActiveIdList.value[index] = spec.id
}

// 渲染数量
const getBadgeNum = (i, item) => {
  return Object.keys(goodsSkuData.value).reduce((prev, cur) => {
    const { goodsNum } = goodsSkuData.value[cur]
    const targetId = cur.split(',')[i]
    if (`${targetId}` === `${item.id}`) {
      prev += goodsNum
    }
    return prev
  }, 0)
}

const handleReceive = () => {
  const lang = CUSTOMER_SERVICE_LANGUAGE_MAP[storageLocale.value]
  window.open(
    `https://support.chinamarket.cn/index/index/home?visiter_id=&visiter_name=&avatar=&business_id=1&groupid=0&special=0&width=100&lang=${lang}`,
    '_blank',
    'height=800,width=950,top=50,left=200,status=yes,toolbar=no,menubar=no,resizable=yes,scrollbars=no,location=no,titlebar=no',
  )
}
const event = useEvent()
const userStore = useUserStore()
const contactCustomer = () => {
  if (!userStore.isLogined) {
    event.emit(OPEN_NEW_LOGIN, {
      routerDisabled: true,
    })
    return false
  }
  return true
}

const showEmptyErrorFlagMap = reactive({})
const handleShowErrorFlag = async (key) => {
  showEmptyErrorFlagMap[key] = true
  await sleepFn(3000)
  Object.keys(showEmptyErrorFlagMap).forEach((item) => {
    showEmptyErrorFlagMap[item] = false
  })
}
const updateSku = () => {
  Emits('updateDetail', () => {
    // 如果当前币种不是用户设置的币种，则更新币种
    chooseCurrency.value = userStore?.userInfo?.priceType || 1
    init()
  })
}
const calculateData = ref({})

const validFormMessage = (baseValidFlag = false) => {
  if (calculateLoading.value) {
    return '价格计算中，请稍后再试'
  }

  const goodsList = calculateData.value?.goodsList || []
  if (!goodsList.length) {
    handleShowErrorFlag('empty')
    return '数量小于起批量'
  }
  // 基础校验返回空
  if (baseValidFlag) return ''
  const { errorMessage, errorCode } = goodsList.find((item) => item.errorMessage) || {}

  // 更新商品
  if (errorCode === '-4') {
    updateSku()
  }

  if (errorCode === '-100') {
    handleShowErrorFlag('min')
    return '数量小于起批量'
  }

  if (errorMessage) {
    return errorMessage
  }

  if (calculateData.value?.orderPrice <= 0) {
    return '商品价格异常'
  }
}
//加入进货单
const addCartLoading = ref(false)
const handleAddCartClick = async () => {
  try {
    if (!contactCustomer()) {
      return
    }
    if (userStore.userInfo?.userType !== 1) {
      event.emit(OPEN_NEW_LOGIN, {
        routerDisabled: true,
      })
      return
    }
    addCartLoading.value = true
    let params = {
      skuList: Object.values(goodsSkuData.value)
        .filter((item) => item.goodsNum)
        .map((item) => {
          return {
            skuId: item.id,
            spuId: item.spuId,
            count: item.goodsNum,
          }
        }),
    }
    //校验商品是否下架
    const res = await getCheck(params)
    console.log(res)

    let message = validFormMessage(true)
    if (message) {
      ElMessage.error(message)
      addCartLoading.value = false
      return
    }
    const calculateFormData = getCalculateFormData()
    await addCartOrder({
      skuList: calculateFormData.sellerList?.[0]?.goodsList || [],
    })

    ElMessage.success('加入进货单成功')
    updateSku()
    cartStore.getCartCount()

    clearForm()
  } catch (e) {
    console.log(e)
  } finally {
    addCartLoading.value = false
  }
}
const clearForm = () => {
  Object.keys(goodsNumData).forEach((key) => delete goodsNumData[key])
  preActiveIdList.value = []

  calculateData.value = {}
  init()
}
// 计算价格参数
const getCalculateFormData = () => {
  const sellerItem = {
    id: props?.detailInfo?.userId,
    goodsList: Object.values(goodsSkuData.value)
      .filter((item) => item.goodsNum)
      .map((item) => {
        return {
          skuId: item.id,
          spuId: item.spuId,
          count: item.goodsNum,
        }
      }),
    deliveryPrice: null,
    servicePrice: null,
    remarks: null,
  }

  const formData = {
    sellerList: [sellerItem],
    priceType: chooseCurrency.value, // 当前切换币种
  }
  return formData
}
const calculateLoading = ref(false)

// 计价
const handleCalculate = async () => {
  try {
    calculateLoading.value = true

    const formData = getCalculateFormData()
    const data = await calculate(formData)
    console.log('calculate---', data)
    if (data?.errorGoodsList?.length) {
      ElMessage.error(data?.errorGoodsList[0]?.errorMessage)
    }
    const formDataStr = JSON.stringify(formData)

    totalPrice.value = data.finalPrice
    if (formDataStr === JSON.stringify(getCalculateFormData())) {
      calculateData.value = {
        ...data,
        goodsList: data?.sellerList?.[0]?.goodsList,
      }
    }
  } catch (e) {
    console.log(e)
  } finally {
    calculateLoading.value = false
  }
}

const buyLoading = ref(false)
const handleBuyClick = async () => {
  if (!contactCustomer()) {
    return
  }
  if (userStore.userInfo?.userType !== 1) {
    event.emit(OPEN_NEW_LOGIN, {
      routerDisabled: true,
    })
    return
  }
  buyLoading.value = true
  await sleepFn(300)
  buyLoading.value = false
  try {
    let params = {
      skuList: Object.values(goodsSkuData.value)
        .filter((item) => item.goodsNum)
        .map((item) => {
          return {
            skuId: item.id,
            spuId: item.spuId,
            count: item.goodsNum,
          }
        }),
    }
    //校验商品是否下架
    const res = await getCheck(params)
    console.log(res)
    let message = validFormMessage()
    if (message) {
      ElMessage.error(message)
      return
    }
    const calculateFormData = getCalculateFormData()
    //cartStore.setOrderSubmitParams(calculateFormData)
    localStorage.setItem('CART_CONFIRM_DATA', JSON.stringify(calculateFormData))

    router.push({
      path: '/cart/confirm',
      query: { from: 'sku' },
    })
  } catch (e) {
    console.log(e)
  }
}
const isLogined = computed(() => userStore.isLogined)
let collectId = null
const getCollect = async () => {
  const { id } = props.detailInfo || {}
  collectId = id
  if (isLogined.value) {
    const data = await getUserCollect({ spuId: id })
    isFavorite.value = !!data
  }
}
watch(isLogined, async () => {
  if (isLogined.value) {
    const data = await getUserCollect({ spuId: collectId })
    isFavorite.value = !!data
  }
})

const isFavorite = ref(false)
const toggleFavorite = () => {
  if (!contactCustomer()) {
    return
  }
  if (userStore.userInfo?.userType !== 1) {
    event.emit(OPEN_NEW_LOGIN, {
      routerDisabled: true,
    })
    return
  }
  if (isFavorite.value) {
    handleCancelColection()
  } else {
    handleCollect()
  }
  // isFavorite.value = !isFavorite.value
}
const collectLoading = ref(false)
// 取消收藏
const handleCancelColection = async () => {
  try {
    if (!contactCustomer()) {
      return
    }
    if (userStore.userInfo?.userType !== 1) {
      event.emit(OPEN_NEW_LOGIN, {
        routerDisabled: true,
      })
      return
    }
    collectLoading.value = true
    const { id } = props.detailInfo || {}
    await goodsCollectCancel({ spuId: id })
    isFavorite.value = false
  } catch (e) {
    console.log(e)
  } finally {
    collectLoading.value = false
  }
}
//收藏

const handleCollect = async () => {
  try {
    if (!contactCustomer()) {
      return
    }
    if (userStore.userInfo?.userType !== 1) {
      event.emit(OPEN_NEW_LOGIN, {
        routerDisabled: true,
      })
      return
    }
    collectLoading.value = true
    const { id } = props.detailInfo || {}
    await goodsCollect({ spuId: id })
    isFavorite.value = true
  } catch (e) {
    console.log(e)
  } finally {
    collectLoading.value = false
  }
}
onMounted(() => {
  getCollect()
})
</script>

<style lang="scss" scoped>
.good-sub-cont {
  max-height: calc(100vh - 432px);
  min-height: 100px;
}
.good-sub-cont-live {
  max-height: calc(100vh - 489px);
  min-height: 100px;
}
.content {
  &-footer {
    // flex-shrink: 0;
    // height: 40px;
    display: flex;
    justify-content: space-between;
  }

  &-btn {
    height: 40px;
    width: 33.33%;
    overflow: hidden;
    border-radius: 4px;
    background: #fff;
    border: 1px solid $primary-color;
    color: $primary-color;
    font-family: PingFang SC;
    font-size: 16px;
    font-weight: 600;

    &.is-disabled {
      opacity: 0.6;
    }

    &:hover {
      border: 1px solid $primary-color;
      color: $primary-color;
    }

    &.primary {
      color: #fff;
      background: $primary-color;
    }
  }
}
em {
  font-style: normal;
}

.border-table {
  border-top: 1px solid #e5e5e5;
  border-left: 1px solid #e5e5e5;
  border-spacing: 0;

  td {
    border-bottom: 1px solid #e5e5e5;
    border-right: 1px solid #e5e5e5;
  }
}

.tr-wrap {
  td {
    border-bottom: 1px dashed #f0f0f0;

    &:first-child {
      padding-left: 0;
    }
  }

  &:last-child {
    td {
      border: none;
    }
  }
}

.attr-item {
  margin-bottom: 4px;
  flex-shrink: 0;

  .value-wrap {
    // max-height: 320px;
    //padding-top: 8px;
  }
  .label-wrap {
    color: #1a1a1a;
    font-size: 16px;
    font-weight: 500;
  }
  &:first-child {
    .value-wrap {
      padding-top: 10px;
    }
    .label-wrap {
      padding-top: 16px;
    }

    .value-wrap {
      width: 100%;
    }
  }

  &:last-child {
    margin-bottom: 0;
    flex-shrink: 1;
    //overflow: hidden;
  }
}

.list-wrap {
  position: absolute;
  bottom: 48px;
  left: 0;
  z-index: 2;
  width: 100%;
  background: #fff;
  max-height: calc(100% - 58px);
}

.attr-item-btn {
  min-width: 40px;
  height: 40px;
  border-radius: 2px;
  padding: 0 8px;
  color: #333;
  background: #fff;
  overflow: hidden;

  img {
    margin-left: -7px;
  }

  &.no-padding {
    padding-left: 0;
  }

  &:hover {
    background: #fff;
    color: #333;
    border-color: $primary-color;
  }
}

.sku-total {
  em {
    font-style: normal;
  }
}

:deep() {
  .el-input-number {
    // width: 100%;
  }
  .el-input-number__decrease,
  .el-input-number__increase {
    background: #fff;
  }

  .el-input-number__decrease.is-disabled,
  .el-input-number__increase.is-disabled {
    background: #fff;
    opacity: 0.4;
  }
}

.el-badge {
  :deep(.el-badge__content.is-fixed) {
    transform: translateY(-50%) translateX(65%);
  }
}

[dir='rtl'] {
  .attr-item-btn {
    img {
      margin-left: 8px;
      margin-right: -7px;
    }
  }

  .el-input-number.is-controls-right {
    :deep(.el-input__wrapper) {
      padding-right: 15px;
      padding-left: 42px;
    }

    :deep(.el-input-number__decrease),
    :deep(.el-input-number__increase) {
      left: 2px;
      right: auto;
      border-right: 1px solid #dcdfe6;
      border-left: none;
      border-radius: 0;
    }
  }

  .el-badge {
    :deep(.el-badge__content.is-fixed) {
      right: auto;
      left: 15px;
      background: red;
      transform: translateY(-50%) translateX(-100%);
    }
  }

  .mr-\[4px\],
  .mr-1 {
    margin-right: 0;
    margin-left: 4px;
  }
}
</style>
