<template>
  <div class="goods-detail" v-if="detailInfo">
    <div class="flex good-detail-title">
      <div class="goods-detail-title mr-[32px]" :class="activeIndex == 1 ? 'active' : ''" @click="changeTab(1)">属性</div>
      <div class="goods-detail-title mr-[32px]" :class="activeIndex == 2 ? 'active' : ''" @click="changeTab(2)">供应商</div>
      <div class="goods-detail-title" :class="activeIndex == 3 ? 'active' : ''" @click="changeTab(3)">描述</div>
    </div>
    <div class="py-5">
      <div class="goods-detail__title good-content-1">商品属性</div>
      <div class="flex flex-wrap attr-border mb-[20px]">
        <div v-for="item in attrListView" :key="item.id" class="w-1/2 flex shrink-0 attr-row" :class="item.isborder ? 'row-border' : ''">
          <div class="text-[#1A1A1A] shrink-0 p-[16px] attr-font attr-label attr-center" :title="item.attr">
            <div class="text-ellipsis-2">{{ item.attr }}</div>
          </div>
          <div class="text-[#888B94] p-[16px] attr-center" :title="renderAttrValue(item.items)">
            <div class="text-ellipsis-2">{{ renderAttrValue(item.items) }}</div>
          </div>
        </div>
      </div>

      <div class="flex justify-center">
        <div
          class="text-[#999] cursor-pointer"
          @click="limitLen = limitLen === defaultLimitLen ? 9999 : defaultLimitLen"
          v-if="attrList.length > defaultLimitLen"
        >
          <span class="mr-[2px] more-btn">更多</span><icon type="icon-shangjiantou" v-if="limitLen > defaultLimitLen"></icon
          ><icon type="icon-xiajiantou" v-else></icon>
        </div>
      </div>

      <div class="goods-detail__title good-content-2">供应商</div>
      <store-info :shopInfo="shopInfo" isJump @handleTitleClick="handleShop"></store-info>
      <div class="goods-detail__title good-content-3">商品描述</div>
      <div v-html="detailInfo.spuInfo" @click="handleDetailClick" class="custom-html"></div>
    </div>

    <ElImageViewer
      v-if="showViewer"
      :urlList="imgDetailList"
      :z-index="99"
      :initialIndex="initialIndex"
      hideOnClickModal
      teleported
      @close="showViewer = false"
    ></ElImageViewer>
  </div>
</template>
<script setup>
import { ElImageViewer } from 'element-plus'
import StoreInfo from '../../components/store-info/index.vue'

const router = useRouter()
const props = defineProps({
  detailInfo: {
    type: Object,
    default: () => ({}),
  },
})

const activeIndex = ref(1)
const changeTab = (index) => {
  if (activeIndex.value == index) {
    return
  } else {
    activeIndex.value = index
    scrollFun()
  }
}
const shopInfo = computed(() => {
  return props.detailInfo?.userInfoApiVo || {}
})
const handleShop = () => {
  const { href } = router.resolve({
    name: 'shop',
    params: {
      id: props.detailInfo.userId,
    },
  })
  window.open(href, '_blank')
}

const defaultLimitLen = 9
const limitLen = ref(defaultLimitLen)

const attrList = computed(() => {
  let attr = []
  if (props.detailInfo?.attr || props.detailInfo?.brandName) {
    try {
      attr = JSON.parse(props.detailInfo?.attr)
      if (props.detailInfo?.brandName) {
        attr.unshift({ attr: '品牌', items: [{ value: props.detailInfo?.brandName }] })
      }
    } catch (e) {
      console.log(e)
    }
  }
  return attr
})
const attrListView = computed(() => {
  let arr = attrList.value.filter((item, i) => i < limitLen.value)
  let arr2 = []
  if (arr?.length) {
    arr.map((item, i) => {
      if (arr.length % 2) {
        if (i == arr.length - 1) {
          arr2.push({
            ...item,
            isborder: false,
          })
        } else {
          arr2.push({
            ...item,
            isborder: true,
          })
        }
      } else {
        if (i == arr.length - 1 || i == arr.length - 2) {
          arr2.push({
            ...item,
            isborder: false,
          })
        } else {
          arr2.push({
            ...item,
            isborder: true,
          })
        }
      }
    })
  }
  return arr2
})

const showViewer = ref(false)
const initialIndex = ref(0)
const imgDetailList = computed(() => {
  const html = props.detailInfo?.spuInfo || ''
  return [...html.matchAll(/<img[^>]+src="([^"]+)"/g)].map((match) => match[1])
})
const handleDetailClick = (e) => {
  const { currentSrc, tagName } = e.target || {}
  if (currentSrc && tagName === 'IMG') {
    initialIndex.value = imgDetailList.value.findIndex((item) => encodeURI(item) === currentSrc)
    showViewer.value = true
  }
}
const renderAttrValue = (list) => {
  if (Array.isArray(list)) {
    return list.map((item) => item.value).join(',')
  }
  return ''
}
const scrollFun = () => {
  //let scrollTop = document.documentElement.scrollTop || document.body.scrollTop

  const inner = document.getElementsByClassName(`good-content-${activeIndex.value}`)
  // const topbannarH = inner[0].offsetTop - 128
  const titleH = document.getElementsByClassName('good-detail-title')

  // 获取元素的计算后的样式inner[0].offsetTop - parseInt(style.paddingTop) - parseInt(inner[0].offsetHeight)
  const style = window.getComputedStyle(inner[0])
  window.scrollTo({
    top: inner[0].offsetTop - parseInt(style.paddingTop) - parseInt(titleH[0].offsetHeight) - 128,
    behavior: 'smooth',
  })
}
</script>
<style lang="scss" scoped>
.goods-detail {
  width: 100%;
  //overflow: hidden;
  margin: 0 auto;
  background: #fff;
  .good-detail-title {
    position: sticky;
    top: 128px;
    background: #fff;
    z-index: 10;
    border-bottom: 1px solid #f0f0f0;
  }
  .shop-wrap {
    background: #f7f8fc;
  }
  .attr-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .attr-row {
    height: 72px;
    width: 50%;
    // border-bottom: 1px solid #f0f0f0;
  }
  .row-border {
    border-bottom: 1px solid #f0f0f0;
  }
  .attr-label {
    width: 144px;
    font-size: 14px;
    font-weight: 500;
    background: #f7f8fc;
  }
  .attr-font {
    display: flex;
    align-content: center;
  }
  .attr-border {
    border: 1px solid #f0f0f0;
  }
  :deep(img),
  :deep(video) {
    display: block;
    width: 100% !important;
    cursor: pointer;
  }

  &-title {
    color: #505259;
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
    padding: 16px 0;
    border-bottom: 2px solid transparent;
  }
  .active {
    color: $primary-color;
    border-bottom: 2px solid $primary-color;
  }

  &__title {
    display: flex;
    align-items: center;
    font-size: 20px;
    font-weight: 500;
    height: 40px;
    color: #1a1a1a;
    margin-bottom: 16px;

    // &::before {
    //   content: '';
    //   border-left: 4px solid $primary-color;
    //   height: 14px;
    //   display: block;
    //   margin-right: 8px;
    // }
  }
}
</style>
