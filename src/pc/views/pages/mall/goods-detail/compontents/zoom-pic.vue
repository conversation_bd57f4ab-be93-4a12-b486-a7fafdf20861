<template>
  <div class="zoom-container" :style="`width: ${boxSize}px;height: ${boxSize}px;`">
    <!-- 左侧缩略图 -->
    <div class="thumbnail" ref="thumbnail" @mousemove="handleMouseMove" @mouseleave="handleMouseLeave">
      <img :src="thumbnailImg" alt="缩略图" />
      <div class="zoom-lens" :style="lensStyle" v-show="isZoomVisible"></div>
    </div>

    <!-- 右侧放大区域 -->
    <div class="zoom-result" v-show="isZoomVisible">
      <img :src="largeImg" :style="resultStyle" alt="大图" />
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  thumbnailImg: {
    type: String,
    default: '',
  },
  largeImg: {
    type: String,
    default: '',
  },
  boxSize: {
    type: Number,
    default: 500,
  },
})
// 放大区域的显示和隐藏
const isZoomVisible = ref(false)
const thumbnail = ref(null)

// 镜头区域样式
const lensWidth = props.boxSize / 2
const lensHeight = props.boxSize / 2
const lensStyle = ref({
  transform: 'translate(0, 0)',
})

// 放大结果区域样式
const largeImageSize = props.boxSize * 2 // 假设大图的实际尺寸
const resultStyle = ref({
  position: 'absolute',
  width: `${largeImageSize}px`, // 大图实际尺寸
  height: `${largeImageSize}px`,
  transform: 'translate(0, 0)',
})

const handleMouseMove = (event) => {
  const { offsetX, offsetY } = event
  const { offsetWidth, offsetHeight } = thumbnail.value

  // 显示放大镜
  isZoomVisible.value = true

  // 限制放大镜区域不超出缩略图
  const lensX = Math.max(0, Math.min(offsetX - lensWidth / 2, offsetWidth - lensWidth))
  const lensY = Math.max(0, Math.min(offsetY - lensHeight / 2, offsetHeight - lensHeight))

  // 更新镜头位置
  lensStyle.value = {
    transform: `translate(${lensX}px, ${lensY}px)`,
  }

  // 计算放大区域移动比例
  const scaleX = largeImageSize / offsetWidth // 根据大图和缩略图比例
  const scaleY = largeImageSize / offsetHeight

  // 调整大图显示的区域
  resultStyle.value = {
    ...resultStyle.value,
    transform: `translate(${-lensX * scaleX}px, ${-lensY * scaleY}px)`,
  }
}

// 鼠标离开时隐藏放大镜
const handleMouseLeave = () => {
  isZoomVisible.value = false
}
</script>

<style lang="scss" scoped>
.zoom-container {
  position: relative;
  width: 100%;
  height: 100%;
}
.thumbnail {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.zoom-lens {
  position: absolute;
  top: 0;
  left: 0;
  width: 50%;
  height: 50%;
  background: radial-gradient(#4085f5 1px, transparent 0);
  background-size: 4px 4px;
  pointer-events: none;
}
.zoom-result {
  width: 100%; /* 固定盒子大小 */
  height: 100%;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 100%;
  z-index: 9;
}
.zoom-result img {
  display: inline-block;
  top: 0;
  left: 0;
  position: absolute;
  width: 200%; /* 大图实际尺寸 */
  height: 200%;
  z-index: 9;
  object-fit: cover;
}

[dir='rtl'] {
  .zoom-result {
    left: 0;
    right: 100%;
  }
}
</style>
