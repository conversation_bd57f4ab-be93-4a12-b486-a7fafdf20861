<template>
  <div class="h-[128px]"></div>
  <div class="logo-nav">
    <SearchInput></SearchInput>
  </div>
  <div class="w-bg">
    <div class="w-1260 pd-[24px]">
      <!-- <Breadcrumb :breadcrumbList="breadcrumbList" /> -->
      <!-- <store-info :shopInfo="shopInfo" isJump @handleTitleClick="handleShop"></store-info> -->
      <el-skeleton :loading="loading" animated>
        <template #template>
          <!--        <div class="flex mb-[16px] px-[20px] py-[12px] w-[100%] bg-[#fff]">-->
          <!--          <el-skeleton-item variant="image" style="height: 80px; width: 80px"> </el-skeleton-item>-->
          <!--          <div class="w-[40%] flex flex-col justify-between ml-[16px]">-->
          <!--            <el-skeleton-item variant="h1" style="width: 60%"></el-skeleton-item>-->
          <!--            <div>-->
          <!--              <el-skeleton-item variant="text"></el-skeleton-item>-->
          <!--              <br />-->
          <!--              <el-skeleton-item variant="text"></el-skeleton-item>-->
          <!--            </div>-->
          <!--          </div>-->
          <!--        </div>-->
          <div class="goods-wrap">
            <el-skeleton-item variant="image" class="goods-left" style="height: 500px"> </el-skeleton-item>
            <div class="goods-right">
              <el-skeleton-item variant="h1"></el-skeleton-item>
              <div v-for="item in 4" :key="item" class="mb-[20px] mt-[20px]">
                <el-skeleton-item variant="text" style="width: 40%; margin-right: 16px"></el-skeleton-item>
              </div>
              <el-skeleton-item style="height: 200px; margin-bottom: 30px"></el-skeleton-item>
              <el-skeleton-item v-for="item in 2" :key="item" variant="button" style="width: 30%; margin-right: 12px"></el-skeleton-item>
            </div>
          </div>
          <div class="goods-detail">
            <el-skeleton-item v-for="item in 4" :key="item" variant="image" style="height: 200px"></el-skeleton-item>
          </div>
        </template>
        <template #default>
          <div class="goods-wrap">
            <div class="goods-left" v-if="detailInfo && detailInfo.id">
              <div class="good-title">{{ detailInfo?.spuName }}</div>
              <div class="good-sale">
                <saleNum :showSaleNum="true" :goodsInfo="detailInfo"></saleNum>
              </div>
              <div class="store"><topStoreInfo :shopInfo="shopInfo" @handleTitleClick="handleShop"></topStoreInfo></div>
              <goodImg :detailInfo="detailInfo" ref="goodsPic"></goodImg>
              <div class="hot-goods"><hotGoods :detailInfo="detailInfo"></hotGoods></div>

              <goodsDetailSub :detailInfo="detailInfo"></goodsDetailSub>
            </div>
            <div class="w-48px"></div>
            <!-- 订单汇总和结算 -->
            <div class="w-[504px] relative" id="fixedPanelBox">
              <div class="goods-right">
                <goods-content :detailInfo="detailInfo">
                  <template #default>
                    <Sku :detailInfo="detailInfo" @goodsPicChange="goodsPicChange" @updateDetail="getGoodsDetailInfo"></Sku>
                  </template>
                </goods-content>
              </div>
            </div>

            <div class="absolute top-0 left-0 w-full h-full flex items-center justify-center" v-if="!detailInfo || !detailInfo.id">
              <el-empty description="商品下架了～"></el-empty>
            </div>
          </div>

          <div class="h-10"></div>
        </template>
      </el-skeleton>
      <ElImageViewer
        v-if="showViewer"
        :urlList="imgDetailList"
        :z-index="99"
        :initialIndex="initialIndex"
        hideOnClickModal
        teleported
        @close="showViewer = false"
      ></ElImageViewer>
    </div>
  </div>
</template>

<script setup>
import { ElImageViewer } from 'element-plus'
//import Breadcrumb from '@/pc/components/breadcrumb/index.vue'
import saleNum from '@/pc/components/sale-num/sale-num.vue'
import SearchInput from '@/pc/components/search-input/search-input.vue'
//import StoreInfo from '../components/store-info/index.vue'
import topStoreInfo from '../components/top-store-info/top-store-info.vue'
import goodImg from './compontents/good-img.vue'
import GoodsContent from './compontents/goods-content.vue'
import goodsDetailSub from './compontents/goods-detail-sub.vue'
import hotGoods from './compontents/hot-goods.vue'
//import GoodsPic from './compontents/goods-pic.vue'
import Sku from './compontents/sku.vue'
import { getGoodsDetail } from '@/apis/goods'

const route = useRoute()
const router = useRouter()
// const breadcrumbList = computed(() => {
//   const { shopId } = route?.query || {}
//   return shopId
//     ? [
//         {
//           path: '/mall',
//           name: { zh: '在线商城', en: 'Online Store' },
//         },
//         {
//           path: `/mall/shop/${shopId}`,
//           name: { zh: '店铺主页', en: 'Shop Homepage' },
//         },
//         {
//           name: { zh: '商品详情', en: 'Product Details' },
//         },
//       ]
//     : [
//         {
//           path: '/mall',
//           name: { zh: '在线商城', en: 'Online Store' },
//         },
//         {
//           name: { zh: '商品详情', en: 'Product Details' },
//         },
//       ]
// })

// todo 临时展示商品视频
const TEMP_WHITE_VIDEO = [
  {
    id: '4050061420148800094',
    video: [
      'https://static.chinamarket.cn/spu/video/6e52e947379f4b4ab718407012095225.mp4',
      'https://static.chinamarket.cn/spu/video/9c014d71a94b4edf9fb70adc306d4622.mp4',
      'https://static.chinamarket.cn/spu/video/db400725c06542a283e61fd66f4051c6.mp4',
    ],
  },
  {
    id: '4050062828885200084',
    video: [
      'https://static.chinamarket.cn/spu/video/dbe39707c31a4df0b9c11433f976f31a.mov',
      'https://static.chinamarket.cn/spu/video/881fa3706fc542cf92cddff9fd9725a9.mov',
      'https://static.chinamarket.cn/spu/video/f982049cd3414ba8bf3d4e2b913f8c90.mov',
    ],
  },
  {
    id: '4050064232050400098',
    video: [
      'https://static.chinamarket.cn/spu/video/9f157da5f9f44c4abc4c1ef7299b9440.mp4',
      'https://static.chinamarket.cn/spu/video/1876d4b1c5b64305887b215f5b6c8b84.mp4',
      'https://static.chinamarket.cn/spu/video/e581440fd41c40c0b2f9ab53883308e4.mp4',
    ],
  },
  {
    id: '4050064231048200017',
    video: [
      'https://static.chinamarket.cn/spu/video/f65bdfc0cc7c4987940f728054dc8909.mp4',
      'https://static.chinamarket.cn/spu/video/d3e3005df9724deea7b216bc203e7dbb.mp4',
      'https://static.chinamarket.cn/spu/video/e10ff830dc5a44b18a18a85cb4fd867a.mp4',
    ],
  },
  {
    id: '4050064020191200034',
    video: [
      'https://static.chinamarket.cn/spu/video/28c2f50cf8a24cfd8f709190dfd88b39.mp4',
      'https://static.chinamarket.cn/spu/video/df97e5b5bf604955b32af46f39e94cc3.mp4',
      'https://static.chinamarket.cn/spu/video/77259bebe47145aaa4f1c8a3f8f37613.mp4',
    ],
  },
  {
    id: '4050063839105600001',
    video: [
      'https://static.chinamarket.cn/spu/video/5b4f296ed9ee4ec3afbcbdfe1f2020f2.mp4',
      'https://static.chinamarket.cn/spu/video/fa89bcb19f6c4b83b88cde399cd49e25.mp4',
      'https://static.chinamarket.cn/spu/video/48dd00ad36bf4ba48df758e8ab12cefe.mp4',
    ],
  },
  {
    id: '4050064232283000048',
    video: [
      'https://static.chinamarket.cn/spu/video/7174819626064198aa79fd069c66e323.mp4',
      'https://static.chinamarket.cn/spu/video/92477dd24fd04c83beac494dc25708dd.mp4',
      'https://static.chinamarket.cn/spu/video/9fe93f21774b483d9d11b3a5d7e1aff5.mp4',
    ],
  },
  {
    id: '4050064232005400069',
    video: [
      'https://static.chinamarket.cn/spu/video/f8049ca5940e4f3d9f12f2368c4b39d6.mp4',
      'https://static.chinamarket.cn/spu/video/efb480a2d880420eb88ebd1b40f7a8a1.mp4',
      'https://static.chinamarket.cn/spu/video/fe93c3eddbed4398bd404205ef87532a.mp4',
    ],
  },
  {
    id: '4050061620966400059',
    video: [
      'https://static.chinamarket.cn/spu/video/9cbb903ad5344ab98b9fde10fb0d2755.mp4',
      'https://static.chinamarket.cn/spu/video/f5c2d7c3d32c4a67ba185a5733cc28b7.mp4',
      'https://static.chinamarket.cn/spu/video/e7777e7fc7c94253ac92444b680d9bc8.mp4',
    ],
  },
  {
    id: '4050061626568000074',
    video: [
      'https://static.chinamarket.cn/spu/video/663dff295b4f42b6868fe368931f1080.mp4',
      'https://static.chinamarket.cn/spu/video/2a8010ea6e774b3f8ca8d63af1a661e7.mp4',
      'https://static.chinamarket.cn/spu/video/98f8d149770147c5928e87cbe031dba9.mp4',
    ],
  },
  {
    id: '4050061432503000016',
    video: [
      'https://static.chinamarket.cn/spu/video/51f855a0ec0b49cf9c781117d59b8d3a.mp4',
      'https://static.chinamarket.cn/spu/video/5d5cf342f4f04cd588c1cdfce8c2a9ec.mp4',
      'https://static.chinamarket.cn/spu/video/95e5a2436bc249b09b57a3857dc875a4.mp4',
    ],
  },
  {
    id: '4050062432827800084',
    video: [
      'https://static.chinamarket.cn/spu/video/11ab5d8486124e1894338ff22e581554.mp4',
      'https://static.chinamarket.cn/spu/video/c4a0a3dc0bb6437b853a16c62131bb6c.mp4',
      'https://static.chinamarket.cn/spu/video/66bfb0c8e86d444c941d1716d31aadc4.mp4',
    ],
  },
  {
    id: '4050061036850000097',
    video: [
      'https://static.chinamarket.cn/spu/video/2b8ecd09c5644be1afeea209a5bcf6d3.mp4',
      'https://static.chinamarket.cn/spu/video/c5e999a0180941f3b79e2de41b181e97.mp4',
      'https://static.chinamarket.cn/spu/video/d0f4f84a150941e9ae511037c82c9881.mp4',
    ],
  },
  {
    id: '4050064226802200080',
    video: [
      'https://static.chinamarket.cn/spu/video/de41ee5dbc5440969f22911b7c3f2072.mp4',
      'https://static.chinamarket.cn/spu/video/57a51c0953ff44dd98121a999c788720.mp4',
      'https://static.chinamarket.cn/spu/video/5c30ba725b8347debd53031ad07d276b.mp4',
    ],
  },
  {
    id: '4050064226931400078',
    video: [
      'https://static.chinamarket.cn/spu/video/588e26b9c86b40ac9ec807b98087a593.mp4',
      'https://static.chinamarket.cn/spu/video/f5426e878e5e43d0a1de5f7e18c4e3ba.mp4',
      'https://static.chinamarket.cn/spu/video/7eef029af20046808acd10d9db3f6a9b.mp4',
    ],
  },
  {
    id: '4050064234183200067',
    video: [
      'https://static.chinamarket.cn/spu/video/3be82928b1874eb282eb60833ea19449.mp4',
      'https://static.chinamarket.cn/spu/video/4f325df27a3f41809a21c18c4aa444dd.mp4',
      'https://static.chinamarket.cn/spu/video/bbe020dfe4af44d8b04a6a212eb7bf61.mp4',
    ],
  },
  {
    id: '4050064234340800093',
    video: [
      'https://static.chinamarket.cn/spu/video/53e4efb26fae464295c7c5fccb0ed15c.mp4',
      'https://static.chinamarket.cn/spu/video/fd646d8d44244572817552c2b5111fc9.mp4',
      'https://static.chinamarket.cn/spu/video/48b754ba23714bc2a87f9d5b24116776.mp4',
    ],
  },
]
// todo 临时展示商品视频
const tempChangeDetailData = (data) => {
  if (TEMP_WHITE_VIDEO.map((item) => item.id).includes(data.id)) {
    const item = TEMP_WHITE_VIDEO.find((item) => item.id === data.id)
    if (!item) {
      return data
    }
    return {
      ...data,
      spuVideo: item.video[0],
      spuVideoEn: item.video[1],
      spuVideoAr: item.video[2],
    }
  }
  return data
}

const detailInfo = ref(null)
const loading = ref(true)

const shopInfo = computed(() => {
  return detailInfo.value?.userInfoApiVo || {}
})
const handleShop = () => {
  const { href } = router.resolve({
    name: 'shop',
    params: {
      id: detailInfo.value.userId,
    },
  })
  window.open(href, '_blank')
}

const getGoodsDetailInfo = async (callback) => {
  try {
    const data = await getGoodsDetail({ id: route.params.id })

    if (data) {
      // todo 临时替换商品详情链接
      detailInfo.value = tempChangeDetailData(data)
    }

    typeof callback === 'function' && callback()
  } catch (e) {
    console.log(e)
  } finally {
    loading.value = false
  }
}
getGoodsDetailInfo()

const showViewer = ref(false)
const initialIndex = ref(0)
const imgDetailList = computed(() => {
  const html = detailInfo.value?.spuInfo || ''
  return [...html.matchAll(/<img[^>]+src="([^"]+)"/g)].map((match) => match[1])
})

const goodsPic = ref(null)
const goodsPicChange = (img) => {
  if (!img) return
  goodsPic?.value?.handleCustomImg({ imgUrl: img, url: img, type: 'image' })
}
</script>

<style lang="scss" scoped>
.w-bg {
  background: #fff;
}
.goods-wrap {
  display: flex;
  width: 100%;
  min-height: 626px;
  //padding: 16px;
  padding: 24px 0;
  box-sizing: border-box;
  //overflow: hidden;
  background: #fff;
  //border-radius: 8px;
  margin-bottom: 27px;

  .goods-left {
    max-width: calc(100% - 552px);
    flex: 1;
    // max-width: 820px;
    // min-width: 700px;
    flex-shrink: 0;
    //width: 500px;
    height: 100%;
    // margin-right: 16px;
    overflow: hidden;
  }

  .goods-right {
    width: 504px;
    top: 152px;
    position: sticky;
    overflow: auto;
    border: 1px solid #f0f0f0;
    padding: 16px 20px;
    border-radius: 8px;
    background: #fff;
  }

  .good-title {
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
    letter-spacing: 0px;
    margin-bottom: 8px;
  }
  .good-sale {
    font-size: 14px;
    font-weight: normal;
    color: #505259;
    line-height: 20px;
    margin-bottom: 8px;
  }
  .store {
  }
}

[dir='rtl'] .goods-wrap {
  .goods-left {
    margin-right: 0;
    margin-left: 16px;
  }
}

.logo-nav {
  position: fixed;
  top: 64px;
  z-index: 49;
  width: 100%;
  height: 64px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.goods-detail {
  width: 100%;
  //overflow: hidden;
  margin: 0 auto;
  background: #fff;

  :deep(img),
  :deep(video) {
    display: block;
    width: 100% !important;
    cursor: pointer;
  }

  &-title {
    color: $primary-color;
    font-weight: 600;
    line-height: 48px;
    padding: 0 16px;
    border-bottom: 2px solid $primary-color;
  }

  &__title {
    display: flex;
    align-items: center;
    color: #333;
    height: 40px;

    &::before {
      content: '';
      border-left: 4px solid $primary-color;
      height: 14px;
      display: block;
      margin-right: 8px;
    }
  }
}

[dir='rtl'] {
  .goods-detail__title {
    &::before {
      margin-right: 0;
      margin-left: 8px;
    }
  }

  .more-btn {
    margin-right: 0;
    margin-left: 2px;
  }

  .pl-4 {
    padding-right: 16px;
    padding-left: 0;
  }

  .pr-4 {
    padding-left: 16px;
    padding-right: 0;
  }
}
/* 响应式设计 */
@media (max-width: 1200px) {
  .goods-right {
    width: 430px;
  }
}
</style>
