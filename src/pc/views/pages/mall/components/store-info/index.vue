<template>
  <div
    class="shop-wrap flex justify-between items-center px-[16px] py-[16px] mb-[20px] bg-[#fff]"
    @click="handleShopTitle"
    v-if="shopInfo && shopInfo.companyName"
  >
    <div class="flex items-center h-[100%] flex-1 shrink-0 overflow-hidden">
      <div class="logo flex justify-center items-center shrink-0 mr-4 overflow-hidden">
        <el-image v-if="!isJump" :src="mainImage" alt="" class="w-[100%] h-[100%]" fit="cover" :preview-src-list="[mainImage]" hide-on-click-modal></el-image>
        <el-image v-else :src="mainImage" alt="" class="w-[100%] h-[100%] cursor-pointer" fit="cover" @click="handleShopTitle"></el-image>
      </div>
      <div class="flex flex-col min-h-[50px] overflow-hidden justify-center">
        <div class="flex items-center mb-[4px]">
          <div :class="`shop-title color-[#333] font-600 mr-[5px] ${isJump ? 'cursor-pointer' : ''}`" @click="handleShopTitle" :title="shopInfo.companyName">
            {{ shopInfo.companyName }}
          </div>
          <!-- <div v-if="shopInfo?.shopLogo" class="logo-border rounded-50%">
            <el-image v-if="shopInfo?.shopLogo" :src="logo" mode="aspectFill" class="w-full h-full rounded-50% bg-whrite"></el-image>
          </div> -->
          <div class="shrink-0 flex">
            <div v-for="(item, index) in shopInfo.labelList" :key="index">
              <div class="shop-tag shop-tab-jlb" v-if="item.labelName == '俱乐部商家'">
                <div class="text-ellipsis-1">俱乐部商家</div>
              </div>
              <div class="shop-tag shop-tab-sl text-ellipsis-1" v-else-if="item.labelName == '实力商家'">
                <div class="text-ellipsis-1">实力商家</div>
              </div>
              <div class="shop-tag shop-tab-gc text-ellipsis-1" v-else-if="item.labelName == '工厂直营'">
                <div class="text-ellipsis-1">工厂直营</div>
              </div>
            </div>
            <div v-for="(item, index) in shopInfo.labelList" :key="index">
              <div
                class="shop-tag shop-tab-default text-ellipsis-1"
                v-if="!(item.labelName == '俱乐部商家' || item.labelName == '实力商家' || item.labelName == '工厂直营')"
              >
                <div class="text-ellipsis-1" :title="item.labelName">{{ item.labelName }}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="whitespace-wrap ellipsis2 mt-1 text-[14px] text-[#505259]" v-if="isJump">
          <span>
            <Icon type="icon-dizhi1" :size="14" color="#505259"></Icon>

            {{ address || '-' }}</span
          >
        </div>
        <div class="shop-brand" v-else-if="shopInfo.shopBrandDescribe">
          <div class="ellipsis2" v-if="shopInfo.shopBrandDescribe.length < 20">{{ shopInfo.shopBrandDescribe }}</div>
          <el-popover v-else placement="bottom" title="" :width="400" trigger="click" :content="shopInfo.shopBrandDescribe">
            <template #reference>
              <div class="ellipsis2 cursor-pointer">{{ shopInfo.shopBrandDescribe }}</div>
            </template>
          </el-popover>
        </div>
      </div>
    </div>
    <div class="shrink-0 shop-right py-[5px]" v-if="isJump">
      <el-button class="jump-btn" @click="handleShopTitle">
        <!-- <icon type="icon-a-dianpuguanliicon" size="16" class="mr-2"></icon> -->
        进入店铺
      </el-button>
    </div>
    <div class="shop-right flex flex-col justify-center max-w-[42%] min-w-[12%] shrink-0 h-[100%]" v-else-if="address || category">
      <table class="table-reset">
        <tr class="label-item-wrap" v-if="address">
          <td class="label-item text-right color-[#999] pr-[8px] whitespace-nowrap">{{ t('address') }}:</td>
          <td>
            <div class="whitespace-wrap ellipsis2">{{ address }}</div>
          </td>
        </tr>

        <tr class="label-item-wrap" v-if="category">
          <td class="label-item text-right color-[#999] pr-[8px] whitespace-nowrap">{{ t('mainGoods') }}:</td>
          <td>
            <div class="whitespace-wrap ellipsis2">{{ category }}</div>
          </td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { ossUrl } from '@/constants/common'

const { t } = useI18n({
  messages: {
    zh: {
      address: '地址',
      mainGoods: '主营商品',
    },
    en: {
      address: 'Address',
      mainGoods: 'Main Products',
    },
  },
})

const props = defineProps({
  shopInfo: {
    type: Object,
    default: () => ({}),
  },
  isJump: {
    type: Boolean,
    default: false,
  },
})
const mainImage = computed(() => props.shopInfo?.businessMainImage || `${ossUrl}/mall/errorImg.png`)
//const logo = computed(() => props.shopInfo?.shopLogo || `${ossUrl}/mall/shoplogo1.png`)
const address = computed(() => {
  return `${props.shopInfo?.provinceName || ''}${props.shopInfo?.cityName || ''}${props.shopInfo?.areaName || ''}${props.shopInfo?.companyAddress}`
})
const category = computed(() => props.shopInfo?.businessInfo)
const emit = defineEmits(['handleTitleClick'])
const handleShopTitle = () => {
  if (!props.isJump) return
  emit('handleTitleClick')
}
</script>

<style scoped lang="scss">
.logo-border {
  width: 22px;
  height: 22px;
  opacity: 1;
  box-sizing: border-box;
  padding: 1px;
  background: linear-gradient(180deg, #d8131a 0%, #ffb73b 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.shop-wrap {
  margin-bottom: 16px;
  border-radius: 8px;
  min-height: 82px;
}

.logo {
  width: 70px;
  height: 70px;
  box-sizing: border-box;
  //border-radius: 50%;
  background: #ccc;

  .img-loader {
    width: 100%;
    height: 100%;
  }
}

.shop-title {
  // max-width: 100px;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.shop-brand {
  font-size: 12px;
  color: #999;
  padding: 4px 6px;
  border-radius: 4px;
  background: rgba(216, 216, 216, 0.2);
  width: max-content;
  max-width: 100%;
}

.shop-right {
  border-left: thin solid #edf0f5;
  color: #333;
  font-size: 12px;
  padding-left: 16px;

  .label-item-wrap + .label-item-wrap {
    td {
      padding-top: 4px;
    }
  }
}

.table-reset {
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;
  border: none;
  padding: 0;
  margin: 0;
  background: none;
}

.table-reset tr {
  width: 100%;
}

.table-reset td {
  vertical-align: baseline;

  .ellipsis2 {
    white-space: pre-wrap;
    word-break: break-all;
  }
}

.ellipsis2 {
  @include ellipsis(2);
}

.jump-btn {
  border-color: $primary-color;
  color: $primary-color;
  height: 40px;
  min-width: 176px;
  font-size: 14px;
  font-weight: 600;
  background: #fff;
}

[dir='rtl'] {
  .logo {
    margin-right: 0;
    margin-left: 16px;
  }

  .label-item {
    padding-right: 0;
    padding-left: 8px;
    text-align: left;
  }

  .shop-right {
    border-left: 0;
    border-right: thin solid #eee;
    padding-right: 16px;
    padding-left: 0;
  }
}
</style>
