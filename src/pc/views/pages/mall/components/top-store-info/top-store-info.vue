<template>
  <div v-if="shopInfo && shopInfo.companyName" class="top-store" @click="handleShopTitle">
    <div class="logo">
      <el-image :src="mainImage" alt="" @click="handleShopTitle" class="w-[100%] h-[100%]" fit="cover"></el-image>
    </div>
    <div class="shop-title text-ellipsis-1" :title="shopInfo.companyName" @click="handleShopTitle">{{ shopInfo.companyName }}</div>
    <div class="shrink-0 flex">
      <div v-for="(item, index) in shopInfo.labelList" :key="index">
        <div class="shop-tag shop-tab-jlb" v-if="item.labelName == '俱乐部商家'">
          <div class="text-ellipsis-1">俱乐部商家</div>
        </div>
        <div class="shop-tag shop-tab-sl text-ellipsis-1" v-else-if="item.labelName == '实力商家'">
          <div class="text-ellipsis-1">实力商家</div>
        </div>
        <div class="shop-tag shop-tab-gc text-ellipsis-1" v-else-if="item.labelName == '工厂直营'">
          <div class="text-ellipsis-1">工厂直营</div>
        </div>
      </div>
      <div v-for="(item, index) in shopInfo.labelList" :key="index">
        <div
          class="shop-tag shop-tab-default text-ellipsis-1"
          v-if="!(item.labelName == '俱乐部商家' || item.labelName == '实力商家' || item.labelName == '工厂直营')"
        >
          <div class="text-ellipsis-1" :title="item.labelName">{{ item.labelName }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ossUrl } from '@/constants/common'

const mainImage = computed(() => props.shopInfo?.businessMainImage || `${ossUrl}/mall/errorImg.png`)
const props = defineProps({
  shopInfo: {
    type: Object,
    default: () => ({}),
  },
})
const emit = defineEmits(['handleTitleClick'])
const handleShopTitle = () => {
  emit('handleTitleClick')
}
</script>
<style lang="scss" scoped>
.top-store {
  background: #f7f8fc;
  padding: 8px;
  display: flex;
  border-radius: 8px;
  flex-direction: row;
  align-items: center;
  cursor: pointer;
}
.logo {
  width: 36px;
  height: 36px;
  border-radius: 4px;
  margin-right: 8px;
  overflow: hidden;
}
.shop-title {
  font-size: 14px;
  font-weight: normal;
  color: #505259;
  //text-decoration: underline;
  cursor: pointer;
  margin-right: 8px;
  border-bottom: 1px solid #505259;
}
</style>
