<template>
  <div class="bg-white mb-[20px] px-[20px] py-[16px] rounded-[8px]">
    <div class="text-[20px] font-600 text-[#333] flex items-center mb-[18px]">
      <img-loader src="/mall/dianshang-icon.png" alt="" img-class="w-[22px] object-contain mr-1 block"></img-loader>
      <div>垂直电商-新明辉模式</div>
    </div>
    <div v-for="(item, i) in list" :key="i" class="flex items-center" :class="i ? '' : 'mb-[18px]'">
      <a v-for="item1 in item" :key="item1.name" class="item-wrap" :href="item1.url" target="_blank">
        <div class="h-183px">
          <img-loader :src="`${ossUrl}${item1.logo}`" :alt="item1.name" img-class="h-full w-full object-contain" />
        </div>
        <div class="text-[#333] text-4 font-600 bg-#eee h-54px flex items-center justify-center text-ellipsis whitespace-nowrap overflow-hidden">
          {{ item1.name }}
        </div>
      </a>
    </div>
  </div>
</template>

<script setup>
import { ossUrl } from '@/constants/common'

const list = [
  [
    {
      url: 'https://www.xinminghui.com/',
      logo: '/mall/%E6%96%B0%E6%98%8E%E8%BE%89-logo.png',
      name: '新明辉',
    },
    {
      url: 'http://www.yc0852.com/',
      logo: '/mall/%E6%B2%82%402x.png',
      name: '沂川商城',
    },
    {
      url: 'https://www.haoduobao6.cn/',
      logo: '/mall/%E5%A5%BD%E5%A4%9A%E5%AE%9D-logo.png',
      name: '好多宝',
    },
    {
      url: 'http://www.e-nongye.cn/wzsy',
      logo: '/mall/e%E5%86%9C-logo.png',
      name: 'e农超',
    },
    {
      url: 'https://slfl.net/',
      logo: '/mall/%E7%9B%9B%E9%9A%86%E4%B9%8B%E5%AE%B6-logo.png',
      name: '盛隆商城',
    },
  ],
  [
    {
      url: 'http://www.xmjwjjd.com/',
      logo: '/mall/%E9%91%AB%E7%BE%8E%E7%9D%AB-logo.png',
      name: '鑫美捷商城',
    },
    {
      url: 'https://iyunsulian.com/#/index',
      logo: '/mall/%E4%BA%91.png',
      name: '云塑链',
    },
    {
      url: 'https://www.chinacoop.gov.cn/',
      logo: '/mall/%E5%90%88%E4%BD%9C%E7%A4%BE-logo.png',
      name: '供合销',
    },
    {
      url: 'http://www.lyhenganlb.com/',
      logo: '/mall/%E6%81%92%E5%AE%89-logo.png',
      name: '恒安劳保',
    },
    {
      url: 'https://qince.com/',
      logo: '/mall/%E6%99%B4%E7%AD%96.png',
      name: '勤策saas软件',
    },
  ],
]
</script>

<style scoped lang="scss">
.item-wrap {
  overflow: hidden;
  width: 20%;
  margin-right: 16px;
  border-radius: 8px;
  background: #fff;
  box-sizing: border-box;
  border: 1px solid #eef0f1;

  &:last-child {
    margin-right: 0;
  }
}
</style>
