<!--
 * @Author: 李兵 <EMAIL>
 * @Date: 2024-08-15 10:49:42
 * @LastEditors: 李兵 <EMAIL>
 * @LastEditTime: 2024-08-15 11:59:28
 * @FilePath: \trade-exhibition\src\views\pages\mall\home\components\swiper-goods.vue
 * @Description: 1111
-->
<template>
  <swiper
    :modules="[Autoplay, Pagination]"
    :loop="true"
    :autoplay="{
      delay: 3000,
      disableOnInteraction: false,
      pauseOnMouseEnter: true,
    }"
    navigation
    :pagination="{ clickable: true }"
  >
    <swiper-slide v-for="item in props.list" :key="item.id" @click="router.push(`/mall/goods-detail/${item.id}`)">
      <img class="object-cover" :src="item.pic" width="100%" height="382px" />
    </swiper-slide>
  </swiper>
</template>

<script setup>
import 'swiper/css'
import 'swiper/css/pagination'
import { Autoplay, Pagination } from 'swiper/modules'
import { Swiper, SwiperSlide } from 'swiper/vue'

const router = useRouter()

const props = defineProps({
  list: { type: Array, default: () => [] },
})
</script>

<style lang="scss" scoped>
:deep() {
  .swiper-pagination-bullet-active {
    background-color: $primary-color;
  }
}
</style>
