<template>
  <div class="flex-1 h-[540px] rounded-4 overflow-hidden shrink-0 bg-[#fff] mr-4 p-6">
    <div
      class="flex justify-around font-semibold mb-5"
      :class="[['en', 'thai'].includes($storageLocale) ? 'text-4' : 'text-5']"
      :style="{ fontSize: $storageLocale === 'indonesian' ? '14px' : '' }"
    >
      <div class="whitespace-nowrap text-ellipsis overflow-hidden">甄选商家</div>
      <div class="whitespace-nowrap text-ellipsis overflow-hidden">产业带</div>
      <div class="whitespace-nowrap text-ellipsis overflow-hidden">外贸专区</div>
      <div class="whitespace-nowrap text-ellipsis overflow-hidden">进口馆</div>
      <div class="whitespace-nowrap text-ellipsis overflow-hidden">服务专区</div>
    </div>
    <div class="relative min-h-336px mb-6">
      <!-- 左箭头 -->
      <button ref="prevButton" class="swiper-btn" v-if="isHide">
        <Icon type="icon-fh" :size="16" />
      </button>
      <!-- uae -->
      <swiper
        v-if="nationalTypeUae"
        :loop="true"
        :autoplay="{
          delay: 2500,
          disableOnInteraction: false,
          pauseOnMouseEnter: true,
        }"
        :navigation="{ prevEl: prevButton, nextEl: nextButton }"
        :modules="modules"
        class="w-[672px] h-[336px] bg-[#fff] rounded-3"
      >
        <swiper-slide v-for="(item, i) in uaeList" :key="i" class="rounded-3">
          <img-loader
            :src="item"
            class="w-[100%] h-[100%] object-cover cursor-pointer rounded-3"
            :loading-img="`${item}?x-oss-process=image/resize,h_100`"
            @click.stop="handleUaeBanner"
          ></img-loader>
        </swiper-slide>
      </swiper>
      <!-- idn -->
      <swiper
        v-else-if="isNation('idn')"
        :loop="true"
        :autoplay="{
          delay: 2500,
          disableOnInteraction: false,
          pauseOnMouseEnter: true,
        }"
        :navigation="{ prevEl: prevButton, nextEl: nextButton }"
        :modules="modules"
        class="w-[672px] h-[336px] bg-[#fff] rounded-3"
      >
        <swiper-slide v-for="(item, i) in idnList" :key="i" class="rounded-3">
          <img-loader
            :src="item"
            class="w-[100%] h-[100%] object-cover cursor-pointer rounded-3"
            :loading-img="`${item}?x-oss-process=image/resize,h_100`"
            @click.stop="handleIdnBanner"
          />
        </swiper-slide>
      </swiper>
      <swiper
        v-else-if="list.length"
        :loop="true"
        :loading="listLoading"
        :autoplay="{
          delay: 2500,
          disableOnInteraction: false,
          pauseOnMouseEnter: true,
        }"
        :navigation="{ prevEl: prevButton, nextEl: nextButton }"
        :modules="modules"
        class="w-[672px] h-[336px] bg-[#fff] rounded-3"
      >
        <swiper-slide v-for="(item, i) in list" :key="i" class="rounded-3">
          <img-loader
            :src="item.adImage"
            class="w-[100%] h-[100%] object-cover cursor-pointer rounded-3"
            :loading-img="`${item.adImage}?x-oss-process=image/resize,h_100`"
            @click.stop="handleBanner(item)"
          ></img-loader>
        </swiper-slide>
      </swiper>
      <!-- 右箭头 -->
      <button ref="nextButton" class="swiper-btn next-btn" v-if="isHide">
        <Icon type="icon-xiala1" :size="16" />
      </button>
    </div>
    <div class="flex justify-around">
      <div v-for="(item, index) in bannerClassList" :key="index" class="flex-1 text-center" @click="handleItemClick(item)">
        <div class="w-56px h-56px mx-auto mb-2">
          <img :src="item.icon" alt="" class="w-full h-full" />
        </div>
        <div class="truncate text-ellipsis overflow-hidden ... max-w-80px text-center text-[#999999] text-[14px] mx-auto" :title="item.name">
          {{ item.name }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import 'swiper/css'
import 'swiper/css/pagination'
// import required modules
import { Autoplay, Navigation } from 'swiper/modules'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { ossUrl } from '@/constants/common'
//import { BANNER_CONFIG_ARRAY } from '@/constants/mall'
import { getSlideImageList } from '@/apis/export-to-domestic'
import { useStorageLocale } from '@/i18n/translatePlugin'

const router = useRouter()
const { storageLocale } = useStorageLocale()

const nationalType = inject('$nationalType')
const nationalTypeUae = nationalType === 'uae'
const { isNation } = useNation()
const isHide = computed(() => !nationalTypeUae && !isNation('idn'))

const uaeList = computed(() => {
  return storageLocale.value === 'zh' ? [`${ossUrl}/uae-national-banner-zh.png`] : [`${ossUrl}/uae-national-banner-en.png`]
})

const idnList = computed(() => {
  return [`${ossUrl}/idn-national-banner-${storageLocale.value}.png`]
})

const handleUaeBanner = () => {
  router.push('/uae-pavilion')
}

const handleIdnBanner = () => {
  router.push('/indonesia-pavilion')
}

const modules = [Autoplay, Navigation]
const prevButton = ref(null)
const nextButton = ref(null)

const bannerClassList = [
  {
    icon: 'https://static.chinamarket.cn/static/trade-exhibition/mall/banner-icon1.png',
    name: '实力商家',
  },
  {
    icon: 'https://static.chinamarket.cn/static/trade-exhibition/mall/banner-icon2.png',
    name: '尾货清仓',
  },
  {
    icon: 'https://static.chinamarket.cn/static/trade-exhibition/mall/banner-icon3.png',
    name: '大集哥找货',
    type: 2,
  },
  {
    icon: 'https://static.chinamarket.cn/static/trade-exhibition/mall/banner-icon4.png',
    name: '品牌馆',
  },
  {
    icon: 'https://static.chinamarket.cn/static/trade-exhibition/mall/banner-icon5.png',
    name: '国家馆',
  },
  {
    icon: 'https://static.chinamarket.cn/static/trade-exhibition/mall/banner-icon6.png',
    name: 'AI视频',
  },
]

const list = ref([])
const listLoading = ref(false)
const getList = async () => {
  try {
    listLoading.value = true
    let params = { bindType: 2 }
    list.value = (await getSlideImageList(params)) || []
  } catch (e) {
    console.log(e)
    // list.value = getoldList()
    console.log('list----', list.value)
  } finally {
    listLoading.value = false
  }
}
// const getoldList = () => {
//   return BANNER_CONFIG_ARRAY.map((item) => {
//     const adImage = item.lang[storageLocale.value] || item.lang.zh
//     const jumpUrl = item.shopId == 'exportToDomestic' ? '/export-to-domestic' : `/mall/shop/${item.shopId}`
//     return { adImage, jumpUrl }
//   })
// }

getList()
const handleBanner = (item) => {
  if (!item.jumpUrl) return
  let isHttp = /^https?/.test(item.jumpUrl)
  if (!isHttp) {
    let path = {
      path: item.jumpUrl,
    }
    const { href } = router.resolve(path)
    window.open(href, '_blank')
  } else {
    window.open(item.jumpUrl, '_blank')
  }
}

const Emits = defineEmits(['handleBottomItemClick'])
const handleItemClick = ({ type }) => {
  if (!type && type !== 0) return
  Emits('handleBottomItemClick', type)
}
</script>

<style lang="scss" scoped>
.img-loader {
  width: 100%;
  border-radius: 8px;

  img {
    border-radius: 8px;
  }
}

.swiper-btn {
  position: absolute;
  width: 40px;
  height: 40px;
  top: 0;
  bottom: 0;
  left: 20px;
  z-index: 2;
  display: block;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.2);
  line-height: 40px;
  color: #fff;
  text-align: center;
  border: none;
  margin: auto;
  cursor: pointer;
  outline: none;
  box-shadow: none;

  &.next-btn {
    left: auto;
    right: 20px;
  }
}
</style>
