<!--
 * @Author: 李兵 <EMAIL>
 * @Date: 2024-08-13 14:59:44
 * @LastEditors: your name
 * @LastEditTime: 2024-12-20 20:50:39
 * @FilePath: /trade-exhibition/src/pc/views/pages/mall/home/<USER>
 * @Description: 商城首页组件
-->
<template>
  <div class="home-wrap pd-[24px]">
    <div class="h-[160px]"></div>
    <div class="logo-nav">
      <SearchInput></SearchInput>
    </div>
    <div class="home-content w-1260">
      <div class="relative flex justify-between mb-5">
        <!-- 商品类目 -->
        <div class="w-60 mr-4">
          <Category></Category>
        </div>
        <!-- banner -->
        <WelcomeBanner @handleBottomItemClick="handleBottomItemClick"></WelcomeBanner>
        <!-- 新手引导 -->
        <BeginnerGuide ref="beginnerRef"></BeginnerGuide>
      </div>
      <!-- <XmhMode v-if="!nationalTypeUae" /> -->
      <!-- 热门活动 -->
      <activity-module></activity-module>
      <!-- 为您推荐商品展示 -->
      <RecommendGoods showPagination scene="home_page" :isMall="true"></RecommendGoods>
    </div>
    <!-- 返回顶部 -->
    <!-- <BackTop></BackTop> -->
  </div>
</template>

<script setup>
// import BackTop from '@/pc/components/back-top/back-top.vue'
import RecommendGoods from '@/pc/components/recommend-goods/index.vue'
import SearchInput from '@/pc/components/search-input/search-input.vue'
import ActivityModule from './components/activity-module.vue'
import BeginnerGuide from './components/beginner-guide.vue'
import Category from './components/category.vue'
import WelcomeBanner from './components/welcome-banner.vue'

// import XmhMode from './components/xmh-mode.vue'

// const nationalType = inject('$nationalType')
// const nationalTypeUae = nationalType === 'uae'

const beginnerRef = ref(null)
const handleBottomItemClick = (type) => {
  // 大集哥找货物
  if (type === 2) {
    beginnerRef.value?.handleOpportunityAdd()
  }
}

setTimeout(() => window?.translate?.execute(), 1000)
</script>

<style lang="scss" scoped>
.home-wrap {
  min-height: calc(100vh - 64px - 162px);
}

.logo-nav {
  position: fixed;
  top: 64px;
  z-index: 49;
  width: 100%;
  height: 64px;
  margin: 0 auto 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.1);
  background-color: #fff;
}
</style>
