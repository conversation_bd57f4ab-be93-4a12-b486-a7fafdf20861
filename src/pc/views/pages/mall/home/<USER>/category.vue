<template>
  <div class="category rounded-4">
    <div class="category-tab flex shrink-0">
      <div class="category-tab__btn active rounded-4 text-4 font-semibold">{{ $t('mall.category') }}</div>
    </div>
    <div class="category-list">
      <div v-for="(list, listI) in categoryList" :key="listI" :class="`category-list-row flex ${isActive(list) ? 'active' : ''}`">
        <div v-for="(item, i) in list" :key="i" class="category-item">
          <div class="category-item__inner" @click="handleSelect(item)">{{ item.categoryName }}</div>
        </div>

        <div class="category-tree">
          <div class="flex">
            <div :class="{ 'mr-5': $storageLocale === 'zh' && hasCategoryImg(list) }">
              <div v-for="item in list" :key="item.id" class="category-tree-item flex flex-wrap w-[100%]">
                <div class="mb-[4px] w-full">
                  <div
                    class="mb-5 w-full cursor-pointer leading-44px text-14px font-semibold text-[#333333] border-b border-b-style-solid border-b-[#E5E5E5]"
                    @click="handleSelect(item)"
                  >
                    {{ item.categoryName }}
                  </div>

                  <div class="flex flex-wrap">
                    <div
                      v-for="level2 in item.subList"
                      :key="level2.id"
                      class="list-item-wrapper"
                      :class="{ active: $storageLocale === 'zh' && hasCategoryImg(list) }"
                    >
                      <div
                        class="shrink-0 mb-4 cursor-pointer color-[#999] flex items-center item-content"
                        :style="{ width: $storageLocale === 'zh' && hasCategoryImg(list) ? '160px' : '148px' }"
                        @click="handleSelect(level2)"
                      >
                        <div class="w-38px h-38px mr-2">
                          <template v-if="level2.categoryIcon">
                            <img
                              class="w-full h-full rounded-[4px]"
                              :src="`${level2.categoryIcon}?x-oss-process=image/resize,h_${72}/quality,q_${72}`"
                              alt=""
                            />
                          </template>
                        </div>
                        <div
                          class="truncate text-ellipsis overflow-hidden ... max-w-100px text-14px text-#666666 hover:text-[#D8131A]"
                          :title="level2.categoryName"
                        >
                          {{ level2.categoryName }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="w-240px pt-24px" v-if="$storageLocale === 'zh' && hasCategoryImg(list)">
              <div
                v-for="(item, index) in getCurrentHoverCategoryAdList(list)"
                :key="index"
                class="w-60 h-25 mb-10px cursor-pointer"
                @click="handleCategoryAdLinkClick(item)"
              >
                <img :src="item.adImage" alt="" class="w-full h-full" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <!--      <div class="category-tree" v-if="showCategoryModal">-->
      <!--        <div class="flex">-->
      <!--          <div v-for="item in activeList" :key="item.id" class="level1-title mr-[8px] color-[#D8131A] mb-[10px]">-->
      <!--            <div class="cursor-pointer active-text" @click="handleSelect(item)">{{ item.categoryName }}</div>-->
      <!--          </div>-->
      <!--        </div>-->

      <!--        <div class="flex flex-wrap">-->
      <!--          <div v-for="item in activeList" :key="item.id" class="category-tree-item flex flex-wrap w-[100%]">-->
      <!--            <div v-for="level2 in item.children" :key="level2.id" class="level2-item mb-[4px]">-->
      <!--              <div class="mb-[4px] cursor-pointer level2-item-title mb-[8px] active-text" @click="handleSelect(level2)">{{ level2.categoryName }}</div>-->

      <!--              <div class="flex flex-wrap">-->
      <!--                <div v-for="level3 in level2.children" :key="level3.id">-->
      <!--                  <div class="shrink-0 mr-[10px] mb-[8px] cursor-pointer color-[#999] active-text" @click="handleSelect(level3)">{{ level3.categoryName }}</div>-->
      <!--                </div>-->
      <!--              </div>-->
      <!--            </div>-->
      <!--          </div>-->
      <!--        </div>-->
      <!--      </div>-->
      <!-- :style="`transform: translateY(${translateY});`" -->
    </div>
  </div>
</template>

<script setup>
import { useCategoryAdStore, useCategoryFilterStore } from '@/pc/stores'

const categoryStore = useCategoryFilterStore()
const categoryList = computed(
  () =>
    categoryStore.categoryLevel1
      .filter((item) => item.categoryName !== '其他')
      .reduce((prev, cur, index) => {
        const i = Math.floor(index / 2)
        if (!prev[i]) prev[i] = []
        prev[i].push(cur)
        return prev
      }, [])
      .filter((item) => item.length > 1), // todo temp: 这里过滤单数，后面要删除
)
const active = ref(null)
const router = useRouter()
const categoryAdStore = useCategoryAdStore()

onMounted(() => {
  if (!categoryList.value.length) {
    categoryStore.getCategoryList()
  }
  if (!categoryAdStore.categoryAdList.length) {
    categoryAdStore.getCategoryAdList()
  }
})

const handleSelect = (item) => {
  active.value = item.id
  const url = router.resolve({
    path: '/mall/goods-list',
    query: {
      categoryId: item.id,
    },
  })
  window.open(url.href, '_blank')
}

// 类目相应的广告图
const getCurrentHoverCategoryAdList = (list) => {
  const adsArray = []
  list.forEach((item) => {
    const findAd = categoryAdStore.categoryAdList.find((ad) => ad.id === item.id)
    if (findAd) {
      adsArray.push(...findAd.ads)
    }
  })
  return adsArray
}
// 点击广告图
const handleCategoryAdLinkClick = (item) => {
  item.jumpUrl && window.open(item.jumpUrl, '_blank')
}

const activeList = ref([])
const hasCategoryImg = (list) => {
  return list.some((item) => categoryAdStore.categoryAdList.some((ad) => ad.id === item.id))
} // 类目右侧大图
const isActive = (list) => JSON.stringify(activeList.value) === JSON.stringify(list)
</script>

<style lang="scss" scoped>
.category {
  position: relative;
  width: 240px;
  height: 541px;
  background: #fff;

  &-tab {
    height: 40px;

    &__btn {
      flex-shrink: 0;
      width: 100%;
      text-align: center;
      line-height: 40px;
      color: #333;
      font-size: 16px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      cursor: pointer;
      background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAABQCAYAAAD2i6slAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAI8SURBVHic7dbLkRwhFETRnPKh/ZFrskzjTzuhTcWENN/+VMEDztkAO1YZ9+V6vf5J8ivAcrYkv3t/Auhju1wur0leO/8D6GDbTxUAC9qSRAXAmrZ/7ioAFvM2ACoA1rO9e6sAWMh/A6ACYC3vCyBRAbCMDwOgAmAdnxVAogJgCZ8OgAqANXxVAIkKgOl9OQAqAOb3XQEkKgCm9u0AqACY208FkKgAmNaPA6ACYF63FECiAmBKNw2ACoA53VoAiQqA6dw8ACoA5nNPASQqAKZy1wCoAJjLvQWQqACYxt0DoAJgHo8UQKICYAoPDYAKgDk8WgCJCoDhPTwAKgDG90wBJCoAhvbUAKgAGNuzBZCoABjW0wOgAmBcRxRAogJgSIcMgAqAMR1VAIkKgOEcNgAqAMZzZAEkKgCGcugAqAAYy9EFkKgAGMbhA6ACYBxnFECiAmAIpwyACoAxnFUAiQqA8k4bABUA9Z1ZAIkKgNJOHQAVALWdXQCJCoCyTh8AFQB1tSiARAVASU0GQAVATa0KIFEBUE6zAVABUE/LAkhUAJTSdABUANTSugASFQBlNB8AFQB19CiARAVACV0GQAVADb0KIFEB0F23AVAB0F/PAkhUAHTVdQBUAPTVuwASFQDddB8AFQD9dB+AnQqADkoMgAqAPkoMwE4FQGNlBkAFQHtlBmCnAqChUgOgAqCtUgOwUwHQSLkBUAHQTrkB2KkAaKDkAKgAaKPkAOxUAJys7ACoADhf2QHYqQA40V9NmFyhJSXIvAAAAABJRU5ErkJggg==')
        no-repeat center center/100% 100%;

      &.active {
        flex-shrink: 1;
        background: #fff;
        color: $primary-color;
      }
    }
  }

  &-list {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: static;
    height: calc(100% - 40px);
    white-space: nowrap;
    padding: 0 0 16px;
    overflow: auto;
    box-sizing: border-box;

    &::-webkit-scrollbar {
      width: 0;
    }

    &-row {
      display: flex;
      align-items: center;
      min-height: 36px;
      height: 100%;
      position: static;
      padding: 0 20px;

      &:hover,
      &.active {
        background: $primary-color;

        .category-item__inner {
          background: transparent;
          color: #fff;
        }

        .category-tree {
          display: block;
        }
      }
    }
  }

  &-item {
    min-width: 80px;
    text-align: center;
    margin-left: 12px;

    &__inner {
      @include ellipsis(1);
      display: block;
      width: 100%;
      padding: 4px 0;
      box-sizing: border-box;
      // background: #faf9f9;
      border-radius: 14px;
      color: #666;
      font-size: 14px;
      cursor: pointer;
    }
  }

  &-tree {
    display: none;
    position: absolute;
    top: 0;
    left: 100%;
    z-index: 10;
    /** width: calc($main-width - 100%); **/
    /** min-height: 100%; **/
    width: 1020px;
    background: #fff;
    border: 2px solid $primary-color;
    border-radius: 8px;
    padding: 12px 24px;
    box-sizing: border-box;
    color: #333;
    font-size: 14px;
    height: 541px;
    overflow-y: hidden;
    overflow-y: auto;
    scrollbar-width: none;
    //  h-[541px] overflow-y-hidden overflow-y-auto

    .active-text:hover {
      color: $primary-color;
      text-decoration: underline;
    }
  }
  &-tree::-webkit-scrollbar {
    display: none;
  }
}

[dir='rtl'] {
  .category-tree {
    direction: rtl;
    right: 100%;
  }

  .category-item {
    margin-left: 0;
    margin-right: 12px;
  }
}

.level1-title {
  font-size: 16px;
  font-weight: 500;
}

.level2-item {
  width: 50%;
  padding-right: 40px;
  box-sizing: border-box;
}
.list-item-wrapper {
  .item-content {
    margin-right: 15px;
  }
  &:nth-child(6n) {
    .item-content {
      margin-right: 0;
    }
  }
  &.active {
    &:nth-child(4n) {
      .item-content {
        margin-right: 0;
      }
    }
    &:nth-child(6n) {
      .item-content {
        margin-right: 15px;
      }
    }
  }
}
</style>
