<!--
 * @Author: 李兵 <EMAIL>
 * @Date: 2024-08-13 17:48:34
 * @LastEditors: your name
 * @LastEditTime: 2024-12-21 10:12:38
 * @FilePath: /trade-exhibition/src/pc/views/pages/mall/home/<USER>/activity-module.vue
 * @Description: 产品分类
-->
<template>
  <div>
    <!-- 热销爆款 -->
    <div class="single-module" v-if="hotList.length">
      <div class="title flex">
        <img :src="`${ossUrl}/mall/hot-icon1.png`" class="w-[20px] h-[20px]" alt="" />
        <div class="title-text mx-[4px]">{{ t('hotPicks') }}</div>
      </div>
      <div class="goods-list mb-[24px]">
        <div v-for="item in hotList" :key="item.id" class="shrink-0 w-[244px] mr-[6px]">
          <GoodsDetailCard :goodsInfo="item" />
        </div>
        <el-empty v-if="hotList.length === 0" :image-size="200" />
      </div>
    </div>
    <!-- 实力商家 -->
    <div class="single-module" v-if="shopList.length">
      <div class="title flex">
        <img :src="`${ossUrl}/mall/new-shop-icon1.png`" class="w-[20px] h-[20px]" alt="" />
        <div class="title-text mx-[8px]">{{ t('topSellers') }}</div>
      </div>
      <div class="goods-list mb-[24px]">
        <div v-for="item in shopList" :key="item.id" class="shrink-0 w-[232px] mr-[16px]">
          <GoodsDetailCard :goodsInfo="item" :pic="item.businessMainImage" custom-click @click="handleShop(item)" img-mode-class="img-contain">
            <template #default>
              <div class="offer-title">{{ item.companyName }}</div>
              <!-- <div class="offer-bottom flex items-center h-[40px]">
                <div class="color-[#999] text-[12px] mr-[8px] whitespace-nowrap offer-label" @click.stop>
                  {{ t('contact') }}: {{ CUSTOMER_SERVICE_PHONE_NUMBER }}
                </div>
                <div
                  class="customer-btn cursor-pointer border-[#D8131A] border-[1px] border-solid"
                  :title="`${['en', 'thai', 'indonesian'].includes($storageLocale) ? '联系客服' : ''}`"
                  @click.stop="showCustomer"
                >
                  {{ t('customer') }}
                </div>
              </div> -->
            </template>
            <template #tag>
              <div
                v-if="item.blackLabel === 1"
                class="flex justify-center items-center absolute top-0 left-0 z-1 min-w-[106px] h-[32px] text-[16px] color-[#EDD1A8] px-[12px]"
                :style="`background: url('${ossUrl}/mall/shoptagbg.png') no-repeat center center/100% 100%;`"
              >
                {{ t('topSellers') }}
              </div>
            </template>
          </GoodsDetailCard>
        </div>
        <el-empty v-if="shopList.length === 0" :image-size="200" />
      </div>
    </div>
    <!-- 本地好货 -->
    <div class="single-module" v-if="localList.length">
      <div class="title flex">
        <img :src="`${ossUrl}/mall/new-good-icon1.png`" class="w-[20px] h-[20px]" alt="" />
        <div class="title-text mx-[8px]">{{ t('localGoods') }}</div>
      </div>
      <div class="goods-list mb-[24px]">
        <div v-for="item in localList" :key="item.id" class="shrink-0 w-[232px] mr-[16px]">
          <GoodsDetailNewCard :goodsInfo="item" />
        </div>
        <el-empty v-if="localList.length === 0" :image-size="200" />
      </div>
    </div>
    <!--    <div class="row-module flex mb-[24px]" v-if="localList.length || shopList.length">-->
    <!--      <div class="row-module__item flex">-->
    <!--        <div class="title">-->
    <!--          <img-loader src="mall/localgoods.png" class="title-icon w-[30px]" alt="" />-->
    <!--          <div class="title-text text-center">{{ t('localGoods') }}</div>-->
    <!--        </div>-->
    <!--        <div class="goods-list">-->
    <!--          <div v-for="item in localList" :key="item.id" class="shrink-0 w-[244px] mr-[12px]">-->
    <!--            <GoodsDetailCard :goodsInfo="item" />-->
    <!--          </div>-->
    <!--          <el-empty v-if="localList.length === 0" :image-size="200" />-->
    <!--        </div>-->
    <!--      </div>-->
    <!--      <div class="row-module__item flex">-->
    <!--        <div class="title">-->
    <!--          <img-loader src="mall/powerfulshop.png" class="title-icon w-[30px]" alt="" />-->
    <!--          <div class="title-text text-center">{{ t('topSellers') }}</div>-->
    <!--        </div>-->
    <!--        <div class="goods-list">-->
    <!--          <div v-for="item in shopList" :key="item.id" class="shrink-0 w-[244px] mr-[12px]">-->
    <!--            <GoodsDetailCard :goodsInfo="item" :pic="item.picture" custom-click @click="handleShop(item)">-->
    <!--              <template #default>-->
    <!--                <div class="offer-title">{{ item.companyName }}</div>-->
    <!--                <div class="offer-bottom flex items-center h-[40px]">-->
    <!--                  <div class="color-[#999] text-[12px] mr-[8px] whitespace-nowrap" @click.stop>{{ t('contact') }}: {{ CUSTOMER_SERVICE_PHONE_NUMBER }}</div>-->
    <!--                  <div class="customer-btn cursor-pointer border-[#D8131A] border-[1px] border-solid" @click.stop="showCustomer">-->
    <!--                    {{ t('customer') }}-->
    <!--                  </div>-->
    <!--                </div>-->
    <!--              </template>-->
    <!--            </GoodsDetailCard>-->
    <!--          </div>-->
    <!--          <el-empty v-if="shopList.length === 0" :image-size="200" />-->
    <!--        </div>-->
    <!--      </div>-->
    <!--    </div>-->
  </div>
  <!-- <Customer ref="customerRef" /> -->
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
// import Customer from '@/pc/components/customer/customer.vue'
import GoodsDetailNewCard from '@/pc/components/goods-detail-card/goods-detail-card-new.vue'
import GoodsDetailCard from '@/pc/components/goods-detail-card/goods-detail-card.vue'
import { ossUrl } from '@/constants/common'
import { PRODUCT_SUGGEST_TYPE } from '@/constants/goods'
// import { CUSTOMER_SERVICE_PHONE_NUMBER } from '@/pc/constant'
import * as API from '@/apis/mall'
import { getSellerPage } from '@/apis/merchants'

const { isNation } = useNation()
const { t } = useI18n({
  messages: {
    zh: {
      hotPicks: '热门爆款',
      localGoods: isNation('idn') ? '地区专供' : '本地好货',
      topSellers: '实力商家',
      contact: '联系方式',
      customer: '联系客服',
    },
    en: {
      hotPicks: 'Hot Picks',
      localGoods: 'Local Goods',
      topSellers: 'Top Merchant',
      contact: 'Contact',
      customer: 'Customer',
    },
  },
})

/* 获取新的本地好货数据 */
const getGoodsList = async (labelType, callback) => {
  const res = await API.getNewHomeGoodsList({
    pageSize: 5,
    pageNum: 1,
    labelType,
  })
  if (res) {
    const { rowList } = res
    callback(rowList || [])
  }
}

// 1: '热门爆款',  2: '本地好货',   3: '为您推荐',
const hotList = ref([])
// getGoodsList(PRODUCT_SUGGEST_TYPE.HOT.id, (list) => (hotList.value = list.filter((item, i) => i < 5)))

const localList = ref([])
getGoodsList(PRODUCT_SUGGEST_TYPE.GOODS.id, (list) => (localList.value = list.filter((item, i) => i < 5)))

const shopList = ref([])
const getShopList = async () => {
  try {
    const { rowList } = await getSellerPage({
      pageSize: 5,
      pageNum: 1,
      suggest: 1,
    })
    if (rowList) {
      shopList.value = rowList?.filter((item, i) => i < 5) || []
    }
  } catch (e) {
    console.log(e)
  }
}
getShopList()

// const customerRef = ref(null)
// const showCustomer = (item) => {
//   customerRef.value.init(item)
// }

const router = useRouter()
const handleShop = (item) => {
  const { href } = router.resolve({
    name: 'shop',
    params: {
      id: item.id,
    },
  })
  window.open(href, '_blank')
}
</script>

<style lang="scss" scoped>
.goods-list {
  width: 100%;
  display: flex;
  flex-wrap: nowrap;
  overflow: hidden;

  :deep(.el-empty) {
    width: 100%;
  }
}

.title {
  font-family: PingFang SC;
  font-size: 20px;
  font-weight: 600;
  // color: #fff;

  &-icon {
    width: 30px;
    height: 30px;
  }
}

.single-module {
  border-radius: 16px;
  opacity: 1;
  background: #ffffff;
  color: #333333;
  // background: linear-gradient(180deg, #f91912 0%, #ffffff 100%);

  .goods-list {
    padding: 10px 16px 12px 16px;
    box-sizing: border-box;
  }

  .title {
    padding: 16px 16px 0;
    align-items: center;

    &-icon {
      margin-right: 4px;
    }
  }
}

[dir='rtl'] .offer-label {
  margin-right: 0;
  margin-left: 8px;
}

.row-module {
  width: 100%;
  justify-content: space-between;

  .title {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0 12px;
    width: 74px;
    height: 351px;
    color: #333;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.6);
    margin-right: 12px;

    &-icon {
      margin-bottom: 12px;
    }
  }

  &__item {
    width: 622px;
    padding: 16px 0 16px 20px;
    box-sizing: border-box;
    border-radius: 8px;
    background: linear-gradient(180deg, #f91912 0%, #ffffff 100%);
  }
}

.offer-title {
  height: 40px;
  line-height: 20px;
  font-size: 14px;
  color: $color-333333;
  @include ellipsis(2);
}

.customer-btn {
  @include ellipsis(1);
  font-size: 12px;
  border-radius: 2px;
  min-width: 58px;
  padding: 0 2px;
  line-height: 20px;
  text-align: center;
  color: $primary-color;
  box-sizing: border-box;
}
</style>
