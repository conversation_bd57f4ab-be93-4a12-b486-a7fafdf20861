<style lang="scss" scoped>
.top-wrapper {
  background: url('https://static.chinamarket.cn/static/trade-exhibition/gg-bg.png') no-repeat;
}
</style>

<template>
  <div class="w-268px rounded-4 bg-white h-[540px] overflow-hidden flex flex-col justify-between pb-5 box-border">
    <div class="top-wrapper flex-1 flex flex-col justify-between rounded-t-4 px-5 pt-5">
      <div class="mb-4 text-right cursor-pointer" @click="handleOpportunityAdd" v-if="opportunityNum">
        <img src="@/assets/imgs/mall/mall-fire-icon.png" alt="" class="w-[10px] mr-1" />
        <span
          >今日有<span class="text-[#D33232] font-600">{{ opportunityNum }}</span
          >人发布找货 <icon type="icon-xiala1"></icon
        ></span>
      </div>
      <div class="flex justify-center flex-col w-full flex-1">
        <!-- 未登录展示 -->
        <div v-if="!userStore.isLogined || userStore.userInfo?.userType !== 1">
          <div class="text-center mb-[12px]">
            <img src="https://static.chinamarket.cn/static/trade-exhibition/logo/logo-red.png?v=1" alt="" class="w-auto h-100px bg-cover" />
            <div class="font-semibold" :class="$storageLocale === 'zh' ? 'text-16px' : 'text-14px'">
              <div>欢迎来到</div>
              <div>中国大集商贸综合服务平台</div>
            </div>
          </div>
          <!--          <div class="mb-2 bg-[#D8131A] rounded-2px leading-8 text-center text-white text-14px cursor-pointer" v-enMode="'Login'" @click="onLogin">登录</div>-->
          <!--          <div-->
          <!--            class="border border-solid border-[#D8131A] rounded-2px leading-8 text-center text-[#D8131A] text-14px cursor-pointer"-->
          <!--            v-enMode="'Register'"-->
          <!--            @click="onLogin"-->
          <!--          >-->
          <!--            注册-->
          <!--          </div>-->
        </div>
        <!-- 登录展示 -->
        <div class="w-full" v-else>
          <div
            class="w-64px h-64px rounded-[50%] mb-10px m-auto bg-[url('https://static.chinamarket.cn/static/trade-exhibition/mall/guide-logo2.png')] bg-cover"
          ></div>
          <div
            class="truncate text-ellipsis overflow-hidden ... text-5 font-semibold text-center mb-10px"
            :title="userStore.userInfo?.companyName || userStore.userInfo?.userName"
          >
            Hi，{{ userStore.userInfo?.companyName || userStore.userInfo?.userName }}
          </div>
          <!-- <div class="text-center text-[14px]">新消息(<span class="text-[#D8131A]">0</span>)</div> -->
        </div>
      </div>
    </div>
    <!-- 新手入门 -->
    <div class="px-5 flex-1 flex flex-col justify-between">
      <div class="flex items-center mb-4">
        <!-- <img class="mr-1 w-4 h-4" src="https://static.chinamarket.cn/static/trade-exhibition/mall/guide-icon.png" alt=""> -->
        <span
          class="truncate text-ellipsis overflow-hidden ... max-w-100px text-[14px] text-[#666666] cursor-pointer mr-4 whitespace-nowrap text-ellipsis overflow-hidden"
          :class="{ 'text-[#D8131A] font-semibold underline underline-offset-4 decoration-2': tabsIndex === 0 }"
          title="通知公告"
          @click="tabsIndex = 0"
          >通知公告</span
        >
        <span
          class="truncate text-ellipsis overflow-hidden ... max-w-100px text-[14px] text-[#666666] cursor-pointer whitespace-nowrap text-ellipsis overflow-hidden"
          :class="{ 'text-[#D8131A] font-semibold underline underline-offset-4 decoration-2': tabsIndex === 1 }"
          title="新手入门"
          @click="tabsIndex = 1"
          >新手入门</span
        >
      </div>
      <div v-show="tabsIndex === 0">
        <div
          v-for="item in noticeList"
          :key="item.id"
          class="truncate text-ellipsis overflow-hidden ... mb-3 text-3 text-[#666666] cursor-pointer hover:text-[#D8131A] hover:decoration-underline"
          :title="item.title"
          @click="toNotice(item)"
        >
          {{ item.title }}
        </div>
      </div>
      <div v-show="tabsIndex === 1">
        <div
          v-for="item in ruleList"
          :key="item.id"
          class="truncate text-ellipsis overflow-hidden ... mb-3 text-3 text-[#666666] cursor-pointer hover:text-[#D8131A] hover:decoration-underline"
          :title="item.title"
          @click="toRules(item)"
        >
          {{ item.title }}
        </div>
      </div>
      <div>
        <div class="text-[14px] text-[#333333] mb-10px">热门功能</div>
        <div class="flex flex-wrap">
          <div
            v-for="item in funcList"
            :key="item.id"
            class="flex text-3 cursor-pointer text-[#999999] hover:text-[#D8131A] mb-2 whitespace-nowrap flex-1"
            :title="$storageLocale === 'en' ? item.lang[$storageLocale] : item.title"
            v-enMode="item.lang[$storageLocale]"
            @click="onFunPage(item)"
          >
            {{ item.title }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { useUserStore } from '@/pc/stores'
import { getOpportunityInfo } from '@/apis/goods'
import { getNoticeList } from '@/apis/notice'
import { useEvent } from '@/event'
import { OPEN_NEW_LOGIN } from '@/event/modules/site.js'

const userStore = useUserStore()
const event = useEvent()
const router = useRouter()

const ruleList = [
  { title: '大集平台信息发布标准', id: 'standards' },
  { title: '中国大集禁售商品细则', id: 'prohibited' },
  { title: '中国大集商家管理规则', id: 'merchantmanagement' },
  { title: '中国大集卖家服务协议', id: 'sellerrule' },
  { title: '个人身份入驻所需材料', id: 'userjoining' },
]

const funcList = [
  {
    title: '逛市场',
    lang: { en: 'Market' },
    path: '/market',
    id: 1,
  },
  {
    title: '大集哥',
    lang: { en: 'AI Brother' },
    path: '/ai-daji',
    id: 2,
  },
  {
    title: '外贸服务',
    lang: { en: 'Trade Service' },
    path: '/outside-trade-serve',
    id: 3,
  },
]

const tabsIndex = ref(0)

// 获取通知公告
const noticeList = ref([])
const getList = async () => {
  try {
    const body = {
      pageNum: 1,
      pageSize: 5,
    }
    const { rowList } = await getNoticeList(body)
    noticeList.value = rowList
  } catch (error) {
    console.log(error)
  }
}
getList()

// 跳转平台规则
const toRules = (item) => {
  router.push({
    path: '/rules',
    query: {
      id: item.id,
    },
  })
}

// 跳转通知公告 /notice-detail/
const toNotice = (item) => {
  router.push({
    path: `/notice-detail/${item.id}`,
  })
}

const onFunPage = (item) => {
  router.push({
    path: item.path,
  })
}

// 点击登录/注册
const onLogin = () => {
  event.emit(OPEN_NEW_LOGIN, {})
}

const opportunityNum = ref(0)
const getOpportunityNum = async () => {
  try {
    opportunityNum.value = await getOpportunityInfo()
  } catch (e) {
    console.log(e)
  }
}
const handleOpportunityAdd = () => {
  if (!userStore.isLogined) {
    onLogin()
  } else if (userStore.userInfo?.userType === 1) {
    router.push({
      name: 'myOpportunity',
    })
  } else {
    event.emit(OPEN_NEW_LOGIN, {})
  }
}
onMounted(() => {
  getOpportunityNum()
})

defineExpose({
  handleOpportunityAdd,
})
</script>
