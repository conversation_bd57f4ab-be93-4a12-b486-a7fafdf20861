<!-- 逛市场 -->
<style lang="scss" scoped>
.content-wrapper {
  background: $basic-white;
  padding-bottom: 60px;
}

.market-wrapper {
  min-width: 1260px;
}
</style>

<template>
  <div class="market-wrapper">
    <!-- banner -->
    <!-- <MarketBanner /> -->
    <div class="content-wrapper">
      <!-- 临沂介绍 -->
      <IntroductoryVideo></IntroductoryVideo>
      <!-- 商贸名城 -->
      <div ref="targetDiv"></div>
      <!-- <MarketAdvantage /> -->
      <!-- 导航 -->
      <MarketNav @onSelectMenu="onSelectMenu" ref="marketNavRef"></MarketNav>
      <!-- 市场介绍 -->
      <MarketIntroduce :markerInfo="markerInfo" :jumpUrlList="jumpUrlList"></MarketIntroduce>
      <!-- 实力商家 -->
      <!-- <TopShop /> -->
      <!-- 热销商品 -->
      <HotGoods ref="hotGoodsRef" :markerInfo="markerInfo" @onAnchorPointChange="onAnchorPointChange"></HotGoods>
      <!-- 交通指引 -->
      <!-- <TrafficGuidance /> -->
      <!-- 生活指引 -->
      <!-- <LifeGuide :markerInfo="markerInfo" /> -->
      <!-- 联系我们 -->
      <ContactUs></ContactUs>
    </div>
  </div>
</template>

<script setup>
import IntroductoryVideo from './components/Introductory-video.vue'
// import TopShop from './components/top-shop.vue'
// import TrafficGuidance from './components/traffic-guidance.vue'
import ContactUs from './components/contact-us.vue'
import HotGoods from './components/hot-goods.vue'
// import LifeGuide from './components/life-guide.vue'
// import MarketAdvantage from './components/market-advantage.vue'
// import MarketBanner from './components/market-banner.vue'
import MarketIntroduce from './components/market-introduce.vue'
import MarketNav from './components/market-nav.vue'
//import { MARKET_TYPE_ARRAY } from '@/constants/market'
import { getMarketUrlInfo } from '@/apis/goods.js'
import { marketInfo } from '@/apis/market.js'

// 获取市场简介
const markerInfo = ref(null)
const getMarketInfo = async (id) => {
  const res = await marketInfo({ marketId: id })
  markerInfo.value = res
}
const hotGoodsRef = ref(null)
const onSelectMenu = (data, pdata) => {
  getMarketInfo(data.id)
  let pname = pdata?.market || data.groupName
  hotGoodsRef.value.getGoodsList(pname || '')
}

const jumpUrlList = ref([])
const getJumpUrl = async () => {
  try {
    jumpUrlList.value = await getMarketUrlInfo()
  } catch (e) {
    console.log(e)
  }
}

const marketNavRef = ref(null)
const onAnchorPointChange = (index) => {
  marketNavRef.value.onAnchorPointChange(index)
}

//首页跳转到逛市场页面
const route = useRoute()
const targetDiv = ref(null)
onMounted(() => {
  if (route.query.scroll) {
    targetDiv.value.scrollIntoView({ behavior: 'smooth' })
  }
  getJumpUrl()
})
</script>
