<style lang="scss" scoped>
.title-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 56px;

  .title-text {
    margin: 0 16px;
    font-size: 24px;
    font-weight: 600;
    color: $color-333333;
  }

  .icon-left,
  .icon-right {
    width: 45px;
    height: 16px;
  }
  .icon-left {
    background: url('https://static.chinamarket.cn/static/trade-exhibition/zu-left.png') no-repeat;
    background-size: cover;
  }
  .icon-right {
    background: url('https://static.chinamarket.cn/static/trade-exhibition/zu-right.png') no-repeat;
    background-size: cover;
  }
}
</style>
<template>
  <div class="title-wrapper">
    <div class="icon-left"></div>
    <div class="title-text">{{ title }}</div>
    <div class="icon-right"></div>
    <slot />
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    default: '',
  },
})
</script>
