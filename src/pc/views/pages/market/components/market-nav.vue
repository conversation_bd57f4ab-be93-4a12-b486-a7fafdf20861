<style lang="scss" scoped>
.w-1260 {
  // display: flex;
  // flex-wrap: wrap;
  position: sticky;
  top: 64px;
  left: 0;
  z-index: 10;
  background: $basic-white;
  padding: 40px 0 0;
  // box-shadow: 0px 2px 20px 0px rgba(49, 0, 0, 0.1);
}
.active-shadow {
  box-shadow: 0px 2px 20px 0px rgba(43, 85, 170, 0.1);
}
.nav-wrapper {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.anchor-point-wrapper {
  display: flex;
  width: fit-content;
  height: 48px;
  margin: 0 auto;
  padding: 4px;
  border-radius: 8px;
  background: $color-F5F6F7;

  .anchor-point-item {
    padding: 0 16px;
    font-size: 14px;
    color: $color-666666;
    line-height: 40px;
    cursor: pointer;

    &.active,
    &:hover {
      // background: linear-gradient(270deg, #d8131a 0%, #fd441f 100%);
      background: #333333;
      color: $basic-white;
      border-radius: 4px;
      font-weight: 600;
    }
  }
}
.category-select__inner {
  display: flex;
  align-items: center;
  border: 1px solid #333333;
  border-radius: 8px;
  height: 46px;
  width: 176px;
}
.search-icon {
  flex-shrink: 0;
  font-size: 12px;
  color: #666;
  margin-right: 8px;

  &-after {
    font-size: 12px;
    color: #999;
    margin-left: 4px;
    cursor: pointer;
  }
}

.search-input {
  width: 100%;
  height: 100%;
  background: inherit;
  outline: none;
  border: none;
  box-shadow: none;
  color: #333;
  padding-left: 8px;
}

.select-list {
  width: 176px;
  position: absolute;
  z-index: 9;
  top: 75px;
  right: 0;
  background: #fff;
  border: 4px;
  box-sizing: border-box;
  // min-width: 100%;
  max-height: 300px;
  overflow: auto;
  box-shadow: 0 4px 10px 0 rgba(49, 0, 0, 0.1);

  .select-item {
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 14px;
    color: #333;
    transition: all 0.1s;
    cursor: pointer;

    @include ellipsis;

    &:hover {
      color: $primary-color;
      background-color: #ffeded;
    }
  }
}

[dir='rtl'] .category-select__inner {
  // direction: ltr;
  padding-right: 10px;
  .search-icon {
    margin-left: 10px;
  }
}
</style>
<template>
  <!-- navWrapperRectTop < 64 这个64是顶部导航栏的高度 :class="{ 'active-shadow': navWrapperRectTop < 64 }" -->
  <div class="w-1260" ref="tabsNavRef">
    <div class="nav-wrapper">
      <template v-for="(item, index) in NavList" :key="index">
        <MarkerSelectNav :item="item" @selectMenu="onSelectMenu"></MarkerSelectNav>
      </template>
      <div class="category-select__inner">
        <input v-model.trim="inputValue" type="text" class="search-input" :placeholder="t('queryCategory')" />
        <el-icon v-if="inputValue" class="search-icon-after" @click="inputValue = ''">
          <CircleClose></CircleClose>
        </el-icon>
        <el-icon class="search-icon" :size="24"><Search></Search></el-icon>
      </div>
      <div class="select-list" v-if="searchResultList.length">
        <div v-for="item in searchResultList" :key="item.id" class="select-item" :title="item.name" @click="onSelectMenu(item)">
          {{ item.name }}
        </div>
      </div>
    </div>
    <div class="anchor-point-wrapper">
      <div
        v-for="(item, index) in ANCHOR_POINT_NAV_ARRAY"
        :key="index"
        class="anchor-point-item"
        :class="{ active: anchorPointIndex === index }"
        @click="onAnchorPointChange(index)"
      >
        {{ item.name[$i18n.locale] }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { CircleClose, Search } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
import MarkerSelectNav from './marker-select-nav.vue'
import { ANCHOR_POINT_NAV_ARRAY } from '@/constants/market.js'
import { marketList } from '@/apis/market'
import { useEvent } from '@/event'
import { LANG_CHANGED } from '@/event/modules/site'

const { t } = useI18n({
  messages: {
    zh: {
      queryCategory: '快速检索',
    },
    en: {
      queryCategory: 'Search',
    },
  },
})

const event = useEvent()

const route = useRoute()
const emits = defineEmits(['onSelectMenu'])

const inputValue = ref('')
const searchResultList = computed(() => {
  const val = inputValue.value
  return val ? searchNavList.value.filter((item) => item.name.includes(val)) : []
})

// 检索列表
const searchNavList = ref([])
const NavList = ref([])

// 获取分类
const getMarketList = async () => {
  try {
    let res = await marketList()
    // 过滤文体和其他
    // res = res?.filter((item) => !['7', '8'].includes(item.industryType)) || []
    NavList.value = res
    searchNavList.value = []
    let arr = []
    res.map((item) => {
      arr = [...arr, ...item.marketList]
    })
    searchNavList.value = setArray(arr)
    if (route.query?.id) {
      onSelectMenu({ id: route.query?.id })
    } else {
      // 初始化进来默认取第一个分类的第一个菜单
      onSelectMenu(NavList.value[0].marketList[0] || '1', NavList.value[0])
    }
  } catch (error) {
    console.log(error)
  }
}
getMarketList()
//数组去重
const setArray = (arr) => {
  let newarr = []
  let iarr = []
  arr.map((item) => {
    if (iarr.indexOf(item.id) == -1) {
      newarr.push(item)
      iarr.push(item.id)
    }
  })
  return newarr
}
const onSelectMenu = (data, pdata) => {
  if (inputValue.value) {
    inputValue.value = ''
  }
  emits('onSelectMenu', data, pdata)
}

// 点击 tabs 禁止监听滚动
const isScroll = ref(true)

const tabsNavRef = ref(null)
// 锚点导航
const anchorPointIndex = ref(0)
const onAnchorPointChange = (index) => {
  isScroll.value = false
  anchorPointIndex.value = index
  const inner = document.getElementsByClassName('jump-item')
  // 获取元素的计算后的样式
  const style = window.getComputedStyle(inner[index])
  window.scrollTo({
    top: inner[index].offsetTop - parseInt(style.paddingTop) - tabsNavRef.value.offsetHeight,
    behavior: 'smooth',
  })
  setTimeout(() => {
    isScroll.value = true
  }, 1000)
}

// 吸顶元素距离顶部的高度
const navWrapperRectTop = ref(0)

const handleScroll = () => {
  const inner = document.getElementsByClassName('jump-item')
  const rect = tabsNavRef.value.getBoundingClientRect()
  navWrapperRectTop.value = rect.top

  if (!isScroll.value) return
  let scrollTop = document.documentElement.scrollTop || document.body.scrollTop
  for (let i = 0; i < inner.length; i++) {
    // 这里的 175 = 吸顶元素的高度 + inner的paddingTop
    if (scrollTop >= inner[i].offsetTop - 175 && scrollTop <= inner[i].offsetTop + inner[i].scrollHeight + 175) {
      anchorPointIndex.value = i
    }
  }
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})
onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})

event.on(LANG_CHANGED, async () => {
  await getMarketList()
})

defineExpose({
  onAnchorPointChange,
})
</script>
