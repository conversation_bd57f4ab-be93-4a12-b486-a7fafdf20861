<template>
  <!-- 县/区信息展示 -->
  <div
    class="info-show-wrapper"
    :class="{
      'bg-right': distinguishType === DISTRICT_LIST.HE_DONG.id,
      'bg-top': [COUNTY_LIST.MYX.id, COUNTY_LIST.PYX.id].includes(distinguishType),
      'bg-lanshan': distinguishType === DISTRICT_LIST.LAN_SHAN.id,
      'bg-hd': distinguishType === DISTRICT_LIST.HE_DONG.id,
    }"
    :style="infoStyle"
  >
    <div class="name">{{ infoData.name[$i18n.locale] }}</div>
    <Tooltip
      :tooltipText="`${$t('market.relevance')}：${infoData.containMarket[$i18n.locale]}`"
      :placement="infoData.placement"
      :disabled="infoData.containMarket.length < 50"
    />
    <Tooltip :tooltipText="`${$t('market.bussScope')}：${infoData.content[$i18n.locale] || '-'}`" :disabled="infoData.content.length < 50" />
    <!-- <div class="info">{{ $t('market.bussScope') }}：{{ infoData.content[$i18n.locale] || '-' }}</div> -->
    <!-- 县城不展示下面三项 -->
    <template v-if="!infoData.isCounty">
      <div class="info">{{ $t('market.shop') }}：{{ infoData.shopsCount || '-' }}</div>
      <div class="info">{{ $t('market.area') }}：{{ infoData.area || '-' }} {{ $t('market.wan') }} {{ $t('market.squareMeter') }}</div>
      <div class="info">{{ $t('market.GMV') }}：{{ infoData.yearOrderAmount || '-' }} {{ $t('market.wan') }} {{ $t('market.yuan') }}</div>
    </template>
  </div>
</template>

<script setup>
import { COUNTY_LIST, DISTRICT_LIST } from '../data-options'

defineProps({
  infoStyle: {
    type: Object,
    default: () => ({
      top: 0,
      left: 0,
    }),
  },
  // 根据区县标识，展示不同的背景
  distinguishType: {
    type: String,
    default: '',
  },
  // 展示区县信息
  infoData: {
    type: Object,
    default: () => ({}),
  },
})
</script>

<style lang="scss" scoped>
// 区县展示弹窗样式
.info-show-wrapper {
  width: 433px;
  height: 291px;
  background: url('https://static.chinamarket.cn/static/trade-exhibition/info-bg.png') no-repeat;
  background-size: cover;
  position: absolute;
  padding: 10px 0 0 70px;
  z-index: 10;

  &.bg-right {
    background: url('https://static.chinamarket.cn/static/trade-exhibition/info-bg-right.png') no-repeat;
    background-size: cover;
    padding: 40px 55px 0 28px;
  }

  &.bg-top {
    background: url('https://static.chinamarket.cn/static/trade-exhibition/info-bg-top.png') no-repeat;
    background-size: cover;
    padding-top: 35px;
  }

  &.bg-lanshan {
    background: url('https://static.chinamarket.cn/static/trade-exhibition/info-bg-lanshan.png') no-repeat;
    background-size: cover;
    padding: 45px 20px 0 82px;
  }

  &.bg-hd {
    background: url('https://static.chinamarket.cn/static/trade-exhibition/info-bg-hd.png') no-repeat;
    background-size: cover;
    padding-top: 45px;
  }

  .name {
    font-size: 20px;
    font-weight: 600;
    color: $basic-white;
    margin-bottom: 24px;
  }

  .info {
    color: $basic-white;
    font-size: 14px;
    margin-bottom: 8px;
    position: relative;

    &.info-range {
      width: 350px;
      @include ellipsis;
    }
  }
}
</style>
