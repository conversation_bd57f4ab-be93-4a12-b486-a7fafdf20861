<!-- 市场介绍 -->
<style lang="scss" scoped>
.w-1260 {
  padding: 16px 0 56px;
  display: flex;

  .img-wrapper {
    flex: 0 0 780px;
    // height: 520px;
    border-radius: 8px 0 0 8px;
    height: 505px;
    :deep(img) {
      width: 100%;
      height: 100%;
      border-radius: 8px 0 0 8px;
      object-fit: cover;
    }
  }

  .introduce-wrapper {
    padding: 58px 48px 0;
    background: $color-F5F6F7;
    border-radius: 0 8px 8px 0;
    position: relative;
    height: 505px;
    overflow: hidden;
    overflow-y: auto;

    .label-wrapper {
      position: absolute;
      left: 0;
      top: 0;
      height: 32px;
      opacity: 1;
      background: url('https://static.chinamarket.cn/static/trade-exhibition/decoration-bg.png') no-repeat;
      background-size: 100% 100%;
      font-size: 16px;
      color: $color-EDD1A8;
      padding: 0 16px;
      line-height: 32px;
      @include ellipsis;
    }
    .number-wrapper {
      font-size: 14px;
      color: $color-333333;
      margin-bottom: 16px;

      .storange-text {
        font-size: 30px;
        font-weight: 600;
      }
      .square-meter {
        margin-right: 20px;
      }
    }
    .introduce-text {
      margin-bottom: 24px;
      font-size: 16px;
      color: $color-666666;
    }

    .title-text {
      margin-bottom: 20px;
      min-width: 64px;
      font-size: 16px;
      color: $color-333333;
      padding: 2px 0;
      position: relative;
      &::before,
      &::after {
        content: '';
        position: absolute;
        left: 0;
        width: 100%;
        width: 64px;
        height: 1px;
        background: linear-gradient(270deg, rgba(216, 19, 26, 0) 0%, #d8131a 50%, rgba(216, 19, 26, 0) 100%);
      }
      &::before {
        top: 0;
      }
      &::after {
        bottom: 0px;
      }
      &.en-active {
        //
        &::before,
        &::after {
          width: 197px;
        }
      }
    }
    .service-info {
      margin-bottom: 12px;
      font-size: 14px;
      color: $color-666666;
      display: flex;
      .open-ai {
        margin-left: 4px;
        cursor: pointer;
      }
    }
  }
}

[dir='rtl'] .number-wrapper {
  direction: ltr;
  text-align: right;

  .square-meter {
    margin-right: 0 !important;
  }
  .square-meter-mr-20 {
    margin-right: 20px;
  }
}
[dir='rtl'] .title-text {
  &::before,
  &::after {
    width: 110px !important;
    right: 0;
  }
}
</style>

<template>
  <div class="w-1260 jump-item">
    <div class="img-wrapper relative">
      <template v-if="markerInfo?.pic">
        <img-loader :src="markerInfo.pic" :loading-img="`${markerInfo.pic}?x-oss-process=image/resize,h_100`" alt="暂无图片" class="w-[100%]"></img-loader>
      </template>
      <template v-else> 暂无图片 </template>
      <a class="jump-btn" v-if="jumpItem" :href="jumpItem.url" target="_blank">进入市场官网</a>
    </div>
    <div class="introduce-wrapper scrollbar">
      <div class="label-wrapper" :title="markerInfo?.name">
        {{ markerInfo?.name }}
      </div>
      <div class="number-wrapper">
        <template v-if="markerInfo?.area">
          <span class="storange-text">{{ markerInfo?.area }}</span>
          <span class="square-meter">{{ $t('market.squareMeter') }}</span>
        </template>
        <template v-if="markerInfo?.merchantCount">
          <span class="storange-text">{{ markerInfo?.merchantCount || '-' }}</span>
          <span class="square-meter-mr-20">{{ $t('market.shop') }}</span>
        </template>
      </div>
      <p class="introduce-text">
        {{ markerInfo?.description }}
      </p>
      <div class="title-text" :class="{ 'en-active': $storageLocale === 'en' }">{{ $t('market.contact') }}</div>
      <div class="service-info">{{ $t('market.supervisor') }}: {{ markerInfo?.serviceManager || '-' }}</div>
      <!-- <div class="service-info">{{ $t('market.consult') }} : {{ markerInfo?.servicePhone || '-' }}</div> -->
      <div class="service-info">
        {{ $t('market.consult') }}: <span class="open-ai" @click="openAiWindow"> {{ aiPath }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useEvent } from '@/event'
import { OPEN_AI_WINDOW } from '@/event/modules/site'

const aiPath = window.location.href + '/openAi'
const props = defineProps({
  markerInfo: {
    type: Object,
    default: () => ({}),
  },
  jumpUrlList: {
    type: Array,
    default: () => [],
  },
})

const jumpItem = computed(() => {
  return props.jumpUrlList.find((item) => {
    return item.id === props.markerInfo?.id
  })
})

const event = useEvent()
const openAiWindow = () => {
  event.emit(OPEN_AI_WINDOW)
}
</script>

<style scoped lang="scss">
.jump-btn {
  position: absolute;
  bottom: 24px;
  left: 50%;
  transform: translateX(-50%);
  min-width: 140px;
  height: 32px;
  padding: 0 12px;
  border-radius: 8px;
  background: linear-gradient(270deg, #d8131a 0%, #e96f18 100%);
  box-sizing: border-box;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}
</style>
