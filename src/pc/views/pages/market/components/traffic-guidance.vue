<!-- 交通指引 -->
<style lang="scss" scoped>
.w-1260 {
  padding-top: 60px;
}
.content-wrapper {
  padding-top: 24px;

  .classification-box {
    height: 81px;
    position: relative;
    border-radius: 2px;
    background: url('https://static.chinamarket.cn/static/trade-exhibition/zs-bg.png') rgba(216, 19, 26, 0.03);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    padding: 16px;
    display: flex;
    align-items: center;
    color: $color-333333;

    .icon-box {
      width: 48px;
      height: 48px;
      background: url('https://static.chinamarket.cn/static/trade-exhibition/self-driving-icon.png') no-repeat;
      background-size: cover;
      margin-right: 12px;

      &.parking {
        background: url('https://static.chinamarket.cn/static/trade-exhibition/parking-icon.png') no-repeat;
        background-size: cover;
      }
    }
    .title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 4px;
    }
    .tips {
      font-size: 14px;
    }
  }

  .self-driving-wrapper {
    margin-bottom: 24px;
  }

  .self-driving-item-box {
    display: flex;
    padding: 24px 0;
    &:not(:last-child) {
      border-bottom: 1px solid $color-E6E9ED;
    }

    .self-drivi-item {
      flex: 1;

      .parking-lot-name {
        margin-bottom: 16px;
        font-size: 18px;
        color: $color-333333;
      }
      .parking-lot-info {
        display: flex;
        .label {
          flex: 0 0 90px;
        }
        .value {
          flex: 1;
          @include ellipsis(2);
        }

        &:not(:last-child) {
          margin-bottom: 12px;
        }
      }

      &:not(:last-child) {
        margin-right: 24px;
      }
    }
  }
}
// 接驳线路样式
.connecting-wrapper {
  padding-top: 24px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;

  .title {
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 600;
    color: $color-333333;
  }
}
</style>
<template>
  <div class="w-1260 jump-item">
    <MarketTitle title="交通指引" />
    <div class="content-wrapper">
      <!-- 自驾 -->
      <div class="classification-box">
        <div class="icon-box"></div>
        <div>
          <div class="title">自驾指南</div>
          <div class="tips">商贸城设有多个停车场，请按需前往</div>
        </div>
      </div>
      <div class="self-driving-wrapper">
        <!-- <div class="self-driving-item-box">
          <div class="self-drivi-item">
            <div class="parking-lot-name">东扩内环停车场</div>
            <div class="parking-lot-info">
              <div class="label">参考费用：</div>
              <div class="value">0-120分钟免费，121-180分钟收费5元，181分钟-240分钟收费10元，240分钟-300分钟收费15元，301分钟</div>
            </div>
            <div class="parking-lot-info">
              <div class="label">营业时间：</div>
              <div class="value">全天可停放</div>
            </div>
          </div>
          <div class="self-drivi-item">
            <div class="parking-lot-name">G区楼道停车场</div>
            <div class="parking-lot-info">
              <div class="label">参考费用：</div>
              <div class="value">0-120分钟免费，121-180分钟收费5元，181分钟-240分钟收费10元，240分钟-300分钟收费15元，301分钟</div>
            </div>
            <div class="parking-lot-info">
              <div class="label">营业时间：</div>
              <div class="value">全天可停放</div>
            </div>
          </div>
          <div class="self-drivi-item">
            <div class="parking-lot-name">西扩内环停车场</div>
            <div class="parking-lot-info">
              <div class="label">参考费用：</div>
              <div class="value">0-120分钟免费，121-180分钟收费5元，181分钟-240分钟收费10元，240分钟-300分钟收费15元，301分钟</div>
            </div>
            <div class="parking-lot-info">
              <div class="label">营业时间：</div>
              <div class="value">全天可停放</div>
            </div>
          </div>
        </div>
        <div class="self-driving-item-box">
          <div class="self-drivi-item">
            <div class="parking-lot-name">东扩内环停车场</div>
            <div class="parking-lot-info">
              <div class="label">参考费用：</div>
              <div class="value">0-120分钟免费，121-180分钟收费5元，181分钟-240分钟收费10元，240分钟-300分钟收费15元，301分钟</div>
            </div>
            <div class="parking-lot-info">
              <div class="label">营业时间：</div>
              <div class="value">全天可停放</div>
            </div>
          </div>
          <div class="self-drivi-item">
            <div class="parking-lot-name">G区楼道停车场</div>
            <div class="parking-lot-info">
              <div class="label">参考费用：</div>
              <div class="value">0-120分钟免费，121-180分钟收费5元，181分钟-240分钟收费10元，240分钟-300分钟收费15元，301分钟</div>
            </div>
            <div class="parking-lot-info">
              <div class="label">营业时间：</div>
              <div class="value">全天可停放</div>
            </div>
          </div>
          <div class="self-drivi-item">
            <div class="parking-lot-name">西扩内环停车场</div>
            <div class="parking-lot-info">
              <div class="label">参考费用：</div>
              <div class="value">0-120分钟免费，121-180分钟收费5元，181分钟-240分钟收费10元，240分钟-300分钟收费15元，301分钟</div>
            </div>
            <div class="parking-lot-info">
              <div class="label">营业时间：</div>
              <div class="value">全天可停放</div>
            </div>
          </div>
        </div> -->
        <!-- 暂无数据 -->
        <EmptyText />
      </div>
      <!-- 停车场 -->
      <div class="classification-box">
        <div class="icon-box parking"></div>
        <div>
          <div class="title">停车场接驳线</div>
          <div class="tips">商贸城各区停车场，设有多条停车接驳线路，推荐优先前往。</div>
        </div>
      </div>
      <div class="connecting-wrapper">
        <div class="">
          <div class="title">接驳专线一</div>
          <CTable :cols="cols" :data="tableData" theadBgColor="linear-gradient(270deg, #FC787D 0%, #D8131A 100%)" />
        </div>
        <div>
          <div class="title">接驳专线二</div>
          <CTable :cols="cols" :data="tableData" theadBgColor="linear-gradient(270deg, #FC787D 0%, #D8131A 100%)" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import EmptyText from '@/pc/components/empty-text/empty-text.vue'
import MarketTitle from './market-title.vue'

const cols = ref([
  {
    label: '运营时间',
    prop: 'time',
  },
  {
    label: '上客地点（始发站）',
    prop: 'aaaa',
    icon: 'icon-shi',
    iconSize: 20,
  },
  {
    id: '3',
    label: '上客地点（下车地点）',
    prop: 'bbbb',
    icon: 'icon-zhong',
    iconSize: 20,
  },
])
const tableData = [
  // { time: '06:00-18:30', aaaa: '城北汽车站', bbbb: '万泰商贸城', cccc: '11111' },
]
</script>
