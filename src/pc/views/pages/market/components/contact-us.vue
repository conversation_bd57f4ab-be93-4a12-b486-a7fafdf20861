<style lang="scss" scoped>
.w-1260 {
  padding-top: 60px;
}

.contact-wrapper {
  display: flex;

  .contact-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
  }

  .left,
  .right {
    background: #f9f9fa;
  }
  .left {
    flex: 0 0 279px;
    margin-right: 16px;
    padding: 20px 16px;
  }
  .right {
    flex: 1;
    padding: 20px;

    .contact-title {
      margin-bottom: 8px;
    }
  }
  .code-box {
    width: 120px;
    height: 120px;
    background: url('https://static.chinamarket.cn/static/trade-exhibition/contact-qr-code.png') no-repeat;
    background-size: cover;
    margin-bottom: 20px;
  }
}

:deep() {
  .el-textarea__inner,
  .el-input__wrapper {
    box-shadow: none;
    border-radius: 2px 0px 0px 2px;
  }
}
[dir='rtl'] {
  .mr-1 {
    margin-left: 4px;
    margin-right: 0;
  }

  .mr-2 {
    margin-left: 8px;
    margin-right: 0;
  }

  .mr-\[10px\] {
    margin-left: 10px;
    margin-right: 0;
  }
}
</style>

<template>
  <div class="w-1260 jump-item">
    <MarketTitle title="联系我们" />
    <div class="contact-wrapper">
      <div class="left">
        <div class="contact-title">在线客服</div>
        <div class="code-box"></div>
        <div class="contact-title">联系电话</div>
        <div class="text-[#666666] text-[14px] mb-5 flex">
          <icon type="icon-lianxifangshi" class="mr-1" :size="20"></icon>
          <div>
            <div>+8619860959205</div>
            <div class="text-[#999999]">(工作日8:30-18:00)</div>
          </div>
        </div>
        <div class="contact-title">办公地址</div>
        <div class="flex text-[#666666]">
          <Icon type="icon-dizhi" class="mr-1 float-left" :size="20" />
          <span class="flex-1 text-[14px] float-left">山东省临沂市兰山区兰山街道临沂商谷智慧产业园C1号楼407室</span>
        </div>
      </div>
      <div class="right">
        <div class="contact-title">留言</div>
        <el-input v-model="formData.leaveMessage" class="mb-5" :rows="6" show-word-limit maxlength="200" type="textarea" placeholder="请输入您的需求" />
        <div class="flex mb-5">
          <div class="flex-1 mr-[10px]">
            <div class="contact-title">手机号</div>
            <el-input v-model="formData.tel" size="large" placeholder="请输入手机号码" />
          </div>
          <div class="flex-1">
            <div class="contact-title">验证码</div>
            <div class="flex" :class="{ 'justify-between': leftSeconds === 0 }">
              <el-input
                v-model="formData.verifyCode"
                class="flex flex-1 mr-2"
                maxlength="6"
                size="large"
                placeholder="请输入验证码"
                @input="(e) => handleInput(e, 'verifyCode')"
              />
              <el-button
                type="primary"
                :class="{ 'skip-translate': leftSeconds > 0 }"
                :style="{ with: 'max-content', borderRadius: '2px' }"
                size="large"
                :disabled="leftSeconds > 0"
                @click="sendCode"
              >
                {{ leftSeconds > 0 ? `${leftSeconds}s` : '发送验证码' }}
              </el-button>
            </div>
          </div>
        </div>
        <div>
          <div class="h-12 w-50 bg-#D8131A text-[#FFFFFF] font-semibold text-4 rounded-[2px] text-center leading-12 cursor-pointer" @click="onSubmit">提交</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import MarketTitle from './market-title.vue'
import { SMS_SEND_WAY } from '@/constants'
import { leaveMessage, smsSend } from '@/apis/common.js'

const formData = ref({
  leaveMessage: '',
  tel: '',
  verifyCode: '',
})

// 倒计时
const leftSeconds = ref(0)
const timer = ref(null)
const startCountDown = () => {
  leftSeconds.value = 60
  const countDown = () => {
    timer.value = setTimeout(() => {
      leftSeconds.value -= 1
      leftSeconds.value > 0 && countDown()
    }, 1000)
  }
  countDown()
}
// 点击获取验证码
const sendCode = async () => {
  const body = {
    account: formData.value.tel,
    prefixId: '2',
    type: SMS_SEND_WAY.LEAVE_MESSAGE.id,
    userType: -1,
  }
  try {
    await smsSend(body)
    ElMessage({
      message: '发送成功',
      type: 'success',
    })
    startCountDown()
  } catch (error) {
    formData.value.smsCode = ''
  }
}

// 限制只能输入数字
const handleInput = (val, key) => {
  formData.value[key] = val.replace(/\D/g, '')
}
// 点击提交
const onSubmit = async () => {
  await leaveMessage({ entry: 'HOME_PAGE', ...formData.value })
  ElMessage.success('提交成功')
  formData.value.leaveMessage = ''
  formData.value.tel = ''
  formData.value.verifyCode = ''
}
</script>
