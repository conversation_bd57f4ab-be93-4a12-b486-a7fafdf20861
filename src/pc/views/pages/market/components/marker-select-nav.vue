<!-- 选择下拉导航 -->
<style lang="scss" scoped>
.popover-wrapper {
  &:hover {
    .bottom-line {
      display: block;
    }
    .nav-title {
      color: #000000;
      font-size: 16px;
      font-weight: 600;
    }
  }
}
.nav-item {
  padding: 0 16px 6px;
  display: flex;
  align-items: center;
  flex: 1;
  box-sizing: border-box;
  position: relative;

  .bottom-line {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 2px;
    background: #000000;
    display: none;
  }

  .nav-title {
    max-width: 164px;
    font-size: 16px;
    color: $color-333333;
    cursor: pointer;
    margin-right: 12px;
    @include ellipsis;
  }
}
.item-nav-wrapper {
  overflow: scroll;
  overflow-x: hidden;
  max-height: 300px;
  border-radius: 4px;
  box-shadow: 0px 4px 10px 0px rgba(49, 0, 0, 0.1);

  .item-nav-content {
    position: relative;
    width: 216px;
    background: $color-F5F6F7;
    padding: 4px 0;

    .icon-sanj {
      position: absolute;
      left: 0;
      right: 0;
      top: -13px;
      margin: auto;
    }

    .item-nav-text {
      width: 216px;
      border-radius: 4px;
      padding: 9px 12px;
      font-size: 16px;
      color: $color-333333;
      cursor: pointer;
      @include ellipsis;

      &:hover {
        color: $basic-white;
        background: $primary-color;
      }
    }
  }
}
.popover-content-wrapper {
  top: 36px !important;
}
</style>

<template>
  <Popover ref="popoverRef" placement="bottom" :bottomDistance="30" :customLeft="item.left">
    <template #reference>
      <div class="nav-item">
        <div v-if="$i18n.fallbackLocale === 'zh'" class="nav-title">{{ item.market }}</div>
        <div v-else class="nav-title w-30">{{ item.market.slice(0, 4) }}</div>
        <Icon type="icon-down" size="12" />
        <div class="bottom-line"></div>
      </div>
    </template>
    <div class="item-nav-wrapper scrollbar">
      <div class="item-nav-content">
        <Icon type="icon-sanj" :size="20" />
        <div v-for="menu in item.marketList" :key="menu.id" class="item-nav-text" :title="menu.name" @click="onSelectMenu(menu, item)">
          {{ menu.name }}
        </div>
      </div>
    </div>
  </Popover>
</template>

<script setup>
const emits = defineEmits(['selectMenu'])
const popoverRef = ref(null)
defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
})

const onSelectMenu = (menu, pdata) => {
  emits('selectMenu', menu, pdata)
  popoverRef.value?.onClose()
}
</script>
