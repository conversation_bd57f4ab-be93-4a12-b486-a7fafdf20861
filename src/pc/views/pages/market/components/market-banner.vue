<template>
  <div class="market-banner-wrapper">
    <!-- 县导航 -->
    <div class="county-wrapper">
      <div
        v-for="(item, index) in COUNTY_LIST_ARRAY"
        :key="item.id"
        class="county-item"
        :class="{ active: countyIndex === index }"
        @click="(e) => onCountyChange(e, item, index)"
      >
        {{ item.name[$i18n.locale] }}
        <CountyInfo v-if="countyIndex === index" :infoStyle="infoStyle" :distinguishType="distinguishType" :infoData="item" />
      </div>
    </div>
    <!-- 兰山区 -->
    <div class="lanshan-distinguish" :class="{ active: distinguishIndex === 1 }" @click="onDistinguish(1, DISTRICT_LIST.LAN_SHAN.id)">
      <CountyInfo v-if="distinguishIndex === 1" :infoStyle="infoStyle" :distinguishType="distinguishType" :infoData="DISTRICT_LIST.LAN_SHAN" />
    </div>
    <!-- 罗庄区 -->
    <div class="luozhuang-distinguish" :class="{ active: distinguishIndex === 2 }" @click="onDistinguish(2, DISTRICT_LIST.LUO_ZHUANG.id)">
      <CountyInfo v-if="distinguishIndex === 2" :infoStyle="infoStyle" :distinguishType="distinguishType" :infoData="DISTRICT_LIST.LUO_ZHUANG" />
    </div>
    <!-- 河东区 -->
    <div class="hedong-distinguish" :class="{ active: distinguishIndex === 3 }" @click="onDistinguish(3, DISTRICT_LIST.HE_DONG.id)">
      <CountyInfo v-if="distinguishIndex === 3" :infoStyle="infoStyle" :distinguishType="distinguishType" :infoData="DISTRICT_LIST.HE_DONG" />
    </div>
  </div>
</template>

<script setup>
import { useWindowSize } from '@vueuse/core'
import CountyInfo from './county-info.vue'
import { COUNTY_LIST_ARRAY, DISTRICT_LIST } from '../data-options'

const { width } = useWindowSize()

// 记录区/县表示，展示不同的背景图
const distinguishType = ref('LAN_SHAN')

// 县城 index
const countyIndex = ref(-1)
const infoStyle = ref({
  left: width.value > 1920 ? '525px' : '385px',
  top: width.value > 1920 ? '540px' : '385px',
})
// 县城点击
const onCountyChange = (e, item, index) => {
  countyIndex.value = index
  distinguishType.value = item.id
  distinguishIndex.value = -1
  if (index === 0 || index === 1) {
    infoStyle.value.left = '227px'
    infoStyle.value.top = '50px'
  } else {
    infoStyle.value.left = '235px'
    infoStyle.value.top = '-273px'
  }
}
// 区点击
const distinguishIndex = ref(1)
const onDistinguish = (n, type) => {
  countyIndex.value = -1
  distinguishIndex.value = n
  distinguishType.value = type
  if (width.value > 1920) {
    if (n === 1) {
      infoStyle.value.left = '525px'
      infoStyle.value.top = '540px'
    } else if (n === 2) {
      infoStyle.value.left = '250px'
      infoStyle.value.top = '-265px'
    } else if (n === 3) {
      infoStyle.value.left = '-360px'
      infoStyle.value.top = '330px'
    }
  } else {
    if (n === 1) {
      infoStyle.value.left = '385px'
      infoStyle.value.top = '385px'
    } else if (n === 2) {
      infoStyle.value.left = '210px'
      infoStyle.value.top = '-265px'
    } else if (n === 3) {
      infoStyle.value.left = '-380px'
      infoStyle.value.top = '260px'
    }
  }
}
</script>

<style lang="scss" scoped>
.market-banner-wrapper {
  width: 100%;
  max-width: 2560px;
  height: 1080px;
  background: url('https://static.chinamarket.cn/static/trade-exhibition/market-bg-new.jpg') no-repeat #f9f8f9;
  background-size: 2560px 100%;
  background-position: center center;
  padding-top: 93px;
  position: relative;
  // 县样式
  .county-wrapper {
    position: absolute;
    left: 80px;
    top: 188px;
    z-index: 10;

    .county-item {
      width: 209px;
      height: 68px;
      background-image: url('https://static.chinamarket.cn/static/trade-exhibition/county-min-bg.png');
      background-size: auto 100%;
      background-repeat: no-repeat;
      margin-bottom: 22px;
      padding: 18px 0 0 89px;
      font-size: 24px;
      font-weight: 600;
      color: $basic-white;
      cursor: pointer;
      position: relative;

      &.active,
      &:hover {
        width: 249px;
        background-image: url('https://static.chinamarket.cn/static/trade-exhibition/county-max-bg.png');
      }
    }
  }
  .lanshan-distinguish,
  .luozhuang-distinguish,
  .hedong-distinguish {
    position: absolute;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  // 兰山区
  .lanshan-distinguish {
    width: 981px;
    height: 574px;
    left: 460px;
    top: 105px;
    background: url('https://static.chinamarket.cn/static/trade-exhibition/d.gif') no-repeat;
    background-size: 86px 86px;
    background-position: 140px 100px;

    &.active {
      background: url('https://static.chinamarket.cn/static/trade-exhibition/d-lanshan-bg.png') no-repeat;
      background-size: 100% 100%;
    }
  }
  // 罗庄区
  .luozhuang-distinguish {
    width: 1112px;
    height: 324px;
    left: 277px;
    bottom: 20px;
    background: url('https://static.chinamarket.cn/static/trade-exhibition/d.gif') no-repeat;
    background-size: 86px 86px;
    background-position: 140px 100px;
    &.active {
      background: url('https://static.chinamarket.cn/static/trade-exhibition/d-luozhuang-bg.png') no-repeat;
      background-size: 100% 100%;
    }
  }
  // 河东区
  .hedong-distinguish {
    width: 496px;
    height: 567px;
    right: 240px;
    top: 392px;
    background: url('https://static.chinamarket.cn/static/trade-exhibition/d.gif') no-repeat;
    background-size: 86px 86px;
    background-position: 140px 100px;
    &.active {
      background: url('https://static.chinamarket.cn/static/trade-exhibition/d-hedong-bg.png') no-repeat;
      background-size: cover;
    }
  }
}

@media screen and (max-width: 1920px) {
  .market-banner-wrapper {
    background: url('https://static.chinamarket.cn/static/trade-exhibition/market-bg-new.jpg') no-repeat #f9f8f9;
    background-size: 1920px 100%;
    background-position: center center;

    // 兰山区
    .lanshan-distinguish {
      width: 817px;
      height: 478px;
      top: 180px;
      left: 500px;
      &.active {
        background-size: 100% 100% !important;
      }
    }
    // 罗庄区
    .luozhuang-distinguish {
      width: 926px;
      height: 270px;
      bottom: 20px;
      left: 350px;
      &.active {
        background-size: 100% 100% !important;
      }
    }
    // 河东区
    .hedong-distinguish {
      width: 413px;
      height: 472px;
      top: 245px;
      right: 20px;
      &.active {
        background-size: 100% 100% !important;
      }
    }
  }
}

@media screen and (min-width: 1921px) {
  .market-banner-wrapper {
    background: url('https://static.chinamarket.cn/static/trade-exhibition/market-bg-new.jpg') no-repeat #f9f8f9;
    background-size: 2560px 100%;
    background-position: center center;
    // 兰山区
    .lanshan-distinguish {
      background-position: 260px 200px;
    }
    // 罗庄区
    .luozhuang-distinguish {
      left: 400px;
      &.active {
        background-size: 100% 100% !important;
      }
    }
    // // 河东区
    .hedong-distinguish {
      width: 413px;
      height: 472px;
      top: 245px;
      right: 50px;
      &.active {
        background-size: 100% 100% !important;
      }
    }
  }
}

@media screen and (max-width: 1440px) {
  .market-banner-wrapper {
    background: url('https://static.chinamarket.cn/static/trade-exhibition/market-bg-new.jpg') no-repeat #f9f8f9;
    background-size: 1920px 100%;
    background-position: center center;

    // 兰山区
    .lanshan-distinguish {
      width: 700px;
      height: 410px;
      top: 218px;
      left: 300px;
      background-position: 200px 120px;
      &.active {
        background-size: 100% 100% !important;
      }
    }
    // 罗庄区
    .luozhuang-distinguish {
      width: 741px;
      height: 216px;
      &.active {
        background-size: 100% 100% !important;
      }
    }
    // 河东区
    .hedong-distinguish {
      width: 304px;
      height: 378px;
      top: 300px;
      right: 50px;
      &.active {
        background-size: 100% 100% !important;
      }
    }
  }
}
</style>
