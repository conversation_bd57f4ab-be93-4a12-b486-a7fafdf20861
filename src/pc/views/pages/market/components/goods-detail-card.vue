<!--
 * @Author: 李兵 <EMAIL>
 * @Date: 2024-08-13 14:59:44
 * @LastEditors: your name
 * @LastEditTime: 2024-09-06 14:08:17
 * @FilePath: /trade-exhibition/src/pc/views/pages/mall/components/goods-detail-card/goods-detail-card.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%A
-->
<template>
  <div class="rec-offer" @click="GoodsDetail(goodsInfo)">
    <div class="goods-img-wrapper">
      <img :src="`${goodsInfo.goodsPic}?x-oss-process=image/resize,h_478`" alt="暂无图片" class="w-full h-full object-cover" />
    </div>
    <div class="goods-content-wrapper">
      <div class="offer-title">{{ goodsInfo.title }}</div>

      <span class="offer-tag s"></span>
      <span class="offer-price">
        <i class="price">
          <em class="symbol">￥</em>
          <em class="number n-b">{{ goodsInfo.price }}</em>
        </i>
      </span>
      <!-- <div class="business-wrapper">
        <div class="business-name">{{ goodsInfo.shopName }}</div>
        <div class="business-address">山东省临沂市兰山区深度票据网山东省临沂市兰山区深度票据网山东省临沂市兰山区深度票据网</div>
      </div> -->
    </div>
  </div>
</template>

<script setup>
// const router = useRouter()
defineProps({
  goodsInfo: {
    type: Object,
    default: () => ({}),
  },
})
const GoodsDetail = (goodsInfo) => {
  console.log(goodsInfo)
  window.open(goodsInfo.goodsUrl)

  // router.push({
  //   name: 'mall-goods-detail',
  //   params: {
  //     id,
  //   },
  // })
}
</script>

<style lang="scss" scoped>
.rec-offer {
  border: 1px solid #edeef1;
  cursor: pointer;
  transition: all 0.1s;

  &:hover {
    border-color: $primary-color;
  }

  .goods-img-wrapper {
    height: 240px;
    border-bottom: 1px solid #edeef1;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .goods-content-wrapper {
    padding: 8px;
  }
  .offer-title {
    height: 40px;
    line-height: 20px;
    font-size: 14px;
    color: $color-333333;
    @include ellipsis(2);
  }
  .label-wrapper {
    display: flex;
    flex-wrap: wrap;

    span {
      padding: 2px 8px;
      margin-right: 8px;
      font-size: 12px;

      &.free-label {
        background: $color-FFE7E7;
        color: $primary-color;
      }

      &.deliver-goods-time {
        background: $color-F1F6FF;
        color: $color-1D5DDC;
      }
    }
  }
  .offer-price {
    display: flex;
    margin-top: 6px;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    .price {
      color: $primary-color;
      font-size: 24px;
      // line-height: 24px;
      .symbol {
        font-size: 14px;
        // line-height: 22px;
        font-style: normal;
      }
      .number {
        font-size: 16px;
        font-style: normal;
        &.n-b {
          font-size: 24px;
          line-height: 32px;
        }
      }
    }
    .slot {
      line-height: 32px;
      color: $color-999999;
      font-size: 12px;
    }
  }
  .business-wrapper {
    .business-name {
      margin-bottom: 6px;
      width: 100%;
      font-size: 14px;
      color: $color-666666;
      @include ellipsis;
    }
    .business-address {
      width: 100%;
      font-size: 14px;
      color: $color-999999;
      @include ellipsis;
    }
  }
}
</style>
