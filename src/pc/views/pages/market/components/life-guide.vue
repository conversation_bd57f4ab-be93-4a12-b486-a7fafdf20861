<!-- 生活指引 -->
<style lang="scss" scoped>
.w-1260 {
  padding-top: 60px;
}
.life-content {
  padding-top: 24px;

  .lift-title {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .text {
      font-size: 16px;
      font-weight: 600;
      color: $primary-color;
    }

    .icon-box {
      width: 116px;
      height: 5px;
      background: url('https://static.chinamarket.cn/static/trade-exhibition/lj-icon.png') no-repeat;
      background-size: cover;
      margin-left: 4px;
    }
  }
}

.mb-12 {
  margin-bottom: 12px;
}
</style>

<template>
  <div class="w-1260 jump-item">
    <MarketTitle :title="$t('market.lifeGuide')" />
    <div class="life-content">
      <div class="lift-item mb-12">
        <div class="lift-title">
          <div class="text">{{ $t('market.nearbyHotels') }}</div>
          <div class="icon-box"></div>
        </div>
        <CTable :cols="hotelCols" :data="markerInfo?.aroundHotels || []" theadBgColor="linear-gradient(270deg, #FC787D 0%, #D8131A 100%)" />
      </div>
      <!--      <div class="lift-item">-->
      <!--        <div class="lift-title">-->
      <!--          <div class="text">{{ $t('market.nearbyDining') }}</div>-->
      <!--          <div class="icon-box"></div>-->
      <!--        </div>-->
      <!--        <CTable :cols="foodCols" :data="markerInfo?.aroundFoods || []" theadBgColor="linear-gradient(270deg, #FC787D 0%, #D8131A 100%)" />-->
      <!--      </div>-->
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import MarketTitle from './market-title.vue'

const { t } = useI18n()

defineProps({
  markerInfo: {
    type: Object,
    default: () => ({}),
  },
})

// 酒店列
const hotelCols = ref([
  {
    label: computed(() => t('market.number')),
    prop: 'index',
    minWidth: 100,
    align: 'center',
  },
  {
    label: computed(() => t('market.hotelName')),
    prop: 'name',
    minWidth: 260,
  },
  {
    label: computed(() => t('market.hotelLevel')),
    prop: 'level',
    minWidth: 106,
  },
  {
    label: computed(() => t('market.hotelAddress')),
    prop: 'address',
  },
  {
    label: computed(() => t('market.hotelContact')),
    prop: 'contactInfo',
    minWidth: 230,
  },
])

// 餐饮列
// const foodCols = ref([
//   {
//     label: computed(() => t('market.number')),
//     prop: 'index',
//     minWidth: 100,
//     align: 'center',
//   },
//   {
//     label: computed(() => t('market.restaurantName')),
//     prop: 'name',
//     minWidth: 260,
//   },
//   {
//     label: computed(() => t('market.restaurantType')),
//     prop: 'category',
//     minWidth: 106,
//   },
//   {
//     label: computed(() => t('market.restaurantAddress')),
//     prop: 'address',
//   },
//   {
//     label: computed(() => t('market.restaurantContact')),
//     prop: 'contactInfo',
//     minWidth: 230,
//   },
// ])
</script>
