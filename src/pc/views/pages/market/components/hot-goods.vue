<!-- 热销商品 -->
<style lang="scss" scoped>
.w-1260 {
  padding-top: 60px;
}
.look-more-btn {
  position: absolute;
  right: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  color: $color-999999;
  .icon-box {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: $color-999999;
    color: $basic-white;
    text-align: center;
    margin-left: 4px;
    // line-height: 12px;
    transition: width 0.5s ease-in-out;

    .icon-lujing {
      display: block;
      margin: 5px auto 0;
      transition: all 0.5s ease-in-out;
    }
    .icon-lianji {
      display: none;
      transition: width 0.5s ease-in-out;
    }
  }
  &:hover {
    color: $primary-color;

    .icon-box {
      width: 24px;
      border-radius: 8px;
      background: $primary-color;

      .icon-lujing {
        display: none;
      }
      .icon-lianji {
        display: block;
        margin: 2px auto 0;
      }
    }
  }
}

.goods-list-wrapper {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16px;
  padding: 24px 0 0 0;
  background: #ffffff;

  & > div {
    max-width: 237px;
  }
}

:deep() {
  .rec-offer {
    border: 1px solid #edeef1;
    border-radius: 0;
    position: relative;
    padding: 0;
    .goods-img-wrapper {
      border: none;
    }
    .offer-title {
      height: 35px;
    }
    .goods-content-wrapper {
      padding: 8px;
    }
    &::before {
      content: '';
      width: 100%;
      height: 1px;
      background: #edeef1;
      position: absolute;
      left: 0;
      top: 240px;
    }
  }
}

[dir='rtl'] .look-more-btn {
  direction: rtl;
  left: 0;
  right: auto;

  .icon-box {
    transform: rotateZ(-180deg) translateX(8px);
  }
}
</style>
<template>
  <div class="w-1260 jump-item">
    <MarketTitle title="为你推荐">
      <!-- <div class="look-more-btn" @click="onMore">
        <span class="look-more">{{ $t('market.more') }}</span>
        <div class="icon-box">
          <icon type="icon-lujing" :size="6" />
          <icon type="icon-lianji" :size="12" />
        </div>
      </div> -->
    </MarketTitle>
    <div class="goods-list-wrapper">
      <template v-for="(item, index) in goodsList" :key="index">
        <GoodsDetailNewCard :goodsInfo="item" @click="onGoodsClick(item)" />
      </template>
    </div>
    <div class="footer" v-if="goodsList.length">
      <el-pagination
        background
        v-model:current-page="pagination.pageNum"
        :page-size="pagination.pageSize"
        layout="total, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-empty v-if="pagination.total === 0" :image-size="200" />
    <!--    <div v-if="isLoading" class="py-[24px] text-center color-[#999] text-[24px] w-[100%]">-->
    <!--      <el-icon class="loading-icon"><Loading /></el-icon>-->
    <!--    </div>-->
    <!--    <div v-else-if="goodsList.length && isEnd" class="py-[24px] text-center color-[#999] mb-[24px]">- {{ t('noMore') }} -</div>-->
  </div>
</template>

<script setup>
import GoodsDetailNewCard from '@/pc/components/goods-detail-card/goods-detail-card-new.vue'
import MarketTitle from './market-title.vue'
import { getNewHomeGoodsList } from '@/apis/mall'
import { useEvent } from '@/event'
import { LANG_CHANGED } from '@/event/modules/site'
import { debounce } from '@/common/js/util'

// import { Loading } from '@element-plus/icons-vue'
// import { useI18n } from 'vue-i18n'

// const { t } = useI18n({
//   messages: {
//     zh: {
//       noMore: '没有更多',
//     },
//     en: {
//       noMore: 'No More',
//     },
//   },
// })

defineProps({
  markerInfo: {
    type: Object,
    default: () => ({}),
  },
})

const event = useEvent()
const emits = defineEmits(['onAnchorPointChange'])

// 分页
const pagination = ref({
  pageNum: 1,
  pageSize: 50,
  total: 0,
})

const goodsList = ref([])
let market = null
const getGoodsList = async (name = null) => {
  if (name) market = name
  const { rowList, totalRecord } = await getNewHomeGoodsList({
    pageNum: pagination.value.pageNum,
    pageSize: pagination.value.pageSize,
    market,
  })
  goodsList.value = rowList
  pagination.value.total = totalRecord
}
// setTimeout(() => {
//   getGoodsList()
// }, 500)

const DEBOUNCE_TIME = 300
// 获取列表数据防抖函数
const deBounceGetData = debounce(() => {
  getGoodsList()
  emits('onAnchorPointChange', 1)
}, DEBOUNCE_TIME)

// 分页条数变化
const handleSizeChange = (val) => {
  pagination.value.pageSize = val
  pagination.value.pageNum = 1
  deBounceGetData()
}

// 分页页数变化
const handleCurrentChange = (val) => {
  pagination.value.pageNum = val
  deBounceGetData()
}

const onGoodsClick = (item) => {
  if (!item.goodsUrl) return
  window.open(item.goodsUrl, '_blank')
}

// const onMore = () => {
//   window.open('/mall')
// }

event.on(LANG_CHANGED, async () => {
  await getGoodsList()
})

// const queryGoodsList = (name) => {
//   pagination.value.pageNum = 1
//   isEnd.value = false
//   getGoodsList(name)
// }
//
// const isLoading = ref(false)
// const isEnd = ref(false)
// const doScroll = () => {
//   const scrollTop = window.scrollY || document.documentElement.scrollTop || document.body.scrollTop
//   const scrollHeight = window.scrollHeight || document.documentElement.scrollHeight || document.body.scrollHeight
//   const clientHeight = window.clientHeight || document.documentElement.clientHeight || document.body.clientHeight
//
//   if (scrollTop + clientHeight >= scrollHeight - 880) {
//     !isLoading.value && getGoodsList()
//   }
// }
// onMounted(() => {
//   document.addEventListener('scroll', doScroll)
// })
//
// onUnmounted(() => {
//   document.removeEventListener('scroll', doScroll)
// })

const queryList = (name) => {
  pagination.value.pageNum = 1
  getGoodsList(name)
}

defineExpose({
  getGoodsList: queryList,
})
</script>
