<!--
 * @Author: 李兵 <EMAIL>
 * @Date: 2024-08-13 17:48:34
 * @LastEditors: 李兵 <EMAIL>
 * @LastEditTime: 2024-08-15 11:01:05
 * @FilePath: \trade-exhibition\src\views\pages\mall\home\components\activity-type.vue
 * @Description: 产品分类
-->
<template>
  <div class="row-module flex w-1260 jump-item">
    <div class="title">
      <img :src="`${ossUrl}/mall/powerfulshop.png`" class="title-icon w-[30px]" alt="" />
      <div class="title-text text-center">{{ t('topSellers') }}</div>
    </div>
    <div class="goods-list">
      <div v-for="item in shopList" :key="item.id" class="shrink-0 w-[277px] mr-[12px]">
        <GoodsDetailCard :goodsInfo="item" :pic="item.picture" custom-click @click="handleShop(item)" imgStyle="height: 253px;">
          <template #default>
            <div class="offer-title">{{ item.companyName }}</div>
            <div class="offer-bottom flex items-center h-[40px] justify-between">
              <div class="color-[#999] text-[12px] mr-[8px] whitespace-nowrap offer-label" @click.stop>
                {{ t('contact') }}: {{ CUSTOMER_SERVICE_PHONE_NUMBER }}
              </div>
              <div class="customer-btn cursor-pointer border-[#D8131A] border-[1px] border-solid" @click.stop="showCustomer">
                {{ t('customer') }}
              </div>
            </div>
          </template>
          <template #tag>
            <div
              v-if="item.blackLabel === 1"
              class="flex justify-center items-center absolute top-0 left-0 z-1 min-w-[106px] h-[32px] text-[16px] color-[#EDD1A8] px-[12px]"
              :style="`background: url('${ossUrl}/mall/shoptagbg.png') no-repeat center center/100% 100%;`"
            >
              {{ t('topSellers') }}
            </div>
          </template>
        </GoodsDetailCard>
      </div>
      <el-empty v-if="shopList.length === 0" :image-size="200" />
    </div>
  </div>
  <Customer ref="customerRef" />
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import Customer from '@/pc/components/customer/customer.vue'
import GoodsDetailCard from '@/pc/components/goods-detail-card/goods-detail-card.vue'
import { ossUrl } from '@/constants/common'
import { CUSTOMER_SERVICE_PHONE_NUMBER } from '@/pc/constant'
import * as API from '@/apis/mall'

const { t } = useI18n({
  messages: {
    zh: {
      topSellers: '实力商家',
      contact: '联系方式',
      customer: '联系客服',
    },
    en: {
      topSellers: 'Top Merchant',
      contact: 'Contact',
      customer: 'Customer',
    },
  },
})

const shopList = ref([])
const getShopList = async () => {
  const data = await API.getSellerList({})
  if (data) {
    shopList.value = data?.filter((item, i) => i < 4) || []
  }
}
getShopList()

const customerRef = ref(null)
const showCustomer = (item) => {
  customerRef.value.init(item)
}

const router = useRouter()
const handleShop = (item) => {
  router.push({
    name: 'shop',
    params: {
      id: item.id,
    },
  })
}
</script>

<style lang="scss" scoped>
.goods-list {
  width: 100%;
  display: flex;
  flex-wrap: nowrap;
  overflow: hidden;

  :deep(.el-empty) {
    width: 100%;
  }
}

.title {
  font-family: PingFang SC;
  font-size: 20px;
  font-weight: 600;
  color: #fff;

  &-icon {
    width: 30px;
    height: 30px;
  }
}

.row-module {
  padding: 16px 0 16px 20px;
  box-sizing: border-box;
  border-radius: 8px;
  background: linear-gradient(180deg, #f91912 0%, #ffffff 100%);
  justify-content: space-between;

  .title {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0 12px;
    width: 74px;
    height: 365px;
    color: #333;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.6);
    margin-right: 12px;

    &-icon {
      margin-bottom: 12px;
    }
  }
}

[dir='rtl'] .offer-label {
  margin-right: 0;
  margin-left: 8px;
}

.offer-title {
  height: 40px;
  line-height: 20px;
  font-size: 14px;
  color: $color-333333;
  @include ellipsis(2);
}

.customer-btn {
  @include ellipsis(1);
  font-size: 12px;
  border-radius: 2px;
  min-width: 58px;
  padding: 0 2px;
  line-height: 20px;
  text-align: center;
  color: $primary-color;
  box-sizing: border-box;
}
</style>
