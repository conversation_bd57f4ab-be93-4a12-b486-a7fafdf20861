<template>
  <div class="full-banner">
    <div class="swiper-container">
      <video
        class="w-full h-full object-cover"
        src="https://static.chinamarket.cn/static/trade-exhibition/intro-video.mp4"
        autoplay
        muted
        loop
        t7-video-player-type="inline"
        x5-video-player-type="h5-page"
        x5-video-orientation="portraint"
        x-webkit-airplay="allow"
        webkit-playsinline
        playsinline
        raw-controls
        controls360="no"
        x5-video-player-fullscreen=""
        disablePictureInPicture
      ></video>
    </div>
    <div class="inner">
      <div class="slogan" v-mode="LINYI_CHINA_MARKET_MAP" :style="{ fontSize: ['ru', 'tr'].includes($storageLocale) ? '48px' : '' }">
        {{ $t('slogan.main') }}
      </div>
      <div class="ai-search">
        <div class="flex items-center">
          <div class="search-change mr-4">
            <div class="search-way hl">{{ $t('search.trigger') }}</div>
            <div class="search-way skip-translate" @click="openAiWindow">
              {{ $storageLocale === 'zh' ? MOBILE_AI_BROTHER_MAP['zh'] : MOBILE_AI_BROTHER_MAP['en'] }}
            </div>
          </div>
          <div class="flex-1 flex-col skip-translate text-white">
            <div class="text-22px">中国最大市场集群 一件也是批发价</div>
            <div class="text-16px">China's grand mart Wholesale price from the start</div>
          </div>
        </div>
        <div ref="searchBarRef" class="search-bar">
          <input v-model="searchInput" class="search-input" type="text" :placeholder="$t('search.placeholder')" @keydown.enter="handleSearch()" />
          <!--          <transition name="fade">-->
          <!--            <div v-show="suggestionList.length > 0" class="search-suggestion">-->
          <!--              <div class="suggestion-item" v-for="item in suggestionList" :key="item.title" @click="onSelectSuggestion(item)">-->
          <!--                <HlPartText :text="item.title" :hl="searchInput" />-->
          <!--              </div>-->
          <!--            </div>-->
          <!--          </transition>-->
          <div class="search-button" @click="handleSearch()">
            <i-inside-search />
          </div>
        </div>
        <div class="hot-search">
          <span>{{ $t('search.hot') }}：</span>
          <div class="hot-search-item" v-for="s in searchList" :key="s.question" @click="onJump(s)">{{ s.label[$i18n.locale] }}</div>
        </div>
      </div>
      <div class="banner-dot">
        <div class="banner-dot-item" :class="{ hl: index === activeDot }" v-for="(item, index) in bannerDot" :key="item.name" @click="goPage(item.path)">
          <img :src="`https://static.chinamarket.cn/static/trade-exhibition/banner-dot-icon/${item.icon}`" draggable="false" />
          <div>
            <div class="banner-dot-item-name">{{ item.name[$i18n.locale] }}</div>
            <div class="banner-dot-item-desc">{{ item.desc[$i18n.locale] }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// import { onClickOutside } from '@vueuse/core'
import { LINYI_CHINA_MARKET_MAP, MOBILE_AI_BROTHER_MAP } from '@/constants/special-field'
import { useSearch } from '@/pc/hooks/useSearch'
import { useEvent } from '@/event'
import { OPEN_AI_WINDOW } from '@/event/modules/site'

const { searchInput } = useSearch()
const searchBarRef = ref(null)
// onClickOutside(searchBarRef, () => {
//   suggestionList.length = 0
//   searchInput.value = ''
// })

const event = useEvent()

// const navCurrentPath = ref('/')
// 热门搜索
const searchList = [
  {
    label: { zh: '机械五金', en: 'Hardware products' },
    agent: 'SC_LIFE_BOT',
    type: 'ai',
    question: '临沂本地五金产品有哪些，并罗列五金相关链接，推荐五金市场',
  },
  {
    label: { zh: '日用百货', en: 'Department store products' },
    agent: 'SC_LIFE_BOT',
    type: 'ai',
    question: '临沂本地百货产品有哪些，并罗列百货相关链接，推荐百货市场',
  },
  {
    label: { zh: '安全防护', en: 'Protective gear' },
    agent: 'SC_LIFE_BOT',
    type: 'ai',
    question: '临沂本地劳保产品有哪些，并罗列劳保相关链接，推荐劳保市场',
  },
]
// 跳转
const onJump = (val) => {
  const { href } = router.resolve({
    path: '/mall/goods-list',
    query: {
      categoryName: val.label.zh,
    },
  })
  window.open(href)
}

const openAiWindow = () => {
  event.emit(OPEN_AI_WINDOW)
}

const router = useRouter()

const bannerDot = [
  {
    name: { zh: '商城海外仓', en: 'Warehouse' },
    desc: { zh: '“临沂”特色海外仓', en: 'Overseas warehouse' },
    icon: '商城海外仓.png',
    path: '/outside-trade/overseas-warehous',
  },
  {
    name: { zh: '跨境数贸', en: 'Cross-border trade' },
    desc: { zh: '在家做全球的贸易', en: 'Conduct global trade' },
    icon: '跨境数贸.png',
    path: '/outside-trade/cross-border-trade',
  },
  {
    name: { zh: '低成本融资', en: 'Low-cost financing' },
    desc: { zh: '提供产融服务平台', en: 'Service platform' },
    icon: '融资.png',
    path: '/outside-trade/cost-finance',
  },
  {
    name: { zh: '外贸资讯', en: 'Latest news' },
    desc: { zh: '了解最新临沂外贸资讯', en: 'Stay updated news' },
    icon: '外贸资讯.png',
    path: '/outside-trade/foreing-info?type=FOREIGN_INFO',
  },
  {
    name: { zh: '国际展会', en: 'Trade show' },
    desc: { zh: '临沂的国际展会服务', en: 'International services' },
    icon: '国际展会.png',
    path: '/outside-trade/country-expo',
  },
]
const activeDot = ref(1)

const goPage = (path) => router.push(path)

const handleSearch = () => {
  if (!searchInput.value || !searchInput.value.trim()) {
    return
  }
  const url = router.resolve({
    path: '/mall/goods-list',
    query: {
      keyword: searchInput.value,
    },
  })
  window.open(url.href, '_blank')
}
</script>

<style lang="scss" scoped>
.full-banner {
  position: relative;
  height: 100vh;
  min-height: 600px;
  background: #b8a9aa;

  .swiper-container {
    position: absolute;
    inset: 0;
    z-index: 1;

    &::after {
      content: '';
      position: absolute;
      inset: 0;
      background-color: rgba($color: #000000, $alpha: 0.5);
    }
  }

  .inner {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: $main-width;
    height: 100%;
    margin: 0 auto;
    padding-bottom: 120px;
  }
}

.slogan {
  font-weight: 600;
  font-size: 68px;
  color: #ffffff;
}

.ai-search {
  width: 960px;
  margin-top: 97px;

  .search-change {
    display: inline-flex;
    padding: 4px;
    border-radius: 8px;
    background: rgba(246, 248, 252, 0.4);

    .search-way {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 80px;
      height: 52px;
      border-radius: 4px;
      font-weight: 600;
      font-size: 16px;
      cursor: pointer;
      color: #fff;
      user-select: none;

      &.hl {
        background: linear-gradient(270deg, #d8131a 0%, #fd441f 100%);
      }
    }
  }

  .search-bar {
    position: relative;
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 16px;
    padding: 8px;
    border-radius: 8px;
    background-color: #fff;
    opacity: 0.8;

    .search-input {
      flex-grow: 1;
      padding: 0 16px;
      border: none;
      height: 48px;
      font-size: 18px;
      outline: none;
    }
    .search-suggestion {
      position: absolute;
      top: 64px;
      right: 0;
      left: 0;
      display: flex;
      flex-direction: column;
      gap: 4px;
      padding: 16px 12px;
      border-radius: 8px;
      background-color: #fff;
      .suggestion-item {
        padding: 4px 12px;
        border-radius: 4px;
        font-size: 14px;
        color: #333333;
        transition: all 0.1s;
        cursor: pointer;
        &:hover {
          color: #d8131a;
          background-color: #ffeded;
        }
      }

      &.fade-enter-active {
        transition: all 0.3s ease;
      }
      &.fade-enter-from {
        opacity: 0;
        transform: translateY(10px);
      }
    }
    .search-button {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 48px;
      height: 40px;
      border-radius: 4px;
      background-image: linear-gradient(270deg, #d8131a 0%, #fd441f 100%);

      img {
        height: 24px;
      }
    }
  }

  .hot-search {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 10px;
    color: #fff;
    font-size: 14px;

    &-item {
      border-radius: 2px;
      padding: 2px 8px;
      background-color: rgba(255, 255, 255, 0.2);
      cursor: pointer;
    }
  }
}

.banner-dot {
  position: absolute;
  bottom: 40px;
  display: flex;
  width: 1260px;
  border: 1px solid #fff;
  border-radius: 4px;
  user-select: none;
  cursor: pointer;

  &-item {
    flex: 1 0;
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px 24px;
    color: #fff;
    background-color: rgba($color: #ffffff, $alpha: 0.4);
    transition: all 0.3s ease;

    &:hover {
      color: #d8131a;
      background-color: rgba($color: #ffffff, $alpha: 0.6);
    }

    &-name {
      font-weight: 600;
      font-size: 16px;
    }
    &-desc {
      font-size: 14px;
    }
  }

  img {
    width: 40px;
    height: 40px;
  }
}
</style>
