<template>
  <div class="ai-group">
    <div class="inner">
      <div class="title">{{ t('aiGroup') }}</div>
      <div class="sub-title">{{ t('aiGroupIntro') }}</div>
      <div class="content">
        <div class="flex items-start grow">
          <div class="tabs w-200px shrink-0">
            <div class="tab-item" :class="{ hl: index === activeTab }" v-for="(item, index) in products" :key="item.name" @click="handleActive(index)">
              <span>{{ item.name[$i18n.locale] }}</span>
              <div :class="{ animated: index === activeTab }"></div>
            </div>
          </div>
          <div class="grow py-4 px-10">
            <transition-group name="fade">
              <template v-for="(item, index) in products" :key="item.name">
                <div v-if="index === activeTab" class="flex flex-col items-start gap-4">
                  <div class="tab-content-name">{{ item.name[$i18n.locale] }}</div>
                  <div class="tab-content-desc">{{ item.desc[$i18n.locale] }}</div>
                  <div class="tab-content-link" :class="{ 'w-full': $storageLocale === 'indonesian' }" @click="goPage(item)">
                    <span>{{ t('trynow') }}</span>
                    <i-inside-link class="ml-3" />
                  </div>
                </div>
              </template>
            </transition-group>
          </div>
        </div>
        <video
          class="preview"
          :src="`https://static.chinamarket.cn/static/trade-exhibition/ai-feature-intro/${products[activeTab].vd}`"
          muted
          autoplay
          loop
          disablePictureInPicture
        ></video>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { useEvent } from '@/event'
import { OPEN_AI_WINDOW } from '@/event/modules/site'

const { t } = useI18n({
  messages: {
    zh: {
      aiGroup: 'AI创新',
      aiGroupIntro: '以人为本，打造 “工具集群” 的生态体系',
      trynow: '立即跳转',
    },
    en: {
      aiGroup: 'AI Features',
      aiGroupIntro: "People-centric, creating an ecosystem of 'tool clusters'",
      trynow: 'Try Now',
    },
  },
})
const router = useRouter()
const event = useEvent()

const products = [
  {
    name: { zh: '多语言视频翻译', en: 'Video Translation' },
    desc: {
      zh: '集成了AI智能语音识别与大型语言模型翻译，带来自然的多语言视频体验',
      en: 'Integrates AI-powered voice recognition and large language model translation to provide a natural multilingual video experience.',
    },
    vd: 'video-translate-new.mp4',
    path: '/ai-features/video-translation',
  },
  {
    name: { zh: '数字代言人', en: 'Digital Spokesperson' },
    desc: {
      zh: '提供丰富的数字人模版，利用实时渲染技术，可以在消费级的硬件环境下实时渲染出高质量的虚拟角色',
      en: 'Offers a variety of digital human templates and uses real-time rendering technology to produce high-quality virtual characters on consumer-grade hardware.',
    },
    vd: 'digital-spokesperson-new.mp4',
    path: '/ai-features/person-square',
  },
  {
    name: { zh: 'AI商品图', en: 'AI Product Images' },
    desc: {
      zh: 'AI技术的应用使得抠图过程自动化，同时提供背景模版以供快速设置商品图片',
      en: 'The application of AI technology automates the background removal process and provides background templates for quick product image setup.',
    },
    vd: '更换图片.mp4',
    path: '/ai-features/product-picture',
  },
  {
    name: { zh: 'AI翻译官', en: 'AI Translator' },
    desc: {
      zh: '基于大型语言模型翻译，提升语言翻译准确性和与原文本的表述一致性，解决客户在外贸过程中的语言障碍',
      en: 'Based on large language model translation, it enhances the accuracy of language translation and consistency with the original text, addressing language barriers in foreign trade processes.',
    },
    vd: '文字翻译.mp4',
    onClick() {
      event.emit(OPEN_AI_WINDOW, {
        agent: 'SC_TRANSLATION_BOT',
      })
    },
  },
]
// 获取当前访问来源
const contentType = ref('')
let xhr = new XMLHttpRequest()
xhr.open('GET', '/', true)
xhr.onreadystatechange = function () {
  if (xhr.readyState === 2) {
    // 请求已发送，响应已接收到一部分
    contentType.value = xhr.getResponseHeader('Web-Evn')
  }
}
xhr.send()

const goPage = (item) => {
  console.log('Web-Evn的值是:', contentType.value)
  if (typeof item.onClick === 'function') {
    return item.onClick()
  }
  if (contentType.value === 'cn' || !contentType.value) {
    router.push(item.path)
  } else {
    event.emit(OPEN_AI_WINDOW, {
      agent: 'SC_TRANSLATION_BOT',
    })
  }
}

let timer
const createInterval = () => {
  timer = setInterval(() => {
    activeTab.value = activeTab.value >= products.length - 1 ? 0 : activeTab.value + 1
  }, 12000)
}

const activeTab = ref(0)
const handleActive = (index) => {
  activeTab.value = index
  clearInterval(timer)
  createInterval()
}

onMounted(() => {
  createInterval()
})
onUnmounted(() => {
  clearInterval(timer)
})
</script>

<style lang="scss" scoped>
.ai-group {
  padding: 80px 0;
  background: #ffffff;

  .inner {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: $main-width;
    margin: 0 auto;
  }
}

.title {
  background: linear-gradient(270deg, #d8131a 0%, #e96f18 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-size: 48px;
  font-weight: 600;
}
.sub-title {
  margin-top: 16px;
  font-size: 32px;
  color: #6c4849;
}
.content {
  display: flex;
  align-items: flex-start;
  width: 100%;
  margin-top: 64px;
}

.tabs {
  // xxx
}
.tab-item {
  position: relative;
  padding: 16px 24px;
  border-right: 2px solid #e9ebf0;
  font-size: 16px;
  color: #380405;
  text-align: right;
  cursor: pointer;
  transition: all 0.3s ease;

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    z-index: 0;
    background: linear-gradient(90deg, rgba(255, 237, 237, 0) 0%, #ffeded 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  &.hl {
    // font-weight: 600;
    color: #e20000;
    // border-right-color: #d8131a;

    &::before {
      opacity: 1;
    }
  }

  span {
    position: relative;
    z-index: 1;
  }
}

.tab-content-name {
  font-weight: 600;
  font-size: 36px;
  color: #380405;
}
.tab-content-desc {
  font-weight: 400;
  font-size: 14px;
  color: #6c4849;
}
.tab-content-link {
  display: flex;
  min-width: 174px;
  height: 40px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: 4px;
  background-image: linear-gradient(to right, #e96f18 0%, #d8131a 50%, #d8131a 100%);
  background-size: 200%;
  font-weight: 600;
  font-size: 16px;
  color: #fff;
  cursor: pointer;
  transition: background-position 0.3s;

  &:hover {
    background-position: 100% 0;
  }
}

.preview {
  width: 716px;
  height: 448px;
  object-fit: cover;
  mix-blend-mode: darken;
}

.fade-enter-active {
  transition: all 0.6s ease;
}
.fade-enter-from {
  opacity: 0;
  transform: translateY(10px);
}
.animated {
  position: absolute;
  width: 2px;
  top: 0;
  right: -2px;
  height: 100%;
  background: #d8131a;
  transform-origin: top;
  animation: growBorder 12s linear;
}
@keyframes growBorder {
  from {
    transform: scaleY(0);
  }
  to {
    transform: scaleY(1);
  }
}
[dir='rtl'] {
  .ml-3 {
    margin-right: 12px;
    margin-left: 0;
  }
}
</style>
