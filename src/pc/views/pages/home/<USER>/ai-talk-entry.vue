<style lang="scss" scoped>
.ai-talk-entry {
  position: fixed;
  top: 66vh;
  right: 0;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  user-select: none;
  cursor: pointer;
}

.intro {
  padding: 4px 12px 12px;
  font-weight: 600;
  font-size: 12px;
  background-image: url('@/assets/imgs/ai-talk-entry-bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  color: #fff;
}

.anim {
  width: 100px;
  user-select: none;
}
.ai-text {
  position: absolute;
  bottom: 2px;
  color: #ffffff;
}
</style>

<template>
  <div v-if="!inBlack" class="ai-talk-entry bg-white rounded-l-16px p-16px group" @click="openAiWindow">
    <!-- 点击X按钮 只是不展示文案 不隐藏入口 -->
    <!-- <div v-if="visible" class="intro">
      <div class="flex items-center pt-[2px]">
        <span>{{ $t('ai.entry') }}</span>
        <i-inside-close class="h-12px text-#fff" @click.stop="visible = false" />
      </div>
    </div> -->
    <!-- <img class="anim" src="https://static.chinamarket.cn/static/trade-exhibition/ai-talk-entry.gif" draggable="false" /> -->

    <!-- <span class="ai-text skip-translate">{{ $storageLocale === 'zh' ? MOBILE_AI_BROTHER_MAP['zh'] : MOBILE_AI_BROTHER_MAP['en'] }}</span> -->
    <!-- <icon size="24" type="icon-shezhi" class="text-black" /> -->
    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="24" height="24" viewBox="0 0 24 24">
      <defs>
        <clipPath id="master_svg0_1678_079584"><rect x="0" y="0" width="24" height="24" rx="0" /></clipPath>
      </defs>
      <g clip-path="url(#master_svg0_1678_079584)">
        <g>
          <path
            d="M5.35714,11.6786C5.35714,10.5313,4.63587,9.5525,3.62204,9.17089Q3.77527,8.10379,4.20048,7.09847Q4.84288,5.57967,6.01485,4.4077Q7.18681,3.23574,8.70562,2.59334Q10.2773,1.92857,12,1.92857Q13.7227,1.92857,15.2944,2.59334Q16.8132,3.23574,17.9852,4.4077Q19.1571,5.57966,19.7995,7.09847Q20.2247,8.10379,20.378,9.17089C19.3641,9.5525,18.6429,10.5313,18.6429,11.6786L18.6429,14.625C18.6429,15.7644,19.3542,16.7376,20.357,17.1247Q20.272,17.5008,20.1195,17.8614Q19.7861,18.6495,19.1776,19.258Q18.5692,19.8665,17.781,20.1998Q16.9658,20.5446,16.0714,20.5446L14.5642,20.5446C14.2313,19.4441,13.2092,18.6429,12,18.6429C10.5207,18.6429,9.32143,19.8421,9.32143,21.3214C9.32143,22.8008,10.5207,24,12,24C13.067,24,13.9883,23.3761,14.419,22.4732L16.0714,22.4732Q17.3569,22.4732,18.5323,21.976Q19.6669,21.4962,20.5414,20.6217Q21.4158,19.7473,21.8957,18.6127Q22.2044,17.8829,22.3214,17.1107C23.3054,16.7144,24,15.7508,24,14.625L24,11.6786C24,10.5543,23.3074,9.59184,22.3256,9.19457Q22.1584,7.72464,21.5757,6.34719Q20.7868,4.48194,19.3489,3.044Q17.9109,1.60605,16.0457,0.817118Q14.1138,1.02179e-7,12,0Q9.88622,0,7.95434,0.817118Q6.08909,1.60605,4.65114,3.044Q3.21319,4.48194,2.42426,6.34719Q1.84165,7.72464,1.67444,9.19457C0.692626,9.59184,0,10.5543,0,11.6786L0,14.625C0,16.1043,1.19924,17.3036,2.67857,17.3036C4.15791,17.3036,5.35714,16.1043,5.35714,14.625L5.35714,11.6786ZM9.16071,9.96429C9.69328,9.96429,10.125,9.53256,10.125,9C10.125,8.46744,9.69328,8.03571,9.16071,8.03571C8.62815,8.03571,8.19643,8.46744,8.19643,9C8.19643,9.53256,8.62815,9.96429,9.16071,9.96429ZM14.8393,9.96429C15.3718,9.96429,15.8036,9.53256,15.8036,9C15.8036,8.46744,15.3718,8.03571,14.8393,8.03571C14.3067,8.03571,13.875,8.46744,13.875,9C13.875,9.53256,14.3067,9.96429,14.8393,9.96429ZM1.92857,14.625Q1.92857,14.9357,2.14824,15.1553Q2.36791,15.375,2.67857,15.375Q2.98923,15.375,3.2089,15.1553Q3.42857,14.9357,3.42857,14.625L3.42857,11.6786Q3.42857,11.3679,3.2089,11.1482Q2.98923,10.9286,2.67857,10.9286Q2.36791,10.9286,2.14824,11.1482Q1.92857,11.3679,1.92857,11.6786L1.92857,14.625ZM20.5714,14.625Q20.5714,14.9357,20.7911,15.1553Q21.0108,15.375,21.3214,15.375Q21.6321,15.375,21.8518,15.1553Q22.0714,14.9357,22.0714,14.625L22.0714,11.6786Q22.0714,11.3679,21.8518,11.1482Q21.6321,10.9286,21.3214,10.9286Q21.0108,10.9286,20.7911,11.1482Q20.5714,11.3679,20.5714,11.6786L20.5714,14.625ZM12.75,21.3214C12.75,20.9072,12.4142,20.5714,12,20.5714C11.5858,20.5714,11.25,20.9072,11.25,21.3214C11.25,21.7356,11.5858,22.0714,12,22.0714C12.4142,22.0714,12.75,21.7356,12.75,21.3214Z"
            fill-rule="evenodd"
            fill="#000000"
            fill-opacity="1"
          />
        </g>
      </g>
    </svg>
    <!-- <div class="text-12px text-ellipsis-2 text-#3D3D3D mt-8px group-hover:text-#000">{{ $t('customerService') }}</div> -->
    <!-- <img class="anim" src="https://static.chinamarket.cn/static/trade-exhibition/ai-daji/dajige1.png" draggable="false" /> -->
  </div>
</template>

<script setup>
import { useStorageLocale } from '@/i18n'
import { CUSTOMER_SERVICE_LANGUAGE_MAP } from '@/i18n/contants'

// import { useEvent } from '@/event'
// import { OPEN_AI_WINDOW } from '@/event/modules/site'

const { storageLocale } = useStorageLocale()
const route = useRoute()
// const visible = ref(true)

// const event = useEvent()
const openAiWindow = () => {
  const lang = CUSTOMER_SERVICE_LANGUAGE_MAP[storageLocale.value]
  const url = `https://support.chinamarket.cn/index/index/home?visiter_id=&visiter_name=&avatar=&business_id=1&groupid=0&special=0&width=100&lang=${lang}`
  window.open(url, '_blank', 'height=800,width=950,top=50,left=200,status=yes,toolbar=no,menubar=no,resizable=yes,scrollbars=no,location=no,titlebar=no')
  // event.emit(OPEN_AI_WINDOW, { path: route?.path })
}

// 配置中的页面不展示入口
const inBlack = computed(() => {
  const list = []
  return list.includes(route.path)
})
</script>
