<template>
  <div class="outside-trade">
    <div class="inner">
      <div v-if="isTitle" class="title">{{ t('outsideTrade') }}</div>
      <div class="sub-title">{{ t('outsideTradeIntro') }}</div>
      <div class="content w-[100%]">
        <div v-for="(item, i) in options" :key="i" class="flex items-center w-[100%]">
          <div v-for="broad in item" :key="broad.name" class="broad-service h-[120px] mr-[24px] mb-[12px]" @click="handleItemClick(broad)">
            <div class="normal-bg"></div>
            <div class="hover-bg">
              <div class="rect"></div>
            </div>
            <div class="large-icon" :style="{ backgroundImage: `url(https://static.chinamarket.cn/static/trade-exhibition/outside-trade/${broad.icon})` }" />
            <div class="broad-name h-[100%] relative z-10 flex-col justify-center flex">{{ broad.name }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'

// import * as API from '@/apis/outside-trade-serve'

const { t } = useI18n({
  messages: {
    zh: {
      outsideTrade: '外贸综合服务',
      outsideTradeIntro: '全面升级外贸生态，开启数字化外贸新体验',
    },
    en: {
      outsideTrade: 'Foreign trade information aggregation',
      outsideTradeIntro: 'Launch a new digital foreign trade experience.',
    },
  },
})
const router = useRouter()
defineProps({
  isTitle: {
    type: Boolean,
    default: true,
  },
})

const outsideTrade = [
  {
    name: { zh: '国际交流', en: 'Communication' },
    icon: 'international-communication.png',
    child: [
      { name: { zh: '国际展会', en: 'International Expo' }, path: '/outside-trade/country-expo' },
      { name: { zh: '外贸资讯', en: 'Foreign Trade Information' }, path: '/outside-trade/foreing-info', type: 'FOREIGN_INFO' },
      {
        name: { zh: '内外资源融合', en: 'Integration of Internal and External Resources' },
        path: '/outside-trade/external-reaource',
        type: 'EXTERNAL_RESOURCE',
      },
    ],
  },
  {
    name: { zh: '国际贸易', en: 'International Trade' },
    icon: 'international-trade.png',
    child: [
      { name: { zh: '跨境贸易', en: 'Cross-border Trade' }, path: '/outside-trade/cross-border-trade' },
      { name: { zh: '国际物流', en: 'International Logistics' }, path: '/outside-trade/country-exhibition' },
      { name: { zh: '海外仓储', en: 'Overseas Warehouse' }, path: '/outside-trade/overseas-warehous' },
    ],
  },
  {
    name: { zh: '外贸政务', en: 'Foreign Trade Affairs' },
    icon: 'foreign-trade-government.png',
    child: [
      { name: { zh: '海关窗口', en: 'Customs' }, path: '/outside-trade/customs-introduce' },
      { name: { zh: '税务协调', en: 'Tax Coordinate' }, path: '/outside-trade/taxation', type: 'TAXATION' },
      { name: { zh: '采购商签证', en: 'Visa' }, path: '/outside-trade/buy-visa', type: 'BUY_VISA' },
    ],
  },
  {
    name: { zh: '国别商事', en: 'Country-specific Business' },
    icon: 'country-commercial.png',
    child: [
      { name: { zh: '开设公司', en: 'Company Registration' }, path: '/outside-trade/open-company' },
      { name: { zh: '外汇交易', en: 'Foreign Exchange Trading' }, path: '/outside-trade/forex-trading', type: 'FOREX_TRADING' },
      { name: { zh: '财会事项', en: 'Financial Matters' }, path: '/outside-trade/financial-matter', type: 'FINANCIAL_MATTER' },
    ],
  },
  {
    name: { zh: '政策支持', en: 'Policy Support' },
    icon: 'policy-support.png',
    child: [
      { name: { zh: '外贸基金', en: 'Trade Fund' }, path: '/outside-trade/trade-fund' },
      { name: { zh: '低成本融资', en: 'Low-cost Financing' }, path: '/outside-trade/cost-finance' },
      { name: { zh: '地方外贸资讯', en: 'Local Trade Information' }, path: '/outside-trade/local-trade', type: 'LOCAL_TRADE' },
    ],
  },
  {
    name: { zh: '外商生活', en: 'Foreign Business Life' },
    icon: 'foreign-life.png',
    child: [
      { name: { zh: '餐饮', en: 'Dining' }, path: '/outside-trade/restaurant', type: '1' },
      { name: { zh: '住宿', en: 'Accommodation' }, path: '/outside-trade/accommodation', type: '2' },
      { name: { zh: '娱乐', en: 'Entertainment' }, path: '/outside-trade/entertainment', type: '3' },
    ],
  },
]

const options = ref(null)
const getCategoryOptions = async () => {
  // const data = (await API.getServiceType()) || []
  // 先写死
  const data = [
    {
      id: 1,
      name: '订单服务',
      children: null,
      pid: 0,
    },
    {
      id: 2,
      name: '支付结算',
      children: null,
      pid: 0,
    },
    {
      id: 3,
      name: '履约服务',
      children: null,
      pid: 0,
    },
    {
      id: 4,
      name: '物流和报关',
      children: null,
      pid: 0,
    },
    {
      id: 5,
      name: '增值服务',
      children: null,
      pid: 0,
    },
    {
      id: 6,
      name: '服务生态',
      children: null,
      pid: 0,
    },
  ]
  const arr = data.map((item, i) => ({
    ...item,
    icon: outsideTrade[i]?.icon,
  }))
  const len = Math.ceil(arr.length / 2)
  const newArr = []
  // 平均分2
  for (let i = 0; i < 2; i++) {
    newArr[i] = arr.slice(i * len, (i + 1) * len)
  }
  options.value = newArr
}

onMounted(() => {
  getCategoryOptions()
})

const handleItemClick = (item) => {
  // 先写死
  if (item.id) return
  const { href } = router.resolve({
    name: 'outsideTradeServe',
    query: {
      serviceTypeId: item.id,
    },
  })
  window.open(href)
}
</script>

<style lang="scss" scoped>
.outside-trade {
  padding: 80px 0;
  background: linear-gradient(68deg, rgba(255, 244, 210, 0.4) 5%, rgba(216, 19, 26, 0.16) 43%, rgba(139, 188, 242, 0.32) 100%);

  .inner {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: $main-width;
    margin: 0 auto;
  }
}

.title {
  background: linear-gradient(270deg, #d8131a 0%, #e96f18 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-size: 48px;
  font-weight: 600;
}
.sub-title {
  margin-top: 16px;
  font-size: 32px;
  color: #6c4849;
}

.content {
  margin-top: 64px;
}
.broad-service {
  flex: 1;
  overflow: hidden;
  position: relative;
  border-radius: 8px;
  padding: 24px;

  .normal-bg {
    position: absolute;
    inset: 0;
    z-index: 0;
    background-image: url('https://static.chinamarket.cn/static/trade-exhibition/outside-trade/card-background.png');
    background-size: cover;
  }
  .hover-bg {
    position: absolute;
    inset: 0;
    z-index: 1;
    background: linear-gradient(291deg, #d8131a 0%, #f0aba6 100%);
    opacity: 0;
    // transform: translateY(4px);
    transition: all 0.3s ease-in;

    .rect {
      position: absolute;
      // top: 118px;
      top: 0;
      right: -80px;
      width: 100%;
      height: 100%;
      border-radius: 8px;
      background-color: #d8131a;
      transform: rotate(45deg);
      opacity: 0.2;
    }
  }
  .large-icon {
    position: absolute;
    right: 10px;
    top: 18px;
    z-index: 11;
    width: 112px;
    height: 112px;
    user-select: none;
    background-size: 112px auto;
  }

  &:hover {
    .hover-bg {
      transform: translateY(0px);
      opacity: 1;
    }
    .broad-name,
    .sub-name {
      color: #ffffff;
    }
    .sub-name {
      background-image: url('@/assets/imgs/icon/arrow-right-white.svg');
    }
    .large-icon {
      animation: gif-animation 1s steps(1) infinite;
      animation-iteration-count: 1;
      animation-fill-mode: forwards;
    }
  }
}

[dir='rtl'] .broad-service {
  .large-icon {
    direction: rtl;
    right: auto;
    left: 10px;
  }
}
.broad-name {
  max-width: 280px;
  font-weight: 600;
  font-size: 22px;
  color: #380405;
  transition: color 0.3s ease;
}
.sub-serve {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  font-size: 16px;
  margin-top: 24px;
  color: #d8131a;
}
.sub-name {
  display: flex;
  align-items: center;
  padding: 0;

  border: 1px solid transparent;
  border-radius: 2px;
  line-height: 30px;
  background-repeat: no-repeat;
  background-size: 20px 20px;
  background-position: right center;
  background-image: url('@/assets/imgs/icon/arrow-right-red.svg');
  background-color: transparent;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    padding: 0 16px;
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.2);
    color: #d8131a !important;
    background-position: right 16px center;
    background-image: url('@/assets/imgs/icon/arrow-right-red.svg') !important;
  }

  span {
    padding-right: 28px;
  }
}
@keyframes gif-animation {
  0% {
    background-position: 0 0;
  }
  $len: 25;
  @for $i from 1 through $len {
    #{ $i * 4%} {
      background-position: 0 -#{$i * 112px};
    }
  }
}
</style>
