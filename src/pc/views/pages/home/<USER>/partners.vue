<template>
  <div class="partners">
    <div class="scroll-wrapper">
      <div class="partners-title">
        <div class="partners-title-text">{{ t('partners') }}</div>
      </div>
      <div class="img-wrapper">
        <div class="mask left_mask"></div>
        <div class="mask right_mask"></div>
        <div class="mb-25">
          <ImgScroll :content="oneList" style="height: 100px" direction="right">
            <template v-slot="{ item }">
              <div class="one-img">
                <img :src="item" alt="" />
              </div>
            </template>
          </ImgScroll>
        </div>
        <div class="mb-25">
          <ImgScroll :content="twoList" style="height: 80px" direction="right">
            <template v-slot="{ item }">
              <div class="two-img">
                <img :src="item" alt="" />
              </div>
            </template>
          </ImgScroll>
        </div>
        <ImgScroll :content="threeList" style="height: 80px" direction="right">
          <template v-slot="{ item }">
            <div class="two-img">
              <img :src="item" alt="" />
            </div>
          </template>
        </ImgScroll>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import ImgScroll from './img-scroll.vue'

const oneList = [
  'https://static.chinamarket.cn/static/trade-exhibition/home/<USER>',
  'https://static.chinamarket.cn/static/trade-exhibition/home/<USER>',
  'https://static.chinamarket.cn/static/trade-exhibition/home/<USER>',
]

const twoList = [
  'https://static.chinamarket.cn/static/trade-exhibition/home/<USER>',
  'https://static.chinamarket.cn/static/trade-exhibition/home/<USER>',
  'https://static.chinamarket.cn/static/trade-exhibition/home/<USER>',
  'https://static.chinamarket.cn/static/trade-exhibition/home/<USER>',
  'https://static.chinamarket.cn/static/trade-exhibition/home/<USER>',
]

const threeList = [
  'https://static.chinamarket.cn/static/trade-exhibition/home/<USER>',
  'https://static.chinamarket.cn/static/trade-exhibition/home/<USER>',
  'https://static.chinamarket.cn/static/trade-exhibition/home/<USER>',
  'https://static.chinamarket.cn/static/trade-exhibition/home/<USER>',
  'https://static.chinamarket.cn/static/trade-exhibition/home/<USER>',
]

const { t } = useI18n({
  messages: {
    zh: {
      partners: '合作伙伴',
    },
    en: {
      partners: 'Partners',
    },
  },
})
</script>

<style lang="scss" scoped>
.partners {
  position: relative;
  text-align: center;
  background: #fff;
  // height: 575px;
  &-title {
    margin: 0 auto;
    width: 500px;
    padding: 80px 0 20px;
    background: #fff;

    &-text {
      text-align: center;
      font-family: PingFang SC;
      font-size: 48px;
      font-weight: 600;
      line-height: normal;
      letter-spacing: 0;
      background: linear-gradient(270deg, #d8131a 0%, #e96f18 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }
  }

  .scroll-wrapper {
    padding-bottom: 80px;
    width: $main-width;
    margin: 0 auto;
    overflow: hidden;
  }
}

.img-item {
  width: 200px;
  margin-right: 10px;
}

:deep() {
  .loop-item {
    margin-right: 20px;
  }
}

.one-img {
  width: 404px;
}

.two-img {
  width: 233px;
}
.one-img,
.two-img {
  img {
    width: 100%;
    height: 100%;
  }
}

.mb-25 {
  margin-bottom: 25px;
}

.img-wrapper {
  position: relative;

  .mask {
    position: absolute;
    z-index: 10;
    width: 200px;
    height: 100%;

    &.left_mask {
      left: 0;
      background: linear-gradient(270deg, rgba(249, 249, 250, 0) 0%, #ffffff 100%);
    }
    &.right_mask {
      right: 0;
      background: linear-gradient(270deg, #ffffff 0%, rgba(249, 249, 250, 0) 100%);
    }
  }
}
</style>
