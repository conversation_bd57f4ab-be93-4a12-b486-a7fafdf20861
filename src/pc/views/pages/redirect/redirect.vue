<style lang="scss" scoped>
.redirect {
  width: 540px;
  min-height: calc(100vh - 64px - 242px);
  padding-top: 100px;
  margin: 0 auto;
}
.logo {
  padding: 0 30px;
  font-size: 28px;
  font-weight: bold;
  color: #333;
}
.info-card {
  margin-top: 8px;
  padding: 20px 30px;
  border-radius: 4px;
  border: 1px solid #d8d8d8;
  background-color: #fff;

  .row1 {
    font-weight: 600;
    font-size: 16px;
    color: #333;
  }
  .row2 {
    margin-top: 12px;
    font-size: 12px;
    color: #333;
  }
  .link {
    margin-top: 4px;
    font-size: 12px;
    color: #666;
    padding-bottom: 12px;
    border-bottom: 1px solid #d8d8d8;
  }
  .jump-button {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    min-width: 74px;
    padding: 0 4px;
    height: 24px;
    border-radius: 4px;
    background: #d8131a;
    font-weight: 500;
    font-size: 12px;
    color: #fff;
    cursor: pointer;
  }
}
</style>

<template>
  <div class="bg-#F5F6F9">
    <div class="redirect">
      <div class="logo">{{ $t('redirect.title') }}</div>
      <div class="info-card">
        <div class="row1">{{ $t('redirect.tip1') }}</div>
        <div class="row2">{{ $t('redirect.tip2') }}</div>
        <div class="link">{{ decodeURIComponent(route.query?.target) }}</div>
        <div class="mt-3 text-right">
          <div class="jump-button" @click="toOutside">{{ $t('redirect.confirm') }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const route = useRoute()

const toOutside = () => {
  location.href = decodeURIComponent(route.query?.target)
}
</script>
