<template>
  <div>
    <img class="block w-full" v-for="img in imgList" :src="img" :key="img" />
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'

const OSS_DIR = 'https://static.chinamarket.cn/static/trade-exhibition/global-shop/'
const { locale } = useI18n()

const imgList = computed(() => [
  `${OSS_DIR}${locale.value}/banner@${Math.min(parseInt(devicePixelRatio ?? 1), 2)}x.webp`,
  `${OSS_DIR}${locale.value}/supplier@${Math.min(parseInt(devicePixelRatio ?? 1), 2)}x.webp?v=1.1`,
  `${OSS_DIR}${locale.value}/compare@${Math.min(parseInt(devicePixelRatio ?? 1), 2)}x.webp`,
  `${OSS_DIR}${locale.value}/advantage@${Math.min(parseInt(devicePixelRatio ?? 1), 2)}x.webp`,
  `${OSS_DIR}${locale.value}/type@${Math.min(parseInt(devicePixelRatio ?? 1), 2)}x.webp`,
  `${OSS_DIR}${locale.value}/product-class@${Math.min(parseInt(devicePixelRatio ?? 1), 2)}x.webp`,
])
</script>
