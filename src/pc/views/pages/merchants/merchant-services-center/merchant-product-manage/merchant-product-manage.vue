<template>
  <div class="product-manage bg-white p-4 h-full flex flex-col">
    <div class="header flex justify-between">
      <div class="search flex mb-4">
        <el-input v-model="searchValue" class="mr-2" size="large" clearable :placeholder="t('searchPlaceHolder')" />
        <el-input v-model="searchNo" class="mr-2" size="large" clearable :placeholder="t('searchPlaceHolderNo')" />
        <el-date-picker v-model="dateArr" type="datetimerange" start-placeholder="开始时间" end-placeholder="结束时间" :default-time="defaultTime" />
        <el-button type="primary" size="large" @click="searchClick">{{ t('search') }}</el-button>
      </div>
    </div>
    <el-table border :data="tableData" show-overflow-tooltip v-loading="loading">
      <!--      <el-table-column label="序号">-->
      <!--        <template #default="scope">-->
      <!--          {{ scope.$index + 1 + (currentPage - 1) * pageSize }}-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column v-for="(item, index) in columns" :key="index" :type="item.type" :prop="item.prop" :label="item.label" :min-width="item.width">
        <template #default="scope">
          <span v-if="item.type">{{ index + 1 }}</span>
          <span v-else-if="item.prop === 'category'">{{ scope.row.category.categoryName }}</span>
          <span v-else-if="item.prop === 'maxPrice'">{{ formatPrice(scope.row.maxPrice) }}</span>
          <span v-else-if="item.prop === 'spuName'">
            <div class="flex items-center overflow-hidden">
              <img-loader
                :src="scope.row.spuImages?.split(',')?.[0]"
                v-if="scope.row.spuImages"
                img-class="w-[40px] h-[40px] object-cover mr-[6px] bg-[#f8f8f8]"
                alt=""
              />
              <div
                class="text-ellipsis overflow-hidden whitespace-nowrap"
                :class="scope.row.publishStatus ? 'cursor-pointer hover:underline hover:text-[#257BFB]' : ''"
                @click="handleGoodsNameClick(scope.row)"
              >
                {{ scope.row.spuName }}
              </div>
            </div>
          </span>
          <div v-else-if="item.prop === 'publishStatus'" class="flex items-center">
            <div class="dot" :class="scope.row.publishStatus ? 'green' : ''"></div>
            {{ scope.row.publishStatus ? t('on') : t('off') }}
          </div>
          <span v-else-if="item.prop === 'createTime'">{{ formatTime(scope.row.createTime) }}</span>
          <span v-else>{{ scope.row[item.prop] || '-' }}</span>
        </template>
      </el-table-column>
      <template #empty>
        <EmptyText />
      </template>
    </el-table>
    <div class="pagination-wrapper" v-if="total > pageSize">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :background="true"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import EmptyText from '@/pc/components/empty-text/empty-text.vue'
import { getSellerServiceGoodsList } from '@/apis/goods'
import { formatTime } from '@/common/js/date'
import { debounce } from '@/common/js/util'

const { t } = useI18n({
  messages: {
    zh: {
      search: '搜索',
      searchPlaceHolder: '请输入商品名称',
      searchPlaceHolderNo: '请输入商品编号',
      no: '序号',
      productID: '商品编号',
      productCode: '商品编码',
      productName: '商品名称',
      productCategories: '商品类别',
      brand: '品牌',
      price: '价格',
      stock: '库存',
      sales: '销量',

      confirmText: '确认执行此操作吗？',
      onSuccess: '上架成功',
      offSuccess: '下架成功',
      goodsStatus: '商品状态',
      on: '上架',
      off: '下架',
      createTime: '创建时间',
    },
    en: {
      search: 'Search',
      searchPlaceHolder: 'Please enter product name',
      searchPlaceHolderNo: 'Please enter product product ID',
      no: 'No.',
      productID: 'Product ID',
      productCode: 'Product Code',
      productName: 'Product Name',
      productCategories: 'Product Categories',
      brand: 'Brand',
      price: 'Price',
      stock: 'Stock',
      sales: 'Sales',
      confirmText: 'Are you sure to perform this operation?',
      onSuccess: 'Listed Successfully',
      offSuccess: ' Unlisted Successfully',
      goodsStatus: 'Status',
      on: 'List',
      off: 'Delist',
      createTime: 'Create Time',
    },
  },
})

const tableData = ref([])
const loading = ref(true)

// columns
const columns = ref([
  { prop: 'spuName', label: computed(() => t('productName')), width: '200' },
  { prop: 'id', label: computed(() => t('productID')), width: '200' },
  // { prop: 'goodsNo', label: computed(() => t('productCode')), width: '180' },
  // { prop: 'brandName', label: computed(() => t('brand')), width: '' },
  { prop: 'maxPrice', label: computed(() => t('price')), width: '110' },
  { prop: 'stock', label: computed(() => t('stock')), width: '110' },
  { prop: 'saleNum', label: computed(() => t('sales')), width: '110' },
  { prop: 'publishStatus', label: computed(() => t('goodsStatus')), width: '110' },
  { prop: 'createTime', label: computed(() => t('createTime')), width: '200' },
])

const searchValue = ref(null)
const searchNo = ref(null)
const dateArr = ref([])
const defaultTime = [formatTime(Date.now(), 'YYYY-MM-DD 00:00:00'), formatTime(Date.now(), 'YYYY-MM-DD 23:59:59')]

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 获取列表
const getList = async () => {
  loading.value = true
  try {
    const body = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      spuName: searchValue.value,
      id: searchNo.value,
      createStartTime: dateArr.value?.[0] ? formatTime(dateArr.value[0]) : null,
      createEndTime: dateArr.value?.[1] ? formatTime(dateArr.value[1]) : null,
    }
    const { totalRecord, rowList } = await getSellerServiceGoodsList(body)
    tableData.value = rowList
    total.value = totalRecord
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}
getList()

// 获取列表 - 防抖包装
const DEBOUNCE_TIME = 300
const debounceGetData = debounce(() => {
  getList()
}, DEBOUNCE_TIME)

// 搜索
const searchClick = () => {
  currentPage.value = 1
  debounceGetData()
}

//分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getList()
}
const handleCurrentChange = (val) => {
  currentPage.value = val
  getList()
}

const router = useRouter()
const handleGoodsNameClick = ({ publishStatus, id }) => {
  if (!publishStatus) return
  const url = router.resolve({
    name: 'goodsDetail',
    params: {
      id,
    },
  })
  window.open(url.href, '_blank')
}

const formatPrice = (price) => {
  return Number(price).toFixed(2) || '0.00'
}
</script>

<style lang="scss" scoped>
.product-manage {
  .search {
    .el-input {
      width: 200px;
    }
  }
}

.dot {
  border-radius: 50%;
  width: 6px;
  height: 6px;
  margin-right: 4px;
  background: #ff0000;

  &.green {
    background: #0abd52;
  }
}

:deep() {
  .el-date-editor.el-input,
  .el-date-editor.el-input__wrapper {
    width: 360px;
    height: 40px;
    margin-right: 8px;
  }

  .el-range-input {
    width: 44%;
  }
}
</style>
