<template>
  <div v-if="!validRole" class="seller-center" v-loading="!validRole"></div>
  <LayoutCenter
    :title="MERCHANTS_TYPE.MERCHANT_SERVICE.title[$i18n.locale]"
    :subTabs="subTabs"
    contentClass="flex flex-col"
    :user-type="MERCHANTS_TYPE.MERCHANT_SERVICE.id"
    v-else
  >
    <ShareHeader />
    <router-view />
  </LayoutCenter>
</template>

<script setup>
import LayoutCenter from '../components/layout-center.vue'
import ShareHeader from '../components/share-header.vue'
import { MERCHANTS_TYPE } from '@/constants/merchants'
import { useMerchantsHooks } from '@/pc/hooks/merchants'

const subTabs = [
  {
    title: { zh: '供应商管理', en: 'Merchant Management' },
    name: 'merchantManage',
    icon: 'icon-shangjiaziliaoguanli-',
  },
  {
    title: { zh: '商品管理', en: 'Product Management' },
    name: 'merchantProductManage',
    icon: 'icon-shangpinguanli',
  },
  // {
  //   title: { zh: '订单管理', en: 'Order Management' },
  //   name: 'merchantOrderManage',
  //   icon: 'icon-shangpinguanli',
  // },
  {
    title: { zh: '账户管理', en: 'Account Management' },
    name: 'merchantServicesInfo',
    icon: 'icon-wodeshangpinshoucang',
  },
]

const { validRole } = useMerchantsHooks(MERCHANTS_TYPE.MERCHANT_SERVICE.id)
</script>

<style scoped lang="scss">
.seller-center {
  min-height: calc($main-height - 64px);
}
</style>
