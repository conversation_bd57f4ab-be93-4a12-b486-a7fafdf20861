<template>
  <div class="flex items-center mb-[16px]">
    <div v-for="item in statisticsList" :key="item.prop" class="w-[30%] h-[52px]">
      <el-statistic :title="item.label" :precision="2" :value="statisticValue(baseInfo[item.prop])">
        <template #suffix>
          <span class="small-text">{{ item.suffix }}</span>
        </template>
      </el-statistic>
    </div>
  </div>
</template>

<script setup>
defineProps({
  baseInfo: {
    type: Object,
    default: () => ({}),
  },
})

const statisticsList = [
  {
    label: '账户余额',
    prop: 'amount',
    suffix: '元',
  },
  {
    label: '已入账金额',
    prop: 'completeAmount',
    suffix: '元',
  },
  {
    label: '总营收',
    prop: 'sumAddAmount',
    suffix: '元',
  },
]

const statisticValue = (val) => {
  return +val || 0
}
</script>

<style scoped lang="scss">
.small-text {
  font-size: 12px;
  color: #333;
}

:deep(.el-statistic__head) {
  font-family: PingFang SC;
  font-size: 14px;
  color: #666;
}

:deep(.el-statistic__number) {
  font-family: PingFang SC;
  font-size: 18px;
  font-weight: 500;
}
</style>
