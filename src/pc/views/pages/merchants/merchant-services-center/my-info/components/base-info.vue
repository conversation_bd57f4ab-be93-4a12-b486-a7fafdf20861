<template>
  <el-form :inline="true" :model="infoForm" :rules="rules" ref="infoFormRef">
    <el-form-item label="银行账号" prop="bankAccountNo">
      <el-input v-model="infoForm.bankAccountNo" placeholder="请输入银行账号" clearable class="min-w-[225px]" />
    </el-form-item>
    <el-form-item label="账户名" prop="bankAccountName">
      <el-input v-model="infoForm.bankAccountName" placeholder="请输入账户名" clearable class="min-w-[225px]" />
    </el-form-item>
    <el-form-item label="开户行" prop="bankBranchName">
      <el-input v-model="infoForm.bankBranchName" placeholder="请输入开户行" clearable class="min-w-[225px]" />
    </el-form-item>
    <div class="text-center">
      <el-button type="primary" @click="onSubmit" :loading="loading">确认修改</el-button>
      <el-button @click="onReset">重置</el-button>
    </div>
  </el-form>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { updateUserInfo } from '@/apis/merchants'

const props = defineProps({
  baseInfo: {
    type: Object,
    default: () => ({}),
  },
})

const rules = reactive({
  bankAccountNo: [
    {
      required: true,
      message: '请输入银行账号',
      trigger: ['change', 'blur'],
    },
    {
      pattern: /^[A-Za-z0-9]{5,34}$/,
      message: '请输入正确的银行账号',
      trigger: ['change', 'blur'],
    },
  ],
  bankAccountName: [
    {
      required: true,
      message: '请输入账户名',
      trigger: ['change', 'blur'],
    },
    { min: 2, max: 50, message: '请输入正确的账户名', trigger: ['change', 'blur'] },
  ],
  bankBranchName: [
    {
      required: true,
      message: '请输入开户行',
      trigger: ['change', 'blur'],
    },
    { min: 2, max: 100, message: '请输入正确的开户行', trigger: ['change', 'blur'] },
  ],
})

const infoForm = ref({
  bankAccountNo: '',
  bankAccountName: '',
  bankBranchName: '',
})

const init = () => {
  infoForm.value = {
    bankAccountNo: props.baseInfo.bankAccountNo,
    bankAccountName: props.baseInfo.bankAccountName,
    bankBranchName: props.baseInfo.bankBranchName,
  }
}

const onReset = () => {
  if (infoFormRef.value) {
    infoFormRef.value.resetFields()
  } else {
    infoForm.value = {
      bankAccountNo: '',
      bankAccountName: '',
      bankBranchName: '',
    }
  }
}

watchEffect(() => {
  if (props.baseInfo.bankAccountNo) {
    init()
  }
})

const infoFormRef = ref(null)
const isSameParams = () => {
  return Object.keys(infoForm.value).every((key) => infoForm.value[key] === props.baseInfo?.[key])
}
const onSubmit = () => {
  infoFormRef.value.validate((valid, fields) => {
    if (valid) {
      if (isSameParams()) {
        ElMessage.success({
          message: '修改成功',
        })
        return
      }
      onSubmitRequest()
    } else {
      console.log(fields)
    }
  })
}
const loading = ref(false)
const emits = defineEmits(['getUserData'])
const onSubmitRequest = async () => {
  try {
    loading.value = true
    const params = Object.assign({}, infoForm.value)
    await updateUserInfo(params)
    ElMessage.success('修改成功')
    emits('getUserData')
  } catch (e) {
    ElMessage.error(e.message || e.msg)
  } finally {
    loading.value = false
  }
}
</script>
