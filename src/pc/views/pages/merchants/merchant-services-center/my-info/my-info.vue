<template>
  <div class="product-manage h-full flex flex-col">
    <div class="p-4 bg-white mb-[16px]">
      <div class="info-title font-600 text-4 flex items-center mb-[16px]">
        <div class="info-title-line mr-2"></div>
        <div>基础信息</div>
      </div>
      <BaseInfo :baseInfo="baseInfo" @getUserData="getUserData" />
    </div>
    <div class="p-4 h-full flex flex-col bg-white">
      <div class="info-title font-600 text-4 flex items-center mb-[16px]">
        <div class="info-title-line mr-2"></div>
        <div>账户营收</div>
      </div>
      <Statistics :baseInfo="accountBaseInfo" />
      <div class="info-title font-600 text-4 flex items-center mb-[16px]">
        <div class="info-title-line mr-2"></div>
        <div>结算明细</div>
      </div>
      <el-table border :data="tableData" show-overflow-tooltip v-loading="loading">
        <el-table-column v-for="(item, index) in columns" :key="index" :type="item.type" :prop="item.prop" :label="item.label" :min-width="item.width">
          <template #default="scope">
            <span v-if="item.type">{{ index + 1 }}</span>
            <span v-else-if="item.prop === 'recordType'">{{ recordTypeEnum[scope.row.recordType] || '-' }}</span>
            <span v-else-if="item.prop === 'status'">{{ statusEnum[scope.row.status] || '-' }}</span>
            <span v-else-if="item.prop === 'completeTime'">{{ formatTime(scope.row.completeTime, 'YYYY年MM月DD日') || '-' }}</span>
            <span v-else>{{ scope.row[item.prop] }}</span>
          </template>
        </el-table-column>
        <template #empty>
          <EmptyText />
        </template>
      </el-table>
      <div class="pagination-wrapper" v-if="total > pageSize">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :background="true"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script setup>
import EmptyText from '@/pc/components/empty-text/empty-text.vue'
import BaseInfo from './components/base-info.vue'
import Statistics from './components/statistics.vue'
import { getUserAccountInfo, getUserAccountList, getUserInfo } from '@/apis/merchants'
import { formatTime } from '@/common/js/date'

const tableData = ref([])
const loading = ref(true)

const recordTypeEnum = {
  1: '收入',
  2: '提现',
}

const statusEnum = {
  1: '已入账',
  0: '未入账',
}

// columns
const columns = ref([
  { prop: 'completeTime', label: '结算日期', width: '90' },
  { prop: 'amount', label: '结算金额(元)' },
  { prop: 'recordType', label: '结算类型' },
  { prop: 'sourceTypeNo', label: '订单编号', width: '150' },
  { prop: 'status', label: '入账状态' },
  { prop: 'afterAmount', label: '账户余额(元)' },
])

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const baseInfo = ref({})
const accountBaseInfo = ref({})

// 获取详情
const getUserData = async () => {
  try {
    baseInfo.value = await getUserInfo()
  } catch (error) {
    console.log(error)
  }
}
// 获取详情
const getAccountInfo = async () => {
  try {
    accountBaseInfo.value = await getUserAccountInfo()
    await getList()
  } catch (error) {
    console.log(error)
  }
}
// 获取列表
const getList = async () => {
  const { id } = accountBaseInfo.value || {}
  if (!id) return
  loading.value = true
  try {
    const body = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      accountId: id,
    }
    const { totalRecord, rowList } = await getUserAccountList(body)
    tableData.value = rowList
    total.value = totalRecord
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}

//分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getList()
}
const handleCurrentChange = (val) => {
  currentPage.value = val
  getList()
}

onMounted(() => {
  getUserData()
  getAccountInfo()
})
</script>

<style lang="scss" scoped>
.info-title-line {
  &::before {
    content: '';
    display: block;
    width: 3px;
    height: 16px;
    background: $primary-color;
    vertical-align: baseline;
  }
}
</style>
