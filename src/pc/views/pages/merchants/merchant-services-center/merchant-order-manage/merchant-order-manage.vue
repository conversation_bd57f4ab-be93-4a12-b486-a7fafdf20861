<template>
  <div class="product-manage bg-white p-4 h-full flex flex-col">
    <div class="header flex justify-between">
      <div class="search flex mb-4">
        <el-input v-model="searchValue" class="mr-2" size="large" clearable :placeholder="t('searchPlaceHolder')" />
        <el-button type="primary" size="large" @click="searchClick">{{ t('search') }}</el-button>
      </div>
    </div>
    <el-table border :data="tableData" show-overflow-tooltip v-loading="loading">
      <el-table-column label="序号">
        <template #default="scope">
          {{ scope.$index + 1 + (currentPage - 1) * pageSize }}
        </template>
      </el-table-column>
      <el-table-column v-for="(item, index) in columns" :key="index" :type="item.type" :prop="item.prop" :label="item.label" :min-width="item.width">
        <template #default="scope">
          <span v-if="item.type">{{ index + 1 }}</span>
          <span v-else-if="item.prop === 'category'">{{ scope.row.category.categoryName }}</span>
          <span v-else>{{ scope.row[item.prop] }}</span>
        </template>
      </el-table-column>
      <template #empty>
        <EmptyText />
      </template>
    </el-table>
    <div class="pagination-wrapper" v-if="total > pageSize">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :background="true"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import EmptyText from '@/pc/components/empty-text/empty-text.vue'
import { getSellerGoodsList } from '@/apis/mall'
import { debounce } from '@/common/js/util'

const { t } = useI18n({
  messages: {
    zh: {
      search: '搜索',
      searchPlaceHolder: '请输入商品名称、品牌或商品编码',
      no: '序号',
      productID: '商品编号',
      productCode: '商品编码',
      productName: '商品名称',
      productCategories: '商品类别',
      brand: '品牌',

      confirmText: '确认执行此操作吗？',
      onSuccess: '上架成功',
      offSuccess: '下架成功',
    },
    en: {
      search: 'Search',
      searchPlaceHolder: 'Please enter product name, brand or product ID',
      no: 'No.',
      productID: 'Product ID',
      productCode: 'Product Code',
      productName: 'Product Name',
      productCategories: 'Product Categories',
      brand: 'Brand',
      confirmText: 'Are you sure to perform this operation?',
      onSuccess: 'Listed Successfully',
      offSuccess: ' Unlisted Successfully',
    },
  },
})

const tableData = ref([])
const loading = ref(true)

// columns
const columns = ref([
  { prop: 'goodsIdentifier', label: computed(() => t('productID')), width: '180' },
  { prop: 'goodsNo', label: computed(() => t('productCode')), width: '180' },
  { prop: 'goodsName', label: computed(() => t('productName')), width: '180' },
  { prop: 'category', label: computed(() => t('productCategories')), width: '180' },
  { prop: 'brand', label: computed(() => t('brand')), width: '180' },
])

const searchValue = ref(null)

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 获取列表
const getList = async () => {
  loading.value = true
  try {
    const body = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      search: searchValue.value,
    }
    const { totalRecord, rowList } = await getSellerGoodsList(body)
    tableData.value = rowList
    total.value = totalRecord
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}
getList()

// 获取列表 - 防抖包装
const DEBOUNCE_TIME = 300
const debounceGetData = debounce(() => {
  getList()
}, DEBOUNCE_TIME)

// 搜索
const searchClick = () => {
  currentPage.value = 1
  debounceGetData()
}

//分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getList()
}
const handleCurrentChange = (val) => {
  currentPage.value = val
  getList()
}
</script>

<style lang="scss" scoped>
.product-manage {
  .search {
    .el-input {
      width: 240px;
    }
  }
}
</style>
