<template>
  <div class="product-manage bg-white p-4 h-full flex flex-col">
    <div class="header flex justify-between">
      <div class="search flex mb-4">
        <el-input v-model="searchValue" class="mr-2" size="large" clearable :placeholder="t('searchPlaceHolder')" />
        <el-button type="primary" size="large" @click="searchClick">{{ t('search') }}</el-button>
      </div>
    </div>
    <el-table border :data="tableData" show-overflow-tooltip v-loading="loading">
      <el-table-column v-for="(item, index) in columns" :key="index" :type="item.type" :prop="item.prop" :label="item.label" :width="item.width">
        <template #default="scope">
          <span v-if="item.type">{{ index + 1 }}</span>
          <span v-else-if="item.prop === 'category'">{{ scope.row.category.categoryName }}</span>
          <span v-else>{{ scope.row[item.prop] || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right">
        <template #default="scope">
          <el-button text @click="handleUnBindClick(scope.row)" :loading="unBindLoading === scope.row.id">解绑</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <EmptyText />
      </template>
    </el-table>
    <div class="pagination-wrapper" v-if="total > pageSize">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :background="true"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import { useI18n } from 'vue-i18n'
import EmptyText from '@/pc/components/empty-text/empty-text.vue'
import { getUserInviteePage, inviteeRemove } from '@/apis/merchants'
import { debounce } from '@/common/js/util'

const { t } = useI18n({
  messages: {
    zh: {
      search: '搜索',
      searchPlaceHolder: '请输入手机号/姓名/公司名',
      no: '序号',
      merchantID: '用户编号',
      mobile: '用户手机号',
      name: '姓名',
      companyName: '公司名',
      address: '地址',
      weChat: '微信号',
      idCard: '身份证号',
      operator: '操作',
    },
    en: {
      search: 'Search',
      searchPlaceHolder: 'Please enter',
      no: 'No.',
      merchantID: '用户编号',
      mobile: '用户手机号',
      name: '姓名',
      companyName: '公司名',
      address: '地址',
      weChat: '微信号',
      idCard: '身份证号',
      operator: '操作',
    },
  },
})

const tableData = ref([])
const loading = ref(true)

// columns
const columns = ref([
  { prop: 'id', label: computed(() => t('merchantID')), minWidth: '150' },
  { prop: 'userName', label: computed(() => t('mobile')), minWidth: '120' },
  { prop: 'companyPersonName', label: computed(() => t('name')), minWidth: '100' },
  { prop: 'companyName', label: computed(() => t('companyName')), minWidth: '150' },
  { prop: 'companyAddress', label: computed(() => t('address')), minWidth: '150' },
  { prop: 'contactWechat', label: computed(() => t('weChat')), minWidth: '150' },
  // { prop: 'idNo', label: computed(() => t('idCard')), width: '160' },
])

const searchValue = ref(null)

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 获取列表
const getList = async () => {
  loading.value = true
  try {
    const body = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      search: searchValue.value,
    }
    const { totalRecord, rowList } = await getUserInviteePage(body)
    tableData.value = rowList
    total.value = totalRecord
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}
getList()

// 获取列表 - 防抖包装
const DEBOUNCE_TIME = 300
const debounceGetData = debounce(() => {
  getList()
}, DEBOUNCE_TIME)

// 搜索
const searchClick = () => {
  currentPage.value = 1
  debounceGetData()
}

//分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getList()
}
const handleCurrentChange = (val) => {
  currentPage.value = val
  getList()
}

const unBindLoading = ref(null)
const handleUnBindClick = async (row) => {
  try {
    await ElMessageBox.confirm('确定要解对该商家除绑定吗', '解绑', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    unBindLoading.value = true
    const data = await inviteeRemove({ id: row.id })
    if (data) ElMessage.success('解绑成功')
    await getList()
  } catch (e) {
    if (typeof e === 'string' && e === 'cancel') return
    ElMessage.error(e.message || e.msg)
  } finally {
    unBindLoading.value = null
  }
}
</script>

<style lang="scss" scoped>
.product-manage {
  .search {
    .el-input {
      width: 320px;
    }
  }
}
</style>
