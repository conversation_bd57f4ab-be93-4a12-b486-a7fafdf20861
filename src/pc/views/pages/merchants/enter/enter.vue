<style lang="scss" scoped>
[dir='rtl'] {
  .enter-wrap .register {
    .enter-title {
      span {
        &:nth-child(2) {
          border-right: 1px solid $primary-color;
          border-left: 1px solid $primary-color;
        }
        &:last-child {
          border-left: none;
        }
      }
    }
    .enter-form {
      .border-title {
        :deep(span:nth-child(2)) {
          margin-right: 6px;
        }
      }
      :deep(.el-checkbox__label) {
        margin-right: 6px;
      }
    }
  }
}
.enter-wrap {
  width: $main-width;
  margin: 17px auto 20px;
  .register {
    background-color: #fff;
    min-height: calc(100vh - 64px - 226px);
    .enter-title {
      background: url('https://static.chinamarket.cn/static/trade-exhibition/merchants/enter-title-bg.png');
      background-repeat: no-repeat;
      background-position: 0 0;
      background-size: 326px 63px;
      border-bottom: 1px solid $primary-color;
    }
    .enter-form {
      .title {
        border-left: 4px solid #d8131a;
        font-size: 16px;
        height: 18px;
        line-height: 18px;
      }
      .tips-item {
        margin-bottom: 10px;
        &.is-error {
          margin-bottom: 20px;
        }
      }
      .login-btn {
        width: max-content;
        padding: 8px 32px;
        font-size: 14px;
        line-height: 20px;
        text-align: center;
        border-radius: 2px;
        background: $primary-color;
        color: $basic-white;
        cursor: pointer;
        margin: 0 auto;
      }
      ::v-deep() {
        .has-empty-icon {
          .empty-icon img {
            width: 80px;
            height: 58px;
          }
        }
        .date {
          display: flex;
          flex: 1;
        }
      }
    }
  }
}
</style>
<template>
  <div class="enter-wrap flex flex-col" :key="storageLocale">
    <div class="register flex flex-col">
      <div class="enter-title w-full h-63px flex items-center pl-9 text-20px text-white font-600">商家入驻</div>
      <el-form ref="enterFormRef" :model="enterForm" :rules="enterFormRules" label-width="180px" size="large" class="enter-form p-5">
        <BorderTitle title="企业信息"></BorderTitle>
        <!-- 营业执照 -->
        <el-form-item label="营业执照" prop="companyLicenseUrl" class="business-license tips-item">
          <div class="flex flex-col justify-center items-center has-empty-icon">
            <ImgUpload
              v-model="enterForm.companyLicenseUrl"
              :emptyIcon="`${ossUrl}/mall/business-license.png`"
              :emptyText="''"
              tipsText="营业执照"
              :dir="OSS_DIR.CERT"
              :size-limit="10"
              height="122px"
              width="244px"
            >
            </ImgUpload>
          </div>
        </el-form-item>
        <el-form-item>
          <div class="h-5 leading-5">营业执照原件照片（清晰且露出4个角）</div>
        </el-form-item>
        <el-row :gutter="24">
          <el-col :span="12">
            <!-- 公司名称 -->
            <el-form-item label="公司名称" prop="companyName">
              <el-input v-model="enterForm.companyName" clearable placeholder="请输入公司名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 统一社会信用代码 -->
            <el-form-item label="统一社会信用代码" prop="companyLicenseId">
              <el-input v-model="enterForm.companyLicenseId" clearable placeholder="请输入统一社会信用代码"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="userType === 5">
            <!-- 营业执照有效期 -->
            <el-form-item label="营业执照有效期" prop="companyLicenseDate">
              <el-date-picker
                class="date mr-2"
                v-model="enterForm.companyLicenseDate"
                type="daterange"
                range-separator="-"
                clearable
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY/MM/DD"
                value-format="x"
                @change="handleDateChange"
              ></el-date-picker>
              <div class="long">
                <el-checkbox v-model="enterForm.companyLicenseDateForever" @change="handleCheckboxChange">长期</el-checkbox>
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <!-- 经营地址 -->
            <template class="flex">
              <el-form-item label="经营地址" prop="address">
                <el-cascader
                  ref="cascaderRef"
                  v-model="enterForm.address"
                  placeholder="请选择省市区"
                  :options="cityList"
                  clearable
                  filterable
                  @change="handleCascaderChange"
                />
              </el-form-item>
              <el-form-item class="flex-1 ml-10px" label="" label-width="0" prop="companyAddress">
                <el-input v-model="enterForm.companyAddress" clearable placeholder="请输入详细地址"></el-input>
              </el-form-item>
            </template>
          </el-col>
          <el-col :span="12">
            <!-- 联系人手机号 -->
            <el-form-item label="联系人手机号" prop="contactPhone">
              <el-input v-model.trim="enterForm.contactPhone" clearable placeholder="请输入联系人手机号"></el-input>
            </el-form-item>
          </el-col>
          <!-- 采购商服务商、供应商服务商需要显示邮箱 -->
          <el-col :span="12" v-if="[4, 5].includes(userType)">
            <!-- 邮箱 -->
            <el-form-item label="邮箱" prop="contactEmail">
              <el-input v-model.trim="enterForm.contactEmail" clearable placeholder="请输入邮箱"></el-input>
            </el-form-item>
          </el-col>
          <template v-if="userType === 2">
            <el-col :span="12">
              <!-- 所属市场market -->
              <el-form-item label="所属市场" prop="marketId">
                <el-select v-model="enterForm.marketId" @change="changeMarket(enterForm.marketId)" filterable clearable placeholder="请选择所属市场">
                  <el-option v-for="item in marketLists" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="12">
            <!-- 微信 -->
            <el-form-item label="微信" prop="contactWechat">
              <el-input v-model="enterForm.contactWechat" clearable placeholder="请输入微信"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <div>
          <!-- 法人信息 -->
          <BorderTitle title="法人信息"></BorderTitle>
          <el-row :gutter="24">
            <el-col :span="9" style="padding-right: 0">
              <el-form-item label="法人身份证" prop="companyPersonIdFrontUrl" class="tips-item">
                <div class="flex flex-col justify-center items-center has-empty-icon">
                  <ImgUpload
                    v-model="enterForm.companyPersonIdFrontUrl"
                    :emptyIcon="`${ossUrl}/mall/id-card-front.png`"
                    :emptyText="''"
                    tipsText="反面（人像面）"
                    :dir="OSS_DIR.ID_CARD"
                    :size-limit="10"
                    height="122px"
                    width="244px"
                  >
                  </ImgUpload>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="9" style="padding-left: 0">
              <el-form-item prop="companyPersonIdBackUrl" label-width="0" class="tips-item">
                <div class="flex flex-col justify-center items-center has-empty-icon">
                  <ImgUpload
                    v-model="enterForm.companyPersonIdBackUrl"
                    :emptyIcon="`${ossUrl}/mall/id-card-back.png`"
                    :emptyText="''"
                    tipsText="正面（国徽面）"
                    :dir="OSS_DIR.ID_CARD"
                    :size-limit="10"
                    height="122px"
                    width="244px"
                  >
                  </ImgUpload>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item style="margin-bottom: 5px">
            <div class="h-5 leading-5">法人身份证照片（清晰且露出4个角）</div>
          </el-form-item>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="法人姓名" prop="companyPersonName">
                <el-input v-model="enterForm.companyPersonName" clearable placeholder="请输入法人姓名" @blur="handleNameBlur"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="法人身份证号码" prop="companyPersonId">
                <el-input v-model.trim="enterForm.companyPersonId" maxlength="18" clearable placeholder="请输入法人身份证号码" @blur="handleBlur"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <!-- 银行账户信息 -->
        <BorderTitle title="银行账户信息"></BorderTitle>
        <el-row :gutter="24">
          <el-col :span="12">
            <!-- 银行卡号 -->
            <el-form-item label="银行卡号" prop="bankAccountNo">
              <el-input v-model.trim="enterForm.bankAccountNo" type="number" clearable placeholder="请输入银行卡号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 银行账户名称 -->
            <el-form-item label="银行账户名称" prop="bankAccountName">
              <el-input v-model="enterForm.bankAccountName" clearable placeholder="请输入银行账户名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 开户行行号 -->
            <el-form-item label="开户行行号" prop="bankBranchNo">
              <el-input v-model="enterForm.bankBranchNo" clearable placeholder="请输入开户行行号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 开户行名称 -->
            <el-form-item label="开户行名称" prop="bankBranchName">
              <el-input v-model="enterForm.bankBranchName" clearable placeholder="请输入开户行名称"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="flex justify-center mt-10px">
          <el-button type="primary" class="login-btn" @click="submitForm" :loading="submitLoading">提交</el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { computed, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ossUrl } from '@/constants/common'
import { OSS_DIR } from '@/constants/oss-dir'
import { useRegionCodeStore } from '@/pc/stores'
import { marketList } from '@/apis/market'
import { getUserAuditInfo, userInsert } from '@/apis/merchants'
import { useStorageLocale } from '@/i18n'
import { validateIDNumber } from '@/common/js/validator.js'
import { trimParamsChangeOrigin } from '@/utils/utils'

const { storageLocale } = useStorageLocale()

/**
 * userType: 商家类型
 * 1 = 采购商
 * 2 = 供应商
 * 3 = 外综服服务商
 * 4 = 供应商服务商
 * 5 = 采购商服务商
 */
const userType = ref(null)
const route = useRoute()
const router = useRouter()
userType.value = Number(route.query?.userType)

const enterFormRules = computed(() => {
  return {
    companyLicenseUrl: [{ required: true, message: '请上传营业执照', trigger: 'change' }],
    companyName: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
    companyLicenseId: [{ required: true, message: '请输入统一社会信用代码', trigger: 'blur' }],
    companyLicenseDate: [
      {
        required: true,
        message: '请选择营业执照有效期',
        trigger: 'change',
      },
    ],
    companyAddress: [{ required: userType.value !== 5, message: '请输入详细地址', trigger: 'blur' }],
    address: [{ required: userType.value !== 5, message: '请选择省市区', trigger: 'blur' }],
    contactPhone: [
      { required: true, message: '请输入联系人手机号', trigger: 'blur' },
      { pattern: userType.value !== 5 ? /^1[3-9]\d{9}$/ : '', message: '手机号格式不正确', trigger: 'blur' },
    ],
    contactEmail: [
      { required: false, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, message: '邮箱格式不正确', trigger: 'blur' },
    ],
    companyPersonIdFrontUrl: [{ required: true, message: '请上传法人身份证正面', trigger: 'change' }],
    companyPersonIdBackUrl: [{ required: true, message: '请上传法人身份证反面', trigger: 'change' }],
    companyPersonName: [{ required: true, message: '请输入法人姓名', trigger: 'blur' }],
    companyPersonId: [
      { required: true, message: '请输入法人身份证号码', trigger: 'blur' },
      {
        validator: (rule, value, callback) => {
          if (userType.value !== 5) {
            if (value && !validateIDNumber(value)) {
              callback(new Error('身份证号码格式不正确'))
            } else {
              callback() // 校验通过
            }
          } else {
            callback() // 如果 userType.value 是 5，直接通过校验
          }
        },
        trigger: 'blur',
      },
    ],
  }
})

// 城市
const regionCodeStore = useRegionCodeStore()
// 城市
const cityList = computed(() => regionCodeStore.areaListTopSD || [])
// 城市
if (!cityList.value.length) {
  regionCodeStore.queryAreaList()
}

// 入驻表单
const enterFormRef = ref(null)
const enterForm = reactive({})

// 获取用户入驻信息
const getUserInfos = async () => {
  try {
    const filterCode = {
      id: null,
      auditStatus: null,
      auditMessage: null,
      auditDateTime: null,
      auditUserId: null,
      auditUserName: null,
      createId: null,
      createName: null,
      createTime: null,
      updateId: null,
      updateName: null,
      updateTime: null,
      deleted: null,
      companyLicenseUrlOcrStatus: null,
    }
    const { provinceCode, cityCode, areaCode, companyLicenseDateForever, companyLicenseStartDate, companyLicenseEndDate, ...rest } = await getUserAuditInfo()
    // 过滤掉 filterCode 中的字段
    Object.keys(filterCode).forEach((key) => {
      delete rest[key]
    })
    Object.assign(enterForm, {
      ...rest,
      address: provinceCode ? [provinceCode, cityCode, areaCode] : null,
      companyLicenseDate: [companyLicenseStartDate, companyLicenseDateForever ? new Date('2099-12-31').getTime() : companyLicenseEndDate],
      companyLicenseDateForever: !!companyLicenseDateForever,
    })
    userType.value = rest?.userType
  } catch (error) {
    console.log(error)
  }
}
// 重新入驻时需要调用接口获取原有资料，进行回显
route.query?.from === 'audit' && getUserInfos()

// 营业执照有效期事件
const handleDateChange = () => {
  enterForm.companyLicenseDateForever = false
}
// 处理长期勾选变化
const handleCheckboxChange = () => {
  if (enterForm.companyLicenseDateForever) {
    enterForm.companyLicenseDate?.length && (enterForm.companyLicenseDate[1] = new Date('2099-12-31').getTime())
  }
}
// 失去焦点方法将带有小写x字母的转成大写X
const handleBlur = () => {
  enterForm.companyPersonId = enterForm.companyPersonId?.replace(/x/g, 'X')
}

// 选择所属城市
const cascaderRef = ref(null)
const handleCascaderChange = async () => {
  await enterFormRef.value.validateField('address')
  const selectedLabels = cascaderRef.value.getCheckedNodes()[0].pathLabels
  if (Array.isArray(selectedLabels)) {
    enterForm.provinceName = selectedLabels[0]
    enterForm.cityName = selectedLabels[1]
    enterForm.areaName = selectedLabels[2]
  }
}

// 为姓名去空格，防止英文状态无法输入空格
const handleNameBlur = () => {
  enterForm.companyPersonName = enterForm.companyPersonName?.trim()
}
// 所属市场列表
let marketLists = ref([])
const getMarketList = async () => {
  try {
    const res = await marketList()
    let list = []
    res.map((item) => {
      list = [...list, ...item.marketList]
    })
    // 将重复的id数据去重
    const uniqueMarketLists = Array.from(new Map(list.map((item) => [item.id, item])).values())
    marketLists.value = [...uniqueMarketLists]
  } catch (error) {
    console.log(error)
  }
}
const changeMarket = (marketId) => {
  marketLists.value.map((item) => {
    if (item.id == marketId) {
      enterForm.market = item.name
    }
  })
}

onMounted(() => {
  userType.value === 2 && getMarketList()
})

// 登录提交
const submitLoading = ref(false)
const submitForm = async () => {
  trimParamsChangeOrigin(enterForm)
  await enterFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      submitLoading.value = true
      try {
        const { address, companyPersonIdFrontUrl, companyPersonIdBackUrl, companyLicenseUrl, companyLicenseDate, companyLicenseDateForever, ...params } = {
          ...enterForm,
          userType: userType.value,
        }
        const body = {
          provinceCode: Array.isArray(address) ? address[0] : null,
          cityCode: Array.isArray(address) ? address[1] : null,
          areaCode: Array.isArray(address) ? address[2] : null,
          companyLicenseUrl: Array.isArray(companyLicenseUrl) ? companyLicenseUrl[0] : companyLicenseUrl,
          companyLicenseDateForever: userType.value === 5 ? (companyLicenseDateForever ? 1 : 0) : null,
          companyLicenseStartDate: companyLicenseDate?.length ? companyLicenseDate[0] : null,
          companyLicenseEndDate: companyLicenseDateForever ? '' : companyLicenseDate?.length ? companyLicenseDate[1] : null,
          companyPersonIdFrontUrl: Array.isArray(companyPersonIdFrontUrl) ? companyPersonIdFrontUrl[0] : companyPersonIdFrontUrl,
          companyPersonIdBackUrl: Array.isArray(companyPersonIdBackUrl) ? companyPersonIdBackUrl[0] : companyPersonIdBackUrl,
          ...params,
        }
        const res = await userInsert(body)
        if (res?.code === '200') {
          router.push(`/merchants-audit?userType=${userType.value}`)
        }
      } catch (error) {
        console.log(error)
      } finally {
        submitLoading.value = false
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}
</script>
