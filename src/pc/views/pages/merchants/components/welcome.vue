<template>
  <div class="welcome h-full c-#333 flex flex-col bg-[#E6E4E9]">
    <div class="title font-bold" :class="$storageLocale !== 'zh' ? 'text-9' : 'text-12'">{{ t('welcome.title') }}</div>
    <div class="desc text-[18px]" :class="$storageLocale !== 'zh' ? 'text-[18px]' : 'text-[24px]'">{{ t('welcome.desc') }}</div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'

const { t } = useI18n({
  messages: {
    zh: {
      welcome: {
        title: '欢迎回来中国大集',
        desc: '数字商贸综合服务平台，让您的生意更简单。',
      },
    },
    en: {
      welcome: {
        title: 'Welcome back to the Chinamarket',
        desc: 'One-stop comprehensive trade service platform that makes your business easier',
      },
    },
  },
})
</script>

<style lang="scss" scoped>
.welcome {
  width: 1064px;
  padding: 154px 0 0 79px;
  background-image: url('https://static.chinamarket.cn/static/trade-exhibition/mall/welcome-center.png');
  background-size: 100%;
  background-position: center;
  background-repeat: no-repeat;
  .title {
    margin-bottom: 14px;
  }
}

[dir='rtl'] .welcome {
  padding: 154px 79px 0 0;
  background-image: url('https://static.chinamarket.cn/static/trade-exhibition/mall/welcome-ar.jpg');
}
</style>
