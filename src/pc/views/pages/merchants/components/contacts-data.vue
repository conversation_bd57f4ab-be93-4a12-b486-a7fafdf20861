<template>
  <div class="contacts">
    <div class="list">
      <div class="title">平台通讯录</div>
      <el-table border :data="tableData" show-overflow-tooltip v-loading="loading">
        <el-table-column prop="contactRoleName" label="角色"> </el-table-column>
        <el-table-column prop="contactAvatarUrl" label="头像">
          <template #default="scope">
            <div :style="{ padding: '6px', display: 'flex', justifyContent: 'center' }" v-if="scope.row?.contactAvatarUrl">
              <el-image
                v-if="scope.row?.contactAvatarUrl"
                :style="{ width: '28px', height: '28px' }"
                :src="scope.row?.contactAvatarUrl"
                :preview-src-list="[scope.row?.contactAvatarUrl] || []"
                :initial-index="0"
                preview-teleported
                fit="contain"
              ></el-image>
            </div>

            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="contactName" label="昵称"> </el-table-column>

        <el-table-column prop="contactWecomUrl" label="企业微信">
          <template #default="scope">
            <div :style="{ padding: '6px', display: 'flex', justifyContent: 'center' }" v-if="scope.row?.contactAvatarUrl">
              <el-image
                v-if="scope.row?.contactWecomUrl"
                :style="{ width: '40px', height: '40px' }"
                :src="scope.row?.contactWecomUrl"
                :preview-src-list="[scope.row?.contactWecomUrl] || []"
                :initial-index="0"
                preview-teleported
                fit="contain"
              ></el-image>
            </div>

            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="contactEmail" label="企业邮箱"> </el-table-column>

        <template #empty>
          <EmptyText></EmptyText>
        </template>
      </el-table>
      <!-- 分页 -->
      <div class="pt-[20px] flex justify-center">
        <el-pagination
          background
          v-model:current-page="pageNum"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-sizes="[10, 20, 30, 40]"
          popper-class="aaaaa"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>
<script setup>
import { onMounted } from 'vue'
import EmptyText from '@/pc/components/empty-text/empty-text.vue'
import { getPlatformContact } from '@/apis/merchants'

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
})
const tableData = ref([])
// tableData.value = DATA
const loading = ref(false)
const pageNum = ref(1)
const pageSize = ref(10)
const total = ref(0)

const handleCurrentChange = (val) => {
  pageNum.value = val
  pageSize.value = 10
  props.id && getList()
}

// 分页条数变化
const handleSizeChange = (val) => {
  pageSize.value = val
  pageNum.value = 1
  props.id && getList()
}

//获取列表
const getList = async () => {
  loading.value = true
  try {
    const body = {
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      userId: props.id,
    }
    const { totalRecord, rowList } = await getPlatformContact(body)
    tableData.value = rowList
    total.value = totalRecord
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}
watch(
  () => props.id,
  (newVal) => {
    if (newVal) {
      getList(newVal)
    }
  },
  { immediate: true },
)
onMounted(() => {})
</script>
<style lang="scss" scoped>
.contacts {
  .back {
    background: #ffffff;
    padding: 8px 16px;
    font-size: 14px;
    line-height: 20px;
    color: #d8131a;
    margin-bottom: 8px;
  }
  .list {
    padding: 16px;
    background: #ffffff;
  }
  .title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
    color: #333333;
    margin-bottom: 16px;
    &::before {
      content: '';
      width: 3px;
      height: 16px;
      margin-right: 8px;
      background: #d8131a;
    }
  }
  .role-logo {
    width: 28px;
    height: 28px;
    border-radius: 50%;
  }
}
</style>
