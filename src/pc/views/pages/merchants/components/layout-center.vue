<style lang="scss" scoped>
.seller-center {
  min-height: calc($main-height - 64px);
  padding-bottom: 24px;
  .left {
    flex-shrink: 0;
    .sub-nav {
      padding: 9px 16px;
      padding-right: 0;
      line-height: 20px;
      margin-bottom: 12px;
      font-size: 14px;
      border: 1px solid #fff;
      color: #999;
      cursor: pointer;
      &.active {
        color: $primary-color;
        background: #ffeded;
        border-radius: 4px;
      }
      &.activeText {
        color: $primary-color;
      }
      &:hover {
        color: $primary-color;
      }
      .svg-icon {
        flex-shrink: 0;
      }
    }
    .child-nav {
      text-align: center;
      padding: 9px 16px;
      justify-content: center;
    }
  }
  .right {
    min-height: 500px;
    max-width: 1064px;
  }
}

[dir='rtl'] {
  .mr-2 {
    margin-left: 8px;
    margin-right: 0;
  }

  .mr-4 {
    margin-left: 16px;
    margin-right: 0;
  }

  .seller-center .left .sub-nav {
    padding-left: 0;
  }
}

#main-content {
  min-width: $main-width;
  max-width: $main-width;
  margin: 0 auto;
  padding: 0;
  min-height: $main-height;
  overflow-y: hidden;
}
</style>

<template>
  <el-main id="main-content" class="main-content">
    <CustomBreadcrumb :beforeBreadcrumb="beforeBreadcrumb"></CustomBreadcrumb>
    <div class="seller-center flex">
      <div class="left py-6 px-4 bg-white w-45 mr-4">
        <BorderTitle class="font-600" :title="title"></BorderTitle>
        <div v-for="(item, index) in newSubtabs" :key="index" @mouseover="subHover(item)" @mouseleave="subleave(item)">
          <div
            class="sub-nav flex items-center"
            @click="subTabClick(item)"
            :class="{
              active: currentTabActive === item.path || currentTabActiveName === item.name,
              activeText: currentTabActive.indexOf(item.path) !== -1,
            }"
          >
            <icon :type="item.icon" :size="16" class="mr-2"></icon>{{ item.title[$i18n.locale] }}
            <icon type="icon-shangjiantou" class="ml-8px" size="12" v-if="item.hasChild && item.showLiveChile"></icon
            ><icon type="icon-xiajiantou" class="ml-8px" size="12" v-if="item.hasChild && !item.showLiveChile"></icon>
          </div>
          <div v-if="item.hasChild && item.showLiveChile">
            <div v-for="(x, index) in item.childs" :key="index">
              <div
                class="sub-nav flex items-center child-nav"
                @click="subTabClick(x)"
                :class="{ active: currentTabActive === x.path || currentTabActiveName === x.name }"
              >
                <icon v-if="x.icon" :type="x.icon" :size="16" class="mr-2"></icon>{{ x.title[$i18n.locale] }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="right flex-1" :class="contentClass">
        <slot></slot>
      </div>
    </div>
  </el-main>
</template>

<script setup>
import CustomBreadcrumb from '@/pc/components/custom-breadcrumb/custom-breadcrumb.vue'

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  subTabs: {
    type: Array,
    default: () => [],
  },
  contentClass: {
    type: String,
    default: '',
  },
  userType: {
    type: Number,
    default: null,
  },
})

const newSubtabs = ref()
watch(
  () => props.subTabs,
  (newVal) => {
    newSubtabs.value = newVal
  },
  { immediate: true },
)
const router = useRouter()
const route = useRoute()
// 面包屑配置
const beforeBreadcrumb = (arr) => {
  if (route.name === 'addProduct') {
    arr[arr.length - 1] = {
      name: { zh: '商品管理', en: 'Product Manage' },
      path: '/seller-center/product-manage',
    }
    arr.push({
      name: route.query.id ? { zh: '商品编辑', en: 'Edit Product' } : { zh: '商品新增', en: 'Add Product' },
      path: null,
    })
  }
  if (route.name === 'myOpportunityDetail') {
    arr[arr.length - 1] = {
      name: { zh: '商机中心', en: 'Opportunity Hub' },
      path: '/buyer-center/my-opportunity',
    }
    arr.push({
      name: { zh: '商机中心详情', en: 'Opportunity Hub Details' },
      path: null,
    })
  }
  if (route.name === 'myOpportunityAdd') {
    arr[arr.length - 1] = {
      name: { zh: '商机中心', en: 'Opportunity Hub' },
      path: '/buyer-center/my-opportunity',
    }
    arr.push({
      name: { zh: '发布找货需求', en: 'Publish Opportunity' },
      path: null,
    })
  }
  if (route.name === 'buyerServiceOpportunityDetail') {
    arr[arr.length - 1] = {
      name: { zh: '商机中心', en: 'Opportunity Hub' },
      path: '/buyer-service-center/my-opportunity',
    }
    arr.push({
      name: { zh: '商机中心详情', en: 'Opportunity Hub Details' },
      path: null,
    })
  }
  if (route.name === 'buyerServiceOpportunityAdd') {
    arr[arr.length - 1] = {
      name: { zh: '商机中心', en: 'Opportunity Hub' },
      path: '/buyer-service-center/my-opportunity',
    }
    arr.push({
      name: { zh: '发布找货需求', en: 'Publish Opportunity' },
      path: null,
    })
  }
  if (route.name == 'addLiveProduct') {
    if (route.query.isEditDetail == 1) {
      //新增
      arr[arr.length - 1] = {
        name: { zh: '未入直播选品池', en: 'uninclude Live Pool' },
        path: '/seller-center/live-product-manage/uninclude-live-pool',
      }
      arr.push({
        name: { zh: '新增直播商品', en: 'Add Live Product' },
        path: null,
      })
    } else if (route.query.isEditDetail == 2) {
      //编辑
      arr[arr.length - 1] = {
        name: { zh: '已入直播选品池', en: 'uninclude Live Pool' },
        path: '/seller-center/live-product-manage/include-live-pool',
      }
      arr.push({
        name: { zh: '编辑直播商品', en: 'Edit Live Product' },
        path: null,
      })
    } else if (route.query.isEditDetail == 3) {
      //详情
      arr[arr.length - 1] = {
        name: { zh: '已入直播选品池', en: 'uninclude Live Pool' },
        path: '/seller-center/live-product-manage/include-live-pool',
      }
      arr.push({
        name: { zh: '直播商品详情', en: 'Live Product Detail' },
        path: null,
      })
    }
  }
  return arr
}

const currentTab = ref(null)
const currentTabActive = computed(() => currentTab.value || route.path)
const currentTabActiveName = computed(() => currentTab.value || route.name)

const subHover = (item) => {
  if (item.hasChild) {
    item.showLiveChile = true
  }
}
const subleave = (item) => {
  if (item.hasChild) {
    if (currentTab.value.includes(item.path)) {
      item.showLiveChile = true
    } else {
      item.showLiveChile = false
    }
  }
}
const changeActiveSub = () => {
  newSubtabs.value.map((item) => {
    if (item.hasChild && currentTab.value.includes(item.path)) {
      item.showLiveChile = true
    } else {
      item.showLiveChile = false
    }
  })
}
const subTabClick = ({ path, name, hasChild }) => {
  if (path) {
    if (hasChild) {
      return
    }
    currentTab.value = path

    router.push({ path })
    return
  }
  if (name) {
    currentTab.value = name
    router.push({ name })
  }
}
//其他页面跳转路由时左侧目录显示选中
watch(
  route,
  (val) => {
    currentTab.value = val.path
    changeActiveSub()
  },
  {
    immediate: true,
  },
)
</script>
