<template>
  <div>
    <div class="min-h-[64px] flex items-center color-[#9C3701] mb-[16px] share-header pl-4 pr-[115px] py-2 flex-wrap">
      <div class="mr-1">您的推荐码是:</div>
      <div v-if="userStore.userInfo" class="text-[20px] font-600">{{ userStore.userInfo.inviteCode }}</div>
      <div v-else class="text-[20px] font-600">-</div>
      <div
        class="mx-2 text-[12px] px-[8px] py-[2px] color-[#D8131A] border-solid border-[1px] border-[#D8131A] rounded-[2px] cursor-pointer"
        @click="handleCopy()"
      >
        复制邀请链接
      </div>
      <div>或者转发二维码</div>
      <div class="text-[12px] ml-1 color-[#257BFB] underline cursor-pointer" @click="handleShowPoster">查看大图</div>
      <!--      <el-popover placement="bottom" width="174px" trigger="click">-->
      <!--        <template #reference>-->
      <!--          <div class="text-[12px] ml-1 color-[#257BFB] underline cursor-pointer">查看大图</div>-->
      <!--        </template>-->
      <!--        <template #default>-->
      <!--          <img :src="shareImg" class="w-[100%] h-[150px] block" alt="" />-->
      <!--        </template>-->
      <!--      </el-popover>-->
      <div>，请{{ roleName }}在注册时填写您的推荐码。</div>
      <div class="right-img flex">
        <img-loader src="/mall/share-header-bg.png" class="w-[100%]" :lazy="false"></img-loader>
      </div>
    </div>
    <Dialog v-model="showPoster" :width="375" top="5vh">
      <div class="w-[375px] h-[672px] relative">
        <div class="relative w-full h-full">
          <img :src="posterUrl" alt="" class="w-full h-full" />
          <img
            :src="shareImg"
            alt=""
            class="w-[180px]] h-[180px] absolute z-1"
            :style="{ left: `${posterInfo.position?.x / 2}px`, top: `${posterInfo.position?.y / 2}px` }"
          />
        </div>
        <div class="w-full absolute bottom-[-40px] left-0 z-1 text-center">
          <el-button @click="savePoster" type="text" class="save-btn">保存图片</el-button>
        </div>
      </div>
    </Dialog>
    <div class="hidden">
      <canvas ref="canvas" width="750" height="1334"></canvas>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import QRCode from 'qrcode'
import Dialog from '@/pc/components/dialog/dialog.vue'
import { ossUrl } from '@/constants/common'
import { LANG_TYPE_ARRAY } from '@/constants/mall'
import { MERCHANTS_TYPE } from '@/constants/merchants'
import { useUserStore } from '@/pc/stores'
import { useStorageLocale } from '@/i18n/translatePlugin'
import { copyToClipboard } from '@/utils/utils'

const SHARE_BG = {
  zh: {
    [MERCHANTS_TYPE.MERCHANT_SERVICE.id]: {
      url: `${ossUrl}/merchants/share-bg-zh2-new.png`,
      position: { x: 196, y: 616 },
    },
    [MERCHANTS_TYPE.BUYER_SERVICE.id]: {
      url: `${ossUrl}/merchants/share-bg-zh.png`,
      position: { x: 196, y: 672 },
    },
  },
  en: {
    [MERCHANTS_TYPE.MERCHANT_SERVICE.id]: {
      url: `${ossUrl}/merchants/share-bg-en2.png`,
      position: { x: 196, y: 616 },
    },
    [MERCHANTS_TYPE.BUYER_SERVICE.id]: {
      url: `${ossUrl}/merchants/share-bg-en.png`,
      position: { x: 196, y: 616 },
    },
  },
  ar: {
    [MERCHANTS_TYPE.MERCHANT_SERVICE.id]: {
      url: `${ossUrl}/merchants/share-bg-ar2.png`,
      position: { x: 196, y: 616 },
    },
    [MERCHANTS_TYPE.BUYER_SERVICE.id]: {
      url: `${ossUrl}/merchants/share-bg-ar.png`,
      position: { x: 196, y: 672 },
    },
  },
}

const router = useRouter()
const userStore = useUserStore()
const isMerchantsService = computed(() => +userStore?.userInfo?.userType === MERCHANTS_TYPE.MERCHANT_SERVICE.id)
const roleName = computed(() => (isMerchantsService.value ? MERCHANTS_TYPE.SELLER.title.zh : MERCHANTS_TYPE.BUYER.title.zh))
const handleCopy = () => {
  const { inviteCode } = userStore.userInfo || {}
  if (!inviteCode) {
    ElMessage.error('邀请码异常，请稍后重试')
    return
  }
  const text = createShareUrl(userStore.userInfo?.inviteCode)
  copyToClipboard(text)
    .then(({ message }) => {
      ElMessage.success(message)
    })
    .catch(({ message }) => {
      ElMessage.error(message)
    })
}

const createShareUrl = () => {
  const localLangKey = storageLocale.value || 'zh'
  const langKey = SHARE_BG[localLangKey] ? localLangKey : 'en'
  const { key } = LANG_TYPE_ARRAY.find((item) => item.subKey === langKey) || {}
  if (isMerchantsService.value) {
    const { href } = router.resolve({
      path: '/register-h5',
      query: {
        referralCode: userStore.userInfo?.inviteCode,
        userType: isMerchantsService.value ? MERCHANTS_TYPE.SELLER.id : MERCHANTS_TYPE.BUYER.id,
        lang: key,
      },
    })
    return `${location.origin}${href}`
  }
  return `${import.meta.env.VUE_APP_MOBILE_URL}/pages-sub/redirect-page/redirect-page?referralCode=${userStore.userInfo?.inviteCode}&pageName=register&lang=${key}`
}

const shareImg = ref('')
const createQrImg = async (code) => {
  const url = createShareUrl(code)
  shareImg.value = await QRCode.toDataURL(url, { margin: 0, width: 340 })
}

const showPoster = ref(false)
const { storageLocale } = useStorageLocale()
const posterInfo = computed(() => {
  const lang = storageLocale.value || 'zh'
  const bgItem = SHARE_BG[lang] || SHARE_BG.en
  const type = +userStore?.userInfo?.userType
  return bgItem[type] || {}
})
const posterUrl = computed(() => posterInfo.value.url)
const canvas = ref(null)
const handleShowPoster = async () => {
  if (shareImg.value) {
    showPoster.value = true
    return
  }
  showPoster.value = true
  await createQrImg()
}
// const getShortUrl = async (url) => {
//   const response = await fetch(url)
//   const blob = await response.blob()
//   return URL.createObjectURL(blob)
// }
const generatePoster = async (url, { x, y }) => {
  const ctx = canvas.value.getContext('2d')
  ctx.clearRect(0, 0, 750, 1334)

  // 加载背景图
  const bgImg = new Image()
  bgImg.crossOrigin = 'Anonymous'
  bgImg.src = url

  bgImg.onload = async () => {
    ctx.drawImage(bgImg, 0, 0, 750, 1334)
    // 加载二维码图片
    const qrImg = new Image()
    qrImg.src = shareImg.value
    qrImg.onload = () => {
      ctx.drawImage(qrImg, x, y, 360, 360) // 绘制二维码

      // 生成海报URL
      const posterImg = canvas.value.toDataURL('image/png')

      const link = document.createElement('a')
      link.href = posterImg
      link.download = 'poster.png'
      link.click()
      ElMessage.success('保存成功')
    }
  }
}

// 保存海报
const savePoster = () => {
  generatePoster(`${posterUrl.value}?t=${Date.now()}`, posterInfo.value.position)
}

watchEffect(() => {
  if (userStore.userInfo?.inviteCode) {
    createQrImg(userStore.userInfo?.inviteCode)
  }
})
</script>

<style scoped lang="scss">
.share-header {
  position: relative;
  background: linear-gradient(180deg, #ffcda3 0%, #fee7cf 100%);

  .right-img {
    position: absolute;
    right: 0;
    bottom: 0;

    .img-loader {
      vertical-align: bottom;
      width: 115px;

      :deep(img) {
        vertical-align: bottom;
      }
    }
  }
}

[dir='rtl'] .share-header {
  padding-left: 115px;
  padding-right: 16px;

  .right-img {
    left: 0;
    right: auto;
  }
}

.save-btn {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
}
</style>
