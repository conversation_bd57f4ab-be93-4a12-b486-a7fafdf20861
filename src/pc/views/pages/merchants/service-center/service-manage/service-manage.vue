<template>
  <div class="product-manage bg-white p-4 h-full flex flex-col">
    <div class="header flex justify-between">
      <div class="search flex mb-4">
        <el-input v-model="searchValue" class="mr-2" size="large" clearable :placeholder="t('searchPlaceHolder')" />
        <el-button type="primary" size="large" @click="searchClick">{{ t('search') }}</el-button>
      </div>
      <el-button type="primary" size="large" @click="handleClick({}, '')">{{ t('add') }}</el-button>
    </div>
    <el-table border :data="tableData" show-overflow-tooltip v-loading="loading">
      <el-table-column
        v-for="(item, index) in columns"
        :key="index"
        :type="item.type"
        :prop="item.prop"
        :label="item.label"
        :width="item.width"
        :min-width="item.minWidth"
      >
        <template #default="scope">
          <span v-if="item.type">{{ index + 1 }}</span>
          <span v-else-if="item.prop === 'serviceType'">{{ getServiceTypeName(scope.row.serviceType) }}</span>
          <span v-else>{{ scope.row[item.prop] }}</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" :label="t('operate')" min-width="160">
        <template #default="scope">
          <el-button class="edit" link type="primary" @click="handleClick(scope.row, 'view')">{{ t('view') }}</el-button>
          <el-button class="edit" link type="primary" @click="handleClick(scope.row, 'edit')">{{ t('edit') }}</el-button>
          <el-popconfirm width="auto" :title="t('confirmText')" @confirm="onOffClick(scope.row)">
            <template #reference>
              <el-button class="edit" link type="primary">
                {{ scope.row.status ? t('off') : t('on') }}
              </el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
      <template #empty>
        <EmptyText />
      </template>
    </el-table>
    <div class="pagination-wrapper" v-if="total > pageSize">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :background="true"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import EmptyText from '@/pc/components/empty-text/empty-text.vue'
import { getDictListByKey, getServiceList, updateServiceStatus } from '@/apis/merchants'
import { debounce } from '@/common/js/util'

const { t } = useI18n({
  messages: {
    zh: {
      search: '搜索',
      searchPlaceHolder: '请输入服务名称、服务类别或服务商',
      no: '序号',
      serviceNo: '服务编号',
      serviceNumber: '服务编码',
      serviceName: '服务名称',
      serviceType: '服务类别',
      serviceProviderName: '服务商名称',
      price: '价格',
      applicableArea: '适用区域',
      add: '新增',
      operate: '操作',
      view: '详情',
      edit: '修改',
      on: '上架',
      off: '下架',
      confirmText: '确认执行此操作吗？',
      onSuccess: '上架成功',
      offSuccess: '下架成功',
    },
    en: {
      search: 'Search',
      searchPlaceHolder: 'Please enter a service name, service category, or service provider',
      no: 'No.',
      serviceNo: 'Product ID',
      serviceNumber: 'Product Code',
      serviceName: 'Product Name',
      serviceType: 'Product Categories',
      serviceProviderName: 'serviceProviderName',
      add: 'Add',
      operate: 'Operate',
      view: 'Detail',
      edit: 'Edit',
      on: 'List',
      off: 'Delist',
      confirmText: 'Are you sure to perform this operation?',
      onSuccess: 'Listed Successfully',
      offSuccess: ' Unlisted Successfully',
    },
  },
})

const tableData = ref([])
const loading = ref(true)

// columns
const columns = ref([
  { prop: 'serviceNo', label: computed(() => t('serviceNo')), width: '180' },
  { prop: 'serviceNumber', label: computed(() => t('serviceNumber')), width: '180' },
  { prop: 'serviceName', label: computed(() => t('serviceName')), width: '180' },
  { prop: 'serviceType', label: computed(() => t('serviceType')), width: '180' },
  { prop: 'serviceProviderName', label: computed(() => t('serviceProviderName')), width: '180' },
  { prop: 'price', label: computed(() => t('price')), width: '180' },
  { prop: 'applicableArea', label: computed(() => t('applicableArea')), width: '180' },
])

const searchValue = ref(null)
const router = useRouter()

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 服务类别
const serviceTypeList = ref([])
// 获取服务类别
const getserviceTypeList = async () => {
  try {
    serviceTypeList.value = await getDictListByKey({ key: 'service_type' })
  } catch (error) {
    console.log(error)
  }
}
getserviceTypeList()

// 根据id获取服务类别名称
const getServiceTypeName = (id) => {
  const serviceType = serviceTypeList.value.find((item) => item.id === id)
  return serviceType ? serviceType.name : ''
}

// 获取列表
const getList = async () => {
  loading.value = true
  try {
    const body = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      query: searchValue.value,
    }
    const { totalRecord, rowList } = await getServiceList(body)
    tableData.value = rowList
    total.value = totalRecord
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}
getList()

// 获取列表 - 防抖包装
const DEBOUNCE_TIME = 300
const debounceGetData = debounce(() => {
  getList()
}, DEBOUNCE_TIME)

// 搜索
const searchClick = () => {
  currentPage.value = 1
  debounceGetData()
}
// 新增、修改
const handleClick = (row, type) => {
  const path = '/service-center/add-service'
  type ? router.push(`${path}?id=${row?.id}&type=${type}`) : router.push(`${path}`)
}

// 上架下架
const onOffClick = async (row) => {
  try {
    const body = { id: row.id, status: row.status ? 0 : 1 }
    await updateServiceStatus(body)
    ElMessage.success(row.status ? t('offSuccess') : t('onSuccess'))
  } catch (error) {
    console.log(error)
  } finally {
    getList()
  }
}

//分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getList()
}
const handleCurrentChange = (val) => {
  currentPage.value = val
  getList()
}
</script>

<style lang="scss" scoped>
.product-manage {
  .search {
    .el-input {
      width: 260px;
    }
  }
}
</style>
