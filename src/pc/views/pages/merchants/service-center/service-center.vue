<template>
  <div v-if="!validRole" class="seller-center" v-loading="!validRole"></div>
  <LayoutCenter
    :title="MERCHANTS_TYPE.FOREIGN_TRADE_SERVICE.title[$i18n.locale]"
    :subTabs="subTabs"
    :user-type="MERCHANTS_TYPE.FOREIGN_TRADE_SERVICE.id"
    v-else
  >
    <router-view />
  </LayoutCenter>
</template>

<script setup>
import LayoutCenter from '../components/layout-center.vue'
import { MERCHANTS_TYPE } from '@/constants/merchants'
import { useMerchantsHooks } from '@/pc/hooks/merchants'

const subTabs = [
  {
    title: { zh: '资料管理', en: 'Service Information Management' },
    path: '/service-center/service-info-manage',
    icon: 'icon-shangjiaziliaoguanli-',
  },
  { title: { zh: '服务管理', en: 'Service Management' }, path: '/service-center/service-manage', icon: 'icon-fuwushangguanli' },
  { title: { zh: '平台通讯录', en: 'Contacts' }, path: '/service-center/contacts', icon: 'icon-a-dianpuguanliicon' },
]

const { validRole } = useMerchantsHooks(MERCHANTS_TYPE.FOREIGN_TRADE_SERVICE.id)
</script>

<style scoped lang="scss">
.seller-center {
  min-height: calc($main-height - 64px);
}
</style>
