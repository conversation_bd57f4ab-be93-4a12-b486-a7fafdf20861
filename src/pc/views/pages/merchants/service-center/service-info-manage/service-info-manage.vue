<style lang="scss" scoped>
.service-info-manage {
  .tips-item {
    margin-bottom: 10px;
  }
  .confirm-update {
    width: max-content;
    margin: 0 auto;
  }
  ::v-deep() .el-cascader {
    width: 100%;
  }
  .business-license {
    .title {
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .svg-icon {
      flex-shrink: 0;
    }
  }
}
</style>

<template>
  <div class="service-info-manage bg-white p-4 h-full flex flex-col">
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px" class="service-form flex-1" :class="{ 'en-ellipsis': enEllipsis }">
      <el-row :gutter="24">
        <el-col :span="8">
          <!-- 手机号 -->
          <el-form-item :label="t('mobile')" prop="userName" :title="enEllipsis ? t('mobile') : ''">
            <el-input
              v-model="formData.userName"
              maxlength="11"
              oninput="value=value.replace(/[^0-9.]/g,'')"
              clearable
              :placeholder="t('mobilePlaceholder')"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <!-- 电子邮箱 -->
          <el-form-item :label="t('email')" prop="contactEmail">
            <el-input v-model.trim="formData.contactEmail" clearable :placeholder="t('emailPlaceholder')" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <!-- 公司名 -->
          <el-form-item :label="t('companyName')" :title="enEllipsis ? t('companyName') : ''">
            <el-input v-model="formData.companyName" clearable disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <!-- 所在地区 -->
        <el-col :span="8">
          <el-form-item :label="t('city')">
            <el-cascader v-model="formData.city" :placeholder="t('cityPlaceholder')" :options="cityList" clearable />
          </el-form-item>
        </el-col>
        <!-- 详细地址 -->
        <el-col :span="8">
          <el-form-item :label="t('address')">
            <el-input v-model.trim="formData.companyAddress" clearable :placeholder="t('addressPlaceholder')" />
          </el-form-item>
        </el-col>
        <!-- 服务类别 -->
        <el-col :span="8">
          <el-form-item :label="t('serviceType')">
            <el-select v-model="formData.serviceType" clearable :placeholder="t('serviceTypePlaceholder')">
              <el-option v-for="(item, index) in serviceTypeList" :key="index" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="7">
          <el-form-item :label="t('idCardImg')" prop="idFrontUrl" class="tips-item">
            <div class="flex flex-col justify-center items-center has-empty-icon">
              <ImgUpload
                v-model="formData.companyPersonIdFrontUrl"
                :emptyIcon="`${ossUrl}/mall/id-card-front.png`"
                :emptyText="''"
                :tipsText="t('idCardFrontPlaceholder')"
                :dir="OSS_DIR.ID_CARD"
                :size-limit="10"
                height="122px"
                width="244px"
              >
              </ImgUpload>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item prop="idBackUrl" label-width="40" class="tips-item">
            <div class="flex flex-col justify-center items-center ml-3 has-empty-icon">
              <ImgUpload
                v-model="formData.companyPersonIdBackUrl"
                :emptyIcon="`${ossUrl}/mall/id-card-back.png`"
                :emptyText="''"
                :tipsText="t('idCardBackPlaceholder')"
                :dir="OSS_DIR.ID_CARD"
                :size-limit="10"
                height="122px"
                width="244px"
              >
              </ImgUpload>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item style="margin-bottom: 5px">
        <div class="h-5 leading-5 c-#BEBEBE">{{ t('idCardSellerTips') }}</div>
      </el-form-item>
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item :label="t('userName')" prop="companyPersonName">
            <el-input v-model="formData.companyPersonName" clearable :placeholder="t('userNamePlaceholder')" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('idNo')" prop="companyPersonId">
            <el-input v-model.trim="formData.companyPersonId" maxlength="18" clearable :placeholder="t('idNoPlaceholder')" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="8">
          <!-- 营业执照 -->
          <el-form-item :label="t('certUrl')" :title="enEllipsis ? t('certUrl') : ''" prop="companyLicenseUrl" class="business-license tips-item">
            <template #label>
              <span class="flex items-center">
                <span class="mr-1 title">{{ t('certUrl') }}</span>
                <el-tooltip :content="t('certUrlTips')" placement="top">
                  <icon type="icon-tishi" :size="16" />
                </el-tooltip>
              </span>
            </template>
            <div class="flex flex-col justify-center items-center">
              <ImgUpload
                v-model="formData.companyLicenseUrl"
                :emptyIcon="`${ossUrl}/mall/business-license.png`"
                :emptyText="''"
                :tipsText="''"
                :dir="OSS_DIR.CERT"
                :size-limit="10"
                height="122px"
                width="228px"
              >
              </ImgUpload>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item>
        <div class="h-5 leading-5 c-#BEBEBE">{{ t('certUrlBottomTips') }}</div>
      </el-form-item>
      <el-row :gutter="24">
        <el-col :span="8">
          <!-- 开户行 -->
          <el-form-item :label="t('depositBank')" prop="bankBranchName">
            <el-input v-model.trim="formData.bankBranchName" maxlength="30" clearable :placeholder="t('depositBankPlaceholder')" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <!-- 银行账号 -->
          <el-form-item :label="t('bankNo')" prop="bankAccountNo">
            <el-input v-model.trim="formData.bankAccountNo" type="number" maxlength="30" clearable :placeholder="t('bankNoPlaceholder')" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-button class="confirm-update" :loading="submitLoading" type="primary" size="large" @click="confirmUpdate">{{ t('confirmUpdate') }}</el-button>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { ossUrl } from '@/constants/common'
import { OSS_DIR } from '@/constants/oss-dir'
import { useRegionCodeStore } from '@/pc/stores'
import { getDictListByKey, getUserInfo, updateUserInfo } from '@/apis/merchants'

const { locale, t } = useI18n({
  messages: {
    zh: {
      companyName: '公司名', // 公司名
      mobile: '手机号',
      mobilePlaceholder: '请输入手机号',
      mobileValidate: '请输入正确的手机号格式',
      email: '电子邮箱',
      emailPlaceholder: '请输入电子邮箱',
      emailValidate: '请输入正确的邮箱格式',
      city: '所在地区', // 所在地区
      cityPlaceholder: '请选择省市区',
      address: '详细地址',
      addressPlaceholder: '请输入详细地址',
      serviceType: '服务类别',
      serviceTypePlaceholder: '请输入服务类别',
      idCardImg: '身份证照片',
      idCardFrontPlaceholder: '正面（个人照片面）',
      idCardSellerTips: '法人身份证照片（清晰且露出4个角）',
      idCardBuyerTips: '身份证照片（清晰且露出4个角）',
      idCardFrontValidate: '请上传身份证正面照片',
      idCardBackPlaceholder: '反面（国徽面）',
      idCardBackTips: '反面（国徽面）',
      idCardBackValidate: '请上传身份证反面照片',
      userName: '姓名',
      userNamePlaceholder: '请输入姓名',
      idNo: '身份证号码',
      idNoPlaceholder: '请输入身份证号码',
      idNoValidate: '请输入正确的身份证号',
      certUrl: '营业执照',
      certUrlTipsPlaceholder: '营业执照照片',
      certUrlTips: '上传文件类型为jpg/jpeg/png, 文件大小不超过10M',
      certUrlBottomTips: '营业执照原件照片（清晰且露出4个角）',
      certUrlPlaceholder: '请上传营业执照',
      depositBank: '开户行',
      depositBankPlaceholder: '请输入开户行',
      bankNo: '银行账号',
      bankNoPlaceholder: '请输入银行账号',
      confirmUpdate: '确认更新',
      updateSuccess: '更新成功',
    },
    en: {
      companyName: 'Company Name',
      mobile: 'Mobile Number',
      mobilePlaceholder: 'Please enter your mobile number',
      mobileValidate: 'Please enter a valid mobile number format',
      email: 'Email',
      emailPlaceholder: 'Please enter email address',
      emailValidate: 'Please enter a valid email address',
      city: 'City',
      cityPlaceholder: 'Please select a city',
      address: 'Detailed Address',
      addressPlaceholder: 'Please enter detailed address',
      serviceType: 'Service category',
      serviceTypePlaceholder: 'Please enter your service category',
      idCardImg: 'idCardImg', // 身份证照片
      idCardFrontPlaceholder: 'Front (personal photo side)',
      idCardSellerTips: `Legal Representative's ID Card Photo（Clear photo showing all four corners of the license）`,
      idCardBuyerTips: 'ID Card Photo（Clear photo showing all four corners of the license）',
      idCardFrontValidate: 'Please upload the front photo of your ID card',
      idCardBackPlaceholder: 'Reverse (national emblem)',
      idCardBackTips: 'Reverse (national emblem)',
      idCardBackValidate: 'Please upload the reverse photo of your ID card',
      userName: 'Username',
      userNamePlaceholder: 'Please enter your name',
      idNo: 'IDCard No', // 身份证号码
      idNoPlaceholder: 'Please enter your ID number',
      idNoValidate: 'Please enter the correct ID number',
      certUrl: 'Business License',
      certUrlTips: 'Upload file type: jpg/jpeg/png, max file size: 10M',
      certUrlBottomTips: 'Original photo of business license (clear with 4 corners)',
      depositBank: 'Bank Name',
      depositBankPlaceholder: 'Please enter your bank name',
      bankNo: 'Bank Account Number',
      bankNoPlaceholder: 'Please enter your bank number',
      confirmUpdate: 'Confirm update',
      updateSuccess: 'Update Success',
    },
  },
})

// 英文状态超出显示省略号
const enEllipsis = computed(() => {
  return locale.value === 'en'
})

// 添加表单校验规则
const validatePhone = (rule, value, callback) => {
  const phoneRegex = /^1[3-9]\d{9}$/ // 中国大陆手机号正则
  if (!value) {
    callback() // 为空时不进行验证
  } else if (!phoneRegex.test(value)) {
    callback(new Error(t('mobileValidate')))
  } else {
    callback()
  }
}
const validateEmail = (rule, value, callback) => {
  const emailRegex = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/
  if (!value) {
    callback() // 为空时不进行验证
  } else if (!emailRegex.test(value)) {
    callback(new Error(t('emailValidate')))
  } else {
    callback()
  }
}
const rules = {
  userName: [{ validator: validatePhone, trigger: 'blur' }],
  contactEmail: [{ validator: validateEmail, trigger: 'blur' }],
}

const formData = ref({
  companyName: '', // 公司名
  userName: '', // 手机号
  contactEmail: '', // 电子邮箱
  city: null, // 所在地区
  companyAddress: '', // 地址
  serviceType: null, // 服务类别
  companyPersonIdFrontUrl: '',
  companyPersonIdBackUrl: '',
  companyPersonName: '',
  companyPersonId: null,
  companyLicenseUrl: '', // 营业执照
  bankBranchName: '', // 开户行
  bankAccountNo: '', // 银行账号
})

const serviceTypeList = ref([]) // 服务类别

// 获取资料详情
const getInfo = async () => {
  try {
    const res = await getUserInfo()
    const { provinceCode, cityCode, areaCode, ...data } = res
    formData.value = { ...data, city: [provinceCode, cityCode, areaCode] }
  } catch (error) {
    console.log(error)
  }
}
getInfo()

// 城市
const regionCodeStore = useRegionCodeStore()
// 城市
const cityList = computed(() => regionCodeStore.areaList || [])
// 城市
if (!cityList.value.length) {
  regionCodeStore.queryAreaList()
}

// 获取服务类别
const getserviceTypeList = async () => {
  try {
    serviceTypeList.value = await getDictListByKey({ key: 'service_type' })
  } catch (error) {
    console.log(error)
  }
}
getserviceTypeList()

// 确认更新
const submitLoading = ref(false)
const formRef = ref(null)
const confirmUpdate = async () => {
  try {
    await formRef.value.validate()
    submitLoading.value = true
    const { city, companyPersonIdFrontUrl, companyPersonIdBackUrl, companyLicenseUrl, ...params } = formData.value
    const body = {
      provinceCode: city ? city[0] : null,
      cityCode: city ? city[1] : null,
      areaCode: city ? city[2] : null,
      companyPersonIdFrontUrl: Array.isArray(companyPersonIdFrontUrl) ? companyPersonIdFrontUrl[0] : companyPersonIdFrontUrl,
      companyPersonIdBackUrl: Array.isArray(companyPersonIdBackUrl) ? companyPersonIdBackUrl[0] : companyPersonIdBackUrl,
      companyLicenseUrl: Array.isArray(companyLicenseUrl) ? companyLicenseUrl[0] : companyLicenseUrl,
      ...params,
    }
    await updateUserInfo(body)
    ElMessage.success(t('updateSuccess'))
    await getInfo()
  } catch (error) {
    console.log(error)
  } finally {
    submitLoading.value = false
  }
}
</script>
