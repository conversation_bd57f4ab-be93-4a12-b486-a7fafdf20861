<style lang="scss" scoped>
.add-service {
  .box {
    padding: 20px;
    margin-bottom: 17px;
    background-color: #fff;
    &.submit-wrapper {
      margin-bottom: 0px;
    }
    .goods-picture {
      ::v-deep() {
        .empty-icon {
          img {
            width: 20px;
            height: 20px;
          }
        }
        .leading-normal {
          padding: 0;
        }
      }
    }
    .sku {
      .header {
        .icon-direction {
          display: inline-block;
          width: 10px;
          height: 10px;
          background: linear-gradient(135deg, transparent 50%, #fff 50%, #fff 100%);
          transform: rotate(-135deg);
          box-shadow: 2px 2px 0 #333;

          &.arrow-up {
            margin-top: 10px;
            transform: rotate(-135deg);
          }
          &.arrow-down {
            margin-bottom: 10px;
            transform: rotate(45deg);
          }
        }
      }
      .content {
        .item {
          .attribute {
            width: 100px;
          }
          .label {
            width: 224px;
          }
        }
      }
    }
  }
  ::v-deep() {
    .el-cascader,
    .el-select {
      width: 100%;
    }
    .video-picture {
      .el-form-item:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>

<template>
  <div class="add-service h-full flex flex-col" v-loading="loading">
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px" :class="{ en: locale === 'en' }" class="base-form flex-1">
      <!-- 基础信息 -->
      <div class="box">
        <BorderTitle class="font-600" :show-left-border="false" :title="t('baseInfo')" />
        <el-row :gutter="24">
          <el-col :span="8">
            <!-- 服务编码 -->
            <el-form-item :label="t('serviceNumber')" prop="serviceNumber">
              <el-input v-model.trim="formData.serviceNumber" :disabled="isDisabled" maxlength="30" clearable :placeholder="t('serviceNumberPlaceholder')" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 服务名称 -->
            <el-form-item :label="t('serviceName')" prop="serviceName">
              <el-input v-model.trim="formData.serviceName" :disabled="isDisabled" maxlength="30" clearable :placeholder="t('serviceNamePlaceholder')" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 服务类别 -->
            <el-form-item :label="t('serviceType')" prop="serviceType">
              <el-select v-model="formData.serviceType" :disabled="isDisabled" clearable :placeholder="t('serviceTypePlaceholder')">
                <el-option v-for="(item, index) in serviceTypeList" :key="index" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <!-- 服务商 -->
          <el-col :span="8">
            <el-form-item :label="t('serviceProviderName')" prop="serviceProviderName">
              <el-input
                v-model.trim="formData.serviceProviderName"
                :disabled="isDisabled"
                maxlength="30"
                clearable
                :placeholder="t('serviceProviderNamePlaceholder')"
              />
            </el-form-item>
          </el-col>
          <!-- 价格 -->
          <el-col :span="8">
            <el-form-item :label="t('price')" prop="price">
              <el-input v-model.trim="formData.price" :disabled="isDisabled" maxlength="10" clearable :placeholder="t('pricePlaceholder')" />
            </el-form-item>
          </el-col>
          <!-- 适用区域 -->
          <el-col :span="8">
            <el-form-item :label="t('applicableArea')" prop="applicableArea">
              <el-input v-model.trim="formData.applicableArea" :disabled="isDisabled" maxlength="30" clearable :placeholder="t('applicableAreaPlaceholder')" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <!-- 服务亮点 -->
          <el-col :span="8">
            <el-form-item :label="t('brightSpot')" prop="brightSpot">
              <el-input v-model.trim="formData.brightSpot" :disabled="isDisabled" maxlength="30" clearable :placeholder="t('brightSpotPlaceholder')" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <!-- 上传服务素材 -->
      <div class="box video-picture">
        <BorderTitle class="font-600" :show-left-border="false" :title="t('uploadMaterials')" />
        <!-- 服务主图 -->
        <el-form-item :label="t('picture')" :title="enEllipsis ? t('picture') : ''" :class="{ 'en-ellipsis': enEllipsis }" prop="picture">
          <div class="flex flex-col justify-center service-picture">
            <ImgUploads
              v-model="formData.picture"
              :dir="OSS_DIR.SERVICE_PIC"
              :on-success="uploadSuccess"
              :width="120"
              :height="120"
              :size-limit="10"
              :img-number="5"
              :disabled="isDisabled"
              @change="handleChange"
            >
              <template #empty>
                <div>
                  <icon type="icon-rongqi2" :size="20"></icon>
                  <div class="mt-2 text-xs">{{ t('pictureUrlsPlaceholder') }}</div>
                </div>
              </template>
            </ImgUploads>
            <div class="h-5 leading-5 mt-2 c-#BEBEBE">{{ t('pictureUrlsTips') }}</div>
          </div>
        </el-form-item>
      </div>
      <!-- 服务详情 -->
      <div class="box" :style="{ marginBottom: isDisabled ? '0px' : '17px' }">
        <BorderTitle class="font-600" :show-left-border="false" :title="t('serviceDetails')" />
        <el-form-item prop="info" label-width="0" class="w-[100%]">
          <RichTextEditor
            class="w-[100%]"
            ref="richTextEditor"
            v-model:content="formData.info"
            :disabled="isDisabled"
            :oss-pic-dir="'SERVICE_INFO'"
            :oss-video-dir="'SERVICE_INFO'"
            height="500px"
          />
        </el-form-item>
      </div>
    </el-form>
    <!-- 确认发布 -->
    <div class="box submit-wrapper flex justify-center" v-if="!isDisabled">
      <el-button :loading="submitLoading" type="primary" size="large" @click="confirmClick">{{ t('confirmPublish') }}</el-button>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import RichTextEditor from '@/pc/components/rich-text-editor/rich-text-editor.vue'
import { OSS_DIR } from '@/constants/oss-dir'
import { getDictListByKey, getServiceCreate, getServiceInfo, updateServiceInfo } from '@/apis/merchants'

const { locale, t } = useI18n({
  messages: {
    zh: {
      serviceNumber: '服务编码',
      serviceNumberPlaceholder: '请输入服务编码',
      serviceName: '服务名称', // 服务名称
      serviceNamePlaceholder: '请输入服务名称',
      serviceType: '服务类别', // 服务类别
      serviceTypePlaceholder: '请输入服务类别',
      serviceProviderName: '服务商', // 服务类别
      serviceProviderNamePlaceholder: '请输入服务商',
      price: '价格', // 价格
      pricePlaceholder: '请输入价格',
      applicableArea: '适用区域', // 适用区域
      applicableAreaPlaceholder: '请输入适用区域',
      brightSpot: '服务亮点',
      brightSpotPlaceholder: '请输入服务亮点',
      minPictureValidate: '至少上传1张主图',
      baseInfo: '基础信息',
      uploadMaterials: '上传服务素材',
      picture: '服务主图',
      pleaseInputServicePicture: '请上传服务主图',
      pictureUrlsPlaceholder: '添加图片',
      pictureUrlsTips: ' 尺寸建议500x500（或者正方形），大小10M以下，最少1张，最多5张。',
      serviceDetails: '服务详情',
      pleaseinfo: '请输入服务详情',
      confirmPublish: '确认发布',
      addSuccess: '添加成功',
      editSuccess: '修改成功',
    },
    en: {
      serviceNumber: 'Service Code',
      serviceNumberPlaceholder: 'Please enter service code',
      serviceName: 'Service Name',
      serviceNamePlaceholder: 'Please enter service name',
      serviceType: 'Service Type', // 服务类别
      serviceTypePlaceholder: 'Please enter service type',
      serviceProviderName: 'Service Provider', // 服务商
      serviceProviderNamePlaceholder: 'Please enter service provider',
      price: 'Price', // 价格
      pricePlaceholder: 'Please enter price',
      applicableArea: 'Applicable Area', // 适用区域
      applicableAreaPlaceholder: 'Please enter applicable area',
      brightSpot: 'Service Highlights',
      brightSpotPlaceholder: 'Please enter service highlights',
      minPictureValidate: 'At least upload 1 main images.',
      baseInfo: 'Basic Information',
      uploadMaterials: 'Upload service Materials',
      picture: 'Service Images',
      pleaseInputServicePicture: 'Please upload service images',
      pictureUrlsPlaceholder: 'Add Images',
      pictureUrlsTips: 'Recommended size 500x500 (or square), less than 10MB, minimum 1 images, maximum 5 images.',
      serviceDetails: 'Service Details',
      pleaseinfo: 'Please enter service details',
      confirmPublish: 'Confirm & Publish',
      addSuccess: 'Added Successfully',
      editSuccess: 'Updated Successfully',
    },
  },
})

// 英文状态超出显示省略号
const enEllipsis = computed(() => {
  return locale.value === 'en'
})

const validateServicePic = (rule, value, callback) => {
  if (value.length < 1) {
    callback(new Error(t('minPictureValidate')))
    return
  }
  callback()
}
const rules = {
  serviceNumber: [{ required: true, message: computed(() => t('serviceNumberPlaceholder')), trigger: 'blur' }],
  serviceName: [{ required: true, message: computed(() => t('serviceNamePlaceholder')), trigger: 'blur' }],
  serviceType: [{ required: true, message: computed(() => t('serviceTypePlaceholder')), trigger: 'change' }],
  serviceProviderName: [{ required: true, message: computed(() => t('serviceProviderNamePlaceholder')), trigger: 'blur' }],
  price: [{ required: true, message: computed(() => t('pricePlaceholder')), trigger: 'blur' }],
  applicableArea: [{ required: true, message: computed(() => t('applicableAreaPlaceholder')), trigger: 'blur' }],
  brightSpot: [{ required: true, message: computed(() => t('brightSpotPlaceholder')), trigger: 'blur' }],
  picture: [
    { required: true, message: computed(() => t('minPictureValidate')), trigger: 'change' },
    { required: true, validator: validateServicePic, trigger: ['blur', 'change'] },
  ],
}

const formData = ref({
  serviceNumber: '', // 服务编码
  serviceName: '', // 服务名称
  serviceType: null, // 服务类别
  serviceProviderName: '', // 服务商名称
  price: '', // 价格
  applicableArea: '', // 适用区域
  brightSpot: '', // 服务亮点
  picture: [], // 服务主图
  info: '', // 服务详情
})

// 服务类别
const serviceTypeList = ref([])
// 获取服务类别
const getserviceTypeList = async () => {
  try {
    serviceTypeList.value = await getDictListByKey({ key: 'service_type' })
  } catch (error) {
    console.log(error)
  }
}
getserviceTypeList()

// 上传图片成功回调
const uploadSuccess = () => {}
const handleChange = () => {}

const formRef = ref(null)

const route = useRoute()
const router = useRouter()
const id = route.query?.id
const isDisabled = ref(false)

// 根据服务id-获取服务详情
const loading = ref(false)
const getinfo = async (id) => {
  try {
    loading.value = true
    const res = await getServiceInfo({ id })
    formData.value = {
      ...res,
      picture: res.picture.split(','),
    }
    route.query?.type === 'view' && (isDisabled.value = true)
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  id && getinfo(id)
})

// 点击确认
const submitLoading = ref(false)
const confirmClick = async () => {
  try {
    await formRef?.value?.validate()
    submitLoading.value = true
    let func = null
    func = id ? updateServiceInfo : getServiceCreate
    await func({
      ...formData.value,
      picture: formData.value.picture.join(','),
    })
    ElMessage.success(id ? t('editSuccess') : t('addSuccess'))
    formRef?.value?.resetFields()
    router.back()
  } catch (e) {
    // eslint-disable-next-line no-console
    console.error(e)
  } finally {
    submitLoading.value = false
  }
}
</script>
