<template>
  <div class="w-1260 audit-wrap py-4">
    <div class="audit-inner" v-loading="loading">
      <div class="enter-title w-full h-63px flex items-center pl-9 text-20px text-white font-600">注册审核进度</div>
      <div class="color-[#333] text-center py-[100px]">
        <div v-if="auditStatus === 1">
          <img-loader src="/merchants/audit-wait.png" alt="" class="w-[200px] h-[200px]" />
          <div>注册审核中，请等待</div>
        </div>
        <div v-if="auditStatus === 2">
          <img-loader src="/merchants/audit-error.png" alt="" class="w-[200px] h-[200px]" />
          <div class="color-[#d8131a] mb-1">注册不通过</div>
          <div class="color-[#999] cursor-pointer mb-[42px]" @click="handleTipsClick">查看失败原因<icon type="icon-xiala1" class="text-[12px]"></icon></div>
          <el-button type="primary" @click="handleToRegister">重新入驻</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { MERCHANTS_TYPE_ARRAY } from '@/constants/merchants'
import { useUserStore } from '@/pc/stores'
import { getUserAuditInfo } from '@/apis/merchants'

const auditStatus = ref(null)
const remark = ref('')
const router = useRouter()

const handleTipsClick = () => {
  ElMessageBox.alert(remark.value || '', {
    confirmButtonText: '确定',
    callback: () => {},
  })
}

let routerObj = null
const handleToRegister = () => {
  router.push(routerObj)
}

const loading = ref(false)
const userInfo = useUserStore()
const getAuditStatus = async () => {
  try {
    loading.value = true
    const data = await getUserAuditInfo()
    // 如果没有审核时间 说明没有审核
    if (!data) {
      routerObj = {
        path: '/merchants-enter',
        query: {
          userType: userInfo?.userInfo?.userType,
        },
      }
      router.replace(routerObj)
      return
    }
    const { auditStatus: status, userType, auditMessage } = data
    const item = MERCHANTS_TYPE_ARRAY.find((item) => item.id === userType) || {}
    switch (status) {
      // 审核通过
      case 1:
        router.push(item.path)
        break
      // 审核中
      case 0:
        auditStatus.value = 1
        break
      // 审核不通过
      case 2:
        remark.value = auditMessage || ''
        auditStatus.value = 2
        break
      default:
        break
    }
  } catch (e) {
    console.log(e)
  } finally {
    loading.value = false
  }
}
getAuditStatus()
</script>

<style scoped lang="scss">
.audit-inner {
  background: #fff;
  border-radius: 8px 8px 0 0;
}

.enter-title {
  background: url('https://static.chinamarket.cn/static/trade-exhibition/merchants/enter-title-bg.png');
  background-repeat: no-repeat;
  background-position: 0 0;
  background-size: 326px 63px;
  border-bottom: 1px solid $primary-color;
}
</style>
