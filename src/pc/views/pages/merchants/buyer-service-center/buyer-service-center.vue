<template>
  <div v-if="!validRole" class="seller-center" v-loading="!validRole"></div>
  <LayoutCenter
    :title="MERCHANTS_TYPE.BUYER_SERVICE.title[$i18n.locale]"
    :subTabs="subTabs"
    contentClass="flex flex-col"
    :user-type="MERCHANTS_TYPE.BUYER_SERVICE.id"
    v-else
  >
    <ShareHeader />
    <router-view />
  </LayoutCenter>
</template>

<script setup>
import LayoutCenter from '../components/layout-center.vue'
import ShareHeader from '../components/share-header.vue'
import { MERCHANTS_TYPE } from '@/constants/merchants'
import { useMerchantsHooks } from '@/pc/hooks/merchants'

const subTabs = [
  { title: { zh: '采购商管理', en: 'My favorites' }, path: '/buyer-service-center/buyer-manage', icon: 'icon-wodeshangpinshoucang' },
  { title: { zh: '商机中心', en: 'Opportunity Hub' }, path: '/buyer-service-center/my-opportunity', icon: 'icon-shangjizhongxin' },
]

const { validRole } = useMerchantsHooks(MERCHANTS_TYPE.BUYER_SERVICE.id)
</script>

<style scoped lang="scss">
.seller-center {
  min-height: calc($main-height - 64px);
}
</style>
