<template>
  <div class="my-collection bg-white p-4 h-full flex flex-col">
    <div class="header flex justify-between">
      <div class="search flex mb-4">
        <el-input v-model.trim="searchForm.search" class="mr-2 search-input" size="large" placeholder="请输入采购商手机号/邮箱/名称" clearable />
        <el-date-picker
          class="date-input"
          v-model="searchForm.dateArr"
          type="datetimerange"
          start-placeholder="入驻开始时间"
          end-placeholder="入驻结束时间"
          :default-time="defaultTime"
          size="large"
        />
        <el-button type="primary" size="large" @click="searchClick">搜索</el-button>
        <el-button type="default" size="large" @click="resetClick">重置</el-button>
      </div>
    </div>
    <el-table border :data="tableData" show-overflow-tooltip v-loading="loading">
      <el-table-column prop="userName" label="采购商手机号/邮箱" />
      <el-table-column prop="companyName" label="采购商名称" />
      <el-table-column prop="createTime" label="入驻时间">
        <template #default="{ row }">
          {{ formatTime(row.createTime, 'YYYY-MM-DD hh:mm:ss') }}
        </template>
      </el-table-column>
      <template #empty>
        <EmptyText />
      </template>
    </el-table>
    <div class="pagination-wrapper" v-if="total > pageSize">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :background="true"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
        <span>前往</span>
      </el-pagination>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import EmptyText from '@/pc/components/empty-text/empty-text.vue'
import { getUserInviteePage } from '@/apis/merchants'
import { formatTime } from '@/common/js/date'
import { debounce } from '@/common/js/util'

const tableData = ref([])
const loading = ref(true)
const searchForm = reactive({
  search: '',
  dateArr: [],
})
const defaultTime = [formatTime(Date.now(), 'YYYY-MM-DD 00:00:00'), formatTime(Date.now(), 'YYYY-MM-DD 23:59:59')]

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 获取列表 - 防抖包装
const DEBOUNCE_TIME = 300
const debounceGetData = debounce(() => {
  getList()
}, DEBOUNCE_TIME)

// 搜索
const searchClick = () => {
  currentPage.value = 1
  debounceGetData()
}

// 重置
const resetClick = () => {
  searchForm.search = null
  searchForm.dateArr = []
  searchClick()
}

// 获取列表
const getList = async () => {
  loading.value = true
  try {
    const body = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      search: searchForm.search,
      createStartTime: formatTime(searchForm.dateArr?.[0], 'YYYY-MM-DD hh:mm:ss') || null,
      createEndTime: formatTime(searchForm.dateArr?.[1], 'YYYY-MM-DD hh:mm:ss') || null,
    }
    const { totalRecord, rowList } = await getUserInviteePage(body)
    tableData.value = rowList
    total.value = totalRecord
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}
getList()

//分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getList()
}
const handleCurrentChange = (val) => {
  currentPage.value = val
  getList()
}
</script>

<style lang="scss" scoped>
.search {
  .el-input {
    width: 300px;
  }
}

:deep() {
  .el-date-editor.el-input,
  .el-date-editor.el-input__wrapper {
    width: 400px;
    height: 40px;
    margin-right: 8px;
  }

  .el-range-input {
    width: 44%;
  }
}
</style>
