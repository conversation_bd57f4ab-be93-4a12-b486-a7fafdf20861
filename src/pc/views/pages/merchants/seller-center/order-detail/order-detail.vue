<template>
  <div class="order-detail flex flex-col">
    <div class="back bg-white px-16px py-8px mb-8px flex items-center text-#D8131A text-14px cursor-pointer" @click="goBack">
      <el-icon class="ml-4px"><ArrowLeft /></el-icon>返回
    </div>
    <div class="detail-content bg-white p-4 h-full flex flex-col">
      <div class="header mb-16px justify-between items-center" :class="{ flex: $storageLocale === 'zh' || [100, 400].includes(detailInfo?.status) }">
        <div class="flex flex-wrap flex-1 items-center font-500 text-14px">
          <span class="order-no">订单编号：{{ detailInfo?.id || '-' }}</span>
          <span class="mx-8px">|</span>
          <span>{{ formatTime(detailInfo?.orderTime) || '-' }}</span>
          <span class="mx-24px">
            当前订单状态：
            <span :style="{ color: [100, 200, 350, 400, 500].includes(detailInfo?.status) ? '#DA161A' : [300].includes(detailInfo?.status) ? '#FE9914' : '' }">
              {{ ORDER_STATUS_MAP[detailInfo?.status] || '-' }}
            </span>
          </span>
          <span class="text-#FAAD14" v-if="detailInfo?.status === 100">请联系大集哥处理订单事宜</span>
        </div>
        <div>
          <template v-if="[100].includes(detailInfo?.status)">
            <el-button type="default" @click="editPriceClick">修改价格</el-button>
            <el-button type="primary" @click="confirmOrderClick">确认订单</el-button>
          </template>
          <div class="count-down flex items-center text-12px font-normal" :class="{ 'text-right': $storageLocale === 'zh' }" v-if="detailInfo?.status === 200">
            {{ deadlinePayTime }}内未完成付款和上传支付凭证，系统将自动取消订单 <br />请联系买家或者行业小二尽快完成付款
          </div>
          <el-button v-if="[400].includes(detailInfo?.status)" type="primary" @click="orderDeliveryClick">去发货</el-button>
          <div class="text-#F99703" v-if="detailInfo?.status === 350">拒绝原因：{{ currentPayInfo?.auditMessage || '-' }}</div>
        </div>
      </div>
      <div v-if="detailInfo?.status !== -1" class="steps px-16px py-4px mb-16px">
        <el-steps class="flex flex-1" :active="getFirstDigit(detailInfo?.status)" align-center finish-status="success">
          <el-step v-for="(item, i) in statusArray" :key="i" :title="item.name" />
        </el-steps>
      </div>
      <div class="flex gap-24px mb-16px text-14px">
        <div class="flex-1">
          <BorderTitle title="采购商信息" />
          <div class="flex flex-col need-border px-16px pt-8px">
            <div class="flex flex-1 h-20px mb-8px" v-for="(item, i) in buyInfo" :key="i">
              <div class="flex text-#999999 w-auto mr-8px">{{ item.label }}</div>
              <div class="flex-1 text-#333">{{ userInfo[item.key] || '-' }}</div>
            </div>
          </div>
        </div>
        <div class="flex-1">
          <BorderTitle title="收货信息">
            <template #icon>
              <span v-if="detailInfo?.status === 400" class="text-#FAAD14 ml-8px text-14px font-normal">请联系大集哥处理发货事宜</span>
            </template>
          </BorderTitle>
          <div class="flex flex-col need-border px-16px pt-8px">
            <div class="flex flex-1 h-20px mb-8px" v-for="(item, i) in receiveInfo" :key="i">
              <template v-if="item.key === 'deliveryPhone'">
                <div class="flex text-#999999 w-auto mr-8px">{{ item.label }}</div>
                <div class="flex-1 text-#333 whitespace-pre-wrap break-all">
                  {{ deliveryAddress?.deliveryPhoneCode ? '+' + deliveryAddress?.deliveryPhoneCode : '' }}
                  {{ deliveryAddress?.deliveryPhone ? deliveryAddress?.deliveryPhone : '' }}
                </div>
              </template>
              <template v-else>
                <div class="flex text-#999999 w-auto mr-8px">{{ item.key === 'address' && !deliveryAddress?.addressType ? '海外地址' : item.label }}</div>
                <div class="flex-1 text-#333 whitespace-pre-wrap break-all">
                  {{ item.key === 'address' ? address || '-' : deliveryAddress[item.key] || '-' }}
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
      <template v-if="[500, 1000, -1].includes(detailInfo?.status)">
        <BorderTitle title="物流信息" />
        <div class="content-wrap mb-16px text-14px flex flex-col text-#999">
          <div class="header flex px-16px py-10px">
            <div class="flex-1">
              物流公司 <span class="flex-1 text-#333 ml-8px">{{ orderLogisticInfo?.logisticsName || '-' }}</span>
            </div>
            <div class="flex-1">
              运单号<span class="flex-1 text-#333 ml-8px">{{ orderLogisticInfo?.logisticsNo || '-' }}</span>
            </div>
          </div>
        </div>
      </template>
      <BorderTitle title="货品信息" />
      <div class="flex mb-16px text-14px">
        <div class="content-wrap text-14px font-500 flex flex-1 flex-col">
          <div class="flex table-header">
            <div :style="{ width: item.width + '%' }" v-for="(item, i) in goodsHeaderList" :key="i" class="cell">{{ item.title }}</div>
          </div>
          <div class="table-content">
            <div class="flex w-full items-center item border-bottom" v-for="(item, i) in detailInfo?.orderGoodsList" :key="i">
              <div class="flex w-65% cell">
                <img-loader v-if="item?.goodsImage" :src="item?.goodsImage" img-class="w-64px h-64px object-cover" alt="" />
                <div class="flex flex-1 flex-col ml-8px justify-between">
                  <div class="text-#333 goods-name">
                    {{ item?.goodsName || '-' }}
                  </div>
                  <div class="text-ellipsis-1 mr-20rpx text-#999">
                    <span v-for="(k, v) in JSON.parse(item?.spec)" :key="v" class="mr-8px">{{ k?.item?.value }}</span>
                  </div>
                </div>
              </div>
              <div class="w-20% cell break-all"><c-symbol :priceType="priceType" />{{ item.price }}</div>
              <div class="w-15% cell text-#666">{{ item?.count }}</div>
            </div>
            <div class="h-40px px-16px py-8px flex items-center">
              <div class="flex-1">
                商品总计
                <span class="text-#D8131A mr-8px"><c-symbol :priceType="priceType" />{{ detailInfo?.orderPrice || '-' }} </span>
                {{ deliveryAddress?.addressType ? '运费总计' : '外贸综合服务费用总计' }}
                <span class="text-#D8131A">
                  <c-symbol v-if="detailInfo?.deliveryPrice !== undefined || detailInfo?.servicePrice !== undefined" :priceType="priceType" />
                  <span v-if="deliveryAddress?.addressType">{{ detailInfo?.deliveryPrice === undefined ? '-' : detailInfo?.deliveryPrice }}</span>
                  <span v-else>{{ detailInfo?.servicePrice === undefined ? '-' : detailInfo?.servicePrice }}</span>
                </span>
              </div>
              <div>
                总计
                <span class="text-#D8131A"><c-symbol :priceType="priceType" />{{ detailInfo?.finalPrice }} </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template v-if="[300, 350, 400, 500, 1000].includes(detailInfo?.status)">
        <BorderTitle title="支付信息" />
        <div class="flex mb-16px text-14px">
          <div class="content-wrap text-14px font-500 flex flex-1 flex-col">
            <div class="flex table-header">
              <div :style="{ width: item.width + '%' }" v-for="(item, i) in payHeaderList" :key="i" class="cell">{{ item.title }}</div>
            </div>
            <div class="pay-content">
              <div class="flex items-center item border-bottom" v-for="(item, i) in detailInfo?.payList" :key="i">
                <div class="w-10% cell">{{ i + 1 }}</div>
                <div class="w-20% cell">{{ item.id }}</div>
                <div class="w-10% cell"><c-symbol showType="enCurrency" :priceType="item?.paySourcePriceType" /></div>
                <div class="w-10% cell">{{ item?.payChannelName }}</div>
                <div class="w-15% cell" :style="{ color: REVIEW_TYPE_MAP_COLOR[item?.auditStatus] }">{{ REVIEW_TYPE_MAP[item?.auditStatus] }}</div>
                <div class="w-35% cell" v-if="item?.payImages">
                  <template v-for="(v, i) in item?.payImages.split(',')" :key="i">
                    <el-image
                      v-if="v"
                      class="w-64px h-64px object-cover mr-8px"
                      :src="v"
                      :initial-index="i"
                      :preview-src-list="item?.payImages.split(',')"
                      show-progress
                      fit="cover"
                    />
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
      <BorderTitle title="订单信息" />
      <div class="content-wrap text-14px flex flex-col text-#999">
        <div class="header flex flex-wrap border-bottom">
          <template v-for="(item, i) in orderConfigList" :key="i">
            <div class="w-1/3 px-16px py-10px" v-if="item.showStatus === 'allStatus' || item.showStatus.includes(detailInfo?.status)">
              {{ item.label }}
              <span class="flex-1 text-#333 ml-8px">
                <template v-if="item.key === 'id'">
                  {{ detailInfo?.[item.key] || '-' }}
                </template>
                <template v-else-if="item.key === 'status'">
                  {{ ORDER_STATUS_MAP[detailInfo?.[item.key]] || '-' }}
                </template>
                <template v-else-if="item.key === 'priceType'">
                  <c-symbol :priceType="priceType" :showCnAndEn="true" />
                </template>
                <template v-else>
                  {{ formatTime(detailInfo?.[item.key]) || '-' }}
                </template>
              </span>
            </div>
          </template>
        </div>
        <div class="flex px-16px py-10px">
          <div class="w-full">
            留言<span class="flex-1 text-#333 ml-8px">{{ detailInfo?.remarks || '-' }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <EditOrderPrice ref="editOrderPriceRef" @confirm-click="getList" />
  <ConfirmOrder ref="confirmOrderRef" @confirm-click="getList" />
  <OrderDelivery ref="orderDeliveryRef" @confirm-click="getList" />
</template>

<script setup>
import { ArrowLeft } from '@element-plus/icons-vue'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import ConfirmOrder from '../order-manage/components/confirm-order.vue'
import EditOrderPrice from '../order-manage/components/edit-order-price.vue'
import OrderDelivery from '../order-manage/components/order-delivery.vue'
import { ORDER_STATUS_ARRAY, ORDER_STATUS_MAP, REVIEW_TYPE_MAP, REVIEW_TYPE_MAP_COLOR } from '@/constants/order'
import { getOrderInfo } from '@/apis/order'
import { formatTime } from '@/common/js/date'

const statusArray = computed(() => {
  return ORDER_STATUS_ARRAY.filter((item) => [100, 200, 300, 400, 500, 1000].includes(item.id))
})

const getFirstDigit = (status) => {
  if (status == null) return 0
  if (status === 1000) return 6
  return parseInt(String(status).charAt(0), 10)
}
// 采购商信息
const buyInfo = [
  { label: '采购商编号', key: 'id' },
  { label: '手机号或邮箱', key: 'userName' },
  { label: '公司名称', key: 'companyName' },
]
// 收货信息
const receiveInfo = [
  { label: '收货人', key: 'deliveryName' },
  { label: '电话号码', key: 'deliveryPhone' },
  { label: '地址', key: 'address' },
]

const goodsHeaderList = ref([
  { title: '货品', width: 65 },
  { title: '单价', width: 20 },
  { title: '数量', width: 15 },
])

const payHeaderList = ref([
  { title: '序号', width: 10 },
  { title: '交易号', width: 20 },
  { title: '支付币种', width: 10 },
  { title: '支付方式', width: 10 },
  { title: '审批状态', width: 15 },
  { title: '支付凭证', width: 35 },
])

const orderConfigList = ref([
  { label: '订单编号', key: 'id', showStatus: 'allStatus' },
  { label: '订单状态', key: 'status', showStatus: 'allStatus' },
  { label: '支付币种', key: 'priceType', showStatus: 'allStatus' },
  { label: '下单时间', key: 'orderTime', showStatus: 'allStatus' },
  { label: '确认订单时间', key: 'confirmTime', showStatus: [200, 300, 350, 400, 500, 1000] },
  { label: '付款时间', key: 'payTime', showStatus: [300, 350, 400, 500, 1000] },
  { label: '发货时间', key: 'sendDeliveryTime', showStatus: [500, 1000] },
  { label: '成交时间', key: 'finishTime', showStatus: [1000] },
  { label: '关闭时间', key: 'cancelTime', showStatus: [-1] },
])

const route = useRoute()
const id = route?.query?.id

const detailInfo = ref({})
const userInfo = ref({})
const deliveryAddress = ref({})
const orderLogisticInfo = ref({})
const currentPayInfo = ref({})
const priceType = ref(null)
const deadlinePayTime = ref(null)
const getList = async () => {
  try {
    const res = await getOrderInfo({ orderId: id })
    detailInfo.value = { ...res }
    const { orderExt, userExt, orderDeliveryAddress, payList, orderLogisticsList, lastPayTime } = res
    priceType.value = orderExt?.priceType
    userExt && Object.assign(userInfo.value, userExt)
    orderDeliveryAddress && Object.assign(deliveryAddress.value, orderDeliveryAddress)
    orderLogisticsList?.length && Object.assign(orderLogisticInfo.value, orderLogisticsList[orderLogisticsList?.length - 1])
    payList?.length && Object.assign(currentPayInfo.value, payList[payList?.length - 1])
    deadlinePayTime.value = formatTime(lastPayTime, 'YYYY年MM月DD日hh点')
  } catch (error) {
    console.log(error)
  }
}
id && getList()

// 根据国内外地址类型，转换地址
const address = computed(() => {
  const addr = deliveryAddress?.value
  if (!addr) return ''
  const { addressType, countryName, provinceName, cityName, areaName, address, postalCode } = addr
  return addressType
    ? `${countryName ? countryName : ''} ${provinceName ? provinceName : ''} ${cityName ? cityName : ''} ${areaName ? areaName : ''} ${address ? address : ''}`
    : `${address ? address : ''}${cityName ? ', ' + cityName : ''}${provinceName ? ', ' + provinceName : ''} ${postalCode ? postalCode : ''}${countryName ? ', ' + countryName : ''}`
})

const editOrderPriceRef = ref(null)
// 修改价格
const editPriceClick = () => {
  editOrderPriceRef.value.init(id)
}

// 确认订单
const confirmOrderRef = ref(null)
const confirmOrderClick = () => {
  confirmOrderRef.value.init(id)
}

// 发货
const orderDeliveryRef = ref(null)
const orderDeliveryClick = () => {
  orderDeliveryRef.value.init(id)
}

// 返回
const router = useRouter()
const goBack = () => {
  router.replace({ name: 'orderManage' })
}
</script>

<style lang="scss" scoped>
::v-deep() {
  .border-title {
    height: 40px;
    color: #333;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 0px;
  }
  .header {
    min-height: 40px;
    .count-down {
      .el-statistic__content {
        color: #f99703;
        font-size: 12px;
        font-weight: normal;
      }
    }
  }
}
.need-border {
  border: 1px solid #edf0f5;
}
.content-wrap {
  border: 1px solid #edeef1;
  .goods-name {
    text-overflow: ellipsis;
    overflow: hidden;
    /* stylelint-disable-next-line */
    display: -webkit-box;
    /* stylelint-disable-next-line */
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    word-break: break-all;
  }
}
.table-header {
  border-bottom: 1px solid #edeef1;
  div:not(:last-child) {
    border-right: 1px solid #edeef1;
  }
}
.border-bottom {
  border-bottom: 1px solid #edeef1;
}
.cell {
  padding: 12px 16px;
  box-sizing: content-box;
  word-break: break-all;
}
</style>
