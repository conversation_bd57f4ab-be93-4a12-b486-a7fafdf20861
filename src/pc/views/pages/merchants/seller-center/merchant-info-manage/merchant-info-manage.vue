<style lang="scss" scoped>
.merchant-info-manage {
  .confirm-update {
    width: max-content;
    margin: 0 auto;
  }
  ::v-deep() .el-cascader {
    width: 100%;
  }
  .business-license {
    .title {
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .svg-icon {
      flex-shrink: 0;
    }
  }
  .tips-item {
    margin-bottom: 10px;
  }
}
</style>

<template>
  <div class="merchant-info-manage bg-white p-4 h-full flex flex-col">
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px" class="merchant-form flex-1" :class="{ 'en-ellipsis': enEllipsis }">
      <el-row :gutter="24">
        <el-col :span="8">
          <!-- 用户名 -->
          <el-form-item :label="t('userName')">
            <el-input v-model="formData.userName" clearable disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <!-- 公司名 -->
          <el-form-item :label="t('companyName')" :title="enEllipsis ? t('companyName') : ''">
            <el-input v-model="formData.companyName" clearable disabled />
          </el-form-item>
        </el-col>
        <!-- 所在地区 -->
        <el-col :span="8">
          <el-form-item :label="t('city')">
            <el-cascader v-model="formData.city" :placeholder="t('cityPlaceholder')" :options="cityList" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <!-- 详细地址 -->
        <el-col :span="8">
          <el-form-item :label="t('companyCity')" :title="enEllipsis ? t('companyCity') : ''">
            <el-input v-model.trim="formData.companyAddress" clearable :placeholder="t('companyCityPlaceholder')" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <!-- 手机号 -->
          <el-form-item :label="t('mobile')" prop="contactPhone" :title="enEllipsis ? t('mobile') : ''">
            <el-input
              v-model="formData.contactPhone"
              maxlength="11"
              oninput="value=value.replace(/[^0-9.]/g,'')"
              clearable
              :placeholder="t('mobilePlaceholder')"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <!-- 微信 -->
          <el-form-item :label="t('wechat')" prop="contactWechat">
            <el-input v-model.trim="formData.contactWechat" clearable :placeholder="t('wechatPlaceholder')" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="8">
          <!-- 电子邮箱 -->
          <el-form-item :label="t('email')" prop="contactEmail">
            <el-input v-model.trim="formData.contactEmail" clearable :placeholder="t('emailPlaceholder')" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <!-- 经营类别 -->
          <el-form-item :label="t('businessCategoryId')" :title="enEllipsis ? t('businessCategoryId') : ''" prop="businessCategoryId">
            <el-select v-model="formData.businessCategoryId" :placeholder="t('businessCategoryIdPlaceholder')" clearable @visible-change="handleSelect">
              <el-option v-for="(item, index) in businessCategoryList" :key="index" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <!-- 经营商品 -->
          <el-form-item :label="t('businessInfo')" :title="enEllipsis ? t('businessInfo') : ''" prop="businessInfo">
            <el-input v-model.trim="formData.businessInfo" clearable :placeholder="t('businessInfoPlaceholder')" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="8">
          <!-- 营业执照 -->
          <el-form-item :label="t('certUrl')" :title="enEllipsis ? t('certUrl') : ''" prop="companyLicenseUrl" class="business-license tips-item">
            <template #label>
              <span class="flex items-center">
                <span class="mr-1 title">{{ t('certUrl') }}</span>
                <el-tooltip :content="t('certUrlTips')" placement="top">
                  <icon type="icon-tishi" :size="16" />
                </el-tooltip>
              </span>
            </template>
            <div class="flex flex-col justify-center items-center">
              <ImgUpload
                v-model="formData.companyLicenseUrl"
                :emptyIcon="`${ossUrl}/mall/business-license.png`"
                :emptyText="''"
                :tipsText="''"
                :dir="OSS_DIR.CERT"
                :size-limit="10"
                height="122px"
                width="228px"
              >
              </ImgUpload>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item>
        <div class="h-5 leading-5 c-#BEBEBE">{{ t('certUrlBottomTips') }}</div>
      </el-form-item>
      <!-- 商家主图 -->
      <el-form-item :label="t('sellerPicture')" prop="businessMainImage" class="tips-item">
        <div class="flex flex-col justify-center items-center">
          <ImgUpload
            v-model="formData.businessMainImage"
            :emptyText="''"
            :tipsText="t('sellerPicturePlaceholder')"
            :dir="OSS_DIR.SELLER_PIC"
            :size-limit="10"
            height="122px"
            width="228px"
          >
          </ImgUpload>
        </div>
      </el-form-item>
      <el-form-item>
        <div class="h-5 leading-5 c-#BEBEBE">{{ t('sellerPictureTips') }}</div>
      </el-form-item>
    </el-form>
    <el-button class="confirm-update" :loading="submitLoading" type="primary" size="large" @click="confirmUpdate">{{ t('confirmUpdate') }}</el-button>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { ossUrl } from '@/constants/common'
import { OSS_DIR } from '@/constants/oss-dir'
import { useRegionCodeStore } from '@/pc/stores'
import { getDictListByKey, getUserInfo, updateUserInfo } from '@/apis/merchants'

const { locale, t } = useI18n({
  messages: {
    zh: {
      userName: '用户名', // 用户名
      companyName: '公司名', // 公司名
      city: '所在地区', // 所在地区
      cityPlaceholder: '请选择省市区',
      companyCity: '详细地址', // 详细地址
      companyCityPlaceholder: '请输入详细地址',
      mobile: '手机号',
      mobilePlaceholder: '请输入手机号',
      mobileValidate: '请输入正确的手机号格式',
      wechat: '微信', // 微信
      wechatPlaceholder: '请输入微信',
      email: '电子邮箱',
      emailPlaceholder: '请输入电子邮箱',
      emailValidate: '请输入正确的邮箱格式',
      businessCategoryId: '经营类别',
      businessCategoryIdPlaceholder: '请选择经营类别',
      businessInfo: '经营商品',
      businessInfoPlaceholder: '请输入经营商品',
      certUrl: '营业执照',
      certUrlTips: '上传文件类型为jpg/jpeg/png, 文件大小不超过10M',
      certUrlBottomTips: '营业执照原件照片（清晰且露出4个角）',
      updateSuccess: '更新成功',
      sellerPicture: '商家主图',
      sellerPicturePlaceholder: '请上传商家主图',
      sellerPictureTips: ' 商家资料管理使用，尺寸建议500x500（或者正方形），大小10M以下',
      confirmUpdate: '确认更新',
    },
    en: {
      userName: 'Username',
      companyName: 'Company Name',
      city: 'City',
      cityPlaceholder: 'Please select a city',
      companyCity: 'Detailed Address', // 详细地址
      companyCityPlaceholder: 'Please enter detailed address',
      mobile: 'Mobile Number',
      mobilePlaceholder: 'Please enter your mobile number',
      mobileValidate: 'Please enter a valid mobile number format',
      wechat: 'WeChat',
      wechatPlaceholder: 'Please enter WeChat',
      email: 'Email',
      emailPlaceholder: 'Please enter email address',
      emailValidate: 'Please enter a valid email address',
      businessCategoryId: 'Business Category',
      businessCategoryIdPlaceholder: 'Please select business category',
      businessInfo: 'Business Products',
      businessInfoPlaceholder: 'Please enter the business products',
      certUrl: 'Business License',
      certUrlTips: 'Upload file type: jpg/jpeg/png, max file size: 10M',
      certUrlBottomTips: 'Original photo of business license (clear with 4 corners)',
      updateSuccess: 'Update Success',
      sellerPicture: 'Merchant Main Image',
      sellerPicturePlaceholder: 'Please upload seller main picture',
      sellerPictureTips: 'For business data management, the size is recommended to be 500x500 (or square), the size is less than 10M',
      confirmUpdate: 'Confirm update',
    },
  },
})

// 英文状态超出显示省略号
const enEllipsis = computed(() => {
  return locale.value === 'en'
})

// 添加表单校验规则
const validatePhone = (rule, value, callback) => {
  const phoneRegex = /^1[3-9]\d{9}$/ // 中国大陆手机号正则
  if (!value) {
    callback() // 为空时不进行验证
  } else if (!phoneRegex.test(value)) {
    callback(new Error(t('mobileValidate')))
  } else {
    callback()
  }
}
const validateEmail = (rule, value, callback) => {
  const emailRegex = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/
  if (!value) {
    callback() // 为空时不进行验证
  } else if (!emailRegex.test(value)) {
    callback(new Error(t('emailValidate')))
  } else {
    callback()
  }
}
const rules = {
  contactPhone: [{ validator: validatePhone, trigger: 'blur' }],
  contactEmail: [{ validator: validateEmail, trigger: 'blur' }],
}

const formData = ref({
  userName: '', // 用户名
  companyName: '', // 公司名
  city: null, // 所在地区
  companyAddress: '', // 详细地址
  contactPhone: '', // 手机号
  contactWechat: '', // 微信
  contactEmail: '', // 电子邮箱
  businessCategoryId: null, // 经营类别
  businessInfo: '', // 经营商品
  companyLicenseUrl: '', // 营业执照
  businessMainImage: '', // 商家主图
})

const businessCategoryList = ref([]) // 经营类别

// 获取表单数据
const getInfo = async () => {
  try {
    const res = await getUserInfo()
    const { provinceCode, cityCode, areaCode, businessCategoryId, ...data } = res
    formData.value = { ...data, city: [provinceCode, cityCode, areaCode], businessCategoryId: +businessCategoryId || null }
  } catch (error) {
    console.log(error)
  }
}
onMounted(() => {
  getInfo()
})

// 城市
const regionCodeStore = useRegionCodeStore()
// 城市
const cityList = computed(() => regionCodeStore.areaList || [])
// 城市
if (!cityList.value.length) {
  regionCodeStore.queryAreaList()
}

// 获取经营类别
const getCategoryList = async () => {
  try {
    businessCategoryList.value = await getDictListByKey({ key: 'business_category' })
  } catch (error) {
    console.log(error)
  }
}
getCategoryList()

// 确认更新
const submitLoading = ref(false)
const formRef = ref(null)
const confirmUpdate = async () => {
  try {
    await formRef.value.validate()
    submitLoading.value = true
    const { city, companyLicenseUrl, businessMainImage, ...data } = formData.value
    const body = {
      ...data,
      provinceCode: city ? city[0] : null,
      cityCode: city ? city[1] : null,
      areaCode: city ? city[2] : null,
      companyLicenseUrl: Array.isArray(companyLicenseUrl) ? companyLicenseUrl[0] : companyLicenseUrl,
      businessMainImage: Array.isArray(businessMainImage) ? businessMainImage[0] : businessMainImage,
    }
    await updateUserInfo(body)
    ElMessage.success(t('updateSuccess'))
  } catch (error) {
    console.log(error)
  } finally {
    submitLoading.value = false
  }
}
// 选择经营类别
const handleSelect = () => {
  nextTick(() => {
    const optionsEl = document.querySelectorAll('.el-select-dropdown__item')
    window.translate?.execute(optionsEl)
  })
}
</script>
