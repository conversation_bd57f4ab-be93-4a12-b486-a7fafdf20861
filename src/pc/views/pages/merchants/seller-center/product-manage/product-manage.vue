<template>
  <div class="product-manage bg-white p-4 h-full flex flex-col">
    <div class="header flex justify-between">
      <div class="search flex mb-4">
        <el-input v-model="searchValue" class="mr-2" size="large" clearable :placeholder="t('searchPlaceHolder')" />
        <el-input v-model="searchNo" class="mr-2" size="large" clearable :placeholder="t('searchPlaceHolderNo')" />
        <el-date-picker v-model="dateArr" type="datetimerange" start-placeholder="开始时间" end-placeholder="结束时间" :default-time="defaultTime" />
        <el-button type="primary" size="large" @click="searchClick">{{ t('search') }}</el-button>
      </div>
      <el-button type="primary" size="large" @click="handleClick()">{{ t('add') }}</el-button>
    </div>
    <el-table border :data="tableData" show-overflow-tooltip v-loading="loading">
      <el-table-column
        v-for="(item, index) in columns"
        :key="index"
        :type="item.type"
        :prop="item.prop"
        :label="item.label"
        :width="item.width"
        :min-width="item.minWidth"
      >
        <template #default="scope">
          <span v-if="item.type">{{ index + 1 }}</span>
          <div v-else-if="item.prop === 'publishStatus'" class="flex items-center">
            <template v-if="scope.row.auditStatus !== 10">
              <div class="dot" :class="scope.row.publishStatus ? 'green' : ''"></div>
              {{ statusMap[scope.row.publishStatus].text }}
              <template v-if="scope.row.publishStatus === 0 && scope.row.auditStatus === 50">
                <el-tooltip class="box-item" effect="dark" :content="scope.row.failReason || ''" placement="top">
                  <el-icon class="v-middle ml-2px">
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </template>
            </template>
            <!-- 待审核 -->
            <template v-else>
              <div class="dot" style="background-color: #d8131a"></div>
              {{ statusMap[2].text }}
            </template>
          </div>
          <span v-else-if="item.prop === 'maxPrice'">{{ formatPrice(scope.row.maxPrice) }}</span>
          <span v-else-if="item.prop === 'createTime'">{{ formatTime(scope.row.createTime) }}</span>
          <span v-else-if="item.prop === 'spuName'">
            <div class="flex items-center overflow-hidden">
              <img-loader
                :src="scope.row.spuImages?.split(',')?.[0]"
                v-if="scope.row.spuImages"
                img-class="w-[40px] h-[40px] object-cover mr-[6px] bg-[#f8f8f8]"
                alt=""
              />
              <div
                class="text-ellipsis overflow-hidden whitespace-nowrap"
                :class="scope.row.publishStatus ? 'cursor-pointer hover:underline hover:text-[#257BFB]' : ''"
                @click="handleGoodsNameClick(scope.row)"
              >
                {{ scope.row.spuName }}
              </div>
            </div>
          </span>
          <span v-else>{{ scope.row[item.prop] || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column :fixed="$storageLocale === 'ar' ? false : 'right'" :label="t('operate')" :min-width="$storageLocale !== 'zh' ? '220px' : '110px'">
        <template #default="scope">
          <div v-if="scope.row.auditStatus !== 10">
            <el-button class="edit" link type="primary" @click="handleClick(scope.row)">{{ t(scope.row.publishStatus ? 'query' : 'edit') }}</el-button>
            <el-popconfirm width="auto" :title="t('confirmText')" @confirm="onOffClick(scope.row)">
              <template #reference>
                <el-button class="edit" link type="primary">
                  <span v-enMode="scope.row.publishStatus ? 'Delist' : 'List'">{{ scope.row.publishStatus ? t('off') : t('on') }}</span>
                </el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <EmptyText />
      </template>
    </el-table>
    <div class="pagination-wrapper" v-if="total > pageSize">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :background="true"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script setup>
import { QuestionFilled } from '@element-plus/icons-vue'
import { onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import EmptyText from '@/pc/components/empty-text/empty-text.vue'
import { getUserGoodsList, updatePublishStatus } from '@/apis/goods'
import { formatTime } from '@/common/js/date'
import { debounce } from '@/common/js/util'

// 审核状态
// 0-正常 10-待审核 50-审核失败（拒绝

const { t } = useI18n({
  messages: {
    zh: {
      search: '搜索',
      searchPlaceHolder: '请输入商品名称',
      searchPlaceHolderNo: '请输入商品编号',
      no: '序号',
      productID: '商品编号',
      productCode: '商品编码',
      productName: '商品名称',
      productCategories: '商品类别',
      brand: '品牌',
      price: '价格',
      stock: '库存',
      sales: '销量',
      add: '新增',
      operate: '操作',
      edit: '修改',
      on: '上架',
      off: '下架',
      confirmText: '确认执行此操作吗？',
      onSuccess: '上架成功',
      offSuccess: '下架成功',
      goodsStatus: '商品状态',
      createTime: '创建时间',
      query: '查看',
    },
    en: {
      search: 'Search',
      searchPlaceHolder: 'Please enter product name',
      searchPlaceHolderNo: 'Please enter product No',
      no: 'No.',
      productID: 'Product ID',
      productCode: 'Product Code',
      productName: 'Product Name',
      productCategories: 'Product Categories',
      brand: 'Brand',
      price: 'Price',
      stock: 'Stock',
      sales: 'Sales',
      add: 'Add',
      operate: 'Operate',
      edit: 'Edit',
      on: 'List',
      off: 'Delist',
      confirmText: 'Are you sure to perform this operation?',
      onSuccess: 'Listed Successfully',
      offSuccess: ' Unlisted Successfully',
      goodsStatus: 'Status',
      createTime: 'Create Time',
      query: 'QUERY',
    },
  },
})

const statusMap = {
  0: { text: t('off'), color: '#333' },
  1: { text: t('on'), color: '#333' },
  2: { text: '审核中', color: '#D8131A' },
}

const tableData = ref([])
const loading = ref(true)

// columns
const columns = ref([
  { prop: 'spuName', label: computed(() => t('productName')), width: '200' },
  { prop: 'id', label: computed(() => t('productID')), width: '200' },
  // { prop: 'goodsNo', label: computed(() => t('productCode')), width: '180' },
  // { prop: 'brandName', label: computed(() => t('brand')), width: '' },
  { prop: 'maxPrice', label: computed(() => t('price')), width: '100' },
  { prop: 'stock', label: computed(() => t('stock')), width: '110' },
  { prop: 'saleNum', label: computed(() => t('sales')), width: '110' },
  { prop: 'publishStatus', label: computed(() => t('goodsStatus')), width: '110' },
  { prop: 'createTime', label: computed(() => t('createTime')), width: '200' },
  // { prop: 'category', label: computed(() => t('productCategories')), width: '180' },
])

const searchValue = ref(null)
const searchNo = ref(null)
const dateArr = ref([])
const defaultTime = [formatTime(Date.now(), 'YYYY-MM-DD 00:00:00'), formatTime(Date.now(), 'YYYY-MM-DD 23:59:59')]
const router = useRouter()

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 获取列表
const getList = async () => {
  loading.value = true
  try {
    const body = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      spuName: searchValue.value,
      id: searchNo.value,
      createStartTime: dateArr.value?.[0] ? formatTime(dateArr.value[0]) : null,
      createEndTime: dateArr.value?.[1] ? formatTime(dateArr.value[1]) : null,
    }
    const { totalRecord, rowList } = await getUserGoodsList(body)
    tableData.value = rowList
    total.value = totalRecord
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}
const route = useRoute()
onMounted(() => {
  const id = route?.query?.id
  searchNo.value = id
  getList()
})
// 获取列表 - 防抖包装
const DEBOUNCE_TIME = 300
const debounceGetData = debounce(() => {
  getList()
}, DEBOUNCE_TIME)

// 搜索
const searchClick = () => {
  currentPage.value = 1
  debounceGetData()
}
// 新增、修改
const handleClick = (row) => {
  const path = '/seller-center/add-product'
  row ? router.push(`${path}?id=${row?.id}&status=${row?.publishStatus}`) : router.push(`${path}`)
}

// 上架下架
const onOffClick = async (row) => {
  try {
    const body = { id: row.id, publishStatus: row.publishStatus ? 0 : 1 }
    await updatePublishStatus(body)
    ElMessage.success(row.publishStatus ? t('offSuccess') : t('onSuccess'))
  } catch (error) {
    console.log(error)
  } finally {
    getList()
  }
}

//分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getList()
}
const handleCurrentChange = (val) => {
  currentPage.value = val
  getList()
}

const handleGoodsNameClick = ({ publishStatus, id }) => {
  if (!publishStatus) return
  const url = router.resolve({
    name: 'goodsDetail',
    params: {
      id,
    },
  })
  window.open(url.href, '_blank')
}

const formatPrice = (price) => {
  return Number(price).toFixed(2) || '0.00'
}
</script>

<style lang="scss" scoped>
.product-manage {
  .search {
    .el-input {
      width: 200px;
    }
  }
}

[dir='rtl'] {
  .el-table {
    :deep(.el-table__cell) {
      text-align: right;
    }
  }
}

.dot {
  border-radius: 50%;
  width: 6px;
  height: 6px;
  margin-right: 4px;
  background: #4571fb;

  &.green {
    background: #0abd52;
  }
}

:deep() {
  .el-date-editor.el-input,
  .el-date-editor.el-input__wrapper {
    width: 360px;
    height: 40px;
    margin-right: 8px;
  }

  .el-range-input {
    width: 44%;
  }
}
</style>
