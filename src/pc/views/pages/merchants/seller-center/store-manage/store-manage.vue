<style lang="scss" scoped>
.store-manage {
  .box {
    padding: 20px;
    margin-bottom: 17px;
    background-color: #fff;
    &:last-child {
      margin-bottom: 0px;
    }
    &.banner-pic {
      min-height: calc(100% - 382px);
    }
    .goods-picture {
      ::v-deep() {
        .empty-icon {
          img {
            width: 20px;
            height: 20px;
          }
        }
        .leading-normal {
          padding: 0;
        }
      }
    }
  }
}
</style>

<template>
  <div class="store-manage h-full flex flex-col" v-loading="loading">
    <el-form ref="formRef" :model="formData" label-width="auto" class="base-form flex-1">
      <!-- 店铺信息管理 -->
      <div class="box">
        <BorderTitle class="font-600" :show-left-border="false" :title="t('storeInfo')" />
        <!-- 公司名 -->
        <el-form-item :label="t('companyName')" prop="companyName">
          <el-input v-model.trim="formData.companyName" clearable disabled style="width: 328px" />
        </el-form-item>
        <!-- 企业介绍 -->
        <el-form-item :label="t('shopBrandDescribe')" prop="shopBrandDescribe">
          <el-input v-model.trim="formData.shopBrandDescribe" maxlength="100" clearable :placeholder="t('shopBrandDescribePlaceholder')" style="width: 60%" />
        </el-form-item>
        <!-- 店铺shopLogo -->
        <el-form-item :label="t('logo')" prop="shopLogo">
          <div class="flex flex-col justify-center goods-picture">
            <ImgUpload
              v-model="formData.shopLogo"
              :emptyIcon="`${ossUrl}/mall/goods-icon.png`"
              :emptyText="''"
              :tipsText="''"
              :dir="OSS_DIR.MERCHANT_LOGO"
              :size-limit="10"
              height="120px"
              width="120px"
            >
              <template #empty>
                <div>
                  <div class="text-#9E9E9E text-xs">{{ t('merchantMainPicturesPlaceholder') }}</div>
                </div>
              </template>
            </ImgUpload>
            <div class="h-5 leading-5 mt-2 c-#BEBEBE">{{ t('storeTips') }}</div>
          </div>
        </el-form-item>
      </div>
      <!-- 店铺营销 -->
      <div class="box banner-pic">
        <BorderTitle class="font-600" :show-left-border="false" :title="t('storeMarketing')">
          <template #icon>
            <span class="flex items-center ml-1">
              <el-tooltip :content="t('storeMarketingTips')" placement="top">
                <icon type="icon-tishi" :size="16" />
              </el-tooltip>
            </span>
          </template>
        </BorderTitle>
        <!-- 店铺宣传图 -->
        <el-form-item :label="t('merchantMainPictures')">
          <div class="flex flex-col justify-center goods-picture">
            <ImgUploads v-model="formData.merchantMainPictures" :dir="OSS_DIR.MERCHANT_PIC" :width="120" :height="120" :size-limit="10" :img-number="3">
              <template #empty>
                <div>
                  <icon type="icon-rongqi2" :size="20"></icon>
                  <div class="mt-2 text-xs">{{ t('merchantMainPicturesPlaceholder') }}</div>
                </div>
              </template>
            </ImgUploads>
            <div class="h-5 leading-5 mt-2 c-#BEBEBE">{{ t('merchantMainPicturesTips') }}</div>
          </div>
        </el-form-item>
      </div>
    </el-form>
    <!-- 确认发布 -->
    <div class="box flex justify-center">
      <el-button :loading="submitLoading" type="primary" size="large" @click="confirmClick">{{ t('confirmPublish') }}</el-button>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { ossUrl } from '@/constants/common'
import { OSS_DIR } from '@/constants/oss-dir'
import { getUserInfo, updateUserInfo } from '@/apis/merchants'

const { t } = useI18n({
  messages: {
    zh: {
      confirmPublish: '确认更新',
      companyName: '公司名称',
      shopBrandDescribe: '企业介绍',
      shopBrandDescribePlaceholder: '请输入企业介绍',
      logo: '店铺 logo',
      storeTips: '尺寸建议160x160，大小10M以下',
      storeMarketing: '店铺营销',
      storeMarketingTips: '店铺宣传图上传以后会显示在店铺首页营销位置',
      merchantMainPictures: '店铺宣传图',
      merchantMainPicturesPlaceholder: '添加图片',
      merchantMainPicturesTips: '上传企业的宣传图片，或商品主推广告图，尺寸建议1260x350，大小10M以下，最多3张',
      storeInfo: '店铺信息管理',
      updateSuccess: '更新成功',
    },
    en: {
      storeInfo: 'Store Information Management',
      companyName: 'Company Name',
      shopBrandDescribe: 'Company introduction',
      shopBrandDescribePlaceholder: 'Please enter the company introduction',
      logo: 'Store logo',
      storeTips: 'Recommended size 160x160，less than 10MB',
      storeMarketing: 'Store Marketing',
      storeMarketingTips: `The store promotional image will be displayed in the marketing section on the store's homepage after uploading.`,
      merchantMainPictures: 'Store Promotional Image',
      merchantMainPicturesPlaceholder: 'Add Image',
      merchantMainPicturesTips:
        'Upload promotional pictures of the company, or the main advertising picture of the product, recommended size 1260x350, less than 10MB, maximum 5 images.',
      confirmPublish: 'Confirm Update',
      updateSuccess: 'Update Success',
    },
  },
})

const formData = ref({
  companyName: '', // 公司名称
  shopBrandDescribe: '', // 企业介绍
  shopLogo: '', // 店铺shopLogo
  merchantMainPictures: [], // 店铺主图
})

// 获取店铺管理信息
const loading = ref(false)
const getMerchantInfo = async () => {
  loading.value = true
  try {
    const { shopMainImage, ...res } = await getUserInfo()
    formData.value = res
    formData.value.merchantMainPictures = shopMainImage?.split(',') || []
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}
getMerchantInfo()

// 点击确认
const submitLoading = ref(false)
const confirmClick = async () => {
  try {
    submitLoading.value = true
    const { merchantMainPictures, shopLogo, ...data } = formData.value
    const body = {
      ...data,
      shopLogo: Array.isArray(shopLogo) ? shopLogo[shopLogo.length - 1] : shopLogo,
      shopMainImage: merchantMainPictures.join(','),
    }
    await updateUserInfo(body)
    ElMessage.success(t('updateSuccess'))
  } catch (e) {
    // eslint-disable-next-line no-console
    console.error(e)
  } finally {
    submitLoading.value = false
  }
}
</script>
