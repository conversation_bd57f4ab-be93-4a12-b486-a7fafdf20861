<style lang="scss" scoped>
.sku {
  border: 1px solid #f0f0f0;

  .header {
    .icon-direction {
      display: inline-block;
      width: 10px;
      height: 10px;
      background: linear-gradient(135deg, transparent 50%, #fff 50%, #fff 100%);
      transform: rotate(-135deg);
      box-shadow: 2px 2px 0 #333;

      &.arrow-up {
        margin-top: 10px;
        transform: rotate(-135deg);
      }
      &.arrow-down {
        margin-bottom: 10px;
        transform: rotate(45deg);
      }
    }
  }
  .content {
    .item {
      .attribute {
        width: 100px;
      }
      .label {
        width: 315px;
      }
    }
  }

  &-line {
    border-right: 1px solid #d9d9d9;
  }

  .add-btn {
    width: 100px;
    border: 1px dashed #d9d9d9;
  }
}

.other-attr-line {
  width: 100%;
  border-top: 1px solid #e1e1e1;
  margin-bottom: 12px;
  margin-top: -3px;
}
</style>

<template>
  <div class="sku">
    <div class="header flex justify-between items-center c-#333 px-4 py-2">
      <div class="flex items-center">
        <div class="flex justify-center items-center font-500 text-sm cursor-pointer" @click="toggle = !toggle">
          <span class="mr-2 icon-direction font-500" :class="{ 'arrow-down': toggle, 'arrow-up': !toggle }"></span> {{ t('skuSpecification') }}
        </div>
        <div class="mx-6 flex items-center">
          <div class="flex items-center cursor-pointer color-[#257BFB]" @click="handleCopySku">复制SKU</div>
          <template v-if="skuLen > 1">
            <div class="sku-line mx-2 h-[12px]"></div>
            <div class="flex items-center cursor-pointer color-[#257BFB]" @click="deleteSku(index)">删除</div>
          </template>
        </div>
      </div>
    </div>
    <div v-show="toggle" class="content px-4 pt-4 bg-[#F6F6F6]">
      <!-- 属性 -->
      <div v-for="(item, subIndex) in skuItem.specifications" :key="subIndex" class="item flex">
        <el-form-item
          class="label mr-4"
          :label="t('attribute') + (subIndex + 1)"
          label-width="100"
          :prop="`skuList[${props.index}].specifications[${subIndex}].label`"
          :rules="[{ required: true, message: t('labelValidate'), trigger: 'blur' }]"
        >
          <el-input
            v-model.trim="item.label"
            clearable
            maxlength="30"
            :placeholder="t('attributeLabelPlaceholder')"
            @change="validateSkuItem(subIndex, item)"
          />
        </el-form-item>
        <el-form-item
          class="value flex-1"
          label-width="0"
          :prop="`skuList[${props.index}].specifications[${subIndex}].value`"
          :rules="[
            { required: true, message: t('attributeValuePlaceholder'), trigger: 'blur' },
            { validator: (...argus) => checkSkuItem(subIndex, item, ...argus), trigger: 'blur' },
          ]"
        >
          <el-input
            v-model.trim="item.value"
            clearable
            maxlength="30"
            :placeholder="t('attributeValuePlaceholder')"
            @blur="validateSkuItem(subIndex, item, true)"
          />
        </el-form-item>
        <div class="flex items-center shrink-0 whitespace-nowrap ml-4 h-8">
          <div class="cursor-pointer color-[#666] hover:color-[#D8131A]" @click="handleCopyItem(subIndex, item)">复制</div>
          <div class="sku-line mx-2 h-[12px]"></div>
          <div v-if="skuItem.specifications.length > 1" class="cursor-pointer color-[#666] hover:color-[#D8131A]" @click="deleteAttr(subIndex, item)">删除</div>
          <div v-else class="cursor-not-allowed color-[#999]">删除</div>
        </div>
      </div>
      <el-form-item class="flex" label-width="100" label="&nbsp;">
        <div class="add-btn flex items-center justify-center color-[#666] hover:color-[#D8131A] flex-1 cursor-pointer" @click="addProperty">
          <el-icon class="mr-1"><Plus /></el-icon> 新增属性
        </div>
        <div class="flex items-center shrink-0 whitespace-nowrap ml-4 opacity-0">
          <div>复制</div>
          <div class="sku-line mx-2 h-[12px]"></div>
          <div>删除</div>
        </div>
      </el-form-item>

      <div class="other-attr-line"></div>
      <el-row :gutter="24">
        <el-col :span="8">
          <!-- 价格 -->
          <el-form-item
            :label="t('price')"
            :prop="`skuList[${props.index}].price`"
            :rules="[{ required: true, message: t('pricePlaceholder'), trigger: 'blur' }]"
          >
            <el-input v-model="skuItem.price" type="text" maxlength="10" clearable :placeholder="t('pricePlaceholder')" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <!-- 库存 -->
          <el-form-item
            :label="t('stockNum')"
            :prop="`skuList[${props.index}].stockNum`"
            :rules="[
              { required: true, message: t('stockNumPlaceholder'), trigger: 'blur' },
              { pattern: /^(?:[0-9]|[1-9]\d{1,8}|1\d{9}|19\d{8}|2000000000)$/, message: t('validateStockNum'), trigger: 'blur' },
            ]"
          >
            <el-input v-model.number="skuItem.stockNum" type="number" clearable :placeholder="t('stockNumPlaceholder')" />
          </el-form-item>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'

const { t } = useI18n({
  messages: {
    zh: {
      skuSpecification: 'SKU 规格',
      addAttribute: '添加属性',
      attribute: '属性',
      attributeLabelPlaceholder: '属性名（最多增加10个）',
      labelValidate: '请输入属性',
      attributeValuePlaceholder: '请输入属性值',
      price: '价格',
      validatePrice: '请输入正确的价格，不能小于0，最多两位小数，且小于20亿',
      stockNum: '库存',
      pricePlaceholder: '请输入价格',
      stockNumPlaceholder: '请输入库存',
      validateStockNum: '起批量大于等于0，小于20亿',
      maxTips: '最多增加10个属性',
    },
    en: {
      skuSpecification: 'SKU Specification',
      addAttribute: 'Add Attribute',
      attribute: 'Attribute',
      attributeLabelPlaceholder: 'Attribute name (up to 10)',
      labelValidate: 'Please enter a property',
      attributeValuePlaceholder: 'Please enter a property value',
      price: 'Price',
      validatePrice: 'Please enter a correct price that is not less than 0 and has at most two decimal places',
      stockNum: 'Inventory',
      pricePlaceholder: 'Please enter the price',
      stockNumPlaceholder: 'Please enter inventory',
      validateStockNum: 'Stock greater than or equal to 0, less than 2 billion.',
      maxTips: 'Add up to 10 attributes',
    },
  },
})

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {},
  },
  index: Number,
  skuLen: Number,
})

const emit = defineEmits(['update:modelValue', 'deleteSku', 'copySku', 'validateField'])
const skuItem = ref(props.modelValue)

watch(
  () => props.modelValue,
  (newValue) => {
    skuItem.value = newValue
  },
)

watch(skuItem, (newValue) => {
  emit('update:modelValue', newValue)
})

const toggle = ref(true)

const isSame = (item, attrItem) => item.label && item.value && attrItem.label === item.label && attrItem.value === item.value
const checkSkuItem = (index, currentItem, rule, value, callback) => {
  const attrList = skuItem.value.specifications.filter((item, i) => i !== index)
  const checkSame = attrList.some((attrItem) => isSame(currentItem, attrItem))
  if (checkSame) {
    return callback(new Error('属性是重复的，请编辑或删除重复属性'))
  }
  return callback()
}

const validateSkuItem = (subIndex, item, isDelete) => {
  // 与他相同的参加校验
  skuItem.value.specifications.forEach((attrItem, i) => {
    if (item.value && item.label && (item.value === attrItem.value || item.label === attrItem.label)) {
      emit('validateField', `skuList[${props.index}].specifications[${i}].value`)
    }
  })
  if (isDelete) return
  emit('validateField', `skuList[${props.index}].specifications[${subIndex}].value`)
}

const addPropertyBefore = (callback) => {
  const attrList = skuItem.value.specifications
  if (attrList.length < 10) {
    typeof callback === 'function' && callback()
  } else {
    ElMessage.warning(t('maxTips'))
  }
}
// 添加属性
const addProperty = () => {
  addPropertyBefore(() => {
    skuItem.value.specifications.push({ label: '', value: '' })
  })
}
// 复制sku
const handleCopyItem = async (index, item) => {
  addPropertyBefore(() => {
    skuItem.value.specifications.splice(index + 1, 0, { ...item })
  })
}

// 删除 属性 行
const deleteAttr = (subIndex, item) => {
  skuItem.value.specifications.splice(subIndex, 1)
  validateSkuItem(subIndex, item, true)
}

// 删除 SKU 规格
const deleteSku = (index) => {
  emit('deleteSku', index)
}

let copyLoading = false
const handleCopySku = () => {
  // 复制内容很多，避免频繁操作加100ms延迟，避免卡顿
  if (copyLoading) return
  copyLoading = true
  setTimeout(() => (copyLoading = false), 100)
  emit('copySku')
}
</script>
