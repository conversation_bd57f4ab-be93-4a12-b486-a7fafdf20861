<template>
  <div class="add-live flex flex-col" v-loading="loading">
    <div class="back bg-white px-16px py-8px mb-8px flex items-center text-#D8131A text-14px cursor-pointer" @click="goBack">
      <el-icon class="ml-4px"><ArrowLeft></ArrowLeft></el-icon>返回
    </div>
    <div class="detail-content bg-white p-4 h-full flex flex-col">
      <div class="mb-16px text-14px">
        <div class="flex-1">
          <BorderTitle title="商品信息"></BorderTitle>
          <div class="px-16px pt-8px">
            <el-row :gutter="24">
              <el-col :span="12">
                <div class="flex mb-8px">
                  <el-tooltip content="商品ID" placement="top" effect="light">
                    <div class="flex text-#333 w-auto text-ellipsis-1 infotitle">商品ID</div>
                  </el-tooltip>

                  <div class="flex-1 text-#999">{{ formData.spuId || '-' }}</div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="flex mb-8px">
                  <el-tooltip content="商品标题" placement="top" effect="light">
                    <div class="text-#333 w-auto text-ellipsis-1 infotitle">商品标题</div>
                  </el-tooltip>

                  <div class="flex-1 text-#999">{{ detailInfo.spuName || '-' }}</div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <div class="flex mb-8px">
                  <el-tooltip content="所属类目" placement="top" effect="light">
                    <div class="flex text-#333 w-auto text-ellipsis-1 infotitle">所属类目</div>
                  </el-tooltip>

                  <div class="flex-1 text-#999">
                    <span v-for="(x, index) in detailInfo.categoryList?.slice().reverse()" :key="index">
                      {{ x.categoryName || '-' }}
                      <span v-if="index < detailInfo.categoryList?.slice().reverse().length - 1">/</span>
                    </span>
                  </div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="flex mb-8px">
                  <el-tooltip content="品牌" placement="top" effect="light">
                    <div class="text-#333 w-auto text-ellipsis-1 infotitle">品牌</div>
                  </el-tooltip>

                  <div class="flex-1 text-#999">{{ detailInfo.brandName || '-' }}</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
      <div class="mb-16px text-14px">
        <BorderTitle title="商品图片"></BorderTitle>
        <div class="flex px-16px pt-8px">
          <el-tooltip content="商品主图" placement="top" effect="light">
            <div class="flex text-#333 w-auto text-ellipsis-1 infotitle">商品主图</div>
          </el-tooltip>

          <div class="flex text-#333" v-if="detailInfo.spuImages">
            <template v-for="(item, index) in detailInfo.spuImages?.split(',')" :key="index">
              <div class="img-border mr-[8px]">
                <img-loader :src="item" v-if="item" img-class="w-full h-full object-cover " alt=""></img-loader>
              </div>
            </template>
          </div>
        </div>
      </div>

      <el-form ref="formRef" :model="formData" label-width="70px" :rules="rules" :scroll-to-error="scrollToError">
        <div class="text-14px pb-16px">
          <BorderTitle title="直播价格设置"></BorderTitle>
          <div class="flex justify-between flex-wrap pt-8px pb-16px" v-if="isEditDetail == 1 || isEditDetail == 2">
            <div class="flex">
              <div class="mr-24px flex items-center">
                <div class="text-#333 mr-8px text-right whitespace-nowrap text-ellipsis overflow-hidden">出厂价</div>

                <el-input-number v-model="originPrice" :min="0" :precision="2" :step="0.1" :controls="false">
                  <template #suffix>
                    <span>元</span>
                  </template>
                </el-input-number>
              </div>
              <div class="mr-24px flex items-center">
                <div class="text-#333 mr-8px text-right whitespace-nowrap text-ellipsis overflow-hidden">快递费</div>
                <el-input-number v-model="deliveryPrice" :min="0" :precision="2" :step="0.1" :controls="false">
                  <template #suffix>
                    <span>元</span>
                  </template>
                </el-input-number>
              </div>
              <div class="flex items-center" v-if="showLivePrice">
                <div class="text-#333 mr-8px w-[72px] text-right whitespace-nowrap text-ellipsis overflow-hidden">直播价</div>
                <el-input-number v-model="livePrice" :min="0" :precision="2" :step="0.1" :controls="false">
                  <template #suffix>
                    <span>元</span>
                  </template>
                </el-input-number>
              </div>
            </div>
            <div class="flex items-center">
              <el-button type="primary" @click="fillPrice" class="mr-24px">批量填写</el-button>
              <el-checkbox
                v-model="showLivePrice"
                @change="changeLivePrice"
                label="控直播价"
                class="whitespace-nowrap text-ellipsis overflow-hidden"
                size="large"
              ></el-checkbox>
            </div>
          </div>
          <div class="flex text-14px">
            <el-table :data="formData.skuList || []" border>
              <el-table-column v-for="item in saleAttrViewList" :key="item.id" prop="spec" :label="item.spec" min-width="125px">
                <template #default="scope">
                  <div v-for="xx in scope.row.spec" :key="xx.id">
                    <div v-if="xx.id == item.id">
                      <div v-if="item.items">
                        <div v-for="imgs in item.items" :key="imgs.id">
                          <div v-if="imgs.id == xx.item.id" class="flex">
                            <img :src="imgs.image" v-if="imgs.image" class="w-[44px] h-[44px] mr-[4px] shrink-0" />{{ xx.item?.value || '-' }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column prop="originPrice" column-key="originPrice" min-width="150px">
                <template #header
                  >出厂价<span>{{ nowprice }}</span>
                </template>
                <template #default="scope">
                  <el-form-item
                    v-if="isEditDetail == 1 || isEditDetail == 2"
                    label=""
                    label-width="0"
                    :prop="'skuList.' + scope.$index + '.originPrice'"
                    :rules="[
                      {
                        required: true,
                        validator: checkPrice,
                        trigger: ['change', 'blur'],
                      },
                    ]"
                  >
                    <el-input-number
                      v-model.trim="scope.row.originPrice"
                      clearable
                      :controls="false"
                      placeholder=""
                      controls-position="right"
                      :precision="2"
                      :min="0.01"
                      :max="LimitNumber"
                    >
                      <template #prefix>
                        <span v-if="priceTypeMap[formData.priceType]">{{ priceTypeMap[formData.priceType].symbol || '' }}</span>
                      </template>
                    </el-input-number>
                  </el-form-item>
                  <div v-else>
                    <span v-if="priceTypeMap[formData.priceType]">{{ priceTypeMap[formData.priceType].symbol || '' }}</span
                    >{{ scope.row.originPrice }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="deliveryPrice" column-key="deliveryPrice" min-width="150px">
                <template #header
                  >快递费 <span>{{ nowprice }}</span></template
                >
                <template #default="scope">
                  <el-form-item
                    label=""
                    v-if="isEditDetail == 1 || isEditDetail == 2"
                    label-width="0"
                    :prop="'skuList.' + scope.$index + '.deliveryPrice'"
                    :rules="[
                      {
                        required: true,
                        validator: checkPrice,
                        trigger: ['change', 'blur'],
                      },
                    ]"
                  >
                    <el-input-number
                      v-model.trim="scope.row.deliveryPrice"
                      clearable
                      :controls="false"
                      placeholder=""
                      controls-position="right"
                      :precision="2"
                      :min="0.0"
                      :max="LimitNumber"
                    >
                      <template #prefix>
                        <span v-if="priceTypeMap[formData.priceType]">{{ priceTypeMap[formData.priceType].symbol || '' }}</span>
                      </template>
                    </el-input-number>
                  </el-form-item>
                  <div v-else>
                    <span v-if="priceTypeMap[formData.priceType]">{{ priceTypeMap[formData.priceType].symbol || '' }}</span
                    >{{ scope.row.deliveryPrice }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="servicePrice" column-key="servicePrice" min-width="130px">
                <template #header
                  >服务费 <span>{{ nowprice }}</span></template
                >
                <template #default="scope">
                  <span v-if="priceTypeMap[formData.priceType]">{{ priceTypeMap[formData.priceType].symbol || '' }}</span>
                  <span v-if="isEditDetail == 3">{{ scope.row.servicePrice }}</span>
                  <span v-else>{{ formatPrice(scope.row.originPrice * 0.1) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="domainPrice" column-key="domainPrice" min-width="160px"
                ><template #header
                  >私域一件代发价 <span>{{ nowprice }}</span></template
                >
                <template #default="scope">
                  <span v-if="priceTypeMap[detailInfo.priceType]">{{ priceTypeMap[detailInfo.priceType].symbol || '' }}</span>
                  <span v-if="isEditDetail == 3">{{ scope.row.domainPrice }}</span>
                  <span v-else>{{ formatPrice(scope.row.originPrice + scope.row.deliveryPrice + scope.row.originPrice * 0.1) }}</span>
                </template>
              </el-table-column>
              <el-table-column v-if="showLivePrice" prop="livePrice" column-key="livePrice" min-width="150px"
                ><template #header
                  >直播价 <span>{{ nowprice }}</span></template
                >
                <template #default="scope">
                  <el-form-item
                    v-if="isEditDetail == 1 || isEditDetail == 2"
                    label=""
                    label-width="0"
                    :prop="'skuList.' + scope.$index + '.livePrice'"
                    :rules="[
                      {
                        required: true,
                        validator: checkPrice,
                        trigger: ['change', 'blur'],
                      },
                    ]"
                  >
                    <el-input-number
                      v-model.trim="scope.row.livePrice"
                      clearable
                      :controls="false"
                      placeholder="价格"
                      controls-position="right"
                      :precision="2"
                      :min="0.01"
                      :max="LimitNumber"
                    >
                      <template #prefix>
                        <span v-if="priceTypeMap[detailInfo.priceType]">{{ priceTypeMap[detailInfo.priceType].symbol || '' }}</span>
                      </template>
                    </el-input-number>
                  </el-form-item>
                  <div v-else>
                    <span v-if="priceTypeMap[detailInfo.priceType]">{{ priceTypeMap[detailInfo.priceType].symbol || '' }}</span
                    >{{ scope.row.livePrice }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="stock" label="商品库存" column-key="stock" width="100px"> </el-table-column>
              <el-table-column v-if="isEditDetail == 1 || isEditDetail == 2" width="80px" fixed="right" label="操作">
                <template #default="scope">
                  <el-button class="edit" link type="primary" @click="handleDelClick(scope.row)"> 删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <BorderTitle title="其他设置"></BorderTitle>
        <el-row :gutter="24" class="mt-8px">
          <el-col :span="12">
            <el-form-item label="是否含税" prop="containTax">
              <template #label> <el-tooltip content="是否含税" placement="top" effect="light">是否含税</el-tooltip></template>
              <el-radio-group v-model="formData.containTax" v-if="isEditDetail == 1 || isEditDetail == 2">
                <el-radio :value="1">含税</el-radio>
                <el-radio :value="0">不含税</el-radio>
              </el-radio-group>
              <el-radio-group v-model="formData.containTax" v-if="isEditDetail == 3">
                <el-radio :value="1" v-if="formData.containTax">含税</el-radio>
                <el-radio :value="0" v-else>不含税</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发货地" prop="deliveryFromIds">
              <template #label> <el-tooltip content="发货地" placement="top" effect="light">发货地</el-tooltip></template>
              <div v-if="isEditDetail == 3">{{ detailInfo.deliveryProvinceName }}{{ detailInfo.deliveryCityName }}{{ detailInfo.deliveryAreaName }}</div>
              <el-cascader
                v-else
                class="w-full"
                ref="cityRef"
                v-model="formData.deliveryFromIds"
                :options="cityList"
                :props="{ expandTrigger: 'click' }"
                clearable
                filterable
                placeholder=""
              ></el-cascader>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="售后保障" prop="afterSale">
          <template #label> <el-tooltip content="售后保障" placement="top" effect="light">售后保障</el-tooltip></template>
          <el-input
            v-if="isEditDetail == 1 || isEditDetail == 2"
            v-model="formData.afterSale"
            maxlength="50"
            show-word-limit
            placeholder="请填写售后保障"
          ></el-input>
          <span v-else>{{ formData.afterSale }}</span>
        </el-form-item>
        <div class="card-wrap mt-32px" v-if="isEditDetail == 1 || isEditDetail == 2">
          <div class="pb-4 text-center">
            <el-button type="primary" class="submit-btn" :loading="btnLoading" @click="handleSubmit">确认</el-button>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ArrowLeft } from '@element-plus/icons-vue'
import { onMounted, ref } from 'vue'
import { useDictStore } from '@/pc/stores'
import { useRegionCodeStore } from '@/pc/stores'
import { getSellerGoodsInfo } from '@/apis/goods'
import { getLiveSpuDetail, saveLiveSpu } from '@/apis/live'

const route = useRoute()
const isEditDetail = ref(route.query.isEditDetail)
const id = route?.query?.id
const LimitNumber = 9999999
const showLivePrice = ref(false)
const livePrice = ref(null)
const originPrice = ref(null)
const deliveryPrice = ref(null)
const cityRef = ref(null)
const changeLivePrice = (val) => {
  if (!val) {
    livePrice.value = null
  }
}

//批量填写
const fillPrice = () => {
  formData.value.skuList.map((item) => {
    console.log('fillPrice', deliveryPrice)
    if (livePrice.value !== null && livePrice.value !== undefined) {
      item.livePrice = livePrice.value
    }
    if (originPrice.value !== null && originPrice.value !== undefined) {
      item.originPrice = originPrice.value
    }
    if (deliveryPrice.value !== null && deliveryPrice.value !== undefined) {
      item.deliveryPrice = deliveryPrice.value
    }
  })
}
//删除
const handleDelClick = (row) => {
  let index = -1
  for (let i = 0; i < formData.value.skuList.length; i++) {
    if (formData.value.skuList[i].id == row.id) {
      index = i
    }
  }
  formData.value.skuList.splice(index, 1)
}

// 城市
const regionCodeStore = useRegionCodeStore()
// 城市
const cityList = computed(() => regionCodeStore.areaListTopSD || [])
// 城市
if (!cityList.value.length) {
  regionCodeStore.queryAreaList()
}

const checkPrice = (rules, value, callback) => {
  if (value == null || value == undefined) {
    return callback(new Error(`请输入价格`))
  }
  callback()
}

const formRef = ref(null)
const formData = ref({
  deliveryFromIds: [],
  skuList: [],
})
const rules = {
  deliveryFromIds: [{ required: true, message: '请填写发货地', trigger: 'change' }],
  containTax: [{ required: true, message: '请选择是否含税', trigger: 'change' }],
  transportfee: [{ required: true, message: '请输入运费', trigger: ['blur'] }],
  afterSale: [{ required: true, message: '请填写售后保障', trigger: ['blur'] }],
}

const formatPrice = (price) => {
  return Number(price).toFixed(2) || '0.00'
}
const detailInfo = ref({})
const dictStore = useDictStore()
const priceTypeMap = computed(() => dictStore.priceTypeMap)
const nowprice = computed(() => {
  let arr = dictStore.priceTypeMap
  if (detailInfo.value.priceType && arr[detailInfo.value.priceType]) {
    return '(' + arr[detailInfo.value.priceType]?.symbol + ')'
  } else {
    return '¥'
  }
})
const scrollToError = ref(false)
const btnLoading = ref(false)
const handleSubmit = async () => {
  scrollToError.value = true
  await formRef?.value?.validate()
  const cityarr = cityRef.value.presentText.split('/') || []
  try {
    // 兜底拦截
    if (!formData.value.skuList.length) {
      // 重置并去到指定位置
      formData.value.skuList = []
      ElMessage.error('缺少sku,请至少保留一条')
      await formRef?.value?.validate()
      return
    }
    let arr = formData.value.skuList
    arr.map((item) => {
      let price1 = item.originPrice * 0.1
      item.servicePrice = Number(price1).toFixed(2) - 0
      let price = item.originPrice + item.deliveryPrice + item.originPrice * 0.1
      item.domainPrice = Number(price).toFixed(2) - 0
      if (!showLivePrice.value) {
        delete item.livePrice
      }
    })
    const submitFormData = {
      spuId: formData.value.spuId,
      id: formData.value.id,
      categoryId: formData.value.categoryId,
      userId: formData.value.userId,
      spec: formData.value.spec,
      containTax: formData.value.containTax,
      afterSale: formData.value.afterSale,
      deliveryProvinceName: cityarr[0],
      deliveryCityName: cityarr[1],
      deliveryAreaName: cityarr[2],
      deliveryProvinceCode: formData.value.deliveryFromIds[0],
      deliveryCityCode: formData.value.deliveryFromIds[1],
      deliveryAreaCode: formData.value.deliveryFromIds[2],
      skuList: arr,
    }
    btnLoading.value = true
    //console.log('submitFormData', submitFormData)
    await saveLiveSpu(submitFormData)
    formRef.value?.resetFields()
    router.back()
  } catch (error) {
    console.log(error)
  } finally {
    btnLoading.value = false
    scrollToError.value = false
  }
}
const saleAttrViewList = computed(() => {
  const arr = formData.value.spec || []
  let arr2 = arr?.map((item) => ({ ...item, items: item.items.filter((item) => item.id) })).filter((item) => item.spec && item.items.length)
  return arr2
})

const loading = ref(false)
const getGoodsDetail = async (id) => {
  try {
    loading.value = true
    const res = await getSellerGoodsInfo({ id })
    formData.value = res
    let skuList = []
    let arr = res.skuList || []
    arr?.map((item) => {
      let obj = {
        skuId: item.id,
        originPrice: null,
        deliveryPrice: null,
        livePrice: null,
        ...item,
        spec: JSON.parse(item.spec),
      }
      skuList.push(obj)
    })
    skuList?.map((item) => {
      delete item.id
    })
    formData.value.skuList = skuList
    formData.value.containTax = 1
    formData.value.deliveryFromIds = []
    formData.value.afterSale = ''
    formData.value.spec = JSON.parse(res.spec)
    formData.value.deliveryFromIds = [res.deliveryProvinceCode, res.deliveryCityCode, res.deliveryAreaCode]
    formData.value.spuId = res.id
    delete formData.value.id
    detailInfo.value = {
      ...res,
    }
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}
const getLiveDetail = async (id) => {
  try {
    loading.value = true
    const res = await getLiveSpuDetail({ id })
    formData.value = res
    let skuList = []
    let arr = res.skuList || []
    arr?.map((item) => {
      let obj = {
        ...item,
        spec: JSON.parse(item.spec),
      }
      if (obj.livePrice) {
        showLivePrice.value = true
      }
      skuList.push(obj)
    })
    formData.value.skuList = skuList
    formData.value.spec = JSON.parse(res.spec)
    formData.value.deliveryFromIds = [res.deliveryProvinceCode, res.deliveryCityCode, res.deliveryAreaCode]
    formData.value.id = res.id
    formData.value.spuId = res.spuId
    detailInfo.value = {
      ...res,
    }
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}
const getLiveandGoodDetail = async (id, spuId) => {
  try {
    loading.value = true
    const res = await getLiveSpuDetail({ id })

    const res2 = await getSellerGoodsInfo({ id: spuId })
    let skuList = []
    let arr = res2.skuList || []
    arr.map((item) => {
      let obj = {
        skuId: item.id,
        originPrice: null,
        deliveryPrice: null,
        livePrice: null,
        ...item,
        spec: JSON.parse(item.spec),
      }
      skuList.push(obj)
    })
    skuList?.map((item) => {
      delete item.id
    })
    formData.value = res
    formData.value.deliveryFromIds = [res.deliveryProvinceCode - 0, res.deliveryCityCode - 0, res.deliveryAreaCode - 0]
    formData.value.id = res.id
    formData.value.spuId = res.spuId
    formData.value.skuList = skuList
    formData.value.spec = JSON.parse(res2.spec)
    detailInfo.value = {
      ...res,
      id: res.spuId,
    }
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}
onMounted(() => {
  if (route.query.isEditDetail == 2 || route.query.isEditDetail == 3) {
    id && getLiveDetail(id)
  } else {
    if (route.query.isEditDetail == 1 && route.query.spuId) {
      //商品sku变更，保留原有信息，sku列表从新增接口从新获取
      id && getLiveandGoodDetail(id, route.query.spuId)
    } else {
      id && getGoodsDetail(id)
    }
  }
})

// 返回
const router = useRouter()
const goBack = () => {
  if (route.query.isEditDetail == 2 || route.query.isEditDetail == 3) {
    router.replace({ name: 'includeLivePool' })
  } else {
    router.replace({ name: 'unincludeLivePool' })
  }
}
</script>

<style lang="scss" scoped>
::v-deep() {
  .el-form-item__label {
    text-align: left;
    padding: 0;
    color: #333;
    margin-right: 20px;
  }
  .el-input-number .el-input__inner {
    text-align: left;
  }
  .el-table .cell {
    padding: 0 16px;
  }
  .el-table {
    .el-form-item {
      margin-bottom: 0px;
    }
  }
  .el-form-item {
    margin-bottom: 16px;
  }
  .border-title {
    height: 40px;
    color: #333;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 0px;
  }
  .header {
    min-height: 40px;
    .count-down {
      .el-statistic__content {
        color: #f99703;
        font-size: 12px;
        font-weight: normal;
      }
    }
  }
}
.infotitle {
  margin-right: 20px;
  font-size: 14px;
  font-weight: 500;
  width: 60px;
  height: 20px;
}
.img-border {
  border: 1px solid #dcdfe6;
  border-radius: 2px;
  height: 120px;
  width: 120px;
  overflow: hidden;
}
.submit-btn {
  border-radius: 2px;
  padding: 10px 26px;
  height: 40px;
}
.need-border {
  border: 1px solid #edf0f5;
}
.content-wrap {
  border: 1px solid #edeef1;
  .goods-name {
    text-overflow: ellipsis;
    overflow: hidden;
    /* stylelint-disable-next-line */
    display: -webkit-box;
    /* stylelint-disable-next-line */
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    word-break: break-all;
  }
}
.table-header {
  border-bottom: 1px solid #edeef1;
  div:not(:last-child) {
    border-right: 1px solid #edeef1;
  }
}
.border-bottom {
  border-bottom: 1px solid #edeef1;
}
.pre-red-text {
  color: #f56c6c;
}
</style>
