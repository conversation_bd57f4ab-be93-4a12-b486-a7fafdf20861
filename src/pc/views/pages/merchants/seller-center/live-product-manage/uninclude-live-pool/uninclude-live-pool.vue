<template>
  <div class="uninclude-live-pool bg-white p-4 h-full flex flex-col">
    <div class="header flex justify-between">
      <div class="search flex mb-4">
        <el-input v-model="searchId" class="mr-2" size="large" clearable :placeholder="t('searchPlaceHolderId')"></el-input>
        <el-input v-model="searchTitle" class="mr-2" size="large" clearable :placeholder="t('searchPlaceHolderTitle')"></el-input>

        <el-button type="primary" size="large" @click="searchClick">{{ t('search') }}</el-button>
        <el-button type="default" size="large" @click="resetClick">{{ t('reset') }}</el-button>
      </div>
    </div>
    <el-table border :data="tableData" v-loading="loading">
      <el-table-column prop="id" :label="t('productID')" show-overflow-tooltip width="200"></el-table-column>
      <el-table-column prop="spuName" :label="t('productName')">
        <template #default="scope">
          <div class="text-ellipsis-2">
            <el-tooltip :content="scope.row.spuName" placement="top">
              {{ scope.row.spuName }}
            </el-tooltip>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="spuImages" :label="t('productImg')" show-overflow-tooltip width="100">
        <template #default="scope">
          <div class="flex items-center overflow-hidden">
            <img-loader
              :src="scope.row.spuImages?.split(',')?.[0]"
              v-if="scope.row.spuImages"
              img-class="w-[40px] h-[40px] object-cover mr-[6px] bg-[#f8f8f8]"
              alt=""
            ></img-loader>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="maxPrice" :label="t('price')" show-overflow-tooltip width="200">
        <template #default="scope">
          <c-symbol v-if="priceTypeMap" :price-type="1" />
          <span v-if="!scope.row.maxPrice || scope.row.minPrice == scope.row.maxPrice">{{ formatPrice(scope.row.minPrice) }}</span>
          <span v-else> {{ formatPrice(scope.row.minPrice) }}-{{ formatPrice(scope.row.maxPrice) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="stock" :label="t('stock')" width="100"> </el-table-column>
      <el-table-column
        :fixed="$storageLocale === 'ar' ? false : 'right'"
        :label="t('operate')"
        show-overflow-tooltip
        min-width="$storageLocale === 'ar' ? '220px' : '130px'"
      >
        <template #default="scope">
          <div class="operate">
            <div class="text-ellipsis overflow-hidden whitespace-nowrap" @click="handleClick(scope.row)">{{ t('query') }}</div>
            <div class="text-ellipsis overflow-hidden whitespace-nowrap" :loading="loadingJoin" @click="handleJoinClick(scope.row)">
              {{ t('joinLivePool') }}
            </div>
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <EmptyText></EmptyText>
      </template>
    </el-table>
    <div class="pagination-wrapper" v-if="total > pageSize">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :background="true"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import EmptyText from '@/pc/components/empty-text/empty-text.vue'
import { getSellerGoodsInfo } from '@/apis/goods'
import { getSpufreeList } from '@/apis/live'
import { debounce } from '@/common/js/util'

const { t } = useI18n({
  messages: {
    zh: {
      reset: '重置',
      search: '查询',
      searchPlaceHolderId: '请输入商品ID',
      searchPlaceHolderTitle: '请输入商品标题',
      productID: '商品ID',
      productName: '商品标题',
      productImg: '商品主图',
      price: '价格',
      stock: '库存',
      add: '新增',
      operate: '操作',
      query: '查看',
      joinLivePool: '加入直播选品池',
    },
    en: {
      reset: 'reset',
      search: 'Search',
      searchPlaceHolderId: 'Please enter product ID',
      searchPlaceHolderTitle: 'Please enter product title',
      productID: 'Product ID',
      productName: 'Product Title',
      productImg: 'Product Image',
      price: 'Price',
      stock: 'Stock',
      add: 'Add',
      operate: 'Operate',
      query: 'QUERY',
      joinLivePool: 'Join the live selection pool',
    },
  },
})

const tableData = ref([])
const loading = ref(true)

const searchId = ref(null)
const searchTitle = ref(null)

const router = useRouter()

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 获取列表
const getList = async () => {
  loading.value = true
  try {
    const body = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      spuId: searchId.value,
      spuName: searchTitle.value,
    }
    const { totalRecord, rowList } = await getSpufreeList(body)
    tableData.value = rowList
    total.value = totalRecord
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}

// 获取列表 - 防抖包装
const DEBOUNCE_TIME = 300
const debounceGetData = debounce(() => {
  getList()
}, DEBOUNCE_TIME)

// 搜索
const searchClick = () => {
  currentPage.value = 1
  debounceGetData()
}
//重置
const resetClick = () => {
  searchId.value = ''
  searchTitle.value = ''
  searchClick()
}

// 查看
const handleClick = (row) => {
  const path = '/seller-center/add-product'
  row ? router.push(`${path}?id=${row?.id}&status=${row?.publishStatus}`) : router.push(`${path}`)
}
const loadingJoin = ref(false)
const getGoodsDetail = async (id) => {
  try {
    loadingJoin.value = true
    const res = await getSellerGoodsInfo({ id })
    return res.publishStatus
  } catch (error) {
    console.log(error)
  } finally {
    loadingJoin.value = false
  }
}
// 新增、修改直播商品
const handleJoinClick = async (row) => {
  try {
    //若当前商品已下架，则提示：该商品已下架，无法加入直播选品池；
    const isPublic = await getGoodsDetail(row.id)
    if (!isPublic) {
      ElMessageBox.alert('该商品已下架，无法加入直播选品池', '提示', {
        confirmButtonText: '确定',
        callback: () => {},
      })
    } else {
      const path = '/seller-center/live-product-manage/add-live-product'
      row ? router.push(`${path}?id=${row?.id}&isEditDetail=1`) : ''
    }
  } catch (error) {
    console.log(error)
  } finally {
    //getList()
  }
}

//分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getList()
}
const handleCurrentChange = (val) => {
  currentPage.value = val
  getList()
}

const formatPrice = (price) => {
  return Number(price).toFixed(2) || '0.00'
}
onMounted(() => {
  getList()
  //console.log('priceTypeMap', priceTypeMap.value)
})
</script>

<style lang="scss" scoped>
.uninclude-live-pool {
  .search {
    .el-input {
      width: 360px;
    }
  }
}

[dir='rtl'] {
  .el-table {
    :deep(.el-table__cell) {
      text-align: right;
    }
  }
}

.dot {
  border-radius: 50%;
  width: 6px;
  height: 6px;
  margin-right: 4px;
  background: #ff0000;

  &.green {
    background: #0abd52;
  }
}
.operate {
  div {
    margin: 4px 0;
    color: #257bfb !important;
    cursor: pointer;
  }
}
:deep() {
  .el-date-editor.el-input,
  .el-date-editor.el-input__wrapper {
    width: 360px;
    height: 40px;
    margin-right: 8px;
  }

  .el-range-input {
    width: 44%;
  }
}
</style>
