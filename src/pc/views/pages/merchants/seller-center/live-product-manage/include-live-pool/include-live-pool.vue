<template>
  <div class="include-live-pool bg-white p-4 h-full flex flex-col">
    <div class="header flex justify-between">
      <div class="search flex mb-4">
        <el-input v-model="searchId" class="mr-2" size="large" clearable placeholder="直播商品ID"></el-input>
        <el-input v-model="searchSpuId" class="mr-2" size="large" clearable placeholder="商品ID"></el-input>
        <el-input v-model="searchTitle" class="mr-2" size="large" clearable placeholder="商品标题"></el-input>
        <el-select v-model="publishStatus" placeholder="商品状态" clearable class="mr-2" size="large">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>

        <el-button type="primary" size="large" @click="searchClick">{{ t('search') }}</el-button>
        <el-button type="default" size="large" @click="resetClick">{{ t('reset') }}</el-button>
      </div>
    </div>
    <el-table border :data="tableData" v-loading="loading">
      <el-table-column prop="id" label="直播商品ID" show-overflow-tooltip width="200">
        <template #default="scope">{{ scope.row.id || '-' }}</template>
      </el-table-column>
      <el-table-column prop="spuId" label="商品ID" show-overflow-tooltip width="200">
        <template #default="scope">{{ scope.row.spuId || '-' }}</template>
      </el-table-column>
      <el-table-column prop="spuName" label="商品标题" width="200">
        <template #default="scope">
          <div class="text-ellipsis-2">
            <el-tooltip :content="scope.row.spuName" placement="top">
              {{ scope.row.spuName || '-' }}
            </el-tooltip>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="spuImages" label="商品主图" width="100">
        <template #default="scope">
          <div class="flex items-center overflow-hidden">
            <img-loader
              :src="scope.row.spuImages?.split(',')?.[0]"
              v-if="scope.row.spuImages"
              img-class="w-[40px] h-[40px] object-cover mr-[6px] bg-[#f8f8f8]"
              alt=""
            ></img-loader>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="maxPrice" label="私域一件代发价" show-overflow-tooltip width="200">
        <template #default="scope">
          <c-symbol v-if="priceTypeMap" :price-type="1" />
          <span v-if="!scope.row.maxPrice || scope.row.minPrice == scope.row.maxPrice">{{ formatPrice(scope.row.minPrice) }}</span>
          <span v-else> {{ formatPrice(scope.row.minPrice) }}-{{ formatPrice(scope.row.maxPrice) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="stock" label="商品库存" width="100"> </el-table-column>
      <el-table-column prop="publishStatus" label="直播商品状态" width="200">
        <template #default="scope">
          <div class="flex items-center">
            <div class="dot" :class="scope.row.publishStatus ? 'green' : ''"></div>
            {{ scope.row.publishStatus ? t('on') : t('off') }}
            <span v-if="!scope.row.publishStatus">
              <!--relateSpuStatus 关联商品状态 0-下架 1-上架-->
              <!-- relateSpuModifySku 关联商品修改了sku 0-未修改 1-已修改-->
              <el-tooltip v-if="scope.row.relateSpuStatus == 0" content="关联商品已下架，导致直播商品下架" placement="bottom" effect="dark">
                <icon type="icon-tishi" :size="12" class="ml-4px"></icon>
              </el-tooltip>
              <el-tooltip
                v-else-if="scope.row.relateSpuModifySku == 1"
                content="关联商品SKU变更导致直播商品下架，需要重新编辑后上架"
                placement="bottom"
                effect="dark"
              >
                <icon type="icon-tishi" :size="12" class="ml-4px"></icon>
              </el-tooltip>
            </span>
          </div>
        </template>
      </el-table-column>

      <el-table-column :fixed="$storageLocale === 'ar' ? false : 'right'" :label="t('operate')" min-width="$storageLocale === 'ar' ? '220px' : '130px'">
        <template #default="scope">
          <div class="operate">
            <div class="text-ellipsis overflow-hidden whitespace-nowrap" @click="handleClick(scope.row)">{{ t('query') }}</div>
            <div class="text-ellipsis overflow-hidden whitespace-nowrap" @click="handleEditClick(scope.row)">编辑</div>
            <div class="text-ellipsis overflow-hidden whitespace-nowrap" @click="handleDelClick(scope.row)">删除</div>
            <div class="text-ellipsis overflow-hidden whitespace-nowrap" @click="handleOnOffClick(scope.row)">
              {{ scope.row.publishStatus ? '下架' : '上架' }}
            </div>
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <EmptyText></EmptyText>
      </template>
    </el-table>
    <div class="pagination-wrapper" v-if="total > pageSize">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :background="true"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import EmptyText from '@/pc/components/empty-text/empty-text.vue'
import { delSpu, getLiveSpuList, updatePublishStatus } from '@/apis/live'
import { debounce } from '@/common/js/util'

const { t } = useI18n({
  messages: {
    zh: {
      reset: '重置',
      search: '查询',
      searchPlaceHolderId: '请输入商品ID',
      searchPlaceHolderTitle: '请输入商品标题',
      no: '序号',
      productID: '商品ID',
      productCode: '商品编码',
      productName: '商品标题',
      productCategories: '商品类别',
      productImg: '商品主图',
      brand: '品牌',
      price: '价格',
      stock: '库存',
      sales: '销量',
      add: '新增',
      operate: '操作',
      edit: '修改',
      on: '上架',
      off: '下架',
      confirmText: '确认执行此操作吗？',
      onSuccess: '上架成功',
      offSuccess: '下架成功',
      delSuccess: '删除成功',
      goodsStatus: '商品状态',
      createTime: '创建时间',
      query: '查看',
      joinLivePool: '加入直播选品池',
    },
    en: {
      reset: 'reset',
      search: 'Search',
      searchPlaceHolderId: 'Please enter product ID',
      searchPlaceHolderTitle: 'Please enter product title',
      no: 'No.',
      productID: 'Product ID',
      productCode: 'Product Code',
      productName: 'Product Title',
      productCategories: 'Product Categories',
      productImg: 'Product Image',
      brand: 'Brand',
      price: 'Price',
      stock: 'Stock',
      sales: 'Sales',
      add: 'Add',
      operate: 'Operate',
      edit: 'Edit',
      on: 'List',
      off: 'Delist',
      confirmText: 'Are you sure to perform this operation?',
      onSuccess: 'Listed Successfully',
      offSuccess: ' Unlisted Successfully',
      delSuccess: 'Delete success',
      goodsStatus: 'Status',
      createTime: 'Create Time',
      query: 'QUERY',
      joinLivePool: 'Join the live selection pool',
    },
  },
})

const tableData = ref([])
const loading = ref(true)
const statusOptions = ref([
  { label: '全部', value: -1 },
  { label: '上架', value: 1 },
  { label: '下架', value: 0 },
])

const searchId = ref(null) //直播商品id
const searchSpuId = ref(null)
const searchTitle = ref(null)
const publishStatus = ref(null)
const router = useRouter()

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 获取列表
const getList = async () => {
  loading.value = true
  try {
    const body = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      id: searchId.value,
      spuId: searchSpuId.value,
      spuName: searchTitle.value,
      publishStatus: publishStatus.value,
    }
    const { totalRecord, rowList } = await getLiveSpuList(body)
    tableData.value = rowList
    total.value = totalRecord
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}
getList()

// 获取列表 - 防抖包装
const DEBOUNCE_TIME = 300
const debounceGetData = debounce(() => {
  getList()
}, DEBOUNCE_TIME)

// 搜索
const searchClick = () => {
  currentPage.value = 1
  debounceGetData()
}
//重置
const resetClick = () => {
  searchId.value = null
  searchTitle.value = null
  searchSpuId.value = null
  publishStatus.value = null
  searchClick()
}

// 查看
const handleClick = (row) => {
  const path = '/seller-center/live-product-manage/add-live-product'
  row ? router.push(`${path}?id=${row?.id}&isEditDetail=3`) : ''
}
//编辑
const handleEditClick = (row) => {
  //关联商品SKU变更
  if (row.relateSpuModifySku == 1) {
    handleAddClick(row)
  } else {
    const path = '/seller-center/live-product-manage/add-live-product'
    row ? router.push(`${path}?id=${row?.id}&isEditDetail=2`) : ''
  }
}
//新增
const handleAddClick = (row) => {
  const path = '/seller-center/live-product-manage/add-live-product'
  row ? router.push(`${path}?id=${row?.id}&isEditDetail=1&spuId=${row.spuId}`) : ''
}
const handleDelClick = (row) => {
  ElMessageBox.confirm('确认删除该选品吗？', '删除', {
    distinguishCancelAndClose: true,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
  })
    .then(async () => {
      const data = await delSpu({ id: row.id })
      console.log('del--', data)
      if (data) {
        ElMessage.success(t('delSuccess'))
        await getList()
      }
    })
    .catch(() => {})
}
const handleOnOffClick = (row) => {
  //relateSpuStatus关联商品状态 0-下架 1-上架
  //relateSpuModifySku关联商品修改了sku 0-未修改 1-已修改
  if (row.publishStatus == 0 && row.relateSpuStatus == 0) {
    ElMessageBox.confirm('该直播商品关联的商品已下架，请先上架关联商品', '提示', {
      distinguishCancelAndClose: true,
      confirmButtonText: '去上架',
      cancelButtonText: '取消',
    })
      .then(() => {
        const path = '/seller-center/product-manage'
        row ? router.push(`${path}?id=${row?.spuId}`) : ''
        return
      })
      .catch(() => {})
  } else if (row.publishStatus == 0 && row.relateSpuModifySku == 1) {
    ElMessageBox.confirm('关联商品SKU变更导致直播商品下架，请重新编辑后上架', '提示', {
      distinguishCancelAndClose: true,
      confirmButtonText: '去编辑',
      cancelButtonText: '取消',
    })
      .then(() => {
        //跳转新增逻辑保留id
        handleAddClick(row)
        return
      })
      .catch(() => {})
  } else {
    let str = row.publishStatus ? '确认下架该选品吗？' : '确认上架该直播商品吗'
    ElMessageBox.confirm(str, row.publishStatus ? '下架' : '上架', {
      distinguishCancelAndClose: true,
      confirmButtonText: '确认',
      cancelButtonText: '取消',
    })
      .then(() => {
        changeStatus(row)
      })
      .catch(() => {})
  }
}
const changeStatus = async (row) => {
  let params = {
    id: row.id,
    publishStatus: row.publishStatus ? 0 : 1,
  }
  try {
    const data = await updatePublishStatus(params)
    if (data) ElMessage.success(row.publishStatus ? t('offSuccess') : t('onSuccess'))
    await getList()
  } catch (err) {
    console.log(err)
  }
}

//分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getList()
}
const handleCurrentChange = (val) => {
  currentPage.value = val
  getList()
}

const formatPrice = (price) => {
  return Number(price).toFixed(2) || '0.00'
}
</script>

<style lang="scss" scoped>
.include-live-pool {
  .search {
    .el-input,
    .el-select {
      width: 200px;
    }
  }
}

[dir='rtl'] {
  .el-table {
    :deep(.el-table__cell) {
      text-align: right;
    }
  }
}

.dot {
  border-radius: 50%;
  width: 6px;
  height: 6px;
  margin-right: 4px;
  background: #ff0000;

  &.green {
    background: #0abd52;
  }
}
.operate {
  div {
    margin: 4px 0;
    color: #257bfb !important;
    cursor: pointer;
  }
}
:deep() {
  .el-date-editor.el-input,
  .el-date-editor.el-input__wrapper {
    width: 360px;
    height: 40px;
    margin-right: 8px;
  }

  .el-range-input {
    width: 44%;
  }
}
</style>
