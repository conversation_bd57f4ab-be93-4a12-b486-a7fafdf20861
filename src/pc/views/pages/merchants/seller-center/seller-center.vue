<template>
  <div v-if="!validRole" class="seller-center" v-loading="!validRole"></div>
  <LayoutCenter :title="MERCHANTS_TYPE.SELLER.title[$i18n.locale]" :subTabs="subTabs" :user-type="MERCHANTS_TYPE.SELLER.id" v-else>
    <router-view></router-view>
  </LayoutCenter>
</template>

<script setup>
import LayoutCenter from '../components/layout-center.vue'
import { MERCHANTS_TYPE } from '@/constants/merchants'
import { useDictStore } from '@/pc/stores'
import { useMerchantsHooks } from '@/pc/hooks/merchants'

const subTabs = [
  {
    title: { zh: '商家资料管理', en: 'Merchant Information Management' },
    path: '/seller-center/merchant-info-manage',
    icon: 'icon-shangjiaziliaoguanli-',
  },
  { title: { zh: '商品管理', en: 'Product Management' }, path: '/seller-center/product-manage', icon: 'icon-shangpinguanli' },
  { title: { zh: '店铺管理', en: 'Store Manage' }, path: '/seller-center/store-manage', icon: 'icon-a-dianpuguanliicon' },
  { title: { zh: '订单管理', en: 'Order Manage' }, path: '/seller-center/order-manage', icon: 'icon-a-dianpuguanliicon' },
  {
    title: { zh: '直播商品管理', en: 'Live Product Manage' },
    path: '/seller-center/live-product-manage',
    icon: 'icon-a-dianpuguanliicon',
    hasChild: true,
    childs: [
      {
        title: { zh: '未入直播选品池', en: 'uninclude Live Pool' },
        path: '/seller-center/live-product-manage/uninclude-live-pool',
        showLiveChile: false,
      },
      {
        title: { zh: '已入直播选品池', en: 'include Live Pool' },
        path: '/seller-center/live-product-manage/include-live-pool',
        showLiveChile: false,
      },
    ],
  },
  { title: { zh: '平台通讯录', en: 'platform Contacts' }, path: '/seller-center/platform-contacts', icon: 'icon-a-dianpuguanliicon' },
]

const { validRole } = useMerchantsHooks(MERCHANTS_TYPE.SELLER.id)

// 进入供应商页面就调用币种枚举，存储localstorage
const dictStore = useDictStore()
const getPriceType = () => {
  dictStore.getPriceTypeList()
}
getPriceType()
</script>

<style scoped lang="scss">
.seller-center {
  min-height: calc($main-height - 64px);
}
</style>
