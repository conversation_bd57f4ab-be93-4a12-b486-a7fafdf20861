<template>
  <div class="order-manage bg-white p-4 h-full flex flex-col">
    <el-tabs v-model="orderStatus" @tab-click="tabClick">
      <el-tab-pane v-for="(item, i) in tabList" :key="i" :label="item.label" :name="item.id"></el-tab-pane>
    </el-tabs>
    <SearchForm ref="searchFormRef" :orderStatus="orderStatus" @searchClick="searchClick" />
    <!-- <div class="header">
      <div class="border h-44px px-16px py-12px inline" v-for="(item, i) in columns" :key="i" :style="{ width: item.width }">{{ item.label }}</div>
    </div> -->
    <el-table class="mt-2" border :data="tableData" :span-method="objectSpanMethod" v-loading="loading">
      <el-table-column
        v-for="(item, index) in columns"
        :key="index"
        :type="item.type"
        :prop="item.prop"
        :label="item.label"
        :width="item.width"
        :min-width="item.minWidth"
        :fixed="$storageLocale === 'ar' ? false : item.fixed"
      >
        <template #default="scope">
          <div v-if="item.prop === 'id'">
            <div>{{ scope.row?.id }}</div>
            <div>{{ formatTime(scope.row?.orderTime) }}</div>
          </div>
          <div v-else-if="item.prop === 'orderGoodsList'">
            <div class="flex items-center">
              <img-loader
                v-if="scope.row.orderGoodsList[0]?.goodsImage"
                :src="scope.row.orderGoodsList[0]?.goodsImage"
                img-class="w-[64px] h-[64px] object-cover mr-[8px] bg-[#f8f8f8]"
                alt=""
              />
              <div class="flex-1 ml-8px">
                <div class="goods-name text-#333">
                  {{ scope.row?.orderGoodsList[0]?.goodsName }}
                </div>
                <div class="flex-1 text-ellipsis-1 mr-20rpx">
                  <span v-for="(k, v) in JSON.parse(scope.row?.orderGoodsList[0]?.spec)" :key="v" class="mr-8px">{{ k?.item?.value }}</span>
                </div>
              </div>
            </div>
          </div>
          <div v-else-if="item.prop === 'price'"><c-symbol :priceType="scope.row?.orderExt?.priceType" />{{ scope.row?.orderGoodsList[0]?.price }}</div>
          <div v-else-if="item.prop === 'count'">{{ scope.row?.orderGoodsList[0]?.count }}</div>
          <div v-else-if="item.prop === 'orderPrice'">
            <div>
              {{ scope.row?.orderDeliveryAddress?.addressType ? '含运费' : '含外贸综合服务费用' }}:
              <c-symbol v-if="scope.row?.servicePrice !== undefined || scope.row?.deliveryPrice !== undefined" :priceType="scope.row?.orderExt?.priceType" />
              <span v-if="scope.row?.orderDeliveryAddress?.addressType">{{ scope.row?.deliveryPrice === undefined ? '-' : scope.row?.deliveryPrice }}</span>
              <span v-else>{{ scope.row?.servicePrice === undefined ? '-' : scope.row?.servicePrice }}</span>
            </div>
            <div>合计：<c-symbol :priceType="scope.row?.orderExt?.priceType" />{{ scope.row?.finalPrice }}</div>
          </div>
          <div v-else-if="item.prop === 'status'" class="text-#999">
            <span :style="{ color: [100, 200, 350, 400, 500].includes(scope.row?.status) ? '#DA161A' : [300].includes(scope.row?.status) ? '#FE9914' : '' }">
              {{ ORDER_STATUS_MAP[scope.row?.status] }}
            </span>
          </div>
          <div v-else-if="item.prop === 'userExt'">
            <div class="text-ellipsis overflow-hidden whitespace-nowrap">ID: {{ scope.row?.userExt?.id }}</div>
            <div class="text-ellipsis overflow-hidden whitespace-nowrap">
              {{ isEmail(scope.row?.userExt?.userName) ? '邮箱：' : '手机号：' }}{{ scope.row?.userExt?.userName }}
            </div>
          </div>
          <div v-else-if="item.prop === 'operate'" class="operate">
            <div v-if="[100].includes(scope.row?.status)" @click="editPriceClick(scope.row)">修改价格</div>
            <div v-if="[100].includes(scope.row?.status)" @click="confirmOrderClick(scope.row)">确认订单</div>
            <div v-if="[400].includes(scope.row?.status)" @click="orderDeliveryClick(scope.row)">发货</div>
            <div @click="viewDetailClick(scope.row)">查看详情</div>
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <EmptyText />
      </template>
    </el-table>
    <div class="pagination-wrapper" v-if="total > pageSize">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :background="true"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
    </div>
  </div>
  <EditOrderPrice ref="editOrderPriceRef" @confirm-click="debounceGetData" />
  <ConfirmOrder ref="confirmOrderRef" @confirm-click="debounceGetData" />
  <OrderDelivery ref="orderDeliveryRef" @confirm-click="debounceGetData" />
</template>

<script setup>
import EmptyText from '@/pc/components/empty-text/empty-text.vue'
import ConfirmOrder from './components/confirm-order.vue'
import EditOrderPrice from './components/edit-order-price.vue'
import OrderDelivery from './components/order-delivery.vue'
import SearchForm from './components/search-form.vue'
import { ORDER_STATUS_ARRAY, ORDER_STATUS_MAP } from '@/constants/order'
import { getSellerPageList } from '@/apis/order'
import { formatTime } from '@/common/js/date'
import { debounce } from '@/common/js/util'

const orderStatus = ref(1)
const tabList = computed(() => {
  return ORDER_STATUS_ARRAY.filter((item) => item.id !== 350).map((item) => ({
    id: item.id,
    label: item.name,
  }))
})
const searchFormRef = ref(null)
const tabClick = (item) => {
  currentPage.value = 1
  orderStatus.value = item?.props?.name
  if (searchFormRef.value?.filterForm) {
    searchFormRef.value.filterForm.statusList = orderStatus.value
  }
  debounceGetData()
}

const tableData = ref([])
const loading = ref(true)

// columns
const columns = ref([
  { prop: 'id', label: '订单编号/时间', width: '130' },
  { prop: 'orderGoodsList', label: '货品', width: '320' },
  { prop: 'price', label: '单价', width: '120' },
  { prop: 'count', label: '数量', width: '110' },
  { prop: 'orderPrice', label: '订单金额', width: '110' },
  { prop: 'status', label: '订单状态', width: '110' },
  { prop: 'userExt', label: '采购商信息', width: '160' },
  { prop: 'operate', label: '操作', width: '102', fixed: 'right' },
])

// 校验是否是邮箱
const isEmail = (value) => {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailPattern.test(value)
}

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
// 查询条件
const searchValue = ref({})

// 获取列表
const getList = async () => {
  loading.value = true
  try {
    const body = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      statusList: orderStatus.value === 1 ? '' : orderStatus.value,
      ...searchValue.value,
    }
    const { totalRecord, rowList } = await getSellerPageList(body)
    const result = []
    rowList.forEach((item) => {
      item.orderGoodsList?.forEach((orderGoods) => {
        result.push({
          ...item,
          orderGoodsList: [orderGoods], // 只保留当前的 orderGoods
        })
      })
    })
    tableData.value = result
    getSpanArr(tableData.value)
    total.value = totalRecord
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}
getList()

// 获取列表 - 防抖包装
const DEBOUNCE_TIME = 300
const debounceGetData = debounce(() => {
  getList()
}, DEBOUNCE_TIME)

// 查询、重置
const searchClick = (search) => {
  const { statusList, ...rest } = search
  orderStatus.value = statusList
  searchValue.value = rest
  currentPage.value = 1
  debounceGetData()
}

const mergeObj = ref({})
const mergeArr = ['id', 'orderPrice', 'status', 'userExt', 'operate']

const getSpanArr = (data) => {
  // eslint-disable-next-line
  mergeArr.forEach((key, index1) => {
    let count = 0 // 用来记录需要合并行的起始位置
    mergeObj.value[key] = [] // 记录每一列的合并信息
    data.forEach((item, index) => {
      // index == 0表示数据为第一行，直接 push 一个 1
      if (index === 0) {
        mergeObj.value[key].push(1)
      } else {
        // 判断当前行是否与上一行其值相等 如果相等 在 count 记录的位置其值 +1 表示当前行需要合并 并push 一个 0 作为占位
        if (item.id === data[index - 1].id) {
          mergeObj.value[key][count] += 1
          mergeObj.value[key].push(0)
        } else {
          // 如果当前行和上一行其值不相等
          count = index // 记录当前位置
          mergeObj.value[key].push(1) // 重新push 一个 1
        }
      }
    })
  })
}

// 默认接受四个值 { 当前行的值, 当前列的值, 行的下标, 列的下标 }
// eslint-disable-next-line
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  // 判断列的属性
  if (mergeArr.indexOf(column.property) !== -1) {
    // 判断其值是不是为0
    if (mergeObj.value[column.property][rowIndex]) {
      return [mergeObj.value[column.property][rowIndex], 1]
    } else {
      // 如果为0则为需要合并的行
      return [0, 0]
    }
  }
}

const editOrderPriceRef = ref(null)
// 修改价格
const editPriceClick = (item) => {
  editOrderPriceRef.value.init(item?.id)
}

// 确认订单
const confirmOrderRef = ref(null)
const confirmOrderClick = (item) => {
  confirmOrderRef.value.init(item?.id)
}

const router = useRouter()
// 查看详情
const viewDetailClick = (item) => {
  router.push(`/seller-center/order-detail?id=${item?.id}`)
}

// 发货
const orderDeliveryRef = ref(null)
const orderDeliveryClick = (item) => {
  orderDeliveryRef.value.init(item?.id)
}

//分页
const handleSizeChange = (val) => {
  currentPage.value = 1
  pageSize.value = val
  getList()
}
const handleCurrentChange = (val) => {
  currentPage.value = val
  getList()
}
</script>

<style lang="scss" scoped>
::v-deep() {
  .el-tabs__header {
    margin: 0 0 8px;
  }
  .el-tabs__item {
    min-width: 110px;
    padding: 0 10px;
  }
}
.goods-name {
  text-overflow: ellipsis;
  overflow: hidden;
  /* stylelint-disable-next-line */
  display: -webkit-box;
  /* stylelint-disable-next-line */
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.operate {
  div {
    margin: 4px 0;
    color: #257bfb !important;
    cursor: pointer;
  }
}
</style>
