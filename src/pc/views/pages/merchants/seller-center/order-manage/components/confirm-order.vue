<template>
  <el-dialog class="custom-dialog text-#333" v-model="isShowDialog" width="545" :show-close="false" style="padding: 24px">
    <template #header>
      <div class="header flex justify-between text-18px font-600 mb-16px">
        <span>确认订单</span>
        <icon class="cursor-pointer" type="icon-guanbi" :size="16" @click="isShowDialog = false" />
      </div>
    </template>
    <template #default>
      <div class="content-wrap text-14px font-500 flex flex-col">
        <div class="table-content px-16px py-12px">
          请确认订单的商品价格、运费、税费等正确无误，且跟采购商双方达成一致。确认订单后，采购商根据订单金额进行支付。
        </div>
      </div>
    </template>
    <template #footer>
      <div class="footer flex-row-reverse">
        <el-button type="default" size="large" @click="cancelClick">取消</el-button>
        <el-button type="primary" size="large" :loading="submitLoading" @click="confirmClick">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { confirmOrder } from '@/apis/order'

const isShowDialog = ref(false)
const orderId = ref(null)
const init = async (id) => {
  isShowDialog.value = true
  orderId.value = id
}

const cancelClick = () => {
  isShowDialog.value = false
}

const emits = defineEmits(['confirmClick'])
const submitLoading = ref(false)
const confirmClick = async () => {
  if (submitLoading.value) return
  submitLoading.value = true
  try {
    await confirmOrder({ id: orderId.value })
    ElMessage.success('确认成功')
    emits('confirmClick')
  } catch (error) {
    console.log(error)
  } finally {
    setTimeout(() => {
      submitLoading.value = false
    }, 200)
    isShowDialog.value = false
  }
}
defineExpose({ init })
</script>

<style lang="scss" scoped></style>
