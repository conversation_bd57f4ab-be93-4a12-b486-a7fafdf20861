<template>
  <el-input class="mb-8px" v-model.trim="filterForm.search" clearable placeholder="请输入订单编号、商品名称、采购商手机号、收货人姓名、收货人手机号" />
  <el-form :model="filterForm" v-show="isFormVisible" transition="fade" label-width="100">
    <el-row :gutter="24">
      <el-col :span="8">
        <el-form-item label="订单编号">
          <el-input v-model.trim="filterForm.orderId" type="number" clearable placeholder="请输入订单编号" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="采购商ID">
          <el-input v-model.trim="filterForm.userId" type="number" clearable placeholder="请输入采购商ID" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="下单时间">
          <el-date-picker
            v-model="filterForm.dateArr"
            type="datetimerange"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-time="defaultTime"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="订单状态">
          <el-select v-model="filterForm.statusList" placeholder="请选择订单状态">
            <el-option v-for="item in ORDER_STATUS_ARRAY" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="采购商手机号">
          <el-input v-model.trim="filterForm.userName" type="number" clearable placeholder="请输入采购商手机号" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="物流公司">
          <el-input v-model.trim="filterForm.logisticsName" clearable placeholder="请输入物流公司" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="支付币种">
          <el-select v-model="filterForm.priceType" placeholder="请选择支付币种">
            <el-option v-for="item in priceTypeOptions" :key="item.id" :label="item.value" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="物流单号">
          <el-input v-model.trim="filterForm.logisticsNo" clearable placeholder="请输入物流单号" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="商品名称">
          <el-input v-model.trim="filterForm.goodsName" clearable placeholder="请输入商品名称" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="订单金额" class="flex justify-between">
          <el-input class="flex-1" v-model.trim="filterForm.minFinalPrice" type="number" clearable placeholder="请输入最小订单金额" />
          <span class="mx-2">-</span>
          <el-input class="flex-1" v-model.trim="filterForm.maxFinalPrice" type="number" clearable placeholder="请输入最大订单金额" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="商品编号">
          <el-input v-model.trim="filterForm.spuId" type="number" clearable placeholder="请输入商品编号" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="收货人姓名">
          <el-input v-model.trim="filterForm.deliveryName" clearable placeholder="请输入收货人姓名" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="收货人手机号">
          <el-input v-model.trim="filterForm.deliveryPhone" type="number" clearable placeholder="请输入收货人手机号" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <div class="flex items-center filter-btn">
    <el-button type="primary" @click="searchClick">查询</el-button>
    <el-button type="default" @click="resetClick">重置</el-button>
    <div class="text-#257BFB text-14px inline ml-2 cursor-pointer" @click="toggleFormVisibility">
      <div class="flex items-center" v-if="isFormVisible">
        收起筛选条件 <el-icon class="ml-4px"><ArrowUp /></el-icon>
      </div>
      <div class="flex items-center" v-else>
        展开筛选条件<el-icon class="ml-4px"><ArrowDown /></el-icon>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import { ORDER_STATUS_ARRAY } from '@/constants/order'
import { useDictStore } from '@/pc/stores'
import { formatTime } from '@/common/js/date'

const dictStore = useDictStore()
const priceTypeOptions = computed(() => dictStore.priceTypeOptions?.value || [])
const props = defineProps({
  orderStatus: {
    type: Number,
    default: () => 1,
  },
})

const isFormVisible = ref(false)

const toggleFormVisibility = () => {
  isFormVisible.value = !isFormVisible.value
}

const defaultTime = [formatTime(Date.now(), 'YYYY-MM-DD 00:00:00'), formatTime(Date.now(), 'YYYY-MM-DD 23:59:59')]

const filterForm = ref({ statusList: props.orderStatus })
const emits = defineEmits(['searchClick'])
const searchClick = () => {
  const { dateArr, ...rest } = filterForm.value
  const emitForm = ref({})
  emitForm.value = {
    ...rest,
    orderStartTime: dateArr?.[0] ? formatTime(dateArr[0]) : null,
    orderEndTime: dateArr?.[1] ? formatTime(dateArr[1]) : null,
  }
  emits('searchClick', emitForm.value)
}

const resetClick = () => {
  Object.keys(filterForm.value).forEach((key) => {
    delete filterForm.value[key]
  })
  filterForm.value.statusList = 1
  searchClick()
}
defineExpose({
  filterForm,
})
</script>

<style lang="scss" scoped>
.fade {
  transition: opacity 0.5s ease;
}
.fade-enter-active,
.fade-leave-active {
  opacity: 1;
}
.fade-enter, .fade-leave-to /* .fade-leave-active in <2.1.8 */ {
  opacity: 0;
}
</style>
