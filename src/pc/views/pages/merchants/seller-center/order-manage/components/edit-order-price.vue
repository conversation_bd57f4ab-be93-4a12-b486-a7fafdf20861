<template>
  <el-dialog class="custom-dialog text-#333" v-model="isShowDialog" width="680" :show-close="false" style="padding: 24px">
    <template #header>
      <div class="header flex justify-between text-18px font-600 mb-16px">
        <span>修改订单价格</span>
        <icon class="cursor-pointer" type="icon-guanbi" :size="16" @click="isShowDialog = false"></icon>
      </div>
    </template>
    <template #default>
      <div class="content-wrap text-14px font-500 flex flex-col">
        <div class="flex table-header">
          <div :style="{ width: item.width + 'px' }" v-for="(item, i) in headerList" :key="i" class="px-16px py-12px">{{ item.title }}</div>
        </div>
        <div class="table-content">
          <div class="flex items-center h-44px px-16px border-bottom">
            订单编号：{{ detailInfo?.id }} <span class="mx-8px">|</span> {{ formatTime(detailInfo?.orderTime) }}
          </div>
          <div class="flex items-center item border-bottom px-16px py-12px" v-for="(item, i) in detailInfo?.orderGoodsList" :key="i">
            <div class="flex w-407px mr-16px">
              <img-loader v-if="item?.goodsImage" :src="item?.goodsImage" img-class="w-64px h-64px object-cover" alt=""></img-loader>
              <div class="flex flex-1 flex-col ml-8px justify-between">
                <div class="text-#333 goods-name">
                  {{ item?.goodsName }}
                </div>
                <div class="text-ellipsis-1 mr-20rpx text-#999">
                  <span v-for="(k, v) in JSON.parse(item?.spec)" :key="v" class="mr-8px">{{ k?.item?.value }}</span>
                </div>
              </div>
            </div>
            <div class="flex-col price_wrap">
              <div class="w-120px flex">
                <el-input-number
                  v-model.trim="item.price"
                  placeholder=""
                  clearable
                  controls-position="right"
                  :controls="false"
                  :precision="2"
                  :min="0.01"
                  :title="item.price"
                >
                  <template #prefix> <c-symbol :priceType="priceType"></c-symbol> </template
                ></el-input-number>
              </div>
              <div v-if="!item.price" class="el-form-item__error">{{ errmsg }}</div>
            </div>

            <div class="w-80px pl-16px text-#666">{{ item?.count }}</div>
          </div>
          <div class="px-16px py-8px flex items-center">
            {{ deliveryAddress?.addressType ? '运费' : '外贸综合服务费用' }}
            <template v-if="detailInfo?.orderDeliveryAddress?.addressType">
              <el-input-number
                v-model.trim="detailInfo.deliveryPrice"
                class="flex-1 ml-8px left-input"
                placeholder=""
                clearable
                :controls="false"
                controls-position="right"
                :precision="2"
                :min="0"
                :title="detailInfo.deliveryPrice"
              >
                <template #prefix>
                  <c-symbol :priceType="priceType"></c-symbol>
                </template>
              </el-input-number>
            </template>
            <template v-else>
              <el-input-number
                v-model.trim="detailInfo.servicePrice"
                class="flex-1 ml-8px left-input"
                placeholder=""
                clearable
                :controls="false"
                controls-position="right"
                :precision="2"
                :min="0"
                :title="detailInfo.servicePrice"
              >
                <template #prefix>
                  <c-symbol :priceType="priceType"></c-symbol>
                </template>
              </el-input-number>
            </template>
          </div>
        </div>
      </div>
    </template>
    <template #footer>
      <div class="footer flex items-center justify-between">
        <div class="flex flex-1 flex-wrap">
          <div class="flex mr-2">
            <span class="mr-1">{{ deliveryAddress?.addressType ? '含运费' : '含外贸综合服务费用' }}</span
            ><c-symbol v-if="detailInfo?.deliveryPrice != undefined || detailInfo?.servicePrice != undefined" :priceType="priceType"></c-symbol>
            <span v-if="deliveryAddress?.addressType">{{
              detailInfo?.deliveryPrice === undefined || detailInfo?.deliveryPrice === null ? '-' : detailInfo?.deliveryPrice
            }}</span>
            <span v-else>{{ detailInfo?.servicePrice === undefined || detailInfo?.servicePrice === null ? '-' : detailInfo?.servicePrice }}</span>
          </div>
          <div class="flex"><span class="mr-1">合计</span><c-symbol :priceType="priceType"></c-symbol>{{ finalPrice }}</div>
        </div>
        <div class="btn">
          <el-button type="default" size="large" @click="cancelClick">取消</el-button>
          <el-button type="primary" size="large" :loading="submitLoading" @click="confirmClick">确认</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { getOrderInfo, updatePrice } from '@/apis/order'
import { formatTime } from '@/common/js/date'

const errmsg = '请输入价格'
const headerList = ref([
  { title: '货品', width: 423 },
  { title: '单价', width: 120 },
  { title: '数量', width: 89 },
])

const detailInfo = ref({})
const priceType = ref(null)
const deliveryAddress = ref({})
const isShowDialog = ref(false)
const init = async (id) => {
  isShowDialog.value = true
  try {
    const res = await getOrderInfo({ orderId: id })
    Object.assign(detailInfo.value, res)
    const { orderExt, orderDeliveryAddress } = res
    orderExt && (priceType.value = orderExt?.priceType)
    orderDeliveryAddress && Object.assign(deliveryAddress.value, orderDeliveryAddress)
  } catch (error) {
    console.log(error)
  }
}

// 动态更新合计
const finalPrice = computed(() => {
  const expressFee = deliveryAddress.value?.addressType ? Number(detailInfo.value?.deliveryPrice ?? 0) : Number(detailInfo.value?.servicePrice ?? 0)

  const goodsTotal =
    detailInfo.value?.orderGoodsList?.reduce((total, item) => {
      return total + Number(item.price ?? 0) * Number(item.count ?? 0)
    }, 0) ?? 0
  const total = goodsTotal + expressFee
  return Number(total.toFixed(2))
})

const cancelClick = () => {
  isShowDialog.value = false
}

const emits = defineEmits(['confirmClick'])
const submitLoading = ref(false)
const confirmClick = async () => {
  if (submitLoading.value) return
  submitLoading.value = true
  try {
    const { orderGoodsList, id, deliveryPrice, servicePrice } = detailInfo.value
    let isnull = false
    const orderGoods = orderGoodsList.map((item) => {
      if (!item.price) {
        isnull = true
      }
      return {
        id: item.id,
        price: item.price,
        count: item.count,
      }
    })
    if (isnull) {
      ElMessage.error(errmsg)
      return
    }
    const form = {
      id,
      orderGoodsList: orderGoods,
      deliveryPrice: deliveryPrice === undefined || deliveryPrice === null ? undefined : deliveryPrice,
      servicePrice: servicePrice === undefined || servicePrice === null ? undefined : servicePrice,
    }
    await updatePrice(form)
    ElMessage.success('修改成功')
    emits('confirmClick')
  } catch (error) {
    console.log(error)
  } finally {
    setTimeout(() => {
      submitLoading.value = false
    }, 200)
    isShowDialog.value = false
  }
}
defineExpose({ init })
</script>

<style lang="scss" scoped>
.custom-dialog {
  .content-wrap {
    border: 1px solid #edeef1;
    .goods-name {
      text-overflow: ellipsis;
      overflow: hidden;
      /* stylelint-disable-next-line */
      display: -webkit-box;
      /* stylelint-disable-next-line */
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      word-break: break-all;
    }
  }
  .table-header {
    border-bottom: 1px solid #edeef1;
    div:not(:last-child) {
      border-right: 1px solid #edeef1;
    }
  }
  .border-bottom {
    border-bottom: 1px solid #edeef1;
  }
  .price-input {
    width: 96px;
  }
  .price_wrap {
    position: relative;
  }
  .left-input {
    ::v-deep() {
      .el-input__inner {
        text-align: left;
      }
    }
  }
}
</style>
