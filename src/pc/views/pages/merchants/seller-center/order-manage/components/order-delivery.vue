<template>
  <el-dialog class="custom-dialog text-#333" v-model="isShowDialog" width="545" :show-close="false" style="padding: 24px">
    <template #header>
      <div class="header flex justify-between text-18px font-600 mb-16px">
        <span>订单发货</span>
        <icon class="cursor-pointer" type="icon-guanbi" :size="16" @click="isShowDialog = false" />
      </div>
    </template>
    <template #default>
      <div class="content-wrap text-14px font-500 flex flex-col w-465px">
        <div class="flex table-header border-bottom bg-#FAFAFA">
          <div class="px-16px py-12px">物流信息</div>
        </div>
        <div class="table-content flex items-center px-16px py-8px">
          <el-form class="flex gap-24px pt-18px" :model="form" :rules="rules" ref="formRef">
            <el-form-item class="flex-1" label="物流公司" prop="logisticsName">
              <el-input v-model.trim="form.logisticsName" clearable class="flex-1"></el-input>
            </el-form-item>
            <el-form-item class="flex-1" label="运单号" prop="logisticsNo">
              <el-input v-model.trim="form.logisticsNo" clearable class="flex-1"></el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </template>
    <template #footer>
      <div class="footer flex-row-reverse">
        <el-button type="default" size="large" @click="cancelClick">取消</el-button>
        <el-button type="primary" size="large" :loading="submitLoading" @click="confirmClick">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { sendDelivery } from '@/apis/order'

const form = reactive({
  logisticsName: '',
  logisticsNo: '',
})
const rules = {
  logisticsName: [{ required: true, message: '请输入物流公司', trigger: 'blur' }],
  logisticsNo: [{ required: true, message: '请输入运单号', trigger: 'blur' }],
}

const isShowDialog = ref(false)
const init = async (id) => {
  Object.keys(form).forEach((key) => (form[key] = ''))
  isShowDialog.value = true
  form.id = id
  setTimeout(() => {
    formRef.value?.clearValidate()
  }, 0)
}

const cancelClick = () => {
  isShowDialog.value = false
}

const formRef = ref(null)
const emits = defineEmits(['confirmClick'])
const submitLoading = ref(false)
const confirmClick = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      if (submitLoading.value) return
      submitLoading.value = true
      try {
        await sendDelivery(form)
        ElMessage.success('操作成功')
        emits('confirmClick')
      } catch (error) {
        console.log(error)
      } finally {
        setTimeout(() => {
          submitLoading.value = false
        }, 200)
        isShowDialog.value = false
      }
    } else {
      return false
    }
  })
}
defineExpose({ init })
</script>

<style lang="scss" scoped>
.content-wrap {
  border: 1px solid #edeef1;
  .border-bottom {
    border-bottom: 1px solid #edeef1;
  }
}
</style>
