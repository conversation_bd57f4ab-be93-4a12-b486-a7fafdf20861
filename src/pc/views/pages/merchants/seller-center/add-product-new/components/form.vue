<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px" :scroll-to-error="scrollToError" :disabled="disabledForm">
    <div class="card-wrap">
      <div class="shrink-0 flex items-center pb-4">
        <div>
          <span>已选类目：</span>
          <span class="text-gray" v-for="(item, i) in selectedViewList" :key="i"
            >{{ item.categoryName }}<icon type="icon-xiala1" v-if="i < selectedViewList.length - 1"></icon
          ></span>
          <span v-if="selectedViewList.length === 0" class="text-gray">-</span>
        </div>
        <el-button class="category-btn" @click="backSelect"> <icon type="icon-qiehuanleimu"></icon>切换类目 </el-button>
      </div>
    </div>
    <div class="card-wrap">
      <FormTitle title="基本信息"></FormTitle>
      <el-form-item label="商品标题" prop="spuName">
        <el-input v-model.trim="formData.spuName" maxlength="30" clearable placeholder="请输入商品标题"></el-input>
      </el-form-item>
      <el-form-item label="品牌" prop="brandId">
        <el-select placeholder="请输入品牌" v-model.trim="formData.brandId" clearable>
          <el-option v-for="item in brandOptionsView" :key="item.id" :label="item.brandName" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="商品编码" prop="spuCode">
        <el-input v-model.trim="formData.spuCode" maxlength="30" clearable placeholder="请输入商品编码"></el-input>
      </el-form-item>
      <el-form-item label="发货城市" prop="deliveryFromIds">
        <el-cascader
          class="w-full"
          ref="cityRef"
          v-model="formData.deliveryFromIds"
          :options="cityList"
          :props="{ expandTrigger: 'click' }"
          clearable
          filterable
          placeholder="请选择发货城市"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="起批量" prop="saleMinNum">
        <el-input v-model.trim="formData.saleMinNum" clearable placeholder="请输入起批量" @change="(val) => handleNumber(val, 'saleMinNum')"></el-input>
      </el-form-item>
      <AttrForm v-model="formData.attr" :basicAttr="basicAttr" @validateField="validateField" :categoryDisabled="categoryDisabled"></AttrForm>
    </div>

    <div class="card-wrap">
      <FormTitle title="商品图片"></FormTitle>
      <!-- 商品主图 -->
      <el-form-item label="商品图片" label-width="100px" prop="spuImages">
        <div class="flex flex-col justify-center goods-picture">
          <ImgUploads
            ref="imgUploads"
            v-model="formData.spuImages"
            :dir="OSS_DIR.GOODS_PIC"
            :on-success="uploadSuccess"
            :width="120"
            :height="120"
            :size-limit="10"
            :img-number="5"
            @change="handleChange"
          >
            <template #empty>
              <div>
                <icon type="icon-rongqi2" :size="20"></icon>
                <div class="mt-2 text-xs">添加图片</div>
              </div>
            </template>
          </ImgUploads>
          <div class="h-5 leading-5 mt-2 c-#BEBEBE">尺寸建议500x500（或者正方形），单张大小10M以下，至少3张，最多5张。</div>
        </div>
      </el-form-item>
      <!-- 商品主视频 -->
      <el-form-item label="商品主视频" label-width="100px" prop="spuVideo">
        <div class="flex w-full">
          <div v-for="item in GOODS_LANG_VIDEO_ARRAY" :key="item.id">
            <div class="flex flex-col justify-center goods-picture mr-3" :class="{ 'mb-9': formData[item.id]?.length > 0 }">
              <ImgUpload
                ref="imgUpload"
                v-model="formData[item.id]"
                :dir="OSS_DIR.GOODS_VIDEO"
                :emptyIcon="`${ossUrl}/mall/video-icon.png`"
                :emptyText="''"
                tipsText="添加视频"
                accept="video/mp4"
                :isVideo="true"
                :size-limit="100"
                width="120px"
                height="120px"
                videoHeight="120px"
                @change="(e) => videoChange(e, item.id)"
              >
              </ImgUpload>
            </div>
            <div class="leading-5 my-2 c-#BEBEBE text-center w-[120px]">{{ item.zhName }}</div>
          </div>
        </div>
        <div class="leading-5 c-#BEBEBE">建议上传60秒内商品推荐视频，视频大小不超过100M，最多3个视频。</div>
      </el-form-item>
    </div>

    <div class="card-wrap">
      <FormTitle title="销售属性"></FormTitle>
      <SkuForm
        v-model="formData.spec"
        :saleAttr="saleAttr"
        @validateField="validateField"
        :categoryDisabled="categoryDisabled"
        :disabledSku="disabledForm"
      ></SkuForm>
      <el-form-item label="销售币种" prop="priceType" column-key="priceType">
        <el-select placeholder="请选择销售币种" v-model.trim="formData.priceType" clearable>
          <el-option v-for="item in saleTypeOptions" :key="item.id" :label="item.value" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="销售规格" prop="skuList">
        <el-table :data="formData.skuList || []">
          <el-table-column
            v-for="item in saleAttrViewList"
            :key="item.id"
            :prop="item.spec"
            :label="item.spec"
            :column-key="item.spec"
            min-width="90px"
          ></el-table-column>
          <el-table-column prop="skuNo" label="规格编码" column-key="skuNo" min-width="150px" fixed="right">
            <template #default="scope">
              <el-form-item label="" label-width="0" :prop="'skuList.' + scope.$index + '.skuNo'">
                <el-input v-model.trim="scope.row.skuNo" clearable placeholder="规格编码"></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="skuCode" label="规格条码" column-key="skuCode" min-width="150px" fixed="right">
            <template #default="scope">
              <el-form-item label="" label-width="0" :prop="'skuList.' + scope.$index + '.skuCode'">
                <el-input v-model.trim="scope.row.skuCode" clearable placeholder="规格条码"></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="price" label="价格" column-key="price" min-width="150px" fixed="right">
            <template #header>
              <div><span class="pre-red-text">*</span>价格</div>
            </template>
            <template #default="scope">
              <el-form-item
                label=""
                label-width="0"
                :prop="'skuList.' + scope.$index + '.price'"
                :rules="[
                  {
                    required: true,
                    validator: checkPrice,
                    trigger: ['change', 'blur'],
                  },
                ]"
              >
                <el-input-number
                  v-model.trim="scope.row.price"
                  clearable
                  placeholder="价格"
                  controls-position="right"
                  :precision="2"
                  :min="0.01"
                  :max="LimitNumber"
                >
                  <template #prefix>
                    <span v-if="priceTypeMap[formData.priceType]">{{ priceTypeMap[formData.priceType].symbol || '' }}</span>
                  </template>
                </el-input-number>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="stock" label="库存" column-key="stock" min-width="130px" fixed="right">
            <template #header>
              <div><span class="pre-red-text">*</span>库存</div>
            </template>
            <template #default="scope">
              <el-form-item
                label=""
                label-width="0"
                :prop="'skuList.' + scope.$index + '.stock'"
                :rules="[
                  {
                    required: true,
                    validator: checkStock,
                    trigger: ['change', 'blur'],
                  },
                ]"
              >
                <el-input-number
                  v-model.trim="scope.row.stock"
                  clearable
                  placeholder="库存"
                  controls-position="right"
                  :precision="0"
                  :min="1"
                  :max="LimitNumber"
                ></el-input-number>
              </el-form-item>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </div>

    <div class="card-wrap">
      <FormTitle title="物流信息"></FormTitle>
      <el-form-item label="运费设置" prop="shippingType">
        <el-radio-group v-model="formData.shippingType">
          <el-radio v-for="item in SHIPPING_TYPE_ARRAY" :key="item.id" :value="item.id" :disabled="item.id !== SHIPPING_TYPE.DISCUSSED.id">{{
            item.name
          }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="发货时效" prop="deliveryLimit">
        <el-radio-group v-model="formData.deliveryLimit">
          <el-radio v-for="item in DELIVERY_LIMIT_ARRAY" :key="item.id" :value="item.id">{{ item.name }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </div>

    <div class="card-wrap">
      <FormTitle title="商品详情"></FormTitle>
      <el-form-item prop="spuInfo" label-width="0" class="w-[100%]">
        <RichTextEditor
          class="w-[100%]"
          ref="richTextEditor"
          v-model:content="formData.spuInfo"
          :oss-pic-dir="'GOODS_PIC'"
          :oss-video-dir="'GOODS_VIDEO'"
          height="500px"
          :disabled="!!formData.publishStatus"
          @update:content="handleValidateGoodsDetail"
        ></RichTextEditor>
      </el-form-item>
    </div>

    <!-- <div class="card-wrap">
      <div class="pb-4 text-center">
        <el-button type="primary" class="submit-btn" :loading="btnLoading" @click="handleSubmit">确认发布</el-button>
      </div>
    </div> -->
  </el-form>
  <div class="card-wrap mt-16px">
    <div class="pb-4 text-center">
      <el-button type="primary" :disabled="disabledForm" class="submit-btn" :loading="btnLoading" @click="handleSubmit">确认发布</el-button>
      <el-button size="large" type="default" :disabled="!formData.publishStatus == 1" @click="addLiveClick">加入直播商品池</el-button>
    </div>
  </div>
</template>

<script setup>
import BigNumber from 'bignumber.js'
import { computed } from 'vue'
import RichTextEditor from '@/pc/components/rich-text-editor/rich-text-editor.vue'
import AttrForm from '../components/attr-form.vue'
import FormTitle from '../components/form-title.vue'
import SkuForm from '../components/sku-form.vue'
import { ossUrl } from '@/constants/common'
import { DELIVERY_LIMIT_ARRAY, GOODS_LANG_VIDEO_ARRAY, SHIPPING_TYPE, SHIPPING_TYPE_ARRAY } from '@/constants/goods'
import { OSS_DIR } from '@/constants/oss-dir'
import { useRegionCodeStore } from '@/pc/stores'
import { goodsSubmit } from '@/apis/goods'
import TransFormData from '../js/transform-data'

const props = defineProps({
  selectedViewList: {
    type: Array,
    default: () => [],
  },
  basicAttr: {
    type: Array,
    default: () => [],
  },
  saleAttr: {
    type: Array,
    default: () => [],
  },
  detailInfo: {
    type: Object,
    default: null,
  },
  brandOptions: {
    type: Array,
    default: () => [],
  },
  saleTypeOptions: {
    type: Array,
    default: () => [],
  },
  categoryDisabled: {
    type: Boolean,
    default: false,
  },
})

const LimitNumber = 9999999
const formRef = ref(null)
const formData = ref({
  spuImages: [],
  deliveryFromIds: [],
  shippingType: SHIPPING_TYPE.DISCUSSED.id,
})
const priceTypeMap = computed(() => {
  return props.saleTypeOptions.reduce((prev, cur) => {
    prev[cur.id] = cur || {}
    return prev
  }, {})
})
const brandOptionsView = computed(() => {
  if (formData.value.brandId && props.brandOptions.every((item) => item.id !== formData.value.brandId)) {
    return props.brandOptions.concat([
      {
        id: formData.value.brandId,
        brandName: formData.value.brandName,
      },
    ])
  }
  return props.brandOptions
})
const setFormDataVal = (key, val) => {
  formData.value[key] = val
}
const scrollToError = ref(false)
const validateGoodsPic = (rule, value, callback) => {
  if (value.length < 3) {
    callback(new Error('至少上传3张主图'))
    return
  }
  callback()
}
const hasImagesInRange = (richText, min = 5, max = 100) => {
  if (!richText) return false
  const imgCount = (richText.match(/<img\s+[^>]*src=['"][^'"]+['"][^>]*>/g) || []).length
  return imgCount >= min && imgCount <= max
}
const validateGoodsDetail = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入商品详情'))
  }
  if (!hasImagesInRange(value)) {
    callback(new Error('商品详情图片最少5张'))
    return
  }
  callback()
}
const rules = {
  spuName: [{ required: true, message: '请输入商品标题', trigger: 'blur' }],
  spuCode: [{ required: true, message: '请输入商品编码', trigger: 'blur' }],
  deliveryFromIds: [{ required: true, message: '请选择发货城市', trigger: 'change' }],
  saleMinNum: [{ required: true, message: '请输入起批量', trigger: 'blur' }],
  spuImages: [
    { required: true, message: '请上传商品主图', trigger: 'blur' },
    { required: true, validator: validateGoodsPic, trigger: ['blur', 'change'] },
  ],
  priceType: [{ required: true, message: '请选择销售币种', trigger: ['change'] }],
  spuInfo: [{ required: true, validator: validateGoodsDetail, trigger: ['change', 'blur'] }],
  shippingType: [{ required: true, message: '请选择运费设置', trigger: 'change' }],
  deliveryLimit: [{ required: true, message: '请选择发货时效', trigger: 'change' }],
}
const checkPrice = (rules, value, callback) => {
  if (!value) {
    return callback(new Error(`请输入价格`))
  }
  callback()
}
const checkStock = (rules, value, callback) => {
  if (!value) {
    return callback(new Error(`请输入库存`))
  }
  callback()
}

const Emits = defineEmits(['backSelect'])
const backSelect = () => {
  Emits('backSelect')
}

// 城市
const regionCodeStore = useRegionCodeStore()
const cityRef = ref(null)
// 城市
const cityList = computed(() => regionCodeStore.areaList || [])
// 城市
if (!cityList.value.length) {
  regionCodeStore.queryAreaList()
}

const saleAttrViewList = computed(() => {
  const arr = formData.value.spec || []
  return arr?.map((item) => ({ ...item, items: item.items.filter((item) => item.id) })).filter((item) => item.spec && item.items.length)
})

const validateField = (key) => {
  scrollToError.value = false
  formRef?.value?.validateField(key)
}
// 上传图片成功回调
const uploadSuccess = () => {}
const handleChange = () => {}
// 上传视频改变url
const videoChange = (e, key) => {
  !e.length && (formData.value[key] = '')
}
const handleValidateGoodsDetail = () => {
  validateField('spuInfo')
}
const handleNumber = (val, key) => {
  if (!/^[1-9]\d*$/.test(val)) {
    formData.value[key] = ''
  }
}

const imgUploads = ref(null)
const imgUpload = ref(null)
const isUploading = () => {
  if (imgUploads?.value?.getUpdateLoading()) {
    ElMessage.error('商品图片上传中,请等待上传完成')
    return true
  }

  if (imgUpload?.value?.some((item) => item?.getUpdateLoading())) {
    ElMessage.error('商品主视频上传中,请等待上传完成')
    return true
  }

  return false
}

const router = useRouter()
const btnLoading = ref(false)

const handleSubmit = async () => {
  scrollToError.value = true
  await formRef?.value?.validate()
  try {
    if (isUploading()) return
    const submitFormData = TransFormData.requestData(formData.value, { deliveryFromName: cityRef.value.presentText })
    // 兜底拦截
    if (!submitFormData.attr.length) {
      // 重置并去到指定位置
      formData.value.attr = []
      ElMessage.error('类目属性无效，请重新添加')
      await formRef?.value?.validate()
      return
    }
    if (!submitFormData.spec.length) {
      formData.value.spec = []
      ElMessage.error('销售属性无效，请重新添加')
      await formRef?.value?.validate()
      return
    }
    btnLoading.value = true
    await goodsSubmit(submitFormData)
    formRef.value?.resetFields()
    router.back()
  } catch (e) {
    console.log(e)
  } finally {
    btnLoading.value = false
    scrollToError.value = false
  }
}

const addLiveClick = () => {
  const path = '/seller-center/live-product-manage/add-live-product'
  formData.value ? router.push(`${path}?id=${formData.value?.id}&isEditDetail=1`) : ''
}

const route = useRoute()
const disabledForm = computed(() => {
  return +route.query?.status === 1 || formData.value.publishStatus === 1
})
watchEffect(() => {
  if (route.query.id && props.detailInfo) {
    formData.value = TransFormData.responseData(props.detailInfo)
  }
})

watchEffect(() => {
  if (props.selectedViewList && props.selectedViewList.length) {
    const { id } = props.selectedViewList[props.selectedViewList.length - 1]
    formData.value.categoryId = id
  }
})

const sortSpecValue = (val) => {
  if (!val) return val
  return val
    .split(',')
    .sort((a, b) => {
      const num1 = new BigNumber(a)
      const num2 = new BigNumber(b)
      return num1.minus(num2)
    })
    .join(',')
}

watchEffect(() => {
  const arr = saleAttrViewList.value.reduce((prev, cur) => {
    const newPrev = []

    prev.forEach((item) => {
      cur.items.forEach((curItem) => {
        const spec = item.spec.concat([{ ...cur, item: curItem }])
        const specValue = `${item.specValue},${curItem.id}`
        newPrev.push({
          ...item,
          [cur.spec]: curItem.value,
          spec,
          specValue: sortSpecValue(specValue),
        })
      })
    })

    // 第一次的 prev 为 []
    if (prev.length === 0) {
      // 第一次处理时直接返回当前规格项的 items
      return cur.items.map((item) => ({ [cur.spec]: item.value, image: item.image, spec: [{ ...cur, item }], specValue: item.id }))
    }

    return newPrev
  }, [])
  formData.value.skuList = arr.map((item) => {
    const originItem = formData.value.skuList.find((spec) => spec.specValue === item.specValue) || {}
    // const originItem = formData.value.skuList[i] || {}
    return {
      ...originItem,
      ...item,
    }
  })
})

defineExpose({
  setFormDataVal,
})
</script>

<style lang="scss" scoped>
:deep() {
  .el-input,
  .el-select,
  .el-cascader {
    max-width: 704px;
  }

  .el-input__prefix-inner {
    line-height: 30px;
  }

  .el-input__prefix-inner > :last-child {
    margin-right: 0;
  }
}

.card-wrap {
  background: #fff;
  padding: 16px 20px 1px;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.goods-picture {
  ::v-deep() {
    .empty-icon {
      img {
        width: 20px;
        height: 20px;
      }
    }
    .leading-normal {
      padding: 0;
    }
  }
}

.submit-btn {
  border-radius: 2px;
  padding-left: 16px;
  padding-right: 16px;
  height: 40px;
}

.pre-red-text {
  color: #f56c6c;
}

:deep() {
  .el-table td.el-table__cell,
  .el-table th.el-table__cell.is-leaf {
    border-right: none !important;
  }
}

:deep() {
  .el-input-number__decrease,
  .el-input-number__increase {
    background: #fff;
  }

  .el-input-number__decrease.is-disabled,
  .el-input-number__increase.is-disabled {
    background: #fff;
    opacity: 0.4;
  }
}

.category-btn {
  background: #ffeded;
  border-radius: 16px;
  color: $primary-color;
  padding: 8px;
  margin-left: 8px;
  border: none !important;

  &.is-disabled {
    opacity: 0.5;
  }
}
</style>
