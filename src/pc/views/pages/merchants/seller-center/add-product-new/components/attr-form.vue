<template>
  <el-form-item
    label="类目属性"
    prop="attr"
    :rules="[
      {
        required: true,
        validator: checkAttr,
        trigger: ['change', 'blur'],
      },
    ]"
  >
    <div class="bg-[#f8f8f8] w-full px-3 py-3">
      <template v-if="attrList.length">
        <el-row :gutter="24" v-for="(arr, i) in basicAttrViewList" :key="i" class="mb-[12px]">
          <el-col :span="8" v-for="(item, index) in arr" :key="item.id">
            <el-form-item
              :label="item.attrName"
              label-width="110px"
              :prop="'attr.' + getCurIndex(i, index) + '.names'"
              :rules="[
                {
                  required: !!item.required,
                  validator: (...argus) => checkAttrValue(item, getCurIndex(i), ...argus),
                  trigger: ['change', 'blur'],
                },
              ]"
            >
              <template #label>
                <el-tooltip :content="item.attrName" placement="top">
                  {{ item.attrName }}
                </el-tooltip>
              </template>
              <el-select
                v-model.trim="attrList[getCurIndex(i, index)].names"
                clearable
                filterable
                allow-create
                collapse-tags
                collapse-tags-tooltip
                :reserve-keyword="false"
                multiple
                placeholder="请选择或输入"
                @change="(val) => handleCreateSelectChange(val, getCurIndex(i, index), item)"
              >
                <el-option v-for="attrItem in item.attrItems || []" :key="attrItem.id" :label="attrItem.attrItemName" :value="attrItem.attrItemName" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="line" v-if="basicAttrViewList.length"></div>
        <div class="mb-[12px]" v-for="(item, i) in basicAttrNewList" :key="i">
          <el-form-item
            label="规格项"
            :prop="`attr.${getNewCurIndex(i)}.items`"
            :rules="[
              {
                required: !!item.required,
                validator: (...argus) => checkAttrCustom(item, getNewCurIndex(i), ...argus),
                trigger: ['change', 'blur'],
              },
            ]"
          >
            <div class="flex">
              <div class="w-[218px] mr-[8px]">
                <el-select
                  v-model.trim="item.attr"
                  clearable
                  filterable
                  allow-create
                  remote
                  remote-show-suffix
                  :remote-method="searchAttrList"
                  :loading="searchLoading"
                  placeholder="请选择或输入规格名"
                  @change="(val) => customCreateAttrSelectChange(val, getNewCurIndex(i))"
                >
                  <el-option
                    v-for="attrItem in allAttrList"
                    :key="attrItem.id"
                    :label="attrItem.attrName"
                    :value="attrItem.attrName"
                    :disabled="disabledAttrFn(attrItem.attrName)"
                  />
                </el-select>
              </div>
              <div class="w-[365px]">
                <el-select
                  class="w-[365px]"
                  v-model.trim="item.names"
                  clearable
                  filterable
                  allow-create
                  collapse-tags
                  collapse-tags-tooltip
                  :max-collapse-tags="3"
                  :reserve-keyword="false"
                  multiple
                  placeholder="请选择或输入"
                  @change="(val) => handleCreateSelectChange(val, getNewCurIndex(i), item)"
                >
                  <el-option v-for="attrItem in item.attrItems || []" :key="attrItem.id" :label="attrItem.attrItemName" :value="attrItem.attrItemName" />
                </el-select>
              </div>
              <el-button text type="primary" @click="handleRemove(getNewCurIndex(i))">删除</el-button>
            </div>
          </el-form-item>
        </div>
      </template>
      <el-button @click="handleAddAttr" :icon="Plus" class="btn" v-if="attrList.length < limitLen">添加规格项</el-button>
    </div>
  </el-form-item>
</template>

<script setup>
import { Plus } from '@element-plus/icons-vue'
import * as API from '@/apis/goods'

const props = defineProps({
  basicAttr: {
    type: Array,
    default: () => [],
  },
  modelValue: {
    type: Array,
    default: () => [],
    require: true,
  },
  categoryDisabled: {
    type: Boolean,
    default: false,
  },
})
const Emits = defineEmits(['update:modelValue', 'change', 'validateField'])
const attrList = ref([])

const size = 3
function _chunkArray(arr) {
  const result = []
  for (let i = 0; i < arr.length; i += size) {
    result.push(arr.slice(i, i + size))
  }
  return result
}
const checkAttrValue = ({ attrName, required }, i, rule, value, callback) => {
  if (required) {
    if (!value || value.length === 0) {
      return callback(new Error(`请选择或输入${attrName}`))
    }
  } else {
    // 如果是第一个 并且 没有必填的 并且 每一个都没有填值 拦截
    if (!i && attrList.value.every((item) => !item.required) && attrList.value.every((item) => item.items.every((_item) => !_item.value))) {
      return callback(new Error(`请选择或输入${attrName}`))
    }
    // 如果除了当前的每一个都没有填属性名和属性值 并且当前填了属性名 拦截
    const validArr = attrList.value.filter((item, i1) => item.attr && i1 !== i && item.items.some((_item) => _item.value))
    // 每个都不必填 并且当前填了属性名
    if (attrList.value.every((item) => !item.required) && !validArr.length && attrList.value[i]?.attr) {
      return callback(new Error(`请选择或输入${attrName}`))
    }
  }
  callback()
}
const limitLen = 100
const checkAttr = (rule, value, callback) => {
  if (attrList.value.length === 0) {
    return callback(new Error('请添加规格项'))
  }
  if (attrList.value.length > limitLen) {
    return callback(new Error(`类目规格项最多${limitLen}个`))
  }
  callback()
}
const checkAttrCustom = (item, i, rule, value, callback) => {
  // 如果是第一个 并且 没有基础属性
  if (!i && !basicAttrViewList.value.length) {
    // 如果都没有填规格名
    if (attrList.value.every((item) => !item.attr)) {
      return callback(new Error('请选择或输入规格名'))
    }
    // 如果都没有填规格值
    if (attrList.value.every((item) => !item.items?.length)) {
      return callback(new Error('请选择或输入规格值'))
    }
  }
  callback()
}
const getCurIndex = (pageNo, i) => {
  return pageNo * size + i
}
const getNewCurIndex = (i) => {
  return props.basicAttr.length + i
}

const basicAttrNewList = computed(() => attrList.value.slice(props.basicAttr.length))
const basicAttrViewList = computed(() => _chunkArray(props.basicAttr))
const defaultAttrItemData = Object.freeze({ id: null, attr: '', names: [], items: [], attrItems: [] })

const disabledAttrFn = (item) => {
  return attrList.value.some((cur) => cur.attr === item)
}
const searchLoading = ref(false)
const allAttrList = ref([])
const allAttrMap = computed(() => {
  return allAttrList.value.reduce((prev, cur) => {
    prev[cur.attrName] = cur
    return prev
  }, {})
})
const searchAttrList = async (attrName) => {
  try {
    if (!attrName || !attrName.trim()) return
    searchLoading.value = true
    allAttrList.value = await API.searchAttrList({ attrName, attrType: 1 })
  } catch (e) {
    console.log(e)
  } finally {
    searchLoading.value = false
  }
}
const createNewAttrItemFn = async (attrItemName) => {
  if (!attrItemName || !attrItemName.trim()) return null
  let data = null
  try {
    data = await API.addAttrItem({ attrItemName, attrItemType: 100 })
  } catch (e) {
    console.log(e)
  }
  return data
}
const createNewAttrFn = async (attrName) => {
  if (!attrName || !attrName.trim()) return null
  let data = null
  try {
    data = await API.addAttr({ attrName, attrType: 100 })
  } catch (e) {
    console.log(e)
  }
  return data
}
// 属性值改变
const handleCreateSelectChange = async (val, curIndex, item) => {
  // 名字唯一性
  let nameMap =
    item?.attrItems?.reduce((prev, cur) => {
      prev[cur.attrItemName] = cur.id
      return prev
    }, {}) || {}
  // 最后一个
  const createIndex = val.length - 1
  let createItem = val[createIndex]
  const isNewItemFlag = !nameMap[createItem] && item?.attrItems?.every((itemData) => itemData.attrItemName !== createItem)
  // 如果是新创建的
  if (isNewItemFlag && createItem) {
    createItem = limitSelectValLength(createItem)
    const data = await createNewAttrItemFn(createItem)
    if (data) {
      if (!item.attrItems) item.attrItems = []
      item.attrItems.push(data)
      nameMap[data.attrItemName] = data.id
    } else {
      item.attrItems = item.attrItems.filter((item) => createItem !== item.attrItemName)
      attrList.value[curIndex].names = val.filter((item) => createItem !== item)
      Reflect.deleteProperty(nameMap, data?.attrItemName)
    }
  }
  // 更新列表
  attrList.value[curIndex].items = attrList.value[curIndex].names.map((name, i) => {
    const originItem = attrList.value[curIndex].items[i] || {}
    return {
      ...originItem,
      id: nameMap[name] || originItem.id,
      value: name,
    }
  })
  if (curIndex < props.basicAttr.length) {
    if (curIndex !== 0) Emits('validateField', `attr.${0}.names`)
    Emits('validateField', `attr.${curIndex}.names`)
  } else {
    if (curIndex !== 0) {
      props.basicAttr.length ? Emits('validateField', `attr.${0}.names`) : Emits('validateField', `attr.${0}.items`)
    }
    Emits('validateField', `attr.${curIndex}.items`)
  }
}
// 属性改变
const customCreateAttrSelectChange = async (val, i) => {
  val = limitSelectValLength(val)
  if (!val) {
    attrList.value[i] = {
      ...defaultAttrItemData,
    }
    Emits('validateField', `attr.${i}.items`)
    return
  }
  const item = allAttrMap.value[val]
  if (attrList.value.some((item, i1) => item.attr === val && i !== i1)) {
    ElMessage.warning('规格已存在')
    attrList.value[i] = {
      ...defaultAttrItemData,
      ...attrList.value[i],
      attr: '',
    }
    Emits('validateField', `attr.${i}.items`)
    return
  }
  // 如果是选中了搜到的数据
  if (item) {
    attrList.value[i] = {
      ...defaultAttrItemData,
      ...attrList.value[i],
      attr: val,
      id: item.id,
      attrItems: item.attrItems || [], // 渲染options
    }
  } else {
    const data = await createNewAttrFn(val)
    attrList.value[i] = {
      ...defaultAttrItemData,
      ...attrList.value[i],
      attr: data?.id ? val : '',
      id: data?.id,
    }
  }
  Emits('validateField', `attr.${i}.items`)
}
const limitTextNum = 50
const limitSelectValLength = (val) => {
  if (val && typeof val === 'string' && val.length > limitTextNum) {
    val = val.slice(0, limitTextNum)
    ElMessage.warning('字数超出限制')
  }
  return val
}
const handleAddAttr = () => {
  attrList.value.push({ ...defaultAttrItemData })
  Emits('validateField', 'attr')
}
const handleRemove = (i) => {
  attrList.value.splice(i, 1)
}

// 初始化formData的参数
watchEffect(() => {
  if (props.categoryDisabled) return
  const attr = props.basicAttr.map((item) => ({ ...defaultAttrItemData, ...item, id: item.id, attr: item.attrName }))
  attrList.value = attr
  Emits('update:modelValue', attr)
  Emits('change', attr)
})
// 监听 props.modelValue 并更新 attrList
const stopWatch = watch(
  () => props.modelValue,
  (newValue) => {
    const emptyFlag = props.categoryDisabled && props.basicAttr.length !== 0
    if (emptyFlag || JSON.stringify(newValue) !== JSON.stringify(attrList.value)) {
      const attr = props.basicAttr.map((item) => {
        const originItem = newValue.find((_item) => _item.id === item.id) || item
        return {
          ...defaultAttrItemData,
          ...originItem,
          required: item.required,
          id: originItem.id,
          attr: originItem.attr || originItem.attrName || item.attrName,
        }
      })
      attrList.value = attr.concat(newValue.filter((item) => attr.every((_item) => _item.id !== item.id)))
      try {
        // 详情接口请求完毕直接终止监听
        stopWatch()
      } catch (e) {
        console.log(e)
      }
    }
  },
  { deep: true, immediate: true },
)

// 监听 attrList 并发出更新事件
watch(
  () => attrList,
  (newValue) => {
    Emits('update:modelValue', newValue)
    Emits('change', attrList.value)
  },
  { deep: true },
)
</script>

<style scoped lang="scss">
.line {
  border-top: 1px dashed #eee;
  margin-bottom: 12px;
}

.btn {
  background: #fff;
}

//:deep() {
//  .el-select__selection.is-near {
//    flex-wrap: nowrap;
//  }
//}
</style>
