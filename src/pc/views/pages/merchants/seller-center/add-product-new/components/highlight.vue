<template>
  <span>
    <slot></slot>
    <span v-html="highlightedText"></span>
  </span>
</template>

<script setup>
// 定义 props
const props = defineProps({
  text: {
    type: String,
    required: true,
    default: '',
  },
  highlight: {
    type: String,
    required: true,
  },
})

// 转义特殊字符用于正则表达式
const escapeRegExp = (str) => {
  return str.replace(/[.*+?^${}()|[\]\\]/g, (match) => '\\' + match)
}

// 计算属性：生成分割后的数组，包含高亮和普通文本部分
const highlightedText = computed(() => {
  if (!props.highlight || !props.text) return [{ text: props.text, highlight: false }]

  // 使用正则表达式进行替换，确保不匹配子串
  const reg = new RegExp(`(${escapeRegExp(props.highlight)})`, 'gi')
  return props.text.replace(reg, '<span class="color-[#d8131a]">$1</span>')
})
</script>
