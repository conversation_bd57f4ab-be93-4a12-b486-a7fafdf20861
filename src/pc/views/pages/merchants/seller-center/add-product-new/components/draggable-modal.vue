<template>
  <el-dialog v-model="visible" title="排序" width="500">
    <template #header>
      <div class="text-[#999]"><span class="text-[#333] mr-2 font-600 text-[18px]">排序</span>您可以直接通过拖拽调节顺序</div>
    </template>
    <div v-for="(item, i) in attrViewList" :key="i" class="mb-4">
      <div>{{ item.spec }}</div>
      <div class="flex items-center mt-1">
        <draggable v-model="item.items" item-key="id" class="flex items-center mt-1">
          <template #item="{ element }">
            <div class="px-3 py-1 border-[1px] border-solid border-[#D9D9D9] cursor-pointer bg-[#F4F4F5] mr-4">{{ element.value }}</div>
          </template>
        </draggable>
      </div>
    </div>
    <div class="flex justify-between items-center">
      <div class="cursor-pointer color-[#257BFB]" @click="handleReset">重置排序</div>
      <div>
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确认排序</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import draggable from 'vuedraggable'

const visible = ref(false)

const attrList = ref([])
const attrViewList = computed(() => {
  return attrList.value.filter((item) => item.items.length > 1)
})
const originArr = ref([])

const showModal = (arr) => {
  visible.value = true
  originArr.value = JSON.parse(JSON.stringify(arr))
  attrList.value = JSON.parse(JSON.stringify(arr))
}

const emits = defineEmits(['submit'])
const handleSubmit = () => {
  emits('submit', attrList.value)
  visible.value = false
}
const handleReset = () => {
  attrList.value = JSON.parse(JSON.stringify(originArr.value))
}

defineExpose({
  showModal,
})
</script>
