<template>
  <div class="h-full bg-white flex flex-col overflow-hidden px-4 pt-4 box-border">
    <div class="text-[16px] mb-4 font-600 text-[#333]"><span class="text-[#D8131A] mr-1">*</span>商品标题</div>
    <el-input placeholder="请输入商品标题" class="title-input" clearable v-model.trim="title" maxlength="30" />
    <div class="text-[16px] my-4 flex items-center text-[#333]">
      <span class="text-[#D8131A] mr-1 font-600">*</span><span class="font-600">图片上传</span
      ><span class="ml-2 text-[12px] color-[#666]">传入商品主图，可智能识别类目信息</span>
    </div>
    <div class="flex-1 h-full upload-wrap">
      <ImageUploadEl v-model="imgUrl" :preview="false" drag :dir="OSS_DIR.GOODS_PIC" :size-limit="10" class="w-full h-full">
        <template #default="{ url }">
          <img :src="url" alt="" class="w-full h-full object-contain" v-if="url" />
          <div class="text-[#999] text-center flex items-center w-full h-full" v-else>
            <div>
              <icon type="icon-rongqi2" class="text-[24px] mb-2"></icon>
              <div class="mb-4">点击或将图片拖拽到这里上传</div>
              <div>
                <el-button type="primary" class="img-btn">上传图片</el-button>
              </div>
            </div>
          </div>
        </template>
      </ImageUploadEl>
    </div>
    <div class="h-[66px] shrink-0 flex items-center justify-between">
      <div>
        <span v-if="selectedList.length">已选类目：</span>
        <span class="text-[#999]" v-for="(item, i) in selectedList" :key="i"
          >{{ item.categoryName }}<icon type="icon-xiala1" v-if="i < selectedList.length - 1"
        /></span>
        <span v-if="selectedList.length === 0" class="text-[#999]"> </span>
      </div>
      <div>
        <el-button type="primary" class="btn" @click="handleSubmit" :loading="loading">确定</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import ImageUploadEl from '@/pc/components/img-upload-el/img-upload-el.vue'
import { OSS_DIR } from '@/constants/oss-dir'
import { getCategoryByImage } from '@/apis/goods'

const Emits = defineEmits(['handleImageSearch'])

const imgUrl = ref('')
const title = ref('')
const selectedList = ref([])

const loading = ref(false)
const handleSubmit = async () => {
  try {
    if (!title.value) return ElMessage.error('请输入商品标题')
    if (!imgUrl.value) return ElMessage.error('请上传商品图片')
    const formData = { image: imgUrl.value, title: title.value }
    loading.value = true
    selectedList.value = []
    const data = await getCategoryByImage(formData)
    if (data && data.length) {
      selectedList.value = data.sort((a, b) => a.categoryLevel - b.categoryLevel)
      Emits('handleImageSearch', selectedList.value, formData.title)
    } else {
      ElMessage.error('未搜索到数据')
    }
  } catch (e) {
    console.log(e)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.upload-wrap {
  width: 100%;
  height: 100%;
  background: rgba(216, 19, 26, 0.02);
  overflow: hidden;

  :deep() {
    .el-upload,
    .el-upload-dragger {
      width: 100%;
      height: 100%;
    }

    .el-upload-dragger {
      display: flex;
      justify-content: center;
      align-items: center;
      border-color: #e7e1e0;
      background: #fcfbfb;
      padding: 2px;

      &:hover,
      &.is-dragover {
        border-color: $primary-color;
        background: rgba(216, 19, 26, 0.02);
      }
    }

    .hover-remove-wrap {
      height: 100%;
    }
  }
}

.title-input {
  height: 40px;

  :deep(.el-input__wrapper) {
    box-shadow: 0 0 0 1px $primary-color inset;
  }
}

.btn {
  border-radius: 2px;
  min-width: 64px;
  height: 40px;
}

.img-btn {
  width: 120px;
  height: 36px;
  border-radius: 2px;
}
</style>
