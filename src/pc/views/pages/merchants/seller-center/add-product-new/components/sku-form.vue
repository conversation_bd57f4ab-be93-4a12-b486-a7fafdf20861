<template>
  <el-form-item
    label="销售属性"
    prop="spec"
    :rules="[
      {
        required: true,
        validator: checkSpec,
        trigger: ['change', 'blur'],
      },
    ]"
  >
    <div class="bg-[#f8f8f8] w-full px-3 py-3">
      <!-- sku值 -->
      <div v-for="(item, i) in attrList" :key="i" class="overflow-hidden sku-item-wrap">
        <!-- 规格名 -->
        <el-form-item
          label="规格名"
          :prop="'spec.' + i"
          :rules="[
            {
              required: !!item.required,
              validator: (...argus) => checkSpecItem(item, i, ...argus),
              trigger: ['change', 'blur'],
            },
          ]"
        >
          <div class="flex mb-[12px] items-center justify-between w-full">
            <div class="flex items-center">
              <div class="w-[268px]">
                <el-select
                  v-model.trim="item.spec"
                  clearable
                  filterable
                  allow-create
                  remote
                  remote-show-suffix
                  :remote-method="searchAttrList"
                  :loading="searchLoading"
                  placeholder="请选择或输入规格项"
                  @change="(val) => customCreateAttrSelectChange(val, i)"
                >
                  <el-option
                    v-for="attrItem in allAttrList"
                    :key="attrItem.id"
                    :label="attrItem.attrName"
                    :value="attrItem.attrName"
                    :disabled="disabledAttrFn(attrItem.attrName)"
                  />
                </el-select>
              </div>
              <div class="flex items-center" v-if="!i">
                <el-switch v-model="item.imageFlag" class="mx-2" @change="(val) => handleImageFlag(val, i)" />
                <div>添加规格图片</div>
              </div>
              <el-button text @click="handleRemove(i)" class="remove-btn" v-if="!item.required">删除</el-button>
            </div>
            <el-button text class="text-btn blue-text" v-if="!i && sortArr.length" @click="showModal">自定义排序</el-button>
          </div>
        </el-form-item>

        <!-- 规格值 -->
        <el-form-item
          label="规格值"
          :prop="'spec.' + i + '.items'"
          :rules="[
            {
              required: !!item.required,
              validator: (...argus) => checkAttrValue(item, i, ...argus),
              trigger: ['change', 'blur'],
            },
          ]"
        >
          <div class="flex items-center flex-wrap w-full">
            <div v-for="(item1, i1) in item.items" :key="i + '_' + i1" class="w-[50%] flex items-center mb-[12px]">
              <div class="flex w-[268px]">
                <div class="mr-2" v-if="!i && item.imageFlag">
                  <ImageUploadEl class="w-[32px] h-[32px]" :dir="OSS_DIR.GOODS_PIC" v-model="item1.image">
                    <template #default="{ url }">
                      <div class="w-8 h-8 relative">
                        <img v-if="url" :src="url" class="w-full h-full object-contain bg-white" alt="" />
                        <div class="w-full h-full flex items-center justify-center border-[1px] border-dashed border-[#DCDFE6] bg-[#FCFBFB] text-[16px]" v-else>
                          <el-icon color="#a8abb2"><Picture /></el-icon>
                        </div>
                      </div>
                    </template>
                    <template #remove>
                      <img
                        src="@/assets/imgs/mall/small-close-icon.png"
                        alt=""
                        class="w-[14px] h-[14px] cursor-pointer absolute right-[-7px] top-[-7px] z-1"
                        v-if="!disabledSku"
                      />
                    </template>
                  </ImageUploadEl>
                </div>
                <el-select
                  v-model.trim="item1.value"
                  clearable
                  filterable
                  allow-create
                  placeholder="请选择或输入规格值"
                  @change="(val) => handleCreateSelectChange(val, i, i1)"
                >
                  <el-option
                    v-for="attrItem in item.attrItems || []"
                    :key="attrItem.id"
                    :label="attrItem.attrItemName"
                    :value="attrItem.attrItemName"
                    :disabled="item.items.some((val) => val.value === attrItem.attrItemName)"
                  />
                </el-select>
              </div>
              <el-button text @click="handleRemoveValue(i, i1)" class="remove-btn" v-if="item.items.length > 1">删除</el-button>
            </div>
            <div class="w-[50%] mb-[12px]">
              <el-button text type="primary" :icon="Plus" @click="handleAddAttrValue(i)" class="text-btn"> 添加规格值 </el-button>
            </div>
          </div>
        </el-form-item>
      </div>
      <el-button @click="handleAddAttr" :icon="Plus" class="btn" v-if="attrList.length < 3">添加规格项</el-button>
    </div>
  </el-form-item>
  <draggableModal ref="draggableModalRef" @submit="handleDraggableSubmit" />
</template>

<script setup>
import { Picture, Plus } from '@element-plus/icons-vue'
import ImageUploadEl from '@/pc/components/img-upload-el/img-upload-el.vue'
import draggableModal from './draggable-modal.vue'
import { OSS_DIR } from '@/constants/oss-dir'
import * as API from '@/apis/goods'

const props = defineProps({
  saleAttr: {
    type: Array,
    default: () => [],
  },
  modelValue: {
    type: Array,
    default: () => [],
    require: true,
  },
  categoryDisabled: {
    type: Boolean,
    default: false,
  },
  priceTypeOptions: {
    type: Array,
    default: () => [],
  },
  disabledSku: {
    type: Boolean,
    default: false,
  },
})

const Emits = defineEmits(['update:modelValue', 'validateField'])
const attrList = ref([])
const defaultAttrItemValueData = JSON.stringify({ id: null, value: '', image: '' })
const defaultAttrItemData = JSON.stringify({ id: null, spec: '', imageFlag: true, names: [], items: [JSON.parse(defaultAttrItemValueData)], attrItems: [] })

const checkSpec = (rule, value, callback) => {
  if (!value || value.length === 0) {
    return callback(new Error(`请添加规格项`))
  }
  if (value.length > 3) {
    return callback(new Error(`销售规格最多3个`))
  }
  callback()
}
const checkSpecItem = (item, i, rule, value, callback) => {
  if (item.required) {
    if (!item.spec) {
      return callback(new Error(`请输入规格名`))
    }
  } else {
    if (!i && attrList.value.every((item) => !item.required) && attrList.value.every((item) => !item.spec)) {
      return callback(new Error(`请输入规格名`))
    }
  }
  callback()
}
const checkAttrValue = ({ required, items }, i, rule, value, callback) => {
  if (required) {
    if (items.every((item) => !item.value)) {
      return callback(new Error(`请选择或输入规格值`))
    }
  } else {
    // 如果除了当前的都没有规格名当前值要校验
    if (attrList.value.filter((_, i1) => i1 !== i).every((item) => !item.spec)) {
      if (items.every((item) => !item.value)) {
        return callback(new Error(`请选择或输入规格值`))
      }
    } else if (!i && attrList.value.every((item) => !item.required) && attrList.value.every((item) => item.items.every((_item) => !_item.value))) {
      return callback(new Error(`请选择或输入规格值`))
    }
  }
  callback()
}

const searchLoading = ref(false)
const allAttrList = ref([])
const allAttrMap = computed(() => {
  return allAttrList.value.reduce((prev, cur) => {
    prev[cur.attrName] = cur
    return prev
  }, {})
})

const draggableModalRef = ref(null)
const showModal = () => {
  draggableModalRef.value.showModal(attrList.value)
}
const handleDraggableSubmit = (arr) => {
  attrList.value = arr
}
const sortArr = computed(() => {
  return attrList.value
    .map((item) => {
      return {
        ...item,
        items: item.items.filter((item1) => item1.id),
      }
    })
    .filter((item) => item.items.length > 1)
})

const disabledAttrFn = (item) => {
  return props.saleAttr.some((cur) => cur.attrName === item)
}
const searchAttrList = async (attrName) => {
  try {
    if (!attrName || !attrName.trim()) return
    searchLoading.value = true
    allAttrList.value = await API.searchAttrList({ attrName, attrType: 2 })
  } catch (e) {
    console.log(e)
  } finally {
    searchLoading.value = false
  }
}
const createNewAttrItemFn = async (attrItemName) => {
  if (!attrItemName || !attrItemName.trim()) return null
  let data = null
  try {
    data = await API.addAttrItem({ attrItemName, attrItemType: 100 })
  } catch (e) {
    console.log(e)
  }
  return data
}
const createNewAttrFn = async (attrName) => {
  if (!attrName || !attrName.trim()) return null
  let data = null
  try {
    data = await API.addAttr({ attrName, attrType: 100 })
  } catch (e) {
    console.log(e)
  }
  return data
}
// 属性改变
const customCreateAttrSelectChange = async (val, i) => {
  val = limitSelectValLength(val)
  if (attrList.value.some((item, i1) => item.spec === val && i !== i1)) {
    ElMessage.warning('规格已存在')
    attrList.value[i] = {
      ...JSON.parse(defaultAttrItemData),
      ...attrList.value[i],
      spec: '',
    }
    if (i) Emits('validateField', `spec.${0}`)
    Emits('validateField', `spec.${i}`)
    return
  }
  const item = allAttrMap.value[val]
  // 如果是选中了搜到的数据
  if (item) {
    attrList.value[i] = {
      ...JSON.parse(defaultAttrItemData),
      ...attrList.value[i],
      spec: val,
      id: item.id,
      attrItems: item.attrItems, // 渲染options
    }
  } else {
    const data = await createNewAttrFn(val)
    attrList.value[i] = {
      ...JSON.parse(defaultAttrItemData),
      ...attrList.value[i],
      spec: data?.id ? val : '',
      id: data?.id,
    }
  }
  if (i) Emits('validateField', `spec.${0}`)
  Emits('validateField', `spec.${i}`)
}
// 属性值改变
const handleCreateSelectChange = async (val, i, i1) => {
  val = limitSelectValLength(val)
  attrList.value[i].items[i1].value = val
  const item = attrList.value[i] || {}
  if (!item.attrItems) item.attrItems = []
  // 名字唯一性
  let nameMap =
    item.attrItems?.reduce((prev, cur) => {
      prev[cur.attrItemName] = cur.id
      return prev
    }, {}) || {}
  if (item.items.some((_item, i2) => _item.value === val && i1 !== i2)) {
    ElMessage.warning('规格值已存在')
    attrList.value[i] = {
      ...item,
      items: item.items.map((_item, i2) => {
        if (i1 === i2) {
          return {
            ..._item,
            value: null,
            id: null,
          }
        }
        return _item
      }),
    }
    if (i) Emits('validateField', `spec.${0}.items`)
    Emits('validateField', `spec.${i}.items`)
    return
  }
  const isNewItemFlag = !nameMap[val] && item?.attrItems?.every((itemData) => itemData.attrItemName !== val)
  // 如果是新创建的
  if (isNewItemFlag) {
    const data = await createNewAttrItemFn(val)
    if (data) {
      item.attrItems.push(data)
      nameMap[data.attrItemName] = data.id
      attrList.value[i].items[i1] = { ...attrList.value[i].items[i1], id: data.id }
    } else {
      item.attrItems = item.attrItems.filter((item) => val !== item.attrItemName)
      if (i1) {
        attrList.value[i].items.splice(i1, 1)
      } else {
        attrList.value[i].items[i1] = { ...attrList.value[i].items[i1], id: null, value: null }
      }
      Reflect.deleteProperty(nameMap, data?.attrItemName)
    }
  } else {
    attrList.value[i].items[i1] = { ...attrList.value[i].items[i1], id: nameMap[val] }
  }
  if (i) Emits('validateField', `spec.${0}.items`)
  Emits('validateField', `spec.${i}.items`)
}
const limitTextNum = 50
const limitSelectValLength = (val) => {
  if (val && typeof val === 'string' && val.length > limitTextNum) {
    val = val.slice(0, limitTextNum)
    ElMessage.warning('字数超出限制')
  }
  return val
}
const handleImageFlag = (val, i) => {
  // 重置图片
  const item = attrList.value[i]
  if (!val) {
    attrList.value[i] = {
      ...item,
      items: item.items.map((_item) => ({ ..._item, image: '' })),
    }
  }
}
const handleAddAttr = () => {
  attrList.value.push({ ...JSON.parse(defaultAttrItemData) })
  Emits('validateField', 'spec')
}
const handleRemove = (i) => {
  attrList.value.splice(i, 1)
}
const handleAddAttrValue = (i) => {
  attrList.value[i].items.push({ ...JSON.parse(defaultAttrItemValueData) })
}
const handleRemoveValue = (i, i1) => {
  attrList.value[i].items.splice(i1, 1)
}

// 初始化formData的参数
watchEffect(() => {
  if (props.categoryDisabled) return
  const attr = props.saleAttr?.map((item) => ({ ...JSON.parse(defaultAttrItemData), ...item, id: item.id, spec: item.attrName })) || []
  attrList.value = attr
  Emits('update:modelValue', attr)
})

// 监听 props.modelValue 并更新 attrList
watch(
  () => props.modelValue,
  (newValue) => {
    if (JSON.stringify(newValue) !== JSON.stringify(attrList.value)) {
      attrList.value = newValue
    }
  },
  { deep: true, immediate: true },
)

// 监听 attrList 并发出更新事件
watch(
  () => attrList,
  (newValue) => {
    Emits('update:modelValue', newValue)
  },
  { deep: true },
)
</script>

<style scoped lang="scss">
.btn {
  background: #fff;
}

.remove-btn {
  color: #666;
}

.text-btn {
  padding: 0;
}

.blue-text {
  color: #257bfb;
}

.line {
  border-top: 1px dashed #eee;
  margin-bottom: 12px;
}

.sku-item-wrap {
  border-bottom: 1px dashed #eee;
  margin-bottom: 12px;

  :deep() {
    .el-form-item.is-error {
      margin-bottom: 16px;
    }
  }
}
</style>
