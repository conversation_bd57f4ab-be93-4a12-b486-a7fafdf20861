import { GOODS_LANG_VIDEO } from '@/constants/goods'
import { isValidJSON } from '@/utils/utils'

// 地址 deliveryFromIds => deliveryProvinceCode
// 图片是字符串 spuImages

// 处理需要序列化且无效的字段
function formatParseVal(val, defaultVal = []) {
  if (val && isValidJSON(val)) {
    return JSON.parse(val)
  }
  return defaultVal
}

export default {
  requestData: (params, { deliveryFromName }) => {
    const { deliveryFromIds, spec, attr } = params
    const deliveryFromNameArr = deliveryFromName?.split('/') || ['', '', '']
    const videoParams = Object.keys(GOODS_LANG_VIDEO).reduce((prev, cur) => {
      const videoValue = params[cur]
      prev[cur] = Array.isArray(videoValue) ? videoValue[videoValue.length - 1] : videoValue
      return prev
    }, {})
    const newParams = {
      ...params,
      ...videoParams,
      spuImages: params.spuImages?.join(',') || '',
      deliveryProvinceCode: deliveryFromIds?.[0],
      deliveryProvinceName: deliveryFromNameArr?.[0]?.trim(),
      deliveryCityCode: deliveryFromIds?.[1],
      deliveryCityName: deliveryFromNameArr?.[1]?.trim(),
      deliveryAreaCode: deliveryFromIds?.[2],
      deliveryAreaName: deliveryFromNameArr?.[2]?.trim(),
      spec: spec
        .map((item) => {
          return {
            ...item,
            items: item.items?.filter((_item) => _item.id) || [],
          }
        })
        .filter((item) => item.id && item.items.length),
      attr: attr
        .map((item) => {
          return {
            ...item,
            items: item.items?.filter((_item) => _item.id) || [],
          }
        })
        .filter((item) => item.id && item.items.length),
    }
    Reflect.deleteProperty(newParams, 'deliveryFromIds')
    // 删除无用数据
    Reflect.deleteProperty(newParams, 'updateTime')
    Reflect.deleteProperty(newParams, 'updateName')
    Reflect.deleteProperty(newParams, 'updateId')
    Reflect.deleteProperty(newParams, 'createId')
    Reflect.deleteProperty(newParams, 'createName')
    Reflect.deleteProperty(newParams, 'userId')
    Reflect.deleteProperty(newParams, 'publishStatus')
    Reflect.deleteProperty(newParams, 'brandName')
    Reflect.deleteProperty(newParams, 'deleted')
    Reflect.deleteProperty(newParams, 'maxPrice')
    Reflect.deleteProperty(newParams, 'minPrice')
    Reflect.deleteProperty(newParams, 'language')
    Reflect.deleteProperty(newParams, 'categoryList')
    return newParams
  },
  responseData: (params) => {
    let newParams = { ...params }
    // 处理没返回情况
    const attr = formatParseVal(params.attr)
    const spec = formatParseVal(params.spec)
    const skuList = Array.isArray(params.skuList) ? params.skuList : []
    const categoryList = Array.isArray(params.categoryList) ? params.categoryList : []
    try {
      newParams = {
        ...params,
        attr: attr.map((item) => {
          return {
            ...item,
            names: item.items?.map((item) => item.value) || [],
            attrItems: params?.basicAttr?.find((_item) => `${_item.id}` === `${item.id}`)?.attrItems || [],
          }
        }),
        spec: spec.map((item) => {
          return {
            ...item,
            imageFlag: true,
            names: item.items.map((item) => item.value),
            attrItems: params?.saleAttr?.find((_item) => `${_item.id}` === `${item.id}`)?.attrItems || [],
          }
        }),
        skuList: skuList.map((item) => {
          let obj = {}
          try {
            obj = JSON.parse(item.spec).reduce((prev, cur) => {
              prev[cur.spec] = cur.item.value
              return prev
            }, {})
            obj.spec = JSON.parse(item.spec)
          } catch (e) {
            console.log(e)
          }
          return {
            ...item,
            ...obj,
          }
        }),
        spuImages: params?.spuImages?.split(',') || [],
        deliveryFromIds: [params.deliveryProvinceCode, params.deliveryCityCode, params.deliveryAreaCode],
        categoryList,
      }
      Reflect.deleteProperty(newParams, 'basicAttr')
      Reflect.deleteProperty(newParams, 'saleAttr')
    } catch (e) {
      console.log(e)
    }
    return newParams
  },
}
