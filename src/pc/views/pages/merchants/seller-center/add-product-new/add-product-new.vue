<template>
  <div class="flex-col-full search-wrap bg-white" v-if="!showAddForm">
    <div class="flex h-[52px] bg-[#f7f7f8] shrink-0">
      <div
        v-for="(item, i) in tabArr"
        :key="i"
        class="flex-1 flex items-center justify-center tab-item"
        :class="{ active: tabActive === item.value }"
        @click="handleTabClick(item)"
      >
        <div><icon :type="item.icon" class="mr-1"></icon>{{ item.label }}</div>
      </div>
    </div>
    <div class="max-h-[1000px] flex-col-full">
      <div v-if="tabActive === 1" class="flex-col-full">
        <div class="px-4 pt-4 bg-white">
          <div class="w-full flex search-box relative">
            <input v-model.trim="inputValue" placeholder="请输入关键词" type="text" @input="onSearch" />
            <div class="search-button" @click="inputValue = ''">
              <i-inside-search v-if="!inputValue" />
              <img src="@/assets/imgs/mall/small-close-icon.png" class="w-[18px]" v-else />
            </div>
          </div>
          <div class="relative">
            <div class="absolute top-0 left-0 z-2 bg-white w-full flex flex-col float-wrap" v-if="inputValue">
              <div class="text-4 font-600 mb-2 text-[#333]">类目</div>
              <div class="overflow-auto">
                <div class="category-search-item" v-for="(item, i) in searchCategoryArr" :key="i" @click="handleSearchItem(item)">
                  <Highlight v-if="item.category1" :text="item.category1.categoryName" :highlight="inputValue" />
                  <Highlight v-if="item.category2" :text="item.category2.categoryName" :highlight="inputValue"><icon type="icon-xiala1" /></Highlight>
                  <Highlight v-if="item.category3" :text="item.category3.categoryName" :highlight="inputValue"><icon type="icon-xiala1" /></Highlight>
                </div>
                <el-empty v-if="searchCategoryArr.length === 0"></el-empty>
              </div>
            </div>
          </div>
        </div>
        <div class="bg-white flex-col-full px-4">
          <div class="text-[16px] py-4 font-600 text-[#333]"><icon type="icon-shangpinleimu" class="mr-1"></icon>选择商品类目</div>
          <div class="flex-1 overflow-hidden">
            <div class="flex h-full category-wrap overflow-hidden">
              <div v-for="(item, i) in categoryArr" :key="i" class="w-[33.33%] flex-col-full category-item">
                <div class="px-4 py-4">
                  <el-input clearable :placeholder="`请输入${item.label}`" v-model.trim="item.value" :suffix-icon="Search" />
                </div>
                <div class="flex-1 overflow-auto">
                  <div
                    class="select-item"
                    :class="{ active: selectedList[i]?.id === item.id }"
                    v-for="item in categoryListView[i] || []"
                    :key="item.id"
                    @click="handleCategoryItem(item, i)"
                  >
                    <div>{{ item.categoryName }}</div>
                    <icon type="icon-xiala1" class="text-[#999]" v-if="i < 2 && item.subList && item.subList.length"></icon>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="h-[66px] shrink-0 flex items-center justify-between">
            <div>
              <span v-if="selectedViewList.length">已选类目：</span>
              <span class="text-[#999]" v-for="(item, i) in selectedViewList" :key="i"
                >{{ item.categoryName }}<icon type="icon-xiala1" v-if="i < selectedViewList.length - 1"
              /></span>
              <span v-if="selectedViewList.length === 0" class="text-[#999]"> </span>
            </div>
            <div>
              <el-button type="primary" class="btn" :disabled="disabledBtn" @click="handleSubmit" :loading="loading">确定</el-button>
            </div>
          </div>
        </div>
      </div>
      <ImgPublish v-else @handleImageSearch="handleImageSearch" />
    </div>
  </div>
  <div v-show="showAddForm">
    <AddForm
      ref="addFormRef"
      :detailInfo="detailInfo"
      :basicAttr="basicAttr"
      :saleAttr="saleAttr"
      :selectedViewList="selectedViewList"
      :categoryList="categoryList"
      @backSelect="backSelect"
      :brandOptions="brandOptions"
      :saleTypeOptions="saleTypeOptions"
      :categoryDisabled="categoryDisabled"
    />
  </div>
</template>

<script setup>
import { Search } from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'
import AddForm from './components/form.vue'
import Highlight from './components/highlight.vue'
import ImgPublish from './components/img-publish.vue'
import { useDictStore } from '@/pc/stores'
import { getCategoryAttrData, getCategoryBrand, getCategoryTree, getSellerGoodsInfo } from '@/apis/goods'
import { debounce } from '@/common/js/util'

const tabArr = [
  { label: '搜索发品', value: 1, icon: 'icon-sousuofapin' },
  { label: '以图发品', value: 2, icon: 'icon-yitufapin' },
]
const tabActive = ref(tabArr[0].value)

const categoryArr = ref([
  { label: '一级类目', value: '' },
  { label: '二级类目', value: '' },
  { label: '三级类目', value: '' },
])
const showAddForm = ref(false)
const detailInfo = ref(null)
const route = useRoute()
const id = route.query?.id

const handleTabClick = (item) => {
  // if (item.value === 2) return
  tabActive.value = item.value
}

const categoryList = reactive([])
const categoryListView = computed(() => {
  return categoryList.map((arrItem, i) => {
    const { value } = categoryArr.value?.[i] || {}
    arrItem =
      arrItem?.filter((item) => {
        if (!value) return true
        return item?.categoryName?.includes(value.trim())
      }) || []
    return arrItem
  })
})
const getCategoryList = async () => {
  try {
    // 1-后台类目, 2-前台类目
    categoryList[0] = await getCategoryTree({ categoryType: 1 })
    if (!id && selectedList.value.length === 0) {
      const category1 = categoryList[0]?.[0]
      if (!category1) return
      const category2 = category1.subList?.[0]
      if (category2) {
        const arr = [category1, category2]
        categoryList[1] = category1.subList
        categoryList[2] = category2.subList || []
        const category3 = category2.subList?.[0]
        if (category3) arr.push(category3)
        selectedList.value = arr
      } else {
        selectedList.value = [category1]
      }
    }
  } catch (e) {
    console.log(e)
  }
}
getCategoryList()

const inputValue = ref(null)
const searchCategoryArr = ref([])
// 处理分类项的函数
const processCategoryItem = (categoryItem, searchCategoryList, val, category1 = null) => {
  if (categoryItem.subList && categoryItem.subList.length) {
    for (let subItem of categoryItem.subList) {
      if (subItem.subList && subItem.subList.length) {
        for (let subSubItem of subItem.subList) {
          searchCategoryList.push({
            category3: subSubItem,
            category2: subItem,
            category1: category1 || categoryItem,
          })
        }
      } else {
        searchCategoryList.push({
          category3: category1 ? subItem : null,
          category2: category1 ? categoryItem : subItem,
          category1: category1 || categoryItem,
        })
      }
    }
  } else {
    searchCategoryList.push({
      category3: null,
      category2: category1 ? categoryItem : null,
      category1: category1 || categoryItem,
    })
  }
}

// 搜索函数
const onSearch = debounce(() => {
  const val = inputValue.value?.trim()
  const searchCategoryList = []
  const regex = new RegExp(val, 'i') // 使用正则表达式进行不区分大小写的匹配

  if (!val) {
    searchCategoryArr.value = []
    return
  }

  if (categoryList?.[0]) {
    for (let item of categoryList[0]) {
      if (regex.test(item.categoryName)) {
        // 处理一级分类匹配
        processCategoryItem(item, searchCategoryList, val)
      } else {
        // 处理二级和三级分类匹配
        if (item.subList) {
          for (let item1 of item.subList) {
            if (regex.test(item1.categoryName)) {
              processCategoryItem(item1, searchCategoryList, val, item)
            } else {
              if (item1.subList) {
                for (let item2 of item1.subList) {
                  if (regex.test(item2.categoryName)) {
                    searchCategoryList.push({
                      category3: item2,
                      category2: item1,
                      category1: item,
                    })
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  searchCategoryArr.value = searchCategoryList
}, 300)
const handleSearchItem = ({ category1, category2, category3 }) => {
  selectedList.value = [category1, category2, category3]
  categoryList[1] = category1?.subList || []
  categoryList[2] = category1?.subList?.find((item) => item.id === category2?.id)?.subList || []
  inputValue.value = ''
}

const selectedList = ref([])
const selectedViewList = computed(() => selectedList.value.filter((item) => item))
const disabledBtn = computed(
  () => !selectedViewList.value[selectedViewList.value.length - 1] || !!selectedViewList.value[selectedViewList.value.length - 1]?.subList?.length,
)
const handleCategoryItem = (item, i) => {
  selectedList.value[i] = item
  if (i === 0) {
    categoryList[i + 1] = item.subList
    categoryList[i + 2] = []
    selectedList.value[i + 1] = null
    selectedList.value[i + 2] = null
  } else if (i === 1) {
    categoryList[i + 1] = item.subList
    selectedList.value[i + 1] = null
  }
}

const brandOptions = ref([])
const brandLoading = ref(false)
const getBrandList = async () => {
  try {
    brandLoading.value = true
    const lastItem = selectedViewList.value[selectedViewList.value.length - 1]
    if (!lastItem) return {}
    brandOptions.value = await getCategoryBrand({ id: lastItem.id })
  } catch (e) {
    console.log(e)
  } finally {
    brandLoading.value = false
  }
}

const loading = ref(false)
const basicAttr = ref([])
const saleAttr = ref([])
const handleSubmit = async () => {
  try {
    loading.value = true
    const lastItem = selectedViewList.value[selectedViewList.value.length - 1]
    if (!lastItem) return {}
    getBrandList()
    let data = {}
    if (categoryDisabled.value && (detailInfo.value?.basicAttr || detailInfo.value?.saleAttr)) {
      data.basicAttr = detailInfo.value?.basicAttr || []
      data.saleAttr = detailInfo.value?.saleAttr || []
    } else {
      data = await getCategoryAttrData({ categoryId: lastItem.id })
      basicAttr.value = data.basicAttr || []
      saleAttr.value = data.saleAttr || []
    }
    showAddForm.value = true
    return data
  } catch (e) {
    console.log(e)
    return {}
  } finally {
    loading.value = false
  }
}

const addFormRef = ref(null)
const handleImageSearch = (arr, title) => {
  selectedList.value = arr || []
  addFormRef?.value?.setFormDataVal('spuName', title)
  handleSubmit()
}

const dictStore = useDictStore()
const saleTypeOptions = computed(() => dictStore.saleTypeOptions)
const getSaleType = () => {
  if (!dictStore.saleTypeOptions.length) {
    dictStore.getSaleTypeList()
  }
}
const getGoodsDetail = async (id) => {
  try {
    loading.value = true
    const res = await getSellerGoodsInfo({ id })
    selectedList.value = res.categoryList.sort((a, b) => a.categoryLevel - b.categoryLevel)
    const data = await handleSubmit()
    detailInfo.value = {
      ...data,
      ...res,
    }
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}

if (id) {
  showAddForm.value = true
}
const categoryDisabled = ref(!!id)
onMounted(() => {
  id && getGoodsDetail(id)
  getSaleType()
})

const backRenderCategory = () => {
  const [category1, category2, category3] = selectedList.value
  if (!category1) return
  const category1Arr = categoryList[0]?.find((item) => item.id === category1.id)?.subList || []
  if (category1 && category2) {
    const category2Arr = category1Arr.find((item) => item.id === category2.id)?.subList || []
    categoryList[1] = category1Arr

    if (category3) {
      categoryList[2] = category2Arr
    }
  }
}
const backSelect = () => {
  backRenderCategory()
  if (!id) {
    showAddForm.value = false
    categoryDisabled.value = false
    tabActive.value = 1
    return
  }
  ElMessageBox.confirm('是否覆盖当前已选类目？', '提示', {
    confirmButtonText: '是',
    cancelButtonText: '否',
    distinguishCancelAndClose: true,
    type: 'warning',
  })
    .then(() => {
      showAddForm.value = false
      categoryDisabled.value = false
      tabActive.value = 1
    })
    .catch((e) => {
      if (e === 'cancel') {
        showAddForm.value = false
        categoryDisabled.value = true
        tabActive.value = 1
      }
    })
}
</script>

<style scoped lang="scss">
.btn {
  border-radius: 2px;
  min-width: 64px;
  height: 40px;
}

.tab-item {
  background-size: cover;
  cursor: pointer;
  font-size: 18px;
  color: #666;

  &.active {
    background-image: url('https://static.chinamarket.cn/static/trade-exhibition/mall/tab1-bg-left-active.png');
    font-weight: 600;
    color: #333;
  }

  &.disabled {
    cursor: not-allowed;
  }

  &:last-child {
    &.active {
      background-image: url('https://static.chinamarket.cn/static/trade-exhibition/mall/tab1-bg-right-active.png');
    }
  }
}

.flex-col-full {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
}

.search-wrap {
  border-radius: 20px 20px 0 0;
  min-height: 500px;
  height: $main-height;
}

.category-wrap {
  border-top: 1px solid #dcdee0;
  border-bottom: 1px solid #dcdee0;
  border-left: 1px solid #dcdee0;

  .category-item {
    border-right: 1px solid #dcdee0;
  }

  .select-item {
    position: relative;
    height: 32px;
    padding: 0 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #333;
    cursor: pointer;

    &::before {
      position: absolute;
      left: 0;
      top: 0;
      content: '';
      display: none;
      height: 100%;
      border-right: 3px solid #d8131a;
    }

    &.active {
      background-color: rgba(253, 246, 246, 1);

      &::before {
        display: block;
      }
    }
  }
}

.search-box {
  input {
    box-sizing: border-box;
    width: 100%;
    height: 40px;
    border-radius: 4px;
    outline: 0;
    border: 1px solid $primary-color;
    background-color: #fff;
    font-weight: 400;
    padding-left: 12px;
    padding-right: 181.5px;
    text-overflow: ellipsis;
    color: #333;
  }

  .search-button {
    cursor: pointer;
    position: absolute;
    right: 8px;
    top: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    color: red;
    // background-image: linear-gradient(270deg, #d8131a 0%, #fd441f 100%);

    ::v-deep {
      path {
        fill: #d8131a;
      }
    }
  }
}

.float-wrap {
  box-shadow: 0 4px 12px 0 rgba(77, 0, 0, 0.1);
  border-radius: 8px;
  height: 320px;
  overflow: hidden;
  padding: 16px 16px 0;
  color: #666;
  box-sizing: border-box;
}

.category-search-item {
  padding: 4px;
  margin-bottom: 4px;
  cursor: pointer;

  &.active,
  &:hover {
    background: #fdf6f6;
  }
}
</style>
