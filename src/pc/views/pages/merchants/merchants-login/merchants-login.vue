<template>
  <div class="login-wrap min-h-wrap full-banner" v-if="!loading">
    <div class="swiper-container">
      <video
        :src="`${ossUrl}/intro-video.mp4`"
        autoplay
        class="w-full h-full object-cover"
        controls360="no"
        disablePictureInPicture
        loop
        muted
        playsinline
        raw-controls
        t7-video-player-type="inline"
        webkit-playsinline
        x-webkit-airplay="allow"
        x5-video-orientation="portraint"
        x5-video-player-fullscreen=""
        x5-video-player-type="h5-page"
      ></video>
    </div>
    <div class="w-1260 min-h-wrap relative flex justify-center items-center inner pt-[64px]">
      <div class="text-[50px] font-bold text-[#fff]" v-mode="LINYI_CHINA_MARKET_MAP">临沂商城·中国大集</div>
      <div class="pl-25 py-[64px]" :class="$storageLocale === 'zh' ? ' w-[580px]' : ' w-[670px]'">
        <LoginForm ref="loginFormRef" :isNormal="false" :userType="userType" :referralCode="referralCode">
          <template #headerLogo="{ isRegister }">
            <div class="mb-4">
              <div class="flex items-center justify-center px-2">
                <div class="text-[32px] font-bold mr-2 text-ellipsis overflow-hidden whitespace-nowrap">{{ title }}{{ isRegister ? '注册' : '' }}</div>
                <div
                  v-if="!isRegister"
                  class="text-[20px] font-bold min-w-[120px] h-[32px] bg-cover color-[#fff] flex items-center justify-center text-ellipsis overflow-hidden whitespace-nowrap px-[12px] shrink-0"
                  :style="`background-image: url('${ossUrl}/merchants/login-title-bg.png')`"
                >
                  欢迎登录
                </div>
              </div>
            </div>
          </template>
        </LoginForm>
      </div>
    </div>
  </div>
</template>

<script setup>
import LoginForm from '@/pc/components/login-form/login-form.vue'
import { ossUrl } from '@/constants/common'
import { MERCHANTS_TYPE, MERCHANTS_TYPE_ARRAY, MERCHANTS_TYPE_MAP } from '@/constants/merchants'
import { LINYI_CHINA_MARKET_MAP } from '@/constants/special-field'
import event from '@/event'
import { OPEN_NEW_LOGIN } from '@/event/modules/site'

const router = useRouter()
const route = useRoute()
const userType = +route.params.id
const referralCode = route.query.referralCode

const title = computed(() => {
  const item = MERCHANTS_TYPE_ARRAY.find((item) => item.id === userType)
  return item?.title?.zh || ''
})

const loading = ref(true)
onBeforeMount(() => {
  // 如果没有用户类型 或者 用户类型无效 或者 是采购商
  if (!userType || !MERCHANTS_TYPE_MAP[userType] || userType === MERCHANTS_TYPE.BUYER.id) {
    router.replace({
      path: '/mall',
    })
    event.emit(OPEN_NEW_LOGIN, {})
  } else {
    loading.value = false
  }
})
</script>

<style scoped lang="scss">
.login-wrap {
  width: 100%;
  padding: 0;
  overflow-y: hidden;
  background: linear-gradient(240deg, #d8131a 0%, #f72f36 86%);
}

.title-text-wrap {
}

.min-h-wrap {
  min-height: $main-height;
}

.full-banner {
  position: relative;
  min-height: $main-height;
  background: #b8a9aa;

  .swiper-container {
    position: absolute;
    inset: 0;
    z-index: 1;

    &::after {
      content: '';
      position: absolute;
      inset: 0;
      background-color: rgba($color: #000000, $alpha: 0.5);
    }
  }

  .inner {
    position: relative;
    z-index: 2;
  }
}

[dir='rtl'] {
  .pl-25 {
    padding-left: 0;
    padding-right: 100px;
  }
}
</style>
