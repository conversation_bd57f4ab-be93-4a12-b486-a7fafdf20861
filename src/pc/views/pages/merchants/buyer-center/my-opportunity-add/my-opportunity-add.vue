<template>
  <div class="flex-col-full search-wrap bg-white pt-4">
    <BorderTitle class="font-600 pl-4" title="发布找货需求" />
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="240px" :scroll-to-error="scrollToError" size="large">
      <el-form-item label="企业名称" prop="companyName">
        <el-input v-model="formData.companyName" maxlength="50" clearable placeholder="请输入企业名称" />
      </el-form-item>
      <el-form-item label="商品标题" prop="title">
        <el-input v-model="formData.title" maxlength="30" show-word-limit clearable placeholder="请输入商品标题" />
      </el-form-item>
      <el-form-item label="商品分类" prop="categoryId">
        <el-select placeholder="请选择商品分类" v-model="formData.categoryId" clearable filterable>
          <el-option v-for="item in category1" :key="item.id" :label="item.categoryName" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="商品描述" prop="description">
        <el-input
          v-model="formData.description"
          maxlength="300"
          show-word-limit
          type="textarea"
          :autosize="{ minRows: 5, maxRows: 8 }"
          clearable
          placeholder="商品的相关描述以及相关要求，便于供应商报价"
        />
      </el-form-item>
      <el-form-item label="求购数量" prop="needCount">
        <el-input-number
          v-model.trim="formData.needCount"
          clearable
          placeholder="请输入求购数量"
          controls-position="right"
          :precision="0"
          :min="1"
          :max="LimitNumber"
          :style="{ width: $storageLocale !== 'zh' ? '50%' : '' }"
        />
      </el-form-item>
      <el-form-item label="截止时间" prop="deadline">
        <el-date-picker
          v-model="formData.deadline"
          type="datetime"
          placeholder="请选择截止时间"
          :disabled-date="disabledDate"
          size="large"
          :style="{ width: $storageLocale !== 'zh' ? '50%' : '' }"
        />
      </el-form-item>
      <!-- 商品主图 -->
      <el-form-item label="商品图片" prop="imageUrl">
        <div class="flex flex-col justify-center goods-picture">
          <ImgUploads v-model="formData.imageUrl" :dir="OSS_DIR.OPPORTUNITY_PIC" :width="120" :height="120" :size-limit="10" :img-number="5">
            <template #empty>
              <div>
                <icon type="icon-rongqi2" :size="20"></icon>
                <div class="mt-2 text-xs">添加图片</div>
              </div>
            </template>
          </ImgUploads>
          <div class="h-5 leading-5 mt-2 c-#BEBEBE">尺寸建议500x500（或者正方形），单张大小10M以下，最多5张。</div>
        </div>
      </el-form-item>
      <!-- 商品主图 -->
      <el-form-item label="商品视频" prop="videoUrl">
        <div class="flex flex-col justify-center goods-picture">
          <ImgUploads
            v-model="formData.videoUrl"
            :dir="OSS_DIR.OPPORTUNITY_VIDEO"
            :emptyText="''"
            accept="video/mp4"
            :width="120"
            :height="120"
            :size-limit="50"
            :img-number="5"
            isVideo
          >
            <template #empty>
              <div>
                <icon type="icon-shipin" :size="20"></icon>
                <div class="mt-2 text-xs">添加视频</div>
              </div>
            </template>
          </ImgUploads>
          <div class="h-5 leading-5 mt-2 c-#BEBEBE">建议上传一段30秒内的商品视频，视频大小不超过50M，最多上传5个</div>
        </div>
      </el-form-item>
      <el-form-item label="预估单价" prop="unitPrice">
        <el-input-number
          v-model.trim="formData.unitPrice"
          clearable
          placeholder="请输入预估单价"
          controls-position="right"
          :precision="2"
          :min="0.01"
          :max="LimitNumber"
          :style="{ width: $storageLocale !== 'zh' ? '50%' : '' }"
        >
          <template #prefix>
            <span>¥</span>
          </template>
        </el-input-number>
      </el-form-item>
      <el-form-item label="是否定制" prop="customized">
        <el-radio-group v-model="formData.customized">
          <el-radio :value="item.id" size="large" v-for="item in CustomizedArray" :key="item.id">{{ item.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="交货周期" prop="deliveryType">
        <el-select placeholder="请选择交货周期" v-model="formData.deliveryType" clearable>
          <el-option v-for="item in DeliveryTypeArray" :key="item.id" :label="item.label" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="交货地址" prop="deliveryPlace">
        <el-input v-model="formData.deliveryPlace" clearable placeholder="请输入交货地址" />
      </el-form-item>
      <el-form-item label="联系电话" prop="phone">
        <el-input v-model="formData.phone" :maxlength="30" :formatter="formatPhone" :parser="parsePhone" clearable placeholder="请输入联系电话" />
      </el-form-item>
      <el-form-item label="" label-width="0">
        <div class="text-center w-full">
          <el-button type="primary" class="submit-btn" :loading="btnLoading" @click="handleSubmit">发布需求</el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { CustomizedArray, DeliveryTypeArray } from '@/constants/goods'
import { OSS_DIR } from '@/constants/oss-dir'
import { useCategoryStore } from '@/pc/stores'
import { opportunityAdd } from '@/apis/goods'
import { trimParamsChangeOrigin } from '@/utils/utils'

const formData = ref({
  imageUrl: [],
  videoUrl: [],
})
const scrollToError = ref(false)
const formRef = ref(null)
const router = useRouter()
const btnLoading = ref(false)
const LimitNumber = *********

const validateGoodsPic = (rule, value, callback) => {
  if (value.length > 5) {
    callback(new Error('最多5个'))
    return
  }
  callback()
}

const disabledDate = (time) => {
  return time.getTime() < Date.now()
}

// 只保留数字、+、空格（显示用）
const formatPhone = (value) => value.replace(/[^\d+\s]/g, '')

// 只保留数字、+、空格（数据处理用）
const parsePhone = (value) => value.replace(/[^\d+\s]/g, '')

const rules = reactive({
  companyName: [{ required: false, message: '请输入企业名称', trigger: ['blur', 'change'] }],
  title: [{ required: true, message: '请输入商品标题', trigger: ['blur', 'change'] }],
  categoryId: [{ required: true, message: '请选择商品分类', trigger: ['blur', 'change'] }],
  description: [{ required: true, message: '请输入商品的相关描述以及相关要求', trigger: ['blur', 'change'] }],
  needCount: [{ required: true, message: '请输入求购数量', trigger: ['blur', 'change'] }],
  deadline: [{ required: true, message: '请输入截止时间', trigger: ['blur', 'change'] }],
  unitPrice: [{ required: true, message: '请输入预估单价', trigger: ['blur', 'change'] }],
  customized: [{ required: true, message: '请选择是否定制', trigger: ['blur', 'change'] }],
  deliveryType: [{ required: true, message: '请选择交货周期', trigger: ['blur', 'change'] }],
  deliveryPlace: [{ required: true, message: '请输入交货地址', trigger: ['blur', 'change'] }],
  phone: [{ required: true, message: '请输入联系电话', trigger: ['blur', 'change'] }],
  imageUrl: [
    { required: false, message: '请上传商品主图', trigger: ['blur', 'change'] },
    { required: false, validator: validateGoodsPic, trigger: ['blur', 'change'] },
  ],
  videoUrl: [
    { required: false, message: '请上传商品视频', trigger: 'blur' },
    { required: false, validator: validateGoodsPic, trigger: ['blur', 'change'] },
  ],
})

const categoryStore = useCategoryStore()
const category1 = computed(() => categoryStore.categoryLevel1 || [])

const handleSubmit = async () => {
  scrollToError.value = true
  trimParamsChangeOrigin(formData.value)
  /* 清除风险字段 */
  await formRef?.value?.validate()
  try {
    const submitFormData = {
      ...formData.value,
      deadline: new Date(formData.value.deadline).getTime(),
      imageUrl: formData.value?.imageUrl?.join(','),
      videoUrl: formData.value?.videoUrl?.join(','),
    }
    btnLoading.value = true
    await opportunityAdd(submitFormData)
    formRef.value?.resetFields()
    router.back()
  } catch (e) {
    console.log(e)
  } finally {
    btnLoading.value = false
  }
}

onMounted(() => {
  if (!category1.value.length) {
    categoryStore.getCategoryList()
  }
})
</script>

<style scoped lang="scss">
.flex-col-full {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
}

:deep() {
  .el-input,
  .el-select,
  .el-textarea,
  .el-cascader {
    max-width: 704px;
  }
}

:deep() {
  .el-input-number__decrease,
  .el-input-number__increase {
    background: #fff;
  }

  .el-input-number__decrease.is-disabled,
  .el-input-number__increase.is-disabled {
    background: #fff;
    opacity: 0.4;
  }
}

.submit-btn {
  min-width: 80px;
  height: 40px;
  border-radius: 2px;
}

.el-input-number {
  width: 220px;
}
</style>
