<template>
  <div class="my-collection bg-white p-4 h-full flex flex-col">
    <div class="header flex justify-between">
      <div class="search flex mb-4">
        <el-input v-model="searchValue" class="mr-2" size="large" :placeholder="t('searchPlaceholder')" clearable></el-input>
        <el-button type="primary" size="large" @click="searchClick">{{ t('search') }}</el-button>
        <el-button type="default" size="large" @click="resetClick">{{ t('reset') }}</el-button>
      </div>
    </div>
    <el-table border :data="tableData" show-overflow-tooltip v-loading="loading">
      <el-table-column prop="spuId" :label="t('goodsIdentifier')" min-width="110"></el-table-column>
      <el-table-column prop="spuName" :label="t('goodsName')" min-width="220">
        <template #default="scope">
          <div class="flex items-center overflow-hidden">
            <div class="relative w-[64px] h-[64px] overflow-hidden mr-[6px]">
              <img-loader
                :src="scope.row.spuImages?.split(',')?.[0]"
                v-if="scope.row.spuImages"
                img-class="w-[64px] h-[64px] object-cover  bg-[#f8f8f8]"
                alt=""
              ></img-loader>
              <div class="w-[30px] h-[12px] absolute top-0 left-0" style="line-height: 12px" v-if="scope.row.showType && scope.row.spuImages">
                <img :src="bannarImgNow" class="w-[100%] h-[100%] object-cover" />
              </div>
            </div>

            <div
              class="text-ellipsis overflow-hidden whitespace-nowrap cursor-pointer hover:underline hover:text-[#257BFB]"
              @click="handleClick(scope.row.spuId)"
            >
              {{ scope.row.spuName || '-' }}
            </div>
          </div>
        </template>
      </el-table-column>
      <!--      <el-table-column prop="categoryName" :label="t('categoryName')" width="220" />-->
      <el-table-column prop="brandName" :label="t('brand')" min-width="200"></el-table-column>
      <el-table-column fixed="right" :label="t('operate')" :min-width="$storageLocale === 'tr' ? '106' : ''">
        <template #default="scope">
          <el-popconfirm width="auto" :title="t('confirmText')" @confirm="handleColection(scope.row)">
            <template #reference>
              <el-button size="small" type="default">
                {{ t('cancelClick') }}
              </el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
      <template #empty>
        <EmptyText></EmptyText>
      </template>
    </el-table>
    <div class="pagination-wrapper" v-if="total > pageSize">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :background="true"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
        <span>前往</span>
      </el-pagination>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import EmptyText from '@/pc/components/empty-text/empty-text.vue'
import { BANNARIMG } from '@/constants/index'
import { getCollectList, goodsCollectCancel } from '@/apis/goods'
import { useStorageLocale } from '@/i18n/translatePlugin'
import { debounce } from '@/common/js/util'

const { storageLocale } = useStorageLocale()

const bannarImgNow = computed(() => {
  const img = BANNARIMG[storageLocale.value] || BANNARIMG.zh
  return img
})

const { t } = useI18n({
  messages: {
    zh: {
      searchPlaceholder: '请输入商品名称',
      search: '查询',
      reset: '重置',
      operate: '操作',
      cancelClick: '取消收藏',
      cancelSuccess: '取消收藏成功',
      goodsIdentifier: '商品编号',
      goodsName: '商品名称',
      categoryName: '商品类别',
      brand: '品牌',
      confirmText: '确认执行此操作吗？',
    },
    en: {
      searchPlaceholder: 'Please enter product name',
      search: 'Search',
      reset: 'Reset',
      operate: 'Operation',
      cancelClick: 'Cancel Collection',
      cancelSuccess: 'Collection cancelled successfully',
      goodsIdentifier: 'Product ID',
      goodsName: 'Product Name',
      categoryName: 'Product Category',
      brand: 'Brand',
      confirmText: 'Are you sure to perform this operation?',
    },
  },
})

const tableData = ref([])
const loading = ref(true)

const searchValue = ref(null)

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 获取列表 - 防抖包装
const DEBOUNCE_TIME = 300
const debounceGetData = debounce(() => {
  getList()
}, DEBOUNCE_TIME)

// 搜索
const searchClick = () => {
  currentPage.value = 1
  debounceGetData()
}

// 重置
const resetClick = () => {
  if (searchValue.value) {
    searchValue.value = null
    searchClick()
  }
}

// 取消收藏
const handleColection = async (row) => {
  try {
    await goodsCollectCancel({ spuId: row.spuId })
    ElMessage.success(t('cancelSuccess'))
    getList()
  } catch (error) {
    console.log(error)
  }
}

// 获取列表
const getList = async () => {
  loading.value = true
  try {
    const body = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      spuName: searchValue.value,
    }
    const { totalRecord, rowList } = await getCollectList(body)
    tableData.value = rowList
    total.value = totalRecord
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}
getList()

//分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getList()
}
const handleCurrentChange = (val) => {
  currentPage.value = val
  getList()
}

const router = useRouter()
const handleClick = (id) => {
  const url = router.resolve({
    name: 'goodsDetail',
    params: {
      id,
    },
  })
  window.open(url.href, '_blank')
}
</script>

<style lang="scss" scoped></style>
