<template>
  <div v-loading="loading" class="order-detail flex flex-col">
    <div class="back bg-white px-16px py-8px mb-8px flex items-center text-#D8131A text-14px cursor-pointer" @click="goBack">
      <el-icon class="ml-1"><ArrowLeft /></el-icon>返回
    </div>
    <div class="flex flex-col flex-1 bg-white p-4 h-full">
      <div class="text-24px font-500">订单详情</div>
      <div class="flex items-center text-14px text-#505259 my-10px">
        <span class="order-no mr-4">订单编号：{{ detailInfo?.id || '-' }}</span>
        <span>下单时间：{{ formatTime(detailInfo?.orderTime) || '-' }}</span>
      </div>
      <div v-if="detailInfo?.status !== -1" class="steps px-16px pt-4 pb-2 mb-16px">
        <el-steps
          class="flex flex-1"
          :class="{ 'is-ar': $storageLocale === 'ar' }"
          :active="getFirstDigit(detailInfo?.status)"
          align-center
          finish-status="success"
        >
          <el-step v-for="(item, i) in statusArray" :key="i" :title="item.stepTitle" />
        </el-steps>
      </div>
      <div class="flex p-4 bg-#F7F8FC rounded-8px justify-between">
        <div class="flex flex-1 flex-col">
          <span class="text-#1A1A1A text-16px font-500 mb-2">{{ BUYER_ORDER_STATUS_MAP[detailInfo?.status] }}</span>
          <span class="text-#FAAD14 text-14px">
            <template v-if="detailInfo?.status === 100"> 供应商需要确认信息，包括商品价格、运费等，确认订单后买家可以进行支付。 </template>
            <template v-if="detailInfo?.status === 200">
              <span class="mr-8">您需要支付：<c-symbol :priceType="priceType" />{{ detailInfo?.finalPrice }}</span
              >请在 {{ deadlinePayTime }} 内完成付款，并上传支付凭证，否则系统自动取消订单
            </template>
            <template v-if="detailInfo?.status === 300"> 已上传支付凭证，待平台审核，平台审核通过后才算完成支付 </template>
            <template v-if="detailInfo?.status === 350"> 拒绝原因：{{ currentPayInfo?.auditMessage || '-' }} </template>
            <template v-if="detailInfo?.status === 400"> 供应商正在备货中，尽快给您发货 </template>
            <template v-if="detailInfo?.status === 500"> 为保障您的售后权益，请收到商品检查无误后再确认收货 </template>
            <template v-if="detailInfo?.status === -1"> 买家取消订单 </template>
          </span>
        </div>
        <div class="flex">
          <el-button v-if="[200, 350].includes(detailInfo?.status)" type="primary" @click="uploadPayClick(detailInfo)">
            {{ detailInfo?.status === 200 ? '立即付款' : '重新上传支付凭证' }}
          </el-button>
          <el-button v-if="[100, 200].includes(detailInfo?.status)" type="primary" @click="confirmOrCancelOrderClick(detailInfo, 'cancel')">取消订单</el-button>
          <el-button v-if="detailInfo?.status === 100" type="default" @click="editDeliveryAddressClick(detailInfo)">修改收货地址</el-button>
          <el-button v-if="detailInfo?.status === 500" type="primary" @click="confirmOrCancelOrderClick(detailInfo, 'receiptGoods')">确认收货</el-button>
          <el-button v-if="detailInfo?.status !== -1" type="default" @click="contactCustomer">咨询客服</el-button>
        </div>
      </div>
      <div class="title">产品信息</div>
      <div class="goods-table flex text-14px rounded-2">
        <div class="content-wrap text-14px font-500 flex flex-1 flex-col">
          <div class="flex table-header bg-#F7F8FC text-#1A1A1A">
            <div :style="{ width: item.width + '%' }" v-for="(item, i) in goodsHeaderList" :key="i" class="cell">
              {{ item.title }}
            </div>
          </div>
          <div class="table-content text-#505259">
            <div class="flex w-full items-center item border-bottom" v-for="(item, i) in detailInfo?.orderGoodsList" :key="i">
              <div class="flex w-50% cell relative">
                <img-loader v-if="item?.goodsImage" :src="item?.goodsImage" img-class="w-64px h-64px object-cover" alt="" />
                <!-- 出口转内销 -->
                <div class="absolute left-4 top-4 z-10 w-[30px] h-[12px] img-logo" v-if="item?.goodsExt?.showType === 1">
                  <img-loader
                    :src="bannarImgNow"
                    class="w-[100%] h-[100%] object-cover cursor-pointer"
                    :loading-img="`${bannarImgNow}?x-oss-process=image/resize,h_100`"
                  ></img-loader>
                </div>
                <div class="flex flex-1 flex-col ml-2">
                  <div class="text-#1A1A1A goods-name">
                    {{ item?.goodsName || '-' }}
                  </div>
                  <div class="text-ellipsis-1 mr-2">
                    <span v-for="(k, v) in JSON.parse(item?.spec)" :key="v" class="mr-2">{{ k?.spec }}：{{ k?.item?.value }}</span>
                  </div>
                </div>
              </div>
              <div class="w-20% cell"><c-symbol :priceType="priceType" />{{ item.price }}</div>
              <div class="w-10% cell">{{ item?.count }}</div>
              <div class="w-20% cell"><c-symbol :priceType="priceType" />{{ item?.finalPrice }}</div>
            </div>
            <div class="remarks p-16px flex items-center">
              <span class="text-#888B94">备注</span><span class="ml-5 flex-1">{{ detailInfo?.remarks || '-' }}</span>
            </div>
            <div class="p-16px flex items-center justify-right">
              <span class="text-#333">产品数量：{{ goodsTotal }}</span>
              <span class="text-#D8131A ml-4 font-500">产品总价：<c-symbol :priceType="priceType" />{{ detailInfo?.orderPrice }} </span>
            </div>
          </div>
        </div>
      </div>
      <div class="title">发货详情</div>
      <div class="flex flex-col detail-border rounded-2">
        <div class="flex p-4">
          <div class="flex flex-1 flex-col pr-4 text-14px text-#505259 justify-center break-all">
            <div class="font-500 text-#1A1A1A">收货地址</div>
            <div class="my-8px">
              {{ deliveryAddress?.deliveryName }}
              <span class="mx-4" dir="ltr">
                {{ deliveryAddress?.deliveryPhoneCode ? '+' + deliveryAddress?.deliveryPhoneCode : '' }}
                {{ deliveryAddress?.deliveryPhone ? deliveryAddress?.deliveryPhone : '' }}
              </span>
            </div>
            <div>
              {{ address }}
            </div>
          </div>
          <div v-if="detailInfo?.status === 100" class="flex items-center justify-right pl-60px border-left">
            <el-button type="default" class="my-8px" @click="editDeliveryAddressClick">修改收货地址</el-button>
          </div>
          <div v-if="detailInfo?.status !== 100" class="flex flex-1 flex-col pl-32px border-left">
            <div class="font-500 text-#1A1A1A">运输方式</div>
            <div class="mt-2">平台指定</div>
          </div>
        </div>
        <div v-if="detailInfo?.status !== 100" class="flex flex-1 p-4 border-top">
          <div class="flex flex-col flex-1 pr-4">
            <div class="font-500 text-#1A1A1A">发货地</div>
            <div class="mt-2">中国 山东省 临沂市</div>
          </div>
          <div class="flex flex-1 flex-col pl-32px border-left">
            <div class="font-500 text-#1A1A1A">物流信息</div>
            <div class="flex flex-col mt-2">
              <span v-if="[200, 300, 350, 400, -1].includes(detailInfo?.status)">暂无发货信息</span>
              <template v-else>
                <div>
                  快递公司 <span class="ml-4">{{ orderLogisticInfo?.logisticsName || '-' }}</span>
                </div>
                <div>
                  物流单号 <span class="ml-4">{{ orderLogisticInfo?.logisticsNo || '-' }}</span>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
      <div class="title">付款详情</div>
      <div class="flex flex-col detail-border rounded-2">
        <div class="flex">
          <div class="flex flex-1 flex-col p-4">
            <div class="font-500 text-#1A1A1A">付款状态</div>
            <div class="mt-2">
              <template v-if="[100, 200].includes(detailInfo?.status)">{{ payDetailMap[detailInfo?.status] }}</template>
              <template v-if="[300, 350, 400, 500, 1000].includes(detailInfo?.status)">
                全额付款<span class="ml-4"><c-symbol :priceType="priceType" />{{ detailInfo?.finalPrice }}</span>
              </template>
              <template v-if="detailInfo?.status === -1">尚无付款记录</template>
            </div>
            <div class="mt-2 font-500 text-#1A1A1A">
              <span v-if="currentPayInfo?.payChannelName">{{ currentPayInfo?.payChannelName }}</span>
            </div>
            <div class="mt-2" v-if="currentPayInfo?.payTime">
              支付时间<span class="ml-4">{{ formatTime(currentPayInfo?.payTime) }}</span>
            </div>
          </div>
          <div v-if="![100, -1].includes(detailInfo?.status)" class="flex items-center justify-right pl-60px border-left my-4 mr-4">
            <el-button v-if="[200].includes(detailInfo?.status)" type="default" @click="uploadPayClick(detailInfo)"> 上传支付凭证 </el-button>
            <el-button v-if="[300, 350, 400, 500, 1000].includes(detailInfo?.status)" type="default" @click="showPayHistory(detailInfo)"> 付款记录 </el-button>
          </div>
        </div>
        <div class="flex flex-1 flex-col p-4 border-top">
          <div class="font-500 text-#1A1A1A">价格明细</div>
          <div class="mt-2">
            产品价格<span class="ml-4"><c-symbol :priceType="priceType" />{{ detailInfo?.orderPrice }}</span>
          </div>
          <div class="mt-2">
            {{ deliveryAddress?.addressType ? '运费' : '外贸综合服务费用' }}
            <span class="ml-4">
              <c-symbol v-if="detailInfo?.deliveryPrice !== undefined || detailInfo?.servicePrice !== undefined" :priceType="priceType" />
              <span v-if="deliveryAddress?.addressType">{{ detailInfo?.deliveryPrice === undefined ? '-' : detailInfo?.deliveryPrice }}</span>
              <span v-else>{{ detailInfo?.servicePrice === undefined ? '-' : detailInfo?.servicePrice }}</span>
            </span>
          </div>
          <div class="mt-2 text-16px">
            总计<span class="ml-4"><c-symbol :priceType="priceType" />{{ detailInfo?.finalPrice }}</span>
          </div>
        </div>
      </div>
      <div class="title">供应商详情</div>
      <div class="flex detail-border p-4 rounded-2">
        <div class="flex flex-1 flex-col pr-4">
          <div class="font-500 text-#1A1A1A">供应商</div>
          <div class="mt-2">{{ sellerInfo?.companyName }}</div>
        </div>
        <div class="flex flex-1 flex-col pl-8 border-left">
          <div class="font-500 text-#1A1A1A">联系方式</div>
          <div class="mt-2">{{ sellerInfo?.contactPhone }}</div>
        </div>
      </div>
    </div>
  </div>
  <OrderConfirmDialog ref="confirmDialogRef" @confirm-click="getDetails" />
  <PayHistoryDialog ref="payHistoryDialogRef" />
  <UploadPayDialog ref="uploadPayDialogRef" @confirm-click="getDetails" />
  <SelectDeliveryAddress ref="selectDeliveryAddressRef" @confirm-click="getDetails" />
</template>

<script setup>
import { ArrowLeft } from '@element-plus/icons-vue'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import OrderConfirmDialog from '../components/order-confirm-dialog.vue'
import PayHistoryDialog from '../components/pay-history-dialog.vue'
import SelectDeliveryAddress from '../components/select-delivery-address.vue'
import UploadPayDialog from '../components/upload-pay-dialog.vue'
import { BANNARIMG } from '@/constants/index'
import { ORDER_STATUS_ARRAY } from '@/constants/order'
import { BUYER_ORDER_STATUS_MAP } from '@/constants/order'
import { getBuyerOrderInfo } from '@/apis/order'
import { useStorageLocale } from '@/i18n'
import { CUSTOMER_SERVICE_LANGUAGE_MAP } from '@/i18n/contants'
import { formatTime } from '@/common/js/date'

const { storageLocale } = useStorageLocale()
const route = useRoute()
const id = route?.query?.id
const bannarImgNow = computed(() => {
  const img = BANNARIMG[storageLocale.value] || BANNARIMG.zh
  return img
})

const statusArray = computed(() => {
  return ORDER_STATUS_ARRAY.filter((item) => [100, 200, 300, 400, 500, 1000].includes(item.id))
})
// 根据国内外地址类型，转换地址
const address = computed(() => {
  const addr = deliveryAddress?.value
  if (!addr) return ''
  const { addressType, countryName, provinceName, cityName, areaName, address, postalCode } = addr
  return addressType
    ? `${countryName ? countryName : ''} ${provinceName ? provinceName : ''} ${cityName ? cityName : ''} ${areaName ? areaName : ''} ${address ? address : ''}`
    : `${address ? address : ''}${cityName ? ', ' + cityName : ''}${provinceName ? ', ' + provinceName : ''} ${postalCode ? postalCode : ''}${countryName ? ', ' + countryName : ''}`
})

const getFirstDigit = (status) => {
  if (status == null) return 0
  if (status === 1000) return 6
  return parseInt(String(status).charAt(0), 10)
}

// 产品信息表头配置
const goodsHeaderList = ref([
  { title: '产品名称', width: 50 },
  { title: '单价', width: 20 },
  { title: '数量', width: 10 },
  { title: '小计', width: 20 },
])

// 付款状态对应文案枚举
const payDetailMap = {
  100: '待供应商确认后方可付款',
  200: '待上传支付凭证',
}
const detailInfo = ref({})
const userInfo = ref({})
const sellerInfo = ref({})
const deliveryAddress = ref({})
const orderLogisticInfo = ref({})
const currentPayInfo = ref({})
const priceType = ref(null)
const goodsTotal = ref(0)
const deadlinePayTime = ref(null)
const loading = ref(false)
const getDetails = async () => {
  try {
    loading.value = true
    const res = await getBuyerOrderInfo({ orderId: id })
    detailInfo.value = { ...res }
    const { orderExt, userExt, sellerExt, orderGoodsList, orderDeliveryAddress, payList, orderLogisticsList, lastPayTime } = res
    priceType.value = orderExt?.priceType
    userExt && (userInfo.value = userExt)
    sellerExt && (sellerInfo.value = sellerExt)
    orderDeliveryAddress && (deliveryAddress.value = orderDeliveryAddress)
    orderLogisticsList?.length && (orderLogisticInfo.value = orderLogisticsList[orderLogisticsList?.length - 1])
    payList?.length && (currentPayInfo.value = payList[payList?.length - 1])
    deadlinePayTime.value = formatTime(lastPayTime, 'YYYY-MM-DD hh:00')
    goodsTotal.value = orderGoodsList.reduce((sum, item) => sum + (item.count || 0), 0)
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}
id && getDetails()

// 修改收货地址
const selectDeliveryAddressRef = ref(null)
const editDeliveryAddressClick = () => {
  selectDeliveryAddressRef.value.init(deliveryAddress.value, 'userDeliveryAddressId')
}

// 取消订单、确认收货
const confirmDialogRef = ref(null)
const confirmOrCancelOrderClick = (item, type) => {
  confirmDialogRef.value.init(item?.id, type)
}

// 立即付款、重新上传支付凭证
const uploadPayDialogRef = ref(null)
const uploadPayClick = (order) => {
  uploadPayDialogRef.value.init(order)
}

// 付款记录
const payHistoryDialogRef = ref(null)
const showPayHistory = (order) => {
  payHistoryDialogRef.value.init(order)
}

// 联系客服
const contactCustomer = () => {
  const lang = CUSTOMER_SERVICE_LANGUAGE_MAP[storageLocale.value]
  const url = `https://support.chinamarket.cn/index/index/home?visiter_id=&visiter_name=&avatar=&business_id=1&groupid=0&special=0&width=100&lang=${lang}`
  window.open(url, '_blank', 'height=800,width=950,top=50,left=200,status=yes,toolbar=no,menubar=no,resizable=yes,scrollbars=no,location=no,titlebar=no')
}
// 返回
const router = useRouter()
const goBack = () => {
  router.replace({ name: 'buyOrderManage' })
}
</script>

<style lang="scss" scoped>
[dir='rtl'] {
  .ml-1 {
    margin-right: 4px;
    margin-left: 0;
  }
  .ml-2 {
    margin-right: 8px;
    margin-left: 0;
  }
  .ml-4 {
    margin-right: 16px;
    margin-left: 0;
  }
  .ml-8 {
    margin-right: 32px;
    margin-left: 0;
  }
}
::v-deep() {
  .el-step {
    &__head,
    &__title {
      font-size: 14px !important;
      &.is-success {
        border-color: #1bc44d !important;
        color: #1bc44d !important;
        font-weight: 500;
      }
      &.is-process {
        border-color: #505259 !important;
        color: #505259 !important;
        font-weight: normal !important;
        .is-text {
          background: #505259;
          color: white;
        }
      }
      &.is-wait {
        border-color: #bbbec7 !important;
        color: #bbbec7 !important;
        font-weight: normal !important;
      }
    }
    &__line {
      background-color: #f0f0f0;
    }
  }
  .is-ar .el-step.is-center .el-step__line {
    right: 72% !important;
    left: -25% !important;
  }
}
.title {
  padding: 16px 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}
.goods-table {
  border: 1px solid #edeef1;
  .remarks {
    border-top: 1px solid #edeef1;
    border-bottom: 1px solid #edeef1;
  }
  .cell {
    padding: 16px;
    box-sizing: content-box;
    word-break: break-all;
  }
}
.detail-border {
  border: 1px solid #f0f0f0;
}
.border-left {
  border-left: 1px solid #f0f0f0;
}
.border-top {
  border-top: 1px solid #f0f0f0;
}
</style>
