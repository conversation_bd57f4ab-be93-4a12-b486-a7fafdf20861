<template>
  <div class="my-collection bg-white p-4 h-full flex flex-col">
    <div class="header flex justify-between">
      <div class="flex items-center pt-1">
        <div v-for="(item, i) in statusOptions" :key="i" class="tab-item" :class="{ active: tabActive === item.id }" @click="handleTabItemChange(item.id)">
          {{ item.label }}
        </div>
      </div>
      <el-button type="primary" size="large" @click="handleAddClick" class="add-btn">发布找货需求</el-button>
    </div>
    <div class="pt-4" v-loading="loading">
      <div class="card-item flex items-center mb-4" v-for="item in tableData" :key="item.id" @click="handleItemClick(item.id)">
        <div class="status-wrap">
          <div class="status-inner" :style="`background: ${formatStatsColor(item.status)}`">{{ OpportunityStatusMap[item.status] }}</div>
        </div>
        <img-loader :src="formatImg(item.imageUrl)" img-class="w-[200px] h-[150px] object-contain shrink-0 mr-5" alt="" error-img="/mall/errorImg.png" />
        <div class="flex flex-col flex-1 overflow-hidden justify-between h-[150px]">
          <div class="flex flex-col overflow-hidden">
            <div class="flex items-center mb-4 overflow-hidden shrink-0">
              <div class="text-ellipsis overflow-hidden whitespace-nowrap text-[16px]">{{ item.title }}</div>
              <div class="category-tag ml-2" v-if="item.categoryName">{{ item.categoryName }}</div>
            </div>
            <div>
              <van-text-ellipsis :content="item.description" :rows="3"></van-text-ellipsis>
            </div>
          </div>
          <div class="text-[#D8131A]">截止时间：{{ formatTime(item.deadline, 'YYYY-MM-DD hh:mm:ss') }}</div>
        </div>
        <!-- 失效原因 -->
        <template v-if="item.status === 3">
          <div v-if="item?.failReason" class="flex flex-col flex-1 overflow-hidden justify-end h-[150px] ml-10px text-[#D8131A]">
            失效原因：{{ item.failReason }}
          </div>
        </template>
      </div>
    </div>
    <el-empty v-if="!loading && total === 0" />
    <div class="pagination-wrapper" v-if="total > pageSize">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :background="true"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
        <span>前往</span>
      </el-pagination>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { OpportunityStatusArray, OpportunityStatusMap } from '@/constants/goods'
import { getOpportunityList } from '@/apis/goods'
import { formatTime } from '@/common/js/date'
import { debounce } from '@/common/js/util'

const tableData = ref([])
const loading = ref(true)

const statusOptions = [
  {
    id: null,
    label: '全部',
  },
  ...OpportunityStatusArray,
]
// 分页
const tabActive = ref(null)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const formatImg = (imageUrl) => {
  return imageUrl?.split(',')?.[0] || ''
}
const formatStatsColor = (status) => {
  let color = '#4571FB'
  switch (status) {
    case 1:
      color = '#FFB55B'
      break
    case 2:
      color = '#6FC445'
      break
    case 3:
      color = '#cccccc'
      break
    case 4:
      color = '#D8131A'
      break
    default:
      break
  }
  return color
}

// 获取列表 - 防抖包装
const DEBOUNCE_TIME = 300
const debounceGetData = debounce(() => {
  getList()
}, DEBOUNCE_TIME)

// 搜索
const searchClick = () => {
  currentPage.value = 1
  debounceGetData()
}

const handleTabItemChange = (id) => {
  tabActive.value = id
  searchClick()
}

// 获取列表
const getList = async () => {
  loading.value = true
  try {
    const body = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      status: tabActive.value,
    }
    const { totalRecord, rowList } = await getOpportunityList(body)
    tableData.value = rowList
    total.value = totalRecord
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}
getList()

//分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getList()
}
const handleCurrentChange = (val) => {
  currentPage.value = val
  getList()
}

const router = useRouter()
const route = useRoute()
const handleAddClick = () => {
  router.push({
    name: route.name === 'buyerServiceOpportunity' ? 'buyerServiceOpportunityAdd' : 'myOpportunityAdd',
  })
}

const handleItemClick = (id) => {
  router.push({
    name: route.name === 'buyerServiceOpportunity' ? 'buyerServiceOpportunityDetail' : 'myOpportunityDetail',
    query: {
      id,
    },
  })
}
</script>

<style lang="scss" scoped>
.header {
  border-bottom: 1px solid #d9d9d9;
}

.tab-item {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  min-width: 100px;
  padding: 0 16px;
  height: 40px;
  box-sizing: border-box;
  cursor: pointer;

  &::after {
    position: absolute;
    bottom: -1px;
    left: 0;
    display: none;
    content: '';
    width: 100%;
    border-top: 2px solid $primary-color;
  }

  &.active {
    color: $primary-color;
    font-weight: 600;

    &::after {
      display: block;
    }
  }
}

.add-btn {
  min-width: 120px;
  border-radius: 2px;
  height: 40px;
}

.card-item {
  position: relative;
  padding: 30px 20px;
  border: 1px solid #dcdfe5;
  color: #3d3d3d;
  border-radius: 4px;
  cursor: pointer;
  overflow: hidden;

  .status-wrap {
    position: absolute;
    // top: 10px;
    // right: 10px;
    // width: 64px;
    // height: 64px;
    top: 0;
    right: 0;
    .status-inner {
      min-width: 60px;
      padding: 0 10px;
      height: 30px;
      background: blue;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      border-radius: 0px 4px 0px 4px;
    }
  }

  .category-tag {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 1px 8px;
    background: rgba(59, 109, 207, 0.1);
    border: thin solid #3b6dcf;
    color: #3b6dcf;
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 70px;
  }
}
</style>
