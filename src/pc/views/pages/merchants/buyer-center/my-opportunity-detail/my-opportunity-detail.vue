<template>
  <div class="my-collection bg-white p-4 h-full">
    <BorderTitle class="font-600" title="需求信息" />
    <div class="card-item mb-4">
      <div class="status-wrap">
        <div class="status-inner" :style="`background: ${formatStatsColor(detailInfo.status)}`">{{ OpportunityStatusMap[detailInfo.status] }}</div>
      </div>
      <div class="flex detailInfos-center mb-5 overflow-hidden shrink-0">
        <div class="text-ellipsis overflow-hidden whitespace-nowrap text-[16px]">{{ detailInfo.title }}</div>
        <div class="category-tag ml-2" v-if="detailInfo.categoryName">{{ detailInfo.categoryName }}</div>
      </div>
      <div class="flex mb-4">
        <div class="item-label">找货企业:</div>
        {{ detailInfo.companyName }}
      </div>
      <div class="flex description-wrap mb-4">
        <div class="item-label">商品描述:</div>
        {{ detailInfo.description }}
      </div>
      <div class="flex mb-4">
        <div class="item-label">求购数量:</div>
        <span class="text-[#D8131A]">{{ detailInfo.needCount }}</span>
      </div>
      <div class="gray-wrap mb-4">
        <div class="gray-wrap-item flex my-1 py-2" v-for="item in options" :key="item.value">
          <div class="item-label">{{ item.label }}:</div>
          <div>
            <div class="text-[#D8131A]" v-if="item.value === 'deadline'">{{ formatTime(detailInfo.deadline, 'YYYY-MM-DD hh:mm:ss') }}</div>
            <div v-else-if="item.value === 'deliveryType'">{{ DeliveryTypeMap[detailInfo.deliveryType] }}</div>
            <div v-else-if="item.value === 'customized'">{{ CustomizedMap[detailInfo.customized] }}</div>
            <div v-else-if="item.value === 'unitPrice'" class="text-[#D8131A]">¥{{ detailInfo.unitPrice }}</div>
            <div v-else>{{ detailInfo[item.value] }}</div>
          </div>
        </div>
      </div>
      <!--      <div class="text-[#D8131A]">{{ formatTime(detailInfo.deadline, 'YYYY-MM-DD hh:mm:ss') }}</div>-->
      <div class="pt-4">
        <el-image
          :preview-src-list="imgList"
          v-for="(item, i) in imgList"
          :key="i"
          :src="item || ''"
          class="w-[150px] h-[150px] shrink-0 mr-4 mb-4 bg-[#f5f5f5]"
          fit="contain"
          alt=""
          error-img="/mall/errorImg.png"
          :initial-index="i"
        />
      </div>
      <div class="pt-4 flex items-center flex-wrap">
        <div class="w-[150px] h-[150px] shrink-0 mr-4 mb-4 bg-[#f5f5f5] relative" v-for="(item, i) in videoList" :key="i">
          <div @click.stop="previewVideo(item)" class="absolute z-1 w-full h-full top-0 left-0 flex items-center justify-center">
            <div class="w-10 h-10 rounded-[50%] bg-[rgba(0,0,0,.5)] flex items-center justify-center">
              <el-icon color="#fff" size="30"><CaretRight /></el-icon>
            </div>
          </div>
          <video :src="item || ''" ref="videoRef" class="object-cover w-full h-full" disablePictureInPicture />
        </div>
      </div>
    </div>

    <!-- 视频预览 -->
    <PreviewVideoDialog ref="previewVideoDialogRef" />
  </div>
</template>

<script setup>
import { CaretRight } from '@element-plus/icons-vue'
import PreviewVideoDialog from '@/pc/components/preview-video-dialog/preview-video-dialog.vue'
import { CustomizedMap, DeliveryTypeMap, OpportunityStatusMap } from '@/constants/goods'
import { getOpportunityDetail } from '@/apis/goods'
import { formatTime } from '@/common/js/date'

const detailInfo = ref({})
const imgList = computed(() => {
  return detailInfo.value?.imageUrl?.split(',') || []
})
const videoList = computed(() => {
  return detailInfo.value?.videoUrl?.split(',') || []
})
const options = [
  { label: '截止时间', value: 'deadline' },
  { label: '交货周期', value: 'deliveryType' },
  { label: '预估单价', value: 'unitPrice' },
  { label: '是否定制', value: 'customized' },
  { label: '交货地址', value: 'deliveryPlace' },
  { label: '联系电话', value: 'phone' },
]
const formatStatsColor = (status) => {
  let color = '#4571FB'
  switch (status) {
    case 1:
      color = '#FFB55B'
      break
    case 2:
      color = '#6FC445'
      break
    case 3:
      color = '#cccccc'
      break
    case 4:
      color = '#D8131A'
      break
    default:
      break
  }
  return color
}

const loading = ref(false)
const route = useRoute()
const getDetailInfo = async () => {
  try {
    loading.value = true
    detailInfo.value = await getOpportunityDetail({ id: route.query.id })
  } catch (e) {
    console.log(e)
  } finally {
    loading.value = false
  }
}
getDetailInfo()

const previewVideoDialogRef = ref(null)
// 打开视频预览
const previewVideo = (url) => {
  previewVideoDialogRef.value.init({
    url,
  })
}
</script>

<style scoped lang="scss">
.card-item {
  position: relative;
  padding: 32px 20px;
  border: 1px solid #dcdfe5;
  color: #3d3d3d;
  cursor: pointer;
  overflow: hidden;

  .status-wrap {
    position: absolute;
    // top: 10px;
    // right: 10px;
    // width: 64px;
    // height: 64px;
    top: 0;
    right: 0;

    .status-inner {
      width: 100px;
      min-width: 60px;
      height: 30px;
      background: blue;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      // transform: rotateZ(45deg);
    }
  }

  .category-tag {
    min-width: 70px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 1px 8px;
    background: rgba(59, 109, 207, 0.1);
    border: thin solid #3b6dcf;
    color: #3b6dcf;
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.description-wrap {
  border-top: 1px dashed #d9d9d9;
  padding-top: 16px;
}

.gray-wrap {
  padding: 4px 16px;
  background: #f9f8f8;
  display: flex;
  flex-wrap: wrap;

  .gray-wrap-item {
    width: 33.33%;
  }
}

.item-label {
  white-space: nowrap;
  flex-shrink: 0;
  margin-right: 8px;
}

[dir='rtl'] {
  .card-item {
    .status-wrap {
      left: 0;
      right: auto;

      .status-inner {
        transform: rotateZ(-45deg);
      }
    }
  }

  .item-label {
    margin-left: 8px;
    margin-right: 0;
  }

  .ml-2 {
    margin-right: 8px;
  }
}
</style>
