<template>
  <el-dialog class="custom-dialog text-#333" v-model="isShowDialog" width="980" :show-close="false" style="padding: 24px">
    <template #header>
      <div class="header flex justify-between text-18px font-600 mb-16px">
        <span><Icon class="text-#FAAD14 mr-2" type="icon-jinggao-shi-tishi1" :size="20" />付款记录</span>
        <icon class="cursor-pointer" type="icon-guanbi" :size="16" @click="isShowDialog = false" />
      </div>
    </template>
    <template #default>
      <div class="content-wrap text-14px font-500 flex">
        <div class="table-content flex flex-1 flex-col">
          <div class="flex table-header">
            <div :style="{ width: item.width + '%' }" v-for="(item, i) in payHeaderList" :key="i" class="cell">
              {{ item.title }}
            </div>
          </div>
          <div class="pay-content">
            <div class="flex w-full items-center item border-bottom" v-for="(item, i) in payList" :key="i">
              <div class="w-5% cell">{{ i + 1 }}</div>
              <div class="w-22% cell">{{ item.id }}</div>
              <div class="w-6% cell"><c-symbol showType="enCurrency" :priceType="item?.paySourcePriceType" /></div>
              <div class="w-10% cell"><c-symbol :priceType="item?.paySourcePriceType" />{{ item?.paySourcePrice }}</div>
              <div class="w-10% cell">{{ item?.payChannelName }}</div>
              <div class="w-15% cell" :style="{ color: REVIEW_TYPE_MAP_COLOR[item?.auditStatus] }">
                {{ REVIEW_TYPE_MAP[item?.auditStatus] }}
              </div>
              <div class="w-32% cell">
                <div v-if="item?.payImages">
                  <template v-for="(v, i) in item?.payImages.split(',')" :key="i">
                    <el-image
                      v-if="v"
                      class="w-64px h-64px object-cover mr-8px"
                      :src="v"
                      :initial-index="i"
                      :preview-src-list="item?.payImages.split(',')"
                      show-progress
                      fit="cover"
                    />
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { REVIEW_TYPE_MAP, REVIEW_TYPE_MAP_COLOR } from '@/constants/order'

const payHeaderList = ref([
  { title: '序号', width: 5 },
  { title: '交易号', width: 20 },
  { title: '支付币种', width: 8 },
  { title: '支付金额', width: 10 },
  { title: '支付方式', width: 10 },
  { title: '审批状态', width: 15 },
  { title: '支付凭证', width: 32 },
])

const isShowDialog = ref(false)
const payList = ref([])
const init = (row) => {
  isShowDialog.value = true
  payList.value = row?.payList
}
defineExpose({ init })
</script>

<style lang="scss" scoped>
.table-content {
  border: 1px solid #edeef1;
}
.table-header {
  border-bottom: 1px solid #edeef1;
  div:not(:last-child) {
    border-right: 1px solid #edeef1;
  }
}
.border-bottom {
  border-bottom: 1px solid #edeef1;
}
.cell {
  padding: 12px 16px;
  box-sizing: content-box;
  word-break: break-all;
}
</style>
