<template>
  <el-dialog class="custom-dialog text-#333" v-model="isShowDialog" width="545" :show-close="false" style="padding: 24px">
    <template #header>
      <div class="header flex justify-between text-18px font-600 mb-16px">
        <span><Icon class="text-#FAAD14 mr-2" type="icon-jinggao-shi-tishi1" :size="20" />{{ dialogConfig[type]?.title }}</span>
        <icon class="cursor-pointer" type="icon-guanbi" :size="16" @click="isShowDialog = false" />
      </div>
    </template>
    <template #default>
      <div class="content-wrap text-14px font-500 flex flex-col">
        <div class="table-content px-16px py-12px">
          {{ dialogConfig[type]?.content }}
        </div>
      </div>
    </template>
    <template #footer>
      <div class="footer flex-row-reverse">
        <el-button type="default" @click="cancelClick">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="confirmClick">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { deleteUserDeliveryAddress, setDefaultDeliveryAddress } from '@/apis/merchants'

const type = ref('delete')
const isShowDialog = ref(false)
const deliveryId = ref(null)
const userId = ref(null)
const dialogConfig = {
  delete: { title: '删除地址', content: '你确认删除该条地址吗？' },
  setDefault: { title: '设置默认地址', content: '确认设置该条为默认地址吗？' },
}
const init = (row, t) => {
  type.value = t
  isShowDialog.value = true
  deliveryId.value = row?.id || null
  userId.value = row?.userId || null
}

const cancelClick = () => {
  isShowDialog.value = false
}

const emits = defineEmits(['confirmClick'])
const submitLoading = ref(false)
const confirmClick = async () => {
  if (submitLoading.value) return
  submitLoading.value = true
  try {
    const isDelete = type.value === 'delete'
    const body = isDelete ? { id: deliveryId.value } : { id: deliveryId.value, userId: userId.value }
    const msg = isDelete ? '删除成功' : '设置成功'
    const func = isDelete ? deleteUserDeliveryAddress : setDefaultDeliveryAddress
    await func(body)
    ElMessage.success(msg)
    emits('confirmClick')
  } catch (error) {
    console.log(error)
  } finally {
    setTimeout(() => {
      submitLoading.value = false
    }, 200)
    isShowDialog.value = false
  }
}
defineExpose({ init })
</script>

<style lang="scss" scoped></style>
