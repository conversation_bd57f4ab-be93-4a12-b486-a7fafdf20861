<template>
  <el-dialog class="custom-dialog text-#333" v-model="isShowDialog" width="840" :show-close="false" style="padding: 24px">
    <template #header>
      <div class="header flex justify-between text-18px font-600 mb-16px">
        <span>选择收货地址</span>
        <icon class="cursor-pointer" type="icon-guanbi" :size="16" @click="isShowDialog = false" />
      </div>
    </template>
    <template #default>
      <div class="content-wrap text-14px flex flex-col">
        <div
          class="address flex justify-between items-center px-16px py-10px mb-4"
          :class="{ active: currentSelectId === address.id }"
          v-for="(address, index) in addresses"
          :key="index"
          @click="selectDeliveryClick(address)"
        >
          <div class="flex flex-col flex-1">
            <div v-if="address?.status === 0" class="info text-12px text-#D8131A"><Icon type="icon-jinggaoAlert1" :size="12" /> 请完善收货地址</div>
            <div class="text-14px font-600 mb-1 text-#1A1A1A">
              {{ address?.deliveryName }}
              <span class="mx-4" dir="ltr">
                {{ address?.deliveryPhoneCode ? '+' + address?.deliveryPhoneCode : '' }}
                {{ address?.deliveryPhone ? address?.deliveryPhone : '' }}
              </span>
              <span v-if="address?.isDefault" class="default-label h-17px rounded text-#fa3e3e text-12px px-8px">默认</span>
              <span v-else class="ml-2 text-12px underline text-#505259 cursor-pointer" @click.stop="setDefaultAddress(address)">设为默认</span>
            </div>
            <div class="address-detail text-12px text-#505259 break-all">
              {{ changeAddress(address) }}
            </div>
          </div>
          <div class="flex items-center text-#000000">
            <span class="p-2 ml-2 cursor-pointer" @click.stop="handleAddEditAddress(address)">
              <Icon type="icon-bianji1" :size="18" />
            </span>
            <span @click.stop="handleDeleteAddress" class="p-2 ml-2 cursor-pointer">
              <el-popconfirm width="auto" title="确认要删除该地址吗？" @confirm="confirmDelete(address)">
                <template #reference>
                  <Icon type="icon-new-shanchu" :size="18" />
                </template>
                <template #actions="{ confirm, cancel }">
                  <div class="flex justify-center mt-6px py-4px">
                    <el-button size="small" @click="cancel">取消</el-button>
                    <el-button type="primary" size="small" @click="confirm"> 确认 </el-button>
                  </div>
                </template>
              </el-popconfirm>
            </span>
          </div>
        </div>
        <EmptyText v-if="addresses.length === 0" />
      </div>
      <div class="pagination-wrapper" v-if="total > pageSize">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :background="true"
          :pager-count="5"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </template>
    <template #footer>
      <div class="footer flex items-center justify-between">
        <span class="cursor-pointer" @click="handleAddEditAddress()">+ 新建收货地址</span>
        <div class="flex-1 flex-row-reverse">
          <el-button type="default" @click="cancelClick">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="confirmClick">确认</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
  <DeliveryAddress ref="deliveryAddressRef" @confirm-click="debounceGetData" />
  <DeliveryConfirmDialog ref="deliveryConfirmDialogRef" @confirm-click="debounceGetData" />
</template>

<script setup>
import { ElPopconfirm } from 'element-plus'
import EmptyText from '@/pc/components/empty-text/empty-text.vue'
import DeliveryAddress from './delivery-address.vue'
import DeliveryConfirmDialog from './delivery-confirm-dialog.vue'
import { useUserStore } from '@/pc/stores'
import { deleteUserDeliveryAddress } from '@/apis/merchants'
import { getUserDeliveryAddress } from '@/apis/merchants.js'
import { updateOrderDeliveryAddress } from '@/apis/order'
import { debounce } from '@/common/js/util'

const props = defineProps({
  // 是否只返回选中的地址信息（下单时）
  isWaitSubmitOrder: {
    type: Boolean,
    default: false,
  },
})

const userStore = useUserStore()
const isShowDialog = ref(false)
const currentSelectId = ref(null)
const selectAddress = ref({}) // 当前选中的地址对象
const orderDeliveryAddressId = ref(null)

let initId = null
const init = (orderDeliveryAddress, type) => {
  currentPage.value = 1
  getList()
  isShowDialog.value = true
  currentSelectId.value = null
  selectAddress.value = Object.assign(selectAddress, orderDeliveryAddress || {})
  initId = orderDeliveryAddress[type] || null
  orderDeliveryAddressId.value = orderDeliveryAddress?.id //订单的id
  currentSelectId.value = initId // 当前选中的地址的id
}

const addresses = ref([])

// 根据国内外地址类型，转换地址
const changeAddress = (deliveryAddress) => {
  if (!deliveryAddress) return ''
  const { addressType, countryName, provinceName, cityName, areaName, address, postalCode } = deliveryAddress
  return addressType
    ? `${countryName ? countryName : ''} ${provinceName ? provinceName : ''} ${cityName ? cityName : ''} ${areaName ? areaName : ''} ${address ? address : ''}`
    : `${address ? address : ''}${cityName ? ', ' + cityName : ''}${provinceName ? ', ' + provinceName : ''} ${postalCode ? postalCode : ''}${countryName ? ', ' + countryName : ''}`
}

const emits = defineEmits(['confirmClick'])
const selectDeliveryClick = (address) => {
  if (address?.status === 0) {
    ElMessage.warning('该地址不可用，请先完善收货地址！')
    return
  }
  currentSelectId.value = address.id
  selectAddress.value = address
}

const deliveryAddressRef = ref(null)
// 新增、修改地址
const handleAddEditAddress = (row) => {
  deliveryAddressRef.value.init(row?.id)
}

// 删除地址
const handleDeleteAddress = async () => {}

const confirmDelete = async (row) => {
  try {
    if (initId === row?.id) {
      ElMessage.warning('使用中的地址，无法删除')
      return
    }
    await deleteUserDeliveryAddress({ id: row?.id })
    ElMessage({ message: '删除成功', type: 'success' })
    await getList()
  } catch (error) {
    console.log(error)
  }
}

const deliveryConfirmDialogRef = ref(null)
const setDefaultAddress = (row) => {
  deliveryConfirmDialogRef.value.init(row, 'setDefault')
}

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

const { id } = userStore.userInfo || {}
const loading = ref(false)
// 获取列表
const getList = async () => {
  loading.value = true
  try {
    const body = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      userId: id,
    }
    const { totalRecord, rowList } = await getUserDeliveryAddress(body)
    addresses.value = rowList
    // 如果选中id与列表中id匹配，则使用列表的数据覆盖选中的地址
    addresses.value.forEach((el) => {
      if (el.id === selectAddress.value.id) {
        selectAddress.value = el
      }
    })
    console.log(selectAddress.value)
    total.value = totalRecord
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}
onMounted(() => {
  getList()
})
// 获取列表 - 防抖包装
const DEBOUNCE_TIME = 300
const debounceGetData = debounce(() => {
  getList()
}, DEBOUNCE_TIME)

//分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getList()
}
const handleCurrentChange = (val) => {
  currentPage.value = val
  getList()
}

const cancelClick = () => {
  isShowDialog.value = false
}

const submitLoading = ref(false)
const confirmClick = async () => {
  if (selectAddress.value?.status === 0) {
    ElMessage.warning('该地址不可用，请先完善收货地址！')
    return
  }
  if (props.isWaitSubmitOrder) {
    isShowDialog.value = false
    // 如果地址没有被删除，则直接返回地址信息
    if (addresses.value.length > 0) {
      emits('confirmClick', selectAddress.value)
    } else {
      ElMessage.warning('请选择地址')
    }
  } else {
    if (submitLoading.value) return
    submitLoading.value = true
    try {
      await updateOrderDeliveryAddress({ id: orderDeliveryAddressId.value, addressId: currentSelectId.value })
      ElMessage.success('修改成功')
      emits('confirmClick')
    } catch (error) {
      console.log(error)
    } finally {
      setTimeout(() => {
        submitLoading.value = false
      }, 200)
      isShowDialog.value = false
    }
  }
}
defineExpose({ init })
</script>

<style lang="scss" scoped>
.content-wrap {
  .address {
    border-radius: 8px;
    border: 1px solid #f0f0f0;
    &.active {
      border-color: #d8131a;
    }
    .default-label {
      background: rgba(250, 62, 62, 0.1);
      border-radius: 17px;
    }
  }
}
</style>
