<template>
  <el-dialog class="custom-dialog text-#333" v-model="isShowDialog" width="640" :show-close="false" style="padding: 24px">
    <template #header>
      <div class="header flex justify-between text-18px font-600">
        <span>{{ payChannelName }}</span>
        <Icon class="cursor-pointer" type="icon-guanbi" :size="16" @click="isShowDialog = false" />
      </div>
    </template>
    <template #default>
      <div v-if="orderStatus === 200" class="flex items-center mb-4">
        请于 <span class="text-#FA3E3E">{{ deadlinePayTime }}</span> 内完成付款，并上传支付凭证。
      </div>
      <div class="text-14px font-500 text-#333 mb-4">收款账号信息</div>
      <el-form
        v-loading="loading"
        :model="form"
        label-width="96"
        :rules="rules"
        ref="formRef"
        label-position="top"
        class="scrollbar"
        style="max-height: 600px; overflow-y: auto"
      >
        <el-form-item :label="item?.label" :prop="item?.key" v-for="(item, index) in formItem" :key="index">
          <div v-if="item?.key === 'moreInfo'" class="more-info flex flex-col justify-center text-14px text-#666">
            <div class="text-14px font-500 text-#333">{{ item?.moreInfoLabel }}</div>
            <div>{{ item?.moreInfoOne }}</div>
            <div>{{ item?.moreInfoTwo }}</div>
          </div>
          <div v-else-if="item?.key === 'payImages'" class="flex flex-col justify-center goods-picture">
            <ImgUploads v-model="form.payImages" :dir="OSS_DIR.PAY_IMAGE" :width="88" :height="88" :size-limit="10" :img-number="3">
              <template #empty>
                <div>
                  <icon type="icon-xinzeng" :size="20"></icon>
                </div>
              </template>
            </ImgUploads>
            <div class="mt-1 text-#BBBEC7">上传支付凭证，比如转账凭条、电子回单、汇票等平台会进行审核</div>
          </div>
          <div v-else-if="item?.key === 'notice'" class="more-info flex flex-col justify-center text-14px text-#666">
            <div class="text-14px font-500 text-#333">{{ item?.noticeLabel }}</div>
            <div>{{ item?.noticeOne }}</div>
            <div>{{ item?.noticeTwo }}</div>
          </div>
          <el-input class="skip-translate" v-else v-model="form[item?.key]" disabled placeholder="" clearable></el-input>
        </el-form-item>
      </el-form>
    </template>
    <template #footer>
      <div class="footer flex-row-reverse">
        <el-button type="default" @click="cancelClick">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="confirmClick">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { OSS_DIR } from '@/constants/oss-dir'
import { getPayChannel, payOrder } from '@/apis/order'
import { formatTime } from '@/common/js/date'

const form = reactive({
  bankAccountName: '',
  bankAccountNo: '',
  bankBranchName: '',
  bankBranchNo: '',
  payImages: [],
})

// 收款账号对应的form配置枚举
const formItemEnum = {
  1: [
    {
      key: 'bankAccountName',
      label: '银行户名',
    },
    {
      key: 'bankAccountNo',
      label: '银行账号',
    },
    {
      key: 'bankBranchName',
      label: '收款银行',
    },
    {
      key: 'bankBranchNo',
      label: '行号',
    },
    {
      key: 'payImages',
      label: '支付凭证',
    },
    {
      key: 'notice',
      noticeLabel: '注意事项：',
      noticeOne: '1、本页面只展示收款账户信息，您需要向收款账户进行转账付款。',
      noticeTwo: '2、转账金额与订单金额请务必保持一致。',
    },
  ],
  2: [
    {
      key: 'accountName',
      label: 'Account name 账户名',
    },
    {
      key: 'accountNo',
      label: 'Account No. 账户号',
    },
    {
      key: 'bank',
      label: 'Bank 开户行',
    },
    {
      key: 'country',
      label: 'Country/Region 国家/地区',
    },
    {
      key: 'bankAddress',
      label: 'Bank address 银行地址',
    },
    {
      key: 'swiftCode',
      label: 'SWIFT CODE',
    },
    {
      key: 'bankCode',
      label: 'BANK CODE',
    },
    {
      key: 'branchCode',
      label: 'BRANCH CODE',
    },
    {
      key: 'moreInfo',
      moreInfoLabel: '更多信息',
      moreInfoOne: '1、支持的收款币种：USD、EUR、JPY、GBP、CAD、AUD、SGD、HKD、CNH。',
      moreInfoTwo: '2、跨境收款，需 0-3个工作日，全球SWIFT网络收款。',
    },
    {
      key: 'payImages',
      label: '支付凭证',
    },
    {
      key: 'notice',
      noticeLabel: '注意事项：',
      noticeOne: '1、本页面只展示收款账户信息，您需要通过当地银行向收款账户汇款。',
      noticeTwo: '2、汇款金额与订单金额请务必保持一致。',
    },
  ],
  3: [
    {
      key: 'accountName',
      label: 'Account name 账户名',
    },
    {
      key: 'accountNo',
      label: 'Account No. 账户号',
    },
    {
      key: 'bank',
      label: 'Bank 开户行',
    },
    {
      key: 'country',
      label: 'Country/Region 国家/地区',
    },
    {
      key: 'bankAddress',
      label: 'Bank address 银行地址',
    },
    {
      key: 'swiftCode',
      label: 'SWIFT CODE',
    },
    {
      key: 'moreInfo',
      moreInfoLabel: '更多信息',
      moreInfoOne: '1、支持的收款币种：仅支持收取AED。',
      moreInfoTwo: '2、阿联酋本地收款，需 0-1个工作日。',
    },
    {
      key: 'payImages',
      label: '支付凭证',
    },
    {
      key: 'notice',
      noticeLabel: '注意事项：',
      noticeOne: '1、本页面只展示收款账户信息，您需要向收款账户进行转账付款。',
      noticeTwo: '2、转账金额与订单金额请务必保持一致。',
    },
  ],
  4: [
    {
      key: 'accountName',
      label: 'Account name',
    },
    {
      key: 'accountNo',
      label: 'Account No.(IBAN)',
    },
    {
      key: 'bank',
      label: 'Bank',
    },
    {
      key: 'country',
      label: 'Country/Region',
    },
    {
      key: 'bankAddress',
      label: 'Bank address',
    },
    {
      key: 'swiftCode',
      label: 'SWIFT CODE',
    },
    {
      key: 'bankCode',
      label: 'BANK CODE',
    },
    {
      key: 'moreInfo',
      moreInfoLabel: '更多信息',
      moreInfoOne: '1、支持的收款币种：IDR。',
      moreInfoTwo: '2、本地收款，需 0-1个工作日。',
    },
    {
      key: 'payImages',
      label: '支付凭证',
    },
    {
      key: 'notice',
      noticeLabel: '注意事项：',
      noticeOne: '1、本页面只展示收款账户信息，您需要向收款账户进行转账付款。',
      noticeTwo: '2、转账金额与订单金额请务必保持一致。',
    },
  ],
}

const formItem = computed(() => {
  return formItemEnum[priceType.value]
})

const rules = {
  payImages: [{ required: true, message: '请上传支付凭证', trigger: 'change' }],
}

const orderStatus = ref(null)
const orderId = ref(null)
const priceType = ref(null)
const isShowDialog = ref(false)
const deadlinePayTime = ref(null)
const init = async (order) => {
  Object.keys(form).forEach((key) => (form[key] = ''))
  form.payImages = []
  isShowDialog.value = true
  deadlinePayTime.value = formatTime(order?.lastPayTime, 'YYYY-MM-DD hh:00')
  orderStatus.value = order?.status
  orderId.value = order?.id
  priceType.value = order?.orderExt?.priceType || 1
  order?.orderExt?.priceType && (await getPayChannelInfo(order?.orderExt?.priceType))
  if (order?.payList?.length) {
    const { payImages = '' } = order?.payList?.at(-1) || {}
    const arr = payImages?.split(',') || []
    form.payImages = arr
  }
  setTimeout(() => {
    formRef.value?.clearValidate()
  }, 0)
}

// 收款账户渠道id、支付方式
const payChannelId = ref(null)
const payChannelName = ref('')
// 收款账户数组
const payChannelsInfo = ref({})
const loading = ref(false)
const getPayChannelInfo = async (priceType) => {
  try {
    const res = await getPayChannel({ priceType })
    payChannelId.value = res[0]?.id
    payChannelName.value = res[0]?.payChannelName
    try {
      payChannelsInfo.value = res[0]?.payChannelExt ? JSON.parse(res[0].payChannelExt) : {}
    } catch (parseError) {
      console.error('解析支付渠道信息失败:', parseError)
      payChannelsInfo.value = {}
    }
    Object.assign(form, payChannelsInfo.value)
  } catch (error) {
    console.log(error)
  }
}

const cancelClick = () => {
  isShowDialog.value = false
}

const formRef = ref(null)
const emits = defineEmits(['confirmClick'])
const submitLoading = ref(false)
const confirmClick = () => {
  console.log('form', form)

  formRef.value.validate(async (valid) => {
    if (valid) {
      if (submitLoading.value) return
      submitLoading.value = true
      try {
        const payImages = form?.payImages && form?.payImages?.map((item) => item).join(',')
        const body = {
          id: orderId.value,
          payChannelId: payChannelId.value,
          payImages,
        }
        await payOrder(body)
        ElMessage.success('上传成功')
        emits('confirmClick')
      } catch (error) {
        console.log(error)
      } finally {
        setTimeout(() => {
          submitLoading.value = false
        }, 200)
        isShowDialog.value = false
      }
    } else {
      return false
    }
  })
}
defineExpose({ init })
</script>

<style lang="scss" scoped>
::v-deep {
  .el-form-item {
    margin-bottom: 16px;
  }
}
</style>
