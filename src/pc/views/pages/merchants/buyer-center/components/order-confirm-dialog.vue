<template>
  <el-dialog class="custom-dialog text-#333" v-model="isShowDialog" width="545" :show-close="false" style="padding: 24px">
    <template #header>
      <div class="header flex justify-between text-18px font-600 mb-16px">
        <span><Icon class="text-#FAAD14 mr-2" type="icon-jinggao-shi-tishi1" :size="20" />{{ dialogConfig[type]?.title }}</span>
        <icon class="cursor-pointer" type="icon-guanbi" :size="16" @click="isShowDialog = false" />
      </div>
    </template>
    <template #default>
      <div class="content-wrap text-14px font-500 flex flex-col">
        <div class="table-content px-16px py-12px">
          {{ dialogConfig[type]?.content }}
        </div>
      </div>
    </template>
    <template #footer>
      <div class="footer flex-row-reverse">
        <el-button type="default" @click="cancelClick">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="confirmClick">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { cancelOrder, takeDelivery } from '@/apis/order'

const type = ref('cancel')
const isShowDialog = ref(false)
const orderId = ref(null)
const dialogConfig = {
  cancel: { title: '取消订单', content: '确认取消订单吗？取消后不可恢复' },
  receiptGoods: { title: '确认收货', content: '为保障您的售后权益，请收到商品检查无误后再确认收货' },
}
const init = (id, t) => {
  type.value = t
  isShowDialog.value = true
  orderId.value = id || null
}

const cancelClick = () => {
  isShowDialog.value = false
}

const emits = defineEmits(['confirmClick'])
const submitLoading = ref(false)
const confirmClick = async () => {
  if (submitLoading.value) return
  submitLoading.value = true
  try {
    const isCancel = type.value === 'cancel'
    const msg = isCancel ? '删除成功' : '设置成功'
    const func = isCancel ? cancelOrder : takeDelivery
    await func({ id: orderId.value })
    ElMessage.success(msg)
    emits('confirmClick')
  } catch (error) {
    console.log(error)
  } finally {
    setTimeout(() => {
      submitLoading.value = false
    }, 200)
    isShowDialog.value = false
  }
}
defineExpose({ init })
</script>

<style lang="scss" scoped></style>
