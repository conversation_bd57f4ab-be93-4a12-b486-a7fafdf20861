<template>
  <el-dialog class="custom-dialog text-#333" v-model="isShowDialog" width="640" :show-close="false" style="padding: 24px">
    <template #header>
      <div class="header flex justify-between text-18px font-600 mb-16px">
        <span>{{ form.id ? '编辑收货地址' : '新建收货地址' }}</span>
        <icon class="cursor-pointer" type="icon-guanbi" :size="16" @click="isShowDialog = false" />
      </div>
    </template>
    <template #default>
      <el-form v-loading="loading" :model="form" label-width="96" :rules="rules" ref="formRef" label-position="top">
        <el-form-item label="国家/地区" prop="countryCode">
          <el-select v-model="form.countryCode" @change="changeCountryCode" filterable clearable placeholder="请选择国家/地区">
            <el-option v-for="item in countryLists" :key="item.id" :label="item.countryName" :value="item.countryCode"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="收货人" prop="deliveryName">
          <el-input v-model="form.deliveryName" maxlength="64" placeholder="请输入收货人姓名" clearable></el-input>
        </el-form-item>
        <el-form-item class="phone" label="电话号码" prop="deliveryPhone">
          <el-input v-model.trim="form.deliveryPhone" maxlength="32" placeholder="请输入电话号码" clearable>
            <template #prepend>
              <el-select :placeholder="''" v-model="form.deliveryPhoneCode" style="width: 86px" filterable>
                <el-option :label="`+${item.phoneCode}`" :value="item.phoneCode" v-for="item in countryLists" :key="item.phoneCode"></el-option>
              </el-select>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item v-if="form.countryCode === 'CN'" label="所在地区" prop="area">
          <el-cascader
            ref="cascaderRef"
            v-model="form.area"
            placeholder="请选择所在地区"
            :options="areaList"
            clearable
            filterable
            @change="handleCascaderChange"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="详细地址" prop="address">
          <el-input v-model="form.address" maxlength="256" placeholder="请输入详细地址" clearable></el-input>
        </el-form-item>
        <el-form-item class="flex" v-if="form.countryCode !== 'CN'" label="所在地区">
          <template #label><span class="text-#f56c6c">*</span> 所在地区</template>
          <el-form-item class="flex-1" label="" prop="provinceCode">
            <el-select v-model="form.provinceCode" @change="changeProvinceCode" filterable clearable placeholder="州或省">
              <el-option v-for="item in provinceList" :key="item.id" :label="item.provinceName" :value="item.provinceCode"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="flex-1 mx-16px" label="" prop="cityCode">
            <el-select v-model="form.cityCode" @change="changeCityCode" filterable clearable placeholder="城市">
              <el-option v-for="item in cityList" :key="item.id" :label="item.cityName" :value="item.cityCode"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="flex-1" label="" prop="postalCode">
            <el-input v-model.trim="form.postalCode" maxlength="20" placeholder="邮政编码" clearable></el-input>
          </el-form-item>
        </el-form-item>
        <el-form-item label="">
          <el-checkbox-group v-model="form.isDefault">
            <el-checkbox value="1" name="isDefault"> 设为默认 </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </template>
    <template #footer>
      <div class="footer flex-row-reverse">
        <el-button type="default" @click="cancelClick">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="confirmClick">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { useRegionCodeStore } from '@/pc/stores'
import { getRegionList } from '@/apis/common'
import { getUserDeliveryAddressById, saveUserDeliveryAddress } from '@/apis/merchants'

const form = reactive({
  countryCode: '',
  countryName: '',
  deliveryName: '',
  deliveryPhoneCode: '',
  deliveryPhone: '',
  area: [],
  address: '',
  isDefault: [],
})
const rules = ref({
  countryCode: [{ required: true, message: '请选择国家/地区', trigger: 'change' }],
  deliveryName: [{ required: true, message: '请输入收货人姓名', trigger: 'blur' }],
  deliveryPhone: [{ required: true, message: '请输入电话号码', trigger: 'blur' }],
  area: [{ required: true, message: '请选择所在地区', trigger: 'change' }],
  address: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
  provinceCode: [{ required: true, message: '请选择州或省', trigger: 'blur' }],
  cityCode: [{ required: true, message: '请选择城市', trigger: 'blur' }],
  postalCode: [{ required: false, message: '', trigger: 'blur' }],
})

const regionCodeStore = useRegionCodeStore()
// 国家、地区
const countryLists = computed(() => regionCodeStore.countryLists || [])
if (!countryLists.value.length) {
  regionCodeStore.queryCountryList()
}

// 中国省市区数据
const areaList = computed(() => regionCodeStore.areaList || [])
if (!areaList.value.length) {
  regionCodeStore.queryAreaList()
}

// 海外州或省
const provinceList = ref([])
// 海外城市
const cityList = ref([])
const isShowDialog = ref(false)
const init = async (id) => {
  Object.keys(form).forEach((key) => (form[key] = ''))
  form.area = []
  provinceList.value = []
  cityList.value = []
  form.isDefault = []
  isShowDialog.value = true
  if (id) {
    form.id = id
    await getDeliveryDetail(id)
  } else {
    // 新增地址时默认cityCode、provinceCode必填，防止编辑过没有州、省、城市数据的地址。
    rules.value.provinceCode[0].required = true
    rules.value.cityCode[0].required = true
  }
  setTimeout(() => {
    formRef.value?.clearValidate()
  }, 0)
}

const loading = ref(false)
const getDeliveryDetail = async (id) => {
  try {
    loading.value = true
    const { isDefault, countryCode, provinceCode, cityCode, areaCode, ...rest } = await getUserDeliveryAddressById({ id: id })
    if (countryCode && countryCode !== 'CN') {
      countryCode && (await getProvinceList(countryCode, 0))
      if (countryCode && provinceCode) {
        await getProvinceList(countryCode, provinceCode)
      }
    }
    Object.assign(form, {
      ...rest,
      countryCode,
      provinceCode,
      cityCode,
      areaCode,
      area: countryCode === 'CN' ? [provinceCode, cityCode, areaCode] : [],
      isDefault: [isDefault?.toString()],
    })
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}

const getProvinceList = async (ctrcode, parcode) => {
  try {
    const res = await getRegionList({ countryCode: ctrcode, parentCode: parcode })
    const mapRegion = (item) => ({
      ...item,
      [parcode ? 'cityCode' : 'provinceCode']: item.regionCode,
      [parcode ? 'cityName' : 'provinceName']: item.regionName,
    })

    if (parcode) {
      cityList.value = res.map(mapRegion)
      // 如果没有城市数据则cityCode非必填
      if (!cityList.value.length) {
        rules.value.cityCode[0].required = false
      } else {
        rules.value.cityCode[0].required = true
      }
    } else {
      provinceList.value = res.map(mapRegion)
      // 如果没有州、省数据则provinceCode、cityCode非必填
      if (!provinceList.value.length) {
        rules.value.provinceCode[0].required = false
        rules.value.cityCode[0].required = false
      } else {
        rules.value.provinceCode[0].required = true
        rules.value.cityCode[0].required = true
      }
    }
  } catch (error) {
    console.log(error)
  }
}

// 选择国家、地区
const changeCountryCode = async (countryCode) => {
  const selectedCountry = countryLists.value.find((item) => item?.countryCode === countryCode)
  form.countryName = selectedCountry?.countryName || ''
  form.deliveryPhoneCode = selectedCountry?.phoneCode || ''
  form.deliveryPhone && (form.deliveryPhone = '')
  form.address = ''
  if (countryCode !== 'CN') {
    form.provinceCode = ''
    form.cityCode = ''
    form.postalCode = ''
    provinceList.value = []
    cityList.value = []
  } else {
    form.area = []
  }
  countryCode && (await getProvinceList(countryCode, 0))
  setTimeout(() => {
    formRef.value?.clearValidate()
  }, 0)
}

// 海外州或省选择事件
const changeProvinceCode = async (provinceCode) => {
  const selectedProvince = provinceList.value.find((item) => item?.provinceCode === provinceCode)
  form.provinceName = selectedProvince?.provinceName || ''
  form.cityCode && (form.cityCode = '')
  form.postalCode && (form.postalCode = '')
  if (form.countryCode && provinceCode) {
    await getProvinceList(form.countryCode, provinceCode)
  }
  provinceCode && (await formRef.value.validateField(['provinceCode']))
}

// 海外城市选择事件
const changeCityCode = async (cityCode) => {
  const selectedCity = cityList.value.find((item) => item?.cityCode === cityCode)
  form.cityName = selectedCity?.cityName || ''
  await formRef.value.validateField('cityCode')
}

// 中国所在地区选择
const cascaderRef = ref(null)
const handleCascaderChange = async () => {
  await formRef.value.validateField('area')
  const selectedLabels = cascaderRef.value.getCheckedNodes()[0].pathLabels
  if (Array.isArray(selectedLabels)) {
    form.provinceName = selectedLabels[0]
    form.cityName = selectedLabels[1]
    form.areaName = selectedLabels[2]
  }
}

const cancelClick = () => {
  isShowDialog.value = false
}

const formRef = ref(null)
const emits = defineEmits(['confirmClick'])
const submitLoading = ref(false)
const confirmClick = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      if (submitLoading.value) return
      submitLoading.value = true
      try {
        const { area, isDefault, ...params } = form
        const body = {
          ...params,
          ...(form.countryCode === 'CN' && {
            provinceCode: Array.isArray(area) ? area[0] : null,
            cityCode: Array.isArray(area) ? area[1] : null,
            areaCode: Array.isArray(area) ? area[2] : null,
          }),
          addressType: form.countryCode === 'CN' ? 1 : 0,
          status: 1,
          isDefault: isDefault?.[0] || 0,
        }
        await saveUserDeliveryAddress(body)
        ElMessage.success(form.id ? '编辑成功' : '新建成功')
        emits('confirmClick', body)
      } catch (error) {
        console.log(error)
      } finally {
        setTimeout(() => {
          submitLoading.value = false
        }, 200)
        isShowDialog.value = false
      }
    } else {
      return false
    }
  })
}
defineExpose({ init })
</script>

<style lang="scss" scoped>
::v-deep {
  .el-form-item__label {
    color: #505259;
  }
  .el-input .el-input__icon {
    display: none;
  }
  .phone {
    .el-form-item__error {
      margin-left: 86px;
    }
  }
  .el-checkbox__label {
    color: #505259;
  }
}
</style>
