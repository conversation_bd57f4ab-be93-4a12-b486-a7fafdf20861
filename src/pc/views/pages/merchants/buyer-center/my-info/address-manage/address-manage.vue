<template>
  <div class="product-manage bg-white p-4 h-full flex flex-col">
    <div class="address-manage">
      <div class="header flex justify-between mb-4">
        <h2 class="text-#1A1A1A text-24px font-500">{{ t('deliveryAddress') }}</h2>
        <el-button type="primary" @click="handleAddEditAddress()">{{ t('addDeliveryAddress') }}</el-button>
      </div>

      <div v-for="(address, index) in addresses" :key="index" class="flex address-item p-4 mb-4 text-#505259" :class="{ default: address.isDefault }">
        <div class="flex flex-1 flex-col address justify-center">
          <div v-if="address?.status === 0" class="info text-12px text-#D8131A"><Icon type="icon-jinggaoAlert1" :size="14" /> 请完善收货地址</div>
          <div class="text-14px font-500 mb-8px">
            {{ address?.deliveryName }}
            <span class="mx-4">
              {{ address?.deliveryPhoneCode ? '+' + address?.deliveryPhoneCode : '' }}
              {{ address?.deliveryPhone ? address?.deliveryPhone : '' }}
            </span>
            <span v-if="address.isDefault" class="default-label h-17px rounded text-#fa3e3e text-12px px-8px">{{ t('isDefault') }}</span>
            <span v-else class="ml-2 underline cursor-pointer" @click="setDefaultAddress(address)">{{ t('setDefaultDeliveryAddress') }}</span>
          </div>
          <div class="address-detail">
            {{ changeAddress(address) }}
          </div>
        </div>
        <div class="flex ml-16px items-center justify-right pl-60px">
          <span class="p-2 cursor-pointer text-#000000" @click="handleAddEditAddress(address)">
            <Icon type="icon-bianji1" :size="18" />
          </span>
          <span class="p-2 ml-2 cursor-pointer text-#000000" @click="handleDeleteAddress(address)">
            <Icon type="icon-new-shanchu" :size="18" />
          </span>
        </div>
      </div>
      <template v-if="addresses.length === 0">
        <EmptyText />
      </template>
    </div>

    <div class="pagination-wrapper" v-if="total > pageSize">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :background="true"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
    </div>
  </div>
  <DeliveryAddress ref="deliveryAddressRef" @confirm-click="debounceGetData" />
  <DeliveryConfirmDialog ref="confirmDialogRef" @confirm-click="debounceGetData" />
</template>

<script setup>
import { onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import EmptyText from '@/pc/components/empty-text/empty-text.vue'
import DeliveryAddress from '../../components/delivery-address.vue'
import DeliveryConfirmDialog from '../../components/delivery-confirm-dialog.vue'
import { useUserStore } from '@/pc/stores'
import { getUserDeliveryAddress } from '@/apis/merchants.js'
import { debounce } from '@/common/js/util'

const userStore = useUserStore()
const { t } = useI18n({
  messages: {
    zh: {
      deliveryAddress: '收货地址',
      addDeliveryAddress: '新增收货地址',
      isDefault: '默认地址',
      setDefaultDeliveryAddress: '设为默认',
    },
  },
})

const addresses = ref([])

// 根据国内外地址类型，转换地址
const changeAddress = (deliveryAddress) => {
  if (!deliveryAddress) return ''
  const { addressType, countryName, provinceName, cityName, areaName, address, postalCode } = deliveryAddress
  return addressType
    ? `${countryName ? countryName : ''} ${provinceName ? provinceName : ''} ${cityName ? cityName : ''} ${areaName ? areaName : ''} ${address ? address : ''}`
    : `${address ? address : ''}${cityName ? ', ' + cityName : ''}${provinceName ? ', ' + provinceName : ''} ${postalCode ? postalCode : ''}${countryName ? ', ' + countryName : ''}`
}

const deliveryAddressRef = ref(null)
// 新增、修改地址
const handleAddEditAddress = (row) => {
  deliveryAddressRef.value.init(row?.id)
}

const confirmDialogRef = ref(null)
const handleDeleteAddress = (row) => {
  confirmDialogRef.value.init(row, 'delete')
}

const setDefaultAddress = (row) => {
  confirmDialogRef.value.init(row, 'setDefault')
}

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

const { id } = userStore.userInfo || {}
const loading = ref(false)
// 获取列表
const getList = async () => {
  loading.value = true
  try {
    const body = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      userId: id,
    }
    const { totalRecord, rowList } = await getUserDeliveryAddress(body)
    addresses.value = rowList
    total.value = totalRecord
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}
onMounted(() => {
  getList()
})
// 获取列表 - 防抖包装
const DEBOUNCE_TIME = 300
const debounceGetData = debounce(() => {
  getList()
}, DEBOUNCE_TIME)

//分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getList()
}
const handleCurrentChange = (val) => {
  currentPage.value = val
  getList()
}
</script>

<style lang="scss" scoped>
::v-deep {
  .el-button + .el-button {
    margin-left: 0;
  }
}
.product-manage {
  .address-item {
    border-radius: 8px 8px 0px 0px;
    box-sizing: border-box;
    border: 1px solid #f0f0f0;
  }
  .address {
    min-height: 112px;
    border-right: 1px solid #f0f0f0;
    .address-detail {
      word-break: break-word;
    }
  }

  .default-label {
    background: rgba(250, 62, 62, 0.1);
    border-radius: 17px;
  }
}

[dir='rtl'] {
  .product-manage {
    text-align: right;
  }
}
</style>
