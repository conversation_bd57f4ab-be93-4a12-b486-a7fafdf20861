<template>
  <div class="info-wrap h-full" v-loading="pageLoading">
    <el-form
      ref="infoFormRef"
      class="info-form"
      :model="infoForm"
      :rules="rules"
      :label-width="$storageLocale === 'en' ? '150px' : '130px'"
      label-position="left"
    >
      <!-- 中文时显示手机号或邮箱 -->
      <el-form-item v-if="storageLocale === 'zh'" :label="t('phone')" prop="userName">
        <el-input v-model.trim="infoForm.userName" :placeholder="t('filed1Placeholder')" disabled />
      </el-form-item>
      <!-- 非中文时时显示邮箱 -->
      <el-form-item v-else :label="t('fieldEmail')" prop="mobile">
        <el-input v-model.trim="infoForm.mobile" :placeholder="t('fieldEmailPlaceholder')" />
      </el-form-item>
      <el-form-item :label="t('company')" prop="companyName">
        <el-input v-model.trim="infoForm.companyName" clearable :placeholder="t('placeholderCompany')" />
      </el-form-item>
      <el-form-item :label="t('address')" prop="companyAddress">
        <el-input v-model.trim="infoForm.companyAddress" clearable :placeholder="t('placeholderAddress')" />
      </el-form-item>
      <el-form-item :label="t('weChat')" prop="contactWechat">
        <el-input v-model.trim="infoForm.contactWechat" clearable :placeholder="t('placeholderWeChat')" />
      </el-form-item>
      <el-form-item :label="t('name')" prop="contactName">
        <el-input v-model.trim="infoForm.contactName" clearable :placeholder="t('placeholderName')" />
      </el-form-item>
      <el-form-item :label="t('storeType')">
        <el-checkbox-group v-model="infoForm.shopType">
          <el-checkbox v-for="item in shopTypeArr" :key="item.id" :value="item.id" name="shopType" class="min-w-[85px] checkbox-item">
            {{ item.name }}
          </el-checkbox>
        </el-checkbox-group>
        <div class="flex w-[310px] overflow-hidden">
          <el-checkbox v-model="shopOtherTypeFlag">{{ t('other') }}</el-checkbox>
          <el-input
            v-if="shopOtherTypeFlag"
            v-model.trim="infoForm.shopOtherType"
            class="w-[120px] ml-[12px] other-item"
            :placeholder="t('storeTypeValid')"
            clearable
          ></el-input>
        </div>
      </el-form-item>
      <el-form-item class="update-btn" label-width="auto">
        <el-button type="primary" @click="onSubmit" :loading="loading" class="w-[366px]">{{ t('update') }}</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/pc/stores'
import { getDictListByKey, getUserInfo, updateUserInfo } from '@/apis/merchants'
import { useStorageLocale } from '@/i18n'

const { storageLocale } = useStorageLocale()

const { t } = useI18n({
  messages: {
    zh: {
      phone: '手机号或邮箱',
      filed1Placeholder: '请输入手机号或邮箱',
      filed1Validate: '请输入正确的手机号或邮箱',
      fieldEmail: '邮箱',
      fieldEmailPlaceholder: '请输入邮箱',
      fieldEmailValidate: '请输入正确的邮箱',
      address: '收货地址',
      company: '公司名',
      weChat: '微信',
      name: '姓名',
      storeType: '店铺类型',
      other: '其他',
      update: '更新',
      updateSuccess: '更新成功',

      addressValid: '请输入地址',
      nameValid: '请输入姓名',
      storeTypeValid: '请输入店铺类型',
      placeholderAddress: '请输入收货地址',
      placeholderCompany: '请输入公司名',
      placeholderWeChat: '请输入微信',
      placeholderName: '请输入姓名',
    },
    en: {
      phone: 'Phone',
      address: 'Address',
      company: 'Company',
      weChat: 'WeChat',
      name: 'Name',
      storeType: 'Store Type',
      other: 'Other',
      update: 'Update',
      updateSuccess: 'Update Successful',
      filed1Placeholder: 'Please enter phone number',
      filed1Placeholder1: 'Please enter a valid phone number',
      addressValid: 'Please enter phone address',
      nameValid: 'Please enter name',
      storeTypeValid: 'Please enter store type',
      placeholderAddress: 'Please enter address',
      placeholderCompany: 'Please enter company',
      placeholderWeChat: 'Please enter weChat',
      placeholderName: 'Please enter name',
    },
  },
})

const infoForm = reactive({
  mobile: '',
  companyName: null,
  companyAddress: '',
  contactWechat: null,
  userName: '',
  contactName: '',
  shopName: null,
  shopType: [],
  shopOtherType: '',
})
const shopOtherTypeFlag = ref(false)
const shopTypeArr = ref([])
const userStore = useUserStore()

const validateRequired = (prop, rule, value, callback) => {
  if (!value) {
    callback(new Error(t(prop)))
  }
  callback()
}

const rules = reactive({
  userName: [
    {
      required: true,
      message: storageLocale.value === 'zh' ? t('filed1Placeholder') : t('fieldEmailPlaceholder'),
      trigger: ['change', 'blur'],
    },
    {
      pattern:
        storageLocale.value === 'zh'
          ? /^1[3456789]\d{9}$|^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
          : /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, // 手机号或邮箱, 邮箱
      message: storageLocale.value === 'zh' ? t('filed1Validate') : t('fieldEmailValidate'),
      trigger: 'blur',
    },
  ],
  companyAddress: [{ required: true, validator: (...args) => validateRequired('addressValid', ...args), trigger: ['blur', 'change'] }],
  contactName: [{ required: true, validator: (...args) => validateRequired('nameValid', ...args), trigger: ['blur', 'change'] }],
})

const infoFormRef = ref(null)
const loading = ref(false)
const pageLoading = ref(false)

const onSubmit = () => {
  infoFormRef.value.validate((valid, fields) => {
    if (valid) {
      onSubmitRequest()
    } else {
      console.log(fields)
    }
  })
}
const onSubmitRequest = async () => {
  if (pageLoading.value) {
    return
  }
  try {
    loading.value = true
    const params = Object.assign({}, infoForm, {
      userType: userStore?.userInfo?.userType || 1,
      shopTypes: infoForm.shopType.join(','),
      shopOtherType: shopOtherTypeFlag.value ? infoForm.shopOtherType : '',
    })
    await updateUserInfo(params)
    ElMessage.success(t('updateSuccess'))
    await buyerInfo()
  } catch (e) {
    console.log(e)
    ElMessage.error(e.message || e.msg)
  } finally {
    loading.value = false
  }
}
const buyerInfo = async () => {
  try {
    pageLoading.value = true
    const data = await getUserInfo()
    if (data) {
      Object.keys(infoForm).forEach((key) => {
        if (key === 'shopType') {
          infoForm[key] = data.shopTypes?.split(',') || []
          return
        }
        infoForm[key] = data[key]
      })
      shopOtherTypeFlag.value = !!infoForm.shopOtherType
    }
  } catch (e) {
    console.log(e)
  } finally {
    pageLoading.value = false
  }
}

const getShopType = async () => {
  try {
    const data = await getDictListByKey({ key: 'shop_type' })
    if (data) {
      shopTypeArr.value = data
    }
  } catch (e) {
    console.log(e)
  }
}

onMounted(() => {
  buyerInfo()
  getShopType()
})
</script>

<style lang="scss" scoped>
.info-wrap {
  width: 100%;
  min-height: calc(100% - 20px);
  background: #fff;
  padding: 16px;
  box-sizing: border-box;

  .checkbox-item {
    margin-right: 15px;
  }
}

.info-form {
  width: 460px;
  margin: 30px auto;
}

:deep(.el-form-item__label) {
  color: #999;
}

:deep(.update-btn) {
  .el-form-item__content {
    display: flex;
    justify-content: center;
  }
}

[dir='rtl'] .info-wrap {
  .checkbox-item {
    margin-right: 0;
    margin-left: 15px;
  }

  :deep(.el-form-item__label) {
    text-align: left;
  }

  .other-item {
    margin-left: 0;
    margin-right: 12px;
  }

  :deep(.el-checkbox__label) {
    padding-right: 8px;
    padding-left: 0;
  }

  :deep(.el-form-item__label) {
    padding-right: 0;
    padding-left: 12px;
  }
}
</style>
