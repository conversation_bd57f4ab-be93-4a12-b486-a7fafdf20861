<template>
  <div v-if="!validRole" class="seller-center" v-loading="!validRole"></div>
  <LayoutCenter :title="MERCHANTS_TYPE.BUYER.title[$i18n.locale]" :subTabs="subTabs" :user-type="MERCHANTS_TYPE.BUYER.id" v-else>
    <router-view />
  </LayoutCenter>
</template>

<script setup>
import LayoutCenter from '../components/layout-center.vue'
import { MERCHANTS_TYPE } from '@/constants/merchants'
import { useMerchantsHooks } from '@/pc/hooks/merchants'

const subTabs = [
  {
    title: { zh: '我的资料', en: 'My Profile' },
    path: '/buyer-center/my-info',
    icon: 'icon-shangjiaziliaoguanli-',
    hasChild: true,
    childs: [
      {
        title: { zh: '基础资料', en: '' },
        path: '/buyer-center/my-info/base-info',
        showLiveChile: false,
      },
      {
        title: { zh: '收货地址管理', en: '' },
        path: '/buyer-center/my-info/address-manage',
        showLiveChile: false,
      },
    ],
  },
  { title: { zh: '我的商品收藏', en: 'My favorites' }, path: '/buyer-center/my-collection', icon: 'icon-wodeshangpinshoucang' },
  { title: { zh: '订单管理', en: 'Order Manage' }, path: '/buyer-center/order-manage', icon: 'icon-wodedingdan' },
  { title: { zh: '商机中心', en: 'Opportunity Hub' }, path: '/buyer-center/my-opportunity', icon: 'icon-shangjizhongxin' },
]

const { validRole } = useMerchantsHooks(MERCHANTS_TYPE.BUYER.id)
</script>

<style scoped lang="scss">
.seller-center {
  min-height: calc($main-height - 64px);
}
</style>
