<template>
  <div class="order-manage bg-white p-4 h-full flex flex-col">
    <el-tabs v-model="orderStatus" @tab-click="tabClick">
      <el-tab-pane v-for="(item, i) in tabList" :key="i" :label="item.label[$storageLocale]" :name="item.id"></el-tab-pane>
    </el-tabs>
    <SearchForm ref="searchFormRef" :orderStatus="orderStatus" :showFoldBtn="false" @searchClick="searchClick" />
    <div class="order-card flex flex-col mt-4" v-for="(order, index) in orderList" :key="index">
      <div class="order-card-header flex items-center px-4 pt-4 pb-2 bg-#F7F8FC">
        <div class="flex-1">
          <div class="status text-#FA3E3E text-16px font-500 mb-2">{{ BUYER_ORDER_STATUS_MAP[order?.status] }}</div>
          <div class="text-#505259 text-14px">
            <span>订单编号：{{ order?.id }}</span
            ><span class="ml-2">下单时间：{{ formatTime(order?.orderTime) }}</span>
          </div>
        </div>
        <div class="total text-16px font-500 text-#1A1A1A">总计：<c-symbol :priceType="order?.orderExt?.priceType" />{{ order?.finalPrice }}</div>
      </div>
      <div class="order-card-content flex p-4">
        <div class="flex flex-1 flex-col good justify-center">
          <div
            class="flex relative"
            :class="{ 'mb-4': index !== order?.orderGoodsList?.length - 1 }"
            v-for="(good, index) in order?.orderGoodsList?.slice(0, 2)"
            :key="index"
          >
            <img-loader v-if="good?.goodsImage" :src="good?.goodsImage" img-class="w-[64px] h-[64px] object-cover bg-[#f8f8f8]" alt="" />
            <!-- 出口转内销 -->
            <div class="absolute left-0 top-0 z-10 w-[30px] h-[12px] img-logo" v-if="good?.goodsExt?.showType === 1">
              <img-loader
                :src="bannarImgNow"
                class="w-[100%] h-[100%] object-cover cursor-pointer"
                :loading-img="`${bannarImgNow}?x-oss-process=image/resize,h_100`"
              ></img-loader>
            </div>
            <div class="flex-1 ml-4">
              <div class="goods-name text-#1A1A1A font-500 text-14px mb-4px">
                {{ good?.goodsName }}
              </div>
              <div class="flex-1 text-ellipsis-1 mr-5 text-#505259">
                <span v-for="(k, v) in JSON.parse(good?.spec)" :key="v" class="mr-2">{{ k?.spec }}：{{ k?.item?.value }}</span>
                <span>数量：{{ good?.count }}</span>
              </div>
              <div class="text-#1A1A1A text-ellipsis-1 font-400 text-14px mt-4px">
                单价：<c-symbol :priceType="order?.orderExt?.priceType" />{{ good?.price }}
              </div>
            </div>
          </div>
          <div v-if="order?.orderGoodsList?.length > 2" class="view-more flex items-center cursor-pointer" @click="viewDetailClick(order)">
            查看全部 {{ order?.orderGoodsList?.length }} 个商品<el-icon class="ml-1"><ArrowDown /></el-icon>
          </div>
        </div>
        <div class="flex flex-col address-actions ml-4 justify-right pl-60px">
          <el-button v-if="[200, 350].includes(order?.status)" type="primary" @click="uploadPayClick(order)">{{
            order?.status === 200 ? '立即付款' : '重新上传支付凭证'
          }}</el-button>
          <el-button v-if="order?.status === 500" type="primary" class="my-8px" @click="confirmOrCancelOrderClick(order, 'receiptGoods')">确认收货</el-button>
          <el-button type="default" class="my-8px" @click="viewDetailClick(order)">查看详情</el-button>
          <el-button
            v-if="!order?.isFold && [100, 200].includes(order?.status)"
            type="primary"
            class="my-8px"
            @click="confirmOrCancelOrderClick(order, 'cancel')"
            >取消订单</el-button
          >
          <el-button v-if="!order?.isFold && order?.status === 100" type="default" class="my-8px" @click="editDeliveryAddressClick(order)"
            >修改收货地址</el-button
          >

          <div
            v-if="[100, 200].includes(order?.status)"
            class="view-more flex items-center justify-center cursor-pointer text-#505259 text-14px"
            @click="order.isFold = !order.isFold"
          >
            <template v-if="order?.isFold"> 更多操作 <Icon class="ml-1" type="icon-xiajiantou" :size="16" /> </template>
            <template v-else> 收起 <Icon class="ml-1" type="icon-shangjiantou" :size="16" /> </template>
          </div>
        </div>
      </div>
    </div>
    <template v-if="orderList?.length === 0">
      <EmptyText />
    </template>
    <div class="pagination-wrapper" v-if="total > pageSize">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :background="true"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
    </div>
  </div>
  <OrderConfirmDialog ref="confirmDialogRef" @confirm-click="debounceGetData" />
  <UploadPayDialog ref="uploadPayDialogRef" @confirm-click="debounceGetData" />
  <SelectDeliveryAddress ref="selectDeliveryAddressRef" @confirm-click="debounceGetData" />
</template>

<script setup>
import { ArrowDown } from '@element-plus/icons-vue'
import EmptyText from '@/pc/components/empty-text/empty-text.vue'
import OrderConfirmDialog from '../components/order-confirm-dialog.vue'
import SelectDeliveryAddress from '../components/select-delivery-address.vue'
import UploadPayDialog from '../components/upload-pay-dialog.vue'
import SearchForm from './components/search-form.vue'
import { BANNARIMG } from '@/constants/index'
import { BUYER_ORDER_STATUS_MAP, ORDER_STATUS_ARRAY } from '@/constants/order'
import { getBuyerPage } from '@/apis/order'
import { useStorageLocale } from '@/i18n/translatePlugin'
import { formatTime } from '@/common/js/date'
import { debounce } from '@/common/js/util'

const { storageLocale } = useStorageLocale()
const bannarImgNow = computed(() => {
  const img = BANNARIMG[storageLocale.value] || BANNARIMG.zh
  return img
})

const orderStatus = ref(1)
const tabList = computed(() => {
  return ORDER_STATUS_ARRAY.filter((item) => ![300, 350].includes(item.id)).map((item) => ({
    id: item.id,
    label: item.translateName,
  }))
})
const searchFormRef = ref(null)
const tabClick = (item) => {
  currentPage.value = 1
  orderStatus.value = item?.props?.name
  if (searchFormRef.value?.filterForm) {
    searchFormRef.value.filterForm.statusList = orderStatus.value
  }
  debounceGetData()
}

const orderList = ref([])
const loading = ref(true)

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
// 查询条件
const searchValue = ref({})

// 获取列表
const getList = async () => {
  loading.value = true
  try {
    const body = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      statusList: orderStatus.value === 1 ? '' : orderStatus.value === 200 ? '200,300,350' : orderStatus.value,
      ...searchValue.value,
    }
    const { totalRecord, rowList } = await getBuyerPage(body)
    orderList.value = rowList.map((order) => ({ ...order, isFold: true }))
    total.value = totalRecord
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}
getList()

// 获取列表 - 防抖包装
const DEBOUNCE_TIME = 300
const debounceGetData = debounce(() => {
  getList()
}, DEBOUNCE_TIME)

// 查询、重置
const searchClick = (search) => {
  const { statusList, ...rest } = search
  orderStatus.value = statusList
  searchValue.value = rest
  currentPage.value = 1
  debounceGetData()
}

// 修改收货地址
const selectDeliveryAddressRef = ref(null)
const editDeliveryAddressClick = (order) => {
  selectDeliveryAddressRef.value.init(order?.orderDeliveryAddress, 'userDeliveryAddressId')
}

// 立即付款、重新上传支付凭证
const uploadPayDialogRef = ref(null)
const uploadPayClick = (order) => {
  uploadPayDialogRef.value.init(order)
}

// 取消订单、确认收货
const confirmDialogRef = ref(null)
const confirmOrCancelOrderClick = (item, type) => {
  confirmDialogRef.value.init(item?.id, type)
}

const router = useRouter()
// 查看详情
const viewDetailClick = (item) => {
  router.push(`/buyer-center/order-detail?id=${item?.id}`)
}

//分页
const handleSizeChange = (val) => {
  currentPage.value = 1
  pageSize.value = val
  getList()
}
const handleCurrentChange = (val) => {
  currentPage.value = val
  getList()
}
</script>

<style lang="scss" scoped>
[dir='rtl'] {
  .ml-1 {
    margin-right: 4px;
    margin-left: 0;
  }
  .ml-2 {
    margin-right: 8px;
    margin-left: 0;
  }
  .ml-4 {
    margin-right: 16px;
    margin-left: 0;
  }
  .ml-8 {
    margin-right: 32px;
    margin-left: 0;
  }
  .mr-5 {
    margin-right: 0px;
  }
}
::v-deep {
  .el-tabs__active-bar {
    bottom: 1px;
  }
  .el-tabs__item {
    font-size: 16px;
    color: #888b94;
    &.is-active {
      color: #fa3e3e;
    }
  }
  .order-card {
    .el-button + .el-button {
      margin-left: 0;
    }
  }
}
.order-manage {
  .order-card {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    &-content {
      .good {
        border-right: 1px solid #f0f0f0;
      }
    }
  }
}
</style>
