<template>
  <el-input :class="showFoldBtn ? 'mb-18px' : 'mb-8px'" v-model.trim="filterForm.search" clearable placeholder="请输入订单编号、商品名称" />
  <el-form :model="filterForm" v-show="isFormVisible || !showFoldBtn" label-position="top" transition="fade" label-width="100">
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="订单状态">
          <el-select v-model="filterForm.statusList" placeholder="请选择订单状态">
            <el-option v-for="item in ORDER_STATUS_ARRAY" :key="item.id" :label="item.translateName[$storageLocale]" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="下单时间">
          <el-date-picker
            v-model="filterForm.dateArr"
            type="datetimerange"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-time="defaultTime"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <div class="flex items-center filter-btn">
    <el-button type="primary" @click="searchClick">查询</el-button>
    <el-button type="default" @click="resetClick">重置</el-button>
    <div class="text-#257BFB text-14px inline ml-2 cursor-pointer" v-if="showFoldBtn" @click="toggleFormVisibility">
      <div class="flex items-center" v-if="isFormVisible">
        收起筛选条件 <el-icon class="ml-4px"><ArrowUp /></el-icon>
      </div>
      <div class="flex items-center" v-else>
        展开筛选条件<el-icon class="ml-4px"><ArrowDown /></el-icon>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import { ORDER_STATUS_ARRAY } from '@/constants/order'
import { formatTime } from '@/common/js/date'

const props = defineProps({
  orderStatus: {
    type: Number,
    default: () => 1,
  },
  showFoldBtn: {
    type: Boolean,
    default: false,
  },
})

const isFormVisible = ref(false)

const toggleFormVisibility = () => {
  isFormVisible.value = !isFormVisible.value
}

const defaultTime = [formatTime(Date.now(), 'YYYY-MM-DD 00:00:00'), formatTime(Date.now(), 'YYYY-MM-DD 23:59:59')]

const filterForm = ref({ statusList: props.orderStatus })
const emits = defineEmits(['searchClick'])
const searchClick = () => {
  const { dateArr, ...rest } = filterForm.value
  const emitForm = ref({})
  emitForm.value = {
    ...rest,
    orderStartTime: dateArr?.[0] ? formatTime(dateArr[0]) : null,
    orderEndTime: dateArr?.[1] ? formatTime(dateArr[1]) : null,
  }
  emits('searchClick', emitForm.value)
}

const resetClick = () => {
  Object.keys(filterForm.value).forEach((key) => {
    delete filterForm.value[key]
  })
  filterForm.value.statusList = 1
  searchClick()
}
defineExpose({
  filterForm,
})
</script>

<style lang="scss" scoped>
.fade {
  transition: opacity 0.5s ease;
}
.fade-enter-active,
.fade-leave-active {
  opacity: 1;
}
.fade-enter, .fade-leave-to /* .fade-leave-active in <2.1.8 */ {
  opacity: 0;
}
</style>
