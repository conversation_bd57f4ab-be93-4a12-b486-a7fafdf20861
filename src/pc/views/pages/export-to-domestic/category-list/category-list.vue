<template>
  <div class="export-category-list pd-[24px]" v-if="!nationalTypeUae">
    <div class="h-[128px]"></div>
    <div class="logo-nav">
      <SearchInput></SearchInput>
    </div>
    <div class="home-content w-1260" v-loading="loading">
      <div class="title">{{ labelName }}</div>
      <div class="list">
        <div class="product-list">
          <template v-for="(item, index) in dataList" :key="index">
            <GoodsDetailNewCard :goodsInfo="item" @click="onGoodsClick(item)"></GoodsDetailNewCard>
          </template>
        </div>
        <div class="footer" v-if="dataList.length">
          <el-pagination
            background
            v-model:current-page="pagination.pageNum"
            :page-size="pagination.pageSize"
            layout="total, prev, pager, next, jumper"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
        <el-empty v-if="dataList.length === 0 && !loading" :image-size="200"></el-empty>
      </div>
    </div>
    <!-- 返回顶部 -->
    <!-- <BackTop></BackTop> -->
  </div>
</template>

<script setup>
// import BackTop from '@/pc/components/back-top/back-top.vue'
import SearchInput from '@/pc/components/search-input/search-input.vue'
import GoodsDetailNewCard from '../components/goods-detail-card.vue'
import { getExportResaleList } from '@/apis/export-to-domestic.js'
import { debounce } from '@/common/js/util'

const nationalType = inject('$nationalType')
const nationalTypeUae = nationalType === 'uae'

const dataList = ref([])

const route = useRoute()
// 分页
const pagination = ref({
  pageNum: 1,
  pageSize: 50,
  total: 0,
})
const labelName = route.query?.labelName
const loading = ref(false)
const getData = async () => {
  try {
    loading.value = true

    let { rowList, totalRecord } =
      (await getExportResaleList({
        labelType: route.params.id,
        pageNum: pagination.value.pageNum,
        pageSize: pagination.value.pageSize,
      })) || []
    pagination.value.total = totalRecord
    dataList.value = rowList
    console.log('dataList---', dataList.value)
  } catch (e) {
    console.log(e)
  } finally {
    loading.value = false
  }
}
const DEBOUNCE_TIME = 300
// 获取列表数据防抖函数
const deBounceGetData = debounce(() => {
  getData()
}, DEBOUNCE_TIME)

// 分页条数变化
const handleSizeChange = (val) => {
  pagination.value.pageSize = val
  pagination.value.pageNum = 1
  deBounceGetData()
}

// 分页页数变化
const handleCurrentChange = (val) => {
  pagination.value.pageNum = val
  deBounceGetData()
}
onMounted(() => {
  route.params.id && getData()
})
</script>

<style lang="scss" scoped>
.export-category-list {
  min-height: calc(100vh - 64px);

  background: #fa3e3e;
  .list {
    background: linear-gradient(180deg, #ffffff 0%, #fff6f6 100%);
    padding: 16px;
    margin-top: 16px;
    border-radius: 8px;
    .product-list {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 16px;
    }
  }
  .home-content {
    padding: 36px 0;
  }
  .title {
    background: url('https://static.chinamarket.cn/static/trade-exhibition/export-to-domestic/category-bg.png') no-repeat;
    background-size: 100% 100%;
    color: #df3325;
    font-size: 32px;
    font-weight: 600;
    width: fit-content;
    line-height: 45px;
    padding: 12px 93px 13px 88px;
    margin: 0 auto;
  }
}
.logo-nav {
  position: fixed;
  top: 64px;
  z-index: 49;
  width: 100%;
  height: 64px;
  //margin: 0 auto 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.1);
  background-color: #fff;
}
</style>
