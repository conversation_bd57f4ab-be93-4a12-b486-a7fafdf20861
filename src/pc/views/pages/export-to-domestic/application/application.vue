<template>
  <div class="export-app pd-[24px]" v-if="!nationalTypeUae">
    <div class="h-[128px]"></div>
    <div class="logo-nav">
      <SearchInput></SearchInput>
    </div>
    <div class="home-content w-1260">
      <div class="banner">
        <div class="w-[552px] h-[151px] mb-[20px]">
          <img-loader
            :src="bannarImgNow"
            class="w-[100%] h-[100%] object-cover cursor-pointer rounded-3"
            :loading-img="`${bannarImgNow}?x-oss-process=image/resize,h_100`"
          ></img-loader>
        </div>
        <div class="title skip-translate">{{ $storageLocale === 'zh' ? EXPORT_TO_DOMESTIC_MAP['zh'] : EXPORT_TO_DOMESTIC_MAP['en'] }}</div>
      </div>
      <div class="app-cont">
        <div class="app-left">
          <div class="form-title mb-[10px]">——填写报名信息——</div>
          <el-form ref="formRef" :model="formData" label-position="top" :rules="rules">
            <el-form-item prop="companyName">
              <template #label><div class="form-label">企业名称</div></template>
              <el-input v-model.trim="formData.companyName" maxlength="50" show-word-limit placeholder="请输入您的企业名称"></el-input>
            </el-form-item>

            <el-form-item prop="companySector">
              <template #label><div class="form-label">所属行业</div></template>
              <el-select v-model="formData.companySector" placeholder="请输入所属行业" clearable filterable>
                <el-option v-for="(item, index) in categoryList" :key="index" :label="item.categoryName" :value="item.categoryName"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item prop="tel">
              <template #label><div class="form-label">手机号码</div></template>
              <el-input
                v-model="formData.tel"
                minlength="1"
                maxlength="20"
                oninput="value=value.replace(/[^0-9.]/g,'')"
                placeholder="请输入您的手机号码"
              ></el-input>
            </el-form-item>

            <el-form-item prop="channels">
              <template #label><div class="form-label">意向转销渠道</div></template>
              <el-checkbox-group v-model="formData.channels" class="ml-[12px]">
                <el-checkbox label="转内销" :value="1"></el-checkbox>
                <el-checkbox label="出口到其他地区" :value="2"></el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <div class="pb-2 text-center">
              <el-button type="primary" class="submit-btn" v-loading="btnLoading" @click="handleSubmit">提交</el-button>
            </div>
          </el-form>
        </div>
        <div class="app-right">
          <div class="form-title">——中国大集转销流程——</div>
          <div class="step">
            <div class="w-[88px] h-[30px] m-auto">
              <img :src="`${ossUrl}/export-to-domestic/step-1.png`" class="w-[100%] h-[100%] object-cover" />
            </div>
            <div class="step-title">提交报名表</div>
            <div class="w-[16px] h-[16px] more">
              <img :src="`${ossUrl}/export-to-domestic/more-btn.png`" class="w-[100%] h-[100%] object-cover cursor-pointer" />
            </div>
            <div class="w-[88px] h-[30px] m-auto">
              <img :src="`${ossUrl}/export-to-domestic/step-2.png`" class="w-[100%] h-[100%] object-cover" />
            </div>
            <div class="step-title">行业小二联系您，明确合作事项</div>
            <div class="w-[16px] h-[16px] more">
              <img :src="`${ossUrl}/export-to-domestic/more-btn.png`" class="w-[100%] h-[100%] object-cover cursor-pointer" />
            </div>
            <div class="w-[88px] h-[30px] m-auto">
              <img :src="`${ossUrl}/export-to-domestic/step-3.png`" class="w-[100%] h-[100%] object-cover" />
            </div>
            <div class="step-title">大集为您定制外贸转销方案</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import SearchInput from '@/pc/components/search-input/search-input.vue'
import { ossUrl } from '@/constants/common'
import { EXPORT_TO_DOMESTIC_FONT_IMG } from '@/constants/index'
import { EXPORT_TO_DOMESTIC_MAP } from '@/constants/special-field'
import { exportResaleSignUp } from '@/apis/export-to-domestic'
import { getCategoryTree } from '@/apis/goods'
import { useStorageLocale } from '@/i18n/translatePlugin'

const { storageLocale } = useStorageLocale()
const nationalType = inject('$nationalType')
const nationalTypeUae = nationalType === 'uae'

const bannarImgNow = computed(() => {
  const img = EXPORT_TO_DOMESTIC_FONT_IMG[storageLocale.value] || EXPORT_TO_DOMESTIC_FONT_IMG.zh
  return img
})

// 获取经营类别
const categoryList = ref([])
const getCategoryList = async () => {
  try {
    const body = { categoryType: 2 } // 前台类目传 2
    categoryList.value = (await getCategoryTree(body)) || []
  } catch (error) {
    console.log(error)
  }
}

const formRef = ref(null)
const formData = ref({
  companyName: '',
  companySector: '',
  tel: '',
  channels: [],
})
const rules = {
  companyName: [{ required: true, message: '请输入您的企业名称', trigger: 'blur' }],
  tel: [{ required: true, message: '请输入您的手机号码', trigger: 'blur' }],
  channels: [{ required: true, message: '请选择意向转销渠道', trigger: ['change'] }],
  companySector: [{ required: true, message: '请选择所属行业', trigger: ['change'] }],
}
const router = useRouter()

const btnLoading = ref(false)
const handleSubmit = async () => {
  await formRef?.value?.validate()

  try {
    const submitFormData = {
      companyName: formData.value.companyName,
      tel: formData.value.tel,
      resaleChannel: formData.value.channels?.length > 0 ? formData.value.channels.join(',') : null,
      companySector: formData.value.companySector,
    }
    btnLoading.value = true
    console.log('submitFormData', submitFormData)
    await exportResaleSignUp(submitFormData)
    showSuccessToast('已提交成功，1-3个工作日内行业小二会跟您联系。')
    formRef.value?.resetFields()
    router.back()
  } catch (error) {
    console.log(error)
  } finally {
    btnLoading.value = false
  }
}
onMounted(() => {
  getCategoryList()
})
setTimeout(() => window?.translate?.execute(), 1000)
</script>

<style lang="scss" scoped>
:deep() {
  .el-form-item__label {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: flex-start;
  }
  .el-checkbox__label {
    color: #505259;
    line-height: normal;
  }
}
.export-app {
  .more {
    transform: rotate(90deg);
    margin: 0 auto;
    margin-bottom: 16px;
  }
  .step {
    text-align: center;
    margin-top: 40px;
  }
  .step-title {
    color: #505259;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 4px;
    margin-top: 4px;
  }
  .form-label {
    color: #1a1a1a;
    font-size: 16px;
    font-weight: 500;
  }
  .submit-btn {
    min-width: 200px;
    font-size: 16px;
    height: 48px;
    background: #fa3e3e;
    border-color: #fa3e3e;
  }
  .app-cont {
    display: flex;
    justify-content: center;
    padding-bottom: 32px;
    .form-title {
      font-size: 20px;
      line-height: 28px;
      font-weight: 500;
      color: #1a1a1a;
      text-align: center;
    }
    .app-left {
      border-radius: 8px;
      margin-right: 24px;
      width: 549px;
      background: #ffffff;
      padding: 40px 54px 32px;
    }
    .app-right {
      width: 332px;
      border-radius: 8px;
      background: #ffffff;
      padding: 40px 20px;
    }
  }
  .home-content {
    border-radius: 8px;
    background: #d8131a;
    min-height: calc(100vh - 128px);
    margin-top: 20px;
  }
  .banner {
    background: url('https://static.chinamarket.cn/static/trade-exhibition/export-to-domestic/application-bg.png') no-repeat;
    background-size: contain;
    height: 363px;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding-top: 77px;
    .title {
      font-size: 36px;
      font-weight: bold;
      line-height: 44px;
      margin-bottom: 71px;
      letter-spacing: 0em;
      color: #ffffff;
      width: 617px;
      word-wrap: break-word;
    }
  }
}
.logo-nav {
  position: fixed;
  top: 64px;
  z-index: 49;
  width: 100%;
  height: 64px;
  //margin: 0 auto 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.1);
  background-color: #fff;
}
</style>
