<!-- 新的卡片页面，修改字段名，适应新接口 -->
<template>
  <div class="rec-offer" @click="GoodsDetail(goodsInfo)">
    <div class="goods-img-wrapper relative" :style="imgStyle">
      <img-loader
        :src="`${goodsPic}?x-oss-process=image/resize,h_478`"
        alt="暂无图片"
        class="w-full h-full object-cover rounded-t-2"
        loadingImg="/mall/errorImg.png"
        errorImg="/mall/errorImg.png"
      ></img-loader>
      <div class="w-[60px] h-[24px] img-logo">
        <img-loader
          :src="bannarImgNow"
          class="w-[100%] h-[100%] object-cover cursor-pointer"
          :loading-img="`${bannarImgNow}?x-oss-process=image/resize,h_100`"
        ></img-loader>
      </div>
    </div>
    <div class="goods-content-wrapper">
      <slot>
        <div class="offer-title">{{ goodsInfo.spuName }}</div>

        <span class="offer-tag s"></span>
        <div class="flex justify-between items-center">
          <span class="offer-price">
            <!-- 数字价格 -->
            <i
              v-if="typeof goodsPrice === 'number'"
              class="price whitespace-nowrap text-ellipsis overflow-hidden"
              :title="`${PRICE_TYPE_MAP[goodsInfo.priceType]}${goodsPrice}`"
            >
              <em class="symbol">{{ PRICE_TYPE_MAP[goodsInfo.priceType] }}</em>
              <em class="number n-b">{{ priceFilter.radixPointBefore(goodsPrice) }}</em>
              <em class="number">{{ priceFilter.radixPointAfter(goodsPrice) }}</em>
            </i>
            <!-- 字符串价格 -->
            <i v-else class="price" :title="`${goodsPrice}`">
              <em :class="`number n-b ${goodsPrice && goodsPrice.length > 3 ? 'number-small-text' : ''}`">{{ goodsPrice }}</em>
            </i>
            <el-tooltip content="促销价" placement="top">
              <span class="price-tip whitespace-nowrap text-ellipsis overflow-hidden">促销价</span>
            </el-tooltip>
          </span>
          <div class="flex justify-center items-center collect-cont" @click.stop="handleCollect">
            <div class="w-[12px] h-[12px] mr-[4px]">
              <img v-if="!isCollect" :src="`${ossUrl}/export-to-domestic/star-border.png`" class="w-[100%] h-[100%] object-cover cursor-pointer" />
              <img v-else :src="`${ossUrl}/export-to-domestic/star-red.png`" class="w-[100%] h-[100%] object-cover cursor-pointer" />
            </div>

            <div
              class="color-[#888B94] whitespace-nowrap text-ellipsis overflow-hidden"
              v-loading="collectLoading"
              :title="isCollect ? '已收藏' : '收藏'"
              dir="ltr"
            >
              <span>{{ isCollect ? '已收藏' : '收藏' }}</span>
            </div>
          </div>
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup>
import { ossUrl } from '@/constants/common'
import { PRICE_TYPE_MAP } from '@/constants/goods'
import { BANNARIMG } from '@/constants/index'
import { useUserStore } from '@/pc/stores'
import { goodsCollect } from '@/apis/goods'
import { useEvent } from '@/event'
import { OPEN_NEW_LOGIN } from '@/event/modules/site'
import { useStorageLocale } from '@/i18n/translatePlugin'
import { priceFilter } from '@/utils/utils.js'

const router = useRouter()
const props = defineProps({
  goodsInfo: {
    type: Object,
    default: () => ({}),
  },
  customClick: {
    type: Boolean,
    default: false,
  },
  pic: {
    type: String,
    default: '',
  },
  imgStyle: {
    type: String,
    default: '',
  },
  shopId: {
    type: String,
    default: '',
  },
})

const { storageLocale } = useStorageLocale()

const bannarImgNow = computed(() => {
  const img = BANNARIMG[storageLocale.value] || BANNARIMG.zh
  return img
})
const userStore = useUserStore()
const event = useEvent()
const contactCustomer = () => {
  if (!userStore.isLogined) {
    event.emit(OPEN_NEW_LOGIN, {
      routerDisabled: true,
    })
    return false
  }
  return true
}
const isCollect = ref(false)
isCollect.value = props.goodsInfo?.collectStatus ? true : false
//收藏
const collectLoading = ref(false)
const handleCollect = async () => {
  if (isCollect.value) {
    return
  }
  try {
    if (!contactCustomer()) {
      return
    }
    if (userStore.userInfo?.userType !== 1) {
      event.emit(OPEN_NEW_LOGIN, {})
      return
    }
    collectLoading.value = true
    const { id } = props.goodsInfo || {}
    // console.log('collect', id)
    await goodsCollect({ spuId: id })
    showSuccessToast('收藏成功')
    isCollect.value = true
  } catch (e) {
    console.log(e)
  } finally {
    collectLoading.value = false
  }
}

const goodsPic = computed(() => props.pic || props.goodsInfo?.spuImages?.split(',')[0] || '')
const goodsPrice = computed(() => props.goodsInfo?.minPrice || '')

const GoodsDetail = (goodsInfo) => {
  if (props.customClick) return
  if (goodsInfo?.origin === 1) {
    window.open(goodsInfo.goodsUrl)
    return
  }

  const query = props.shopId ? { shopId: props.shopId } : {}
  const url = router.resolve({ path: `/mall/goods-detail/${goodsInfo.id}`, query })
  window.open(url.href, '_blank')
  // router.push({
  //   name: 'mall-goods-detail',
  //   params: {
  //     id,
  //   },
  // })
}

// const showQRCode = ref(false)
</script>

<style lang="scss" scoped>
.collect-cont {
  font-size: 12px;
  line-height: 12px;
}
.img-logo {
  position: absolute;
  top: 0;
  left: 0;
}
.price-tip {
  height: 18px;
  border-radius: 8px 2px 8px 2px;
  opacity: 1;
  padding: 0px 4px;
  margin-left: 3px;
  font-size: 10px;
  font-weight: normal;
  color: #ffffff;
  background: #fa3e3e;
  line-height: 18px;
  width: 38px;
}
.rec-offer {
  position: relative;
  // padding: 12px;
  background-color: #fff;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-sizing: border-box;
  border: 1px solid #edeef1;

  .offer-title {
    transition: all 0.3s ease;
  }

  &:hover {
    border-color: #fa3e3e;
    :deep(img) {
      border-top-left-radius: 8px;
      // transform: scale(1.01);
    }

    .offer-title {
      color: $primary-color;
    }
  }

  .goods-img-wrapper {
    overflow: hidden;
    height: 244px;
    border-bottom: 1px solid #edeef1;

    :deep(img) {
      width: 100%;
      height: 100%;
      -o-object-fit: cover !important;
      object-fit: cover !important;
      -o-object-position: center !important;
      object-position: center !important;
      transition: all 0.3s ease;
    }
  }
  .qr-code-wrap {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    border-radius: 8px;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
    background: rgba(0, 0, 0, 0.6);
    img {
      width: 216px;
      height: 216px;
      margin-bottom: 10px;
    }
  }
  .goods-content-wrapper {
    overflow: hidden;
    // height: 88px;
    padding: 12px 10px;
    box-sizing: border-box;
  }
  .offer-title {
    height: 40px;
    line-height: 20px;
    font-size: 14px;
    color: $color-333333;
    @include ellipsis(2);
  }
  .label-wrapper {
    display: flex;
    flex-wrap: wrap;

    span {
      padding: 2px 8px;
      margin-right: 8px;
      font-size: 12px;

      &.free-label {
        background: $color-FFE7E7;
        color: $primary-color;
      }

      &.deliver-goods-time {
        background: $color-F1F6FF;
        color: $color-1D5DDC;
      }
    }
  }
  .offer-price {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    white-space: nowrap;
    justify-content: space-between;
    align-items: center;
    .price {
      max-width: 110px;
      display: block;
      color: #fa3e3e;
      font-size: 24px;
      // line-height: 24px;
      .symbol {
        font-size: 14px;
        // line-height: 22px;
        font-style: normal;
      }
      .number {
        font-size: 16px;
        font-style: normal;
        &.n-b {
          font-size: 24px;
          line-height: 32px;
        }

        &.number-small-text {
          font-size: 20px;
        }
      }
    }
    .slot {
      line-height: 32px;
      color: $color-999999;
      font-size: 12px;
    }
  }
  .business-wrapper {
    .business-name {
      margin-bottom: 6px;
      width: 100%;
      font-size: 14px;
      color: $color-666666;
      @include ellipsis;
    }
    .business-address {
      width: 100%;
      font-size: 14px;
      color: $color-999999;
      @include ellipsis;
    }
  }
}
</style>
