<template>
  <div class="banner-wrapper">
    <swiper
      v-if="imgList.length"
      :loop="true"
      :autoplay="{
        delay: 2500,
        disableOnInteraction: false,
      }"
      :pagination="true"
      :modules="modules"
      class="w-[100%] h-[100%] bg-[#fff]"
    >
      <swiper-slide v-for="(item, i) in imgList" :key="i" class="h-[100%] swiper-slide-normal">
        <div class="w-[100%] h-[100%] flex justify-center items-center banner-item relative">
          <img-loader :src="item" :loading-img="`${item}?x-oss-process=image/resize,h_100`" img-class="largeImg" class="largeImg"></img-loader>
        </div>
      </swiper-slide>
      <swiper-slide v-for="(item, i) in imgLargeList" :key="i" class="h-[100%] swiper-slide-large">
        <div class="w-[100%] h-[100%] flex justify-center items-center banner-item relative">
          <img-loader :src="item" :loading-img="`${item}?x-oss-process=image/resize,h_100`" img-class="largeImg" class="largeImg"></img-loader>
        </div>
      </swiper-slide>
    </swiper>
  </div>
</template>

<script setup>
import { Autoplay } from 'swiper/modules'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { ossUrl } from '@/constants/common'

const modules = [Autoplay]
const imgList = [`${ossUrl}/exportToDomestic-list-bg.png`]
const imgLargeList = [`${ossUrl}/exportToDomestic-list-bg.png`]
</script>

<style lang="scss" scoped>
.banner-wrapper {
  width: 100%;
  height: 1042px;
  position: absolute;
  top: 0;
  z-index: 1;
  // height: 100vh;
  //min-height: 640px;
  //margin-top: -64px;
}

.banner-item {
  .img-loader {
    width: 100%;
    height: 100%;
    object-fit: cover; /* 保持图片的宽高比 */
    object-position: center top; /* 图片居中 */

    :deep(img) {
      width: 100%;
      height: 100%;
      object-fit: cover; /* 保持图片的宽高比 */
      object-position: center top; /* 图片居中 */
    }
  }
}

:deep(.swiper-wrapper) {
  height: 100%;
}

.swiper-slide-large {
  display: none;
}

@media screen and (max-width: 1480px) {
  .banner-text {
    font-size: 44px;
  }
}

@media screen and (min-width: 2560px) {
  .banner-text {
    display: none;
  }

  .swiper-slide-normal {
    display: none;
  }

  .swiper-slide-large {
    display: block;
  }
}
</style>
