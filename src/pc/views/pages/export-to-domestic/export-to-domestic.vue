<template>
  <div class="export-to-domestic pd-[24px]" v-if="!nationalTypeUae">
    <div class="h-[128px]"></div>
    <div class="logo-nav">
      <SearchInput></SearchInput>
    </div>
    <!-- banner -->
    <banner></banner>
    <div class="banner-text-wrapper z-2 w-[100%]">
      <div class="w-1260">
        <div class="banner-text text-white">
          <div class="text">中国大集 助力外贸企业</div>
        </div>
        <div class="banner-appli">
          <div class="w-[1000px] h-[274px] mb-[22px]">
            <img-loader
              :src="bannarImgNow"
              class="w-[100%] h-[100%] object-cover cursor-pointer rounded-3"
              :loading-img="`${bannarImgNow}?x-oss-process=image/resize,h_100`"
            ></img-loader>
          </div>
          <div class="app-btn" @click="applicationFun">
            <div class="btn-cont">
              <div class="mr-[10px]">立即报名</div>
              <div class="w-[46px] h-[46px]">
                <img :src="`${ossUrl}/export-to-domestic/right-btn.png`" class="w-[100%] h-[100%] object-cover cursor-pointer" />
              </div>
            </div>
          </div>

          <div class="app-text">外贸企业 零门槛入驻 极速上架</div>
        </div>
      </div>
    </div>
    <div class="content w-1260">
      <div class="list" v-loading="loading">
        <div class="list-nav" ref="tabsNavRef">
          <el-scrollbar v-if="dataList.length > 7">
            <div class="nav-cont">
              <div
                class="nav-item"
                :class="anchorPointIndex == index ? 'active' : ''"
                v-for="(item, index) in dataList"
                @click="onAnchorPointChange(index)"
                :key="index"
              >
                <div class="w-[24px] h-[24px] mr-[4px]">
                  <img :src="item.icon" class="w-[100%] h-[100%] object-cover cursor-pointer" />
                </div>
                <div class="nav-title text-ellipsis overflow-hidden whitespace-nowrap">{{ item.labelName }}</div>
              </div>
            </div>
          </el-scrollbar>
          <div class="nav-cont" v-else>
            <div
              class="nav-item"
              :class="anchorPointIndex == index ? 'active' : ''"
              v-for="(item, index) in dataList"
              @click="onAnchorPointChange(index)"
              :key="index"
            >
              <div class="w-[24px] h-[24px] mr-[4px]">
                <img :src="item.icon" class="w-[100%] h-[100%] object-cover cursor-pointer" />
              </div>
              <div class="nav-title text-ellipsis overflow-hidden whitespace-nowrap">{{ item.labelName }}</div>
            </div>
          </div>
        </div>
        <div class="list-cont">
          <div class="jump-item" v-for="(goodsInfo, index) in dataList" :key="index">
            <div class="title-wrapper">
              <div class="icon-left"></div>
              <div class="title-text text-ellipsis overflow-hidden whitespace-nowrap">{{ goodsInfo.labelName }}</div>
              <div class="icon-right"></div>
              <div class="more" @click="moreFun(goodsInfo)">
                <span>查看更多</span>
                <div class="ml-[4px] w-[16px] h-[16px]">
                  <img :src="`${ossUrl}/export-to-domestic/more-btn.png`" class="w-[100%] h-[100%] object-cover cursor-pointer" />
                </div>
              </div>
            </div>
            <div class="product-list">
              <template v-for="(item, index) in goodsInfo.spuList" :key="index">
                <GoodsDetailNewCard :goodsInfo="item" @click="onGoodsClick(item)"></GoodsDetailNewCard>
              </template>
            </div>
          </div>
        </div>
        <el-empty v-if="dataList.length === 0 && !loading" :image-size="200"></el-empty>
      </div>
    </div>
    <!-- 返回顶部 -->
    <!-- <BackTop></BackTop> -->
  </div>
</template>

<script setup>
// import BackTop from '@/pc/components/back-top/back-top.vue'
import SearchInput from '@/pc/components/search-input/search-input.vue'
import banner from './components/banner.vue'
import GoodsDetailNewCard from './components/goods-detail-card.vue'
import { ossUrl } from '@/constants/common'
import { EXPORT_TO_DOMESTIC_FONT_IMG } from '@/constants/index'
import { getExportResaleTop10List } from '@/apis/export-to-domestic.js'
import { useStorageLocale } from '@/i18n/translatePlugin'

const { storageLocale } = useStorageLocale()

const bannarImgNow = computed(() => {
  const img = EXPORT_TO_DOMESTIC_FONT_IMG[storageLocale.value] || EXPORT_TO_DOMESTIC_FONT_IMG.zh

  return img
})
const router = useRouter()
const applicationFun = () => {
  router.push(`/export-to-domestic/application`)
}
const nationalType = inject('$nationalType')
const nationalTypeUae = nationalType === 'uae'

setTimeout(() => window?.translate?.execute(), 1000)
const dataList = ref([])

const onGoodsClick = (item) => {
  if (!item.goodsUrl) return
  window.open(item.goodsUrl, '_blank')
}

//查看更多
const moreFun = (item) => {
  // const url = router.resolve({ path: `/export-to-domestic/category-list/${item.id}` })
  // window.open(url.href, '_blank')
  if (!item.labelId) {
    return
  }
  router.push({ path: `/export-to-domestic/category-list/${item.labelId}`, query: { labelName: item.labelName } })
}
// 点击 tabs 禁止监听滚动
const isScroll = ref(true)

const tabsNavRef = ref(null)

// 锚点导航
const anchorPointIndex = ref(0)
const onAnchorPointChange = (index) => {
  isScroll.value = false
  anchorPointIndex.value = index
  const inner = document.getElementsByClassName('jump-item')
  const topbannar = document.getElementsByClassName('banner-text-wrapper')
  //顶部图片加文字高度
  const topbannarH = topbannar[0].offsetHeight + topbannar[0].offsetTop
  // 获取元素的计算后的样式
  const style = window.getComputedStyle(inner[index])
  // console.log('click', topbannarH, inner[index].offsetTop - parseInt(style.paddingTop) + tabsNavRef.value.offsetHeight)
  window.scrollTo({
    //128是顶部导航栏高度
    top: inner[index].offsetTop - parseInt(style.paddingTop) - tabsNavRef.value.offsetHeight + topbannarH - 128,
    behavior: 'smooth',
  })
  setTimeout(() => {
    isScroll.value = true
  }, 1000)
}

// 吸顶元素距离顶部的高度
const navWrapperRectTop = ref(0)

const handleScroll = () => {
  const inner = document.getElementsByClassName('jump-item')
  const rect = tabsNavRef.value.getBoundingClientRect()
  navWrapperRectTop.value = rect.top

  if (!isScroll.value) return
  let scrollTop = document.documentElement.scrollTop || document.body.scrollTop
  const topbannar = document.getElementsByClassName('banner-text-wrapper')
  //顶部图片加文字高度
  const topbannarH = topbannar[0].offsetHeight + topbannar[0].offsetTop

  for (let i = 0; i < inner.length; i++) {
    //128是顶部导航栏高度
    const style = window.getComputedStyle(inner[i])
    let hmin = inner[i].offsetTop - parseInt(style.paddingTop) - tabsNavRef.value.offsetHeight + topbannarH - 128 - 100
    // console.log('scroll--i', scrollTop, i, hmin)
    if (scrollTop >= hmin && scrollTop <= hmin + inner[i].scrollHeight) {
      anchorPointIndex.value = i
    }
  }
}
const loading = ref(true)
const getData = async () => {
  try {
    loading.value = true
    dataList.value = (await getExportResaleTop10List()) || []
  } catch (e) {
    console.log(e)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getData()
  window.addEventListener('scroll', handleScroll)
})
onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style lang="scss" scoped>
.export-to-domestic {
  min-height: 1080px;
  background: #fa3e3e;
  padding-bottom: 53px;
  .content {
    //  margin-top: -270px;
    position: relative;
    z-index: 1;

    .list {
      background: #ffffff;
      border-radius: 8px;
    }
  }
}
.product-list {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16px;
  padding: 16px 0;
  background: #ffffff;
}
.list {
  padding: 30px 16px 16px;
  min-height: 300px;
}
.list-nav {
  position: sticky;
  top: 128px;
  left: 0;
  z-index: 10;
  background: $basic-white;
  padding: 8px 0;
}
.nav-cont {
  display: flex;
}

.nav-item {
  flex: 1;
  height: 38px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 7px 20px;

  cursor: pointer;
  &:hover {
    .nav-title {
      color: #fa3e3e;
    }
  }
  .nav-title {
    width: 96px;
    color: #888b94;
    font-size: 16px;
    font-weight: 500;
  }
}
.active {
  background: #fef5f5;
  .nav-title {
    color: #fa3e3e;
  }
}
.logo-nav {
  position: fixed;
  top: 64px;
  z-index: 49;
  width: 100%;
  height: 64px;
  //margin: 0 auto 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.more {
  position: absolute;
  right: 0;
  bottom: 16px;
  font-size: 16px;
  color: #505259;
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: normal;
}
.title-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 56px;
  margin: 28px 0 16px;

  .title-text {
    margin: 0 16px;
    font-size: 40px;
    font-weight: 600;
    color: #1a1a1a;
    max-width: 50%;
  }

  .icon-left,
  .icon-right {
    width: 45px;
    height: 16px;
  }
  .icon-left {
    background: url('https://static.chinamarket.cn/static/trade-exhibition/zu-left.png') no-repeat;
    background-size: cover;
  }
  .icon-right {
    background: url('https://static.chinamarket.cn/static/trade-exhibition/zu-right.png') no-repeat;
    background-size: cover;
  }
}
.banner-text-wrapper {
  margin-top: 127px;
  position: relative;
  .banner-appli {
    display: flex;
    flex-direction: column;
    text-align: center;
    align-items: center;
    margin-bottom: 37px;
    .app-btn {
      cursor: pointer;
      margin-bottom: 12px;
      background: url('https://static.chinamarket.cn/static/trade-exhibition/export-to-domestic-btn.png?v=1') no-repeat;
      background-size: 100%;

      .btn-cont {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 532px;
        height: 124px;
        font-size: 28px;
        line-height: 39px;
        font-weight: 600;
        text-align: center;
        letter-spacing: 0em;
        color: #fa3e3e;
        padding-bottom: 8px;
      }
    }
    .app-text {
      font-size: 24px;
      font-weight: 600;
      line-height: normal;
      text-align: center;
      letter-spacing: 0em;
      color: #fff;
      max-width: 50%;
      word-break: break-word;
    }
  }
  .banner-text {
    .text {
      text-align: center;
      letter-spacing: 0em;
      font-size: 64px;
      font-weight: bold;
      line-height: normal;
    }

    .red-text {
      font-size: 28px;
      width: max-content;
      font-family: PingFang SC;
      font-weight: 600;
      line-height: normal;
      background: linear-gradient(270deg, #d8131a 0%, #e96f18 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }
}
</style>
