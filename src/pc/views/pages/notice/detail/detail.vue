<template>
  <div class="w-1260 main-wrap">
    <Breadcrumb :breadcrumbList="breadcrumbList" />
    <div class="px-[20px] py-[20px] bg-white mb-[40px] min-h-[500px]">
      <div v-if="detailInfo">
        <div class="text-[24px] mb-[16px] font-bold">{{ detailInfo.title }}</div>
        <div class="text-gray flex items-center text-[12px] mb-[40px]" v-if="detailInfo.publishDate">
          <div class="mr-[40px]"><Icon type="icon-yingyeshijian" class="mr-[2px]"></Icon>{{ formatTimeDate(detailInfo.publishDate) }}</div>
        </div>
        <div v-html="detailInfo.info" class="px-[60px] box-border re-reset"></div>
      </div>
      <el-empty v-else-if="!loading" :image-size="200" />
    </div>
  </div>
</template>

<script setup>
import Breadcrumb from '@/pc/components/breadcrumb/index.vue'
import { getNoticeDetail } from '@/apis/notice'
import { formatTime } from '@/common/js/date'

const breadcrumbList = reactive([
  {
    path: '/mall',
    name: { zh: '在线商城', en: 'Online Store' },
  },
  {
    path: '/notice',
    name: { zh: '通知公告', en: 'Notice' },
  },
  {
    name: { zh: '通知公告详情', en: 'Notice Details' },
  },
])

const detailInfo = ref(null)
const loading = ref(false)
const route = useRoute()

const getDetail = async () => {
  loading.value = true
  try {
    detailInfo.value = await getNoticeDetail({ id: route.params.id })
    loading.value = false
  } catch (e) {
    console.log(e)
    loading.value = false
  }
}

const formatTimeDate = (date) => formatTime(date, 'YYYY-MM-DD')

getDetail()
</script>

<style scoped lang="scss">
.main-wrap {
  min-height: $main-height;
  overflow: hidden;
}
</style>
