<template>
  <div class="card-item flex px-[20px] py-[12px] bg-white cursor-pointer border-box" @click="handleClick">
    <slot name="left" :row="itemInfo"></slot>

    <div class="flex flex-col flex-1 justify-between">
      <div>
        <div class="text-ellipsis text-[18px] large-title font-bold mb-[12px]">{{ itemInfo.title || '-' }}</div>
        <div class="text-[#666] text-ellipsis-2">{{ itemInfo.content || '-' }}</div>
      </div>
      <slot name="cardBottom" :row="itemInfo"></slot>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  itemInfo: {
    type: Object,
    default: () => ({}),
  },
})

const emits = defineEmits(['handleItemClick'])
const handleClick = () => {
  emits('handleItemClick', props.itemInfo.id)
}
</script>

<style scoped lang="scss">
.notice-wrap {
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.9);
  box-sizing: border-box;
  border: 1px solid #fff;
  padding: 24px 32px 32px;
  margin-top: 24px;
}

.date-text {
  font-family: D-DIN;
  letter-spacing: -0.5px;
}

.title-btn {
  border-bottom: 4px solid $primary-color;
  color: $primary-color;
  line-height: 70px;
}

.title-wrap {
  border-bottom: 1px solid $primary-color;
}

.right-btn {
  color: $primary-color;
  cursor: pointer;
}

.card-item {
  &:hover {
    .large-title {
      color: $primary-color;
    }
  }
}
</style>
