<template>
  <div class="main-wrap w-1260">
    <Breadcrumb :breadcrumbList="breadcrumbList" />
    <el-form inline class="mb-[16px] px-[16px] pt-[16px] bg-white rounded-[8px]" :model="searchForm" @submit.prevent="searchClick">
      <el-form-item label="发布时间">
        <el-date-picker v-model="searchForm.date" type="daterange" range-separator="~" start-placeholder="开始时间" end-placeholder="结束时间" clearable>
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-input v-model.trim="searchForm.search" :placeholder="t('searchPlaceholder')" class="min-w-[240px]" clearable>
          <template #prefix>
            <Icon type="icon-sousuo" class="text-[16px] ml-[4px]"></Icon>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" native-type="submit" class="sub-btn">{{ t('search') }}</el-button>
      </el-form-item>
    </el-form>

    <div class="bg-white p-4 mb-[40px]">
      <div class="card-list">
        <NoticeCard v-for="item in tableData" :key="item.id" :itemInfo="item" @handleItemClick="handleItemClick">
          <template #left="{ row }">
            <div class="flex h-[122px]">
              <img-loader class="mr-[20px] w-[240px] h-full object-cover" error-img="/mall/errorImg.png" :src="row.logo || ''" alt="" />
            </div>
          </template>
          <template #cardBottom="{ row }">
            <div class="text-gray flex items-center text-[12px]" v-if="row.publishDate">
              <div class="mr-[40px]"><Icon type="icon-yingyeshijian" class="mr-[2px]"></Icon>发文时间：{{ formatTime(row.publishDate, 'YYYY-MM-DD') }}</div>
            </div>
          </template>
        </NoticeCard>
      </div>
      <el-empty v-if="tableData.length === 0 && !loading"></el-empty>
      <div class="pagination-wrapper" v-if="total > pageSize">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :background="true"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
          <span>前往</span>
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import Breadcrumb from '@/pc/components/breadcrumb/index.vue'
import NoticeCard from './components/notice-card.vue'
import { getNoticeList } from '@/apis/notice'
import { formatTime } from '@/common/js/date'
import { debounce } from '@/common/js/util'

const { t } = useI18n({
  messages: {
    zh: {
      searchPlaceholder: '请输入关键词',
      search: '查询',
      reset: '重置',
    },
    en: {
      searchPlaceholder: 'Please enter Keywords',
      search: 'Search',
      reset: 'Reset',
    },
  },
})

const breadcrumbList = reactive([
  {
    path: '/mall',
    name: { zh: '在线商城', en: 'Online Store' },
  },
  {
    name: { zh: '通知公告', en: 'Notice' },
  },
])

const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const router = useRouter()
const searchForm = reactive({
  date: [],
  search: '',
})

const handleItemClick = (id) => {
  router.push({
    name: 'noticeDetail',
    params: {
      id,
    },
  })
}

// 获取列表 - 防抖包装
const DEBOUNCE_TIME = 300
const debounceGetData = debounce(() => {
  getList()
}, DEBOUNCE_TIME)

// 搜索
const searchClick = () => {
  currentPage.value = 1
  debounceGetData()
}

//分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getList()
}
const handleCurrentChange = (val) => {
  currentPage.value = val
  getList()
}

const getList = async () => {
  loading.value = true
  const date = searchForm.date || []
  try {
    const body = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      title: searchForm.search,
      startDate: date?.[0] ? new Date(date[0]).getTime() : null,
      endDate: date?.[1] ? new Date(date[1]).getTime() : null,
    }
    const { totalRecord, rowList } = await getNoticeList(body)
    tableData.value = rowList
    total.value = totalRecord
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}
getList()
</script>

<style scoped lang="scss">
.main-wrap {
  min-height: $main-height;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 12px;
}
</style>
