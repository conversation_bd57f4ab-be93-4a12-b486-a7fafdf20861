<template>
  <div class="serve-card rounded-[8px] bg-[#fff] w-[410px] h-[180px] px-[16px] py-[16px] flex flex-col justify-between cursor-pointer" @click="handleClick">
    <div class="flex items-center h-[88px] overflow-hidden">
      <div class="w-[88px] h-[88px] mr-[16px] shrink-0">
        <img-loader
          :src="`${goodsPic}?x-oss-process=image/resize,h_300`"
          alt="暂无图片"
          class="w-full h-full object-cover"
          loadingImg="/mall/errorImg.png"
          errorImg="/mall/errorImg.png"
        />
      </div>
      <div class="flex flex-col justify-between h-[100%]">
        <div class="ellipsis text-[20px] font-600 mb-[4px]">{{ serveInfo.serviceProviderName }}</div>
        <div class="flex justify-between items-center overflow-hidden">
          <div class="text-[22px] color-[#D8131A] shrink-0 mr-[8px]"><span>¥</span>{{ serveInfo.price }}</div>
          <!--          <div class="px-[4px] py-[2px] bg-[#FBE7E8] color-[#D8131A] tag-text">标签的我的我</div>-->
        </div>
      </div>
    </div>
    <div class="color-[#999] text-[14px] ellipsis h-[40px]">
      {{ serveInfo.brightSpot }}
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  serveInfo: {
    type: Object,
    default: () => ({}),
  },
})

const goodsPic = computed(() => props.serveInfo?.picture?.[0] || '')
const router = useRouter()
const handleClick = () => {
  const { href } = router.resolve({
    name: 'serveDetail',
    params: {
      id: props.serveInfo?.id,
    },
  })
  window.open(href)
}
</script>

<style lang="scss" scoped>
.ellipsis {
  @include ellipsis(2);
}

.tag-text {
  max-width: 70%;
  @include ellipsis(1);
}
</style>
