<template>
  <div :class="['serve-list', { 'no-data': serveList.length === 0 }]">
    <ServeCard v-for="item in serveList" :key="item.id" :serveInfo="item" />
    <el-empty v-if="showEmpty && serveList.length === 0 && !loading" :image-size="200" />
  </div>
  <div v-if="loading" class="py-[24px] text-center color-[#999] text-[24px] w-[100%]">
    <el-icon class="loading-icon"><Loading /></el-icon>
  </div>
</template>

<script setup>
import { Loading } from '@element-plus/icons-vue'
import ServeCard from '../serve-card/index.vue'

defineProps({
  serveList: {
    type: Array,
    default: () => [],
  },
  showEmpty: {
    type: Boolean,
    default: false,
  },
  loading: {
    type: Boolean,
    default: false,
  },
})
</script>

<style scoped lang="scss">
.serve-list {
  // overflow: auto;
  width: $main-width;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  margin: 0 auto 24px;

  &.no-data {
    display: flex;
    justify-content: center;
  }
}

.loading-icon {
  animation: spin 1s linear infinite; /* 动画属性：1秒转一圈，线性，循环无限次 */
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
