<style lang="scss" scoped>
.code-warp {
  width: 176px;
  height: 176px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-size: cover;
  background-image: url('https://static.chinamarket.cn/static/trade-exhibition/code-warp.png');
  .qrcode {
    width: 160px;
    height: 160px;
  }
}
</style>
<template>
  <Dialog v-model="isShowLogin" width="260">
    <template #default>
      <div class="p-4 rounded-1 text-center" style="background-image: linear-gradient(0deg, #faf5f5 0%, #ffeded 100%)">
        <div class="relative mb-1 text-white text-16px font-semibold bg-[#D8131A] h-[30px] w-[126px] m-auto leading-30px rounded-20px">
          {{ $t('service') }}
          <div class="w-[12px] h-[12px] bg-[#D8131A] absolute left-0 right-0 bottom-[-4px] m-auto" style="transform: rotate(-45deg)"></div>
        </div>
        <div class="code-warp m-auto">
          <img class="qrcode" src="https://static.chinamarket.cn/static/trade-exhibition/wechat-work-qrcode.png" alt="" />
        </div>
        <Icon class="cursor-pointer mt-1" type="icon-guanbi" :size="16" @click="isShowLogin = false" />
      </div>
    </template>
  </Dialog>
</template>

<script setup>
import Dialog from '@/pc/components/dialog/dialog.vue'

const isShowLogin = ref(false)
const init = () => {
  isShowLogin.value = true
}
defineExpose({ init })
</script>
