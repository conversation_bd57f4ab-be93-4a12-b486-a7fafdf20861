<template>
  <div class="banner-wrapper relative min-h-[640px]">
    <div class="banner-text-wrapper absolute top-[40%] left-[0px] z-2 w-[100%]">
      <div class="w-1260">
        <div class="banner-text font-600 text-[56px] text-white">
          <div>服务商招募中</div>
          <div>抢先入驻享特权</div>
        </div>
        <div class="btn text-center" @click.stop="handleJoinClick">{{ t('shopJoin.buttonText') }}</div>
      </div>
    </div>
    <swiper
      v-if="imgList.length"
      :loop="true"
      :autoplay="{
        delay: 2500,
        disableOnInteraction: false,
      }"
      :pagination="true"
      :modules="modules"
      class="w-[100%] h-[100%] bg-[#fff]"
    >
      <swiper-slide v-for="(item, i) in imgList" :key="i" class="h-[100%]">
        <div class="w-[100%] h-[100%] flex justify-center items-center banner-item relative">
          <img-loader :src="item" :loading-img="`${item}?x-oss-process=image/resize,h_100`" img-class="largeImg" class="largeImg"></img-loader>
        </div>
      </swiper-slide>
    </swiper>
    <div class="tabs flex justify-center w-1260 h-74px bg-white w-1260px flex rounded-8px overflow-hidden">
      <div
        class="tab flex flex-1 items-center justify-center px-16px cursor-pointer text-23px font-500"
        :class="{ active: activeTab === i + 1, small: $storageLocale !== 'zh' }"
        v-for="(item, i) in tabs"
        :key="i"
        @click="tabClick(i + 1)"
      >
        <Icon class="mr-4px" :type="item.icon" :size="$storageLocale !== 'zh' ? 26 : 20"></Icon><span class="ml-10px">{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Autoplay } from 'swiper/modules'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { useI18n } from 'vue-i18n'

const props = defineProps({
  currentTab: {
    type: Number,
    default: 1,
  },
})
const activeTab = ref(1)

watch(
  () => props.currentTab,
  (newTab) => {
    activeTab.value = +newTab || 1 // 更新 currentTab 的值
  },
)

const modules = [Autoplay]

const { t } = useI18n({
  messages: {
    zh: {
      shopJoin: {
        buttonText: '我要报名入驻',
      },
      userTypeToast: '请先退出登录再报名入驻',
    },
    en: {
      shopJoin: {
        buttonText: 'Sign Up',
      },
      userTypeToast: 'Identity Mismatch',
    },
  },
})

const imgList = ['/outside-trade-serve/super-service/super-service-bg.jpg']

const handleJoinClick = () => {
  window.open(`${import.meta.env.VUE_APP_WEB_URL_ZH}/service-center`, '_blank')
}

const tabs = ref([
  {
    icon: 'icon-guanjianhuishui',
    name: '关检汇税',
  },
  {
    icon: 'icon-guojiwuliu',
    name: '国际物流',
  },
  {
    icon: 'icon-shangchenghaiwaicang',
    name: '商城海外仓',
  },
  {
    icon: 'icon-guojihuizhan',
    name: '国际展会',
  },
  {
    icon: 'icon-dichengbenrongzi',
    name: '低成本融资',
  },
])

const emits = defineEmits(['tabClick'])
const tabClick = (i) => {
  emits('tabClick', i)
}
</script>

<style lang="scss" scoped>
.banner-wrapper {
  position: relative;
  width: 100%;
  height: 100vh;
  min-height: 640px;
  .tabs {
    position: absolute;
    bottom: 20px;
    left: 50%;
    margin-left: -630px;
    z-index: 9;
    background: #fff6;
    border: 1px solid #fff;
  }
  .tab {
    color: #fff;
    &.active {
      color: #d8131a;
      background: #fff9;
    }

    &.small {
      font-size: 17px;
    }
  }
  .banner-text {
    margin-bottom: 32px;

    .red-text {
      font-size: 28px;
      width: max-content;
      font-family: PingFang SC;
      font-weight: 600;
      line-height: normal;
      background: linear-gradient(270deg, #d8131a 0%, #e96f18 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }
  }

  .btn {
    min-width: 152px;
    width: max-content;
    height: 42px;
    border-radius: 4px;
    padding: 10px 24px;
    background: linear-gradient(270deg, #d8131a 0%, #fd441f 100%);
    font-size: 16px;
    font-weight: 600;
    color: $basic-white;
    cursor: pointer;
  }
}

.banner-item {
  .img-loader {
    width: 100%;
    height: 100%;
    object-fit: cover; /* 保持图片的宽高比 */
    object-position: center top; /* 图片居中 */

    :deep(img) {
      width: 100%;
      height: 100%;
      object-fit: cover; /* 保持图片的宽高比 */
      object-position: center top; /* 图片居中 */
    }
  }
}

:deep(.swiper-wrapper) {
  height: 100%;
}

@media screen and (max-width: 1480px) {
  .banner-text {
    font-size: 44px;
  }
}
</style>
