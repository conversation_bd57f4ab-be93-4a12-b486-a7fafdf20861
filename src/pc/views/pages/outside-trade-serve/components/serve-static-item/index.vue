<template>
  <div class="flex mb-[40px] justify-between">
    <div class="min-h-[300px] w-[600px] relative rounded-[8px] overflow-hidden shrink-0">
      <img-loader :src="serveInfo.logo" class="w-[600px] h-[100%] min-h-[300px]" />
      <div :class="`serve-tag flex justify-center items-center`" :style="{ backgroundColor: serveInfo.bgClass }">{{ serveInfo.serveType }}</div>
    </div>
    <div class="min-h-[300px] w-[620px] flex flex-col justify-between">
      <div :class="`${['en', 'thai', 'indonesian'].includes($storageLocale) ? 'text-[14px]' : 'text-[16px]'} mb-[12px] text-#333333`">
        <span class="text-[-20px] font-600">{{ serveInfo.companyName }}</span> {{ serveInfo.companyDetails }}
      </div>
      <div class="flex">
        <div class="w-[50%] shrink-0 pd-[24px]">
          <div class="tag-title">业务介绍</div>
          <ul :class="`${['thai', 'indonesian'].includes($storageLocale) ? 'text-[12px]' : 'text-[14px]'} tag-ul text-[#666]`">
            <li
              v-for="item in serveInfo.serveIntroduce"
              :class="`${['en', 'thai', 'indonesian'].includes($storageLocale) ? 'mb-[3px]' : 'mb-[10px]'}`"
              :key="item"
            >
              {{ item }}
            </li>
          </ul>
        </div>
        <div class="w-[50%] shrink-0">
          <div class="tag-title">服务优势</div>
          <ul :class="`${['thai', 'indonesian'].includes($storageLocale) ? 'text-[12px]' : 'text-[14px]'} tag-ul text-[#666]`">
            <li
              v-for="item in serveInfo.serveFeatures"
              :class="`${['en', 'thai', 'indonesian'].includes($storageLocale) ? 'mb-[3px]' : 'mb-[10px]'}`"
              :key="item"
            >
              {{ item }}
            </li>
          </ul>
        </div>
      </div>
      <div class="static-btn mt-[12px]" @click="handleShowCustomer">联系客服</div>
    </div>
  </div>
</template>
<script setup>
defineProps({
  serveInfo: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['handleShowCustomer'])
const handleShowCustomer = () => {
  emit('handleShowCustomer')
}
</script>

<style scoped lang="scss">
.tag-title {
  width: max-content;
  font-size: 14px;
  padding: 2px 8px;
  background: linear-gradient(90deg, #6c5354 0%, #4a4e66 100%);
  color: #edd1a8;
  margin-bottom: 10px;
}

.static-btn {
  width: max-content;
  color: $primary-color;
  font-size: 12px;
  padding: 0 4px;
  border-radius: 2px;
  border: 1px solid $primary-color;
  cursor: pointer;
}

.serve-tag {
  position: absolute;
  top: 0;
  left: 0;
  min-width: 108px;
  height: 32px;
  border-radius: 0 0 8px 0;
  padding: 0 12px;
  z-index: 1;
  font-size: 16px;
  font-weight: bold;
  color: #fff;
}
</style>
