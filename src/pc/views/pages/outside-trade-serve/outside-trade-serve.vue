<template>
  <div class="serve-wrap bg-[#ffffff]">
    <!-- banner -->
    <Banner :currentTab="currentTab" @tabClick="tabClick" />

    <div v-if="true">
      <!-- 关检汇税 -->
      <tariffCheck v-if="currentTab === 1" />
      <!-- 国际物流 -->
      <internationalFlow v-if="currentTab === 2" />
      <!-- 商城海外仓 -->
      <shopOverseeHouse v-if="currentTab === 3" />
      <!-- 国际展会 -->
      <internationalExhibition v-if="currentTab === 4" />
      <!-- 低成本融资 -->
      <lowerFinance v-if="currentTab === 5" />
    </div>

    <!-- 无数据 -->
    <div class="w-1260" v-else>
      <div class="text-center tab-wrap relative mt-[-24px] mb-[24px] rounded-[8px] px-[4px] py-[4px]">
        <el-segmented v-model="severId" :options="options" size="large" class="max-w-[100%] rounded-[8px] bg-[#fff]" @change="handleServeChange" />
        <div class="flex items-center pt-[16px] px-[24px]" v-if="level2.length">
          <div
            v-for="item in level2"
            :key="item.id"
            :class="`mr-[24px] mb-[12px] text-[14px] color-[#999] cursor-pointer category-item ${item.id === activeId ? 'active' : ''}`"
            @click="handleItemChange(item.id)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>

      <serve-list :serve-list="severList" showEmpty :loading="isLoading" />
      <div v-if="severList.length && isEnd" class="text-center color-[#999] mb-[24px]">- {{ t('noMore') }} -</div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
import Banner from './components/banner.vue'
import ServeList from './components/serve-list/index.vue'
import internationalExhibition from './tab-components/international-exhibition/international-exhibition.vue'
import internationalFlow from './tab-components/international-flow/international-flow.vue'
import lowerFinance from './tab-components/lower-finance/lower-finance.vue'
import shopOverseeHouse from './tab-components/shop-oversee-house/shop-oversee-house.vue'

/* tab Components */
import tariffCheck from './tab-components/tariff-check/tariff-check.vue'
import * as API from '@/apis/outside-trade-serve'

const { t } = useI18n({
  messages: {
    zh: {
      otherProducts: '其他商品',
      noMore: '没有更多',
    },
    en: {
      otherProducts: 'Other Products',
      noMore: 'No More',
    },
  },
})

const route = useRoute()
const currentTab = ref(1)

const tabClick = (i) => {
  currentTab.value = i
}

watch(
  () => route.query?.tab,
  (newTab) => {
    currentTab.value = +newTab || 1 // 更新 currentTab 的值
  },
)

onMounted(() => {
  currentTab.value = +route.query?.tab
})

// const route = useRoute()
const severId = ref(null)
const activeId = ref(null)
const options = ref([])
const optionsLevel2 = ref(null)
const level2 = computed(() => {
  return optionsLevel2.value?.[severId.value] || []
})

// const getCategoryOptions = async () => {
//   const data = (await API.getServiceType()) || []
//   options.value = data.map((item) => ({
//     label: item.name,
//     value: item.id,
//   }))
//   // 暂无二级
//   // optionsLevel2.value = data.reduce((prev, cur) => {
//   //   prev[cur.id] = cur.children
//   //   return prev
//   // }, {})
//   severId.value = +route.query.serviceTypeId || data?.[0]?.id
// }

const handleServeChange = () => {
  activeId.value = null
  queryList()
}

const queryList = () => {
  pagination.pageNum = 1
  isEnd.value = false
  getSeverList()
}

const severList = ref([])
const isLoading = ref(false)
const isEnd = ref(true)
const pagination = reactive({
  pageNum: 1,
  pageSize: 20,
})
const getSeverList = async () => {
  if (isEnd.value) return
  isLoading.value = true
  try {
    const res = await API.getWebServiceList({
      serviceTypeId: severId.value,
      ...pagination,
    })
    const { rowList, totalPage } = res
    if (pagination.pageNum === 1) {
      severList.value.length = 0
    }
    severList.value.push(...rowList)
    isLoading.value = false
    if (totalPage > pagination.pageNum) {
      Object.assign(pagination, {
        pageNum: pagination.pageNum + 1,
      })
    } else {
      isEnd.value = true
    }
  } catch (e) {
    console.log(e)
    isLoading.value = false
  }
}

const handleItemChange = (id) => {
  activeId.value = id
}

// const doScroll = () => {
//   const scrollTop = window.scrollY || document.documentElement.scrollTop || document.body.scrollTop
//   const scrollHeight = window.scrollHeight || document.documentElement.scrollHeight || document.body.scrollHeight
//   const clientHeight = window.clientHeight || document.documentElement.clientHeight || document.body.clientHeight
//
//   if (scrollTop + clientHeight >= scrollHeight - 280) {
//     !isLoading.value && getSeverList()
//   }
// }
// onMounted(() => {
//   getCategoryOptions().then(() => {
//     queryList()
//   })
//   document.addEventListener('scroll', doScroll)
// })
//
// onUnmounted(() => {
//   document.removeEventListener('scroll', doScroll)
// })
</script>

<!-- 外贸综合服务二级页面 -->
<style lang="scss" scoped>
.serve-wrap {
  min-height: $main-height;
}

.tab-wrap .el-segmented {
  --el-segmented-bg-color: #f5f6f7;
  --el-segmented-item-selected-color: #fff;
  --el-segmented-item-selected-bg-color: linear-gradient(270deg, #d8131a 0%, #fd441f 100%);
  --el-border-radius-base: 4px;
  --el-segmented-item-padding: 12px;
}

:deep(.el-segmented) {
  padding: 4px;
}

:deep(.el-segmented__item) {
  min-width: 137px;
  height: 40px;
  padding: 0 16px;
  font-size: 14px;
  font-weight: 600;
}

.category-item {
  &.active {
    color: $primary-color;
  }
}
[dir='rtl'] {
  .ml-10px {
    margin-right: 10px;
    margin-left: 0;
  }
}
</style>
