<!--
 * @Author: 李兵 <EMAIL>
 * @Date: 2024-08-24 17:52:38
 * @LastEditors: 李兵 <EMAIL>
 * @LastEditTime: 2024-08-27 14:53:07
 * @FilePath: \trade-exhibition\src\views\pages\outside-trade-serve\pages\open-company\open-company.vue
 * @Description: 外贸综合服务---开设公司
-->
<style scoped lang="scss">
.open-company {
  min-width: $main-width;
  max-width: $main-width;
  margin: 0 auto;
  margin-bottom: 40px;
  .banner-wrap {
    width: 100%;
    height: 200px;
    margin-bottom: 24px;
    background-size: cover;
  }
  .company-register {
    padding: 24px;
    background: $basic-white;
    .trade-title {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 11px;
      line-height: 28px;
      position: relative;
      padding-left: 12px;
      color: $color-333333;
      &::before {
        content: '';
        display: inline-block;
        width: 6px;
        height: 20px;
        background: $primary-color;
        position: absolute;
        top: 0;
        bottom: 0;
        margin: auto;
        left: 0;
      }
    }
    .trade-content {
      @include flex-sb;
      .trade-type-item {
        background: #f5f6f9;
        padding: 20px;
        width: 900px;
        height: 85px;
        cursor: pointer;
        @include flex-vc;
      }
      .type-item-icon {
        width: 45px;
        height: 45px;
        margin-right: 12px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .type-item-title {
        font-size: 18px;
        color: $color-333333;
        font-weight: 600;
        line-height: 25px;
      }
      .type-item-des {
        color: $regular-text;
        font-size: 14px;
        line-height: 20px;
      }
    }
  }
  .right {
    width: 308px;
    margin-left: 12px;
    flex-shrink: 0;
    border-radius: 4px;
    overflow: hidden;
    .flow-title {
      height: 49px;
      background: #d8131a;
      font-size: 18px;
      color: $basic-white;
      line-height: 49px;
      font-weight: 500;
      padding: 0 24px;
    }
    .flow-list {
      padding: 24px;
      color: #333;
      background: #f5f6f9;
    }
    .flow-item {
      @include flex-sb;
      line-height: 20px;
      margin-bottom: 12px;
      &-title {
        display: inline-block;
        flex-shrink: 0;
        font-weight: 600;
      }
      &-des {
        display: inline-block;
        flex: 1;
      }
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>

<template>
  <div class="open-company">
    <!-- banner 图 -->
    <div class="banner-wrap" :style="{ backgroundImage: `url(${`${ossUrl}/outside-trade-serve/open-company_${$i18n.locale}.png`})` }"></div>
    <!-- 公司设立登记 -->
    <div class="company-register">
      <h3 class="trade-title">{{ $t('openCompany.title') }}</h3>
      <div class="trade-content">
        <div class="left">
          <ul>
            <li v-for="(item, index) in typeList" :key="index" class="trade-type-item" @click="openClick">
              <span class="type-item-icon"><img :src="companyIconUrl" alt="" /></span>
              <div>
                <h4 class="type-item-title">{{ item.title }}</h4>
                <p class="type-item-des">{{ item.des }}</p>
              </div>
            </li>
          </ul>
        </div>
        <div class="right">
          <dl>
            <dt class="flow-title">{{ flowData.title }}</dt>
            <div class="flow-list">
              <dd v-for="(item, index) in flowData.list" :key="index" class="flow-item" :class="{ 'flex-col': $i18n.locale === 'en' }">
                <span class="flow-item-title">{{ item.title }}：</span>
                <p class="flow-item-des">{{ item.des }}</p>
              </dd>
            </div>
          </dl>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { ossUrl } from '@/constants/common'

const { t } = useI18n()
const companyIconUrl = `${ossUrl}/outside-trade-serve/company-icon.png`

// 办事分类
const typeList = ref([
  {
    icon: '',
    title: computed(() => t('openCompany.title')),
    des: computed(() => t('openCompany.desc')),
  },
])

// 办事流程
const flowData = ref({
  title: computed(() => t('openCompany.process')),
  list: [
    {
      id: 1,
      title: computed(() => t('openCompany.window')),
      des: computed(() => t('openCompany.address1')),
    },
    {
      id: 2,
      title: computed(() => t('openCompany.contact')),
      des: '0539-2032100',
    },
    {
      id: 3,
      title: computed(() => t('openCompany.complain')),
      des: '0539-8226155',
    },
    {
      id: 4,
      title: computed(() => t('openCompany.address')),
      des: computed(() => t('openCompany.address2')),
    },
  ],
})
const openClick = () => {
  window.open(
    'https://lyzwfw.sd.gov.cn/lys/icity/chain/business?flowId=C689A8D691D7499984CF3455B5713991&flowName=%E6%88%91%E8%A6%81%E5%BC%80%E5%87%BA%E5%8F%A3%E8%B4%B8%E6%98%93%E5%85%AC%E5%8F%B8',
    '_blank',
  )
}
</script>
