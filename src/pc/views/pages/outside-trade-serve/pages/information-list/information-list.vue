<template>
  <div class="information-list">
    <div class="info-wrapper" v-if="data.length > 0">
      <div class="item cursor-pointer" v-for="item in data" :key="item.id" @click="goPage(item.url)">
        <div class="title">{{ item.title }}</div>
        <div class="content" v-html="item.summary"></div>
        <div>
          <span class="origin">{{ $t('article.origin') }}：{{ item.publishPerson }}</span>
          <span class="time">{{ $t('article.datetime') }}：{{ item.publishTime }}</span>
        </div>
      </div>

      <div class="footer">
        <el-pagination
          background
          v-model:current-page="pagination.currentPage"
          :page-size="pagination.pageSize"
          layout="total, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script setup>
import * as API from '@/apis/outside-trade-serve'
import { useEvent } from '@/event'
import { LANG_CHANGED } from '@/event/modules/site'
import { debounce } from '@/common/js/util'
import { useRedirect } from '@/pc/views/pages/redirect/hook'

const event = useEvent()

const DEBOUNCE_TIME = 300
// 获取列表数据防抖函数
const deBounceGetData = debounce(() => {
  getData()
}, DEBOUNCE_TIME)

// 分页条数变化
const handleSizeChange = (val) => {
  pagination.value.pageSize = val
  pagination.value.pageNum = 1
  deBounceGetData()
}
// 分页页数变化
const handleCurrentChange = (val) => {
  pagination.value.pageNum = val
  deBounceGetData()
}

const menu = ref({
  FOREIGN_INFO: {
    name: '外贸资讯',
    apiName: 'getForeignTradeInfo',
  },
  EXTERNAL_RESOURCE: {
    name: '内外资源融合',
    apiName: 'getExternalResource',
  },
  TAXATION: {
    name: '税务协调',
    apiName: 'getTaxInfo',
  },
  BUY_VISA: {
    name: '采购商签证',
    apiName: 'getVisaInfo',
  },
  LOCAL_TRADE: {
    name: '地方外贸资讯',
    apiName: 'getLocalInfo',
  },
  FOREX_TRADING: {
    name: '外汇交易',
    apiName: 'getforexTrade',
  },
  FINANCIAL_MATTER: {
    name: '财会事项',
    apiName: 'getFinanceInfo',
  },
})

// 分页
const pagination = ref({
  pageNum: 1,
  pageSize: 10,
  total: 0,
})

const apiName = ref()

// 数据
const data = reactive([])
const getData = async () => {
  const body = {
    pageNum: pagination.value.pageNum,
    pageSize: pagination.value.pageSize,
  }

  let res = await API[apiName.value](body)

  const { rowList, totalRecord } = res
  data.length = 0
  data.push(...rowList)
  pagination.value.total = totalRecord
}

const redirect = useRedirect()
const goPage = (url) => {
  redirect.to(url)
}

onMounted(async () => {
  const route = useRoute()
  let pageName = route.query.type
  apiName.value = menu.value[pageName].apiName
  getData()
})

event.on(LANG_CHANGED, async () => {
  await getData()
})
</script>

<style lang="scss" scoped>
.information-list {
  max-width: 1260px;
  margin: 0 auto;

  .info-wrapper {
    padding: 8px 150px 20px;
    margin-bottom: 20px;

    background: $basic-white;
  }

  .item {
    padding: 12px 0;
    border-bottom: 1px solid #edf0f5;
  }
  .title {
    font-size: 18px;
    color: $primary-color;
  }
  .content {
    color: $regular-text;
    margin: 12px 0;
  }
  .origin {
    color: #257bfb;
  }
  .time {
    margin: 12px 0 0 12px;
    color: $color-999999;
  }
  .footer {
    margin-top: 20px;
    display: flex;
    justify-content: center;

    .el-pagination.is-background .btn-next,
    .el-pagination.is-background .btn-prev,
    .el-pagination.is-background .el-pager li {
      background-color: #795548;
      margin: 0 4px;
    }
  }
}
</style>
