<!--
 * @Author: 李兵 <EMAIL>
 * @Date: 2024-08-24 13:37:13
 * @LastEditors: 李兵 <EMAIL>
 * @LastEditTime: 2024-08-27 17:28:27
 * @FilePath: \trade-exhibition\src\views\pages\outside-trade-serve\pages\customs-introduce\customs-introduce.vue
 * @Description: 外贸综合服务---海关介绍
-->
<style scoped lang="scss">
.customs-introduce {
  min-width: $main-width;
  max-width: $main-width;
  margin: 0 auto;
  margin-bottom: 40px;
  .banner-wrap {
    width: 100%;
    height: 200px;
    margin-bottom: 24px;
    background-size: cover;
  }
  .trade-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 11px;
    line-height: 28px;
    position: relative;
    padding-left: 12px;
    &::before {
      content: '';
      display: inline-block;
      width: 6px;
      height: 20px;
      background: $primary-color;
      position: absolute;
      top: 0;
      bottom: 0;
      margin: auto;
      left: 0;
    }
  }
  .customs-table-wrap {
    background: $basic-white;
    padding: 24px;
    table {
      border-collapse: collapse;
      table-layout: fixed;
    }

    table,
    th,
    td {
      border: 1px solid #edeef1;
    }
    th,
    td {
      padding: 10px;
    }
    th {
      font-size: 16px;
      color: $color-333333;
      background: #fafafa;
      line-height: 25px;
      height: 25px;
    }
    td {
      font-size: 14px;
      color: $regular-text;
      line-height: 20px;
      &.center {
        text-align: center;
      }
      &.address {
        padding-left: 32px;
      }
    }
    .organ-roster {
      font-size: 14px;
      height: 20px;
      line-height: 20px;
      color: #257bfb;
      margin-top: 24px;
      @include flex-vc;
      padding-bottom: 2px;
      width: fit-content;
      border-bottom: 1px solid #257bfb;
    }
  }
}
</style>

<template>
  <div class="customs-introduce">
    <!-- banner 图 -->
    <div class="banner-wrap" :style="{ backgroundImage: `url(${`${ossUrl}/outside-trade-serve/customs-introduce_${$i18n.locale}.png`})` }"></div>
    <!-- 临沂海关 -->
    <div class="customs-table-wrap">
      <h3 class="trade-title">{{ $t('customsIntroduce.title') }}</h3>
      <table class="table">
        <tr>
          <th width="152px">{{ $t('customsIntroduce.tableH.col1') }}</th>
          <th width="200px">{{ $t('customsIntroduce.tableH.col2') }}</th>
          <th width="533px">{{ $t('customsIntroduce.tableH.col3') }}</th>
          <th width="110px">{{ $t('customsIntroduce.tableH.col4') }}</th>
          <th width="220px">{{ $t('customsIntroduce.tableH.col5') }}</th>
        </tr>
        <tr>
          <td rowspan="4">
            {{ $t('customsIntroduce.tableC.col1[0]') }}<br />
            {{ $t('customsIntroduce.tableC.col1[1]') }}
          </td>
          <td rowspan="4" class="address">
            {{ $t('customsIntroduce.tableC.col2[0]') }}<br />
            {{ $t('customsIntroduce.tableC.col2[1]') }} <br />
            {{ $t('customsIntroduce.tableC.col2[2]') }}
          </td>
          <td rowspan="4">
            {{ $t('customsIntroduce.tableC.col3[0]') }}<br />
            {{ $t('customsIntroduce.tableC.col3[1]') }}<br />
            {{ $t('customsIntroduce.tableC.col3[2]') }}<br />
            {{ $t('customsIntroduce.tableC.col3[3]') }}<br />
            {{ $t('customsIntroduce.tableC.col3[4]') }}<br />
            {{ $t('customsIntroduce.tableC.col3[5]') }}
          </td>
          <td rowspan="4">
            {{ $t('customsIntroduce.tableC.col4') }}<br />
            8:30-12:00<br />
            14:00-17:00
          </td>
          <td class="center">0539-7015535</td>
        </tr>
        <tr>
          <td class="center">0539-7015536</td>
        </tr>
        <tr>
          <td class="center">0539-7015537</td>
        </tr>
        <tr>
          <td class="center">0539-7015539</td>
        </tr>
      </table>
      <!-- 机构名单 -->
      <div>
        <a :href="`${ossUrl}/临沂海关办事机构信息.docx`" class="organ-roster" download="临沂海关办事机构信息.docx">
          <icon class="mr-1" type="icon-a-iconxinxi" size="16"></icon>
          {{ $t('customsIntroduce.file') }}
        </a>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ossUrl } from '@/constants/common'
</script>
