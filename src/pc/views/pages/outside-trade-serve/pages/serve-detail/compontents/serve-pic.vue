<template>
  <div v-if="detailInfo">
    <div class="main-pic-wrap">
      <el-image
        :src="activePreview"
        fit="cover"
        class="w-[100%] h-[100%]"
        :preview-src-list="urlList"
        hide-on-click-modal
        :initial-index="initialIndex"
      ></el-image>
    </div>

    <div class="spec-wrap">
      <button ref="prevButton" class="spec-button">
        <el-icon><CaretLeft /></el-icon>
      </button>
      <swiper
        direction="horizontal"
        class="spec-list"
        :cssMode="true"
        :navigation="{ prevEl: prevButton, nextEl: nextButton }"
        :mousewheel="true"
        :modules="modules"
      >
        <swiper-slide v-for="(itemList, i) in imgList" :key="i">
          <div class="spec-item">
            <img
              v-for="(item, i1) in itemList"
              :key="i1"
              @mouseenter="handleMouseenter(item)"
              :class="['spec-item-pic', activePreview === item ? 'active' : '']"
              :src="item"
              alt=""
            />
          </div>
        </swiper-slide>
      </swiper>
      <button ref="nextButton" class="spec-button">
        <el-icon><CaretRight /></el-icon>
      </button>
    </div>
  </div>
  <ElImageViewer v-if="visible" :urlList="urlList" :z-index="99" :initialIndex="initialIndex" hideOnClickModal teleported @close="visible = false" />
</template>
<script setup>
import { CaretLeft, CaretRight } from '@element-plus/icons-vue'
import { ElImageViewer } from 'element-plus'
// Import Swiper styles
import 'swiper/css'
import 'swiper/css/navigation'
// import required modules
import { Mousewheel, Navigation } from 'swiper/modules'
import { Swiper, SwiperSlide } from 'swiper/vue'

const props = defineProps({
  detailInfo: {
    type: Object,
    default: () => ({}),
  },
})

const modules = [Navigation, Mousewheel]
const prevButton = ref(null)
const nextButton = ref(null)
const visible = ref(false)
const splitLen = 5
const imgList = computed(() => {
  const picture = props.detailInfo?.picture || []
  const newArrLen = Math.ceil(picture.length / splitLen)
  const newArr = []
  for (let i = 0; i < newArrLen; i++) {
    newArr[i] = picture.slice(i * splitLen, (i + 1) * splitLen)
  }
  return newArr
})
const urlList = computed(() => props.detailInfo?.picture || [])
const activeUrl = ref('')
const activePreview = computed(() => {
  return activeUrl.value || urlList.value[0]
})
const initialIndex = computed(() => urlList.value.indexOf(activePreview.value))

const handleMouseenter = (item) => {
  activeUrl.value = item
}
</script>

<style lang="scss" scoped>
.main-pic-wrap,
.main-video-wrap {
  width: 100%;
  height: 500px;
  margin-bottom: 16px;
  position: relative;

  .preview-btn {
    width: 30px;
    line-height: 30px;
    text-align: center;
    position: absolute;
    bottom: 0;
    right: 0;
    z-index: 1;
    font-size: 16px;
    color: #fff;
    background: #8a8a8a;
    cursor: pointer;
  }
}

.spec-wrap {
  display: flex;
  justify-content: space-between;
  width: 500px;
  height: 78px;
  overflow: hidden;
  background: #fff;

  .spec-button {
    display: block;
    flex-shrink: 0;
    width: 19px;
    height: 100%;
    background: #f9f9f9;
    cursor: pointer;
  }

  :deep(.swiper-button-disabled) {
    cursor: not-allowed;
  }
}

.spec-list {
  width: 450px;
  height: 100%;

  .spec-item {
    display: flex;
    height: 100%;

    &-pic {
      width: 78px;
      height: 78px;
      margin: 0 6px;
      border: 1px solid transparent;
      box-sizing: border-box;

      &.active {
        border-color: #d8131a;
      }
    }
  }
}

[dir='rtl'] {
  .main-pic-wrap,
  .main-video-wrap {
    .preview-btn {
      right: auto;
      left: 0;
    }
  }

  .spec-wrap {
    .spec-button {
      transform: rotateZ(-180deg);
    }
  }
}
</style>
