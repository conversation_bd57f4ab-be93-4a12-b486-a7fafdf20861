<template>
  <div class="content-wrap content" v-if="detailInfo">
    <div class="content-title">{{ detailInfo.serviceProviderName }}</div>
    <div class="content-detail">
      <div class="content-row mb-[16px]">
        <div class="content-row-label">{{ t('price') }}:&nbsp;</div>
        <div class="content-row-value text-[24px] color-[#D8131A]">{{ detailInfo.price }}</div>
      </div>
    </div>
    <div class="content-attr">
      <div class="flex">
        <div class="mr-[4px] shrink-0 color-[#999] min-w-[30px]">{{ t('features') }}:</div>
        <div>{{ detailInfo.brightSpot }}</div>
      </div>
    </div>
    <div class="content-footer">
      <el-button class="content-btn" @click="handleReceive">{{ t('contactSupport') }}</el-button>
    </div>
    <Customer ref="customerRef" />
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import Customer from '@/pc/components/customer/customer.vue'
import { useUserStore } from '@/pc/stores'
import { useEvent } from '@/event'
import { OPEN_NEW_LOGIN } from '@/event/modules/site'

const { t } = useI18n({
  messages: {
    zh: {
      price: '价格',
      contactSupport: '联系客服',
      features: '亮点',
    },
    en: {
      price: 'Price',
      contactSupport: 'Contact Support',
      features: 'Features',
    },
  },
})

defineProps({
  detailInfo: {
    type: Object,
    default: () => ({}),
  },
})
const customerRef = ref(null)
const userStore = useUserStore()
const event = useEvent()
const contactCustomer = () => {
  if (!userStore.isLogined) {
    event.emit(OPEN_NEW_LOGIN, {
      routerDisabled: true,
    })
    return false
  }
  return true
}

const handleReceive = () => {
  if (contactCustomer()) {
    customerRef.value.init()
  }
}
</script>

<style lang="scss" scoped>
.content {
  &-wrap {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 100%;
    color: #333;
  }

  &-title {
    font-family: PingFang SC;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 18px;
    white-space: pre-wrap;
    word-break: break-all;
  }

  &-detail {
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    &-row {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      width: 50%;
      margin-bottom: 16px;
    }
  }

  &-row {
    display: flex;
    align-items: center;

    &-label {
      color: #999;
    }
  }

  &-attr {
    max-height: 100%;
    padding: 16px;
    overflow: auto;
    background: #faf9f9;

    &-item {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      border-bottom: 1px dashed #eee;
      margin-bottom: 8px;

      &:last-child {
        border: none;
      }
    }
  }

  &-footer {
    flex-shrink: 0;
    height: 40px;
    margin-top: 24px;
  }

  &-btn {
    width: 144px;
    height: 100%;
    border-radius: 4px;
    background: #fff;
    border: 1px solid $primary-color;
    color: $primary-color;
    font-family: PingFang SC;
    font-size: 16px;
    font-weight: 600;

    &.is-disabled {
      opacity: 0.6;
    }

    &:hover {
      border: 1px solid $primary-color;
      color: $primary-color;
    }

    &.primary {
      color: #fff;
      background: $primary-color;
    }
  }
}

[dir='rtl'] .content {
  .mr-\[4px\] {
    margin-right: 0;
    margin-left: 4px;
  }
}
</style>
