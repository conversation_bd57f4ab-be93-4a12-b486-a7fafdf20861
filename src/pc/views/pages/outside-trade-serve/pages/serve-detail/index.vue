<template>
  <div class="h-[160px]" />
  <div class="logo-nav">
    <SearchInput />
  </div>
  <div class="w-1260 pd-[24px]">
    <Breadcrumb :breadcrumbList="breadcrumbList" />
    <el-skeleton :loading="loading" animated>
      <template #template>
        <div class="goods-wrap">
          <el-skeleton-item variant="image" class="goods-left" style="height: 500px"> </el-skeleton-item>
          <div class="goods-right">
            <el-skeleton-item variant="h1"></el-skeleton-item>
            <div class="flex mb-[12px] mt-[12px]">
              <el-skeleton-item variant="text" style="width: 40%; margin-right: 16px" />
            </div>
            <el-skeleton-item style="height: 240px"></el-skeleton-item>
            <el-skeleton-item variant="button" style="width: 30%; margin-right: 12px" />
          </div>
        </div>
        <div class="goods-detail">
          <el-skeleton-item v-for="item in 4" :key="item" variant="image" style="height: 200px"></el-skeleton-item>
        </div>
      </template>
      <template #default>
        <div class="goods-wrap">
          <div class="goods-left">
            <goods-pic :detailInfo="detailInfo" />
          </div>
          <div class="goods-right">
            <goods-content :detailInfo="detailInfo" />
          </div>
        </div>
        <div class="goods-detail pb-[24px]" v-if="detailInfo" v-html="detailInfo.info" @click="handleDetailClick"></div>
      </template>
    </el-skeleton>
    <ElImageViewer
      v-if="showViewer"
      :urlList="imgDetailList"
      :z-index="99"
      :initialIndex="initialIndex"
      hideOnClickModal
      teleported
      @close="showViewer = false"
    />
  </div>
</template>

<script setup>
import { ElImageViewer } from 'element-plus'
import Breadcrumb from '@/pc/components/breadcrumb/index.vue'
import SearchInput from '@/pc/components/search-input/search-input.vue'
import GoodsContent from './compontents/serve-content.vue'
import GoodsPic from './compontents/serve-pic.vue'
import { getWebServiceInfo } from '@/apis/outside-trade-serve'

const breadcrumbList = reactive([
  {
    path: '/outside-trade-serve',
    name: { zh: '外贸综合服务', en: 'Trade Service' },
  },
  {
    name: { zh: '服务详情', en: 'Service Details' },
  },
])

const route = useRoute()
const detailInfo = ref(null)
const loading = ref(true)

const getGoodsDetailInfo = async () => {
  try {
    const data = await getWebServiceInfo({ id: route.params.id })
    if (data) {
      detailInfo.value = data
    }
  } catch (e) {
    console.log(e)
  } finally {
    loading.value = false
  }
}
getGoodsDetailInfo()

const showViewer = ref(false)
const initialIndex = ref(0)
const imgDetailList = computed(() => {
  const html = detailInfo.value?.info || ''
  return [...html.matchAll(/<img[^>]+src="([^"]+)"/g)].map((match) => match[1])
})
const handleDetailClick = (e) => {
  const { currentSrc, tagName } = e.target || {}
  if (currentSrc && tagName === 'IMG') {
    initialIndex.value = imgDetailList.value.findIndex((item) => encodeURI(item) === currentSrc)
    showViewer.value = true
  }
}
</script>

<style lang="scss" scoped>
.goods-wrap {
  display: flex;
  width: 100%;
  height: 626px;
  padding: 16px;
  box-sizing: border-box;
  overflow: hidden;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 27px;

  .goods-left {
    flex-shrink: 0;
    width: 500px;
    height: 100%;
    margin-right: 16px;
  }

  .goods-right {
    flex: 1;
    height: 100%;
    padding: 0 8px;
    overflow: hidden;
  }
}

.logo-nav {
  position: fixed;
  top: 64px;
  left: 0;
  z-index: 49;
  width: 100%;
  height: 64px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.goods-detail {
  width: 900px;
  overflow: hidden;
  margin: 0 auto;

  :deep(img),
  :deep(video) {
    display: block;
    width: 100% !important;
    cursor: pointer;
  }
}

[dir='rtl'] .goods-wrap {
  .goods-left {
    margin-right: 0;
    margin-left: 16px;
  }
}
</style>
