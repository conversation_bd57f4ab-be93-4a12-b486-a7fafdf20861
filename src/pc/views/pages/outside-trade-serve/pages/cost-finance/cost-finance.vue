<!--
 * @Author: 李兵 <EMAIL>
 * @Date: 2024-08-27 09:24:58
 * @LastEditors: 李兵 <EMAIL>
 * @LastEditTime: 2024-08-27 16:30:05
 * @FilePath: \trade-exhibition\src\views\pages\outside-trade-serve\pages\cost-finance\cost-finance.vue
 * @Description: 外贸综合服务----低成本融资
-->
<style scoped lang="scss">
.cost-finance {
  margin-bottom: 40px;
  &-banner {
    background-image: url('https://static.chinamarket.cn/static/trade-exhibition/outside-trade/banner-cost-finance.png');
    background-size: cover;
    padding: 80px 40px 80px 80px;
    height: 400px;
    @include flex-sb;
    align-items: flex-start;
    .left {
      width: 400px;
      &-title {
        font-size: 36px;
        font-weight: 600;
        line-height: 50px;
        margin-bottom: 16px;
      }
      &-des {
        font-size: 14px;
        color: $regular-text;
        line-height: 20px;
        margin-bottom: 23px;
      }
    }
    .right {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 126px;
      height: auto;
      padding-top: 12px;
      padding-bottom: 12px;
      background-image: url('https://static.chinamarket.cn/static/trade-exhibition/outside-trade-serve/code-warp1.png');
      background-size: 100% 100%;
      box-sizing: border-box;
      span {
        line-height: 20px;
        background: linear-gradient(270deg, #d8131a 0%, #e96f18 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        color: transparent;
        box-sizing: border-box;
        padding: 0 10px;
      }
      img {
        width: 80px;
        height: 80px;
        margin-top: 2px;
      }
    }
  }
  .bank-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    margin-top: 80px;
    .bank-item {
      display: flex;
      flex-direction: column;
      border-radius: 4px;
      padding: 0px 16px 16px 16px;
      cursor: pointer;
      &-logo {
        height: 64px;
      }
      &-title {
        position: relative;
        margin-bottom: 12px;
        font-weight: 600;
        font-size: 24px;
        line-height: 34px;
        text-align: center;
      }
      &-des {
        font-size: 14px;
        line-height: 20px;
        color: $color-999999;
      }
      &-content {
        flex-grow: 1;
        padding: 16px 24px;
      }
      &.more {
        background: #f5f6f7;
        font-size: 18px;
        color: $color-999999;
        @include flex-cc;
        text-align: center;
      }
    }
  }
}
</style>

<template>
  <div class="cost-finance">
    <!-- 低成本融资介绍 -->
    <div class="cost-finance-banner-wrap">
      <div class="cost-finance-banner">
        <div class="left">
          <h2 class="left-title">{{ $t('financing.title') }}</h2>
          <p class="left-des">{{ $t('financing.desc') }}</p>
        </div>
        <div class="right">
          <span>{{ t('slogan') }}</span>
          <img class="h-[126px]" src="https://static.chinamarket.cn/static/trade-exhibition/wechat-work-qrcode.png" draggable="false" />
        </div>
      </div>
    </div>
    <!-- 资方列表 -->
    <div>
      <ul class="bank-list">
        <li @click="jumpTo" class="bank-item" :style="{ borderTop: `4px solid #E50012`, background: '#FCE8E6' }">
          <div class="bank-item-logo flex items-center">
            <img-loader lazy class="w-full" src="/outside-trade-serve/ylt-logo.png" />
          </div>
          <div class="bank-item-content" :style="{ background: 'rgba(255, 255, 255, 0.6)' }">
            <div class="bank-item-title" :style="{ color: `#E50012` }">沂链通</div>
            <div class="bank-item-des">一票融通，资金速达，极简操作，全国覆盖，秒级放款，安全可靠。票据一站式服务平台，致力于解决中小微企业融资难融资贵。</div>
          </div>
        </li>
        <li
          v-for="item in bankList"
          :key="item.id"
          class="bank-item"
          :style="{ borderTop: `4px solid ${item.textColor}`, background: `${item.bgColor}` }"
          @click="onConsulting"
        >
          <div class="bank-item-logo">
            <img-loader lazy class="w-full" :src="item.icon" />
          </div>
          <div class="bank-item-content" :style="{ background: `${item.textBg}` }">
            <div class="bank-item-title" :style="{ color: `${item.textColor}` }">
              {{ item.title[$i18n.locale] }}
            </div>
            <div class="bank-item-des">{{ item.des[$i18n.locale] }}</div>
          </div>
        </li>
        <!-- <li class="bank-item more">
          {{ $t('financing.more[0]') }}<br />
          {{ $t('financing.more[1]') }}
        </li> -->
      </ul>
    </div>
  </div>

  <ContactInfo ref="contactInfoRef" />
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import ContactInfo from '../../components/contact-info.vue'
import { ossUrl } from '@/constants/common'

const { t } = useI18n({
  messages: {
    zh: {
      slogan: '好生意 不等待',
    },
    en: {
      slogan: 'Service Center',
    },
  },
})

const bankList = [
  {
    id: 1,
    icon: `${ossUrl}/outside-trade-serve/bank1.png`,
    title: { zh: '微业贷', en: 'Weiye Loan' },
    des: {
      zh: '无需抵质押，额度立等可见，随借随用，按日计息，为中小微企业提供高效便捷的融资服务。',
      en: 'No collateral required, instant visibility of credit limit, available for use anytime, interest calculated daily, providing efficient and convenient financing services for small and micro enterprises.',
    },
    bgColor: '#E2E7F8',
    textColor: '#3A5ECF',
    textBg: '#EEF1FB',
    linkUrl: 'https://www.webank.com/product',
  },
  {
    id: 2,
    icon: `${ossUrl}/outside-trade-serve/bank2.png`,
    title: { zh: '金企贷', en: 'Jinqi Loan' },
    des: {
      zh: '最高授信额度500万，全流程线上申请，最快1分钟放款，无需抵质押，0手续费，0中介费，随用随借，按单利计息。',
      en: 'Credit limit up to 5 million, fully online application, fastest disbursement in 1 minute, no collateral required, 0 fees, 0 intermediary charges, available anytime, interest calculated on a per-loan basis.',
    },
    bgColor: '#FBDEDB',
    textColor: '#E72410',
    textBg: '#FDEBE9',
    linkUrl: 'https://www.kcbebank.com/servicePage',
  },
  {
    id: 3,
    icon: `${ossUrl}/outside-trade-serve/bank3.png`,
    title: { zh: '好企e贷', en: 'HaoqiE Loan' },
    des: {
      zh: '服务中小微企业/企业主贷款，最高额度300万元，按日计息，随借随还。',
      en: 'Serves small and micro enterprises/owners with loans up to 3 million yuan, interest calculated daily, available for borrowing and repayment anytime.',
    },
    bgColor: '#DBF5E4',
    textColor: '#10BD4D',
    textBg: '#E9F9EF',
    linkUrl: 'https://www.xwbank.com/#/haoqiEdai',
  },
  {
    id: 4,
    icon: `${ossUrl}/outside-trade-serve/bank4.png`,
    title: { zh: '网商贷', en: 'Wangshang Loan' },
    des: {
      zh: '专为电商客户设计，促进实体经济发展，最高300万额度，随借随还，提前还无手续费，极速到账。',
      en: 'Designed for e-commerce customers, promotes the development of the real economy, credit limit up to 3 million, available for borrowing and repayment anytime, no fees for early repayment, fast disbursement.',
    },
    bgColor: '#DDE8FF',
    textColor: '#1968FC',
    textBg: '#EBF1FF',
    linkUrl: 'https://login.mybank.cn/login/loginhome.htm?redirectURL=https%3A%2F%2Floanweb.mybank.cn%2F',
  },
  {
    id: 5,
    icon: `${ossUrl}/outside-trade-serve/bank5.png`,
    title: { zh: '拍周转', en: 'Paizhou Loan' },
    des: {
      zh: '关注小微企业成长，上线小微服务专区，致力于为小微用户提供全方位服务。',
      en: 'Focuses on the growth of small and micro enterprises, launching a dedicated small and micro service zone, committed to providing comprehensive services for small and micro users.',
    },
    bgColor: '#FAECD9',
    textColor: '#DF7F00',
    textBg: '#FCF4E8',
    linkUrl: 'https://www.ppdai.com/',
  },
]

const contactInfoRef = ref(null)
const onConsulting = () => {
  contactInfoRef.value?.init()
}

const jumpTo = () => {
  window.open('https://www.yiliantong.cn/')
}
</script>
