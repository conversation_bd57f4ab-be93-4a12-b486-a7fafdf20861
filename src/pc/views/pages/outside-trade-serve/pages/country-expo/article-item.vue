<!--
 * @Author: 李兵 <EMAIL>
 * @Date: 2024-08-26 15:24:23
 * @LastEditors: 李兵 <EMAIL>
 * @LastEditTime: 2024-08-27 16:22:45
 * @FilePath: \trade-exhibition\src\views\pages\outside-trade-serve\pages\country-expo\article-item.vue
 * @Description: 外贸综合服务 国际展会 文章item
-->
<style scoped lang="scss">
.country-expo {
  @include flex-sbc;
  background: $basic-white;
  padding: 24px;
  cursor: pointer;

  &:hover {
    background: #f8f8f8;
  }

  .left {
    flex-shrink: 0;
    width: 140px;
    height: 100px;
    margin-right: 10px;
    overflow: hidden;
    img {
      width: 100%;
      min-height: 100%;
    }
  }
  .right {
    flex: 1;
    .title {
      font-size: 16px;
      color: $color-333333;
      line-height: 22px;
      margin-bottom: 8px;
      @include ellipsis(1);
    }
    .des {
      font-size: 12px;
      color: $color-999999;
      margin-bottom: 8px;
      @include ellipsis(2);
    }
    .date {
      font-size: 12px;
      color: $color-999999;
    }
  }
}
</style>
<template>
  <div class="country-expo" @click="redirect.to(item.url)">
    <div class="left">
      <img :src="item.img" @error="onImageError" />
    </div>
    <div class="right">
      <h3 class="title">{{ props.item.title }}</h3>
      <p class="des" v-if="props.item.summary">{{ props.item.summary }}</p>
      <i class="date">{{ props.item.publishTime }}</i>
    </div>
  </div>
</template>

<script setup>
import { ossUrl } from '@/constants/common'
import { useRedirect } from '@/pc/views/pages/redirect/hook'

const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
})

const onImageError = (event) => {
  event.target.src = `${ossUrl}/outside-trade-serve/onData.png`
}

const redirect = useRedirect()
</script>
