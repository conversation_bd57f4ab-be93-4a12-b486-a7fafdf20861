<!--
 * @Author: 李兵 <EMAIL>
 * @Date: 2024-08-26 15:01:48
 * @LastEditors: your name
 * @LastEditTime: 2024-09-09 15:13:53
 * @FilePath: /trade-exhibition/src/pc/views/pages/outside-trade-serve/pages/country-expo/country-expo.vue
 * @Description: 外贸综合服务 国际展会
-->
<style scoped lang="scss">
.article-item {
  margin-bottom: 40px;
  @include flex-sb;
  .article-wrap {
    padding: 24px;
    background: $basic-white;
    flex: 1;
  }
  .goods-wrap {
    width: 268px;
    flex-shrink: 0;
    margin-left: 24px;
  }
  .pagination-wrap {
    @include flex-cc;
    padding: 24px 0 0;
  }
}
:deep() {
  .el-table__cell {
    padding: 0;
  }
  .cell {
    padding: 0;
  }
  .is-leaf {
    padding: 0 !important;
  }
}
</style>

<template>
  <div class="article-item">
    <!-- 左侧 -->
    <div class="article-wrap">
      <el-table :data="data" border v-if="data.length">
        <el-table-column prop="date">
          <template #default="scope">
            <ArticleItem :item="scope.row" />
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-wrap" v-if="data.length">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          :page-size="pagination.pageSize"
          background
          layout="total, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <!-- 右侧 -->
    <!--    <div class="goods-wrap">-->
    <!--      <div v-for="(item, index) in goodsList" :key="index" class="cursor-pointer">-->
    <!--        <GoodsItem :item="item" @click="onGoodsClick(item)" />-->
    <!--      </div>-->
    <!--    </div>-->
  </div>
</template>

<script setup>
import ArticleItem from './article-item.vue'
// import GoodsItem from './goods-item.vue'
import useList from '@/pc/hooks/useList'
// import { hotProductList } from '@/apis/market'
import { getForeignTrade } from '@/apis/outside-trade-serve'
import { useEvent } from '@/event'
import { LANG_CHANGED } from '@/event/modules/site'

const event = useEvent()

const { data, handleSizeChange, handleCurrentChange, pagination, getData } = useList({
  api: getForeignTrade,
})

// const goodsList = ref([])
// const getGoodsList = async () => {
//   const res = await hotProductList()
//   goodsList.value = res ? res.slice(2, 4) : []
// }
// getGoodsList()

// const onGoodsClick = (item) => {
//   if (!item.goodsUrl) return
//   window.open(item.goodsUrl, '_blank')
// }

event.on(LANG_CHANGED, async () => {
  await getData()
  // await getGoodsList()
})
</script>
