<!--
 * @Author: 李兵 <EMAIL>
 * @Date: 2024-08-26 15:24:44
 * @LastEditors: 李兵 <EMAIL>
 * @LastEditTime: 2024-08-27 16:51:34
 * @FilePath: \trade-exhibition\src\views\pages\outside-trade-serve\pages\country-expo\goods-item.vue
 * @Description: 外贸综合服务 国际展会--热销商品
-->
<style scoped lang="scss">
.goods-item {
  background: $basic-white;
  padding: 12px 24px;
  margin-bottom: 16px;
  .hot {
    font-size: 20px;
    font-weight: 400;
    margin-bottom: 11px;
    line-height: 28px;
    position: relative;
    padding-left: 12px;
    &::before {
      content: '';
      display: inline-block;
      width: 6px;
      height: 20px;
      background: $primary-color;
      position: absolute;
      top: 0;
      bottom: 0;
      margin: auto;
      left: 0;
    }
  }
  .goods-img {
    width: 220px;
    height: 220px;
    border: 1px solid #edeef1;
    border-radius: 4px;
    margin-bottom: 12px;
    overflow: hidden;
    img {
      width: 100%;
      min-height: 100%;
    }
  }
  .goods-name-wrap {
    @include flex-sb;
    .goods-name {
      width: 52px;
      height: 21px;
      border-radius: 4px;
      text-align: center;
      background: $primary-color;
      line-height: 21px;
      font-size: 12px;
      font-weight: 500;
      flex-shrink: 0;
      color: $basic-white;
      margin-right: 8px;
    }
    .goods-des {
      @include ellipsis(1);
      font-size: 14px;
      color: $color-333333;
      line-height: 20px;
      flex: 1;
    }
  }
  .goods-price-wrap {
    @include flex-vc;
    padding-top: 5px;
    .goods-price-title {
      width: 52px;
      height: 21px;
      border-radius: 4px;
      background: #b99839;
      text-align: center;
      line-height: 21px;
      font-size: 12px;
      font-weight: 500;
      color: $basic-white;
      margin-right: 8px;
    }
    .goods-price-des {
      color: $primary-color;
      font-size: 16px;
      &::before {
        content: '￥';
        display: inline-block;
        font-size: 12px;
      }
      .unit {
        font-size: 12px;
      }
    }
  }
}
</style>

<template>
  <div class="goods-item">
    <dl>
      <!-- 标题 -->
      <dt class="hot">{{ t('tip') }}</dt>
      <!-- 图片 -->
      <dd class="goods-img"><img :src="props.item.goodsPic" alt="" /></dd>
      <!-- 产品名 -->
      <dd class="goods-name-wrap">
        <span class="goods-name">{{ t('name') }}</span>
        <p class="goods-des">{{ props.item.title }}</p>
      </dd>
      <!-- 价格 -->
      <dd class="goods-price-wrap">
        <span class="goods-price-title">{{ t('price') }}</span>
        <p class="goods-price-des">
          {{ price[0] }}<i class="unit" v-if="price[1]">.{{ price[1] }}</i>
        </p>
      </dd>
    </dl>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'

const { t } = useI18n({
  messages: {
    zh: {
      tip: '热销',
      name: '产品名',
      price: '价格',
    },
    en: {
      tip: 'Hot-selling',
      name: 'Name',
      price: 'Price',
    },
  },
})

const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
})

const price = computed(() => {
  const a = props.item.price + ''
  return a.split('.') || []
})
</script>
