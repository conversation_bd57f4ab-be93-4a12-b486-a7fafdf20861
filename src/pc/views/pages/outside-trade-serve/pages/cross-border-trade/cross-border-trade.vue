<template>
  <div class="cross-border-trade">
    <div class="search-wrapper">
      {{ $t('crossBorderTrade.searchScope') }}：
      <div @click="onChange(s)" :class="s.value !== curChoose && 's-disable'" class="search-item" v-for="s in rangeOption" :key="s.value">
        <div class="circle"></div>
        {{ s.label[$i18n.locale] }}
      </div>
    </div>
    <div class="info-wrapper" v-if="data.length > 0">
      <div class="item" v-for="item in data" :key="item.id" @click="toPage(item.url)">
        <div class="item-inner">
          <div class="tag" :class="['tag1', `tag-${item.affairCategory}`]">{{ affairCategoryToText(item.affairCategory) }}</div>
          <div class="title">{{ item.title }}</div>
          <div class="btn" v-if="item.url">{{ $t('crossBorderTrade.goDetail') }}</div>
        </div>
      </div>

      <div class="footer">
        <el-pagination
          background
          v-model:current-page="pagination.currentPage"
          :page-size="pagination.pageSize"
          layout="total, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import * as API from '@/apis/outside-trade-serve'
import { useEvent } from '@/event'
import { LANG_CHANGED } from '@/event/modules/site'
import { debounce } from '@/common/js/util'
import { useRedirect } from '@/pc/views/pages/redirect/hook'

const { locale } = useI18n()
const event = useEvent()

const DEBOUNCE_TIME = 300
// 获取列表数据防抖函数
const deBounceGetData = debounce(() => {
  getData()
}, DEBOUNCE_TIME)

const rangeOption = reactive([
  { label: { zh: '行政检查', en: 'Administrative inspection' }, value: 2 },
  { label: { zh: '行政处罚', en: 'Administrative penalty' }, value: 1 },
  // { label: '行政许可', value: 3 },
])
const affairCategoryToText = (id) => {
  return rangeOption.find((item) => item.value === id)?.label[locale.value] ?? ''
}

// 分页
const pagination = ref({
  pageNum: 1,
  pageSize: 10,
  total: 0,
})

// 默认行政检查
const curChoose = ref(2)

const onChange = (val) => {
  if (val.value === curChoose.value) return
  curChoose.value = val.value
  pagination.value.pageNum = 1
  getData()
}
// 数据
const data = reactive([])
const getData = async () => {
  const body = {
    affairCategory: curChoose.value,
    pageNum: pagination.value.pageNum,
    pageSize: pagination.value.pageSize,
  }
  let res = await API.getCrossBorderTrade(body)

  const { rowList, totalRecord } = res
  data.length = 0
  data.push(...rowList)
  pagination.value.total = totalRecord
}

// 分页条数变化
const handleSizeChange = (val) => {
  pagination.value.pageSize = val
  pagination.value.pageNum = 1
  deBounceGetData()
}

// 分页页数变化
const handleCurrentChange = (val) => {
  console.log('val', val)
  pagination.value.pageNum = val
  deBounceGetData()
}

const redirect = useRedirect()
const toPage = (val) => {
  if (val.startsWith('http')) {
    redirect.to(val)
  }
}
onMounted(async () => {
  getData()
})

event.on(LANG_CHANGED, async () => {
  await getData()
})
</script>

<style lang="scss" scoped>
.cross-border-trade {
  padding: 24px;
  max-width: 1260px;
  margin: 0 auto;
  background: $basic-white;
  display: grid;
  grid-template-columns: 288px 1fr;

  .search-wrapper {
    padding: 24px;
    width: 288px;
    height: 154px;
    background: #f5f6f9;

    .search-item {
      cursor: pointer;
      display: grid;
      position: relative;
      margin-top: 12px;
      padding: 4px 12px;
      background: $basic-white;
      grid-template-columns: 30px 1fr;
    }
    .circle {
      position: relative;
      width: 12px;
      height: 12px;
    }

    .circle::before {
      content: '';
      position: absolute;
      top: 10px;
      left: 12px;
      width: 100%;
      height: 100%;
      border: 4px solid #d8131a;
      border-radius: 50%;
      transform: translate(-50%, -50%);
    }

    .s-disable {
      color: $color-B3B3B3;

      .circle::before {
        content: '';
        position: absolute;
        top: 10px;
        left: 12px;
        width: 100%;
        height: 100%;
        border: 4px solid $color-B3B3B3;
        border-radius: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }

  .info-wrapper {
    margin: 0 0 20px 12px;

    .item {
      padding: 0 12px;
      cursor: pointer;
      &:hover {
        background: #f8f8f8;
        .btn {
          color: $basic-white;
          background: $primary-color;
          border: 1px solid $primary-color;
        }
      }
      .item-inner {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 2px solid #edf0f5;
        gap: 12px;
      }
    }
    .title {
      flex: 1 0 0;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      font-size: 16px;
      color: #257bfb;
      padding-right: 12px;
    }
    .content {
      color: $regular-text;
      margin: 12px 0;
    }

    .tag {
      flex: 0 0;
      padding: 0 4px;
      border-radius: 4px;
      line-height: 25px;
      font-size: 12px;
      white-space: nowrap;
    }

    .tag-1 {
      background: #fbe7e8;
      border: 1px solid $primary-color;
      color: $primary-color;
    }

    .tag-2 {
      background: rgba(37, 123, 251, 0.1);
      border: 1px solid #257bfb;
      color: #257bfb;
    }

    .btn {
      flex: 0 0 74px;
      font-size: 12px;
      cursor: pointer;
      height: 24px;
      line-height: 24px;
      text-align: center;
      border-radius: 100px;
      border: 1px solid $color-333333;
      color: $color-333333;
      transition: all 0.1s;
    }
  }
}
</style>
