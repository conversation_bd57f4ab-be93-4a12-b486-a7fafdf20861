<!--
 * @Author: 李兵 <EMAIL>
 * @Date: 2024-08-24 11:03:44
 * @LastEditors: 李兵 <EMAIL>
 * @LastEditTime: 2024-08-27 18:37:15
 * @FilePath: \trade-exhibition\src\views\pages\outside-trade-serve\pages\components\banner-card.vue
 * @Description: banner图  + 介绍card 组合  布局
-->
<style scoped lang="scss">
.banner-wrap {
  width: 100%;
  height: 200px;
  margin-bottom: 24px;
  background-repeat: no-repeat;
  background-size: 1260px 100%;
  background-position: top center;
}
.info-card {
  @include flex-cc;
  flex-direction: column;
  background: $basic-white;
  padding-bottom: 24px;
  &-list {
    display: flex;
    gap: 24px;
    padding: 24px;
  }
  .card-item {
    flex: 1 0 0;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0px 10px 20px 0px rgba(100, 100, 100, 0.1);
    border: 1px solid #d1d1d1;
    &-icon {
      width: 64px;
      height: 64px;
      border-radius: 4px;
      background: #f3f5f8;
      margin: 0 auto;
      margin-bottom: 12px;
      overflow: hidden;
      box-shadow: 0px 10px 20px 0px rgba(100, 100, 100, 0.1);
      img {
        width: 100%;
        min-height: 100%;
      }
    }
    &-title {
      font-size: 18px;
      color: $color-333333;
      text-align: center;
      line-height: 25px;
      margin-bottom: 12px;
    }
    &-des {
      color: $color-999999;
      font-size: 16px;
      line-height: 22px;
    }
  }
  .more-btn {
    height: 46px;
    padding: 0 16px;
    border-radius: 100px;
    background: $basic-white;
    border: 1px solid #d1d1d1;
    cursor: pointer;
    color: $color-999999;
  }
}
</style>

<template>
  <div class="banner-card">
    <!-- banner图 -->
    <div class="banner-wrap" :style="{ backgroundImage: `url(${props.bannerBgUrl})` }"></div>
    <!-- 介绍 card -->
    <div class="info-card">
      <div class="info-card-list">
        <dl v-for="item in props.list" :key="item.id" class="card-item">
          <dt class="card-item-icon"><img :src="item.icon" alt="" /></dt>
          <dd class="card-item-title">{{ item.title[$i18n.locale] }}</dd>
          <dd class="card-item-des">{{ item.des[$i18n.locale] }}</dd>
        </dl>
      </div>
      <button class="more-btn">{{ $t('internationalLogistics.more') }}.......</button>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
  bannerBgUrl: {
    type: String,
  },
})
</script>
