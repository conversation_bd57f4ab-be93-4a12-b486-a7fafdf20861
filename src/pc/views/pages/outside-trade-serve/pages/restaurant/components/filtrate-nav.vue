<style lang="scss" scoped>
.filtrate-class-item {
  display: flex;
  .class-name {
    flex: 0 0 24px;
    font-size: 12px;
    line-height: 25px;
    margin-right: 32px;
    color: $color-999999;
  }

  .class-label {
    display: flex;
  }
  .label-active {
    // font-size: 12px;
    color: $basic-white;
    border-radius: 14px;
    padding: 4px 10px;
    background: linear-gradient(270deg, #d8131a 0%, #e96f18 100%);
  }
  .all-label {
    margin-right: 48px;
    height: 25px;
    flex: 0 0 44px;
    line-height: 25px;
    padding: 0 10px;
    font-size: 12px;
    cursor: pointer;
  }

  .select-label-box {
    overflow: auto;
    display: flex;
    flex-wrap: wrap;
    align-content: baseline;
    flex: 1;
    max-height: 200px;
    padding: 0 6px;
    transition: all 0.3s ease;
  }
  .select-label-item {
    margin-bottom: 12px;
    text-align: center;
    font-size: 12px;
    color: $color-666666;
    cursor: pointer;

    span {
      padding: 0 10px;
      line-height: 25px;
      display: inline-block;
      @include ellipsis;
    }
  }
}
.more-btn {
  flex: 0 0 100px;
  font-size: 12px;
  color: $color-666666;
  line-height: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
}
.mb-20 {
  margin-bottom: 20px;
}

.h-84 {
  height: 84px;
}

.tabs-wrapper {
  display: flex;
  align-items: center;

  .tabs-item {
    line-height: 33px;
    width: 110px;
    text-align: center;
    font-size: 12px;
    color: $color-666666;
    background: $basic-white;
    cursor: pointer;

    &.active {
      background: #f8f8f8;
      border-width: 1px 1px 0px 1px;
      border-style: solid;
      border-color: #edeef1;
      color: $primary-color;
      position: relative;
      &::before {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 100%;
        height: 1px;
        background-color: #f8f8f8;
      }
    }
  }
}

.addres-wrapper {
  .select-label-box {
    border: 1px solid #edeef1;
    background: #f8f8f8;
    padding-top: 8px;
  }
}
</style>

<template>
  <div>
    <!-- 美食分类 -->
    <div class="filtrate-class-item mb-20" v-if="route.query.type !== '2'">
      <div class="class-name">{{ t('condition') }}</div>
      <div class="class-label">
        <div class="all-label" :class="{ 'label-active': delicacyIndex === -1 }" @click="onDelicacyClassAll">{{ t('unlimit') }}</div>
        <div class="select-label-box h-84" ref="selectLableRef">
          <div v-for="(item, index) in delicacyList" :key="index" class="select-label-item" @click="onSelectDelicacy(item, index)">
            <span :class="{ 'label-active': delicacyIndex === index }">{{ item.name }}</span>
          </div>
        </div>
      </div>
      <div class="more-btn" @click="onToggle">
        {{ t('more') }}
        <icon :type="!isToggle ? 'icon-xiajiantou' : 'icon-shangjiantou'" :size="12" />
      </div>
    </div>
    <!-- 地区 -->
    <div class="filtrate-class-item" v-if="regionList.length !== 0 || administDivisionList !== 0">
      <div class="class-name">{{ t('address') }}</div>
      <div class="class-label">
        <div class="all-label" :class="{ 'label-active': regionIndex === -1 }" @click="onRegionClassAll">{{ t('unlimit') }}</div>
        <div class="addres-wrapper">
          <div class="tabs-wrapper">
            <template v-if="regionList.length">
              <div class="tabs-item" :class="{ active: tabsIndex === 0 }" @click="onTabsChang(0)">{{ t('hotArea') }}</div>
            </template>
            <template v-if="administDivisionList.length">
              <div class="tabs-item" :class="{ active: tabsIndex === 1 }" @click="onTabsChang(1)">{{ t('district') }}</div>
            </template>
          </div>
          <div class="select-label-box" v-if="tabsIndex === 0 && regionList.length">
            <div v-for="(item, index) in regionList" :key="item" class="select-label-item" @click="onSelectRegion(item, index)">
              <span :class="{ 'label-active': regionIndex === index }">{{ item.name }}</span>
            </div>
          </div>
          <div class="select-label-box" v-if="tabsIndex === 1 && administDivisionList.length">
            <div v-for="(item, index) in administDivisionList" :key="item" class="select-label-item" @click="onSelectRegion(item, index)">
              <span :class="{ 'label-active': regionIndex === index }">{{ item.name }}</span>
            </div>
          </div>
        </div>
        <div class="more-btn" style="visibility: hidden">{{ t('more') }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { entertainmentArea, entertainmentCategory, foodsCategory, foodsRegion, hotelArea } from '@/apis/outside-trade-serve.js'
import { useEvent } from '@/event'
import { LANG_CHANGED } from '@/event/modules/site'

const { t } = useI18n({
  messages: {
    zh: {
      unlimit: '不限',
      more: '更多',
      condition: '分类',
      address: '地点',
      hotArea: '热门地区',
      district: '行政区',
    },
    en: {
      unlimit: 'None',
      more: 'More',
      condition: 'Condition',
      address: 'Address',
      hotArea: 'Popular regions',
      district: 'District',
    },
  },
})
const event = useEvent()
const route = useRoute()

const emits = defineEmits(['onSelectDelicacy', 'onSelectRegion'])

// 分类 API 1.美食 3.娱乐
const CATEGORY_API = {
  1: foodsCategory,
  3: entertainmentCategory,
}

// 区域 API 1.美食 3.娱乐
const AREA_API = {
  1: foodsRegion,
  2: hotelArea,
  3: entertainmentArea,
}

// 分类
const delicacyList = reactive([])

// 获取分类数据
const getCategory = async () => {
  const res = await CATEGORY_API[route.query.type]({ type: route.query.type })
  delicacyList.length = 0
  delicacyList.push(...res)
}
if (route.query.type !== '2') {
  getCategory()
}

// 美食分类的参数
const delicacyIndex = ref(-1)
// 点击美食分类的不限制
const onDelicacyClassAll = () => {
  delicacyIndex.value = -1
  emits('onSelectDelicacy', null)
}
// 美食分类筛选
const onSelectDelicacy = (item, index) => {
  delicacyIndex.value = index
  emits('onSelectDelicacy', item)
}
// 展开/收起
const isToggle = ref(false)
const selectLableRef = ref(null)
const onToggle = () => {
  isToggle.value = !isToggle.value
  selectLableRef.value.style.height = isToggle.value ? '200px' : '84px'
}

// 热门地区
const regionList = reactive([])
// 地区 tabs 切换
const tabsIndex = ref(0)
// 获取热门地区数据
const getArea = async () => {
  const res = await AREA_API[route.query.type]({ type: 1 })
  regionList.length = 0
  regionList.push(...res)
  if (regionList.length === 0) {
    tabsIndex.value = 1
  }
}
getArea()

// 行政区域
const administDivisionList = reactive([])
// 获行政区域数据
const getAdministDivision = async () => {
  const res = await AREA_API[route.query.type]({ type: 2 })
  administDivisionList.length = 0
  administDivisionList.push(...res)
}
setTimeout(() => {
  getAdministDivision()
}, 500)

const onTabsChang = (index) => {
  tabsIndex.value = index
  regionIndex.value = -1
  // if (index === 0) {
  //     getArea()
  // } else {
  //   getAdministDivision()
  // }
}
const regionIndex = ref(-1)
// 点击地区不限
const onRegionClassAll = () => {
  regionIndex.value = -1
  emits('onSelectRegion', null)
}
// 热门地区/行政区筛选
const onSelectRegion = (item, index) => {
  regionIndex.value = index
  emits('onSelectRegion', item)
}

event.on(LANG_CHANGED, async () => {
  if (route.query.type !== '2') {
    await getCategory()
  }
  await getArea()
})
</script>
