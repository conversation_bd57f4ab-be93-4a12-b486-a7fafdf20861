<style lang="scss" scoped>
.code-wrapper {
  width: 180px;
  height: 218px;
  padding: 30px;
  background: $basic-white;
  border: 1px solid #edeef1;

  .code-box {
    width: 120px;
    height: 120px;
    background: url('https://static.chinamarket.cn/static/trade-exhibition/outside-trade/qr-code.png') no-repeat;
    background-size: 100% 100%;
    margin-bottom: 16px;
  }
  .text-box {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
  }
}
</style>

<template>
  <div class="code-wrapper">
    <div class="code-box"></div>
    <div class="text-box">
      <icon type="icon-rongqi" :size="16" />
      {{ t('scan') }}
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'

const { t } = useI18n({
  messages: {
    zh: {
      scan: '手机扫码',
    },
    en: {
      scan: 'Scan Code',
    },
  },
})
</script>
