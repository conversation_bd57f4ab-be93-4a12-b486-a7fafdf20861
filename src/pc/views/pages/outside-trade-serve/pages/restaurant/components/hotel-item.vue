<style lang="scss" scoped>
.hotel-item {
  background: $basic-white;
  padding: 20px;
  display: flex;
  cursor: pointer;

  &:hover {
    background: #f8f8f8;
  }

  .img-box {
    flex: 0 0 230px;
    height: 142px;
    margin-right: 20px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .content-box {
    .hotel-name {
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 600;
      color: $color-333333;
    }
    .hotel-info {
      margin-bottom: 8px;
      font-size: 12px;
      color: $color-999999;
    }
  }
}
</style>

<template>
  <div class="hotel-item" @click="goPage(item.url)">
    <div class="img-box">
      <img :src="item.img" alt="" />
    </div>
    <div class="content-box">
      <div class="hotel-name">{{ item.name }}</div>
      <div class="hotel-info">{{ item.address }}</div>
      <div class="hotel-info">{{ t('contact') }}: {{ item.contactInfo }}</div>
      <div class="hotel-info">{{ categoryType[route.query.type][$i18n.locale] }} {{ item.category }}</div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { useRedirect } from '@/pc/views/pages/redirect/hook'

const { t } = useI18n({
  messages: {
    zh: {
      contact: '联系方式',
    },
    en: {
      contact: 'Phone Number',
    },
  },
})
const redirect = useRedirect()
const route = useRoute()

defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
})

const categoryType = {
  1: { zh: '推荐菜系：', en: 'Recommended Cuisine:' },
  2: { zh: '', en: '' },
  3: { zh: '娱乐项目：', en: 'Entertainment Projects:' },
}

const goPage = (url) => {
  if (!url) return
  redirect.to(url)
}
</script>
