<!--
 * @Author: 王俊杰
 * @Date: 2024-08-26 09:24:58
 * @LastEditors: your name
 * @LastEditTime: 2024-09-09 15:21:27
 * @Description: 
 * @FilePath: /trade-exhibition/src/pc/views/pages/outside-trade-serve/pages/restaurant/restaurant.vue
-->

<style lang="scss" scoped>
.restaurant-wrapper {
  padding-bottom: 48px;
}
.filtrate_nav_wrapper {
  background: $basic-white;
  padding: 20px;
  margin-bottom: 20px;
}
.list-wrapper {
  display: flex;
}
.hotel-list_wrapper {
  margin-right: 16px;
  flex: 1;
  .hotel-item:not(:last-child) {
    border-bottom: 1px solid #edeef1;
  }
}

.pagination-wrapper {
  height: 40px;
  padding-top: 4px;
  display: flex;
  justify-content: flex-end;
  padding-right: 16px;
}
:deep() {
  .el-pager,
  .el-pagination__jump {
    display: flex;
  }
  .el-pagination__goto {
    width: 70px;
  }
  .el-pagination.is-background .btn-next,
  .el-pagination.is-background .btn-prev,
  .el-pagination.is-background .el-pager li {
    background: transparent !important;
    border: none !important;

    &:hover {
      color: $primary-color !important;
    }
  }
  .el-pagination.is-background .btn-next.is-active,
  .el-pagination.is-background .btn-prev.is-active,
  .el-pagination.is-background .el-pager li.is-active {
    color: $primary-color;
    background: transparent !important;
  }
}
</style>

<template>
  <div class="restaurant-wrapper">
    <div class="w-1260">
      <div class="filtrate_nav_wrapper">
        <FiltrateNav @onSelectDelicacy="onSelectDelicacy" @onSelectRegion="onSelectRegion" />
      </div>
      <div class="list-wrapper">
        <div class="hotel-list_wrapper">
          <div v-if="list.length">
            <template v-for="item in list" :key="item.id">
              <HotelItem :item="item" />
            </template>
          </div>
          <EmptyText v-else />
          <div class="pagination-wrapper">
            <el-pagination
              :current-page="currentPage"
              :page-size="pageSize"
              background="red"
              layout="total, prev, pager, next, jumper"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            >
              <span>前往</span>
            </el-pagination>
          </div>
        </div>
        <QrCode />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElPagination } from 'element-plus'
import EmptyText from '@/pc/components/empty-text/empty-text.vue'
import FiltrateNav from './components/filtrate-nav.vue'
import HotelItem from './components/hotel-item.vue'
import QrCode from './components/qr-code.vue'
import { entertainmentList, foodsList, hotelList } from '@/apis/outside-trade-serve.js'
import { useEvent } from '@/event'
import { LANG_CHANGED } from '@/event/modules/site'

const event = useEvent()

const route = useRoute()

// 列表 API 1.美食 2.住宿 3.娱乐
const LIST_API = {
  1: foodsList,
  2: hotelList,
  3: entertainmentList,
}

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
//分页该拜年
const handleSizeChange = (val) => {
  pageSize.value = val
  getList()
}
const handleCurrentChange = (val) => {
  currentPage.value = val
  getList()
}

const list = reactive([])

const categoryId = ref(null)
const districtId = ref(null)

// 获取列表
const getList = async () => {
  const res = await LIST_API[route.query.type]({
    pageNum: currentPage.value,
    pageSize: pageSize.value,
    categoryId: categoryId.value,
    districtId: districtId.value,
  })
  list.length = 0
  list.push(...res.records)
  total.value = res.total
}
getList()
// 分类选择
const onSelectDelicacy = (data) => {
  currentPage.value = 1
  categoryId.value = data?.id || null
  getList()
}
// 区域筛选
const onSelectRegion = (data = null) => {
  currentPage.value = 1
  districtId.value = data?.id || null
  getList()
}

event.on(LANG_CHANGED, async () => {
  await getList()
})
</script>
