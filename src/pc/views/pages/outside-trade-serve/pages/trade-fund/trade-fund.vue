<!--
 * @Author: 李兵 <EMAIL>
 * @Date: 2024-08-27 09:22:05
 * @LastEditors: 李兵 <EMAIL>
 * @LastEditTime: 2024-08-28 09:17:14
 * @FilePath: \trade-exhibition\src\views\pages\outside-trade-serve\pages\trade-fund\trade-fund.vue
 * @Description: 外贸综合服务----外贸基金
-->
<style scoped lang="scss">
.trade-fund {
  width: 100%;
  height: 664px;
  margin-bottom: 40px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
  .content {
    padding: 100px 0 0 100px;
    width: 600px;
    .content-title {
      font-size: 36px;
      font-weight: 600;
      line-height: 50px;
      margin-bottom: 29px;
      position: relative;
      &::before {
        display: inline-block;
        content: '';
        width: 216px;
        height: 1px;
        background: url('https://static.chinamarket.cn/static/trade-exhibition/outside-trade-serve/top-border.png');
        position: absolute;
        left: 0;
        top: -5px;
      }
      &::after {
        display: inline-block;
        content: '';
        width: 216px;
        height: 1px;
        background: url('https://static.chinamarket.cn/static/trade-exhibition/outside-trade-serve/bottom-border.png');
        position: absolute;
        left: 0;
        bottom: -5px;
      }
    }
    .data-title {
      font-size: 18px;
      line-height: 25px;
      color: $color-333333;
      margin-bottom: 16px;
    }
    .data-des {
      font-size: 14px;
      color: $regular-text;
      line-height: 20px;
      margin-bottom: 12px;
      width: 438px;
      padding-left: 12px;
      position: relative;
      &::before {
        content: '';
        display: inline-block;
        width: 4px;
        height: 4px;
        background: $primary-color;
        position: absolute;
        left: 0;
        top: 8px;
      }
    }
  }
}
</style>

<template>
  <div class="trade-fund" :style="{ backgroundImage: `url(${pagebgUrl})` }">
    <div class="content">
      <h2 class="content-title">{{ tradeData.title }}</h2>
      <dl>
        <dt class="data-title">{{ tradeData.data.title }}</dt>
        <dd v-for="item in tradeData.data.desList" :key="item.id" class="data-des">
          {{ item.des }}
        </dd>
      </dl>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { ossUrl } from '@/constants/common'

const { t } = useI18n()

const pagebgUrl = `${ossUrl}/outside-trade-serve/trade-fund-bg.png`

const tradeData = {
  title: computed(() => t('tradeFund.title')),
  data: {
    title: computed(() => t('tradeFund.subTitle')),
    desList: [
      {
        id: 1,
        des: computed(() => t('tradeFund.desc1')),
      },
      {
        id: 2,
        des: computed(() => t('tradeFund.desc2')),
      },
      {
        id: 3,
        des: computed(() => t('tradeFund.desc3')),
      },
    ],
  },
}
</script>
