<template>
  <div class="lower-finance">
    <CostFinance />
  </div>
</template>

<script setup>
import CostFinance from '@/pc/views/pages/outside-trade-serve/pages/cost-finance/cost-finance.vue'
</script>

<style lang="scss" scoped>
$wrap-width: 1250px;

:deep() {
  .cost-finance {
    overflow: hidden;

    .cost-finance-banner,
    .bank-list {
      width: $wrap-width;
      margin-left: auto;
      margin-right: auto;
    }

    .bank-list {
      margin-bottom: 100px;
    }

    .cost-finance-banner {
      background: transparent;
      flex-direction: column;

      &-wrap {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 700px;
        background-image: url('https://static.chinamarket.cn/static/trade-exhibition/outside-trade/banner-cost-finance.png');
        background-size: cover;
        background-position: center center;
        background-repeat: no-repeat;
      }
    }
  }
}
</style>
