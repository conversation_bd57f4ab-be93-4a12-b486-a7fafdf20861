<template>
  <div class="exhibition pb-56px pt-40px">
    <!-- 顶部 -->
    <div class="header">
      <el-carousel height="240px" arrow="hover">
        <el-carousel-item v-for="item in carouselList" :key="item.imgUrl">
          <el-image :src="item.imgUrl" class="w-full h-full" />
        </el-carousel-item>
      </el-carousel>
    </div>
    <!-- 推荐展会 -->
    <div class="recommend">
      <div class="title-header">
        <div class="title-text">推荐展会</div>
      </div>
      <!-- 列表内容 -->
      <div class="recommend-list">
        <!-- 列表项 -->
        <div class="list-item" v-for="item in recommendExhibitionList" :key="item.imgUrl">
          <el-image :src="item.imgUrl" />
        </div>
      </div>
    </div>
    <!-- 近期展会 -->
    <div class="nearest">
      <div class="title-header">
        <div class="title-text">近期展会</div>
      </div>
      <!-- 列表内容 -->
      <div class="nearest-list">
        <!-- 列表项 -->
        <div class="nearest-item" v-for="(item, index) in nearestList" :key="index">
          <div class="item-lt">
            <el-image :src="item.imgUrl" class="w-full h-full"></el-image>
          </div>
          <div class="item-rt">
            <div class="title">{{ item.title }}</div>
            <div class="content">
              <div>
                <div v-if="item.range">展会范围：{{ item.range }}</div>
                <div v-if="item.info">展会简介：{{ item.info }}</div>
              </div>
            </div>
            <div class="address">
              <div class="address-icon">展会地址</div>
              <div class="address-info text-[#666]">{{ item.address }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 全部展会 -->
    <div class="all">
      <div class="title-header">
        <div class="title-text">全部展会</div>
      </div>

      <div class="wrap">
        <el-tabs v-model="currentTab" class="all-tabs" stretch>
          <el-tab-pane label="综合" :name="1">
            <div class="tab-wrap">
              <div class="all-tab-item" v-for="(item, index) in compositeData" :key="index">
                <div class="lt">
                  <div class="lt-title">{{ item.title }}</div>
                  <div class="lt-address">
                    <span class="lt-address-icon">展会地址</span>
                    <span class="text-[#666]">{{ item.address }}</span>
                  </div>
                </div>
                <div class="rt">
                  <div class="month">{{ (dayjs(item.date).month() + 1).toString().padStart(2, '0') }}</div>
                  <div class="sprit">/</div>
                  <div class="year">
                    <div>月</div>
                    <div>{{ dayjs(item.date).year() }}</div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="五金建材" :name="2">
            <div class="tab-wrap">
              <div class="all-tab-item" v-for="(item, index) in materialData" :key="index">
                <div class="lt">
                  <div class="lt-title">{{ item.title }}</div>
                  <div class="lt-address">
                    <span class="lt-address-icon">展会地址</span>
                    <span class="text-[#666]">{{ item.address }}</span>
                  </div>
                </div>
                <div class="rt">
                  <div class="month">{{ (dayjs(item.date).month() + 1).toString().padStart(2, '0') }}</div>
                  <div class="sprit">/</div>
                  <div class="year">
                    <div>月</div>
                    <div>{{ dayjs(item.date).year() }}</div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="机械设备" :name="3">
            <div class="tab-wrap">
              <div class="all-tab-item" v-for="(item, index) in deviceData" :key="index">
                <div class="lt">
                  <div class="lt-title">{{ item.title }}</div>
                  <div class="lt-address">
                    <span class="lt-address-icon">展会地址</span>
                    <span class="text-[#666]">{{ item.address }}</span>
                  </div>
                </div>
                <div class="rt">
                  <div class="month">{{ (dayjs(item.date).month() + 1).toString().padStart(2, '0') }}</div>
                  <div class="sprit">/</div>
                  <div class="year">
                    <div>月</div>
                    <div>{{ dayjs(item.date).year() }}</div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="家居用品" :name="4">
            <div class="tab-wrap">
              <div class="all-tab-item" v-for="(item, index) in houseData" :key="index">
                <div class="lt">
                  <div class="lt-title">{{ item.title }}</div>
                  <div class="lt-address">
                    <span class="lt-address-icon">展会地址</span>
                    <span class="text-[#666]">{{ item.address }}</span>
                  </div>
                </div>
                <div class="rt">
                  <div class="month">{{ (dayjs(item.date).month() + 1).toString().padStart(2, '0') }}</div>
                  <div class="sprit">/</div>
                  <div class="year">
                    <div>月</div>
                    <div>{{ dayjs(item.date).year() }}</div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="食品&酒店用品" :name="5">
            <div class="tab-wrap">
              <div class="all-tab-item" v-for="(item, index) in foodsData" :key="index">
                <div class="lt">
                  <div class="lt-title">{{ item.title }}</div>
                  <div class="lt-address">
                    <span class="lt-address-icon">展会地址</span>
                    <span class="text-[#666]">{{ item.address }}</span>
                  </div>
                </div>
                <div class="rt">
                  <div class="month">{{ (dayjs(item.date).month() + 1).toString().padStart(2, '0') }}</div>
                  <div class="sprit">/</div>
                  <div class="year">
                    <div>月</div>
                    <div>{{ dayjs(item.date).year() }}</div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="服务贸易" :name="6">
            <div class="tab-wrap">
              <div class="all-tab-item" v-for="(item, index) in serviceData" :key="index">
                <div class="lt">
                  <div class="lt-title">{{ item.title }}</div>
                  <div class="lt-address">
                    <span class="lt-address-icon">展会地址</span>
                    <span class="text-[#666]">{{ item.address }}</span>
                  </div>
                </div>
                <div class="rt">
                  <div class="month">{{ (dayjs(item.date).month() + 1).toString().padStart(2, '0') }}</div>
                  <div class="sprit">/</div>
                  <div class="year">
                    <div>月</div>
                    <div>{{ dayjs(item.date).year() }}</div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="纺织服饰" :name="7">
            <div class="tab-wrap">
              <div class="all-tab-item" v-for="(item, index) in clothData" :key="index">
                <div class="lt">
                  <div class="lt-title">{{ item.title }}</div>
                  <div class="lt-address">
                    <span class="lt-address-icon">展会地址</span>
                    <span class="text-[#666]">{{ item.address }}</span>
                  </div>
                </div>
                <div class="rt">
                  <div class="month">{{ (dayjs(item.date).month() + 1).toString().padStart(2, '0') }}</div>
                  <div class="sprit">/</div>
                  <div class="year">
                    <div>月</div>
                    <div>{{ dayjs(item.date).year() }}</div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { ref } from 'vue'
import { ossUrl } from '@/constants/common'
import { clothData, compositeData, deviceData, foodsData, houseData, materialData, serviceData } from '../../data-options.js'

/* 轮播 */
const carouselList = [
  {
    imgUrl: `${ossUrl}/banner-zh.png`,
  },
  {
    imgUrl: `${ossUrl}/banner-alq.png`,
  },
  {
    imgUrl: `${ossUrl}/banner-db.png`,
  },
  {
    imgUrl: `${ossUrl}/banner-st.png`,
  },
]

/* 推荐 */
const recommendExhibitionList = [
  {
    imgUrl: `${ossUrl}/recommend-exhibition-zh.png`,
  },
  {
    imgUrl: `${ossUrl}/recommend-exhibition-alq.png`,
  },
  {
    imgUrl: `${ossUrl}/recommend-exhibition-db.png`,
  },
  {
    imgUrl: `${ossUrl}/recommend-exhibition-st.png`,
  },
]

/* 近期 */
const nearestList = [
  {
    imgUrl: `${ossUrl}/nearest-exhibition-bx.png`,
    title: '巴西国际建材展览会（Feicon）',
    range: '五金建材、门窗玻璃、室内外装修材料等',
    info: '涵盖多个民用领域，是南美地区规模最大、最具影响力的建筑行业贸易洽谈会之一',
    address: '巴西圣保罗',
  },
  {
    imgUrl: `${ossUrl}/nearest-exhibition-hskst.png`,
    title: '2025年第二届中国（临沂）-哈萨克斯坦国际商品展',
    range: '五金建材、劳保防护、光伏新能源等20大类',
    info: '为临沂地产品“走出去”提供有力保障，擦亮“临沂制造”招牌，为临沂企业拓展海外业务提供便利。',
    address: '哈萨克斯坦阿拉木图市',
  },
  {
    imgUrl: `${ossUrl}/nearest-exhibition-alq.png`,
    title: '阿联酋中国轮胎汽配展UAEChinaTyre&AutoPartsExpo',
    range:
      '轮胎轮毂、轮胎原材料、轮胎、拆卸工具、发动机装备、电子以及电子产品、汽车美容、护理产品等上届展会展出面积一万平方米，展位近300个，共组织来自山东、江西、河北、河南等地的中国企业参展。展会邀请了来自中东、非洲、中亚和南亚等地区的近万名专业买家到场洽谈，意向成交额达2.6亿美元',
    info: '',
    address: '阿联酋沙迦',
  },
]

/* 全部 */
const currentTab = ref(1)
</script>

<style lang="scss" scoped>
$wrap-width: 1250px;

.exhibition {
  width: $wrap-width;
  margin: 0 auto;
}

.title-header {
  padding: 24px 0;
  .title-text {
    font-size: 30px;
    color: #191919;
    font-weight: 600;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    &::before,
    &::after {
      content: '';
      display: block;
      width: 45px;
      height: 16px;
      margin: 0 12px;
      background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAAAgCAYAAACSEW+lAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAK7SURBVGiB7ZmxTttQFIb/c2+MQ6hCY0ETGEAqrpAqeI8sWSt1ZGPtG+QNWNkYkViz8B6gSiihUjoAKchpoxIcnHtPBxKIAjK+DikM9xuv/uP/+sjy9fFPGFAFxHaxOHt2Tbls1s0AQBj2+stz3N1ttW6qgMYLUIfvhoXOuhRyDYI8AIDmQGl1mm3nTz6h0XsRH8/LK+FWwKIM8NrdKp2C9KHUvdqnIOi8hE9zfqXQdaItYvoKYGOwfMzE+7nI2Vv987MNAAQADMhGqeSpvuM8dTGZiSL/4iIgQE2yqV+Li+8CJcs8bPAYpDnwpDr8cHn5dxKf7wsLS0LP7IDgPylgNLS4/fb56up8Ep8f75dWbwVqINp82oePZjQqH3+fN0UVEHFNBgDVd5xGqeRVAZF2U3X4blyTAYAFeYGS5Tp8N7WP5+VjmwwABF/omZ265+XT+jTnVwqxTQYAos1bgVpzfqUgtovF2bgmD1F9x9kuFmfTbiwsdNbjmjyEBXlhobOe1kcJtxLb5CEEXwm3ktan60RbsU2+96HNrhNtibNryiW9uIl2HCnk2jS0j2BRnop2jME7ObFWDA++JJhoR2GAkOBpvsdEO8IBIB8OviSYaEeqAImHgy8JG6nfuRYzRBj2+knFJtpRCGBoDhIXmGhH+AIogE6TV5hoR6ruvr6ODUqOxfIcd5OqTbTjKK0S35SJ9hGkD6eiHYOJ9020YrfVupGZKHpOLDNRtNtq3aTdWLadP6EETyppDrLt/ElaH6l7NTAazwoZDal7tbQ+ucjZA/PR8z58lIucPTuwTIDJwELDtaodwVORdAR/VRggHvwGmCYHgLz79JsuDEj+Dz4Wi8VisVgsllfHDiwTYjPDNzaC28wwJTYztJmhzQxtZmiIzQzfMDYzTIHNDAGbGdrMEHZgSYvNDPH2RvB/dk+ggddFU7AAAAAASUVORK5CYII=')
        no-repeat center center/100% 100%;
    }
  }
}

.recommend {
  display: flex;
  justify-content: center;
  margin: 0 auto;
  flex-direction: column;
  padding-bottom: 40px;
  .recommend-list {
    height: 200px;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 40px;
    .list-item {
      // cursor: pointer;
    }
  }
}

.nearest {
  padding-bottom: 40px;
}

.nearest-item {
  min-height: 200px;
  box-sizing: border-box;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;

  .item-rt {
    display: flex;
    flex-direction: column;
    .title {
      font-weight: 600;
      font-size: 20px;
      min-height: 30px;
    }
    .content {
      flex: 1;
      display: flex;
      align-items: center;
    }
    .address {
      height: 50px;

      .address-icon {
        display: inline;
        color: #edd1a8;
        padding: 2px 8px;
        background: linear-gradient(90deg, #6c5354 0%, #4a4e66 100%);
      }
      .address-info {
        line-height: 50px;
      }
    }
  }
  & + .nearest-item {
    margin-top: 40px;
  }
}

.tab-wrap {
  min-height: 300px;
  padding: 10px;
  .all-tab-item {
    height: 110px;
    box-sizing: border-box;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    border-radius: 8px;
    background: #ffffff;
    box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.1);
    & + .all-tab-item {
      margin-top: 20px;
    }
    .lt {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .lt-title {
        color: #3d3d3d;
        font-weight: 600;
        font-size: 20px;
        // border: 1px solid red;
      }
      .lt-address {
        .lt-address-icon {
          color: #edd1a8;
          padding: 2px 8px;
          background: linear-gradient(90deg, #6c5354 0%, #4a4e66 100%);
          margin-right: 10px;
        }
      }
    }
    .rt {
      width: 140px;
      color: #d8131a;
      display: flex;
      .month {
        font-size: 50px;
        font-weight: 600;
      }
      .sprit {
        font-size: 50px;
        margin: 0 6px;
      }
      .year {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        font-weight: 600;
      }
    }
  }
}
</style>
