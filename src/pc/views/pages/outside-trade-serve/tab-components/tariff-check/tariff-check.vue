<template>
  <div class="w-1260" style="margin-top: 40px; padding-bottom: 56px">
    <ServeStaticItem v-for="(item, i) in staticServeOptions" :key="i" :serveInfo="item" @handleShowCustomer="showCustomer" />
  </div>
  <Customer ref="customerRef" />
</template>

<script setup>
import Customer from '@/pc/components/customer/customer.vue'
import ServeStaticItem from '../../components/serve-static-item/index.vue'
import { staticServeOptions } from '../../data-options.js'

const customerRef = ref(null)
const showCustomer = () => {
  customerRef.value.init()
}
</script>
