<template>
  <div></div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { detectDeviceType } from '@/utils/utils'

const route = useRoute()
onMounted(() => {
  const deviceType = detectDeviceType()
  const path = `/register?userType=2&referralCode=${route.query.referralCode}`

  if (deviceType === 'Mobile' || deviceType === 'iPad') {
    window.location.href = import.meta.env.VUE_APP_MOBILE_URL + path
  } else {
    window.location.href = window.location.origin + path
  }
})
</script>

<style lang="scss" scoped></style>
