<!--
 * @Author: 李兵 <EMAIL>
 * @Date: 2024-08-24 16:20:17
 * @LastEditors: 李兵 <EMAIL>
 * @LastEditTime: 2024-08-24 16:23:38
 * @FilePath: \trade-exhibition\src\views\layout\layout-secondLevel.vue
 * @Description: 二/三级页面 layout
-->
<style lang="scss" scoped>
#main-content {
  width: $main-width;
  margin: 0 auto;
  padding: 0;
  min-height: $main-height;
  overflow-y: hidden;
}
</style>

<template>
  <div :class="customClass">
    <el-main id="main-content" class="main-content">
      <CustomBreadcrumb></CustomBreadcrumb>
      <router-view></router-view>
    </el-main>
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router'
import CustomBreadcrumb from '@/pc/components/custom-breadcrumb/custom-breadcrumb.vue'

const route = useRoute()
const customClass = computed(() => {
  return route.meta.class || 'bg-[#fff]'
})
</script>
