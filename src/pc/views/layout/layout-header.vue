<style lang="scss" scoped>
.layout-header-wrapper {
  height: 64px;
  left: 0;
  top: 0;
  width: 100%;
  min-width: $main-width;
  background: rgba(255, 255, 255, 0.2);
  z-index: 50;
  display: flex;
  justify-content: space-between;
  gap: 64px;
  padding: 0 64px;
  user-select: none;
  white-space: nowrap;
  transition: background 0.1s;

  &.red-bg {
    background: #d8131a;
    .layout-header-left {
      .nav-box {
        .nav-item.active {
          &::after {
            display: block;
            border-color: #fff;
          }
        }
        .national-pavilion {
          color: #fff;
          cursor: pointer;
          font-size: 16px;
        }
      }
    }
  }
  &.white-bg {
    background: #fff;
    box-shadow: 0px 4px 10px 0px rgba(49, 0, 0, 0.1);

    &.hide-boxshadow {
      box-shadow: none;
    }

    .logo-text {
      color: #333333;
    }

    .layout-header-left .nav-box {
      .nav-item {
        color: #333333;

        &.active {
          color: #d8131a;

          &::after {
            display: block;
            border-color: $primary-color;
          }
        }
      }
      .national-pavilion {
        color: #333333;
        .arrow {
          color: #333333 !important;
        }
      }
    }
    .layout-header-right {
      color: #333333;
      .lang-text,
      .input-box {
        color: #333333;
        i,
        input {
          color: #333333;
          &::placeholder {
            color: #999999;
          }
        }
      }
    }
  }
}

.layout-header-left {
  display: flex;
  align-items: center;
  height: 100%;

  .logo-box {
    height: 100%;
    cursor: pointer;
  }

  .nav-box {
    display: flex;
    align-items: center;
    height: 100%;

    .nav-item {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 15px;
      height: 100%;
      color: $basic-white;
      text-align: center;
      font-size: 16px;
      cursor: pointer;

      &::after {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: calc(100% - 30px);
        content: '';
        display: none;
        border-bottom: 5px solid #fff;
      }

      &.active {
        position: relative;

        &::after {
          display: block;
        }
      }
    }
    .national-pavilion {
      color: $basic-white;
      cursor: pointer;
      font-size: 16px;
      display: flex;
      align-items: center;
      .arrow {
        color: $basic-white !important;
      }
    }
  }
}

.layout-header-right {
  position: relative;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  color: $basic-white;
  margin-left: 40px;

  .input-box-out {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 16px;
    &.slide,
    &.hl {
      input::placeholder {
        color: #999;
      }
      input,
      .svg-icon {
        color: #333;
      }
    }
    &.slide {
      position: absolute;
      left: -260px;
      top: 0;
      background-color: #fff;
    }

    &.hl {
      background-color: #fff;
      flex-grow: 1;
      .input-box {
        border-color: #d8131a;
      }
    }
    &.slide-enter-active {
      transition: all 0.3s ease;
    }
    &.slide-enter-from {
      opacity: 0;
      transform: translateX(10px);
    }
  }
  .input-box {
    display: flex;
    align-items: center;
    width: 248px;
    padding: 6px 12px;
    border: 1px solid $color-D7D8D9;
    border-radius: 2px;
    color: $basic-white;

    .shuke-zoom {
      cursor: pointer;
    }

    input {
      flex-grow: 1;
      border: none;
      outline: none;
      background: transparent;
      color: $basic-white;
      margin-right: 4px;
    }
  }
  .input-suggest {
    position: absolute;
    top: 64px;
    left: 0;
    right: 0;
    padding: 0 16px 16px;
    background-color: #fff;
    box-shadow: 0px 4px 10px 0px rgba(49, 0, 0, 0.1);

    .suggestion-item {
      overflow: hidden;
      padding: 4px 12px;
      border-radius: 4px;
      font-size: 14px;
      color: #333;
      white-space: nowrap;
      text-overflow: ellipsis;
      transition: all 0.1s;
      cursor: pointer;

      &:hover {
        color: #d8131a;
        background-color: #ffeded;
      }
    }
  }
}

.nav-item-text {
  @include ellipsis(2);
  line-height: 1.4;

  &:hover {
    @include ellipsis(4);
  }
}

.enWrap {
  .nav-item-text {
    font-size: 14px !important;
    max-width: 160px;
    white-space: break-spaces;
    text-align: left;
  }

  .logo-box {
    margin-left: -80px;
  }
}

[dir='rtl'] {
  .enWrap {
    .logo-box {
      margin-left: 0;
    }
  }
}

.lang-text {
  color: #fff;
  line-height: 64px;
  cursor: pointer;
}
.menu-item {
  margin-right: 24px;

  &:last-child {
    margin-right: 0;
  }
}
.customer {
  display: flex;
  align-items: center;
  gap: 2px;
  line-height: 64px;
  cursor: pointer;
}
input::placeholder {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.5);
}

// @media screen and (max-width: 1420px) {
//   .layout-header-wrapper {
//     .layout-header-left {
//       gap: 10px;
//       .nav-box.need-small-gap {
//         gap: 10px;
//       }
//     }
//   }
// }
::v-deep {
  .skip-translate {
    &.active {
      color: #d8131a; // 选中语言的颜色
      font-weight: bold; // 选中语言的加粗样式
    }
  }
  .national-pavilion-item {
    &.active {
      color: #d8131a; // 选中语言的颜色
      font-weight: bold; // 选中语言的加粗样式
    }
  }
}

[dir='rtl'] .layout-header-right {
  .ml-6 {
    margin-right: 24px;
    margin-left: 0;
  }

  .mr-6 {
    margin-right: 0;
    margin-left: 24px;
  }
  .logout {
    margin-right: 4px;
  }

  .menu-item {
    margin-right: 0;
    margin-left: 24px;
  }
}
</style>

<template>
  <div
    ref="headerRef"
    class="layout-header-wrapper"
    :class="{ 'red-bg': isNeedRedBg, 'white-bg': isNeedWhiteBg, 'hide-boxshadow': hideBoxShadow }"
    :style="{
      position: onTheNavWhiteList ? 'fixed' : 'sticky',
    }"
  >
    <div class="w-1260 h-full flex justify-between" :class="{ enWrap: $storageLocale !== 'zh' }">
      <div class="layout-header-left">
        <div @click="handleLogoClick" class="flex items-center h-full">
          <img
            class="logo-box mr-2"
            :src="isNeedWhiteBg ? `${ossUrl}/logo/chinamarket-logo-b.png?v=1` : `${ossUrl}/logo/chinamarket-logo-w.png`"
            draggable="false"
            alt=""
          />
          <!-- UAE -->
          <div class="text-white font-bold logo-text" v-if="nationalTypeUae">
            <div class="text-[20px] pr-[35px]" v-if="$storageLocale !== 'en'">中国大集·阿联酋国家馆</div>
            <div v-else class="text-[14px] pr-[20px]">
              <div>Chinamarket - Overseas</div>
              <div>Digital Mall UAE National Pavilion</div>
            </div>
          </div>

          <!-- idn -->
          <div class="text-white font-bold logo-text" v-if="isNation('idn')">
            <div class="text-[14px] pr-[35px]" v-if="$storageLocale === 'indonesian'">Chinamarket Paviliun Indonesia</div>
            <div class="text-[14px] pr-[35px]" v-else-if="$storageLocale === 'en'">Chinamarket Indonesia Pavilion</div>
            <div class="text-[20px] pr-[35px]" v-else>中国大集·印度尼西亚国家馆</div>
          </div>
        </div>

        <div class="nav-box" v-if="!onlyUserTab">
          <template v-for="(item, index) in navListPreview" :key="index">
            <!-- 国家馆 -->
            <div class="nav-item" v-if="item.id === 'national'">
              <el-dropdown>
                <div class="national-pavilion" @click="handleNational">
                  <div class="nav-item-text" v-mode="{ ru: 'Оффшорный цифровой торговый город', tr: 'Denizaşırı Dijital Ticaret Platformu' }">海外数字商城</div>
                  <Icon class="arrow cursor-pointer mx-1 text-white" type="icon-xiajiantou" :size="16" />
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      class="national-pavilion-item"
                      :class="{ active: navCurrentPath.includes(item.path) }"
                      v-for="(item, index) in nationalPavilionList"
                      :key="index"
                      @click="toPage(item)"
                    >
                      <span v-mode="item.name">{{ item.name[$storageLocale] }}</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
            <!-- 外贸综合服务 -->
            <div class="nav-item" v-else-if="item.id === 'outsideTradeServe'">
              <el-dropdown>
                <div class="national-pavilion" @click="toPage(superServiceList[0], false, true)">
                  <div class="nav-item-text">{{ LOCALES.outside_trade_serve[$storageLocale] }}</div>
                  <Icon class="arrow cursor-pointer mx-1 text-white" type="icon-xiajiantou" :size="16" />
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item class="national-pavilion-item" v-for="(item, index) in superServiceList" :key="index" @click="toPage(item, false, true)">
                      <span>{{ item.name }}</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
            <template v-else>
              <div class="nav-item" :class="{ active: navCurrentPath.includes(item.path) }" @click="toPage(item)">
                <div class="nav-item-text" v-mode="item.name">{{ item.name[$i18n.locale] }}</div>
              </div>
            </template>
          </template>
        </div>
      </div>
      <div class="layout-header-right">
        <template v-if="!onlyUserTab">
          <LangAndPayType />
          <CartSymbol />
        </template>
        <template v-else>
          <el-dropdown class="menu-item">
            <div class="lang-text skip-translate">
              {{ PC_LANG_TYPE_MAP[$storageLocale] }}<Icon class="cursor-pointer mt-1 mx-1 text-white" type="icon-xiajiantou" :size="12" />
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  class="skip-translate"
                  :class="{ active: storageLocale === item.subKey }"
                  v-for="(item, index) in langArr"
                  :key="index"
                  @click="onChangeLang(item)"
                  >{{ item.name }}</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
        <!--        <div class="lang-text menu-item" @click="handleToHome" v-show="onlyUserTab">采购商端</div>-->
        <!-- UAE || IDN -->
        <div class="menu-item" v-if="nationalTypeUae || isNation('idn')">
          <!-- 如果是采购商登录了 -->
          <div v-if="userStore.userInfo?.userType === MERCHANTS_TYPE.BUYER.id" class="flex items-center">
            <span class="cursor-pointer" @click="mineClick">{{ t('mine') }}</span>
            <Icon class="ml-[4px] cursor-pointer logout" type="icon-tuichu" :size="20" @click="onLogout" />
          </div>
          <div v-else class="lang-text" @click="handleBuyerClick"><span v-enMode="'Login'">登录</span>/<span v-enMode="'Register'">注册</span></div>
        </div>
        <el-dropdown class="menu-item" v-else>
          <div class="lang-text">
            <!-- 如果是采购商 -->
            <template v-if="!onlyUserTab">
              <!-- 如果是采购商登录了 -->
              <div v-if="userStore.userInfo?.userType === MERCHANTS_TYPE.BUYER.id" class="flex items-center">
                <span class="cursor-pointer" @click="mineClick">{{ t('mine') }}</span>
                <Icon class="ml-[4px] cursor-pointer logout" type="icon-tuichu" :size="20" @click="onLogout" />
              </div>
              <div v-else class="lang-text">
                <span v-enMode="'Login'">登录</span>/<span v-enMode="'Register'">注册</span
                ><Icon class="cursor-pointer mt-1 mx-1 text-white" type="icon-xiajiantou" :size="12" />
              </div>
            </template>
            <!-- 如果是其他商户后台 -->
            <template v-else>
              <!-- 如果登录了 并且是需要登录的路由 显示 我的/退出 -->
              <div v-if="userStore.isLogined && route?.meta?.requiredLogin" class="flex items-center">
                <span class="cursor-pointer" @click="mineClick">{{ t('mine') }}</span>
                <Icon class="ml-[4px] cursor-pointer logout" type="icon-tuichu" :size="20" @click="onLogout" />
              </div>
              <!-- 否则就是登录页 显示 商户中心 -->
              <div v-else>
                <span v-enMode="'Login'">登录</span>/<span v-enMode="'Register'">注册</span
                ><Icon class="cursor-pointer mt-1 mx-1 text-white" type="icon-xiajiantou" :size="12" />
              </div>
            </template>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                class="national-pavilion-item"
                :class="{ active: merchantsActive(item) }"
                v-for="(item, index) in merchantsArr"
                :key="index"
                @click="handleMerchantsItemClick(item)"
              >
                <span v-mode="item.name">{{ item.name[$storageLocale] || item.name.en }}</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <div class="cursor-pointer menu-item h-full flex items-center leading-14px" @click="toRules">{{ t('rules') }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useWindowScroll } from '@vueuse/core'
import { useI18n } from 'vue-i18n'
import CartSymbol from '@/pc/components/cart-symbol/cart-symbol.vue'
import LangAndPayType from '@/pc/components/lang-pay-type/lang-pay-type.vue'
import { ossUrl } from '@/constants/common'
import { LANG_TYPE_ARRAY, PC_LANG_TYPE_MAP } from '@/constants/mall'
import { MERCHANTS_TYPE, MERCHANTS_TYPE_ARRAY } from '@/constants/merchants'
import { PAGE_HEADER_NAV_WHITH_LIST } from '@/pc/constants/router-white-list'
import { useUserStore } from '@/pc/stores'
import { getMerchantsLoginRoutePath, isMerchantsBackend } from '@/pc/hooks/merchants'
import { loginOut } from '@/apis/merchants'
import { useEvent } from '@/event'
import { LANG_CHANGED, OPEN_NEW_LOGIN } from '@/event/modules/site.js'
import { LOCALES, storageKey, useStorageLocale } from '@/i18n'

const nationalType = inject('$nationalType')
const nationalTypeUae = nationalType === 'uae'
const langArr = computed(() => {
  if (nationalTypeUae) {
    return LANG_TYPE_ARRAY.filter((item) => ['zh', 'en'].includes(item.subKey))
  }
  if (isNation('idn')) {
    return LANG_TYPE_ARRAY.filter((item) => ['zh', 'en', 'indonesian'].includes(item.subKey))
  }
  return LANG_TYPE_ARRAY
})
const { storageLocale } = useStorageLocale()
const { t } = useI18n({
  messages: {
    zh: {
      signIn: '登录/注册',
      mine: '我的',
      rules: '平台规则',
      wecom: {
        title: '客服中心',
        sub1: '请用微信扫描下方二维码',
        sub2: '客服经理竭诚为您服务',
      },
      logout: {
        confirm: '确定要退出当前帐号吗？',
        title: '退出当前账号',
        confirmBtn: '确定',
        cancelBtn: '取消',
        success: '已退出当前帐号',
      },
    },
    en: {
      signIn: 'Sign in',
      mine: 'Mine',
      rules: 'Rules',
      wecom: {
        title: 'Service Center',
        sub1: 'Please scan the QR code below with WeChat',
        sub2: 'Our customer service manager will assist you',
      },
      logout: {
        confirm: 'Are you sure you want to log out of the current account?',
        title: 'Logout current account',
        confirmBtn: 'Confirm',
        cancelBtn: 'Cancel',
        success: 'Logged out of the current account',
      },
    },
  },
})
const userStore = useUserStore()

const router = useRouter()
const route = useRoute()
const event = useEvent()

/* idn */
const { isNation } = useNation()

const onlyUserTab = computed(() => isMerchantsBackend(route.meta))
const hideBoxShadow = computed(() => ['shop', 'goodsDetail', 'goodsList', 'mall', 'serveDetail'].includes(route.name) || route.name?.includes('Pavilion'))
const onTheNavWhiteList = computed(() => {
  return PAGE_HEADER_NAV_WHITH_LIST.includes(route.path) || PAGE_HEADER_NAV_WHITH_LIST.includes(route.name)
})
const headerRef = ref(null)
const { x, y } = useWindowScroll()
watch(x, () => {
  if (!headerRef.value) return
  if (onTheNavWhiteList.value) {
    headerRef.value.style.transform = `translateX(-${x.value}px)`
  }
})
const merchantsArr = computed(() => {
  return MERCHANTS_TYPE_ARRAY
})
const merchantsActive = (item) => {
  if (item.id === MERCHANTS_TYPE.BUYER.id) {
    return !onlyUserTab.value && userStore?.userInfo?.userType === MERCHANTS_TYPE.BUYER.id
  }
  if (navCurrentPath.value.includes(item.path)) {
    return true
  }
  if (route.name === 'merchantsLogin') {
    return item.id === +route?.params?.id
  }
  return onlyUserTab.value && userStore?.userInfo?.userType === item.id
}

// 切换语言
const onChangeLang = (item) => {
  window.translate?.changeLanguage(item.key)
  localStorage.setItem(storageKey, item.subKey)
  event.emit(LANG_CHANGED, item.subKey)
  window.location.reload()
  document.documentElement.setAttribute('dir', item.subKey === 'ar' ? 'rtl' : 'ltr')
}

const navCurrentPath = computed(() => route.path)
const navList = ref([
  {
    id: 'home',
    name: { zh: '首页', en: 'Home' },
    path: '/home',
  },
  // {
  //   name: { zh: '逛展会', en: 'Visit Exhibition' },
  //   path: '/visit-exhibition',
  // },
  {
    id: 'market',
    name: { zh: '逛市场', en: 'Market' },
    path: '/market',
  },
  {
    id: 'mall',
    name: { zh: '在线商城', en: 'Online Store' },
    path: '/mall',
  },
  {
    id: 'uaePavilion',
    name: { zh: '精选样品库', en: 'Top Picks Showcase', indonesian: 'Sampel Pilihan' },
    path: '/uae-pavilion',
  },
  {
    id: 'idnPavilion',
    name: { zh: '精选样品库', en: 'Top Picks Showcase', indonesian: 'Sampel Pilihan' },
    path: '/indonesia-pavilion',
  },
  {
    id: 'national',
  },
  {
    id: 'outsideTradeServe',
  },
  {
    id: 'ai-daji',
    name: { zh: 'AI大集哥', en: 'AI Brother', indonesian: 'AI Pasar kakak laki-laki' },
    path: '/ai-daji',
  },
  // {
  //   id: 'government-services',
  //   name: { zh: '政府服务站群', en: 'Government Services', ru: 'Группа государственных служб' },
  //   path: '/government-services',
  // },
  // {
  //   name: { zh: '全球选品中心', en: 'Selection' },
  //   path: '/global-shop',
  // },
])

const navListPreview = computed(() => {
  if (nationalTypeUae) {
    return navList.value.filter((item) => {
      return !['national', 'government-services', 'idnPavilion'].includes(item.id)
    })
  }
  if (isNation('idn')) {
    return navList.value.filter((item) => {
      return !['national', 'government-services', 'uaePavilion'].includes(item.id)
    })
  }
  return navList.value.filter((item) => !['uaePavilion', 'idnPavilion'].includes(item.id))
})

const nationalPavilionList = ref([
  {
    id: 'uae-pavilion',
    name: LOCALES.usa_pavilion,
    path: `${import.meta.env.VUE_APP_WEB_URL_UAE}`,
  },
  {
    id: 'indonesia-pavilion',
    name: LOCALES.indonesia_pavilion,
    path: `${import.meta.env.VUE_APP_WEB_URL_IDN}`,
  },
  {
    id: 'sa-pavilion',
    name: LOCALES.sa_pavilion,
    path: '/sa-pavilion',
  },
  {
    id: 'thailand-pavilion',
    name: LOCALES.thai_pavilion,
    path: '/thailand-pavilion',
  },
])

// const currentNationalPavilion = computed(() => {
//   return nationalPavilionList.value.find((item) => navCurrentPath.value.includes(item.path)) || { name: LOCALES.national_pavilion }
// })

const superServiceList = ref([
  {
    id: 1,
    name: '关检汇税',
  },
  {
    id: 2,
    name: '国际物流',
  },
  {
    id: 3,
    name: '商城海外仓',
  },
  {
    id: 4,
    name: '国际展会',
  },
  {
    id: 5,
    name: '低成本融资',
  },
])

const handleLogoClick = () => {
  // 跳回主站
  if (nationalTypeUae || isNation('idn')) {
    window.open(import.meta.env.VUE_APP_WEB_URL_ZH)
    return
  }
  if (onlyUserTab.value) {
    return
  }
  toPage({ path: '/home' })
}
const handleNational = () => {
  const item = nationalPavilionList.value[0]
  toPage({
    ...item,
    path: '/uae-pavilion',
  })
}
const toPage = (item, openNewTab = false, isSuperService = false) => {
  if (item?.path?.startsWith('http')) {
    window.open(item?.path, '_blank')
    return
  }
  if (openNewTab) {
    const { href } = router.resolve(item?.path, '_blank')
    window.open(href)
    return
  }
  if (!isSuperService) {
    router.push(item?.path)
  } else {
    router.push(`/outside-trade-serve?tab=${item.id}`)
  }
}
const handleMerchantsItemClick = (item) => {
  // 如果是采购商
  if (item.id === MERCHANTS_TYPE.BUYER.id) {
    // 如果其他商户页面
    if (onlyUserTab.value) {
      toPage(item, true)
    } else {
      // 如果登录的是采购商
      if (userStore.userInfo?.userType && userStore.userInfo?.userType === item.id) {
        mineClick()
      } else {
        onLogin()
      }
    }
    return
  }
  // 已选中不能点击
  if (merchantsActive(item)) return
  toPage(item, true)
}
const handleBuyerClick = () => {
  const buyer = merchantsArr.value.find((item) => item.id === MERCHANTS_TYPE.BUYER.id)
  handleMerchantsItemClick(buyer)
}
// const handleToHome = () => {
//   router.push('/mall')
// }
// 是否需要红色白色导航栏
const isNeedRedBg = computed(() => {
  return !['merchantsLogin', 'home'].includes(route.name) || y.value > 0
})
// 是否需要显示白色导航栏
const isNeedWhiteBg = computed(() => {
  return !['merchantsLogin', 'home'].includes(route.name) && y.value > 0
})

// 客服中心弹窗
// const qrcodeVisible = ref(true)

// 显示的用户名
// const userPhone = computed(() => {
//   const info = userStore.userInfo || {}

//   return info?.mobile ?? ''
// })

// 我的点击进入相应的欢迎中心
const mineClick = () => {
  const { userType } = userStore.userInfo || {}
  const item = MERCHANTS_TYPE_ARRAY.find((item) => item.id === +userType)
  if (item && item.path) {
    router.push(item.path)
  }
}

// 点击登录/注册
const onLogin = () => {
  event.emit(OPEN_NEW_LOGIN, {})
}

const onLogout = async () => {
  try {
    await ElMessageBox.confirm(t('logout.confirm'), t('logout.title'), {
      confirmButtonText: t('logout.confirmBtn'),
      cancelButtonText: t('logout.cancelBtn'),
      type: 'warning',
    })
  } catch (e) {
    return
  }
  let path = getMerchantsLoginRoutePath(route)
  let isMerchantsBackendFlag = isMerchantsBackend(route.meta)
  await loginOut()
  userStore.logout()
  if (route.name !== 'merchantsEnter') {
    path = getMerchantsLoginRoutePath(route)
    isMerchantsBackendFlag = isMerchantsBackend(route.meta)
  }
  ElMessage.success(t('logout.success'))
  if (isMerchantsBackendFlag) {
    router.replace(path)
    return
  }
  event.emit(OPEN_NEW_LOGIN, {})
  router.push('/mall')
}

const toRules = () => {
  router.push('/rules')
}
</script>
