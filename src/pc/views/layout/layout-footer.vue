<template>
  <div class="layout-footer-wrapper">
    <div class="w-1260">
      <div class="highlights-wrapper">
        <div v-for="(item, index) in highlightsList" :key="index" class="highlights-item" :class="{ 'w-26': storageLocale === 'en' && index === 0 }">
          <div class="icon">
            <img :src="item.icon" alt="" />
          </div>
          <div class="text">{{ item.text }}</div>
        </div>
      </div>
      <div class="footer-content">
        <img class="logo-box" src="https://static.chinamarket.cn/static/trade-exhibition/logo/logo-black-footer.png" draggable="false" @click="goHome" />
        <div class="user-info-box">
          <div class="title">{{ t('contact') }}</div>
          <p>
            <span>{{ t('phone') }}</span
            ><span dir="ltr">+8619860959205</span> (<span>{{ t('workingDays') }}</span
            ><span dir="ltr">8:30-18:00</span>)
          </p>
          <p>{{ t('address') }}</p>
        </div>
        <div class="code-box">
          <div class="mobile-code text-left">
            <div class="title">{{ t('mobileVer') }}</div>
            <div class="label mb-12px">{{ mobileUrl }}</div>
            <div v-if="nationalType === 'cn'">
              <div class="title">{{ t('governmentServices') }}</div>
              <div class="label">{{ governmentServicesUrl }}</div>
            </div>
          </div>
          <!-- <div class="user-code">
            <img class="code" src="https://static.chinamarket.cn/static/trade-exhibition/wechat-work-qrcode.png" draggable="false" />
            <div class="label">{{ t('contact') }}</div>
          </div> -->
        </div>
      </div>
      <div class="filing-info-box">Copyright ◎ 临沂商城严选供应链管理公司 鲁ICP备2024130520号-1</div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { useStorageLocale } from '@/i18n'

const nationalType = inject('$nationalType')

const { storageLocale } = useStorageLocale()
const { t } = useI18n({
  messages: {
    zh: {
      contact: '联系我们',
      phone: '电话: ',
      workingDays: '工作日: ',
      address: '地址：山东省临沂市兰山区兰山街道临沂商谷智慧产业园C1号楼407室',
      tg: '商城严选供应链管理公司',
      mobileVer: '移动端网址',
      governmentServices: '政府服务站群',
    },
    en: {
      contact: 'Contact Us',
      phone: 'Phone: ',
      workingDays: 'Working days ',
      address:
        'Address：Room 407, Building C1, Linyi Business Valley Smart Industry Park, Lanshan Street, Lanshan District, Linyi City, Shandong Province, China',
      tg: 'Shangcheng Yanxuan Supply Chain Management Company',
      mobileVer: 'Mobile',
      governmentServices: 'Government Services',
    },
  },
})
const router = useRouter()
const goHome = () => router.push('/')
const mobileUrl = import.meta.env.VUE_APP_MOBILE_URL
const governmentServicesUrl = import.meta.env.VUE_APP_GOVERMENT_SERVICES_URL
const highlightsList = [
  {
    text: '国企信誉有保障',
    icon: 'https://static.chinamarket.cn/static/trade-exhibition/pc-footer/highlights-icon1.png',
  },
  {
    text: '经营户可靠',
    icon: 'https://static.chinamarket.cn/static/trade-exhibition/pc-footer/highlights-icon2.png',
  },
  {
    text: '物流成本低',
    icon: 'https://static.chinamarket.cn/static/trade-exhibition/pc-footer/highlights-icon3.png',
  },
  {
    text: '进出口便捷',
    icon: 'https://static.chinamarket.cn/static/trade-exhibition/pc-footer/highlights-icon4.png',
  },
]
</script>

<style lang="scss" scoped>
.layout-footer-wrapper {
  height: 326px;
  background: #efeff1;

  .w-1260 {
    padding: 20px 0 0px;
  }

  .filing-info-box {
    font-size: 14px;
    text-align: center;
    color: #999999;
  }

  .footer-content {
    display: flex;
    justify-content: space-between;
    gap: 64px;
    margin-bottom: 33px;

    .logo-box {
      height: 80px;
      cursor: pointer;
    }

    .title {
      color: #333333;
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 12px;
    }
    .user-info-box {
      p {
        font-size: 14px;
        color: #666666;

        &:nth-child(1) {
          margin-bottom: 12px;
        }
        &:nth-child(2) {
          margin-bottom: 12px;
        }
      }
    }
    .code-box {
      display: flex;
      align-items: center;
      flex-shrink: 0;

      .mobile-code {
        margin-right: 24px;
      }

      .code {
        margin-bottom: 4px;
        width: 80px;
        height: 80px;
        vertical-align: top;
      }
      .label {
        font-size: 14px;
        color: #666666;
      }
    }
  }
}

.highlights-wrapper {
  margin-bottom: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #e5e5e5;

  .highlights-item {
    display: flex;
    align-items: center;
    // flex: 1;

    .icon {
      width: 48px;
      height: 48px;
      margin-right: 10px;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
      }
    }
    .text {
      font-size: 18px;
      color: #333333;
      margin-right: 10px;
    }

    &.w-26 {
      width: 26%;
    }
  }
}
</style>
