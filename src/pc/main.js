// sort-imports-ignore
import 'vant/lib/index.css'
import 'normalize.css'
import 'virtual:uno.css'
import '@/common/scss/common.scss'
import '@/common/scss/element-reset.scss'
import '@/common/scss/reset.scss'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import createPiniaPersist from '@/pc/stores/plugins/persist'
import createPiniaBroadcastSyncPlugin from '@/pc/stores/plugins/broadcastSyncPlugin'
import App from './app.vue'
import components from '@/pc/components'
import router from '@/pc/router'
import directives from '@/common/js/directives'
import TrackingDirectives from '@/utils/tracking'
import vDialogDrag from '@/pc/utils/drag'
import i18n from '@/i18n'
import translatePlugin from '@/i18n/translatePlugin'
import translateToLang from '@/i18n/translateLang'
import { setToastDefaultOptions } from 'vant'
import Arms from '@/pc/utils/arms'

Arms.init()

setToastDefaultOptions({
  zIndex: 9000,
})
translateToLang.initTranslate()
// import disableDevtool from 'disable-devtool'
// "disable-devtool": "^0.3.8",

// if (import.meta.env.MODE === 'production') {
//   disableDevtool({
//     md5: 'd4579b2688d675235f402f6b4b43bcbf', // ddtk:do
//     url: 'https://bing.com',
//   })
// }

console.log('当前国家馆：', import.meta.env.VUE_APP_NATIONAL_TYPE)

const app = createApp(App)
const pinia = createPinia()
pinia.use(createPiniaPersist())
pinia.use(createPiniaBroadcastSyncPlugin())

// 存国家馆环境变量
app.config.globalProperties.$nationalType = import.meta.env.VUE_APP_NATIONAL_TYPE
app.provide('$nationalType', import.meta.env.VUE_APP_NATIONAL_TYPE)

app.use(router).use(pinia).use(i18n).use(translatePlugin).use(directives).use(TrackingDirectives).use(components).use(vDialogDrag).mount('#app')
