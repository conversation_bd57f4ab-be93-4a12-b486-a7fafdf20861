import { useI18n } from 'vue-i18n'

// import * as API from '@/apis/common'
// import event from '@/event'
// import { OPEN_AI_WINDOW } from '@/event/modules/site'
// import { debounce } from '@/common/js/util'
export const useRedirect = () => {
  return {
    to(url) {
      open(`${location.origin}/redirect?target=${encodeURIComponent(url)}`)
    },
  }
}
// 搜索结果类型枚举
const TYPES_MAP = {
  LOCAL: -1, // 本站页面
  MARKET: 1, // 逛市场
  PRODUCT: 2, // 沂采通商品
  FOREIGN: 3, // 采集的文章
  FOODS: 4, // 跳地图
  HOTELS: 5, // 跳地图
  PLAYS: 6, // 跳地图
}

// 站点地图
// const localPageMap = [
//   { keyword: ['逛市场'], path: '/market' },
//   {
//     keyword: ['大集哥', '大集AI'],
//     on() {
//       event.emit(OPEN_AI_WINDOW)
//     },
//   },
//
//   { keyword: ['多语言视频翻译'], path: '/ai-features/video-translation' },
//   { keyword: ['数字代言人', '数字人'], path: '/ai-features/person-square' },
//   // { keyword: ['AI商品图'], path: '/outside-trade/country-expo' },
//   {
//     keyword: ['AI翻译官'],
//     on() {
//       event.emit(OPEN_AI_WINDOW, {
//         agent: 'SC_TRANSLATION_BOT',
//       })
//     },
//   },
//
//   { keyword: ['国际交流 - 国际展会'], path: '/outside-trade/country-expo' },
//   { keyword: ['国际交流 - 外贸资讯'], path: '/outside-trade/foreing-info?type=FOREIGN_INFO' },
//   { keyword: ['国际交流 - 内外资源融合'], path: '/outside-trade/external-reaource?type=EXTERNAL_RESOURCE' },
//
//   { keyword: ['国际贸易 - 跨境贸易'], path: '/outside-trade/cross-border-trade' },
//   { keyword: ['国际贸易 - 国际物流'], path: '/outside-trade/country-exhibition' },
//   { keyword: ['国际贸易 - 海外仓储'], path: '/outside-trade/overseas-warehous' },
//
//   { keyword: ['外贸政务 - 海关窗口'], path: '/outside-trade/customs-introduce' },
//   { keyword: ['外贸政务 - 税务协调'], path: '/outside-trade/taxation?type=TAXATION' },
//   { keyword: ['外贸政务 - 采购商签证'], path: '/outside-trade/buy-visa?type=BUY_VISA' },
//
//   { keyword: ['国别商事 - 开设公司'], path: '/outside-trade/open-company' },
//   { keyword: ['国别商事 - 外汇交易'], path: '/outside-trade/forex-trading?type=FOREX_TRADING' },
//   { keyword: ['国别商事 - 财会事项'], path: '/outside-trade/financial-matter?type=FINANCIAL_MATTER' },
//
//   { keyword: ['政策支持 - 外贸基金'], path: '/outside-trade/trade-fund' },
//   { keyword: ['政策支持 - 低成本融资'], path: '/outside-trade/cost-finance' },
//   { keyword: ['政策支持 - 地方外贸资讯'], path: '/outside-trade/local-trade?type=LOCAL_TRADE' },
//
//   { keyword: ['外商生活 - 餐饮'], path: '/outside-trade/restaurant?type=1' },
//   { keyword: ['外商生活 - 住宿'], path: '/outside-trade/accommodation?type=2' },
//   { keyword: ['外商生活 - 娱乐'], path: '/outside-trade/entertainment?type=3' },
//
//   { keyword: ['在线商城'], path: '/mall' },
//   { keyword: ['全球选品中心'], path: '/global-shop' },
// ]

// 匹配本地内容
// const filterLocalPage = (input) => {
//   const result = []
//   localPageMap.forEach((localPage) => {
//     localPage.keyword.some((keyword) => {
//       if (keyword.includes(input.toUpperCase()) || input.includes(keyword)) {
//         result.push({
//           types: TYPES_MAP.LOCAL,
//           title: keyword,
//           url: localPage.path,
//           on: localPage.on,
//         })
//         return true
//       }
//       return false
//     })
//   })
//   return result.slice(0, 10)
// }

export const useSearch = () => {
  const router = useRouter()
  const route = useRoute()
  const redirect = useRedirect()
  const { t } = useI18n()

  // 不同页面搜索框接口不同
  // const api = {
  //   none: API.search,
  //   outsideTrade: API.outsideTradeSearch,
  // }[filter]
  // 输入框内容
  const inputValue = ref('')
  // 建议列表
  const suggestionList = reactive([])

  // const getSearchList = async () => {
  //   suggestionList.length = 0
  //
  //   // 优先匹配本地内容
  //   const localRes = filterLocalPage(inputValue.value)
  //   if (localRes.length > 0) {
  //     suggestionList.push(...localRes)
  //     return
  //   }
  //   // 匹配云端内容
  //   const remoteRes = await api({ keyword: inputValue.value })
  //   if (Array.isArray(remoteRes)) {
  //     suggestionList.push(...remoteRes)
  //   }
  // }
  // const debounceGetSearchList = debounce(getSearchList)
  const onSelectSuggestion = (option) => {
    if (!option && suggestionList.length === 0) {
      ElMessage.info(t('search.noContent'))
      return
    }
    if (!option) {
      option = suggestionList[0]
    }
    switch (option.types) {
      case TYPES_MAP.LOCAL:
        if (option.on) {
          option.on()
        } else if (/^https?/.test(option.url)) {
          redirect.to(option.url)
        } else {
          router.push(option.url)
        }
        break
      case TYPES_MAP.MARKET:
        if (route.path == '/market') {
          ElMessage.info(t('search.inCurrentPage'))
        } else {
          router.push(`/market?id=${option.marketId}&scroll=1`)
        }
        break
      case TYPES_MAP.FOREIGN:
      case TYPES_MAP.PRODUCT:
        redirect.to(option.url)
        break
      case TYPES_MAP.FOODS:
      case TYPES_MAP.HOTELS:
      case TYPES_MAP.PLAYS:
        redirect.to(`http://api.map.baidu.com/place/search?output=html&query=${option.title}&region=临沂市`)
        break
      default:
        break
    }
  }

  // watch(inputValue, (val) => {
  //   if (val.trim().length > 1) {
  //     debounceGetSearchList()
  //   } else {
  //     suggestionList.length = 0
  //   }
  // })

  return {
    searchInput: inputValue,
    suggestionList,
    onSelectSuggestion,
  }
}
