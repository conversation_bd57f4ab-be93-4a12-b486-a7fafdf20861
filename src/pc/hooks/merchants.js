import { IS_MERCHANTS_BACKEND } from '@/constants/merchants'
import { useUserStore } from '@/pc/stores'
import { getUserBaseInfo } from '@/apis/merchants'

// 是商户中心
export const isMerchantsBackend = (meta) => {
  return meta?.[IS_MERCHANTS_BACKEND]
}

// 无权限跳转
export const getMerchantsLoginRoutePath = (route) => {
  const { userType } = useUserStore()?.userInfo || {}
  if (route.name === 'merchantsAudit') {
    if (!userType) {
      return `/merchants-login/${route.query?.userType}`
    }
    return `/merchants-audit?userType=${route.query?.userType}`
  }
  if (route.name === 'merchantsLogin') {
    return route.path
  }
  if (userType) {
    return `/merchants-login/${userType}`
  }
  if (route.name === 'merchantsEnter') {
    return `/merchants-login/${route.query?.userType}`
  }
  const regex = /\/([\w-]+)\/[^/?]+(?:\/[^/?]+)*(\?.*)?/
  return route.path.replace(regex, (match, prefix, queryString) => {
    return `/${prefix}/login${queryString || ''}` // 保留查询参数
  })
}

// 身份校验
export const useMerchantsHooks = (userType) => {
  const router = useRouter()
  const userStore = useUserStore()

  // 无效的身份
  const invalidRole = (data) => {
    if (data?.userType && data?.userType !== userType) {
      router.replace({
        path: `/merchants-login/${userType}`,
      })
    } else {
      if (data?.auditStatus === 0) {
        router.replace({
          path: `/merchants-audit`,
          query: {
            userType,
          },
        })
      } else if (data?.auditStatus < 0) {
        router.replace({
          path: `/merchants-enter`,
          query: {
            userType,
          },
        })
      }
    }
  }

  // 获取用户信息
  const getUserInfo = async () => {
    try {
      const data = await getUserBaseInfo()
      userStore.setUserInfo(data)
      invalidRole(data)
    } catch (e) {
      console.log(e)
    }
  }

  onBeforeMount(() => {
    getUserInfo()
  })

  // 监听身份变化
  watch(
    () => userStore.userInfo,
    (val) => {
      invalidRole(val)
    },
    {
      deep: true,
    },
  )

  return {
    validRole: computed(() => userStore?.userInfo?.auditStatus > 0 && userStore?.userInfo?.userType === userType),
  }
}
