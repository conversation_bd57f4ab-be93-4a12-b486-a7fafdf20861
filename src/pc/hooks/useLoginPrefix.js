import { getPhonePrefixIds } from '@/apis/merchants'

// 缓存
let tempPrefixIdList = null

const translateTextFn = (list) => {
  return new Promise((resolve) => {
    try {
      window?.translate?.request?.translateText(
        list.map((item) => item.desc),
        function (data) {
          resolve(data.text.map((item, i) => ({ ...list[i], desc: item })))
        },
      )
    } catch (e) {
      resolve(list)
    }
  })
}

export default function useLoginPrefix(autoflag = true) {
  const prefixIdList = ref([
    {
      prefix: 86,
      desc: '中国',
      id: '2',
    },
  ])
  const getTranslateList = async () => {
    prefixIdList.value = await translateTextFn(prefixIdList.value)
  }
  const loading = ref(false)
  const getPrefixIdList = async () => {
    if (tempPrefixIdList?.length > 1) {
      return (prefixIdList.value = tempPrefixIdList)
    }
    try {
      loading.value = true
      prefixIdList.value = (await getPhonePrefixIds({ prefixType: 2 })) || []
      await getTranslateList()
      tempPrefixIdList = prefixIdList.value
    } catch (e) {
      await getTranslateList()
      console.log(e)
    } finally {
      loading.value = false
    }
  }

  onMounted(() => {
    if (autoflag) getPrefixIdList()
  })

  return {
    getPrefixIdList,
    prefixIdList,
    loading,
  }
}
