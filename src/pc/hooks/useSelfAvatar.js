/* eslint-disable no-undef */
import AudioPlayer from '/public/tts/index.esm'
import '@/pc/utils/base64.js'
import '@/pc/utils/crypto-js.js'
import { isPunctuation } from '@/utils/utils'

export default function () {
  const APPID = 'a7340f0d'
  const API_SECRET = 'MjgyZWFjYTUwYWI5MjM3MGUyNTA2MTcx'
  const API_KEY = '65de64b55e0c2834a7838705fe54a109'

  let ttsWS

  let playStr = '' // 播报字符串
  let languageCode = ''

  let audioPlayer = new AudioPlayer('/tts')
  //初始化SDK
  const initSDK = () => {
    audioPlayer.onPlay = () => {
      document.getElementById('video').style.zIndex = 3
    }
    audioPlayer.onStop = (audioDatas) => {
      document.getElementById('video').style.zIndex = 1
      if (playStr.trim()) {
        connectWebSocket(playStr, languageCode)
        console.log(playStr, '播报成功')
        playStr = ''
      }
      console.log(audioDatas)
    }
  }

  const getWebSocketUrl = (apiKey, apiSecret) => {
    var url = 'wss://tts-api.xfyun.cn/v2/tts'
    var host = location.host
    var date = new Date().toGMTString()
    var algorithm = 'hmac-sha256'
    var headers = 'host date request-line'
    var signatureOrigin = `host: ${host}\ndate: ${date}\nGET /v2/tts HTTP/1.1`
    var signatureSha = CryptoJS.HmacSHA256(signatureOrigin, apiSecret)
    var signature = CryptoJS.enc.Base64.stringify(signatureSha)
    var authorizationOrigin = `api_key="${apiKey}", algorithm="${algorithm}", headers="${headers}", signature="${signature}"`
    var authorization = btoa(authorizationOrigin)
    url = `${url}?authorization=${authorization}&date=${date}&host=${host}`
    return url
  }

  const encodeText = (text, type) => {
    if (type === 'unicode') {
      let buf = new ArrayBuffer(text.length * 4)
      let bufView = new Uint16Array(buf)
      for (let i = 0, strlen = text.length; i < strlen; i++) {
        bufView[i] = text.charCodeAt(i)
      }
      let binary = ''
      let bytes = new Uint8Array(buf)
      let len = bytes.byteLength
      for (let i = 0; i < len; i++) {
        binary += String.fromCharCode(bytes[i])
      }
      return window.btoa(binary)
    } else {
      return Base64.encode(text)
    }
  }

  const language = {
    zh: 'x4_chaoge', // 中文 aisjiuxu
    en: 'x4_chaoge', // 英文 aisjiuxu
    jp: '', // TODO 日文
    kor: '', // TODO 韩文
    ar: '', // TODO 阿拉伯文
  }
  /*
   * @param text: 待合成文本
   * @param msgid : 消息ID
   * @param lang: 语种 默认是中文
   */
  let textSplitStr = ''
  let messageId = ''
  const writeText = (text, msgid = '', lang = 'zh') => {
    // 把换行符\n去掉
    // textSplitStr += text.replace(new RegExp('\\n', 'g'), '')
    textSplitStr += text
    // 从后面往前循环 防止一次性返回所有问题，就怕整句话直接转换成语音
    for (let index = textSplitStr.length; index > 0; index--) {
      const item = textSplitStr[index]
      if (isPunctuation(item)) {
        let text = textSplitStr.slice(0, index + 1)
        // console.log('@@@@@', text, '数字人拆句')
        if (msgid && msgid !== messageId) {
          // 清空之前的 playStr
          playStr = ''
          languageCode = lang
          // 开始合成
          connectWebSocket(text, lang)
        } else {
          playStr += text
        }
        textSplitStr = textSplitStr.slice(index + 1)
        messageId = msgid
        break
      }
    }
    // ttsWS.close();
  }
  // 开始合成
  const connectWebSocket = (text, lang = 'zh') => {
    if (!language[lang]) return
    text = text || ''
    const url = getWebSocketUrl(API_KEY, API_SECRET)
    if ('WebSocket' in window) {
      ttsWS = new WebSocket(url)
    } else if ('MozWebSocket' in window) {
      ttsWS = new MozWebSocket(url)
    } else {
      alert('浏览器不支持WebSocket')
      return
    }

    ttsWS.onopen = () => {
      audioPlayer.start({
        autoPlay: true,
        sampleRate: 16000,
        resumePlayDuration: 1000,
      })
      var tte = 'UTF8'
      var params = {
        common: {
          app_id: APPID,
        },
        business: {
          aue: 'raw',
          auf: 'audio/L16;rate=16000',
          vcn: language[lang] || 'x4_chaoge',
          speed: 50,
          volume: 50,
          pitch: 50,
          bgs: 0,
          tte,
        },
        data: {
          status: 2,
          text: encodeText(text, tte),
        },
      }
      ttsWS.send(JSON.stringify(params))
    }
    ttsWS.onmessage = (e) => {
      let jsonData = JSON.parse(e.data)
      // console.error(jsonData);
      // 合成失败
      if (jsonData.code !== 0) {
        // console.error(jsonData);
        // changeBtnStatus('UNDEFINED')
        return
      }
      audioPlayer.postMessage({
        type: 'base64',
        data: jsonData.data.audio,
        isLastData: jsonData.data.status === 2,
      })
      if (jsonData.code === 0 && jsonData.data.status === 2) {
        ttsWS.close()
      }
    }
    ttsWS.onerror = (e) => {
      closeWebSocket()
      console.error(e)
    }
    ttsWS.onclose = (e) => {
      console.log(e)
    }
  }

  // 停止合成
  const closeWebSocket = () => {
    playStr = ''
    document.getElementById('video').style.zIndex = 1
    ttsWS?.close()
    audioPlayer.reset()
  }

  return {
    initSDK,
    writeText,
    connectWebSocket,
    closeWebSocket,
  }
}
