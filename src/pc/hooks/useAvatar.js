import AvatarPlatform, { SDKEvents } from '/public/avatar-sdk-web_3.0.3.1009/index'

const COMMON_INFO = {
  // 基础信息
  API_INFO: {
    serverUrl: 'wss://avatar.cn-huadong-1.xf-yun.com/v1/interact',
    appId: '8b27ed31',
    apiKey: '14c310d7b8fca37b6bc956957911ce24',
    apiSecret: 'ZTQwMDQ0NjY1YzVkNmMxNzM4NTIxYjdi',
    sceneId: '66397006335184896',
  },

  // 声音配置
  VOICE_CONFIG: {
    // 音色  x4_lingxiaoying_assist
    vcn: 'x4_mingge',
    // 语速
    speed: 65,
    // 音调
    pitch: 50,
    // 音量大小
    volume: 100,
  },

  // 数字人配置
  AVATAR_CONFIG: {
    // 110339003
    avatar_id: '110334002',
    // avatar_id: '110339003',
    scale: 1,
    move_h: 0,
    move_v: 0,
    audio_format: 0,
  },

  // 推流配置
  PUSH_CONFIG: {
    protocol: 'xrtc',
    // protocol: 'webrtc',
    alpha: 1,
    bitrate: 1000000,
    fps: 25,
  },

  BGSTING:
    '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',
}

export default function () {
  let interative = null

  //初始化SDK
  const initSDK = () => {
    try {
      if (!interative) {
        interative = new AvatarPlatform({
          useInlinePlayer: true,
          keepAliveTime: 10 * 1000,
        })
        interative
          .on(SDKEvents.asr, (asrData) => {
            console.log('sdk event: asr', asrData)
          })
          .on(SDKEvents.nlp, (nlpData) => {
            console.log('sdk event: nlp', nlpData)
          })
        console.log('初始化成功 可以开启后续实例方法调用')
      } else {
        console.log('请勿多次初始化 或先销毁当前实例')
      }
    } catch (e) {
      console.error(e)
    }
  }
  const setApiInfo = () => {
    if (!interative) {
      return console.log('请初始化sdk')
    }
    interative.setApiInfo({
      ...COMMON_INFO.API_INFO,
    })
    console.log('Api 服务信息设置成功 ')
  }

  //配置全局设置
  const setGlobalParams = (option = {}) => {
    if (!interative) {
      return console.log('请初始化sdk')
    }
    interative.setGlobalParams({
      avatar_dispatch: {
        interactive_mode: 0,
      },
      stream: {
        ...COMMON_INFO.PUSH_CONFIG,
      },
      avatar: {
        ...COMMON_INFO.AVATAR_CONFIG,
        width: option.width,
        height: option.height,
      },
      tts: {
        ...COMMON_INFO.VOICE_CONFIG,
      },
      background: {
        type: 'data',
        // data: COMMON_INFO.BGSTING
      },
    })
    console.log('全局 start 信息 设置成功 服务信息设置成功 ')
  }

  // 是否有资源 // 0（未加载资源）， 1（已加载资源），2（资源不可用）
  const hasResource = ref(0)
  // 连接虚拟人，拉流订阅，流播放
  const startAvatar = async (wrapper) => {
    if (!interative) {
      return console.log('请初始化sdk')
    }
    const loading = ElLoading.service({
      background: 'rgba(0,0,0,0.4)',
      lock: true,
    })
    await interative
      ?.start({
        wrapper,
      })
      .then(() => {
        hasResource.value = 1
        // ElMessage.success('数字人激活成功')
      })
      .catch((e) => {
        console.error(e.code, e.message, e.name, e.stack)
        switch (e.code) {
          case 11203:
            hasResource.value = 2
            ElMessage.warning('数字人通道无可用资源')
            break
        }
      })
      .finally(() => {
        loading.close()
      })
  }

  // 停止虚拟人
  const stopAvatar = async () => {
    if (!interative) {
      return console.log('请初始化sdk')
    }
    interative?.stop()
  }
  //销毁SDK
  const uninitSDK = () => {
    interative?.destroy()
    interative = undefined
  }

  const hasOperate = ref(false)
  // 打开声音
  const cancelMute = () => {
    if (hasOperate.value) return
    const player = interative.player
    if (!player) return
    player.resume()
    hasOperate.value = true
  }

  // const inputValue = ref('')
  // 文本驱动
  const writeText = async (text) => {
    if (!interative) {
      return console.log('请初始化sdk')
    }
    if (!text.trim()) return
    interative
      .writeText(text, {
        interactive_mode: 0,
        speed: 65,
        pitch: 50,
        volume: 100,
        nlp: false,
      })
      .then((reqId) => {
        // interrupt()
        console.log(`发送成功request_id: ${reqId}: ${text}`)
      })
      .catch(() => {
        console.log('发送失败，可以打开控制台查看信息')
      })
  }

  const isRecording = ref(false)
  // 开始录音
  const startRecord = () => {
    if (!interative) {
      return console.log('请初始化sdk')
    }
    const full_duplex = 0 // 0单轮 1全双工
    const single_seconds = 20
    const recorder = interative.recorder || interative?.createRecorder()
    recorder
      ?.startRecord(
        full_duplex ? 0 : single_seconds * 1000,
        () => {
          isRecording.value = false
        },
        {
          nlp: true,
        },
      )
      .then(() => {
        // interrupt()
        isRecording.value = true
      })
      .catch(() => {
        // ElMessage.error(e.message)
      })
  }
  // 停止录音
  const stopRecorder = () => {
    if (!interative) {
      return console.log('请初始化sdk')
    }
    isRecording.value = false
    const recorder = interative.recorder || interative.createRecorder()
    recorder?.stopRecord()
  }
  // 打断
  const interrupt = async () => {
    if (!interative) {
      return console.log('请初始化sdk')
    }
    interative.interrupt()
  }

  return {
    hasResource,
    isRecording,
    initSDK,
    setApiInfo,
    setGlobalParams,
    startAvatar,
    stopAvatar,
    uninitSDK,
    cancelMute,
    writeText,
    startRecord,
    stopRecorder,
    interrupt,
  }
}
