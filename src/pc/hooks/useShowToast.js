import { ElMessage } from 'element-plus'
import { showToast } from 'vant'
import { detectDeviceType, translateTextCustom } from '@/utils/utils'

export default function useShowToast() {
  let deviceType
  const showMessage = (obj) => {
    if (!deviceType) deviceType = detectDeviceType()
    if (deviceType === 'PC') {
      ElMessage(obj)
    } else {
      translateTextCustom(typeof obj === 'string' ? obj : obj.message).then((textList) => {
        showToast(textList[0])
      })
    }
  }

  return {
    showMessage,
    deviceType,
  }
}
