import { useRoute, useRouter } from 'vue-router'
import { MERCHANTS_TYPE, MERCHANTS_TYPE_ARRAY } from '@/constants/merchants'
import { notBakHome } from '@/pc/constants/router-white-list'
import { getUserAuditInfo } from '@/apis/merchants'

export default function useLoginSuccess(isH5 = false) {
  const loading = ref(false)
  const router = useRouter()
  const route = useRoute()

  const loginSuccessToPage = async ({ auditStatus, userType }, routerDisabled, loginSuccessRedirectPath = '') => {
    const isBuyer = MERCHANTS_TYPE.BUYER.id === userType
    const item = MERCHANTS_TYPE_ARRAY.find((item) => item.id === userType)
    // debugger
    try {
      // 如果是采购商页面
      if (isBuyer) {
        if (isH5) {
          window.location.href = import.meta.env.VUE_APP_MOBILE_URL
        } else {
          // 正常弹窗跳转
          if (!notBakHome(route) && !routerDisabled) {
            router.push(item.path)
          } else if (loginSuccessRedirectPath) {
            router.push(loginSuccessRedirectPath)
          }
        }
        return
      }

      // 判断审核状态
      if (auditStatus > 0) {
        if (isH5) {
          router.push(`/m/enter-audit?userType=${userType}`)
        } else {
          router.push(item.path)
        }
        return
      }

      loading.value = true
      const data = await getUserAuditInfo()
      // 如果没有入驻
      if (!data || typeof data === 'boolean') {
        if (isH5) {
          router.push({
            path: '/m/enter',
            query: {
              userType,
            },
          })
        } else {
          router.push({
            path: '/merchants-enter',
            query: {
              userType,
            },
          })
        }
        return
      }

      // 去审核页面
      if (isH5) {
        router.push(`/m/enter-audit?userType=${userType}`)
      } else {
        router.push({
          name: 'merchantsAudit',
          query: {
            userType,
          },
        })
      }
    } catch (e) {
      console.log(e)
    } finally {
      loading.value = false
    }
  }

  return {
    loginSuccessToPage,
    loading,
  }
}
