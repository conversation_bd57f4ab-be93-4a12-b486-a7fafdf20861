/*
 * @Author: 李兵 <EMAIL>
 * @Date: 2024-08-27 15:36:36
 * @LastEditors: your name
 * @LastEditTime: 2024-09-09 15:13:00
 * @FilePath: /trade-exhibition/src/pc/hooks/useList.js
 * @Description: 列表公用分页查询
 */
import { debounce } from '@/common/js/util'

const useList = ({ api }) => {
  // 接口
  const queryAPI = ref(api)

  // 分页
  const pagination = ref({
    pageNum: 1,
    pageSize: 10,
    total: 0,
  })

  // 数据
  const data = reactive([])

  // 接口请求
  const getData = async () => {
    const body = {
      pageNum: pagination.value.pageNum,
      pageSize: pagination.value.pageSize,
    }
    let res = await queryAPI.value(body)

    const { rowList, totalRecord } = res
    data.length = 0
    data.push(...rowList)
    pagination.value.total = totalRecord
  }

  const DEBOUNCE_TIME = 300
  // 获取列表数据防抖函数
  const deBounceGetData = debounce(() => {
    getData()
  }, DEBOUNCE_TIME)

  // 分页条数变化
  const handleSizeChange = (val) => {
    pagination.value.pageSize = val
    pagination.value.pageNum = 1
    deBounceGetData()
  }

  // 分页页数变化
  const handleCurrentChange = (val) => {
    pagination.value.pageNum = val
    deBounceGetData()
  }

  onMounted(() => {
    getData()
  })
  return {
    pagination,
    data,
    queryAPI,
    handleSizeChange,
    handleCurrentChange,
    getData,
  }
}

export default useList
