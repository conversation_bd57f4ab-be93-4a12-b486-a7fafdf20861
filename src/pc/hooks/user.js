import pinia, { useUserStore } from '@/pc/stores'
// import { getLoginUserInfo } from '@/apis/common.js'
import { getScyxUserInfo } from '@/apis/common.js'
import event from '@/event'
import { OPEN_NEW_LOGIN } from '@/event/modules/site.js'
import user from '@/pc/utils/user'

const userStore = useUserStore(pinia)

// 用户信息
const userInfo = ref(null)
// 获取用户信息的 promise
let getUserInfoPromise = null

// 删除用户信息
export const clearUserInfo = () => {
  getUserInfoPromise = null
  userInfo.value = null
}

// 数字人登录获取用户信息
/*
  @param {Boolean} forceUpdate 是否强制更新
*/
// export const getUserInfo = async (forceUpdate) => {
//   const token = user.getToken()
//   // token不存在不调用接口
//   if (!getUserInfoPromise && !forceUpdate && token) {
//     clearUserInfo()
//     getUserInfoPromise = await getLoginUserInfo()
//   }
//   if (!token) {
//     event.emit(OPEN_NEW_LOGIN, {})
//   }
//   userInfo.value = (await getUserInfoPromise?.userInfo) || {}
//   userStore.setUserInfo(userInfo.value || {})
//   // userInfo已经处理完成 并且是非强制更新 直接return
//   if (userInfo.value && !forceUpdate) {
//     return userInfo.value
//   }
//   return userInfo.value
// }

// 买家卖家获取用户信息
export const getUserInfo = async (forceUpdate) => {
  const token = user.getToken()
  // token不存在不调用接口
  if (!getUserInfoPromise && !forceUpdate && token) {
    clearUserInfo()
    userInfo.value = await getScyxUserInfo()
  }
  if (!token) {
    event.emit(OPEN_NEW_LOGIN, {})
  }

  userStore.setUserInfo(userInfo.value || {})
  return userInfo.value
}

// 使用用户信息
export const useUserInfo = () => {
  getUserInfo()
  return userInfo
}
