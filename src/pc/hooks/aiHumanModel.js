/*
 * @Author: 王俊杰 “<EMAIL>”
 * @Date: 2024-03-20 10:38:18
 * @LastEditors: your name
 * @LastEditTime: 2024-08-30 16:17:51
 * @FilePath: /trade-exhibition/src/hooks/aiHumanModel.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 获取数字人模版
import { reactive } from 'vue'
import { listAiHumanModel } from '@/apis/ai-features'

export const useHumanModel = () => {
  const humanList = reactive([])
  const getHumanList = async () => {
    try {
      const res = await listAiHumanModel()
      humanList.length = 0
      humanList.push(...res)
    } catch (error) {
      console.log(error)
    }
  }
  return { humanList, getHumanList }
}
