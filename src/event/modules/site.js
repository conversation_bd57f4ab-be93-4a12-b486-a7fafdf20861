export const LOGIN_SUCCESS = 'LOGIN_SUCCESS' // 登录成功事件
export const SITE_TOKEN_EXPIRED = 'SITE_TOKEN_EXPIRED' // token 无效事件
export const REAL_NAME_FAILURE_CODE = 'REAL_NAME_FAILURE_CODE' // 实名失效事件
export const SITE_OPEN_ACCOUNT = 'SITE_OPEN_ACCOUNT' // 打开开户流程弹窗事件
export const SITE_YLYH_OPEN_ACCOUNT = 'SITE_YLYH_OPEN_ACCOUNT' // 打开亿联银行开户流程弹窗事件
export const SITE_OPEN_CONTACT_SERVICE = 'SITE_OPEN_CONTACT_SERVICE' // 打开联系客服弹窗
export const SITE_OPEN_CREDIT = 'SITE_OPEN_CREDIT' // 打开开户流程弹窗事件
export const REFRESH_ENDORSEMENT = 'REFRESH_ENDORSEMENT' // 刷新背书账户列表
export const REFRESH_ACCOUNT = 'REFRESH_ACCOUNT' // 刷新电子账户列表
export const USER_CENTER_SCROLL_TO = 'USER_CENTER_SCROLL_TO' // 触发用户中心页面滚动到指定位置
export const WINDOW_SCROLL = 'WINDOW_SCROLL' // window滚动事件
export const MQTT_ORDER = 'MQTT_ORDER' // mqtt消息推送，交易状态/催单/客服介入等
export const MARKET_REFRESH = 'MARKET_REFRESH' // 刷新服务大厅
export const MQTT_BARGAIN = 'MQTT_BARGAIN' // 议价消息-刷新议价消息列表
export const BARGAIN_DIALOG = 'BARGAIN_DIALOG' // 还价弹窗
export const RECEIVE_ORDER_DETAIL = 'RECEIVE_ORDER_DETAIL' // 接单详情弹窗(议价里打开)
export const NOTIFICATION_UNREAD_COUNT = 'NOTIFICATION_UNREAD_COUNT' // 消息提醒未读数量
export const RECEIVE_ORDER_DETAIL_NOTIFICATION
  = 'RECEIVE_ORDER_DETAIL_NOTIFICATION' // 接单详情弹窗(消息推送、列表里打开)
export const NOTIFICATION_LIST = 'NOTIFICATION_LIST' // 消息中心列表刷新
export const TOOLBAR_REFRESH = 'TOOLBAR_REFRESH' // 工具栏刷新事件
export const OPEN_CUSTOMER_SERVICE_DIALOG = 'OPEN_CUSTOMER_SERVICE_DIALOG' // 客服弹窗
export const MARKET_ENTER_EVENT = 'MARKET_ENTER_EVENT' // 服务大厅回车事件
export const XUANDAN_DETAIL_NOTIFICATION = 'XUANDAN_DETAIL_NOTIFICATION' // 询单详情
export const ORDER_DETAIL_GO_BACK = 'ORDER_DETAIL_GO_BACK' // 订单详情的返回事件
export const ARTICLE_GO_BACK = 'ARTICLE_GO_BACK' // 文章详情的返回事件
export const NOTICE_UNREAD_COUNT = 'NOTICE_UNREAD_COUNT' // 平台公告数量获取
export const FULL_USER_RISK_TIPS = 'FULL_USER_RISK_TIPS' // 全量用户登录后风险提示
export const STOCK_USER_RISK_TIPS = 'STOCK_USER_RISK_TIPS' // 存量用户登录后风险提示
export const SDM_PROTOCOL_TIPS = 'SDM_PROTOCOL_TIPS' // 云豆阅读协议提示
export const REFRESH_PAGE = 'REFRESH_PAGE' // 通知刷新组件
export const REAL_NAME_CERTIFICATION = 'REAL_NAME_CERTIFICATION' // 打开实名认证流程弹窗事件

export const REAL_NAME_CERTIFICATION_CLOSE = 'REAL_NAME_CERTIFICATION_CLOSE' // 认证流程弹窗关闭事件
export const SITE_OPEN_ACCOUNT_CLOSE = 'SITE_OPEN_ACCOUNT_CLOSE' // 认证流程弹窗关闭事件

export const ORDER_REMIND_BYNUM = 'ORDER_REMIND_BYNUM' // 根据交易中的订单数量触发菜单的消息提醒

export const ZFB_OPENING_FAILED_TIPS = 'ZFB_OPENING_FAILED_TIPS' // 智付邦开通失败弹窗提醒

export const EVIDENCE_MATERIAL_TIPS = 'EVIDENCE_MATERIAL_TIPS' // 上传佐证材料提示

export const DOWNLOAD_CERTIFICATE = 'DOWNLOAD_CERTIFICATE' // 打开交易凭证事件

export const CHANNEL_SIGN_DIGLOG_CLOSE = 'CHANNEL_SIGN_DIGLOG_CLOSE' // 渠道签约流程弹窗关闭事件
export const OPEN_RISK_CONTROL_PUBLIC_WELFARE_VIDEO = 'OPEN_RISK_CONTROL_PUBLIC_WELFARE_VIDEO' // 打开风险防范公益视频
export const OPEN_SAFETY_INSTRUCTIONS_DIALOG = 'OPEN_SAFETY_INSTRUCTIONS_DIALOG' // 打开安全规则弹窗
export const CLOSE_RELEASE_DIALOG = 'CLOSE_RELEASE_DIALOG' // 关闭阻止发布票据弹窗
// E++全局事件变量
export const OPEN_BANK_LOGIN_AUTH_DIALOG = 'OPEN_BANK_LOGIN_AUTH_DIALOG' // 打开网银登录授权弹窗
export const OPEN_BANK_LOGIN_AUTH_SUCCESS = 'OPEN_BANK_LOGIN_AUTH_SUCCESS' // 网银登录授权成功
