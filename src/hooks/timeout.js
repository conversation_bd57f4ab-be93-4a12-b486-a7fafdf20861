import { onBeforeUnmount } from 'vue'

/**
 * 创建定时器
 * @param {function} callback 需要定时执行的回调函数，返回值为 true 时表示下次继续调用，返回 false 表示终止计时
 * @param {number} delay 定时时间
 * @returns {object} 包含 start 和 clear 方法的对象
 */
const useTimeout = (callback, delay = 1000) => {
  // setTimeout 返回的 id
  let timeoutId = null
  // 清除定时器
  const clear = () => {
    timeoutId && clearTimeout(timeoutId)
    timeoutId = null
  }
  // 开启定时器
  const start = () => {
    clear()
    // 是否继续执行定时器
    let continueTimeout = callback()
    if (continueTimeout) {
      timeoutId = setTimeout(start, delay)
    }
  }
  onBeforeUnmount(clear)

  return {
    start,
    clear,
  }
}

export default useTimeout
