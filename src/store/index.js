// 自动挂载指定目录下的store
// import { onZoom } from '@/common/js/screen-scale'
import Vue from 'vue'
import Vuex from 'vuex'
import { SDM_NAME, SDM_UNIT } from '@/constant'

Vue.use(Vuex)

let modules = {}

// @/store/module 目录下的文件自动挂载为 store 模块
const subModuleList = require.context('@/store/modules', false, /.js$/)
subModuleList.keys().forEach(subModule => {
  const moduleName = subModule.substring(2, subModule.length - 3)
  modules[moduleName] = subModuleList(subModule).default
})
const recognizeModuleList = require.context('@recognize/store/modules', false, /.js$/)
recognizeModuleList.keys().forEach(subModule => {
  const moduleName = subModule.replace(/^\.\/(.*)\.js$/, '$1')
  modules[`recognize-${moduleName}`] = recognizeModuleList(subModule).default
})

const store = new Vuex.Store({
  state: {
    // 如果是测试环境并且是broker环境的 取VUE_APP_BASE_BROKER_URL 其他环境不变
    baseUrl: process.env.VUE_APP_BASE_URL,
    // 商票版域名
    spbUrl: process.env.VUE_APP_SPB_URL,
    // 推广大使域名
    tgdsUrl: process.env.VUE_APP_TGPRO_URL,
    // 经纪商合伙人地址
    jjsUrl: process.env.VUE_APP_JJS_URL,
    // oss资源文件域名
    ossUrl: 'https://cdn.sdpjw.cn/static/shenduBihu',
    pageScale: 1, // 页面缩放比例
    sdmName: SDM_NAME,
    sdmUnit: SDM_UNIT
  },
  mutations: {
    // 设置页面缩放比例
    setPageScale(state, payload) {
      state.pageScale = payload
    }
  },
  actions: {},
  modules
})

// onZoom(zoom => {
//   store.commit('setPageScale', zoom)
// })

export default store
