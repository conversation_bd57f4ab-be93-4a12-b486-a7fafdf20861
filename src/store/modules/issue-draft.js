
/* eslint-disable no-shadow */
import { ISSUE_DRAFT_TYPE_LIST } from '@/constant'
import { QS_INCREATED_STOCK_COUNT } from '@/constant-storage'
import draftStockApi from '@/apis/draft-stock'
import issueDraftApi from '@/apis/issue-draft' // 接口
import { yuan2wan } from '@/common/js/number'
import Storage from '@/common/js/storage'

const state = {
  issueDraftType: ISSUE_DRAFT_TYPE_LIST.ORDINARY.id, // 发布票据的类型
  qsStockSummary: {}, // 识票助手票据库存统计
  increatedQsStock: 0, // 识票助手识别清单新增数量
  // 票方服务费数据(企承云)
  sellerServiceFee: {},
  splitRulesArr: [], // 识票助手拆分规则数据
}

const getters = {
  issueDraftType: state => state.issueDraftType,
  qsDiscernStock: state => state.qsDiscernStock,
  qsStockSummary: state => state.qsStockSummary,
  increatedQsStock: state => state.increatedQsStock,
  sellerServiceFee: state => state.sellerServiceFee,
  splitRulesArr: state => state.splitRulesArr
}

const mutations = {
  setState: (state, objects) => {
    Object.keys(objects).forEach(key => {
      state[key] = objects[key]
    })
  },
  // 设置发布类型
  setIssueDraftType: (state, value) => {
    state.issueDraftType = value
  },
  setQsStockSummary: (state, data) => {
    state.qsStockSummary = data || {}
  },
  setSplitRulesArr: (state, data) => {
    state.splitRulesArr = data || []
  },
  setIncreatedQsStock: (state, data) => {
    let count = Storage.get(QS_INCREATED_STOCK_COUNT)
    let total = count ? count * 1 + data : data
    state.increatedQsStock = total || {}
    Storage.set(QS_INCREATED_STOCK_COUNT, total)
  },
  clearIncreatedQsStock: (state, data) => {
    state.increatedQsStock = data || 0
    Storage.remove(QS_INCREATED_STOCK_COUNT)
  },
  // 票方服务费设置
  setSellerServiceFee: (state, data) => {
    state.sellerServiceFee = data || {}
  }

}

const actions = {
  // 识票助手票据库存数据统计
  getQsStockSummary(context, params) {
    return new Promise(resolve => {
      draftStockApi.getStockSummary(params).then(res => {
        res.draftAmt = res.draftAmt ? yuan2wan(res.draftAmt) : ''
        context.commit('setQsStockSummary', res)
        resolve(res)
      })
    })
  },
  // 票方支付服务费查询
  getSellerServiceFee(context, params) {
    return new Promise(resolve => {
      issueDraftApi.getPostOrderFee(params).then(res => {
        context.commit('setSellerServiceFee', res)
        resolve(res)
      })
    })
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
