import API from '@/apis/common'
export default {
  namespaced: true,
  state() {
    return {

    }
  },
  getters: { },
  mutations: { },
  actions: {
    // 获取当前用户的交易奖励数据
    getActivityCountAndAmt() {
      return new Promise(resolve => {
        try {
          API.getActivityCountAndAmt().then(data => {
            resolve(data)
          })
            .catch(() => {
              resolve({})
            })
        } catch (err) {
          resolve({})
        }
      })
    }
  }
}
