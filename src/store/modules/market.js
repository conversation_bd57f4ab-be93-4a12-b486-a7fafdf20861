import marketApi from '@/apis/market'
import Storage from '@/common/js/storage' // 本地缓存对象
import user from '@/utils/user'
import { formatTime } from '@/common/js/date'
import {
  INQUITY_NOTICE_NUMBER,
  MARKET_TERM_ADJUSTMENT
} from '@/constant-storage' // 服务大厅接单条件新旧版
const MARKET_WIN_LIST = ['marketOne', 'marketTwo', 'marketThree', 'marketFour', 'marketFive', 'marketSix', 'marketSeven', 'marketEight', 'marketNine', 'marketTen'] // 大厅窗口展示个数数组
export default {
  namespaced: true,
  state() {
    return {
      floatPreferencesHeight: 51, // 浮动收藏偏好高度
      accepterSelectVisible: false, // 默认是false
      bargainingCount: 0, // 议价中的所有议价数量(包括票方,资方)
      inquiryBargain: user.getInquiryNoticeNumber(),
      newMagCount: 0, // 议价/询单 生成随机数 用于控制图标闪烁
      predilectionCount: 50, // 32, 偏好数量  predilectionCount:-1为不限制
      marketWinList: [...MARKET_WIN_LIST], // 大厅窗口展示个数数组
      marketWinLastStr: MARKET_WIN_LIST[0], // 记录大厅最后一次打开值 用于大厅窗口数量达到上限后 再点击侧边栏回到最后一一侧打开的窗口
    }
  },
  getters: {
    floatPreferencesHeight: state => state.floatPreferencesHeight,
    bargainingCount: state => state.bargainingCount,
    inquiryBargain: state => state.inquiryBargain,
    newMagCount: state => state.newMagCount,
    predilectionCount: state => state.predilectionCount, // 偏好数量
  },
  mutations: {
    // 删除tebs的时候把当前的元素添加到数组
    setMarketWinList(state, item) {
      if (item && !state.marketWinList.includes(item)) {
        state.marketWinList.unshift(item)
      }
    },
    // 打开页面的时候 删除当前元素
    setEelMarketWinList(state, item) {
      if (item && state.marketWinList.includes(item)) {
        state.marketWinList = state.marketWinList.filter(v => v !== item)
        state.marketWinLastStr = item
      }
    },

    // 删除的时候  还原大厅的窗口标识代码数组
    setInitMarketWinList(state, item) {
      let winList = [...MARKET_WIN_LIST]
      if (item) {
        winList = MARKET_WIN_LIST.filter(v => v !== item)
      }
      state.marketWinList = winList
      state.marketWinLastStr = winList[winList.length - 1]
    },

    // 设置浮动收藏偏好高度
    setFloatPreferencesHeight(state, payload) {
      state.floatPreferencesHeight = payload
    },
    // 设置承兑人下拉框是否显示
    setAccepterSelectVisible(state, payload) {
      state.accepterSelectVisible = payload
    },
    // 设置议价中的所有议价数量
    setBargainingCount(state, payload) {
      state.bargainingCount = payload
    },
    // 设置询单数量
    setInquiryBargainCount(state, payload) {
      let { totalCount, sellerTotalCount, buyerTotalCount } = payload
      state.inquiryBargain = {
        totalCount,
        sellerTotalCount,
        buyerTotalCount,
      }
      Storage.set(INQUITY_NOTICE_NUMBER, state.inquiryBargain)
    },
    // 议价/询单 生成随机数 用于控制图标闪烁
    setNewMagCount(state, payload) {
      state.newMagCount = payload
    },
    // 获取偏好数量
    setPreferenceCount(state, payload) {
      state.predilectionCount = payload
    },
  },
  actions: {
    // 获取大厅查询月份的枚举值
    getCalculationInterestDay() {
      return new Promise(resolve => {
        const adjustmentDay = Storage.get(MARKET_TERM_ADJUSTMENT)
        const currentDate = formatTime(Date.now(), 'YYYY-MM-DD')
        // 如果本地缓存有值 && 缓存中的日期===当前日期（当天缓存的）不调用接口  直接返回缓存数据
        if (adjustmentDay && adjustmentDay.currentDate === currentDate) {
          resolve(adjustmentDay)
        } else {
          marketApi.getMarketBillTerm().then(data => {
            data.currentDate = currentDate
            Storage.set(MARKET_TERM_ADJUSTMENT, data)
            resolve(data)
          })
            .catch(() => {
              // 接口失败给默认值
              resolve({
                interestDay3: '90',
                interestDay6: '180',
                interestDay9: '270',
                interestDay12: '360',
              })
            })
        }
      })
    },
    // 偏好数量限制
    async getCondition(context) {
      const { preferenceAmount } = await marketApi.getPreferenceConfig()
      context.commit('setPreferenceCount', preferenceAmount)
      return preferenceAmount
    },
  },
}
