import commonApi from '@/apis/common'
import { formatTime, dealTime } from '@/common/js/date'
import { fen2yuan } from '@/common/js/number'
import Storage from '@/common/js/storage' // 本地缓存对象
import { SERVICE_RULE } from '@/constant-storage'

export default {
  namespaced: true,
  state() {
    return {
      appVersion: '', // 版本号
      sysTime: 0, // 当前服务器时间
      diffTime: 0, // 本地与服务器的时间差 单位是毫秒，大于0表示，本地时间比服务器时间快
      closeMarket: 0, // 当前是否节假日闭市
      closeMarketStartTime: 0, // 节假日闭市开始时间
      closeMarketEndTime: 0, // 节假日闭市结束时间
      closeMarketByEveryday: 0, // 当前是否每日闭市
      everydayStartTime: 0, // 每日闭市开始时间
      everydayEndTime: 0, // 每日闭市结束时间
      radarLimit: {}, // 自动限制信息
      fastLimit: {}, // 极速限制信息
      layoutAsideIsClose: false, // 侧边栏菜单是否收起
      yinServiceRule: '银票：按照票面金额万0标准收取，单笔起收0米、封顶0米；',
      caiServiceRule: '财票：按照票面金额万0标准收取，单笔起收0米、封顶0米；',
      shangServiceRule: '商票：按照票面金额千0标准收取，单笔起收0米、封顶0米。',
      oss: {}, // 上传OSS的token信息
      newVersionDraftOpen: null,
      sellerBankAccountId: null,
      limitOrderInfo: {}, // 限制接单配置数据
      batchSignNum: null, // 邦+批量签收解付订单最大笔数
      limitReleaseInfo: {}, // 限制发布配置数据
      mobilePattern: 0, // 是否开启联系方式模式 1开启 0关闭
      zbBankPlusOpenFlag: 0, // 众邦+是否开启 0开启  1关闭
      publicConfig: {}, // 后台配置类数据
      silentUserTradeRemind: {}, // 沉默户交易的提醒 remindType 1-不提醒 2-20到30天提醒 3-超过30天提醒
      // nacosConfig: {} // nacos配置类数据集合
      inDisConnectionWhiteCorp: 0, // 是否E++白名单内企业 1关闭 0开启
      startCrawlerScheme: 0, // E++是否开启爬虫方案 1开启 0关闭
      publishContent: {}, // 发布内容提示数据
      batchInQueryNum: 0, // 最大批量询单数

    }
  },
  getters: {
    sysTime: state => state.sysTime,
    diffTime: state => state.diffTime,
    mobilePattern: state => state.mobilePattern,
    zbBankPlusOpenFlag: state => state.zbBankPlusOpenFlag,
    limitReleaseInfo: state => state.limitReleaseInfo,
    silentUserTradeRemind: state => state.silentUserTradeRemind,
    inDisConnectionWhiteCorp: state => state.inDisConnectionWhiteCorp,
    startCrawlerScheme: state => state.startCrawlerScheme,
    publishContent: state => state.publishContent,
    batchInQueryNum: state => state.batchInQueryNum
    // nacosConfig: state => state.nacosConfig,
  },
  mutations: {
    // 设置阿里云OSS Token信息
    updateOssPolicy(state, payload) {
      state.oss = payload
    },
    updateAppVersion(state, payload) {
      state.appVersion = payload
    },
    // 保存手续费收取情况
    SET_SERVICE_RULE(state, { data, name }) {
      const { bankFeeRate, bankFeeMin, bankFeeMax, financialFeeRate, financialFeeMin, financialFeeMax, commercialFeeRate, commercialFeeMin, commercialFeeMax } = data
      state.yinServiceRule = `银票：按照票面金额万${bankFeeRate}标准收取，单笔起收${bankFeeMin}${name}、封顶${bankFeeMax}${name}；`
      state.caiServiceRule = `财票：按照票面金额万${financialFeeRate}标准收取，单笔起收${financialFeeMin}${name}、封顶${financialFeeMax}${name}；`
      state.shangServiceRule = `商票：按照票面金额万${commercialFeeRate}标准收取，单笔起收${commercialFeeMin}${name}、封顶${commercialFeeMax}${name}。`
    },
    // 更新服务器时间
    updateSysTime(state, payload) {
      if (!payload) {
        return
      }
      state.sysTime = payload
      const localTime = new Date().getTime()
      this.commit('common/updateDiffTime', payload ? localTime - payload : 0)
    },
    // 更新服务器与本地的时间差
    updateDiffTime(state, payload) {
      state.diffTime = payload
    },
    // 更新闭市
    updateCloseMarket(state, closeObj) {
      state.closeMarket = closeObj?.closeMarket || 0
      state.closeMarketStartTime = closeObj?.startTime || 0
      state.closeMarketEndTime = closeObj?.endTime || 0
      state.closeMarketByEveryday = closeObj?.closeMarketByEveryday || 0
      state.everydayStartTime = closeObj?.everydayStartTime || 0
      state.everydayEndTime = closeObj?.everydayEndTime || 0
    },
    // 更新违约限制自动交易
    updateRadarLimit(state, obj) {
      const { punishIng = 0, triggerPunishTime = '', day } = obj
      let startTime = ''
      let endTime = ''
      if (punishIng) {
        startTime = dealTime(triggerPunishTime)
        // 计算恢复时间
        const startDate = new Date(startTime)
        endTime = startDate.setDate(startDate.getDate() + 1 + day)
        // 格式化
        startTime = formatTime(startTime, 'YYYY-MM-DD')
        endTime = formatTime(endTime, 'YYYY-MM-DD')
      }
      state.radarLimit = {
        ...obj,
        startTime,
        endTime
      }
    },
    // 获取违约限制极速交易
    updateFastLimit(state, obj) {
      const { punishIng = 0, triggerPunishTime = '', day } = obj
      let startTime = ''
      let endTime = ''
      if (punishIng) {
        startTime = dealTime(triggerPunishTime)
        // 计算恢复时间
        const startDate = new Date(startTime)
        endTime = startDate.setDate(startDate.getDate() + 1 + day)
        // 格式化
        startTime = formatTime(startTime, 'YYYY-MM-DD')
        endTime = formatTime(endTime, 'YYYY-MM-DD')
      }
      state.fastLimit = {
        ...obj,
        startTime,
        endTime
      }
    },
    // 更新侧边菜单栏是否收起
    updateLayoutAsideIsClose(state, payload) {
      state.layoutAsideIsClose = payload
    },
    // 更新新票配置
    updateNewVersionDraftConfig(state, res) {
      // state.nacosConfig = res
      state.newVersionDraftOpen = res.openSwitch
      state.newVersionDraftSplit = fen2yuan(res.minAmount)
      state.sellerBankAccountId = res.sellerBankAccountId ? Number(res.sellerBankAccountId) : ''
      state.batchSignNum = res.batchSignNum ? Number(res.batchSignNum) : '' // 邦+批量签收解付订单最大笔数
      state.limitOrderInfo.payAmountMax = res.payAmountMax // 票方回款账户选择亿联一般户且订单应付金额≥payAmountMax万元时
      state.limitOrderInfo.payAmountMin = res.payAmountMin // 票方回款账户选择非亿联一般户且订单应付金额≥payAmountMin万元时
      state.limitOrderInfo.todayIsHoliday = res.todayIsHoliday // 工作日 0是工作日 1是节假日
      state.limitOrderInfo.holidayTime = res.holidayTime // 节假日提示延迟到账时间范围
      state.limitOrderInfo.workdayHintTime = res.workdayHintTime // 工作日 提示延迟到账时间范围
      state.limitOrderInfo.workdayTime = res.workdayTime // 工作日 提示跨行转账限额时间范围
      state.limitOrderInfo.fasterTradeOrderWorkdayLimitTime = res.fasterTradeOrderWorkdayLimitTime // 光速订单 工作日 限制接单时间
      state.limitOrderInfo.fasterTradeOrderHolidayLimitTime = res.fasterTradeOrderHolidayLimitTime // 光速订单 节假日 限制接单时间
      state.limitOrderInfo.fasterTradeOrderGeneralAccountLimitAmt = res.fasterTradeOrderGeneralAccountLimitAmt // 光速订单 一般户 限制接单金额 万
      state.limitOrderInfo.fasterTradeOrderLimitAmt = res.fasterTradeOrderLimitAmt // 光速订单 非一般户 限制接单金额 万
      state.limitOrderInfo.ylWorkDayWorkTimeTakeOrderMin = res.ylWorkDayWorkTimeTakeOrderMin // 跨行（非一般户）接单配置金额
      state.limitOrderInfo.ylWorkDayWorkTimeConfirmOrderMin = res.ylWorkDayWorkTimeConfirmOrderMin // 跨行（非一般户）确认订单配置金额
      state.limitOrderInfo.ylCueQuotaAmt = res.ylCueQuotaAmt // E+提示限额
      // 邦+限制接单参数
      state.limitOrderInfo.payAmountMaxZb = res.payAmountMaxZb // 票方回款账户选择亿联一般户且订单应付金额≥payAmountMax万元时
      state.limitOrderInfo.payAmountMinZb = res.payAmountMinZb // 票方回款账户选择非亿联一般户且订单应付金额≥payAmountMin万元时
      state.limitOrderInfo.holidayTimeZb = res.holidayTimeZb // 节假日提示延迟到账时间范围
      state.limitOrderInfo.workdayHintTimeZb = res.workdayHintTimeZb // 工作日 提示延迟到账时间范围
      state.limitOrderInfo.workdayTimeZb = res.workdayTimeZb // 工作日 提示跨行转账限额时间范围
      state.limitOrderInfo.fasterTradeOrderWorkdayLimitTimeZb = res.fasterTradeOrderWorkdayLimitTimeZb// 光速订单 工作日 限制接单时间
      state.limitOrderInfo.fasterTradeOrderHolidayLimitTimeZb = res.fasterTradeOrderHolidayLimitTimeZb // 光速订单 节假日 限制接单时间
      state.limitOrderInfo.fasterTradeOrderGeneralAccountLimitAmtZb = res.fasterTradeOrderGeneralAccountLimitAmtZb // 光速订单 一般户 限制接单金额 万
      state.limitOrderInfo.fasterTradeOrderLimitAmtZb = res.fasterTradeOrderLimitAmtZb // 光速订单 非一般户 限制接单金额 万
      state.limitOrderInfo.zbWorkDayWorkTimeTakeOrderMin = res.zbWorkDayWorkTimeTakeOrderMin // 跨行（非一般户）接单配置金额
      state.limitOrderInfo.zbWorkDayWorkTimeConfirmOrderMin = res.zbWorkDayWorkTimeConfirmOrderMin // 跨行（非一般户）确认订单配置金额
      state.limitOrderInfo.zbCueQuotaAmt = res.zbCueQuotaAmt // 邦+提示限额
      state.limitReleaseInfo = {
        generalPostFlag: res.generalPostFlag, // 是否关闭前端发布  1关闭 0开启
        republishFlag: res.republishFlag, // 是否关闭重新发布  1关闭 0开启
        republishFailFlag: res.republishFailFlag, // 是否关闭失败后重新发布  1关闭 0开启
        discernBatchPostFlag: res.discernBatchPostFlag, // 是否关闭识票据批量发布  1关闭 0开启
        signOrderForceCheckTicket: res.signOrderForceCheckTicket, // 签收环节是否强制进行验票  1关闭 0开启
        postOrderFlag: !!res.postOrderFlag // 是否发布白名单内 true => 在白名单内
      }
      localStorage.setItem('generalPostFlag', res.generalPostFlag) // 本地存储 是否关闭前端发布  1关闭 0开启
      // state.limitReleaseInfo.generalPostFlag = res.generalPostFlag // 是否关闭前端发布  1关闭 0开启
      // state.limitReleaseInfo.republishFlag = res.republishFlag // 是否关闭重新发布  1关闭 0开启
      // state.limitReleaseInfo.republishFailFlag = res.republishFailFlag // 是否关闭失败后重新发布  1关闭 0开启
      // state.limitReleaseInfo.discernBatchPostFlag = res.discernBatchPostFlag // 是否关闭识票据批量发布  1关闭 0开启
      // state.limitReleaseInfo.signOrderForceCheckTicket = res.signOrderForceCheckTicket // 签收环节是否强制进行验票  1关闭 0开启
      state.mobilePattern = res.mobilePattern// 是否开启联系方式模式 1开启 0关闭
      state.zbBankPlusOpenFlag = res.zbBankPlusOpenFlag || 0
      // 沉默户交易的提醒
      state.silentUserTradeRemind = {
        remindType: res.remindType, // 1-不提醒 2-20到30天提醒 3-超过30天提醒
        exceedingDay: res.exceedingDay, // 超过${exceedingDay}天数提醒
        nearDay: res.nearDay // 近${nearDay}天内提醒
      }
      state.inDisConnectionWhiteCorp = res.inDisConnectionWhiteCorp || 0 // 是否在E++白名单内 1-是 0-否
      state.startCrawlerScheme = res.startCrawlerScheme || 0 // E++是否开启爬虫方案 1开启 0关闭
      // 最大批量询单数
      state.batchInQueryNum = res.batchInQueryNum || 0
      // state.zbBankPlusOpenFlag = 1
    },

    // 更新公有配置数据
    updatePublicConfig(state, res) {
      Object.assign(state.publicConfig, res)
    },
    setPublishContent(state, res) {
      state.publishContent = res || {}
    }
  },
  actions: {
    // 获取阿里云OSS TOKEN
    async getOssPolicy(context) {
      try {
        const MINUTE = 10 // 每10分钟刷新一次
        const policy = await commonApi.getUploadPolicy({ image: 'DRAFT' })
        context.commit('updateOssPolicy', policy)

        setTimeout(() => {
          context.dispatch('getOssPolicy')
        }, MINUTE * 60 * 1000)
      } catch (err) {
        // eslint-disable-next-line no-console
        console.log(err)
      }
    },

    // 获取当前服务器的时间
    async getSysCurrentTime(context) {
      try {
        const sysTime = await commonApi.getSysCurrentTime()
        const localTime = new Date().getTime()
        context.commit('updateSysTime', sysTime)
        context.commit('updateDiffTime', sysTime ? localTime - sysTime : 0)

        return sysTime
      } catch (err) {
        // eslint-disable-next-line no-console
        console.log(err)
      }
    },

    // 获取当前闭市的配置
    async getCloseMarket(context) {
      try {
        const res = await commonApi.getCloseMarket()
        context.commit('updateCloseMarket', res)
      } catch (err) {
        // eslint-disable-next-line no-console
        console.log(err)
      }
    },
    // 获取违约限制自动交易
    async getRadarLimit(context) {
      try {
        const res = await commonApi.getRadarLimit()
        context.commit('updateRadarLimit', res || {})
      } catch (err) {
        // eslint-disable-next-line no-console
        console.log(err)
      }
    },
    // 获取违约限制极速交易
    async getFastLimit(context) {
      try {
        const res = await commonApi.getFastLimit()
        context.commit('updateFastLimit', res || {})
      } catch (err) {
        // eslint-disable-next-line no-console
        console.log(err)
      }
    },
    // 获取服务费规则
    async getServiceRule({ commit, rootState }) {
      // const rule = Storage.get(SERVICE_RULE)
      const currentDate = formatTime(Date.now(), 'YYYY-MM-DD')
      // // 如果本地缓存有值 && 缓存中的日期===当前日期（当天缓存的）不调用接口  直接返回缓存数据
      // if (rule && rule.currentDate === currentDate) {
      //   commit('SET_SERVICE_RULE', { data: rule, name: rootState.sdmUnit })
      // } else {
      const data = await commonApi.getServicesRule() || {}
      data.currentDate = currentDate
      Storage.set(SERVICE_RULE, data)
      commit('SET_SERVICE_RULE', { data, name: rootState.sdmUnit })
      // }
    },
    // 获取当前服务器的时间
    async getNewVersionDraftConfig(context) {
      try {
        let nacosConfig = await commonApi.getNewVersionDraftConfig()
        context.commit('updateNewVersionDraftConfig', nacosConfig)
        return nacosConfig
      } catch (err) {
        // eslint-disable-next-line no-console
        console.log(err)
      }
    },
    // 获取配置类数据
    async getPublicConfig(context) {
      try {
        const res = await commonApi.getNewConfig()
        context.commit('updatePublicConfig', res)
      } catch (err) {
        // eslint-disable-next-line no-console
        console.log(err)
      }
    },
    // 查询更新内容
    async getPublishContent(context) {
      try {
        let deviceId = localStorage.getItem('DEVICE_UUID')
        const res = await commonApi.queryPublishContent({ deviceCode: deviceId })
        context.commit('setPublishContent', res)
      } catch (error) {
        // console.log(error)
      }
    }
  }
}
