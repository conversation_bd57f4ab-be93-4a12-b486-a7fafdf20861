import user from '@/utils/user.js'
import userApi from '@/apis/user'
import sdmApi from '@/apis/sdm'
import gptApi from '@/apis/ai'
import settingApi from '@/apis/setting'
import { BAN_STATUS } from '@/constants/open-account'// 常量
import creditApi from '@/apis/credit'
import Storage from '@/common/js/storage' // 本地缓存对象
import { USER_INFO } from '@/constant-storage'
import websocket from '@/websocket/index.js'

let getUserPromise = null
// 是否已监听 mqtt 断开事件
let disconnectListening = false

export default {
  namespaced: true,
  state() {
    return {
      userInfo: null, // 用户信息
      isLogined: !!user.getToken(), // 是否已经登录
      isOpenAccount: false, // 是否已开户
      corpInfo: null, // 企业信息
      sdmInfo: null, // 米账号信息
      corpConfig: null, // 企业设置
      paymentAccountList: null, // 电子交易账户列表
      noBanAccountList: null, // 未禁用电子交易账户列表
      postConfig: null, // 发布配置
      passedBankCardList: null, // 已通过的背书账户列表
      remindSetting: null, // 消息提醒设置
      creditInfo: {}, // 我的信用信息
      vipCorpInfo: null, // vip信息
      warning: {}, // 企业证件信息过期警告
      mqttStatus: 'disconnected', // mqtt 连接状态, disconnected(未连接)/connecting(连接中)/connected(已连接)
      showCreditRemind: 0, // 是否显示信用分提示
      isSdkLink: true, // 是否通过SDK跳转京东收银台
      marketOpen: false, // 是否展示服务大厅
      couponOpen: 1, // 优惠券功能开关 1开启 0关闭  开关接口不调了 默认写死true
      contactList: [], // 联系人信息
      recoginzeSettings: {}, // 签手客户端配置
      couponCount: 0, // 消费券数量
      consumeLevel: {}, // 消费云豆
      completeLevel: {}, // 交易笔数
      sellerBankAccountList: null, // 已通过的背书回款账户列表
      signBankAccountList: null, // 已通过的背书签收账户列表
      isShowAi: false, // 1开，0关
      sdmConfigInfo: {
        showScanPayFlag: 1, // 展示扫码支付标识  0-不展示  1-展示
        showAlipayFlag: 1, // 展示支付宝充值标识  0-不展示  1-展示
        showWeChatPayFlag: 1 // 展示微信充值标识  0-不展示  1-展示
      } // 承接贝相关配置类
    }
  },
  getters: {
    userInfo: state => state.userInfo,
    corpInfo: state => state.corpInfo,
    sdmInfo: state => state.sdmInfo,
    corpConfig: state => state.corpConfig,
    paymentAccountList: state => state.paymentAccountList,
    noBanAccountList: state => state.noBanAccountList,
    isOpenAccount: state => state.isOpenAccount,
    postConfig: state => state.postConfig,
    passedBankCardList: state => state.passedBankCardList,
    remindSetting: state => state.remindSetting,
    creditInfo: state => state.creditInfo,
    vipCorpInfo: state => state.vipCorpInfo,
    limitLight: state => !!state?.corpInfo?.limitLight, // 是否限制极速交易
    corpStatus: state => state?.corpInfo?.corpStatus === 2 || state?.corpInfo?.corpStatus === 3, // 企业状态：0-未实名，1-实名中，2-已实名，3-实名失效
    contactList: state => state.contactList,
    couponCount: state => state.couponCount,
    couponOpen: state => state.couponOpen,
    consumeLevel: state => state.consumeLevel, // 消费云豆
    completeLevel: state => state.completeLevel, // 交易笔数
    sellerBankAccountList: state => state.sellerBankAccountList,
    signBankAccountList: state => state.signBankAccountList,
    isShowAi: state => state.isShowAi,
    sdmConfigInfo: state => state.sdmConfigInfo
  },
  mutations: {
    // 更新用户信息
    updateUser(state, payload) {
      state.isLogined = !!payload
      state.userInfo = payload
      Storage.set(USER_INFO, payload)
    },
    // 设置是否已开户状态
    setIsOpenAccount(state, payload) {
      state.isOpenAccount = !!payload
    },
    // 设置是否已开户状态
    setCorpInfo(state, payload) {
      // 默认设置企业亿联可用 发布票票据上传合同不做限制，全部渠道开放上传合同展示，仅智付E+/智联通票面金额大于500万校验必填
      payload.yilianPayOpen = 1

      state.corpInfo = payload
      let market = !!payload.marketOpen
      state.marketOpen = market
    },
    // 设置是否已开户状态
    setCouponUp(state, payload) {
      state.couponOpen = payload.couponOpen
    },
    // 设置消费券等级  1消费云豆 2交易笔数
    setCouponLevel(state, payload) {
      if (payload.rewardType === 1) {
        state.consumeLevel = payload
      } else {
        state.completeLevel = payload
      }
    },
    // 设置米账号信息
    setSdmInfo(state, payload) {
      state.sdmInfo = payload
    },
    // 设置vip信息
    setVipCorpInfo(state, payload) {
      state.vipCorpInfo = payload
    },
    // 赋值函数
    setState: (state, objects) => {
      Object.keys(objects).forEach(key => {
        state[key] = objects[key]
      })
    },
    // 设置企业信息证件信息过期警告
    setWarning(state, payload) {
      state.warning = payload
    },
    // 设置 mqtt 状态
    setMqttStatus(state, status) {
      state.mqttStatus = status
    },
    // 设置信用分提示
    setShowCreditRemind(state, showCreditRemind) {
      state.showCreditRemind = showCreditRemind
    },
    // 设置联系人信息
    settingContactList(state, data) {
      state.contactList = data
    },
    // 设置签手端配置
    updateRecoginzeSettings(state, payload) {
      state.recoginzeSettings = payload
    },
    // 消费券数量
    setCouponCount(state, payload) {
      state.couponCount = payload
    },
    // 设置AI权限状态
    setAiState(state, payload) {
      state.isShowAi = payload
    },
    setSdmConfigInfo(state, payload) {
      state.sdmConfigInfo = payload
    }
  },
  actions: {
    // 获取当前用户信息
    async getUserInfo(context, payload) {
      // forceUpdate 表示是否强制更新
      if (context.state.userInfo && !payload?.forceUpdate) {
        return context.state.userInfo
      }
      if (!getUserPromise || payload?.forceUpdate) {
        getUserPromise = userApi.getUserInfo()
      }
      // 获取用户信息
      try {
        const userInfo = await getUserPromise
        context.commit('updateUser', userInfo)
      } finally {
        getUserPromise = null
      }
      return context.state.userInfo
    },

    // 连接 mqtt
    async connectMqtt(context) {
      if (context.state.mqttStatus !== 'disconnected') {
        return
      } else if (!disconnectListening) {
        websocket.on('disconnect', () => {
          context.commit('setMqttStatus', 'disconnected')
        })
        disconnectListening = true
      }
      context.commit('setMqttStatus', 'connecting')
      try {
        await websocket.connect()
        context.commit('setMqttStatus', 'connected')
      } catch (e) {
        // eslint-disable-next-line no-console
        console.log(e)
        context.commit('setMqttStatus', 'disconnected')
        throw e
      }
    },

    // 连接 mqtt
    disconnectMqtt(context) {
      websocket.disconnect()
      context.commit('setMqttStatus', 'disconnected')
    },

    // 登出
    logout(context) {
      // 是否手动退出
      // 调用 postLogout 接口会导致登录该账户的其他电脑也退出，根据产品要求，这里注释掉
      // const { manual } = payload || {}
      // if (manual) {
      //   await userApi.postLogout()
      // }
      getUserPromise = null
      user.clearToken()
      user.clearQrCode()
      user.clearStaticRequestCache()
      context.commit('updateUser', null)
      context.commit('setIsOpenAccount', false)

      // 清空用户信息数据
      context.commit('setState', {
        corpInfo: null, // 企业信息
        sdmInfo: null, // 米账号信息
        corpConfig: null, // 企业设置
        paymentAccountList: null, // 电子交易账户列表
        noBanAccountList: null, // 未禁用电子交易账户列表
        remindSetting: null, // 消息提醒设置
        vipCorpInfo: null, // vip信息
      })
    },

    // 获取企业信息
    async getCorpInfo(context) {
      const res = await userApi.getCorpInfo()
      context.commit('setCorpInfo', res)
      return res
    },

    // 获取消费券开关
    async getSwitchCoupon(context) {
      const res = await userApi.couponAllow()
      context.commit('setCouponUp', res)
      return res
    },

    // 获取活动等级
    async getCouponLevel(context, val) {
      const res = await userApi.couponProgress({ rewardType: val })
      res.rewardType = val // 1消费云豆 2交易笔数 7票方极速订单限时返豆活动
      context.commit('setCouponLevel', res)
      return res
    },

    // 获取vip信息
    async getVipCorpInfo(context) {
      const res = await userApi.getVipCorp()
      context.commit('setVipCorpInfo', res)
      return res
    },

    // 获取是否已开户
    async getIsOpenAccount(context) {
      const res = await userApi.getCorpInfo()
      // "corpStatus":1,  // 企业状态：0-未实名，1-实名中，2-已实名，3-实名失效
      // paymentChannelOpenSuccess 电子交易账户是否开通成功，0-否，1-是
      // yilianCorpStatus 亿连实名状态 0-未实名，1-实名中，2-已实名，3-实名失效
      let flag = (res.corpStatus === 2 || res.yilianCorpStatus === 2) && res.paymentChannelOpenSuccess === 1
      context.commit('setIsOpenAccount', flag)
      context.commit('setCorpInfo', res)
      return flag
    },

    // 获取米账号信息
    async getSdmInfo(context) {
      const sdmInfo = await sdmApi.getSdmInfo()
      context.commit('setSdmInfo', sdmInfo)
      return sdmInfo
    },

    // 查询企业设置
    async getCorpConfig({ commit }) {
      const corpConfig = await settingApi.getCorpConfig()

      commit('setState', {
        corpConfig,
      })
      return corpConfig
    },

    // 查询电子交易账户列表
    async getPaymentAccountList({ commit }) {
      const paymentAccountList = await settingApi.getPaymentAccountList()

      commit('setState', {
        paymentAccountList, // 交易列表
      })
      return paymentAccountList
    },

    // 查询未禁用电子交易账户列表
    async getNoBanAccountList({ commit }) {
      const paymentAccountList = await settingApi.getPaymentAccountList()
      const noBanAccountList = paymentAccountList ? paymentAccountList.filter(item => item.banStatus !== BAN_STATUS.DISABLE.id) : []// 未禁用的交易列表
      commit('setState', {
        noBanAccountList,
        paymentAccountList, // 交易列表
      })
      return noBanAccountList
    },

    // 查询发布设置
    async getPostConfig({ commit }) {
      const data = await settingApi.getPostConfig()
      const postConfig = { ...data, ...data.traderCorpConfig }
      commit('setState', {
        postConfig,
      })
      return postConfig
    },

    // 查询回款账户列表
    async getSellerBankAccountList({ commit }) {
      const sellerBankAccountList = await userApi.translateBankList()
      commit('setState', {
        sellerBankAccountList, // 交易列表
      })
      return sellerBankAccountList
    },

    // 查询已通过的背书账户列表
    async getPassedBankCardList({ commit }) {
      const res = await settingApi.getPassedBankCardList() || []
      // 已通过的背书账户列表
      let passedBankCardList = Array.isArray(res) ? res : []
      passedBankCardList = passedBankCardList.map(v => {
        // eslint-disable-next-line no-magic-numbers
        v.label = `${v.bankBranchName}(**${v.bankAccount.substring(v.bankAccount.length - 4)})`
        v.value = v.id
        return v
      })
      // 已通过的背书回款账户列表
      let sellerBankAccountList = (passedBankCardList.length && passedBankCardList.filter(e => e.backAccountStatus === 1 && e.backAccountFlag === 0)) || []
      // 已通过的背书签收账户列表
      let signBankAccountList = (passedBankCardList.length && passedBankCardList.filter(e => e.signAccountStatus === 1 && e.signAccountFlag === 0)) || []

      commit('setState', {
        passedBankCardList,
        sellerBankAccountList,
        signBankAccountList
      })

      return passedBankCardList
    },

    // 获取用户提醒配置
    async getRemindSetting({ commit }) {
      const remindSetting = await userApi.getRemindSetting() || {}

      commit('setState', {
        remindSetting,
      })

      return remindSetting
    },

    // 获取我信用信息
    async getMyCredit({ state, commit }, payload) {
      if (state.creditInfo && Object.keys(state.creditInfo).length && !payload?.forceUpdate) {
        return state.creditInfo
      }
      const data = await creditApi.getMyCredit()
      commit('setState', {
        creditInfo: data || {},
      })
      return data || {}
    },
    // 获取是否显示信用分提醒
    async getShowCreditRemind({ commit, dispatch, state }) {
      await dispatch('getMyCredit')
      const { creditInfo } = state
      try {
        if (creditInfo && creditInfo.currentCreditPoints >= 500 && creditInfo.currentCreditPoints <= 550) {
          const data = await creditApi.getCreditRemind()
          commit('setShowCreditRemind', data || 0)
        } else {
          commit('setShowCreditRemind', 0)
        }
      } catch (err) {
        commit('setShowCreditRemind', 0)
      }
    },
    // 获取联系人
    async getContactList({ commit }) {
      const data = await settingApi.getMobileList()
      commit('settingContactList', data)
      return data
    },
    // 获取签手客户端配置
    async getRecoginzeSettings({ commit }) {
      const settings = await userApi.getDiscernConfig()
      commit('updateRecoginzeSettings', settings)
      return settings
    },
    // 获取是否可以查看AI助手
    async getAiState({ commit }) {
      // 0:关  1:开
      const data = await gptApi.getAiState()
      commit('setAiState', !!data)
    },
    // 获取承接贝配置类接口
    async getSdmConfigInfo(context) {
      const sdmConfigInfo = await sdmApi.getMarginConfiguration()
      context.commit('setSdmConfigInfo', sdmConfigInfo)
      return sdmConfigInfo
    },
  }
}
