import API from '@/apis/home'
import { Message } from '@shendu/element-ui'
import { USER_INFO } from '@/constant-storage'
import Storage from '@/common/js/storage'
// import commonApi from '@/apis/common'
import concernsApi from '@/apis/concerns'
export default {
  namespaced: true,
  state() {
    return {
      spAxiosSucc: false,
      spFlag: '',
      spToken: '',
      // 精简版本下-是否定向发布
      // isDirectionPublish: 0,
      // isOpenUp: 0, // 闭市开关控制  0关闭（维持现状） 1开启（新的要求）
      // 关注企业配置信息
      concernsConfig: {},
    }
  },
  getters: {
    // isOpenUp: state => state.isOpenUp,
  },
  mutations: {
    SET_SP_FLAG(state, data) {
      state.spAxiosSucc = data
    },
    SET_SP_CODE(state, data) {
      state.spFlag = data.flag
      state.spToken = data.token
    },
    // 是否发布开关设置
    // setDirectionPublish(state, data) {
    //   state.isDirectionPublish = data
    // },
    // 交易大厅开关开关设置
    // setOpenUp(state, data) {
    //   state.isOpenUp = data
    // },
    // 设置关注企业的配置数据
    setConcernsConfig(state, data) {
      state.concernsConfig = data
    },
  },
  actions: {
    // 商票板企业信息同步并获取token
    getCorpInfoSync({ commit, state, rootState }, path) {
      const { spbUrl } = rootState
      const userInfo = Storage.get(USER_INFO) || {}
      return new Promise(resolve => {
        if (state.spAxiosSucc) {
          let url = `${spbUrl}${path}?flag=${state.spFlag}&token=${state.spToken}`
          resolve(url)
          return
        }
        // 5分钟内直接取state的值  5分钟后请求在调用接口
        let flag = false
        setTimeout(() => {
          if (!flag) {
            flag = true
            let url = `${spbUrl}${path}`
            resolve(url)
          }
        }, 5000)
        const corpInfo = {
          corpId: userInfo.corpId,
          mobile: userInfo.mobile
        }
        API.getCorpInfoSync(corpInfo)
          .then(data => {
            try {
              if (!flag) {
                flag = true
                if (data && data.flag === 0 && data.token) {
                  commit('SET_SP_CODE', data)
                  commit('SET_SP_FLAG', true)
                  let href = `${spbUrl}${path}?flag=${data.flag}&token=${data.token}`
                  setTimeout(() => {
                    commit('SET_SP_FLAG', false)
                  }, 300000)
                  resolve(href)
                } else {
                  resolve(`${spbUrl}${path}`)
                }
              }
            } catch (err) {
              if (!flag) {
                flag = true
                resolve(`${spbUrl}${path}`)
              }
            }
          })
          .catch(() => {
            if (!flag) {
              flag = true
              resolve(`${spbUrl}${path}`)
            }
          })
      })
    },

    // 获取带token的商票信评url链接-平台授权跳转商票信评
    openSpxp(context, info) {
      return new Promise(resolve => {
        if (!localStorage.getItem('TOKEN')) {
          return Message.warning('查看商票信评请先登录！')
        }
        const { rootState } = context
        let params = {
          channel: rootState.baseUrl,
          terminal: 'pc',
        }
        if (info) params = { ...params, ...info }
        API.getSpxpTokenUrl(params).then(data => {
          window.open(data, '_blank')
          resolve(data)
        })
          // eslint-disable-next-line no-empty-function
          .catch(() => {
          })
      })
    },

    // 请求发布/大厅开关设置数据
    // getSwitchAuth({ commit }) {
    //   return new Promise(resolve => {
    //     commonApi.querySwitchAuth().then(result => {
    //       commit('setDirectionPublish', result?.publishSwitch)
    //       commit('setOpenUp', result?.tradingHallSwitch)
    //       resolve(result)
    //     })
    //   })
    // },

    // 获取关注企业的配置信息
    getConcernsCompanyConfig({ commit }) {
      return new Promise(resolve => {
        concernsApi.queryConcernsCompanyInfo().then(res => {
          commit('setConcernsConfig', res)
          resolve(res)
        })
      })
    },
    // 获取优惠券付费开关
    // getCouponPaySwitch({ commit }) {
    //   return new Promise(resolve => {
    //     orderApi.getCouponSwitch().then(result => {
    //       commit('setCouponPay', result.couponOpen)
    //       resolve(result)
    //     })
    //   })
    // }
  },
}
