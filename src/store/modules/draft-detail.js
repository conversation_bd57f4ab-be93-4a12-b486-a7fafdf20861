/* eslint-disable no-shadow */
// 订单详情
import orderApi from '@/apis/order'
import {
  DISPUTE_SOURCE, // 争议订单来源
  DRAFT_STATUS, //  订单状态tab
  DRAFT_STATUS_VALUE_MAP,
  ORDER_OPERATE_LOG_VALUE_MAP, // 订单详情-交易记录
  ORDER_STATUS, // 订单状态
  TRANSACTION_STATUS, // 订单详情-交易状态
  ROLE, // 角色方
  ORDER_BROKE_STATUS, // 订单违约状态
  PAYMENT_CHANNEL
} from '@/constant'
import { yuan2wan } from '@/common/js/number' // 金额单位转换
import { formatTime, formatDuration } from '@/common/js/date' // 时间处理
import { isNull } from '@/common/js/util' // 判断是否空值

const state = {
  traderCorpOrderInfo: {}, // 订单详情
  tabStatusLabel: '', // 订单tab状态文本
  orderTabTitle: '', // 订单详情tab标题
  isSale: null, // 是否票方
  isBuy: null, // 是否资方
  isOvertime: false, // 订单操作倒计时是否结束
  isAddTime: false, // 加时开始时间
  hasSubStatus: [ // 有多个子状态的订单状态
    DRAFT_STATUS.CANCELING.id, // 取消中
    DRAFT_STATUS.OFF_SHELF.id, // 已下架
    DRAFT_STATUS.DEAL_FAIl.id, // 已失败
  ],
  orderOperateLog: [], // 订单操作记录
  evidenceList: [], // 凭证列表
  disputeInfo: {}, // 争议订单数据
  isUpdateOrderInfo: false, // 是否刷新订单详情
  updateTicketInfo: null, // 连号票-变更票号信息
  updateTicketStatus: 0 // 变更票号的状态0:发起，1:修改，2:查询
}

const getters = {
  traderCorpOrderInfo: state => state.traderCorpOrderInfo, // 订单详情
  orderNo: state => state.traderCorpOrderInfo?.orderNo, // 订单NO
  tabStatus: state => state.traderCorpOrderInfo?.tabStatus, // 订单tab状态id
  orderStatus: state => state.traderCorpOrderInfo?.orderStatus, // 订单状态id
  transactionStatus: state => state.traderCorpOrderInfo?.transactionStatus, // 交易状态id
  draftType: state => state.traderCorpOrderInfo?.draftType, // 票类型

  tabStatusLabel: state => state.tabStatusLabel, // 订单tab状态文本
  orderTabTitle: state => state.orderTabTitle, // 订单详情tab标题
  isSale: state => state.traderCorpOrderInfo?.role === ROLE.SALE.id, // 是否票方 role 1资方 2票方
  isBuy: state => state.traderCorpOrderInfo?.role === ROLE.BUY.id, // 是否资方 role 1资方 2票方
  isOvertime: state => state.isOvertime, // 订单操作倒计时是否结束
  isAddTime: state => state.isAddTime, // 加时开始时间
  hasSubStatus: state => state.hasSubStatus, // 有多个子状态的订单状态
  orderOperateLog: state => state.orderOperateLog, // 订单操作记录
  evidenceList: state => state.evidenceList, // 凭证列表
  disputeInfo: state => state.disputeInfo, // 争议订单数据

  // 票据类型
  isAgentOrder: state => !!state.traderCorpOrderInfo?.agentOrder, // 是否定向票 0、1 转成 false-否、true-是
  isSerialDraft: state => !!state.traderCorpOrderInfo?.serialDraft, // 是否连号票 0、1 转成 false-否、true-是
  isFastTrade: state => !!state.traderCorpOrderInfo?.fastTrade, // 是否极速票 0、1 转成 false-否、true-是
  isRadarType: state => !!state.traderCorpOrderInfo?.radarType, // 是否自动类型 0、1 转成 false-否、true-是
  isNewDraft: state => !!state.traderCorpOrderInfo?.draftType, // 是否新票 0、1 转成 false-否、true-是

  // 订单类型
  isBroke: state => (state.traderCorpOrderInfo?.orderBrokeStatus === ORDER_BROKE_STATUS.MY.id) || (state.traderCorpOrderInfo?.orderBrokeStatus === ORDER_BROKE_STATUS.OTHER.id), // 是否违约

  /* 订单状态 */
  waitingReview: state => state.traderCorpOrderInfo?.tabStatus === DRAFT_STATUS.WAITING_REVIEW.id, // 待审核
  waitingReceiving: state => state.traderCorpOrderInfo?.tabStatus === DRAFT_STATUS.WAITING_RECEIVING.id, // 待接单
  waitingConfirm: state => state.traderCorpOrderInfo?.tabStatus === DRAFT_STATUS.WAITING_CONFIRM.id, // 待确认
  waitingPay: state => state.traderCorpOrderInfo?.tabStatus === DRAFT_STATUS.WAITING_PAY.id, // 待支付
  waitingEndorse: state => state.traderCorpOrderInfo?.tabStatus === DRAFT_STATUS.WAITING_ENDORSE.id, // 待背书
  waitingSubmission: state => state.traderCorpOrderInfo?.tabStatus === DRAFT_STATUS.WAITING_SIGN.id, // 待签收
  checking: state => state.traderCorpOrderInfo?.tabStatus === DRAFT_STATUS.CHECKING.id, // 校验中
  canceling: state => state.traderCorpOrderInfo?.tabStatus === DRAFT_STATUS.CANCELING.id, // 取消中
  offShelf: state => state.traderCorpOrderInfo?.tabStatus === DRAFT_STATUS.OFF_SHELF.id, // 已下架
  offShelfReasonIsKf: state => state.traderCorpOrderInfo?.orderStatus === ORDER_STATUS.REVIEW_OFF_SHELF.id, // 已下架-下架原因-客服(审核下架)
  failed: state => state.traderCorpOrderInfo?.tabStatus === DRAFT_STATUS.DEAL_FAIl.id, // 已失败
  finished: state => state.traderCorpOrderInfo?.tabStatus === DRAFT_STATUS.DEAL_COMPLETED.id, // 已完成
  waitingOnShelf: state => state.traderCorpOrderInfo?.orderStatus === ORDER_STATUS.NOT_LISTED.id, // 待接单-未上架
  cancelingEndorse: state => [TRANSACTION_STATUS.ENDORSEMENT_SALE_CANCELING.id, TRANSACTION_STATUS.ENDORSEMENT_BUY_CANCELING.id].includes(state.traderCorpOrderInfo?.transactionStatus), // 取消中-待背书阶段，票方超时未背书，资方申请取消
  cancelingSubmission: state => [TRANSACTION_STATUS.SUBMISSION_BUY_CANCELING.id, TRANSACTION_STATUS.SUBMISSION_SALE_CANCELING.id].includes(state.traderCorpOrderInfo?.transactionStatus), // 取消中-待签收阶段，
  cancelingPay: state => [TRANSACTION_STATUS.PAYING_BUY_CANCELING.id, TRANSACTION_STATUS.PAYING_SALE_CANCELING.id].includes(state.traderCorpOrderInfo?.transactionStatus), // 取消中-待支付
  onSpliting: state => state.traderCorpOrderInfo?.orderStatus === ORDER_STATUS.ON_SPLITING.id, // 拆分中
  // 票方发起取消中
  saleInitiateCanceling: state => [
    TRANSACTION_STATUS.ENDORSEMENT_SALE_CANCELING.id, // 背书阶段票方取消中
    TRANSACTION_STATUS.PAYING_SALE_CANCELING.id, // 支付阶段票方取消中
    TRANSACTION_STATUS.SUBMISSION_SALE_CANCELING.id, // 签收阶段票方取消中
  ].includes(state.traderCorpOrderInfo?.transactionStatus),
  // 资方发起取消中
  buyInitiateCanceling: state => [
    TRANSACTION_STATUS.ENDORSEMENT_BUY_CANCELING.id, // 背书阶段资方取消中
    TRANSACTION_STATUS.PAYING_BUY_CANCELING.id, // 支付阶段资方取消中
    TRANSACTION_STATUS.SUBMISSION_BUY_CANCELING.id, // 签收阶段资方取消中
    TRANSACTION_STATUS.SELLER_CANCELING_PENDING_CANCELING.id // 票方同意取消订单取消中
  ].includes(state.traderCorpOrderInfo?.transactionStatus),

  /* 订单状态 */

  isUpdateOrderInfo: state => state.isUpdateOrderInfo, // 是否刷新订单详情
  updateTicketInfo: state => state.updateTicketInfo,
  updateTicketStatus: state => state.updateTicketStatus,

  // 是否E++渠道
  isEPlusChannel: state => state.traderCorpOrderInfo?.paymentChannel === PAYMENT_CHANNEL.YL_PLUS.id,
}

const mutations = {
  // 赋值函数
  setState: (state, objects) => {
    Object.keys(objects).forEach(key => {
      state[key] = objects[key]
    })
  },

  // 重置数据
  resetData(state) {
    state.traderCorpOrderInfo = {} // 订单详情
    state.tabStatusLabel = ''// 订单tab状态文本
    state.orderTabTitle = '' // 订单详情tab标题
    state.isOvertime = false // 倒计时是否结束
    state.isAddTime = false // 加时开始时间
    state.orderOperateLog = [] // 订单操作记录
    state.evidenceList = [] // 凭证列表
    state.disputeInfo = {} // 争议订单数据
    state.isUpdateOrderInfo = false // 是否刷新订单详情
    // console.log('resetData重置数据 :>> ', state)
  },
  setUpdateTicketInfo(state, data) {
    state.updateTicketInfo = data
  },
  setUpdateTicketStatus(state, data) {
    state.updateTicketStatus = data
  }
}

const actions = {

  // 获取订单详情
  getTraderCorpOrderInfo({ commit, state }, { orderNo = '', isHistory = false }) {
    let params = orderNo || state.orderNo
    return (isHistory ? orderApi.getHistoricalTraderCorpOrderInfo : orderApi.getTraderCorpOrderInfo)(params).then((res = {}) => {
      // TODO 测试数据-start
      // res.role = 1 // 订单处理角色，1-资方,2-票方,3-系统
      // res.tabStatus = 1 // tab状态，0-待审核，1-待接单、2-待确认、3-待支付、4-待背书、5-待签收，6-校验中、7-取消中、8-已下架、9-已完成，10-已失败
      // res.transactionStatus = 30 // 交易状态，,11-未交易、12-待确认、13-待支付、14-支付中、15-待背书、16-待签收、17-校验中、18-交易完成、21-确认阶段票方已取消、22-确认阶段接票方取消订单，23-确认阶段平台已取消、,24-支付阶段票方已取消、25-支付阶段资方已取消、26-支付阶段平台已取消、27-背书阶段票方取消中、28-背书阶段票方已取消、29-背书阶段平台已取消、30-签收阶段资方取消中、,31-签收阶段资方已取消、32签收阶段票方已取消，33-签收阶段平台已取消
      // res.nowTime = new Date().getTime()
      // res.verifyEndTime = new Date().getTime() + 400000 // 最迟确认时间
      // res.payEndTime = new Date().getTime() + 400000 // 最迟支付时间
      // res.endorseEndTime = new Date().getTime() - 400000 // 最迟背书时间
      // res.submissionEndTime = new Date().getTime() - 400000 // 最迟签收时间
      // res.cancelEndTime = new Date().getTime() - 400000 // 最迟取消时间
      // res.fastTrade = 1 // 是否极速票，0-否、1-是
      // res.serialDraft = 1 // 是否连号票，0-否、1-是
      // res.agentOrder = 0 // 是否定向票，0-否、1-是
      // res.disputeInfo = {
      //   disputeSource: 2, // 争议订单来源，1-票方，2-资方，3-客服
      //   invokeThirdPartyTimes: 0, // 争议次数
      //   dispute: 1, // 是否争议订单，0不是，1是
      //   replenishEvidenceTimes: 1, // 补充凭证次数
      //   remainSignTimeOutValue: 1, // 待签收，剩余超时时间，单位秒
      //   remainEndorseTimeOutValue: 1, // 待背书，剩余超时时间，单位秒
      //   dutyPartyType: 1
      // } // 争议数据
      // res.billOrderCancelMsg = {
      //   cancelParty: 2
      // }
      // res.cancelType = 15
      // res.waitTakeTime = new Date().getTime() + 400000
      // res.orderStatus = 2
      // TODO 测试数据-end
      let orderInfo = res || {}

      let {
        draftAmount = 0, // 票面金额
        disputeInfo = {}, // 争议订单
      } = orderInfo

      // 订单tab标题
      const orderTabTitle = ` ${isNull(draftAmount) ? '--' : yuan2wan(draftAmount || 0)}万-${DRAFT_STATUS_VALUE_MAP[orderInfo?.tabStatus] || ''}`
      // 订单状态文案
      let tabStatusLabel = DRAFT_STATUS_VALUE_MAP[orderInfo?.tabStatus] || ''
      if (orderInfo.orderStatus === ORDER_STATUS.ON_SPLITING.id) {
        tabStatusLabel = ORDER_STATUS.ON_SPLITING.name
      }

      // 争议订单-衍生判断数据
      isNull(disputeInfo) && (disputeInfo = {})
      if (disputeInfo) {
        disputeInfo.isDisputeOrder = !!disputeInfo?.dispute // 是否争议订单，0、1 转成 false-否、true-是
        disputeInfo.isSaleDisputeOrder = disputeInfo.isDisputeOrder && disputeInfo?.disputeSource === DISPUTE_SOURCE.SALE.id // 是否票方发起的客服介入（争议订单来源）
        disputeInfo.isBuyDisputeOrder = disputeInfo.isDisputeOrder && disputeInfo?.disputeSource === DISPUTE_SOURCE.BUY.id // 是否资方发起的客服介入（争议订单来源）
        disputeInfo.isServiceDisputeOrder = disputeInfo.isDisputeOrder && disputeInfo?.disputeSource === DISPUTE_SOURCE.SERVICE.id // 是否客服强推介入的（争议订单来源）
      }

      // 票面金额 单位万
      orderInfo.draftAmountWan = yuan2wan(draftAmount)

      commit('setState', {
        traderCorpOrderInfo: orderInfo,
        orderTabTitle,
        tabStatusLabel,
        orderNo,
        disputeInfo,
      })
      // 使用接口返回的系统时间更新时间差
      commit('common/updateSysTime', orderInfo.nowTime, { root: true })

      return orderInfo
    })
  },

  // 获取订单操作记录
  getBillorderOperateLog({ commit }, { orderNo = '', isHistory = false }) {
    const api = isHistory ? 'getHistoryOrderOperateLog' : 'getBillorderOperateLog'
    return orderApi[api](orderNo).then((res = []) => {
      let data = res || {}
      // 转换数据
      let orderOperateLog = data.map((v, index) => {
        if (v.createTime) {
          v.timeStr = formatTime(v.createTime, 'YYYY-MM-DD hh:mm:ss')
          v.value = ORDER_OPERATE_LOG_VALUE_MAP[v.operateType]
          let preIndex = index - 1
          let diffTimeMs = preIndex > -1 ? (v.createTime - data[preIndex].createTime) : 0 // 毫秒，传入formatDuration
          v.useTime = v.operateType !== 0 ? `用时${formatDuration(diffTimeMs)}` : ''
        }
        return v
      })
      commit('setState', {
        orderOperateLog
      })

      return orderOperateLog
    })
  },

  // 获取凭证列表
  getEvidenceList({ commit }, orderNo = '') {
    return orderApi.getEvidenceList(orderNo).then((res = []) => {
      let evidenceList = res || []
      // console.log('evidenceList :>> ', res)

      evidenceList = evidenceList.map((v, index) => {
        v.createdAtStr = formatTime(v.createdAt, 'MM-DD hh:mm:ss')
        v.createdAtStrDate = v.createdAtStr.split(' ')[0]
        v.createdAtStrTime = v.createdAtStr.split(' ')[1]

        // 补充说明 第一行为客服介入 第二行为补充凭证，目前只能通过index来判断
        v.evidenceMsg = index === 0 ? v.interveneReason : v.evidenceNote

        return v
      })

      commit('setState', {
        evidenceList
      })

      return evidenceList
    })
  },

  // 查询变更票号信息
  queryUpdateTicketInfo({ commit }, params) {
    return new Promise(resolve => {
      orderApi.queryChangeTicketNumber(params).then(res => {
        // 接口约定：res返回null，表示没有申请过票号变更，可以发起申请票号
        let state = 0
        // res有值：根据返回的status,判断是同意还是拒绝
        if (res) {
          state = res.status === 0 ? 1 : 2
          if (res.status === 0) {
            state = 1 // 修改变更票号
          }
          if (res.status === 1 || res.status === 2) {
            state = 0 // 发起变更票号
          }
          // state = res.status === 0 ? 1 : 2
        }
        commit('setUpdateTicketStatus', state)
        commit('setUpdateTicketInfo', res)
        resolve(res)
      })
    })
  }

}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
