<!-- 实名认证弹窗 -->
<style lang="scss" scoped>
.box-warp {
  display: flex;
  width: 100%;
  background-color: $color-FFFFFF;

  .text-right {
    ::v-deep.el-input__inner {
      text-align: right;
    }
  }

  .left {
    margin: 16px;
    border-right: 1px solid $color-D9D9D9;
    width: 204px;
  }

  .right {
    overflow: auto;
    padding: 16px;
    width: 100%;
    max-height: 516px;
  }
}

.open-account {
  .empty-box {
    height: 520px;
    background-color: $color-F2F2F2;
  }
}

::v-deep {
  .open-account {
    margin-top: 10vh !important;

    .el-dialog__body {
      background-color: $color-F2F2F2;
    }

    // step流程
    .el-step {
      width: 20%;
    }

    .el-steps--horizontal {
      margin-bottom: 12px;
    }

    .el-step__title {
      line-height: 24px;
      margin-top: 4px;
      margin-bottom: 2px;

      &.is-process,
      &.is-finish {
        font-weight: normal;
        color: $color-text-primary;
      }

      &.is-wait {
        color: $color-text-secondary;
      }

      &.is-error {
        color: $--color-primary;
      }
    }

    .el-step__head .el-step__icon {
      border: none;
      background: transparent;
    }

    .el-step__icon.is-text {
      border-width: 1px;
    }

    .el-step__head.is-finish {
      .el-step__line {
        background-color: $--color-primary;
      }
    }

    .el-step__head.is-error {
      .el-step__icon.is-text {
        &::after {
          top: -2px;
        }

        i::before {
          content: "";
        }
      }
    }

    .el-step__head {
      .el-step__line {
        right: -42%;
        left: 58%;
        height: 3px;
        background-color: $color-D9D9D9;
      }

      &.is-wait {
        .el-step__icon.is-text {
          position: relative;
          z-index: 1;
          border: none;
          background-color: $color-D9D9D9;

          &::after {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 2;
            border-radius: 50%;
            width: 100%;
            height: 100%;
            background-color: $color-D9D9D9;
            content: "";
          }
        }
      }
    }

    // step流程 end

    // 段落
    .paragraph {
      // margin-bottom: 10px;
      padding-left: 1em;
      font-size: 12px;
      text-indent: -1em;
      color: $color-text-primary;
      line-height: 24px;

      .num {
        position: relative;
        display: inline-block;
        width: 1em;
        text-indent: 0;

        &::after {
          position: absolute;
          right: 0;
          content: ".";
        }
      }
    }

    // 标题
    .title-left-border {
      position: relative;
      margin: 0 0 8px !important;
      padding-left: 12px;
      font-size: 16px;
      font-weight: 600;
      line-height: 22px;

      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: $line-bar-width;
        height: 16px;
        background: $--color-primary;
        transform: translateY(-50%);
        content: "";
      }
    }

    // form表单相关
    .g-required {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .el-checkbox-group {
      .el-checkbox {
        margin-right: 16px;

        &:last-child {
          margin-right: 0;
        }
      }
    }

    .el-form-item {
      display: flex;
      margin-bottom: 12px;
      flex-flow: column;
    }

    .el-select,
    .el-cascader {
      width: 100%;
    }

    .el-form-item__label {
      margin-bottom: 4px;
      width: auto !important;
      height: 22px;
      text-align: left;
      color: $color-text-secondary;
      line-height: 22px;

      &::before {
        font-weight: 600;
      }
    }

    .el-form-item__content {
      margin-left: 0 !important;
      width: 100%;
    }

    .el-form-item__error--inline {
      display: inherit;
      margin-left: 0;
      line-height: 12px;
    }

    .el-input__inner,
    .el-textarea__inner {
      padding-left: 12px;
      font-size: 16px;
    }

    .el-textarea__inner {
      height: 100px;
    }

    .el-input--prefix .el-input__inner {
      padding-left: 30px;
    }

    .el-checkbox__input,
    .el-checkbox__label {
      font-weight: normal;
      vertical-align: middle;
    }

    .el-date-editor.el-input,
    .el-date-editor.el-input__inner {
      width: 100%;
    }

    .el-checkbox .el-checkbox__input.is-checked + .el-checkbox__label {
      color: $color-text-primary;
    }

    .multiseriate {
      display: flex;

      >div {
        flex: 1;

        &:last-child {
          margin-left: 8px;
        }
      }

      width: 100%;
    }

    // 协议
    .agreement {
      line-height: 22px;
      display: flex;
      flex-wrap: nowrap;

      .link {
        @include example-underline;
      }
    }

    // 底部按钮
    .footer {
      display: flex;
      align-items: center;
      margin-top: 12px;

      .btn-box {
        flex: 1;
        text-align: right;
      }
    }
  }
}

.company-warp {
  display: flex;

  .left {
    width: 204px;
  }

  .right {
    width: 100%;
  }
}
</style>

<template>
  <el-dialog
    title="实名认证"
    width="1120px"
    append-to-body
    custom-class="open-account"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <!-- 空白loading -->
    <div
      v-if="isShowIntroduce === 1"
      v-loading="loading"
      class="empty-box"
      element-loading-background="rgba(0, 0, 0, 0)"
    />
    <!-- 开户介绍 -->
    <Introduce v-if="isShowIntroduce && isShowIntroduce !== 1" :open="open" />
    <!-- 流程步骤 -->
    <Step
      v-if="!isShowIntroduce"
      :active-step="activeStep"
      :step-item="stepItem"
      :stage-status="stageStatus"
    />
    <section v-if="!isShowIntroduce">
      <!-- 开户所需文件 -->
      <UploadImg v-if="activeStep === 0" :next="next" />

      <!-- 完善企业信息 -->
      <EnterpriseInformation
        v-if="activeStep === 1 && legalStep === 0"
        :next="next"
        :legal-step="legalStep"
      />
      <!-- 法人信息 -->
      <LegalPerson
        v-if="activeStep === 1 && legalStep === LEGAL_STEP.legalRepresentative.step"
        :next="next"
        :legal-step="legalStep"
      />
      <!-- 被授权人（经办人）基本信息 -->
      <AgentIdentity
        v-if="activeStep === 1 && legalStep === LEGAL_STEP.agentEqualLegal.step"
        :next="next"
        :legal-step="legalStep"
      />
      <!-- 受益人信息 -->
      <Beneficiary
        v-if="activeStep === 1 && legalStep === LEGAL_STEP.beneficiaryEqualLegal.step"
        :next="next"
        :legal-step="legalStep"
      />
      <!-- 大股东信息 -->
      <Stockholder
        v-if="activeStep === 1 && legalStep === LEGAL_STEP.stockholderEqualLegal.step"
        :next="next"
        :legal-step="legalStep"
      />
      <!-- 实际控制人 -->
      <ActualController
        v-if="activeStep === 1 && legalStep === LEGAL_STEP.actualController.step"
        :next="next"
        :legal-step="legalStep"
      />
      <!-- 绑定银行账户 -->
      <BindBankAccount
        v-if="activeStep === 2"
        :next="next"
        :pay-fail-stage="payFailStage"
      />
      <!-- 认证资料审核  activeStep === 3 -->
      <DataReview
        v-if="activeStep === 3"
        :stage-status="stageStatus"
        :fail-reason="failReason"
        :pay-fail-stage="payFailStage"
        :close="close"
        :next="next"
      />

      <!-- 验证银行账户 -->
      <!-- <VerifyBank v-if="activeStep === 4" :next="next" /> -->

      <!-- 签署协议 -->
      <SignProtocol
        v-if="activeStep === 4"
        :stage-status="stageStatus"
        :fail-reason="failReason"
        :close="close"
        :next="next"
      />
      <!-- 开通电子账户 -->
      <Success v-if="activeStep === 5" :close="close" />
    </section>
    <!-- 小额打款失败 联系客服 -->
    <ContactService ref="contactService" :title="title" sub-title="请联系客服进行处理" />
  </el-dialog>
</template>

<script>
/* eslint-disable no-magic-numbers */
import { REAL_NAME_CERTIFICATION_CLOSE, SITE_OPEN_ACCOUNT_CLOSE } from '@/event/modules/site' // 监听事件常量
import UploadImg from './components/upload-img.vue' // 开户所需文件
import EnterpriseInformation from './components/enterprise-information.vue' // 完善企业信息
import AgentIdentity from './components/agent-identity.vue' // 被授权人（经办人）基本信息
import Beneficiary from './components/beneficiary.vue' // 受益人信息
import Stockholder from './components/stockholder.vue' // 大股东信息
import ActualController from './components/actual-controller.vue' // 实际控制人信息
import BindBankAccount from './components/bind-bank-account.vue' // 绑定银行账户
// import VerifyBank from './components/verify-bank-account.vue' // 验证银行账户
import SignProtocol from './components/sign-protocol.vue'
import Success from './components/success-lite.vue' // 开通电子账户
import openAccountApi from '@/apis/open-account' // 开户接口
import user from '@/utils/user' // 用户对象
import ContactService from '@/views/components/common/contact-service/contact-service.vue' // 联系客服
import { LEGAL_STEP, objEveryValue, objSomeValue } from './options'// 不同身份同法人步骤
import Step from './components/step.vue' // 流程步骤组件
import LegalPerson from './components/legal-person.vue' // 法人信息
import DataReview from './components/data-review.vue' // 资料审核
import Introduce from './components/introduce.vue'

export default {
  name: 'real-name-certification',

  components: {
    Introduce,
    UploadImg, // 开户所需文件
    EnterpriseInformation, // 完善企业信息
    AgentIdentity, // 被授权人（经办人）基本信息
    Beneficiary, // 受益人信息
    Stockholder, // 大股东信息
    ActualController, // 实际控制人信息
    BindBankAccount, // 绑定银行账户
    SignProtocol,
    // VerifyBank, // 验证银行账户
    Success, // 开通电子账户
    ContactService, // 联系客服
    LegalPerson, // 法人信息
    Step, // 流程组件
    DataReview // 资料审核
  },
  // 向所有子组件注入依赖变量
  provide() {
    return {
      openGlobalInfo: this.openGlobalInfo,
      // getQxbCorpInfo: this.getQxbCorpInfo,
      // qxbRemoteValidate: this.qxbRemoteValidate
    }
  },

  data() {
    return {
      visible: false, // 是否显示弹窗
      loading: false, // 加载中
      openAccount: {}, // 本地缓存开户信息
      qxbCropInfo: null, // 启信宝查询到的企业信息，用来校验
      isShowIntroduce: 1, // 是否显示开户介绍
      activeStep: 0, // 开户流程步骤
      legalStep: 0, // 0, 1:法人信息 2:被授权人或经办人同法人, 3: 大股东同法人, 4: 受益人同法人，5：实际控制人
      LEGAL_STEP,
      stage: 0, // 开户阶段 1-完善企业信息,2-绑定银行账户,3-认证资料审核,4-验证银行账户，5-开通电子账户
      stageStatus: 1, // 1-处理中，2-失败，3-成功
      failReason: '', // 审核失败原因
      payFailStage: 0, // 京东是否打款失败 0否 1是
      realNameHandlerType: 0, // 企业实名处理人类型：0-用户，1-客服
      title: '', // 联系客服提示title
      openGlobalInfo: {
        // 实名类型，0-首次实名，1-收到实名预警通知后重新实名，2-实名失效后重新实名，3-开通失败的支付渠道进行重新实名，非空字段
        realNameAuthType: 0,
        // 如果realNameAuthType=3需要传值，重新实名需要开通的支付渠道类型，以，隔开,1-智付亿联、2-智付百信、3-智付连连、4-智付合利宝
        reAuthPaymentChannel: '',
        corpOpenInfoUpdate: false, // 是否更新商户开户信息
      },
      // stepItem: ['上传证照文件', '完善企业信息', '绑定银行账户', '认证资料审核', '签署协议', '实名认证完成'] // 删除'验证银行账户'环节, 添加'签署协议',
    }
  },

  computed: {
    // 企业信息
    corpInfo() {
      return this.$store.state?.user?.corpInfo
    },
    // 步骤条数据
    stepItem() {
      // 首次实名需要签署协议，非首次实名不需要签署协议步骤(协议已存在)
      return this.corpInfo.corpStatus < 2
        ? ['上传证照文件', '完善企业信息', '绑定银行账户', '认证资料审核', '签署协议', '实名认证完成']
        : ['上传证照文件', '完善企业信息', '绑定银行账户', '认证资料审核', '实名认证完成']
    }
  },
  created() {
    this.$event.on(REAL_NAME_CERTIFICATION_CLOSE, () => {
      this.visible = false
    })
  },

  methods: {
    // 初始化
    /**
     * @param {Object} option
     * realNameAuthType: 0, // 实名类型 ，0-首次实名，1-收到实名预警通知后重新实名，2-实名失效后重新实名，3-开通失败的支付渠道进行重新实名，非空字段
     * realNameAuthType: 支付渠道类型，如果realNameAuthType=3需要传值，重新实名需要开通的支付渠道类型，以，隔开,1-智付亿联、2-智付百信、3-智付连连、4-智付合利宝
     * startOver：默认等于true时，代表去请求拿开户阶段
     * from: 从哪里点击进来的  reopening(渠道开通失败，点击重新开通)
     */
    async init(option) {
      this.$event.emit(SITE_OPEN_ACCOUNT_CLOSE) // 渠道流程弹窗
      this.visible = true // 显示弹窗
      this.isShowIntroduce = 1 // 空白loading
      this.activeStep = 0
      this.legalStep = 0
      this.stage = 0
      this.stageStatus = 1
      this.failReason = '' // 审核失败原因
      this.realNameHandlerType = 0
      // 重新实名
      this.openGlobalInfo.realNameAuthType = option?.realNameAuthType || 0 // 实名类型
      this.openGlobalInfo.reAuthPaymentChannel = option?.reAuthPaymentChannel || '' // 重新实名需要开通的支付渠道类型
      this.openGlobalInfo.corpOpenInfoUpdate = option?.corpOpenInfoUpdate || false // 是否更新商户开户信息
      this.openGlobalInfo.from = option?.from || '' // 从哪里点击进来的
      // 查询商户开 户信息
      if (this.corpInfo?.id) {
        await this.getCorpOpenInfo()
      }
      // 首次实名，或者startOver : 默认等于true时，代表去请求拿开户阶段
      if (this.openGlobalInfo.realNameAuthType === 0 || option?.startOver) {
        await this.getCorpOpenInfoStage()
      } else {
        this.isShowIntroduce = false
      }
    },

    // 查询商户开户信息
    async getCorpOpenInfo() {
      try {
        let res = await openAccountApi.getCorpOpenInfo()
        if (res) {
          res.businessLicenseLongTerm = !!res.businessLicenseLongTerm // 营业执执照是否长期有效,0-否,1-是，非空字段
          res.legalIdentityCardLongTerm = !!res.legalIdentityCardLongTerm // 法人身份证是否长期有效,0-否,1-是，非空字段
          res.agentIdentityCardLongTerm = !!res.agentIdentityCardLongTerm // 经办人身份证是否长期有效,0-否,1-是，非空字段
          res.stockholderIdentityCardLongTerm = !!res.stockholderIdentityCardLongTerm // 大股东身份证是否长期有效,0-否,1-是，非空字段
          res.agentEqualLegal = !!res.agentEqualLegal // 经办人是否同法人,0-否,1-是
          res.beneficiaryEqualLegal = !!res.beneficiaryEqualLegal // 受益人是否同法人,0-否,1-是
          res.stockholderEqualLegal = !!res.stockholderEqualLegal // 大股东是否同法人,0-否,1-是
          // 实际控制人
          if (res.actualController) {
            res.actualController.cardLongTerm = !!res.actualController.cardLongTerm // 证件是否长期有效，0-否，1-是
            res.actualController.controllerEqualLegal = !!res.actualController.controllerEqualLegal // 是否同法人，0-否，1-是
            res.actualController.controllerEqualStockholder = !!res.actualController.controllerEqualStockholder // 是否同股东，0-否，1-是
          }
          // 拼接省市区code
          if (res.provinceCode && res.cityCode && res.districtCode && ![res.provinceName, res.cityName, res.districtName].includes('未知') && ![res.provinceCode, res.cityCode, res.districtCode].includes('000000')) {
            res.provinceCityDistricCodes = [res.provinceCode, res.cityCode, res.districtCode] // 经营省市区code
          } else {
            res.provinceCityDistricCodes = []
          }
          // 设置风控流水号集合
          res.serialNos = []

          // 设置到本地缓存
          this.openAccount = Object.assign(this.openAccount, res)
          user.setOpenAccount(this.openAccount)
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('查询商户开户信息错误 =>', error)
      }
    },

    // 查询开户流程阶段
    async getCorpOpenInfoStage() {
      this.loading = true

      const res = await openAccountApi.getCorpOpenInfoStage({ isRealName: 1, payChannel: '' })
      this.loading = false

      this.stage = res.stage
      this.stageStatus = res.stageStatus || 1

      this.failReason = res.failReason // 审核失败原因
      this.payFailStage = res.payFailStage // 是否打款失败
      this.realNameHandlerType = res.realNameHandlerType
      if (res.stage === 1) { // 完善企业信息 步骤
        this.isShowIntroduce = false
        this.activeStep = 1
        return
      } else if (res.stage === 2) { // 绑定银行账户 步骤
        this.isShowIntroduce = false
        this.activeStep = 2
        // if (this.stageStatus === 3) { // 审核成功
        //   this.activeStep = 3
        // }
        return
      } else if (res.stage === 3) { // 认证资料审核 步骤
        this.isShowIntroduce = false
        this.activeStep = 3
        // if (this.stageStatus === 3) { // 审核成功
        //   this.activeStep = 5
        // }
        return
      } else if (res.stage === 4) { // 签署协议
        this.activeStep = 4
        this.isShowIntroduce = false
        return
      } else if (res.stage === 5) { // 实名认证成功 步骤
        this.activeStep = 5
        this.isShowIntroduce = false
        return
      }

      // else if (res.stage === 4) { // 验证银行账户 步骤
      //   this.activeStep = 4
      //   this.isShowIntroduce = false
      //   if (this.stageStatus === 3) {
      //     this.activeStep = 5
      //   }

      //   if (res.checkAmountFailType === 1) { // 验证失败超过上限
      //     this.title = '今日输入次数已达上限！'
      //     this.$refs.contactService.init()
      //   } else if (res.checkAmountFailType === 2) { // 已过验证期
      //     this.title = '小额打款已过验证期！'
      //     this.$refs.contactService.init()
      //   }

      //   return
      // }

      // 获取本地缓存开户
      this.openAccount = user.getOpenAccount()

      // 打开开户介绍
      this.isShowIntroduce = true
      if (this.openAccount) {
        // 判断开户所需文件有值就跳过开户介绍
        if (objSomeValue(['businessLicenseUrl', 'legalIdentityCardFrontUrl', 'legalIdentityCardBackUrl'], this.openAccount)) {
          this.isShowIntroduce = false
        }

        // 是否已填开户所需文件
        this.isFillUploadImg()

        // 判断是否已填完善企业信息
        this.isFillEnterpriseInformation()
      }
    },

    // 判断是否已填开户所需文件
    isFillUploadImg() {
      if (objEveryValue(['businessLicenseUrl', 'legalIdentityCardFrontUrl', 'legalIdentityCardBackUrl'], this.openAccount)) {
        this.activeStep = 1
      }
    },

    // 判断是否已填完善企业信息
    isFillEnterpriseInformation() {
      if (objEveryValue([
        'companyName',
        'companyIndustry',
        'companyCreditCode',
        'businessLicenseFromDate',
        'companyAddress',
        'provinceCityDistricCodes',
        'establishDate', // 成立日期，非空字段
        'holdingType', // 企业控股类型
        'taxOfficeType', // 收税机构类型
        'companyAddress', // 企业地址，非空字段
        'registerOffice', // 登记机关
        'jdOpenType', // 业务类型
        'bcpNo', // 基本户核准号
        'registerCapital', // 企业注册资本(万)
        'legalName',
        'legalIdentityCard',
        'legalPhone',
        'legalIdentityCardFromDate',
        'legalEmail',
        'legalHouseholdAddress',
        'agentName',
        'agentIdentityCard',
        'agentIdentityCardFrontUrl',
        'agentIdentityCardBackUrl',
        'agentIdentityCardFromDate',
        'agentPhone',
        'agentEmail',
        'stockholderName',
        'stockholderIdentityCard',
        'stockholderIdentityCardFrontUrl',
        'stockholderIdentityCardBackUrl',
        'stockholderIdentityCardFromDate',
        'stockholderPhone',
        'stockholderHouseholdAddress',
        'beneficiaryName',
        'beneficiaryIdentityCard',
        'beneficiaryIdentityCardFrontUrl',
        'beneficiaryIdentityCardBackUrl',
        'beneficiaryIdentityCardFromDate',
        'beneficiaryHouseholdAddress',
        'beneficiaryPhone',
      ], this.openAccount)) {
        if (!this.openAccount.agentEqualLegal) { // 经办人是否同法人,0-否,1-是
          if (this.openAccount.agentAuthUrl) {
            this.activeStep = 2
          }
        } else {
          this.activeStep = 2
        }
      }
    },

    // 马上开户
    open() {
      this.isShowIntroduce = false
    },

    /**
     * 下一步
     * @param {Object} obj 对象
     * @param {string} step 步骤
     * @param {string} type 类型
     * @param {string} legalStep 同法人步骤
     */
    async next(obj) {
      this.activeStep = obj.step
      if (obj.update) { // 传入如果是重新提交
        this.openGlobalInfo.corpOpenInfoUpdate = true
        this.openGlobalInfo.reAuthPaymentChannel = Number(obj.reAuthPaymentChannel)
      }

      switch (obj.step) {
        case 0: // 开户所需文件
          this.stageStatus = 1
          this.openGlobalInfo.realNameAuthType = obj.realNameAuthType || this.openGlobalInfo.realNameAuthType
          this.openGlobalInfo.corpOpenInfoUpdate = this.openGlobalInfo.corpOpenInfoUpdate || obj.update || false
          return
        case 1: // 完善企业信息
          this.stageStatus = 1
          this.openAccount = user.getOpenAccount()
          this.legalStep = obj.legalStep || 0

          if (obj.legalStep > 5) {
            this.activeStep = 2
          }
          return
        case 2: // 绑定银行账户
          this.stage = 0
          this.stageStatus = 1
          this.payFailStage = obj.payFailStage // 京东是否打款失败
          if (obj.resubmit) return // 修改银行账户不查询流程接口
          // 查询开户流程阶段
          await this.getCorpOpenInfoStage()
          return
        case 3: // 认证资料审核
          // 查询开户流程阶段
          await this.getCorpOpenInfoStage()
          return
        // eslint-disable-next-line no-unused-expressions
        default: ''
      }
    },
    // 关闭窗口
    close() {
      this.visible = false
    }
  }
}
</script>
