<!-- 签约中状态文案展示 -->
<style lang="scss" scoped>
.status-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 520px;
  text-align: center;

  .icon {
    display: inline-block;
    border-radius: 50%;
    width: 52px;
    height: 52px;
    color: $font-color;

    &.error {
      color: $color-warning;
    }
  }

  .title {
    margin: 20px 0 4px;
    font-size: 20px;
    font-weight: 600;
    line-height: 28px;
  }

  .tip {
    font-size: 16px;
    color: $color-text-secondary;
    line-height: 22px;
  }

  .contact {
    overflow: hidden;
    overflow-y: auto;
    margin-top: 16px;
    max-height: 150px;
  }

  .code-box {
    display: flex;
    justify-content: space-between;
    margin: 24px auto 32px;
    width: 352px;

    >div {
      width: 140px;
      font-size: 16px;

      img {
        margin-bottom: 8px;
        width: 100%;
      }
    }
  }

  .btn {
    margin-top: 16px;
  }
}

.dec {
  margin-top: 16px;
  font-size: 16px;
  line-height: 16px;
}

.center {
  text-align: center;
}
</style>

<template>
  <section class="status-tip">
    <div v-if="stageStatus === 1">
      <icon class="icon" type="chengjie-wait" />
      <div class="title">签约中</div>
      <div class="tip">您的电子交易账户签约申请正在审核中，请您耐心等待！</div>
      <div class="tip">如有疑问可扫码联系客服经理！</div>
      <div class="code-box">
        <div>
          <img :src="configDefault.customerManagerQr1" alt="联系客服经理">
        </div>
        <div>
          <img :src="configDefault.customerManagerQr2" alt="联系客服经理">
        </div>
      </div>

      <el-button
        class="btn"
        type="primary"
        size="large"
        @click="close"
      >
        我知道了
      </el-button>
    </div>
    <div v-if="stageStatus === 3 || signPaymentFlag">
      <icon class="icon error" type="chengjie-close-circle" />
      <div class="title">{{ signPaymentFlag ? '您的电子交易账户已禁用，暂无法进行接单' : '您的电子交易账户签约失败，请重新发起签约' }}</div>
      <div class="tip contact">
        <!-- 原因：{{ failReason }} -->
      </div>
      <div class="tip">{{ failReason ? `${signPaymentFlag ? '原因：' : '失败原因：'}` + failReason + "，" : "" }}如有疑问可扫码联系客服经理！</div>

      <div class="code-box">
        <div>
          <img :src="configDefault.customerManagerQr1" alt="联系小桑">
          <div class="center">客服一号</div>
        </div>
        <div>
          <img :src="configDefault.customerManagerQr2" alt="联系小朱">
          <div class="center">客服二号</div>
        </div>
      </div>
      <el-button
        v-if="signPaymentFlag"
        class="btn"
        type="primary"
        size="large"
        @click="close"
      >
        我知道了
      </el-button>
      <el-button
        v-else
        v-waiting="['get::loading::/corpOpenInfo']"
        class="btn"
        type="primary"
        size="large"
        @click="back"
      >
        重新签约
      </el-button>
    </div>
  </section>
</template>

<script>
export default {
  name: 'data-review',

  props: {
    next: { // 下一步
      type: Function,
      require: true
    },
    failReason: String, // 审核失败原因
    signPaymentFlag: [String, Number], // 签约户是否禁用
    stageStatus: { // 开户状态
      type: Number,
      require: true
    },
    close: Function, // 关闭窗口
  },

  methods: {
    back() {
      this.next({
        step: 0,
        reSign: true, // 是否重新签约
        viewStageType: 2 // 重新签约
      })
    },

  }
}
</script>
