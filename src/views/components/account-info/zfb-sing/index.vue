<!-- 添加银行签收账户弹窗 -->
<style lang="scss" scoped>
.open-account {
  .empty-box {
    height: 520px;
    background-color: $color-F2F2F2;
  }
}

::v-deep {
  .open-account {
    // 弹窗
    margin-top: 10vh !important;

    .el-dialog__body {
      background-color: $color-F2F2F2;
    }

    .title-left-border {
      position: relative;
      margin: 0 0 8px !important;
      padding-left: 12px;
      font-size: 16px;
      font-weight: 600;
      line-height: 22px;

      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: 4px;
        height: 16px;
        background: $--color-primary;
        transform: translateY(-50%);
        content: "";
      }
    }

    // form表单相关
    .g-required {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .el-form-item {
      display: flex;
      margin-bottom: 12px;
      flex-flow: column;
    }

    .el-select {
      width: 100%;
    }

    .el-form-item__label {
      margin-bottom: 4px;
      width: auto !important;
      height: 22px;
      text-align: left;
      color: $color-text-secondary;
      line-height: 22px;

      &::before {
        font-weight: 600;
      }
    }

    .el-form-item__content {
      margin-left: 0 !important;
      width: 100%;
      height: 40px;
    }

    .el-form-item__error--inline {
      position: absolute;
      display: inherit;
      margin-left: 0;
      line-height: 7px;
    }

    .el-input__inner {
      padding-left: 12px;
      font-size: 16px;
    }
  }
}
</style>

<template>
  <el-dialog
    title="开通电子交易账户"
    width="984px"
    append-to-body
    custom-class="open-account"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <Step
      :active-step="activeStep"
      :step-item="stepItem"
    />

    <div
      v-if="fristJoin"
      v-loading="loading"
      class="empty-box"
      element-loading-background="rgba(0, 0, 0, 0)"
    />

    <section v-if="!fristJoin">
      <!-- 验证银行账户 -->
      <BindBankAccount
        v-if="activeStep === BANG_SING_STAGE_STEP.BIND_BANK_ACCOUNT.STEP"
        :next="next"
        :trader-account-id="traderAccountId"
        :bank-account-name="bankAccountName"
        :re-sign="reSign"
        :payment-channel="paymentChannel"
        :info="info"
      />
      <!-- 签约审核中 -->
      <DataReview
        v-if="activeStep === BANG_SING_STAGE_STEP.VERIFY_BANK_ACCOUNT.STEP"
        :stage-status="stageStatus"
        :fail-reason="failReason"
        :sign-payment-flag="signPaymentFlag"
        :close="close"
        :next="next"
      />
    </section>
  </el-dialog>
</template>

<script>
import BindBankAccount from './bind-bank-account.vue' // 绑定银行账户
import userApi from '@/apis/user' // 接口
import {
  BANG_SING_STAGE_STEP, // 开通流程步骤
  BANG_SING_STAGE_STATUS // 开通流程状态
} from '@/constant' // 常量
import Step from '../components/step.vue' // 流程步骤组件
import DataReview from './data-review.vue' // 签约审核中文案展示
export default {
  name: 'bank-endorsed-account',

  components: {
    Step,
    BindBankAccount,
    DataReview
  },

  data() {
    return {
      stepItem: ['验证银行账户', '签约审核中', '签约成功'],
      failReason: '', // 审核失败原因
      BANG_SING_STAGE_STEP, // 开通流程步骤
      BANG_SING_STAGE_STATUS, // 开通流程状态
      visible: false,
      loading: true, // 加载中
      stageStatus: 1, // 1-处理中，2-失败，3-成功
      ...this.initFormat()
    }
  },

  methods: {
    // 格式化data数据
    initFormat() {
      return {
        info: {},
        reSign: 0, // 是否重新签约，0-不是，1-是
        fristJoin: true, // 第一次进入
        activeStep: 0, // 0-验证银行账户，1-签约审核中,2-签约成功
        stageObj: {}, // 签约状态
        bankAccountName: '', // 企业名称
        traderAccountId: '',
        paymentChannel: 8, // 支付渠道类型，8-智付邦+
        traderCorpId: '',
        viewStageType: null, // 是否是查看进度
        signPaymentFlag: 0, // 签约户是否禁用: 0-否；1-是
      }
    },
    // 初始化
    init(obj) {
      this.reSign = obj.reSign ? 1 : 0
      this.failReason = '' // 审核失败原因
      this.traderAccountId = obj.traderAccountId
      this.paymentChannel = obj.paymentChannel
      this.bankAccountName = obj?.bankAccountName || ''
      this.viewStageType = obj.viewStageType
      this.traderCorpId = obj.traderCorpId
      this.info = obj
      this.visible = true

      if (this.reSign) { // 重新提交
        this.activeStep = 0
        this.stageStatus = 1
        this.fristJoin = false
        return
      }
      // 查询签收流程阶段
      this.getSignStage()
      // 签约银行信息
      // this.getSignBankInfo()
    },

    // 签约银行信息
    async getSignBankInfo() {
      await userApi.getSignBankInfo({ traderAccountId: this.traderAccountId, paymentChannel: this.paymentChannel, traderCorpId: this.traderCorpId })
      // console.log('查询签收流程阶段', res)
    },

    // 查询签收流程阶段
    async getSignStage() {
      // 重新签约不调用接口  0-【查看进度】按钮、1-【查看原因】按钮、2-【重新签约】
      if (this.viewStageType === 1 || this.viewStageType === 0) {
        this.fristJoin = true
        const res = await userApi.channelSignStage({ traderAccountId: this.traderAccountId, viewStageType: this.viewStageType, paymentChannel: this.paymentChannel })
        // console.log('查询签收流程阶段', res)
        this.stageObj = res
        this.stageStatus = res.stageStatus // 1-处理中，2-成功，3-失败
        this.activeStep = res.signStage// 0-验证银行账户，1-签约审核中,2-签约成功
        this.failReason = res.failReason || null
        this.signPaymentFlag = res.signPaymentFlag || 0
      }
      this.fristJoin = false
    },

    // 下一步
    next(obj) {
      this.viewStageType = obj?.viewStageType
      this.reSign = obj.reSign ? 1 : 0
      if (obj.reSign) { // 重新提交
        this.stageStatus = 1
        this.activeStep = BANG_SING_STAGE_STEP.BIND_BANK_ACCOUNT.STEP
        this.fristJoin = false
        return
      }
      // 查询签收流程阶段
      this.getSignStage()
    },

    // 关闭窗口
    close() {
      this.visible = false
    }
  }
}
</script>
