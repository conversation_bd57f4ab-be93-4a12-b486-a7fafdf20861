<!-- 流程步骤组件 -->
<style lang="scss" scoped>
.guide-warp {
  .item {
    display: flex;
    align-items: center;
    padding: 10px 0 10px 4px;

    .icon {
      display: inline-block;
      border-radius: 50%;
      width: 6px;
      height: 6px;
      background: $color-text-secondary;
    }

    .title {
      display: inline-block;
      padding-left: 10px;
      font-size: 14px;
      font-weight: 500;
      color: $color-text-secondary;
    }
  }
}

.current {
  background: rgba($color: $--color-primary, $alpha: 10%);

  .icon {
    background: $--color-primary !important;
  }

  .title {
    color: $--color-primary !important;
  }
}

.after {
  .icon {
    background: $color-text-primary !important;
  }

  .title {
    font-weight: 400 !important;
    color: $color-text-primary !important;
  }
}
</style>

<template>
  <div class="guide-warp">
    <template v-for="(item, index) in guideItem">
      <div :key="index" :class="['item', index === legalStep && 'current', index < legalStep && 'after']">
        <span class="icon" />
        <span class="title">{{ item }}</span>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  name: 'enterprise-guide',
  props: {
    legalStep: Number
  },
  data() {
    return {
      guideItem: ['企业基础信息', '法定代表人信息', '被授权人(经办人)信息', '大股东信息', '实际控股人', '受益所有人']
    }
  },
  computed: {},

}
</script>
