<!-- 被授权人（经办人）、大股东、受益人 表单信息 -->
<style lang="scss" scoped>
@import "../account.scss";

.person-form {
  .main {
    @include flex-sb;

    .left {
      // padding: 16px  16px 0;
      width: 100%;
      background-color: $color-FFFFFF;

      .name {
        margin-top: 16px;
      }
    }

    .img-upload-box {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .subtitle {
        @include flex-sbc;

        margin-bottom: 4px;
        color: $color-text-secondary;
        line-height: 22px;

        .example {
          ::v-deep {
            .el-link--inner {
              color: $font-color;
              cursor: pointer;

              &:hover {
                color: $font-color-hover;
              }
            }

            .el-link.el-link--primary::after {
              border-color: $font-color;
            }
          }

          .example-text {
            @include example-underline;
          }
        }
      }

      .img-box {
        height: 100px;

        .upload-txt {
          font-size: 14px;
          font-weight: 400;
          color: $--color-primary;
        }

        .drag {
          font-size: 14px;
          font-weight: 400;
          color: $color-text-regular;
        }

        .describe {
          padding-bottom: 4px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.person-form {
  .img-upload-icon-plus {
    display: none !important;
  }

  .img-upload-empty {
    height: 102px;
  }
}
</style>

<template>
  <section class="person-form">
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      class="form"
      :inline-message="true"
      :disabled="ruleForm.equalLegal || ruleForm.equalStockholder"
    >
      <div class="main">
        <div style="width: 100%;">
          <template v-if="ruleForm.cardType === STOCKHOLDER_CARD_TYPE.personal.id">
            <div class="fix">
              <div class="img-upload-box width50">
                <div class="subtitle">
                  <span class="g-required">{{ prefix }}身份证人像面</span>
                  <label class="example">
                    <el-image
                      :preview-src-list="legalIdentityFrontExampleUrlList"
                      img-text="示例"
                    />
                  </label>
                </div>
                <div class="img-box">
                  <el-form-item label="" prop="identityCardFrontUrl">
                    <ImgUpload
                      v-model="ruleForm.identityCardFrontUrl"
                      :size-limit="2"
                      :dir="OSS_DIR.ID_CARD"
                      :disabled="ruleForm.equalLegal"
                    >
                      <div slot="empty">
                        <div class="describe"><span class="upload-txt">点击上传</span><span class="drag-txt">/拖拽到此区域</span></div>
                        <div class="describe">证件原件或加盖公章的复印件扫描件</div>
                        <div class="describe">（支持jpg/jpeg/png格式，不超过2M）</div>
                      </div>
                    </ImgUpload>
                  </el-form-item>
                </div>
              </div>
              <div class="img-upload-box width50 mar-left8">
                <div class="subtitle">
                  <span class="g-required">{{ prefix }}身份证国徽面</span>
                  <label class="example">
                    <el-image
                      :preview-src-list="legalIdentityBackExampleUrlList"
                      img-text="示例"
                    />
                  </label>
                </div>
                <div class="img-box">
                  <el-form-item label="" prop="identityCardBackUrl">
                    <ImgUpload
                      v-model="ruleForm.identityCardBackUrl"
                      :size-limit="2"
                      :dir="OSS_DIR.ID_CARD"
                      :disabled="ruleForm.equalLegal"
                    >
                      <div slot="empty">
                        <div class="describe"><span class="upload-txt">点击上传</span><span class="drag-txt">/拖拽到此区域</span></div>
                        <div class="describe">证件原件或加盖公章的复印件扫描件</div>
                        <div class="describe">（支持jpg/jpeg/png格式，不超过2M）</div>
                      </div>
                    </ImgUpload>
                  </el-form-item>
                </div>
              </div>
            </div>
          </template>
          <template v-if="ruleForm.cardType === STOCKHOLDER_CARD_TYPE.enterprise.id && prefix === '大股东'">
            <div class="img-upload-box">
              <div class="subtitle">
                <span class="g-required">{{ prefix }}营业执照原件或加盖公章的复印件扫描件</span>
                <label class="example">
                  <el-image
                    :preview-src-list="businessLicenseExampleUrlList"
                    img-text="示例"
                  />
                </label>
              </div>
              <div class="img-box">
                <el-form-item label="" prop="businessLicenseUrl">
                  <ImgUpload v-model="ruleForm.businessLicenseUrl" :size-limit="2" :dir="OSS_DIR.BUSINESS_LICENSE">
                    <div slot="empty">
                      <div class="describe"><span class="upload-txt">点击上传</span><span class="drag-txt">/拖拽到此区域</span></div>
                      <div class="describe">证件原件或加盖公章的复印件扫描件</div>
                      <div class="describe">（支持jpg/jpeg/png格式，不超过2M）</div>
                    </div>
                  </ImgUpload>
                </el-form-item>
              </div>
            </div>
          </template>

          <!-- 大股东表单项 -->
          <template v-if="prefix === '大股东'">
            <div class="multiseriate">
              <el-form-item
                v-if="prefix === '大股东'"
                :label="`${prefix}证件类型`"
                prop="cardType"
              >
                <el-select v-model="ruleForm.cardType" placeholder="请选择" @change="selectChange">
                  <el-option
                    :label="STOCKHOLDER_CARD_TYPE.personal.name"
                    :value="STOCKHOLDER_CARD_TYPE.personal.id"
                  />
                  <el-option
                    :label="STOCKHOLDER_CARD_TYPE.enterprise.name"
                    :value="STOCKHOLDER_CARD_TYPE.enterprise.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item :label="`${prefix}姓名`" prop="name" class="name">
                <el-input v-model="ruleForm.name" placeholder="请输入大股东名称" />
              </el-form-item>
            </div>
            <div class="multiseriate">
              <el-form-item label="大股东证件号码" prop="identityCard">
                <el-input v-model="ruleForm.identityCard" placeholder="请输入大股东证件号码" maxlength="18" />
              </el-form-item>
              <el-form-item :label="`${prefix}手机号`" prop="phone">
                <el-input v-model="ruleForm.phone" :placeholder="`请输入${prefix}手机号`" maxlength="11" />
              </el-form-item>
            </div>
            <div class="g-required">
              {{ prefix }}证件有效期
              <el-checkbox v-model="ruleForm.identityCardLongTerm" label="结束日期：长期" name="type" />
            </div>
            <div class="multiseriate">
              <el-form-item prop="identityCardFromDate">
                <el-date-picker
                  v-model="ruleForm.identityCardFromDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="开始日期"
                  style="width: 100%;"
                  @change="identityCardFromDateChange"
                />
              </el-form-item>
              <el-form-item prop="identityCardEndDate">
                <el-date-picker
                  v-model="ruleForm.identityCardEndDate"
                  value-format="yyyy-MM-dd"
                  :disabled="!!ruleForm.identityCardLongTerm"
                  type="date"
                  placeholder="结束日期"
                  style="width: 100%;"
                />
              </el-form-item>
            </div>

            <el-form-item :label="`${prefix}证件地址`" prop="householdAddress">
              <el-input v-model="ruleForm.householdAddress" :placeholder="`请输入${prefix}证件地址`" />
            </el-form-item>
          </template>

          <!-- 被授权人表单项 -->
          <template v-if="prefix === '被授权人'">
            <div class="multiseriate">
              <el-form-item :label="`${prefix}姓名`" prop="name" class="name">
                <el-input v-model="ruleForm.name" :placeholder="`${prefix}姓名`" />
              </el-form-item>
              <div class="mar-left8">
                <el-form-item
                  :label="`${prefix}证件类型`"
                  prop="cardType"
                >
                  <el-select
                    v-model="ruleForm.cardType"
                    :disabled="true"
                    placeholder="请选择"
                    @change="selectChange"
                  >
                    <el-option
                      :label="IDENTITY_CARD_TYPE.personal.name"
                      :value="IDENTITY_CARD_TYPE.personal.id"
                    />
                  </el-select>
                </el-form-item>
              </div>
              <el-form-item :label="`${prefix}身份证号`" prop="identityCard">
                <el-input v-model="ruleForm.identityCard" :placeholder="`请输入${prefix}身份证号`" maxlength="18" />
              </el-form-item>
            </div>
            <div class="g-required">
              {{ prefix }}证件有效期
              <el-checkbox v-model="ruleForm.identityCardLongTerm" label="结束日期：长期" name="type" />
            </div>
            <div class="multiseriate">
              <el-form-item prop="identityCardFromDate">
                <el-date-picker
                  v-model="ruleForm.identityCardFromDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="开始日期"
                  style="width: 100%;"
                  @change="identityCardFromDateChange"
                />
              </el-form-item>
              <el-form-item prop="identityCardEndDate">
                <el-date-picker
                  v-model="ruleForm.identityCardEndDate"
                  value-format="yyyy-MM-dd"
                  :disabled="!!ruleForm.identityCardLongTerm"
                  type="date"
                  placeholder="结束日期"
                  style="width: 100%;"
                />
              </el-form-item>
            </div>
            <div class="multiseriate">
              <el-form-item :label="`${prefix}手机号`" prop="phone">
                <el-input v-model="ruleForm.phone" :placeholder="`请输入${prefix}手机号`" maxlength="11" />
              </el-form-item>
              <div class="mar-left8">
                <el-form-item :label="`${prefix}性别`" prop="sex">
                  <el-select v-model="ruleForm.sex" :placeholder="`请选择${prefix}性别`">
                    <el-option label="男" :value="1" />
                    <el-option label="女" :value="2" />
                  </el-select>
                </el-form-item>
              </div>
              <el-form-item :label="`${prefix}电子邮箱`" prop="email">
                <el-input v-model="ruleForm.email" :placeholder="`请输入${prefix}电子邮箱`" />
              </el-form-item>
            </div>
            <div class="multiseriate">
              <el-form-item :label="`${prefix}户籍地址`" prop="householdAddress">
                <el-input v-model="ruleForm.householdAddress" :placeholder="`请输入${prefix}户籍地址`" />
              </el-form-item>
              <el-form-item :label="`${prefix}签发机关`" prop="identityCardIssuingAuthority">
                <el-input v-model="ruleForm.identityCardIssuingAuthority" :placeholder="`请输入${prefix}签发机关`" />
              </el-form-item>
            </div>
            <div v-if="prefix === '被授权人' && !ruleForm.equalLegal" class="img-upload-box">
              <div class="subtitle">
                <span class="g-required">授权委托书正面图片</span>
                <label class="example">
                  <a
                    class="example-text"
                    href="https://cdn.sdpjw.cn/static/shenduBihu/pdf/开户-授权委托书.pdf"
                    target="_blank"
                    rel="noopener noreferrer"
                  >下载授权委托书</a>
                </label>
              </div>
              <div class="img-box">
                <el-form-item label="" prop="authUrl">
                  <ImgUpload v-model="ruleForm.authUrl" :size-limit="2" :dir="OSS_DIR.POWER_OF_ATTORNEY">
                    <div slot="empty">
                      <div class="describe"><span class="upload-txt">点击上传</span><span class="drag-txt">/拖拽到此区域</span></div>
                      <div class="describe">委托书正面加盖公章的复印件扫描件</div>
                      <div class="describe">（支持jpg/jpeg/png格式，不超过2M）</div>
                    </div>
                  </ImgUpload>
                </el-form-item>
              </div>
            </div>
          </template>

          <!-- 受益人表单项 -->
          <template v-if="prefix === '受益人'">
            <div class="multiseriate">
              <el-form-item
                :label="`${prefix}证件类型`"
                prop="cardType"
              >
                <el-select
                  v-model="ruleForm.cardType"
                  placeholder="请选择"
                  @change="selectChange"
                >
                  <el-option
                    :label="IDENTITY_CARD_TYPE.personal.name"
                    :value="IDENTITY_CARD_TYPE.personal.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item :label="`${prefix}姓名`" prop="name" class="name">
                <el-input v-model="ruleForm.name" :placeholder="`请输入${prefix}姓名`" />
              </el-form-item>
            </div>

            <div class="multiseriate">
              <el-form-item :label="`${prefix}证件号码`" prop="identityCard">
                <el-input v-model="ruleForm.identityCard" :placeholder="`请输入${prefix}证件号码`" maxlength="18" />
              </el-form-item>
              <el-form-item :label="`${prefix}手机号`" prop="phone">
                <el-input v-model="ruleForm.phone" :placeholder="`请输入${prefix}手机号`" maxlength="11" />
              </el-form-item>
            </div>
            <div class="g-required">
              {{ prefix }}证件有效期
              <el-checkbox v-model="ruleForm.identityCardLongTerm" label="结束日期：长期" name="type" />
            </div>
            <div class="multiseriate">
              <el-form-item prop="identityCardFromDate">
                <el-date-picker
                  v-model="ruleForm.identityCardFromDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="开始日期"
                  style="width: 100%;"
                  @change="identityCardFromDateChange"
                />
              </el-form-item>
              <el-form-item prop="identityCardEndDate">
                <el-date-picker
                  v-model="ruleForm.identityCardEndDate"
                  :disabled="!!ruleForm.identityCardLongTerm"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="结束日期"
                  style="width: 100%;"
                />
              </el-form-item>
            </div>

            <el-form-item :label="`${prefix}证件地址`" prop="householdAddress">
              <el-input v-model="ruleForm.householdAddress" :placeholder="`请输入${prefix}证件地址`" />
            </el-form-item>
          </template>

          <!-- <div class="title-left-border">{{ prefix === '被授权人' ? '被授权人（经办人）' : prefix }}基本信息</div> -->

        <!--
          <div class="right">
          <div class="title-left-border">{{ prefix === '被授权人' ? '被授权人（经办人）' : prefix }}证件图片</div>
          </div>
        -->
        </div>
      </div>
    </el-form>
  </section>
</template>

<script>
import ImgUpload from '@/views/components/common/img-upload/img-upload.vue' // 图片上传组件
import { isPhone, validateIDNumber, validateEmail, validateUnifiedSocialCreditCode } from '@/common/js/validator' // 验证规则
import { getWord } from '@/common/js/util' // 获取首字母大写的字符串
import openAccountApi from '@/apis/open-account' // 开户接口
import { OSS_DIR } from '@/constant' // 上传文件夹
import { IDENTITY_CARD_TYPE, STOCKHOLDER_CARD_TYPE } from '../options' // 证件类型 大股东企业类型
import user from '@/utils/user' // 用户对象
import { dealTime } from '@/common/js/date' // 时间格式
import { OSS_FILES_URL } from '@/constants/oss-files-url' // 平台违约规则url

const legalIdentityFrontExampleUrl = 'https://oss.chengjie.red/web/imgs/open-account/legal-identity-front.png' // 法人身份证正面示例url
const legalIdentityBackExampleUrl = 'https://oss.chengjie.red/web/imgs/open-account/legal-identity-back.png' // 法人身份证背面示例url
const businessLicenseExampleUrl = 'https://oss.chengjie.red/web/imgs/open-account/business-license.jpeg' // 营业执照示例url
export default {
  name: 'person-form',
  components: {
    ImgUpload
  },

  // inject: ['qxbRemoteValidate'],
  props: {
    // 数据对象
    infoObj: {
      type: Object,
      require: true
    }
  },

  data() {
    // 自定义名称验证规则
    const checkName = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(`请输入${this.prefix}${this.prefix === '大股东' ? '名称' : '姓名'}`))
      }
      callback()
    }

    // // 自定义户籍地址校验
    // const checkDomicileAddress = (rule, value, callback) => {
    //   if (!value) {
    //     return callback(new Error(`请输入${this.prefix}户籍地址`))
    //   }
    //   callback()
    // }

    // 自定义法人证件号验证规则
    const checkLegalIdentityCard = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(`请输入${this.prefix}证件号`))
      }
      if (this.ruleForm.cardType === STOCKHOLDER_CARD_TYPE.enterprise.id) { // 企业类型
        if (!validateUnifiedSocialCreditCode(value)) {
          return callback(new Error(`输入${this.prefix}证件号格式不对`))
        }
      } else if (!validateIDNumber(value)) {
        return callback(new Error(`输入${this.prefix}证件号格式不对`))
      }

      callback()
    }

    // 自定义法人身份证结束日期检验规则
    const checkLegalIdentityCardEndDate = (rule, value, callback) => {
      if (this.ruleForm.identityCardFromDate && value && dealTime(this.ruleForm.identityCardFromDate) > dealTime(value)) {
        callback(new Error('结束日期不能小于开始日期'))
      } else if (value || this.ruleForm.identityCardLongTerm) {
        callback()
      } else {
        callback(new Error('请选择结束日期'))
      }
    }

    // 自定义手机号码验证规则
    const checkPhone = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(`请输入${this.prefix}手机号码`))
      }
      if (!isPhone(value)) {
        return callback(new Error('输入手机号码格式不对'))
      }
      callback()
    }

    // 自定义法人邮箱验证规则
    const checkLegalEmail = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(`请输入${this.prefix}邮箱`))
      }
      if (!validateEmail(value)) {
        return callback(new Error(`输入${this.prefix}邮箱格式不对`))
      }
      callback()
    }

    // 自定义地址验证规则
    const checkAddress = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(`请输入${this.prefix}详细地址`))
      }
      callback()
    }

    // eslint-disable-next-line consistent-this
    // const vm = this
    // 启信宝校验函数
    // const { qxbRemoteValidate } = this

    return {
      OSS_FILES_URL,
      STOCKHOLDER_CARD_TYPE, // 大股东企业类型
      IDENTITY_CARD_TYPE, // 证件类型
      OSS_DIR, // 上传文件夹
      openAccount: null, // 本地缓存开户信息
      infoObjCopy: '', // 备份数据
      prefix: '', // 前缀
      legalIdentityFrontExampleUrlList: [legalIdentityFrontExampleUrl], // 法人身份证正面示例url
      legalIdentityBackExampleUrlList: [legalIdentityBackExampleUrl], // 法人身份证背面示例url
      businessLicenseExampleUrlList: [businessLicenseExampleUrl], // 营业执照示例url
      ruleForm: {
        name: '', // 名称
        cardType: IDENTITY_CARD_TYPE.personal.id, // 大股东证件类型，101-身份证，201-企业
        identityCard: '', // 身份证id
        identityCardFromDate: '', // 开始日期
        identityCardEndDate: '', // 结束日期
        identityCardLongTerm: false, // 否长期有效 0-否,1-是
        phone: '', // 手机号码
        email: '', // 电子邮箱
        householdAddress: '', // 地址
        identityCardFrontUrl: '', // 身份证正面
        identityCardBackUrl: '', // 身份证背面
        businessLicenseUrl: '', // 营业执照
        authUrl: '', // 授权委托书正面图片
        equalLegal: false, // 是否同法人
        sex: '', // 性别
        identityCardIssuingAuthority: ''// 签发机关
      },
      rules: {
        name: [
          { required: true, validator: checkName, trigger: ['blur'] },
          // {
          //   validator(_, value, callback) {
          //     const map = {
          //       大股东: { key: 'shareholderName', errMsg: '企业股权占比最高的股东名称不匹配' },
          //       受益人: { key: 'beneficiaryName', errMsg: `${value}受益所有人不存在` }
          //     }
          //     if (!map[vm.prefix]) {
          //       callback()
          //       return
          //     }
          //     qxbRemoteValidate(map[vm.prefix].key, value).then(
          //       () => callback(),
          //       () => {
          //         const { errMsg } = map[vm.prefix]
          //         callback(new Error(errMsg))
          //       }
          //     )
          //   },
          //   trigger: 'blur'
          // }
        ],
        identityCard: [{ required: true, validator: checkLegalIdentityCard, trigger: ['blur', 'change'] }],
        cardType: [
          { required: true, message: '请选择证件类型', trigger: 'change' },
          // {
          //   validator(_, value, callback) {
          //     qxbRemoteValidate('shareholderCardType', [vm.ruleForm.name, value]).then(
          //       () => callback(),
          //       () => {
          //         const errMsg = '企业股权占比最高的股东证件类型不匹配'
          //         callback(new Error(errMsg))
          //       }
          //     )
          //   },
          //   trigger: 'blur'
          // }
        ],
        identityCardFromDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
        identityCardEndDate: [{ required: true, validator: checkLegalIdentityCardEndDate, trigger: 'change' }],
        phone: [{ required: true, validator: checkPhone, trigger: 'blur' }],
        email: [{ required: true, validator: checkLegalEmail, trigger: 'blur' }],
        householdAddress: [{ required: true, validator: checkAddress, trigger: 'blur' }],
        identityCardFrontUrl: [{ required: true, message: '请上传身份证人像面', trigger: 'none' }],
        identityCardBackUrl: [{ required: true, message: '请上传身份证国徽面', trigger: 'none' }],
        businessLicenseUrl: [{ required: true, message: '请上传营业执照', trigger: 'none' }],
        authUrl: [{ required: true, message: '请上传授权委托书正面图片', trigger: 'none' }],
      },
      isClear: true, // 是否清空结束日期
    }
  },

  watch: {
    infoObj: {
      deep: true,
      handler() {
        Object.assign(this.ruleForm, this.infoObj)
        if (this.ruleForm.equalLegal) {
          this.isClear = false
        }
        this.$refs.ruleForm.validate()
      }
    },

    // 监听变化更新本地缓存
    ruleForm: {
      deep: true,
      handler() {
        this.updateOpenAccount()
      }
    },

    // 监听是否长期有效
    'ruleForm.identityCardLongTerm': {
      handler(val) {
        if (val) {
          this.ruleForm.identityCardEndDate = '2099-12-31'
        } else {
          this.isClear && (this.ruleForm.identityCardEndDate = '')
        }
        this.isClear = true

        this.$refs.ruleForm.validateField('identityCardEndDate')
      }
    },
    // 监听法人身份证正面url
    'ruleForm.identityCardFrontUrl'(url) {
      if (url && !this.ruleForm.equalLegal && !this.ruleForm.equalStockholder && url !== this.infoObjCopy.identityCardFrontUrl) {
        this.postOcrIdentityCardFront(url)
      }
    },
    // 监听法人身份证背面url
    'ruleForm.identityCardBackUrl'(url) {
      if (url && !this.ruleForm.equalLegal && !this.ruleForm.equalStockholder && url !== this.infoObjCopy.identityCardBackUrl) {
        this.postOcrIdentityCardBack(url)
      }
    },
    // 监听营业执照url
    'ruleForm.businessLicenseUrl'(url) {
      if (url && url !== this.infoObjCopy.businessLicenseUrl) {
        this.postOcrBusinessLicense(url)
      }
    },
  },

  created() {
    // 获取本地缓存开户
    this.openAccount = user.getOpenAccount()
    this.prefix = this.infoObj.prefix
    Object.assign(this.ruleForm, this.infoObj)
    this.infoObjCopy = Object.assign({}, this.infoObj)
  },

  methods: {

    // 监听开始日期变化
    identityCardFromDateChange() {
      this.$refs.ruleForm.validateField('identityCardEndDate')
    },

    // 企业类型切换监听变化
    selectChange(val) {
      // 当选中企业时，而且实际控股人选中了同股东，就清空实际控制人的信息（因为企业没有同股东）
      if (val === STOCKHOLDER_CARD_TYPE.enterprise.id && this.openAccount?.actualController?.controllerEqualStockholder) {
        this.openAccount.actualController = {
          name: '', // 名称
          cardType: '', // 实际控制人证件类型，101-身份证，102-护照，103-港澳通行证，104-台湾往来大陆通行证，105-临时身份证，非空字段
          cardNo: '', // 实际控制人证件号，非空字段
          cardValidityBegin: '', // 实际控制人证件开始日期，非空字段
          cardValidityEnd: '', // 实际控制人证件结束日期，非空字段
          cardLongTerm: false, // 证件是否长期有效，0-否，1-是，非空字段
          mobile: '', // 实际控制人手机号码，非空字段
          actualControllerAddress: '', // 实际控制人地址，非空字段
          controllerEqualLegal: false, // 是否同法人，0-否，1-是
          controllerEqualStockholder: false // 是否同股东，0-否，1-是
        }
        user.setOpenAccount(this.openAccount)
      }
      // 当选中企业时，而且受益人选中了同股东，就清空受益人的信息（因为企业没有同股东）
      if (val === STOCKHOLDER_CARD_TYPE.enterprise.id && this.openAccount?.beneficiaryList?.[0]?.beneficiaryEqualStockholder) {
        this.openAccount.beneficiaryList[0] = {
          beneficiaryName: '', // 名称
          beneficiaryIdentityCard: '', // 受益人证件号，非空字段
          beneficiaryIdentityCardFrontUrl: '',
          beneficiaryIdentityCardBackUrl: '',
          beneficiaryIdentityCardFromDate: '', // 受益人证件开始日期，非空字段
          beneficiaryIdentityCardEndDate: '', // 受益人证件结束日期，非空字段
          beneficiaryIdentityCardLongTerm: false, // 证件是否长期有效，0-否，1-是，非空字段
          beneficiaryPhone: '', // 受益人手机号码，非空字段
          beneficiaryHouseholdAddress: '', // 受益人地址，非空字段
          beneficiaryEqualLegal: false, // 是否同法人，0-否，1-是
          beneficiaryEqualStockholder: false // 是否同股东，0-否，1-是
        }
        user.setOpenAccount(this.openAccount)
      }
      this.ruleForm.name = ''
      this.ruleForm.identityCard = ''
      this.ruleForm.identityCardFromDate = ''
      this.ruleForm.identityCardEndDate = ''
      this.ruleForm.identityCardLongTerm = false
      this.ruleForm.phone = ''
      this.ruleForm.email = ''
      this.ruleForm.householdAddress = ''
      this.ruleForm.identityCardFrontUrl = ''
      this.ruleForm.identityCardBackUrl = ''
      this.ruleForm.businessLicenseUrl = ''
      this.ruleForm.agentAuthUrl = ''
    },

    // 身份证正面ocr
    async postOcrIdentityCardFront(url) {
      const res = await openAccountApi.postOcrIdentityCardFront({
        identityCardFrontUrl: url,
      })

      this.ruleForm.name = res.name // 法人姓名，非空字段
      this.ruleForm.identityCard = res.identityCard // 法人身份证号码，非空字段
      this.ruleForm.householdAddress = res.householdAddress // 法人户籍地址，非空字段
      this.ruleForm.sex = res.sex // 性别,选填字段
      res.name && this.$refs.ruleForm.validateField('name')
      res.identityCard && this.$refs.ruleForm.validateField('identityCard')
      res.householdAddress && this.$refs.ruleForm.validateField('householdAddress')
      res.sex && this.$refs.ruleForm.validateField('sex')
      this.updateOpenAccount()
    },

    // 身份证背面ocr
    async postOcrIdentityCardBack(url) {
      const res = await openAccountApi.postOcrIdentityCardBack({
        identityCardBackUrl: url
      })

      this.ruleForm.identityCardFromDate = res.identityCardFromDate // 法人身份证开始日期，非空字段
      this.ruleForm.identityCardEndDate = res.identityCardEndDate // 法人身份证结束日期，非空字段
      this.ruleForm.identityCardLongTerm = !!res.identityCardLongTerm // 法人身份证是否长期有效,0-否,1-是，非空字段
      this.isClear = false
      res.identityCardFromDate && this.$refs.ruleForm.validateField('identityCardFromDate')
      res.identityCardEndDate && this.$refs.ruleForm.validateField('identityCardEndDate')
      this.updateOpenAccount()
    },

    // 识别不出来处理
    identifyEmpty(obj) {
      for (const key in obj) {
        if (Object.hasOwnProperty.call(obj, key)) {
          if (obj[key] === '--' || obj[key] === '无') {
            obj[key] = ''
          }
        }
      }
    },

    // 营业执照ocr
    async postOcrBusinessLicense(url) {
      this.loading = true
      const res = await openAccountApi.postOcrBusinessLicense({
        businessLicenseUrl: url
      })

      // 识别不出来处理
      this.identifyEmpty(res)

      if (!res.companyName && !res.companyCreditCode && !res.businessLicenseLegalName) {
        this.loading = false
        this.ruleForm.businessLicenseUrl = ''
        return this.$message({
          message: '检测到您上传的不是营业执照,或者图片不清晰，识别不出来，请重新上传',
        })
      }

      this.ruleForm.name = res.companyName // 法人姓名，非空字段 // 企业名称
      this.ruleForm.identityCard = res.companyCreditCode // 法人身份证号码，非空字段 // 统一社会信用代码
      this.ruleForm.householdAddress = res.companyAddress // 法人户籍地址，非空字段 // 企业地址
      res.companyName && this.$refs.ruleForm.validateField('name')
      res.companyCreditCode && this.$refs.ruleForm.validateField('identityCard')
      res.companyAddress && this.$refs.ruleForm.validateField('householdAddress')
      this.ruleForm.identityCardFromDate = res.businessLicenseFromDate // 法人身份证开始日期，非空字段 // 营业执照开始日期
      this.ruleForm.identityCardEndDate = res.businessLicenseEndDate // 法人身份证结束日期，非空字段 // 营业执照结束日期
      this.ruleForm.identityCardLongTerm = !!res.businessLicenseLongTerm // 法人身份证是否长期有效,0-否,1-是，非空字段 // 营业执执照是否长期有效
      res.businessLicenseFromDate && this.$refs.ruleForm.validateField('identityCardFromDate')
      res.businessLicenseEndDate && this.$refs.ruleForm.validateField('identityCardEndDate')
      this.updateOpenAccount()
    },

    // keyName拼接
    keyNameFn(key) {
      let keyName = ''
      if (this.prefix === '被授权人') {
        keyName = `agent${getWord(key)}`
      } else if (this.prefix === '大股东') {
        keyName = `stockholder${getWord(key)}`
      } else if (this.prefix === '受益人') {
        keyName = `beneficiary${getWord(key)}`
      }
      return keyName
    },

    // 更新本地缓存开户
    updateOpenAccount() {
      this.openAccount = user.getOpenAccount()

      const { ruleForm } = this
      for (const key in ruleForm) {
        let keyName = this.keyNameFn(key)

        if (this.prefix === '受益人') {
          // console.log('keyName', keyName, this.ruleForm[key])
          this.openAccount.beneficiaryList[this.infoObj.index][keyName] = this.ruleForm[key]
        } else {
          this.openAccount[keyName] = this.ruleForm[key]
        }
      }
      user.setOpenAccount(this.openAccount)
      this.$emit('change')
    },

    // 验证表单
    validate() {
      return this.$refs.ruleForm.validate()
    },

    // 移除表单项的校验结果
    clearValidate() {
      return this.$refs.ruleForm.clearValidate()
    }
  }
}
</script>
