<style lang="scss" scoped>
.status-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 520px;
  text-align: center;

  .icon {
    display: inline-block;
    border-radius: 50%;
    width: 52px;
    height: 52px;
    color: $font-color;

    &.error {
      color: $color-warning;
    }
  }

  .title {
    margin: 20px 0 4px;
    font-size: 20px;
    font-weight: 600;
    line-height: 28px;
  }

  .tip {
    font-size: 16px;
    color: $color-text-secondary;
    line-height: 22px;
  }

  .code-box {
    display: flex;
    justify-content: space-between;
    margin: 24px auto 32px;
    width: 352px;

    >div {
      width: 140px;
      font-size: 16px;

      img {
        margin-bottom: 8px;
        width: 100%;
      }
    }
  }

  .btn {
    margin-top: 16px;
  }
}

.center {
  text-align: center;
}

.error-txt {
  font-size: 16px;
  color: #F51818;
}

.link-text {
  font-size: 14px;
  line-height: 22px;
  color: $font-color;
}

.mt-s {
  margin-top: 6px;
}
</style>

<template>
  <section class="status-tip">
    <!-- 签署协议 -->
    <div v-if="currentStatus !== 2" style="width: 100%;">
      <template v-if="!signUrl">
        <icon class="icon" type="dj-wait" />
        <div class="title">签署协议</div>
        <div class="tip">
          需先完成
          <a
            class="link-text"
            target="_blank"
            :href="OSS_FILES_URL.REGISTER_SERVICE_URL"
            rel="noopener noreferrer"
          >《{{ themeName }}注册服务协议》</a>签署，本次协议签署意愿验证需要跳转E签宝平台进行盖章。
        </div>
        <div class="error-txt mt-s">如当前企业或企业经办人已在e签宝进行过实名认证，请用e签宝实名认证的手机号登录e签宝签署合同。</div>
        <div class="code-box">
          <div>
            <img :src="configDefault.customerManagerQr1" alt="联系客服经理">
            <div class="center">客服一号</div>
          </div>
          <div>
            <img :src="configDefault.customerManagerQr2" alt="联系客服经理">
            <div class="center">客服二号</div>
          </div>
        </div>
        <el-button
          v-loading="loading"
          class="btn"
          type="primary"
          size="large"
          @click="onSign"
        >
          去签署协议
        </el-button>
      </template>
      <template v-else>
        <iframe
          ref="myIframe"
          :src="signUrl"
          style="width: 100%;height: 600px;"
          frameborder="0"
        />
      </template>
    </div>
    <!-- 签署失败 -->
    <div v-if="currentStatus === 2">
      <icon class="icon error" type="dj-close-circle" />
      <div class="title">签署失败</div>
      <div class="tip contact">
        <a
          class="link-text"
          target="_blank"
          :href="OSS_FILES_URL.REGISTER_SERVICE_URL"
          rel="noopener noreferrer"
        >《{{ themeName }}注册服务协议》</a>协议签署失败，失败原因：<span class="error-txt">{{ failReason }}</span>
      </div>
      <div class="tip">如有疑问可扫码联系客服经理！</div>
      <div class="code-box">
        <div>
          <img :src="configDefault.customerManagerQr1" alt="联系小桑">
          <div class="center">客服一号</div>
        </div>
        <div>
          <img :src="configDefault.customerManagerQr2" alt="联系小朱">
          <div class="center">客服二号</div>
        </div>
      </div>
      <el-button
        class="btn"
        type="primary"
        size="large"
        @click="resubmit"
      >
        重新提交
      </el-button>
    </div>
  </section>
</template>

<script>
import { OSS_FILES_URL } from '@/constants/oss-files-url.js'
import openAccountApi from '@/apis/open-account' // 开户接口

export default {
  name: 'sign-protocol-step',
  // inject: ['openGlobalInfo'],
  props: {
    stageStatus: { // 开户状态
      type: Number,
      require: true
    },
    failReason: String, // 审核失败原因
    close: Function, // 关闭窗口
    isRealNameStep: { // 是否是实名认证流程
      type: Number,
      default: 1
    },
  },
  data() {
    return {
      OSS_FILES_URL,
      loading: false, // 去签署协议按钮loading
      currentStatus: null,
      firstSignFlag: true, // 是否是首次发起签署协议，true是首次，false是签署失败(重新提交)
      signUrl: null
    }
  },
  mounted() {
    this.currentStatus = this.stageStatus
  },
  methods: {
    // 去验证
    async onSign() {
      let params = {
        contractType: 'cjReg20240906',
        firstSignFlag: this.firstSignFlag
      }
      try {
        this.loading = true
        let res = await openAccountApi.verifyESignProtocol(params)
        if (res && res.signUrl) {
          this.signUrl = res.signUrl
          // window.location.href = res.signUrl
        }
      } catch (error) {
        // eslint-disable-next-line
        console.log(error)
      } finally {
        this.loading = false
      }
    },
    // 重新提交
    resubmit() {
      this.signUrl = null
      this.currentStatus = 1
      this.firstSignFlag = false
    }
  }
}
</script>
