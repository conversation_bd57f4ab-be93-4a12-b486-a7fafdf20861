<!-- 被授权人（经办人）基本信息 -->
<style lang='scss' scoped>
@import "../account.scss";

.top-box {
  background-color: $color-FFFFFF;
}

.checkbox-box {
  display: flex;
  align-items: center;
  padding-bottom: 16px;

  .label {
    margin-top: 2px;
    margin-right: 16px;
    font-size: 16px;
    font-weight: bold;
  }

  ::v-deep {
    .el-checkbox {
      margin-right: 16px;
    }
  }
}
</style>

<template>
  <main class="agent-identity-page">
    <div class="enterprise-info-box">
      <div class="left">
        <Guide :legal-step="legalStep" />
      </div>
      <div class="right">
        <section class="top-box">
          <div class="checkbox-box">
            <!-- <span class="label">被授权人</span> -->
            <el-checkbox v-model="infoObj.equalLegal" label="同法人" @change="change" />
          </div>

          <PersonForm ref="personFormRef" :info-obj="infoObj" />
        </section>
      </div>
    </div>

    <div class="footer">
      <div class="btn-box">
        <el-button
          size="large"
          type="primary"
          border
          @click="prev"
        >
          上一步
        </el-button>
        <el-button size="large" type="primary" @click="submitForm">下一步</el-button>
      </div>
    </div>
  </main>
</template>

<script>
import PersonForm from './person-form.vue'
import user from '@/utils/user' // 用户对象
import Guide from './enterprise-guide.vue' // 企业信息左侧导航指引
import openAccountApi from '@/apis/open-account' // 开户接口
import { setSerialNos } from '../options'

const infoObjInit = {
  equalLegal: false, // 经办人是否同法人,0-否,1-是
  name: '', // 名称
  identityCardFromDate: '', // 开始日期
  identityCardEndDate: '', // 结束日期
  identityCardLongTerm: false, // 否长期有效 0-否,1-是
  phone: '', // 手机号码
  email: '', // 电子邮箱
  identityCard: '', // 身份证号码
  identityCardFrontUrl: '', // 身份证正面
  identityCardBackUrl: '', // 身份证背面
  authUrl: '', // 经办人授权证明文件url
  sex: '', // 性别
  householdAddress: '', // 户籍地址
  identityCardIssuingAuthority: ''// 签发机关
}

export default {
  name: 'agent-identity-page',
  components: {
    PersonForm,
    Guide
  },

  props: {
    legalStep: Number,
    // 下一步
    next: {
      type: Function,
      require: true
    }
  },

  data() {
    return {
      openAccountApi,
      openAccount: null, // 本地缓存开户信息
      infoObj: { ...infoObjInit }
    }
  },

  computed: {
    // 用户登录手机号码
    mobile() {
      return this.$store.state?.user?.userInfo?.mobile
    },
  },

  created() {
    // 获取本地缓存开户
    this.openAccount = user.getOpenAccount()
    this.infoObj.prefix = '被授权人' // 前缀
    this.infoObj.equalLegal = !!this.openAccount.agentEqualLegal // 经办人是否同法人,0-否,1-是
    this.infoObj.name = this.openAccount.agentName || '' // 名称
    this.infoObj.identityCardFromDate = this.openAccount.agentIdentityCardFromDate || '' // 开始日期
    this.infoObj.identityCardEndDate = this.openAccount.agentIdentityCardEndDate || '' // 结束日期
    this.infoObj.identityCardLongTerm = !!this.openAccount.agentIdentityCardLongTerm // 否长期有效 0-否,1-是
    this.infoObj.phone = this.openAccount.agentPhone || '' // 手机号码
    this.infoObj.email = this.openAccount.agentEmail || '' // 电子邮箱
    this.infoObj.identityCard = this.openAccount.agentIdentityCard // 身份证号码
    this.infoObj.identityCardFrontUrl = this.openAccount.agentIdentityCardFrontUrl || '' // 身份证正面
    this.infoObj.identityCardBackUrl = this.openAccount.agentIdentityCardBackUrl || '' // 身份证背面
    this.infoObj.authUrl = this.openAccount.agentAuthUrl || '' // 经办人授权证明文件url

    this.infoObj.sex = this.openAccount.agentSex || '' // 经办人性别
    this.infoObj.householdAddress = this.openAccount.agentHouseholdAddress || '' // 经办人户籍地址
    this.infoObj.identityCardIssuingAuthority = this.openAccount.agentIdentityCardIssuingAuthority || '' // 签发机关
  },

  methods: {
    // 复选框 同法人 监听改变
    change(v) {
      if (v) {
        this.infoObj.equalLegal = true // 经办人是否同法人,0-否,1-是
        this.infoObj.name = this.openAccount.legalName || '' // 名称
        this.infoObj.identityCardFromDate = this.openAccount.legalIdentityCardFromDate || '' // 开始日期
        this.infoObj.identityCardEndDate = this.openAccount.legalIdentityCardEndDate || '' // 结束日期
        this.infoObj.identityCardLongTerm = !!this.openAccount.legalIdentityCardLongTerm // 否长期有效 0-否,1-是
        this.infoObj.phone = this.openAccount.legalPhone || '' // 手机号码
        this.infoObj.email = this.openAccount.legalEmail || '' // 电子邮箱
        this.infoObj.identityCard = this.openAccount.legalIdentityCard // 身份证号码
        this.infoObj.identityCardFrontUrl = this.openAccount.legalIdentityCardFrontUrl || '' // 身份证正面
        this.infoObj.identityCardBackUrl = this.openAccount.legalIdentityCardBackUrl || '' // 身份证背面
        this.infoObj.sex = this.openAccount.legalSex || '' // 性别
        this.infoObj.householdAddress = this.openAccount.legalHouseholdAddress || '' // 户籍地址
        this.infoObj.identityCardIssuingAuthority = this.openAccount.legalIdentityCardIssuingAuthority || '' // 签发机关
      } else {
        Object.assign(this.infoObj, infoObjInit)
        this.$nextTick().then(() => {
          this.$refs.personFormRef.clearValidate()
        })
      }
    },
    // 上一步
    prev() {
      // 初始化风控流水号
      setSerialNos({ no: '', step: this.legalStep, isNext: false })
      this.next({
        step: 1,
        type: 'prev',
        legalStep: this.legalStep - 1
      })
    },

    // 下一步
    async submitForm() {
      // 获取本地缓存开户
      this.openAccount = user.getOpenAccount()
      // if (!(this.mobile === this.openAccount.legalPhone || this.mobile === this.openAccount.agentPhone)) {
      //   return this.$message.warning('法人手机号与授权人手机号任一个必须与注册手机号一致，请重新修改')
      // }
      if (this.openAccount.agentPhone !== this.mobile && this.openAccount.agentPhone !== this.openAccount.legalPhone) {
        return this.$message.warning('被授权人手机号码需与注册手机号码或法定代表人手机号码相同')
      }
      if (await this.$refs.personFormRef.validate()) {
        // 经办人三要素校验
        await openAccountApi.agentUserCheck({
          agentName: this.openAccount.agentName,
          agentIdentityCard: this.openAccount.agentIdentityCard,
          agentPhone: this.openAccount.agentPhone,
        })
        // 被授权人不掉风控接口 但需要设置空的风控编号进行占位
        setSerialNos({ no: '', step: this.legalStep, isNext: true })
        this.next({
          step: 1,
          type: 'next',
          legalStep: this.legalStep + 1
        })
      }
    },
  },
}
</script>
