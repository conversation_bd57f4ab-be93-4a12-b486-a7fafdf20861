<style lang="scss" scoped>
.open-account-success {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0 26px;
  height: 523px;
  font-size: 16px;
  text-align: center;

  .title {
    margin: 0 0 4px;
    margin-bottom: 40px;
    font-size: 20px;
    font-weight: 600;
    line-height: 28px;
  }

  .icon {
    display: inline-block;
    margin-bottom: 20px;
    border-radius: 50%;
    font-size: 64px;
    color: $--color-primary;
  }
}
</style>

<template>
  <section class="open-account-success">
    <div>
      <icon class="icon success" type="chengjie-check-circle" />
      <div class="title">实名认证成功！</div>
      <div>
        <el-button
          size="large"
          type="primary"
          @click="trading"
        >
          马上交易
        </el-button>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'success-lite',
  props: {
    close: Function // 关闭窗口
  },
  data() {
    return {}
  },
  mounted() {
    this.$store.dispatch('user/getUserInfo', { forceUpdate: true })

    setTimeout(() => {
      // 获取企业信息 刷新侧边栏菜单
      this.$store.dispatch('user/getIsOpenAccount')
    }, 2000)
  },
  methods: {
    // 马上交易
    trading() {
      this.close()
      this.$router.push('/user-center/cute-hand')
    },
  }
}
</script>
