<!-- 开户成功组件 -->
<style lang="scss" scoped>
.open-account-success {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0 26px;
  height: 523px;
  font-size: 16px;
  text-align: center;

  .success {
    color: $font-color !important;
  }

  .error {
    color: $--color-primary !important;
  }

  .unopen {
    color: $color-text-secondary;
  }

  .icon {
    display: inline-block;
    margin-bottom: 20px;
    border-radius: 50%;
    width: 52px;
    height: 52px;
    font-size: 52px;
    color: $--color-primary;
  }

  .title {
    margin: 0 0 4px;
    font-size: 20px;
    font-weight: 600;
    line-height: 28px;
  }

  .tip {
    font-size: 16px;
    color: $color-text-secondary;
    line-height: 22px;
  }

  .contact {
    @include example-underline;
  }

  .open-results-box {
    display: flex;
    justify-content: center;
    margin: 24px 0;

    .open-results {
      margin-right: 16px;
      border-radius: 2px;
      padding-top: 26px;
      width: 172px;
      height: 180px;
      text-align: center;
      background: $color-FFFFFF;

      &:last-child {
        margin-right: 0;
      }

      .icon-box {
        height: 32px;

        .svg-icon {
          font-size: 32px;
        }
      }

      .name {
        margin: 12px 0 8px;
        font-size: 16px;
        font-weight: 600;
        line-height: 22px;
      }

      .rang {
        position: relative;
        display: inline-block;
        margin-right: 5px;
        width: 32px;
        height: 32px;

        &::before {
          position: absolute;
          top: 0%;
          left: 0%;
          width: 100%;
          height: 100%;
          background-size: 100%;
          content: "";
          background-image: url("https://oss.chengjie.red/web/imgs/open-account/rang.png");
          translate: all .5s;
          animation: around 1s linear infinite;
        }

        &::after {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 10px;
          height: 15px;
          background-size: 100%;
          transform: translateX(-50%) translateY(-50%);
          content: "";
          background-image: url("https://oss.chengjie.red/web/imgs/open-account/lightning.png");
        }
      }

      @keyframes around {
        0% {
          transform: rotate(0);
        }

        100% {
          transform: rotate(360deg);
        }
      }

      .status {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        line-height: 20px;
        color: $color-text-regular;

        .dot {
          display: inline-block;
          border-radius: 50%;
          width: 2px;
          height: 2px;
          animation: dotting 2.4s  infinite step-start;
        }

        @keyframes dotting {
          25% {
            box-shadow: 2px 0 0 $color-text-secondary;
          }

          50% {
            box-shadow: 2px 0 0 $color-text-secondary, 6px 0 0 $color-text-secondary;
          }

          75% {
            box-shadow: 2px 0 0 $color-text-secondary, 6px 0 0 $color-text-secondary, 10px 0 0 $color-text-secondary;
          }
        }
      }

      .reasons {
        font-size: 12px;
        color: $color-text-secondary;

        @include ellipsis(3);
      }
    }
  }
}

.tooltip {
  width: 200px;
}

.link-tips {
  font-size: 14px;

  a {
    color: $--color-primary;
    cursor: pointer;
  }
}
</style>

<template>
  <section class="open-account-success">
    <div v-if="isShow">
      <icon class="icon success" :class="[failNum === channelNum ? 'error' : '']" :type="openingNum === channelNum ? 'sdicon-wait' : failNum === channelNum ? 'sdicon-close-circle' : 'sdicon-check-circle'" />

      <!-- 所有渠道都未开通 -->
      <template v-if="unopenNum === channelNum">
        <div class="title">小额打款验证成功！</div>
        <div v-if="isShowGuideText" class="link-tips">为了您有更便捷的操作体验，我们会在您实名认证成功后将所有交易渠道全部开通，详情可联系客服经理</div>
      </template>

      <!-- 所有渠道正在开通中 -->
      <template v-else-if="openingNum === channelNum">
        <div class="title">小额打款验证成功！</div>
        <div class="title"> 因不同用户使用不同的电子账户，为提高成交效率，平台会帮您开通现有的 <span class="emphasis">{{ channelNum }}</span> 个电子交易账户</div>
        <div class="title">预计等待 <span class="emphasis">5</span> 分钟</div>
        <div v-if="isShowGuideText" class="link-tips">为了您有更便捷的操作体验，我们会在您实名认证成功后将所有交易渠道全部开通，详情可联系客服经理</div>
      </template>

      <!-- 所有渠道开通失败 -->
      <template v-else-if="failNum === channelNum">
        <div class="title">有 <span class="emphasis">{{ channelNum }}</span> 个电子交易账户开通失败，请联系客服处理</div>
        <div v-if="isShowGuideText" class="link-tips">为了您有更便捷的操作体验，我们会在您实名认证成功后将所有交易渠道全部开通，详情可联系客服经理</div>
      </template>

      <!-- 混合情况 -->
      <template v-else>
        <div v-if="successNum > 0" class="title">您已成功开通 <span class="emphasis">{{ successNum }}</span> 个电子交易账户，可以进行交易啦</div>
        <div v-if="openingNum > 0" class="title">有 <span class="emphasis">{{ openingNum }}</span> 个电子交易账户正在开通中</div>
        <div v-if="failNum > 0" class="tip">有 {{ failNum }} 个电子交易账户开通失败，可联系客服帮忙处理</div>
        <div v-if="isShowGuideText" class="link-tips">为了您有更便捷的操作体验，我们会在您实名认证成功后将所有交易渠道全部开通，详情可联系客服经理</div>
      </template>

      <ul class="open-results-box">
        <template v-for="item in list">
          <li v-if="![PAYMENT_CHANNEL.ZHI_FU_YI_LIAN.id, PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG.id].includes(item.paymentChannel)" :key="item.paymentChannel" class="open-results">
            <div class="icon-box">
              <icon v-if="item.accountStatus === 0" class="unopen" type="sdicon-info-circle" />
              <i v-if="item.accountStatus === 1" class="rang" />
              <icon v-if="item.accountStatus === 2" class="success" type="chengjie-check-circle" />
              <icon v-if="item.accountStatus === 3" class="error" type="chengjie-close-circle" />
            </div>
            <div class="name">
              {{ PAYMENT_CHANNEL_VALUE_MAP[item.paymentChannel] }}
            </div>

            <div class="status">
              <div v-if="item.accountStatus === 0">未开通</div>
              <div v-if="item.accountStatus === 1" class="opening">
                正在开通中<span class="dot" />
              </div>
              <div v-if="item.accountStatus === 2" class="success">
                开通成功
              </div>
              <div v-if="item.accountStatus === 3" class="error">
                开通失败
              </div>
              <div v-if="item.accountStatus === 4" class="error">
                已注销
              </div>
            </div>

            <el-tooltip
              v-if="item.accountStatus === 3"
              effect="dark"
              placement="top"
              :disabled="item.accountFailReason && item.accountFailReason.length < 40"
            >
              <template #content>
                <div class="tooltip">
                  {{ item.accountFailReason }}
                </div>
              </template>
              <div class="reasons">{{ item.accountFailReason }}</div>
            </el-tooltip>
          </li>
        </template>
      </ul>

      <div v-if="openingNum > 0" class="tip">您可以关闭当前弹窗，任意渠道的开户结果都会通过弹窗提醒您</div>
      <div>
        <el-button
          v-if="successNum > 0"
          size="large"
          type="primary"
          @click="trading"
        >
          马上交易
        </el-button>
        <el-button
          v-if="failNum === channelNum"
          size="large"
          type="primary"
          @click="resubmit"
        >
          重新开通
        </el-button>

        <el-button
          v-if="failNum > 0"
          size="large"
          type="primary"
          border
          @click="contact"
        >
          联系客服
        </el-button>
      </div>
    </div>
  </section>
</template>

<script>
import { SITE_OPEN_CONTACT_SERVICE } from '@/event/modules/site'
import openAccountApi from '@/apis/open-account' // 开户接口
import { WEB_SOCKET_EVENT } from '@/websocket/constant' // mqtt监听事件
import { PAYMENT_CHANNEL_VALUE_MAP, PAYMENT_CHANNEL } from '@/constant' // 常量
import { ZHI_FU_YI_LIAN_PLUS_GUIDE_URL } from '@/constants/oss-files-url' // 智付E+操作指引
export default {
  name: 'open-account-success',

  props: {
    next: {
      type: Function,
      require: true
    },
    // stage: Number, // 开户阶段
    // stageStatus: { // 开户状态
    //   type: Number,
    //   require: true
    // },
    // failReason: String, // 审核失败原因
    close: Function // 关闭窗口
  },

  data() {
    return {
      PAYMENT_CHANNEL_VALUE_MAP,
      PAYMENT_CHANNEL,
      list: [], // 开通账户列表
      isShow: false, // 是否显示
      isShowGuideText: false, // 是否展示智付E+操作指引文案
    }
  },

  computed: {
    // 未开通数量
    unopenNum() {
      return this.list.filter(item => item.accountStatus === 0).length
    },
    // 开通中的数量
    openingNum() {
      return this.list.filter(item => item.accountStatus === 1).length
    },
    // 开通成功数量
    successNum() {
      return this.list.filter(item => item.accountStatus === 2).length
    },
    // 开通失败数量
    failNum() {
      // eslint-disable-next-line no-magic-numbers
      return this.list.filter(item => item.accountStatus === 3).length
    },
    // 开通渠道的数量
    channelNum() {
      return this.list.length
    }
  },

  mounted() {
    this.getPaymentAccountList()

    // 监听支付渠道开通结果
    this.$websocket.on(WEB_SOCKET_EVENT.PAYMENT_CHANNEL_APPLY_RESULT, data => {
      data.paymentChannel !== 6 && this.getPaymentAccountList() // 亿联银行的不调用开户进度弹窗事件
    })

    setTimeout(() => {
      // 获取企业信息 刷新侧边栏菜单
      this.$store.dispatch('user/getIsOpenAccount')
    }, 2000)
  },

  methods: {
    // 查询支付渠道账户列表
    async getPaymentAccountList() {
      const res = await openAccountApi.getPaymentAccountList()
      this.isShow = true
      // 智付渠道列表中否包含智付E+渠道展示操作指引文案提示
      this.isShowGuideText = res.some(item => item.paymentChannel === PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id && item.banStatus === 0)
      //  "banStatus": 0 // 禁用状态，1-禁用、0-正常
      this.list = res.filter(item => item.banStatus === 0 && ![PAYMENT_CHANNEL.YI_LIAN_BANK.id, PAYMENT_CHANNEL.ZHI_FU_YI_LIAN.id, PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG.id].includes(item.paymentChannel))
    },

    // 联系客服
    contact() {
      this.$event.emit(SITE_OPEN_CONTACT_SERVICE)
    },

    // 马上交易
    trading() {
      this.close()
      this.$router.push('/user-center/cute-hand')
    },

    // 重新开通
    resubmit() {
      // this.next({
      //   step: 0,
      //   realNameAuthType: REAL_NAME_AUTH_TYPE.FAIL,
      // })
      this.close()
      this.$router.push('/user-center/info')
    },
    viewGuide() {
      window.open(ZHI_FU_YI_LIAN_PLUS_GUIDE_URL)
    },
  }
}
</script>
