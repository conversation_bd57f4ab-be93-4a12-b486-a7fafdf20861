<!-- 受益人信息 -->
<style lang='scss' scoped>
@import "../account.scss";

.box {
  .checkbox-cls {
    padding: 12px 0;
  }
}

.block {
  margin-bottom: 12px;
  background-color: $color-FFFFFF;

  &:last-child {
    margin-bottom: 0;
  }
}

.title {
  border-bottom: 1px solid $color-F0F0F0;
  padding: 12px 0;
  font-size: 16px;

  @include flex-sbc;

  ::v-deep {
    .el-checkbox {
      top: -2px;
      margin-left: 16px;
    }
  }

  .name {
    font-weight: 600;
  }

  .title-left-border {
    margin-bottom: 0 !important;
  }

  .folding-panel {
    font-weight: 600;
    color: $--color-primary;

    >span {
      cursor: pointer;
    }
  }
}

.put-away-text {
  margin-right: 3px;
}

.refresh-btn {
  transition: all .4s;
  transform: rotate(0);
}

.refresh-btn-active {
  display: inline-block;
  transform: rotate(180deg);
}
</style>

<template>
  <main class="beneficiary-page">
    <div class="enterprise-info-box">
      <div class="left">
        <Guide :legal-step="legalStep" />
      </div>
      <div class="right">
        <section ref="box" class="box">
          <div v-for="(item, index) in beneficiaryList" :key="index" class="block">
            <div v-if="beneficiaryList.length > 1" class="title">
              <div class="name title-left-border">
                受益人-{{ item.name }}
              </div>
              <div class="folding-panel">
                <span v-if="index > 0" @click="del(index)">删除</span>
                <span @click="toggle(index)">
                  {{ item.isVisible ? '收起' : '展开' }}
                  <icon
                    class="icon refresh-btn"
                    :class="!item.isVisible && 'refresh-btn-active'"
                    type="chengjie-up"
                  />
                </span>
              </div>
            </div>
            <el-collapse-transition>
              <div v-show="item.isVisible">
                <div class="checkbox-cls">
                  <el-checkbox
                    v-if="index === 0"
                    v-model="item.equalLegal"
                    :disabled="item.equalStockholder"
                    @change="equalLegalChange"
                  >
                    同法人
                  </el-checkbox>
                  <el-checkbox
                    v-if="index === 0 && openAccount.stockholderCardType === STOCKHOLDER_CARD_TYPE.personal.id"
                    v-model="item.equalStockholder"
                    :disabled="item.equalLegal"
                    @change="equalStockholderChange"
                  >
                    同股东
                  </el-checkbox>
                </div>
                <PersonForm
                  :ref="`personFormRef${index}`"
                  :key="index"
                  :info-obj="item"
                  @change="change"
                />
              </div>
            </el-collapse-transition>
          </div>
        </section>
      </div>
    </div>

    <div class="footer">
      <div class="btn-box">
        <el-button
          size="large"
          type="primary"
          border
          :disabled="beneficiaryList.length >= 3"
          @click="add"
        >
          添加受益人
        </el-button>
        <el-button
          size="large"
          type="primary"
          border
          @click="prev"
        >
          上一步
        </el-button>
        <el-button size="large" type="primary" @click="submitForm">{{ openGlobalInfo.realNameAuthType === REAL_NAME_AUTH_TYPE.FAIL ? '提交' : '下一步' }}</el-button>
      </div>
    </div>
  </main>
</template>

<script>
import PersonForm from './person-form.vue' // 被授权人（经办人）、大股东、受益人 表单信息
import user from '@/utils/user' // 用户对象
import openAccountApi from '@/apis/open-account' // 开户接口
import { REFRESH_ACCOUNT } from '@/event/modules/site' // 监听常量
import { formatTime } from '@/common/js/date' // 时间格式化
import { STOCKHOLDER_CARD_TYPE, riskManagementTips, setSerialNos, IDENTITY_CARD_TYPE } from '../options' // 大股东企业类型
import { REAL_NAME_AUTH_TYPE } from '@/constant'
import Guide from './enterprise-guide.vue' // 企业信息左侧导航指引

export default {
  name: 'beneficiary-page',
  components: {
    PersonForm,
    Guide
  },
  inject: ['openGlobalInfo'],
  props: {
    legalStep: Number,
    // 下一步
    next: {
      type: Function,
      require: true
    },
    isRealNameStep: { // 是否是实名认证流程
      type: Number,
      default: 1
    },
  },
  data() {
    return {
      REAL_NAME_AUTH_TYPE,
      STOCKHOLDER_CARD_TYPE,
      IDENTITY_CARD_TYPE,
      openAccount: null, // 本地缓存开户信息
      beneficiaryList: [], // 受益人列表
      ruleForm: {}
    }
  },

  created() {
    // 获取本地缓存开户
    this.openAccount = user.getOpenAccount()
    if (this.openAccount.beneficiaryList && this.openAccount.beneficiaryList.length) {
      this.beneficiaryList = this.openAccount.beneficiaryList.map((item, index) => ({
        index,
        isVisible: true,
        prefix: '受益人', // 前缀
        name: item.beneficiaryName, // 名称
        identityCard: item.beneficiaryIdentityCard, // 身份证id
        identityCardFromDate: item.beneficiaryIdentityCardFromDate, // 开始日期
        identityCardEndDate: item.beneficiaryIdentityCardEndDate, // 结束日期
        identityCardLongTerm: !!item.beneficiaryIdentityCardLongTerm, // 否长期有效 0-否,1-是
        phone: item.beneficiaryPhone, // 手机号码
        householdAddress: item.beneficiaryHouseholdAddress, // 地址
        identityCardFrontUrl: item.beneficiaryIdentityCardFrontUrl, // 身份证正面
        identityCardBackUrl: item.beneficiaryIdentityCardBackUrl, // 身份证背面
        equalLegal: !!item.beneficiaryEqualLegal, // 受益人是否同法人,0-否,1-是
        equalStockholder: !!item.beneficiaryEqualStockholder, // 受益人是否同股东,0-否,1-是
        cardType: item.beneficiaryCardType || IDENTITY_CARD_TYPE.personal.id // 受益人证件类型
      }))
      // console.log('beneficiaryList', this.beneficiaryList)
      if (this.beneficiaryList[0].equalLegal) { // 受益人是同法人，才去触发更新实时同步法人信息，因为前面有可能更改法人信息
        this.equalLegalChange(this.beneficiaryList[0].equalLegal)
      }
    } else {
      this.add()
    }

    // 为了和法人和股东信息改变后保持一致
    this.$nextTick().then(() => {
      this.beneficiaryList[0].equalLegal && this.equalLegalChange(true)
      this.beneficiaryList[0].equalStockholder && this.equalStockholderChange(true)
    })
  },

  methods: {
    // 是否同法人
    equalLegalChange(v) {
      // console.log(v)
      if (v) {
        this.openAccount.beneficiaryList[0] = {
          beneficiaryName: this.openAccount.legalName, // 受益人姓名，非空字段
          beneficiaryIdentityCard: this.openAccount.legalIdentityCard, // 受益人身份证号码，非空字段
          beneficiaryIdentityCardFrontUrl: this.openAccount.legalIdentityCardFrontUrl, // 受益人身份证正面url，非空字段
          beneficiaryIdentityCardBackUrl: this.openAccount.legalIdentityCardBackUrl, // 受益人身份证背面url，非空字段
          beneficiaryIdentityCardFromDate: this.openAccount.legalIdentityCardFromDate, // 受益人身份证开始日期，非空字段
          beneficiaryIdentityCardEndDate: this.openAccount.legalIdentityCardEndDate, // 受益人身份证结束日期，非空字段
          beneficiaryIdentityCardLongTerm: this.openAccount.legalIdentityCardLongTerm, // 受益人身份证是否长期有效,0-否,1-是，非空字段
          beneficiaryPhone: this.openAccount.legalPhone, // 受益人手机号码，非空字段
          beneficiaryHouseholdAddress: this.openAccount.legalHouseholdAddress, // 受益人户籍地址，非空字段
          beneficiaryEqualLegal: true, // 受益人是否同法人,0-否,1-是
          beneficiaryEqualStockholder: false, // 受益人是否同股东,0-否,1-是
          beneficiaryCardType: this.openAccount.legalIdentityCardType || IDENTITY_CARD_TYPE.personal.id // 受益人证件类型
        }
      } else {
        this.openAccount.beneficiaryList[0] = {
          beneficiaryName: '', // 受益人姓名，非空字段
          beneficiaryIdentityCard: '', // 受益人身份证号码，非空字段
          beneficiaryIdentityCardFrontUrl: '', // 受益人身份证正面url，非空字段
          beneficiaryIdentityCardBackUrl: '', // 受益人身份证背面url，非空字段
          beneficiaryIdentityCardFromDate: '', // 受益人身份证开始日期，非空字段
          beneficiaryIdentityCardEndDate: '', // 受益人身份证结束日期，非空字段
          beneficiaryIdentityCardLongTerm: '', // 受益人身份证是否长期有效,0-否,1-是，非空字段
          beneficiaryPhone: '', // 受益人手机号码，非空字段
          beneficiaryHouseholdAddress: '', // 受益人户籍地址，非空字段
          beneficiaryEqualLegal: false, // 受益人是否同法人,0-否,1-是
          beneficiaryEqualStockholder: false, // 受益人是否同股东,0-否,1-是
          beneficiaryCardType: IDENTITY_CARD_TYPE.personal.id // 受益人证件类型 默认身份证
        }
      }
      this.$nextTick().then(() => {
        this.$refs.personFormRef0[0].clearValidate()
      })
      this.updateOpenAccount()
      this.change()
    },

    // 是否同股东
    equalStockholderChange(v) {
      if (v) {
        this.openAccount.beneficiaryList[0] = {
          beneficiaryName: this.openAccount.stockholderName, // 受益人姓名，非空字段
          beneficiaryIdentityCard: this.openAccount.stockholderIdentityCard, // 受益人身份证号码，非空字段
          beneficiaryIdentityCardFrontUrl: this.openAccount.stockholderIdentityCardFrontUrl, // 受益人身份证正面url，非空字段
          beneficiaryIdentityCardBackUrl: this.openAccount.stockholderIdentityCardBackUrl, // 受益人身份证背面url，非空字段
          beneficiaryIdentityCardFromDate: this.openAccount.stockholderIdentityCardFromDate, // 受益人身份证开始日期，非空字段
          beneficiaryIdentityCardEndDate: this.openAccount.stockholderIdentityCardEndDate, // 受益人身份证结束日期，非空字段
          beneficiaryIdentityCardLongTerm: this.openAccount.stockholderIdentityCardLongTerm, // 受益人身份证是否长期有效,0-否,1-是，非空字段
          beneficiaryPhone: this.openAccount.stockholderPhone, // 受益人手机号码，非空字段
          beneficiaryHouseholdAddress: this.openAccount.stockholderHouseholdAddress, // 受益人户籍地址，非空字段
          beneficiaryEqualLegal: false, // 受益人是否同法人,0-否,1-是
          beneficiaryEqualStockholder: true, // 受益人是否同股东,0-否,1-是
          beneficiaryCardType: this.openAccount.stockholderCardType || IDENTITY_CARD_TYPE.personal.id // 受益人证件类型
        }
      } else {
        this.openAccount.beneficiaryList[0] = {
          beneficiaryName: '', // 受益人姓名，非空字段
          beneficiaryIdentityCard: '', // 受益人身份证号码，非空字段
          beneficiaryIdentityCardFrontUrl: '', // 受益人身份证正面url，非空字段
          beneficiaryIdentityCardBackUrl: '', // 受益人身份证背面url，非空字段
          beneficiaryIdentityCardFromDate: '', // 受益人身份证开始日期，非空字段
          beneficiaryIdentityCardEndDate: '', // 受益人身份证结束日期，非空字段
          beneficiaryIdentityCardLongTerm: '', // 受益人身份证是否长期有效,0-否,1-是，非空字段
          beneficiaryPhone: '', // 受益人手机号码，非空字段
          beneficiaryHouseholdAddress: '', // 受益人户籍地址，非空字段
          beneficiaryEqualLegal: false, // 受益人是否同法人,0-否,1-是
          beneficiaryEqualStockholder: false, // 受益人是否同股东,0-否,1-是
          beneficiaryCardType: IDENTITY_CARD_TYPE.personal.id, // 受益人证件类型 默认身份证
        }
      }
      this.$nextTick().then(() => {
        this.$refs.personFormRef0[0].clearValidate()
      })
      this.updateOpenAccount()
      this.change()
    },

    // 表单修改
    change() {
    // 获取本地缓存开户
      this.openAccount = user.getOpenAccount()
      // console.log('表单修改', this.openAccount.beneficiaryList)
      this.beneficiaryList = this.openAccount.beneficiaryList.map((item, index) => ({
        index,
        isVisible: item.isVisible === undefined ? true : item.isVisible,
        prefix: '受益人', // 前缀
        name: item.beneficiaryName, // 名称
        identityCard: item.beneficiaryIdentityCard, // 受益人身份证号码
        identityCardFromDate: item.beneficiaryIdentityCardFromDate, // 开始日期
        identityCardEndDate: item.beneficiaryIdentityCardEndDate, // 结束日期
        identityCardLongTerm: !!item.beneficiaryIdentityCardLongTerm, // 否长期有效 0-否,1-是
        phone: item.beneficiaryPhone, // 手机号码
        householdAddress: item.beneficiaryHouseholdAddress, // 地址
        identityCardFrontUrl: item.beneficiaryIdentityCardFrontUrl, // 身份证正面
        identityCardBackUrl: item.beneficiaryIdentityCardBackUrl, // 身份证背面
        equalLegal: !!item.beneficiaryEqualLegal, // 受益人是否同法人,0-否,1-是
        equalStockholder: !!item.beneficiaryEqualStockholder, // 受益人是否同股东,0-否,1-是
        cardType: item.beneficiaryCardType // 受益人证件类型

      }))
    },

    // 展开收起
    toggle(index) {
      this.beneficiaryList[index].isVisible = !this.beneficiaryList[index].isVisible
      this.$forceUpdate()
      this.openAccount.beneficiaryList[index].isVisible = this.beneficiaryList[index].isVisible
      this.updateOpenAccount()
    },

    // 添加受益人
    add() {
      // console.log('add')
      this.beneficiaryList.push({
        index: this.beneficiaryList.length,
        isVisible: true,
        prefix: '受益人',
        name: '', // 名称
        identityCard: '', // 身份证号码
        identityCardFromDate: '', // 开始日期
        identityCardEndDate: '', // 结束日期
        identityCardLongTerm: false, // 否长期有效 0-否,1-是
        phone: '', // 手机号码
        householdAddress: '', // 地址
        identityCardFrontUrl: '', // 身份证正面
        identityCardBackUrl: '', // 身份证背面
        equalLegal: false, // 受益人是否同法人,0-否,1-是
        equalStockholder: false, // 受益人是否同股东,0-否,1-是
        cardType: IDENTITY_CARD_TYPE.personal.id // 受益人证件类型 默认身份证

      })

      this.openAccount.beneficiaryList.push({
        beneficiaryName: '', // 名称
        beneficiaryIdentityCard: '', // 受益人身份证号码
        beneficiaryIdentityCardFromDate: '', // 开始日期
        beneficiaryIdentityCardEndDate: '', // 结束日期
        beneficiaryIdentityCardLongTerm: false, // 否长期有效 0-否,1-是
        beneficiaryPhone: '', // 手机号码
        beneficiaryHouseholdAddress: '', // 地址
        beneficiaryIdentityCardFrontUrl: '', // 身份证正面
        beneficiaryIdentityCardBackUrl: '', // 身份证背面
        beneficiaryEqualLegal: false, // 受益人是否同法人,0-否,1-是
        beneficiaryEqualStockholder: false, // 受益人是否同股东,0-否,1-是
        beneficiaryCardType: IDENTITY_CARD_TYPE.personal.id, // 受益人证件类型 默认身份这个
      })
      this.updateOpenAccount()

      // 滚动最底部
      this.$nextTick().then(() => {
        let dom = this.$refs.box
        dom.scrollTop = dom.scrollHeight
      })
    },

    //  删除
    del(index) {
      // console.log('删除')
      this.$confirm('删除后该受益人信息将不存在，必要时可重新添加。', '确认删除受益人？', {
        dangerouslyUseHTMLString: true,
        type: 'warning',
        confirmButtonText: '确认',
      })
        .then(() => {
          this.beneficiaryList.splice(index, 1)
          this.openAccount.beneficiaryList.splice(index, 1)
          if (this.beneficiaryList.length === 1) {
            this.openAccount.beneficiaryList[0].isVisible = true
            this.beneficiaryList[0].isVisible = true
          }
          this.updateOpenAccount()
        })
    },

    // 更新本地缓存开户
    updateOpenAccount() {
      user.setOpenAccount(this.openAccount)
    },

    // 上一步
    prev() {
      // 初始化风控流水号
      setSerialNos({ no: '', step: this.legalStep, isNext: false })
      this.next({
        step: 1,
        type: 'prev',
        legalStep: this.legalStep - 1
      })
    },

    // 下一步
    async submitForm() {
      try {
        for (let index = 0; index < this.beneficiaryList.length; index++) {
          await this.$refs[`personFormRef${index}`][0].validate()
        }
      } catch (error) {
        // console.error(error)
        return this.$message('存在未填写信息或填写有误，请检查并完善表单内容')
      }

      // 判断受益人证件号，手机号是否重复
      if (this.beneficiaryList.length > 1) {
        let tempIdentityCard = []
        let tempPhone = []
        for (let index = 0; index < this.beneficiaryList.length; index++) {
          if (tempIdentityCard.includes(this.beneficiaryList[index].identityCard)) {
            return this.$message('受益人证件号重复')
          } else {
            tempIdentityCard.push(this.beneficiaryList[index].identityCard)
          }

          if (tempPhone.includes(this.beneficiaryList[index].phone)) {
            return this.$message('受益人手机号码重复')
          } else {
            tempPhone.push(this.beneficiaryList[index].phone)
          }
        }
      }
      // 风控校验
      const list = []
      this.beneficiaryList.length && this.beneficiaryList.forEach(item => {
        let obj = {
          beneficiaryName: item.name,
          beneficiaryIdentityCardEndDate: item.identityCardEndDate,
          beneficiaryIdentityCard: item.identityCard
        }
        list.push(obj)
      })

      const riskData = await openAccountApi.checkBeneficiaryInfo({
        companyCreditCode: this.openAccount.companyCreditCode,
        beneficiaryInfoList: list
      })
      if (riskData.result === 1) return riskManagementTips(riskData)
      setSerialNos({ no: riskData.serialNo, step: this.legalStep, isNext: true })

      this.upcheck()
    },

    /**
     * 日期转换统一处理
     * @param {string} longTerm 是否长期的key
     * @param {string} formDate 开始日期的key
     * @param {string} endDate 结束日期的key
     */
    timeTransformation(longTerm, formDate, endDate) {
      // 是否长期有效,0-否,1-是，非空字段
      this.ruleForm[longTerm] = +this.ruleForm[longTerm]
      // 开始日期，非空字段
      this.ruleForm[formDate] = formatTime(this.ruleForm[formDate], 'YYYY-MM-DD')
      // 结束日期，非空字段
      if (this.ruleForm[endDate]) {
        this.ruleForm[endDate] = formatTime(this.ruleForm[endDate], 'YYYY-MM-DD')
      }
      // 结束日期为2099-12-31 则变为长期
      if (this.ruleForm[endDate] === '2099-12-31') {
        this.ruleForm[longTerm] = 1
      }
    },

    // 检验通过
    async upcheck() {
      // 开通失败的支付渠道重新实名, 就不用下一步，直接提交
      if (this.openGlobalInfo.realNameAuthType === REAL_NAME_AUTH_TYPE.FAIL) {
        // 获取本地缓存开户
        this.openAccount = user.getOpenAccount()
        this.ruleForm = Object.assign(this.ruleForm, this.openAccount)

        // 营业执执照日期转换统一处理
        this.timeTransformation('businessLicenseLongTerm', 'businessLicenseFromDate', 'businessLicenseEndDate')

        // 法人身份证日期转换统一处理
        this.timeTransformation('legalIdentityCardLongTerm', 'legalIdentityCardFromDate', 'legalIdentityCardEndDate')

        // 经办人身份证日期转换统一处理
        this.timeTransformation('agentIdentityCardLongTerm', 'agentIdentityCardFromDate', 'agentIdentityCardEndDate')

        // 大股东身份证日期转换统一处理
        this.timeTransformation('stockholderIdentityCardLongTerm', 'stockholderIdentityCardFromDate', 'stockholderIdentityCardEndDate')

        // 企业成立日期
        this.ruleForm.establishDate = formatTime(this.ruleForm.establishDate, 'YYYY-MM-DD')

        // 经办人是否同法人,0-否,1-是
        this.ruleForm.agentEqualLegal = +this.ruleForm.agentEqualLegal
        // 大股东是否同法人,0-否,1-是
        this.ruleForm.stockholderEqualLegal = +this.ruleForm.stockholderEqualLegal
        // 受益人是否同法人,0-否,1-是
        this.ruleForm.beneficiaryEqualLegal = +this.ruleForm.beneficiaryEqualLegal

        // 受益人
        this.ruleForm.beneficiaryList.map(item => {
          // 受益人是否同法人,0-否,1-是
          item.beneficiaryEqualLegal = +item.beneficiaryEqualLegal
          // 受益人是否同股东,0-否,1-是
          item.beneficiaryEqualStockholder = +item.beneficiaryEqualStockholder
          // 受益人身份证是否长期有效,0-否,1-是，非空字段
          item.beneficiaryIdentityCardLongTerm = +item.beneficiaryIdentityCardLongTerm
          // 受益人身份证开始日期，非空字段
          item.beneficiaryIdentityCardFromDate && (item.beneficiaryIdentityCardFromDate = formatTime(item.beneficiaryIdentityCardFromDate, 'YYYY-MM-DD'))
          // 受益人身份证结束日期，非空字段
          item.beneficiaryIdentityCardEndDate && (item.beneficiaryIdentityCardEndDate = formatTime(item.beneficiaryIdentityCardEndDate, 'YYYY-MM-DD'))
          // 受益人身份证结束日期为2099-12-31 则变为长期
          if (item.beneficiaryIdentityCardEndDate === '2099-12-31') {
            item.beneficiaryIdentityCardLongTerm = 1
          }
          return item
        })

        // console.log('this.corpOpenInfoUpdate', this.openGlobalInfo?.corpOpenInfoUpdate)

        if (this.ruleForm.stockholderCardType === STOCKHOLDER_CARD_TYPE.enterprise.id) { // 大股东类型是企业
          this.ruleForm.stockholderIdentityCardFrontUrl = this.ruleForm.stockholderBusinessLicenseUrl // 大股东企业营业执照
        } else {
          this.ruleForm.stockholderCardType = STOCKHOLDER_CARD_TYPE.personal.id // 大股东类型设置为个人
        }

        // 实际控制人
        if (this.ruleForm.actualController) {
        // 证件是否长期有效，0-否，1-是，非空字段
          this.ruleForm.actualController.cardLongTerm = +this.ruleForm.actualController.cardLongTerm
          // 实际控制人开始日期，非空字段
          this.ruleForm.actualController.cardValidityBegin = formatTime(this.ruleForm.actualController.cardValidityBegin, 'YYYY-MM-DD')
          // 实际控制人结束日期，非空字段
          if (this.ruleForm.actualController.cardValidityEnd) {
            this.ruleForm.actualController.cardValidityEnd = formatTime(this.ruleForm.actualController.cardValidityEnd, 'YYYY-MM-DD')
          }
          // 实际控制人结束日期为2099-12-31 则变为长期
          if (this.ruleForm.actualController.beneficiaryIdentityCardEndDate === '2099-12-31') {
            this.ruleForm.actualController.beneficiaryEqualLegal = 1
          }

          // 是否同法人，0-否，1-是
          this.ruleForm.actualController.controllerEqualLegal = +this.ruleForm.actualController.controllerEqualLegal
          // 是否同股东，0-否，1-是
          this.ruleForm.actualController.controllerEqualStockholder = +this.ruleForm.actualController.controllerEqualStockholder
        }
        this.ruleForm.realNameAuthType = this.openGlobalInfo.realNameAuthType
        if (this.openGlobalInfo?.corpOpenInfoUpdate) { // 更新商户开户信息
          // 渠道开户流程 更新商户须传入当前操作的支付渠道类型
          !this.isRealNameStep && this.openGlobalInfo.reAuthPaymentChannel && (this.ruleForm.reAuthPaymentChannel = this.openGlobalInfo.reAuthPaymentChannel)
          await openAccountApi.corpOpenInfoUpdate(this.ruleForm)
        } else {
          this.openGlobalInfo.reAuthPaymentChannel && (this.ruleForm.reAuthPaymentChannel = this.openGlobalInfo.reAuthPaymentChannel)
          await openAccountApi.postCorpOpenInfoSave(this.ruleForm)
        }
        user.removeOpenAccount()
        // 重新获取企业信息
        await this.$store.dispatch('user/getCorpInfo')

        // 刷新用户中心-电子账户状态
        this.$event.emit(REFRESH_ACCOUNT)
        this.next({
          step: 2
        })
      } else {
        this.next({
          step: 1,
          type: 'next',
          legalStep: this.legalStep + 1
        })
      }
    },
  },
}
</script>
