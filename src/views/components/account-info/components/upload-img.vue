<!-- 上传开户图片 -->
<style lang="scss" scoped>
@import "../account.scss";

.open-account-upload-img {
  .main {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    padding: 16px;
    background: $color-FFFFFF;

    .left {
      margin-right: 12px;
      border-right: 1px solid $color-D9D9D9;
      padding-right: 12px;
      width: 300px;
      background-position: left bottom;
      background-repeat: no-repeat;
      background-size: 255px 237px;
      background-color: $color-FFFFFF;
      letter-spacing: .5px;

      .title {
        margin-bottom: 10px;
        font-size: 18px;
      }
    }

    .right {
      overflow: auto;
      flex: 1;
      height: 517px;
      background-color: $color-FFFFFF;

      .img-upload-box {
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .subtitle {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 4px;
          color: $color-text-secondary;
          line-height: 24px;

          .g-required {
            margin-bottom: 0;
          }

          .example-underline {
            @include example-underline;
          }

          .example {
            .el-image {
              display: block;
            }

            ::v-deep {
              .el-link {
                font-weight: 500;

                &::after {
                  bottom: 4px;
                }
              }

              .el-link--inner {
                color: $font-color;
                cursor: pointer;

                &:hover {
                  color: $font-color-hover;
                }
              }

              .el-link.el-link--primary::after {
                border-color: $font-color;
              }
            }
          }
        }

        .img-box {
          height: 100px;

          .upload-txt {
            font-size: 14px;
            font-weight: 400;
            color: $--color-primary;
          }

          .drag {
            font-size: 14px;
            font-weight: 400;
            color: $color-text-regular;
          }

          .describe {
            padding-bottom: 4px;
            font-size: 12px;
          }
        }
      }
    }
  }
}

.fix {
  display: flex;
}

.width50 {
  width: 50%;
}

.pad-left8 {
  padding-left: 8px;
}

.svg-icon {
  display: none !important;
}
</style>

<style lang="scss">
.open-account-upload-img {
  .img-upload-icon-plus {
    display: none !important;
  }

  .img-upload-empty {
    height: 102px;
  }
}
</style>

<template>
  <section class="open-account-upload-img">
    <div class="main">
      <div class="left">
        <h3 class="title">账户安全有保障</h3>
        <div class="paragraph">
          <label class="num">1</label>为了后续<span class="word-mark">安全顺利</span>的交易，您需完成账户实名认证并开通电子交易账户
        </div>
        <div class="paragraph">
          <label class="num">2</label>买卖双方都需完成账户实名认证并开通电子交易账户，才可以获得银行提供的资金监管服务，<span class="word-mark">确保</span>交易过程中的<span class="word-mark">资金安全</span>
        </div>
        <div class="paragraph">
          <label class="num">3</label>开通的电子交易账户，只能由<span class="word-mark">同名</span>企业网银进行资金转入转出，由您<span class="word-mark">自主操作</span>，没有被第三方挪用资金的风险
        </div>
        <div class="paragraph">
          <label class="num">4</label>电子交易账户由<span class="word-mark">银行监管</span>，只有账户的持有者才可以发起交易，交易过程中资金的流转也仅在银行系统内部进行，资金安全有保障
        </div>
        <div class="paragraph">
          <label class="num">5</label>银行需企业的<span class="word-mark">真实信息</span>来开立对应的电子交易账户，且电子交易账户开通，须上传法定代表人视频影像
        </div>
      </div>
      <div class="right">
        <!-- <div class="title-left-border">开户所需文件</div> -->
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          class="form"
          :inline-message="true"
        >
          <div v-if="isRealNameStep" class="img-upload-box">
            <div class="subtitle">
              <span class="g-required">营业执照原件或加盖公章的复印件扫描件</span>
              <label class="example">
                <el-image
                  :preview-src-list="businessLicenseUrlExampleUrlList"
                  img-text="示例"
                />
              </label>
            </div>
            <div class="img-box">
              <el-form-item label="" prop="businessLicenseUrl">
                <ImgUpload
                  :value="ruleForm.businessLicenseUrl"
                  :size-limit="2"
                  :dir="OSS_DIR.BUSINESS_LICENSE"
                  @input="postOcrBusinessLicense"
                >
                  <div slot="empty">
                    <div class="describe"><span class="upload-txt">点击上传</span><span class="drag-txt">/拖拽到此区域</span></div>
                    <div class="describe">证件原件或加盖公章的复印件扫描件</div>
                    <div class="describe">（支持jpg/jpeg/png格式，不超过2M）</div>
                  </div>
                </ImgUpload>
              </el-form-item>
            </div>
          </div>
          <div v-if="isRealNameStep" class="fix">
            <div class="img-upload-box width50">
              <div class="subtitle">
                <span class="g-required">法人身份证人像面</span>
                <label class="example">
                  <el-image
                    :preview-src-list="legalIdentityFrontExampleUrlList"
                    img-text="示例"
                  />
                </label>
              </div>
              <div class="img-box">
                <el-form-item label="" prop="legalIdentityCardFrontUrl">
                  <ImgUpload
                    :value="ruleForm.legalIdentityCardFrontUrl"
                    :size-limit="2"
                    :dir="OSS_DIR.ID_CARD"
                    @input="postOcrIdentityCardFront"
                  >
                    <div slot="empty">
                      <div class="describe"><span class="upload-txt">点击上传</span><span class="drag-txt">/拖拽到此区域</span></div>
                      <div class="describe">证件原件或加盖公章的复印件扫描件</div>
                      <div class="describe">（支持jpg/jpeg/png格式，不超过2M）</div>
                    </div>
                  </ImgUpload>
                </el-form-item>
              </div>
            </div>
            <div class="img-upload-box width50 pad-left8">
              <div class="subtitle">
                <span class="g-required">法人身份证国徽面</span>
                <label class="example">
                  <el-image
                    :preview-src-list="legalIdentityBackExampleUrlList"
                    img-text="示例"
                  />
                </label>
              </div>
              <div class="img-box">
                <el-form-item label="" prop="legalIdentityCardBackUrl">
                  <ImgUpload
                    :value="ruleForm.legalIdentityCardBackUrl"
                    :size-limit="2"
                    :dir="OSS_DIR.ID_CARD"
                    @input="postOcrIdentityCardBack"
                  >
                    <div slot="empty">
                      <div class="describe"><span class="upload-txt">点击上传</span><span class="drag-txt">/拖拽到此区域</span></div>
                      <div class="describe">证件原件或加盖公章的复印件扫描件</div>
                      <div class="describe">（支持jpg/jpeg/png格式，不超过2M）</div>
                    </div>
                  </ImgUpload>
                </el-form-item>
              </div>
            </div>
          </div>
          <div class="fix">
            <div class="img-upload-box width50">
              <div class="subtitle">
                <span>法人视频影像</span>
                <label class="example-underline" @click="lookVideoMessage">
                  示例
                </label>
              </div>
              <div class="img-box">
                <el-form-item label="" prop="legalVideoUrl">
                  <ImgUpload
                    v-model="ruleForm.legalVideoUrl"
                    accept="video/mp4"
                    :is-video="true"
                    :size-limit="20"
                    :dir="OSS_DIR.ID_CARD"
                  >
                    <div slot="empty">
                      <div class="describe"><span class="upload-txt">点击上传</span><span class="drag-txt">/拖拽到此区域</span></div>
                      <!-- <div class="describe">点击或拖拽图片至此上传法人视频影像</div> -->
                      <div class="describe">（支持mp4格式，不超过 20 M）</div>
                    </div>
                  </ImgUpload>
                </el-form-item>
              </div>
            </div>
            <div class="img-upload-box width50 pad-left8">
              <div class="subtitle">
                <span>其他文件(具体补充材料请与客户经理确认)</span>
              </div>
              <div class="img-box">
                <el-form-item label="" prop="otherFileUrl">
                  <ImgUpload
                    v-model="ruleForm.otherFileUrl"
                    accept="application/x-rar-compressed,application/x-rar,.rar,application/zip,application/x-zip-compressed,application/x-zip"
                    :is-zip="true"
                    :is-video="false"
                    :size-limit="20"
                    :dir="OSS_DIR.ID_CARD"
                  >
                    <div slot="empty">
                      <div class="describe"><span class="upload-txt">点击上传</span><span class="drag-txt">/拖拽到此区域</span></div>
                      <!-- <div class="describe">点击或拖拽压缩包至此上传文件</div> -->
                      <div class="describe">（支持zip/rar格式，不超过 20 M）</div>
                    </div>
                  </ImgUpload>
                </el-form-item>
              </div>
            </div>
          </div>
        </el-form>
      </div>
    </div>
    <div class="footer">
      <div class="btn-box">
        <el-button
          size="large"
          type="primary"
          :loading="loading"
          @click="submitForm('ruleForm')"
        >
          下一步
        </el-button>
      </div>
    </div>

    <!-- 法人视频影像话术示例弹窗 -->
    <VideoMessage ref="videoMessageRef" />
  </section>
</template>

<script>
import ImgUpload from '@/views/components/common/img-upload/img-upload.vue' // 图片上传组件
import VideoMessage from './video-message.vue' // 法人视频影像话术示例弹窗
import openAccountApi from '@/apis/open-account' // 开户接口
import { OSS_DIR } from '@/constant' // 上传文件夹
import user from '@/utils/user' // 用户对象
// import { riskManagementTips } from '../options' // 风控校验提示

const businessLicenseUrlExampleUrl = 'https://oss.chengjie.red/web/imgs/open-account/business-license.jpeg' // 营业执照示例url
const legalIdentityFrontExampleUrl = 'https://oss.chengjie.red/web/imgs/open-account/legal-identity-front.png' // 法人身份证正面示例url
const legalIdentityBackExampleUrl = 'https://oss.chengjie.red/web/imgs/open-account/legal-identity-back.png' // 法人身份证背面示例url
export default {
  name: 'open-account-upload-img',
  components: {
    ImgUpload, // 图片上传组件
    VideoMessage, // 法人视频影像话术示例弹窗
  },

  props: {
    // 下一步
    next: {
      type: Function,
      require: true
    },
    isRealNameStep: { // 是否是实名认证流程
      type: Number,
      default: 1
    },
  },

  data() {
    return {
      OSS_DIR, // 上传文件夹
      openAccount: null, // 本地缓存开户信息
      businessLicenseUrlExampleUrlList: [businessLicenseUrlExampleUrl], // 营业执照示例url
      legalIdentityFrontExampleUrlList: [legalIdentityFrontExampleUrl], // 法人身份证正面示例url
      legalIdentityBackExampleUrlList: [legalIdentityBackExampleUrl], // 法人身份证背面示例url
      ruleForm: {
        businessLicenseUrl: '', // 营业执照url，非空字段
        legalIdentityCardFrontUrl: '', // 法人身份证正面url，非空字段
        legalIdentityCardBackUrl: '', // 法人身份证背面url，非空字段
        legalVideoUrl: '', // 法人视频影像
        otherFileUrl: '', // 其他文件
      },
      rules: {
        businessLicenseUrl: [{ required: true, message: '请上传营业执照', trigger: 'change' }],
        legalIdentityCardFrontUrl: [{ required: true, message: '请上传法人身份证人像面', trigger: 'change' }],
        legalIdentityCardBackUrl: [{ required: true, message: '请上传法人身份证国徽面', trigger: 'change' }],
      },
      loading: false
    }
  },

  watch: {
    // 监听本地缓存开户信息对象变化
    ruleForm: {
      deep: true,
      handler(val) {
        user.setOpenAccount(val)
      }
    },
  },

  created() {
    // 获取本地缓存开户
    this.openAccount = user.getOpenAccount()
    this.ruleForm = Object.assign(this.ruleForm, this.openAccount)
  },

  methods: {
    // 识别不出来处理
    identifyEmpty(obj) {
      for (const key in obj) {
        if (Object.hasOwnProperty.call(obj, key)) {
          if (obj[key] === '--' || obj[key] === '无') {
            obj[key] = ''
          }
        }
      }
    },

    // 营业执照ocr
    async postOcrBusinessLicense(url) {
      this.ruleForm.businessLicenseUrl = url
      if (!url) return
      this.loading = true
      const res = await openAccountApi.postOcrBusinessLicense({
        businessLicenseUrl: url
      })

      // 识别不出来处理
      this.identifyEmpty(res)

      if (!res.companyName && !res.companyCreditCode && !res.businessLicenseLegalName) {
        this.loading = false
        this.ruleForm.businessLicenseUrl = ''
        return this.$message({
          message: '检测到您上传的不是营业执照,或者图片不清晰，识别不出来，请重新上传',
        })
      }

      this.ruleForm.companyName = res.companyName // 企业名称
      this.ruleForm.companyCreditCode = res.companyCreditCode // 统一社会信用代码
      this.ruleForm.businessLicenseLegalName = res.businessLicenseLegalName // 企业法人代表名称
      this.ruleForm.establishDate = res.establishDate || '' // 成立日期，非空字段
      this.ruleForm.businessLicenseFromDate = res.businessLicenseFromDate || '' // 营业执照开始日期
      this.ruleForm.businessLicenseEndDate = res.businessLicenseEndDate || '' // 营业执照结束日期
      this.ruleForm.businessLicenseLongTerm = !!res.businessLicenseLongTerm // 营业执执照是否长期有效,0-否,1-是，非空字段
      this.ruleForm.companyAddress = res.companyAddress // 企业地址
      this.ruleForm.businessScope = res.companyBusinessScope // 公司经营范围
      this.ruleForm.registerCapital = res.companyRegisterCapital // 企业注册资本(万)

      user.setOpenAccount(this.ruleForm)
      this.loading = false
    },

    // 身份证正面ocr
    async postOcrIdentityCardFront(url) {
      this.ruleForm.legalIdentityCardFrontUrl = url
      if (!url) return
      this.loading = true
      const res = await openAccountApi.postOcrIdentityCardFront({
        identityCardFrontUrl: url,
      })
      this.ruleForm.legalName = res.name // 法人姓名，非空字段
      this.ruleForm.legalIdentityCard = res.identityCard // 法人身份证号码，非空字段
      this.ruleForm.legalHouseholdAddress = res.householdAddress // 法人户籍地址，非空字段
      this.ruleForm.legalSex = res.sex // 法人性别,0-未知,1-男，2-女，非空字段
      user.setOpenAccount(this.ruleForm)
      this.loading = false
    },

    // 身份证背面ocr
    async postOcrIdentityCardBack(url) {
      this.ruleForm.legalIdentityCardBackUrl = url
      if (!url) return
      this.loading = true
      const res = await openAccountApi.postOcrIdentityCardBack({
        identityCardBackUrl: url
      })

      this.ruleForm.legalIdentityCardFromDate = res.identityCardFromDate || '' // 法人身份证开始日期，非空字段
      this.ruleForm.legalIdentityCardEndDate = res.identityCardEndDate || '' // 法人身份证结束日期，非空字段
      this.ruleForm.legalIdentityCardLongTerm = !!res.identityCardLongTerm // 法人身份证是否长期有效,0-否,1-是，非空字段
      this.ruleForm.legalIdentityCardIssuingAuthority = res.identityCardIssuingAuthority // 法人身份证签发机关，非空字段
      user.setOpenAccount(this.ruleForm)
      this.loading = false
    },

    // 点击法人视频影像话术示例
    lookVideoMessage() {
      this.$refs.videoMessageRef.init()
    },

    // 下一步
    submitForm(formName) {
      // 风控校验提示
      // let no = '123123'
      // let errData = '企业名称是否正确,企业统一社会信用代码是否正确,企业名称是否正确,企业统一社会信用代码是否正确'
      // if (errData) return riskManagementTips(no, errData)
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.next({
            step: 1
          })
        } else {
          return false
        }
      })
    },
  }
}
</script>
