<!-- 法人视频影像话术示例弹窗 -->
<style lang="scss" scoped>
::v-deep .video-message-dialog {
  .content {
    padding: 12px;
    background-color: $color-FFFFFF;

    >div {
      margin-bottom: 12px;
      line-height: 22px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>

<template>
  <main>
    <el-dialog
      width="504px"
      title="法人视频影像话术示例"
      :visible.sync="dialogVisible"
      :append-to-body="true"
      :close-on-click-modal="false"
      custom-class="video-message-dialog"
    >
      <div class="content">
        <div class="text-bold">本人是***，是**********公司的法定代表人。</div>
        <div>
          <span class="text-bold">这是我的身份证正面，</span><span class="text-gray">（往镜头前推下身份证正面，需清楚看到身份证上的所有正面信息）</span>
        </div>
        <div>
          <span class="text-bold">这是我的身份证背面，</span><span class="text-gray">（往镜头前推下身份证背面，需清楚看到身份证上的所有背面信息）</span>
        </div>
        <div class="text-bold">
          我知晓本公司在相关平台上开展票据交易业务，清楚了解并知晓双方的权利和义务,同意本公司在吉林亿联银行股份有限公司，申请注册开立用于平台交易的相关账“簿(bù)”。
        </div>
      </div>
    </el-dialog>
  </main>
</template>

<script>
export default {
  name: 'preview-video-dialog',
  props: {
    visible: Boolean, // 显示弹窗
  },

  data() {
    return {
      dialogVisible: false, // 显示弹窗
    }
  },

  methods: {
    init() {
      this.dialogVisible = true
    },
  }
}
</script>
