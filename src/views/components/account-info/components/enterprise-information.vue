<!-- eslint-disable max-lines -->
<!-- 完善企业信息 -->
<style lang="scss" scoped>
@import "../account.scss";

.enterprise-information {
  ::v-deep {
    .el-checkbox {
      display: flex;
      align-items: center;
      margin-right: 26px;

      &:last-of-type {
        margin-right: 0;
      }
    }
  }

  .form {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    background-color: $color-FFFFFF;

    .left::after {
      position: absolute;
      top: 0;
      right: -12px;
      z-index: 1;
      width: 12px;
      height: 100%;
      background-color: $color-F2F2F2;
      content: "";
    }

    .right::after {
      position: absolute;
      top: 0;
      left: -12px;
      z-index: 1;
      width: 12px;
      height: 100%;
      background-color: $color-F2F2F2;
      content: "";
    }

    .face-recognition {
      font-size: 16px;

      .svg-icon {
        font-size: 16px;
      }
    }
  }

  .checkbox-box {
    margin-bottom: 16px;
  }

  .tip {
    margin: 5px 0 0;
    color: $color-text-secondary;
  }
}

.refer-box {
  display: flex;
  justify-content: space-between;

  >div {
    flex: 1;
    display: flex;
    justify-content: space-between;

    &:first-child {
      margin-right: 12px;
    }

    .link {
      @include example-underline;
    }
  }
}

.icon-question {
  margin-left: 4px;
}

.flex-start {
  display: flex;
}
</style>

<template>
  <section class="enterprise-information">
    <div class="enterprise-info-box">
      <div class="left">
        <Guide :legal-step="legalStep" />
      </div>
      <div class="right">
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          class="form"
          :inline-message="true"
        >
          <div style="width: 100%;">
            <div class="multiseriate">
              <el-form-item label="企业名称" prop="companyName" :error="companyNameError">
                <el-input v-model="ruleForm.companyName" :disabled="!isRealNameStep" placeholder="请输入企业名称" />
              </el-form-item>
              <el-form-item label="统一社会信用代码" prop="companyCreditCode" :error="companyCreditCodeError">
                <el-input
                  v-model="ruleForm.companyCreditCode"
                  :disabled="!isRealNameStep"
                  placeholder="请输入统一社会信用代码"
                  maxlength="18"
                />
              </el-form-item>
            </div>

            <div class="multiseriate">
              <el-form-item label="所在行业" prop="companyIndustry">
                <el-select
                  v-model="ruleForm.companyIndustry"
                  :disabled="!isRealNameStep"
                  filterable
                  placeholder="请选择所在行业"
                >
                  <el-option
                    v-for="item in companyIndustryList"
                    :key="item.value"
                    :label="item.industryName"
                    :value="item.industryCode"
                  />
                </el-select>
              </el-form-item>
              <el-form-item prop="establishDate" label="成立日期">
                <el-date-picker
                  v-model="ruleForm.establishDate"
                  type="date"
                  placeholder="选择日期"
                />
              </el-form-item>
            </div>

            <div class="g-required">
              营业执照有效期
              <el-checkbox v-model="ruleForm.businessLicenseLongTerm" label="结束日期：长期" name="type" />
            </div>
            <div class="multiseriate">
              <el-form-item prop="businessLicenseFromDate">
                <el-date-picker
                  v-model="ruleForm.businessLicenseFromDate"
                  type="date"
                  placeholder="开始日期"
                  value-format="yyyy-MM-dd"
                  @change="businessLicenseFromDateChange"
                />
              </el-form-item>
              <el-form-item prop="businessLicenseEndDate">
                <el-date-picker
                  v-model="ruleForm.businessLicenseEndDate"
                  value-format="yyyy-MM-dd"
                  :disabled="!!ruleForm.businessLicenseLongTerm"
                  type="date"
                  placeholder="结束日期"
                />
              </el-form-item>
            </div>

            <!--
              <div class="multiseriate">
              <el-form-item label="企业控股类型" prop="holdingType">
              <el-select v-model="ruleForm.holdingType" placeholder="请选择企业控股类型">
              <el-option
              v-for="item in holdingTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              />
              </el-select>
              </el-form-item>
              <div class="mar-left8">
              <div class="refer-box">
              <div class="g-required">
              收税机构类型
              <a
              class="link"
              :href="OSS_FILES_URL.REFERENCE_TABLE"
              target="_blank"
              rel="noopener noreferrer"
              >参考表</a>
              </div>
              </div>
              <el-form-item prop="taxOfficeType">
              <el-select v-model="ruleForm.taxOfficeType" placeholder="请选择收税机构类型">
              <el-option
              v-for="item in taxOfficeTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              />
              </el-select>
              </el-form-item>
              </div>
              <div class="mar-left8">
              <div class="refer-box">
              <div class="g-required">
              企业规模类型
              <a
              class="link"
              :href="OSS_FILES_URL.REFERENCE_TABLE"
              target="_blank"
              rel="noopener noreferrer"
              >参考表</a>
              </div>
              </div>
              <el-form-item prop="scaleType">
              <el-select v-model="ruleForm.scaleType" placeholder="请选择企业规模类型">
              <el-option
              v-for="item in scaleTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              />
              </el-select>
              </el-form-item>
              </div>
              </div>
            -->

            <el-form-item label="经营省市区" prop="provinceCityDistricCodes">
              <el-cascader
                ref="cascaderCity"
                v-model="ruleForm.provinceCityDistricCodes"
                :options="region"
                placeholder="请选择经营省市区"
                separator=""
                filterable
                @change="handleChangeRegion"
              />
            </el-form-item>

            <!--
              <el-form-item label="企业地址" prop="companyAddress">
              <el-input v-model="ruleForm.companyAddress" placeholder="请输入企业地址" />
              </el-form-item>
            -->
            <div class="multiseriate">
              <el-form-item label="注册资本" prop="registerCapital">
                <el-input
                  v-model="ruleForm.registerCapital"
                  placeholder="请输入注册资本"
                  class="text-right"
                  type="number"
                  :number-format="registerCapitalFormat"
                >
                  <template slot="append">万</template>
                </el-input>
              </el-form-item>
              <el-form-item label="登记机关（即营业执照的盖章机构）" prop="registerOffice">
                <el-input
                  v-model="ruleForm.registerOffice"
                  placeholder="请输入登记机关"
                />
              </el-form-item>
            </div>

            <div class="multiseriate">
              <el-form-item label="注册地址" prop="companyAddress">
                <el-input v-model="ruleForm.companyAddress" :disabled="!isRealNameStep" placeholder="请输入注册地址" />
              </el-form-item>
              <el-form-item label="基本户核准号" prop="bcpNo">
                <el-input
                  v-model="ruleForm.bcpNo"
                  oninput="value=value.replace(/\s+/g,'')"
                  placeholder="请输入企业开户许可证核准号或基本存款账户编号"
                />
              </el-form-item>
            </div>

            <el-form-item label="经营范围" prop="businessScope">
              <el-input
                v-model="ruleForm.businessScope"
                :disabled="!isRealNameStep"
                placeholder="请输入经营范围"
              />
            </el-form-item>
          </div>
        </el-form>
      </div>
    </div>

    <div class="footer">
      <div v-if="!isRealNameStep" class="agreement">
        <el-checkbox v-model="ruleForm.checked">我已阅读并同意</el-checkbox>
        <div>
          <!--
            <template v-if="isRealNameStep">
            <a
            class="link"
            :href="OSS_FILES_URL.EQIANBAO_SERVICE_PROTOCOL_URL"
            target="_blank"
            rel="noopener noreferrer"
            >《e签宝服务协议与数字证书申请协议》</a>
            <a
            class="link"
            :href="OSS_FILES_URL.EQIANBAO_PRIVACY_PROTOCOL_URL"
            target="_blank"
            rel="noopener noreferrer"
            >《e签宝隐私协议》</a>
            <a
            class="link"
            :href="OSS_FILES_URL.REAL_NAME_AUTHENTICATION_URL"
            target="_blank"
            rel="noopener noreferrer"
            >
            《实名认证服务协议》
            </a>
            </template>
          -->
          <a
            v-if="paymentChannel === PAYMENT_CHANNEL.ZHI_FU_YI_LIAN.id || paymentChannel === PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id"
            class="link"
            target="_blank"
            rel="noopener noreferrer"
            @click="showAgreement(OSS_FILES_URL.YILIAN_OPEN_PROTOCOL_URL)"
          >《智付E+虚户开通电子协议》</a>
          <a
            v-if="paymentChannel === PAYMENT_CHANNEL.ZHI_FU_HE_LI_BAO.id"
            class="link"
            target="_blank"
            rel="noopener noreferrer"
            @click="showAgreement(OSS_FILES_URL.HELIBAO_OPEN_PROTOCOL_URL)"
          >《智付合利宝虚户开通电子协议》</a>
          <!-- eslint-disable-next-line vue/html-self-closing -->
          <a
            v-if="paymentChannel === PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG.id"
            class="link"
            target="_blank"
            rel="noopener noreferrer"
            @click="showAgreement(OSS_FILES_URL.ZHONGBANG_OPEN_PROTOCOL_URL)"
          >《智付邦+E账通系统开户三方协议》</a>
        </div>
      </div>
      <div class="btn-box">
        <el-button
          class="at-once-btn"
          type="primary"
          size="large"
          border
          width="80px"
          @click="prev"
        >
          上一步
        </el-button>
        <el-button
          v-waiting="[openAccountApi.postQueryFaceResult, openAccountApi.getJudgeRepeatCorp]"
          class="at-once-btn"
          type="primary"
          size="large"
          width="80px"
          @click="submitForm('ruleForm')"
        >
          下一步
        </el-button>
      </div>
    </div>
  </section>
</template>

<script>
/* eslint-disable no-magic-numbers */

import region from '@/common/json/region-code.json' // 地址库
import { validateUnifiedSocialCreditCode } from '@/common/js/validator' // 验证规则
import openAccountApi from '@/apis/open-account' // 开户接口
import user from '@/utils/user' // 用户对象
import { dealTime } from '@/common/js/date' // 时间格式
import { OSS_FILES_URL } from '@/constants/oss-files-url'
import {
  OPEN_URL_IN_DEFAULT_BROWSER,
} from '@recognize/ipc-event-constant'
import {
  holdingTypeOptions, // 企业控股类型 选项
  taxOfficeTypeOptions, // 收税机构类型收税机构类型 选项
  scaleTypeOptions, // 企业规模类型 选项
  STOCKHOLDER_CARD_TYPE, // 大股东企业类型
  jdOpenTypeOptions, // 企业 业务类型
  riskManagementTips, // 风控校验提示
  setSerialNos, // 设置业务流水号
} from '../options'
import { REAL_NAME_AUTH_TYPE, PAYMENT_CHANNEL } from '@/constant'
import { APPLICATION_STATUS } from '@/constants/open-account' // 常量
import Guide from './enterprise-guide.vue' // 企业信息左侧导航指引

export default {
  name: 'enterprise-information',

  components: {
    Guide
  },

  inject: ['openGlobalInfo'], // 'getQxbCorpInfo' 'qxbRemoteValidate'
  props: {
    legalStep: Number,
    // 下一步
    next: {
      type: Function,
      require: true
    },
    isRealNameStep: { // 是否是实名认证流程
      type: Number,
      default: 1
    },
    paymentChannel: { // 渠道类型
      type: Number
    }
  },

  data() {
    // 自定义统一社会信用代码验证规则
    const checkCompanyCreditCode = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入统一社会信用代码'))
      }
      if (!validateUnifiedSocialCreditCode(value)) {
        return callback(new Error('统一社会信用代码式为18位'))
      }
      callback()
    }

    // 自定义营业执照结束日期检验规则
    const checkBusinessLicenseEndDate = (rule, value, callback) => {
      if (this.ruleForm.businessLicenseFromDate && value && dealTime(this.ruleForm.businessLicenseFromDate) > dealTime(value)) {
        callback(new Error('结束日期不能小于开始日期'))
      } else if (value || this.ruleForm.businessLicenseLongTerm) {
        callback()
      } else {
        callback(new Error('请选择结束日期'))
      }
    }

    // 基本户核准号校验规则
    const checkBcpNo = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入企业开户许可证核准号或基本存款账户编号'))
      }
      // 核准号首位为大写的字母且范围为“J、L、Z”以内，第2-14位数字，最小要传14位
      // 判断首字母
      const myRegExp = new RegExp('^[JLZ]')
      const initials = myRegExp.exec(value)
      // 判断第2位以后是不是纯数字
      const newValue = value.slice(1)
      const isNumber = /^\d+$/.test(newValue)
      if (!initials || value.length < 14 || !isNumber) {
        return callback(new Error('基本户核准号格式不正确'))
      }
      callback()
    }

    // 启信宝校验函数
    // const { qxbRemoteValidate } = this

    return {
      OSS_FILES_URL,
      openAccountApi,
      REAL_NAME_AUTH_TYPE, // 实名类型常量
      openAccount: null, // 本地缓存开户信息
      companyNameError: '', // 企业名称验证错误提示
      companyCreditCodeError: '', // 统一社会信用代码验证错误提示
      PAYMENT_CHANNEL, // 渠道
      // 表单验证规则
      ruleForm: {
        businessLicenseUrl: '', // 营业执照url，非空字段
        legalIdentityCardFrontUrl: '', // 法人身份证正面url，非空字段
        legalIdentityCardBackUrl: '', // 法人身份证背面url，非空字段
        legalVideoUrl: '', // 法人视频文件url，非空字段
        companyName: '', // 企业名称，非空字段
        companyIndustry: '', // 公司行业，非空字段
        companyCreditCode: '', // 统一社会信用代码，非空字段
        businessLicenseFromDate: '', // 营业执照开始日期，非空字段
        businessLicenseEndDate: '', // 营业执照结束日期，非空字段
        businessLicenseLongTerm: false, // 营业执执照是否长期有效,0-否,1-是，非空字段
        establishDate: '', // 成立日期，非空字段
        holdingType: '', // 企业控股类型
        taxOfficeType: '', // 收税机构类型
        scaleType: '', // 企业规模类型
        companyAddress: '', // 企业地址，非空字段
        provinceCityDistricCodes: '', // 省市区编码数组
        provinceName: '', // 公司所在省名称，非空字段
        provinceCode: '', // 公司所在省编码，非空字段
        cityName: '', // 公司所在市名称，非空字段
        cityCode: '', // 公司所在市编码，非空字段
        districtName: '', // 公司所在区县名称，非空字段
        districtCode: '', // 公司所在区县编码，非空字段
        businessScope: '', // 公司经营范围，非空字段
        registerOffice: '', // 登记机关
        jdOpenType: '', // 业务类型
        bcpNo: '', // 基本户核准号
        registerCapital: '', // 企业注册资本(万)
        legalName: '', // 法人姓名，非空字段
        legalIdentityCard: '', // 法人身份证号码，非空字段
        legalPhone: '', // 法人手机号码，非空字段
        legalIdentityCardFromDate: '', // 法人身份证开始日期，非空字段
        legalIdentityCardEndDate: '', // 法人身份证结束日期，非空字段
        legalIdentityCardLongTerm: false, // 法人身份证是否长期有效,0-否,1-是，非空字段
        legalEmail: '', // 法人邮箱，非空字段
        legalHouseholdAddress: '', // 法人户籍地址，非空字段
        legalSex: '', // 法人性别,0-未知,1-男，2-女，
        legalIdentityCardIssuingAuthority: '', // 法人身份证签发机关，
        legalIdentityCardType: '', // 法人证件类型 默认身份证类型101
        agentName: '', // 经办人姓名，非空字段
        agentIdentityCard: '', // 经办人身份证号码，非空字段
        agentIdentityCardFrontUrl: '', // 经办人身份证正面url，非空字段
        agentIdentityCardBackUrl: '', // 经办人身份证背面url，非空字段
        agentAuthUrl: '', // 经办人授权证明文件url
        agentIdentityCardFromDate: '', // 经办人身份证开始日期，非空字段
        agentIdentityCardEndDate: '', // 经办人身份证结束日期，非空字段
        agentIdentityCardLongTerm: false, // 经办人身份证是否长期有效,0-否,1-是，非空字段
        agentPhone: '', // 经办人手机号码，非空字段
        agentEmail: '', // 经办人电子邮箱，非空字段
        agentSex: '', // 经办人性别，非空字段
        agentHouseholdAddress: '', // 经办人户籍地址 非空字段
        agentIdentityCardIssuingAuthority: '', // 经办人签发机关 非空字段
        stockholderName: '', // 大股东名称，非空字段
        stockholderIdentityCard: '', // 大股东身份证号码，非空字段
        stockholderIdentityCardFrontUrl: '', // 大股东身份证正面url，非空字段
        stockholderIdentityCardBackUrl: '', // 大股东身份证背面url，非空字段
        stockholderIdentityCardFromDate: '', // 大股东身份证开始日期，非空字段
        stockholderIdentityCardEndDate: '', // 大股东身份证结束日期，非空字段
        stockholderIdentityCardLongTerm: false, // 大股东身份证是否长期有效,0-否,1-是，非空字段
        stockholderPhone: '', // 大股东手机号码，非空字段
        stockholderHouseholdAddress: '', // 大股东户籍地址，非空字段
        beneficiaryList: [
          {
            beneficiaryName: '', // 受益人名称，非空字段
            beneficiaryIdentityCard: '', // 受益人身份证号码，非空字段
            beneficiaryIdentityCardFrontUrl: '', // 受益人身份证正面url，非空字段
            beneficiaryIdentityCardBackUrl: '', // 受益人身份证背面url，非空字段
            beneficiaryIdentityCardFromDate: '', // 受益人身份证开始日期，非空字段
            beneficiaryIdentityCardEndDate: '', // 受益人身份证结束日期，非空字段
            beneficiaryIdentityCardLongTerm: false, // 受益人身份证是否长期有效,0-否,1-是，非空字段
            beneficiaryHouseholdAddress: '', // 受益人户籍地址，非空字段
            beneficiaryPhone: '', // 受益人手机号码，非空字段
            beneficiaryEqualLegal: false, // 受益人是否同法人，0-否,1-是
          }
        ],
        bankAccount: '', // 开户行账号，非空字段
        bankCnap: '', // 开户行银联编码
        bankName: '', // 开户银行名称，非空字段
        bankCode: '', // 开户行行号，非空字段
        bankBranchName: '', // 开户支行名称，非空字段
        bankBranchCode: '', // 开户行支行行号
        checked: false, // 是否勾选已读协议
        checkList: ['被授权人或经办人同法人', '大股东同法人', '受益人同法人'], // 是否同法人选项
        agentEqualLegal: false, // 经办人是否同法人,0-否,1-是
        beneficiaryEqualLegal: false, // 受益人是否同法人,0-否,1-是
        stockholderEqualLegal: false, // 大股东是否同法人,0-否,1-是
        applyVersion: '' // 开户申请版本号
      },
      rules: {
        companyName: [{ required: true, message: '请输入企业名称', trigger: ['blur', 'change'] }],
        companyIndustry: [{ required: true, message: '请选择所在行业', trigger: 'change' }],
        companyCreditCode: [{ required: true, validator: checkCompanyCreditCode, trigger: ['blur', 'change'] }],
        businessLicenseFromDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
        businessLicenseEndDate: [{ required: true, validator: checkBusinessLicenseEndDate, trigger: 'change' }],
        establishDate: [{ required: true, message: '请选择成立日期', trigger: 'change' }],
        // holdingType: [{ required: true, message: '请选择企业控股类型', trigger: 'change' }],
        // taxOfficeType: [{ required: true, message: '请选择收税机构类型', trigger: 'change' }],
        // scaleType: [{ required: true, message: '请选择企业规模类型', trigger: 'change' }],
        companyAddress: [{ required: true, message: '请输入企业地址', trigger: ['blur', 'change'] }],
        provinceCityDistricCodes: [{ required: true, message: '请选择经营省市区', trigger: 'change' }],
        registerCapital: [{ required: true, message: '请输入注册资本', trigger: ['blur', 'change'] }],
        businessScope: [{ required: true, message: '请输入经营范围', trigger: ['blur', 'change'] }],
        registerOffice: [{ required: true, message: '请输入登记机关', trigger: ['blur', 'change'] }],
        bcpNo: [{ required: true, validator: checkBcpNo, trigger: ['blur', 'change'] }],
        jdOpenType: [{ required: true, message: '请选择业务类型', trigger: 'change' }]
      },
      // 注册资本数字格式
      registerCapitalFormat: {
        decimal: true, // 是否支持小数
        negative: false, // 是否支持负数
        leadingZero: false, // 是否支持前导零，如 001
        maxDecimalLength: 6,
      },
      companyIndustryList: [], // 所在行业列表
      holdingTypeOptions, // 企业控股类型
      taxOfficeTypeOptions, // 收税机构类型
      scaleTypeOptions, // 企业规模类型
      jdOpenTypeOptions, // 企业业务类型
      region, // 地址json
      companyCreditCode: '', // 统一社会信用代码
      realNameAuthType: '', // 实名类型
    }
  },

  computed: {
    // 企业信息
    corpInfo() {
      return this.$store.state?.user?.corpInfo
    },

    // 统一社会信用代码 是否禁用
    companyCreditCodeDisable() {
      // newestCorpOpenInfoApplyStatus === 7 已通过，或者 newestCorpOpenInfoRealNameAuthType !== 0 不是首次实名
      if (this.corpInfo.newestCorpOpenInfoApplyStatus === APPLICATION_STATUS.HAVE_BEEN_THROUGH.id || (this.corpInfo.newestCorpOpenInfoRealNameAuthType && this.corpInfo.newestCorpOpenInfoRealNameAuthType !== 0)) {
        return true
      }
      return false
    }
  },

  watch: {
    // 监听本地缓存开户信息对象变化
    ruleForm: {
      deep: true,
      handler(val) {
        // 取消企业规模类型<其他规模>和<未知>选项的选中
        if (val.scaleType === 'CS00' || val.scaleType === 'CS09') {
          val.scaleType = ''
        }
        user.setOpenAccount(val)
      }
    },
    // 监听营业执执照是否长期有效
    'ruleForm.businessLicenseLongTerm': {
      handler(val) {
        if (val) {
          this.ruleForm.businessLicenseEndDate = '2099-12-31'
        } else {
          this.ruleForm.businessLicenseEndDate = ''
        }
        this.$refs.ruleForm.validateField('businessLicenseEndDate')
      }
    },
  },

  created() {
    // 获取本地缓存开户
    this.openAccount = user.getOpenAccount()
    if (this.openAccount) {
      // 营业执执照是否长期有效,0-否,1-是，非空字段
      this.openAccount.businessLicenseLongTerm = !!this.openAccount.businessLicenseLongTerm

      // 法人身份证是否长期有效,0-否,1-是，非空字段
      this.openAccount.legalIdentityCardLongTerm = !!this.openAccount.legalIdentityCardLongTerm

      // 经办人身份证是否长期有效,0-否,1-是，非空字段
      this.openAccount.agentIdentityCardLongTerm = !!this.openAccount.agentIdentityCardLongTerm

      // 大股东身份证是否长期有效,0-否,1-是，非空字段
      this.openAccount.stockholderIdentityCardLongTerm = !!this.openAccount.stockholderIdentityCardLongTerm
    }

    // 拼接省市区code
    if (this.openAccount.provinceCode && this.openAccount.cityCode && this.openAccount.districtCode) {
      this.openAccount.provinceCityDistricCodes = [this.openAccount.provinceCode, this.openAccount.cityCode, this.openAccount.districtCode]
    }

    this.ruleForm = Object.assign(this.ruleForm, this.openAccount)

    this.getCompanyIndustryList() // 查询企业行业列表

    // 开户资料页面，业务类型中，需要特殊处理，此时需要隐藏“银票秒贴”类型
    if (this.openGlobalInfo.realNameAuthType === 0) {
      this.jdOpenTypeOptions = this.jdOpenTypeOptions.filter(item => item.value !== 2)
    }
  },

  mounted() {
    if (this.corpInfo?.id) {
      this.getCorpOpenInfo()
    }
  },

  methods: {
    // 营业执照开始日期改变监听
    businessLicenseFromDateChange() {
      this.$refs.ruleForm.validateField('businessLicenseEndDate')
    },

    // 身份证开始日期改变监听
    legalIdentityCardFromDateChange() {
      this.$refs.ruleForm.validateField('legalIdentityCardEndDate')
    },

    // 查询企业行业列表
    async getCompanyIndustryList() {
      const res = await openAccountApi.getCompanyIndustryList()
      this.companyIndustryList = res
    },

    // 查询商户开户信息
    async getCorpOpenInfo() {
      let res = await openAccountApi.getCorpOpenInfo()
      if (res) {
        this.companyCreditCode = res.companyCreditCode
        // 实名类型，0-首次实名，1-收到实名预警通知后重新实名，2-实名失效后重新实名，3-开通失败的支付渠道进行重新实名
        this.realNameAuthType = res.realNameAuthType
      }
    },

    // 上一步
    prev() {
      // 初始化风控流水号
      setSerialNos({ no: '', step: this.legalStep, isNext: false })
      this.next({
        step: 0
      })
    },

    // 下一步
    submitForm(formName) {
      if (!this.ruleForm.companyName) return
      this.$refs[formName].validate(async valid => {
        if (valid) {
          if (!this.isRealNameStep && !this.ruleForm.checked) {
            return this.$message({
              message: '请阅读协议并勾选',
            })
          }

          // 更新信息
          if (this.openGlobalInfo.corpOpenInfoUpdate) {
            //  realNameAuthType 实名类型，0-首次实名，1-收到实名预警通知后重新实名，2-实名失效后重新实名，3-开通失败的支付渠道进行重新实名
            if (this.realNameAuthType !== 0 && this.ruleForm.companyCreditCode !== this.companyCreditCode) {
              this.$message.error('新企业的统一社会信用代码与旧企业不一致，请重新上传新营业执照')
              return
            }
          }

          const minTimeLength = 60 * 24 * 60 * 60 * 1000 // 最小时间距离，60天
          if (!this.ruleForm.businessLicenseLongTerm && dealTime(this.ruleForm.businessLicenseEndDate) - Date.now() < minTimeLength) {
            this.$message.error('距离营业执照结束日期不足60天，暂时无法开户，请先更新营业执照')
            return
          }

          // 判断是否同一个企业  @return 0-未重复，1-统一营业执照编码重复，2-企业名称重复，3-法人邮箱重复，4-统一营业执照编码不允许修改，5-企业名称不允许修改
          let res = await openAccountApi.getJudgeRepeatCorp({
            companyCreditCode: this.ruleForm.companyCreditCode,
            companyName: this.ruleForm.companyName,
            legalEmail: '', // 后端接口限制 企业基础信息校验法人邮箱传''
            isRealName: this.isRealNameStep
          })

          if (res && res.length > 0) {
            let message = ''
            for (let index = 0; index < res.length; index++) {
              const element = res[index]
              message += `${this.errorNameTip(element)}，`
            }
            if (message) {
              return this.$message.error(message.replace(/(，)$/, '。'))
            }
          }

          // 风控校验
          const riskData = await openAccountApi.checkBasicCorpInfo({
            companyName: this.ruleForm.companyName,
            companyCreditCode: this.ruleForm.companyCreditCode,
            businessLicenseEndDate: this.ruleForm.businessLicenseEndDate
            // businessLicenseEndDate: '2021-09-09'
          })
          if (riskData.result === 1) return riskManagementTips(riskData)
          // 风控校验通过设置风控流水号
          setSerialNos({ no: riskData.serialNo, step: this.legalStep, isNext: true })
          this.upcheck()
        } else {
          return this.$message('存在未填写信息或填写有误，请检查并完善表单内容')
        }
      })
    },

    // 检验通过
    upcheck() {
      if (this.ruleForm.agentEqualLegal) { // 经办人是否同法人,0-否,1-是
        this.ruleForm.agentName = this.ruleForm.legalName // 经办人姓名，非空字段
        this.ruleForm.agentIdentityCard = this.ruleForm.legalIdentityCard // 经办人身份证号码，非空字段
        this.ruleForm.agentIdentityCardFrontUrl = this.ruleForm.legalIdentityCardFrontUrl // 经办人身份证正面url，非空字段
        this.ruleForm.agentIdentityCardBackUrl = this.ruleForm.legalIdentityCardBackUrl // 经办人身份证背面url，非空字段
        this.ruleForm.agentIdentityCardFromDate = this.ruleForm.legalIdentityCardFromDate // 经办人身份证开始日期，非空字段
        this.ruleForm.agentIdentityCardEndDate = this.ruleForm.legalIdentityCardEndDate // 经办人身份证结束日期，非空字段
        this.ruleForm.agentIdentityCardLongTerm = this.ruleForm.legalIdentityCardLongTerm // 经办人身份证是否长期有效,0-否,1-是，非空字段
        this.ruleForm.agentPhone = this.ruleForm.legalPhone // 经办人手机号码，非空字段
        this.ruleForm.agentEmail = this.ruleForm.legalEmail // 经办人电子邮箱，非空字段
        this.ruleForm.agentSex = this.ruleForm.legalSex // 经办人性别 非空字段
        this.ruleForm.agentHouseholdAddress = this.ruleForm.legalHouseholdAddress // 经办人户籍地址，非空字段
        this.ruleForm.agentIdentityCardIssuingAuthority = this.ruleForm.legalIdentityCardIssuingAuthority // 经办人签发机关
      }

      if (this.ruleForm.stockholderEqualLegal) { // 大股东是否同法人,0-否,1-是
        this.ruleForm.stockholderCardType = STOCKHOLDER_CARD_TYPE.personal.id // 大股东证件类型 个人
        this.ruleForm.stockholderName = this.ruleForm.legalName // 大股东名称，非空字段
        this.ruleForm.stockholderIdentityCard = this.ruleForm.legalIdentityCard // 大股东身份证号码，非空字段
        this.ruleForm.stockholderIdentityCardFrontUrl = this.ruleForm.legalIdentityCardFrontUrl // 大股东身份证正面url，非空字段
        this.ruleForm.stockholderIdentityCardBackUrl = this.ruleForm.legalIdentityCardBackUrl // 大股东身份证背面url，非空字段
        this.ruleForm.stockholderIdentityCardFromDate = this.ruleForm.legalIdentityCardFromDate // 大股东身份证开始日期，非空字段
        this.ruleForm.stockholderIdentityCardEndDate = this.ruleForm.legalIdentityCardEndDate // 大股东身份证结束日期，非空字段
        this.ruleForm.stockholderIdentityCardLongTerm = this.ruleForm.legalIdentityCardLongTerm //  大股东身份证是否长期有效,0-否,1-是，非空字段段
        this.ruleForm.stockholderPhone = this.ruleForm.legalPhone // 大股东手机号码，非空字段
        this.ruleForm.stockholderHouseholdAddress = this.ruleForm.legalHouseholdAddress // 大股东户籍地址，非空字段
      }

      if (this.ruleForm.beneficiaryEqualLegal) { // 受益人是否同法人,0-否,1-是
        const beneficiaryTempObj = {
          beneficiaryName: this.ruleForm.legalName, // 受益人姓名，非空字段
          beneficiaryIdentityCard: this.ruleForm.legalIdentityCard, // 受益人身份证号码，非空字段
          beneficiaryIdentityCardFrontUrl: this.ruleForm.legalIdentityCardFrontUrl, // 受益人身份证正面url，非空字段
          beneficiaryIdentityCardBackUrl: this.ruleForm.legalIdentityCardBackUrl, // 受益人身份证背面url，非空字段
          beneficiaryIdentityCardFromDate: this.ruleForm.legalIdentityCardFromDate, // 受益人身份证开始日期，非空字段
          beneficiaryIdentityCardEndDate: this.ruleForm.legalIdentityCardEndDate, // 受益人身份证结束日期，非空字段
          beneficiaryIdentityCardLongTerm: this.ruleForm.legalIdentityCardLongTerm, // 受益人身份证是否长期有效,0-否,1-是，非空字段
          beneficiaryPhone: this.ruleForm.legalPhone, // 受益人手机号码，非空字段
          beneficiaryHouseholdAddress: this.ruleForm.legalHouseholdAddress, // 受益人户籍地址，非空字段
          beneficiaryEqualLegal: true // 受益人是否同法人,0-否,1-是
        }
        if (this.ruleForm.beneficiaryList && this.ruleForm.beneficiaryList.length) {
          this.ruleForm.beneficiaryList[0] = beneficiaryTempObj
        } else {
          this.ruleForm.beneficiaryList = [beneficiaryTempObj]
        }
      }

      // console.log(this.ruleForm)
      const openAccount = user.getOpenAccount()
      user.setOpenAccount(Object.assign(this.ruleForm, { verifyCode: openAccount.verifyCode, serialNos: openAccount.serialNos }))
      this.next({
        step: 1,
        type: 'next',
        legalStep: this.legalStep + 1
      })
    },

    // 错误名称提示
    errorNameTip(res) {
      let str = ''
      if (res === 1) {
        str = '该企业已被注册'
        this.companyCreditCodeError = str
      } else if (res === 2) {
        str = '企业名称已存在'
        this.companyNameError = str
      } else if (res === 4) {
        str = '统一社会信用代码不允许修改'
        this.companyCreditCodeError = str
      } else if (res === 5) {
        str = '企业名称不允许修改'
        this.companyNameError = str
      }
      return str
    },

    // 地址级联选择器改变回调
    handleChangeRegion(value) {
      this.ruleForm.provinceCode = value[0]
      this.ruleForm.cityCode = value[1]
      this.ruleForm.districtCode = value[2] || ''
      // 获取省市区的name
      this.$nextTick().then(() => {
        const labelArr = this.$refs.cascaderCity.getCheckedNodes()[0].pathLabels // 获取由 label 组成的数组
        this.ruleForm.provinceName = labelArr[0]
        this.ruleForm.cityName = labelArr[1]
        this.ruleForm.districtName = labelArr[2] || ''
      })
    },

    showAgreement(url) {
      if (this.$ipc) {
        this.$ipc.send(OPEN_URL_IN_DEFAULT_BROWSER, url)
      } else {
        window.open(url)
      }
    }
  }
}
</script>
