<!-- 绑定银行账户 -->
<style lang="scss" scoped>
@import "../account.scss";

.bind-bank-account {
  .main {
    // display: flex;
    // justify-content: space-between;
    margin-bottom: 12px;

    // padding: 16px;
    // background: $color-FFFFFF;

    .form-main {
      display: flex;
      justify-content: space-between;

      .mr-m {
        margin-right: 12px;
      }

      .form-left,
      .form-right {
        padding: 16px;
        width: 50%;
        background: #FFFFFF;
      }
    }

    .description {
      margin-top: 12px;
      padding: 16px;
      background-color: #EAF4FF; // $color-FFFFFF;

      .title {
        margin-bottom: 10px;
        font-size: 14px;
      }
    }

    ::v-deep {
      .img-upload-icon-plus {
        display: none !important;
      }

      .el-form-item__label {
        @include flex-vc;

        position: relative;
      }
    }

    .image-text {
      position: absolute;
      right: 0;
    }

    .img-upload-cls {
      margin-bottom: 8px;
      height: 80px !important;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>

<template>
  <section class="bind-bank-account">
    <div class="main">
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        class="form"
        :inline-message="true"
        :disabled="openGlobalInfo.realNameAuthType === 3"
      >
        <section class="form-main">
          <div class="form-left mr-m">
            <div class="title-left-border">银行账户信息</div>
            <el-form-item label="企业名称" prop="companyName">
              <el-input v-model="ruleForm.companyName" placeholder="请输入企业名称" disabled />
            </el-form-item>
            <el-form-item label="银行账号" prop="bankAccount">
              <el-input
                v-model="ruleForm.bankAccount"
                type="number"
                placeholder="请输入银行账号"
                :number-format="numberFormat"
              />
            </el-form-item>
            <el-form-item label="开户行行号" prop="bankBranchCode">
              <el-input
                v-model="ruleForm.bankBranchCode"
                type="number"
                placeholder="请输入开户行行号"
                maxlength="12"
                @change="bankCodeChange"
              />
            </el-form-item>
            <el-form-item label="开户行名称" prop="bankBranchName">
              <el-select
                v-model="ruleForm.bankBranchName"
                placeholder="请输入开户行名称"
                filterable
                remote
                :remote-method="querySearchAsync"
                @change="handleSelect"
              >
                <el-option
                  v-for="item in bankBranchList"
                  :key="item.branchCnaps"
                  :label="item.branchName"
                  :value="item.branchName"
                />
              </el-select>
            </el-form-item>
          </div>
          <div class="form-right">
            <div class="title-left-border">上传账户使用凭证</div>
            <el-form-item prop="certificateList">
              <template #label>
                请上传该银行账户近三天的银行交易收款回单
                <el-image
                  class="image-text"
                  :preview-src-list="exampleUrl"
                  img-text="示例"
                />
              </template>
              <ImgUpload
                v-for="(item, index) in bankVoucher"
                :key="index"
                v-model="item.url"
                class="img-upload-cls"
                :size-limit="10"
                :on-success="(url, file) => onUploadSuccess(url, file, index)"
                :dir="OSS_DIR.BANK_CARD_CERTIFICATE"
              >
                <div slot="empty">
                  <div class="describe">点击或拖拽至此处上传文件</div>
                  <div class="describe">（支持jpg/jpeg/png格式，不超过10M）</div>
                </div>
              </ImgUpload>
            </el-form-item>
          </div>
        </section>
      </el-form>
      <div class="description">
        <h3 class="title">为什么要绑定银行账户</h3>
        <div class="paragraph">
          <label class="num">1</label>电子交易账户中用来交易的资金，其充值和提现都是通过绑定的<span class="word-mark">企业同名银行账户</span>完成
        </div>
        <div class="paragraph">
          <label class="num">2</label>通过<span class="word-mark">绑定银行账户</span>，我们才可以确认当前操作者确实为企业网银的持有者，从而保障每一位客户在后续交易中的交易对手都是经过验证的真实有效的客户
        </div>
        <div class="paragraph">
          <label class="num">3</label>银行需要企业的<span class="word-mark">真实信息</span>来开立对应的电子交易账户
        </div>
      </div>
    </div>

    <div class="footer">
      <!--
        <div class="agreement">
        <template v-if="showAgreement">
        <el-checkbox v-model="ruleForm.banckAgreementChecked">我已阅读并同意</el-checkbox>
        <div>
        <a
        class="link"
        target="_blank"
        rel="noopener noreferrer"
        @click="checkAgreement(OSS_FILES_URL.YILIAN_OPEN_PROTOCOL_URL)"
        >《智付E+虚户开通电子协议》</a>
        <a
        class="link"
        target="_blank"
        rel="noopener noreferrer"
        @click="checkAgreement(OSS_FILES_URL.HELIBAO_OPEN_PROTOCOL_URL)"
        >《智付合利宝虚户开通电子协议》</a>
        <br>
        <a
        class="link"
        target="_blank"
        rel="noopener noreferrer"
        @click="checkAgreement(OSS_FILES_URL.ZHONGBANG_OPEN_PROTOCOL_URL)"
        >《智付邦+银行E账通系统开户三方协议》</a>
        </div>
        </template>
        </div>
      -->
      <div class="btn-box">
        <el-button
          v-if="!payFailStage"
          size="large"
          type="primary"
          border
          @click="prev"
        >
          上一步
        </el-button>
        <el-button
          v-waiting="['post::loading::/corpOpenInfo/save', 'post::loading::/corpOpenInfo/reAuth']"
          size="large"
          type="primary"
          @click="submitForm('ruleForm')"
        >
          提交
        </el-button>
      </div>
    </div>
  </section>
</template>

<script>
import openAccountApi from '@/apis/open-account' // 开户接口
import {
  OPEN_URL_IN_DEFAULT_BROWSER,
} from '@recognize/ipc-event-constant'
import { formatTime } from '@/common/js/date' // 时间格式化
import user from '@/utils/user' // 用户对象
import { REFRESH_ENDORSEMENT, REFRESH_ACCOUNT } from '@/event/modules/site' // 监听常量
import { STOCKHOLDER_CARD_TYPE } from '../options' // 大股东企业类型
import { APPLICATION_STATUS } from '@/constants/open-account'// 常量
import { OSS_DIR, REAL_NAME_AUTH_TYPE } from '@/constant'
import { OSS_FILES_URL } from '@/constants/oss-files-url'
import ImgUpload from '@/views/components/common/img-upload/img-upload.vue' // 图片上传组件

export default {
  name: 'bind-bank-account',
  components: { ImgUpload },
  inject: ['openGlobalInfo'],
  props: {
    // 下一步
    next: {
      type: Function,
      require: true
    },
    payFailStage: {
      type: Number,
      default: 0
    }
  },

  data() {
    // 自定义开户账号验证规则
    const checkBankAccount = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入银行账户'))
      }
      if (/^*********/.test(value)) {
        return callback(new Error('该银行账户为智付E+二类账户,不可绑定!'))
      }
      callback()
    }

    // 自定义开户行支行行号验证规则
    const checkBankBranchCode = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入开户行行号'))
      }
      // eslint-disable-next-line no-magic-numbers
      if (value.length !== 12) {
        return callback(new Error('开户行行号为12位'))
      }
      callback()
    }
    const checkBankVoucher = (rule, value, callback) => {
      if (!this.ruleForm?.certificateList || !this.ruleForm.certificateList.length) {
        return callback(new Error('请上传账户使用凭证'))
      }
      callback()
    }

    return {
      OSS_FILES_URL,
      OSS_DIR,
      APPLICATION_STATUS,
      exampleUrl: ['https://static-file.duijie.org.cn/frontend-file/frontend/imgs/bank-account/银行账户上传凭证-电子回单示例.png'],
      bankVoucher: [{ url: '', name: '' }, { url: '', name: '' }, { url: '', name: '' }],
      bankBranchList: [],
      ruleForm: {
        bankAccount: '', // 开户行账号，非空字段
        bankCnap: '', // 开户行银联编码
        bankName: '', // 开户银行名称，非空字段
        bankCode: '', // 开户行行号，非空字段
        bankBranchName: '', // 开户支行名称，非空字段
        bankBranchCode: '', // 开户行支行行号
        // banckAgreementChecked: false, // 请阅读协议并勾选
        bankProvinceId: '', // 省ID，非空字段
        bankProvinceName: '', // 省名称，非空字段
        bankCityId: '', // 市ID，非空字段
        bankCityName: '' // 市名称，非空字段
      },
      rules: {
        bankAccount: [{ required: true, validator: checkBankAccount, trigger: 'blur' }], // 开户行账号
        bankBranchCode: [{ required: true, validator: checkBankBranchCode, trigger: ['change', 'blur'] }], // 开户行支行行号
        bankBranchName: [{ required: true, message: '请输入开户行名称', trigger: ['change', 'blur'] }], // 开户支行名称
        certificateList: [{ required: true, validator: checkBankVoucher, trigger: ['change', 'blur'] }]

      },
      // 银行账户数字格式
      numberFormat: {
        decimal: false, // 是否支持小数
        negative: false, // 是否支持负数
        maxLength: 24, // 最大长度
      }
    }
  },

  computed: {
    // 企业信息
    corpInfo() {
      return this.$store.state?.user?.corpInfo || {}
    },
    // 是否展示协议，重新认证/实名预警/实名失效，填写银行账户页面，不展示开通渠道的协议
    showAgreement() {
      if (this.openGlobalInfo?.realNameAuthType && [REAL_NAME_AUTH_TYPE.RECEIVE, REAL_NAME_AUTH_TYPE.FAILURE, REAL_NAME_AUTH_TYPE.ACTIVE_REAL_NAME].includes(this.openGlobalInfo?.realNameAuthType)) {
        return false
      } else {
        return true
      }
    }
  },

  watch: {
    // 监听本地缓存开户信息对象变化
    ruleForm: {
      deep: true,
      handler(val) {
        user.setOpenAccount(val)
      }
    },
  },

  created() {
    // 获取本地缓存开户
    this.openAccount = user.getOpenAccount()
    this.ruleForm = Object.assign(this.ruleForm, this.openAccount)
    // 回显交易回单数据
    if (this.openAccount.certificateList && this.openAccount.certificateList.length > 0) {
      this.bankVoucher = this.bankVoucher.map((item, index) => {
        let voucher = this.openAccount.certificateList[index]
        item.url = voucher ? voucher.url : ''
        item.name = voucher ? voucher.name : ''
        return item
      })
    }
  },

  methods: {

    // 上一步
    prev() {
      this.next({
        step: 1,
        type: 'prev',
        legalStep: 5
      })
    },

    // 下一步
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          // if (!this.ruleForm.banckAgreementChecked && this.showAgreement) {
          //   return this.$message({
          //     message: '请阅读协议并勾选',
          //   })
          // }
          this.upcheck()
        } else {
          return false
        }
      })
    },

    /**
     * 日期转换统一处理
     * @param {string} longTerm 是否长期的key
     * @param {string} formDate 开始日期的key
     * @param {string} endDate 结束日期的key
     */
    timeTransformation(longTerm, formDate, endDate) {
      // 是否长期有效,0-否,1-是，非空字段
      this.ruleForm[longTerm] = +this.ruleForm[longTerm]
      // 开始日期，非空字段
      this.ruleForm[formDate] = formatTime(this.ruleForm[formDate], 'YYYY-MM-DD')
      // 结束日期，非空字段
      if (this.ruleForm[endDate]) {
        this.ruleForm[endDate] = formatTime(this.ruleForm[endDate], 'YYYY-MM-DD')
      }
      // 结束日期为2099-12-31 则变为长期
      if (this.ruleForm[endDate] === '2099-12-31') {
        this.ruleForm[longTerm] = 1
      }
    },

    // 检验通过
    async upcheck() {
      // 营业执执照日期转换统一处理
      this.timeTransformation('businessLicenseLongTerm', 'businessLicenseFromDate', 'businessLicenseEndDate')

      // 法人身份证日期转换统一处理
      this.timeTransformation('legalIdentityCardLongTerm', 'legalIdentityCardFromDate', 'legalIdentityCardEndDate')

      // 经办人身份证日期转换统一处理
      this.timeTransformation('agentIdentityCardLongTerm', 'agentIdentityCardFromDate', 'agentIdentityCardEndDate')

      // 大股东身份证日期转换统一处理
      this.timeTransformation('stockholderIdentityCardLongTerm', 'stockholderIdentityCardFromDate', 'stockholderIdentityCardEndDate')

      // 企业成立日期
      this.ruleForm.establishDate = formatTime(this.ruleForm.establishDate, 'YYYY-MM-DD')

      // 经办人是否同法人,0-否,1-是
      this.ruleForm.agentEqualLegal = +this.ruleForm.agentEqualLegal
      // 大股东是否同法人,0-否,1-是
      this.ruleForm.stockholderEqualLegal = +this.ruleForm.stockholderEqualLegal
      // 受益人是否同法人,0-否,1-是
      this.ruleForm.beneficiaryEqualLegal = +this.ruleForm.beneficiaryEqualLegal

      // 受益人
      this.ruleForm.beneficiaryList.map(item => {
        // 受益人是否同法人,0-否,1-是
        item.beneficiaryEqualLegal = +item.beneficiaryEqualLegal
        // 受益人是否同股东,0-否,1-是
        item.beneficiaryEqualStockholder = +item.beneficiaryEqualStockholder
        // 受益人身份证是否长期有效,0-否,1-是，非空字段
        item.beneficiaryIdentityCardLongTerm = +item.beneficiaryIdentityCardLongTerm
        // 受益人身份证开始日期，非空字段
        item.beneficiaryIdentityCardFromDate && (item.beneficiaryIdentityCardFromDate = formatTime(item.beneficiaryIdentityCardFromDate, 'YYYY-MM-DD'))
        // 受益人身份证结束日期，非空字段
        item.beneficiaryIdentityCardEndDate && (item.beneficiaryIdentityCardEndDate = formatTime(item.beneficiaryIdentityCardEndDate, 'YYYY-MM-DD'))
        // 受益人身份证结束日期为2099-12-31 则变为长期
        if (item.beneficiaryIdentityCardEndDate === '2099-12-31') {
          item.beneficiaryIdentityCardLongTerm = 1
        }
        return item
      })

      // console.log('this.corpOpenInfoUpdate', this.openGlobalInfo?.corpOpenInfoUpdate)

      if (this.ruleForm.stockholderCardType === STOCKHOLDER_CARD_TYPE.enterprise.id) { // 大股东类型是企业
        this.ruleForm.stockholderBusinessLicenseUrl && (this.ruleForm.stockholderIdentityCardFrontUrl = this.ruleForm.stockholderBusinessLicenseUrl) // 大股东企业营业执照
      } else {
        this.ruleForm.stockholderCardType = STOCKHOLDER_CARD_TYPE.personal.id // 大股东类型设置为个人
      }

      // 实际控制人
      if (this.ruleForm.actualController) {
        // 证件是否长期有效，0-否，1-是，非空字段
        this.ruleForm.actualController.cardLongTerm = +this.ruleForm.actualController.cardLongTerm
        // 实际控制人开始日期，非空字段
        this.ruleForm.actualController.cardValidityBegin = formatTime(this.ruleForm.actualController.cardValidityBegin, 'YYYY-MM-DD')
        // 实际控制人结束日期，非空字段
        if (this.ruleForm.actualController.cardValidityEnd) {
          this.ruleForm.actualController.cardValidityEnd = formatTime(this.ruleForm.actualController.cardValidityEnd, 'YYYY-MM-DD')
        }
        // 实际控制人结束日期为2099-12-31 则变为长期
        if (this.ruleForm.actualController.beneficiaryIdentityCardEndDate === '2099-12-31') {
          this.ruleForm.actualController.beneficiaryEqualLegal = 1
        }

        // 是否同法人，0-否，1-是
        this.ruleForm.actualController.controllerEqualLegal = +this.ruleForm.actualController.controllerEqualLegal
        // 是否同股东，0-否，1-是
        this.ruleForm.actualController.controllerEqualStockholder = +this.ruleForm.actualController.controllerEqualStockholder
      }

      // console.log('realNameAuthType', this.openGlobalInfo.realNameAuthType)
      // console.log('reAuthPaymentChannel', this.openGlobalInfo.reAuthPaymentChannel)
      this.ruleForm.realNameAuthType = this.openGlobalInfo.realNameAuthType
      if (this.openGlobalInfo?.corpOpenInfoUpdate) { // 更新商户开户信息
        // 是否为修改银行账户打款失败重新提交 0-不是，1-是 （用于后端区分是否需要校验风控)
        this.ruleForm.payFailStage = this.payFailStage
        await openAccountApi.corpOpenInfoUpdate(this.ruleForm)
      } else {
        this.openGlobalInfo.reAuthPaymentChannel && (this.ruleForm.reAuthPaymentChannel = this.openGlobalInfo.reAuthPaymentChannel)
        await openAccountApi.postCorpOpenInfoSave(this.ruleForm)
      }

      user.removeOpenAccount()
      // 重新获取企业信息
      await this.$store.dispatch('user/getCorpInfo')
      // 刷新用户中心-背书账户状态
      this.$event.emit(REFRESH_ENDORSEMENT)
      // 刷新用户中心-电子账户状态
      this.$event.emit(REFRESH_ACCOUNT)
      this.next({
        step: 2
      })
    },

    // 开户行名称数据
    async querySearchAsync(queryString) {
      if (!queryString) {
        this.bankBranchList = []
        return
      }
      const results = await openAccountApi.getBankSearch({
        branchName: queryString
      })
      this.bankBranchList = results
    },

    // 开户行名称选择
    handleSelect(bankBranchName) {
      this.setBankInfo(this.bankBranchList.find(item => item.branchName === bankBranchName))
    },

    // 监听开户行行号 失去焦点或用户按下回车时触发
    async bankCodeChange(branchCnaps) {
      if (!branchCnaps) {
        return
      }
      const res = await openAccountApi.getBankInfo({
        branchCnaps
      })
      if (res) {
        this.setBankInfo(res)
      } else {
        this.ruleForm.bankBranchName = ''
      }
    },

    // 设置开户行信息
    setBankInfo(obj) {
      this.ruleForm.bankCnap = obj.bankCnaps || ''
      this.ruleForm.bankName = obj.bankName || ''
      this.ruleForm.bankCode = obj.bankCode || ''
      this.ruleForm.bankBranchName = obj.branchName || ''
      this.ruleForm.bankBranchCode = obj.branchCnaps || ''
      this.ruleForm.bankProvinceId = obj.provinceId || ''
      this.ruleForm.bankProvinceName = obj.provinceName || ''
      this.ruleForm.bankCityId = obj.cityId || ''
      this.ruleForm.bankCityName = obj.cityName || ''
    },
    checkAgreement(url) {
      if (this.$ipc) {
        this.$ipc.send(OPEN_URL_IN_DEFAULT_BROWSER, url)
      } else {
        window.open(url)
      }
    },
    setCertificateList() {
      let voucherList = this.bankVoucher.filter(e => e.url)
      this.$set(this.ruleForm, 'certificateList', voucherList)
    },
    onUploadSuccess(url, file, index) {
      this.$set(this.bankVoucher, index, { url, name: file.name })
      this.setCertificateList()
    },
  }
}
</script>
