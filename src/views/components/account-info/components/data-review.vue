<!-- 认证资料审核 -->
<style lang="scss" scoped>
.status-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 520px;
  text-align: center;

  .icon {
    display: inline-block;
    border-radius: 50%;
    width: 52px;
    height: 52px;
    color: $font-color;

    &.error {
      color: $color-warning;
    }
  }

  .title {
    margin: 20px 0 4px;
    font-size: 20px;
    font-weight: 600;
    line-height: 28px;
  }

  .tip {
    font-size: 16px;
    color: $color-text-secondary;
    line-height: 22px;
  }

  .contact {
    overflow: hidden;
    overflow-y: auto;
    margin-top: 16px;
    max-height: 150px;
  }

  .code-box {
    display: flex;
    justify-content: space-between;
    margin: 24px auto 32px;
    width: 352px;

    >div {
      width: 140px;
      font-size: 16px;

      img {
        margin-bottom: 8px;
        width: 100%;
      }
    }
  }

  .btn {
    margin-top: 16px;
  }
}

.dec {
  margin-top: 16px;
  font-size: 16px;
  line-height: 16px;
}

.center {
  text-align: center;
}
</style>

<template>
  <section class="status-tip">
    <div v-if="stageStatus === 1">
      <icon class="icon" type="chengjie-wait" />
      <div class="title">客服审核中</div>
      <div class="tip">您的{{ isRealNameStep ? '实名认证' : '电子交易账户开通' }}申请正在审核中，客服会尽快审核完成，请您耐心等待！</div>
      <div class="tip">客服审核资料期间，如有疑问可扫码联系客服经理！</div>
      <div class="code-box">
        <div>
          <img :src="configDefault.customerManagerQr1" alt="联系客服经理">
        </div>
        <div>
          <img :src="configDefault.customerManagerQr2" alt="联系客服经理">
        </div>
      </div>
      <div class="tip">
        <!-- 预计等待时间：<span class="emphasis">工作日10分钟</span> -->
      </div>
      <el-button
        class="btn"
        type="primary"
        size="large"
        @click="close"
      >
        我知道了
      </el-button>
    </div>
    <div v-if="stageStatus === 2">
      <icon class="icon error" type="chengjie-close-circle" />
      <div class="title">审核被驳回，请重新提交资料</div>
      <div class="tip contact">
        您的{{ isRealNameStep ? '实名认证' : '电子交易账户开通' }}申请客服审核驳回，驳回原因：{{ failReason }}
      </div>
      <div class="code-box">
        <div>
          <img :src="configDefault.customerManagerQr1" alt="联系小桑">
          <div class="center">客服一号</div>
        </div>
        <div>
          <img :src="configDefault.customerManagerQr2" alt="联系小朱">
          <div class="center">客服二号</div>
        </div>
      </div>
      <el-button
        v-if="payFailStage"
        class="btn"
        type="primary"
        size="large"
        @click="back"
      >
        修改银行账户
      </el-button>
      <el-button
        v-else
        v-waiting="['get::loading::/corpOpenInfo']"
        class="btn"
        type="primary"
        size="large"
        @click="resubmit"
      >
        重新提交
      </el-button>
    </div>
    <!--
      <div>
      <icon class="icon success" type="chengjie-check-circle" />
      <div class="title">验证成功</div>
      <div class="tip">系统正在帮您开通电子交易账户，开户结果会通过短信和消息提醒通知您，请耐心等待</div>
      <div class="tip">预计等待时间：<span class="emphasis">5分钟</span></div>
      <el-button
      class="btn"
      type="primary"
      size="large"
      @click="close"
      >
      我知道了
      </el-button>
      </div>
    -->
  </section>
</template>

<script>
import openAccountApi from '@/apis/open-account' // 开户接口
import user from '@/utils/user' // 用户对象
export default {
  name: 'data-review',
  inject: ['openGlobalInfo'],

  props: {
    next: { // 下一步
      type: Function,
      require: true
    },
    stageStatus: { // 开户状态
      type: Number,
      require: true
    },
    failReason: String, // 审核失败原因
    close: Function, // 关闭窗口
    isRealNameStep: { // 是否是实名认证流程
      type: Number,
      default: 1
    },
    payFailStage: { // 是否打款失败
      type: Number,
      default: 0
    }
  },

  methods: {
    async resubmit() {
      const res = await this.getCorpOpenInfo()
      this.next({
        step: 0,
        update: true,
        realNameAuthType: this.openGlobalInfo.realNameAuthType || (res && res.realNameAuthType) || '',
        reAuthPaymentChannel: this.openGlobalInfo.reAuthPaymentChannel || (res && res.reAuthPaymentChannel) || ''
      })
    },
    back() {
      this.next({
        step: 2,
        resubmit: true,
        update: true,
        payFailStage: this.payFailStage // 是否打款失败
      })
    },

    // 查询商户开户信息
    async getCorpOpenInfo() {
      let res = await openAccountApi.getCorpOpenInfo()
      if (res) {
        res.businessLicenseLongTerm = !!res.businessLicenseLongTerm
        res.legalIdentityCardLongTerm = !!res.legalIdentityCardLongTerm
        res.agentIdentityCardLongTerm = !!res.agentIdentityCardLongTerm
        res.stockholderIdentityCardLongTerm = !!res.stockholderIdentityCardLongTerm
        res.agentEqualLegal = !!res.agentEqualLegal // 经办人是否同法人,0-否,1-是
        res.beneficiaryEqualLegal = !!res.beneficiaryEqualLegal // 受益人是否同法人,0-否,1-是
        res.stockholderEqualLegal = !!res.stockholderEqualLegal // 大股东是否同法人,0-否,1-是
        res.provinceCityDistricCodes = [res.provinceCode, res.cityCode, res.districtCode]
        this.openAccount = user.getOpenAccount() || {}
        this.openAccount = Object.assign(this.openAccount, res)
        user.setOpenAccount(this.openAccount)
      }
      return res
    }
  }
}
</script>
