<!-- 实际控制人信息 -->
<style lang="scss" scoped>
@import "../account.scss";

.main {
  background-color: $color-FFFFFF;

  .checkbox-box {
    display: flex;
    align-items: center;
    padding-bottom: 12px;

    .label {
      margin-top: 2px;
      margin-right: 16px;
      font-size: 16px;
      font-weight: bold;
    }

    ::v-deep {
      .el-checkbox {
        margin-right: 16px;
      }
    }
  }

  .el-form-item:last-child {
    margin-bottom: 0;
  }
}
</style>

<template>
  <main class="actual-controller-page">
    <div class="enterprise-info-box">
      <div class="left">
        <Guide :legal-step="legalStep" />
      </div>
      <div class="right">
        <div class="main">
          <div class="checkbox-box">
            <!-- <span class="label">实际控制人</span> -->
            <el-checkbox v-model="ruleForm.controllerEqualLegal" label="同法人" @change="controllerEqualLegalChange" />
            <!-- 当股东证件类型为“个人”时才可勾选【同股东】 -->
            <el-checkbox
              v-if="openAccount.stockholderCardType === STOCKHOLDER_CARD_TYPE.personal.id"
              v-model="ruleForm.controllerEqualStockholder"
              label="同股东"
              @change="controllerEqualStockholderChange"
            />
            <span class="describe-txt">(企业的实际控股人，如实际控股人为企业可选择同法人)</span>
          </div>
          <el-form
            ref="ruleForm"
            :model="ruleForm"
            :rules="rules"
            class="form"
            :inline-message="true"
            :disabled="ruleForm.controllerEqualLegal || (openAccount.stockholderCardType === STOCKHOLDER_CARD_TYPE.personal.id && ruleForm.controllerEqualStockholder)"
          >
            <div class="multiseriate">
              <el-form-item prop="cardType" label="实际控制人证件类型">
                <el-select v-model="ruleForm.cardType" placeholder="请选择实际控制人证件类型">
                  <el-option
                    v-for="item in cardType"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="实际控制人姓名" prop="name" class="name">
                <el-input v-model="ruleForm.name" placeholder="请输入实际控制人姓名" />
              </el-form-item>
            </div>
            <div class="multiseriate">
              <el-form-item prop="cardNo" label="实际控制人证件号">
                <el-input v-model="ruleForm.cardNo" placeholder="请输入实际控制人证件号" />
              </el-form-item>
              <el-form-item label="实际控制人手机号" prop="mobile">
                <el-input v-model="ruleForm.mobile" placeholder="请输入实际控制人手机号" maxlength="11" />
              </el-form-item>
            </div>
            <div class="g-required">
              实际控制人证件有效期
              <el-checkbox v-model="ruleForm.cardLongTerm" label="结束日期：长期" name="type" />
            </div>
            <div class="multiseriate">
              <el-form-item prop="cardValidityBegin">
                <el-date-picker
                  v-model="ruleForm.cardValidityBegin"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="开始日期"
                  style="width: 100%;"
                  @change="cardValidityBeginChange"
                />
              </el-form-item>
              <el-form-item prop="cardValidityEnd">
                <el-date-picker
                  v-model="ruleForm.cardValidityEnd"
                  :disabled="!!ruleForm.cardLongTerm"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="结束日期"
                  style="width: 100%;"
                />
              </el-form-item>
            </div>

            <el-form-item label="实际控制人证件地址" prop="actualControllerAddress">
              <el-input v-model="ruleForm.actualControllerAddress" placeholder="请输入实际控制人证件地址" />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <div class="footer">
      <div class="btn-box">
        <el-button
          size="large"
          type="primary"
          border
          @click="prev"
        >
          上一步
        </el-button>
        <el-button size="large" type="primary" @click="submitForm">下一步</el-button>
      </div>
    </div>
  </main>
</template>

<script>
import user from '@/utils/user' // 用户对象
import { isPhone, validateIDNumber } from '@/common/js/validator' // 验证规则
import { STOCKHOLDER_CARD_TYPE, riskManagementTips, setSerialNos } from '../options' // 大股东企业类型
import { dealTime } from '@/common/js/date' // 时间格式
import Guide from './enterprise-guide.vue' // 企业信息左侧导航指引
import openAccountApi from '@/apis/open-account' // 开户接口

// 实际控制人证件类型
const cardType = [
  {
    name: '身份证',
    id: 101
  },
  {
    name: '护照',
    id: 102
  },
  {
    name: '港澳通行证',
    id: 103
  },
  {
    name: '台湾往来大陆通行证',
    id: 104
  },
  {
    name: '临时身份证',
    id: 105
  }
]

const formInit = {
  name: '', // 名称
  cardType: 101, // 实际控制人证件类型，101-身份证，102-护照，103-港澳通行证，104-台湾往来大陆通行证，105-临时身份证，非空字段
  cardNo: '', // 实际控制人证件号，非空字段
  cardValidityBegin: '', // 实际控制人证件开始日期，非空字段
  cardValidityEnd: '', // 实际控制人证件结束日期，非空字段
  cardLongTerm: false, // 证件是否长期有效，0-否，1-是，非空字段
  mobile: '', // 实际控制人手机号码，非空字段
  actualControllerAddress: '', // 实际控制人地址，非空字段
  controllerEqualLegal: false, // 是否同法人，0-否，1-是
  controllerEqualStockholder: false // 是否同股东，0-否，1-是
}

export default {
  name: 'actual-controller-page',
  components: {
    Guide
  },
  props: {
    legalStep: Number,
    // 下一步
    next: {
      type: Function,
      require: true
    }
  },

  data() {
    // 自定义法人证件号验证规则
    const checkCardNo = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入实际控制人证件号'))
      }
      // eslint-disable-next-line no-magic-numbers
      if (this.ruleForm.cardType === 101 && !validateIDNumber(value)) {
        return callback(new Error('输入实际控制人证件号格式不对'))
      }
      callback()
    }

    // 自定义结束日期检验规则
    const checkLegalIdentityCardEndDate = (rule, value, callback) => {
      if (this.ruleForm.cardValidityBegin && value && dealTime(this.ruleForm.cardValidityBegin) > dealTime(value)) {
        callback(new Error('结束日期不能小于开始日期'))
      } else if (value || this.ruleForm.cardLongTerm) {
        callback()
      } else {
        callback(new Error('请选择结束日期'))
      }
    }

    // 自定义手机号码验证规则
    const checkPhone = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入实际控制人手机号'))
      }
      if (!isPhone(value)) {
        return callback(new Error('输入手机号码格式不对'))
      }
      callback()
    }

    return {
      STOCKHOLDER_CARD_TYPE, // 大股东企业类型
      cardType, // 实际控制人证件类型
      openAccount: null, // 本地缓存开户信息
      ruleForm: { ...formInit },
      rules: {
        name: [{ required: true, message: '请输入实际控制人姓名', trigger: ['blur'] }],
        cardType: [{ required: true, message: '请选择实际控制人证件类型', trigger: ['blur'] }],
        cardNo: [{ required: true, validator: checkCardNo, trigger: ['blur', 'change'] }],
        cardValidityBegin: [{ required: true, message: '请选择开始日期', trigger: 'none' }],
        cardValidityEnd: [{ required: true, validator: checkLegalIdentityCardEndDate, trigger: 'change' }],
        mobile: [{ required: true, validator: checkPhone, trigger: 'blur' }],
        actualControllerAddress: [{ required: true, message: '请输入实际控制人详细地址', trigger: 'blur' }],
      },
    }
  },

  watch: {
    // 监听变化更新本地缓存
    ruleForm: {
      deep: true,
      handler() {
        this.updateOpenAccount()
      }
    },
    // 监听是否长期有效
    'ruleForm.cardLongTerm': {
      handler(val) {
        if (val) {
          this.ruleForm.cardValidityEnd = '2099-12-31'
        } else {
          !this.ruleForm.controllerEqualLegal && !this.ruleForm.controllerEqualStockholder && (this.ruleForm.cardValidityEnd = '')
        }
        this.$refs.ruleForm.validateField('cardValidityEnd')
      }
    },
  },

  created() {
    // 获取本地缓存开户
    this.openAccount = user.getOpenAccount()
    if (this.openAccount.actualController) {
      // 证件是否长期有效，0-否，1-是，非空字段
      this.openAccount.actualController.cardLongTerm = !!this.openAccount.actualController.cardLongTerm
      // 是否同法人，0-否，1-是
      this.openAccount.actualController.controllerEqualLegal = !!this.openAccount.actualController.controllerEqualLegal

      // 是否同股东，0-否，1-是
      this.openAccount.actualController.controllerEqualStockholder = !!this.openAccount.actualController.controllerEqualStockholder
      Object.assign(this.ruleForm, this.openAccount.actualController)

      // 为了和法人和股东信息改变后保持一致
      this.$nextTick().then(() => {
        this.openAccount.actualController.controllerEqualLegal && this.controllerEqualLegalChange(true)
        this.openAccount.actualController.controllerEqualStockholder && this.controllerEqualStockholderChange(true)
      })
    }
  },

  methods: {
    // 监听开始日期变化
    cardValidityBeginChange() {
      this.$refs.ruleForm.validateField('cardValidityEnd')
    },

    // 复选框 同法人 监听改变
    controllerEqualLegalChange(v) {
      if (v) {
        this.ruleForm.controllerEqualStockholder = false // 设置同股东为false

        const { openAccount } = this
        this.setRuleForm({
          name: openAccount.legalName, // 名称
          cardType: 101, // 实际控制人证件类型，101-身份证，102-护照，103-港澳通行证，104-台湾往来大陆通行证，105-临时身份证，非空字段
          cardNo: openAccount.legalIdentityCard, // 实际控制人证件号，非空字段
          cardValidityBegin: openAccount.legalIdentityCardFromDate, // 实际控制人证件开始日期，非空字段实际控制人证件开始日期，非空字段
          cardValidityEnd: openAccount.legalIdentityCardEndDate, // 实际控制人证件结束日期，非空字段
          cardLongTerm: openAccount.legalIdentityCardLongTerm, // 证件是否长期有效，0-否，1-是，非空字段
          mobile: openAccount.legalPhone, // 实际控制人手机号码，非空字段
          actualControllerAddress: openAccount.legalHouseholdAddress, // 实际控制人地址，非空字段
        })
      } else {
        this.clearRuleForm()
      }
    },

    // 复选框 同股东 监听改变
    controllerEqualStockholderChange(v) {
      if (v) {
        this.ruleForm.controllerEqualLegal = false // 设置同法人为false

        const { openAccount } = this
        this.setRuleForm({
          name: openAccount.stockholderName, // 名称
          cardType: 101, // 实际控制人证件类型，101-身份证，102-护照，103-港澳通行证，104-台湾往来大陆通行证，105-临时身份证，非空字段
          cardNo: openAccount.stockholderIdentityCard, // 实际控制人证件号，非空字段
          cardValidityBegin: openAccount.stockholderIdentityCardFromDate, // 实际控制人证件开始日期，非空字段实际控制人证件开始日期，非空字段
          cardValidityEnd: openAccount.stockholderIdentityCardEndDate, // 实际控制人证件结束日期，非空字段
          cardLongTerm: openAccount.stockholderIdentityCardLongTerm, // 证件是否长期有效，0-否，1-是，非空字段
          mobile: openAccount.stockholderPhone, // 实际控制人手机号码，非空字段
          actualControllerAddress: openAccount.stockholderHouseholdAddress, // 实际控制人地址，非空字段

        })
      } else {
        this.clearRuleForm()
      }
    },

    // 设置数据
    setRuleForm(obj) {
      Object.assign(this.ruleForm, obj)
      this.$refs.ruleForm.validate()
    },

    // 清空
    clearRuleForm() {
      Object.assign(this.ruleForm, { ...formInit })
    },

    // 更新本地缓存开户
    updateOpenAccount() {
      this.openAccount = user.getOpenAccount()
      const { ruleForm } = this
      this.openAccount.actualController = ruleForm
      user.setOpenAccount(this.openAccount)
    },

    // 上一步
    prev() {
      setSerialNos({ no: '', step: this.legalStep, isNext: false })
      this.next({
        step: 1,
        type: 'prev',
        legalStep: this.legalStep - 1
      })
    },

    // 下一步
    async submitForm() {
      if (await this.$refs.ruleForm.validate()) {
        const riskData = await openAccountApi.checkControllerInfo({
          name: this.ruleForm.name,
          cardNo: this.ruleForm.cardNo,
          controllerCardValidityEnd: this.ruleForm.cardValidityEnd,
          companyCreditCode: this.openAccount.companyCreditCode,
        })
        if (riskData.result === 1) return riskManagementTips(riskData)
        // 风控校验通过设置风控流水号
        setSerialNos({ no: riskData.serialNo, step: this.legalStep, isNext: true })
        this.next({
          step: 1,
          type: 'next',
          legalStep: this.legalStep + 1
        })
      }
    },
  },
}
</script>
