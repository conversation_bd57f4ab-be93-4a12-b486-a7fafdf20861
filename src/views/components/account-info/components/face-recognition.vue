<!-- 人脸识别活体检测弹窗 -->
<style lang="scss" scoped>
::v-deep .video-message-dialog {
  .content {
    padding: 16px;
    background-color: $color-FFFFFF;

    .img-box {
      @include flex-cc;

      .failure {
        position: relative;
        width: 170px;
        height: 170px;
      }

      .code-bg {
        width: 100%;
        height: 100%;
        background-image: url("https://oss.chengjie.red/web/imgs/open-account/code-demo.png");
        background-size: 100% 100%;
        opacity: .1;
      }

      .refresh-box {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 170px;
        font-size: 16px;
        text-align: center;
        transform: translateX(-50%) translateY(-50%);
        line-height: 22px;
        cursor: pointer;

        .sdicon-reload {
          width: 32px;
          height: 32px;
          color: $font-color;
        }
      }
    }

    .sdicon-reload-box {
      margin-bottom: 10px;
      text-align: center;
      color: $font-color;
      cursor: pointer;
    }

    .text-tip {
      font-size: 16px;
      line-height: 22px;
      text-align: center;
    }
  }

  .dialog-footer {
    .el-button {
      width: 138px;
      height: 40px;
    }
  }
}
</style>

<template>
  <main>
    <el-dialog
      width="600px"
      title="人脸识别活体检测"
      :visible.sync="dialogVisible"
      :append-to-body="true"
      custom-class="video-message-dialog"
    >
      <div class="content">
        <div v-loading="loading" class="img-box">
          <Qrcode
            v-if="qrcodeMsg"
            tag="img"
            class="qrcode"
            :value="qrcodeMsg"
            :options="{width: 170}"
          />
          <div v-else class="failure">
            <div class="code-bg" />
          </div>
        </div>
        <div class="sdicon-reload-box" @click="reloadCode">
          <icon type="chengjie-reload" class="sdicon-reload" /> 刷新二维码
        </div>
        <div class="text-tip">请打开 <span class="text-primary text-bold">支付宝APP > 扫一扫</span>，扫描上方二维码，完成法定代表人的人脸识别活体检测，二维码超时失效后需重新获取二维码。</div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">未完成识别</el-button>
        <el-button
          type="primary"
          @click="dialogVisible = false"
        >
          已完成识别
        </el-button>
      </div>
    </el-dialog>
  </main>
</template>

<script>
import Qrcode from '@chenfengyuan/vue-qrcode'
import openAccountApi from '@/apis/open-account'
export default {
  name: 'preview-video-dialog',
  components: {
    Qrcode,
  },

  props: {
    visible: Boolean, // 显示弹窗
  },

  data() {
    return {
      dialogVisible: false, // 显示弹窗
      qrcodeMsg: '', // 二维码
      infoObj: null, // 传参对象
      timer: null, // 定时器
      loading: true,
    }
  },

  beforeDestroy() {
    this.timer && clearInterval(this.timer)
  },

  methods: {
    init(obj) {
      obj.refresh = 0 // 是否主动刷新，0-否，1-是
      this.infoObj = obj
      this.postFaceUrl(obj)

      this.dialogVisible = true
    },

    // 倒计时
    timing(faceAuthUrlExpireTime) {
      if (this.timer) {
        clearInterval(this.timer)
      }
      this.timer = setInterval(() => {
        if (Date.now() >= faceAuthUrlExpireTime) {
          clearInterval(this.timer)
          this.reloadCode()
        }
      }, 1000)
    },

    // 获取人脸识别的url
    async postFaceUrl(obj) {
      this.loading = true
      const res = await openAccountApi.postFaceUrl(obj)
      this.loading = false
      this.qrcodeMsg = res.faceAuthUrl // 人脸识别链接
      // this.timing(res.faceAuthUrlExpireTime) // 人脸识别链接失效时间

      if (obj.refresh === 1) {
        this.$message.success('刷新成功')
      }

      // "faceAuthStatus":1,  //人脸识别状态，0-认证中，1-认证失败，2-认证成功
      if (res.faceAuthStatus === 2) {
        this.$message.success('您已人脸识别成功')
      }
    },

    // 点击刷新二维码
    reloadCode() {
      this.postFaceUrl({
        ...this.infoObj,
        refresh: 1, // 是否主动刷新，0-否，1-是
      })
    }
  }
}
</script>
