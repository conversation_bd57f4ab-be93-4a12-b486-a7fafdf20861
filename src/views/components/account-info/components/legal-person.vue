<style lang="scss" scoped>
@import "../account.scss";

.legal-info {
  ::v-deep {
    .el-checkbox {
      display: flex;
      align-items: center;
      margin-right: 26px;

      &:last-of-type {
        margin-right: 0;
      }
    }
  }

  .form {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    background-color: $color-FFFFFF;

    .left::after {
      position: absolute;
      top: 0;
      right: -12px;
      z-index: 1;
      width: 12px;
      height: 100%;
      background-color: $color-F2F2F2;
      content: "";
    }

    .right::after {
      position: absolute;
      top: 0;
      left: -12px;
      z-index: 1;
      width: 12px;
      height: 100%;
      background-color: $color-F2F2F2;
      content: "";
    }

    .face-recognition {
      width: 366px;
      font-size: 14px;

      .svg-icon {
        margin-bottom: 2px;
        font-size: 16px;
      }
    }
  }

  .checkbox-box {
    margin-bottom: 16px;
  }

  .tip {
    margin: 5px 0 0;
    color: $color-text-secondary;
  }
}

.refer-box {
  display: flex;
  justify-content: space-between;

  >div {
    flex: 1;
    display: flex;
    justify-content: space-between;

    &:first-child {
      margin-right: 12px;
    }

    .link {
      @include example-underline;
    }
  }
}

.icon-question {
  margin-left: 4px;
}

.flex-start {
  display: flex;

  // @include flex-vc;
}
</style>

<template>
  <section class="legal-info">
    <div class="enterprise-info-box">
      <div class="left">
        <Guide :legal-step="legalStep" />
      </div>
      <div class="right">
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          class="form"
          :inline-message="true"
        >
          <div style="width: 100%;">
            <div class="multiseriate">
              <el-form-item label="法人姓名" prop="legalName">
                <el-input v-model="ruleForm.legalName" :disabled="!isRealNameStep" placeholder="请输入法人姓名" />
              </el-form-item>
              <el-form-item label="法人手机号码" prop="legalPhone">
                <el-input
                  v-model="ruleForm.legalPhone"
                  :disabled="!isRealNameStep"
                  placeholder="请输入法人手机号码"
                  maxlength="11"
                  type="number"
                />
              </el-form-item>
            </div>
            <div class="multiseriate">
              <el-form-item label="证件类型">
                <el-select v-model="ruleForm.legalIdentityCardType" :disabled="true" placeholder="请选择证件类型">
                  <el-option
                    :label="IDENTITY_CARD_TYPE.personal.name"
                    :value="IDENTITY_CARD_TYPE.personal.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="法人证件号" prop="legalIdentityCard">
                <el-input
                  v-model="ruleForm.legalIdentityCard"
                  :disabled="!isRealNameStep"
                  placeholder="请输入法人证件号"
                  maxlength="18"
                />
              </el-form-item>
            </div>

            <div class="g-required">
              法人证件有效期
              <el-checkbox v-model="ruleForm.legalIdentityCardLongTerm" label="结束日期：长期" name="type" />
            </div>
            <div class="multiseriate">
              <el-form-item prop="legalIdentityCardFromDate">
                <el-date-picker
                  v-model="ruleForm.legalIdentityCardFromDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="开始日期"
                  @change="legalIdentityCardFromDateChange"
                />
              </el-form-item>
              <el-form-item prop="legalIdentityCardEndDate">
                <el-date-picker
                  v-model="ruleForm.legalIdentityCardEndDate"
                  value-format="yyyy-MM-dd"
                  :disabled="!!ruleForm.legalIdentityCardLongTerm"
                  type="date"
                  placeholder="结束日期"
                />
              </el-form-item>
            </div>

            <div class="multiseriate">
              <el-form-item label="电子邮箱" prop="legalEmail" :error="legalEmailError">
                <el-input v-model="ruleForm.legalEmail" :disabled="!isRealNameStep" placeholder="请输入法人邮箱" />
              </el-form-item>
              <div class="mar-left8">
                <el-form-item label="法人性别" prop="legalSex">
                  <el-select v-model="ruleForm.legalSex" placeholder="请选择法人性别">
                    <el-option label="男" :value="1" />
                    <el-option label="女" :value="2" />
                  </el-select>
                </el-form-item>
              </div>
              <el-form-item label="签发机关" prop="legalIdentityCardIssuingAuthority">
                <el-input v-model="ruleForm.legalIdentityCardIssuingAuthority" placeholder="请输入签发机关" />
              </el-form-item>
            </div>

            <el-form-item label="法人户籍地址" prop="legalHouseholdAddress">
              <el-input v-model="ruleForm.legalHouseholdAddress" placeholder="请输入法人户籍地址" />
            </el-form-item>

            <div class="multiseriate">
              <div>
                <div class="g-required">
                  法人人脸识别活体检测
                </div>
                <div>
                  <el-button
                    type="primary"
                    border
                    class="face-recognition"
                    @click="getFaceRecognition"
                  >
                    <icon type="chengjie-scan" />
                    点击获取人脸识别二维码
                  </el-button>
                </div>
              </div>
              <div>
                <div class="refer-box">
                  <div class="g-required">
                    <div class="flex-start">
                      业务类型
                      <el-tooltip effect="dark" placement="top">
                        <icon class="icon icon-question" type="chengjie-wenti" />
                        <template #content>
                          <div>
                            1. 票据撮合交易：须上传法人视频影像与其他文件<br>申请电子交易账户开通，开通后支持票据撮合交易
                            <!-- <br>与银票秒贴； -->
                          </div>
                          <!-- <div>2. 银票秒贴：无须上传法人视频影像与其他文件，<br>不申请电子交易账户开通，认证成功后仅支持银票<br>秒贴。</div> -->
                        </template>
                      </el-tooltip>
                    </div>
                  </div>
                </div>
                <el-form-item prop="jdOpenType">
                  <!-- openGlobalInfo.realNameAuthType !== 0 || !isRealNameStep -->
                  <el-select v-model="ruleForm.jdOpenType" placeholder="请选择业务类型" :disabled="true">
                    <el-option
                      v-for="item in jdOpenTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </div>
            </div>

            <!--
              <div class="checkbox-box">
              <el-checkbox v-model="ruleForm.agentEqualLegal" disabled>被授权人或经办人同法人</el-checkbox>
              </div>
              <div class="checkbox-box">
              <el-checkbox v-model="ruleForm.beneficiaryEqualLegal">受益人仅1个且同法人</el-checkbox>
              </div>
              <div class="checkbox-box">
              <el-checkbox v-model="ruleForm.stockholderEqualLegal">大股东同法人</el-checkbox>
              </div>

              <div class="tip">若取消勾选，则需要手动填写对应信息</div>
            -->
          </div>
        </el-form>
      </div>
    </div>

    <div class="footer">
      <div class="btn-box">
        <el-button
          class="at-once-btn"
          type="primary"
          size="large"
          border
          width="80px"
          @click="prev"
        >
          上一步
        </el-button>
        <el-button
          v-waiting="[openAccountApi.postQueryFaceResult, openAccountApi.getJudgeRepeatCorp]"
          class="at-once-btn"
          type="primary"
          size="large"
          width="80px"
          @click="submitForm('ruleForm')"
        >
          下一步
        </el-button>
      </div>
    </div>

    <!-- 人脸识别活体检测弹窗 -->
    <FaceRecognition ref="faceRecognitionRef" />
  </section>
</template>

<script>
/* eslint-disable no-magic-numbers */

// import region from '@/common/json/region-code.json' // 地址库
import { isPhone, validateIDNumber, validateEmail } from '@/common/js/validator' // 验证规则
import openAccountApi from '@/apis/open-account' // 开户接口
import user from '@/utils/user' // 用户对象
import { dealTime } from '@/common/js/date' // 时间格式
import { OSS_FILES_URL } from '@/constants/oss-files-url'
import {
  // holdingTypeOptions, // 企业控股类型 选项
  // taxOfficeTypeOptions, // 收税机构类型收税机构类型 选项
  // scaleTypeOptions, // 企业规模类型 选项
  // STOCKHOLDER_CARD_TYPE, // 大股东企业类型
  jdOpenTypeOptions, // 企业 业务类型
  riskManagementTips, // 风控校验提示
  setSerialNos, // 设置风控流水号
  IDENTITY_CARD_TYPE, // 证件类型
} from '../options'
import { REAL_NAME_AUTH_TYPE } from '@/constant'
// import { APPLICATION_STATUS } from '@/constants/open-account' // 常量
import FaceRecognition from './face-recognition.vue' // 人脸识别活体检测弹窗
import Guide from './enterprise-guide.vue' // 企业信息左侧导航指引

export default {
  name: 'legal-person',

  components: {
    FaceRecognition, // 人脸识别活体检测弹窗
    Guide
  },

  inject: ['openGlobalInfo'], // 'qxbRemoteValidate'
  props: {
    legalStep: Number,
    // 下一步
    next: {
      type: Function,
      require: true
    },
    isRealNameStep: { // 是否是实名认证流程
      type: Number,
      default: 1
    },
  },

  data() {
    // 自定义法人身份证结束日期检验规则
    const checkLegalIdentityCardEndDate = (rule, value, callback) => {
      if (this.ruleForm.legalIdentityCardFromDate && value && dealTime(this.ruleForm.legalIdentityCardFromDate) > dealTime(value)) {
        callback(new Error('结束日期不能小于开始日期'))
      } else if (value || this.ruleForm.legalIdentityCardLongTerm) {
        callback()
      } else {
        callback(new Error('请选择结束日期'))
      }
    }

    // 自定义手机号码验证规则
    const checkPhone = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入手机号码'))
      }
      if (!isPhone(value)) {
        return callback(new Error('输入手机号码格式不对'))
      }
      callback()
    }

    // 自定义法人证件号验证规则
    const checkLegalIdentityCard = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入法人证件号'))
      }
      if (!validateIDNumber(value)) {
        return callback(new Error('输入法人证件号格式不对'))
      }
      callback()
    }

    // 自定义法人邮箱验证规则
    const checkLegalEmail = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入法人邮箱'))
      }
      if (!validateEmail(value)) {
        return callback(new Error('输入法人邮箱格式不对'))
      }
      callback()
    }

    // 基本户核准号校验规则
    const checkBcpNo = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入企业开户许可证核准号或基本存款账户编号'))
      }
      // 核准号首位为大写的字母且范围为“J、L、Z”以内，第2-14位数字，最小要传14位
      // 判断首字母
      const myRegExp = new RegExp('^[JLZ]')
      const initials = myRegExp.exec(value)
      // 判断第2位以后是不是纯数字
      const newValue = value.slice(1)
      const isNumber = /^\d+$/.test(newValue)
      if (!initials || value.length < 14 || !isNumber) {
        return callback(new Error('基本户核准号格式不正确'))
      }
      callback()
    }

    return {
      OSS_FILES_URL,
      IDENTITY_CARD_TYPE,
      openAccountApi,
      REAL_NAME_AUTH_TYPE, // 实名类型常量
      openAccount: null, // 本地缓存开户信息
      legalEmailError: '', // 法人邮箱验证错误提示
      // 表单验证规则
      ruleForm: {
        legalIdentityCardFrontUrl: '', // 法人身份证正面url，非空字段
        legalIdentityCardBackUrl: '', // 法人身份证背面url，非空字段
        legalVideoUrl: '', // 法人视频文件url，非空字段
        legalName: '', // 法人姓名，非空字段
        legalIdentityCard: '', // 法人身份证号码，非空字段
        legalPhone: '', // 法人手机号码，非空字段
        legalIdentityCardFromDate: '', // 法人身份证开始日期，非空字段
        legalIdentityCardEndDate: '', // 法人身份证结束日期，非空字段
        legalIdentityCardLongTerm: false, // 法人身份证是否长期有效,0-否,1-是，非空字段
        legalEmail: '', // 法人邮箱，非空字段
        legalHouseholdAddress: '', // 法人户籍地址，非空字段
        legalSex: '', // 法人性别,0-未知,1-男，2-女，
        legalIdentityCardIssuingAuthority: '', // 法人身份证签发机关，
        jdOpenType: 1, // 业务类型
        legalIdentityCardType: '' // 证件类型默认身份证不可修改
      },
      jdOpenTypeOptions, // 企业业务类型
      rules: {
        bcpNo: [{ required: true, validator: checkBcpNo, trigger: ['blur', 'change'] }],
        legalName: [{ required: true, message: '请输入法人姓名', trigger: ['blur', 'change'] }],
        legalPhone: [{ required: true, validator: checkPhone, trigger: ['blur', 'change'] }],
        legalIdentityCard: [{ required: true, validator: checkLegalIdentityCard, trigger: ['blur', 'change'] }],
        legalIdentityCardFromDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
        legalIdentityCardEndDate: [{ required: true, validator: checkLegalIdentityCardEndDate, trigger: 'change' }],
        legalEmail: [{ required: true, validator: checkLegalEmail, trigger: ['blur', 'change'] }],
        legalHouseholdAddress: [{ required: true, message: '请输入法人户籍地址', trigger: ['blur', 'change'] }],
        jdOpenType: [{ required: true, message: '请选择业务类型', trigger: 'change' }]
      },
    }
  },

  computed: {
    // 企业信息
    corpInfo() {
      return this.$store.state?.user?.corpInfo
    },
  },

  watch: {
    // 监听本地缓存开户信息对象变化
    ruleForm: {
      deep: true,
      handler(val) {
        user.setOpenAccount(val)
      }
    },
    // 监听法人身份证是否长期有效
    'ruleForm.legalIdentityCardLongTerm': {
      handler(val) {
        if (val) {
          this.ruleForm.legalIdentityCardEndDate = '2099-12-31'
        } else {
          this.ruleForm.legalIdentityCardEndDate = ''
        }
        this.$refs.ruleForm.validateField('legalIdentityCardEndDate')
      }
    }
  },

  created() {
    // 获取本地缓存开户
    this.openAccount = user.getOpenAccount()
    if (this.openAccount) {
      // 法人身份证是否长期有效,0-否,1-是，非空字段
      this.openAccount.legalIdentityCardLongTerm = !!this.openAccount.legalIdentityCardLongTerm
    }
    this.ruleForm = Object.assign(this.ruleForm, this.openAccount)
    // 身份证类型默认 身份证
    this.ruleForm.legalIdentityCardType = this.ruleForm.legalIdentityCardType || IDENTITY_CARD_TYPE.personal.id
    // 业务类型默认 1
    this.ruleForm.jdOpenType = 1

    // 开户资料页面，业务类型中，需要特殊处理，此时需要隐藏“银票秒贴”类型
    if (this.openGlobalInfo.realNameAuthType === 0) {
      this.jdOpenTypeOptions = this.jdOpenTypeOptions.filter(item => item.value !== 2)
    }
  },

  methods: {

    // 身份证开始日期改变监听
    legalIdentityCardFromDateChange() {
      this.$refs.ruleForm.validateField('legalIdentityCardEndDate')
    },

    // 点击获取人脸识别二维码
    async getFaceRecognition() {
      const { companyName, companyCreditCode, legalName, legalIdentityCard } = this.ruleForm
      if (!companyName) {
        return this.$message({
          message: '请输入企业名称',
        })
      }
      if (!companyCreditCode) {
        return this.$message({
          message: '请输入统一社会信用代码',
        })
      }
      if (!legalName) {
        return this.$message({
          message: '请输入法人姓名',
        })
      }
      if (!legalIdentityCard) {
        return this.$message({
          message: '请输入法人身份证号码',
        })
      }
      if (!validateIDNumber(legalIdentityCard)) {
        return this.$message({
          message: '输入法人证件号格式不对',
        })
      }

      if (await this.checkedFaceResult()) {
        return this.$message.success({
          message: '您已认证成功',
        })
      }

      this.$refs.faceRecognitionRef.init({
        companyName,
        companyCreditCode,
        legalName,
        legalIdentityCard
      })
    },

    // 上一步
    prev() {
      // 初始化风控流水号
      setSerialNos({ no: '', step: this.legalStep, isNext: false })
      this.next({
        step: 1,
        type: 'prev',
        legalStep: this.legalStep - 1
      })
    },

    // 查询人脸识别认证结果
    async checkedFaceResult() {
      const { companyName, companyCreditCode, legalName, legalIdentityCard } = this.ruleForm
      const res = await openAccountApi.postQueryFaceResult({
        companyName,
        companyCreditCode,
        legalName,
        legalIdentityCard
      })
      // 0-未认证，1-认证失败，2-认证成功, 3-认证过期
      return res === 2
    },

    // 下一步
    submitForm(formName) {
      this.$refs[formName].validate(async valid => {
        if (valid) {
          this.legalEmailError = '' // 法人邮箱错误提示
          // 判断是否同一个企业  @return 0-未重复，1-统一营业执照编码重复，2-企业名称重复，3-法人邮箱重复，4-统一营业执照编码不允许修改，5-企业名称不允许修改
          let res = await openAccountApi.getJudgeRepeatCorp({
            companyCreditCode: this.ruleForm.companyCreditCode,
            legalEmail: this.ruleForm.legalEmail,
            companyName: '',
            isRealName: this.isRealNameStep
          })

          if (res && res.length > 0) {
            let message = ''
            for (let index = 0; index < res.length; index++) {
              const element = res[index]
              message += `${this.errorNameTip(element)}，`
            }
            if (message) {
              return this.$message.error(message.replace(/(，)$/, '。'))
            }
          }

          // 人脸识别活体检测
          if (!(await this.checkedFaceResult())) {
            return this.$confirm('请先完成法定代表人人脸识别活体检测。', '提示', {
              type: 'warning',
              showClose: false,
              iconPosition: 'title',
              cancelButtonText: '稍后处理',
              confirmButtonText: '获取人脸识别二维码',
            })
              .then(() => {
                this.getFaceRecognition()
              })
          }

          // 实名法人信息三要素验证
          await openAccountApi.verifyLegalThreeElements({
            idCard: this.ruleForm.legalIdentityCard,
            realName: this.ruleForm.legalName,
            mobile: this.ruleForm.legalPhone,
            idCardStartDate: this.ruleForm.legalIdentityCardFromDate,
            idCardEndDate: this.ruleForm.legalIdentityCardEndDate,
            idCardLongTerm: this.ruleForm.legalIdentityCardLongTerm ? 1 : 0
          })

          // 风控校验
          const riskData = await openAccountApi.checkLegalInfo({
            legalIdentityCard: this.ruleForm.legalIdentityCard,
            legalName: this.ruleForm.legalName,
            legalIdentityCardEndDate: this.ruleForm.legalIdentityCardEndDate,
            companyCreditCode: this.ruleForm.companyCreditCode,
            companyName: this.ruleForm.companyName,
          })
          if (riskData.result === 1) return riskManagementTips(riskData)
          // 风控校验通过设置风控流水号
          setSerialNos({ no: riskData.serialNo, step: this.legalStep, isNext: true })

          const openAccount = user.getOpenAccount()
          user.setOpenAccount(Object.assign(this.ruleForm, { verifyCode: openAccount.verifyCode, serialNos: openAccount.serialNos }))

          this.next({
            step: 1,
            type: 'next',
            legalStep: this.legalStep + 1
          })

          // this.upcheck()
        } else {
          return this.$message('存在未填写信息或填写有误，请检查并完善表单内容')
        }
      })
    },

    // 错误名称提示
    errorNameTip(res) {
      let str = ''
      if (res === 3) {
        str = '法人邮箱已存在，请重新输入'
        this.legalEmailError = str
      }
      return str
    },
  }
}
</script>
