<!-- 大股东信息 -->
<style lang='scss' scoped>
@import "../account.scss";

.top-box {
  background-color: $color-FFFFFF;
}

.checkbox-box {
  display: flex;
  align-items: center;
  padding-bottom: 16px;

  .label {
    margin-top: 2px;
    margin-right: 16px;
    font-size: 16px;
    font-weight: bold;
  }
}
</style>

<template>
  <main class="stockholder-page">
    <div class="enterprise-info-box">
      <div class="left">
        <Guide :legal-step="legalStep" />
      </div>
      <div class="right">
        <section class="top-box">
          <div class="checkbox-box">
            <!-- <span class="label">{{ infoObj.prefix }}</span> -->
            <el-checkbox v-model="infoObj.equalLegal" label="同法人" @change="change" />
            <span class="describe-txt">（企业中持股比例最高的股东信息）</span>
          </div>

          <PersonForm ref="personFormRef" :info-obj="infoObj" />
        </section>
      </div>
    </div>

    <div class="footer">
      <div class="btn-box">
        <el-button
          size="large"
          type="primary"
          border
          @click="prev"
        >
          上一步
        </el-button>
        <el-button size="large" type="primary" @click="submitForm">下一步</el-button>
      </div>
    </div>
  </main>
</template>

<script>
import PersonForm from './person-form.vue'
import user from '@/utils/user' // 用户对象
import { STOCKHOLDER_CARD_TYPE, riskManagementTips, setSerialNos } from '../options'
import Guide from './enterprise-guide.vue' // 企业信息左侧导航指引
import openAccountApi from '@/apis/open-account' // 开户接口

const infoObjInit = {
  equalLegal: false, // 经办人是否同法人,0-否,1-是
  name: '', // 名称
  identityCardFromDate: '', // 开始日期
  identityCardEndDate: '', // 结束日期
  identityCardLongTerm: false, // 否长期有效 0-否,1-是
  phone: '', // 手机号码
  householdAddress: '', // 地址
  identityCard: '', // 身份证号码
  identityCardFrontUrl: '', // 身份证正面
  identityCardBackUrl: '', // 身份证背面
}

export default {
  name: 'stockholder-page',
  components: {
    PersonForm,
    Guide

  },

  props: {
    legalStep: Number,
    // 下一步
    next: {
      type: Function,
      require: true
    }
  },

  data() {
    return {
      openAccount: null, // 本地缓存开户信息
      infoObj: { ...infoObjInit },
      openAccountApi
    }
  },

  created() {
    // 获取本地缓存开户
    this.openAccount = user.getOpenAccount()
    this.infoObj.prefix = '大股东' // 前缀
    this.infoObj.equalLegal = this.openAccount.stockholderEqualLegal // 大股东是否同法人,0-否,1-是
    this.infoObj.cardType = this.openAccount.stockholderCardType || STOCKHOLDER_CARD_TYPE.personal.id // 大股东证件类型，101-身份证，201-营业执照
    this.infoObj.name = this.openAccount.stockholderName || '' // 名称
    this.infoObj.identityCardFromDate = this.openAccount.stockholderIdentityCardFromDate || '' // 开始日期
    this.infoObj.identityCardEndDate = this.openAccount.stockholderIdentityCardEndDate || '' // 结束日期
    this.infoObj.identityCardLongTerm = !!this.openAccount.stockholderIdentityCardLongTerm // 否长期有效 0-否,1-是
    this.infoObj.phone = this.openAccount.stockholderPhone || '' // 手机号码
    this.infoObj.householdAddress = this.openAccount.stockholderHouseholdAddress || '' // 地址
    this.infoObj.identityCard = this.openAccount.stockholderIdentityCard // 身份证号码
    this.infoObj.identityCardFrontUrl = this.openAccount.stockholderIdentityCardFrontUrl || '' // 身份证正面
    this.infoObj.identityCardBackUrl = this.openAccount.stockholderIdentityCardBackUrl || '' // 身份证背面
    this.infoObj.businessLicenseUrl = this.openAccount.stockholderBusinessLicenseUrl || this.openAccount.stockholderIdentityCardFrontUrl || '' // 营业执照
  },

  methods: {
    // 复选框 同法人 监听改变
    change(v) {
      if (v) {
        this.infoObj.equalLegal = true // 经办人是否同法人,0-否,1-是
        this.infoObj.cardType = STOCKHOLDER_CARD_TYPE.personal.id // 大股东证件类型，101-身份证，201-营业执照
        this.infoObj.name = this.openAccount.legalName || '' // 名称
        this.infoObj.identityCardFromDate = this.openAccount.legalIdentityCardFromDate || '' // 开始日期
        this.infoObj.identityCardEndDate = this.openAccount.legalIdentityCardEndDate || '' // 结束日期
        this.infoObj.identityCardLongTerm = !!this.openAccount.legalIdentityCardLongTerm // 否长期有效 0-否,1-是
        this.infoObj.phone = this.openAccount.legalPhone || '' // 手机号码
        this.infoObj.householdAddress = this.openAccount.legalHouseholdAddress || '' // 地址
        this.infoObj.identityCard = this.openAccount.legalIdentityCard // 身份证号码
        this.infoObj.identityCardFrontUrl = this.openAccount.legalIdentityCardFrontUrl || '' // 身份证正面
        this.infoObj.identityCardBackUrl = this.openAccount.legalIdentityCardBackUrl || '' // 身份证背面
      } else {
        Object.assign(this.infoObj, infoObjInit)
        this.$nextTick().then(() => {
          this.$refs.personFormRef.clearValidate()
        })
      }
    },
    // 上一步
    prev() {
      // 初始化风控流水号
      setSerialNos({ no: '', step: this.legalStep, isNext: false })
      this.next({
        step: 1,
        type: 'prev',
        legalStep: this.legalStep - 1
      })
    },

    // 下一步
    async submitForm() {
      if (await this.$refs.personFormRef.validate()) {
        this.openAccount = user.getOpenAccount()
        // 风控校验
        const {
          companyName,
          stockholderName,
          stockholderCardType,
          stockholderIdentityCard,
          stockholderIdentityCardEndDate,
          companyCreditCode
        } = this.openAccount
        const riskData = await openAccountApi.checkStockholderInfo({
          companyName,
          stockholderName,
          stockholderCardType,
          stockholderIdentityCard,
          stockholderIdentityCardEndDate,
          companyCreditCode,
        })
        if (riskData.result === 1) return riskManagementTips(riskData)
        // 风控校验通过设置风控流水号
        setSerialNos({ no: riskData.serialNo, step: this.legalStep, isNext: true })

        this.next({
          step: 1,
          type: 'next',
          legalStep: this.legalStep + 1
        })
      }
    },
  },
}
</script>
