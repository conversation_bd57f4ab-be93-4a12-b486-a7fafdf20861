<!-- 流程步骤组件 -->
<style lang="scss" scoped>
.step-warp {
  display: flex;
  justify-content: space-between;
  padding-bottom: 16px;

  .item {
    display: flex;
    align-items: center;
    flex: 1;

    .icon-box {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 4px;
      border: 1px solid;
      border-radius: 50%;
      width: 28px;
      height: 28px;
      font-weight: 500px;

      .erp-wancheng {
        color: $color-FFFFFF;
      }

      .cheng<PERSON><PERSON>-<PERSON><PERSON><PERSON> {
        font-size: 28px;
        color: $--color-primary;
      }

      span {
        font-size: 14px;
        font-weight: 500;
      }
    }

    .title {
      font-size: 14px;
      font-weight: 400;
      color: $color-text-regular;
      line-height: 20px;
    }

    .line {
      flex: none; /* 取消占比分配 让线条占剩余全部位置 */
      margin: 0 10px;
      border: 1px dashed;
      width: 15%;
      flex-grow: 1;
    }
  }
}

.flex-type {
  flex: inherit !important;
}

.current {
  .icon-box {
    border-color: $--color-primary !important;

    span {
      color: $--color-primary;
    }
  }

  .title {
    font-weight: 500 !important;
    color: $--color-primary !important;
  }

  .line {
    border-color: $color-D9D9D9 !important;
  }
}

.before {
  .icon-box {
    border: none !important;

    // background: $--color-primary;
  }

  .title {
    color: $color-text-primary !important;
  }

  .line {
    border-color: $--color-primary !important;
  }
}

.after {
  .icon-box {
    border-color: $color-D9D9D9 !important;

    span {
      color: $color-text-regular;
    }
  }

  .line {
    border-color: $color-D9D9D9 !important;
  }
}
</style>

<template>
  <div v-if="stepItem" class="step-warp">
    <template v-for="(item, index) in stepItem">
      <!-- activeStep === index 当前流程； activeStep > index 已过的流程； activeStep < index 未过的流程 -->
      <div :key="index" :class="['item', index === stepItem.length - 1 && 'flex-type', activeStep === index && 'current', activeStep > index && 'before', activeStep < index && 'after', (stageStatus === 3 || isSignSuccess) && 'before']">
        <div class="icon-box">
          <icon v-if="activeStep > index || stageStatus === 3 || isSignSuccess" type="chengjie-duihao" />
          <span v-else>0{{ index + 1 }}</span>
        </div>
        <span class="title">{{ item }}</span>
        <span v-if="index !== stepItem.length - 1" class="line" />
      </div>
    </template>
  </div>
</template>

<script>
export default {
  name: 'step',
  props: {
    activeStep: {
      type: Number,
      default: 0
    },
    // 状态
    stageStatus: {
      type: Number,
      default: 0
    },
    stepItem: {
      type: Array
    },
    // 渠道签约 是否签约成功
    isSignSuccess: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {

    }
  },
}
</script>
