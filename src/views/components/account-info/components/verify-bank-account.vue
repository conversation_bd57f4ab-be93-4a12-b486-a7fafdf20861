<!-- 验证银行账户 -->
<style lang="scss" scoped>
.verify-bank-account {
  .main {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;

    .left {
      padding: 16px;
      width: 462px;
      background-color: $color-FFFFFF;

      .subtitle {
        color: $color-text-secondary;
        line-height: 22px;
      }

      .content {
        margin-bottom: 10px;
        font-size: 16px;
        line-height: 24px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .companyName {
        font-weight: 600;
      }
    }

    .right {
      padding: 16px;
      width: 462px;
      background-color: $color-FFFFFF;

      .tip-box {
        margin-bottom: 12px;
        font-size: 16px;
      }
    }
  }

  .text-right {
    ::v-deep.el-input__inner {
      text-align: right;
    }
  }

  ::v-deep {
    .el-form-item {
      margin-bottom: 0 !important;
    }
  }

  .remain-count-box {
    display: inline-block;
    margin-right: 8px;
    font-size: 16px;
    color: $color-text-regular;

    .info-circle {
      font-size: 18px;
      color: $--color-warning;
    }

    .num {
      font-weight: bold;
      color: $--color-primary;
    }
  }
}
</style>

<template>
  <section class="verify-bank-account">
    <div class="main">
      <div class="left">
        <div class="title-left-border">账户信息</div>
        <div class="subtitle">企业名称</div>
        <div class="content companyName">{{ bankInfo.bankAccountName }}</div>
        <div class="subtitle">开户行</div>
        <div class="content">{{ bankInfo.bankBranchName }}</div>
        <div class="subtitle">银行账号</div>
        <div class="content">{{ bankInfo.bankAccount }}</div>
      </div>
      <div class="right">
        <div class="title-left-border">小额打款验证</div>
        <WarnContent class="tip-box">
          银行已向<span class="emphasis">左侧账户</span>转入了随机金额的打款(0.01 - 0.99 元)，请登录网银查询打款金额并填入右边输入框进行验证。
        </WarnContent>
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          class="form"
          :inline-message="true"
        >
          <el-form-item label="验证金额" prop="verifyAmount" :error="errorMsg">
            <el-input
              v-model="ruleForm.verifyAmount"
              placeholder="请输入验证金额"
              type="number"
              class="text-right"
            >
              <template slot="append">元</template>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="footer">
      <div class="btn-box">
        <div v-if="remainCount || remainCount === 0" class="remain-count-box">
          <icon class="info-circle" type="sdicon-info-circle" /> 还剩 <span class="num">{{ remainCount }}</span> 次验证机会
        </div>
        <el-button
          v-waiting="['put::loading::/corpOpenInfo/verifyJdAmount']"
          size="large"
          type="primary"
          @click="submitForm('ruleForm', $event)"
        >
          验证金额
        </el-button>
      </div>
    </div>
    <ContactService ref="contactService" :title="title" sub-title="请联系客服进行处理" />
  </section>
</template>

<script>
import WarnContent from '@/views/components/common/warn-content.vue' // 警告文本
import openAccountApi from '@/apis/open-account' // 开户接口
import ContactService from '@/views/components/common/contact-service/contact-service.vue' // 联系客服
import { REFRESH_ENDORSEMENT } from '@/event/modules/site'

export default {
  name: 'verify-bank-account',
  components: {
    WarnContent,
    ContactService,
  },

  props: {
    // 下一步
    next: {
      type: Function,
      require: true
    }
  },

  data() {
    // 自定义手机号码验证规则
    const checkVerifyAmount = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入验证金额'))
      }
      const min = 0.01
      const max = 0.99
      if (value < min || value > max) {
        return callback(new Error('验证金额错误，请重新输入，范围是0.01 - 0.99'))
      }
      callback()
    }

    return {
      bankInfo: {}, // 银行信息
      ruleForm: {
        verifyAmount: '', // 验证金额
      },
      rules: { // 验证规则
        verifyAmount: [{ required: true, validator: checkVerifyAmount, trigger: 'blur' }],
      },
      errorMsg: '', // 错误提示
      remainCount: '', // 验证剩余次数
      title: '', // 联系客服提示title
    }
  },

  created() {
    this.getCorpOpenInfoBank()
  },

  methods: {
    // 查询商户开户的银行信息
    async getCorpOpenInfoBank() {
      this.bankInfo = await openAccountApi.getCorpOpenInfoBank()
      // 验证剩余次数
      if (this.bankInfo?.jdCheckAmountRemainCount) {
        this.remainCount = this.bankInfo.jdCheckAmountRemainCount
      }
    },

    // 验证金额
    submitForm(formName, event) {
      if (event.target.nodeName === 'SPAN' || event.target.nodeName === 'I') {
        event.target.parentNode.blur()
      } else {
        event.target.blur()
      }
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.upcheck()
        } else {
          return false
        }
      })
    },

    // 验证金额
    async upcheck() {
      this.errorMsg = ''
      const res = await openAccountApi.putVerifyJdAmount(this.ruleForm.verifyAmount)
      if (res.verify) {
        this.next({
          step: 3,
        })
        return
      }

      if (res.checkAmountFailType === 1) {
        this.title = '今日输入次数已达上限！'
        this.contactService()
      } else if (res.checkAmountFailType === 2) {
        this.title = '小额打款已过验证期！'
        this.contactService()
      } else {
        this.errorMsg = '验证金额错误，请重新输入'
      }
      this.remainCount = res.remainCount || 0

      // 刷新背书账户列表
      this.$event.emit(REFRESH_ENDORSEMENT)
    },

    // 联系客服
    contactService() {
      this.$refs.contactService.init()
    },
  }
}
</script>
