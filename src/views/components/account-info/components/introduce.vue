<!-- 开户介绍 -->
<style lang="scss" scoped>
.open-account-introduce {
  padding: 6px 167px 24px;

  .title {
    margin-bottom: 12px;
    font-size: 20px;
    line-height: 28px;
    color: $color-text-primary;
  }

  .focused {
    font-size: 24px;
  }

  .flow {
    display: block;
    margin: 30px auto 0;
    width: 586px;
    height: 318px;
  }

  .at-once-btn {
    display: block;
    margin: 36px auto 0;
  }
}
</style>

<template>
  <section class="open-account-introduce">
    <h2 class="title">为了后续 <span class="focused text-bold text-primary">安全顺利</span> 地交易票据，您需要开通电子交易账户。</h2>
    <div class="paragraph">
      <label class="num">1</label>买卖双方都需要开通电子交易账户，才可获得银行提供的资金监管服务，确保<span class="text-primary">交易过程中</span>的<span class="text-primary">资金安全，解决“谁先背票谁先打款”的信任问题</span>；
    </div>
    <div class="paragraph">
      <label class="num">2</label>该账户由<span class="text-primary">银行</span>监管，只有账户持有者才可发起交易，交易过程中<span class="text-primary">资金的流转</span>也仅在<span class="text-primary">银行系统内部</span>进行，资金安全有保障。
    </div>
    <img class="flow" src="https://oss.chengjie.red/web/imgs/open-account/flow-qql.svg">
    <el-button
      class="at-once-btn"
      type="primary"
      size="large"
      width="312"
      @click="atOnce"
    >
      马上开户
    </el-button>
  </section>
</template>

<script>
import mixpanel from '@/utils/mixpanel' // 埋点对象

export default {
  name: 'open-account-introduce',
  props: {
    // 马上开户
    open: {
      type: Function,
      require: true
    }
  },

  methods: {
    // 马上开户
    atOnce() {
      this.open()
      mixpanel.goOpenAccountClick()
    }
  }
}
</script>
