<!-- 开通电子交易账户弹窗 -->
<style lang="scss" scoped>
.open-account {
  .empty-box {
    height: 520px;
    background-color: $color-F2F2F2;
  }
}

::v-deep {
  .open-account {
    // 弹窗
    margin-top: 10vh !important;

    .el-dialog__body {
      background-color: $color-F2F2F2;
    }

    // step流程
    .el-step {
      width: 20%;
    }

    .el-steps--horizontal {
      margin-bottom: 12px;
    }

    .el-step__title {
      line-height: 24px;
      margin-top: 4px;
      margin-bottom: 2px;

      &.is-process,
      &.is-finish {
        font-weight: normal;
        color: $color-text-primary;
      }

      &.is-wait {
        color: $color-text-secondary;
      }

      &.is-error {
        color: $--color-primary;
      }
    }

    .el-step__head .el-step__icon {
      border: none;
      background: transparent;
    }

    .el-step__icon.is-text {
      border-width: 1px;
    }

    .el-step__head.is-finish {
      .el-step__line {
        background-color: $--color-primary;
      }
    }

    .el-step__head.is-error {
      .el-step__icon.is-text {
        &::after {
          top: -2px;
        }

        i::before {
          content: "";
        }
      }
    }

    .el-step__head {
      .el-step__line {
        right: -42%;
        left: 58%;
        height: 3px;
        background-color: $color-D9D9D9;
      }

      &.is-wait {
        .el-step__icon.is-text {
          position: relative;
          z-index: 1;
          border: none;
          background-color: $color-D9D9D9;

          &::after {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 2;
            border-radius: 50%;
            width: 100%;
            height: 100%;
            background-color: $color-D9D9D9;
            content: "";
          }
        }
      }
    }

    // step流程 end

    // 段落
    .paragraph {
      margin-bottom: 10px;
      padding-left: 1em;
      font-size: 14px;
      text-indent: -1em;
      color: $color-text-primary;
      line-height: 24px;

      .num {
        position: relative;
        display: inline-block;
        width: 1em;
        text-indent: 0;

        &::after {
          position: absolute;
          right: 0;
          content: ".";
        }
      }
    }

    // 标题
    .title-left-border {
      position: relative;
      margin: 0 0 8px !important;
      padding-left: 12px;
      font-size: 16px;
      font-weight: 600;
      line-height: 22px;

      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: $line-bar-width;
        height: 16px;
        background: $--color-primary;
        transform: translateY(-50%);
        content: "";
      }
    }

    // form表单相关
    .g-required {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .el-checkbox-group {
      .el-checkbox {
        margin-right: 16px;

        &:last-child {
          margin-right: 0;
        }
      }
    }

    .el-form-item {
      display: flex;
      margin-bottom: 12px;
      flex-flow: column;
    }

    .el-select,
    .el-cascader {
      width: 100%;
    }

    .el-form-item__label {
      margin-bottom: 4px;
      width: auto !important;
      height: 22px;
      text-align: left;
      color: $color-text-secondary;
      line-height: 22px;

      &::before {
        font-weight: 600;
      }
    }

    .el-form-item__content {
      margin-left: 0 !important;
      width: 100%;
    }

    .el-form-item__error--inline {
      display: inherit;
      margin-left: 0;
      line-height: 12px;
    }

    .el-input__inner,
    .el-textarea__inner {
      padding-left: 12px;
      font-size: 16px;
    }

    .el-textarea__inner {
      height: 100px;
    }

    .el-input--prefix .el-input__inner {
      padding-left: 30px;
    }

    .el-checkbox__input,
    .el-checkbox__label {
      font-weight: normal;
      vertical-align: middle;
    }

    .el-date-editor.el-input,
    .el-date-editor.el-input__inner {
      width: 100%;
    }

    .el-checkbox .el-checkbox__input.is-checked + .el-checkbox__label {
      color: $color-text-primary;
    }

    .multiseriate {
      display: flex;

      >div {
        flex: 1;

        &:last-child {
          margin-left: 12px;
        }
      }

      width: 100%;
    }

    // 协议
    .agreement {
      line-height: 22px;
      display: flex;
      flex-wrap: nowrap;

      .link {
        @include example-underline;
      }
    }

    // 底部按钮
    .footer {
      display: flex;
      align-items: center;
      margin-top: 12px;

      .btn-box {
        flex: 1;
        text-align: right;
      }
    }
  }
}
</style>

<template>
  <el-dialog
    title="开通电子交易账户"
    width="984px"
    append-to-body
    custom-class="open-account"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <!-- 空白loading -->
    <div
      v-if="isShowIntroduce === 1"
      v-loading="loading"
      class="empty-box"
      element-loading-background="rgba(0, 0, 0, 0)"
    />
    <!-- 流程步骤 -->
    <Step
      v-show="!isShowIntroduce"
      :active-step="activeStep"
      :stage-status="stageStatus"
      :step-item="stepItem"
    />

    <section v-if="!isShowIntroduce">
      <!-- 开户所需文件 -->
      <UploadImg
        v-if="activeStep === 0"
        :is-real-name-step="isRealNameStep"
        :next="next"
      />
      <!-- 完善企业信息 -->
      <EnterpriseInformation
        v-if="activeStep === 1 && legalStep === 0"
        :is-real-name-step="isRealNameStep"
        :next="next"
        :legal-step="legalStep"
        :payment-channel="openGlobalInfo.reAuthPaymentChannel"
      />
      <!-- 法人信息 -->
      <LegalPerson
        v-if="activeStep === 1 && legalStep === LEGAL_STEP.legalRepresentative.step"
        :is-real-name-step="isRealNameStep"
        :next="next"
        :legal-step="legalStep"
      />
      <!-- 被授权人（经办人）基本信息 -->
      <AgentIdentity
        v-if="activeStep === 1 && legalStep === LEGAL_STEP.agentEqualLegal.step"
        :next="next"
        :legal-step="legalStep"
      />
      <!-- 受益人信息 -->
      <Beneficiary
        v-if="activeStep === 1 && legalStep === LEGAL_STEP.beneficiaryEqualLegal.step"
        :is-real-name-step="isRealNameStep"
        :next="next"
        :legal-step="legalStep"
      />
      <!-- 大股东信息 -->
      <Stockholder
        v-if="activeStep === 1 && legalStep === LEGAL_STEP.stockholderEqualLegal.step"
        :next="next"
        :legal-step="legalStep"
      />
      <!-- 实际控制人 -->
      <ActualController
        v-if="activeStep === 1 && legalStep === LEGAL_STEP.actualController.step"
        :next="next"
        :legal-step="legalStep"
      />
      <!-- 开通电子账户 -->
      <Success v-if="activeStep === 3" :next="next" :close="close" />
      <!-- 补充资料审核  activeStep === 2 -->
      <DataReview
        v-if="activeStep === 2"
        :stage-status="stageStatus"
        :fail-reason="failReason"
        :close="close"
        :next="next"
        :is-real-name-step="isRealNameStep"
      />
    </section>
  </el-dialog>
</template>

<script>
/* eslint-disable no-magic-numbers */
import { SITE_OPEN_ACCOUNT_CLOSE, REAL_NAME_CERTIFICATION_CLOSE } from '@/event/modules/site' // 监听事件常量
import UploadImg from './components/upload-img.vue' // 开户所需文件
import EnterpriseInformation from './components/enterprise-information.vue' // 完善企业信息
import AgentIdentity from './components/agent-identity.vue' // 被授权人（经办人）基本信息
import Beneficiary from './components/beneficiary.vue' // 受益人信息
import Stockholder from './components/stockholder.vue' // 大股东信息
import ActualController from './components/actual-controller.vue' // 实际控制人信息
import Success from './components/success.vue' // 开通电子账户
import openAccountApi from '@/apis/open-account' // 开户接口
import user from '@/utils/user' // 用户对象
import { LEGAL_STEP, objEveryValue } from './options'// 不同身份同法人步骤
import Step from './components/step.vue' // 流程步骤组件
import LegalPerson from './components/legal-person.vue' // 法人信息
import DataReview from './components/data-review.vue' // 资料审核

export default {
  name: 'open-account',

  components: {
    UploadImg, // 开户所需文件
    EnterpriseInformation, // 完善企业信息
    AgentIdentity, // 被授权人（经办人）基本信息
    Beneficiary, // 受益人信息
    Stockholder, // 大股东信息
    ActualController, // 实际控制人信息
    Success, // 开通电子账户
    Step,
    LegalPerson, // 法人信息
    DataReview // 补充资料审核
  },
  // 向所有子组件注入依赖变量
  provide() {
    return {
      openGlobalInfo: this.openGlobalInfo,
    }
  },

  data() {
    return {
      visible: false, // 是否显示弹窗
      loading: false, // 加载中
      openAccount: {}, // 本地缓存开户信息
      isShowIntroduce: 1, // 是否显示开户介绍（默认显示空白的loading页）
      // isSubmit: false, // 是否已提交开户资料
      activeStep: 0, // 开户流程步骤 1-完善企业信息,2-补充资料审核,3-电子账户开通
      legalStep: 0, // 0, 1:法人信息 2:被授权人或经办人同法人, 3: 大股东同法人, 4: 受益人同法人，5：实际控制人
      LEGAL_STEP,
      stageStatus: 1, // 1-处理中，2-失败，3-成功
      failReason: '', // 审核失败原因
      realNameHandlerType: 0, // 企业实名处理人类型：0-用户，1-客服
      openGlobalInfo: {
        // 实名类型，0-首次实名，1-收到实名预警通知后重新实名，2-实名失效后重新实名，3-开通失败的支付渠道进行重新实名，非空字段
        realNameAuthType: 0,
        // 如果realNameAuthType=3需要传值，重新实名需要开通的支付渠道类型，以，隔开,1-智付亿联、2-智付百信、3-智付连连、4-智付合利宝
        reAuthPaymentChannel: '',
        corpOpenInfoUpdate: false, // 是否更新商户开户信息
      },
      stepItem: ['补充证照文件', '完善企业信息', '补充资料审核', '电子账户开通'],
      isRealNameStep: 0 // 0=>电子交易账户开通流程 1=>实名认证流程
    }
  },

  computed: {
    // 企业信息
    corpInfo() {
      return this.$store.state?.user?.corpInfo
    },
  },
  created() {
    this.$event.on(SITE_OPEN_ACCOUNT_CLOSE, () => {
      this.visible = false
    })
  },

  methods: {
    // 初始化
    /**
     * @param {Object} option
     * realNameAuthType: 0, // 实名类型 ，0-首次实名，1-收到实名预警通知后重新实名，2-实名失效后重新实名，3-开通失败的支付渠道进行重新实名，非空字段
     * realNameAuthType: 支付渠道类型，如果realNameAuthType=3需要传值，重新实名需要开通的支付渠道类型，以，隔开,1-智付亿联、2-智付百信、3-智付连连、4-智付合利宝
     * startOver：默认等于true时，代表去请求拿开户阶段
     * from: 从哪里点击进来的  reopening(渠道开通失败，点击重新开通)
     */
    async init(option) {
      this.$event.emit(REAL_NAME_CERTIFICATION_CLOSE) // 关闭实名流程框
      this.visible = true // 显示弹窗
      this.isShowIntroduce = 1 // 空白loading
      // this.isSubmit = false
      this.activeStep = 0
      this.legalStep = 0
      this.stageStatus = 1
      this.failReason = '' // 审核失败原因
      this.realNameHandlerType = 0
      // 重新实名
      this.openGlobalInfo.realNameAuthType = option?.realNameAuthType || 0 // 实名类型
      this.openGlobalInfo.reAuthPaymentChannel = option?.reAuthPaymentChannel || '' // 重新实名需要开通的支付渠道类型
      this.openGlobalInfo.corpOpenInfoUpdate = option?.corpOpenInfoUpdate || false // 是否更新商户开户信息
      this.openGlobalInfo.from = option?.from || '' // 从哪里点击进来的

      // 查询商户开户信息
      if (this.corpInfo?.id) {
        await this.getCorpOpenInfo()
      }
      // 首次实名，或者startOver : 默认等于true时，代表去请求拿开户阶段
      if (option?.startOver) {
        await this.getCorpOpenInfoStage()
      } else {
        this.isShowIntroduce = false
      }
    },

    // 查询商户开户信息
    async getCorpOpenInfo() {
      try {
        let res = await openAccountApi.getCorpOpenInfo()
        if (res) {
          res.businessLicenseLongTerm = !!res.businessLicenseLongTerm // 营业执执照是否长期有效,0-否,1-是，非空字段
          res.legalIdentityCardLongTerm = !!res.legalIdentityCardLongTerm // 法人身份证是否长期有效,0-否,1-是，非空字段
          res.agentIdentityCardLongTerm = !!res.agentIdentityCardLongTerm // 经办人身份证是否长期有效,0-否,1-是，非空字段
          res.stockholderIdentityCardLongTerm = !!res.stockholderIdentityCardLongTerm // 大股东身份证是否长期有效,0-否,1-是，非空字段
          res.agentEqualLegal = !!res.agentEqualLegal // 经办人是否同法人,0-否,1-是
          res.beneficiaryEqualLegal = !!res.beneficiaryEqualLegal // 受益人是否同法人,0-否,1-是
          res.stockholderEqualLegal = !!res.stockholderEqualLegal // 大股东是否同法人,0-否,1-是
          // 实际控制人
          if (res.actualController) {
            res.actualController.cardLongTerm = !!res.actualController.cardLongTerm // 证件是否长期有效，0-否，1-是
            res.actualController.controllerEqualLegal = !!res.actualController.controllerEqualLegal // 是否同法人，0-否，1-是
            res.actualController.controllerEqualStockholder = !!res.actualController.controllerEqualStockholder // 是否同股东，0-否，1-是
          }
          // 拼接省市区code
          if (res.provinceCode && res.cityCode && res.districtCode && ![res.provinceName, res.cityName, res.districtName].includes('未知') && ![res.provinceCode, res.cityCode, res.districtCode].includes('000000')) {
            res.provinceCityDistricCodes = [res.provinceCode, res.cityCode, res.districtCode] // 经营省市区code
          } else {
            res.provinceCityDistricCodes = []
          }
          // console.log('查询商户开户信息 res', res)

          // 设置风控流水号集合
          res.serialNos = []
          // 设置到本地缓存
          this.openAccount = Object.assign(this.openAccount, res)
          user.setOpenAccount(this.openAccount)
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('查询商户开户信息错误 =>', error)
      }
    },

    // 查询开户流程阶段
    async getCorpOpenInfoStage() {
      this.loading = true
      // isRealName 是否实名流程 payChannel 渠道类型
      const res = await openAccountApi.getCorpOpenInfoStage({ isRealName: 0, payChannel: this.openGlobalInfo.reAuthPaymentChannel })

      this.loading = false
      this.isShowIntroduce = false // 默认是1 展示空白loading 渠道开通不需要开户介绍页

      this.stageStatus = res.stageStatus || 1 // 审核状态

      this.failReason = res.failReason // 审核失败原因
      this.realNameHandlerType = res.realNameHandlerType
      if (res.stage === 1) { // 完善企业信息 步骤
        this.activeStep = 1
        return
      } else if (res.stage === 2) { // 补充资料审核
        this.activeStep = 2
        if (this.stageStatus === 3) { // 审核成功
          this.activeStep = 3
        }
        return
      } else if (res.stage === 3) { // 电子账户开通
        this.activeStep = 3
        return
      }
      // 获取本地缓存开户
      this.openAccount = user.getOpenAccount()

      if (this.openAccount) {
        // 是否已填开户所需文件
        this.isFillUploadImg()

        // 判断是否已填完善企业信息
        this.isFillEnterpriseInformation()
      }
    },

    // 判断是否已填开户所需文件
    isFillUploadImg() {
      if (objEveryValue(['businessLicenseUrl', 'legalIdentityCardFrontUrl', 'legalIdentityCardBackUrl'], this.openAccount)) {
        this.activeStep = 1
      }
    },

    // 判断是否已填完善企业信息
    isFillEnterpriseInformation() {
      if (objEveryValue([
        'companyName',
        'companyIndustry',
        'companyCreditCode',
        'businessLicenseFromDate',
        'companyAddress',
        'provinceCityDistricCodes',
        'establishDate', // 成立日期，非空字段
        'holdingType', // 企业控股类型
        'taxOfficeType', // 收税机构类型
        'companyAddress', // 企业地址，非空字段
        'registerOffice', // 登记机关
        'jdOpenType', // 业务类型
        'bcpNo', // 基本户核准号
        'registerCapital', // 企业注册资本(万)
        'legalName',
        'legalIdentityCard',
        'legalPhone',
        'legalIdentityCardFromDate',
        'legalEmail',
        'legalHouseholdAddress',
        'agentName',
        'agentIdentityCard',
        'agentIdentityCardFrontUrl',
        'agentIdentityCardBackUrl',
        'agentIdentityCardFromDate',
        'agentPhone',
        'agentEmail',
        'stockholderName',
        'stockholderIdentityCard',
        'stockholderIdentityCardFrontUrl',
        'stockholderIdentityCardBackUrl',
        'stockholderIdentityCardFromDate',
        'stockholderPhone',
        'stockholderHouseholdAddress',
        'beneficiaryName',
        'beneficiaryIdentityCard',
        'beneficiaryIdentityCardFrontUrl',
        'beneficiaryIdentityCardBackUrl',
        'beneficiaryIdentityCardFromDate',
        'beneficiaryHouseholdAddress',
        'beneficiaryPhone',
      ], this.openAccount)) {
        if (!this.openAccount.agentEqualLegal) { // 经办人是否同法人,0-否,1-是
          if (this.openAccount.agentAuthUrl) {
            this.activeStep = 2
          }
        } else {
          this.activeStep = 2
        }
      }
    },

    // // 马上开户
    // open() {
    //   this.isShowIntroduce = false
    // },

    /**
     * 下一步
     * @param {Object} obj 对象
     * @param {string} step 步骤
     * @param {string} type 类型
     * @param {string} legalStep 同法人步骤
     */
    async next(obj) {
      this.activeStep = obj.step
      if (obj.update) { // 传入如果是重新提交
        this.openGlobalInfo.corpOpenInfoUpdate = true
        this.openGlobalInfo.reAuthPaymentChannel = Number(obj.reAuthPaymentChannel) // 当前需要开通的支付渠道
      }

      switch (obj.step) {
        case 0: // 补充证照文件
          this.stageStatus = 1
          this.openGlobalInfo.realNameAuthType = obj.realNameAuthType || this.openGlobalInfo.realNameAuthType
          this.openGlobalInfo.corpOpenInfoUpdate = this.openGlobalInfo.corpOpenInfoUpdate || obj.update || false
          return
        case 1: // 完善企业信息
          this.stageStatus = 1
          this.openAccount = user.getOpenAccount()
          this.legalStep = obj.legalStep || 0

          if (obj.legalStep > 5) { // 完善企业信息步骤
            this.activeStep = 2
          }
          return
        case 2: // 补充资料审核
          this.stageStatus = 1
          if (obj.resubmit) return
          await this.getCorpOpenInfoStage()
          return
        case 3: // 电子账户开通
          await this.getCorpOpenInfoStage()
          return
        // eslint-disable-next-line no-unused-expressions
        default: ''
      }
    },
    // 关闭窗口
    close() {
      this.visible = false
    }
  }
}
</script>
