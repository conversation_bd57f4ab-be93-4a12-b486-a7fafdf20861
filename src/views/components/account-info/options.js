/* eslint-disable no-unused-vars */
import { Message } from '@shendu/element-ui'
import user from '@/utils/user' // 用户对象
import './account.scss'
// 不同身份同法人步骤
export const LEGAL_STEP = {
  legalRepresentative: {
    step: 1,
    name: '法定代表人'
  },
  agentEqualLegal: {
    step: 2,
    name: '被授权人或经办人同法人'
  },
  beneficiaryEqualLegal: {
    step: 5,
    name: '受益人仅1个且同法人'
  },
  stockholderEqualLegal: {
    step: 3,
    name: '大股东同法人'
  },
  actualController: {
    step: 4,
    name: '实际控制人'
  }
}

// 企业控股类型 选项
export const holdingTypeOptions = [
  {
    value: 'A0100',
    label: '国有控股'
  },
  {
    value: 'A0200',
    label: '集体控股'
  },
  {
    value: 'B0100',
    label: '私人控股'
  },
  {
    value: 'B0200',
    label: '港澳台控股'
  },
  {
    value: 'B0300',
    label: '外商控股'
  },
]

// 收税机构类型 选项
export const taxOfficeTypeOptions = [
  {
    value: 'T01',
    label: '消极非金融机构'
  },
  {
    value: 'T02',
    label: '其他非金融机构'
  }
]

// 企业规模类型 选项
export const scaleTypeOptions = [
  {
    value: 'CS01',
    label: '大型企业'
  },
  {
    value: 'CS02',
    label: '中型企业'
  },
  {
    value: 'CS03',
    label: '小型企业'
  },
  {
    value: 'CS04',
    label: '微型企业'
  },
  // {
  //   value: 'CS00',
  //   label: '其他规模'
  // },
  // {
  //   value: 'CS09',
  //   label: '未知'
  // },
]

// 大股东企业类型
export const STOCKHOLDER_CARD_TYPE = {
  personal: {
    id: 101,
    name: '个人-身份证'
  },
  enterprise: {
    id: 201,
    name: '企业-营业执照'
  },
}
// 大股东企业类型
export const IDENTITY_CARD_TYPE = {
  personal: {
    id: 101,
    name: '身份证'
  },
}

// 企业业务类型
export const jdOpenTypeOptions = [
  { value: 1, label: '票据撮合交易' },
  { value: 2, label: '银票秒贴' },
]

// 风控提示
export const riskManagementTips = errData => {
  let errText = errData.reason.replaceAll('、', ',').split(',')
  let liStr = ''
  errText.forEach(item => {
    liStr += `<li>${item}</li>`
  })
  Message.closeAll()
  Message({
    dangerouslyUseHTMLString: true,
    customClass: 'custom-msg-tips',
    // duration: 0,
    type: 'error',
    message: '<div class="content-warp">'
          + `<span class="title">该企业综合风险审核不通过，详情请咨询您的客户经理！业务流水号：${errData.serialNo}</span>`
          + '<ul class="ul">'
          + `${liStr}`
          + '</ul>'
          + '</div>'
  })
}
// 完善企业信息设置风控流程号 no=>流水号 step=>企业信息风控步骤 isNext=>是否为下一步操作
export const setSerialNos = data => {
  const { no, step, isNext } = data
  const accountInfo = user.getOpenAccount()
  if (!accountInfo.serialNos) { // 本地缓存不存在风控流程号字段就初始化一下
    accountInfo.serialNos = []
  }
  if (isNext) {
    accountInfo.serialNos && accountInfo.serialNos.push(no)
  } else {
    accountInfo.serialNos.splice(step, 1)
  }
  user.setOpenAccount(accountInfo)
}

/**
    * 判断keys数组的字段，在obj中都有值就返回 true, 反之 false
    * <AUTHOR> Chen <<EMAIL>>
    * @version 1.0.0
    * @param {(string | string[])} keys 需要判断的key
    * @param {Object} obj 需要判断的对象，默认开户信息this.openAccount
    * @returns {Boolean} 返回真假
    *
    * @example
    * this.objIsValue('businessLicenseUrl')
    * this.objIsValue(['businessLicenseUrl', 'legalIdentityCardFrontUrl', 'legalIdentityCardBackUrl'])
    */
export const objEveryValue = (keys, obj) => [].concat(keys).every(item => obj[item])

/**
 * 判断keys数组的字段，在obj中任意一个有值就返回 true，反之 false
 * @param {(string | string[])} keys 需要判断的key
 * @param {Object} obj 需要判断的对象，默认开户信息this.openAccount
 * @returns {Boolean} 返回真假
 */
export const objSomeValue = (keys, obj) => [].concat(keys).some(item => obj[item])
