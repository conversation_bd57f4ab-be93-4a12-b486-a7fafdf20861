<!-- 添加银行签收账户弹窗 -->
<style lang="scss" scoped>
.yl-sign-dialog {
  .empty-box {
    height: 520px;
    background-color: $color-F2F2F2;
  }
}

::v-deep {
  .yl-sign-dialog {
    // 弹窗
    margin-top: 10vh !important;

    .el-dialog__body {
      background-color: $color-F2F2F2;
    }

    .title-left-border {
      position: relative;
      margin: 0 0 8px !important;
      padding-left: 12px;
      font-size: 16px;
      font-weight: 600;
      line-height: 22px;

      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: 4px;
        height: 16px;
        background: $--color-primary;
        transform: translateY(-50%);
        content: "";
      }
    }

    // form表单相关
    .g-required {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .el-form-item {
      display: flex;
      margin-bottom: 12px;
      flex-flow: column;
    }

    .el-select {
      width: 100%;
    }

    .el-form-item__label {
      margin-bottom: 4px;
      width: auto !important;
      height: 22px;
      text-align: left;
      color: $color-text-secondary;
      line-height: 22px;

      &::before {
        font-weight: 600;
      }
    }

    .el-form-item__content {
      margin-left: 0 !important;
      width: 100%;
      height: 40px;
    }

    .el-form-item__error--inline {
      position: absolute;
      display: inherit;
      margin-left: 0;
      line-height: 7px;
    }

    .el-input__inner {
      padding-left: 12px;
      font-size: 16px;
    }
  }
}
</style>

<template>
  <el-dialog
    title="开通电子交易账户"
    width="984px"
    append-to-body
    custom-class="yl-sign-dialog"
    :visible.sync="visible"
    :close-on-click-modal="false"
  >
    <Step
      :active-step="activeStep"
      :is-sign-success="stageStatus === 5 ? true : false"
      :step-item="stepItem"
    />

    <div
      v-if="fristJoin"
      v-loading="loading"
      class="empty-box"
      element-loading-background="rgba(0, 0, 0, 0)"
    />

    <section v-if="!fristJoin">
      <!-- 绑定银行账户 -->
      <BindBankAccount
        v-if="activeStep === BANG_SING_STAGE_STEP.BIND_BANK_ACCOUNT.STEP"
        :next="next"
        :stage-status="stageStatus"
        :trader-account-id="traderAccountId"
        :bank-account-name="bankAccountName"
        :re-sign="reSign"
        :close="close"
        :payment-channel="paymentChannel"
        :info="info"
      />
      <!-- 验证银行账户 -->
      <VerifyBankAccount
        v-if="activeStep === BANG_SING_STAGE_STEP.VERIFY_BANK_ACCOUNT.STEP"
        :stage-status="stageStatus"
        :next="next"
        :close="close"
        :trader-account-id="traderAccountId"
        :bank-account-name="bankAccountName"
        :get-sign-stage="getSignStage"
        :check-amount-fail-type="checkAmountFailType"
        :fail-reason="failReason"
        :payment-channel="paymentChannel"
        :sign-payment-flag="signPaymentFlag"
      />

      <!-- 签约成功 -->
      <Success
        v-if="activeStep === BANG_SING_STAGE_STEP.SUCCESS_OPEN.STEP"
        :stage-status="stageStatus"
        :trader-account-id="traderAccountId"
        :fail-reason="failReason"
        :sign-payment-flag="signPaymentFlag"
        :close="close"
        :next="next"
      />
    </section>
  </el-dialog>
</template>

<script>
import BindBankAccount from './bind-bank-account.vue' // 绑定银行账户
import VerifyBankAccount from './verify-bank-account.vue' // 验证银行账户
import userApi from '@/apis/user' // 接口
import {
  BANG_SING_STAGE_STEP, // 开通流程步骤
  BANG_SING_STAGE_STATUS, // 开通流程状态
} from '@/constant' // 常量
import Step from '../components/step.vue' // 流程步骤组件
import Success from './success.vue' // 签约审核中文案展示
import { CHANNEL_SIGN_DIGLOG_CLOSE } from '@/event/modules/site' // 监听事件常量
export default {
  name: 'bank-endorsed-account',

  components: {
    Step,
    BindBankAccount,
    VerifyBankAccount,
    Success
  },

  data() {
    return {
      stepItem: ['绑定银行账户', '验证银行账户', '签约成功'],
      failReason: '', // 审核失败原因
      BANG_SING_STAGE_STEP, // 开通流程步骤
      BANG_SING_STAGE_STATUS, // 开通流程状态
      visible: false,
      loading: true, // 加载中
      stageStatus: 0, // 默认 0=>未提交 初始化填写绑定银行信息页面
      ...this.initFormat()
    }
  },
  created() {
    this.$event.on(CHANNEL_SIGN_DIGLOG_CLOSE, () => {
      this.visible = false
    })
  },

  methods: {
    // 格式化data数据
    initFormat() {
      return {
        info: {},
        reSign: 0, // 是否重新签约，0-不是，1-是
        fristJoin: true, // 第一次进入
        activeStep: 0, // 0-绑定银行账户，1-验证银行账户,2-签约成功
        stageObj: {}, // 签约状态
        bankAccountName: '', // 企业名称
        traderAccountId: '',
        paymentChannel: 7, // 支付渠道类型，7-智付e+
        traderCorpId: '',
        viewStageType: null, // 是否是查看进度
        checkAmountFailType: null, // 验证金额失败原因 1=>次数已达上限 2=>小额打款已过验证期
        signPaymentFlag: 0, // 签约户是否禁用: 0-否；1-是
        isMqtt: false // 是否消息推送打开
      }
    },
    // 初始化
    init(obj) {
      this.reSign = obj.reSign ? 1 : 0
      this.failReason = '' // 审核失败原因
      this.traderAccountId = obj.traderAccountId
      this.paymentChannel = obj.paymentChannel
      this.bankAccountName = obj?.bankAccountName || ''
      this.viewStageType = obj.viewStageType
      this.traderCorpId = obj.traderCorpId
      this.isMqtt = obj.isMqtt
      this.info = obj
      this.visible = true
      if (this.reSign) { // 重新提交
        this.activeStep = 0
        this.stageStatus = 0
        this.fristJoin = false
        return
      }
      // 查询签收流程阶段
      this.getSignStage()
      // 签约银行信息
      // this.getSignBankInfo()
    },

    // 签约银行信息
    async getSignBankInfo() {
      await userApi.getSignBankInfo({ traderAccountId: this.traderAccountId, paymentChannel: this.paymentChannel, traderCorpId: this.traderCorpId })
      // console.log('查询签收流程阶段', res)
    },

    // 查询签收流程阶段
    async getSignStage() {
      // 重新签约不调用接口  0-【查看进度】按钮、1-【查看原因】按钮、2-【重新签约】
      if (this.viewStageType === 1 || this.viewStageType === 0 || this.isMqtt) {
        this.fristJoin = true
        const res = await userApi.channelSignStage({ traderAccountId: this.traderAccountId, paymentChannel: this.paymentChannel })
        // console.log('查询签收流程阶段', res)
        this.stageObj = res
        this.stageStatus = res.stageStatus // 银行账户状态
        this.activeStep = res.signStage// 0-绑定银行账户 1-验证银行账户,2-签约成功
        this.failReason = res.failReason || null
        this.checkAmountFailType = res.checkAmountFailType || null // 验证金额失败原因 1=>次数已达上限 2=>小额打款已过验证期
        this.signPaymentFlag = res.signPaymentFlag || 0
      }
      this.fristJoin = false
    },

    // 下一步
    next(obj) {
      this.viewStageType = obj?.viewStageType
      this.reSign = obj.reSign ? 1 : 0
      if (obj.reSign) { // 重新提交
        this.stageStatus = 0
        this.activeStep = BANG_SING_STAGE_STEP.BIND_BANK_ACCOUNT.STEP
        this.fristJoin = false
        return
      }
      // 查询签收流程阶段
      this.getSignStage()
    },

    // 关闭窗口
    close() {
      this.visible = false
    }
  }
}
</script>
