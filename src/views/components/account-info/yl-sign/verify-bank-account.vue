<!-- 验证银行账户 -->
<style lang="scss" scoped>
.verify-bank-account {
  .main {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;

    .left {
      padding: 16px;
      width: 462px;
      background-color: $color-FFFFFF;

      .subtitle {
        color: $color-text-secondary;
        line-height: 22px;
      }

      .content {
        margin-bottom: 10px;
        font-size: 16px;
        line-height: 24px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .companyName {
        font-weight: 600;
      }
    }

    .right {
      padding: 16px;
      width: 462px;
      background-color: $color-FFFFFF;

      .tip-box {
        margin-bottom: 12px;
        font-size: 16px;
      }
    }
  }

  .text-right {
    ::v-deep.el-input__inner {
      text-align: right;
    }
  }

  ::v-deep {
    .el-form-item {
      margin-bottom: 0 !important;
    }
  }

  .remain-count-box {
    display: inline-block;
    margin-right: 8px;
    font-size: 16px;
    color: $color-text-regular;

    .info-circle {
      font-size: 18px;
      color: $--color-warning;
    }

    .num {
      font-weight: bold;
      color: $--color-primary;
    }
  }

  .footer {
    text-align: center;
  }
}

.btn-box {
  text-align: end;
}
</style>

<template>
  <section class="verify-bank-account">
    <!-- 打款失败 待审核 验证失败 审核驳回 签约户已禁用 -->
    <template v-if="[ENDORSEMENT_STATUS.PAYMENT_FAIL.id, ENDORSEMENT_STATUS.AUDIT.id, ENDORSEMENT_STATUS.EXAMINE_ERROR.id, ENDORSEMENT_STATUS.REJECTED.id].includes(stageStatus) || signPaymentFlag">
      <StatusTemplate
        :stage-status="stageStatus"
        :sign-payment-flag="signPaymentFlag"
        :check-amount-fail-type="checkAmountFailType"
        :close="close"
        :fail-reason="failReason"
      />
      <div v-if=" [ENDORSEMENT_STATUS.REJECTED.id, ENDORSEMENT_STATUS.PAYMENT_FAIL.id, ENDORSEMENT_STATUS.EXAMINE_ERROR.id].includes(stageStatus) || signPaymentFlag" class="footer">
        <el-button
          v-if="signPaymentFlag"
          size="large"
          type="primary"
          @click="close"
        >
          我知道了
        </el-button>
        <template v-else>
          <!-- 1-小额超限 2-小额输入过期验证 隐藏上传凭证按钮 -->
          <el-button
            v-if="![1, 2].includes(checkAmountFailType)"
            size="large"
            type="primary"
            @click="upload"
          >
            上传凭证
          </el-button>
          <el-button
            size="large"
            type="primary"
            @click="back"
          >
            重新签约
          </el-button>
        </template>
      </div>
    </template>
    <!-- 打款成功 -->
    <template v-else-if=" stageStatus === ENDORSEMENT_STATUS.PAYMENT_SUCCESS.id">
      <div class="main">
        <div class="left">
          <div class="title-left-border">账户信息</div>
          <div class="subtitle">企业名称</div>
          <div class="content companyName">{{ bankAccountName }}</div>
          <div class="subtitle">开户行行号</div>
          <div class="content">{{ bankBranchCode }}</div>
          <div class="subtitle">开户行名称</div>
          <div class="content">{{ bankBranchName }}</div>
        </div>
        <div class="right">
          <div class="title-left-border">小额打款验证</div>
          <WarnContent class="tip-box">
            银行已向<span class="emphasis">左侧账户</span>转入了随机金额的打款(0.01 - 0.99 元)，请登录网银查询打款金额并填入右边输入框进行验证。
          </WarnContent>
          <el-form
            ref="ruleForm"
            :model="ruleForm"
            :rules="rules"
            class="form"
            :inline-message="true"
          >
            <el-form-item label="验证金额" prop="verifyAmount" :error="errorMsg">
              <el-input
                v-model="ruleForm.verifyAmount"
                placeholder="请输入验证金额"
                type="number"
                class="text-right"
              >
                <template slot="append">元</template>
              </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="footer">
        <div class="btn-box">
          <div v-if="remainCount || remainCount === 0" class="remain-count-box">
            <icon class="info-circle" type="sdicon-info-circle" /> 还剩 <span class="num">{{ remainCount }}</span> 次验证机会
          </div>
          <el-button
            v-waiting="['put::loading::/corpOpenInfo/verifyJdAmount']"
            size="large"
            type="primary"
            @click="submitForm('ruleForm', $event)"
          >
            验证金额
          </el-button>
        </div>
      </div>
    </template>

    <ContactService ref="contactService" :title="title" sub-title="请联系客服进行处理" />
    <UploadVoucher ref="uploadVoucherRef" @success="uploadSuccess" />
  </section>
</template>

<script>
import WarnContent from '@/views/components/common/warn-content.vue' // 警告文本
import ContactService from '@/views/components/common/contact-service/contact-service.vue' // 联系客服
import UploadVoucher from './upload-voucher.vue' // 上传凭证
import userApi from '@/apis/user' // 用户接口
import StatusTemplate from './status-template.vue'
import {
  ENDORSEMENT_STATUS, // 银行账户状态
} from '@/constant' // 常量

export default {
  name: 'verify-bank-account',
  components: {
    WarnContent,
    ContactService,
    UploadVoucher,
    StatusTemplate
  },

  props: {
    // 下一步
    next: {
      type: Function,
      require: true
    },
    traderAccountId: [String, Number], // 支付渠道id
    getSignStage: {
      type: Function,
      require: true
    },
    failReason: String,
    close: Function, // 关闭窗口
    bankAccountName: String, // 企业名称
    signPaymentFlag: [String, Number], // 签约户是否禁用
    stageStatus: {
      type: [String, Number],
      default: 0
    }, // 提交状态
    checkAmountFailType: [String, Number] // 验证金额失败原因 1-小额超限 2-小额输入过期验证
  },

  data() {
    // 自定义手机号码验证规则
    const checkVerifyAmount = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入验证金额'))
      }
      const min = 0.01
      const max = 0.99
      if (value < min || value > max) {
        return callback(new Error('验证金额错误，请重新输入，范围是0.01 - 0.99'))
      }
      callback()
    }

    return {
      ENDORSEMENT_STATUS, // 银行账户状态
      ruleForm: {
        verifyAmount: '', // 验证金额
      },
      rules: { // 验证规则
        verifyAmount: [{ required: true, validator: checkVerifyAmount, trigger: 'blur' }],
      },
      errorMsg: '', // 错误提示
      remainCount: '', // 验证剩余次数
      title: '', // 联系客服提示title
      bankBranchName: '吉林亿联银行股份有限公司', // 开户支行名称，非空字段
      bankBranchCode: '************', // 开户行支行行号
    }
  },

  created() {
    this.getSignVerifyAmountTimes()
    this.$nextTick().then(() => {
      if (this.checkAmountFailType === 1) {
        this.title = '今日输入次数已达上限！'
        this.contactService()
      } else if (this.checkAmountFailType === 2) {
        this.title = '小额打款已过验证期！'
        this.contactService()
      }
    })
  },

  methods: {
    // 获取小额验证次数
    async getSignVerifyAmountTimes() {
      const res = await userApi.signVerifyAmountTimes({ traderAccountId: this.traderAccountId })
      // 验证剩余次数
      this.remainCount = res || 0
    },

    // 验证金额
    submitForm(formName, event) {
      if (event.target.nodeName === 'SPAN' || event.target.nodeName === 'I') {
        event.target.parentNode.blur()
      } else {
        event.target.blur()
      }
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.upcheck()
        } else {
          return false
        }
      })
    },

    // 验证金额
    async upcheck() {
      this.errorMsg = ''
      const res = await userApi.signVerifyCheckAmount({ checkAmount: this.ruleForm.verifyAmount, traderAccountId: this.traderAccountId })
      if (res.verify) {
        this.getSignStage()
        return
      }
      if (res.checkAmountFailType === 1) {
        this.title = '今日输入次数已达上限！'
        this.contactService()
      } else if (res.checkAmountFailType === 2) {
        this.title = '小额打款已过验证期！'
        this.contactService()
      } else {
        this.errorMsg = '验证金额错误，请重新输入'
      }
      this.remainCount = res.remainCount || 0

      // 刷新背书账户列表
      // this.$event.emit(REFRESH_ENDORSEMENT)
    },

    // 联系客服
    contactService() {
      this.$refs.contactService.init()
    },
    // 重新签约
    back() {
      this.next({
        reSign: true, // 是否重新签约
        viewStageType: 2 // 重新签约
      })
    },
    // 上传凭证
    upload() {
      this.$refs.uploadVoucherRef.init(this.traderAccountId)
    },
    // 上传完成
    uploadSuccess() {
      this.getSignStage()
    }
  }
}
</script>
