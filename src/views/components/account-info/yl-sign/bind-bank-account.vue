<!-- 验证银行账户 -->
<style lang="scss" scoped>
.bind-bank-account {
  .main {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    padding: 16px;
    background: $color-FFFFFF;

    .left {
      margin-right: 12px;
      border-right: 1px solid $color-D9D9D9;
      padding-right: 12px;
      width: 300px;
      background-position: left bottom;
      background-repeat: no-repeat;
      background-size: 255px 237px;
      background-color: $color-FFFFFF;
      letter-spacing: .5px;

      .title {
        margin-bottom: 10px;
        font-size: 18px;
      }
    }

    .right {
      overflow: auto;
      flex: 1;
      height: 517px;
      background-color: $color-FFFFFF;
    }
  }

  .word-mark {
    font-weight: 600;
    color: $--color-primary;
  }

  .paragraph {
    margin-bottom: 10px;
    padding-left: 1em;
    font-size: 14px;
    text-indent: -1em;
    color: $color-text-primary;
    line-height: 24px;

    .num {
      position: relative;
      display: inline-block;
      width: 1em;
      text-indent: 0;

      &::after {
        position: absolute;
        right: 0;
        content: ".";
      }
    }
  }

  .footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 12px;
  }

  .key-word {
    color: $color-warning;
  }
}
</style>

<template>
  <div>
    <section v-if="stageStatus === ENDORSEMENT_STATUS.EXAMINE_NO.id" class="bind-bank-account">
      <div class="main">
        <div class="left">
          <h3 class="title">为什么要绑定银行账户</h3>
          <div class="paragraph">
            <label class="num">1</label><span class="word-mark">电子交易账户</span>中用来交易的资金，后续交易是通过<span class="word-mark">绑定的企业同名智付E+银行一般户银行账户</span>完成。
          </div>
          <div class="paragraph">
            <label class="num">2</label>通过<span class="word-mark">绑定银行账户</span>，我们才可以确认当前操作者确实为企业网银的持有者，从而保障每一位客户在后续交易中的交易对手都是经过验证的真实有效的客户。
          </div>
          <div class="paragraph">
            <label class="num">3</label>银行需企业的<span class="word-mark">真实信息</span>来开立对应的电子交易账户。
          </div>
        </div>
        <div class="right">
          <div class="title-left-border">银行账户信息</div>
          <el-form
            ref="ruleForm"
            :model="ruleForm"
            class="form"
            :inline-message="true"
          >
            <el-form-item label="企业名称">
              <el-input :value="bankAccountName" placeholder="请输入企业名称" disabled />
            </el-form-item>

            <el-form-item label="开户行行号" prop="bankBranchCode">
              <el-input
                v-model="ruleForm.bankBranchCode"
                type="number"
                placeholder="请输入开户行行号"
                maxlength="12"
                disabled
              />
            </el-form-item>
            <el-form-item label="开户行名称" prop="bankBranchName">
              <el-select
                v-model="ruleForm.bankBranchName"
                placeholder="请输入开户行名称"
                filterable
                remote
                disabled
              >
                <el-option
                  v-for="item in bankBranchList"
                  :key="item.branchCnaps"
                  :label="item.branchName"
                  :value="item.branchName"
                />
              </el-select>
            </el-form-item>

            <el-form-item prop="bankAccount">
              <template slot="label">
                <span class="key-word">*</span>
                银行账号
              </template>
              <el-input
                v-model="ruleForm.bankAccount"
                type="number"
                placeholder="请输入银行账号"
                :number-format="numberFormat"
              />
              <div class="key-word" style="line-height: 25px;">请填写您在智付E+银行开通的对公银行一般户</div>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <div class="footer">
        <div class="agreement">
          <!--
            <el-checkbox v-model="readChecked">
            我已阅读并同意
            <a
            class="text-link"
            target="_blank"
            rel="noopener noreferrer"
            :href="OSS_FILES_URL.ZHONGBANGPULS_OPEN_PROTOCOL_URL"
            >《智付邦+开户协议》</a>
            </el-checkbox>
          -->
        </div>

        <el-button
          v-waiting="['post::loading::/corpOpenInfo/save', 'post::loading::/corpOpenInfo/reAuth']"
          size="large"
          type="primary"
          @click="submitForm('ruleForm')"
        >
          提交
        </el-button>
      </div>
    </section>
    <!-- 等待验证银行账户模块 -->
    <section v-if="stageStatus === ENDORSEMENT_STATUS.PAYMENT_WAIT.id" class="bind-bank-account">
      <StatusTemplate :stage-status="stageStatus" :close="close" />
    </section>
  </div>
</template>

<script>
import userApi from '@/apis/user' // 用户接口
import { REFRESH_ACCOUNT } from '@/event/modules/site'
import { OSS_FILES_URL } from '@/constants/oss-files-url'
import StatusTemplate from './status-template.vue'
import {
  ENDORSEMENT_STATUS, // 银行账户状态
} from '@/constant' // 常量

export default {
  name: 'bind-bank-account',
  components: {
    StatusTemplate
  },
  props: {
    // 下一步
    next: {
      type: Function,
      require: true
    },
    traderAccountId: [String, Number], // 支付渠道id
    bankAccountName: String, // 企业名称
    reSign: [Number], // 是否重新签约，0-不是，1-是
    paymentChannel: [String, Number], // 支付渠道类型，7-智付e+
    info: Object,
    stageStatus: {
      type: [String, Number],
      default: 0
    }, // 提交状态
    close: Function, // 关闭窗口

  },

  data() {
    // 自定义开户账号验证规则
    const checkBankAccount = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(''))
      }

      callback()
    }

    return {
      OSS_FILES_URL,
      ENDORSEMENT_STATUS,
      readChecked: false,
      bankBranchList: [],
      ruleForm: {
        bankAccount: '', // 开户行账号，非空字段
        bankCnap: '', // 开户行银联编码
        bankName: '', // 开户银行名称，非空字段
        bankCode: '', // 开户行行号，非空字段
        bankBranchName: '吉林亿联银行股份有限公司', // 开户支行名称，非空字段
        bankBranchCode: '************', // 开户行支行行号
        bankProvinceId: '', // 省ID，非空字段
        bankProvinceName: '', // 省名称，非空字段
        bankCityId: '', // 市ID，非空字段
        bankCityName: '' // 市名称，非空字段
      },
      rules: {
        bankAccount: [{ required: true, validator: checkBankAccount, trigger: 'blur' }],
      },
      // 银行账户数字格式
      numberFormat: {
        decimal: false, // 是否支持小数
        negative: false, // 是否支持负数
        maxLength: 24, // 最大长度
      }
    }
  },

  methods: {
    // 下一步
    submitForm() {
      if (!this.ruleForm.bankAccount) {
        this.$message.warning('请填写银行账号')
      } else {
        this.upcheck()
      }
    },
    // 检验通过
    async upcheck() {
      // if (!this.readChecked) return this.$message.warning('请确认我已经阅读并同意以上内容')
      this.ruleForm.bankAccountName = this.bankAccountName
      this.ruleForm.paymentChannel = this.paymentChannel
      this.ruleForm.reSign = this.reSign
      this.ruleForm.traderAccountId = this.traderAccountId

      await userApi.channelSign(this.ruleForm)
      // 刷新用户中心-电子账户状态
      this.$event.emit(REFRESH_ACCOUNT)
      this.next({
        viewStageType: 0 // 查看进度
      })
    },

  }
}
</script>
