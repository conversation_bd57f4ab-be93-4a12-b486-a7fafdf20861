<!-- 签约中状态文案展示 -->
<style lang="scss" scoped>
.status-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 520px;
  text-align: center;

  .icon {
    display: inline-block;
    border-radius: 50%;
    width: 52px;
    height: 52px;
    color: $font-color;

    &.error {
      color: $color-warning;
    }
  }

  .title {
    margin: 20px 0;
    font-size: 20px;
    font-weight: 600;
    line-height: 28px;
  }

  .tip {
    padding-bottom: 12px;
    font-size: 16px;
    color: $color-text-secondary;
    line-height: 22px;
  }

  .contact {
    overflow: hidden;
    overflow-y: auto;
    margin-top: 16px;
    max-height: 150px;
  }

  .code-box {
    display: flex;
    justify-content: space-between;
    margin: 24px auto 32px;
    width: 352px;

    >div {
      width: 140px;
      font-size: 16px;

      img {
        margin-bottom: 8px;
        width: 100%;
      }
    }
  }

  .btn {
    margin-top: 16px;
  }
}

.dec {
  margin-top: 16px;
  font-size: 16px;
  line-height: 16px;
}

.label {
  color: #333333;

  &::before {
    content: "*";
    color: #EC3535;
  }
}

.footer {
  text-align: center;
}
</style>

<template>
  <section class="status-tip">
    <div v-if="signPaymentFlag">
      <StatusTemplate
        :stage-status="stageStatus"
        :sign-payment-flag="signPaymentFlag"
        :close="close"
        :fail-reason="failReason"
      />
      <div class="footer">
        <el-button
          size="large"
          type="primary"
          @click="close"
        >
          我知道了
        </el-button>
      </div>
    </div>

    <div v-if="stageStatus === ENDORSEMENT_STATUS.EXAMINE_SUCCESS.id" style="margin-bottom: 100px;">
      <icon class="icon success" type="chengjie-check-circle" />
      <div class="title">签约成功</div>
      <div class="tip"><span class="label">开户银行：</span>吉林亿联银行股份有限公司</div>
      <div class="tip"><span class="label">银行账户：</span>{{ bankNo }}</div>

      <el-button
        class="btn"
        type="primary"
        size="large"
        @click="close"
      >
        马上交易
      </el-button>
      <el-button
        class="btn"
        type="primary"
        size="large"
        @click="syncBank"
      >
        同步至回款账户
      </el-button>
    </div>
  </section>
</template>

<script>
import {
  ENDORSEMENT_STATUS, // 银行账户状态
} from '@/constant' // 常量
import userApi from '@/apis/user' // 接口
import { mapGetters } from 'vuex'
import StatusTemplate from './status-template.vue'

export default {
  name: 'data-review',
  components: {
    StatusTemplate
  },
  props: {
    next: { // 下一步
      type: Function,
      require: true
    },
    failReason: String, // 审核失败原因
    traderAccountId: [String, Number], // 支付渠道id
    signPaymentFlag: [String, Number], // 签约户是否禁用
    stageStatus: { // 开户状态
      type: Number,
      require: true
    },
    close: Function, // 关闭窗口
  },
  data() {
    return {
      ENDORSEMENT_STATUS,

    }
  },
  computed: {
    ...mapGetters({
      paymentAccountList: 'user/paymentAccountList', // 电子交易账户列表
    }),
    bankNo() {
      return this.paymentAccountList && this.paymentAccountList.filter(e => e.paymentChannel === 7)[0].jdAccountNo
    }
  },

  async created() {
    await this.$store.dispatch('user/getNoBanAccountList')
  },

  methods: {
    back() {
      this.next({
        reSign: true, // 是否重新签约
        viewStageType: 2 // 重新签约
      })
    },
    async syncBank() {
      await userApi.signSuccessSyncBank({ traderAccountId: this.traderAccountId, toAccountType: 2 })
      this.close()
    }

  }
}
</script>
