<style lang="scss" scoped>
  .flex-wrap {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 50px;

    .icon {
      display: inline-block;
      border-radius: 50%;
      width: 52px;
      height: 52px;
      color: $font-color;

      &.error {
        color: $color-warning;
      }
    }

    .title {
      margin: 20px 0 4px;
      font-size: 18px;
      font-weight: 600;
      line-height: 28px;
    }

    .tip {
      padding-bottom: 8px;
      font-size: 16px;
      color: $color-text-secondary;
      line-height: 22px;
    }

    .contact {
      overflow: hidden;
      overflow-y: auto;
      margin-top: 16px;
      max-height: 150px;
    }

    .code-box {
      display: flex;
      justify-content: space-between;
      margin: 24px auto 32px;
      width: 352px;

      >div {
        width: 140px;
        font-size: 16px;

        img {
          margin-bottom: 8px;
          width: 100%;
        }
      }
    }

    .red-cls {
      color: #EC3535;
    }
  }

  .pb30 {
    padding-bottom: 30px !important;
  }

  .center {
    text-align: center;
  }
</style>

<template>
  <div>
    <!-- 待打款 待审核 -->
    <template v-if="stageStatus === ENDORSEMENT_STATUS.PAYMENT_WAIT.id || stageStatus === ENDORSEMENT_STATUS.AUDIT.id ">
      <div class="flex-wrap">
        <icon class="icon" type="chengjie-wait" />
        <div :class="['title', stageStatus === ENDORSEMENT_STATUS.PAYMENT_WAIT.id && 'red-cls']">资料已提交,等待验证银行账户</div>
        <div class="tip pb30">请耐心等待，预计等待时间：<span class="red-cls">工作日10分钟</span></div>
        <el-button
          class="btn"
          type="primary"
          size="large"
          @click="close"
        >
          我知道了
        </el-button>
      </div>
    </template>
    <!-- 打款失败 验证失败 禁用 -->
    <template v-if="[ENDORSEMENT_STATUS.PAYMENT_FAIL.id, ENDORSEMENT_STATUS.EXAMINE_ERROR.id, ENDORSEMENT_STATUS.REJECTED.id].includes(stageStatus) || signPaymentFlag">
      <div class="flex-wrap">
        <icon class="icon error" type="chengjie-close-circle" />
        <div class="title">{{ signPaymentFlag ? '您的电子交易账户已禁用，暂无法进行接单' : `您的电子交易账户签约失败，请重新发起签约${[1, 2].includes(checkAmountFailType) ? '' : '或上传凭证'}` }}</div>

        <div class="tip">{{ failReason ? `${signPaymentFlag ? '原因：' : '失败原因：'}` + failReason + "，" : "" }}如有疑问可扫码联系客服经理！</div>

        <div v-if="!signPaymentFlag && ![1, 2].includes(checkAmountFailType)" class="tip">若账户信息无误但收不到打款金额时请点击【上传凭证】按钮，若打款账户信息错误请点击【重新签约】按钮</div>

        <div class="code-box">
          <div>
            <img :src="configDefault.customerManagerQr1" alt="联系小桑">
            <div class="center">客服一号</div>
          </div>
          <div>
            <img :src="configDefault.customerManagerQr2" alt="联系小朱">
            <div class="center">客服二号</div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import {
  ENDORSEMENT_STATUS, // 银行账户状态
} from '@/constant' // 常量

export default {
  name: 'status-template',
  props: {
    failReason: String,
    close: Function, // 关闭窗口
    stageStatus: {
      type: [String, Number],
      default: 0
    }, // 提交状态
    signPaymentFlag: { // 签约户是否已禁用 默认0未禁用
      type: [String, Number],
      default: 0
    },
    checkAmountFailType: [String, Number] // 验证金额失败原因 1-小额超限 2-小额输入过期验证
  },
  data() {
    return {
      ENDORSEMENT_STATUS
    }
  }
}
</script>
