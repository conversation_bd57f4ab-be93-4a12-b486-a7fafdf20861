<!-- 银行账户-上传凭证 -->

<style lang="scss" scoped>
.voucher-model-wrap {
  padding: 16px;
  background-color: #FFFFFF;

  .refer {
    font-size: 16px;
    font-weight: 400;
    line-height: 25px;
    color: #333333;

    .red {
      color: #EC3535;
    }
  }

  .item-label {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #666666;

    .preview-cls {
      padding-left: 5px;
    }

    .txt {
      cursor: pointer;
      border-bottom: 1px solid $--color-primary;
      color: $--color-primary;
    }

    &::before {
      margin-right: 4px;
      color: #EC3535;
      content: "*";
    }
  }
}

.upload-cls {
  ::v-deep .el-form-item__content {
    line-height: 30px;
  }

  .upload-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

::v-deep .el-form-item {
  margin-bottom: 10px;
}

::v-deep.el-form--label-top .el-form-item__label {
  padding: 0;
}

::v-deep .el-upload-dragger {
  padding-top: 20px;
  width: 150px;
  height: 80px;

  s .el-icon-upload {
    line-height: 0;
    margin: 0;
    font-size: 30px;
  }
}

::v-deep .el-textarea__inner {
  height: 80px;
}

.bottom-cls {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;

  .left {
    width: 50%;
  }
}
</style>

<template>
  <el-dialog
    width="560px"
    :visible.sync="visible"
    append-to-body
    :close-on-click-modal="false"
    title="上传账户使用凭证"
    :before-close="handleClose"
  >
    <div class="voucher-model-wrap">
      <el-form
        ref="form"
        label-position="top"
      >
        <div class="item-label">
          请上传该银行账户近三天的银行交易收款回单
          <label class="preview-cls">
            <el-image
              :preview-src-list="srcList"
              img-text="示例"
            />
          </label>
        </div>
        <el-form-item class="upload-cls" label="" prop="realMaterialList">
          <div class="upload-flex">
            <ImgUpload
              v-for="(item, index) in voucher"
              :key="index"
              v-model="item.url"
              :dir="OSS_DIR.BANK_CARD_CERTIFICATE"
              :height="80"
              style="width: 150px;"
              accept="image/png,image/jpg,image/jpeg"
              :on-success="onUploadSuccess"
            >
              <div slot="empty">
                <div>点击或拖动上传文件</div>
              </div>
            </ImgUpload>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div class="bottom-cls">
      <div class="right">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="onSubmit">确认上传</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import ImgUpload from '@/views/components/common/img-upload/img-upload.vue'
import userApi from '@/apis/user' // 用户接口
import {
  OSS_DIR
} from '@/constant.js'
export default {
  name: 'evidence-dialog',
  components: {
    ImgUpload
  },
  data() {
    return {
      srcList: ['https://oss.chengjie.red/web/imgs/bank-account/银行账户上传凭证-电子回单示例.png'],
      visible: false,
      OSS_DIR,
      voucher: [], // 上传的材料
      traderAccountId: ''
    }
  },
  methods: {
    // 打开弹窗-初始化数据
    init(id) {
      this.traderAccountId = id
      this.visible = true
      this.voucher = [{ name: '', url: '' }, { name: '', url: '' }, { name: '', url: '' }]
    },
    handleClose() {
      this.visible = false
    },
    onUploadSuccess(url, file) {
      // 上传成功后更新voucher中上传数据
      this.$nextTick().then(() => {
        this.voucher.forEach(e => {
          if (e.url === url) {
            e.name = file.name
            e.size = file.size
            e.type = file.type
          }
        })
      })
    },
    async onSubmit() {
      // 校验是否有上传凭证文件
      const validate = this.voucher.some(e => e.name !== '' && e.url !== '')
      if (!validate) return this.$message.warning('请上传账户使用凭证')
      // 校验上传文件总大小
      const fileTotalSize = this.voucher.reduce((acc, curr) => acc + curr.size, 0) / 1024 / 1024
      if (fileTotalSize > 6) return this.$message.warning('上传的材料文件总大小不能超过6M')
      const params = {
        traderAccountId: this.traderAccountId,
        pictureList: this.voucher.map(e => ({ name: e.name, url: e.url })).filter(e => e.name !== '' && e.url !== '')
      }
      await userApi.channelSignUploadCertificate(params)
      this.$message.success('上传成功')
      this.$emit('success')
      this.handleClose()
    }

  }
}
</script>
