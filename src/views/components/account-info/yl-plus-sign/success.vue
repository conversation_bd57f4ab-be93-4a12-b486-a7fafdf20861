<!-- 签约中状态文案展示 -->
<style lang="scss" scoped>
.status-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 520px;
  text-align: center;

  .icon {
    display: inline-block;
    border-radius: 50%;
    width: 52px;
    height: 52px;
    color: $font-color;

    &.error {
      color: $color-warning;
    }
  }

  .title {
    margin: 20px 0;
    font-size: 20px;
    font-weight: 600;
    line-height: 28px;
  }

  .tip {
    padding-bottom: 12px;
    font-size: 16px;
    color: $color-text-secondary;
    line-height: 22px;
  }

  .c-333333 {
    color: #333333 !important;
  }

  .contact {
    overflow: hidden;
    overflow-y: auto;
    margin-top: 16px;
    max-height: 150px;
  }

  .code-box {
    display: flex;
    justify-content: space-between;
    margin: 24px auto 32px;
    width: 352px;

    >div {
      width: 140px;
      font-size: 16px;

      img {
        margin-bottom: 8px;
        width: 100%;
      }
    }
  }

  .btn {
    margin-top: 16px;
  }
}

.dec {
  margin-top: 16px;
  font-size: 16px;
  line-height: 16px;
}

.label {
  color: #333333;

  &::before {
    content: "*";
    color: #EC3535;
  }
}

.footer {
  text-align: center;
}
</style>

<template>
  <section class="status-tip">
    <!-- 禁用 -->
    <div v-if="signPaymentFlag">
      <StatusTemplate
        :stage-status="stageStatus"
        :sign-payment-flag="signPaymentFlag"
        :fail-reason="failReason"
      />
      <div class="footer">
        <el-button
          size="large"
          type="primary"
          @click="close"
        >
          我知道了
        </el-button>
      </div>
    </div>

    <div v-if="stageStatus === PAYMENT_CHANNEL_SIGN_STATUS.SUCCESS.id && !signPaymentFlag" style="margin-bottom: 100px;">
      <icon class="icon success" type="chengjie-check-circle" />
      <div class="title">签约成功</div>
      <!-- TODO: 邮箱银行侧暂未上线，暂时隐藏邮箱信息文案部分 -->
      <!--
        <div class="tip c-333333">恭喜您签约成功，已为您创建邮箱，邮箱初始密码已为您发送至短信中，请您及时登录并修改密码。</div>
        <div class="tip"><span class="label">邮箱地址：</span>{{ corpInfo.emailWebsiteAddress }}</div>
        <div class="tip"><span class="label">邮箱账号：</span>{{ corpInfo.emailAccount }}</div>
      -->

      <el-button
        class="btn"
        type="primary"
        size="large"
        @click="close"
      >
        马上交易
      </el-button>
    </div>
  </section>
</template>

<script>
import {
  PAYMENT_CHANNEL_SIGN_STATUS, // 渠道签约状态
} from '@/constant' // 常量
// import userApi from '@/apis/user' // 接口
// import { mapGetters } from 'vuex'
import StatusTemplate from './status-template.vue'

export default {
  name: 'data-review',
  components: {
    StatusTemplate
  },
  props: {
    next: { // 下一步
      type: Function,
      require: true
    },
    failReason: String, // 审核失败原因
    traderAccountId: [String, Number], // 支付渠道id
    signPaymentFlag: [String, Number], // 签约户是否禁用
    stageStatus: { // 开户状态
      type: Number,
      require: true
    },
    close: Function, // 关闭窗口
  },
  data() {
    return {
      PAYMENT_CHANNEL_SIGN_STATUS
    }
  },
  computed: {
    corpInfo() {
      return this.$store.state?.user?.corpInfo
    },
    // ...mapGetters({
    //   paymentAccountList: 'user/paymentAccountList', // 电子交易账户列表
    // }),
    // bankNo() {
    //   return this.paymentAccountList && this.paymentAccountList.filter(e => e.paymentChannel === 7)[0].jdAccountNo
    // }
  },

  async created() {
    // await this.$store.dispatch('user/getNoBanAccountList')
  },

  methods: {
    back() {
      this.next({
        reSign: true, // 是否重新签约
        viewStageType: 2 // 重新签约
      })
    },
  }
}
</script>
