<style lang="scss" scoped>
  .flex-wrap {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 50px;

    .icon {
      display: inline-block;
      border-radius: 50%;
      width: 52px;
      height: 52px;
      color: $font-color;

      &.error {
        color: $color-warning;
      }
    }

    .title {
      margin: 20px 0 4px;
      font-size: 18px;
      font-weight: 600;
      line-height: 28px;
    }

    .tip {
      padding-bottom: 8px;
      font-size: 16px;
      color: $color-text-secondary;
      line-height: 22px;
    }

    .contact {
      overflow: hidden;
      overflow-y: auto;
      margin-top: 16px;
      max-height: 150px;
    }

    .code-box {
      display: flex;
      justify-content: space-between;
      margin: 24px auto 32px;
      width: 352px;

      >div {
        width: 140px;
        font-size: 16px;

        img {
          margin-bottom: 8px;
          width: 100%;
        }
      }
    }

    .red-cls {
      color: #EC3535;
    }
  }

  .pb30 {
    padding-bottom: 30px !important;
  }

  .center {
    text-align: center;
  }
</style>

<template>
  <div>
    <!-- 签约中 -->
    <template v-if="stageStatus === PAYMENT_CHANNEL_SIGN_STATUS.PENDING.id">
      <div class="flex-wrap">
        <icon class="icon" type="chengjie-wait" />
        <div :class="['title']">您的简易版网银账户状态校验中，预计等待时间：<span class="red-cls">工作日10分钟</span></div>
        <div class="tip pb30">如有疑问可扫码联系客户经理！</div>
        <div class="code-box">
          <div>
            <img :src="configDefault.customerManagerQr1" alt="联系小桑">
            <div class="center">客服一号</div>
          </div>
          <div>
            <img :src="configDefault.customerManagerQr2" alt="联系小朱">
            <div class="center">客服二号</div>
          </div>
        </div>
        <!--
          <el-button
          class="btn"
          type="primary"
          size="large"
          @click="close"
          >
          我知道了
          </el-button>
        -->
      </div>
    </template>
    <!-- 签约失败 禁用 -->
    <template v-if="[PAYMENT_CHANNEL_SIGN_STATUS.ERROR.id].includes(stageStatus) || signPaymentFlag">
      <div class="flex-wrap">
        <icon class="icon error" type="chengjie-close-circle" />
        <div class="title">{{ signPaymentFlag ? '您的电子交易账户已禁用，暂无法进行接单' : `您的电子交易账户签约失败，请重新发起签约` }}</div>

        <div class="tip">{{ failReason ? `${signPaymentFlag ? '原因：' : '失败原因：'}` + failReason + "，" : "" }}如有疑问可扫码联系客服经理！</div>

        <!-- <div v-if="!signPaymentFlag && ![1, 2].includes(checkAmountFailType)" class="tip">若账户信息无误但收不到打款金额时请点击【上传凭证】按钮，若打款账户信息错误请点击【重新签约】按钮</div> -->

        <div class="code-box">
          <div>
            <img :src="configDefault.customerManagerQr1" alt="联系小桑">
            <div class="center">客服一号</div>
          </div>
          <div>
            <img :src="configDefault.customerManagerQr2" alt="联系小朱">
            <div class="center">客服二号</div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import {
  PAYMENT_CHANNEL_SIGN_STATUS, // 银行账户状态
} from '@/constant' // 常量

export default {
  name: 'status-template',
  props: {
    failReason: String,
    // close: Function, // 关闭窗口
    stageStatus: {
      type: [String, Number],
      default: 0
    }, // 提交状态
    signPaymentFlag: { // 签约户是否已禁用 默认0未禁用
      type: [String, Number],
      default: 0
    },
  },
  data() {
    return {
      PAYMENT_CHANNEL_SIGN_STATUS
    }
  }
}
</script>
