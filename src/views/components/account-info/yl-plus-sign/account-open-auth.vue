<!-- 账户开通授权  -->
<style lang="scss" scoped>
.account-open-auth {
  .main {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    padding: 16px;
    background: $color-FFFFFF;

    .left {
      margin-right: 12px;
      border-right: 1px solid $color-D9D9D9;
      padding-right: 12px;
      width: 300px;
      background-position: left bottom;
      background-repeat: no-repeat;
      background-size: 255px 237px;
      background-color: $color-FFFFFF;
      letter-spacing: .5px;

      .title {
        margin-bottom: 10px;
        font-size: 18px;
      }
    }

    .right {
      overflow: auto;
      flex: 1;
      background-color: $color-FFFFFF;
    }
  }

  .word-mark {
    font-weight: 600;
    color: $--color-primary;
  }

  .paragraph {
    margin-bottom: 10px;
    padding-left: 1em;
    font-size: 14px;
    text-indent: -1em;
    color: $color-text-primary;
    line-height: 24px;

    .num {
      position: relative;
      display: inline-block;
      width: 1em;
      text-indent: 0;

      &::after {
        position: absolute;
        right: 0;
        content: ".";
      }
    }
  }

  .text {
    margin-bottom: 10px;
    font-size: 14px;
    color: $color-text-primary;
    line-height: 24px;
  }

  .footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 12px;
  }

  .key-word {
    color: $color-warning;
  }

  .reminder {
    margin-top: 12px;
    font-size: 14px;
    color: #999999;
    line-height: 24px;
  }

  ::v-deep .el-form-item__error {
    line-height: 1px !important;
  }
}
</style>

<template>
  <div>
    <section class="account-open-auth">
      <div class="main">
        <div class="left">
          <h3 class="title">为什么要授权开通</h3>
          <div class="text">
            为了确保您的交易体验流畅无阻，并让您能第一时间掌握订单的任何变动，软件诚挚地请求您进行以下授权或开通服务：
          </div>
          <div class="paragraph">
            <label class="num">1</label>授权模拟登录：让软件在您需要时，能够安全、便捷地代表您处理相关事务，确保信息同步更新。
          </div>
          <div class="paragraph">
            <label class="num">2</label>开通邮箱服务：通过邮箱，软件将即时向您发送订单状态更新、重要通知等关键信息，让您随时掌握交易动态。
          </div>
          <div class="paragraph">
            <label class="num">3</label>启用识单助手：借助这一智能工具，软件能更有效地识别并处理您的订单信息，提升服务效率，为您带来更加个性化的服务体验。请您放心，所有操作均遵循严格的隐私保护政策，旨在为您提供更加贴心、高效的服务。
          </div>
        </div>
        <div class="right">
          <div class="title-left-border">网银账户信息</div>
          <el-form
            ref="accountOpenAuthForm"
            :model="ruleForm"
            class="form"
            :rules="rules"
            :inline-message="true"
          >
            <el-form-item label="粘贴双因子链接">
              <el-input
                v-model="ruleForm.bankLoginUrl"
                clearable
                placeholder="请粘贴简易版网银双因子链接"
                @paste.native="handlePaste"
                @input="handlePaste"
              />
            </el-form-item>

            <el-form-item label="简易版网银账号" prop="bankLoginAccount">
              <el-input v-model="ruleForm.bankLoginAccount" clearable placeholder="请输入简易版网银登录账号" />
            </el-form-item>
            <el-form-item label="OTP key" prop="bankKey">
              <el-input
                v-model="ruleForm.bankKey"
                placeholder="请输入简易版网银OTP密钥"
                clearable
              />
            </el-form-item>
            <el-form-item label="登录密码" prop="bankLoginPassword">
              <el-input
                v-model="ruleForm.bankLoginPassword"
                clearable
                placeholder="请输入简易版网银登录密码"
              />
            </el-form-item>
          </el-form>
          <div class="reminder">
            温馨提醒：
            <div>1. 如您的企业简易版网银现已成功开通，请务必认真核对并精确填入收到的OTP（一次性密码）</div>
            <div>key值，以便完成后续的安全验证步骤。</div>
            <div>2. 如在操作过程中遇到任何问题或疑问，欢迎随时联系您的专属客户经理。</div>
          </div>
        </div>
      </div>

      <div class="footer">
        <div class="agreement">
          <el-checkbox v-model="readChecked">
            我已阅读并同意
          </el-checkbox>

          <a
            class="text-link"
            target="_blank"
            rel="noopener noreferrer"
            @click="handleUrlClick(E_PLUS_SIGN_COMPANY_INFO_AUTH_URL)"
          >《模拟登录企业信息授权书》</a>
          <a
            class="text-link"
            target="_blank"
            rel="noopener noreferrer"
            @click="handleUrlClick(E_PLUS_SIGN_EMAIL_COMPANY_INFO_AUTH_URL)"
          >《邮箱开通企业信息授权书》</a>
          <a
            class="text-link"
            target="_blank"
            rel="noopener noreferrer"
            @click="handleUrlClick(E_PLUS_SIGN_DISCOVERY_HELPER_COMPANY_INFO_AUTH_URL)"
          >《识单助手启用企业信息授权书》</a>
        </div>
        <div>
          <el-button
            v-waiting="['post::loading::/corpOpenInfo/save', 'post::loading::/corpOpenInfo/reAuth']"
            size="large"
            @click="handleBack"
          >
            上一步
          </el-button>
          <el-button
            v-waiting="['post::loading::/corpOpenInfo/save', 'post::loading::/corpOpenInfo/reAuth']"
            size="large"
            type="primary"
            @click="submitForm('ruleForm')"
          >
            提交
          </el-button>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import userApi from '@/apis/user' // 用户接口
// import { REFRESH_ACCOUNT } from '@/event/modules/site'
import {
  E_PLUS_SIGN_COMPANY_INFO_AUTH_URL,
  E_PLUS_SIGN_EMAIL_COMPANY_INFO_AUTH_URL,
  E_PLUS_SIGN_DISCOVERY_HELPER_COMPANY_INFO_AUTH_URL
} from '@/constants/oss-files-url'
import {
  OPEN_URL_IN_DEFAULT_BROWSER,
} from '@recognize/ipc-event-constant'
// import StatusTemplate from './status-template.vue'
// import {
//   ENDORSEMENT_STATUS, // 银行账户状态
// } from '@/constant' // 常量

export default {
  name: 'account-open-auth',
  components: {
    // StatusTemplate
  },
  props: {
    // 下一步
    next: {
      type: Function,
      require: true
    },
    back: {
      type: Function,
      require: true
    },
    setFormData: {
      type: Function,
      require: true
    },
    formData: Object,
    close: Function, // 关闭窗口

  },

  data() {
    return {
      E_PLUS_SIGN_COMPANY_INFO_AUTH_URL,
      E_PLUS_SIGN_EMAIL_COMPANY_INFO_AUTH_URL,
      E_PLUS_SIGN_DISCOVERY_HELPER_COMPANY_INFO_AUTH_URL,
      // ENDORSEMENT_STATUS,
      readChecked: false,
      bankBranchList: [],
      ruleForm: {
        bankLoginAccount: '',
        bankLoginPassword: '',
        bankKey: '',
        bankLoginUrl: ''// 双因子链接
      },
      rules: {
        bankLoginAccount: [{ required: true, message: '请输入简易版网银登录账号', trigger: '' }],
        bankLoginPassword: [{ required: true, message: '请输入简易版网银登录密码', trigger: '' }],
        bankKey: [{ required: true, message: '请输入简易版网银OPT密钥', trigger: '' }],
      },
    }
  },

  watch: {
    formData: {
      handler(newVal) {
        if (newVal) {
          // this.ruleForm = newVal
          Object.assign(this.ruleForm, newVal)
        }
      },
      deep: true,
      immediate: true
    }
  },

  methods: {
    // 下一步
    submitForm() {
      this.$refs.accountOpenAuthForm.validate(valid => {
        if (valid) {
          this.upcheck()
        }
      })
    },
    // 返回上一步
    handleBack() {
      this.setFormData(this.ruleForm)
      this.back(0)
    },
    // 检验通过
    async upcheck() {
      if (!this.readChecked) return this.$message.warning('请确认我已经阅读并同意以上内容')
      this.ruleForm.reSign = this.reSign

      await userApi.channelSign(this.ruleForm)
      // 刷新用户中心-电子账户状态
      // this.$event.emit(REFRESH_ACCOUNT)
      this.next({
        viewStageType: 0 // 查看进度
      })
    },
    // 粘贴识别
    handlePaste(e) {
      let text
      if (e.clipboardData) {
      // 粘贴事件
        e.preventDefault() // 阻止默认粘贴行为
        text = e.clipboardData.getData('text')
      } else {
      // input事件
        text = e
      }
      // 识别链接中的secret是否存在
      const secretMatch = text.match(/secret=([^&]*)/)
      const secret = secretMatch ? secretMatch[1] : ''
      // 识别链接中的account是否存在
      const accountMatch = text.match(/%3A([^?]*)/)
      this.ruleForm.bankLoginUrl = text
      if (secret && accountMatch[1]) {
        this.ruleForm.bankKey = secret // 设置 opt key
        this.ruleForm.bankLoginAccount = accountMatch[1] // 识别网银账号
      } else {
        this.ruleForm.bankKey = '' // 设置 opt key
        this.ruleForm.bankLoginAccount = '' // 识别网银账号
      }
    },
    // 打开授权书
    handleUrlClick(url) {
      if (this.$ipc) {
        this.$ipc.send(OPEN_URL_IN_DEFAULT_BROWSER, url)
      } else {
        window.open(url)
      }
    }

  }
}
</script>
