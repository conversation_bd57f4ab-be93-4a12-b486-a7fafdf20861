<!-- 验证银行账户 -->
<style lang="scss" scoped>
.verify-bank-account {
  .main {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;

    .left {
      padding: 16px;
      width: 462px;
      background-color: $color-FFFFFF;

      .subtitle {
        color: $color-text-secondary;
        line-height: 22px;
      }

      .content {
        margin-bottom: 10px;
        font-size: 16px;
        line-height: 24px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .companyName {
        font-weight: 600;
      }
    }

    .right {
      padding: 16px;
      width: 462px;
      background-color: $color-FFFFFF;

      .tip-box {
        margin-bottom: 12px;
        font-size: 16px;
      }
    }
  }

  .text-right {
    ::v-deep.el-input__inner {
      text-align: right;
    }
  }

  ::v-deep {
    .el-form-item {
      margin-bottom: 0 !important;
    }
  }

  .remain-count-box {
    display: inline-block;
    margin-right: 8px;
    font-size: 16px;
    color: $color-text-regular;

    .info-circle {
      font-size: 18px;
      color: $--color-warning;
    }

    .num {
      font-weight: bold;
      color: $--color-primary;
    }
  }

  .footer {
    text-align: center;
  }
}

.btn-box {
  text-align: end;
}
</style>

<template>
  <section class="verify-bank-account">
    <StatusTemplate
      :stage-status="stageStatus"
      :sign-payment-flag="signPaymentFlag"
      :fail-reason="failReason"
    />
    <!-- 禁用 || 签约中 -->
    <div class="footer">
      <el-button
        v-if="[PAYMENT_CHANNEL_SIGN_STATUS.PENDING.id].includes(stageStatus) || signPaymentFlag"
        size="large"
        type="primary"
        @click="close"
      >
        我知道了
      </el-button>
      <!-- 签约失败 -->
      <el-button
        v-if="stageStatus === PAYMENT_CHANNEL_SIGN_STATUS.ERROR.id"
        size="large"
        type="primary"
        @click="back"
      >
        重新签约
      </el-button>
    </div>
  </section>
</template>

<script>
import StatusTemplate from './status-template.vue'
import {
  PAYMENT_CHANNEL_SIGN_STATUS, // 银行账户状态
} from '@/constant' // 常量

export default {
  name: 'verify-bank-account',
  components: {
    StatusTemplate
  },

  props: {
    // 下一步
    next: {
      type: Function,
      require: true
    },
    traderAccountId: [String, Number], // 支付渠道id
    getSignStage: {
      type: Function,
      require: true
    },
    failReason: String,
    close: Function, // 关闭窗口
    bankAccountName: String, // 企业名称
    signPaymentFlag: [String, Number], // 签约户是否禁用
    stageStatus: {
      type: [String, Number],
      default: 0
    }, // 提交状态
  },

  data() {
    return {
      PAYMENT_CHANNEL_SIGN_STATUS, // 银行账户状态
    }
  },

  methods: {

    // 重新签约
    back() {
      this.next({
        reSign: true, // 是否重新签约
        viewStageType: 2 // 重新签约
      })
    },
  }
}
</script>
