<!-- 添加银行签收账户弹窗 -->
<style lang="scss" scoped>
.yl-sign-dialog {
  .empty-box {
    height: 520px;
    background-color: $color-F2F2F2;
  }
}

::v-deep {
  .yl-sign-dialog {
    // 弹窗
    margin-top: 10vh !important;

    .el-dialog__body {
      padding: 16px;
      background-color: $color-F2F2F2;
    }

    .title-left-border {
      position: relative;
      margin: 0 0 8px !important;
      padding-left: 12px;
      font-size: 16px;
      font-weight: 600;
      line-height: 22px;

      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: 4px;
        height: 16px;
        background: $--color-primary;
        transform: translateY(-50%);
        content: "";
      }
    }

    // form表单相关
    .g-required {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .el-form-item {
      display: flex;
      margin-bottom: 12px;
      flex-flow: column;
    }

    .el-select {
      width: 100%;
    }

    .el-form-item__label {
      margin-bottom: 4px;
      width: auto !important;
      height: 20px;
      text-align: left;
      color: $color-text-secondary;
      line-height: 20px;

      &::before {
        font-weight: 600;
      }
    }

    .el-form-item__content {
      margin-left: 0 !important;
      width: 100%;
      height: 40px;
    }

    .el-form-item__error--inline {
      position: absolute;
      display: inherit;
      margin-left: 0;
      line-height: 7px;
    }

    .el-input__inner {
      padding-left: 12px;
      font-size: 16px;
    }
  }
}
</style>

<template>
  <el-dialog
    title="签约电子交易账户"
    width="984px"
    append-to-body
    custom-class="yl-sign-dialog"
    :before-close="close"
    :visible.sync="visible"
    :close-on-click-modal="false"
  >
    <Step
      :active-step="activeStep"
      :is-sign-success="stageStatus === 2 ? true : false"
      :step-item="stepItem"
    />

    <div
      v-if="fristJoin"
      v-loading="loading"
      class="empty-box"
      element-loading-background="rgba(0, 0, 0, 0)"
    />

    <section v-if="!fristJoin">
      <!-- 绑定银行账户 -->
      <BindBankAccount
        v-if="activeStep === YL_PLUS_SIGN_STAGE_STEP.BIND_BANK_ACCOUNT.STEP"
        :next="next"
        :stage-status="stageStatus"
        :form-data="formData"
        :set-form-data="setFormData"
        :re-sign="reSign"
        :close="close"
      />
      <!-- 账户开通授权 -->
      <AccountOpenAuth
        v-if="activeStep === YL_PLUS_SIGN_STAGE_STEP.ACCOUNT_OPEN_AUTH.STEP"
        :next="next"
        :back="back"
        :set-form-data="setFormData"
        :form-data="formData"
        :stage-status="stageStatus"
        :close="close"
      />
      <!-- 验证银行账户 -->
      <VerifyBankAccount
        v-if="activeStep === YL_PLUS_SIGN_STAGE_STEP.VERIFY_BANK_ACCOUNT.STEP"
        :stage-status="stageStatus"
        :next="next"
        :close="close"
        :trader-account-id="traderAccountId"
        :get-sign-stage="getSignStage"
        :sign-payment-flag="signPaymentFlag"
        :fail-reason="failReason"
        :payment-channel="paymentChannel"
      />

      <!-- 签约成功 -->
      <Success
        v-if="activeStep === YL_PLUS_SIGN_STAGE_STEP.SUCCESS_OPEN.STEP"
        :stage-status="stageStatus"
        :trader-account-id="traderAccountId"
        :fail-reason="failReason"
        :sign-payment-flag="signPaymentFlag"
        :close="close"
        :next="next"
      />
    </section>
  </el-dialog>
</template>

<script>
import BindBankAccount from './bind-bank-account.vue' // 绑定银行账户
import AccountOpenAuth from './account-open-auth.vue' // 账户开通授权
import VerifyBankAccount from './verify-bank-account.vue' // 验证银行账户
import userApi from '@/apis/user' // 接口
import {
  YL_PLUS_SIGN_STAGE_STEP, // e++签约进度
  BANG_SING_STAGE_STATUS, // 开通流程状态
} from '@/constant' // 常量
import Step from '../components/step.vue' // 流程步骤组件
import Success from './success.vue' // 签约审核中文案展示
import { CHANNEL_SIGN_DIGLOG_CLOSE } from '@/event/modules/site' // 监听事件常量
export default {
  name: 'bank-endorsed-account',

  components: {
    Step,
    BindBankAccount,
    VerifyBankAccount,
    AccountOpenAuth,
    Success
  },

  data() {
    return {
      stepItem: ['绑定银行账户', '账户开通授权', '验证账户', '签约成功'],
      failReason: '', // 审核失败原因
      YL_PLUS_SIGN_STAGE_STEP, // 开通流程步骤
      BANG_SING_STAGE_STATUS, // 开通流程状态
      visible: false,
      loading: true, // 加载中
      stageStatus: 0, // 默认 0=>未提交 初始化填写绑定银行信息页面
      ...this.initFormat()
    }
  },
  created() {
    this.$event.on(CHANNEL_SIGN_DIGLOG_CLOSE, () => {
      this.visible = false
    })
  },

  methods: {
    // 格式化data数据
    initFormat() {
      return {
        reSign: 0, // 是否重新签约，0-不是，1-是
        fristJoin: true, // 第一次进入
        activeStep: 0, // 0-绑定银行账户，1-账户开通授权,2-验证账户，3-签约成功
        viewStageType: null, // 是否是查看进度
        paymentChannel: 9, // 支付渠道类型，9-e++
        traderCorpId: '', // 企业id
        traderAccountId: '', // 渠道账户id
        isMqtt: false, // 是否消息推送打开
        formData: { // 签约提交
          bankBranchName: '吉林亿联银行股份有限公司', // 开户支行名称，非空字段
          bankBranchCode: '************', // 开户行支行行号
          bankAccount: '', // 银行账号
          bankLoginPassword: '', // 简易版网银登录密码
          bankKey: '', // 简易版网银OTP密钥
          bankLoginAccount: '', // 简易版网银登录账号
          bankAccountName: '' // 企业名称
        },
        signPaymentFlag: 0, // 签约户是否禁用: 0-否；1-是
      }
    },
    // 初始化
    init(obj) {
      this.reSign = obj.reSign ? 1 : 0
      this.failReason = '' // 审核失败原因
      const { traderAccountId, paymentChannel, bankAccountName, viewStageType, traderCorpId, isMqtt } = obj

      Object.assign(this, {
        viewStageType,
        traderCorpId,
        traderAccountId,
        paymentChannel,
        isMqtt,
      })
      // 默认数据设置
      this.setFormData({
        traderCorpId,
        traderAccountId,
        paymentChannel,
        bankAccountName
      })

      this.visible = true

      if (this.reSign) { // 重新提交
        this.activeStep = 0
        this.stageStatus = 0
        this.fristJoin = false
        return
      }
      // 查询签收流程阶段
      this.getSignStage()
      // 签约银行信息
      // this.getSignBankInfo()
    },

    // 签约银行信息
    async getSignBankInfo() {
      await userApi.getSignBankInfo({ traderAccountId: this.traderAccountId, paymentChannel: this.paymentChannel, traderCorpId: this.traderCorpId })
      // console.log('查询签收流程阶段', res)
    },

    // 查询签收流程阶段
    async getSignStage() {
      // 重新签约不调用接口  0-【查看进度】按钮、1-【查看原因】按钮、2-不调用查询进度接口
      if (this.viewStageType === 1 || this.viewStageType === 0 || this.isMqtt) {
        this.fristJoin = true
        const res = await userApi.channelSignStage({ traderAccountId: this.traderAccountId, paymentChannel: this.paymentChannel, viewStageType: this.viewStageType })
        // console.log('查询签收流程阶段', res)
        const { stageStatus, signStage, failReason, signPaymentFlag } = res
        Object.assign(this, {
          stageStatus,
          activeStep: signStage,
          failReason,
          signPaymentFlag
        })
        // this.checkAmountFailType = res.checkAmountFailType || null // 验证金额失败原因 1=>次数已达上限 2=>小额打款已过验证期
      }
      this.fristJoin = false
    },

    // 下一步
    next(obj) {
      this.viewStageType = obj?.viewStageType
      this.reSign = obj.reSign ? 1 : 0
      if (obj.reSign) { // 重新提交
        this.stageStatus = 0
        this.activeStep = YL_PLUS_SIGN_STAGE_STEP.BIND_BANK_ACCOUNT.STEP
        this.fristJoin = false
        return
      }
      // 绑定银行账户下一步自定义
      if (this.activeStep === YL_PLUS_SIGN_STAGE_STEP.BIND_BANK_ACCOUNT.STEP) {
        this.activeStep = YL_PLUS_SIGN_STAGE_STEP.ACCOUNT_OPEN_AUTH.STEP
      } else {
        // 查询签收流程阶段
        this.getSignStage()
      }
    },

    // 上一步
    back(activeStep) {
      this.activeStep = activeStep || 0
    },

    // 关闭窗口
    close() {
      this.initFormData()
      this.visible = false
    },
    // 设置表单数据
    setFormData(obj) {
      Object.assign(this.formData, obj)
    },
    // 初始化表单数据
    initFormData() {
      this.formData = {
        bankBranchName: '吉林亿联银行股份有限公司', // 开户支行名称，非空字段
        bankBranchCode: '************', // 开户行支行行号
        bankAccount: '', // 银行账号
        bankLoginPassword: '', // 简易版网银登录密码
        bankKey: '', // 简易版网银OTP密钥
        bankLoginAccount: '', // 简易版网银登录账号
        traderCorpId: '',
        traderAccountId: '',
        paymentChannel: '',
        bankAccountName: ''
      }
      this.activeStep = 0
    }
  }
}
</script>
