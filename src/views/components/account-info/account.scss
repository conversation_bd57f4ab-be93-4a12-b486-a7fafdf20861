.enterprise-info-box {
  display: flex;
  width: 100%;
  background: #FFFFFF;

  .left {
    margin: 16px 0 16px 16px;
    border-right: 1px solid #D9D9D9;
    width: 204px;
  }

  .right {
    overflow: auto;
    padding: 16px;
    width: 100%;
    max-height: 516px;
  }
}

.mar-left8 {
  margin-left: 8px;
}

.fix {
  display: flex;
  justify-content: space-between;
}

.width50 {
  width: 50%;
}

.describe-txt {
  font-size: 14px;
  font-weight: 400;
  color: #999999;
}

.word-mark {
  font-weight: 600;
  color: $--color-primary;
}

.custom-msg-tips {
  padding: 20px 16px !important;

  .elicon-close-circle {
    position: absolute;
    top: 18px;
    left: 8px;
  }

  .content-warp {
    padding: 0 16px;
    font-weight: 600;

    .title {
      padding-left: 10px;
    }

    .ul {
      padding: 10px 30px;
    }

    .ul li {
      list-style-type: disc !important;
      padding-bottom: 8px;
      font-size: 14px;

      &:last-child {
        padding-bottom: 0;
      }
    }
  }
}
