
import Vue from 'vue'
import store from '@/store/index'
// import { getRandomInt } from '@/utils/util'
import { OPERATION_MESSAGE_MSGTYPE } from '@/constants/notification.js'
import NotificationQueue from './notification-queue' // 消息队列
import '../scss/notification.scss' // css

import OrderNotificationCard from '../components/notification-card-list/order-notification-card.vue' // 订单交易通知组件
import AccountNotificationCard from '../components/notification-card-list/account-notification-card.vue' // 账户通知组件
import BargainNotificationCard from '@/views/components/inquiry-bargain/bargain/bargain-card/bargain-card.vue' // 议价通知组件
import PlatformNotificationCard from '../components/notification-card-list/platform-notification-card.vue' // 平台通知组件
import BreakContractNotificationCard from '../components/notification-card-list/break-contract-notification-card.vue' // 交易违约、违约判罚通知组件
import RadarSuccessNotificationCard from '../components/notification-card-list/radar-success-notification-card.vue' // 自动自动接单成功组件
import RadarStopNotificationCard from '../components/notification-card-list/radar-stop-notification-card.vue' // 自动停止接单组件
import IuquiryNotificationCard from '@/views/components/inquiry-bargain/inquiry/inquiry-notification-card/inquiry-notification-card.vue'
import AuditNotificationCard from '../components/notification-card-list/audit-notification-card.vue' // 审核申请通知组件
import couponUpgradNotificationCard from '../components/notification-card-list/coupon-upgrad-notification-card.vue' // 消费云豆送券活动弹窗
import EvidenceMaterialNotificationCard from '../components/notification-card-list/evidence-material-notification-card.vue' // 佐证材料审核通知组件
import AnomalousTipNotificationCard from '../components/notification-card-list/anomalous-tip-notification-card.vue' // 准入异常提示通知组件

const defaultOptions = {
  maxDurationCount: 1, // 消息提醒存在最大数量
  duration: 5000, // 消息提醒存在时间 默认5秒 (平台发出的消息通知不自动关闭，其余统一5秒后自动关闭)
  title: '消息提醒', // 消息提醒左上角标题
  offset: 0, // 消息位置偏移量
  position: 'top-right'
}

let idCount = 1 // 消息id(前端自定义字段，用来识别关闭对应mqtt消息)

// 消息弹窗关闭的倒计时秒数
let countDownTime = defaultOptions.duration

// 弹窗关闭倒计时的定时器
let intervalId = null

// 消息队列初始化
export const notificationQueue = new NotificationQueue(defaultOptions.maxDurationCount)

// 获取对应的通知组件
const getComponent = topic => {
  switch (topic) {
    case 'audit':
      return AuditNotificationCard
    case 'order':
      return OrderNotificationCard
    case 'account':
      return AccountNotificationCard
    case 'bargain':
      return BargainNotificationCard
    case 'platForm':
      return PlatformNotificationCard
    case 'breakContract':
      return BreakContractNotificationCard
    case 'radarSuccess':
      return RadarSuccessNotificationCard
    case 'radarStop':
      return RadarStopNotificationCard
    case 'consult':
      return IuquiryNotificationCard
    case 'upGrade':
      return couponUpgradNotificationCard
    case 'evidenceMaterial':
      return EvidenceMaterialNotificationCard
    case 'anomalousTip':
      return AnomalousTipNotificationCard
    default:
      break
  }
}

export default {

  /**
   * 执行消息提醒弹窗
   * @param {String} topic 消息提醒类型(account:开户 order交易订单 bargain议价 platForm平台 breakContract违约相关 issue发布提醒)
   * @param {Object|String} data 消息提醒数据
   * @param {Object} options 配置数据
   */
  notify(topic = '', data, options = {}) {
    try {
      const NotificationCard = getComponent(topic)
      if (!NotificationCard) {
      // eslint-disable-next-line no-console
        console.warn('组件不存在')
        return
      }

      // 如果消息组件存在initData初始化函数，则调用该函数处理data数据，再传入vm，否则直接传入data数据
      if (NotificationCard.methods.initData) {
        data = NotificationCard.methods.initData(data)
      }
      data.countDownTime = (data.duration || data.duration === 0) ? parseInt(data.duration / 1000) : (defaultOptions.duration / 1000)

      // 使用props传入data数据，消息组件监听data再赋值给自身定义的数据来使用
      let Ctor = Vue.extend({
        name: 'notify',
        render(h) {
          return h(NotificationCard, {
            props: {
              data,
              notificationId: `notification_${idCount}`,
              type: 1, // 该交易消息卡片组件用途 1-MQTT推送 2-消息列表
            }
          })
        }
      })
      let vm = new Ctor({
        store,
      })
      vm.$mount()

      // 监听组件内按钮点击事件，触发关闭消息提醒
      // vm.$on('close-notification', id => {
      //   notificationQueue.closeNotification(id)
      // })

      // ele Notification 组件配置
      const notificationOptions = Object.assign({}, {
        title: options.notificationTitle || defaultOptions.title,
        // eslint-disable-next-line no-underscore-dangle
        message: vm._vnode,
        duration: options.duration || defaultOptions.duration,
        customClass: options.customClass || 'notification-popup',
        position: options.position || defaultOptions.position,
        offset: options.offset || defaultOptions.offset,
        // eslint-disable-next-line no-underscore-dangle
        onClose: this.onClose.bind(vm._vnode, `notification_${idCount}`),
      }, options)

      idCount++
      // 延迟弹框显示的毫秒数(更新票据库存三分钟之内随机显示，其他默认200ms)
      let delay = OPERATION_MESSAGE_MSGTYPE[data.msgType] && OPERATION_MESSAGE_MSGTYPE[data.msgType].isDelayRandom ? data.delayRandom : 200
      setTimeout(() => {
        notificationQueue.run(notificationOptions)
        this.canCountDown(data)
      }, delay)
    } catch (error) {
      // eslint-disable-next-line no-console
      console.warn('error :>> ', error)
    }
  },
  // 执行消息弹窗关闭的倒计时
  canCountDown(data) {
    // 根据消息类型判断是否显示倒计时（目前平台消息通知中更新票据库存需要显示倒计时，其他不显示）
    if (!(OPERATION_MESSAGE_MSGTYPE[data.msgType] && OPERATION_MESSAGE_MSGTYPE[data.msgType].showCloseTime)) return
    if (data.duration === 0) return
    // 初始化倒计时时间，单位秒
    countDownTime = parseInt(data.duration / 1000) || defaultOptions.duration / 1000
    intervalId = setInterval(() => {
      countDownTime -= 1
      data.countDownTime = countDownTime
      if (countDownTime <= 0) {
        intervalId && clearInterval(intervalId)
        intervalId = null
        this.onClose(`notification_${idCount}`)
      }
    }, 1000)
  },
  // 消息提醒组件关闭（通知队列执行下一条消息）
  onClose(id) {
    // 弹窗关闭，清除存在的计时器
    if (intervalId) {
      clearInterval(intervalId)
      intervalId = null
    }
    // 若在Notification组件的onClose里调用，则一定要传false,否则会出错
    notificationQueue.closeNotification(id, false)
  },
}
