<!-- eslint-disable max-lines -->
<!-- 消息通知 -->
<style lang="scss" scoped>
.notification {
  // 悬浮固定样式
  &.fixed-style {
    position: fixed;
    right: 50px;
    bottom: 50px;
    z-index: 999;

    .notification-btn {
      user-select: none;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      width: 56px;
      height: 56px;
      text-align: center;
      color: $color-FFFFFF;
      background: $--color-primary;
      box-shadow:
        0 4px 12px rgb(0 0 0 / 12%),
        0 9px 28px 8px rgb(0 0 0 / 12%),
        0 6px 16px rgb(0 0 0 / 12%),
        0 3px 6px -4px rgb(0 0 0 / 22%);
      cursor: pointer;

      &:hover {
        background: $--color-primary;
      }

      .icon {
        margin-left: 3px;
        font-size: 40px;
        font-weight: 600;
        color: rgba($color:$color-FFFFFF, $alpha: 80%);
      }

      .message-badge {
        position: absolute;
        top: -5px;
        right: -5px;

        ::v-deep .el-badge__content {
          border: 2px solid $color-warning;
          border-radius: 50%;
          padding: 0;
          width: 24px;
          height: 24px;
          font-size: 12px;
          font-weight: 600;
          color: $color-warning;
          background-color: #FFFFFF;
          line-height: 20px;
          box-sizing: border-box;
        }

        &.more {
          right: 30px;

          ::v-deep .el-badge__content {
            border-radius: 9px;
            width: 30px;
            height: 20px;
            line-height: 18px;
            font-size: 12px;
          }
        }
      }
    }
  }

  // 头部导航样式
  &.header-style {
    display: inline-block;

    // height: 32px;
    // vertical-align: top;
    // line-height: 32px;
    // cursor: pointer;

    &.mqtt-connected {
      margin-right: 20px;
    }

    .notification-btn {
      position: relative;
      width: 50px;
      font-size: 12px;
      color: $color-FFFFFF;

      .icon {
        margin-left: 2px;
        font-size: 26px;
        vertical-align: -.25em;
        color: rgba($color:$color-FFFFFF, $alpha: 80%);
      }

      .text {
        cursor: pointer;

        &:hover {
          border-bottom: 1px solid  $color-FFFFFF;
        }

        line-height: 18px;
        font-size: 12px;
        font-weight: 500;
        text-align: left;
      }

      .message-badge {
        position: absolute;
        top: -11px;
        left: 37px;

        ::v-deep .el-badge__content {
          border: 0;
          border-radius: 50%;
          padding: 0;
          width: 18px;
          height: 18px;
          font-weight: 600;
          color: #FFFFFF;
          background-color: $color-warning;
          line-height: 18px;
        }

        &.max {
          position: absolute;
          top: -11px;
          left: 35px;

          ::v-deep .el-badge__content {
            border-radius: 12px;
            width: 22px;
            height: 18px;
            font-size: 12px;
            line-height: 18px;
          }
        }

        &.more {
          top: -11px;
          left: 35px;

          ::v-deep .el-badge__content {
            border-radius: 13px;
            width: 28px;
            height: 18px;
            line-height: 18px;
            font-size: 12px;
          }
        }
      }
    }
  }

  // 右侧工具栏样式
  &.tool-style {
    width: 60px;
    height: 65px;

    .notification-btn {
      display: flex;
      align-items: center;
      width: 60px;
      height: 65px;
      text-align: center;
      color: $color-FFFFFF;
      flex-direction: column;
      user-select: none;
      cursor: pointer;

      .icon {
        margin: 6px 0;
        font-size: 26px;
      }

      .message-badge {
        position: absolute;
        top: 9px;
        right: 6px;

        ::v-deep .el-badge__content {
          border-color: $color-warning;
          border-radius: 50%;
          padding: 0;
          width: 20px;
          height: 20px;
          font-weight: 600;
          color: $color-FFFFFF;
          background-color: $color-warning;
          line-height: 19px;
          box-sizing: border-box;
        }

        &.more {
          position: absolute;
          top: 9px;
          right: 30px;

          ::v-deep .el-badge__content {
            border-radius: 18px;
            width: 26px;
            height: 18px;
            line-height: 18px;
            font-size: 12px;
          }
        }

        &.mixmore {
          position: absolute;
          top: 9px;
          right: 30px;

          ::v-deep .el-badge__content {
            border-radius: 30px;
            min-width: 26px;
            height: 20px;
            font-size: 12px;
            line-height: 20px;
          }
        }
      }
    }
  }
}
</style>

<style lang="scss">
.notification-home-dialog {
  .el-message-box__message {
    overflow: auto;
    max-height: 400px;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin: 20px 0;
    line-height: 1.5;
  }
  h1 { font-size: 32px; }
  h2 { font-size: 24px; }
  h3 { font-size: 18.72px; }
  h4 { font-size: 16px; }
  h5 { font-size: 13.28px; }

  p {
    margin: 15px 0 !important;
  }

  em > span {
    font-style: italic;
  }

  ul,
  ol {
    padding-left: 20px;
  }

  li {
    margin: 10px 0;
    line-height: inherit;
  }

  ol li {
    list-style: decimal;
  }

  ul li {
    list-style: initial;
  }

  /* 表格 */
  table {
    margin: 0 auto;
    border-collapse: collapse;
  }

  table th,
  table td {
    border: 1px solid #CCCCCC;
    min-width: 50px;
    height: 20px;
    text-align: left;
  }

  table th {
    text-align: center;
    background-color: #F1F1F1;
  }

  video {
    display: block;
    margin: 0 auto;
  }

  img {
    min-width: 20px;
    max-width: 100%;
    min-height: 20px;
    cursor: default;
  }
}
</style>

<template>
  <div
    class="notification"
    :class="{
      'fixed-style': styleType === 1,
      'header-style': styleType === 2,
      'tool-style': styleType === 3,
      [`mqtt-${mqttStatus}`]: true,
    }"
  >
    <div v-show="styleType !== 2 || mqttStatus === 'connected'" class="notification-btn" @click="showNotificationList">
      <span class="text">消息</span>
      <icon class="icon" type="erp-xiaoxi" size="20" />
      <!--
        <span>
        消息{{ styleType === 3 ? '通知' : '' }}
        </span>
      -->

      <el-badge
        v-if="msgUnReadCount > 0"
        class="message-badge"
        :class="{
          'max': msgUnReadCount > 9 && msgUnReadCount < 100,
          'more': msgUnReadCount > 99
        }"
        :value="msgUnReadCount > 99 ? '99+' : msgUnReadCount"
      />
    </div>

    <!-- 消息列表弹窗 -->
    <NotificationListDialog v-if="isShowNotificationList" ref="notificationList" @notification-all-count="val => msgUnReadCount = val" />

    <!-- 消息提醒音频播放组件 -->
    <NotificationAudio
      ref="notificationAudio"
      :remind-setting="remindSetting"
      :mqtt-data="mqttData"
    />

    <!-- 接单-票据详情 -->
    <ReceiveOrderDetail v-if="isShowReceiveOrderDetail" ref="receiveOrderDetail" @success="handleRefresh" />

    <!-- 预警通知弹窗 -->
    <NotificationWarnNoticeDialog ref="notificationWarnNotice" @again="openWarnNoticeDialog('run')" />

    <!-- 询单--详情 -->
    <InquiryReplayDialog
      v-if="showInquiryReplayDialog"
      :card-data="inquiryData.cardData"
      :inquiry-type="inquiryData.inquiryType"
      @success="closeInquiryReplayDialog"
    />
  </div>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex'
import { WEB_SOCKET_EVENT } from '@/websocket/constant' // mqtt监听事件
import notification from './js/notification' // 消息派发
import NotificationListDialog from './components/notification-list-dialog/notification-list-dialog.vue' // 消息列表弹窗
import NotificationAudio from './components/notification-audio/notification-audio.vue' // 消息提醒音频播放组件
import ReceiveOrderDetail from '@/views/pages/market/components/receive-order-detail/receive-order-detail.vue' // 接单详情组件
import NotificationWarnNoticeDialog from './components/notification-warn-notice-dialog/notification-warn-notice-dialog.vue' // 预警通知弹窗
import InquiryReplayDialog from '@/views/components/inquiry-bargain/inquiry/inquiry-replay-dialog/inquiry-replay-dialog.vue' // 询单--详情-组件
import {
  SITE_OPEN_ACCOUNT, // 渠道开户
  REAL_NAME_CERTIFICATION, // 实名认证
  MQTT_ORDER, // 收到mqtt消息推送，交易、催单、交易中的订单被客服强推介入等，刷新页面
  MQTT_BARGAIN, // 收到议价消息提醒
  NOTIFICATION_UNREAD_COUNT, // 获取消息提醒未读数量
  RECEIVE_ORDER_DETAIL_NOTIFICATION, // 打开消息中心里的接单详情弹窗事件
  XUANDAN_DETAIL_NOTIFICATION, // 消息里的询单详情
} from '@/event/modules/site' // 监听事件常量
import { INQUIRYREFRESH } from '@/constants/inquiry-bargain'
import {
  TRANSACTION_STATUS, // 订单详情-交易状态
  // SDM_NAME,
} from '@/constant'

import {
  MSG_TYPE, // 消息提醒类型,
  OPERATION_MESSAGE_MSGTYPE,
  MQTT_REFRESH_TIME, // 节流时间
  MQTT_ALERT_TIME
} from '@/constants/notification'

import Storage from '@/common/js/storage' // 本地缓存对象
import {
  NOTIFICATION_LAST_ID, // 最后的消息提醒id
} from '@/constant-storage'
import { debounce } from '@/common/js/util'
import { CORP_STATUS } from '@/constants/open-account' // 开户相关常量
import { getRandomInt } from '@/utils/util'

export default {
  name: 'notification',

  components: {
    NotificationListDialog, // 消息列表弹窗
    NotificationAudio, // 消息提醒音频播放组件
    ReceiveOrderDetail, // 接单详情组件
    NotificationWarnNoticeDialog, // 预警通知弹窗
    InquiryReplayDialog // 询单--详情
  },

  props: {
    // 消息提醒样式类型 1-悬浮固定样式 2-头部导航样式 3-右侧工具栏样式
    styleType: {
      type: Number,
      default: 1
    }
  },

  data() {
    return {
      isShowNotificationList: false, // 是否加载消息列表组件
      msgUnReadCount: 0, // 消息提醒的所有未读消息数量
      mqttData: {}, // 接收到的mqtt推送消息数据
      isShowReceiveOrderDetail: false, // 是否显示接单详情弹窗
      isActivePage: false, // 当前页面是否为可见状态
      mqttFlag: false, // 误删 -- 消息频率开关
      warnNoticeList: [], // 预警通知弹窗队列
      onRefreshDebounce: null,
      showInquiryReplayDialog: false, // 是否显示询单详情
      inquiryData: {}, // 询单数据
    }
  },

  computed: {
    // mqtt 连接状态, disconnected(未连接)/connecting(连接中)/connected(已连接)
    ...mapState('user', ['mqttStatus']),
    ...mapActions('user', {
      getSdmInfo: 'getSdmInfo'
    }),
    ...mapGetters('user', {
      remindSetting: 'remindSetting', // 消息提醒设置
      couponOpen: 'couponOpen', // 优惠券活动开关，1开启，0关闭
      sellerBankAccountList: 'sellerBankAccountList', // 回款账户列表
      sdmInfo: 'sdmInfo', // 米账号信息
    }),
    sellerBankAccountId() {
      return this.$store.state.common.sellerBankAccountId
    },

    // 当前页面路径
    path() {
      return this.$route?.path || '/'
    }

  },

  mounted() {
    // 初始化
    this.init()
    this.onRefreshDebounce = debounce(this.mqttRefresh, MQTT_REFRESH_TIME)
  },

  methods: {
    ...mapActions('user', {
      getRemindSetting: 'getRemindSetting', // 获取用户提醒配置
    }),

    // 初始化
    init() {
      // 接口数据初始化
      this.initData()

      // 当前页面可见状态事件监听
      this.pageVisibilityListener()

      // MQTT推送监听
      this.handleMQTTListener()

      // 全局事件监听
      this.handleEvenListener()
    },

    // 接口数据初始化
    initData() {
      try {
      // 获取用户提醒设置
        if (!this.remindSetting) {
          this.getRemindSetting()
        }

        // 获取所有未读的消息数量
        this.getMsgUnReadCount()
      } catch (error) {
      // console.log('error :>> ', error)
      }
    },

    // 平台消息推送
    operationMessageNotice(data) {
      const { way } = data
      const ways = way.toString().split(',')
      const indexNotification = '1' // 欢迎页消息通知
      const otherNotification = '2' // 站内信通知
      const cuteHandNotification = '3' // 智能助手页面通知
      // 1.首页弹窗在首页展示
      let showWelcomeNotification = ways.includes(indexNotification) && this.path.includes('/user-center/welcome')
      // 3.智能助手页面通知
      let showCuteHandNotification = ways.includes(cuteHandNotification) && this.path.includes('/user-center/cute-hand')
      if (showWelcomeNotification || showCuteHandNotification) {
        this.$alert(data.content, data.title, {
          dangerouslyUseHTMLString: true,
          type: '',
          customClass: 'notification-home-dialog',
          confirmButtonText: '我知道了'
        })
        // 消息提醒音频播放处理
        this.handlePlayAudio(data)
        return
      }

      // 是否推送消息  2. 站内信在其他页面展示
      const isNotify = (ways.includes(otherNotification))
      if (isNotify) {
        let durationTime = data.closeTime ? data.closeTime * 1000 : 0
        let delayRandom = OPERATION_MESSAGE_MSGTYPE[data.msgType] && OPERATION_MESSAGE_MSGTYPE[data.msgType].isDelayRandom ? getRandomInt() : null
        let notifyOpts = {
          duration: durationTime,
          delayRandom,
          showCloseTime: OPERATION_MESSAGE_MSGTYPE[data.msgType] && OPERATION_MESSAGE_MSGTYPE[data.msgType].showCloseTime,
          buttonLink: data.buttonLine
        }
        this.handleNotify('platForm', { ...data, ...notifyOpts }, {
          notificationTitle: data.msgType && OPERATION_MESSAGE_MSGTYPE[data.msgType].id ? data.title : '消息通知', // '消息通知', // 消息通知左上角标题
          duration: durationTime, // 消息提醒存在时间 (平台发出的消息通知不自动关闭，其余统一5秒后自动关闭,0为不关闭)
        })
        // 消息提醒音频播放处理
        this.handlePlayAudio({ ...data, delayRandom })
      }
    },

    // MQTT推送监听
    handleMQTTListener() {
      // 监听交易状态刷新事件
      this.$websocket.on(WEB_SOCKET_EVENT.ORDER_STATUS_REFRESH, data => {
        const {
          msgType,
        } = data

        const options = {
          notificationTitle: '', // 消息通知左上角标题
        }
        switch (msgType) {
          case MSG_TYPE.ORDER_REFRESH.id: // 1-交易订单刷新状态
            options.notificationTitle = MSG_TYPE.ORDER_REFRESH.notificationTitle
            break
          case MSG_TYPE.INTENTION.id: // 2-意向价格
            options.notificationTitle = MSG_TYPE.INTENTION.notificationTitle
            break
          case MSG_TYPE.ISSUE.id: // 5-发布提醒
            options.notificationTitle = MSG_TYPE.ISSUE.notificationTitle
            break
          case MSG_TYPE.COMMERCIAL_AUDIT_NOTICE.id: // 6-审核通知
            options.notificationTitle = MSG_TYPE.ISSUE.notificationTitle
            break
          default:
            break
        }
        // 根据消息提醒的设置-是否弹出消息提示框
        if (this.isShowNotificationBySetting(data, this.remindSetting)) {
        // 触发消息提醒弹窗
          this.handleNotify('order', data, options)

          // 消息提醒音频播放处理
          this.handlePlayAudio(data, this.isPlayAudioBySetting(data, this.remindSetting))
        }

        // 通知服务大厅、订单详情更新数据
        this.onRefreshDebounce(data, 'orderStatusRefresh')
        // this.$event.emit(MQTT_ORDER, {
        //   data,
        //   type: 'orderStatusRefresh', //  交易刷新
        // })

        // 若当前已打开消息中心那么刷新列表否则只更新消息数量
        this.$nextTick().then(() => {
          this.$refs.notificationList?.dialogVisible ? this.handleRefresh() : this.getMsgUnReadCount()
        })
      })
      // setTimeout(() => {
      //   // 以下为 交易订单/意向订单消息/发布提醒 测试代码，可删
      //   this.$websocket.notify(WEB_SOCKET_EVENT.ORDER_STATUS_REFRESH, {
      //     acceptorName: '广州壁虎信息科技有限公司', // 承兑人名称
      //     draftAmount: 10000, //  票据金额，单位元
      //     transactionStatus: 13, // 交易状态，11-未交易、12-待确认、13-待支付、14-支付中、15-待背书、16-待签收、17-校验中、18-交易完成、21-确认阶段持票方已取消、22-确认接单解票方取消订单，23-确认阶段平台已取消、24-支付阶段持票方已取消、245支付阶段接单方已取消、26-支付阶段平台已取消、27-背书阶段持票方取消中、28-背书阶段持票方已取消、29-背书阶段平台已取消、30-签收阶段接单方取消中、31-签收阶段接单方已取消、32 签收阶段持票方已取消，33-签收阶段平台已取消
      //     draftActualAmount: 9500, // 订单到账金额
      //     // draftPaymentAmount: 9000, // 订单实付金额
      //     sendTime: '2021-12-21 12:54:25', // 确认时间（消息发送的时间）
      //     radarType: 1, // 是否自动类型，0不是，1是
      //     msg: '提示123',
      //     orderNo: 121121215151,
      //     lastSixDraftNo: 1234567, // 票号后六位
      //     id: Math.ceil(Math.random() * 100),
      //     msgId: Math.ceil(Math.random() * 100),
      //     msgType: 1, // 1-订单状态变更消息 2-意向订单消息 5-发布提醒
      //     fastTrade: 0, // 是否极速票，0不是，1是
      //     role: 2, // 操作角色，1-接单方，2-持票方
      //     _service: 'topic_shendu_order',
      //     _msgtype: 'orderStatusRefresh',
      //     topicChannel: 'topic_shendu_order', // 消息类型  和 _service 一致
      //   })
      // }, 3000)

      // 监听意向价格推送事件
      this.$websocket.on(WEB_SOCKET_EVENT.INTENDED_ORDER_NOTICE, data => {
        // console.log('意向价格推送消息', data)
        this.handleNotify('order', data)
        // 消息提醒音频播放处理
        this.handlePlayAudio(data)
        // 通知服务大厅、订单详情更新数据
        // this.onRefreshDebounce(data, 'accelerateOrder')
        // this.$event.emit(MQTT_ORDER, {
        //   data,
        //   type: 'accelerateOrder', //  催单
        // })

        // 若当前已打开消息中心那么刷新列表否则只更新消息数量
        this.$nextTick().then(() => {
          this.$refs.notificationList?.dialogVisible ? this.handleRefresh() : this.getMsgUnReadCount()
        })
      })

      // 监听催单刷新事件
      this.$websocket.on(WEB_SOCKET_EVENT.ACCELERATE_ORDER, data => {
        // console.log('催单mqtt消息', data)
        this.handleNotify('order', data)
        // 消息提醒音频播放处理
        this.handlePlayAudio(data)
        // 通知服务大厅、订单详情更新数据
        // this.onRefreshDebounce(data, 'accelerateOrder')
        // this.$event.emit(MQTT_ORDER, {
        //   data,
        //   type: 'accelerateOrder', //  催单
        // })
      })

      // 监听订单变更票号事件
      this.$websocket.on(WEB_SOCKET_EVENT.ORDER_CHANGE_DRAFTNO_NOTICE, data => {
        this.handleNotify('order', data)
      })

      // 监听交易订单签署合同状态通知
      this.$websocket.on(WEB_SOCKET_EVENT.ORDER_CONTRACT_STATUS_NOTICE, data => {
        this.handleNotify('order', data, { notificationTitle: '签署提醒' })
        this.$event.emit(MQTT_ORDER, { data })
      })
      // 错号补充信息签署提醒
      this.$websocket.on(WEB_SOCKET_EVENT.ORDER_WRONGNUMBER_CONTRACT_NOTICE, data => {
        this.handleNotify('order', data, { notificationTitle: '签署提醒' })
        this.$event.emit(MQTT_ORDER, { data })
      })

      // feat:下线隐藏佐证材料相关内容

      // 监听订单佐证材料审核驳回
      // this.$websocket.on(WEB_SOCKET_EVENT.ORDER_EVIDENCE_AUDIT_REJECT_NOTICE, data => {
      //   const options = {
      //     notificationTitle: '审核结果提醒', // 消息通知左上角标题
      //   }
      //   this.handleNotify('evidenceMaterial', data, options)
      // })

      // 监听订单佐证材料审核通过
      // this.$websocket.on(WEB_SOCKET_EVENT.ORDER_EVIDENCE_AUDIT_SUCCESS_NOTICE, data => {
      //   const options = {
      //     notificationTitle: '审核结果提醒', // 消息通知左上角标题
      //   }
      //   this.handleNotify('evidenceMaterial', data, options)
      // })

      // 监听订单佐证材料上传提醒
      // this.$websocket.on(WEB_SOCKET_EVENT.ORDER_EVIDENCE_UPLOAD_REMIND_NOTICE, data => {
      //   const options = {
      //     notificationTitle: '上传佐证材料提醒', // 消息通知左上角标题
      //   }
      //   this.handleNotify('evidenceMaterial', data, options)
      // })

      // 监听企业实名预警事件
      this.$websocket.on(WEB_SOCKET_EVENT.REAL_NAME_STATUS_WARN, data => {
        // console.log('企业实名预警mqtt消息', data)
        this.openWarnNoticeDialog('push', { type: 'warn', data })
      })

      // 监听企业实名状态变更
      this.$websocket.on(WEB_SOCKET_EVENT.REAL_NAME_STATUS_CHANGE, data => {
        // console.log('企业实名状态变更mqtt消息', data)
        if (data?.corpStatus === CORP_STATUS.REAL_NAME_FAILURE.id) {
          this.openWarnNoticeDialog('push', { type: 'invalid', data })
        } else {
          this.handleNotify('account', data, {
            notificationTitle: '账户提醒', // 消息通知左上角标题本来写死的标题 现在配置在右上角
          })
        }
      })

      // 监听企业实名认证申请结果
      this.$websocket.on(WEB_SOCKET_EVENT.REAL_NAME_AUTH_APPLY_RESULT, async data => {
        // console.log('企业实名认证申请结果mqtt消息', data)
        await this.$store.dispatch('user/getIsOpenAccount')
        // realNameAuthType 0-首次实名，1-收到实名预警通知后重新实名，2-实名失效后重新实名，3-开通失败的支付渠道进行重新实名
        if (data.realNameAuthType === 3) {
          this.$event.emit(SITE_OPEN_ACCOUNT, {
            isMqtt: true,
            reAuthPaymentChannel: data.reAuthPaymentChannel,
            realNameAuthType: data.realNameAuthType,
            startOver: true
          })
        } else {
          this.$event.emit(REAL_NAME_CERTIFICATION, {
            isMqtt: true,
            realNameAuthType: data.realNameAuthType,
            startOver: true
          })
        }
      })

      // 监听议价通知事件
      this.$websocket.on(
        WEB_SOCKET_EVENT.ORDER_BARGAIN_STATUS_REFRESH,
        async data => {
          // 通知议价消息列表更新数据
          this.$event.emit(MQTT_BARGAIN)
          // 生成随机数 用来监听随机数 控制图标闪烁
          this.$store.commit('market/setNewMagCount', Math.floor(Math.random() * 10))

          // 后端 isShowPromptBox 字段控制是否仅刷新列表，0展示，1不展示
          if (data.isShowPromptBox === 1) {
            return
          }

          // 闭市不显示议价弹窗
          await this.$store.dispatch('common/getCloseMarket')
          if (this.$store.state.common.closeMarket) {
            return
          }

          // 消息提醒音频播放处理
          this.handlePlayAudio(data, this.isPlayAudioBySetting(data, this.remindSetting))

          // 根据消息体判断是否需要展示
          if (this.isShowNotificationBySetting(data, this.remindSetting)) {
            await this.$store.dispatch('user/getPassedBankCardList')
            data.sellerBankAccountList = this.sellerBankAccountList // 传入回款账户列表
            await this.$store.dispatch('common/getNewVersionDraftConfig')
            // 自定义设置回款账户id 消息未返回回款账户Id就取配置中的回款账户id
            data.sellerBankAccount = Number(data.sellerBankAccountId) || Number(this.sellerBankAccountId)
            // 米账号信息
            await this.$store.dispatch('user/getSdmInfo')
            data.sdmInfo = this.sdmInfo
            this.handleNotify('bargain', data, {
              notificationTitle: '议价消息提醒', // 消息通知左上角标题
            })
          }
        }
      )

      // 监听交易中的订单被客服强推介入的通知
      this.$websocket.on(WEB_SOCKET_EVENT.DISPUTE_ORDER_REFRESH, () => {
        // console.log('交易中的订单被客服强推介入的通知', data)
        // 通知订单列表、详情更新数据
        this.$event.emit(MQTT_ORDER)
      })

      // 监听平台消息推送事件
      this.$websocket.on(WEB_SOCKET_EVENT.OPERATION_MESSAGE_NOTICE, data => {
        // console.log('平台mqtt消息推送', data)
        this.operationMessageNotice(data)
      })

      // 监听平台消息推送所有人事件
      this.$websocket.on(WEB_SOCKET_EVENT.OPERATION_MESSAGE_ALL_CORP_NOTICE, data => {
        // console.log('平台mqtt消息推送所有人', data)
        this.operationMessageNotice(data)
      })

      // 监听关注的企业过期推送消息
      this.$websocket.on(WEB_SOCKET_EVENT.FOLLOW_EXPIRE_NOTICE, data => {
        this.$alert(data.msg, '消息通知', {
          customClass: 'notification-home-dialog',
          confirmButtonText: '查看',
          callback: action => {
            if (action === 'confirm') {
              this.$router.push('/user-center/focus-on?currentBreach=1')
            }
          },
        })
        // 消息提醒音频播放处理
        this.handlePlayAudio(data)
      })

      // 审核消息通知 后台注释了--废弃
      this.$websocket.on(WEB_SOCKET_EVENT.COMMERCIAL_BILL_ACCEPTOR_AUDIT_NOTICE, data => {
        this.handleNotify('audit', data, {
          notificationTitle: '消息提醒', // 消息通知左上角标题
        })
        // 若当前已打开消息中心那么刷新列表否则只更新消息数量
        this.$nextTick().then(() => {
          this.$refs.notificationList?.dialogVisible ? this.handleRefresh() : this.getMsgUnReadCount()
        })
        // this.operationMessageNotice(data)
      })

      // this.$websocket.notify(WEB_SOCKET_EVENT.FOLLOW_EXPIRE_NOTICE, {
      //   _service: 'WEB_SOCKET_EVENT.FOLLOW_EXPIRE_NOTICE',
      //   msgId: 11031533889309941762,
      //   _msgtype: WEB_SOCKET_EVENT.FOLLOW_EXPIRE_NOTICE,
      //   sendTime: '2022-10-27 18:32:38',
      //   topicChannel: 'WEB_SOCKET_EVENT.FOLLOW_EXPIRE_NOTICE'
      // })

      // // 以下为 平台消息通知 测试代码，可删
      // this.$websocket.notify(WEB_SOCKET_EVENT.OPERATION_MESSAGE_ALL_CORP_NOTICE, {
      //   _service: 'topic_shendu_corp',
      //   msgId: 1103153388930994176,
      //   _msgtype: 'operationMessageAllCorpNotice',
      //   title: '消息推送测试7',
      //   content: '<p>\t&nbsp;消息推送测试7</p>',
      //   way: '2',
      //   sendTime: '2022-10-27 18:32:38',
      //   topicChannel: 'operationMessageAllCorpNotice'
      // })

      // 监听交易违约消息推送事件
      this.$websocket.on(WEB_SOCKET_EVENT.BROKE_CONTRACT_RECORD_NOTICE, data => {
        this.handleNotify('breakContract', data, {
          notificationTitle: MSG_TYPE.BROKE_CONTRACT_RECORD_NOTICE.notificationTitle, // 消息通知左上角标题
          // duration: 100000000
        })

        // 消息提醒音频播放处理
        this.handlePlayAudio(data)
      })

      // 准入异常名单推送提醒
      this.$websocket.on(WEB_SOCKET_EVENT.OPERATION_ABNORMAL_RISK_MESSAGE_ALL_CORP_NOTICE, data => {
        this.handleNotify('anomalousTip', data, {
          notificationTitle: '风险提示', // 消息通知左上角标题
          duration: 0, // 消息提醒存在时间 (平台发出的消息通知不自动关闭，其余统一5秒后自动关闭,0为不关闭)
          customClass: 'anomalous-tip-notification-card',
          showClose: false
        })
        // 消息提醒音频播放处理
        this.handlePlayAudio(data)
      })

      // 监听违约判罚消息推送事件
      this.$websocket.on(WEB_SOCKET_EVENT.BROKE_CONTRACT_RECORD_PUNISH_NOTICE, data => {
        this.handleNotify('breakContract', data, {
          notificationTitle: MSG_TYPE.BROKE_CONTRACT_RECORD_PUNISH_NOTICE.notificationTitle, // 消息通知左上角标题
          // duration: 100000000
        })

        // 消息提醒音频播放处理
        this.handlePlayAudio(data)
      })

      // 监听自动自动接单成功推送事件
      this.$websocket.on(WEB_SOCKET_EVENT.BILL_ORDER_NEED_RULE_SUCCESS, data => {
        // eslint-disable-next-line no-console
        console.log('监听自动自动接单成功推送事件', data)
        // 根据消息提醒的设置-是否弹出消息提示框
        if (this.isShowNotificationBySetting(data, this.remindSetting)) {
          data.msg = '系统已自动为您接单，请尽快处理'
          this.handleNotify('radarSuccess', data, {
            notificationTitle: '系统接单', // 消息通知左上角标题
          })

          // 消息提醒音频播放处理
          this.handlePlayAudio(data, this.isPlayAudioBySetting(data, this.remindSetting))
        }

        // 通知服务大厅、订单详情更新数据
        this.onRefreshDebounce(data, 'orderStatusRefresh')
        // this.$event.emit(MQTT_ORDER, {
        //   data,
        //   type: 'orderStatusRefresh', //  交易刷新
        // })
      })

      // 以下为 监听自动自动接单成功推送事件 测试代码，可删
      // this.$websocket.notify(WEB_SOCKET_EVENT.BILL_ORDER_NEED_RULE_SUCCESS, {
      //   draftAmount: 10000, //  票据金额，单位元
      //   sendTime: '2021-12-21 12:54:25', // 确认时间（消息发送的时间）
      //   lastSixDraftNo: 121121215151, // 票号后六位
      // })

      // 监听自动停止接单推送事件
      this.$websocket.on(WEB_SOCKET_EVENT.BILL_ORDER_NEED_RULE_STOP, data => {
        // console.log('监听自动停止接单推送事件', data)
        this.handleNotify('radarStop', data, {
          notificationTitle: '停止接单', // 消息通知左上角标题
        })
      })

      // 以下为 监听自动停止接单推送事件 测试代码，可删
      // this.$websocket.notify(WEB_SOCKET_EVENT.BILL_ORDER_NEED_RULE_STOP, {
      //   ruleName: '票面十万无保证无赔偿', //  名称
      //   stopType: 1, // 停止类型，1-接单额度满，2-接单截止时间到，3-授信额度已到，4-保证金不足，5-晚上八点自动停止
      // })

      // 以下为 交易违约 测试代码，可删
      // this.$websocket.notify(WEB_SOCKET_EVENT.BROKE_CONTRACT_RECORD_NOTICE, {
      //   msg: '您的订单出现违约',
      //   orderNo: 121121215151,
      //   lastSixDraftNo: '555555', // 票号
      //   brokeLevel: 1, // 违约等级 0 轻 1中 2重
      //   brokeMessage: '待确认环节，超时未确认', // 原因
      //   payMargin: 10, // 扣除深
      //   msgType: 3, // 3-交易违约 4-违约判罚
      // })

      // 以下为 违约判罚 测试代码，可删
      // this.$websocket.notify(WEB_SOCKET_EVENT.BROKE_CONTRACT_RECORD_PUNISH_NOTICE, {
      //   msg: '您的违约已生成判罚',
      //   orderNo: 121121215151,
      //   lastSixDraftNo: '555555', // 票号
      //   brokeLevel: 1, // 违约等级 0 轻 1中 2重
      //   brokeMessage: '待确认环节，超时未确认', // 原因
      //   payMargin: 10, // 扣除深
      //   msgType: 4, // 3-交易违约 4-违约判罚
      // })

      // 以下为 企业实名预警 测试代码，可删
      // this.$websocket.notify(WEB_SOCKET_EVENT.REAL_NAME_STATUS_WARN, {
      //   id: 1, // 企业id
      //   corpName: '广州壁虎信息科技有限公司', // 企业名称
      //   invalidWarnType: 1, // 实名失效预警类型：0-无，1-证件过期提醒，2-账户冻结提醒，3-企业违法/变更提醒，4-企业经营异常提醒，5-企业工商信息变更提醒
      //   invalidWarnMsg: '实名失效预警内容实名失效预警内容实名失效预警内容实名失效预警内容实名失效预警内容', // 实名失效预警内容
      //   sendTime: 1639152000000, // 消息发送的时间
      //   _msgtype: WEB_SOCKET_EVENT.REAL_NAME_STATUS_WARN
      // })

      // this.$websocket.notify(WEB_SOCKET_EVENT.REAL_NAME_STATUS_CHANGE, {
      //   id: 1, // 企业id
      //   corpName: '广州壁虎信息科技有限公司', // 企业名称
      //   corpStatus: 3, // 企业状态：0-未实名，1-实名中，2-已实名，3-实名失效
      //   failReason: 'FREEZE', // 企业实名失效原因类型，FREEZE-已冻结,EXPIRED-已过期，CREDIT_IMMEDIATE-因客户企业违法/工商变更而失效
      //   sendTime: 1639152000000, // 消息发送的时间
      // })

      // 以下为 议价通知 测试代码，可删
      // this.$websocket.notify(
      //   WEB_SOCKET_EVENT.ORDER_BARGAIN_STATUS_REFRESH,
      //   // TODO 议价 测试数据修改
      //   {
      //     id: '246', // 主键
      //     role: 1, // 操作角色，1-资方，2-票方
      //     orderNo: '1540240603190632450', // 订单号
      //     acceptorName: '广州壁虎信息科技有限公司', // 承兑人名称
      //     draftAmount: 100000, // 票据金额，单位元
      //     maturityDate: '2022-01-01', // 到期日
      //     lakhFee: 51515151, // 每十万扣款
      //     annualInterest: 1.1111, // 年利率真实年利率100
      //     interestDays: 182, // 计息天数
      //     endTime: new Date().getTime() + 30000, // 议价超时时间
      //     updatedAt: new Date().getTime(),
      //     bidLakhFee: 122, // 出价每十万扣款
      //     bidAnnualInterest: 1.2222, // 出价年利率真实年利率100
      //     dickerLakhFee: -133, // 还价每十万扣款
      //     dickerAnnualInterest: 1.3333, // 还价年利率真实年利率100
      //     bargainCount: 0, // 议价次数
      //     createdAt: '', // 创建时间
      //     isFinished: 0, // 是否已关闭
      //     bargainStatus: 2, // 议价状态，0-议价中，1-议价成功，2-议价失败
      //     topicChannel: WEB_SOCKET_EVENT.ORDER_BARGAIN_STATUS_REFRESH,
      //     bargainingState: 3, // 议价中状态:
      //     // 0-买方发起议价、1-买方撤销议价、2-卖方同意议价、
      //     // 3-卖方拒绝议价、4-卖方还价、   5-卖方撤销还价、
      //     // 6-买方同意还价、7-买方拒绝还价、8-超时议价失败、9-订单状态变更取消
      //     draftNo: '2342342324324342332233243243243', // TODO
      //     frontImageUrl: 'https://shendu-qa.oss-cn-hangzhou.aliyuncs.com/draft/2022/01/12/nUSkOc_1641955221911_票面正面1641955221817.png,https://shendu-qa.oss-cn-hangzhou.aliyuncs.com/draft/2022/01/12/nUSkOc_1641955221911_票面正面1641955221817.png' // TODO
      //   }
      // )
      // 推送活动百分比--很早以前票方返券活动
      // this.$websocket.on(WEB_SOCKET_EVENT.ACTICITY_MARKETING_PROGRESS_NOTICE, data => {
      //   this.$store.commit('user/setCouponLevel', data)

      //   // 如果升级 要有弹窗提醒 upGrade===1 升级
      //   if (data.upGrade && this.couponOpen) {
      //     this.handleNotify('upGrade', data, {
      //       notificationTitle: data.rewardType === 1 ? `消费${SDM_NAME}送券活动` : '完成笔数送券活动', // 消息通知左上角标题
      //       duration: 300000, // 消息提醒存在时间 (平台发出的消息通知不自动关闭，其余统一5秒后自动关闭,0为不关闭)
      //     })
      //   }
      // })

      // 以下为 推送活动百分比 测试代码，可删
      // setTimeout(() => {
      //   this.$websocket.notify(WEB_SOCKET_EVENT.ACTICITY_MARKETING_PROGRESS_NOTICE, {
      //     alreadyReward: 50000,
      //     topicChannel: 'activityMarketingProgressNotice',
      //     beginDate: '2023-03-01',
      //     currentPeriodAchievement: 0,
      //     endDate: '2023-03-31',
      //     highestLevel: null,
      //     id: null,
      //     nextLevelGap: 500,
      //     nextLevelReward: 60000,
      //     progressPercentage: 10,
      //     receiveCouponStatus: 3,
      //     rewardType: 1,
      //     upGrade: 1 // 是否升级
      //   })
      // }, 0)

      // 询单-列表刷新||数量
      this.$websocket.on(WEB_SOCKET_EVENT.BILLCONSULTLISTREFRESHNOTICE, data => {
        this.$store.commit('market/setInquiryBargainCount', data)
        // 生成随机数 用来监听随机数 控制图标闪烁
        this.$store.commit('market/setNewMagCount', Math.floor(Math.random() * 10))
        this.$event.emit(INQUIRYREFRESH, data)
      })
      // 以下为 询单 测试代码，可删
      // setInterval(() => {
      //   this.$websocket.notify(WEB_SOCKET_EVENT.BILLCONSULTORDERNOTICE, {
      //     acceptorName: '浙商银行苏州分行',
      //     annualInterest: 0.0212,
      //     billOrderId: 741632,
      //     buyerTotalCount: 0,
      //     consultStatus: 0,
      //     consultTime: '2022-06-25T15:23:30.929',
      //     draftAmount: 2,
      //     draftNo: '131630500002920220610581641667',
      //     id: 9,
      //     lakhFee: 10,
      //     maturityDate: '2022-12-10',
      //     messageTitle: '资方咨询票据是否包含个体户，请尽快回复',
      //     messageType: 39,
      //     orderNo: '1540300180611735554',
      //     sellerConsultOrderDetailsList: [
      //       {
      //         billConsultOrderId: 9,
      //         consultOrderDetails: [
      //           {
      //             billConsultOrderId: 9,
      //             consultTime: '2022-06-25T14:27:39',
      //             consultType: 3,
      //             id: 12,
      //             replyStatus: 0
      //           }
      //         ]
      //       }
      //     ],
      //     sellerTotalCount: 1,
      //     topicChannel: 'billConsultOrderNotice',
      //     totalCount: 1,
      //     userRole: 2,
      //   })
      // }, 0)

      // this.$websocket.notify(WEB_SOCKET_EVENT.BILLCONSULTLISTREFRESHNOTICE, {
      //   buyerTotalCount: 0,
      //   messageType: 43,
      //   sellerTotalCount: 0,
      //   topicChannel: 'billConsultListRefreshNotice',
      //   totalCount: 0,
      // })

      // 以下为 佐证材料驳回 测试代码，可删
      // this.$websocket.notify(WEB_SOCKET_EVENT.ORDER_EVIDENCE_AUDIT_REJECT_NOTICE, {
      //   msg: '上传佐证材料审核已驳回，请重新上传\n驳回原因：反映真实交易关系/债权债务关系的材料部符合要求',
      //   fastTrade: 0,
      //   msgType: 7,
      //   orderNo: '3714119097162240001',
      //   role: 1,
      //   acceptorName: '温州民商银行股份有限公司',
      //   transactionStatus: 12,
      //   _service: 'topic_shendu_evidence',
      //   msgId: 1238938788135178200,
      //   orderStatus: 5,
      //   messageId: 16980,
      //   _msgtype: 'orderEvidenceAuditRejectNotice',
      //   draftType: 0,
      //   radarType: 0,
      //   sendTime: '2023-11-06 11:15:20',
      //   msgTab: 2,
      //   draftPaymentAmount: 99999.99,
      //   draftActualAmount: 99999.99,
      //   draftAmount: 100000,
      //   lastSixDraftNo: '362555',
      //   topicChannel: 'orderEvidenceAuditRejectNotice'
      // })

      // 询单-回复
      this.$websocket.on(WEB_SOCKET_EVENT.BILLCONSULTORDERNOTICE, data => {
        if (this.isShowNotificationBySetting(data, this.remindSetting)) {
          this.handleNotify('consult', data, {
            notificationTitle: '询单', // 消息通知左上角标题
          })
          // 消息提醒音频播放处理
          this.handlePlayAudio(data, this.isPlayAudioBySetting(data, this.remindSetting))
        }
      })
    },

    // 全局事件监听
    handleEvenListener() {
      // 监听获取消息提醒所有的未读数量
      this.$event.on(NOTIFICATION_UNREAD_COUNT, () => {
        this.getMsgUnReadCount()
      })

      // 监听打开接单详情弹窗(场景:1.意向价格满足，打开接单)
      this.$event.on(RECEIVE_ORDER_DETAIL_NOTIFICATION, data => this.handelReceiveOrderDetail(data))
      // 监听打开询单详情
      this.$event.on(XUANDAN_DETAIL_NOTIFICATION, data => this.handelXunDanOrderDetail(data))
    },

    // 打开消息里的询单详情
    handelXunDanOrderDetail(data) {
      this.inquiryData = data
      this.showInquiryReplayDialog = true
    },

    // 关闭询单消息弹窗
    closeInquiryReplayDialog(isClose) {
      // false 表示 需要关闭， true为不关闭
      if (isClose) return
      this.showInquiryReplayDialog = false
    },

    // 展示消息列表弹窗
    showNotificationList() {
      this.isShowNotificationList = true // 加载消息提醒列表组件
      this.$nextTick().then(() => {
        this.$refs.notificationList && this.$refs.notificationList.init()
      })
    },

    // 消息列表所有未读数量(包括票方、资方和平台公告)
    getMsgUnReadCount() {
      this.isShowNotificationList = true // 加载消息提醒列表组件
      this.$nextTick().then(() => {
        this.$refs.notificationList
          && this.$refs.notificationList.getMsgUnReadCount()
      })
    },

    // 处理打开接单详情弹窗
    handelReceiveOrderDetail(data) {
      const {
        orderNo, // 订单no
      } = data

      this.isShowReceiveOrderDetail = true //  加载接单详情弹窗组件

      this.$nextTick().then(async() => {
        if (this.$refs.receiveOrderDetail) {
          await this.$refs.receiveOrderDetail.init(orderNo) // 接单详情弹窗组件初始化
        }
      })
    },

    // 刷新列表数据
    handleRefresh() {
      this.isShowReceiveOrderDetail = false
      this.isShowNotificationList = true // 加载消息提醒列表组件
      this.$nextTick().then(() => {
        this.$refs.notificationList
          && this.$refs.notificationList.handleRefresh()
      })
    },

    // 当前页面是否可见状态事件监听
    pageVisibilityListener() {
      document.addEventListener('visibilitychange', this.setPageVisibility)

      this.$once('hook:beforeDestroy', () => {
        document.removeEventListener('visibilitychange', this.setPageVisibility)
      })
    },

    // 设置当前页面可见状态
    setPageVisibility() {
      this.isActivePage = !document?.hidden
    },

    /**
     * 执行消息提醒弹窗
     * @param {String} type 消息提醒类型(account:开户 order交易订单 bargain议价 platForm平台 breakContract违约相关)
     * @param {Object|String} data 消息提醒数据
     * @param {Object} options 配置数据
     */
    handleNotify(type = '', data, options) {
      if (!data) return
      if (this.mqttFlag) return
      this.mqttFlag = true
      setTimeout(() => {
        this.mqttFlag = false
      }, MQTT_ALERT_TIME)
      notification.notify(type, data, options)
    },

    /**
     * 消息提醒音频播放处理
     * TIPS:特殊场景（多个相同页面下推送相同的消息提醒，音频只播放一次）
     * @param {Object} mqttData 消息提醒数据
     * @param {Object} isPlayAudio 是否播放音频
     */
    handlePlayAudio(mqttData, isPlayAudio = true) {
      if (!mqttData || !isPlayAudio) return
      this.mqttData = mqttData // 设置播放音频类型
      const {
        msgId = 0, // 消息ID
      } = (mqttData || {})
      // 加setTimeout(Math.random())为了确保多个相同页面下以下这段代码在多个相同页面下不会同一时刻执行，以便缓存消息Id后，其他相同页面能获取缓存后最新的消息id
      const RANDOM_MIN = 20 // 随机最小数
      const RANDOM_MAX = 200 // 随机最大数
      let time = this.isActivePage ? 0 : Math.random() * (RANDOM_MAX - RANDOM_MIN) + RANDOM_MIN // 20~200之间的随机数 (如果是当前页面则为0，优先执行)
      if (mqttData.delayRandom) {
        time = mqttData.delayRandom
      }
      setTimeout(async() => {
        if (msgId === Storage.get(NOTIFICATION_LAST_ID) || 0) {
          // console.log('多个相同页面下推送相同的消息提醒，音频只播放一次:>> ', Storage.get(NOTIFICATION_LAST_ID))
          return
        }

        if (this.$refs.notificationAudio) {
          Storage.set(NOTIFICATION_LAST_ID, msgId) // 缓存最后的消息Id
          try {
            // console.log('播放消息提醒音频 :>> ', this.$refs.notificationAudio)
            await this.$refs.notificationAudio.play()
          } catch {
            Storage.remove(NOTIFICATION_LAST_ID)
          }
        }
      }, time)
    },

    // 状态更新后节流
    mqttRefresh(data, type) {
      this.$event.emit(MQTT_ORDER, { data, type })
    },

    // 激活预警通知弹窗
    async openWarnNoticeDialog(type, obj) {
      if (type === 'push') {
        this.warnNoticeList.push({ ...obj })
      } else {
        this.warnNoticeList.length && this.warnNoticeList.shift()
      }
      await this.$nextTick()
      this.warnNoticeList.length && this.$refs.notificationWarnNotice.init(this.warnNoticeList[0].type, this.warnNoticeList[0].data)
    },

    // 是否弹出消息提示框-根据消息提醒的设置
    isShowNotificationBySetting(mqttData, remindSetting) {
      if (!mqttData || !remindSetting) return true
      let res = true // 默认弹出消息提示框
      const {
        textRemindPendingConfirm = 1, // 待确认消息提醒
        textRemindPendingPay = 1, // 待支付消息提醒
        textRemindPendingEndorse = 1, // 待背书消息提醒
        textRemindPendingSignIn = 1, // 待签收消息提醒
        textRemindPendingRadar = 1, // 自动自动接单成功
        textRemindBillOrderBargain = 1, // 议价消息提醒
        textRemindBillConsult = 1, // 询单消息提醒
      } = (this.remindSetting || {})

      // mqtt推送频道-交易订单刷新
      if (mqttData?.topicChannel === WEB_SOCKET_EVENT.ORDER_STATUS_REFRESH) {
        const {
          transactionStatus = -1,
        } = (mqttData || {})

        switch (transactionStatus) {
          case TRANSACTION_STATUS.WAITING_CONFIRM.id: // 待确认
            res = !!textRemindPendingConfirm
            break
          case TRANSACTION_STATUS.WAITING_PAY.id: // 待支付
            res = !!textRemindPendingPay
            break
          case TRANSACTION_STATUS.WAITING_ENDORSE.id: // 待背书
            res = !!textRemindPendingEndorse
            break
          case TRANSACTION_STATUS.WAITING_SIGN.id: // 待签收
            res = !!textRemindPendingSignIn
            break
          default:
            break
        }
      }

      // mqtt推送频道-自动自动接单成功
      if (mqttData?.topicChannel === WEB_SOCKET_EVENT.BILL_ORDER_NEED_RULE_SUCCESS) {
        res = !!textRemindPendingRadar
      }

      // mqtt推送频道-询单
      if (mqttData?.topicChannel === WEB_SOCKET_EVENT.BILLCONSULTORDERNOTICE) {
        res = !!textRemindBillConsult
      }

      // mqtt推送频道-议价
      if (mqttData?.topicChannel === WEB_SOCKET_EVENT.ORDER_BARGAIN_STATUS_REFRESH) {
        res = !!textRemindBillOrderBargain
      }

      return res
    },

    // 是否可播放音频-根据消息提醒的设置
    isPlayAudioBySetting(mqttData, remindSetting) {
      if (!mqttData || !remindSetting) return true
      let res = true // 默认播放声音

      // 消息提醒设置
      const {
        voiceRemindPendingConfirm = 1, // 待确认声音提醒
        voiceRemindPendingPay = 1, // 待支付声音提醒
        voiceRemindPendingEndorse = 1, // 待背书声音提醒
        voiceRemindPendingSignIn = 1, // 待签收声音提醒
        voiceRemindPendingRadar = 1, // 自动自动接单成功提醒
        voiceRemindBillOrderBargain = 1, // 议价声音提醒
        voiceRemindBillConsult = 1, // 询单声音提醒
      } = (remindSetting || {})

      // mqtt推送频道-交易订单刷新
      if (mqttData?.topicChannel === WEB_SOCKET_EVENT.ORDER_STATUS_REFRESH) {
        const {
          transactionStatus = -1,
        } = (mqttData)

        switch (transactionStatus) {
          case TRANSACTION_STATUS.WAITING_CONFIRM.id: // 待确认
            res = !!voiceRemindPendingConfirm
            break
          case TRANSACTION_STATUS.WAITING_PAY.id: // 待支付
            res = !!voiceRemindPendingPay
            break
          case TRANSACTION_STATUS.WAITING_ENDORSE.id: // 待背书
            res = !!voiceRemindPendingEndorse
            break
          case TRANSACTION_STATUS.WAITING_SIGN.id: // 待签收
            res = !!voiceRemindPendingSignIn
            break
          default:
            break
        }

        // 定向票-待支付（接单方确认后持票方会收到消息，此时不需要播放声音，其他交易环节正常）
        // if ((mqttData?.role === 2) && (transactionStatus === TRANSACTION_STATUS.WAITING_PAY.id)) {
        //   res = false
        // }
      }

      // mqtt推送频道-自动自动接单成功
      if (mqttData?.topicChannel === WEB_SOCKET_EVENT.BILL_ORDER_NEED_RULE_SUCCESS) {
        res = !!voiceRemindPendingRadar
      }

      // mqtt推送频道-询单
      if (mqttData?.topicChannel === WEB_SOCKET_EVENT.BILLCONSULTORDERNOTICE) {
        res = !!voiceRemindBillConsult
      }

      // mqtt推送频道-议价
      if (mqttData?.topicChannel === WEB_SOCKET_EVENT.ORDER_BARGAIN_STATUS_REFRESH) {
        res = !!voiceRemindBillOrderBargain
      }

      return res
    },

  },
}
</script>
