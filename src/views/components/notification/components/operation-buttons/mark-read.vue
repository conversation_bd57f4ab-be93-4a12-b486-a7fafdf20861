<!-- 单个消息忽略按钮组件 -->
<template>
  <el-button
    border
    height="40"
    type="primary"
    :loading="loading"
    :disabled="disabled"
    @click="skipMessage"
  >
    已读
  </el-button>
</template>

<script>
import notificationApi from '@/apis/notification' // 消息中心接口
import {
  NOTIFICATION_LIST, // 刷新消息中心列表事件
} from '@/event/modules/site'
export default {
  name: 'mark-read',
  props: {
    messageId: {
      type: [String, Number],
    },
    // 是否是mqtt 消息
    isMqtt: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      disabled: false
    }
  },
  methods: {
    // 消息已读处理
    async handleUpdateRead(id) {
      try {
        await (notificationApi.putNotificationCount(id) || {}) // 已读消息
      } catch (error) {
        // console.log('消息已读error :>> ', error)
      }
    },

    // 已读消息
    async skipMessage() {
      if (!this.messageId) return
      this.loading = true
      this.disabled = false
      await this.handleUpdateRead(this.messageId)
      if (!this.isMqtt) {
        // 非mqtt消息，弹窗提示
        this.$message.success('已读成功！')
      }
      this.loading = false
      this.disabled = true
      this.$event.emit(NOTIFICATION_LIST) // 刷新消息提醒列表、未读数量
      this.$emit('close')
    },
  }
}
</script>
