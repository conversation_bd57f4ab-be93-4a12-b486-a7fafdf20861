<!-- 预警&失效通知弹窗 -->
<style lang="scss" scoped>
.dialog-container {
  text-align: center;

  .content {
    line-height: 24px;
    font-size: 16px;
    text-align: start;
  }

  .qrcode {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 24px;
    padding: 16px;
    background: $color-FFFFFF;

    .fix {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .qrcode-item {
      font-size: 16px;
      font-weight: 400;
      text-align: center;
      line-height: 24px;

      &:first-child {
        margin-right: 64px;
      }

      img {
        margin-bottom: 8px;
        width: 140px;
        height: 140px;
        object-fit: cover;
        vertical-align: top;
      }
    }
  }

  .btn {
    margin-top: 12px;
    text-align: end;
  }

  .el-button--primary.is-border {
    font-weight: normal;
  }

  ::v-deep .high-light {
    margin: 0 4px;
    font-weight: bold;
    color: $--color-primary;
  }
}

::v-deep .el-dialog__body {
  padding: 20px 20px 12px !important;
}
</style>

<template>
  <el-dialog
    append-to-body
    :title="options.title || '提醒'"
    :visible.sync="visible"
    :width="options.width || '600px'"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    @close="closeDialog"
  >
    <div class="dialog-container">
      <!-- eslint-disable-next-line vue/no-v-html -->
      <!-- <slot name="content"><div class="content" v-html="options.content" /></slot> -->
      <slot name="content">
        <WarnContent :class-type="'blue'">
          <!-- eslint-disable-next-line vue/no-v-html -->
          <div class="content" v-html="options.content" />
        </WarnContent>
      </slot>
      <div class="qrcode">
        <div class="content"> {{ options.qrcodeTitle }}</div>
        <div class="fix">
          <div class="qrcode-item">
            <img :src="configDefault.customerManagerQr1">
          </div>
          <div class="qrcode-item">
            <img :src="configDefault.customerManagerQr2">
          </div>
        </div>
      </div>
      <div class="btn">
        <slot name="button">
          <el-button
            v-if="options.showAfterHandleBtn"
            type="primary"
            border
            @click="handleBeforeHandle"
          >
            稍后处理
          </el-button>
          <el-button v-if="options.showUploadCertificateBtn" type="primary" @click="handleUploadCertificate">立即上传证件</el-button>
          <el-button v-if="options.showIKnowBtn" type="primary" @click="handleClose">我知道了</el-button>
        </slot>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { REAL_NAME_AUTH_TYPE } from '@/constant' // 常量
// import { APPLICATION_STATUS } from '@/constants/open-account'
import { REAL_NAME_CERTIFICATION } from '@/event/modules/site' // 常量
import WarnContent from '@/views/components/common/warn-content.vue'
// import openAccountApi from '@/apis/open-account'
// 预警信息
const warnInfoMap = {
  // 证件过期提醒
  1: {
    title: '证件过期提醒',
    content: '',
    showAfterHandleBtn: true, // 是否显示“稍后处理”按钮
    showUploadCertificateBtn: true, // 是否显示“立即上传证件”按钮
  },
  2: {
    title: '账户冻结提醒 ',
    content: '您好，当前账户即将冻结，请立即上传证件更新实名认证信息！',
    qrcodeTitle: '如有疑问可联系客服经理!',
    showUploadCertificateBtn: true, // 是否显示“立即上传证件”按钮
  },
  3: {
    title: '违法/变更提醒',
    content: '您好，当前账户存在风险行为，请尽快与您的客户经理联系确认原因！',
    qrcodeTitle: '如有疑问可扫码联系客服经理!',
    showIKnowBtn: false, // 是否显示“我知道了”按钮
  },
  4: {
    title: '经营异常提醒',
    content: '您好，当前账户存在经营异常，请尽快与工商局核实原因并处理！',
    qrcodeTitle: '如有疑问可扫码联系客服经理!',
    showIKnowBtn: false, // 是否显示“我知道了”按钮
  },
  5: {
    title: '工商变更提醒',
    content: '您好，当前账户的工商信息已发生变更，请立即上传证件更新实名认证信息！',
    qrcodeTitle: '如有疑问可扫码联系客服经理!',
    showUploadCertificateBtn: true, // 是否显示“我知道了”按钮
  }
}
// 实名失效
const realNameInvalidMap = {
  FREEZE: {
    title: '实名失效提醒',
    content: '您好，当前账户已冻结，请立即上传证件更新实名认证信息！',
    qrcodeTitle: '如有疑问可联系客服经理!',
    showUploadCertificateBtn: true, // 是否显示“立即上传证件”按钮
  },
  EXPIRED: {
    title: '实名失效提醒',
    content: '您好，当前账户存在证件已过期，请立即上传证件更新实名认证信息！',
    qrcodeTitle: '如有疑问可联系客服经理!',
    showUploadCertificateBtn: true, // 是否显示“立即上传证件”按钮
  },
  CREDIT_IMMEDIATE: {
    title: '实名失效提醒',
    content: '您好，当前账户存在企业违法/工商变更，请立即上传证件更新实名认证信息！',
    qrcodeTitle: '如有疑问可联系客服经理!',
    showUploadCertificateBtn: true, // 是否显示“立即上传证件”按钮
  }
}
export default {
  name: 'notification-warn-notice-dialog',
  components: {
    WarnContent
  },

  data() {
    return {
      visible: false,
      options: {},
      isOpening: false, // 是否正在打开弹窗
    }
  },

  computed: {

  },

  methods: {
    init(type, data) {
      // this.getQRcode()
      if (this.isOpening) {
        return
      }
      if (!data) {
        return
      }
      this.isOpening = true
      const current = type === 'warn' ? warnInfoMap[data.invalidWarnType] : realNameInvalidMap[data.failReason]
      this.options.title = current.title
      // 证件过期提醒，内容信息要特别处理
      if (type === 'warn' && data.invalidWarnType === 1) {
        this.options.content = `您好，当前账户的 <span class="high-light">${data.invalidWarnExpireCardName}</span> 将于 <span class="high-light">${data.invalidWarnExpireDate}</span> 过期，请于到期日之前更新证件，逾期将影响您的支付业务`
      } else {
        this.options.content = current.content
      }
      this.options.showAfterHandleBtn = current.showAfterHandleBtn
      this.options.showUploadCertificateBtn = current.showUploadCertificateBtn
      this.options.showIKnowBtn = current.showIKnowBtn
      this.options.qrcodeTitle = current.qrcodeTitle || '如有疑问请联系您的客户经理！'
      this.visible = true
    },

    // 我知道了
    handleClose() {
      this.visible = false
      this.$emit('close')
    },

    // 稍后处理
    handleBeforeHandle() {
      this.visible = false
      this.$emit('before-handle')
    },

    // 弹窗关闭回调
    closeDialog() {
      this.isOpening = false
      this.$emit('again')
    },

    // 重新认证
    recertification() {
      // this.corpInfo = await userApi.getCorpInfo()
      this.$event.emit(REAL_NAME_CERTIFICATION, {
        realNameAuthType: REAL_NAME_AUTH_TYPE.RECEIVE,
        form: 'user-center'
      })

      // 企业最新开户记录申请单状态：0-未提交，1-待京东审核，2-京东审核失败，3-待京东打款，4-京东打款成功，5-京东打款失败，6-打款验证失败，7-已通过
      // if (this.corpInfo.newestCorpOpenInfoApplyStatus >= APPLICATION_STATUS.HAVE_BEEN_THROUGH.id) {
      //   this.$event.emit(REAL_NAME_CERTIFICATION, {
      //     realNameAuthType: REAL_NAME_AUTH_TYPE.RECEIVE,
      //     form: 'user-center'
      //   })
      // } else {
      //   this.$event.emit(REAL_NAME_CERTIFICATION, {
      //     realNameAuthType: REAL_NAME_AUTH_TYPE.RECEIVE,
      //     startOver: true,
      //     form: 'user-center'
      //   })
      // }
    },

    // 立即上传证件
    handleUploadCertificate() {
      this.visible = false
      this.recertification()
    }
  }
}
</script>
