<!-- eslint-disable max-lines -->
<!-- 消息提醒设置弹窗 -->
<style lang="scss" scoped>
.notification-setting-dialog {
  border-radius: 2px;

  .body {
    overflow-y: auto;
    height: 625px;
  }

  .setting-form {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
  }

  .setting-box {
    margin-bottom: 12px;
    padding: 12px 16px;
    width: 100%;
    background-color: #FFFFFF;

    &:last-child {
      margin-bottom: 0;
    }

    .g-title {
      position: relative;
      display: flex;
      align-items: center;
      overflow: hidden;
      margin-bottom: 12px;
      padding-left: 10px;
      font-size: 16px;
      font-weight: 600;
      text-align: left;
      color: $color-text-primary;
      line-height: 22px;

      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: 2px;
        height: 16px;
        background-color: $--color-primary;
        transform: translateY(-50%);
        content: "";
      }

      .tips {
        display: flex;
        align-items: center;
        margin-left: 12px;
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;

        .icon {
          margin-right: 2px;
          font-size: 18px;
          color: $font-color;
        }
      }
    }
  }

  .voice-radio {
    display: flex;
  }

  .bottom-text {
    margin-top: 10px;

    .title {
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      color: $color-text-primary;
    }

    .tips {
      font-size: 12px;
      font-weight: 400;
      line-height: 20px;
      color: $color-text-light;
    }

    .tel-tips {
      margin: 8px auto;
      line-height: 28px;

      .el-col:not(:first-child) {
        border-left: 1px solid $color-D9D9D9;
      }
    }

    .bold {
      font-weight: 600;
    }
  }
}

// el组件样式
::v-deep {
  .el-form-item {
    margin-bottom: 0;
  }

  .el-switch {
    .el-switch__core {
      border-radius: 16px;
      height: 22px;
      line-height: 22px;

      &::after {
        width: 18px;
        height: 18px;
      }
    }

    &.is-checked .el-switch__core::after {
      margin-left: -19px;
    }
  }

  .el-radio {
    display: flex;
    margin-right: 24px;

    .el-radio__label {
      padding-left: 6px;
    }
  }

  .tel-input {
    display: flex;
    width: 100%;
    font-size: 16px;

    &.is-input {
      .el-input {
        .el-input__inner {
          border-bottom: 1px solid  $color-text-light;
          border-radius: 0;
          width: 155px;
          color: $color-text-primary;
          background-color: #FFFFFF !important;
        }
      }
    }

    .el-form-item__content {
      display: flex;
    }

    .el-input {
      .el-input__suffix {
        right: 0;
      }

      .el-input__inner {
        border: none;
        padding: 0;
        width: 110px;
        height: 24px;
        line-height: 24px;
        color: $color-text-primary;
        background-color: transparent !important;
        box-shadow: none;
        cursor: text;
      }

      .el-input__clear {
        width: 20px;
      }
    }

    .tel-operation {
      .icon {
        margin-left: 6px;
        vertical-align: -4px;
      }

      .icon-set {
        font-size: 16px;
        color: $color-text-light;
        cursor: pointer;

        &:hover {
          color: $--color-primary;
        }
      }

      .icon-cancel {
        font-size: 16px;
        cursor: pointer;
        color: $color-warning;
      }

      .icon-confirm {
        font-size: 16px;
        cursor: pointer;
        color: $font-color;
      }
    }
  }

  .switch-text {
    margin-left: 8px;
    font-size: 14px;
  }

  // input 占位符字体颜色
  // .el-input__inner::placeholder,
  // .el-textarea__inner::placeholder {
  //   color: $color-text-primary;
  // }

  .red-tips {
    font-size: 14px;
    color: $color-warning;
  }
}
</style>

<template>
  <div class="notification-setting-dialog">
    <!--
      <el-dialog
      class="notification-setting-dialog"
      title="消息提醒设置"
      :visible.sync="dialogVisible"
      width="1020px"
      height="700px"
      :before-close="handleClose"
      :close-on-click-modal="false"
      append-to-body
      center
      :lock-scroll="true"
      >
    -->
    <div
      class="body"
    >
      <el-form
        ref="form"
        class="setting-form"
        :model="form"
        :rules="rules"
        :hide-required-asterisk="true"
        align="left"
      >
        <div class="setting-box web-notify">
          <h2 class="g-title">
            网站提醒设置
            <span class="tips">
              <icon class="icon" type="sdicon-info-circle" size="16" />
              请设置您需要开启或关闭的网站消息、提示音和短信提醒，按钮设置为打开则表示开启。
            </span>
          </h2>

          <el-table
            :data="webTable"
            outer-border
            style="width: 100%;"
          >
            <el-table-column
              v-for="col in webTableColumn"
              :key="col.id"
              :label="col.label"
              :min-width="col.width"
            >
              <template slot-scope="scope">
                <template v-if="col.type === 'text'">
                  {{ scope.row.name }}
                </template>

                <template v-if="col.type === 'switch'">
                  <template v-if="col.name !== 'msgRemind' || (col.name === 'msgRemind' && (scope.row.key === 'PendingConfirm' || scope.row.key === 'PendingPay'))">
                    <el-form-item :prop="`${col.name}${scope.row.key}`">
                      <el-switch
                        v-model="form[`${col.name}${scope.row.key}`]"
                        :width="44"
                        :active-value="1"
                        :inactive-value="0"
                        :disabled="col.name === 'voiceRemind' && !form[`textRemind${scope.row.key}`]"
                        @change="handleSwitchChange(`${col.name}${scope.row.key}`, form[`${col.name}${scope.row.key}`], scope.row)"
                      />
                      <span class="switch-text">{{ form[`${col.name}${scope.row.key}`] ? col.activeText : col.inactiveText }}</span>
                    </el-form-item>
                  </template>
                </template>

                <template v-if="col.type === 'radio'">
                  <el-form-item :prop="`${col.name}${scope.row.key}Type`">
                    <el-radio-group
                      v-if="col.radioOptions"
                      v-model="form[`${col.name}${scope.row.key}Type`]"
                      :disabled="!form[`${col.name}${scope.row.key}`]"
                      class="voice-radio"
                      @change="handleRadioChange(`${col.name}${scope.row.key}Type`, form[`${col.name}${scope.row.key}Type`])"
                    >
                      <el-radio v-for="option in col.radioOptions" :key="option.id" :label="option.value">{{ option.label }}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </template>
                <template v-if="col.type === 'tel-input' && (scope.row.key === 'PendingConfirm' || scope.row.key === 'PendingPay')">
                  <el-form-item :prop="`${col.name}${scope.row.key}Mobile`" class="tel-input" :class="{'is-input': col.telIndex === scope.row.telIndex && scope.row.telIsInput}">
                    <div class="tel-number">
                      <el-input
                        :ref="`${col.name}${scope.row.key}Mobile_${col.telIndex}`"
                        v-model="(form[`${col.name}${scope.row.key}Mobile`] || [])[col.telIndex]"
                        :readonly="!(col.telIndex === scope.row.telIndex && scope.row.telIsInput)"
                        :placeholder="col.telIndex === scope.row.telIndex && scope.row.telIsInput ? '请输入手机号码' : '-'"
                        clearable
                        show-word-limit
                        maxlength="11"
                      />
                    </div>
                    <div v-show="scope.row.key === 'PendingConfirm' && form.msgRemindPendingConfirm || (scope.row.key === 'PendingPay' && form.msgRemindPendingPay)" class="tel-operation">
                      <template v-if="col.telIndex === scope.row.telIndex && scope.row.telIsInput">
                        <icon
                          class="icon icon-cancel"
                          type="chengjie-close"
                          @click="handleTelInputCancel(scope.row, col.telIndex, `${col.name}${scope.row.key}Mobile`, col.name)"
                        />
                        <icon
                          class="icon icon-confirm"
                          type="chengjie-check"
                          @click="handleTelInputConfirm(scope.row, col.telIndex, `${col.name}${scope.row.key}Mobile`, col.name)"
                        />
                      </template>
                      <icon
                        v-else
                        class="icon icon-set"
                        type="chengjie-modify"
                        @click="handleTelInput(scope.row, col.telIndex, true, `${col.name}${scope.row.key}Mobile`, col.name)"
                      />
                    </div>
                  </el-form-item>
                </template>
              </template>
            </el-table-column>
          </el-table>

          <div class="bottom-text">
            <p class="red-tips">您可以自行设置网站交易环节的消息弹框、提示音和短信提醒~</p>
            <p class="tips">消息设置完成后,系统会根据您的设置进行提醒哦，开启网站提示音前，需要先开启网站消息弹框哦~</p>
          </div>
        </div>
        <!-- 暂时隐藏电话提醒 -->
        <div v-if="false" class="setting-box phone-notify">
          <h2 class="g-title">
            电话提醒设置
            <span class="tips">
              <icon class="icon" type="sdicon-info-circle" size="16" />
              请可以自行设置在交易过程中用于接收电话提醒的手机号~
            </span>
          </h2>

          <el-table
            :data="telTable"
            outer-border
            style="width: 100%;"
          >
            <el-table-column
              v-for="col in telTableColumn"
              :key="col.id"
              :label="col.label"
              :min-width="col.width"
            >
              <template slot-scope="scope">
                <template v-if="col.type === 'text'">
                  {{ scope.row.name }}
                </template>

                <template v-if="col.type === 'switch'">
                  <el-form-item :prop="`${col.name}${scope.row.key}`">
                    <el-switch
                      v-model="form[`${col.name}${scope.row.key}`]"
                      :width="44"
                      :active-value="1"
                      :inactive-value="0"
                      @change="handleSwitchChange(`${col.name}${scope.row.key}`, form[`${col.name}${scope.row.key}`], scope.row)"
                    />
                    <span class="switch-text">{{ form[`${col.name}${scope.row.key}`] ? col.activeText : col.inactiveText }}</span>
                  </el-form-item>
                </template>

                <template v-if="col.type === 'tel-input'">
                  <el-form-item :prop="`${col.name}${scope.row.key}MobileList`" class="tel-input" :class="{'is-input': col.telIndex === scope.row.telIndex && scope.row.telIsInput}">
                    <div class="tel-number">
                      <el-input
                        :ref="`${col.name}${scope.row.key}MobileList_${col.telIndex}`"
                        v-model="(form[`${col.name}${scope.row.key}MobileList`] || [])[col.telIndex]"
                        :readonly="!(col.telIndex === scope.row.telIndex && scope.row.telIsInput)"
                        :placeholder="col.telIndex === scope.row.telIndex && scope.row.telIsInput ? '请输入手机号码' : '-'"
                        clearable
                        show-word-limit
                        maxlength="11"
                      />
                    </div>
                    <div class="tel-operation">
                      <template v-if="col.telIndex === scope.row.telIndex && scope.row.telIsInput">
                        <icon
                          class="icon icon-cancel"
                          type="chengjie-close"
                          @click="handleTelInputCancel(scope.row, col.telIndex, `${col.name}${scope.row.key}MobileList`, col.name)"
                        />
                        <icon
                          class="icon icon-confirm"
                          type="chengjie-check"
                          @click="handleTelInputConfirm(scope.row, col.telIndex, `${col.name}${scope.row.key}MobileList`, col.name)"
                        />
                      </template>
                      <icon
                        v-else
                        class="icon icon-set"
                        type="chengjie-modify"
                        @click="handleTelInput(scope.row, col.telIndex, true, `${col.name}${scope.row.key}MobileList`, col.name)"
                      />
                    </div>
                  </el-form-item>
                </template>
              </template>
            </el-table-column>
          </el-table>

          <div class="bottom-text">
            <div class="red-tips">为了让您不错过任何一笔交易，平台现推出电话提醒服务，您可以控制每个交易环节是否接收电话提醒，系统仅会给开启电话提醒开关的交易环节发送电话提醒。若您同意接受该服务并成功设置，我们将会按照如下规则给您拨打电话提醒：</div>
            <el-row class="tel-tips" :gutter="32">
              <el-col :span="24">
                <div class="grid-content">
                  <!-- <h2 class="title bold">普通订单：</h2> -->
                  <p>
                    - 待确认: 3分钟未操作第一次电话提醒, 5分钟未操作第二次电话提醒； <br>
                    - 待支付: 10分钟未操作第一次电话提醒, 15分钟未操作第二次电话提醒；<br>
                    - 待背书: 5分钟未操作第一次电话提醒, 15分钟未操作第二次电话提醒；<br>
                    - 待签收: 10分钟未操作第一次电话提醒, 15分钟未操作第二次电话提醒。<br>
                  </p>
                </div>
              </el-col>
              <!--
                <el-col :span="12">
                <div class="grid-content">
                <h2 class="title bold">极速/自动订单：</h2>
                <p>
                - 待确认: 3分钟未操作第一次电话提醒, 5分钟未操作第二次电话提醒；<br>
                - 待支付: 3分钟未操作第一次电话提醒，6分钟未操作第二次电话提醒；<br>
                - 待背书: 3分钟未操作第一次电话提醒，6分钟未操作第二次电话提醒；<br>
                - 待签收: 5分钟未操作第一次电话提醒, 10分钟未操作第二次电话提醒;<br>
                </p>
                </div>
                </el-col>
              -->
            </el-row>
            <p class="tips">电话提醒仅在交易日08:00:00-18:00:00启用，其余时间不会打扰您的休息。</p>
          </div>
        </div>
      </el-form>
    </div>
    <!-- </el-dialog> -->
  </div>
</template>

<script>
/* eslint-disable no-magic-numbers */
import { isNull } from '@/common/js/util'
import userApi from '@/apis/user'
import { mapGetters, mapActions } from 'vuex'

const DEFAULT_WEB_TABLE_COLUMN = [
  {
    id: 1,
    name: 'orderStatus',
    label: '提示环节',
    type: 'text',
    width: '100',
  },
  {
    id: 2,
    name: 'textRemind', // 对应后端字段的前缀
    label: '消息弹窗提醒',
    type: 'switch',
    width: '200',
    activeText: '开启消息弹窗提醒',
    inactiveText: '关闭消息弹窗提醒',
  },
  {
    id: 3,
    name: 'voiceRemind', // 对应后端字段的前缀
    label: '声音提醒',
    type: 'switch',
    width: '200',
    activeText: '开启声音提醒',
    inactiveText: '关闭声音提醒',
  },
  {
    id: 4,
    name: 'voiceRemind', // 对应后端字段的前缀
    label: '声音类型',
    type: 'radio',
    width: '330',
    radioOptions: [
      {
        id: 1,
        label: '女声',
        value: 0
      },
      {
        id: 2,
        label: '男声',
        value: 1
      },
      {
        id: 3,
        label: '系统默认',
        value: -1
      }
    ]
  },
  {
    id: 5,
    name: 'msgRemind', // 对应后端字段的前缀
    label: '短信提醒',
    type: 'switch',
    width: '180',
    activeText: '开启短信提醒',
    inactiveText: '关闭短信提醒',
  },
  {
    id: 6,
    name: 'msgRemind', // 对应后端字段的前缀
    label: '短信接收手机号',
    type: 'tel-input',
    width: '280',
    telIndex: 0,
  },
]

const DEFAULT_WEB_TABLE = [
  {
    id: 1,
    name: '待确认',
    key: 'PendingConfirm', // 对应后端字段的后缀
    telIndex: -1,
    telIsInput: false, // 接收手机号是否输入中
  },
  {
    id: 2,
    name: '待支付',
    key: 'PendingPay', // 对应后端字段的后缀
    telIndex: -1,
    telIsInput: false, // 接收手机号是否输入中
  },
  {
    id: 3,
    name: '待背书',
    key: 'PendingEndorse', // 对应后端字段的后缀
  },
  {
    id: 4,
    name: '待签收',
    key: 'PendingSignIn', // 对应后端字段的后缀
  },
  {
    id: 6,
    name: '议价',
    key: 'BillOrderBargain', // 对应后端字段的后缀
  },
  {
    id: 7,
    name: '询单',
    key: 'BillConsult', // 对应后端字段的后缀
  },
  // {
  //   id: 5,
  //   name: '自动接单成功',
  //   key: 'PendingRadar', // 对应后端字段的后缀
  // },
]

const DEFAULT_TEL_TABLE_COLUMN = [
  {
    id: 1,
    name: 'receiveStatus',
    label: '接收状态',
    type: 'text',
    width: '188',
  },
  {
    id: 2,
    name: 'telRemind', // 对应后端字段的前缀
    label: '默认接收手机号',
    type: 'tel-input',
    width: '300',
    telIndex: 0,
  },
  {
    id: 3,
    name: 'telRemind', // 对应后端字段的前缀
    label: '备用接收手机号',
    type: 'tel-input',
    width: '300',
    telIndex: 1
  },
  {
    id: 4,
    name: 'telRemind', // 对应后端字段的前缀
    label: '电话提醒',
    type: 'switch',
    width: '148',
    activeText: '开启电话提醒',
    inactiveText: '关闭电话提醒',
  },

]

const DEFAULT_TEL_TABLE = [
  {
    id: 1,
    name: '提醒确认',
    key: 'Confirm', // 对应后端字段的后缀
    telIndex: -1,
    telIsInput: false, // 接收手机号是否输入中
    // telBackUpIsInput: false, // 备用接收手机号是否输入中
  },
  {
    id: 2,
    name: '提醒支付',
    key: 'Pay', // 对应后端字段的后缀
    telIndex: -1,
    telIsInput: false, // 接收手机号是否输入中
    // telBackUpIsInput: false, // 备用接收手机号是否输入中
  },
  {
    id: 3,
    name: '提醒背书',
    key: 'Endorse', // 对应后端字段的后缀
    telIndex: -1,
    telIsInput: false, // 接收手机号是否输入中
    // telBackUpIsInput: false, // 备用接收手机号是否输入中
  },
  {
    id: 4,
    name: '提醒签收',
    key: 'SignIn', // 对应后端字段的后缀
    telIndex: -1,
    telIsInput: false, // 接收手机号是否输入中
    // telBackUpIsInput: false, // 备用接收手机号是否输入中
  },
]

export default {
  name: 'notification-setting-dialog',
  components: {
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      loading: false,
      form: {
      },
      // 用于保存 原始待确认短信提醒手机号码|待打款短信提醒手机号码
      msgRemindMobile: {},
      rules: {
        msgRemindPendingConfirmMobile: [{ validator: this.checkTel, trigger: 'none' }], // 待确认短信提醒手机号码
        msgRemindPendingPayMobile: [{ validator: this.checkTel, trigger: 'none' }], // 待打款短信提醒手机号码
        telRemindConfirmMobileList: [{ validator: this.checkTel, trigger: 'none' }],
        telRemindPayMobileList: [{ validator: this.checkTel, trigger: 'none' }],
        telRemindEndorseMobileList: [{ validator: this.checkTel, trigger: 'none' }],
        telRemindSignInMobileList: [{ validator: this.checkTel, trigger: 'none' }],
      },
      // 网站提醒设置表格
      webTable: [],
      // 电话提醒设置表格
      telTable: [],
      // 网站提醒设置表头
      webTableColumn: [],
      // 电话提醒设置表头
      telTableColumn: [],
      curInputValue: null, // 电话提醒设置-- 临时保存当前输入框的数据,用于取消输入回归原来的输入值
      curInputWebValue: null, // 网站提醒设置-- 临时保存当前输入框的数据,用于取消输入回归原来的输入值
    }
  },
  computed: {
    ...mapGetters('user', {
      remindSetting: 'remindSetting', // 消息提醒设置
    }),

    // dialogVisible: {
    //   get() {
    //     return this.visible
    //   },
    //   set(v) {
    //     this.$emit('update:visible', v)
    //   }
    // },
  },
  watch: {
    // visible: {
    //   handler(val) {
    //     if (val) {
    //       this.init()
    //     }
    //   },
    // }
  },
  mounted() {
    this.init()
  },

  methods: {
    isNull, // 判断是否空值

    ...mapActions('user', {
      getRemindSetting: 'getRemindSetting', // 获取用户提醒配置
    }),

    // 初始化
    init() {
      this.initData() // 初始化数据
    },

    // 初始化数据
    async initData() {
      this.clearData()

      try {
        // 初始化网站提醒设置表格数据
        this.handleTableInit()

        this.loading = true

        // 获取用户提醒设置
        await this.getRemindSetting()
        // if (!this.remindSetting) {
        //   await this.getRemindSetting()
        // }

        this.form = this.remindSetting // 用户提醒设置
        // 初始化赋值 保存网站提醒设置的手机号码 接口返回字符串  转成 数组
        this.msgRemindMobile.Confirm = this.form.msgRemindPendingConfirmMobile
        this.msgRemindMobile.PendingPay = this.form.msgRemindPendingPayMobile
        this.form.msgRemindPendingConfirmMobile = [this.form.msgRemindPendingConfirmMobile]
        this.form.msgRemindPendingPayMobile = [this.form.msgRemindPendingPayMobile]
        this.loading = false
      } catch (error) {
        // console.log('议价error :>> ', error)

        this.loading = false
      }
    },

    // 关闭弹窗
    // handleClose() {
    //   this.dialogVisible = false
    // },

    // 刷新
    handleRefresh() {
      // if (this.loading) return
      this.$nextTick().then(() => {
        this.initData() // 刷新数据
      })
    },

    // 清除数据
    clearData() {
      this.loading = false
    },

    // switch切换
    handleSwitchChange(key, val, row) {
      // console.log('switch切换val :>> ', key, val, row)
      const params = {
        [key]: val
      }

      // 消息提醒关闭，声音同步关闭
      if (this.form[`textRemind${row.key}`] === 0) {
        this.form[`voiceRemind${row.key}`] = this.form[`textRemind${row.key}`]
        params[`voiceRemind${row.key}`] = this.form[`textRemind${row.key}`]
      }

      this.saveRemindSetting(params)
      if (key === 'msgRemindPendingConfirm' || key === 'msgRemindPendingPay') {
        let tempKey = `${key}Mobile`
        let tempTelIndex = parseInt(0)
        this.form.msgRemindPendingConfirmMobile[0] = this.msgRemindMobile.Confirm
        this.form.msgRemindPendingPayMobile[0] = this.msgRemindMobile.PendingPay
        let isInput = false

        this.webTable = this.webTable.map(item => {
        // 给当前点击的输入框变更为输入状态
          item.telIndex = item.key === row.key ? tempTelIndex : -1
          item.telIsInput = item.key === row.key ? isInput : false
          return item
        })
        this.handleTelInput(row, tempTelIndex, false, tempKey) // 关闭输入框
        // this.handleOtherTelInput(row, tempTelIndex)
        this.curInputWebValue = null
      }
    },

    // radio切换
    handleRadioChange(key, val) {
      // console.log('radio切换 :>> ', key, val)
      const params = {
        [key]: val
      }
      this.saveRemindSetting(params)
    },

    // 初始化提醒设置表格数据
    handleTableInit() {
      this.curInputValue = null
      this.curInputWebValue = null
      this.webTable = []
      this.telTable = []
      this.webTableColumn = []
      this.telTableColumn = []

      this.webTableColumn = JSON.parse(JSON.stringify(DEFAULT_WEB_TABLE_COLUMN)) // 网站提醒设置表头
      this.telTableColumn = JSON.parse(JSON.stringify(DEFAULT_TEL_TABLE_COLUMN)) // 电话提醒设置表头
      this.webTable = JSON.parse(JSON.stringify(DEFAULT_WEB_TABLE)) // 网站提醒设置表格
      this.telTable = JSON.parse(JSON.stringify(DEFAULT_TEL_TABLE))// 电话提醒设置表格
    },

    // 处理其他正在输入的手机号码
    handleOtherTelInput(row, telIndex) {
      this.telTable.map(item => {
        // 若有其他输入框正在输入
        if (item.telIsInput && item.telIndex > -1 && !(item.key === row.key && item.telIndex === telIndex)) {
          this.form[`telRemind${item.key}MobileList`][item.telIndex] = this.curInputValue // 不保存,回显原来数据
        }
        return item
      })
      this.webTable.map(item => {
        // 若有其他输入框正在输入
        if (item.telIsInput && item.telIndex > -1 && !(item.key === row.key && item.telIndex === telIndex)) {
          this.form[`msgRemind${item.key}Mobile`][item.telIndex] = this.curInputWebValue // 不保存,回显原来数据
        }
        return item
      })
    },

    // 手机号码输入
    handleTelInput(row, telIndex, isInput, key, colName) {
      // console.log('手机号码输入row :>> ', row, telIndex, isInput, key, this.form[key])

      // 对其他正在输入的手机号码处理,不保存,回显原来数据
      this.handleOtherTelInput(row, telIndex, colName)

      // 当前选中的输入框切换输入状态
      this.telTable = this.telTable.map(item => {
        // 给当前点击的输入框变更为输入状态
        item.telIndex = item.key === row.key ? telIndex : -1
        item.telIsInput = item.key === row.key ? isInput : false
        return item
      })
      this.webTable = this.webTable.map(item => {
        // 给当前点击的输入框变更为输入状态
        item.telIndex = item.key === row.key ? telIndex : -1
        item.telIsInput = item.key === row.key ? isInput : false
        return item
      })
      if (colName === 'msgRemind') {
        this.curInputWebValue = this.form[key][telIndex]
      } else {
        // 保存当前输入值,用户取消时可回归数据
        this.curInputValue = this.form[key][telIndex]
      }

      this.$nextTick().then(() => {
        if (this.$refs[`${key}`] && this.$refs[`${key}_${telIndex}`][0]) {
          this.$refs[`${key}_${telIndex}`][0].focus() // 使输入框获得焦点
        }
      })
    },

    // 手机号码输入取消
    handleTelInputCancel(row, telIndex, key, colName) {
      // console.log('手机号码输入取消 :>> ', row, telIndex, key)
      if (colName === 'msgRemind') {
        this.form[key][telIndex] = this.curInputWebValue || ''
        this.curInputWebValue = null
      } else {
        this.form[key][telIndex] = this.curInputValue || ''
        this.curInputValue = null
      }

      this.handleTelInput(row, telIndex, false, key)
    },

    // 手机号码输入确认
    async handleTelInputConfirm(row, telIndex, key, colName) {
      // console.log('手机号码输入确认 :>> ', row, telIndex, key)
      const curTelValue = this.form[key][telIndex] // 当前正在输入的手机号码

      // 当前无输入，不提交
      if (curTelValue === this.curInputValue || curTelValue === this.curInputWebValue) {
        this.handleTelInput(row, telIndex, false, key)
        return
      }

      let isValidateFail = true // 校验结果
      await this.$refs.form.validateField(key, error => {
        isValidateFail = !!error
      })

      // 校验通过
      if (!isValidateFail) {
        try {
          const telValue = (this.form[key]) || ''
          const params = {
            [key]: colName === 'msgRemind' ? telValue[0] : telValue
          }
          const isSuccess = await this.saveRemindSetting(params)
          isSuccess && this.$message.success('修改成功')
          // 修改成功 更新原始值
          this.msgRemindMobile.Confirm = this.form.msgRemindPendingConfirmMobile[0]
          this.msgRemindMobile.PendingPay = this.form.msgRemindPendingPayMobile[0]
        } catch (error) {
          // console.log('error :>> ', error)
        }

        this.handleTelInput(row, telIndex, false, key) // 关闭输入框
        // 清除临时保存的输入值
        colName === 'msgRemind' ? this.curInputValue = null : this.curInputWebValue = null
        // this.curInputValue = null // 清除临时保存的输入值
      }
    },

    // 手机号码验证
    checkTel(rule, value, callback) {
      // console.log('checkTel :>> ', rule, value, callback)
      const key = rule.field
      const { telIndex } = this.telTable.filter(v => v.telIsInput)[0] || {}
      const telValue = this.form[key][telIndex] // 当前正在输入的手机号码
      const firstTelValue = this.form[key][0] || '' // 默认接收手机号码
      // console.log('telIndex :>> ', telIndex)
      // console.log('telValue :>> ', telValue)

      // 校验必填
      if (!firstTelValue) {
        this.$message.closeAll()
        this.$message.warning('默认接收手机号不能为空')
        return callback(new Error('默认接收手机号不能为空'))
      }

      // 校验手机格式
      if (telValue && !this.checkTelNumber(telValue)) {
        this.$message.closeAll()
        this.$message.warning('手机号码格式不正确')
        return callback(new Error('手机号码格式不正确'))
      }

      // 校验该接收状态下设置的所有手机是否存在相同号码
      if (this.checkSameTel(telValue, this.form[key], telIndex)) {
        this.$message.closeAll()
        this.$message.warning('您两次输入的手机号相同，请设置不同的手机号')
        return callback(new Error('您两次输入的手机号相同，请设置不同的手机号'))
      }

      callback()
    },

    // 校验手机格式
    checkTelNumber(telValue) {
      return /^[1][3-9][0-9]{9}$/.test(telValue)
    },

    // 检查重复手机号码
    checkSameTel(telValue, allTelValue, telIndex) {
      // 排除当前输入值后的所有手机号
      const list = allTelValue.reduce((t, p, i) => {
        i !== telIndex && (t.push(p))
        return t
      }, [])
      return list.includes(telValue)
    },

    // 保存用户提醒配置
    async saveRemindSetting(params) {
      // console.log('保存用户提醒配置params :>> ', params)
      if (this.loading) return
      this.loading = true
      try {
        const res = await userApi.saveRemindSetting(params) // 保存用户提醒设置
        await this.getRemindSetting() // 更新vuex里的用户提醒设置
        this.loading = false
        return res
      } catch (error) {
        await this.getRemindSetting() // 更新vuex里的用户提醒设置
        this.loading = false
        // return error
      }
    },
  }
}
</script>
