<!-- 消息通知弹窗组件 -->
<style lang="scss" scoped>
.notification-list-dialog {
  border-radius: 2px;

  .body {
    display: flex;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    background: $color-FFFFFF;
    box-sizing: border-box;
    flex: 1;

    .title {
      font-size: 18px;
      font-weight: 600;
      color: $color-text-primary;
    }

    .sdicon {
      margin-right: 14px;
      font-size: 20px;
      color: $color-text-secondary;
      cursor: pointer;

      &:hover {
        color: $--color-primary;
      }
    }
  }

  .content {
    box-sizing: border-box;

    .data-list {
      overflow-x: hidden;
      overflow-y: auto;
      margin: 10px auto;
      margin-bottom: 0;
      width: 100%;
      height: 480px;

      .notificationCard {
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .btn-wrap {
    display: flex;
    justify-content: flex-end;
    margin: 6px auto;
  }

  .refresh-btn {
    margin: 3px 0;
    font-size: 16px;
    color: $font-color;
    cursor: pointer;
    line-height: 18px;

    .icon {
      vertical-align: -.2em;
    }
  }
}

// el组件样式
::v-deep {
  .el-dialog__header {
    overflow: hidden;
  }

  .el-radio-button {
    &.is-focus,
    &:active,
    &:focus,
    &:link,
    &:visited,
    &.is-active {
      box-shadow: none;

      .el-radio-button__inner {
        border-left: 0;
        font-weight: 600;
        color: $--color-primary;
        background: $--color-primary-hover;
        box-shadow: none;
      }
    }

    &:first-child .el-radio-button__inner {
      border-left: 0;
    }

    .el-radio-button__inner {
      border: 0;
      padding: 0;
      padding-left: 23px;
      width: 100%;
      height: 54px;
      font-size: 16px;

      // font-weight: 600;
      text-align: left;
      color: $color-text-primary;
      background: #FFFFFF;
      box-shadow: none;
      line-height: 54px;

      .icon {
        font-size: 20px;
        vertical-align: -.2em;
      }

      .label {
        display: inline-block;
        padding-left: 10px;
        min-width: 80px;
        text-align: left;
      }
    }
  }

  .el-tabs__header {
    margin: 0;
  }

  .el-tabs__nav {
    display: flex;
    border-bottom: 0;
    width: 100%;

    .el-tabs__active-bar {
      display: none;
    }

    .el-tabs__item {
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px solid $border-color-lighter;
      border-right: none;
      padding: 0;
      min-width: 0;
      height: 42px;
      font-weight: 400;
      background-color: #FFFFFF;
      flex: 1;
      line-height: 42px;

      .el-badge .el-badge__content {
        border: 1px solid $color-warning;
        padding: 0 5px;
        min-width: 8px;
      }

      &.is-active {
        border: 1px solid $--color-primary;
        color: $color-FFFFFF;
        background-color: $--color-primary;

        // .el-badge .el-badge__content {
        //   margin-left: 4px;
        //   color: $--color-primary;
        //   background-color: #FFFFFF;
        // }

        .el-badge.hn .el-badge__content {
          border: 1px solid $color-warning;

          // color: $color-FFFFFF;
          // background-color: $color-warning;
        }
      }

      .label {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .el-badge {
        display: flex;
        align-items: center;
        margin-left: 2px;

        &.more {
          margin-left: 4px;

          .el-badge__content {
            margin-left: 0;
            border-radius: 50px;
            width: 30px;
            height: 22px;
            font-size: 14px;
            line-height: 22px;
          }
        }
      }

      &:nth-child(2) {
        border-radius: 4px 0 0 4px;
      }

      &:last-child {
        border-right: 1px solid $border-color-lighter;
        border-radius: 0 4px 4px 0;
      }

      &.is-active:last-child {
        border-right: 0;
        border-radius: 0 4px 4px 0;
      }

      &:hover:not(.is-active) {
        color: $--color-primary;
      }
    }
  }

  .el-divider__text {
    color: $color-text-secondary;
    background-color: $color-F2F2F2;
  }

  .el-empty {
    margin-top: 84px;

    .el-empty__image {
      width: auto;
    }

    .el-empty__description {
      margin-top: -20px;
    }

    .el-empty__description p {
      font-size: 18px;
      color: $color-text-secondary;
    }
  }

  .empty-box {
    .empty-img {
      font-size: 14em;
      color: #FFFFFF;
    }
  }
}
</style>

<template>
  <div>
    <el-dialog
      class="notification-list-dialog"
      :visible.sync="dialogVisible"
      width="400px"
      height="627px"
      :before-close="handleClose"
      :close-on-click-modal="false"
      append-to-body
      center
      :lock-scroll="false"
    >
      <div slot="title" class="header">
        <div class="title">通知提醒</div>
        <!--
          <div class="control">
          <icon class="sdicon" type="chengjie-setting" @click="handleNotificationSetting" />
          </div>
        -->
      </div>
      <div class="content">
        <!-- 顶部tab栏 -->
        <el-tabs
          v-model="activeTab"
          @tab-click="handleClickTab"
        >
          <el-tab-pane
            v-for="tab in tabsList"
            :key="tab.id"
            :name="`${tab.id}`"
          >
            <span slot="label" class="label">
              {{ tab.label }}
              <el-badge
                v-if="tab.msgCount > 0"
                :value="tab.msgCount > 99 ? '99+' : tab.msgCount"
                :class="['hn', { more: tab.msgCount > 99 }]"
              />
            </span>
            <div class="btn-wrap">
              <span v-if="tab.msgCount" class="refresh-btn text-link" @click="orderHandleAllRead">
                全部已读
              </span>
            </div>
            <!-- 消息提醒列表 -->
            <div
              v-infinite-scroll="getNotificationList"
              class="data-list"
              :infinite-scroll-disabled="disabled"
              :infinite-scroll-distance="50"
            >
              <template v-if="+tab.id === +activeTab && list.length > 0">
                <!-- 消息卡片 -->
                <component
                  :is="data.notificationCard"
                  v-for="data in list"
                  :key="data.id"
                  class="notificationCard"
                  :card-data="data"
                  :type="2"
                  @handle-close="handleClose"
                  @success="handleRefresh"
                />

                <el-divider class="mt50">
                  <template v-if="loading">
                    加载中
                  </template>
                  <template v-else-if="isNoMore">
                    到底了
                  </template>
                </el-divider>
              </template>

              <el-empty v-if="!loading && !list.length">
                <div slot="image" class="empty-box">
                  <icon type="chengjie-empty" class="empty-img" />
                </div>
              </el-empty>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>

    <!-- 消息提醒设置弹窗 -->
    <el-dialog
      title="消息提醒设置"
      :visible.sync="isShowNotificationSetting"
      width="1020px"
      height="700px"
      :before-close="() => { isShowNotificationSetting = false }"
      :close-on-click-modal="false"
      append-to-body
      center
      :lock-scroll="true"
    >
      <NotificationSettingDialog />
    </el-dialog>
  </div>
</template>

<script>
// TIPS: 消息列表里的平台公告需要另外的接口获取列表数据和提交已读
import NotificationSettingDialog from '../notification-setting-dialog/notification-setting-dialog.vue' // 消息提醒设置弹窗
import OrderNotificationCard from '../notification-card-list/order-notification-card.vue' // 交易订单消息提醒卡片组件
import PlatformNotificationCard from '../notification-card-list/platform-notification-card.vue' // 平台公告消息提醒卡片组件
import BreakContractNotificationCard from '../notification-card-list/break-contract-notification-card.vue' // 交易违约、违约判罚通知组件
import AuditNotificationCard from '../notification-card-list/audit-notification-card.vue' // 审核申请通知组件
import EvidenceMaterialNotificationCard from '../notification-card-list/evidence-material-notification-card.vue' // 佐证材料审核通知组件

import notificationApi from '@/apis/notification' // api接口
import { NOTIFICATION_LIST, TOOLBAR_REFRESH, NOTICE_UNREAD_COUNT } from '@/event/modules/site' // 刷新消息中心列表事件
// import articleApi from '@/apis/article' // 平台公告接口

// 消息通知列表tab
const NOTIFICATION_TAB = Object.freeze({
  SALE: {
    id: 1,
    label: '我是票方',
    name: 'sale'
  },
  BUY: {
    id: 2,
    label: '我是资方',
    name: 'buy'
  },
  // OTHER: {
  //   id: 3,
  //   label: '平台公告',
  //   name: 'other'
  // },
})

// 消息通知类型使用对应的消息组件 默认1-订单交易状态变更消息 2-意向订单消息 3-交易违约 4-违约判罚 -1-平台公告(注意是手动添加的id,非后端返回数据,后续若被其他顶替记得更换为别的key)
const NOTIFICATION_TYPE_MAP = Object.freeze({
  1: 'OrderNotificationCard',
  2: 'OrderNotificationCard',
  3: 'BreakContractNotificationCard',
  4: 'BreakContractNotificationCard',
  5: 'OrderNotificationCard',
  [-1]: 'PlatformNotificationCard',
  6: 'AuditNotificationCard',
  7: 'EvidenceMaterialNotificationCard',
  8: 'OrderNotificationCard' // 签署合同
})

export default {
  name: 'notification-list-dialog',

  components: {
    OrderNotificationCard, // 交易订单消息提醒卡片组件
    NotificationSettingDialog, // 消息提醒设置弹窗
    PlatformNotificationCard, // 平台公告消息提醒卡片组件
    BreakContractNotificationCard, // 交易违约、违约判罚通知组件
    AuditNotificationCard, // 审核结果通知组件
    EvidenceMaterialNotificationCard // 佐证材料审核通知组件
  },

  data() {
    return {
      loading: false,
      dialogVisible: false, // 是否显示消息列表
      activeTab: `${NOTIFICATION_TAB.SALE.id}`, // 顶部tab选项列表-当前选中的tab选项
      tabsList: [], // 顶部tab选项列表
      list: [], // 消息提醒列表
      isShowNotificationSetting: false, // 是否加载消息提醒设置弹窗组件
      notificationSettingVisible: false, // 是否显示消息提醒设置弹窗
      NOTIFICATION_TAB, // 消息通知列表tab
      // 查询参数
      query: {
        pageNum: 1, // 当前页码
        pageSize: 10, // 每页条数
      },
      totalRecord: 0, // 总条数
      isNoMore: false, // 没有更多了
      isLoadError: false, // 是否请求加载失败（用来阻止触底加载数据等）
    }
  },

  computed: {
    // 是否可请求列表数据
    disabled() {
      return this.loading || this.isNoMore || this.isLoadError
    },
    // 企业ID
    corpId() {
      return this.$store.state.user?.userInfo?.corpId
    },
  },

  created() {
    // 监听刷新消息中心列表
    this.$event.on(NOTIFICATION_LIST, () => {
      this.handleRefresh()
    })
  },

  methods: {

    // 初始化
    async init() {
      this.dialogVisible = !this.dialogVisible
      if (!this.dialogVisible) return

      this.activeTab = `${NOTIFICATION_TAB.SALE.id}` // 每次打开都默认选中平台公告
      // 初始化数据/
      await this.initData()
    },

    // 初始化数据
    async initData() {
      this.clearData()

      try {
        // this.loading = true

        await this.getNotificationList() // 获取消息提醒列表
        await this.getMsgUnReadCount() // 获取消息提醒的所有消息数量

        // this.loading = false
      } catch (error) {
        // console.log('消息列表error :>> ', error)

        // this.loading = false
      }
    },

    // 获取消息提醒列表
    async getNotificationList() {
      if (this.isNoMore || this.loading) return

      // 列表传参
      const params = {
        ...this.query, // 分页参数
      }
      let getListApi = null // 列表请求接口

      // 是否平台公告tab (用于平台公告列表需另外调取别的接口)
      // const isPlatformTab = +this.activeTab === NOTIFICATION_TAB.OTHER.id
      // if (isPlatformTab) {
      //   getListApi = articleApi.postContentListPageLogin // 平台公告列表接口
      //   params.type = 1 // 内容类型 默认1-平台公告 2-平台资讯 3-行业动态 4-深度益课堂 5-诚信企业表彰 6-新手指引 7-深度早报
      // } else {
      //   getListApi = notificationApi.getNotificationList // 消息中心列表接口
      //   params.msgTab = +this.activeTab // 消息tab 默认1-卖方 2-买方
      // }

      getListApi = notificationApi.getNotificationList // 消息中心列表接口
      params.msgTab = +this.activeTab // 消息tab 默认1-卖方 2-买方

      try {
        this.loading = true
        const data = await (getListApi(params) || {})
        const { rowList, totalRecord } = data

        // 处理数据
        const newList = rowList.reduce((t, p) => {
          const item = Object.assign({}, JSON.parse(p.bodyJson), { ...p })

          // isPlatformTab && this.$set(p, 'msgType', -1) // 由于平台公告列表接口没有该字段，故手动添加，以便使用对应的消息卡片组件

          // 当前消息卡片使用对应的消息组件
          item.notificationCard = NOTIFICATION_TYPE_MAP[p.msgType]
          t.push(item)
          return t
        }, [])

        this.list = this.list.concat(newList)

        this.totalRecord = totalRecord
        this.query.pageNum++

        this.isNoMore = totalRecord === 0 || rowList.length === 0 ? true : (this.list.length > 0 && (this.list.length >= totalRecord))

        this.loading = false
        this.isLoadError = false
      } catch (error) {
        this.loading = false
        this.isLoadError = true
        // console.log('消息列表error :>> ', error)
        return error
      }
    },

    // 获取消息提醒所有的未读数量
    async getMsgUnReadCount() {
      try {
        const data = await (notificationApi.getMsgUnReadCount() || {})
        const {
          // sum = 0, // 总未读数量
          buyerCount = 0, // 资方未读数量
          sellerCount = 0, // 票方未读数量
          announcementCount = 0, // 平台公告未读数量
        } = data
        this.$event.emit(NOTICE_UNREAD_COUNT, announcementCount)
        this.tabsList = this.tabsList.map(v => {
          switch (v.id) {
            case NOTIFICATION_TAB.SALE.id: // 票方
              v.msgCount = sellerCount
              break
            case NOTIFICATION_TAB.BUY.id: // 资方
              v.msgCount = buyerCount
              break
            // case NOTIFICATION_TAB.OTHER.id: // 平台公告
            //   v.msgCount = announcementCount
            //   break
            default:
              break
          }
          return v
        })
        // this.tabsList = this.tabsList.splice(0, 2)

        this.$emit('notification-all-count', buyerCount + sellerCount || 0)
      } catch (error) {
        // console.log('消息提醒未读数量error :>> ', error)
        return error
      }
    },

    // 点击tab选择
    handleClickTab(tab) {
      this.activeTab = tab?.name
      this.handleRefresh()
    },

    // 刷新
    handleRefresh() {
      // if (this.loading) return
      this.$nextTick().then(() => {
        this.initData()
      })
    },

    // 清除数据
    clearData() {
      this.list = [] // 清除列表
      this.query.pageNum = 1
      this.totalRecord = 0
      this.isNoMore = false
      this.isLoadError = false
      this.loading = false
      this.tabsList = Object.values(NOTIFICATION_TAB) // 重置tab数据
    },

    // 关闭弹窗
    handleClose() {
      this.clearData()
      this.$event.emit(TOOLBAR_REFRESH)
      this.dialogVisible = false
    },

    // 是否展示消息提醒设置弹窗
    handleNotificationSetting() {
      this.isShowNotificationSetting = true // 加载消息提醒设置弹窗组件
      this.$nextTick().then(() => {
        this.notificationSettingVisible = true
      })
    },
    // 消息全部忽略（我是票方｜我是资方）
    async orderHandleAllRead() {
      try {
        let params = { corpId: this.corpId, msgTab: this.activeTab }
        await notificationApi.orderMessageAllRead(params)
        this.handleRefresh()
      } catch (error) {
        return error
      }
    },
    // 全部已读(目前只有平台公告独有功能)
    async handleAllRead() {
      try {
        await (notificationApi.updateAllRead() || {})

        this.handleRefresh()
      } catch (error) {
        // console.log('消息列表error :>> ', error)

        return error
      }
    }
  }
}
</script>
