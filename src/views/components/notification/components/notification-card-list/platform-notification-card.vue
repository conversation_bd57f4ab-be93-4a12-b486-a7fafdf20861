<!-- 平台消息通知、平台公告通知组件 -->
<style lang="scss" scoped>
@import "./common.scss";

.icon-new {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 9;
  font-size: 49px;
}

.card-header-new {
  @include font(12px,$color-FFFFFF,500);
  @include flex-cc;

  margin-left: 6px;
  border-radius: 8px 10px 10px 0;
  width: 44px;
  height: 19px;
  background-color: $color-warning;
}

::v-deep .notification-card {
  padding: 8px 16px;
  width: 360px;

  .card-footer {
    margin-top: 8px;
    border-top: 0;
    padding-top: 0;
  }

  .card-body {
    margin-top: 10px;
    padding: 8px;
  }
}

.notification-card .card-header .card-header-title.label::after {
  width: 2px;
}

.notification-card .card-header .card-header-tip {
  @include font(14px,$color-warning);
}

.footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
}

.ismqtt {
  ::v-deep {
    .card-header {
      display: none; // 去掉标题
    }

    .notification-card {
      padding-top: 0;
      width: 360px !important;
    }

    .card-footer {
      margin-top: 0;
      border-top: 0;
      padding-top: 0;
    }

    .svg-icon {
      display: inline-block;
      margin-right: 10px;
      width: 20px;
      height: 20px;
      line-height: 20px;
      vertical-align: sub;
    }
  }
}

.down-time {
  @include flex-vc;

  margin-right: 20px;
}
</style>

<template>
  <div :class="isMQTT ? 'ismqtt' : 'nomqtt'">
    <div>
      <!-- 平台消息通知mqtt推送 -->
      <template v-if="isMQTT">
        <NotificationCard v-if="myData">
          <!-- <div slot="header" /> -->

          <!--
            <template slot="header">
            <h2 class="card-header-title label">
            <text-tooltip :content="myData.title" show-when-overflow />
            </h2>
            <div v-if="isNew" class="card-header-new">
            NEWS
            </div>
            <div class="card-header-tip">
            <text-tooltip :content="myData.tips" show-when-overflow />
            </div>
            </template>
          -->

          <RichText
            v-if="myData && myData.content"
            slot="main"
            height="123px"
            :content="myData.content"
          />
        </NotificationCard>
      </template>
      <template v-else>
        <!-- 消息列表的平台公告 -->
        <NotificationCard v-if="myData" :data="myData">
          <!-- <RichText v-if="myData && myData.content" slot="main" :content="myData.content" /> -->
          <!-- 卡片底部操作区 -->
          <template slot="footer">
            <el-button
              width="104"
              height="40"
              type="primary"
              @click="onClickHandle"
            >
              查看详情
            </el-button>
            <icon v-if="isNew" type="chengjie-new2" class="icon icon-new" />
          </template>
        </NotificationCard>
      </template>

      <!-- 消息通知的卡片不在白色背景里面 -->
      <div class="footer">
        <span v-if="showCountDown" class="down-time">{{ myData.countDownTime }}秒后自动关闭</span>
        <el-button
          v-if="!myData.buttonLink"
          width="104"
          height="32"
          type="primary"
          @click="onClickHandle"
        >
          我知道了
        </el-button>
        <el-button
          v-else
          width="104"
          height="32"
          type="primary"
          @click="onButtonLink"
        >
          立即前往
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import NotificationCard from '../notification-card/notification-card.vue'
import { dataType, isNull } from '@/common/js/util' // 获取数据类型
import { notificationQueue } from '@/views/components/notification/js/notification' // 消息实例队列
import notificationApi from '@/apis/notification' // 消息中心接口
import {
// NOTIFICATION_LIST, // 刷新消息中心列表事件
} from '@/event/modules/site'
import RichText from '@/views/components/rich-text/rich-text-viewer.vue'
import {
  MSG_TYPE, // 消息提醒类型
} from '@/constants/notification'

// 平台公告通知描述列表
const PLATFORM_ROW = [
  {
    id: 1,
    isEllipsisText: true,
    list: [
      {
        id: 1,
        label: '',
        key: 'title',
        value: null,
        isBold: true,
      },
    ],
  },
  {
    id: 2,
    isEllipsisText: true,
    ellipsisTextNum: 2,
    list: [
      {
        id: 1,
        label: '概要',
        key: 'outline',
        value: null,
        isBold: false,
      },
    ],
  },
]

export default {
  name: 'platform-notification-card',

  components: {
    NotificationCard, // 消息组件卡片
    RichText, // 富文本编辑器
  },

  props: {
    // vue.extend传入来的data数据（mqtt传入使用的数据）
    data: {
      type: Object,
      default: () => ({})
    },
    // 该消息组件id
    notificationId: {
      type: String,
      default: ''
    },
    // 消息列表传入使用的数据
    cardData: {
      type: Object,
      default: () => ({})
    },
    // 该交易消息卡片组件用途 1-MQTT推送 2-消息列表
    type: {
      type: Number,
      default: 1
    },
  },

  data() {
    return {
      myData: null, // 保存vue.extend传入来的data数据
    }
  },

  computed: {
    // 该消息卡片组件是否用于mqtt
    isMQTT() {
      return this.type === 1 // 1-MQTT推送 2-消息列表
    },

    // 该消息卡片组件是否用于消息列表
    isNotificationList() {
      return this.type === 2 // 1-MQTT推送 2-消息列表
    },

    // 是否未读
    isNew() {
      const {
        isRead = 0, // 是否已读 默认0-未读 1-已读
      } = (this.myData || {})
      return isRead === 0
    },
    // 是否显示倒计时
    showCountDown() {
      const { showCloseTime, duration, countDownTime } = this.data
      return showCloseTime && duration > 0 && countDownTime > 0
    }
  },

  watch: {
    // 监听该数据,若传入该数据表明该组件用在列表或其他地方上,而不是消息推送
    cardData: {
      handler(val) {
        if (val && this.isNotificationList) {
          this.myData = this.initData(val)
        }
      },
      immediate: true,
      deep: true
    },
    // 监听vue.extend传入来的data数据，再保存在自身数据myData上使用，实现数据响应式
    data: {
      handler(val) {
        if (val && this.isMQTT) {
          this.myData = val
        }
      },
      immediate: true,
      deep: true
    },
  },

  methods: {
    // 数据初始化
    initData(data) {
      // 交易状态transaction  催单reminders 目前返回数据一样
      if (!data) return

      // 传入的不是obj数据,无须处理数据，若是字符串消息返回,不是则跳出
      const dataTypeRes = dataType(data) // 数据类型
      if (data && dataTypeRes !== 'object') {
        if (dataTypeRes !== 'string') return // 不是消息字符串跳出
        return data
      }

      const res = { ...data }

      res.title = MSG_TYPE.PLATFORM.cardTitle // 消息卡片头部标题
      let rows = PLATFORM_ROW

      res.tips = data?.msg || '' // 消息头部提示

      // 消息描述列表
      const newRows = rows.map(v => {
        v.list = v.list.map(item => {
          if (data[item.key]) {
            item.value = data[item.key]
          }

          // 概要
          if (item.key === 'outline') {
            item.value = `${isNull(data.outline) ? '该公告暂无概要，您可点击“查看详情”查看该公告的全部内容。' : data[item.key]}`
          }

          return item
        })
        return v
      })

      res.rows = JSON.parse(JSON.stringify(newRows))

      return res
    },

    // 关闭消息
    closeNotification(id) {
      notificationQueue.closeNotification(id)
    },

    // 处理
    // async onClickHandle() {
    onClickHandle() {
      if (!this.myData) return
      const {
        id = 0, // 消息ID(目前用于已读)
        messageId = 0, // 消息ID(目前用于已读) 该字段用于mqtt类型
      } = this.myData
      const msgId = this.isMQTT ? messageId : id // mqtt使用 messageId 列表使用 id
      // let isRefreshList = false // 是否需要刷新消息列表

      // 消息列表里的查看详情跳转到平台公告详情
      if (this.isNotificationList) {
        this.handleJumpNews(msgId)
      }

      // 若为新消息执行已读功能
      // if (this.isNew) {
      // this.isNew && await this.handleUpdateRead(msgId)
      // isRefreshList = true
      // }

      this.closeNotification(this.notificationId) // 点击处理后，关闭该消息
      // isRefreshList && this.$event.emit(NOTIFICATION_LIST) // 刷新消息提醒列表、未读数量
    },

    // 立即前往目标路由页面
    onButtonLink() {
      let path = this.myData.buttonLink || '/user-center/sale-draft?tab=1'
      window.location.href = path
      this.closeNotification(this.notificationId) // 点击处理后，关闭该消息
    },

    // 消息已读处理
    async handleUpdateRead(id) {
      try {
        await (notificationApi.putNotificationCountPlatform(id) || {}) // 已读消息（平台公告专用接口）
      } catch (error) {
        // console.log('消息已读error :>> ', error)
      }
    },

    // 平台公告跳转详情页处理
    handleJumpNews(id) {
      // let params = { pageType: 'notice', id, from: 'notification' }
      // this.$router.push({ name: 'news-detail', params })
      // window.open(`/user-center/news-detail/${id}`) // 新开处理页面
      // window.open(`/news/announcement/${id}`) // 新开处理页面
      // this.$emit('handle-close')
      // router.push(`/news/announcement/${id}`)
      // this.$router.push(`/news/announcement/${id}`)
      // window.open(`/news/announcement/${id}`)
      window.location.href = `/news/announcement/${id}`
    },
  },
}
</script>
