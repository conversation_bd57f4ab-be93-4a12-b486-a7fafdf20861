<!-- 准入异常提示 -->
<style lang="scss" scoped>
@import "./common.scss";

.btn {
  display: flex;
  justify-content: center;
  margin-top: 23px;
}

.anomalous-tips-wrap {
  position: relative;
  font-size: 16px;
  color: #333333;

  .close-icon {
    cursor: pointer;
    position: absolute;
    top: -52px;
    right: -8px;
  }
}

.link {
  border-bottom: 1px solid $--color-primary;
  color: $--color-primary;
  cursor: pointer;
}

.line {
  line-height: 26px;
}

.more {
  &:first-child {
    margin-right: 2px;
  }
}

.center {
  text-align: center;
}

.p-b8 {
  padding-bottom: 8px;
}

.red-cls {
  color: $color-warning;
}
</style>

<style lang="scss">
  .anomalous-tip-notification-card {
    padding: 0 !important;
    width: 412px !important;

    .el-notification__title {
      padding: 12px 20px;
    }

    .el-notification__group {
      margin: 0 !important;
      width: 100%;
    }

    .el-notification__content {
      margin: 0 !important;
      padding: 16px 20px;
      background: #F2F2F2 !important;
    }
  }
</style>

<template>
  <div v-if="myData" class="anomalous-tips-wrap">
    <div class="close-icon">
      <div v-if="countdown"><span class="red-cls">{{ countdown }}s后</span> 可关闭</div>
      <icon v-else type="chengjie-close1" @click="closeNotification" />
    </div>
    <div class="content">
      <div class="center p-b8">
        <icon class="red-cls" size="40" type="chengjie-fengxiantubiao" />
      </div>

      <div class="line">
        新增{{ myData.corpNum }}条
        <span v-if="myData.sceneType === SCENE_TYPE.ACCESS.id" class="link" @click="link(SCENE_TYPE.ACCESS.id)">{{ SCENE_TYPE.ACCESS.name }}</span>
        <span v-if="myData.sceneType === SCENE_TYPE.VIOLATE.id" class="link" @click="link(SCENE_TYPE.VIOLATE.id)">{{ SCENE_TYPE.VIOLATE.name }}</span>
        <template v-if="myData.sceneType === SCENE_TYPE.ALL.id">
          <template
            v-for="(item, idx) in SCENE_TYPE.ALL.name"
          >
            <span
              :key="item.id"
              class="link more"
              @click="link(item.id)"
            >{{ item.name }}</span>
            {{ idx !== SCENE_TYPE.ALL.name.length - 1 ? '、' : '' }}
          </template>
        </template>
        的企业，请及时查看。使用承接识票签收核验，有效避免风险发生。
      </div>
    </div>
    <div class="btn">
      <el-button
        width="104"
        height="32"
        type="primary"
        @click="link()"
      >
        立即查看
      </el-button>
    </div>
  </div>
</template>

<script>
import { dataType } from '@/common/js/util' // 获取数据类型
import { notificationQueue } from '@/views/components/notification/js/notification' // 消息实例队列
import notificationApi from '@/apis/notification'
import {
  MSG_TYPE, // 消息提醒类型
} from '@/constants/notification'

const LINK_MAP = {
  1: '/user-center/leakage-info?tabId=3',
  2: '/user-center/leakage-info?tabId=4',
  3: '/user-center/leakage-info?tabId=3'
}

const SCENE_TYPE = Object.freeze({
  ACCESS: {
    id: 1,
    name: '《异常准入名单》'
  },
  VIOLATE: {
    id: 2,
    name: '《违反软件安全规则名单》'
  },
  ALL: {
    id: 3,
    name: [{ id: 1, name: '《异常准入名单》' }, { id: 2, name: '《违反软件安全规则名单》' }]
  }
})

export default {
  name: 'audit-notification-card',

  props: {
    // vue.extend传入来的data数据（mqtt传入使用的数据）
    data: {
      type: Object,
      default: () => ({})
    },
    // 该消息组件id
    notificationId: {
      type: String,
      default: ''
    },
  },

  data() {
    return {
      SCENE_TYPE,
      myData: null, // 保存vue.extend传入来的data数据
      MSG_TYPE, // 消息提醒类型
      countdown: 5, // 初始倒计时设为 5 秒
      timer: null, // 定时器
    }
  },

  computed: {
  },

  watch: {
    // 监听vue.extend传入来的data数据，再保存在自身数据myData上使用，实现数据响应式
    data: {
      handler(val) {
        if (val) {
          this.myData = val
        }
      },
      immediate: true,
      deep: true
    },
  },
  beforeDestroy() {
    clearInterval(this.timer) // 确保在组件销毁时清除定时器
  },
  mounted() {
    this.startCountdown()
  },

  methods: {
    // 数据初始化
    initData(data) {
      if (!data) return
      // 传入的不是obj数据,无须处理数据，若是字符串消息返回,不是则跳出
      const dataTypeRes = dataType(data) // 数据类型
      if (data && dataTypeRes !== 'object') {
        if (dataTypeRes !== 'string') return // 不是消息字符串跳出
        return data
      }

      const res = { ...data }
      return res
    },
    // init() {
    // setTimeout(() => {
    //   this.isClose = true
    // }, 5000)

    // },

    startCountdown() {
      this.countdown = 5 // 重置倒计时
      this.timer = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--
        } else {
          clearInterval(this.timer) // 清除定时器
        }
      }, 1000) // 每秒减少 1
    },

    // 关闭消息
    closeNotification() {
      clearInterval(this.timer)
      this.markMsgReaded()
      notificationQueue.closeNotification(this.notificationId)
    },

    link(type) {
      window.location.href = LINK_MAP[type || this.myData.sceneType]
      this.markMsgReaded()
    },
    // 已读标记
    async markMsgReaded() {
      try {
        await notificationApi.postRiskMsgNoticeRead()
      } catch (error) {
        // console.log(error)
      }
    }

  },
}
</script>
