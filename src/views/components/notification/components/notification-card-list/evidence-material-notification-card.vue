<!-- 交易状态通知组件 -->
<style lang="scss" scoped>
@import "./common.scss";

.icon-new {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 9;
  font-size: 49px;
}

.item-label {
  display: contents;

  @include font(14px, $color-text-regular);
}

.item-value {
  display: contents;

  @include font(14px,  $color-text-primary);
}

.card-header-new {
  @include font(12px,$color-FFFFFF,500);
  @include flex-cc;

  margin-left: 6px;
  border-radius: 8px 10px 10px 0;
  width: 44px;
  height: 19px;
  background-color: $color-warning;
}

.btn-small {
  width: 76px !important;
  height: 32;
}

.btn-big {
  width: 104px !important;
  height: 32;
}

.notificationCard {
  width: 360px;
}

::v-deep .notification-card {
  padding: 8px 16px;
  width: 360px;

  .card-footer {
    margin-top: 8px;
    border-top: 0;
    padding-top: 0;
  }
}

.ismqtt {
  ::v-deep {
    // .card-footer {
    //   margin-top: 0;
    //   border-top: 0;
    //   padding-top: 0;
    // }

    .svg-icon {
      display: inline-block;
      margin-right: 10px;
      width: 20px;
      height: 20px;
      line-height: 20px;
      vertical-align: sub;
    }
  }
}

.notification-card .card-header .card-header-title.label::after {
  width: 2px;
}

.notification-card .card-header .card-header-tip {
  @include font(14px,$color-warning);
}

.messageTile {
  width: 372px;
  color: $color-warning;
}

.new-draft {
  position: absolute;
  top: 5px;
  left: 110px;
}

.left {
  left: 140px;
}
</style>

<template>
  <div :class="isMQTT ? 'ismqtt' : 'nomqtt'">
    <div :class="['new-draft', myData.topicChannel === WEB_SOCKET_EVENT.ORDER_EVIDENCE_UPLOAD_REMIND_NOTICE ? 'left' : '']">
      <!-- <span v-if="myData.draftType && isMQTT" class="g-xinpiao">新票</span> -->
    </div>
    <div class="messageTile">
      <text-tooltip
        v-if="isMQTT"
        :content="myData.tips"
        style="white-space: pre-line;"
        show-when-overflow
      >
        <!-- <icon class="icon question-icon error-tip" type="chengjie-exclamation-circle" /> -->
        {{ myData.tips }}
      </text-tooltip>
    </div>
    <NotificationCard
      v-if="myData"
      :data="myData"
      :is-new="isNew"
      :is-mqtt="isMQTT"
    >
      <template slot="header">
        <template v-if="!isMQTT">
          <h2 class="card-header-title label">
            <text-tooltip :content="myData.title" show-when-overflow />
          </h2>
          <div v-if="isNew" class="card-header-new">
            NEW
          </div>
        </template>
        <div class="card-header-tip">
          <text-tooltip v-if="!isMQTT" :content="myData.tips" show-when-overflow />
        </div>
      </template>

      <ul
        slot="main"
        class="desc-list"
      >
        <li
          v-for="row in myData.rows"
          :key="row.id"
        >
          <div
            v-for="item in row.list"
            :key="item.id"
            class="ellipsis-mb-4"
          >
            <div class="item-label">
              {{ item.label }} ：
            </div>
            <div class="item-value">
              <text-tooltip :content="item.value" show-when-overflow />
            </div>
          </div>
        </li>
      </ul>

      <!-- 卡片底部操作区 -->
      <template slot="footer">
        <div class="footer">
          <el-button
            class="btn-small"
            width="64"
            height="40"
            type="primary"
            @click="onClickHandle"
          >
            {{ myData.topicChannel === 'orderEvidenceAuditSuccessNotice' ? '查看' : '处理' }}
          </el-button>
        </div>
      </template>
    </NotificationCard>
  </div>
</template>

<script>
import NotificationCard from '../notification-card/notification-card.vue'
import { yuan2wan } from '@/common/js/number' // 金额单位转换
import { dataType, isNull } from '@/common/js/util' // 获取数据类型
import { notificationQueue } from '@/views/components/notification/js/notification' // 消息实例队列
import notificationApi from '@/apis/notification' // 消息中心接口
import {
  NOTIFICATION_LIST, // 刷新消息中心列表事件
} from '@/event/modules/site'
import { formatTime } from '@/common/js/date' // 时间格式化
import { WEB_SOCKET_EVENT } from '@/websocket/constant' // mqtt监听事件

import {
  MSG_TYPE, // 消息提醒类型
} from '@/constants/notification'
import { ROLE } from '@/constant'

// 交易消息提醒描述列表
const ORDER_ROW = [
  {
    id: 1,
    isEllipsisText: true,
    list: [
      {
        id: 1,
        label: '承兑人',
        key: 'acceptorName',
        value: null,
        isBold: true,
      },
    ],
  },
  {
    id: 2,
    isEllipsisText: true,
    list: [
      {
        id: 1,
        label: '', // 票据金额 / 实付金额(实收金额)
        key: 'draftAmountPayOrActual', // 自定义key，后面动态取值
        value: null,
        isBold: true,
      },
      {
        id: 2,
        label: '通知时间',
        key: 'sendTime',
        value: null,
        isBold: true,
      },
    ],
  },
]

export default {
  name: 'evidence-material-notification-card',

  components: {
    NotificationCard, // 消息组件卡片
  },

  props: {
    // vue.extend传入来的data数据（mqtt传入使用的数据）
    data: {
      type: Object,
      default: () => ({})
    },
    // 该消息组件id
    notificationId: {
      type: String,
      default: ''
    },
    // 消息列表传入使用的数据
    cardData: {
      type: Object,
      default: () => ({})
    },
    // 该交易消息卡片组件用途 1-MQTT推送 2-消息列表
    type: {
      type: Number,
      default: 1
    },
  },

  data() {
    return {
      WEB_SOCKET_EVENT,
      myData: null, // 保存vue.extend传入来的data数据
      MSG_TYPE, // 消息提醒类型
    }
  },

  computed: {
    // 该消息卡片组件是否用于mqtt
    isMQTT() {
      return this.type === 1 // 1-MQTT推送 2-消息列表
    },

    // 该消息卡片组件是否用于消息列表
    isNotificationList() {
      return this.type === 2 // 1-MQTT推送 2-消息列表
    },

    // 是否未读
    isNew() {
      const {
        isRead = 0, // 是否已读 默认0-未读 1-已读
      } = (this.myData || {})
      return isRead === 0
    },
  },

  watch: {
    // 监听该数据,若传入该数据表明该组件用在列表或其他地方上,而不是消息推送
    cardData: {
      handler(val) {
        if (val && this.isNotificationList) {
          this.myData = this.initData(val)
        }
      },
      immediate: true,
      deep: true
    },
    // 监听vue.extend传入来的data数据，再保存在自身数据myData上使用，实现数据响应式
    data: {
      handler(val) {
        if (val && this.isMQTT) {
          this.myData = val
        }
      },
      immediate: true,
      deep: true
    },
  },

  methods: {

    // 数据初始化
    initData(data) {
      // 交易状态transaction  催单reminders 目前返回数据一样
      if (!data) return

      // 传入的不是obj数据,无须处理数据，若是字符串消息返回,不是则跳出
      const dataTypeRes = dataType(data) // 数据类型
      if (data && dataTypeRes !== 'object') {
        if (dataTypeRes !== 'string') return // 不是消息字符串跳出
        return data
      }

      const res = { ...data }

      let rows = null

      rows = ORDER_ROW
      res.title = MSG_TYPE.ORDER_REFRESH.cardTitle// 消息卡片头部标题

      res.tips = data?.msg || '' // 消息头部提示

      // 消息描述列表
      const newRows = rows.map(v => {
        v.list = v.list.map(item => {
          if (data[item.key]) {
            item.value = data[item.key]
          }

          // 票据金额 / 实付(实收)
          if (item.key === 'draftAmountPayOrActual') {
            const { draftActualAmount = 0, draftPaymentAmount = 0, role } = data
            const isSale = role === ROLE.SALE.id
            const amount = isSale ? draftActualAmount : draftPaymentAmount // 金额
            item.label = `票据金额 / ${isSale ? '到账金额 ' : '实付金额'}`
            item.value = `${isNull(data.draftAmount) ? '--' : yuan2wan(data.draftAmount)}万 / ${isNull(amount) ? '--' : yuan2wan(amount)}万`
          }

          // 发布时间
          if (item.key === 'publishTime') {
            item.value = formatTime(data[item.key], 'YYYY-MM-DD hh:mm:ss')
          }

          return item
        })
        return v
      })

      res.rows = JSON.parse(JSON.stringify(newRows))

      return res
    },

    // 关闭消息
    closeNotification(id) {
      notificationQueue.closeNotification(id)
    },

    // 处理
    async onClickHandle() {
      if (!this.myData) return
      const {
        id = 0, // 消息ID(目前用于已读)
        messageId = 0, // 消息ID(目前用于已读) 该字段用于mqtt类型
      } = this.myData
      const msgId = this.isMQTT ? messageId : id // mqtt使用 messageId 列表使用 id
      let isRefreshList = false // 是否需要刷新消息列表
      window.location.href = '/user-center/buy-draft?tab=9'
      // 若为新消息执行已读功能
      if (this.isNew) {
        await this.handleUpdateRead(msgId)
        isRefreshList = true
      }

      this.closeNotification(this.notificationId) // 点击处理后，关闭该消息
      isRefreshList && this.$event.emit(NOTIFICATION_LIST) // 刷新消息提醒列表、未读数量
    },

    // 消息已读处理
    async handleUpdateRead(id) {
      try {
        await (notificationApi.putNotificationCount(id) || {}) // 已读消息
      } catch (error) {
        // console.log('消息已读error :>> ', error)
      }
    },
  }
}
</script>
