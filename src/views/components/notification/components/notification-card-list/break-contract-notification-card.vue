<!-- 交易违约提醒、违约判罚提醒组件 -->
<style lang="scss" scoped>
@import "./common.scss";

.icon-new {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 9;
  font-size: 49px;
}

.tips {
  letter-spacing: -.1px;
}

.text-link {
  margin: 0;
  color: $color-warning;

  &::after {
    border-color: $color-warning;
  }

  &:hover {
    color: rgba($color: $color-warning, $alpha: 75%);

    &::after {
      border-color: rgba($color: $color-warning, $alpha: 75%);
    }
  }
}

.item-label {
  display: inline-block;

  @include font(14px, $color-text-regular);
}

.item-value {
  display: inline-block;

  @include font(14px,  $color-text-primary);
}

.card-header-new {
  @include font(12px,$color-FFFFFF,500);
  @include flex-cc;

  margin-left: 6px;
  border-radius: 8px 10px 10px 0;
  width: 44px;
  height: 19px;
  background-color: $color-warning;
}

.btn-small {
  width: 76px !important;
  height: 32;
}

.btn-big {
  width: 104px !important;
  height: 32;
}

.notificationCard {
  width: 360px;
}

::v-deep .notification-card {
  padding: 8px 16px;
  width: 360px;

  .card-footer {
    margin-top: 8px;
    border-top: 0;
    padding-top: 0;
  }
}

.notification-card .card-header .card-header-title.label::after {
  width: 2px;
}

.notification-card .card-header .card-header-tip {
  @include font(14px,$color-warning);
}
</style>

<template>
  <div :class="isMQTT ? 'ismqtt' : 'nomqtt'">
    <!-- mqtt推送 -->
    <div v-if="isMQTT" class="messageTile mt8">
      <!-- 顶部提示语 -->
      <!-- 交易违约 -->
      <div v-if="isTrading">
        如有异议，订单结束24小时内可在
        “<a class="text-link" target="_blank" href="/user-center/my-breach">违约订单</a>”
        进行申诉。
      </div>
      <!-- 违约判罚 -->
      <template v-if="isJudge">
        {{ sdmName }}将在同一笔订单所有违约结束后统一划转,有疑问可查看
        <a
          class="text-link"
          target="_blank"
          :href="PLATFORM_DEFAULT_RULESNEW_URL"
          rel="noopener noreferrer"
        >《平台订单违约规则》</a>
      </template>
    </div>

    <!-- 消息列表的平台公告 -->
    <NotificationCard
      v-if="myData"
      :data="myData"
      :is-new="isNew"
      :is-mqtt="isMQTT"
    >
      <template v-if="!isMQTT" slot="header">
        <h2 class="card-header-title label">
          <text-tooltip :content="myData.title" show-when-overflow />
        </h2>
        <div class="card-header-tip">
          <text-tooltip :content="myData.tips" show-when-overflow />
        </div>
      </template>

      <ul
        slot="main"
        class="desc-list"
      >
        <li
          v-for="row in myData.rows"
          :key="row.id"
        >
          <div
            v-for="item in row.list"
            :key="item.id"
            class="ellipsis-mb-4"
          >
            <div class="item-label">
              {{ item.label }}：
            </div>
            <div class="item-value">
              <!-- {{ item.value }} -->
              <text-tooltip :content="item.value" show-when-overflow />
            </div>
          </div>
        </li>
      </ul>
      <!-- 卡片底部操作区 -->
      <template slot="footer">
        <!-- 交易违约 -->
        <div class="footer">
          <template v-if="isTrading">
            <el-button
              width="96"
              height="40"
              border
              type="primary"
              class="btn-big"
              @click="onClickHandle({isJumpBreakContract: true})"
            >
              前往查看
            </el-button>
            <el-button
              width="96"
              height="40"
              type="primary"
              class="btn-big"
              @click="onClickHandle"
            >
              稍后处理
            </el-button>
          </template>
          <!-- 违约判罚 -->
          <template v-if="isJudge">
            <el-button
              width="96"
              height="40"
              border
              type="primary"
              class="btn-big"
              @click="onClickHandle"
            >
              稍后查看
            </el-button>
            <el-button
              width="96"
              height="40"
              type="primary"
              class="btn-big"
              @click="onClickHandle({isJumpBreakContract: true})"
            >
              立即查看
            </el-button>
          </template>
        </div>
      </template>
    </NotificationCard>
  </div>
</template>

<script>
import NotificationCard from '../notification-card/notification-card.vue'
import { dataType } from '@/common/js/util' // 获取数据类型
import { notificationQueue } from '@/views/components/notification/js/notification' // 消息实例队列
import notificationApi from '@/apis/notification' // 消息中心接口
import {
  NOTIFICATION_LIST, // 刷新消息中心列表事件
} from '@/event/modules/site'
import { SDM_UNIT } from '@/constant'
import {
  MSG_TYPE, // 消息提醒类型
  BREAK_CONTRACT_RANK, // 违约等级
} from '@/constants/notification'
import { PLATFORM_DEFAULT_RULESNEW_URL } from '@/constants/oss-files-url' // 平台订单违约规则url
import { BREAK_CONTRACT_ROW } from '@/constant-special'
// import { router } from '@/router'
export default {
  name: 'break-contract-notification-card',

  components: {
    NotificationCard, // 消息组件卡片
  },

  props: {
    // vue.extend传入来的data数据（mqtt传入使用的数据）
    data: {
      type: Object,
      default: () => ({})
    },
    // 该消息组件id
    notificationId: {
      type: String,
      default: ''
    },
    // 消息列表传入使用的数据
    cardData: {
      type: Object,
      default: () => ({})
    },
    // 该交易消息卡片组件用途 1-MQTT推送 2-消息列表
    type: {
      type: Number,
      default: 1
    },
  },

  data() {
    return {
      PLATFORM_DEFAULT_RULESNEW_URL, // 平台订单违约规则url
      BREAK_CONTRACT_ROW, // 消息内容描述列表
      myData: null, // 保存vue.extend传入来的data数据
    }
  },

  computed: {
    // 该消息卡片组件是否用于mqtt
    isMQTT() {
      return this.type === 1 // 1-MQTT推送 2-消息列表
    },

    // 该消息卡片组件是否用于消息列表
    isNotificationList() {
      return this.type === 2 // 1-MQTT推送 2-消息列表
    },

    // 是否未读
    isNew() {
      const {
        isRead = 0, // 是否已读 默认0-未读 1-已读
      } = (this.myData || {})
      return isRead === 0
    },

    // 该消息是否是交易违约
    isTrading() {
      const {
        msgType, //  1-订单状态变更消息 2-意向订单消息 3-交易违约 4-违约判罚
      } = (this.myData || {})
      return msgType === MSG_TYPE.BROKE_CONTRACT_RECORD_NOTICE.id
    },

    // 该消息是否是违约判罚
    isJudge() {
      const {
        msgType, //  1-订单状态变更消息 2-意向订单消息 3-交易违约 4-违约判罚
      } = (this.myData || {})
      return msgType === MSG_TYPE.BROKE_CONTRACT_RECORD_PUNISH_NOTICE.id
    },
  },

  watch: {
    // 监听该数据,若传入该数据表明该组件用在列表或其他地方上,而不是消息推送
    cardData: {
      handler(val) {
        if (val && this.isNotificationList) {
          this.myData = this.initData(val)
        }
      },
      immediate: true,
      deep: true
    },
    // 监听vue.extend传入来的data数据，再保存在自身数据myData上使用，实现数据响应式
    data: {
      handler(val) {
        if (val && this.isMQTT) {
          this.myData = val
        }
      },
      immediate: true,
      deep: true
    },
  },

  methods: {

    // 数据初始化
    initData(data) {
      if (!data) return

      // 传入的不是obj数据,无须处理数据，若是字符串消息返回,不是则跳出
      const dataTypeRes = dataType(data) // 数据类型
      if (data && dataTypeRes !== 'object') {
        if (dataTypeRes !== 'string') return // 不是消息字符串跳出
        return data
      }

      const res = { ...data }

      res.title = this.isTrading ? MSG_TYPE.BROKE_CONTRACT_RECORD_NOTICE.cardTitle : MSG_TYPE.BROKE_CONTRACT_RECORD_PUNISH_NOTICE.cardTitle // 消息卡片头部标题
      let rows = BREAK_CONTRACT_ROW// 消息内容描述列表
      res.tips = data?.msg || '' // 消息头部提示

      // 消息描述列表
      const newRows = rows.map(v => {
        v.list = v.list.map(item => {
          if (data[item.key]) {
            item.value = data[item.key]
          }

          // 违约等级
          if (item.key === 'brokeLevel') {
            item.value = Object.values(BREAK_CONTRACT_RANK).filter(k => k.id === data[item.key])[0]?.label
          }

          // 扣除米
          if (item.key === 'payMargin') {
            item.value = `${data[item.key]} ${SDM_UNIT}`
          }

          return item
        })
        return v
      })

      res.rows = JSON.parse(JSON.stringify(newRows))

      return res
    },

    // 关闭消息
    closeNotification(id) {
      notificationQueue.closeNotification(id)
    },

    // 处理
    async onClickHandle(obj = {}) {
      if (!this.myData) return
      const {
        id = 0, // 消息ID(目前用于已读)
        messageId = 0, // 消息ID(目前用于已读) 该字段用于mqtt类型
      } = this.myData
      const msgId = this.isMQTT ? messageId : id // mqtt使用 messageId 列表使用 id
      let isRefreshList = false // 是否需要刷新消息列表

      const {
        isJumpBreakContract, // 是否跳转到我的违约页面
      } = obj

      if (isJumpBreakContract) {
      // 跳转到违约记录
        this.handleJumpBreakContract(msgId)
      }

      // 若为新消息执行已读功能
      if (this.isNew) {
        await this.handleUpdateRead(msgId)
        isRefreshList = true
      }

      this.closeNotification(this.notificationId) // 点击处理后，关闭该消息
      isRefreshList && this.$event.emit(NOTIFICATION_LIST) // 刷新消息提醒列表、未读数量
    },

    // 消息已读处理
    async handleUpdateRead(id) {
      try {
        await (notificationApi.putNotificationCount(id) || {}) // 已读消息
      } catch (error) {
        // console.log('消息已读error :>> ', error)
      }
    },

    // 跳转到违约记录
    handleJumpBreakContract() {
      // this.$emit('handle-close')
      window.location.href = '/user-center/my-breach'
      // router.push('/user-center/my-breach')
      // window.open('/user-center/my-breach') // 新开处理页面
    },
  }
}
</script>
