<!-- 审核结果通知组件 -->
<style lang="scss" scoped>
.icon-new {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 9;
  font-size: 49px;
}
</style>

<template>
  <div>
    <NotificationCard v-if="myData" :data="myData">
      <template slot="header">
        <h2 class="card-header-title label">
          <text-tooltip :content="myData.title" show-when-overflow />
        </h2>
        <div class="card-header-tip">
          <text-tooltip :content="myData.tips" show-when-overflow />
        </div>
      </template>
      <div slot="header" />
      <RichText
        v-if="myData && myData.content"
        slot="main"
        height="112px"
        :content="myData.content"
      />
      <!-- 卡片底部操作区 -->
      <template slot="footer">
        <!--
          <el-button
          v-if="isISSUE"
          width="76"
          height="40"
          type="primary"
          @click="onClickHandle"
          >
          去发布
          </el-button>
        -->
        <el-button
          v-if="!isISSUE"
          width="76"
          height="40"
          type="primary"
          @click="onClickHandle"
        >
          去申请
        </el-button>

        <icon v-if="isNew" type="chengjie-new2" class="icon icon-new" />
      </template>
    </NotificationCard>
  </div>
</template>

<script>
import NotificationCard from '../notification-card/notification-card.vue'
import { dataType } from '@/common/js/util' // 获取数据类型
import { notificationQueue } from '@/views/components/notification/js/notification' // 消息实例队列
import notificationApi from '@/apis/notification' // 消息中心接口
// import store from '@/store/index'
import {
  NOTIFICATION_LIST, // 刷新消息中心列表事件
} from '@/event/modules/site'
import RichText from '@/views/components/rich-text/rich-text-viewer.vue'
import {
  MSG_TYPE, // 消息提醒类型
} from '@/constants/notification'

export default {
  name: 'audit-notification-card',

  components: {
    NotificationCard, // 消息组件卡片
    RichText, // 富文本编辑器
  },

  props: {
    // vue.extend传入来的data数据（mqtt传入使用的数据）
    data: {
      type: Object,
      default: () => ({})
    },
    // 该消息组件id
    notificationId: {
      type: String,
      default: ''
    },
    // 消息列表传入使用的数据
    cardData: {
      type: Object,
      default: () => ({})
    },
    // 该交易消息卡片组件用途 1-MQTT推送 2-消息列表
    type: {
      type: Number,
      default: 1
    },
  },

  data() {
    return {
      myData: null, // 保存vue.extend传入来的data数据
      MSG_TYPE, // 消息提醒类型
    }
  },

  computed: {
    // 该消息卡片组件是否用于mqtt
    isMQTT() {
      return this.type === 1 // 1-MQTT推送 2-消息列表
    },

    // 该消息卡片组件是否用于消息列表
    isNotificationList() {
      return this.type === 2 // 1-MQTT推送 2-消息列表
    },

    // 是否未读
    isNew() {
      const {
        isRead = 0, // 是否已读 默认0-未读 1-已读
      } = (this.myData || {})
      return isRead === 0
    },

    // 审核通过
    isISSUE() {
      return this.myData.auditStatus === 1
    }
  },

  watch: {
    // 监听该数据,若传入该数据表明该组件用在列表或其他地方上,而不是消息推送
    cardData: {
      handler(val) {
        if (val && this.isNotificationList) {
          this.myData = this.initData(val)
        }
      },
      immediate: true,
      deep: true
    },
    // 监听vue.extend传入来的data数据，再保存在自身数据myData上使用，实现数据响应式
    data: {
      handler(val) {
        if (val && this.isMQTT) {
          this.myData = val
        }
      },
      immediate: true,
      deep: true
    },
  },

  methods: {

    // 数据初始化
    initData(data) {
      // 交易状态transaction  催单reminders 目前返回数据一样
      if (!data) return

      // 传入的不是obj数据,无须处理数据，若是字符串消息返回,不是则跳出
      const dataTypeRes = dataType(data) // 数据类型
      if (data && dataTypeRes !== 'object') {
        if (dataTypeRes !== 'string') return // 不是消息字符串跳出
        return data
      }

      const res = { ...data }
      res.title = MSG_TYPE.COMMERCIAL_AUDIT_NOTICE.cardTitle // 消息卡片头部标题
      let contentPass = `<p style="font-size: 16px;line-height:35px">您提交的商票承兑人白名单申请审核已通过，可发布承兑人【${res.acceptorName}】的票据了</p>`
      let contentUnPass = '<p style="font-size: 16px;line-height:35px">您提交的商票承兑人白名单申请审核未通过，仍需发布请核对信息后重新申请或联系客户经理</p>'
      res.content = res.auditStatus === 1 ? contentPass : contentUnPass

      return res
    },

    // 关闭消息
    closeNotification(id) {
      notificationQueue.closeNotification(id)
    },
    // 处理
    async onClickHandle() {
      if (!this.myData) return
      const {
        id = 0, // 消息ID(目前用于已读)
        messageId = 0, // 消息ID(目前用于已读) 该字段用于mqtt类型
      } = this.myData
      const msgId = this.isMQTT ? messageId : id // mqtt使用 messageId 列表使用 id
      let isRefreshList = false // 是否需要刷新消息列表

      // 若为新消息执行已读功能
      if (this.isNew) {
        await this.handleUpdateRead(msgId)
        isRefreshList = true
      }
      switch (this.myData.auditStatus) {
        case 1: // 去发布
          this.handleReIssue()
          break
        case 2: // 去申请
          this.handleApply()
          break
        default:
          break
      }
      this.closeNotification(this.notificationId) // 点击处理后，关闭该消息
      isRefreshList && this.$event.emit(NOTIFICATION_LIST) // 刷新消息提醒列表、未读数量
    },

    // 消息已读处理
    async handleUpdateRead(id) {
      try {
        await (notificationApi.putNotificationCount(id) || {}) // 已读消息
      } catch (error) {
        // console.log('消息已读error :>> ', error)
      }
    },

    // 发布
    handleReIssue() {
      // 新开处理页面
      window.open('/user-center/issue-draft')
    },
    // 申请
    handleApply() {
      this.$alert('请联系您的客户经理申请开通!', '提示')
      // 新开处理页面
      // const temp = window.open(`${store.state.spbUrl}`)
      // store.dispatch('corp-info/getCorpInfoSync', '/issue/white').then(res => {
      //   temp.location.href = res
      // })
    }
  }
}
</script>
