<!-- 账户通知组件 -->
<style lang="scss" scoped>
@import "./common.scss";

.invalid-warn-msg {
  max-width: 284px;

  @include ellipsis(2);
}

.detail {
  .item-value { vertical-align: top; }
}

.ismqtt .desc-list li div {
  height: unset !important;
}
</style>

<style>
.invalid-warn-content {
  width: 200px;
}
</style>

<template>
  <div v-if="myData" class="ismqtt">
    <div class="messageTile">
      {{ cardData.tips }}
    </div>
    <NotificationCard :data="cardData" :is-mqtt="true">
      <ul slot="main" class="desc-list">
        <li>
          <div class="mb-4">
            <div class="item-label">
              企业名称：
            </div>
            <div class="item-value bold">
              {{ myData.corpName }}
            </div>
          </div>
        </li>
        <li>
          <div>
            <div class="item-label">
              通知时间：
            </div>
            <div class="item-value">
              {{ formatTime(myData.sendTime) }}
            </div>
          </div>
        </li>
        <li>
          <div class="detail">
            <div class="item-label">
              消息详情：
            </div>
            <div class="item-value bold ">
              <el-tooltip
                placement="top"
                :hide-after="10000"
              >
                <div slot="content" class="invalid-warn-content">{{ message }}</div>
                <div class="invalid-warn-msg">
                  {{ message }}
                </div>
              </el-tooltip>
            </div>
          </div>
        </li>
      </ul>

      <div slot="footer" class="footer">
        <el-button
          v-if="myData.invalidWarnType === 1 || myData.invalidWarnType === 2 || myData.invalidWarnType === 5 || myData.corpStatus === 3"
          v-waiting="'get::loading::/corpOpenInfo/reAuthCheck'"
          height="40"
          type="primary"
          @click="recertificationTip"
        >
          重新实名
        </el-button>
        <el-button
          v-if="myData.invalidWarnType === 2"
          v-waiting="`get::loading::/corp/userCenterUrl?type=recharge`"
          height="40"
          type="primary"
          @click="enterIntoAccount('recharge')"
        >
          充值
        </el-button>
        <el-button
          v-if="myData.invalidWarnType === 2"
          v-waiting="`get::loading::/corp/userCenterUrl?type=withdraw`"
          height="40"
          type="primary"
          @click="enterIntoAccount('withdraw')"
        >
          提现
        </el-button>
        <el-button
          v-if="myData.invalidWarnType === 3 || myData.invalidWarnType === 4"
          height="40"
          type="primary"
          @click="onClickService"
        >
          联系客服
        </el-button>
      </div>
    </NotificationCard>
  </div>
</template>

<script>
import NotificationCard from '../notification-card/notification-card.vue'
import { SITE_OPEN_ACCOUNT, SITE_OPEN_CONTACT_SERVICE } from '@/event/modules/site' // 常量
import userApi from '@/apis/user' // 用户接口
import { formatTime } from '@/common/js/date'
import { REAL_NAME_AUTH_TYPE } from '@/constant' // 常量
import { notificationQueue } from '@/views/components/notification/js/notification' // 消息实例队列
import { APPLICATION_STATUS } from '@/constants/open-account'
import { openWindow } from '@/common/js/util'
import { jumpJdPay } from '@/utils/jdpay.js'

export default {
  name: 'account-notification-card',
  components: {
    NotificationCard
  },
  props: {
    // vue.extend传入来的data数据
    data: {
      type: Object,
      default: () => ({})
    },
    // 该消息组件id
    notificationId: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      myData: null, // 保存vue.extend传入来的data数据
      cardData: {},
    }
  },
  computed: {
    // 企业信息
    // corpInfo() {
    //   return this.$store?.state?.user?.corpInfo
    // },
    // 消息内容
    message() {
      let msg = ''
      if (this.myData.invalidWarnMsg) {
        msg = this.myData.invalidWarnMsg
      }
      // //企业状态：0-未实名，1-实名中，2-已实名，3-实名失效
      // eslint-disable-next-line no-magic-numbers
      if (this.myData.corpStatus === 3 && this.myData.failReason) {
        // failReason 企业实名失效原因类型，FREEZE-已冻结,EXPIRED-已过期，CREDIT_IMMEDIATE-因客户企业违法/工商变更而失效
        switch (this.myData.failReason) {
          case 'FREEZE':
            msg = '账户实名失效：连续180天未交易，账户已被冻结，无法发布票据和接单'
            break
          case 'EXPIRED':
            msg = '账户实名失效：证件已过期，无法发布票据和接单'
            break
          case 'CREDIT_IMMEDIATE':
            msg = '账户实名失效：因客户企业违法/工商变更而失效，无法发布票据和接单'
            break
          default:
            msg = this.myData.failReason
            break
        }
      }
      return msg
    }
  },
  watch: {
    // 监听vue.extend传入来的data数据，再保存在自身数据myData上使用，实现数据响应式
    data: {
      handler(val) {
        if (val) {
          this.myData = val
        }
      },
      immediate: true,
      deep: true
    },
  },

  created() {
    this.cardData.title = '账户提醒'
    this.cardData.tips = '账户存在异常，请及时处理'
  },
  methods: {
    formatTime,
    // 打开重新实名 提示
    recertificationTip() {
      this.$confirm(`
        <div>如已解决异常问题，请点击【已解决】进行重新实名。</div>
        <div>如未解决就去重新实名，系统将会再次发送异常预警。</div>
        `, '重新实名确认', {
        dangerouslyUseHTMLString: true,
        type: 'warning',
        showClose: false,
        iconPosition: 'title',
        confirmButtonText: '已解决',
      })
        .then(() => {
          this.recertification()
        })
    },
    // 打开重新实名
    async recertification() {
      // console.log('corpInfo', this.corpInfo, this, this.$store)
      // tips: 产品后端说重新实名不影响接单，就先不用判断了
      // 先判断是否可以重新实名
      // const data = await userApi.getRreAuthCheck()
      // if (data === true) {
      this.corpInfo = await userApi.getCorpInfo()
      // 企业最新开户记录申请单状态：0-未提交，1-提交失败，2-待京东审核，3-京东审核失败，4-待京东打款，5-京东打款成功，6-京东打款失败，7-打款验证失败，8-已通过
      if (this.corpInfo.newestCorpOpenInfoApplyStatus >= APPLICATION_STATUS.HAVE_BEEN_THROUGH.id) {
        this.$event.emit(SITE_OPEN_ACCOUNT, {
          realNameAuthType: REAL_NAME_AUTH_TYPE.RECEIVE,
          form: 'user-center'
        })
      } else {
        this.$event.emit(SITE_OPEN_ACCOUNT, {
          realNameAuthType: REAL_NAME_AUTH_TYPE.RECEIVE,
          startOver: true,
          form: 'user-center'
        })
      }
      // } else {
      //   this.$confirm('您当前有交易中订单，请先完成交易。', '提示', {
      //     showCancelButton: false,
      //     type: 'warning',
      //     confirmButtonText: '我知道了'
      //   }).then(() => {
      //   })
      // }
      this.close()
    },

    // 充值 或 提现
    enterIntoAccount(type) {
      if (this.$store.state.user.isSdkLink) {
        // 通过SDK跳转
        let querys = {
          accountType: 'YILLION'
        }
        jumpJdPay({ querys })
        return
      }
      // 新窗口打开外链接
      openWindow(async() => {
        const res = await userApi.getPaymentAccountUserCenterUrl(type)
        return res
      })
      this.close()
    },

    // 联系客服
    onClickService() {
      this.$event.emit(SITE_OPEN_CONTACT_SERVICE)
      this.close()
    },

    // 关闭消息
    close() {
      notificationQueue.closeNotification(this.notificationId)
    },
  }
}
</script>
