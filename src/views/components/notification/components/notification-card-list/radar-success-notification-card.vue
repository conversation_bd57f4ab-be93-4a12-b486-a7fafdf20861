<!-- 自动自动接单成功组件 -->
<style lang="scss" scoped>
@import "./common.scss";

.icon-new {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 9;
  font-size: 49px;
}

::v-deep {
  .item-primary {
    .item-value {
      color: $--color-primary;
    }
  }

  // 顶部提示语
  .el-alert {
    margin-bottom: 12px;
    padding: 9px 13px;
    max-width: 397px;

    &--info {
      &.is-light {
        background-color: $color-E6F3F3;
      }
    }

    &__title {
      .title {
        font-size: 16px;
        font-weight: 400;
        text-align: left;
        color: $color-text-primary;
        font-style: normal;
        line-height: 22px;
        letter-spacing: 0;
      }

      .icon {
        margin-right: 2px;
        font-size: 17.5px;
        color: $font-color;
      }
    }

    &__content {
      padding: 0;
    }

    .title {
      display: flex;

      .icon {
        margin-top: 2px;
        margin-right: 11px;
        width: 18px;
        height: 18px;
      }

      .text {
        flex: 1;
        min-width: 0;
      }
    }
  }

  .link {
    @include example-underline;

    margin: 0 8px;
  }
}
</style>

<template>
  <div class="ismqtt">
    <div class="messageTile">
      <text-tooltip :content="myData.tips" show-when-overflow />
    </div>
    <NotificationCard v-if="myData" :data="myData" :is-mqtt="isMQTT">
      <ul
        slot="main"
        class="desc-list"
      >
        <li
          v-for="row in myData.rows"
          :key="row.id"
        >
          <div
            v-for="item in row.list"
            :key="item.id"
            class="mb-4"
          >
            <div class="item-label">
              {{ item.label }}：
            </div>
            <div class="item-value">
              {{ item.value }}
              <!-- <text-tooltip :content="item.value" show-when-overflow /> -->
            </div>
          </div>
        </li>
      </ul>
      <!-- 卡片底部操作区 -->
      <div slot="footer" class="footer">
        <el-button
          width="96"
          height="40"
          type="primary"
          @click="onClickHandle"
        >
          处理
        </el-button>
      </div>
    </NotificationCard>
  </div>
</template>

<script>
import NotificationCard from '../notification-card/notification-card.vue'
import { dataType, isNull } from '@/common/js/util' // 获取数据类型
import { notificationQueue } from '@/views/components/notification/js/notification' // 消息实例队列
import { formatTime } from '@/common/js/date' // 时间格式化
import { yuan2wan } from '@/common/js/number' // 金额单位转换

// 消息内容描述列表
const BREAK_CONTRACT_ROW = [
  {
    id: 1,
    isEllipsisText: true,
    ellipsisTextNum: 2,
    list: [
      {
        id: 1,
        label: '接单时间',
        key: 'sendTime',
      },

    ],
  },
  {
    id: 2,
    isEllipsisText: true,
    ellipsisTextNum: 2,
    list: [
      {
        id: 1,
        label: '票号后六位',
        key: 'lastSixDraftNo',
        value: null,
        isBold: true
      },
      {
        id: 2,
        label: '票面金额',
        key: 'draftAmount',
        value: null,
        isBold: true,
        itemClass: 'item-primary'
      },
    ],
  }
]

export default {
  name: 'break-contract-notification-card',

  components: {
    NotificationCard, // 消息组件卡片
  },

  props: {
    // vue.extend传入来的data数据（mqtt传入使用的数据）
    data: {
      type: Object,
      default: () => ({})
    },
    // 该消息组件id
    notificationId: {
      type: String,
      default: ''
    },
    // 消息列表传入使用的数据
    cardData: {
      type: Object,
      default: () => ({})
    },
    // 该交易消息卡片组件用途 1-MQTT推送 2-消息列表
    type: {
      type: Number,
      default: 1
    },
  },

  data() {
    return {
      myData: null, // 保存vue.extend传入来的data数据
    }
  },

  computed: {
    // 该消息卡片组件是否用于mqtt
    isMQTT() {
      return this.type === 1 // 1-MQTT推送 2-消息列表
    },

    // 该消息卡片组件是否用于消息列表
    isNotificationList() {
      return this.type === 2 // 1-MQTT推送 2-消息列表
    },
  },

  watch: {
    // 监听该数据,若传入该数据表明该组件用在列表或其他地方上,而不是消息推送
    cardData: {
      handler(val) {
        if (val && this.isNotificationList) {
          this.myData = this.initData(val)
        }
      },
      immediate: true,
      deep: true
    },
    // 监听vue.extend传入来的data数据，再保存在自身数据myData上使用，实现数据响应式
    data: {
      handler(val) {
        if (val && this.isMQTT) {
          this.myData = val
        }
      },
      immediate: true,
      deep: true
    },
  },

  methods: {

    // 数据初始化
    initData(data) {
      if (!data) return

      // 传入的不是obj数据,无须处理数据，若是字符串消息返回,不是则跳出
      const dataTypeRes = dataType(data) // 数据类型
      if (data && dataTypeRes !== 'object') {
        if (dataTypeRes !== 'string') return // 不是消息字符串跳出
        return data
      }

      const res = { ...data }

      res.title = '系统接单' // 消息卡片头部标题
      let rows = BREAK_CONTRACT_ROW// 消息内容描述列表
      res.tips = data?.msg || '' // 消息头部提示

      // 消息描述列表
      const newRows = rows.map(v => {
        v.list = v.list.map(item => {
          if (data[item.key]) {
            item.value = data[item.key]
          }

          // 接单时间
          if (item.key === 'sendTime') {
            item.value = formatTime(data[item.key], 'YYYY-MM-DD hh:mm:ss')
          }

          // 意向价格-票据金额
          if (item.key === 'draftAmount') {
            item.value = `${isNull(data.draftAmount) ? '--' : yuan2wan(data.draftAmount)}万`
          }

          return item
        })
        return v
      })

      res.rows = JSON.parse(JSON.stringify(newRows))

      return res
    },

    // 关闭消息
    closeNotification(id) {
      notificationQueue.closeNotification(id)
    },

    // 处理
    onClickHandle() {
      // 跳转到我是资方
      window.open('/user-center/buy-draft') // 新开处理页面
    },

  }
}
</script>
