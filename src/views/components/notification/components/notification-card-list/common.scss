.messageTile {
  margin-bottom: 8px;
  width: 372px;
  color: $color-warning;
}

.nomqtt {
  ::v-deep .card-body {
    margin-top: 10px;
    padding: 8px;
    background-color: $color-F8F8F8;
  }
}

//  解决tooltip  样式 问题
.desc-list li .mb-4 {
  margin-bottom: 4px;
  line-height: 20px;
}

.desc-list li .ellipsis-mb-4 {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  white-space: nowrap;
  line-height: 20px;
}

.ismqtt {
  .item-label {
    display: contents;
    line-height: 20px !important;

    @include font(14px, $color-text-regular);
  }

  .item-value {
    display: contents;
    line-height: 20px !important;

    @include font(14px,  $color-text-primary);
  }

  ::v-deep {
    // 列表样式
    // .desc-list li div {
    //   margin-bottom: 4px;

    //   // height: 20px;
    // }

    .desc-list .ellipsis-text {
      line-height: 20px;
    }

    .item-label {
      line-height: unset;
    }

    .item-value {
      line-height: unset;
      font-weight: 600;
    }

    .notification-card {
      margin-top: 0;
      border: 1px solid #D9D9D9;
      padding: 8px 8px 4px;
      width: 372px !important;
      box-shadow: 0 0 8px 0 rgb(0 0 0 / 10%);
    }

    .card-body {
      margin-top: 0 !important;
      padding: 0 !important;
      background-color: $color-FFFFFF !important;
    }
  }

  // 消息通知里面 按钮 32px
  .card-footer .el-button {
    height: 32px !important;
    font-size: 14px !important;
  }
}

.footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
}
