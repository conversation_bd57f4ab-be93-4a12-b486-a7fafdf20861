<!-- 消费券升级通知组件 -->
<style lang="scss" scoped>
@import "./common.scss";

::v-deep {
  .notification-card .card-header {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .notification-card .card-body {
    margin-top: 0;
  }
}

.content {
  display: flex;
  align-items: center;
  border: 1px solid #FFCEC3;
  padding: 8px;
  font-size: 14px;
  font-weight: 500;
  color: $color-text-primary;
  background: #FFEEEA;
  box-shadow: 0 0 8px 0 rgb(255 80 41 / 10%);
}

.btn {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
}

.official {
  line-height: 20px;
  margin-left: 12px;
}

.keywords {
  font-weight: 600;
  color: $color-warning;
}
</style>

<template>
  <div>
    <div class="messageTile ">
      已自动为您开启下一等级任务，请继续加油！
    </div>
    <div slot="header" class="content">
      <img src="https://oss.chengjie.red/web/imgs/public/celebration-activities-new.png" alt="">
      <div class="official">
        恭喜您！完成订单送券阶段性任务
        <p>已获得 <span class="keywords">{{ myData.alreadyReward }}</span> 消费券</p>
      </div>
    </div>
    <div class="btn">
      <el-button
        width="104"
        height="32"
        type="primary"
        @click="goDetail"
      >
        查看详情
      </el-button>
    </div>
  </div>
</template>

<script>
import { dataType } from '@/common/js/util' // 获取数据类型
import { notificationQueue } from '@/views/components/notification/js/notification' // 消息实例队列
import {
  MSG_TYPE, // 消息提醒类型
} from '@/constants/notification'

export default {
  name: 'audit-notification-card',

  props: {
    // vue.extend传入来的data数据（mqtt传入使用的数据）
    data: {
      type: Object,
      default: () => ({})
    },
    // 该消息组件id
    notificationId: {
      type: String,
      default: ''
    },
    // 消息列表传入使用的数据
    cardData: {
      type: Object,
      default: () => ({})
    },
    // 该交易消息卡片组件用途 1-MQTT推送 2-消息列表
    type: {
      type: Number,
      default: 1
    },
  },

  data() {
    return {
      myData: null, // 保存vue.extend传入来的data数据
      MSG_TYPE, // 消息提醒类型
    }
  },

  computed: {
    // 该消息卡片组件是否用于mqtt
    isMQTT() {
      return this.type === 1 // 1-MQTT推送 2-消息列表
    },

    // 该消息卡片组件是否用于消息列表
    isNotificationList() {
      return this.type === 2 // 1-MQTT推送 2-消息列表
    },

  },

  watch: {
    // 监听该数据,若传入该数据表明该组件用在列表或其他地方上,而不是消息推送
    cardData: {
      handler(val) {
        if (val && this.isNotificationList) {
          this.myData = this.initData(val)
        }
      },
      immediate: true,
      deep: true
    },
    // 监听vue.extend传入来的data数据，再保存在自身数据myData上使用，实现数据响应式
    data: {
      handler(val) {
        if (val && this.isMQTT) {
          this.myData = val
        }
      },
      immediate: true,
      deep: true
    },
  },

  methods: {
    // 数据初始化
    initData(data) {
      if (!data) return
      // 传入的不是obj数据,无须处理数据，若是字符串消息返回,不是则跳出
      const dataTypeRes = dataType(data) // 数据类型
      if (data && dataTypeRes !== 'object') {
        if (dataTypeRes !== 'string') return // 不是消息字符串跳出
        return data
      }
      const res = { ...data }
      return res
    },

    // 关闭消息
    closeNotification(id) {
      notificationQueue.closeNotification(id)
    },

    goDetail() {
      window.location.href = '/user-center/coupon?tabs=receiveCentre'
      // this.$router.push('/user-center/coupon?tabs=receiveCentre')
    }
  }
}
</script>
