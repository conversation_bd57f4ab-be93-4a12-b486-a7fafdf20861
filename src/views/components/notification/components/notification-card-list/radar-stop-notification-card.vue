<!-- 自动停止接单组件 -->
<style lang="scss" scoped>
@import "./common.scss";

.content {
  width: 100%;
  font-size: 14px;
  color: $color-text-primary;
}
</style>

<template>
  <div v-if="myData" class="ismqtt">
    <NotificationCard :data="cardData" :is-mqtt="true">
      <div slot="main" class="content">
        您的自动找票需求<span class="text-bold">【{{ myData.ruleName }}】</span> <br>
        因 <span class="text-bold text-primary">{{ stopTypeMap[myData.stopType] }}</span>，系统自动停止接单。
      </div>

      <div slot="footer" class="footer">
        <el-button
          height="40"
          type="primary"
          @click="onClickHandle"
        >
          处理
        </el-button>
      </div>
    </NotificationCard>
  </div>
</template>

<script>
import NotificationCard from '../notification-card/notification-card.vue'

export default {
  name: 'account-notification-card',
  components: {
    NotificationCard
  },
  props: {
    // vue.extend传入来的data数据
    data: {
      type: Object,
      default: () => ({})
    },
    // 该消息组件id
    notificationId: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      myData: null, // 保存vue.extend传入来的data数据
      cardData: {},
      // "stopType":1, // 停止类型，1-接单额度满，2-接单截止时间到，3-授信额度已到，4-保证金不足，5-晚上八点自动停止,6-支付渠道的交易权限被禁用
      stopTypeMap: {
        1: '接单额度满',
        2: '接单截止时间到',
        3: '授信额度已到',
        4: '保证金不足',
        5: '晚上八点自动停止',
        6: '支付渠道的交易权限被禁用',
      }
    }
  },

  watch: {
    // 监听vue.extend传入来的data数据，再保存在自身数据myData上使用，实现数据响应式
    data: {
      handler(val) {
        if (val) {
          this.myData = val
        }
      },
      immediate: true,
      deep: true
    },
  },

  created() {
    this.cardData.title = '停止接单'
  },

  methods: {
    onClickHandle() {
      // 跳转到服务大厅
      window.open('/market') // 新开处理页面
    }
  }
}
</script>
