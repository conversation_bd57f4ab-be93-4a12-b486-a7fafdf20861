<!-- 消息通知卡片组件 -->
<style lang="scss" scoped>
$spaceSize: 12px;

.notification-card {
  position: relative;
  display: flex;
  margin-top: 16px;
  border-radius: 2px;
  padding: 12px 16px;
  width: 397px;
  text-align: left;
  background: $color-FFFFFF;
  flex-direction: column;

  &:first-child {
    margin-top: 0;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2px;
    width: 100%;

    .card-header-title {
      position: relative;
      padding-top: 2px;
      max-width: 50%;
      font-size: 16px;
      font-weight: 600;
      text-align: left;
      color: $color-text-primary;
      line-height: 22px;

      &::before {
        height: 16px;
      }

      &.label {
        padding-left: 10px;

        &::after {
          position: absolute;
          top: 50%;
          left: 0;
          width: 4px;
          height: 16px;
          background-color: $--color-primary;
          transform: translateY(-50%);
          content: "";
        }
      }

      .ellipsis-text {
        line-height: inherit;
      }
    }

    .card-header-tip {
      padding-left: 20px;
      min-width: 0;
      font-size: 16px;
      text-align: right;
      color: $--color-font-main;
      flex: 1;
    }
  }

  .card-body {
    display: flex;
    margin-top: 6px;
  }

  .card-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: $spaceSize;
    border-top: 1px solid #D9D9D9;
    padding-top: $spaceSize;
  }
}

.desc-list {
  width: 100%;

  .row {
    display: flex;
    justify-content: space-between;
    width: 100%;

    .item {
      padding: 4px 0;
      min-width: 0;
      flex: 1;

      .bold {
        font-weight: 600;
      }

      // &-column-1{
      // }

      &-column-2 {
        &:first-child {
          border-right: 1px solid $color-D9D9D9;
          padding-right: $spaceSize;
        }

        &:last-child {
          padding-left: $spaceSize;
          box-sizing: border-box;
        }
      }

      &-column-3 {
        &:first-child {
          margin-right: $spaceSize;
          border-right: 1px solid $color-D9D9D9;
          padding-right: $spaceSize;
        }

        &:last-child {
          margin-left: $spaceSize;
          border-left: 1px solid $color-D9D9D9;
          padding-left: $spaceSize;
          box-sizing: border-box;
        }
      }

      &-ellipsis-2 {
        .ellipsis-text {
          white-space: initial;

          @include ellipsis(2);
        }
      }

      &-ellipsis-3 {
        .ellipsis-text {
          white-space: initial;

          @include ellipsis(3);
        }
      }
    }
  }

  .ellipsis-text {
    line-height: 24px;
  }
}

.item-label {
  font-size: 14px;
  color: $color-text-secondary;
  line-height: 22px;
}

.item-value {
  display: flex;
  font-size: 16px;
  color: $color-text-primary;
  line-height: 24px;
}

// el组件样式
::v-deep {
  .el-button {
    margin-left: 8px;
    font-size: 16px;
  }
}
</style>

<template>
  <div>
    <div
      v-if="data"
      class="notification-card"
      :class="[data.cardClass ? data.cardClass : '']"
    >
      <template v-if="dataType(data) !== 'object'">
        {{ data }}
      </template>
      <template v-else>
        <!-- 消息通知不显示标题 -->
        <div v-if="!isMqtt" class="card-header">
          <slot name="header">
            <h2 class="card-header-title label">
              <text-tooltip :content="data.title" />
            </h2>
            <div class="card-header-tip">
              <text-tooltip :content="data.tips" />
            </div>
          </slot>
        </div>
        <div class="card-body">
          <slot name="main">
            <ul class="desc-list">
              <li
                v-for="row in data.rows"
                :key="row.id"
                class="row"
              >
                <div
                  v-for="item in row.list"
                  :key="item.id"
                  :class="['item', `item-column-${row.list.length || 0}`, item.itemClass ? item.itemClass : '']"
                >
                  <div v-if="item.label" class="item-label">
                    <template v-if="row.isEllipsisText">
                      <text-tooltip :content="item.label" popper-style="max-width: 370px;" show-when-overflow />
                    </template>
                    <template v-else>
                      {{ item.label }}
                    </template>
                  </div>
                  <div class="item-value" :class="[`${item.isBold ? 'bold' : ''}`, `item-ellipsis-${row.ellipsisTextNum || 1}`]">
                    <template v-if="row.isEllipsisText">
                      <text-tooltip :content="item.value" popper-style="max-width: 370px;" show-when-overflow />
                    </template>
                    <template v-else>
                      {{ item.value }}
                    </template>
                  </div>
                </div>
              </li>
            </ul>
          </slot>
        </div>
      </template>
      <div>
        <slot name="content" />
      </div>
      <div v-if="!isMqtt" class="card-footer">
        <slot name="footer" />
      </div>
    </div>
    <div v-if="isMqtt" class="card-footer">
      <slot name="footer" />
    </div>
  </div>
</template>

<script>
import { dataType } from '@/common/js/util' // 获取数据类型

export default {
  name: 'notification-card',

  components: {
  },

  props: {
    data: {
      type: [Object, String],
      default: () => ({})
    },
    isMqtt: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
    }
  },

  methods: {
    dataType,
  },
}
</script>
