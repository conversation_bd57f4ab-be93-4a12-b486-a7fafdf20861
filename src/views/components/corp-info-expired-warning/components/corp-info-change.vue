// 工商变更提醒弹窗
<template>
  <CommonDialog
    dialog-title="工商变更提醒"
    :visible="corpInfo.failReason === 'ENTER_INFO_CHANGE_WARN'"
    warning-text="您好，当前账户的工商信息已发生变更，已暂停您的支付业务，请立即上传证件更新实名认证信息，如有疑问可扫码联系客服经理！"
  >
    <template #operation>
      <el-button type="primary" @click="recertification">立即上传证件</el-button>
    </template>
  </CommonDialog>
</template>

<script>
import CommonDialog from './common-dialog.vue'
import handleMixin from './handleMixin'

export default {
  name: 'corp-info-change',
  components: {
    CommonDialog,
  },
  mixins: [handleMixin],
  data() {
    return {
    }
  },
  computed: {
    corpInfo() {
      return this.$store.state?.user?.corpInfo
    }
  },
}
</script>
