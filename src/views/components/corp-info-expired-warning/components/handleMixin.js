import { REAL_NAME_AUTH_TYPE } from '@/constant'
import { SITE_OPEN_ACCOUNT } from '@/event/modules/site' // 常量
import { CORP_INFO_EXPIRED_WARN_VISIBLE } from '@/constant-storage'
import { formatTime } from '@/common/js/date'
import Storage from '@/common/js/storage' // 本地缓存对象

export default {
  data() {
    const today = new Date(formatTime(Date.now(), 'YYYY-MM-DD'))
    return {
      visible: !Storage.get(CORP_INFO_EXPIRED_WARN_VISIBLE) || new Date(Storage.get(CORP_INFO_EXPIRED_WARN_VISIBLE)) < today // 弹窗是否显示
    }
  },
  methods: {
    // 关闭弹窗，直至下次登录再展示
    handleClose() {
      this.visible = false
      Storage.set(CORP_INFO_EXPIRED_WARN_VISIBLE, formatTime(Date.now(), 'YYYY-MM-DD'))
    },

    // 重新上传证件
    recertification() {
      this.$event.emit(SITE_OPEN_ACCOUNT, {
        realNameAuthType: REAL_NAME_AUTH_TYPE.FAILURE,
        form: 'user-center'
      })
    },
  },
}
