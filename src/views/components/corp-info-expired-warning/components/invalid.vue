<template>
  <CommonDialog
    :visible.sync="visible"
    dialog-title="实名失效提醒"
    warning-text="您好，当前账户的实名认证状态已失效，已暂停您的支付业务，请立即上传证件更新实名认证信息，如有疑问可扫码联系客服经理！"
  >
    <template #operation>
      <el-button type="primary" @click="recertification">立即上传证件</el-button>
    </template>
  </CommonDialog>
</template>

<script>
import CommonDialog from './common-dialog.vue'
import { REAL_NAME_AUTH_TYPE } from '@/constant'
import { REAL_NAME_CERTIFICATION } from '@/event/modules/site' // 常量

export default {
  name: 'invalid',
  components: {
    CommonDialog,
  },
  data() {
    return {
      visible: false
    }
  },
  methods: {
    open() {
      this.visible = true
    },
    // 重新上传证件
    recertification() {
      this.visible = false
      this.$event.emit(REAL_NAME_CERTIFICATION, {
        realNameAuthType: REAL_NAME_AUTH_TYPE.FAILURE,
        form: 'user-center'
      })
    },
  },
}
</script>
