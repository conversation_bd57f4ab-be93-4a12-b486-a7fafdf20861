// 经营异常提醒弹窗
<template>
  <CommonDialog
    dialog-title="经营异常提醒"
    :visible="visible && corpInfo.failReason === 'ENTER_UNUSUAL_WARN'"
    warning-text="您好，当前账户存在经营异常，已暂停您的支付业务，请尽快与工商局核实原因并处理，如有疑问可扫码联系客服经理！"
  >
    <template #operation>
      <el-button type="primary" @click="handleClose">我知道了</el-button>
    </template>
  </CommonDialog>
</template>

<script>
import CommonDialog from './common-dialog.vue'
import handleMixin from './handleMixin'

export default {
  name: 'abnormal-operation',
  components: {
    CommonDialog,
  },
  mixins: [handleMixin],
  data() {
    return {
    }
  },
  computed: {
    corpInfo() {
      return this.$store.state?.user?.corpInfo
    }
  }
}
</script>
