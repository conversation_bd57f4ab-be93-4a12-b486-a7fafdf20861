// 企业信息预警弹窗
<style lang="scss" scoped>
.common-dialog-contain {
  margin-bottom: 20px;
  padding: 20px 16px;
  color: $color-text-primary;
  background-color: #FFFFFF;
}

.warning-text {
  font-size: 16px;
  font-weight: 400;
  text-align: center;
  line-height: 24px;
}

.qrcode {
  display: flex;
  justify-content: center;
  margin-top: 24px;

  .qrcode-item {
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    line-height: 24px;

    &:first-child {
      margin-right: 64px;
    }

    img {
      margin-bottom: 8px;
      width: 140px;
      height: 140px;
      object-fit: cover;
      vertical-align: top;
    }
  }
}

.operation {
  margin-top: 24px;
  text-align: center;
}

.ovh {
  overflow: hidden;
}
</style>

<template>
  <el-dialog
    :title="dialogTitle"
    :visible="visible"
    :show-close="closeable"
    :close-on-click-modal="closeable"
    width="600px"
    append-to-body
    @close="$emit('close')"
  >
    <div class="ovh">
      <div class="common-dialog-contain">
        <div class="warning-text">
          <slot>
            {{ warningText }}
          </slot>
        </div>
        <!-- 经纪商不展示二维码 -->
        <div class="qrcode">
          <div class="qrcode-item">
            <img :src="configDefault.customerManagerQr1">
          </div>
          <div class="qrcode-item">
            <img :src="configDefault.customerManagerQr2">
          </div>
        </div>
        <div class="operation">
          <slot name="operation" />
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'common-dialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    dialogTitle: {
      type: String
    },
    closeable: {
      type: Boolean,
      default: false
    },
    // 警示文案
    warningText: {
      type: String,
    }
  },
}
</script>
