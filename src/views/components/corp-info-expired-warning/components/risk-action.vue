// 违法/变更提醒弹窗
<template>
  <CommonDialog
    dialog-title="违法/变更提醒"
    :visible="visible && corpInfo.failReason === 'ENTER_ILLEGALITY_WARN'"
    warning-text="您好，当前账户存在风险行为，已暂停您的支付业务，请尽快与您的客户经理联系确认原因，如有疑问可扫码联系客服经理！"
  >
    <template #operation>
      <el-button type="primary" @click="handleClose">我知道了</el-button>
    </template>
  </CommonDialog>
</template>

<script>
import CommonDialog from './common-dialog.vue'
import handleMixin from './handleMixin'

export default {
  name: 'risk-action',
  components: {
    CommonDialog,
  },
  mixins: [handleMixin],
  data() {
    return {
    }
  },
  computed: {
    corpInfo() {
      return this.$store.state?.user?.corpInfo
    }
  }
}
</script>
