// 账户冻结
<template>
  <CommonDialog
    dialog-title="账户冻结提醒"
    :visible="corpInfo.failReason === 'FREEZE'"
    warning-text="您好，账户实名已失效，连续180天未交易，账户已被冻结，无法发布票据和接单，请立即上传证件更新实名认证信息，如有疑问可扫码联系客服经理！"
  >
    <template #operation>
      <el-button type="primary" @click="recertification">立即上传证件</el-button>
    </template>
  </CommonDialog>
</template>

<script>
import CommonDialog from './common-dialog.vue'
import handleMixin from './handleMixin'

export default {
  name: 'corp-info-change',
  components: {
    CommonDialog,
  },
  mixins: [handleMixin],
  data() {
    return {
    }
  },
  computed: {
    corpInfo() {
      return this.$store.state?.user?.corpInfo
    }
  },
}
</script>
