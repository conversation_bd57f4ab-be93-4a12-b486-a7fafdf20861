// 证件过期提醒弹窗
<template>
  <CommonDialog
    dialog-title="证件过期提醒"
    :visible="visible && corpInfo.failReason === 'EXPIRED'"
    @close="handleClose"
  >
    <span v-if="isExpired">您好，当前账户的{{ corpInfo.invalidWarnExpireCardName }}已过期，已暂停您的支付业务，请立即上传证件更新实名认证信息，如有疑问请联系您的客户经理！</span>
    <span v-else>您好，当前账户的{{ corpInfo.invalidWarnExpireCardName }}将于{{ corpInfo.invalidWarnExpireDate }}过期，请于{{ corpInfo.invalidWarnExpireDate }}之前更新证件，逾期将影响您的支付业务，如有疑问请联系您的客户经理！</span>
    <template #operation>
      <el-button v-if="!isExpired" @click="handleClose">稍后处理</el-button>
      <el-button type="primary" @click="recertification">立即上传证件</el-button>
    </template>
  </CommonDialog>
</template>

<script>
import CommonDialog from './common-dialog.vue'
import handleMixin from './handleMixin'

export default {
  name: 'certificate-expired',
  components: {
    CommonDialog,
  },
  mixins: [handleMixin],
  data() {
    return {
    }
  },
  computed: {
    corpInfo() {
      return this.$store.state?.user?.corpInfo
    },
    isExpired() {
      return new Date(this.corpInfo.invalidWarnExpireCardName) <= (Date.now() - this.$store.state?.common.diffTime)
    }
  }
}
</script>
