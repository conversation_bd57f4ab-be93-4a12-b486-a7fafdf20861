<template>
  <div v-if="corpInfo && corpInfo.corpStatus === 3">
    <Freeze />
    <CertificateExpired />
    <RiskAction />
    <AbnormalOperation />
    <CorpInfoChange />
  </div>
</template>

<script>
import CertificateExpired from './components/certificate-expired.vue'
import RiskAction from './components/risk-action.vue'
import AbnormalOperation from './components/abnormal-operation.vue'
import CorpInfoChange from './components/corp-info-change.vue'
import Freeze from './components/freeze.vue'

export default {
  name: 'corp-info-expired-warning',
  components: {
    CertificateExpired,
    RiskAction,
    AbnormalOperation,
    CorpInfoChange,
    Freeze
  },
  computed: {
    corpInfo() {
      return this.$store.state?.user?.corpInfo
    }
  },
}
</script>
