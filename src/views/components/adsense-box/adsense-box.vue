<style lang='scss' scoped>
$bg: #FFFFFF;
$w: 156px;

.m-t {
  margin-top: 16px;
}

.phone-panel,
.phone-panel_switch,
.phone-panel_arrow-bg {
  top: 50%;
  transform: translateY(-50%);
}

.phone-panel {
  position: fixed;
  left: -140px;
  z-index: 2000;
  padding: 16px 16px 16px 0;
  width: $w;
  background: $bg;
  box-shadow: 0 4px 4px 0 rgb(21 47 62 / 10%);
  transition: left .3s;
  box-sizing: border-box;

  .phone-panel_arrow-bg {
    position: absolute;
    left: $w;
    width: 30px;
  }

  .phone-panel_switch {
    position: absolute;
    left: $w;
    z-index: 2001;
    width: 22px;
    height: 50px;
    cursor: pointer;

    &::before,
    &::after {
      display: block;
      border-right: 22px solid transparent;
      width: 0;
      height: 0;
      content: "";
    }

    &::before {
      border-bottom: 10px solid transparent;
      transform: translateY(-9px);
    }

    &::after {
      border-top: 10px solid transparent;
      transform: translateY(39px);
    }

    .phone-panel_arrow {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 10px;
      height: 10px;
      object-fit: contain;
      transition: left .3s;
    }

    .reverse {
      transform: translate(-50%, -50%) rotate(180deg);
    }
  }

  .phone-panel_content {
    padding-bottom: 10px;
    box-sizing: border-box;
    width: 140px;
    background: #FFFFFF;
    box-shadow: 2px 2px 4px 0 rgb(21 47 62 / 10%);

    .phone-panel_content-img-panel {
      overflow: hidden;
      width: 140px;
      height: 140px;
      background: #F6F6F6;

      .phone-panel_content-img {
        display: block;
        width: 140px;
        height: 140px;
        transition: left .3s;
      }
    }

    .phone-panel_content-text {
      display: flex;
      overflow: hidden;
      margin-top: 4px;
      padding: 0 9px;
      width: 100%;
      font-size: 12px;
      font-weight: 500;
      text-overflow: ellipsis;
      color: #1F1F1F;
      box-sizing: border-box;
      line-height: 17px;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;

      &.space {
        margin-top: 10px;
      }
    }
  }

  .phone-panel_hover {
    cursor: pointer;

    &:hover {
      .phone-panel_content-img-panel .phone-panel_content-img {
        transform: scale(1.1);
        transform-origin: center;
      }
    }
  }
}

.phone-show {
  left: 0;
}
</style>

<template>
  <div :class="['phone-panel', {'phone-show': isShow}]" :style="`top:${top}`">
    <img class="phone-panel_arrow-bg" :src="arrowBg" alt="">
    <div class="phone-panel_switch" @click="tooglePanel">
      <img :class="['phone-panel_arrow', {'reverse': !isShow}]" :src="arrow" alt="">
    </div>
    <div
      v-for="(item, i) in list"
      :key="item.id"
      :class="['phone-panel_content', {'phone-panel_hover': item.handle, 'm-t': i !== 0}]"
      @click="handleClick(item)"
    >
      <div class="phone-panel_content-img-panel">
        <img class="phone-panel_content-img" :src="item.img" alt="">
      </div>
      <div v-for="(text, j) in item.desc" :key="`${item.id}_${j}`" :class="['phone-panel_content-text', {'space': j === 0}]">{{ text }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'phone-box',
  props: {
    list: {
      type: Array,
      required: true
    },
    global: { // 是否是全局改变状态，默认为否
      type: Boolean,
      default: false
    },
    globalName: { // 全局改变状态的命名
      type: String,
      default: ''
    },
    top: {
      type: String,
      default: '50%'
    }
  },
  data() {
    return {
      arrow: 'https://cdn.sdpjw.cn/static/shenduBihu/channel/toogle-arrow.png',
      arrowBg: 'https://cdn.sdpjw.cn/static/shenduBihu/channel/arrow-bg.png',
      isShow: false
    }
  },
  created() {
    this.initStatus()
  },
  methods: {
    // 初始化广告位收缩状态
    initStatus() {
      if (this.global && this.globalName) { // 全局改变状态
        let lastTime = localStorage.getItem(this.globalName)
        // 当天0点时间
        let currentTime = new Date(new Date().toLocaleDateString()).getTime()
        if (!lastTime || lastTime < currentTime) { // 每天第一次登录默认展开
          localStorage.setItem(this.globalName, new Date().getTime())
          localStorage.setItem(`${this.globalName}_show`, '1')
          this.isShow = true
        } else {
          let isShow = localStorage.getItem(`${this.globalName}_show`)
          this.isShow = isShow === '1'
        }
        return
      }
      // 局部改变状态
      this.tooglePanel()
    },
    handleClick(item) {
      if (!item.handle) {
        return
      }
      item.handle()
    },
    tooglePanel() {
      if (this.global && this.globalName) { // 全局改变状态
        let isShow = localStorage.getItem(`${this.globalName}_show`)
        this.isShow = isShow !== '1'
        localStorage.setItem(`${this.globalName}_show`, isShow === '1' ? '0' : '1')
        return
      }
      this.isShow = !this.isShow
    }
  }
}
</script>
