<!-- 瑕疵提示弹窗 -->
<style lang="scss" scoped>
.defects-notify-dialog {
  .content {
    overflow-y: auto;
    padding: 16px 16px 6px;
    max-height: 268px;
    background: $color-FFFFFF;
  }

  .title {
    margin-bottom: 2px;
    font-size: 14px;
    color: $color-text-secondary;
  }

  .effect {
    margin-bottom: 10px;
    font-size: 16px;
    color: $color-text-primary;
    line-height: 24px;
  }
}
</style>

<template>
  <el-dialog
    title="瑕疵提示"
    :visible.sync="dialogVisible"
    width="490px"
    :close-on-click-modal="false"
    class="defects-notify-dialog whead-gbody-dialog"
    append-to-body
  >
    <div class="content">
      <p v-if="obj.endorseDefect" class="title">背书手数</p>
      <p v-show="obj.endorseDefect" ref="endorseDefect" class="effect" />
      <template v-for="item in obj.other">
        <p :key="item.id" class="title">{{ item.title }}：</p>
        <p :key="item.id" class="effect">{{ item.list.join('；') }}</p>
      </template>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button type="primary" size="large" @click="dialogVisible = false">我知道了</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'batch-edit-dialog',

  data() {
    return {
      dialogVisible: false,
      obj: { }
    }
  },

  methods: {
    // 切换显示隐藏
    async toggle(obj) {
      this.dialogVisible = !this.dialogVisible
      this.dialogVisible && (this.obj = obj)
      await this.$nextTick()
      if (obj.endorseDefect) {
        this.$refs.endorseDefect.innerHTML = obj.endorseDefect
      }
    },
  }
}
</script>
