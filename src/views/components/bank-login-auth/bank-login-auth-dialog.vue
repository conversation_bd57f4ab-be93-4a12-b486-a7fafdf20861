<!-- 网银登录授权 -->
<style lang="scss" scoped>
.bank-login-auth-dialog {
  .bank-login-auth-dialog-content {
    padding: 10px;
    background: #FFFFFF;

    .content-form {
      padding-top: 10px;
    }

    ::v-deep {
      .el-form-item {
        margin-bottom: 12px;
      }

      .el-form-item__content {
        margin-left: 0;
      }

      .el-form-item__label {
        text-align: left;
        line-height: 30px;
      }
    }
  }

  .footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 10px;
  }
}
</style>

<style lang="scss">
.bank-login-auth-dialog {
  .el-dialog__body {
    padding: 15px;
  }
}
</style>

<template>
  <div>
    <el-dialog
      title="网银登录授权"
      :visible.sync="visible"
      width="500px"
      class="bank-login-auth-dialog"
      append-to-body
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <div class="bank-login-auth-dialog-content">
        <WarnContent>
          为完成当前订单操作须要您授权简易版网银登录信息，该授权仅对该笔订单在简易版网银内的交易状态进行核验。
        </WarnContent>
        <div class="content-form">
          <el-form
            ref="ruleForm"
            :model="form"
            :rules="rules"
          >
            <el-form-item label="双因子链接" prop="bankLoginUrl">
              <el-input
                v-model="form.bankLoginUrl"
                placeholder="请输入简易版网银双因子链接"
                clearable
                @paste.native="handlePaste"
                @input="handlePaste"
              />
            </el-form-item>
            <el-form-item label="登录账号" prop="bankLoginAccount">
              <el-input v-model="form.bankLoginAccount" placeholder="请输入简易版网银登录账号" />
            </el-form-item>
            <el-form-item label="OTP key" prop="bankKey">
              <el-input v-model="form.bankKey" show-password placeholder="请输入简易版网银OTP密钥 " />
            </el-form-item>
            <el-form-item label="登录密码" prop="bankLoginPassword">
              <el-input v-model="form.bankLoginPassword" show-password placeholder="请输入简易版网银登录密码" />
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="footer-content">
        <div>
          <el-checkbox v-model="isRead">我已阅读并同意</el-checkbox>
          <a
            class="text-link"
            target="_blank"
            rel="noopener noreferrer"
            @click="handleUrlClick(E_PLUS_SIGN_COMPANY_INFO_AUTH_URL)"
          >《模拟登录企业授权书》</a>
        </div>
        <div>
          <el-button @click="handleClose">取 消</el-button>
          <el-button v-waiting="userApi.loginCrawler" type="primary" @click="confirm">确 定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import WarnContent from '@/views/components/common/warn-content.vue' // 提示
import { OPEN_BANK_LOGIN_AUTH_SUCCESS } from '@/event/modules/site'
import {
  OPEN_URL_IN_DEFAULT_BROWSER,
} from '@recognize/ipc-event-constant'
import userApi from '@/apis/user'
import {
  E_PLUS_SIGN_COMPANY_INFO_AUTH_URL,
} from '@/constants/oss-files-url'
export default {
  name: 'bank-login-auth-dialog',
  components: {
    WarnContent
  },
  data() {
    return {
      E_PLUS_SIGN_COMPANY_INFO_AUTH_URL,
      userApi,
      visible: false,
      form: {
        bankLoginAccount: '',
        bankLoginPassword: '',
        bankKey: '',
        bankLoginUrl: '',
      },
      key: '', // 简易版网银登录校验成功 唯一标识区分 参考枚举字典 E_PLUS_LOGIN_AUTH_KEY
      param: null, // 不同场景对应的传入的参数列表存储
      rules: {
        bankLoginAccount: [{ required: true, message: '请输入简易版网银登录账号', trigger: 'blur' }],
        bankLoginPassword: [{ required: true, message: '请输入简易版网银登录密码', trigger: 'blur' }],
        bankKey: [{ required: true, message: '请输入OTP动态密码', trigger: 'blur' }]
      },
      isRead: false
    }
  },
  methods: {
    async init(data) {
      this.key = data?.key || ''
      this.param = data?.param || null
      // 判断是否属于已登录状态 0-未登录;1-登录成功;2-登录失败;3-登录中
      const res = await userApi.checkLoginCrawlerStatus()
      if (res.loginStatus === 1) { // 已登录
        this.$event.emit(OPEN_BANK_LOGIN_AUTH_SUCCESS, { key: this.key, param: this.param })
      } else if (res.loginStatus === 3) { // 登录中
        this.$message.warning('简易版网银登录授权中，请稍后...')
      } else { // 未登录 登录失败
        this.visible = true
      }
    },

    handleClose() {
      this.key = ''
      this.form = {
        bankLoginAccount: '',
        bankLoginPassword: '',
        bankKey: '',
        bankLoginUrl: '',
      }
      this.$nextTick().then(() => {
        this.$refs.ruleForm.resetFields()
      })
      this.visible = false
    },

    async confirm() {
      if (!this.isRead) {
        this.$message.warning('请确认我已阅读并同意')
        return
      }
      await this.$refs.ruleForm.validate()
      // 校验登录状态接口
      const result = await userApi.checkLoginCrawlerStatus()
      if (result.loginStatus === 1) { // 已登录
        this.$event.emit(OPEN_BANK_LOGIN_AUTH_SUCCESS, { key: this.key, param: this.param })
        // this.handleClose()
      } else if (result.loginStatus === 3) { // 登录中
        this.$message.warning('简易版网银登录授权中，请稍后...')
      } else { // 登录失败
        // 简易版网银登录接口
        const res = await userApi.loginCrawler(this.form)
        if (res.loginStatus === 1) { // 登录成功
          this.$event.emit(OPEN_BANK_LOGIN_AUTH_SUCCESS, { key: this.key, param: this.param })
          // this.handleClose()
        } else if (res.loginStatus === 3) { // 登录中
          this.$message.warning('简易版网银登录授权中，请稍后...')
        } else { // 登录失败
          this.$message.error(`${res.loginFailReason}`)
        }
      }
      this.handleClose()
    },

    handleUrlClick(url) {
      if (this.$ipc) {
        this.$ipc.send(OPEN_URL_IN_DEFAULT_BROWSER, url)
      } else {
        window.open(url)
      }
    },
    // 粘贴识别
    handlePaste(e) {
      let text
      if (e.clipboardData) {
      // 粘贴事件
        e.preventDefault() // 阻止默认粘贴行为
        text = e.clipboardData.getData('text')
      } else {
      // input事件
        text = e
      }

      // 识别链接中的secret是否存在
      const secretMatch = text.match(/secret=([^&]*)/)
      const secret = secretMatch ? secretMatch[1] : ''
      // 识别链接中的account是否存在
      const accountMatch = text.match(/%3A([^?]*)/)
      this.form.bankLoginUrl = text
      if (secret && accountMatch[1]) {
        this.form.bankKey = secret // 设置 opt key
        this.form.bankLoginAccount = accountMatch[1] // 识别网银账号
      } else {
        this.form.bankKey = '' // 设置 opt key
        this.form.bankLoginAccount = '' // 识别网银账号
      }
    },
  },
}
</script>
