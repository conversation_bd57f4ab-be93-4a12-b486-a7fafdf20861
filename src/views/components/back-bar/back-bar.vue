<style lang="scss" scoped>
.back-bar-wrapper {
  @include flex-vc;

  margin: 0 16px;
  margin-bottom: 1px;
  padding: 0 20px;
  height: 48px;
  background: $color-FFFFFF;

  .back-left {
    @include flex-vc;

    .back-text-wrap {
      cursor: pointer;
    }

    .back-text {
      display: inline-block;
      margin-left: 8px;
      font-size: 16px;
      font-weight: 600;
      color: $color-text-primary;
      line-height: 20px;
    }

    .arrow-icon {
      transform: rotate(180deg);
      vertical-align: -.1em;
    }
  }
}
</style>

<template>
  <div class="back-bar-wrapper">
    <div class="back-left">
      <span class="back-text-wrap" @click="onBackEvent">
        <icon class="arrow-icon" type="chengjie-xiangyou" />
        <span class="back-text">{{ backText }}</span>
      </span>
      <slot />
    </div>
    <slot name="backRight" />
  </div>
</template>

<script>
import { ORDER_DETAIL_GO_BACK, ARTICLE_GO_BACK } from '@/event/modules/site'
export default {
  name: 'back-bar',
  props: {
    backText: {
      type: String,
      default: '返回'
    }
  },
  data() {
    return {
      maxWin: false
    }
  },
  methods: {
    onBackEvent() {
      const { path, query } = this.$route
      // console.log(path)
      switch (path) {
        case '/user-center/payment-channel-contrast': // 账户对比说明
          this.$router.replace({ path: query.fromPath })
          break
        case '/user-center/draft-detail': // 订单详情
          this.$event.emit(ORDER_DETAIL_GO_BACK)
          break
        case '/user-center/news-detail': // 文章详情
          this.$event.emit(ARTICLE_GO_BACK)
          break
        default: this.$router.go(-1)
          break
      }
      this.$emit('back-click')
    }
  }
}
</script>
