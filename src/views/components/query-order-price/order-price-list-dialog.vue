<!-- 订单价格列表窗口 -->

<style lang="scss" scoped>
.order-price-list-wrap {
  position: relative;
  padding: 16px;
  background: #FFFFFF;

  .items {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    white-space: nowrap;

    .item-label {
      margin-right: 16px;
      font-weight: 600;
      color: $color-text-secondary;
    }

    // 多选按钮样式 start
    .el-checkbox {
      border-radius: 2px;
      width: 48px;
      height: 32px;
      line-height: 31px;
      text-align: center;
      color: $color-text-primary;
    }

    .fixed-width {
      ::v-deep {
        .el-checkbox {
          .el-checkbox__label {
            padding: 0 10px;
          }
        }
      }
    }

    .el-checkbox.is-bordered {
      margin-right: 8px;
      margin-left: 0;
      padding: 0;

      &:last-child {
        margin-right: 0;
      }
    }

    // 多选按钮样式 end

    .fill-amount {
      font-weight: 400;

      @include flex-vc;
    }

    .append-input {
      width: 155px;

      ::v-deep {
        ::placeholder {
          color: $color-text-light;
        }
      }
    }

    .append-input-small {
      width: 200px;
    }

    .charges-input {
      ::v-deep {
        .el-input__inner {
          padding-left: 0;
        }
      }
    }

    .switch-btn {
      margin-left: 8px;
      border: 1px solid $color-D9D9D9;
      border-radius: 45px;
      padding: 0 10px;
      min-width: 52px;
      height: 32px;
      color: $--color-primary;
      background: $color-FFFFFF;

      &:hover {
        border-color: $--color-primary;
      }
    }

    // 票面期限日期卡片样式
    .draft-deadline-date {
      width: 155px;

      ::v-deep {
        ::placeholder {
          font-weight: normal;
          color: $color-text-light;
        }

        .el-input__inner {
          padding-right: 12px;
          font-size: 14px;
        }
      }
    }
  }

  // 多选按钮样式 start
  .el-checkbox {
    border-radius: 2px;
    height: 32px;
    line-height: 31px;
    text-align: center;
    color: $color-text-primary;
  }

  .fixed-width {
    ::v-deep {
      .el-checkbox {
        .el-checkbox__label {
          padding: 0 10px;
        }
      }
    }
  }

  ::v-deep .el-input__inner {
    height: 32px;
  }

  ::v-deep .el-input__icon,
  .accept-input-icon {
    line-height: 32px;
  }

  .tips {
    margin-bottom: 16px;
    font-size: 14px;

    .tips-title {
      color: $color-warning;
    }

    .tips-text {
      color: #999999;
    }

    .tips-text-link {
      margin-left: 16px;
      border-bottom: 1px solid $--color-primary;
      color: $--color-primary;
      cursor: pointer;
    }
  }

  .package-btn {
    position: absolute;
    top: 70px;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 20px 0 0 20px;
    padding: 6px 0;
    width: 110px;
    text-align: center;
    color: $color-FFFFFF;
    background: #EE9D25;
    box-shadow: 0 4px 10px 0 rgb(219 143 31 / 20%);
    cursor: pointer;

    .btn-icon {
      margin-right: 8px;
    }
  }

  ::v-deep .el-pagination {
    padding: 16px 0 0;
  }

  ::v-deep .el-table__body-wrapper {
    overflow: hidden;
    overflow-y: auto;
    max-height: 280px;
  }

  .acceptor {
    text-align: left;

    @include ellipsis(2);
  }

  .flex-center {
    display: flex;
    justify-content: space-between;
  }

  .dialog-footer {
    margin-left: 20px;
  }
}
</style>

<style lang="scss">
.order-price-list-dialog-cls {
  .el-dialog__footer {
    padding: 12px 20px;
  }
}
</style>

<template>
  <el-dialog
    title="查询订单价格"
    :visible.sync="visible"
    class="order-price-list-dialog-cls"
    width="880px"
    :close-on-click-modal="false"
    :append-to-body="true"
    :before-close="handleClose"
  >
    <div class="special-box order-price-list-wrap">
      <div class="items">
        <span class="item-label">票据类型</span>
        <el-checkbox-group v-model="searchForm.acceptorType" class="fixed-width draft-type" @change="(e) => checkboxChoose(e, 'acceptorType')">
          <el-checkbox
            v-for="item in draftTypeOptions"
            :key="item.value"
            :label="item.value"
            type="button"
          >
            {{ item.label }}
          </el-checkbox>
        </el-checkbox-group>
        <el-input
          v-model="searchForm.accepterKeyword"
          placeholder="承兑人名称"
          style="margin-left: 8px;width: 300px;"
          :class="['accept-input']"
        >
          <icon
            slot="prefix"
            class="accept-input-icon"
            type="chengjie-search"
            size="16"
          />
        </el-input>
      </div>
      <div class="items">
        <span class="item-label">票面金额</span>
        <div>
          <el-input
            v-model="searchForm.draftAmountMin"
            type="number"
            class="append-input"
            placeholder="最小金额"
            :number-format="wanNumberFormat"
          >
            <template slot="append">万</template>
          </el-input>
          <span class="to"> - </span>
          <el-input
            v-model="searchForm.draftAmountMax"
            type="number"
            class="append-input"
            placeholder="最大金额"
            :number-format="wanNumberFormat"
          >
            <template slot="append">万</template>
          </el-input>
        </div>
      </div>
      <div class="items">
        <span class="item-label">票据期限</span>
        <div>
          <template v-if="searchForm.periodType">
            <el-date-picker
              v-model="searchForm.beginMaturityDate"
              class="draft-deadline-date"
              type="date"
              placeholder="开始日期"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptionsStart"
              :clearable="false"
            />
            <span class="to"> - </span>
            <el-date-picker
              v-model="searchForm.endMaturityDate"
              class="draft-deadline-date"
              type="date"
              placeholder="结束日期"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptionsEnd"
              :clearable="false"
            />
          </template>
          <template v-else>
            <el-input
              v-model="searchForm.interestDaysMin"
              type="number"
              class="append-input"
              placeholder="最小天数"
              :number-format="dayNumberFormat"
              maxlength="3"
            >
              <template slot="append">天</template>
            </el-input>
            <span class="to"> - </span>
            <el-input
              v-model="searchForm.interestDaysMax"
              type="number"
              class="append-input"
              placeholder="最大天数"
              :number-format="dayNumberFormat"
              maxlength="3"
            >
              <template slot="append">天</template>
            </el-input>
          </template>
          <el-tooltip placement="top" disabled>
            <div slot="content">切换为{{ searchForm.periodType ? '天' : '日期' }}</div>
            <el-button class="switch-btn" @click="periodTypeHandle">
              <icon class="icon" type="chengjie-swap" />
              <span>{{ searchForm.periodType ? '天' : '日期' }}</span>
            </el-button>
          </el-tooltip>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button
            v-waiting="['post::loading::/contact/pageOrderPriceList']"
            height="32px"
            type="primary"
            @click="queryDebounceData"
          >查 询</el-button>
          <el-button height="32px" @click="handleReset">清 空</el-button>
        </span>
      </div>
      <div class="tips">
        <span class="tips-title">温馨提示：</span>
        <span class="tips-text">可购买查询订单套餐，查询更多订单价格请</span>
        <span class="tips-text-link" @click="openOrderPricePackageDialog">购买套餐</span>
      </div>

      <!-- 表格数据 -->
      <el-table
        ref="tableRef"
        border
        :data="tableData"
        @sort-change="handleSortChange"
      >
        <el-table-column
          key="1"
          prop="finishTimeSort"
          label="时间"
          min-width="80"
          sortable="custom"
          :sort-orders="['ascending', 'descending']"
          :sort-loop-click="true"
          class-name="sort-absolute-column fast-column"
        >
          <template slot-scope="scope">
            <template v-if="scope.row.finishTime">
              {{ formatTime(scope.row.finishTime, 'hh:mm') }}
            </template>
            <template v-else>-</template>
          </template>
        </el-table-column>
        <el-table-column
          key="2"
          prop="acceptorName"
          label="承兑人"
          min-width="110"
        >
          <template slot-scope="scope">
            <el-tooltip
              v-if="scope.row.acceptorName"
              placement="top-start"
              :open-delay="500"
              :content="scope.row.acceptorName"
            >
              <div class="acceptor">{{ scope.row.acceptorName || '-' }}</div>
            </el-tooltip>
            <template v-else>-</template>
          </template>
        </el-table-column>
        <el-table-column
          key="3"
          prop="draftAmount"
          label="票面金额（万）"
          min-width="110"
        >
          <template slot-scope="scope">
            <template v-if="scope.row.draftAmount">
              {{ yuan2wan(scope.row.draftAmount) }}
            </template>
            <template v-else>-</template>
          </template>
        </el-table-column>
        <el-table-column
          key="4"
          prop="maturityDate"
          label="到期日"
          min-width="110"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.maturityDate">
              <div>{{ scope.row.maturityDate }}</div>
              <div v-if="scope.row.interestDays">(剩 {{ scope.row.interestDays }} 天)</div>
            </div>
            <template v-else>-</template>
          </template>
        </el-table-column>
        <el-table-column
          key="5"
          prop="lakhFeeSort"
          label="每十万扣款（元）"
          min-width="130"
          sortable="custom"
          :sort-orders="['ascending', 'descending']"
          :sort-loop-click="true"
          class-name="sort-absolute-column fast-column"
        >
          <template slot-scope="scope">
            <template v-if="scope.row.lakhFee">
              {{ scope.row.lakhFee }}
            </template>
            <template v-else>-</template>
          </template>
        </el-table-column>
        <el-table-column
          key="6"
          prop="annualInterestSort"
          label="利率"
          min-width="80"
          sortable="custom"
          :sort-orders="['ascending', 'descending']"
          :sort-loop-click="true"
          class-name="sort-absolute-column fast-column"
        >
          <template slot-scope="scope">
            <template v-if="scope.row.annualInterest">
              {{ scope.row.annualInterest }}%
            </template>
            <template v-else>-</template>
          </template>
        </el-table-column>
      </el-table>
      <div class="footer-pagination">
        <el-pagination
          :current-page="query.pageNum"
          :page-size="query.pageSize"
          background
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalRecord"
          :page-sizes="[10, 20, 30, 50]"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      <div class="package-btn" @click="openOrderPricePackageDialog">
        <template v-if="packageDetail && packageDetail.purchaseValidPackage">
          <span>套餐剩余{{ packageDetail.maxValidDay || 0 }}天</span>
        </template>
        <template v-else>
          <icon type="chengjie-taocan" class="btn-icon" />
          <span>购买套餐</span>
        </template>
      </div>
    </div>
    <OrderPricePackageDialog ref="orderPricePackageDialogRef" @recharge="recharge" @success="getPurchaseOrderPriceDetail" />
    <Recharge ref="recharge" />
  </el-dialog>
</template>

<script>
import orderConditionsData from '@/views/pages/market/components/order-conditions-components/order-conditions-data.js' // 选项数据
import OrderPricePackageDialog from './order-price-package-dialog.vue'
import Recharge from '@/views/components/user-center/recharge/recharge.vue'
import orderApi from '@/apis/order'
import { yuan2wan, wan2yuan } from '@/common/js/number'
import { formatTime } from '@/common/js/date'
import {
  debounce // 防抖
} from '@/common/js/util'

// 选项数据解构
const {
  draftTypeOptions, // 票据类型选项
} = orderConditionsData

const defaultSearchForm = {
  acceptorType: [], // 票据类型
  accepterKeyword: '', // 承兑人名称
  draftAmountMin: '', // 票面金额最小值
  draftAmountMax: '', // 票面金额最大值
  periodType: 1, // 票据期限类型 1-表示选择开始日期与结算日期，默认为0,选择票面期限最小计息天数与最大计息天数
  interestDaysMin: '', // 票面期限最小计息天数
  interestDaysMax: '', // 票面期限最大计息天数
  beginMaturityDate: null, // 票据期限开始日期
  endMaturityDate: null, // 票据期限结束日期
}

const defaultQuery = {
  pageNum: 1,
  pageSize: 10,
}

export default {
  name: 'order-price-list-dialog',
  components: {
    OrderPricePackageDialog,
    Recharge
  },
  data() {
    return {
      yuan2wan,
      formatTime,
      visible: false,
      draftTypeOptions: [...draftTypeOptions].filter(e => e.value),
      searchForm: { ...defaultSearchForm },
      totalRecord: 0,
      query: { ...defaultQuery },
      sortData: {},
      // 万为单位的输入框格式
      wanNumberFormat: {
        decimal: true,
        maxDecimalLength: 6,
        negative: false,
        leadingZero: false,
        maxIntegerLength: 5
      },
      // 天为单位的输入框格式
      dayNumberFormat: {
        decimal: false,
        negative: false,
        leadingZero: false,
      },
      isShowDeadlineDateorDay: true, // 切换票面期限日期、天数
      queryDebounceData: '',

      tableData: [],

      packageDetail: {}, // 套餐详情

      // 限制结束日期大于开始日期
      pickerOptionsStart: {
        disabledDate: time => {
          let endDateVal = this.searchForm.endMaturityDate
          let beforeToday = time.getTime() < (Date.now() - (24 * 60 * 60 * 1000))
          let timeDifference = time.getTime() > new Date(endDateVal).getTime()
          return endDateVal ? (beforeToday || timeDifference) : beforeToday
        }
      },
      // 限制开始日期小于结束日期
      pickerOptionsEnd: {
        disabledDate: time => {
          let beginDateVal = this.searchForm.beginMaturityDate
          let beforeToday = time.getTime() < (Date.now() - (24 * 60 * 60 * 1000))
          let timeDifference = time.getTime() < new Date(`${beginDateVal} 00:00:00`).getTime()
          return beginDateVal ? (beforeToday || timeDifference) : beforeToday
        }
      },
    }
  },
  computed: {

  },
  methods: {
    recharge() {
      this.$refs.recharge && this.$refs.recharge.init()
    },

    // 判断是否为Null
    isNull(data) {
      return !data && typeof (data) !== 'undefined' && data !== 0
    },

    init() {
      this.visible = true

      this.queryDebounceData = debounce(this.pageOrderPriceList, 300)
      // this.queryDebounceData()
      this.getPurchaseOrderPriceDetail()
    },

    setParams() {
      const params = {
        ...this.searchForm,
        ...this.query,
        ...this.sortData,
      }
      params.acceptorType = params.acceptorType.filter(item => item !== null)
      params.draftAmountMin = params.draftAmountMin ? wan2yuan(params.draftAmountMin) : ''
      params.draftAmountMax = params.draftAmountMax ? wan2yuan(params.draftAmountMax) : ''
      return params
    },

    async pageOrderPriceList() {
      // 存在查询条件才调用接口
      const form = JSON.parse(JSON.stringify(this.searchForm))
      delete form.periodType // 剔除
      const isQuery = Object.values(form).some(item => {
        if (Array.isArray(item)) {
          return item.length > 0
        }
        return item !== '' && item !== null && item !== undefined
      })
      if (isQuery) {
        const params = this.setParams()
        const res = await orderApi.pageOrderPriceList(params)
        this.tableData = res.rowList
        this.totalRecord = res.totalRecord
      } else {
        this.$message.warning('请至少输入一种查询条件')
      }
    },

    async getPurchaseOrderPriceDetail() {
      const res = await orderApi.purchaseOrderPriceDetail()
      this.packageDetail = res || {}
    },

    // 更改每页条数
    handleSizeChange(val) {
      this.query.pageSize = val
      this.query.pageNum = 1
      this.queryDebounceData()
    },

    // 更改当前页
    handleCurrentChange(val) {
      this.query.pageNum = val
      this.queryDebounceData()
    },

    handleClose() {
      this.searchForm = { ...defaultSearchForm }
      this.query = { ...defaultQuery }
      this.visible = false
    },
    // 票据类型、票面期限 多选事件
    checkboxChoose(e, type) {
      if (e.length) {
        if (e[e.length - 1] === null) {
          this.searchForm[type] = [null]
        } else if (e.length > 1 && e[0] === null) {
          this.searchForm[type].splice(0, 1)
        } else {
          this.searchForm[type] = e
        }
      } else {
        this.searchForm[type] = [null]
      }
      // 用来修复多选重复点击【全选】按钮的问题
      if (e.length && this.isNull(this.searchForm[type][0])) {
        this.searchForm[type] = [null]
      }
    },
    // 票面期限日期选择事件
    // draftDeadline4PickerChange() {
    //   this.searchForm.interestDaysMin = ''
    //   this.searchForm.interestDaysMax = ''
    // },
    // // 票面期限输入事件
    // draftDeadline4InputChange() {
    //   this.searchForm.beginMaturityDate = ''
    //   this.searchForm.endMaturityDate = ''
    // },

    // 排序事件
    handleSortChange(column) {
      const sortData = {}
      // 有排序时赋值
      if (column.order) {
        sortData[column.prop] = column.order === 'ascending' ? 1 : 0
        this.sortData = sortData
      }
      this.queryDebounceData()
    },
    // 打开购买套餐弹窗
    openOrderPricePackageDialog() {
      this.$refs.orderPricePackageDialogRef.init()
    },

    periodTypeHandle() {
      this.searchForm.periodType = this.searchForm.periodType ? 0 : 1
      this.searchForm.interestDaysMin = '' // 票面期限最小计息天数
      this.searchForm.interestDaysMax = '' // 票面期限最大计息天数

      this.searchForm.beginMaturityDate = '' // 票据期限开始日期
      this.searchForm.endMaturityDate = ''
    },
    handleReset() {
      this.searchForm = { ...defaultSearchForm }
      this.query = { ...defaultQuery }
      this.tableData = []
      this.totalRecord = 0
    }
  }
}
</script>
