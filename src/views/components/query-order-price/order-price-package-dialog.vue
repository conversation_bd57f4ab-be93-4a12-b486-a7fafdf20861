<!-- 联系方式充值弹窗 -->
<style lang="scss" scoped>
.pay-contact-dialog-cls {
  .content {
    margin-top: 12px;
    padding: 16px;
    font-size: 16px;
    font-weight: 500;
    background: #FFFFFF;

    .title {
      display: flex;
      align-items: center;
      padding-bottom: 12px;

      &::before {
        display: inline-block;
        margin-right: 8px;
        width: 4px;
        height: 16px;
        background-color: $--color-primary;
        content: "";
      }
    }

    .item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;
      border: 1px solid $color-F2F2F2;
      border-radius: 4px;
      padding: 12px;
      background: $color-F2F2F2;

      ::v-deep .el-radio {
        display: flex;
        align-items: center;
        width: 100%;
        font-size: 16px;
        font-weight: 500;
      }

      ::v-deep .el-radio__input {
        margin-top: 4px;
      }

      ::v-deep .el-radio__label {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-left: 4px;
        width: 100%;
      }

      ::v-deep .el-radio__inner {
        border: 1px solid rgb(156 162 185 / 50%);
        box-shadow: inset 2px 2px 4px 0 rgb(156 162 185 / 32%);
      }

      .left {
        .red {
          color: #F51818;
        }
      }

      .right {
        span {
          font-size: 14px;
          font-weight: normal;
          color: #3D3D3D;
        }
      }
    }

    ::v-deep {
      .el-form-item {
        margin-bottom: 12px;
      }

      .get-code-box {
        .el-form-item__content {
          display: flex;
        }

        .get-code-btn {
          margin-left: 8px;
        }
      }
    }

    .balance-wrap {
      padding-bottom: 12px;
      font-size: 14px;

      .label {
        color: $color-text-secondary;

        .num {
          font-weight: 600;
          color: $color-text-primary;
        }
      }
    }
  }
}

.f-16-500 {
  font-size: 16px;
  font-weight: 500;
}

.recharge {
  @include example-underline;

  line-height: 20px;
  margin-left: 6px;
}

.w-100 {
  min-width: 100px;
}

.flx {
  display: flex;
  align-items: center;
}

.f-line {
  text-decoration: line-through;
}

.icon-cls {
  padding-left: 10px;

  span {
    display: inline-block;
    padding-left: 4px;
    font-size: 14px;
    font-weight: normal;
    color: #F7A600;
  }
}

.form-item-title {
  font-size: 14px;
  font-weight: normal;
  color: #8C8C8C;

  &::before {
    content: "*";
    color: #F5222D;
  }

  .get-code-box {
    display: flex;
  }
}

.check {
  border: 1px solid $--color-primary !important;
  border-radius: 5px !important;
  background: rgb(54 106 240 / 10%) !important;
}
</style>

<style lang="scss">
  .pay-contact-dialog-cls {
    .el-dialog__body {
      padding: 20px 20px 12px;
    }
  }
</style>

<template>
  <el-dialog
    title="购买查询价格套餐"
    custom-class="pay-contact-dialog-cls"
    :visible.sync="visible"
    :append-to-body="true"
    width="700px"
    :center="true"
    :before-close="handleClose"
  >
    <WarnContent>
      查询更多订单价格需购买以下套餐，一经购买不可退。
    </WarnContent>
    <div class="content">
      <div class="title">请选择套餐</div>
      <div v-for="item in orderPricePackageList" :key="item.id" :class="['item', item.id === packageId ? 'check' : '']">
        <el-radio v-model="packageId" :label="item.id">
          <div class="left f-16-500 flx">
            {{ item.packageName }} <span class="red">{{ item.price }}</span> {{ sdmName }}
            <template v-if="item.originalPrice">
              <span>
                (<span class="f-line">{{ item.originalPrice }}</span>{{ sdmName }})
              </span>
            </template>
            <!-- recommend 是否推按 1-是 2-否 -->
            <div v-if="item.recommend === 1" class="icon-cls flx">
              <img style="width: 28px;" src="https://oss.chengjie.red/web/imgs/new_erp/images/recommend.png">
              <span>推荐</span>
            </div>
          </div>
          <div class="right">
            <span>{{ item.remark }}</span>
            <!--
              <span v-if="!item.validDays">该笔订单无限次数查看成交价格</span>
              <span v-else>{{ item.validDays }}天不限次数查看成交价格</span>
            -->
            <!-- 可查看订单条数 -->
            <!-- <span v-if="item.orderDisplayQuantity">，可查看{{ item.orderDisplayQuantity }}条</span> -->
          </div>
        </el-radio>
      </div>
      <div class="balance-wrap">
        <div class="label">
          {{ sdmName }}可用余额
          <span class="num">{{ (sdmInfo || {}).balanceAmt || '0.00' }}{{ sdmUnit }}</span>
          <span class="recharge" @click="recharge">充值</span>
        </div>
      </div>
      <el-form ref="formData" :model="formData" :rules="rules">
        <div class="form-item-title">手机号码</div>
        <el-form-item prop="mobile">
          <el-input
            :value="mobile"
            placeholder="请输入手机号码"
            :disabled="true"
          />
        </el-form-item>
        <div class="form-item-title">验证码</div>
        <el-form-item class="get-code-box" prop="code" :error="errorCodeMsg">
          <el-input
            v-model="formData.code"
            placeholder="请输入验证码"
            :height="40"
            type="number"
            :number-format="{ maxLength: 6, decimal: false, negative: false }"
            @change="codeChange"
          />
          <el-button
            v-waiting="'post::loading::/api/platform/user/updateCorpMemberMobile/sendPhoneVerifyCode'"
            class="get-code-btn"
            type="primary"
            width="132"
            height="40"
            :disabled="!canGetCode"
            @click="getCode"
          >
            {{ canGetCode ? "获取验证码" : `还剩 ${leftSeconds}s` }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="confirmPay">确认支付</el-button>
    </span>
  </el-dialog>
</template>

<script>
import WarnContent from '@/views/components/common/warn-content.vue'
import { mapGetters, mapActions } from 'vuex'
import Storage from '@/common/js/storage' // 本地缓存对象
import orderApi from '@/apis/order'
import { SEND_CODE_TIME } from '@/constant-storage' // 发送验证码倒计时key
import { PURCHASE_PACKAGE_BUSINESS_TYPE } from '@/constant'

const SEND_CODE_TIME_KEY = 'fixMobileContact' // 充值
export default {
  name: 'contacts-dialog',
  components: {
    WarnContent,
  },
  data() {
    return {
      visible: false,
      orderNo: '',
      orderPricePackageList: [],
      packageId: '', // 选中套餐id
      formData: {},
      errorCodeMsg: null, // 验证码的错误提示
      leftSeconds: 0, // 剩余秒数
      rules: { // 表单校验规则
        code: [
          {
            required: true,
            pattern: /[0-9]{6}$/,
            message: '请输入6位验证码',
            trigger: ['blur']
          },
        ],
      },
    }
  },
  computed: {
    ...mapGetters('user', {
      sdmInfo: 'sdmInfo', // 米账号信息
    }),
    screenW() {
      return window.innerWidth
    },
    mobile() {
      return this.$store.state?.user?.userInfo?.mobile
    },
    // 是否可以获取验证码
    canGetCode() {
      return !(this.leftSeconds > 0)
    },

  },

  methods: {
    ...mapActions('user', {
      getSdmInfo: 'getSdmInfo'
    }),
    init(orderNo) {
      this.getSdmInfo()
      // 套餐配置获取
      this.orderPricePackageList = this.$store.state.common.publicConfig?.orderPricePackageList || []
      // 设置默认选中
      this.orderPricePackageList.length && (this.packageId = this.orderPricePackageList[0].id)
      this.visible = true
      this.orderNo = orderNo
      // 弹窗打开时，若上一次发送验证码的倒计时未结束，则继续开始倒计时
      if (Storage.get(SEND_CODE_TIME) && Storage.get(SEND_CODE_TIME)[SEND_CODE_TIME_KEY]) {
        this.startCountDown()
      }
    },
    handleClose() {
      this.clearTime()
      this.$refs.formData.resetFields()
      this.visible = false
    },
    recharge() {
      this.$emit('recharge')
    },
    codeChange() {
      this.errorCodeMsg = null
    },
    // 获取验证码
    async getCode() {
      this.errorMobileMsg = null
      try {
        await orderApi.getContactVerificationCode({ mobile: this.mobile, businessType: PURCHASE_PACKAGE_BUSINESS_TYPE.ORDER_PRICE.id })
        this.$message({
          message: '短信验证码已发送，请注意查收',
          type: 'success'
        })
        this.startCountDown()
      } catch (err) {
        // eslint-disable-next-line no-console
        console.log(err)
      }
    },
    // 开始倒计时
    startCountDown() {
      let nowTime = new Date().getTime() // 当前时间
      let sendCodeTime = Storage.get(SEND_CODE_TIME) || '' // 所有发送验证码时间的本地缓存对象
      let lastTime = sendCodeTime && sendCodeTime[SEND_CODE_TIME_KEY] ? sendCodeTime[SEND_CODE_TIME_KEY] : null // 上一次发送验证码时间
      let durationTime = 60 // 倒计时时间

      if (sendCodeTime) {
        // 上一次发送验证码的倒计时未结束
        if (lastTime) {
          durationTime = durationTime - Math.round(((nowTime - lastTime) / 1000)) // 计算还剩多少秒倒计时
        } else {
          sendCodeTime[SEND_CODE_TIME_KEY] = nowTime
          Storage.set(SEND_CODE_TIME, sendCodeTime) // 本次的发送验证码倒计时添加入缓存对象
        }
      } else {
        sendCodeTime = {
          [SEND_CODE_TIME_KEY]: nowTime
        }
        // 保存这次发送验证码的时间
        Storage.set(SEND_CODE_TIME, sendCodeTime)
      }

      this.leftSeconds = durationTime
      const countDown = () => {
        this.clock = setTimeout(() => {
          this.leftSeconds -= 1
          if (this.leftSeconds <= 0) {
            // this.canGetCode = true
            sendCodeTime[SEND_CODE_TIME_KEY] = null // 倒计时结束，清掉获取验证码倒计时缓存
            Storage.set(SEND_CODE_TIME, sendCodeTime)
          } else {
            countDown()
          }
        }, 1000)
      }
      countDown()
    },

    // 清除定时器
    clearTime() {
      clearTimeout(this.clock) // 清除
      this.clock = null
    },
    // 确认支付
    async confirmPay() {
      try {
        if (!this.packageId) return this.$message.info('请选择套餐')
        await this.$refs.formData.validate()
        const params = {
          packageId: this.packageId,
          mobile: this.mobile,
          verifyCode: this.formData.code,
          orderNo: this.orderNo,
          businessType: PURCHASE_PACKAGE_BUSINESS_TYPE.ORDER_PRICE.id
        }
        const res = await orderApi.purchaseContactMeal(params)
        if (res) {
          this.$message.success('购买成功')
          this.$emit('success')
          this.handleClose()
        } else {
          this.$message.success('购买失败')
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log(error)
      }
    }
  }
}
</script>
