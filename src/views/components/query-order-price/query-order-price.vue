<!-- 查询订单价格 -->

<style lang="scss" scoped>
.query-order-price-button {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;

  .btn-icon {
    margin-right: 8px;
  }

  .new-tag {
    position: absolute;
    top: -20px;
    right: -22px;

    @include new-icon;
  }
}
</style>

<template>
  <div class="query-order-price">
    <slot name="button">
      <el-button
        type="primary"
        width="112"
        :height="String(height)"
        @click="init"
      >
        <slot>
          <div class="query-order-price-button">
            <icon type="chengjie-chaxun" class="btn-icon" />
            <span>查看价格</span>
          </div>
        </slot>
      </el-button>
    </slot>
    <OrderPriceListDialog ref="orderPriceListDialogRefs" />
  </div>
</template>

<script>
// import CollectionDialog from './collection-dialog.vue'
import OrderPriceListDialog from './order-price-list-dialog.vue'

export default {
  name: 'query-order-price',
  components: {
    OrderPriceListDialog
  },
  props: {
    height: {
      type: [Number, String],
      default: 30
    }
  },
  methods: {
    init() {
      this.$refs.orderPriceListDialogRefs && this.$refs.orderPriceListDialogRefs.init()
    }
  }
}
</script>
