<style lang="scss" scoped>
.mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: auto;
  margin: 0;
  background: rgb(0 0 0 / 50%);

  .container {
    position: relative;
    z-index: 1111;
    color: black;

    .close {
      position: absolute;
      top: -20px;
      right: -20px;
      border-radius: 50%;
      font-size: 40px;
      color: rgb(255 255 255 / 50%);
      background: none;
      cursor: pointer;
    }
  }
}

.player {
  width: 70vw;
  height: auto;

  &:focus {
    outline: none;
  }
}
</style>

<template>
  <div v-show="show" class="mask" @click.self="hide">
    <div class="container">
      <video
        id="player"
        ref="player"
        class="player"
        controls
        :poster="video.imgs"
        :src="video.video"
      />
      <i class="el-icon-circle-close close" title="关闭" @click="hide" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'player',
  props: {
    show: {
      type: Boolean,
      default: true
    },
    video: {
      type: Object,
      default: () => ({
        video: 'https://cdn.sdpjw.cn/media/LeafletRecognition.mp4',
        imgs: 'https://cdn.sdpjw.cn/static/shenduBihu/publicity/download.png',
        text: '下载安装演示',
      })
    }
  },
  data() {
    return {

    }
  },
  watch: {
    show(news) {
      if (!news) {
        let p = this.$refs.player
        p.pause()
        p.currentTime = 0
      }
    }
  },
  methods: {
    hide() {
      this.$emit('update:show', false)
    }
  }
}
</script>
