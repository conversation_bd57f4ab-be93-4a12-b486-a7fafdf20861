<!-- 订单列表表格左上角订单类型标签 -->

<template>
  <div>
    <!--
      <el-tooltip
      v-if="row.draftType"
      popper-class="custom-tooltip"
      placement="top"
      :disabled="true"
      content="新一代票据由票方发布时选择是否接受拆分接单，若接受拆分且拆分接单后，订单将按拆分金额进行支付和背票。"
      >
      <div class="order-tag" :style="`left: ${handleOrderTypeLabelPosition0()}`">
      <div class="g-tag-xinpiao">
      新票
      </div>
      </div>
      </el-tooltip>
    -->

    <div class="order-tag" :style="`left: ${handleOrderTypeLabelPosition1()}`">
      <el-tooltip
        v-if="row.fastTrade || row.radarType"
        :disabled="showTip || !row.fastTrade"
        content="票在户，免确认，效率高"
        placement="top"
      >
        <div v-if="row.fastTrade" class="g-tag-jisu">
          极速
        </div>
        <div v-if="row.radarType" class="g-tag-zidong">
          自动
        </div>
      </el-tooltip>
      <!--
        <el-tooltip
        v-if="isMarket && row.hiddenFlag"
        content="该票面信息不全，若您有需要请联系客户经理"
        placement="top"
        >
        <div class="g-tag-yin">
        隐
        </div>
        </el-tooltip>
      -->
    </div>
  </div>
</template>

<script>
export default {
  name: 'order-type-label',
  props: {
    row: {
      type: [Array, Object],
      default: () => ({})
    },
    showTip: { // 是否禁用
      type: Boolean,
      default: false
    },
    isMarket: { // 是否是智能助手，只有智能助手页面才显示“隐”标签
      type: Boolean,
      default: false
    },
  },
  methods: {
    handleOrderTypeLabelPosition0() {
      return `-${document.getElementsByClassName('fast-column')[0].offsetLeft || 0}px`
    },
    handleOrderTypeLabelPosition1() {
      return '0px'
    }
  }
}
</script>
