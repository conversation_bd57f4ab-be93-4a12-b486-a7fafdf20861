<!-- 警告文本 -->
<style lang="scss" scoped>
.warn-content {
  display: flex;
  border-radius: 2px;
  padding: 12px 16px;
  width: 100%;
  font-size: 14px;
  color: $color-text-primary;
  line-height: 24px;
}

.blue-warn-content {
  @extend .warn-content;

  background-color: #F0F4FF;
}

.red-warn-content {
  @extend .warn-content;

  background-color: #FFEFEF;
}

.info-circle-icon {
  margin-right: 8px;
  font-size: 18px;
}

.red-icon {
  @extend .info-circle-icon;

  color: $color-warning;
}

.blue-icon {
  @extend .info-circle-icon;

  color: $--color-primary;
}
</style>

<template>
  <div :class="classType === 'red' ? 'red-warn-content' : 'blue-warn-content'">
    <i v-if="classType === 'red'" class="elicon elicon-info-circle red-icon" />
    <i v-else class="elicon elicon-info-circle blue-icon" />
    <div class="content"><slot /></div>
  </div>
</template>

<script>
export default {
  name: 'warn-content',
  props: {
    classType: {
      type: String,
      default: 'red' // 默认红色red, 蓝色blue
    }
  }
}
</script>
