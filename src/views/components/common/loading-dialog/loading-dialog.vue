<!-- 加载中弹窗 -->
<style lang="scss" scoped>
::v-deep .loading-dialog {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  margin-top: 0 !important;

  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    background: $color-FFFFFF;
  }
}

.dialog-main {
  display: flex;
  min-height: 120px;

  .loading-icon {
    position: relative;
    width: 24px;
    height: 24px;

    @include flex-cc;

    .bg {
      position: absolute;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      background-size: 100% 100%;
      background-image: url("https://oss.chengjie.red/web/imgs/auto-orders/rotate-green-min.png");

      @include animation-rotate;
    }

    .icon {
      font-size: 10px;
      color: $font-color;
    }
  }

  .dialog-content {
    margin-left: 16px;
  }

  .dialog-title {
    font-size: 18px;
    font-weight: 600;
    line-height: 24px;
  }

  .dialog-text {
    margin-top: 8px;
    font-size: 16px;
    line-height: 24px;
  }
}
</style>

<template>
  <el-dialog
    :width="`${width}px`"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :show-close="false"
    :append-to-body="true"
    :close-on-press-escape="false"
    custom-class="loading-dialog"
  >
    <div class="dialog-main">
      <div class="loading-icon">
        <div class="bg" />
        <div class="icon-box">
          <icon class="icon" type="chengjie-lightning" />
        </div>
      </div>
      <div class="dialog-content">
        <div class="dialog-title">{{ title }}</div>
        <div class="dialog-text">{{ content }}</div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'loading-dialog',
  props: {
    visible: Boolean, // 显示弹窗
    // 弹窗宽度
    width: {
      type: String,
      default: '490'
    },

    // 标题
    title: {
      type: String,
      default: '加载中'
    },

    // 内容
    content: {
      type: String,
      default: '请耐心等待...'
    }
  },

  data() {
    return {
      dialogVisible: false, // 显示弹窗
    }
  },

  watch: {
    // 监听传值
    visible: {
      handler(val) {
        this.dialogVisible = val
      },
      immediate: true,
    }
  }
}
</script>
