<!-- 预览视频弹窗 -->
<style lang="scss" scoped>
  ::v-deep .preview-video-dialog {
    .video-box {
      height: 600px;

      .video {
        width: 100%;
        height: 100%;
      }
    }
  }
</style>

<template>
  <main class="preview-video-dialog">
    <el-dialog
      width="800px"
      title="视频预览"
      :visible.sync="dialogVisible"
      :append-to-body="true"
      :close-on-click-modal="false"
      custom-class="preview-video-dialog"
      :destroy-on-close="true"
    >
      <div class="video-box">
        <video class="video" :src="url" controls />
      </div>
    </el-dialog>
  </main>
</template>

<script>
export default {
  name: 'preview-video-dialog',
  props: {
    visible: Boolean, // 显示弹窗
  },

  data() {
    return {
      dialogVisible: false, // 显示弹窗
      url: '', // 视频url
    }
  },

  methods: {
    init(data) {
      this.dialogVisible = true
      this.url = data.url
    },
  }
}
</script>
