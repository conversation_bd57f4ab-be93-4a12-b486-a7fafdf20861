<!-- 联系客服弹窗 -->
<style lang="scss" scoped>
.failed-open {
  ::v-deep {
    .el-button--primary {
      margin-top: 16px;
      font-size: 18px;
      font-weight: 600;
    }
  }
}

.red-high-light {
  font-weight: 600;
  color: $--color-font-main;
}

.main {
  padding: 24px 0;
  width: 100%;
  text-align: center;
  color: $color-text-primary;
  background-color: $color-FFFFFF;
}

.name {
  margin-bottom: 4px;
  height: 22px;
  font-size: 16px;
  font-weight: 600;
  color: $--color-primary;
  line-height: 22px;
}

.dec {
  margin-bottom: 16px;
  font-size: 16px;
  line-height: 22px;
}

.qrcode {
  display: flex;
  justify-content: center;
  margin-top: 24px;

  .qrcode-item {
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    line-height: 24px;

    &:first-child {
      margin-right: 64px;
    }

    img {
      margin-bottom: 8px;
      width: 140px;
      height: 140px;
      object-fit: cover;
      vertical-align: top;
    }
  }
}

.center {
  text-align: center;
}
</style>

<template>
  <el-dialog
    class="failed-open"
    title="联系客服"
    :visible.sync="visible"
    width="596px"
    append-to-body
  >
    <div class="main">
      <div v-if="title" class="name">{{ title }}</div>
      <div class="dec">
        <div v-if="subTitle">{{ subTitle }}</div>
        <template v-else>
          <div>如有疑问可扫码联系客服经理</div>
          <div>请打开微信扫一扫添加</div>
        </template>
      </div>
      <div class="qrcode">
        <div class="qrcode-item">
          <img :src="configDefault.customerManagerQr1">
          <div class="center">客服一号</div>
        </div>
        <div class="qrcode-item">
          <img :src="configDefault.customerManagerQr2">
          <div class="center">客服二号</div>
        </div>
      </div>
      <el-button
        type="primary"
        width="120"
        height="40"
        @click="visible = false"
      >
        我知道了
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'contact-service',
  props: {
    title: String,
    subTitle: String
  },
  data() {
    return {
      visible: false, // 弹窗是否打开
    }
  },
  computed: {
    // 企业信息
    corpInfo() {
      return this.$store.state?.user?.corpInfo || {}
    }
  },
  methods: {
    init() {
      this.visible = true
    }
  }
}
</script>
