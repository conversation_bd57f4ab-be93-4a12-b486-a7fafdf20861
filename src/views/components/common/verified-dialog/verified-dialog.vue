
<template>
  <div>
    <!-- 开户流程 -->
    <InvalidJdCertificate ref="invalidJdCertificate" />
  </div>
</template>

<script>
import InvalidJdCertificate from '@/views/components/corp-info-expired-warning/components/invalid.vue' // 企业认证失效警告
import { APPLICATION_STATUS } from '@/constants/open-account'

export default {
  name: 'verified-dialog',
  components: {
    InvalidJdCertificate,
  },
  methods: {

    init() {
      let { corpInfo } = this.$store.state?.user || {}
      // corpStatus  //企业状态：0-未实名，1-实名中，2-已实名，3-实名失效
      // newestCorpOpenInfoApplyStatus,  //企业最新开户记录申请单状态
      // eslint-disable-next-line no-magic-numbers
      if (corpInfo.corpStatus === 2 && corpInfo.newestCorpOpenInfoApplyStatus === APPLICATION_STATUS.HAVE_BEEN_THROUGH.id) {
        this.$refs.invalidJdCertificate.open()
      }
    }
  }
}
</script>
