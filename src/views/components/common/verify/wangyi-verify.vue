<!-- 网易人机滑块验证 -->
<style lang="scss">
.captcha-box {
  .yidun.yidun--light.yidun--float .yidun_panel {
    z-index: 9999 !important;
  }
}
</style>

<template>
  <div class="captcha-box">
    <div :id="ncId" :style="ncType === 'contact' ? 'height: 100%' : ''" />
  </div>
</template>

<script>
import $initNECaptchaWithFallback from 'initNECaptchaWithFallback'
export default {
  name: 'captcha',
  props: {
    ncwidth: {
      type: String,
      default: '416px'
    },
    mode: {
      type: String,
      default: 'float'
    },
    // 验证类型 login：登录Key contact：获取联系人Key
    ncType: {
      type: String,
      default: 'login'
    },
  },
  data() {
    return {
      ncId: `captcha-${new Date().getTime()}`, // 动态ID
      captchaIns: null, // 验证实例
      captchaIdMap: {
        login: '0b15a7966d4c4005bc435b0a354d64df', // 登录Key
        contact: '7b1cd0d945b84d7797613b8fb62c5e28', // 获取联系人Key
      }
    }
  },
  mounted() {
    this.$nextTick().then(() => {
      this.init()
    })
  },
  methods: {
    init() {
      const { ncId, ncwidth, mode } = this
      // 实例化captcha
      // 若使用降级方案接入
      // initNECaptcha 替换成 initNECaptchaWithFallback
      // eslint-disable-next-line no-undef
      $initNECaptchaWithFallback(
        {
          captchaId: this.captchaIdMap[this.ncType], // 验证码业务ID
          element: `#${ncId}`, // 容器元素或容器元素选择器
          width: ncwidth, // 验证按钮宽度，推荐使用宽度 260px-400px。类型为 String 时，支持后缀 px、rem、%，类型为 Number 时，内部会将其转换成 px 单位的值。当值为 "auto" 时，其宽度与容器元素宽度一致。mode 为 "popup" 时，百分比单位无效
          apiVersion: 2,
          mode, // 验证码模式，有三种模式可选："float"（触发式）、"embed"（嵌入式）、"popup"（弹出式），其中智能无感知类型不支持 "embed" 模式
          onVerify: (err, data) => {
          // 当验证失败时, 内部会自动 refresh 方法, 无需手动再调用一次
            if (err) return
            // 以下为业务侧逻辑
            this.$emit('verify-success', data)
          },
          onClose: () => { // 验证码关闭
            this.$emit('verify-close')
          },
        },
        this.ncOnload,
        this.ncOnerror
      )
    },

    // 验证码初始化成功回调 返回初始化成功后的验证码实例
    ncOnload(instance) {
      this.captchaIns = instance
    },

    // 验证码初始化失败回调
    ncOnerror(err) {
      // 初始化失败后触发该函数, err对象描述当前错误信息
      // eslint-disable-next-line no-console
      console.log(err)
    },
    // 刷新验证码
    ncRefresh() {
      this.captchaIns && this.captchaIns.refresh()
    },
    // 显示弹出式滑块
    popupVerify() {
      this.captchaIns.verify()
    }
  }
}
</script>
