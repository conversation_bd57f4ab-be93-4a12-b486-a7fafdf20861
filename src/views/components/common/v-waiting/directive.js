/* eslint-disable no-underscore-dangle */
// 监听 ajax请求，自动为匹配到的 dom 元素添加点击约束
import { isContainIn } from '@/common/js/dom'
import Vue from 'vue'
import VWaiting from './v-waiting.vue' // 引入loading模板
const Mask = Vue.extend(VWaiting)
const waitingDirective = {}

/**
 *  使用例子
 * @param {string} template    文案显示模板  spinner/white-background 默认值spinner
 * @param {string} text        加载文案
 * @param {string} spinner     自定义加载图标类名
 * @param {string} background  遮罩背景色
 * @param {string} customClass 自定义类名
 * @example <div v-waiting="['get::/test/users?pageIndex=2', 'get::/test/users?pageIndex=1']" @click="test">点击</div>
 * @example <div v-waiting="'get::http://www.baidu.com/mock/50/test/users?pageIndex=2'" @click="test1">点击</div>
 * @example
            <div
              v-waiting="'get::http://www.baidu.com/mock/50/test/users?pageIndex=2'"
              waiting-template="white-background"
              waiting-text="拼命加载中..."
              waiting-spinner="el-icon-loading"
              waiting-background="rgba(0, 0, 0, 0.8)"
              waiting-customClass="custom-class"
              @click="test2"
            >
              点击
            </div>
  */
// 样式是参考element 的Loading 组件 https://element.eleme.cn/#/zh-CN/component/loading，做一些二次开发修改

// 兼容转换传入的函数，转化为 URL 字符串
function cmptFunStrToUrl(targetList) {
  targetList = Array.isArray(targetList) ? targetList : [targetList] // 兼容转化为数组
  return targetList.map(targetItem => {
    if (typeof targetItem === 'function') { // 如果传入的是函数
      const funStr = targetItem.toString() // 将函数转化为字符串，进行解析
      if (funStr === 'function () { [native code] }') {
        throw new Error(`点击约束，因 Function.prototype.toString 限制，this 被 bind 修改过的函数无法解析, 请显式输入 url 字符串。 ${targetItem.name}，详情可参考 https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Function/toString`)
      }
      const [, method, apiURL] = (funStr.match(/\.(get|post|delete|put|patch)\(['"]([^'"]+)/) || [])
      // get::http://www.baidu.com/mock/50/test/users?pageIndex=2
      return `${method}::${apiURL}`
    }
    return targetItem
  })
}

waitingDirective.install = v => {
  v.directive('waiting', {
    bind: (targetDom, binding) => {
    // 注入全局方法
      (function() {
        targetDom?.__vue__ && (targetDom.__vue__.vWaitingValue = binding.value) // 暴露绑定值
        // 如果已经重置过，则不再进入。解决开发时局部刷新导致重新加载问题
        if (window.hadResetAjaxForWaiting) {
          return
        }
        window.hadResetAjaxForWaiting = true
        window.waitingAjaxMap = {} // 接口映射 {'get::http://www.baidu.com/mock/50/test/users?pageIndex=1': dom}

        // 保存一份原生的 XMLHttpRequest 对象 和 open 方法
        let OriginXHR = window.XMLHttpRequest
        // let originOpen = OriginXHR.prototype.open

        // 自定义追加到dom的类名
        let customClassName = 'v-waiting-loading'

        // 重置 XMLHttpRequest
        window.XMLHttpRequest = function() {
          let targetDomList = [] // 存储 ajax 请求，影响到的 dom 元素

          let realXHR = new OriginXHR() // 实例化出一份新的 XMLHttpRequest对象，来进行重载
          let startTime = 0 // 请求开始发起时间
          let endTime = 0 // 请求完成时间
          let originOpen = realXHR.open
          realXHR.open = function(method, url, async) {
            startTime = new Date().getTime()
            Object.keys(window.waitingAjaxMap).forEach(key => {
              let [targetMethod, type, targetUrl] = key.split('::')
              if (!targetUrl) {
                targetUrl = type
              }

              if (targetMethod.toLocaleLowerCase() === method.toLocaleLowerCase() && (url.indexOf(targetUrl) > -1 || new RegExp(targetUrl).test(url))) {
                targetDomList = [...window.waitingAjaxMap[key], ...targetDomList]
                window.waitingAjaxMap[key].forEach(dom => {
                  const isButton = dom.tagName === 'BUTTON'
                  // 如果是通过点击事件触发的，且当前组件是按钮，则仅当按钮为点击事件触发的对应按钮时才出现 loading
                  // 防止列表中所有按钮同时 loading
                  if (isButton && window.event && window.event.type === 'click' && (window.event.target !== dom && !isContainIn(window.event.target, dom))) {
                    return
                  }
                  // 如果当前是一个 vue 组件实例对应的 dom，且 data 中存在 vWaiting 属性，则将其设为 true，组件中则自己根据 vWaiting 属性来实现 loading 效果
                  const vm = dom.__vue__
                  if (vm && vm._data && typeof vm._data.vWaiting === 'boolean') {
                    vm.vWaiting = true
                    // 发起请求的是创建一个定时器，10秒后强制清除loading状态
                    setTimeout(() => {
                      vm.vWaiting = false
                    // eslint-disable-next-line no-magic-numbers
                    }, 10000)
                  } else if (!dom.classList.contains(customClassName)) { // 判断dom的class是否已经挂载v-waiting-loading
                    dom.classList.add(customClassName) // 给 dom 添加 v-waiting-loading类做标记
                    const templateExr = dom.getAttribute('waiting-template')
                    const textExr = dom.getAttribute('waiting-text')
                    const spinnerExr = dom.getAttribute('waiting-spinner')
                    const backgroundExr = dom.getAttribute('waiting-background')
                    const customClassExr = dom.getAttribute('waiting-custom-class')
                    const mask = new Mask({
                      el: document.createElement('div'),
                      data: {
                        visible: true,
                        template: (vm && vm[templateExr]) || templateExr || 'spinner',
                        text: (vm && vm[textExr]) || textExr,
                        spinner: (vm && vm[spinnerExr]) || spinnerExr,
                        background: (vm && vm[backgroundExr]) || backgroundExr,
                        customClass: (vm && vm[customClassExr]) || customClassExr,
                        fullscreen: !!binding.modifiers.fullscreen
                      }
                    })
                    dom._instance = mask
                    dom.appendChild(mask.$el)

                    if (window.getComputedStyle(dom).position === 'static') { // 如果是 static 定位，则修改为 relative，为伪类的绝对定位做准备
                      dom.style.position = 'relative'
                    }
                    // const division = 'sdpjw.cn'
                    // // 需要waf拦截的API集合
                    // const blackList = []
                    // if (url.indexOf(division) > -1) {
                    //   const api = url.split(division)[1] || ''
                    //   // 判断当前请求是不是需要被waf拦截的接口 之所以不用includes 是有的接口后面会有参数的，用includes会匹配不上
                    //   for (let i = 0; i < blackList.length; i++) {
                    //     const wafApi = blackList[i]
                    //     // eslint-disable-next-line max-depth
                    //     if (wafApi.indexOf(api) > -1) {
                    //       // 10S后自动停止loading 防止接口被阿里云拦截  出现不返回realXHR.readyState = 4的情况
                    //       setTimeout(() => {
                    //         if (dom._instance) {
                    //           dom._instance.visible = false
                    //         }
                    //       }, 10 * 1000)
                    //       break
                    //     }
                    //   }
                    // }
                  }
                  dom.waitingAjaxNum = dom.waitingAjaxNum || 0 // 不使用 dataset，是应为 dataset 并不实时，在同一个时间内，上一次存储的值不能被保存
                  dom.waitingAjaxNum++
                })
              }
            })
            // 使用原生的 XMLHttpRequest open操作
            originOpen.call(realXHR, method, url, async)
          }

          // 监听加载完成，清除 waiting
          // eslint-disable-next-line no-empty-function
          realXHR.addEventListener('loadend', () => {}, false)
          // 监听onreadystatechange方法 清除 waiting
          realXHR.onreadystatechange = function() { // realXHR的5种状态都会调用本方法
            if (realXHR.readyState === 4) { // 判断是否为4状态读取服务器响应结束
              endTime = new Date().getTime()
              targetDomList.forEach(dom => {
                dom.waitingAjaxNum--
                if (dom.waitingAjaxNum !== 0) return
                dom.classList.remove(customClassName)
                if (dom.querySelector('.el-loading-mask')) {
                  dom.removeChild(dom.querySelector('.el-loading-mask'))
                  dom._instance && dom._instance.$destroy()
                }
                const vm = dom.__vue__
                if (vm && vm._data && typeof vm._data.vWaiting === 'boolean') {
                  const diff = endTime - startTime // 接口响应结束/开始时间差
                  // eslint-disable-next-line no-magic-numbers
                  if (diff > 500) {
                    vm.vWaiting = false
                  } else {
                    setTimeout(() => {
                      vm.vWaiting = false
                    }, 500 - diff)
                  }
                }
              })
            }
          }
          return realXHR
        }
      })();

      (() => {
        if (!document.getElementById('v-waiting')) {
          let code = `
          .v-waiting-loading {
            pointer-events: none;
            /*cursor: not-allowed; 与 pointer-events: none 互斥，设置 pointer-events: none 后，设置鼠标样式无效 */
          }
          `
          let style = document.createElement('style')
          style.id = 'v-waiting'
          style.type = 'text/css'
          style.rel = 'stylesheet'
          style.appendChild(document.createTextNode(code))
          let head = document.getElementsByTagName('head')[0]
          head.appendChild(style)
        }
      })()

      // 添加需要监听的接口，注入对应的 dom
      const targetUrlList = cmptFunStrToUrl(binding.value)
      targetUrlList.forEach(targetUrl => {
        window.waitingAjaxMap[targetUrl] = [targetDom, ...(window.waitingAjaxMap[targetUrl] || [])]
      })
    },

    // 参数变化
    update: (targetDom, binding) => {
      if (binding.oldValue !== binding.value) {
        const preTargetUrlList = cmptFunStrToUrl(binding.oldValue)
        preTargetUrlList.forEach(targetUrl => {
          const index = (window.waitingAjaxMap[targetUrl] || []).indexOf(targetDom)
          index > -1 && window.waitingAjaxMap[targetUrl].splice(index, 1)
        })

        // 添加需要监听的接口，注入对应的 dom
        const targetUrlList = cmptFunStrToUrl(binding.value)
        targetUrlList.forEach(targetUrl => {
          window.waitingAjaxMap[targetUrl] = [targetDom, ...(window.waitingAjaxMap[targetUrl] || [])]
        })
      }
    },

    // 指令被卸载，消除消息监听
    unbind: (targetDom, binding) => {
      targetDom._instance && targetDom._instance.$destroy()
      const targetUrlList = cmptFunStrToUrl(binding.value)
      targetUrlList.forEach(targetUrl => {
        const index = window.waitingAjaxMap[targetUrl].indexOf(targetDom)
        index > -1 && window.waitingAjaxMap[targetUrl].splice(index, 1)
        if (window.waitingAjaxMap[targetUrl].length === 0) {
          delete window.waitingAjaxMap[targetUrl]
        }
      })
    }
  })
}

export default waitingDirective
