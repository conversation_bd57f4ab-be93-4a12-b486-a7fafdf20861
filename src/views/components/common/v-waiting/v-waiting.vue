<style lang="scss" scoped>
@mixin b($block) {
  .#{"el-" + $block} {
    @content;
  }
}

@mixin m($modifier) {
  $selector: &;
  $currentSelector: "";

  @each $unit in $modifier {
    $currentSelector: #{$currentSelector + & + "--" + $unit + ","};
  }

  @at-root {
    #{$currentSelector} {
      @content;
    }
  }
}

@mixin when($state) {
  @at-root {
    &.#{"is-" + $state} {
      @content;
    }
  }
}

@include b(loading-parent) {
  @include m(relative) {
    position: relative !important;
  }

  @include m(hidden) {
    overflow: hidden !important;
  }
}

@include b(loading-mask) {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2000;
  margin: 0;
  background-color: rgb(255 255 255 / 40%);
  transition: opacity .3s;

  @include when(fullscreen) {
    position: fixed;

    .el-loading-spinner {
      transform: translateY(-50%);

      .circular {
        width: 50px;
        height: 50px;
      }
    }
  }
}

@include b(loading-spinner) {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  margin-top: 0;
  width: 100%;
  text-align: center;

  .el-loading-text {
    margin: 3px 0;
    font-size: 16px;
    color: $--color-primary;
  }

  .circular {
    width: 42px;
    height: 42px;
    animation: loading-rotate 2s linear infinite;
  }

  .path {
    animation: loading-dash 1.5s ease-in-out infinite;
    stroke-dasharray: 90, 150;
    stroke-dashoffset: 0;
    stroke-width: 2;
    stroke: $--color-primary;
    stroke-linecap: round;
  }

  i {
    font-size: 24px;
    color: $--color-primary;
  }
}

@include b(loading-white-background) {
  position: absolute;
  top: 50%;
  left: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 2px;
  padding: 9px 16px;
  text-align: center;
  background-color: #FFFFFF;
  box-shadow: 0 2px 4px rgb(0 0 0 / 12%), 0 0 6px rgb(0 0 0 / 4%);
  transform: translateX(-50%) translateY(-50%);

  .el-loading-text {
    margin: 0 4px;
    font-size: 16px;
    color: $color-text-primary;
  }

  .circular {
    width: 36px;
    height: 36px;
    animation: loading-rotate 2s linear infinite;
  }

  .path {
    animation: loading-dash 1.5s ease-in-out infinite;
    stroke-dasharray: 90, 150;
    stroke-dashoffset: 0;
    stroke-width: 2;
    stroke: $--color-primary;
    stroke-linecap: round;
  }

  i {
    font-size: 24px;
    color: $--color-primary;
  }
}

.el-loading-fade-enter,
.el-loading-fade-leave-active {
  opacity: 0;
}

@keyframes loading-rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loading-dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -40px;
  }

  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -120px;
  }
}
</style>

<template>
  <transition name="el-loading-fade" @after-leave="handleAfterLeave">
    <div
      v-show="visible"
      class="el-loading-mask"
      :style="{ backgroundColor: background || '' }"
      :class="[customClass, { 'is-fullscreen': fullscreen }]"
    >
      <div v-if="template === 'spinner'" class="el-loading-spinner">
        <svg v-if="!spinner" class="circular" viewBox="25 25 50 50">
          <circle
            class="path"
            cx="50"
            cy="50"
            r="20"
            fill="none"
          />
        </svg>
        <i v-else :class="spinner" />
        <p v-if="text" class="el-loading-text">{{ text }}</p>
      </div>
      <div v-else-if="template === 'white-background'" class="el-loading-white-background">
        <svg v-if="!spinner" class="circular" viewBox="25 25 50 50">
          <circle
            class="path"
            cx="50"
            cy="50"
            r="20"
            fill="none"
          />
        </svg>
        <i v-else :class="spinner" />
        <p v-if="text" class="el-loading-text">{{ text }}</p>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  name: 'loading',
  data() {
    return {
      template: 'spinner',
      text: null,
      spinner: null,
      background: null,
      fullscreen: true,
      visible: false,
      customClass: ''
    }
  },

  methods: {
    handleAfterLeave() {
      this.$emit('after-leave')
    },
    setText(text) {
      this.text = text
    }
  }
}
</script>
