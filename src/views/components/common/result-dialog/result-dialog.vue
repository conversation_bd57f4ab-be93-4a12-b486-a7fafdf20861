<!-- 批量操作结果弹窗 -->
<style lang="scss" scoped>
::v-deep .result-dialog {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  margin-top: 0 !important;

  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    background: $color-FFFFFF;
  }
}

.dialog-main {
  display: flex;
  margin-bottom: 24px;

  .icon {
    font-size: 24px;

    &.el-icon-success {
      color: $font-color;
    }

    &.el-icon-warning {
      color: $--color-warning;
    }

    &.el-icon-error {
      color: $--color-primary;
    }
  }

  .dialog-content {
    margin-left: 16px;
    flex: 1;
  }

  .title {
    margin-bottom: 8px;
    font-size: 18px;
    font-weight: bold;
    line-height: 24px;
  }
}

.red-high-light {
  margin: 0 4px;
  font-weight: bold;
  color: $--color-font-main;
}

.fail-content {
  overflow-y: auto;
  max-height: 480px;
}

.fail-text {
  line-height: 24px;
  margin-bottom: 8px;
  font-size: 16px;
}

.fail-item {
  display: flex;
  line-height: 24px;
  font-size: 16px;

  &:nth-child(2n) {
    margin-bottom: 4px;
  }

  &:last-child {
    margin-bottom: 0;
  }

  .label {
    font-weight: bold;
  }

  .value {
    flex: 1;
  }
}
</style>

<template>
  <el-dialog
    :width="`${width}px`"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :show-close="false"
    :append-to-body="true"
    :close-on-press-escape="false"
    custom-class="result-dialog"
  >
    <div class="dialog-main">
      <div :class="['icon', `el-icon-${iconType}`]" />
      <div class="dialog-content">
        <div class="title">{{ title }}</div>
        <slot name="content">
          <div v-if="errorData" class="fail-text">
            <!-- 回款账户提示兼容处理-sellerAccountUpdateNum，目前只用于批量改价接口反馈提示 -->
            <template v-if="(errorData.successNum || errorData.sellerAccountUpdateNum) && !errorData.failNum">
              <template v-if="errorData.successNum">成功{{ handleStr }}了<span class="red-high-light">{{ errorData.successNum }}</span>张票据。</template>
              <template v-if="errorData.sellerAccountUpdateNum">回款账户修改成功。</template>
            </template>
            <template v-else>
              <template v-if="errorData.successNum">本次{{ handleStr }}<span class="red-high-light">{{ errorData.successNum + errorData.failNum }}</span>张票据，{{ handleStr }}完成<span class="red-high-light">{{ errorData.successNum }}</span>张，</template>
              以下<span class="red-high-light">{{ errorData.failNum }}</span>张{{ handleStr }}失败：
            </template>
          </div>
        </slot>
        <div class="fail-content">
          <slot name="text">
            <template v-if="errorData && errorData.failNum">
              <template v-for="item in errorData[errorMessageKey]">
                <template v-if="!item.executeSuccessFlag">
                  <div :key="item.orderNo" class="fail-item">
                    <div class="label">票号：</div>
                    <div class="value">{{ item.draftNo }}</div>
                  </div>
                  <div :key="`reason${item.orderNo}`" class="fail-item">
                    <div class="label">失败原因：</div>
                    <div class="value">{{ item.failMsg }}</div>
                  </div>
                </template>
              </template>
            </template>
          </slot>
        </div>
      </div>
    </div>
    <div class="dialog-footer">
      <slot name="footer">
        <el-button type="primary" size="large" @click="handleClose">{{ buttonText }}</el-button>
      </slot>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'result-dialog',
  props: {
    visible: Boolean, // 显示弹窗
    // 弹窗宽度
    width: {
      type: String,
      default: '490'
    },

    // 标题
    title: {
      type: String,
      default: '提示'
    },

    // 图标类型
    type: {
      type: String,
      default: 'success'
    },

    // 批量操作类型，例如背书、签收
    handleStr: {
      type: String,
      default: ''
    },

    // 按钮文字
    buttonText: {
      type: String,
      default: '我知道了'
    },

    // 错误信息中对象的key值
    errorMessageKey: {
      type: String,
      default: 'orderTradeVOList'
    }
  },

  data() {
    return {
      dialogVisible: true, // 显示弹窗
      errorData: {}, // 错误内容
    }
  },

  watch: {
    // 监听传值
    visible: {
      handler(val) {
        this.dialogVisible = val
      },
      immediate: true,
    },
    // 图标类型
    type: {
      handler(val) {
        this.iconType = val
      },
      immediate: true,
    }
  },

  methods: {
    init(data) {
      this.dialogVisible = true
      this.errorData = data
      if (data) {
        if (data.failNum && data.successNum) {
          this.iconType = 'warning'
        } else if (data.failNum && !data.successNum) {
          this.iconType = 'error'
        } else {
          this.iconType = 'success'
        }
      }
    },

    // 关闭
    handleClose() {
      this.dialogVisible = false
      this.$emit('close')
    }
  }
}
</script>
