<!-- 软件热更新 -->
<style scoped lang="scss">
.app-update {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2000;
  width: 100%;
  height: 100%;
  background-color: rgba($color: #000000, $alpha: 50%);
}

$width: 388px;

.middle-box {
  position: absolute;
  top: 55%;
  left: 50%;
  border-radius: 4px;
  padding-bottom: 24px;
  width: $width;
  background-color: #FFFFFF;
  transform: translateX(-50%) translateY(-50%);

  .top {
    margin-top: -82px;
    width: $width;
    height: 242px;
    background-size: 100% 100%;
    background-image: url("https://cdn.sdpjw.cn/static/erp/img/update.png");

    &.success {
      background-image: url("https://cdn.sdpjw.cn/static/erp/img/update-success.png");
    }

    &.fail {
      background-image: url("https://cdn.sdpjw.cn/static/erp/img/update-error.png");
    }
  }

  .title {
    padding: 6px 0 10px;
    font-size: 20px;
    font-weight: 600;
    text-align: center;
  }

  .version {
    font-size: 16px;
    text-align: center;
    color: #BFBFBF;
  }

  .content {
    padding: 16px 20px;
    min-height: 150px;

    .row {
      font-size: 16px;
      color: #161616;
      line-height: 30px;
    }
  }

  .percent-container {
    position: relative;
  }

  .percent-text {
    position: absolute;
    top: -20px;
    font-size: 16px;
    font-weight: 600;
    white-space: nowrap;
    color: $--color-primary;
    transition: all .1s;
  }

  .progress {
    margin: 20px auto;

    ::v-deep {
      .el-progress-bar {
        margin-right: 0;
        padding-right: 0;
      }

      .el-progress__text {
        display: none;
      }
    }
  }

  .contact-box {
    .text {
      font-size: 16px;
      color: #161616;
      line-height: 22px;
    }

    img {
      display: block;
      margin: 8px auto;
      width: 115px;
    }

    .contact {
      height: 24px;
      font-size: 16px;
      text-align: center;
      color: #8F8F8F;
      line-height: 24px;

      .phone {
        font-weight: bold;
        color: #161616;
      }

      .copy {
        display: inline-block;
        margin-left: 10px;
        font-size: 12px;
        font-weight: 600;
        cursor: pointer;
        vertical-align: top;
        color: $--color-primary;
      }
    }
  }

  .btn {
    display: block;
    margin: 0 auto;
    border-radius: 4px;
    width: 192px;
    height: 38px;
    font-size: 16px;
    font-weight: 600;
    text-align: center;
    color: #FFFFFF;
    background: $--color-primary;
    line-height: 38px;
    cursor: pointer;

    &.disable {
      opacity: .4 !important;
    }

    &:hover {
      opacity: .9;
    }

    &.retry {
      background: #FAAD14;
    }
  }
}
</style>

<template>
  <div v-show="visible" class="app-update">
    <div v-if="info" class="middle-box">
      <div
        class="top"
        :class="{ 'success': percent === 100, 'fail': errorMessage}"
      />
      <div class="title">
        <template v-if="!errorMessage">
          <template v-if="percent === 0">发现新版本</template>
          <template
            v-else-if="percent > 0 && percent < 100"
          >
            更新中...
          </template>
          <template v-else-if="percent === 100">恭喜，更新完成</template>
        </template>
        <template v-else>更新失败，请重试</template>
      </div>
      <!-- <div class="version">v{{ version }} > v{{ info.version }}</div> -->
      <div class="version">更新版本 v{{ info.version }}</div>
      <div class="content">
        <div v-if="!errorMessage" class="percent-container">
          <template v-if="percent === 0">
            <div
              v-for="(item, index) in info.releaseNotes.split('\n')"
              :key="item"
              class="row"
            >
              {{ `${index + 1}. ${item}` }}
            </div>
          </template>
          <template v-else-if="percent > 0 && percent < 100">
            <div class="percent-text" :style="percentTextStyle">{{ percent }}%</div>
            <el-progress
              class="progress"
              :stroke-width="14"
              :percentage="percent"
              :color="colorprimary"
            />
          </template>
          <div v-else-if="percent === 100">
            您已更新至最新版本，点击重启即刻体验。
          </div>
        </div>
        <div v-else class="contact-box">
          <template v-if="updateErrorNumber < 3">{{ reason }}</template>
          <template v-else>
            <div class="text">
              更新失败，您可以主动和您的客户经理联系，获得协助。
            </div>
            <!-- <img :src="configDefault.customerManagerQr1"> -->
            <!-- <div class="contact"> -->
            <!-- 联系方式：<span class="phone">18068830089</span> -->
            <!-- <span v-copy="{value: '18068830089', onSuccess: () => copySuccess(), onError: () => copyError}" class="copy">复制</span> -->
            <!-- </div> -->
          </template>
        </div>
      </div>
      <template v-if="!errorMessage">
        <el-button
          v-if="percent < 100"
          class="btn"
          width="192px"
          height="38px"
          type="primary"
          :loading="isDownloading"
          @click="update"
        >
          马上更新
        </el-button>
        <el-button
          v-else-if="percent === 100"
          class="btn"
          width="192px"
          height="38px"
          type="primary"
          :loading="isInstalling"
          @click="restart"
        >
          立即重启
        </el-button>
      </template>
      <template v-else-if="updateErrorNumber < 3">
        <el-button
          class="btn retry"
          type="primary"
          width="192px"
          height="38px"
          :loading="isRetrying"
          @click="retry"
        >
          重试
        </el-button>
      </template>
    </div>
  </div>
</template>

<script>
import { colorprimary } from '@/common/scss/export.scss' // 引入样式变量
import Storage from '@/common/js/storage'

const UPDATE_ERROR = 'UPDATE_ERROR'
const UPDATE_AVAILABLE = 'UPDATE_AVAILABLE'
const UPDATE_NOT_AVAILABLE = 'UPDATE_NOT_AVAILABLE'
const UPDATE_DOWNLOADING = 'UPDATE_DOWNLOADING'
const UPDATE_DOWNLOADED = 'UPDATE_DOWNLOADED'
const UPDATE_CHECK_UPDATE = 'UPDATE_CHECK_UPDATE'
const RETRY = 'RETRY'
const UPDATE_UPDATE = 'UPDATE_UPDATE'
const QUIT_AND_INSTALL = 'QUIT_AND_INSTALL'

const INCREMENTAL_INSTALLING = 'INCREMENTAL_INSTALLING'

export default {
  name: 'app-update',

  data() {
    return {
      colorprimary,
      // 是否显示进度条
      isShowProgress: false,
      // 获取当前版本号
      version: window.APP_VERSION || '',
      visible: false, // 是否显示更新弹窗
      info: null, // 更新信息
      percent: 0, // 更新进度百分比
      errorMessage: '', // 更新失败信息
      updateErrorNumber: 0, // 更新失败次数
      checkUpdateTime: null, // 轮询检查是否有更新计时器
      checkoutUpdateFlag: false, // 是否检查到有更新
      isDownloading: false, // 是否正在下载更新
      isRetrying: false, // 是否正在重试
      isInstalling: false, // 当前是否正在安装
      random: Math.floor(Math.random() * 6), // 0-5的随机数
    }
  },

  computed: {
    // 失败原因
    reason() {
      if (this.errorMessage.indexOf('signature') > -1) {
        return '失败原因：安装包签名错误。'
      } else if (/(ERR_INTERNET_DISCONNECTED|ERR_CONTENT_LENGTH_MISMATCH)/.test(this.errorMessage)) {
        return '失败原因：网络异常，请检查您的网络。'
      }
      return '更新失败，您可点击下方按钮重试，也可以主动和您的客户经理联系，获得协助。'
    },
    // 进度文字样式
    percentTextStyle() {
      let percent = this.percent - 10
      percent = Math.max(percent, 0)
      percent = Math.min(percent, 85)
      return { left: `${percent}%` }
    }
  },

  created() {
    if (Storage.get(INCREMENTAL_INSTALLING)) {
      Storage.remove(INCREMENTAL_INSTALLING)
      setTimeout(() => {
        this.$message.success('更新成功！')
      }, 1000)
    }
    if (typeof this.$ipc?.on !== 'function') return
    this.addUpdateListen()
    this.checkoutUpdate()

    // FIXME: 修复从 0.3.3 版本升级到 0.3.4 这部分用户开机自启动状态不同步的问题
    setTimeout(() => {
      if (window.APP_VERSION === '0.3.4' && !Storage.get('ADD_AUTO_LAUNCH') && this.$ipc) {
        Storage.set('ADD_AUTO_LAUNCH', true)
        this.$ipc.invoke('SET_START_ON_LOGIN', true)
      }
    }, 30 * 1000)
  },

  methods: {
    // 添加在线更新监听
    addUpdateListen() {
      // 监听当更新发生错误的时候触发
      this.$ipc.on(UPDATE_ERROR, (event, error) => {
        this.isDownloading = false
        this.isInstalling = false
        this.isRetrying = false
        this.errorMessage = error.message
        // 记录更新错误次数在本地
        let updateErrorNumber = sessionStorage.getItem('updateErrorNumber') || 0
        sessionStorage.setItem('updateErrorNumber', ++updateErrorNumber)
        this.updateErrorNumber = updateErrorNumber

        // eslint-disable-next-line no-console
        console.log('监听当更新发生错误的时候触发', error.message)
      })
      // 监听有可用的更新
      this.$ipc.on(UPDATE_AVAILABLE, (event, info) => {
        this.visible = true
        this.info = info
        this.percent = 0
        this.checkoutUpdateFlag = true

        // eslint-disable-next-line no-console
        console.log('有可用的更新，信息如下：', info)
      })
      // 监听 没有可用的更新
      this.$ipc.on(UPDATE_NOT_AVAILABLE, (...params) => {
        // 清除安装失败次数记录
        sessionStorage.removeItem('updateErrorNumber')

        // eslint-disable-next-line no-console
        console.log('UPDATE_NOT_AVAILABLE', ...params)
      })
      // 监听 下载中
      this.$ipc.on(UPDATE_DOWNLOADING, (event, info) => {
        // 可通过 info.percent 获取进度
        this.percent = +info.percent.toFixed(2)
        this.isDownloading = true

        // eslint-disable-next-line no-console
        console.log('安装包下载中：', info)
      })
      // 监听下载完成
      this.$ipc.on(UPDATE_DOWNLOADED, (event, info) => {
        // 接下来通知主进程调用 autoUpdater.quitAndInstall() 方法退出并安装，mac 下由于安装包未签名会报错，windows 应该正常
        this.percent = 100
        this.isDownloading = false

        // eslint-disable-next-line no-console
        console.log('安装包下载完成', info)
      })
      // 监听重试
      this.$ipc.on(RETRY, () => {
        this.isDownloading = true
        this.isRetrying = false
        this.percent = 0
        this.errorMessage = ''
      })
      // 第一次打开软件，由于增量更新会导致渲染进程版本号比主进程高，故需通知主进程当前的版本号
      // this.$ipc.send(UPDATE_VERSION, window.require('electron').app.getVersion())

      // 第一次打开软件，先检查更新一次，之后再checkoutUpdate函数轮询
      this.$ipc.send(UPDATE_CHECK_UPDATE)
    },

    // 主动查询应用是否有更新
    checkoutUpdate() {
      // 如果检查到有更新，则停止轮询
      if (this.checkoutUpdateFlag) {
        return
      }
      this.checkUpdateTime = setTimeout(() => {
        // 当前小时
        let hours = new Date().getHours()
        // 在24点和早上6点之间，随机刷新页面  防止web发布用户没有更新
        if (hours === this.random) {
          if (typeof this.$ipc?.send === 'function') {
            this.$ipc.send('RELOAD_IGNORING_CACHE')
          } else {
            location.reload()
          }
        }
        // 在20点 - 早上6点之间，
        if (hours >= 20 || hours <= 6) {
          this.$ipc.send(UPDATE_CHECK_UPDATE)
        }
        this.checkoutUpdate()
      }, 1000 * 60 * 30)
    },

    // 马上更新
    update() {
      this.isDownloading = true
      this.$ipc.send(UPDATE_UPDATE)
    },

    // 立即重启
    restart() {
      this.isInstalling = true
      if (this.info.type === 'incremental') {
        Storage.set(INCREMENTAL_INSTALLING, true)
      }
      this.$ipc.send(QUIT_AND_INSTALL)
    },

    // 重试
    retry() {
      this.isRetrying = true
      this.$ipc.send(RETRY)
    },

    // 复制联系方式
    copySuccess() {
      this.$message.success('复制成功！')
    }
  }
}
</script>
