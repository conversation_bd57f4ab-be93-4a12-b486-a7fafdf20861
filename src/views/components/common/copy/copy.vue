<!-- 复制组件 -->
<!-- Tips:绑定的值若为请求的数据，组件要用v-if判断一下，有数据了才渲染组件，不然绑定的是undefined，无法实现copy文本功能 -->
<style lang="scss" scoped>
.copy-icon {
  margin-left: 6px;
  font-size: 18px;
  color: $color-text-light;
  cursor: pointer;

  &:hover {
    color: $--color-primary;
  }
}

.text-sp {
  margin-left: 8px;
  text-decoration: underline;
  color: $font-color;
  cursor: pointer;

  &:hover {
    color: $font-color-hover;
  }
}
</style>

<template>
  <span v-if="isText" v-copy="{ value: content, onSuccess, onError }" class="text-sp">
    <slot>复制</slot>
  </span>
  <el-button
    v-else-if="isButton"
    v-copy="{ value: content, onSuccess, onError }"
    type="primary"
    border
    height="30"
  >
    <slot>复制</slot>
  </el-button>
  <icon
    v-else
    v-copy="{ value: content, onSuccess, onError }"
    class="copy-icon"
    :size="size"
    type="chengjie-copy"
  />
</template>

<script>
export default {
  name: 'copy-vue',
  props: {
    content: [String, Number], // 传入需要复制的内容
    success: Function, // 复制成功回调
    error: Function, // 复制失败回调
    // 复制元素是文本，不是icon
    isText: {
      type: Boolean,
      default: false
    },
    isButton: {
      type: Boolean,
      default: false
    },
    size: [String, Number]
  },
  methods: {
    onSuccess() {
      if (this.success) {
        this.success()
      } else {
        this.$message.success('复制成功')
      }
    },
    onError() {
      if (this.error) {
        this.error()
      } else {
        this.$message.error('复制失败，请重试')
      }
    }
  }
}
</script>
