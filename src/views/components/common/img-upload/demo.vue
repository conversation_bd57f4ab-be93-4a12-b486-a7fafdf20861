<!-- 文件上传组件 demo -->
<style lang="scss" scoped>
.img-upload-demo {
  padding: 20px 0;
}

.img-upload {
  width: 430px;

  // height: 80px;
}
</style>

<template>
  <div class="img-upload-demo">
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-position="top"
    >
      <el-form-item label="上传身份证" prop="url">
        <ImgUpload
          v-model="form.url"
          :size-limit="0.5"
          :dir="OSS_DIR.ID_CARD"
          :on-success="onUploadSuccess"
        >
          <div slot="empty">
            <div>点击或拖拽图片至此上传身份证背面</div>
            <div>（不超过1.5M）</div>
          </div>
        </ImgUpload>
      </el-form-item>
      <el-form-item label="上传视频" prop="legalVideoUrl">
        <ImgUpload
          v-model="form.legalVideoUrl"
          accept="video/mp4"
          :is-video="true"
          :size-limit="20"
          :dir="OSS_DIR.ID_CARD"
        >
          <div slot="empty">
            <div>点击或拖拽图片至此上传法人视频影像</div>
            <div>（支持mp4格式，不超过 20 M）</div>
          </div>
        </ImgUpload>
      </el-form-item>
    </el-form>
    <el-button type="primary" @click="submit">提交</el-button>
  </div>
</template>

<script>
import ImgUpload from './img-upload.vue'
import { OSS_DIR } from '@/constant' // 上传文件夹

export default {
  name: 'img-upload-demo',
  components: {
    ImgUpload
  },
  data() {
    return {
      OSS_DIR, // 上传文件夹
      form: {
        url: ''
        // url: 'http://ticket-discern.oss-cn-hangzhou.aliyuncs.com/158/zhengxin-shipiaoUPrIds_1634813653210_%E9%A3%9E%E4%B9%A620210926-151516.png',
      },
      rules: {
        url: [{ required: true, message: '请上传身份证', trigger: 'change' }]
      }
    }
  },
  methods: {
    submit() {
      this.$refs.form.validate(valid => {
        // console.log(this.form)
        if (valid) {
          this.$message.success('提交成功')
        } else {
          this.$message.error('校验失败')
        }
      })
    },
    onUploadSuccess() {
      // console.log('onSuccess', url)
      return new Promise(resolve => {
        setTimeout(() => {
          // console.log('onSuccess done')
          resolve('ocr 识别成功')
          // reject(new Error('ocr 识别失败'))
        // eslint-disable-next-line no-magic-numbers
        }, 3000)
      })
    }
  }
}
</script>
