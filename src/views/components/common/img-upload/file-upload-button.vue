<!-- 按钮上传组件 -->
<style lang="scss" scoped>
.img-upload-input {
  display: none;
}

.file-box-section {
  @include flex-vc;

  .file-box-cont {
    border: 1px solid $color-D9D9D9;
    padding: 0 12px;
    height: 32px;
    line-height: 32px;

    .file-name {
      cursor: pointer;

      &:hover {
        color: $--color-primary;
      }
    }

    .icon-close {
      margin-left: 10px;
      cursor: pointer;
    }
  }
}

.img-upload-empty {
  display: flex;
  align-items: center;
  border-radius: 2px;
  height: 100%;
  text-align: center;

  // background: $color-F4F5F6;
  cursor: pointer;
}

.img-upload-empty-disabled {
  cursor: default;
}

.img-upload-empty-dragover {
  border-width: 2px;
  background: rgba($--color-primary, .1);
}

.img-upload-empty-content {
  flex: 1;
}

.img-upload-icon-plus {
  color: $--color-primary;
}

.img-upload-empty-text {
  font-size: 14px;
  color: $color-text-secondary;
}

.img-upload-preview-container {
  border: 1px solid $color-D9D9D9;
  border-radius: 2px;
  height: 100%;
  background: $color-FFFFFF;
}

.video-box,
.zip-box {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
  cursor: pointer;

  &:hover {
    .preview {
      background: rgb(0 0 0 / 65%);
      opacity: 1;
    }
  }

  .video {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .preview {
    position: absolute;
    top: 50%;
    left: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    line-height: 0;
    opacity: 0;
    transform: translateX(-50%) translateY(-50%);

    .img-upload-preview-icon,
    .img-upload-delete-icon {
      color: $color-FFFFFF;
    }
  }
}

.zip-box {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;

  .img-upload-icon-zip {
    display: inline;
    color: $--color-primary;
  }

  .img-upload-zip-name {
    line-height: 1;
    margin-top: 8px;
    color: $color-text-secondary;
  }
}

.img-upload-preview {
  width: 100%;
  height: 100%;
}

.img-upload-preview-icon,
.img-upload-delete-icon {
  cursor: pointer;

  &:hover {
    color: $--color-primary;
  }
}

.img-upload-delete-icon {
  margin-left: 20px;
}
</style>

<style lang="scss">
.el-form-item.is-error .img-upload-empty {
  border-color: $color-warning;
  background-color: #FFF3F4;
}

.el-form-item.is-error .img-upload-icon-plus {
  color: $color-warning;
}
</style>

<template>
  <div class="img-upload">
    <input
      ref="input"
      :key="inputKey"
      type="file"
      class="img-upload-input"
      :accept="accept"
      @change="handleInputChange"
    >
    <section class="file-box-section">
      <!-- 已上传文件显示内容 -->
      <div v-if="showFile && value" class="file-box-cont">
        <span class="file-name" @click="onPreview">{{ otherFileName }}</span>
        <icon class="icon icon-close" type="chengjie-close" @click="onDel" />
      </div>
      <!-- 未上传文件 -->
      <div
        v-else
        v-loading="uploading"
        class="img-upload-empty"
        :class="{
          'img-upload-empty-dragover': dragover,
          'img-upload-empty-disabled': !uploadable,
        }"
        @click="handleClick"
      >
        <div class="img-upload-empty-content">
          <div class="img-upload-empty-text">
            <slot name="empty">{{ emptyText }}</slot>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import emitter from '@/mixins/emitter'
import { upload } from '@/utils/oss'
import { OSS_DIR } from '@/constant'
// import PreviewVideoDialog from '@/views/components/common/preview-video-dialog/preview-video-dialog.vue' // 视频预览组件

export default {
  name: 'file-upload-button',

  mixins: [emitter],

  props: {
    // 图片的 src，没有图片时为空字符串
    value: {
      type: String,
      require: true
    },
    // 未上传文件时的提示文字，也可用 empty slot
    emptyText: {
      type: String,
      default: '上传文件'
    },
    disabled: Boolean, // 是否禁用
    // 接受的文件类型
    accept: {
      type: String,
      // https://developer.mozilla.org/en-US/docs/Web/Media/Formats/Image_types
      default: '.xlsx,.pdf'
    },
    beforeUpload: Function, // 上传前的回调函数, 返回 false 可以阻止上传
    onSuccess: Function, // 上传成功的回调函数
    sizeLimit: Number, // 限制的文件大小，单位为 M
    sizeLimitMessage: String, // 超出限制的文件大小时的提示
    // 上传文件夹
    dir: {
      required: true,
      type: String,
      validator(dir) {
        const isValid = Object.values(OSS_DIR).some(d => d === dir)
        if (process.env.NODE_ENV === 'development' && !isValid) {
          const keys = Object
            .keys(OSS_DIR)
            .map(d => `OSS_DIR.${d}`)
            .join('、')
          // eslint-disable-next-line no-console
          console.error(`[img-upload]dir 属性必须为 ${keys} 其中一个`)
        }
        return isValid
      }
    },
    // 删除图片文案
    delTip: {
      type: String,
      default: '确定要删除吗？'
    },
    // 上传成功的提示文案
    successMsg: {
      type: String,
      default: ''
    },
    // 是否显示上传的文件
    showFile: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      dragover: false, // 是否鼠标悬浮
      tempUrl: '', // 通过 URL.createObjectURL 创建的临时 url
      uploading: false, // 是否正在上传
      inputKey: 0, // input 的 key
      otherFileName: '', // 其他文件的名称
    }
  },

  computed: {
    // 预览链接
    // previewUrl() {
    //   return this.value || this.tempUrl
    // },
    // 当前是否可上传
    uploadable() {
      return !this.disabled && !this.uploading
    }
  },

  watch: {
    value(val) {
      // 触发表单校验
      this.dispatch('ElFormItem', 'el.form.change', [val])
      if (!val) {
        this.clearFile()
      } else {
        this.otherFileName = decodeURIComponent(val.substring(val.lastIndexOf('_') + 1))
      }
    }
  },

  created() {
    this.otherFileName = decodeURIComponent(this.value?.substring(this.value.lastIndexOf('/') + 1))
  },

  beforeDestroy() {
    this.tempUrl && URL.revokeObjectURL(this.tempUrl)
  },

  methods: {
    // 点击
    handleClick() {
      if (this.uploadable) {
        this.$refs.input.click()
      }
    },
    // 点击上传文件
    handleInputChange(e) {
      const { files } = e.target
      if (!files || files.length === 0) return
      this.uploadFiles(files)
    },

    // 拖拽是鼠标悬浮
    // onDragover() {
    //   if (this.uploadable) {
    //     this.dragover = true
    //   }
    // },

    // 拖拽释放鼠标
    // onDrop(e) {
    //   if (!this.uploadable) return
    //   if (this.value) {
    //     this.clearFile()
    //     this.$emit('input', '')
    //   }
    //   this.dragover = false
    //   this.uploadFiles(e.dataTransfer.files)
    // },

    // 验证文件
    validateFiles(files) {
      if (this.accept) {
        files = [].slice.call(files).filter(file => {
          const { type, name } = file
          const extension = name.indexOf('.') > -1
            ? `.${name.split('.').pop()}`
            : ''
          const baseType = type.replace(/\/.*$/, '')
          return this.accept.split(',')
            .map(t => t.trim())
            .filter(Boolean)
            .some(acceptedType => {
              if (/\..+$/.test(acceptedType)) {
                return extension === acceptedType
              }
              if (/\/\*$/.test(acceptedType)) {
                return baseType === acceptedType.replace(/\/\*$/, '')
              }
              if (/^[^/]+\/[^/]+$/.test(acceptedType)) {
                return type === acceptedType
              }
              return false
            })
        })
        if (!files.length) {
          this.$message.warning('不支持该文件类型')
        }
      }
      if (!files || !files.length) return
      const file = files[0]
      if (this.sizeLimit) {
        // eslint-disable-next-line no-magic-numbers
        const fileSize = file.size / 1024 / 1024
        if (fileSize >= this.sizeLimit) {
          this.$message.error(this.sizeLimitMessage || `上传的文件大小不能超过 ${this.sizeLimit}M`)
          return
        }
      } else if (typeof this.beforeUpload === 'function' && !this.beforeUpload(file)) {
        return
      }
      return file
    },

    // 清除表单验证信息
    clearFormValidate() {
      let formItem = this.$parent
      while (formItem && formItem.$options.componentName !== 'ElFormItem') {
        formItem = formItem.$parent
      }
      formItem && formItem.clearValidate()
    },

    // 上传文件
    async uploadFiles(files) {
      const fileToUpload = this.validateFiles(files)
      if (!fileToUpload) {
        this.clearFile()
        return
      }
      this.uploading = true
      this.clearFormValidate()
      try {
        const url = await upload(fileToUpload, this.dir)
        this.otherFileName = files[0].name
        // onSuccess 可以返回一个 promise，如果返回 promise，上传结果会取决于 promise 最终的状态
        try {
          typeof this.onSuccess === 'function' && await this.onSuccess(url, fileToUpload)
          this.$emit('input', url)
          this.successMsg && this.$message.success(this.successMsg)
        } catch (e) {
          // onSuccess 函数报错
        }
      } catch (e) {
        // eslint-disable-next-line
        console.log('上传失败',e)
        // this.$message.error('上传失败') // fix: 此时不用请提示，防止与upload内部接口报错提示重复
      } finally {
        this.uploading = false
        this.clearFile() // 上传完清空,解决重复上传文件不触发change事件
      }
    },

    // 打开图片预览框
    // preview() {
    //   this.$refs.image.openViewer()
    // },

    // 打开视频预览
    // previewVideo() {
    //   this.$refs.previewVideoDialogRef.init({
    //     url: this.previewUrl
    //   })
    // },

    // 打开文件下载
    // previewZip() {
    //   window.open(this.value, '_blank')
    // },

    // 清空 input 的值
    clearFile() {
      this.$refs.input && (this.$refs.input.value = '')
      // IE10 下 input.value = '' 无效，故在此将其重新渲染
      if (this.$refs.input?.value) {
        this.inputKey++
      }
    },

    // 预览pdf文件
    onPreview() {
      window.open(this.value, '_blank')
    },

    // 删除
    onDel() {
      this.clearFile()
      this.$emit('input', '')
      this.otherFileName = ''
    }
  }
}
</script>
