<!-- 可拖动弹窗 -->
<style lang="scss" scoped>
.drag-dialog {
  position: relative;
  user-select: text;

  &-container {
    position: fixed;
    top: 0;
    right: 90px;
    z-index: 1999;
    border-radius: 2px;
    text-align: left;
    color: $color-text-primary;
    background: $color-F2F2F2;
    box-shadow: 0 9px 28px 8px rgb(0 0 0 / 5%), 0 6px 16px rgb(0 0 0 / 8%), 0 3px 6px -4px rgb(0 0 0 / 12%);
  }

  &-header {
    padding: 16px 20px;
    background: $color-FFFFFF;
    cursor: move;

    @include flex-sbc;

    &-title {
      font-size: 18px;
      font-weight: bold;
      line-height: 26px;
    }
  }

  &-body {
    padding: 20px;

    &.no-padding-bottom {
      padding-bottom: 0;
    }
  }

  &-footer {
    padding: 12px 20px;
    text-align: right;
  }
}

@media screen and (max-width: 1440px) {
  .drag-dialog-body {
    overflow: auto;
    max-height: 500px;
  }
}
</style>

<template>
  <div class="drag-dialog">
    <div @click="handleToggleDialog(true)">
      <slot name="button" />
    </div>
    <div
      v-if="dialogVisible"
      ref="dragDialogRef"
      class="drag-dialog-container"
      :style="`width: ${width}`"
    >
      <div v-drag="{dragParentClass: 'drag-dialog-container'}" class="drag-dialog-header">
        <div class="drag-dialog-header-title">
          {{ title }}
          <span style="font-size: 16px;color: #999999;">{{ describe }}</span>
        </div>
        <button
          v-if="showClose"
          type="button"
          class="el-dialog__headerbtn"
          aria-label="Close"
          @click="handleToggleDialog(false)"
        >
          <i class="elicon elicon-close el-dialog__close" />
        </button>
      </div>
      <div :class="['drag-dialog-body', $slots.footer && 'no-padding-bottom']">
        <slot />
      </div>
      <div v-if="$slots.footer" class="drag-dialog-footer">
        <slot name="footer" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'drag-dialog',
  props: {
    // 弹窗宽度
    width: {
      type: [Number, String],
      default: '450px'
    },
    // 弹窗标题
    title: String,
    // 标题描述
    describe: String,
    // 是否显示关闭图标
    showClose: {
      type: Boolean,
      default: true
    },
    // 是否显示
    visible: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      dialogVisible: false, // 弹窗是否显示
    }
  },

  watch: {
    visible(val) {
      this.dialogVisible = val
    }
  },
  methods: {

    // 显示和关闭弹窗弹窗
    handleToggleDialog(flag) {
      // 当前重复点击回到原点
      if (flag && this.$refs.dragDialogRef) {
        this.$refs.dragDialogRef.style.left = '50%'
        this.$refs.dragDialogRef.style.transform = 'translateX(-50%)'
        this.$refs.dragDialogRef.style.top = '60px'
      }
      this.$emit('toggle', flag)
    },
  }
}
</script>
