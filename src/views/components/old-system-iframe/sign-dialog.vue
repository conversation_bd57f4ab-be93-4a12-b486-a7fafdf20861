<!-- 在线签约弹窗 -->
<style lang="scss" scoped>
.dialog-content {
  height: 650px;
  background: $color-FFFFFF;
}
</style>

<template>
  <!-- 在线签约弹窗 -->
  <el-dialog
    title="在线签约"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    width="1400px"
  >
    <div class="dialog-content">
      <iframe
        v-if="url"
        :src="url"
        frameborder="0"
        width="100%"
        height="650"
      />
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'sign-dialog',

  props: {
    visible: Boolean, // 当前弹窗是否显示
    url: String, // 需加载的 url
  },

  computed: {
    // 弹窗是否显示
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    }
  }
}
</script>
