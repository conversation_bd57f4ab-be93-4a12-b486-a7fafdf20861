<!-- 秒贴页面 -->
<style lang="scss" scoped>
#iframe-class {
  display: block;
  min-height: calc(100vh - 65px);
}
</style>

<template>
  <div v-if="token" class="old-system-iframe">
    <iframe
      id="iframe-class"
      ref="iframe"
      :src="src"
      frameborder="0"
      width="100%"
    />
  </div>
  <!-- :height="height" @load="postDomain" :key="renderKey" -->
</template>

<script>
import { mapState } from 'vuex'
import { DOMAIN } from '@/utils/old-system'
// onMessage, postMessage, SEND_EVENTS, onScrollFn
// import discountApi from '@/apis/discount'
import user from '@/utils/user.js'

export default {
  name: 'old-system-iframe',

  props: {
    // 需加载的页面 id，银企1，营销2
    type: {
      type: String,
      require: true
    }
  },

  data() {
    return {
      src: '',
      DOMAIN, // 秒贴系统域名
      // token: '', // 旧系统的 token
      // height: 200, // 页面高度
      // renderKey: 0, // 用于重新渲染的 key
    }
  },

  computed: mapState('user', {
    isLogined: state => state.isLogined, // 当前是否登录状态
  }),

  created() {
    this.token = user.getToken()
    this.routeInfo = this.$route.query
    this.getSrc()
    // this.getToken()
    // onMessage(data => {
    //   // console.log(data)
    //   // eslint-disable-next-line no-magic-numbers
    //   if (data.type === 6) {
    //     // 高度变化
    //     this.height = data.data
    //   // eslint-disable-next-line no-magic-numbers
    //   } else if (data.type === 7) {
    //     // token 失效
    //     // this.rerender()
    //   }
    // }, this)
  },

  methods: {
    getSrc() {
      let { query } = this.$route
      // this.src = `${DOMAIN}/directBank?type=${this.type}&token=${this.token}`
      let i
      let queryUrl = '&channel=hn'
      for (i in query) queryUrl += `&${i}=${query[i]}`
      this.src = `${DOMAIN}/directBank?type=${this.type}&token=${this.token}${queryUrl}`
    }
    // 重新渲染
    // async rerender() {
    //   await this.getToken()
    //   this.renderKey++
    // },

    // 获取旧系统 token
    // async getToken() {
    //   const data = await discountApi.getDiscountToken()
    //   this.token = data.token
    // },

    // 通知旧系统页面当前域名
    // postDomain() {
    //   postMessage(this.$refs.iframe, {
    //     event: SEND_EVENTS.PARENT_DOMAIN,
    //     data: `${window.location.origin}/market`
    //   })
    //   // 监听秒贴页面滚动的高度，用于解决秒贴iframe 引入页面弹框定位的问题
    //   onScrollFn(this.$refs.iframe, this)
    // }
  }
}
</script>
