<style lang="scss" scoped>
.issue-draft-block {
  margin-bottom: 12px;
  padding: 12px;
  background: $color-FFFFFF;
}

.block-flex {
  display: flex;
}

::v-deep {
  .el-form-item__content {
    line-height: 1;
  }

  .el-form-item__error--inline {
    display: inherit;
    margin-left: 0;
  }

  .el-input__inner {
    padding-left: 12px;
    font-size: 14px;
  }
}

.form {
  .el-form-item {
    margin-bottom: 0;
  }

  .g-title-small {
    margin-right: 6px;
    color: $color-text-primary;
  }
}

.margin-bottom {
  margin-bottom: 12px;

  /** 老的客户端样式 */
  &.old ::v-deep .el-form-item__label {
    display: flex;
    align-items: center;
    padding: 0;
    height: 22px;
    color: $color-text-secondary;
    line-height: 22px;

    &::before {
      display: none;
    }
  }

  &.old {
    .form-item {
      display: flex;
      align-items: center;
    }
  }
}
</style>

<template>
  <el-form
    ref="ruleForm"
    :model="form"
    :rules="rules"
    label-position="left"
    :class="[{'margin-bottom': isReIssue}, 'form old']"
    :inline-message="true"
    @validate="validateResult"
  >
    <div class="issue-draft-block">
      <el-form-item
        label="''"
        prop="inviteCode"
        :error="directionalErrorMsg"
        class="form-item"
      >
        <span slot="label" class="g-title-small">对方</span>
        <el-input
          v-model="form.inviteCode"
          size="small"
          placeholder="请输入对方手机号码或定向码（必填）"
          :width="304"
        />
      </el-form-item>
    </div>
  </el-form>
</template>

<script>
import issueDraftApi from '@/apis/issue-draft'

// 初始表单字段
const defaultForm = {
  inviteCode: '', // 对方id
}
export default {
  name: 'directional-release',
  props: {
    isReIssue: {
      type: Boolean,
      default: true
    },
    directionalErrorMsg: String
  },
  data() {
    // 对方校验
    const validateCounterparty = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入对方手机号或定向码'))
      } else {
        callback()
      }
    }
    return {
      form: JSON.parse(JSON.stringify(defaultForm)),
      rules: {
        inviteCode: [{ required: true, validator: validateCounterparty, trigger: 'none' }], // 使用'none',是的不默认触发，只能通过方法触发
      },
      formFailField: {}, // 存一下表单校验不通过的字段，true为不通过，通过监听表单的validate
    }
  },

  watch: {
    // 监听表单数据改变回传给父组件
    form: {
      handler(val) {
        // 如果有校验不通过的字段，那么在该字段有值时，需要去重复校验一下
        Object.keys(val).forEach(key => {
          if (this.formFailField[key] && val[key]) {
            this.$refs.ruleForm.validateField(key)
          }
        })
        this.$emit('change-form', val)
      },
      deep: true
    },
  },

  methods: {
    // 监听表单校验信息
    validateResult(prop, value) {
      this.formFailField[prop] = !value
    },

    // 给父组件执行表单校验
    async validateForm() {
      let bool = true
      this.$refs.ruleForm.validate(valid => {
        bool = valid
      })
      if (bool) {
        const isAreaInBlack = await issueDraftApi.validateAgentOrderArea({
          counterparty: this.form.inviteCode,
          limitType: 1
        })
        if (isAreaInBlack.isLimit) {
          await this.$confirm('对方在您设置的地区黑名单，请确认是否继续定向', '提示', {
            type: 'warning',
            iconPosition: 'title',
            showClose: false,
            showCancelButton: true,
            cancelButtonText: '取消',
            confirmButtonText: '确认'
          })
        }
      }
      return bool
    },

    // 清空表单
    clearForm() {
      this.form = JSON.parse(JSON.stringify(defaultForm))
      this.$nextTick().then(() => {
        this.$refs.ruleForm.clearValidate()
      })
    },
  }
}
</script>
