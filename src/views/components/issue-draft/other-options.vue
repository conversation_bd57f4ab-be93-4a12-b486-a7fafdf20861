<!-- eslint-disable max-lines -->
<!-- 老的发布票面的其他选项 -->
<style lang="scss" scoped>
.issue-draft-block {
  margin-bottom: 83px;
  padding: 12px;
  background: $color-FFFFFF;
}

.block-flex {
  position: relative;
  display: flex;
}

.form {
  margin-top: 12px;

  .g-title-small {
    margin-bottom: 10px;
  }

  ::v-deep {
    .el-form-item__content {
      line-height: 1;
    }

    .el-form-item__label {
      display: flex;
      align-items: center;
      margin-bottom: 4px;
      padding: 0;
      height: 22px;
      color: $color-text-secondary;
      line-height: 22px;

      &::before {
        font-weight: bold;
      }
    }

    .el-form-item__error--inline {
      display: inherit;
      margin-left: 0;
    }

    .el-input__inner,
    .el-textarea__inner {
      padding-left: 12px;
      font-size: 14px;
    }

    .el-input--prefix .el-input__inner {
      padding-left: 30px;
    }

    .el-checkbox__label {
      font-weight: normal;
    }

    .el-input-group--append {
      height: 32px;
    }
  }

  .el-form-item {
    display: flex;
    margin-bottom: 12px;
    flex-flow: column;
  }

  .el-select {
    width: 100%;
  }
}

.pay-type-item {
  display: flex;
  align-items: center;
  line-height: 1;
  flex-wrap: wrap;

  ::v-deep .el-input__inner {
    padding-right: 24px !important;
  }

  .el-checkbox.is-bordered {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 8px;
    width: 124px;

    &.is-disabled {
      border-color: $color-D9D9D9;
      color: $color-text-secondary;
      background: $color-F4F5F6;
    }

    ::v-deep {
      .el-checkbox__label {
        font-size: 14px;
        line-height: 1;
      }

      .open {
        margin-left: 4px;
      }
    }
  }
}

.link {
  line-height: 18px;
  height: 18px;

  @include example-underline;
}

.example {
  margin-left: 16px;

  ::v-deep {
    .el-link {
      vertical-align: baseline;
    }

    .el-link--inner {
      height: 18px;
      line-height: 18px;
    }

    .el-link.el-link--primary {
      color: $font-color;

      @include example-underline;

      &::after {
        border: 0;
        color: $font-color-hover;
      }
    }
  }
}

.default-setting-link-bargain {
  margin-left: 12px;
}

.el-checkbox.is-bordered {
  margin: 0;
  padding-right: 0;
  padding-left: 0;
  text-align: center;

  ::v-deep .el-checkbox__label {
    padding-left: 0;
  }

  // ::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
  //   font-weight: bold;
  // }

  & + .el-checkbox.is-bordered {
    margin-left: 0;
  }
}

.half-block {
  width: 50%;
}

.black-list {
  margin: 12px 12px 0 0;
}

// 覆盖默认element样式
.el-switch {
  height: 20px;

  ::v-deep .el-switch__core {
    border-radius: 10px;
    height: 20px;

    &::after {
      width: 16px;
      height: 16px;
    }
  }

  &.is-checked ::v-deep .el-switch__core::after {
    margin-left: -17px;
  }
}

.icon-question {
  margin-left: 4px;
  font-size: 20px;
}

.blacklist-block {
  ::v-deep .el-form-item__content {
    display: flex;
    align-items: center;
    margin-top: -12px;
    line-height: 1;
    flex-wrap: wrap;
  }
}

.endorse-account-select {
  width: calc(100% - 16px);
}

// 黑名单按钮一些公共样式
@mixin region-btn() {
  display: inline-flex;
  align-items: center;
  margin: 12px 12px 0 0;
  border: 1px solid $color-D9D9D9;
  border-radius: 2px;
  padding: 0 12px;
  height: 40px;
}

.region-item {
  @include region-btn;

  justify-content: space-between;
  width: 128px;

  .g-ellipsis {
    flex: 1;
  }

  .icon-close {
    font-size: 16px;
    cursor: pointer;
    user-select: none;
  }
}

.region-add-btn {
  @include region-btn;

  justify-content: center;
  width: 98px;
  cursor: pointer;
  user-select: none;
  font-size: 16px;

  .icon-plus-square {
    margin-right: 8px;
    font-size: 16px;
  }
}

.block-switch-flex {
  display: flex;
  margin-bottom: 12px;

  .el-form-item {
    margin-bottom: 0;
  }

  .block-flex {
    + .block-flex {
      margin-left: 55px;
    }
  }

  &.is-re-issue {
    flex-wrap: wrap;
    min-height: auto;

    .block-flex {
      margin-bottom: 12px;
      width: 55%;

      + .block-flex {
        margin-bottom: 0;
        margin-left: 0;
      }

      &:nth-last-child(2) {
        margin-bottom: 0;
      }

      &.order2 {
        order: 2;
        width: 45%;
      }

      &.order3 {
        order: 3;
      }

      &.order4 {
        order: 4;
        width: 45%;
      }
    }
  }
}

.flex-item {
  display: flex;
  align-items: center;

  &.flex-item-margin {
    min-width: 460px;
  }
}

.bargaining-limit {
  position: relative;
  margin-left: 12px;
  width: 200px;

  .bargaining-limit-input {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
  }
}

.margin-tips {
  margin-left: 10px;
  line-height: 18px;
}

.link-margin-left {
  margin-left: 16px;
}

.margin-balance {
  margin-left: 12px;
  user-select: none;

  .balance {
    font-size: 16px;
    font-weight: bold;
    color: $color-warning;
  }
}

.switch-text {
  margin-left: 10px;
}

.tooltip-color {
  color: $color-warning;
}

.tooltip-href-color {
  @include example-underline;
}

.tips-upload-text {
  margin-left: 8px;
  font-size: 12px;
  color: $color-warning;
}

.mr-s {
  margin-right: 8px;
}

::v-deep .clear-after-cls {
  margin-left: 12px;

  .el-form-item__label {
    &::after {
      content: none !important;
    }
  }

  .el-form-item__content {
    position: relative;
    display: block;
    width: 200px;
  }

  .el-tooltip {
    position: absolute;
    top: 10px;
    right: -20px;
  }
}

.payment-channel-new-tag {
  position: absolute;
  top: -16px;
  right: -1px;

  @include new-icon;
}

.contact-label {
  color: $color-text-secondary;
}
</style>

<style>
.custom-popover {
  margin-top: -40px !important;
  border: none;
  padding: 0;
}

.issue-draft-tooltip {
  width: 296px;
}

.seller-select-btn {
  font-size: 16px;
  text-align: center;
  color: #0076F6;
  line-height: 40px;
  cursor: pointer;
}

.seller-bank-link {
  border-bottom: 1px solid #0076F6;
  color: #0076F6;
  cursor: pointer;
}
</style>

<template>
  <el-form
    ref="ruleForm"
    :model="form"
    :rules="rules"
    label-position="top"
    class="form"
    :inline-message="true"
    @validate="validateResult"
  >
    <div class="issue-draft-block">
      <div class="g-title-small">其他选项</div>
      <div style="display: flex;flex-wrap: wrap;">
        <el-form-item
          label="选择支付渠道"
          prop="payMethod"
        >
          <div class="pay-type-item">
            <el-checkbox
              v-for="item in noBanList"
              :key="item.value"
              v-model="form[item.key]"
              class="channel-special"
              type="button"
              size="small"
              :true-label="1"
              :false-label="0"
              :disabled="isPaymentChannelDisabled(item)"
              @change="handleChangePayMethod"
            >
              <el-tooltip
                v-if="item.value === PAYMENT_CHANNEL.ZHI_FU_YI_LIAN.id"
                class="item"
                effect="dark"
                content="不支持此支付渠道"
                placement="top"
              >
                <span>{{ item.name }}</span>
              </el-tooltip>
              <template v-else>
                {{ item.name }}
                <span v-if="item.hasNewTag" class="payment-channel-new-tag">NEW</span>
                <span
                  v-if="!item.isOpen && item.value !== PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id"
                  class="link open"
                  @click="opening(item)"
                >开通</span>
              </template>
            </el-checkbox>

            <div class="link" @click="goUserSetting">默认设置</div>
          </div>
        </el-form-item>
        <!-- 智付E+选中显示回款账户 -->

        <el-form-item
          v-if="form.acceptJdYlpay || form.acceptZbankPlus"
          prop="sellerBankAccountId"
          label="回款账户"
          class="form-item-title clear-after-cls"
        >
          <div class="pay-type-item">
            <el-select
              ref="sellerBankAccount"
              v-model="form.sellerBankAccountId"
              placeholder="请选择回款账户"
              size="small"
            >
              <el-option
                v-for="item in sellerBankAccountList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
              <div class="seller-select-btn" @click="() => { $router.push('/user-center/bank-account?tabStatus=2'); $refs.sellerBankAccount.blur(); $emit('close-dialog') }"><i class="el-icon-plus" />添加回款账户</div>
            </el-select>
          </div>
          <el-tooltip
            placement="top"
            popper-class="issue-draft-tooltip"
          >
            <template slot="content">
              <div>依照智付E+、E++渠道交易模式，票方发布票据必须选择回款账户(支持任意银行一般户)。需要在<span class="seller-bank-link" @click="() => { $router.push('/user-center/bank-account?tabStatus=2');$emit('close-dialog') }">银行回款账户</span>页面绑定回款账户，交易完成后票款自动提现到账。</div>
            </template>
            <icon class="icon icon-question" type="chengjie-wenti" />
          </el-tooltip>
        </el-form-item>
      </div>
      <div
        class="block-switch-flex"
        :class="{
          'is-re-issue': isReIssue
        }"
      >
        <el-form-item
          v-if="isShowFastTrade"
          class="block-flex"
        >
          <template slot="label">
            极速出票
            <!--
              <el-tooltip
              placement="top"
              popper-class="issue-draft-tooltip"
              content="若开启极速交易，则免确认，且需要在 10 分钟内支付、背书，15 分钟内签收。"
              >
              <icon class="icon icon-question" type="chengjie-wenti" />
              </el-tooltip>
            -->
          </template>
          <transaction-tooltip-button :types="[TRANSACTION_TOOLTIP_TYPE.FAST]" class="flex-item flex-item-bargaining">
            <template v-slot:content="{disabled}">
              <el-switch
                v-model="form.fastTrade"
                :disabled="disabled"
                :active-value="1"
                :inactive-value="0"
                :width="40"
                @change="onFastTradeChange"
              />
              <span class="margin-tips text-primary">{{ form.fastTrade ? '开启极速出票，接单免确认，资方10分钟内打款' : '' }}</span>
            </template>
          </transaction-tooltip-button>
        </el-form-item>
        <el-form-item prop="margin" class="block-flex">
          <template slot="label">
            保证金
            <el-tooltip
              placement="top"
              popper-class="issue-draft-tooltip"
            >
              <template slot="content">
                保证金是为防范双方交易违约行为缴纳的资金，从保证金账户冻结， 按票面金额的万分之 3 缴纳，最低 10 元，最高 600 元，交易若无违约将退回。 查看《<a
                  class="tooltip-href-color"
                  :href="isNewVersionDraft ? PLATFORM_DEFAULT_RULESNEW_URL_NEW_DRAFT : PLATFORM_DEFAULT_RULESNEW_URL"
                  target="_blank"
                  rel="noopener noreferrer"
                >平台订单违约规则</a>》
              </template>
              <icon class="icon icon-question" type="chengjie-wenti" />
            </el-tooltip>
          </template>
          <div class="flex-item flex-item-margin">
            <el-switch
              v-model="form.margin"
              :active-value="1"
              :inactive-value="0"
              :width="40"
              @change="onMarginChange"
            />
            <span v-if="form.margin" class="margin-balance">{{ sdmName }}余额：<span class="balance">{{ (sdmInfo || {}).balanceAmt || '0.00' }}{{ sdmUnit }}</span></span>
            <span v-else class="margin-tips text-primary">关闭后，将无法约束交易违约行为及被接单速度</span>
            <span class="link link-margin-left" @click="handleRecharge">充值</span>
            <div v-if="(isReIssue && !form.margin) || !isReIssue" class="link link-margin-left" @click="goUserSetting">默认设置</div>
          </div>
        </el-form-item>

        <div v-if="isShowBargain" class="block-flex" :class="isReIssue && 'order3'">
          <el-form-item>
            <template slot="label">
              议价设置
              <el-tooltip
                placement="top"
                popper-class="issue-draft-tooltip"
                content="若您接受议价，资方发起议价申请后您可前往议价窗口选择同意或拒绝。若您同意，系统将自动帮您修改票据价格并确认订单。您可设置议价上限（资方不可见），超出上限的议价申请，系统将自动拒绝。您设置的议价上限银票不超过12%，财票不超过25%，商票不超过36%。"
              >
                <icon class="icon icon-question" type="chengjie-wenti" />
              </el-tooltip>
            </template>
            <div class="flex-item flex-item-bargaining">
              <el-switch
                v-model="form.bargaining"
                :active-value="1"
                :inactive-value="0"
                :width="40"
              />
              <span class="switch-text">{{ form.bargaining ? '接受' : '不接受' }}议价</span>
              <div v-if="form.bargaining" class="bargaining-limit">
                <el-input
                  v-model="form.bargainingLimit"
                  placeholder="议价上限"
                  :width="168"
                  type="number"
                  size="small"
                  :number-format="{
                    negative: false,
                    maxDecimalLength: 2,
                    maxIntegerLength: 5,
                    leadingZero: false
                  }"
                  class="bargaining-limit-input"
                >
                  <template slot="append">元</template>
                </el-input>
              </div>
              <div class="link default-setting-link-bargain" @click="goUserSetting">默认设置</div>
            </div>
          </el-form-item>
        </div>
        <el-form-item class="block-flex" :class="isReIssue && 'order2'">
          <!--
            <template slot="label">
            交易凭证
            <el-tooltip
            placement="top-start"
            popper-class="issue-draft-tooltip"
            >
            <template slot="content">
            开启交易凭证开关，表示您需要该笔订单的交易凭证（服务费每份 <span class="tooltip-color">5</span> 元），凭证服务费将在交易完成时扣除。
            您可以在支付电子账户内查看费用明细及下载交易凭证。交易凭证内容可点击查看右侧“示例”。
            </template>
            <icon class="icon icon-question" type="chengjie-wenti" />
            </el-tooltip>
            </template>
          -->
          <div class="flex-item flex-item-voucher">
            <!--
              <el-switch
              v-model="form.needVoucher"
              :active-value="1"
              :inactive-value="0"
              />
              <span v-if="form.needVoucher" class="switch-text service-amount">
              <span>凭证服务费：</span>
              <span class="tooltip-color">5</span>
              <span> 元</span>
              </span>
              <el-image :preview-src-list="[voucherImage]" img-text="示例" class="example" />
            -->
          </div>
        </el-form-item>
      </div>
      <el-form-item class="blacklist-block">
        <template slot="label">
          资方所在地黑名单
          <el-tooltip
            placement="top"
            popper-class="issue-draft-tooltip"
            content="开启后，黑名单地区资方将看不到您发布的票据。"
          >
            <icon class="icon icon-question" type="chengjie-wenti" />
          </el-tooltip>
        </template>
        <BlackList v-model="form.blackRegionList" class="black-list">
          <div class="link default-setting-link" @click="goUserSetting(SETTING_USER.SALE.id, (SALE_SETTING_OPTIONS.BUY_AREA_BLACKLIST.id))">默认设置</div>
        </BlackList>
      </el-form-item>
      <el-form-item v-if="corpInfo.yilianPayOpen === 1">
        <template slot="label">
          <span>贸易合同及发票</span>
          <el-tooltip
            placement="top"
            popper-class="issue-draft-tooltip"
            content="上传您与出票人的贸易背景或您与上一手企业的贸易合同及发票"
          >
            <icon class="icon icon-question" type="chengjie-wenti" />
          </el-tooltip>
          <span class="tips-upload-text">支持pdf格式，不超过20M</span>
        </template>
        <el-row type="flex" :gutter="8">
          <FileUpload
            v-model="form.tradeContractUrl"
            class="mr-s"
            :size-limit="20"
            :show-file="true"
            accept="application/pdf"
            :dir="OSS_DIR.DRAFT"
          >
            <div slot="empty">
              <el-button
                slot="reference"
                height="32"
                plain
              >
                <icon class="icon icon-plus-square" type="chengjie-plus-square" />上传贸易合同
              </el-button>
            </div>
          </FileUpload>
          <FileUpload
            v-model="form.invoiceUrl"
            :size-limit="20"
            :show-file="true"
            accept="application/pdf"
            :dir="OSS_DIR.DRAFT"
          >
            <div slot="empty">
              <el-button
                slot="reference"
                height="32"
                plain
              >
                <icon class="icon icon-plus-square" type="chengjie-plus-square" />上传发票
              </el-button>
            </div>
          </FileUpload>
        </el-row>
      </el-form-item>
      <!-- 联系方式设置 -->
      <!--
        <ContactSettingField
        ref="ContactSettingField"
        v-model="form.mobile"
        set-button-pos="bottom"
        :is-set-default="contactIsSetDfault"
        @close="() => closeReIssueDialog()"
        >
        <template #label>
        <span class="contact-label">联系方式</span>
        </template>
        </ContactSettingField>
      -->
    </div>
    <!-- 米充值 -->
    <Recharge ref="recharge" />
  </el-form>
</template>

<script>
import { SITE_OPEN_ACCOUNT, SITE_YLYH_OPEN_ACCOUNT } from '@/event/modules/site'
import BigNumber from 'bignumber.js'
import Recharge from '@/views/components/user-center/recharge/recharge.vue' // 米充值
// import ImgUpload from '@/views/components/common/img-upload/img-upload.vue'
import FileUpload from '@/views/components/common/img-upload/file-upload-button.vue'
import { mapGetters, mapActions } from 'vuex'
import {
  PAYMENT_CHANNEL, // 支付类型
  ACCOUNT_STATUS, // 电子状态开通状态
  ISSUE_DRAFT_TYPE_LIST, // 发布的票据类型
  DRAFT_TYPE,
  OSS_DIR,
  REAL_NAME_AUTH_TYPE
} from '@/constant'
import {
  SETTING_USER,
  SALE_SETTING_OPTIONS, // 票方设置选项
} from '@/constants/setting'
import userApi from '@/apis/user' // 用户相关接口
import { isNull } from '@/common/js/util'
import BlackList from './black-list.vue' // 黑名单
import { TRANSACTION_TOOLTIP_TYPE } from '@/constants/transaction-tooltip'
import { BAN_STATUS } from '@/constants/open-account'
import { PLATFORM_DEFAULT_RULESNEW_URL, PLATFORM_DEFAULT_RULESNEW_URL_NEW_DRAFT } from '@/constants/oss-files-url' // 平台订单违约规则url
import openAccountApi from '@/apis/open-account'
// import ContactSettingField from '@/views/pages/setting/components/contact-setting-field.vue'

// 初始表单字段
const defaultForm = {
  blackRegionList: [], // 屏蔽的省市列表
  acceptYillionpay: 0, // 是否支持亿联支付1-是0-否
  acceptHelipay: 0, // 是否支持合利宝支付
  acceptLianlianpay: 0, // 是否支持连连支付
  acceptAipay: 0, // 是否支持百信支付
  acceptZbankpay: 0, // 是否支持众邦
  acceptYlpay: 0, // 是否支持亿联银行
  acceptJdYlpay: 0, // 是否支持智付E+
  acceptZbankPlus: 0, // 是否支持智付邦+
  acceptYiPlusPlus: 0, // 是否支持E++
  margin: 0, // 是否需要保证金，0-否、1-是
  needVoucher: 0, // 是否需要交易凭证，0-否、1-是
  bargaining: 0, // 是否接受议价，0-否、1-是
  bargainingLimit: '', // 议价上限值
  fastTrade: 0, // 是否开启光速交易，0-否、1-是（默认不开启，当只有光速票下架重新发布时才有该选项设置）
  tradeContractUrl: '', // 贸易合同文件
  invoiceUrl: '', // 发票文件
  sellerBankAccountId: '', // 回款账户
  // mobile: [], // 联系方式
}

export default {
  name: 'other-options',

  components: {
    Recharge,
    BlackList,
    FileUpload,
    // ContactSettingField
  },
  inject: ['closeReIssueDialog'],
  props: {
    isReIssue: Boolean, // 是不是重新发布
    issueDraftType: Number, // 票据发布类型
    draftAmount: [Number, String], // 票面金额 单位万 用于计算保证金
    // 表单默认信息，用于回显
    defaultValue: {
      type: Object,
      default: () => ({})
    },
    // 上传的票据的票据类型
    accepterType: null,
    fastTrade: Boolean, // 是否展示光速交易设置
    isDisableYlbankChannel: Boolean, // 大于500W的票禁用亿联银行支付渠道
    isNewVersionDraft: Boolean, // 是否新票
    sellerBankAccountId: Number // 回款账户
  },

  data() {
    // 支付渠道校验
    let validatePayMethod = (rule, value, callback) => {
      const { acceptYillionpay, acceptHelipay, acceptLianlianpay, acceptAipay, acceptZbankpay, acceptYlpay, acceptJdYlpay, acceptZbankPlus, acceptYiPlusPlus } = this.form
      if (!acceptYillionpay && !acceptHelipay && !acceptLianlianpay && !acceptAipay && !acceptZbankpay && !acceptYlpay && !acceptJdYlpay && !acceptZbankPlus && !acceptYiPlusPlus) {
        callback(new Error('请至少选择一个支付渠道'))
      } else {
        callback()
      }
    }
    // 保证金余额校验
    let validateMargin = (rule, value, callback) => {
      if (this.form.margin && this.draftAmount) {
        // 保证金=万分之3*票面金额（最低 10 元，最高 600 元）
        // 这里的draftAmount单位是万
        /* eslint-disable no-magic-numbers */
        let draftMargin = new BigNumber(this.draftAmount).multipliedBy(1e4)
          .multipliedBy(0.0003)
          .toFixed(2)
        if (draftMargin < 10) {
          draftMargin = 10
        } else if (draftMargin > 600) {
          draftMargin = 600
        }
        const balanceAmt = (this.sdmInfo || {}).balanceAmt || 0 // 米可用余额
        if (draftMargin > balanceAmt) {
          this.isDraftMarginEnough = false
          callback(new Error('保证金余额不足'))
        } else {
          this.isDraftMarginEnough = true
          callback()
        }
      } else {
        this.isDraftMarginEnough = true
        callback()
      }
    }
    // 回款账户
    const validateSellerBankAccountId = (rule, value, callback) => {
      // 未选中智付E+支付渠道不校验
      if (this.form.acceptJdYlpay !== 1) return callback()
      if (this.form.acceptJdYlpay === 1 && !value) {
        callback(new Error('请选择回款账户'))
      }
    }
    return {
      PAYMENT_CHANNEL,
      OSS_DIR,
      SETTING_USER,
      TRANSACTION_TOOLTIP_TYPE, // 交易限制提示类型
      SALE_SETTING_OPTIONS, // 票方设置选项
      list: ISSUE_DRAFT_TYPE_LIST, // 发布票据的类型
      PLATFORM_DEFAULT_RULESNEW_URL, // 平台订单违约规则url
      PLATFORM_DEFAULT_RULESNEW_URL_NEW_DRAFT,
      voucherImage: 'https://oss.chengjie.red/web/imgs/draft/voucher.png',
      form: JSON.parse(JSON.stringify(defaultForm)), // 表单字段
      // 支付渠道
      payList: [
        // 智付亿联
        // {
        //   name: PAYMENT_CHANNEL.ZHI_FU_YI_LIAN.name,
        //   value: PAYMENT_CHANNEL.ZHI_FU_YI_LIAN.id,
        //   key: 'acceptYillionpay',
        //   isOpen: false,
        //   hasChannel: false,
        // },
        // 智付合利宝
        {
          name: PAYMENT_CHANNEL.ZHI_FU_HE_LI_BAO.name,
          value: PAYMENT_CHANNEL.ZHI_FU_HE_LI_BAO.id,
          key: PAYMENT_CHANNEL.ZHI_FU_HE_LI_BAO.key,
          isOpen: false,
          hasChannel: false,
        },
        // 智付连连
        {
          name: PAYMENT_CHANNEL.ZHI_FU_LIAN_LIAN.name,
          value: PAYMENT_CHANNEL.ZHI_FU_LIAN_LIAN.id,
          key: PAYMENT_CHANNEL.ZHI_FU_LIAN_LIAN.key,
          isOpen: false,
          hasChannel: false,
        },
        // 智付百信
        {
          name: PAYMENT_CHANNEL.ZHI_FU_BAI_XIN.name,
          value: PAYMENT_CHANNEL.ZHI_FU_BAI_XIN.id,
          key: PAYMENT_CHANNEL.ZHI_FU_BAI_XIN.key,
          isOpen: false,
          hasChannel: false,
        },
        // 智付众邦
        // {
        //   name: PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG.name,
        //   value: PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG.id,
        //   key: 'acceptZbankpay',
        //   isOpen: false,
        //   hasChannel: false,
        // },
        // 亿联银行
        {
          name: PAYMENT_CHANNEL.YI_LIAN_BANK.name,
          value: PAYMENT_CHANNEL.YI_LIAN_BANK.id,
          key: PAYMENT_CHANNEL.YI_LIAN_BANK.key,
          isOpen: false,
          hasChannel: false,
        },
        // 智付E+
        {
          name: PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.name,
          value: PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id,
          key: PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.key,
          isOpen: false,
          hasChannel: false,
          hasNewTag: PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.hasNewTag,
        },
        // 智付邦+
        {
          name: PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.name,
          value: PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id,
          key: PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.key,
          isOpen: false,
          hasChannel: false,
          hasNewTag: PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.hasNewTag,
        },
        // E++
        {
          name: PAYMENT_CHANNEL.YL_PLUS.name,
          value: PAYMENT_CHANNEL.YL_PLUS.id,
          key: PAYMENT_CHANNEL.YL_PLUS.key,
          isOpen: false,
          hasChannel: false,
          hasNewTag: PAYMENT_CHANNEL.YL_PLUS.hasNewTag,
        },
        // 票++商企付
        // {
        //   name: PAYMENT_CHANNEL.SHANG_QI_FU.name,
        //   value: PAYMENT_CHANNEL.SHANG_QI_FU.id,
        //   isOpen: false,
        // }
      ],
      rules: {
        payMethod: [{ required: true, validator: validatePayMethod, trigger: 'none' }], // 支付渠道校验，至少选择一个
        margin: [{ validator: validateMargin, trigger: 'change' }],
        sellerBankAccountId: [{ required: true, validator: validateSellerBankAccountId, trigger: 'change' }],
      },
      formFailField: {}, // 存一下表单校验不通过的字段，true为不通过，通过监听表单的validate
      isDraftMarginEnough: true, // 保证金是否足够
      noBanList: [], // 没有被禁用的渠道列表
      // mobileList: []
    }
  },

  computed: {
    ...mapGetters('user', {
      sdmInfo: 'sdmInfo', // 米账号信息
      postConfig: 'postConfig', // 发布配置
      corpInfo: 'corpInfo', // 企业信息
      limitLight: 'limitLight', // 是否限制极速
      sellerBankAccountList: 'sellerBankAccountList'// 回款账户列表
    }),
    // sellerBankAccountId() {
    //   return this.$store.state.common.sellerBankAccountId
    // },

    // 是否显示议价(定向票不显示议价)
    isShowBargain() {
      return this.issueDraftType !== ISSUE_DRAFT_TYPE_LIST.DIRECTIONAL.id // 定向票不显示议价
    },

    // 是否展示极速交易设置
    isShowFastTrade() {
      const { fastTrade } = (this.$route || {})?.query
      return !this.limitLight && (!!(+fastTrade) || !!(+this.fastTrade))
    },
    // 联系方式设置 是否显示默认配置 针对存量订单设置 存量订单存在联系方式直接回显 不存在取默认
    // contactIsSetDfault() {
    //   return !!(this.defaultValue.mobile || this.defaultValue.mobile !== '[]')
    // }

  },

  watch: {
    // 监听票面金额重新校验保证金
    draftAmount() {
      this.$refs.ruleForm.validateField('margin')
    },
    // 监听表单数据改变回传给父组件
    form: {
      handler(val) {
        // 如果有校验不通过的字段，那么在该字段有值时，需要去重复校验一下
        Object.keys(val).forEach(key => {
          if (this.formFailField[key] && val[key]) {
            this.$refs.ruleForm.validateField(key)
          }
        })
        this.$emit('change-form', val)
      },
      deep: true,
    },
    // 监听数据回显
    defaultValue: {
      handler(val) {
        if (!Object.keys(val).length) {
          return
        }
        this.form = {
          ...this.form,
          margin: val.margin || 0,
          bargaining: val.bargaining,
          bargainingLimit: val.bargainingLimit,
          needVoucher: val.needVoucher,
          blackRegionList: val.buyerRegionBlacklist,
          fastTrade: val.fastTrade,
          // mobile: Array.isArray(val.mobile) && val.mobile ? val.mobile : JSON.parse(val.mobile)
        }
        val.buyerRegionBlacklist && (this.form.regionBlacklist = val.buyerRegionBlacklist) // 兼容一下字段名不一致
        this.bargaining = val.bargaining
      },
      deep: true,
      immediate: true
    },
    // 监听用户设置-是否开启议价
    postConfig: {
      handler(val) {
        if (val) {
          const {
            bargain, // 是否开启议价，0-否，1-是
            defaultPaymentChannelList, // 默认支付渠道，1-智付亿联、2-智付百信、3-智付连连、4-智付合利宝,未设置null
            releaseMargin, // 是否开启保证金发布，0-否，1-是
          } = val
          if (defaultPaymentChannelList) {
            this.payList.forEach(item => {
              this.form[item.key] = defaultPaymentChannelList.indexOf(item.value) !== -1 && item.isOpen && item.hasChannel ? 1 : 0 // 默认渠道有，且已开通和没有被禁用时候选中
              // FIXME: 限制发布和接单时使用百信通道
              if (item.value === PAYMENT_CHANNEL.ZHI_FU_BAI_XIN.id || item.value === PAYMENT_CHANNEL.ZHI_FU_LIAN_LIAN.id) {
                this.form[item.key] = 0
              }
            })
          }
          // 如果该订单中有开启了保证金、议价设置，则使用订单中，如果没有，就使用用户设置的
          this.form.margin = this.defaultValue.margin ? this.defaultValue.margin : releaseMargin
          // this.form.bargaining = this.defaultValue.bargaining ? this.defaultValue.bargaining : bargain

          // 重新议价设置取用户设置的的议价数据
          this.form.bargaining = bargain
          this.getBargainingLimit()
          // if (this.defaultValue.bargaining) {
          //   this.form.bargainingLimit = this.defaultValue.bargainingLimit
          // } else {
          //   this.getBargainingLimit()
          // }
        }
      },
      deep: true,
    },
    // 监听当前上传的票据的票据类型
    accepterType: {
      handler() {
        this.getBargainingLimit()
      },
    },
    // 监听保证金是否足够，用于父组件弹toast
    isDraftMarginEnough(val) {
      this.$emit('draft-margin-enough-result', val)
    },
    // 监听后台限制极速时,将极速开关设为否
    limitLight: {
      handler(n) {
        n && (this.form.fastTrade = 0)
      },
      immediate: true
    },
    // 监听亿联银行渠道状态变化
    isDisableYlbankChannel(val) {
      if (val) {
        this.form.acceptYlpay = 0
      }
    }
  },

  created() {
    this.init()
  },

  methods: {
    ...mapActions('user', {
      getPostConfig: 'getPostConfig', // 获取发布设置
      getSdmInfo: 'getSdmInfo', // 获取米账号信息
      getCorpInfo: 'getCorpInfo', // 获取企业信息
    }),

    // 开通
    async opening(channel) {
      if (channel.accountStatus === ACCOUNT_STATUS.LOGOUT.id) {
        this.$message.warning('暂不支持已注销账户的重新开户！')
        return
      }

      //  亿联银行开通直接打开弹框
      if (channel.value === PAYMENT_CHANNEL.YI_LIAN_BANK.id) {
        this.$event.emit(SITE_YLYH_OPEN_ACCOUNT, channel)
        return
      }

      // 查询是否在途接口 isRealName=> 0:渠道开通 1:实名认证
      const isInTransit = await openAccountApi.getExistOnPassageApply({ isRealName: 0, payChannel: channel.value })
      let obj = {
        // startOver: true,
        reAuthPaymentChannel: channel.value,
        realNameAuthType: REAL_NAME_AUTH_TYPE.FAIL
      }
      // 渠道开通是否存在在途流程
      if (isInTransit) {
        obj.startOver = true // 是否查询流步骤接口
      }

      // let obj = {
      //   realNameAuthType: REAL_NAME_AUTH_TYPE.FAIL,
      // }
      // // 企业状态不是已开通状态 和 渠道是开通中，就去开户查进度
      // if (this.corpInfo.newestCorpOpenInfoApplyStatus < APPLICATION_STATUS.HAVE_BEEN_THROUGH.id || channel.accountStatus === ACCOUNT_STATUS.PENDING.id) {
      //   obj.startOver = true
      // }
      this.$event.emit(SITE_OPEN_ACCOUNT, obj)
    },

    // 初始化
    async init() {
      await this.getPaymentAccountList()
      await this.getPostConfig() // 获取发布设置
      // if (!this.isReIssue) {
      //   this.getAreaBlackList() // 设置默认黑名单
      // }
      this.getAreaBlackList() // 取用户设置默认的黑名单
      this.getSdmInfo() // 获取米账号信息
      this.getCorpInfo() // 获取极速限制开关
      // 回显当前订单的回款账户
      this.form.sellerBankAccountId = this.sellerBankAccountId
    },

    // 获取地区黑名单设置
    getAreaBlackList() {
      // 兼容重新发布-地区黑名单默认从订单记录中取，defaultValue没有数据再从默认设置中取
      const { buyerRegionBlacklist } = this.defaultValue
      if (buyerRegionBlacklist && buyerRegionBlacklist.length > 0) {
        this.form.blackRegionList = buyerRegionBlacklist
      } else {
        this.form.blackRegionList = this.postConfig.areaBlackConfig.map(v => ({
          cityCode: v.cityCode,
          cityName: v.cityName,
          provinceCode: v.provinceCode,
          provinceName: v.provinceName,
        }))
      }
    },

    // 前往用户设置--不传打开默认设置  传参跳转票方--资方黑名单设置页面 random-为了区分当前页面跳转当前页面 弹框不关闭的问题
    async goUserSetting(contactTab, saleTab) {
      let query
      // eslint-disable-next-line valid-typeof
      try {
        if (typeof (contactTab) !== 'object') {
          query = { activeContactTab: `${contactTab}`, activeSaleTab: `${saleTab}`, random: Math.floor(Math.random() * 100) }
          // query = { activeSaleTab: `${val}` }
          await this.$router.push({ name: 'setting', query })
        } else {
          await this.$router.push({ name: 'setting' })
        }
        // 跳转用户设置后，关闭重新发布弹窗
        this.$emit('close-dialog')
      } catch (e) {
        // 相同路由之间切换或重定向
        // eslint-disable-next-line no-console
        console.log(e.message)
      }
    },

    // 获取电子账号开通情况
    async getPaymentAccountList() {
      const res = await userApi.getPaymentAccountList()
      if (res && res.length) {
        this.payList.forEach(item => {
          const current = res.filter(i => i.paymentChannel === item.value)
          if (current.length) {
            item.isOpen = current[0].accountStatus === ACCOUNT_STATUS.SUCCESS.id
            item.hasChannel = current[0].banStatus === BAN_STATUS.ENABLE.id
            item.accountStatus = current[0].accountStatus
          }
          // 在有回显数据的情况下，如果当前方式已开通，则取回显数据的是否选中
          // FIXME: 限制发布和接单时使用百信通道
          if (Object.keys(this.defaultValue).length && item.isOpen && item.value !== PAYMENT_CHANNEL.ZHI_FU_BAI_XIN.id && item.value !== PAYMENT_CHANNEL.ZHI_FU_LIAN_LIAN.id) {
            this.form[item.key] = this.defaultValue.draftOrderPay[item.key]
          }
          // 在有回显数据的情况下，如果当前方式未开通或者被禁用，则设为0
          if (Object.keys(this.defaultValue).length && (!item.isOpen || !item.hasChannel)) {
            this.form[item.key] = 0
          }
        })
      }
      this.noBanList = this.payList.filter(i => i.hasChannel)
    },

    // 是否需要禁用某个支付渠道的选中
    isPaymentChannelDisabled() {
      // 暂时全部禁用 ==>   isPaymentChannelDisabled(channel)
      return true
      // if (!channel.isOpen) return true
      // if (channel.value === PAYMENT_CHANNEL.ZHI_FU_BAI_XIN.id) return true
      // if (channel.key === PAYMENT_CHANNEL.ZHI_FU_LIAN_LIAN.key) return true
      // if (channel.value === PAYMENT_CHANNEL.YI_LIAN_BANK.id && this.isDisableYlbankChannel) return true
      // return false
    },

    // 支付渠道改变回调
    handleChangePayMethod() {
      this.formFailField.payMethod && this.$refs.ruleForm.validateField('payMethod')
    },

    // 给父组件执行表单校验
    validateForm() {
      let bool = true
      this.$refs.ruleForm.validate(valid => {
        bool = valid
      })
      return bool
    },

    // 监听表单校验信息
    validateResult(prop, value) {
      this.formFailField[prop] = !value
    },

    // 清空表单
    clearForm() {
      this.form = JSON.parse(JSON.stringify(defaultForm))
      if (this.postConfig && this.postConfig.defaultPaymentChannelList) { // 用户设置-是否选择支付渠道回显
        this.payList.forEach(item => {
          this.form[item.key] = this.postConfig.defaultPaymentChannelList.indexOf(item.value) !== -1 && item.isOpen && item.hasChannel ? 1 : 0
        })
      }
      this.form.bargaining = this.postConfig?.bargain // 用户设置-是否开启议价回显
      this.form.margin = this.postConfig?.releaseMargin // 用户设置-是否开启保证金回显

      this.$nextTick().then(() => {
        this.$refs.ruleForm.clearValidate()
      })
    },

    // 获取用户设置中的议价上限，并根据条件设置
    getBargainingLimit() {
      if (!isNull(this.accepterType)) {
        const {
          bankDraftBargainLimit, // 银票议价上限(每十万扣)，单位为元
          financialDraftBargainLimit, // 财票议价上限(每十万扣)，单位为元
          commercialDraftBargainLimit, // 商票议价上限(每十万扣)，单位为元
        } = (this.postConfig || {})
        this.form.bargainingLimit = (bankDraftBargainLimit && bankDraftBargainLimit > 0) ? bankDraftBargainLimit : '' // 除了财票和商票以外都为银票

        // 财票
        if (+this.accepterType === DRAFT_TYPE.CAI_PIAO.id) {
          this.form.bargainingLimit = financialDraftBargainLimit && financialDraftBargainLimit > 0 ? financialDraftBargainLimit : ''
        }
        // 商票
        if (+this.accepterType === DRAFT_TYPE.SHANG_PIAO.id) {
          this.form.bargainingLimit = commercialDraftBargainLimit && commercialDraftBargainLimit > 0 ? commercialDraftBargainLimit : ''
        }
      }
    },

    // 显示米充值弹窗
    handleRecharge() {
      this.$refs.recharge.init()
    },

    // 保证金切换
    async onMarginChange(val) {
      // 当展示极速交易设置时，关闭了保证金,极速交易也需要关闭  0 不开启 1 开启
      if (this.isShowFastTrade && val === 0 && this.form.fastTrade === 1) {
        this.form.fastTrade = 0
      }
      // 每次打开重新请求一下米信息
      val && await this.getSdmInfo()
      this.$nextTick().then(() => {
        if (val && !this.isDraftMarginEnough) {
          this.$message.warning('保证金余额不足')
        }
      })
    },

    // 极速票切换
    onFastTradeChange(val) {
      // 当展示极速交易设置时，开启了极速交易，保证金必现打开  0 不开启 1 开启
      if (this.isShowFastTrade && val === 1 && this.form.margin === 0) {
        this.form.margin = 1
      }
    }
  }
}
</script>
