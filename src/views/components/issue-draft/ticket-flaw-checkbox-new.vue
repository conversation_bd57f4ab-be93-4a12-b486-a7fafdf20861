<!-- 瑕疵按钮选择列表 -->
<style lang="scss" scoped>
.el-checkbox.is-bordered {
  padding-right: 0;
  padding-left: 0;
  text-align: center;

  ::v-deep .el-checkbox__label {
    padding-left: 0;
    font-size: 14px;
  }
}

.ticket-flaw-checkbox {
  margin-top: -12px;
  margin-right: -12px;

  .flaw-item {
    margin: 12px 12px 0 0 !important;
    width: 98px;

    &:last-child {
      margin-right: 0 !important;
    }

    &.no-error ::v-deep .el-input__inner {
      border: 1px solid $--border-color-base;
      background-color: $color-FFFFFF;
    }

    &.other-input-item {
      width: 204px;
    }
  }

  .other-input {
    width: 204px;
  }

  .flaw-form {
    display: flex;
    flex-wrap: wrap;
  }

  .el-form-item {
    display: inline-block;
    margin: 12px 12px 0 0 !important;
    min-width: 98px;

    ::v-deep .el-input__inner {
      font-size: 14px !important;
    }
  }
}

::v-deep {
  .discern-disabled.el-checkbox.is-bordered.is-disabled.is-checked {
    border: 1px solid $--color-primary !important;
    background: $--color-primary-hover !important;
  }

  .discern-disabled .el-input__inner {
    color: $color-text-primary !important;
  }
}

.flaw-out {
  display: flex;
  align-items: center;
  margin-right: 8px;
}
</style>

<template>
  <div class="ticket-flaw-checkbox">
    <el-form
      ref="ruleFlawForm"
      label-position="top"
      class="flaw-form"
      :model="form"
      :rules="rules"
      :inline-message="true"
      @validate="validateResult"
    >
      <div v-for="(item, index) in ticketFlawList" :key="`${item.name}Out`" class="flaw-out">
        <el-checkbox
          v-model="item.active"
          :label="item.name"
          class="flaw-item"
          size="small"
          :class="isDisabled && 'discern-disabled'"
          type="button"
          :disabled="isDisabled"
          @change="(val) => handleCheckboxChange(val, index)"
        />
        <el-form-item
          v-if="item.showInput && (item.active || item.name === BACK_DEFECT_TYPE_NAME_MAP.OTHER.name)"
          :key="`input${item.name}`"
          :prop="item.key"
          :class="['flaw-item', item.name === BACK_DEFECT_TYPE_NAME_MAP.OTHER.name && 'other-input-item', isDisabled && 'discern-disabled']"
        >
          <el-input
            v-model="item.input"
            :maxlength="10"
            :placeholder="item.name === BACK_DEFECT_TYPE_NAME_MAP.OTHER.name ? '瑕疵描述（最大长度为10）' : '次数'"
            :class="[item.name === BACK_DEFECT_TYPE_NAME_MAP.OTHER.name && 'other-input',
                     (item.name === BACK_DEFECT_TYPE_NAME_MAP.OTHER.name || item.input) && 'no-error'
            ]"
            :type="item.name === BACK_DEFECT_TYPE_NAME_MAP.OTHER.name ? 'text' : 'number'"
            :number-format="{
              negative: false,
              decimal: false,
              leadingZero: false,
              maxLength: 2
            }"
            :disabled="isDisabled"
            size="small"
            @input="(val) => { item.input = String(val).replace(/^[0]+$/gi, '') }"
          >
            <template v-if="item.name !== BACK_DEFECT_TYPE_NAME_MAP.OTHER.name" slot="append">次</template>
          </el-input>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
// 瑕疵列表
import { BACK_DEFECT_TYPE_NAME_MAP } from '@/constant'
const defaultList = [
  {
    name: '无瑕疵',
    active: false,
    value: '',
  },
  {
    name: BACK_DEFECT_TYPE_NAME_MAP.ABA.name, // 回出票人aba
    active: false,
    key: 'defectsAba',
    input: 1, // 次数
    showInput: false, // 是否可以输入
  },
  {
    name: BACK_DEFECT_TYPE_NAME_MAP.ABCA.name, // 回出票人abca
    active: false,
    key: 'defectsAbca',
    input: 1, // 次数
    showInput: true
  },
  {
    name: BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_HUI_TOU.name, // 背书回头
    active: false,
    key: 'defectsTurnAround',
    input: 1, // 次数
    showInput: true
  },
  {
    name: BACK_DEFECT_TYPE_NAME_MAP.HUI_SHOU_KUAN_REN.name, // 回收款人
    active: false,
    key: 'defectsReturnFront',
    input: 1, // 次数
    showInput: true
  },
  {
    name: BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_CHONG_FU.name, // 背书重复
    active: false,
    key: 'defectsDuplicated',
    input: 1, // 次数
    showInput: true
  },
  {
    name: BACK_DEFECT_TYPE_NAME_MAP.ABB.name, // abb
    active: false,
    key: 'defectsAbb',
    input: 1, // 次数
    showInput: false, // 是否可以输入
  },
  {
    name: BACK_DEFECT_TYPE_NAME_MAP.ZHI_YA.name, // 质押
    active: false,
    key: 'defectsPledgeEndorsement',
    input: 1, // 次数
    showInput: true
  },
  {
    name: BACK_DEFECT_TYPE_NAME_MAP.BAO_ZHENG.name, // 保证
    active: false,
    key: 'defectsPromise',
    input: 1, // 次数
    showInput: false, // 是否可以输入
  },
  {
    name: BACK_DEFECT_TYPE_NAME_MAP.SHANG_XIA_BU_YI_ZHI.name, // 不一致
    active: false,
    key: 'defectsInconformity',
    input: 1, // 次数
    showInput: false, // 是否可以输入
  },
  {
    name: BACK_DEFECT_TYPE_NAME_MAP.HUI_GOU_TIE_XIAN.name, // 回购式贴现
    active: false,
    key: 'defectsRepoDiscount',
    input: 1, // 次数
    showInput: false, // 是否可以输入
  },
  {
    name: BACK_DEFECT_TYPE_NAME_MAP.OTHER.name, // 其他
    active: false,
    key: 'defectsOther',
    input: '', // 原因
    showInput: true
  },
]

export default {
  name: 'ticket-flaw-checkbox',
  props: {
    // 默认数据 {noFlaw: true} 表示无瑕疵， {} 表示没选择， 其他瑕疵按照key列出
    defaultValue: {
      type: Object,
      default: () => ({})
    },
    // 是否禁用
    isDisabled: {
      type: [Boolean, Number],
      default: false
    }
  },
  data() {
    // 瑕疵校验
    const validateTicketFlaw = (rule, value, callback) => {
      // 分别校验两种方式下的输入框
      if (!value && value !== 0) {
        callback(new Error('请输入次数'))
      } else if (value === 0) {
        callback(new Error('请输入正确的次数'))
      } else {
        callback()
      }
    }
    return {
      BACK_DEFECT_TYPE_NAME_MAP, // 瑕疵列表
      // 瑕疵情况
      ticketFlawList: JSON.parse(JSON.stringify(defaultList)),
      // 表单校验规则
      rules: {
        defectsAbca: [{ required: true, validator: validateTicketFlaw, trigger: 'none' }], // 使用'none',是的不默认触发，只能通过方法触发
        defectsTurnAround: [{ required: true, validator: validateTicketFlaw, trigger: 'none' }],
        defectsReturnFront: [{ required: true, validator: validateTicketFlaw, trigger: 'none' }],
        defectsDuplicated: [{ required: true, validator: validateTicketFlaw, trigger: 'none' }],
        defectsPledgeEndorsement: [{ required: true, validator: validateTicketFlaw, trigger: 'none' }],
      },
      form: {}, // 表单
      formFailField: {} // 存一下表单校验不通过的字段，true为不通过，通过监听表单的validate
    }
  },

  watch: {
    // 监听列表，用于返回父组件, 处理成接口需要的格式
    ticketFlawList: {
      handler(val) {
        this.form = {}
        // 无瑕疵
        if (val[0].active) {
          // 用于区别无瑕疵和没选，在父组件删除
          this.form = {
            noFlaw: true
          }
        } else {
          val.forEach(item => {
            if (item.active) {
              if (item.name === BACK_DEFECT_TYPE_NAME_MAP.OTHER.name) {
                this.form[item.key] = 1
                this.form.defectsOtherDesc = item.input
              } else {
                this.form[item.key] = item.input ? Number(item.input) : ''
              }
            } else {
              delete this.form[item.key]
              if (item.name === BACK_DEFECT_TYPE_NAME_MAP.OTHER.name) {
                delete this.form.defectsOtherDesc
              }
            }
          })
        }
        this.$emit('change', this.form)
      },
      deep: true
    },
    // 监听表单数据改变
    form: {
      handler(val) {
        // 如果有校验不通过的字段，那么在该字段有值时，需要去重复校验一下
        Object.keys(val).forEach(key => {
          if (this.formFailField[key] && val[key]) {
            this.$nextTick().then(() => {
              this.$refs.ruleFlawForm.validateField(key)
            })
          }
        })
      },
      deep: true
    },
    // 默认表单数据回填
    defaultValue: {
      handler(val) {
        const list = Object.keys(val)
        this.ticketFlawList = JSON.parse(JSON.stringify(defaultList))
        if (list.length === 0) {
          return
        }
        if (val.noFlaw) {
          this.ticketFlawList[0].active = true
        } else {
          this.ticketFlawList.forEach((item, index) => {
            if (item.key === 'defectsOther') {
              item.active = !!val[item.key]
              item.input = val.defectsOtherDesc
            } else {
              item.input = val[item.key] ? val[item.key] : 1
              item.active = !!val[item.key]
            }
            index === 0 && (item.active = false)
          })
        }
      },
      deep: true,
      immediate: true
    }
  },

  methods: {
    // 点击多选按钮
    handleCheckboxChange(val, index) {
      // 无瑕疵
      if (index === 0 && val) {
        this.ticketFlawList.forEach((item, i) => {
          i > 0 && (item.active = false)
        })
      } else {
        this.ticketFlawList[0].active = false
      }
    },

    // 给父组件执行表单校验
    validateForm() {
      let bool = true
      this.$refs.ruleFlawForm.validate(valid => {
        bool = valid
      })
      return bool
    },

    // 监听表单校验信息
    validateResult(prop, value) {
      this.formFailField[prop] = !value
    },

    // 清空表单
    clearForm() {
      this.form = {}
      this.formFailField = {}
      this.ticketFlawList = JSON.parse(JSON.stringify(defaultList))
      this.$nextTick().then(() => {
        this.$refs.ruleFlawForm.clearValidate()
      })
    },

  }
}
</script>
