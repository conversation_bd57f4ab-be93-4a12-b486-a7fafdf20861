<!-- 黑名单列表 -->
<style lang="scss" scoped>
.black-list {
  display: inline-flex;
  flex-wrap: wrap;
  align-items: center;

  .black-item {
    position: relative;
    margin: 0 8px 0 0;
    border: 1px solid $color-D9D9D9;
    border-radius: 2px;
    padding: 0 22px 0 12px;
    width: 128px;
    height: 32px;
    line-height: 32px;
    cursor: pointer;

    @include ellipsis;

    .icon-close {
      position: absolute;
      top: 50%;
      right: 7px;
      transform: translateY(-50%);
      cursor: pointer;
    }
  }

  .icon-plus-square {
    display: inline-block;
    margin-top: -2px;
    margin-right: 8px;
    font-size: 16px;
    vertical-align: middle;
  }

  .add-btn {
    margin-right: 8px;
  }
}

.dialog-main {
  padding: 16px;
  background: $color-FFFFFF;

  .el-form ::v-deep {
    .el-form-item {
      margin-bottom: 0;
    }

    .el-form-item__label {
      padding-bottom: 2px;
      color: $color-text-secondary;
      line-height: 22px;
    }

    .el-form-item__error {
      position: relative;
    }

    .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label::before {
      display: none;
    }
  }

  .form-flex {
    @include flex-sb;

    .el-form-item {
      flex: 1;
      margin-right: 10px;

      &:last-child {
        margin-right: 0;
      }
    }

    .el-select {
      width: 100%;
    }
  }
}

.dialog-footer {
  padding: 12px 0;
}
</style>

<style>
.tooltip-text {
  max-width: 296px;
}
</style>

<template>
  <div class="black-list">
    <template v-for="(item, index) in blacklist">
      <div v-if=" blacklist.length > index && index > blacklist.length - 6" :key="item + index" class="black-item">
        <el-tooltip
          :content="tooltipContext"
          placement="top"
          popper-class="tooltip-text"
        >
          <span>{{ item }}</span>
        </el-tooltip>
        <icon class="icon icon-close" type="chengjie-close" @click="handleDeleteBlacklist(index)" />
      </div>
    </template>
    <el-popover
      v-if="!blacklist || blacklist.length < 30"
      v-model="showPopover"
      placement="bottom-start"
      trigger="click"
      popper-class="custom-popover"
      :visible-arrow="false"
    >
      <el-button
        slot="reference"
        width="84"
        height="32"
        plain
        class="add-btn"
        @click="handleAdd"
      >
        <icon class="icon icon-plus-square" type="chengjie-plus-square" />添加
      </el-button>
    </el-popover>
    <slot />
    <BlackDialog
      ref="blackDialogRef"
      type="issue"
      @on-delete="onDelete"
      @on-save="onSave"
    />
  </div>
</template>

<script>
import region from '@/common/json/region-code.json' // 地址库
import BlackDialog from './black-dialog.vue'
const defaultForm = {
  searchText: '', // 黑名单输入文字
  provinceName: '', // 地区-省，非空字段
  provinceCode: '', // 地区-省编码，非空字段
  cityName: '', // 地区-市，非空字段
  cityCode: '' // 地区-市编码，非空字段
}

export default {
  name: 'black-list',
  components: {
    BlackDialog
  },
  props: {
    value: Array, // 黑名单列表
  },
  data() {
    return {
      region,
      selectedRegion: [], // 地址级联选框选中值，用于在选择后再次打开时不回显
      blacklist: [],
      showPopover: false, // 地址选择框是否显示
      blackRes: [],
      tooltipContext: [], // 全部数据展示内容
      cityList: [], // 市列表
      visible: false,
      form: { ...defaultForm },
      //  新增和编辑校验规则
      rules: {
        provinceCode: [{ required: true, message: '请选择省', trigger: 'none' }]
      },
    }
  },

  watch: {
    value(v) {
      if (v && v[0]) {
        this.blacklist = v.map(item => `${item.provinceName}${item.cityName ? item.cityName : ''}`)
        this.tooltipContext = this.blacklist.toString()
        this.blackRes = v
      }
    },
    blackRes(n) {
      this.$emit('input', n)
    }
  },

  mounted() {
    if (this.value && this.value[0]) {
      this.blacklist = this.value.map(item => `${item.provinceName}${item.cityName ? item.cityName : ''}`)
      this.tooltipContext = this.blacklist.toString()
      this.blackRes = this.value
    } else {
      this.blacklist = []
      this.blackRes = []
    }
  },

  methods: {
    onSave(form) {
      const obj = {
        provinceCode: form.provinceCode,
        provinceName: form.provinceName,
        cityCode: form.cityCode,
        cityName: form.cityName,
      }
      this.blacklist.push(obj.provinceName + obj.cityName)
      this.blackRes.push(obj)
      this.showPopover = false
      this.selectedRegion = []
      this.tooltipContext = this.blacklist.toString()
      this.$refs.blackDialogRef.handleDialogCancel()
    },
    onDelete(index) {
      this.blacklist = this.blacklist.filter((n, i) => !index.includes(i))
      this.blackRes = this.blackRes.filter((n, i) => !index.includes(i))
      this.tooltipContext = this.blacklist.toString()
    },
    // 新增黑名单
    handleAdd() {
      let blackCode = this.blackRes.map(item => `${item.cityCode ? item.cityCode : item.provinceCode}`)
      const options = {
        blacklist: this.blacklist,
        blackCode
      }
      this.$refs.blackDialogRef.init(options)
      // this.visible = true
    },
    // 删除黑名单
    handleDeleteBlacklist(index) {
      this.blacklist.splice(index, 1)
      this.blackRes.splice(index, 1)
      this.tooltipContext = this.blacklist.toString()
    },
  }
}
</script>
