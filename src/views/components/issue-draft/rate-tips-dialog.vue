<style lang="scss" scoped>
.body-wrapper {
  padding: 16px;
  font-size: 16px;
  background: #FFFFFF;

  .mb-s {
    margin-bottom: 8px;
  }

  .li-item {
    margin-bottom: 4px;
  }
}
</style>

<template>
  <el-dialog
    width="500px"
    :visible.sync="visible"
    :before-close="handleClose"
    :close-on-click-modal="false"
    append-to-body
    title="发布票据"
  >
    <div class="body-wrapper">
      <!-- 单张发布 -->
      <div v-if="scene === 'single'">该票据承兑人有更优成交价格的渠道，您可联系客户经理进行咨询。</div>
      <!-- 批量发布 -->
      <div v-if="scene === 'multiple'">
        <p class="mb-s">
          以下票据承兑人有更优成交价格的渠道，您可联系客户经理进行咨询。
        </p>
        <ul>
          <li v-for="(name, i) in (currentData.acceptorNames || [])" :key="i" class="li-item">承兑人{{ i + 1 }}：{{ name }}</li>
        </ul>
      </div>
    </div>
    <div slot="footer">
      <el-button @click="handleClose">返回</el-button>
      <el-button
        type="primary"
        @click="confirm"
      >
        继续发布
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'rate-tips-dialog',
  props: {
    // 组件使用场景，单张发布single, 批量发布multiple
    scene: {
      type: String,
      default: 'single'
    }
  },
  data() {
    return {
      visible: false,
      currentData: {}
    }
  },
  methods: {
    init(params) {
      this.currentData = params
      this.visible = true
    },
    handleClose() {
      this.visible = false
    },
    confirm() {
      this.$emit('on-publish', {
        ...(this.currentData || {})
      })
      this.handleClose()
    }
  }
}
</script>
