<!-- eslint-disable max-lines -->
<!-- 新发布票面的其他选项 -->
<style lang="scss" scoped>
.issue-draft-block {
  margin-bottom: 98px;
  padding: 12px 12px 0;
  background: $color-FFFFFF;
}

.block-flex {
  position: relative;
}

.form {
  margin-top: 8px;

  ::v-deep {
    .el-form-item__label {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      padding: 0;
      height: 22px;
      color: $color-text-secondary;
      line-height: 22px;

      &::before {
        font-weight: bold;
      }
    }

    .el-form-item__error--inline {
      display: inherit;
      margin-left: 0;
    }

    .el-input__inner,
    .el-textarea__inner {
      padding-left: 12px;
      font-size: 14px;
    }

    .el-input--prefix .el-input__inner {
      padding-left: 30px;
    }

    .el-checkbox__label {
      font-weight: normal;
    }

    .el-input-group--append {
      height: 32px;
    }
  }

  .el-form-item {
    display: flex;
    margin-bottom: 12px;
    flex-flow: column;
  }

  .el-select {
    width: 100%;
  }
}

.pay-type-item {
  position: relative;
  display: flex;
  align-items: center;
  line-height: 1;
  flex-wrap: wrap;

  .el-checkbox.is-bordered {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 12px;
    width: 124px;

    &.is-disabled {
      border-color: $color-D9D9D9;
      color: $color-text-regular;
      background: $color-F4F5F6;
    }

    ::v-deep {
      .el-checkbox__label {
        font-size: 14px;
        line-height: 1;
      }
    }
  }
}

.link {
  @include example-underline;
}

.example {
  margin-left: 16px;

  ::v-deep {
    .el-link {
      vertical-align: baseline;
    }

    .el-link--inner {
      height: 18px;
      line-height: 18px;
    }

    .el-link.el-link--primary {
      @include example-underline;

      &::after {
        bottom: 1px;
        color: $font-color-hover;
      }
    }
  }
}

.default-setting-link {
  margin-left: 12px;
}

// <!-- fix:默认禁用所有渠道-设置选中样式 -->

.default-disabed {
  .is-checked {
    background: $button-background !important;
  }
}

.el-checkbox.is-bordered {
  margin: 0;
  padding-right: 0;
  padding-left: 0;
  text-align: center;

  ::v-deep .el-checkbox__label {
    padding-left: 0;
  }

  ::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
    font-weight: bold;
  }

  & + .el-checkbox.is-bordered {
    margin-left: 0;
  }
}

::v-deep .black-list {
  .black-item {
    &:last-child {
      margin-right: 0;
    }
  }

  .add-btn {
    margin-right: 0;
  }
}

// 覆盖默认element样式
.el-switch {
  height: 20px;

  ::v-deep .el-switch__core {
    border-radius: 10px;
    height: 20px;

    &::after {
      width: 16px;
      height: 16px;
    }
  }

  &.is-checked ::v-deep .el-switch__core::after {
    margin-left: -17px;
  }
}

.icon-question {
  margin-left: 4px;
  font-size: 20px;
}

.blacklist-block {
  margin-bottom: 0 !important;
  border: 1px solid $color-D9D9D9;
  padding: 8px 12px 0;

  ::v-deep .el-form-item__content {
    display: flex;
    align-items: center;
    margin-top: -12px;

    // line-height: 1;
    flex-wrap: wrap;
  }
}

.contact-block {
  width: 246px;

  .contact-mobile {
    margin-right: 8px;
  }

  .contact-qrcode {
    display: inline-block;
    vertical-align: middle;
    width: 60px;
    height: 30px;
    background-image: url("https://cdn.sdpjw.cn/static/shenduBihu/index/contact-qrcode.png");
    background-size: cover;
    cursor: pointer;
  }

  .el-icon-warning {
    margin-right: 6px;
    font-size: 18px;
    line-height: 20px;
    vertical-align: text-bottom;
    color: $color-assist3;
  }
}

.endorse-account-select {
  width: calc(100% - 16px);
}

// 黑名单按钮一些公共样式
@mixin region-btn() {
  display: inline-flex;
  align-items: center;
  margin: 12px 12px 0 0;
  border: 1px solid $color-D9D9D9;
  border-radius: 2px;
  padding: 0 12px;
  height: 40px;
}

.region-item {
  @include region-btn;

  justify-content: space-between;
  width: 128px;

  .g-ellipsis {
    flex: 1;
  }

  .icon-close {
    font-size: 16px;
    cursor: pointer;
    user-select: none;
  }
}

.region-add-btn {
  @include region-btn;

  justify-content: center;
  width: 98px;
  cursor: pointer;
  user-select: none;
  font-size: 16px;

  .icon-plus-square {
    margin-right: 8px;
    font-size: 16px;
  }
}

.block-switch-flex {
  display: flex;
  padding-bottom: 4px;
  flex-wrap: wrap;

  .el-form-item {
    margin-bottom: 0;
  }

  .block-flex {
    margin-bottom: 8px;
    border: 1px solid $color-D9D9D9;
    padding: 8px 12px 0;
    min-height: 72px;

    &:not(:last-child) {
      margin-right: 8px;
    }
  }

  &.is-re-issue {
    flex-wrap: wrap;
    min-height: auto;

    .block-flex {
      margin-bottom: 8px;
      width: 55%;

      + .block-flex {
        margin-bottom: 0;
        margin-left: 0;
      }

      &:nth-last-child(2) {
        margin-bottom: 0;
      }

      &.order2 {
        order: 2;
        width: 45%;
      }

      &.order3 {
        order: 3;
      }

      &.order4 {
        order: 4;
        width: 45%;
      }
    }
  }
}

.flex-item {
  display: flex;
  align-items: center;

  &.flex-item-margin {
    max-width: 460px;
    height: 40px;
  }
}

.bargaining-limit {
  position: relative;
  margin-left: 16px;
  width: 154px;

  .bargaining-limit-input {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
  }
}

.shendumi-cont {
  position: relative;
  margin-left: 16px;
  width: 160px;
}

.margin-tips {
  margin-left: 10px;
  line-height: 18px;
}

.link-margin-left {
  margin-left: 16px;
}

.margin-balance {
  margin-left: 12px;
  user-select: none;

  .balance {
    font-size: 16px;
    font-weight: bold;
    color: $color-warning;
  }
}

.switch-text {
  margin-left: 10px;
}

.tooltip-color {
  color: $color-warning;
}

.title-required {
  position: relative;
  font-size: 16px;
  font-weight: bold;
  line-height: 32px;
  color: $color-text-primary;
  flex-flow: row;
}

::v-deep .form-item-title {
  display: flex;
  align-items: center;
  flex-flow: row !important;

  .el-form-item__label {
    position: relative;
    margin-right: 8px;
    margin-bottom: 0;
    height: 32px;

    &::before {
      margin-left: 12px;
    }

    &::after {
      position: absolute;
      top: 50%;
      left: 0;
      width: $line-bar-width;
      height: 16px;
      background: $--color-primary;
      transform: translateY(-50%);
      content: "";
    }
  }
}

::v-deep .clear-after-cls {
  .el-form-item__label {
    &::after {
      content: none !important;
    }
  }

  .el-form-item__content {
    position: relative;
    display: block;
  }

  .el-tooltip {
    position: absolute;
    top: 5px;
    right: -25px;
  }
}

// ::v-deep .black-block {
//   .el-form-item__label {
//     margin-bottom: 3px;
//   }
// }

.open {
  margin-left: 8px;
}

.block-bargain {
  min-width: 302px;
}

.trade-contract {
  overflow: hidden;
  margin-bottom: 0;

  .tips-upload-text {
    margin-left: 8px;
    font-size: 12px;
    color: $color-warning;
  }

  ::v-deep {
    .el-form-item__content {
      @include flex-vc;
    }

    .img-upload + .img-upload {
      margin-left: 8px;
    }

    .el-form-item__label {
      margin-bottom: 10px;
    }
  }

  .trade-btn button {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
  }

  .trade-btn button .svg-icon {
    vertical-align: sub;
  }
}

.flex {
  display: flex;
}

.payment-channel-new-tag {
  position: absolute;
  top: -16px;
  right: -1px;

  @include new-icon;
}

.contact-field {
  border: 1px solid $color-D9D9D9;
  padding: 8px 12px;
  height: 78px;

  .contact-label {
    color: $color-text-secondary;
  }
}
</style>

<style>
.custom-popover {
  margin-top: -40px !important;
  border: none;
  padding: 0;
}

.issue-draft-tooltip {
  width: 296px;
}

.seller-select-btn {
  font-size: 16px;
  text-align: center;
  color: #0076F6;
  line-height: 40px;
  cursor: pointer;
}

.seller-bank-link {
  border-bottom: 1px solid #0076F6;
  color: #0076F6;
  cursor: pointer;
}
</style>

<style lang="scss">
  .custom-contact-cls {
    margin-bottom: 0 !important;

    .phone-wrapper {
      margin-top: 12px !important;
    }
  }
</style>

<template>
  <el-form
    ref="ruleForm"
    :model="form"
    :rules="rules"
    label-position="top"
    class="form"
    :inline-message="true"
    @validate="validateResult"
  >
    <div class="issue-draft-block">
      <div class="flex">
        <el-form-item
          prop="payMethod"
          class="form-item-title"
        >
          <span slot="label" class="title-required">支付渠道</span>
          <div class="pay-type-item special-box default-disabed">
            <el-checkbox
              v-for="item in noBanList"
              :key="item.value"
              v-model="form[item.key]"
              type="button"
              size="small"
              :true-label="1"
              :false-label="0"
              :disabled="isPaymentChannelDisabled(item)"
              @change="handleChangePayMethod"
            >
              <el-tooltip
                v-if="item.value === PAYMENT_CHANNEL.ZHI_FU_YI_LIAN.id"
                class="item"
                effect="dark"
                content="不支持此渠道"
                placement="top"
              >
                <span>{{ item.name }}</span>
              </el-tooltip>
              <template v-else>
                <span>{{ item.name }}</span>
                <span v-if="item.hasNewTag" class="payment-channel-new-tag">NEW</span>
                <span
                  v-if="!item.isOpen && item.value !== PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id "
                  class="link open"
                  @click="opening(item)"
                >开通</span>
              </template>
            </el-checkbox>

            <div class="link" @click="goUserSetting">默认设置</div>
          </div>
        </el-form-item>
        <!-- 智付E+、智付邦+选中显示回款账户 -->

        <el-form-item
          v-if="form.acceptJdYlpay || form.acceptZbankPlus "
          prop="sellerBankAccountId"
          label="回款账户"
          class="form-item-title clear-after-cls"
        >
          <div class="pay-type-item">
            <el-select
              ref="sellerBankAccount"
              v-model="form.sellerBankAccountId"
              placeholder="请选择回款账户"
              size="small"
            >
              <el-option
                v-for="item in sellerBankAccountList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
              <div class="seller-select-btn" @click="() => { $router.push('/user-center/bank-account?tabStatus=2'); $refs.sellerBankAccount.blur(); }"><i class="el-icon-plus" />添加回款账户</div>
            </el-select>
          </div>
          <el-tooltip
            placement="top"
            popper-class="issue-draft-tooltip"
          >
            <template slot="content">
              <div>依照智付E+、E++渠道交易模式，票方发布票据必须选择回款账户(支持任意银行一般户)。需要在<span class="seller-bank-link" @click="() => { $router.push('/user-center/bank-account?tabStatus=2') }">银行回款账户</span>页面绑定回款账户，交易完成后票款自动提现到账。</div>
            </template>
            <icon class="icon icon-question" type="chengjie-wenti" />
          </el-tooltip>
        </el-form-item>
        <div style="margin-left: 30px;line-height: 32px;">
          <div class="link" @click="() => { $router.push('/user-center/bank-account?tabStatus=2') }">默认设置</div>
        </div>
      </div>
      <div
        class="block-switch-flex"
        :class="{
          'is-re-issue': isReIssue
        }"
      >
        <el-form-item prop="margin" class="block-flex">
          <template slot="label">
            <div class="g-flex-vc">
              保证金
              <el-tooltip
                placement="top"
                popper-class="issue-draft-tooltip"
              >
                <template slot="content">
                  <div v-if="isRongyiTicket">
                    保证金是为防范双方交易违约行为缴纳的资金，从保证金账户冻结， 按自定义的金额进行冻结，交易若无违约将全部退回，反之会全部划转给守约方。
                  </div>
                  <div v-else>
                    保证金是为防范双方交易违约行为缴纳的资金，从保证金账户冻结， 按票面金额的万分之 3 缴纳，最低 10 元，最高 600 元，交易若无违约将退回。
                    <p>
                      查看
                      <a
                        class="text-link"
                        :href="isNewVersionDraft ? PLATFORM_DEFAULT_RULESNEW_URL_NEW_DRAFT : PLATFORM_DEFAULT_RULESNEW_URL"
                        target="_blank"
                        rel="noopener noreferrer"
                      >《平台订单违约规则》</a>
                    </p>
                  </div>
                </template>
                <icon class="icon icon-question" type="chengjie-wenti" />
              </el-tooltip>
              <span v-if="!form.margin" class="margin-tips text-primary">关闭后，将无法约束交易违约行为及被接单速度</span>
            </div>
            <div v-if="(isReIssue && !form.margin) || !isReIssue" class="link default-setting-link" @click="goUserSetting">默认设置</div>
          </template>
          <div class="flex-item flex-item-margin">
            <el-switch
              v-model="form.margin"
              :active-value="1"
              :inactive-value="0"
              :width="40"
              @change="onMarginChange"
            />
            <div v-if="isRongyiTicket && form.margin" class="shendumi-cont">
              <el-form-item prop="marginAmount">
                <el-input
                  v-model="form.marginAmount"
                  style="width: 100%;"
                  :placeholder="`请输入${sdmName}`"
                  type="number"
                  size="small"
                  :number-format="{
                    negative: false,
                    maxDecimalLength: 2,
                    maxIntegerLength: 10,
                    leadingZero: false
                  }"
                />
              </el-form-item>
            </div>
            <span v-if="form.margin" class="margin-balance">{{ sdmName }}余额：<span class="balance">{{ (sdmInfo || {}).balanceAmt || '0.00' }}{{ sdmUnit }}</span></span>
            <span class="link link-margin-left" @click="handleRecharge">充值</span>
          </div>
        </el-form-item>

        <div v-if="isShowBargain" class="block-flex block-bargain" :class="isReIssue && 'order3'">
          <el-form-item>
            <template slot="label">
              <div class="g-flex-vc">
                议价设置
                <el-tooltip
                  placement="top"
                  popper-class="issue-draft-tooltip"
                  content="若您接受议价，资方发起议价申请后您可前往议价窗口选择同意或拒绝。若您同意，系统将自动帮您修改票据价格并确认订单。您可设置议价上限（资方不可见），超出上限的议价申请，系统将自动拒绝。您设置的议价上限银票不超过12%，财票不超过25%，商票不超过36%。"
                >
                  <icon class="icon icon-question" type="chengjie-wenti" />
                </el-tooltip>
              </div>
              <div class="link default-setting-link" @click="goUserSetting">默认设置</div>
            </template>
            <div class="flex-item flex-item-bargaining">
              <el-switch
                v-model="form.bargaining"
                :active-value="1"
                :inactive-value="0"
                :width="40"
              />
              <span class="switch-text">{{ form.bargaining ? '接受' : '不接受' }}议价</span>
              <div v-if="form.bargaining" class="bargaining-limit">
                <el-input
                  v-model="form.bargainingLimit"
                  placeholder="请输入议价上限"
                  :width="123"
                  type="number"
                  size="small"
                  :number-format="{
                    negative: false,
                    maxDecimalLength: 2,
                    maxIntegerLength: 5,
                    leadingZero: false
                  }"
                  class="bargaining-limit-input"
                >
                  <template slot="append">元</template>
                </el-input>
              </div>
            </div>
          </el-form-item>
        </div>

        <!--
          <el-form-item class="block-flex" :class="isReIssue && 'order2'">
          <template slot="label">
          <div>
          交易凭证
          <el-tooltip
          placement="top-start"
          popper-class="issue-draft-tooltip"
          >
          <template slot="content">
          开启交易凭证开关，表示您需要该笔订单的交易凭证（服务费每份 <span class="tooltip-color">5</span> 元），凭证服务费将在交易完成时扣除。
          您可以在支付电子账户内查看费用明细及下载交易凭证。交易凭证内容可点击查看右侧“示例”。
          </template>
          <icon class="icon icon-question" type="chengjie-wenti" />
          </el-tooltip>
          </div>
          </template>
          <div class="flex-item flex-item-voucher">
          <el-switch
          v-model="form.needVoucher"
          :active-value="1"
          :inactive-value="0"
          />
          <span v-if="form.needVoucher" class="switch-text service-amount">
          <span>凭证服务费：</span>
          <span class="tooltip-color">5</span>
          <span> 元</span>
          </span>
          <el-image :preview-src-list="[voucherImage]" img-text="示例" class="example" />
          </div>
          </el-form-item>
        -->
        <div v-if="isShowFastTrade" class="block-flex" :class="isReIssue && 'order4'">
          <el-form-item>
            <template slot="label">
              极速出票
              <el-tooltip
                placement="top"
                popper-class="issue-draft-tooltip"
                content="若开启极速出票，则免确认，且需要在 10 分钟内支付、背书，15 分钟内签收。"
              >
                <icon class="icon icon-question" type="chengjie-wenti" />
              </el-tooltip>
            </template>
            <transaction-tooltip-button :types="[TRANSACTION_TOOLTIP_TYPE.FAST]" class="flex-item flex-item-bargaining">
              <template v-slot:content="{disabled}">
                <el-switch
                  v-model="form.fastTrade"
                  :disabled="disabled"
                  :active-value="1"
                  :inactive-value="0"
                  :width="40"
                  @change="onFastTradeChange"
                />
                <span class="switch-text">{{ form.fastTrade ? '开启' : '关闭' }}极速出票</span>
              </template>
            </transaction-tooltip-button>
          </el-form-item>
        </div>
        <el-form-item v-if="isShowBlackList" class="block-flex black-block">
          <template slot="label">
            <div class="g-flex-vc">
              资方所在地黑名单
              <el-tooltip
                placement="top"
                popper-class="issue-draft-tooltip"
                content="开启后，黑名单地区资方将看不到您发布的票据。"
              >
                <icon class="icon icon-question" type="chengjie-wenti" />
              </el-tooltip>
            </div>
            <div class="link default-setting-link" @click="goUserSetting(SETTING_USER.SALE.id, (SALE_SETTING_OPTIONS.BUY_AREA_BLACKLIST.id))">默认设置</div>
          </template>
          <BlackList v-model="form.blackRegionList" class="black-list" />
        </el-form-item>
      </div>
      <div
        v-if="corpInfo && corpInfo.yilianPayOpen === 1"
        class="block-switch-flex"
        :class="{
          'is-re-issue': isReIssue
        }"
      >
        <el-form-item class="block-flex trade-contract">
          <template slot="label">
            <div class="g-flex-vc">
              贸易合同及发票
              <el-tooltip
                placement="top"
                popper-class="issue-draft-tooltip"
                content="上传您与出票人的贸易背景或您与上一手企业的贸易合同及发票"
              >
                <icon class="icon icon-question" type="chengjie-wenti" />
              </el-tooltip>
              <span class="tips-upload-text">支持pdf格式，不超过20M</span>
            </div>
          </template>
          <FileUpload
            v-model="form.tradeContractUrl"
            :size-limit="20"
            :show-file="true"
            accept="application/pdf"
            :dir="OSS_DIR.DRAFT"
          >
            <div slot="empty" class="trade-btn">
              <el-button
                slot="reference"
                height="32"
                plain
              >
                <icon class="icon icon-plus-square" type="chengjie-plus-square" />上传贸易合同
              </el-button>
            </div>
          </FileUpload>
          <FileUpload
            v-model="form.invoiceUrl"
            :size-limit="20"
            :show-file="true"
            accept="application/pdf"
            :dir="OSS_DIR.DRAFT"
          >
            <div slot="empty" class="trade-btn">
              <el-button
                slot="reference"
                height="32"
                plain
              >
                <icon class="icon icon-plus-square" type="chengjie-plus-square" />上传发票
              </el-button>
            </div>
          </FileUpload>
        </el-form-item>
        <!-- 联系方式设置 -->
        <!--
          <ContactSettingField ref="contactSettingRef" v-model="form.mobile" class="custom-contact-cls contact-field">
          <template #label>
          <span class="contact-label">联系方式</span>
          </template>
          </ContactSettingField>
        -->
      </div>
    </div>
    <!-- 米充值 -->
    <Recharge ref="recharge" />
  </el-form>
</template>

<script>
import Recharge from '@/views/components/user-center/recharge/recharge.vue' // 米充值
import BlackList from './black-list.vue' // 黑名单
// import ContactSettingField from '@/views/pages/setting/components/contact-setting-field.vue'
import FileUpload from '@/views/components/common/img-upload/file-upload-button.vue'
import otherOptions from '@/views/pages/issue-draft/mixins/other-options-new'

export default {
  name: 'other-options-new',

  components: {
    Recharge,
    BlackList,
    // ContactSettingField,
    FileUpload
  },
  mixins: [otherOptions]

}
</script>
