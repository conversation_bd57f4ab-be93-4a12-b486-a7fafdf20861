<!-- 黑名单弹框 -->
<style lang="scss" scoped>
.dialog-main {
  margin-top: 12px;
  padding: 16px;
  background: $color-FFFFFF;

  .el-form ::v-deep {
    .el-form-item {
      margin-bottom: 0;
    }

    .el-form-item__label {
      padding-bottom: 2px;
      color: $color-text-secondary;
      line-height: 22px;
    }

    .el-form-item__error {
      position: relative;
    }

    .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label::before {
      display: none;
    }
  }

  .form-flex {
    @include flex-sb;

    .el-form-item {
      flex: 1;
      margin-right: 10px;

      &:last-child {
        margin-right: 0;
      }
    }

    .el-select {
      width: 100%;
    }
  }
}

.dialog-footer {
  padding: 12px 0;
}
</style>

<template>
  <el-dialog
    :title="`地区黑名单${isEdit ? '编辑' : '新增'}`"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :append-to-body="true"
    :before-close="handleDialogCancel"
    width="600px"
  >
    <RedWarnContent>
      如需要屏蔽全省，只选择省份即可（市一级为非必选项）！
    </RedWarnContent>
    <div class="dialog-main">
      <el-form
        ref="ruleForm"
        :model="form"
        :rules="rules"
        label-position="top"
      >
        <div class="form-flex">
          <el-form-item label="省" prop="provinceCode">
            <el-select
              v-model="form.provinceCode"
              filterable
              placeholder="请选择省"
              @change="handleChangeProvince"
            >
              <el-option
                v-for="item in region"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="市" prop="cityCode">
            <el-select
              v-model="form.cityCode"
              placeholder="请选择市"
              filterable
              @change="handleChangeCity"
            >
              <el-option
                v-for="item in cityList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="dialog-footer">
      <el-button @click="handleDialogCancel">取消</el-button>
      <el-button
        type="primary"
        @click="handleChangeRegion"
      >
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import region from '@/common/json/region-code.json' // 地址库
import RedWarnContent from '@/views/components/common/warn-content.vue'
const defaultForm = {
  currentEditId: '',
  searchText: '', // 黑名单输入文字
  provinceName: '', // 地区-省，非空字段
  provinceCode: '', // 地区-省编码，非空字段
  cityName: '', // 地区-市，非空字段
  cityCode: '' // 地区-市编码，非空字段
}

export default {
  name: 'black-list',
  components: {
    RedWarnContent,
  },
  props: {
    type: String, // 黑名单列表
  },
  data() {
    return {
      isEdit: false,
      region,
      selectedRegion: [], // 地址级联选框选中值，用于在选择后再次打开时不回显
      blackRes: [],
      cityList: [], // 市列表
      blackCode: [],
      tableData: [], // 设置弹框需要用到
      visible: false,
      form: { ...defaultForm },
      //  新增和编辑校验规则
      rules: {
        provinceCode: [{ required: true, message: '请选择省', trigger: 'none' }]
      },
      index: [], // 需要删除的index
      ids: [] // 需要删除的ids
    }
  },
  methods: {
    init(options) {
      this.index = []
      this.ids = []
      this.visible = true
      this.tableData = options.tableData
      this.blackCode = options.blackCode
      if (options.editForm) {
        this.isEdit = true
        this.form.provinceName = options.editForm.provinceName
        this.form.provinceCode = options.editForm.provinceCode
        // handleChangeProvince 必须在cityCode前面，因为handleChangeProvince需要置空cityCode
        this.handleChangeProvince(options.editForm.provinceCode)
        this.form.cityName = options.editForm.cityName
        this.form.cityCode = options.editForm.cityCode
        this.form.currentEditId = options.editForm.currentEditId
      }
    },
    // 地址级联选择器改变回调
    handleChangeRegion() {
      const form = { ...this.form }
      // 判断是否重复
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          let code = form.cityCode || form.provinceCode
          let level = form.cityCode ? 2 : 1
          // 完全重复 提示请勿重复选择 如果不是完全重复，再去判断是否是上下级关系 当选择省份时有下级城市 需要自动删除城市
          if (this.blackCode.length && this.blackCode.includes(code)) {
            this.$message.warning('请勿重复选择！')
          } else if (level === 1 && this.subordinate(code, form)) {
            this.deleteType(form.provinceName, form)
          } else if (level === 1 && !this.subordinate(code, form)) {
            this.$emit('on-save', form)
          } else if (level === 2) {
            this.upLevel(code, form)
          } else {
            this.$message.warning('请勿重复选择！')
          }
        }
      })
    },
    upLevel(val, form) {
      val = `${val.substring(0, 2)}0000`
      if (this.blackCode.includes(val)) {
        this.$message.warning(`已存在${form.provinceName}，请勿重复选择！`)
      } else {
        this.$emit('on-save', form)
      }
    },

    // 发布页面  判断是否有已选列表是否有上下级 省判断下级  市判断上级
    subordinate(val) {
      val = val.substring(0, 2)
      let len = this.blackCode.length
      for (let i = len - 1; i >= 0; i--) {
        // 存在下级市区
        if (this.blackCode[i].substring(0, 2) === val) {
          this.type === 'issue' ? this.index.push(i) : this.ids.push(this.tableData[i].id)
        }
      }
      // 判断删除
      return this.ids.length > 0 || this.index.length > 0
    },
    // 目前用户设置只做提示
    deleteType(name, form) {
      // 当前选择省份   判断其有无下级  有下级需要 用户确认是否删除数据
      if (this.type !== 'issue') {
        this.$message.warning(`已存在${name}省份下城市，请先删除相关城市再添加。`)
        // this.$confirm(`已存在${name}省份下城市，点击确认将自动去重。`, '提示', {
        //   confirmButtonText: '确认',
        //   type: 'warning',
        //   iconPosition: 'title',
        //   showClose: false,
        // }).then(() => {
        //   this.ids.forEach(item => {
        //     this.$emit('on-delete', item)
        //   })
        //   setTimeout(() => {
        //     this.$emit('on-save', form)
        //   }, 100);
        // })
        //   // eslint-disable-next-line no-empty-function
        //   .catch(() => {
        //   })
      } else {
        this.$emit('on-delete', this.index)
        this.$message.warning(`已自动删除${name}下的城市`)
        this.$emit('on-save', form)
      }
    },
    // 取消按钮
    handleDialogCancel() {
      this.visible = false
      this.form = { ...defaultForm }
      this.currentEditId = null
      this.$nextTick().then(() => {
        this.$refs.ruleForm.clearValidate()
      })
    },
    // 选择省回调
    handleChangeProvince(val) {
      const current = this.region.filter(item => item.value === val)
      if (current && current.length) {
        this.cityList = current[0].children
        this.form.provinceName = current[0].label
      }
      this.form.cityCode = ''
      this.form.cityName = ''
    },

    // 选择市回调
    handleChangeCity(val) {
      const current = this.cityList.filter(item => item.value === val)
      this.form.cityName = current?.[0]?.label || ''
    },
  }
}
</script>
