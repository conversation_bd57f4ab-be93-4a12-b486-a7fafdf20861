<template>
  <el-upload
    ref="upload"
    :action="oss.host || ''"
    :file-list="fileList"
    :limit="limit"
    :data="formData"
    :on-preview="handlePreview"
    :on-exceed="handleExceed"
    :on-success="handleSuccess"
    :on-remove="handleRemove"
    :on-error="handleError"
    :before-upload="beforeUpload"
  >
    <slot />
  </el-upload>
</template>

<script>
import { randomString } from '@/common/js/util'

// 本地文件远程路径映射
const fileNameMap = {}

export default {
  name: 'file-upload',
  props: {
    value: {
      type: String,
    },
    limit: {
      type: Number,
      default: 1
    },
  },
  data() {
    return {
      formData: {},
      fileList: []
    }
  },
  computed: {
    oss() {
      return this.$store.state.common.oss
    }
  },
  created() {
    // 回显数据
    if (typeof this.value === 'string' && this.value !== '') {
      this.fileList = this.value.split(',').map(url => ({
        name: url.slice(url.lastIndexOf('/') + 1),
        url,
      }))
    }
  },
  methods: {
    // 同步已上传文件给绑定值
    setBindValue() {
      const files = (this.$refs?.upload?.uploadFiles ?? []).filter(file => file.status === 'success')
      this.$emit('input', files.map(file => fileNameMap[file.uid] ?? file.url).join())
    },
    // 获取混淆后的文件名
    getFileName(file) {
      const randomStringLength = 6
      const fileName = file.name
      const randomName = `${randomString(randomStringLength, 'mix', '')}_${Date.now()}_${fileName}`
      // 要上传到后端返回的指定路径下，否则报无权限
      return `draft/${randomName}`
    },
    beforeUpload(file) {
      const fileName = this.getFileName(file)
      fileNameMap[file.uid] = `${this.oss.host}/${encodeURIComponent(fileName)}`
      Object.assign(this.formData, {
        policy: this.oss.policy,
        OSSAccessKeyId: this.oss.accessKeyId,
        signature: this.oss.signature,
        success_action_status: '200',
        key: fileName,
      })
    },
    handlePreview(file) {
      window.open(fileNameMap[file.uid] || file.url, '_blank')
    },
    handleExceed() {
      this.$message.warning(`最多上传${this.limit}个文件`)
    },
    handleSuccess() {
      this.setBindValue()
    },
    handleRemove() {
      this.setBindValue()
    },
    handleError() {
      this.setBindValue()
    }
  }
}
</script>
