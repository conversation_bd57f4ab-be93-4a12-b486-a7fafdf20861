<!-- 闭市提示 -->
<style lang="scss" scoped>
.is-disabled {
  // color: $color-text-light !important;
  cursor: not-allowed !important;
}

.link {
  @include example-underline;
}

.btn-right {
  padding: 10px 8px 0;
  text-align: right;
}
</style>

<template>
  <el-tooltip
    :offset="offset"
    :placement="placement"
    :popper-style="{lineHeight: '22px'}"
    class="transaction-tooltip-button"
    :disabled="!nowType"
    :transition="!nowType ? '' : 'el-fade-in-linear'"
  >
    <div
      v-if="$scopedSlots.content"
      v-bind="$attrs"
      :class="btnDisabled && 'is-disabled'"
      v-on="{...$listeners, click: () => {}}"
      @click.capture="btnDisabled ? (e) => {} : $listeners.click && $listeners.click($event) "
    >
      <slot name="content" :disabled="!!nowType" />
    </div>
    <el-button
      v-else
      v-waiting="vWaitingValue"
      v-bind="$attrs"
      :type="$attrs.type || 'primary'"
      :border="$attrs.border"
      :height="$attrs.height"
      :width="$attrs.width"
      :class="btnDisabled && 'is-disabled'"
      v-on="{...$listeners, click: () => {}}"
      @click.capture="btnDisabled ? () => {} : $listeners.click($event) "
    >
      <slot />
    </el-button>

    <template slot="content" style="white-space: pre-line;">
      <p v-if="nowType === TRANSACTION_TOOLTIP_TYPE.HOLIDAY">闭市时间<br>{{ holidayCloseTime[0] }} ~ {{ holidayCloseTime[1] }}</p>

      <p v-else-if="nowType === TRANSACTION_TOOLTIP_TYPE.DAILY">{{ dailyCloseTime[0] }} ~ {{ dailyCloseTime[1] }}<br>接单功能关闭</p>

      <div v-else-if="nowType === TRANSACTION_TOOLTIP_TYPE.RADAR">
        <p>
          由于您 <strong class="text-primary">{{ radarLimit.startTime }}</strong> 在自动订单支付环节的违约情<br>况触发限制条件，当前已禁用自动接单功能。<br>
          <strong class="text-primary">{{ radarLimit.endTime }}</strong> 自动恢复。
        </p>
        <p>
          如有疑问可联系客户经理或 <a
            class="link"
            :href="PLATFORM_DEFAULT_RULESNEW_URL"
            target="_blank"
            rel="noopener noreferrer"
          >查看平台订单违约规则</a>
        </p>
      </div>

      <div v-else-if="nowType === TRANSACTION_TOOLTIP_TYPE.FAST">
        <p>
          由于您 <strong class="text-primary">{{ fastLimit.startTime }}</strong> 在极速订单背书环节的违约情<br>况触发限制条件，当前已禁用发布极速订单功能。<br>
          <strong class="text-primary">{{ fastLimit.endTime }}</strong> 自动恢复。
        </p>
        <p>
          如有疑问可联系客户经理或 <a
            class="link"
            :href="PLATFORM_DEFAULT_RULESNEW_URL"
            target="_blank"
            rel="noopener noreferrer"
          >查看平台订单违约规则</a>
        </p>
      </div>

      <div v-else-if="nowType === TRANSACTION_TOOLTIP_TYPE.CREDIT">
        <p>
          您当前账号的信用分为 {{ creditInfo.currentCreditPoints || 0 }} 分，<br> 低于 500 分时只能进行带保交易。<br>  请您遵守平台交易规范，避免违约。
        </p>
        <p>
          <a
            class="link"
            :href="PLATFORM_DEFAULT_RULESNEW_URL"
            target="_blank"
            rel="noopener noreferrer"
          >《平台订单违约规则》</a>
          <a
            class="link"
            target="_blank"
            rel="noopener noreferrer"
            @click="onSeeCreditRule"
          >《信用分规则》</a>
        </p>
        <p class="btn-right">
          <el-button
            width="64"
            height="28"
            type="primary"
            @click="closeCredit"
          >
            我知道了
          </el-button>
        </p>
      </div>
    </template>
  </el-tooltip>
</template>

<script>
import { formatTime } from '@/common/js/date'
import { TRANSACTION_TOOLTIP_TYPE } from '@/constants/transaction-tooltip'
import { mapState } from 'vuex'
import { SITE_OPEN_CREDIT } from '@/event/modules/site'
import creditApi from '@/apis/credit'
import { PLATFORM_DEFAULT_RULESNEW_URL } from '@/constants/oss-files-url'
import { USER_MOBILE } from '@/constant-storage' // 开户信息对象
import Storage from '@/common/js/storage' // 本地缓存对象
export default {
  name: 'transaction-tooltip-button',
  props: {
    // 不显示提示
    hideTip: {
      type: Boolean,
      default: false
    },
    // 需要的提示类型
    types: {
      type: Array,
      default: () => ([])
    },
    // tooltip位置
    placement: {
      type: String,
      default: 'top'
    },
    // tip偏移量
    offset: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      TRANSACTION_TOOLTIP_TYPE, // 交易限制提示类型
      PLATFORM_DEFAULT_RULESNEW_URL, // 平台违约规则url
      nowType: '', // 当前显示类型，空为不显示
      disabledTypeList: [
        TRANSACTION_TOOLTIP_TYPE.HOLIDAY,
        TRANSACTION_TOOLTIP_TYPE.DAILY,
        TRANSACTION_TOOLTIP_TYPE.RADAR,
        TRANSACTION_TOOLTIP_TYPE.FAST,
      ], // 需要禁用按钮的类型
      vWaitingValue: [], // 点击约束绑定值
      // whiteList: ['15000000048', '15753950263', '13355004715', '17560153697', '15988888002', '18306502402'], // 自动禁用白名单 白名单内的不会触发禁用
      whiteList: [], // 自动禁用白名单 白名单内的不会触发禁用
    }
  },
  computed: {
    storeCommon() {
      return (this.$store || {}).state?.common
    },
    // 节假日闭市开始时间
    closeMarketStartTime() {
      return this.storeCommon?.closeMarketStartTime
    },
    // 当前是否节假日闭市
    closeMarket() {
      return this.storeCommon?.closeMarket
    },
    // 节假日闭市结束时间
    closeMarketEndTime() {
      return this.storeCommon?.closeMarketEndTime
    },

    // 日常闭市开始时间
    everydayStartTime() {
      return this.storeCommon?.everydayStartTime
    },
    // 当前是否日常闭市
    closeMarketByEveryday() {
      return this.storeCommon?.closeMarketByEveryday
    },
    // 日常闭市结束时间
    everydayEndTime() {
      return this.storeCommon?.everydayEndTime
    },

    // 服务器时差
    // diffTime() {
    //   return this.storeCommon?.diffTime
    // },
    // 节假日闭市时间
    holidayCloseTime() {
      return [formatTime(this?.closeMarketStartTime, ('MM/DD hh:mm')), formatTime(this?.closeMarketEndTime, ('MM/DD hh:mm'))]
    },
    // 日常闭市时间
    dailyCloseTime() {
      return [this.everydayStartTime, this.everydayEndTime]
    },
    // 获取违约限制自动信息
    radarLimit() {
      return this.storeCommon.radarLimit
    },
    // 获取极速限制自动信息
    fastLimit() {
      return this.storeCommon.fastLimit
    },
    // 是否白名单
    isWhiteList() {
      let mobile = Storage.get(USER_MOBILE)
      return !this.whiteList.includes(mobile)
    },
    // 按钮禁用
    btnDisabled() {
      return this.isWhiteList && this.nowType && this.disabledTypeList.includes(this.nowType)
    },
    // 是否显示信用分提醒
    ...mapState('user', {
      showCreditRemind: 'showCreditRemind',
      creditInfo: 'creditInfo',
    }),
  },

  watch: {
    types() {
      this.initTip()
    },
    hideTip() {
      this.initTip()
    },
    closeMarket() {
      this.initTip()
    },
    closeMarketByEveryday() {
      this.initTip()
    },
    showCreditRemind() {
      this.initTip()
    }
  },

  created() {
    this.initTip()
  },

  methods: {
    // 初始化显示提示
    initTip() {
      if (!this.$store) return
      this.nowType = ''
      if (!this.hideTip) {
        this.showCreditTip()
        this.showDailyTip()
        this.showHolidayTip()
        this.showRadarTip()
        this.showFastTip()
        // 白名单中的账号不做限制
        if (!this.isWhiteList) {
          this.nowType = ''
        }
        this.$emit('change', this.nowType)
      }
    },
    // 显示信用分提示，优先级最低
    showCreditTip() {
      const needShow = this.types.includes(TRANSACTION_TOOLTIP_TYPE.CREDIT)
      if (needShow && this.showCreditRemind) {
        this.nowType = TRANSACTION_TOOLTIP_TYPE.CREDIT
      }
    },
    // 显示日常闭市
    showDailyTip() {
      const needShow = this.types.includes(TRANSACTION_TOOLTIP_TYPE.DAILY)
      // if (needShow && inTimeRange(new Date().getTime() - this.diffTime, this.dailyCloseTime)) {
      if (needShow && this.closeMarketByEveryday) {
        this.nowType = TRANSACTION_TOOLTIP_TYPE.DAILY
      }
    },
    // 显示节假日闭市,优先于日常闭市
    showHolidayTip() {
      const needShow = this.types.includes(TRANSACTION_TOOLTIP_TYPE.HOLIDAY)
      if (needShow && this.closeMarket) {
        this.nowType = TRANSACTION_TOOLTIP_TYPE.HOLIDAY
      }
    },
    // 显示自动接单限制提示,优先于闭市
    showRadarTip() {
      const needShow = this.types.includes(TRANSACTION_TOOLTIP_TYPE.RADAR)
      if (needShow && this.radarLimit.punishIng) {
        this.nowType = TRANSACTION_TOOLTIP_TYPE.RADAR
      }
    },
    // 显示极速订单限制提示，优先于闭市
    showFastTip() {
      const needShow = this.types.includes(TRANSACTION_TOOLTIP_TYPE.FAST)
      if (needShow && this.fastLimit.punishIng) {
        this.nowType = TRANSACTION_TOOLTIP_TYPE.FAST
      }
    },
    // 显示信用分规则
    onSeeCreditRule() {
      this.$event.emit(SITE_OPEN_CREDIT)
    },

    // 关闭信用分提示
    async closeCredit() {
      await creditApi.closeCreditRemind()
      this.$store.commit('user/setShowCreditRemind', 0)
    }
  }
}
</script>
