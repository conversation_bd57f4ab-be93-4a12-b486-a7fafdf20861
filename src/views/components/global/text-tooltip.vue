<!-- 这是省略文本页面（组件）参数与回调与el-tooltip组件一致 -->
<style lang="scss" scoped>
.ellipsis-text {
  overflow: hidden;
  width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>

<template>
  <el-tooltip
    v-bind="{ ...defaultOptions, ...$attrs, content: `${$attrs.content + ''}` }"
    v-on="$listeners"
  >
    <template v-if="icon">
      <icon
        :class="['icon', {
          [`icon-${icon}`]: `${icon ? `icon-${icon}` : ''} `
        }]"
        :type="icon"
      />
    </template>
    <template v-else>
      <div :class="{'ellipsis-text': ellipsis}">
        <slot>
          {{ $attrs.content }}
        </slot>
      </div>
    </template>
  </el-tooltip>
</template>

<script>
export default {
  name: 'text-tooltip',
  props: {
    // 只显示icon
    icon: {
      type: String, // icon的type
      default: '',
    },
    // 是否需要省略文本
    ellipsis: {
      type: Boolean,
      default: true,
    }
  },
  data() {
    return {
      // toolTips默认配置
      defaultOptions: {
        placement: 'top',
        effect: 'dark',
        openDelay: 500,
        // showWhenOverflow: true, // 超出显示
      }
    }
  },
  computed: {
  },
  methods: {

  },
}
</script>
