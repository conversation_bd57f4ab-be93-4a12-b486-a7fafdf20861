<template>
  <div :style="info.style">
    <span v-if="isStart" :style="isTimeout ? 'color:#EC3535;' : 'color:#0076F6'">{{ countTime }}</span>
  </div>
</template>

<script>
import {
  dealTime, // 时间兼容
  formatDurationHour, // 根据时间戳格式化时间为20:00:00格式，用于倒计时
} from '@/common/js/date'

export default {
  name: 'count-down',
  props: {
    info: {
      type: Object,
    }
    // info = { targeTime: 目标时间, style: 自定义样式规则, futureDays: 天数}
  },
  data() {
    return {
      countTime: null,
      isTimeout: false, // 是否超时
      isStart: false, // 是否开始计时
      timer: null, // 计时器
    }
  },
  mounted() {
    this.startCountDown()
  },
  methods: {
    setCountTime(targeTime) {
      const currentDate = dealTime(new Date()) // 当前时间
      const diff = dealTime(targeTime) - currentDate // 计算时间差
      if (diff < 0) { // 超时处理
        this.isTimeout = true
      } else {
        this.isTimeout = false
      }
      this.countTime = formatDurationHour(diff, 'string')
    },
    startCountDown() {
      let { targeTime } = this.info
      // eslint-disable-next-line vue/no-mutating-props
      // targeTime = dealTime(new Date('2023-1-21 10:56:48')) // 测试使用时间

      // 倒计时时间计算 目标时间往后推futureDays天
      // eslint-disable-next-line vue/no-mutating-props
      targeTime = targeTime + 1000 * 60 * 60 * 24 * this.info.futureDays
      // 计算出小时数
      let hour = Math.floor((dealTime(targeTime) - dealTime(new Date())) / 3600000)
      // eslint-disable-next-line vue/custom-event-name-casing
      this.$emit('setIsTimeOut', 'not-started') // 未开始倒计时设置
      if (hour > this.info.futureDays * 24) return // 根据传入的天数计算是否开始倒计时
      this.isStart = true
      const start = () => {
        if (this.timer) {
          clearTimeout(this.timer)
          this.timer = null
        }
        this.setCountTime(targeTime)

        // eslint-disable-next-line vue/custom-event-name-casing
        this.$emit('setIsTimeOut', this.isTimeout) // 开始倒计时设置是否超时

        this.timer = setTimeout(start, 1000)
      }
      start()
      this.$once('hook:deactivated', () => {
        clearTimeout(this.timer)
        this.timer = null
      })
    }
  }
}
</script>
