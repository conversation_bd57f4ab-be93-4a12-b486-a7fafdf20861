<!-- 识单助手识别结果 -->
<style lang="scss" scoped>
.recognition-result-dialog {
  .recognition-result-dialog-content {
    padding: 10px;
    background: #FFFFFF;

    .content-form {
      padding-top: 10px;
    }

    ::v-deep {
      .el-form-item {
        margin-bottom: 12px;
      }

      .el-form-item__content {
        margin-left: 0;
      }

      .el-form-item__label {
        text-align: left;
        line-height: 30px;
      }
    }

    .flex-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .label-text {
      padding-bottom: 8px;
      font-size: 16px;
      color: #999999;
    }

    .value-text {
      font-size: 16px;
      color: #333333;
    }

    .p-b-10 {
      padding-bottom: 10px;
    }
  }

  .footer-content {
    padding-top: 10px;

    // display: flex;
    // justify-content: space-between;
    // align-items: center;
    text-align: end;
  }
}
</style>

<style lang="scss">
.recognition-result-dialog {
  .el-dialog__body {
    padding: 15px;
  }
}
</style>

<template>
  <div>
    <el-dialog
      title="识别订单结果"
      :visible.sync="visible"
      width="500px"
      class="recognition-result-dialog"
      append-to-body
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <div class="recognition-result-dialog-content">
        <WarnContent>
          {{ tableData.length > 1 ? '仅该笔订单当前操作类型的状态识别结果均为“成功”时，您可以点击弹窗下方按钮完成平台订单操作。' : '仅该笔订单当前操作类型的状态识别结果为“成功”时，您可以点击弹窗下方按钮完成平台订单操作。' }}
        </WarnContent>
        <div class="content-form">
          <!-- 批量订单显示 -->
          <template v-if="tableData.length > 1">
            <el-table :data="tableData" border>
              <el-table-column prop="orderNo" label="订单编号" />
              <el-table-column prop="tradeType" label="网银操作类型" />
              <el-table-column prop="tradeStatus" label="网银支付状态">
                <template slot-scope="scope">
                  <span>{{ scope.row.tradeStatus || '-' }}</span>
                </template>
              </el-table-column>
            </el-table>
          </template>
          <!-- 单笔订单显示 -->
          <template v-else>
            <div v-if="tableData.length">
              <div class="p-b-10">
                <div class="label-text">订单编号</div>
                <div class="value-text">{{ tableData[0].orderNo || '-' }}</div>
              </div>
              <div class="flex-row">
                <div>
                  <div class="label-text">网银操作类型</div>
                  <div class="value-text">{{ tableData[0].tradeType || '-' }}</div>
                </div>
                <div>
                  <div class="label-text">网银支付状态</div>
                  <div class="value-text">{{ tableData[0].tradeStatus || '-' }}</div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div class="footer-content">
        <el-button @click="handleClose">{{ type === E_PLUS_RECOGNITION_SCENE.ORDER_CANCEL.id ? '暂不取消' : '取 消' }}</el-button>
        <el-button type="primary" :disabled="!isConfirm || !tableData.length" @click="confirm">{{ E_PLUS_RECOGNITION_SCENE_MAP[type] }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import WarnContent from '@/views/components/common/warn-content.vue' // 提示
import ylBankApi from '@/apis/yl-bank'
import { E_PLUS_RECOGNITION_SCENE, E_PLUS_RECOGNITION_SCENE_MAP, E_PLUS_RECOGNITION_SCENE_TYPE, DRAFT_STATUS } from '@/constant'
export default {
  name: 'recognition-result-dialog',
  components: {
    WarnContent
  },
  data() {
    return {
      E_PLUS_RECOGNITION_SCENE,
      E_PLUS_RECOGNITION_SCENE_MAP,
      visible: false,
      type: null, // 识单助手场景 1:订单支付 2:订单签收 3:订单取消
      tableData: [], // 银行订单状态数据
      orderNoList: null, // 传入需要查询的订单号
      tabStatus: null // 订单状态
    }
  },
  computed: {
    // 是否是待支付订单
    isWaitingPay() {
      return this.tabStatus === DRAFT_STATUS.WAITING_PAY.id
    },
    // 是否可以确认操作 必须全部都是成功才可以操作确认 || 取消待支付环节不论识别结果都可以取消
    isConfirm() {
      return this.tableData.every(item => item.tradeStatus === '成功') || (this.isWaitingPay && this.type === E_PLUS_RECOGNITION_SCENE.ORDER_CANCEL.id)
    }
  },
  methods: {
    init(param) {
      this.orderNoList = param?.orderNo || null // 订单号
      this.type = param?.type // 识单助手场景 1:订单支付 2:订单签收 3:订单取消
      this.visible = true
      this.tabStatus = param?.tabStatus || null // 订单状态
      this.queryYlBankOrderStatus()
    },

    // 处理订单记录,按订单号分组并按优先级选择记录
    processTableData(list) {
      const result = []
      // const orderNoList = [...new Set(list.map(item => item.orderNo))]
      // 状态优先级定义 优先取成功 > 处理中 > 失败
      this.orderNoList.forEach(orderNo => {
        const currentList = list.filter(item => item.orderNo === orderNo)
        const success = currentList.find(item => item.tradeStatus === '成功') // 成功状态的数据
        const processing = currentList.find(item => item.tradeStatus === '处理中') // 处理中的数据
        const failed = currentList.find(item => item.tradeStatus === '失败') // 失败状态的数据
        const noRecord = currentList.find(item => item.tradeStatus === '' || item.tradeStatus === null || item.tradeStatus === undefined) // 无记录的情况

        result.push(success || processing || failed || noRecord || { orderNo, tradeType: E_PLUS_RECOGNITION_SCENE_TYPE[this.type], tradeStatus: '' })
      })
      return result || []
    },

    // 查询亿联银行订单状态
    async queryYlBankOrderStatus() {
      const res = await ylBankApi.queryYlBankOrderStatus({ orderNoList: this.orderNoList })
      // 过滤符合当前场景的订单 1:订单支付 2:订单签收 3:订单取消
      const list = (res || []).filter(item => item.tradeType === E_PLUS_RECOGNITION_SCENE_TYPE[this.type])
      this.tableData = this.processTableData(list)
    },

    handleClose() {
      this.visible = false
    },

    confirm() {
      this.$emit('success')
      this.handleClose()
      // 识别确认操作
    }
  },
}
</script>
