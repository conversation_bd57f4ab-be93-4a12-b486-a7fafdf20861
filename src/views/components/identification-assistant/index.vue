<!-- 识单助手 -->

<style lang="scss" scoped>
.query-order-price-button {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;

  .btn-icon {
    margin-right: 8px;
  }

  .new-tag {
    position: absolute;
    top: -20px;
    right: -22px;

    @include new-icon;
  }
}

.primary {
  color: $--color-primary;
}
</style>

<template>
  <div class="query-order-price">
    <slot name="button">
      <el-button
        type="primary"
        width="112"
        :height="String(height)"
        @click="ebankStatusIdentify"
      >
        <slot>
          <div class="query-order-price-button">
            <span>识单助手</span>
            <!-- <span class="new-tag">NEW</span> -->
          </div>
        </slot>
      </el-button>
      <el-tooltip
        placement="top"
      >
        <div slot="content">
          使用识单助手前，请确保您已在浏览器登录<span class="primary">简易版网银-延时转账交易查询</span>页面且在当前页面内可查看到目标订单。
        </div>
        <icon
          class="icon icon-question"
          type="chengjie-wenti"
        />
      </el-tooltip>
    </slot>
    <RecognitionResultDialog ref="recognitionResultDialogRefs" @success="handleSuccess" />
  </div>
</template>

<script>
import RecognitionResultDialog from './recognition-result-dialog.vue'
import ylBankApi from '@/apis/yl-bank'

export default {
  name: 'identification-assistant',
  components: {
    RecognitionResultDialog
  },
  props: {
    height: {
      type: [Number, String],
      default: 30
    },
    order: {
      type: [Object, Array],
      default: () => ({})
    },
    type: { // 识单助手场景 1:订单支付 2:订单签收 3:订单取消
      type: [Number, String],
    },
    checkData: {
      type: Function,
    }
  },
  computed: {
    // 是否批量
    hasAllOrder() {
      return this.order instanceof Array
    },
  },
  created() {
    if (this.$ipc) {
      this.$ipc.removeAllListeners('MARKET_DRAFT_GET_DISCONNECTION_DATA_EX')
      this.$ipc.on('MARKET_DRAFT_GET_DISCONNECTION_DATA_EX', this.handleEBankStatusIdentify)
    }
  },
  beforeDestroy() {
    if (this.$ipc) {
      this.$ipc.removeAllListeners('MARKET_DRAFT_GET_DISCONNECTION_DATA_EX')
    }
  },
  methods: {
    async ebankStatusIdentify() {
      // 校验父组件必填数据是否都填写
      if (this.checkData) {
        const res = await this.checkData()
        if (!res) {
          return
        }
      }
      if (!this.$ipc) {
        this.$message.warning('请在客户端内进行识别操作')
        return
      }
      if (navigator.platform.indexOf('Win') === -1) {
        this.$message.error('当前系统不支持该功能！')
        return
      }
      try {
        this.$ipc.send('MARKET_DRAFT_RECOGNIZE_SHOW_LAYER', '', 10)
      } catch (e) {
        // xxx
      }
    },
    async handleEBankStatusIdentify(_, data) {
      const handleError = () => {
        this.$confirm('<div>当前窗口未检测到延时转账交易记录。</div><div>请检查是否打开了简易版网银-延时转账交易查询页面。</div><div>建议使用IE浏览器或360浏览器兼容模式。</div>', '提示', {
          dangerouslyUseHTMLString: true,
          type: 'warning',
          showClose: false,
          iconPosition: 'title',
          confirmButtonText: '知道了',
          showCancelButton: false,
        })
      }
      // 取消识别
      if (!data) {
        return this.$message.error('取消识别')
      }
      // 识别失败
      if (data.code !== 0 || data.bankName === '暂不支持此银行') {
        return handleError()
      }
      // C++传输HTML给后端识别
      if (typeof data.html === 'string') {
        if (data.html.length === 0) {
          return handleError()
        }
        try {
          await ylBankApi.identifyYlBankStatus({
            type: 1,
            bankUrl: data.bankUrl,
            html: data.html,
          })
          this.$refs.recognitionResultDialogRefs.init({ orderNo: this.hasAllOrder ? this.order.map(item => item.orderNo) : [this.order?.orderNo], type: this.type })
        } catch {
          handleError()
        }
        return
      }
      handleError()
    },
    // 识别结果操作确认
    handleSuccess() {
      this.$emit('success')
    }
  }
}
</script>
