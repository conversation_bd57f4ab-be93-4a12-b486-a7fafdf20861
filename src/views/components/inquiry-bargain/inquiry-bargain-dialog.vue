<style lang="scss" scoped>
::v-deep {
  .dialog-title {
    position: absolute;
    right: 0;
    left: 0;
    z-index: 100;
    width: 100%;

    .el-tabs__header {
      margin: 0;
    }

    .el-tabs__nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 46px;
    }

    .el-tabs__item {
      padding: 0;
      width: 50%;
      text-align: center;
    }
  }

  .el-dialog__headerbtn {
    position: absolute !important;
    top: 12px;
    z-index: 101;
  }

  .el-tabs__item {
    display: flex;
    justify-content: center;
    align-items: center;

    .label {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .el-badge {
      display: flex;
      align-items: center;

      .el-badge__content {
        margin-left: 2px;
        border: 0;
        border-color: transparent;
        border-radius: 50%;
        padding: 0;
        width: 18px;
        height: 18px;
        font-size: 12px;
        color: $color-FFFFFF;
        background-color: $color-warning;
        line-height: 18px;
      }

      &.more {
        .el-badge__content {
          border-radius: 18px;
          padding: 0 6px;
          width: auto;
        }
      }

      &.mixmore {
        .el-badge__content {
          border-radius: 18px;
          padding: 0 4px;
          width: auto;
        }
      }
    }
  }
}
</style>

<template>
  <div>
    <el-dialog
      :visible.sync="dialogVisible"
      width="600px"
      height="754px"
      :before-close="handleClose"
      :close-on-click-modal="false"
      append-to-body
      center
      :lock-scroll="true"
    >
      <!-- 1-询单&议价   -->
      <div v-if="showType === INQUIRYBARGAIN_NAME.ALL.id" slot="title" class="dialog-title">
        <el-tabs
          v-model="activeTab"
        >
          <el-tab-pane
            v-for="tab in tabsList"
            :key="tab.id"
            :name="`${tab.id}`"
          >
            <span slot="label" class="label">
              {{ tab.label }}
              <!-- 议价 -->
              <el-badge
                v-if="bargainCount > 0 && tab.id === '0'"
                :class="{
                  more: bargainCount > 99,
                  mixmore: bargainCount > 9 && bargainCount < 100
                }"
                :value="bargainCount > 99 ? '99+' : bargainCount"
              />
              <!-- 询单 -->
              <el-badge
                v-if="inquiryCount > 0 && tab.id === '1'"
                :class="{
                  more: inquiryCount > 99,
                  mixmore: inquiryCount > 9 && inquiryCount < 100
                }"
                :value="inquiryCount > 99 ? '99+' : inquiryCount"
              />
            </span>
          </el-tab-pane>
        </el-tabs>
      </div>
      <!-- 2-议价 -->
      <div v-if="showType === INQUIRYBARGAIN_NAME.YIJIA.id" slot="title">
        {{ INQUIRYBARGAIN_TAB_LIST.YIJIA.name }}
      </div>
      <!-- 3-询单 -->
      <div v-if="showType === INQUIRYBARGAIN_NAME.XUNDAN.id" slot="title">
        {{ INQUIRYBARGAIN_TAB_LIST.XUNDAN.name }}
      </div>

      <div class="dialog-body">
        <!-- 议价消息列表弹窗 -->
        <BargainListDialog
          v-if="activeTab === INQUIRYBARGAIN_TAB_LIST.YIJIA.id || showType === INQUIRYBARGAIN_NAME.YIJIA.id"
          @close="handleClose"
        />
        <!-- 询单消息列表弹窗 -->
        <InquiryListDialog
          v-if="activeTab === INQUIRYBARGAIN_TAB_LIST.XUNDAN.id || showType === INQUIRYBARGAIN_NAME.XUNDAN.id"
          :bargain-id="bargainId"
        />
      </div>
    </el-dialog>

    <!-- 还价弹窗 -->
    <BargainDickerDialog
      v-if="isShowBargainDickerDialog"
      ref="bargainDickerDialog"
      :data="bargainData || {}"
      @close="handleClose"
    />

    <!-- 接单-票据详情 -->
    <ReceiveOrderDetail v-if="isShowReceiveOrderDetail" ref="receiveOrderDetail" @success="refreshList" />

    <!-- 议价申请弹窗 -->
    <BargainApplyDialog
      v-if="isShowBargainApplyDialog"
      ref="bargainApplyDialogRef"
      :draft-info="draftInfo"
      @success="refreshList"
    />

    <!-- 询单--详情 -->
    <!--
      <InquiryReplayDialog
      v-if="showInquiryReplayDialog"
      :card-data="inquiryData.cardData"
      :inquiry-type="inquiryData.inquiryType"
      @success="closeInquiryReplayDialog"
      />
    -->
  </div>
</template>

<script>
import marketApi from '@/apis/market'
// import InquiryReplayDialog from '@/views/components/inquiry-bargain/inquiry/inquiry-replay-dialog/inquiry-replay-dialog.vue' // 询单--详情-组件
import {
  TOOLBAR_REFRESH, // 刷新消息中心列表事件
  MQTT_BARGAIN, // 议价成功消息--刷新服务大厅
  RECEIVE_ORDER_DETAIL,
  // XUANDAN_DETAIL_NOTIFICATION, // 消息里的询单详情
} from '@/event/modules/site'
import {
  INQUIRYBARGAIN_TAB_LIST,
  INQUIRYBARGAIN_NAME
} from '@/constants/inquiry-bargain'
import {
  BARGAIN_STATUS, // 议价状态
} from '@/constants/bargain'
import bargainApi from '@/apis/bargain' // api接口
import inquiry from '@/apis/inquiry' // api接口
import BargainListDialog from '@/views/components/inquiry-bargain/bargain/bargain-list-dialog/bargain-list-dialog.vue'
import InquiryListDialog from '@/views/components/inquiry-bargain/inquiry/inquiry-list-dialog/inquiry-list-dialog.vue'
import BargainDickerDialog from '@/views/components/inquiry-bargain/bargain/bargain-dicker-dialog/bargain-dicker-dialog.vue' // 还价弹窗
import BargainApplyDialog from '@/views/components/inquiry-bargain/bargain/bargain-apply-dialog/bargain-apply-dialog.vue' // 议价申请弹窗
import ReceiveOrderDetail from '@/views/pages/market/components/receive-order-detail/receive-order-detail.vue' // 接单详情组件
import { mapActions, mapGetters } from 'vuex'
export default {
  name: 'inquiry-bargain-dialog',
  components: {
    BargainListDialog,
    InquiryListDialog,
    BargainDickerDialog,
    BargainApplyDialog,
    ReceiveOrderDetail,
    // InquiryReplayDialog // 询单--详情-组件
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    showType: {
      type: Number,
      default: 1
    },

    // 当前询单的id 用于定位
    bargainId: {
      type: String,
      default: null
    },
  },
  data() {
    return {
      inquiryCount: 0, // 询单
      bargainCount: 0, // 议价
      activeTab: `${INQUIRYBARGAIN_TAB_LIST.YIJIA.id}`,
      tabsList: Object.values(INQUIRYBARGAIN_TAB_LIST),
      INQUIRYBARGAIN_TAB_LIST,
      INQUIRYBARGAIN_NAME,
      isShowReceiveOrderDetail: false, // 是否显示接单详情弹窗
      isShowBargainDickerDialog: false, // 是否显示还价弹窗
      isShowBargainApplyDialog: false, // 是否显示议价申请弹窗
      bargainData: null, // 某条议价消息数据,用于还价弹窗
      draftInfo: {}, // 票据信息
      // showInquiryReplayDialog: false, // 是否显示询单详情
      // inquiryData: {}, // 询单数据
    }
  },
  computed: {
    ...mapGetters('market', {
      inquiryBargain: 'inquiryBargain',
      bargainingCount: 'bargainingCount'
    }),
    dialogVisible: {
      get() {
        return this.visible
      },
      set(v) {
        this.$emit('update:visible', v)
      }
    },
  },
  watch: {
    inquiryBargain: {
      handler(newVal) {
        this.inquiryCount = newVal.totalCount
      },
    },
    bargainingCount: {
      handler(newVal) {
        this.bargainCount = newVal
      },
    }
  },

  mounted() {
    // 初始化赋值 询单/议价总数量
    this.inquiryCount = this.inquiryBargain.totalCount
    this.bargainCount = this.bargainingCount
    if (this.showType !== INQUIRYBARGAIN_NAME.ALL.id) {
      this.activeTab = null
    }
    if (this.showType === INQUIRYBARGAIN_NAME.YIJIA.id || this.showType === INQUIRYBARGAIN_NAME.ALL.id) {
      // 监听议价消息-刷新议价消息列表
      this.$event.on(MQTT_BARGAIN, () => {
        this.getBargainingTabNum()
      })
      // // 监听还价弹窗(场景:1.票方发起还价)
      // this.$event.on(BARGAIN_DIALOG, data => {
      //   console.log('====================================')

      //   console.log('====================================')
      //   this.bargainData = data || {}
      //   this.isShowBargainDickerDialog = true
      //   this.$nextTick().then(() => {
      //     this.$refs.bargainDickerDialog && this.$refs.bargainDickerDialog.init()
      //   })
      // })
      // 监听打开接单详情弹窗(场景:1.原价接单, 2.重新议价)
      this.$event.on(RECEIVE_ORDER_DETAIL, data => this.handelReceiveOrderDetail(data))

      this.getBargainingTabNum()
    }
    // if (this.showType === INQUIRYBARGAIN_NAME.XUNDAN.id || this.showType === INQUIRYBARGAIN_NAME.ALL.id) {
    //   // 监听打开询单详情
    //   this.$event.on(XUANDAN_DETAIL_NOTIFICATION, data => this.handelXunDanOrderDetail(data))
    // }
    if (this.showType === INQUIRYBARGAIN_NAME.XUNDAN.id || this.showType === INQUIRYBARGAIN_NAME.ALL.id) {
      this.getInquiryTabNum()
    }
    //  this.bargainId是 智能助手询单跳转的 默认询单tab
    this.activeTab = this.bargainId ? INQUIRYBARGAIN_TAB_LIST.XUNDAN.id : INQUIRYBARGAIN_TAB_LIST.YIJIA.id
  },
  methods: {
    ...mapActions('user', {
      getPaymentAccountList: 'getPaymentAccountList', // 查询电子交易账户列表
    }),

    initOpen(data, type) {
      if (type === 'order') {
        this.handelReceiveOrderDetail(data)
      } else {
        this.bargainData = data || {}
        this.isShowBargainDickerDialog = true
        this.$nextTick().then(() => {
          this.$refs.bargainDickerDialog && this.$refs.bargainDickerDialog.init()
        })
      }
    },

    // 刷新议价消息列表
    refreshList() {
      this.getBargainingTabNum()
      this.getInquiryTabNum()
    },
    // 关闭弹窗
    handleClose() {
      this.$event.emit(TOOLBAR_REFRESH)
      this.$emit('close-inquire-bargain-list-dialog')
    },
    // 处理打开接单详情弹窗
    handelReceiveOrderDetail(data) {
      const {
        orderNo, // 订单no
        isShowBargainDialog, // 是否打开发起议价弹窗
        bargainTitle, // 议价标题
        isShowReceiveOrderDetail, // 是否打开接单详情弹窗
      } = data

      // 是否需要打开接单详情弹窗
      if (isShowReceiveOrderDetail) {
        this.isShowReceiveOrderDetail = true //  加载接单详情弹窗组件

        this.$nextTick().then(async() => {
          try {
            await this.$refs.receiveOrderDetail.init(orderNo) // 接单详情弹窗组件初始化
          } catch (error) {
            // console.log('接单详情加载失败 :>> ', error)
          }
        })
      }

      // 是否需要打开发起议价弹窗
      if (isShowBargainDialog) {
        this.isShowBargainApplyDialog = true //  加载发起议价弹窗组件
        this.$nextTick().then(async() => {
          try {
            this.draftInfo = await marketApi.getDraftOrderInfo(orderNo) // 请求接单详情接口
            await this.getPaymentAccountList() // 用户设置-查询电子交易账户列表
            this.$refs.bargainApplyDialogRef.init()
            bargainTitle && (this.$refs.bargainApplyDialogRef.title = bargainTitle) // 修改议价弹窗标题
          } catch (error) {
            // console.log('发起议价失败error :>> ', error)
          }
        })
      }
    },
    // 查询询单中的数量
    async getInquiryTabNum() {
      try {
        const data = await (inquiry.consultTabNum() || {})
        let count = {
          totalCount: data?.all || 0, // 全部
          buyerTotalCount: data?.buyer || 0, // 资方
          sellerTotalCount: data?.seller || 0 // 票方
        }

        this.$store.commit('market/setInquiryBargainCount', count)
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log('error :>> ', error)
      }
    },
    // 查询议价中的数量
    async getBargainingTabNum() {
      // 议价列表传参
      const params = {
        corpTradeRole: +this.activeTab, // 议价操作角色，1-资方，2-票方
        bargainStatus: +BARGAIN_STATUS.BARGAINING.id // 议价状态 0-议价中
      }

      try {
        const data = await (bargainApi.bargainTabNum(params) || {})
        const {
          all, // 全部
        } = data

        // 议价中，回调议价中的所有议价数量
        this.$store.commit('market/setBargainingCount', all || 0)
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log('error :>> ', error)
      }
    },

    // // 打开消息里的询单详情
    // handelXunDanOrderDetail(data) {
    //   console.log(1111111, data)
    //   this.inquiryData = data
    //   this.showInquiryReplayDialog = true
    // },
    // // 关闭询单消息弹窗
    // closeInquiryReplayDialog(isClose) {
    //   // false 表示 需要关闭， true为不关闭
    //   if (isClose) return
    //   this.showInquiryReplayDialog = false
    // },
  },
}
</script>
