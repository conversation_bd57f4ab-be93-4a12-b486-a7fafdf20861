
<!-- 议价&询单-悬浮按钮 -->
<style lang="scss" scoped>
.inquiry-bargain {
  margin-right: 18px;

  // 悬浮固定样式
  &.fixed-style {
    .box-content {
      width: 55px;
    }

    .count {
      position: relative;
      color: $color-FFFFFF;

      .text {
        cursor: pointer;

        &:hover {
          border-bottom: 1px solid  $color-FFFFFF;
        }

        font-size: 12px;
        font-weight: 500;
        line-height: 22px;
      }

      .icon {
        margin-left: 2px;
        color: rgba($color:$color-FFFFFF, $alpha: 80%);
      }

      .svg-icon {
        vertical-align: top;
      }

      .message-badge {
        position: absolute;
        top: -10px;
        right: -7px;

        ::v-deep .el-badge__content {
          border: 0;
          border-radius: 50%;
          padding: 0;
          width: 18px;
          height: 18px;
          font-weight: 600;
          color: #FFFFFF;
          background-color: $color-warning;
          line-height: 18px;
        }

        &.more {
          position: absolute;
          top: -10px;
          right: -12px;

          ::v-deep .el-badge__content {
            border-radius: 18px;
            width: 26px;
            height: 18px;
            line-height: 18px;
            font-size: 12px;
          }
        }

        &.mixmore {
          position: absolute;
          top: -10px;
          right: -7px;

          ::v-deep .el-badge__content {
            border-radius: 18px;
            width: 22px;
            height: 18px;
            line-height: 18px;
            font-size: 12px;
          }
        }
      }
    }

    .blingbling {
      animation: scaleout .5s infinite ease-in-out;
    }

    @keyframes scaleout {
      0% { transform: scale(1); }

      100% {
        transform: scale(1.1);
        opacity: 0;
      }
    }

    @keyframes scaleout {
      0% {
        transform: scale(1);
      }

      100% {
        opacity: 0;
        transform: scale(1.1);
      }
    }
  }
}
</style>

<template>
  <div
    class="inquiry-bargain fixed-style"
  >
    <div
      class="count"
      :class="isFlicker && 'blingbling'"
      @click="handleShowInquireBargainListDialog"
    >
      <!-- 悬浮固定样式 -->
      <template v-if="INQUIRYBARGAIN_NAME.ALL.id === showType">
        <!-- 深度完整版/海南版（议价和询单） -->
        <div class="box-content">
          <span class="text">询/议</span>
          <icon size="20" type="chengjie-yi" class="icon" />
        </div>
      </template>

      <el-badge
        v-if="count > 0"
        class="message-badge"
        :class="{
          more: count > 99,
          mixmore: count > 9 && count < 100
        }"
        :value="count > 99 ? '99+' : count"
      />
    </div>
    <InquiryBargainDialog
      v-if="showInquiryBargainDialog"
      ref="inquiryBargainDialog"
      :show-type="showType"
      :visible.sync="visible"
      @close-inquire-bargain-list-dialog="closeInquireBargainListDialog"
    />
  </div>
</template>

<script>
import InquiryBargainDialog from './inquiry-bargain-dialog.vue'
import { INQUIRY_NAMES_VALUE_MAP, INQUIRYBARGAIN_NAME } from '@/constants/inquiry-bargain'
import { mapGetters } from 'vuex'
import { BARGAIN_DIALOG, RECEIVE_ORDER_DETAIL } from '@/event/modules/site'
export default {
  name: 'inquiry-bargain-new',
  components: {
    InquiryBargainDialog
  },
  props: {
    // 按钮样式类型 1-悬浮固定样式 3-右侧工具栏样式
    styleType: {
      type: Number,
      default: 1
    },
    // 类型：1-询单&议价 2-议价 3-询单
    showType: {
      type: Number,
      default: 1
    },
  },
  data() {
    return {
      isFlicker: false,
      showInquiryBargainDialog: true,
      visible: false,
      INQUIRY_NAMES_VALUE_MAP,
      INQUIRYBARGAIN_NAME
    }
  },
  computed: {
    ...mapGetters('market', {
      bargainingCount: 'bargainingCount',
      inquiryBargain: 'inquiryBargain',
      newMagCount: 'newMagCount'
    }),
    count() {
      let val = 0
      switch (this.showType) {
        case 2:
          val = this.bargainingCount
          break
        case 3:
          val = this.inquiryBargain.totalCount
          break
        case 1:
        default:
          val = this.bargainingCount + this.inquiryBargain.totalCount
          break
      }
      return val
    }
  },
  watch: {
    // 被侦听的变量count  count 变化 图标闪烁
    newMagCount: {
      handler() {
        // 数量增加闪烁  减少不闪烁
        this.isFlicker = true
        setTimeout(() => {
          this.isFlicker = false
        }, 1000)
      }
    },
    $route: {
      handler() {
        this.showInquiryBargainDialog = false
      },
      deep: true
    }
  },
  mounted() {
    // 监听还价弹窗(场景:1.票方发起还价)
    this.$event.on(BARGAIN_DIALOG, data => {
      this.showInquiryBargainDialog = true
      this.$nextTick().then(() => {
        this.$refs.inquiryBargainDialog.initOpen(data, 'inquiery')
      })
    })
    // 监听打开接单详情弹窗(场景:1.原价接单, 2.重新议价)
    this.$event.on(RECEIVE_ORDER_DETAIL, data => {
      this.showInquiryBargainDialog = true
      this.$nextTick().then(() => {
        this.$refs.inquiryBargainDialog.initOpen(data, 'order')
      })
    })
  },
  methods: {
    // 默认打开 弹窗
    handleShowInquireBargainListDialog() {
      this.showInquiryBargainDialog = true
      this.visible = true
    },
    closeInquireBargainListDialog() {
      this.visible = false
    }
  },
}
</script>
