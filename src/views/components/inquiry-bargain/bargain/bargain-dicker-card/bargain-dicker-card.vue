<!-- 还价、重新还价、重新议价卡片 -->
<style lang="scss" scoped>
.bargain-dicker-card {
  position: relative;
  display: flex;
  padding: 16px;
  width: 100%;
  height: 100%;
  text-align: left;
  background-color: $color-FFFFFF;
  flex-direction: column;
  box-sizing: border-box;

  > .el-form-item {
    margin-bottom: 0;
    padding-bottom: 10px;

    // &:last-child {
    //   padding-bottom: 0;
    // }
  }

  .bargain-amount {
    display: flex;
    justify-content: flex-start;
    flex-direction: column;

    .text-primary {
      margin: 0 6px;
      font-weight: 600;
    }
  }

  .g-title {
    position: relative;
    overflow: hidden;
    margin-bottom: 12px;
    padding-left: 10px;
    font-size: 16px;
    font-weight: 600;
    text-align: left;
    color: $color-text-primary;
    line-height: 22px;

    &::before {
      position: absolute;
      top: 50%;
      left: 0;
      width: 4px;
      height: 16px;
      background-color: $--color-primary;
      transform: translateY(-50%);
      content: "";
    }
  }
}

.flex-box {
  display: flex;
}

.bold {
  font-weight: 600;
}

// ele组件样式
::v-deep {
  .el-form-item__label,
  .el-form-item {
    line-height: 22px;
  }

  .el-form-item__content {
    font-size: 16px;
    color: $color-text-primary;
    line-height: 24px;
  }

  .el-form-item__label {
    padding-bottom: 0 !important;
    font-size: 14px;
    color: $color-text-secondary;
  }

  // 议价输入框
  .bargain-amount-item {
    width: 100%;
    min-height: 80px;

    .el-form-item__content {
      display: flex;
      width: 100%;

      >.el-form-item {
        flex: 1;
      }
    }

    .el-input__suffix {
      top: 0;
      top: 1px;
      right: 0;
      right: 1px;
      bottom: 1px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-left: 1px solid $color-D9D9D9;
      min-width: 36px;
      height: auto;
      font-size: 14px;
      font-weight: 400;
      color: $color-text-primary;
      background-color: $color-FAFAFA;
    }

    .bargain-amount-input {
      margin-right: 8px;
      min-width: 0;
      max-width: 210px;
      height: 40px;
      flex: 1;
    }
  }

  // radio单选按钮
  .el-radio-group {
    .el-radio-button {
      font-size: 16px;

      &:not(:first-child) {
        margin-left: 8px;
      }

      &.is-active {
        .el-radio-button__inner {
          border-color: $--color-primary;
          color: $--color-primary;
          background-color: $--color-primary-hover;
          box-shadow: none;
        }
      }

      &.is-disabled {
        .el-radio-button__inner {
          border: 1px solid $color-D9D9D9;
          box-shadow: none;
        }
      }

      .el-radio-button__inner {
        border-left: 1px solid $color-D9D9D9;
        border-radius: 2px;
        padding: 0 17px;
        height: 40px;
        font-size: 16px;
        box-shadow: none;
        line-height: 40px;
      }
    }

    .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      border-color: $--color-primary;
      color: $--color-primary;
      background-color: $--color-primary-hover;
      box-shadow: none;
    }
  }

  .el-input__inner {
    &::placeholder {
      font-size: 16px;
      text-align: right;
    }
  }

  // .el-form-item {
  //   padding-bottom: 20px;
  // }

  // 议价后报价
  .bargain-price {
    >.el-form-item__content {
      display: flex;
      width: 100%;
    }
  }

  .bargain-amount {
    flex: 1;
  }

  .bargain-actual-payment-amount {
    flex: 0 200px;

    .el-form-item__label {
      width: 100%;
      text-align: right;
    }

    .el-form-item__content {
      display: flex;
      justify-content: flex-end;
      flex-direction: column;
      text-align: right;
    }
  }

  // 有效期radio
  .bargain-valid-period-box {
    &.el-radio-group {
      .el-radio-button {
        &__inner {
          padding: 0 40px;
        }
      }
    }
  }
}

.sellerBankAccount-cls {
  padding-top: 15px;
}

.item-position {
  position: relative;

  ::v-deep .el-form-item__label {
    padding: 0;
  }

  .el-tooltip {
    position: absolute;
    top: -21px;
    left: 58px;
  }

  .max-width {
    width: 100%;
  }
}

.seller-select-btn {
  font-size: 16px;
  text-align: center;
  color: $--color-primary;
  line-height: 40px;
  cursor: pointer;
}

.seller-bank-link {
  border-bottom: 1px solid $--color-primary;
  color: $--color-primary;
  cursor: pointer;
}

.split-bargain {
  margin-bottom: 15px;

  .split-bargain-title {
    font-size: 14px;
    color: #999999;
  }

  .blod-cls {
    font-weight: 500;
    color: $color-warning;
  }
}
</style>

<template>
  <el-form
    ref="form"
    class="bargain-dicker-card"
    :model="form"
    :rules="rules"
    :title="`我的${bargainLabel}`"
    label-position="top"
    :hide-required-asterisk="true"
  >
    <h2 class="g-title">
      我的{{ bargainLabel }}
    </h2>
    <!-- 拆分议价 区间 || 定额 -->
    <div v-if="isIntervalSplit || isQuotaSplit" class="split-bargain">
      <div class="split-bargain-title">
        可拆分议价金额：
        <template v-if="isIntervalSplit">
          <span class="text-primary">{{ yuan2wan(fen2yuan(data.splitAmtMin)) }}</span>
          <span> 万元 ~ </span>
          <span class="text-primary">{{ yuan2wan(fen2yuan(data.splitAmtMax)) }}</span>
          <span> 万元</span>
        </template>
        <template v-if="isQuotaSplit">
          <span>
            <span class="blod-cls">{{ yuan2wan(fen2yuan(data.splitAmtMin)) }}</span> 万元
            <span v-if="data.integerMultiples === 1">或其整数倍</span>
            <el-tooltip
              v-if="data.integerMultiples === 1"
              placement="top"
              content="可拆分议价金额=M×N，M为票方设置的可固定拆分金额，N为正整数"
            >
              <icon class="icon icon-question" type="chengjie-wenti" size="18" />
            </el-tooltip>
          </span>
        </template>
      </div>
      <el-form-item class="m-t10" prop="splitAmtIntValidate">
        <el-input
          v-model="form.splitAmount"
          placeholder="拆分金额"
          :disabled="isDisableSplitAmt"
          :width="240"
          type="number"
          :number-format="{
            decimal: true,
            negative: false,
            leadingZero: false,
            maxDecimalLength: 6,
            maxIntegerLength: 4,
          }"
          @input="onChangeSplitAmount"
        >
          <span slot="append">万</span>
        </el-input>
      </el-form-item>
    </div>
    <!-- 还价、重新还价、重新议价-报价方式 -->
    <el-form-item
      :label="`${bargainLabel}方式`"
      class="bargain-method-item"
    >
      <el-radio-group v-model="form.bargainMethod" @change="onChangeBargainMethod">
        <el-radio-button
          v-for="item in bargainMethodOption"
          :key="item.value"
          :label="item.value"
          type="button"
        >
          {{ item.label }}
        </el-radio-button>
      </el-radio-group>
    </el-form-item>

    <!-- 议价输入金额 -->
    <!-- 议价/还价-每十万扣款 -->
    <el-form-item
      v-if="form.bargainMethod === BARGAIN_METHOD_OPTIONS_MAP.DEDUCTION.value"
      ref="bargainAmountInput"
      prop="bargainAmountInput"
      class="bargain-amount-item"
      :label="bargainLabel"
    >
      <!-- 输入框 -->
      <el-input
        v-model="form.bargainAmountInput"
        class="bargain-amount-input"
        placeholder="您的加价"
        type="number"
        :number-format="yuanNumberFormat"
        @input="onChangeBargainAmount"
      >
        <template slot="append" class="unit">元</template>
      </el-input>
      <!--  快捷选择金额 -->
      <el-radio-group v-model="form.bargainAmountInput" @change="onChangeBargainAmount">
        <el-radio-button
          v-for="item in bargainAmountRadioOptions"
          :key="item.value"
          :value="item.value"
          :label="item.value"
        >
          {{ item.label }}
        </el-radio-button>
      </el-radio-group>
    </el-form-item>
    <!-- 年化+每十万手续费 -->
    <el-form-item v-else prop="annualInterestInput" class="bargain-amount-item flex-box">
      <el-form-item prop="" required label="年化">
        <!-- 年化输入框 -->
        <el-input
          v-model="form.annualInterestInput"
          placeholder="年化"
          type="number"
          class="bargain-amount-input"
          :number-format="annualInterestNumberFormat"
          @input="onChangeAnnualInterest"
        >
          <template slot="append" class="unit">%</template>
        </el-input>
      </el-form-item>

      <el-form-item prop="" label="每十万手续费">
        <!-- 每十万手续费输入框 -->
        <el-input
          v-model="form.serviceCharge"
          class="bargain-amount-input"
          placeholder="每十万手续费"
          type="number"
          :number-format="yuanNumberFormat"
          :show-message="false"
          @input="onChangeServiceCharge"
        >
          <template slot="append" class="unit">元</template>
        </el-input>
      </el-form-item>
    </el-form-item>

    <!-- 有限期 -->
    <template v-if="type === 1">
      <el-form-item label="有效期">
        <el-radio-group v-model="form.bargainLimitTime" class="bargain-valid-period-box" @change="onChangeBargainLimitTime">
          <el-radio-button
            v-for="item in bargainLimitTimeOptions"
            :key="item.value"
            :label="item.value"
            type="button"
          >
            {{ item.label }}
          </el-radio-button>
        </el-radio-group>
      </el-form-item>
    </template>

    <!-- 我还价、重新还价的报价 -->
    <el-form-item v-if="data.isBargain && data.isSale" prop="" class="bargain-price">
      <el-form-item prop="" class="bargain-amount" :label="`我${bargainLabel}后的报价`">
        <template v-if="isInputBargain">
          每十万扣款<span class="text-primary">{{ bargainLakhDeduction }}</span>元 /<span class="text-primary">{{ bargainAnnualInterest }}%</span>
        </template>
        <template v-else>
          -
        </template>
      </el-form-item>
      <!-- 我还价、重新还价后的实付金额 -->
      <template v-if="type === 2">
        <el-form-item class="bargain-actual-payment-amount" :label="`${bargainLabel}后实付金额`">
          <span v-if="isInputBargain" class="text-primary bold">{{ bargainPayAmount }} 万元</span>
          <span v-else>-</span>
        </el-form-item>
      </template>
    </el-form-item>
    <div v-if="type === 2 && !data.sellerBankAccountId && data.paymentChannel === 7" class="sellerBankAccount-cls">
      <div class="txt">选中的订单包含支持智付E+渠道，根据平台规则请选择回款账户</div>
      <el-form-item
        label="回款账户"
        prop="sellerBankAccountId"
        class="form-item-block item-position"
        required
      >
        <el-tooltip
          placement="top"
          popper-class="issue-draft-tooltip"
        >
          <template slot="content">
            <div>依照智付E+、E++渠道交易模式，票方发布票据必须选择回款账户(支持任意银行一般户)。需要在<span class="seller-bank-link" @click="() => { $router.push('/user-center/bank-account');$emit('close') }">银行账户</span>页面绑定回款账户(银行签收账户可作为回款账户)，交易完成后票款自动提现到账。</div>
          </template>
          <icon class="icon icon-question" type="chengjie-wenti" />
        </el-tooltip>

        <div class="pay-type-item">
          <el-select
            ref="sellerBankAccount"
            v-model="form.sellerBankAccountId"
            class="max-width"
            :height="40"
            placeholder="请选择回款账户"
          >
            <el-option
              v-for="item in sellerBankAccountList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
            <div class="seller-select-btn" @click="() => { $router.push('/user-center/bank-account?tabStatus=2');$refs.sellerBankAccount.blur();$emit('close') }"><i class="el-icon-plus" />添加回款账户</div>
          </el-select>
        </div>
      </el-form-item>
    </div>
  </el-form>
</template>

<script>
/* eslint-disable no-magic-numbers */

import {
  interestRateMath, // * 以利率计算 => 每十万扣款 年化利率 和 到账金额
  lakhDeductionMath, // * 每十万扣款计算 => 年化利率 和 到账金额
} from '@/common/js/draft-math'
import BigNumber from 'bignumber.js'
import {
  dataType, // 数据类型判断
  isNotVoid, // 是否空值
  isNull, // 判断是否为Null
  debounce
} from '@/common/js/util'
import { yuan2wan, fen2yuan, wan2yuan } from '@/common/js/number'
import { mapGetters } from 'vuex'
import marketApi from '@/apis/market'

// 议价/还价类型
const BARGAIN_TYPE_MAP = Object.freeze({
  BARGAIN: {
    value: 1,
    label: '议价'
  },
  COUNTEROFFER: {
    value: 2,
    label: '还价'
  },
})

// 议价金额-快捷选择金额
const BARGAIN_AMOUNT_SELECT_OPTIONS_MAP = Object.freeze({
  TEN: {
    label: '+10',
    value: 10,
  },
  TWENTY: {
    label: '+20',
    value: 20,
  },
  THIRTY: {
    label: '+30',
    value: 30,
  }
})

// 报价方式
const BARGAIN_METHOD_OPTIONS_MAP = Object.freeze({
  DEDUCTION: {
    value: 1,
    label: '每十万扣款'
  },
  ANNUALIZED_RATE: {
    value: 2,
    label: '年化+每十万手续费'
  }
})

// 议价有效期
const LIMIT_TIME_MAP = Object.freeze({
  FIVE_MINS: {
    value: 11,
    label: '5分钟'
  },
  TEN_MINS: {
    value: 12,
    label: '10分钟'
  }
})

// 当前议价组件目前用于议价申请或者mqtt消息推送的议价里的还价、重新议价、重新还价弹窗
export default {
  name: 'bargain-dicker-card',
  components: {
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    // 该组件用途
    type: {
      type: Number,
      default: 2 // 议价类型：1.议价申请 2.还价、重新议价、重新还价弹窗
    },
    // isShowValidPeriod: {
    //   type: Boolean,
    //   default: false // 是否需要展示有效期选项，议价申请需要，还价弹窗不需要
    // },
  },
  data() {
    // 校验拆分金额
    const validateSplitAmtInt = (rule, value, callback) => {
      if (!this.form.splitAmount) {
        callback(new Error('请输入拆分金额'))
      }
      // 订单票面金额>1万元时 拆分金额小于1万元提示 拆分定向的金额必须大于等于1万元
      if (new BigNumber(yuan2wan(this.data.draftAmount)).isGreaterThan(new BigNumber(1))) {
        if (this.form.splitAmount && new BigNumber(this.form.splitAmount).isLessThan(new BigNumber(1))) {
          callback(new Error('拆分议价的金额必须大于等于1万元'))
        }
      }
      // 拆分金额大于订单票面金额提示 拆分金额必须小于等于订单票面金额
      if (this.form.splitAmount && new BigNumber(this.form.splitAmount).isGreaterThan(new BigNumber(yuan2wan(this.data.draftAmount)))) {
        callback(new Error('拆分议价的金额不能大于票面金额'))
      }

      // 定额拆分 支持整倍数 校验
      if (this.isQuotaSplit && this.data.integerMultiples === 1) {
        // 输入的拆分金额 不能被splitAmtMin整除
        if (!new BigNumber(this.form.splitAmount)
          .mod(new BigNumber(yuan2wan(fen2yuan(this.data.splitAmtMin))))
          .isZero()) {
          callback(new Error('拆分金额必须为其整倍数'))
        }
      }
      callback()
    }
    return {
      yuan2wan,
      fen2yuan,
      BARGAIN_TYPE_MAP, // 议价类型（议价/还价）
      BARGAIN_METHOD_OPTIONS_MAP, // 议价方式
      bargainMethodOption: Object.values(BARGAIN_METHOD_OPTIONS_MAP), // 议价方式选项
      bargainAmountRadioOptions: Object.values(BARGAIN_AMOUNT_SELECT_OPTIONS_MAP), // 议价金额-快捷选择金额
      bargainLimitTimeOptions: Object.values(LIMIT_TIME_MAP), // 议价有效期选项
      // lakhFee: null, // 每十万扣款，单位元
      // annualInterest: null, // 年利率
      // interestDays: null, // 计息天数
      // draftAmount: null, // 票据金额，单位元
      // platformServiceFee: null, // 服务费，单位米
      // 年利率为单位的输入框格式
      annualInterestNumberFormat: {
        decimal: true,
        maxIntegerLength: 5,
        maxDecimalLength: 4,
        negative: false,
        leadingZero: false,
      },
      // 元为单位的输入框格式
      yuanNumberFormat: {
        decimal: true,
        maxIntegerLength: 5,
        maxDecimalLength: 2,
        negative: false,
        leadingZero: false,
      },
      form: {
        bargainMethod: BARGAIN_METHOD_OPTIONS_MAP.DEDUCTION.value, // 当前选择的议价方式，默认每十万扣款
        bargainAmountInput: null, // 当前议价金额，默认不加
        annualInterestInput: null, // 输入的年利率
        serviceCharge: null, // 每十万手续费
        bargainLimitTime: LIMIT_TIME_MAP.FIVE_MINS.value, // 当前选择的议价有效期
        sellerBankAccountId: null, // 回款账户
        splitAmount: '', // 拆分金额
      },
      rules: {
        bargainAmountInput: [
          { required: true, message: '议价金额不能为空', trigger: 'none' },
          { validator: this.checkBargainInput, trigger: 'none' }
        ],
        sellerBankAccountId: [{ required: true, message: '请选择回款账户', trigger: 'change' }],
        annualInterestInput: [
          { required: true, message: '输入的年利率不能为空', trigger: 'none' },
          { validator: this.checkBargainInput, trigger: 'none' }
        ],
        splitAmtIntValidate: [{ required: true, validator: validateSplitAmtInt, trigger: ['change', 'blur'] }] // 拆分金额校验
        // serviceCharge: [{ validator: this.checkBargainInput, trigger: 'none' }],
      }
    }
  },

  computed: {
    ...mapGetters('user', {
      sellerBankAccountList: 'sellerBankAccountList', // 已通过的签收账户列表
    }),
    // 是否区间拆分 splitFlag === 1 && splitType === 0 按可拆分金额拆分 && integerMultiples === 0 为区间拆分
    isIntervalSplit() {
      return this.data.splitFlag && this.data.splitType === 0 && this.data.integerMultiples === 0
    },
    // 是否定额拆分 splitFlag === 1 && splitType === 0 按可拆分金额拆分 && integerMultiples === 1 || 2 为定额拆分
    isQuotaSplit() {
      return this.data.splitFlag && this.data.splitType === 0 && [1, 2].includes(this.data.integerMultiples)
    },
    // 票面金额<=1万元时禁用拆分金额输入框
    isDisableSplitAmt() {
      return new BigNumber(yuan2wan(this.data.draftAmount)).isLessThanOrEqualTo(new BigNumber(1))
    },
    // 回款账户id
    sellerBankAccountId() {
      return this.$store.state.common.sellerBankAccountId
    },
    // 资方-是否重新议价
    isRePostBargain() {
      const { isDicker, isBargain, isBuy } = this.data
      return (isDicker || isBargain) && isBuy
    },

    // 票方-是否还价
    isDickerBargain() {
      const { isBargain, isSale } = this.data
      return isBargain && isSale
    },

    // 是否有输入议价金额
    isInputBargain() {
      const { bargainAmountInput, annualInterestInput } = this.form
      return !isNull(bargainAmountInput) || !isNull(annualInterestInput)
    },

    // 显示文案：议价/还价
    bargainLabel() {
      const { isBargain, isSale } = this.data
      let res = isBargain && isSale ? BARGAIN_TYPE_MAP.COUNTEROFFER.label : BARGAIN_TYPE_MAP.BARGAIN.label
      return res
    },

    // 报价-实付金额（未算上交易凭证费用、平台手续费费用）
    actualPayAmount() {
      let res = null
      let {
        lakhFee, // 每十万扣款
        interestDays, // 计息天数
        draftAmount, //  票据金额
      } = this.data
      // 优先取拆分议价金额 > 票面金额
      draftAmount = wan2yuan(this.form.splitAmount) || draftAmount
      const { receivedAmount } = lakhDeductionMath(draftAmount, lakhFee || 0, interestDays)
      res = yuan2wan(receivedAmount || 0, { digits: 6, parseNumber: false, mode: 'round' })
      return res
    },

    // 议价后的报价-每十万扣款
    bargainLakhDeduction() {
      let res = null
      // 计算方式:每十万扣款
      if (this.form.bargainMethod === 1) {
        const {
          lakhFee, // 每十万扣款
        } = this.data

        const {
          bargainAmountInput, // 输入的议价值
        } = this.form

        res = new BigNumber(lakhFee || 0).plus(bargainAmountInput || 0)
          .toNumber()
      }

      // 计算方式:年化+每十万手续费
      if (this.form.bargainMethod === 2) {
        let {
          draftAmount, //  票据金额
          interestDays, // 计息天数
        } = this.data

        const {
          annualInterestInput, // 输入利率
          serviceCharge, // 每十万手续费
          splitAmount, // 输入的拆分金额
        } = this.form
        // 优先取输入拆分金额 > 票面金额
        draftAmount = wan2yuan(splitAmount) || draftAmount
        const {
          // annualInterest, // 年化利率
          lakhDeduction, // 每十万扣款
          // receivedAmount, // 到账金额
        } = interestRateMath(draftAmount, annualInterestInput || 0, serviceCharge || 0, interestDays)
        res = annualInterestInput && isNotVoid(annualInterestInput) ? lakhDeduction : this.lakhFee // 没输入显示原值
      }
      return res
    },

    // 议价后的报价-年化利率
    bargainAnnualInterest() {
      let res = null
      // 计算方式:每十万扣款
      if (this.form.bargainMethod === 1) {
        let {
          lakhFee, // 每十万扣款
          interestDays, // 计息天数
          draftAmount, //  票据金额
        } = this.data

        const {
          bargainAmountInput, // 议价值
          splitAmount, // 输入的拆分金额
        } = this.form
        // 优先取输入拆分金额 > 票面金额
        draftAmount = wan2yuan(splitAmount) || draftAmount
        let newLakhFee = new BigNumber(lakhFee || 0).plus(bargainAmountInput || 0)
          .toNumber()
        const { annualInterest } = lakhDeductionMath(draftAmount, newLakhFee, interestDays)
        res = (+annualInterest).toFixed(4)
      }

      // 计算方式:年化+每十万手续费
      if (this.form.bargainMethod === 2) {
        let {
          draftAmount, //  票据金额
          interestDays, // 计息天数
        } = this.data
        const {
          serviceCharge, // 每十万手续费
          annualInterestInput, // 输入利率
          splitAmount, // 输入的拆分金额
        } = this.form
        // 优先取输入拆分金额 > 票面金额
        draftAmount = wan2yuan(splitAmount) || draftAmount
        const {
          annualInterest, // 年化利率
          // lakhDeduction, // 每十万扣款
          // receivedAmount, // 到账金额
        } = interestRateMath(draftAmount, +annualInterestInput || 0, serviceCharge || 0, interestDays)

        res = +annualInterestInput && isNotVoid(+annualInterestInput) ? (+annualInterest).toFixed(4) : this.annualInterest // 没输入显示原值
      }
      return res
    },

    // 议价后的报价-实付金额(还没算上平台手续费)
    bargainPayAmount() {
      let res = null
      // 计算方式:每十万扣款
      if (this.form.bargainMethod === 1) {
        let {
          lakhFee, // 每十万扣款
          interestDays, // 计息天数
          draftAmount, //  票据金额
        } = this.data
        const {
          bargainAmountInput, // 议价值
        } = this.form
        // 议价接口返回的还价拆分金额 > 输入拆分金额 > 票面金额
        draftAmount = this.data.splitAmount || wan2yuan(this.form.splitAmount) || draftAmount

        let newLakhFee = new BigNumber(lakhFee || 0).plus(bargainAmountInput || 0)
          .toNumber()
        const { receivedAmount } = lakhDeductionMath(draftAmount, newLakhFee, interestDays)
        res = yuan2wan(receivedAmount || 0, { digits: 6, parseNumber: false, mode: 'round' })
      }

      // 计算方式:年化+每十万手续费
      if (this.form.bargainMethod === 2) {
        let {
          draftAmount, //  票据金额
          interestDays, // 计息天数
        } = this.data
        const {
          annualInterestInput, // 输入利率
          serviceCharge, // 每十万手续费
          splitAmount, // 输入的拆分金额
        } = this.form
        // 优先取输入拆分金额 > 票面金额
        draftAmount = wan2yuan(splitAmount) || draftAmount
        const {
          // annualInterest, // 年化利率
          // lakhDeduction, // 每十万扣款
          receivedAmount, // 到账金额
        } = interestRateMath(draftAmount, annualInterestInput || 0, serviceCharge || 0, interestDays)
        res = (annualInterestInput && isNotVoid(annualInterestInput)) ? yuan2wan(receivedAmount || 0, { digits: 6, parseNumber: false, mode: 'round' }) : this.actualPayAmount // 没输入显示原值
      }
      return res
    },
  },
  watch: {
    data: {
      handler(val) {
        if (val) {
          this.form.splitAmount = yuan2wan(val.draftAmount)
        }
      },
      immediate: true,
      deep: true
    },
    // 监听表单数据改变
    form: {
      handler() {
        this.updatedBargain() // 更新数据给父组件

        // 清除校验结果报错
        this.$nextTick().then(() => {
          this.$refs.form.clearValidate()
        })
      },
      deep: true,
      immediate: true,
    },
    // sellerBankAccountId: {
    //   handler(val) {
    //     debugger
    //     this.form.sellerBankAccountId = Number(val)
    //   }
    // }
  },
  created() {
    this.form.sellerBankAccountId = Number(this.data.sellerBankAccountId) || this.sellerBankAccountId
    this.$emit('change', { sellerBankAccountId: this.form.sellerBankAccountId })
    // 创建防抖函数
    this.debouncedSplitAmount = debounce(async val => {
      // 根据拆分金额 重新计算服务费
      const res = await marketApi.calculateSplitOrderFee({ orderNo: this.data.orderNo, draftAmount: wan2yuan(val) || this.data.draftAmount })

      this.$emit('update-order-fee', { data: Object.assign(res, { splitAmount: wan2yuan(this.form.splitAmount) }) })
    }, 600)
  },

  methods: {
    // 初始化
    init() {
      // const {
      //   lakhFee, // 每十万扣款，单位元
      //   annualInterest, // 年利率
      //   interestDays, // 计息天数
      //   draftAmount, // 票据金额，单位元
      //   platformServiceFee, // 服务费，单位米
      // } = this.data
      // this.lakhFee = lakhFee
      // this.annualInterest = annualInterest
      // this.interestDays = interestDays
      // this.draftAmount = draftAmount
      // this.platformServiceFee = platformServiceFee

      // this.updatedBargain()
    },

    // 清除数据
    clearData() {
      // console.log('重置数据 :>> ', val)
      this.form.bargainAmountInput = null // 议价
      this.form.annualInterestInput = null // 年化
      this.form.serviceCharge = null // 每十万手续费
    },

    // 重置默认数据
    // resetDefaultData() {
    //   this.form.bargainMethod = BARGAIN_METHOD_OPTIONS_MAP.DEDUCTION.value // 当前选择的议价方式，默认每十万扣款
    //   this.bargainLimitTime = LIMIT_TIME_MAP.FIVE_MINS.value // 当前选择的议价有效期

    //   this.updatedBargain()
    // },

    // 议价方式更改
    onChangeBargainMethod() {
      // console.log('议价方式更改 :>> ', val)
      this.resetForm()
    },

    // 议价输入金额更改
    onChangeBargainAmount() {
      // console.log('议价输入金额更改 :>> ', val)
    },

    // 议价输入有效期
    onChangeBargainLimitTime() {
      // console.log('有效期 :>> ', val)
    },

    // 议价输入年化
    onChangeAnnualInterest() {
      // console.log('议价输入年化 :>> ', val,
    },

    // 议价输入每十万手续费
    onChangeServiceCharge() {
      // console.log('议价输入每十万手续费 :>> ', val)
    },

    // 更新数据
    updatedBargain() {
      const data = {
        bargainPayAmount: +this.bargainPayAmount, // 议价后的实付金额
        bargainAnnualInterest: +this.bargainAnnualInterest, // 议价后的年化利率
        bargainLakhDeduction: +this.bargainLakhDeduction, // 议价后的每十万扣款款
        bargainLimitTime: this.form.bargainLimitTime, // 有效期
        bargainAmountInput: this.form.bargainAmountInput, // 输入的议价
        annualInterestInput: this.form.annualInterestInput, // 输入的年化
        serviceCharge: this.form.serviceCharge, // 输入的每十万手续费
        sellerBankAccountId: this.form.sellerBankAccountId, // 回款账户
        splitAmount: this.form.splitAmount, // 输入的拆分金额
      }
      // 非拆分议价类型 移除splitAmount
      if (this.data.splitFlag && this.data.splitType !== 0) {
        delete data.splitAmount
      }
      // console.log('更新数据 :>> ', data)
      this.$emit('change', data)
    },

    // 表单校验
    validateForm(formName = 'form') {
      return new Promise(resolve => {
        this.$nextTick().then(() => {
          let validateResult = true
          this.$refs[formName].validate(valid => {
            if (!valid) {
              validateResult = false
            }
          })
          resolve(validateResult)
        })
      })
    },

    // 重置表单
    resetForm(formName = 'form') {
      this.clearData()
      this.$nextTick().then(() => {
        this.$refs[formName].resetFields()
      })
    },

    // 议价金额校验
    checkBargainInput(rule, val, callback) {
      const {
        isRePostBargain, // 是否资方重新议价
        bargainLakhDeduction, // 议价后的每十万扣款
        isDickerBargain, // 是否票方还价
      } = this

      const {
        lakhFee, // 票方报价的每十万扣款
        bidLakhFee, // 资方议价后报价的每十万扣款
      } = this.data

      // 输入值小于0
      if (+val <= 0 || dataType(+val) === 'NaN') {
        callback(new Error('请输入大于0的金额!'))
      }

      // 校验资方-重新议价是否超过其的第一次议价价格
      if (isRePostBargain && (+bargainLakhDeduction > +bidLakhFee)) {
        callback(new Error('重新议价，价格不能超过第一次议价价格!'))
        return
      }

      // 资方议价格不能超过报价的20%
      const MAX_BARGAIN = new BigNumber(lakhFee).multipliedBy(1.2)
        .toNumber()
      // console.log('MAX_BARGAIN :>> ', MAX_BARGAIN)
      if (!isRePostBargain && !isDickerBargain && (+bargainLakhDeduction > MAX_BARGAIN)) {
        callback(new Error(' '))
        this.$message.error('检查议价，您的报价不得超过票方价格的20%')
        return
      }

      callback()
    },
    onChangeSplitAmount(val) {
      // 拆分议价 重新计算服务费 定额拆分 区间拆分
      // if (this.isIntervalSplit || this.isDickerBargain) {
      // 防抖截流处理
      this.$nextTick().then(() => {
        this.debouncedSplitAmount(val)
      })
      // }

      // const res = await marketApi.calculateSplitOrderFee({ orderNo: this.data.orderNo, draftAmount: val, margin: this.data.margin })
    }
  }
}
</script>
