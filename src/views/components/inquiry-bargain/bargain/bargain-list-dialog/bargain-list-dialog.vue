
<!-- 议价-消息列表弹窗 -->
<style lang="scss" scoped>
.bargain-container {
  display: flex;

  .left-wrap {
    display: flex;
    padding: 0 0 4px;
    width: 159px;
    min-height: 664px;
    flex-direction: column;
    background-color: #FFFFFF;

    .label {
      display: inline-block;
      padding-left: 10px;
      min-width: 80px;
      text-align: left;
    }
  }

  .right-wrap {
    margin-left: 12px;
    flex: 1;
    min-width: 0;
  }
}

.data-list {
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  width: 100%;
  height: 590px;

  .bargain-card {
    margin-top: 0;
    margin-bottom: 12px;
  }

  ::v-deep .notification-card {
    width: 100%;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .row {
    margin-top: 0;
  }
}

.btn-wrap {
  display: flex;
  justify-content: flex-end;
  margin: 6px auto;
}

.refresh-btn {
  font-size: 16px;
  color: $font-color;
  cursor: pointer;
  line-height: 24px;

  @include flex-vc;

  .icon {
    margin-right: 4px;
  }
}

.mt50 {
  margin-top: 50px;
}

// el组件样式
::v-deep {
  .el-tabs__header {
    margin: 0;
  }

  .el-tabs__nav {
    display: flex;
    border-bottom: 0;
    width: 100%;

    .el-tabs__active-bar {
      display: none;
    }

    .el-tabs__item {
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px solid $color-D9D9D9;
      padding: 0;
      min-width: 0;
      height: 42px;
      font-weight: 400;
      background-color: $color-FFFFFF;
      flex: 1;
      line-height: 42px;

      &.is-active {
        border: 1px solid $--color-primary;
        color: #FFFFFF;
        background-color: $--color-primary;

        .el-badge {
          .el-badge__content {
            margin-left: 4px;
            font-size: 14px;
            color: $color-text-primary;
            color: $color-FFFFFF;
            background-color: $color-warning;
          }
        }
      }

      .label {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .el-badge {
        display: flex;
        align-items: center;

        .el-badge__content {
          margin-left: 2px;
          border: 0;
          border-color: transparent;
          border-radius: 50%;
          padding: 0;
          width: 18px;
          height: 18px;
          font-size: 12px;
          color: $color-FFFFFF;
          background-color: $color-warning;
          line-height: 18px;
        }

        &.more {
          .el-badge__content {
            border-radius: 18px;
            padding: 0 6px;
            width: auto;
          }
        }

        &.mixmore {
          .el-badge__content {
            border-radius: 18px;
            padding: 0 4px;
            width: auto;
          }
        }
      }

      &:nth-child(2) {
        border-radius: 2px 0 0 2px;
      }

      &:last-child {
        border-radius: 0 2px 2px 0;
      }
    }
  }

  .el-divider__text {
    color: $color-text-secondary;
    background-color: $color-F2F2F2;
  }

  .el-empty {
    margin-top: 28%;

    .el-empty__image {
      width: auto;
    }

    .el-empty__description {
      margin-top: -20px;
    }

    .el-empty__description p {
      font-size: 18px;
      color: $color-text-secondary;
    }
  }

  .empty-box {
    .empty-img {
      font-size: 14em;
      color: #FFFFFF;
    }
  }
}
</style>

<style lang="scss">
.bargain-container {
  .el-radio-button {
    .el-radio-button__inner {
      border: none;
      border-radius: none;
      height: 54px !important;
      font-size: $font-size-medium;

      .icon {
        font-size: 20px;
        vertical-align: -.2em;
      }
    }

    &:not(.is-active) {
      .el-radio-button__inner:hover {
        background: $--color-primary-hover;
      }
    }
  }
}
</style>

<template>
  <div class="bargain-container">
    <!-- 议价状态左侧栏 -->
    <el-radio-group v-model="activeStatus" class="left-wrap" @change="handleChangeStatus">
      <el-radio-button v-for="status in statusList" :key="status.id" :label="`${status.id}`">
        <icon class="icon" :type="status.icon" />
        <span class="label">
          {{ status.label }}
        </span>
      </el-radio-button>
    </el-radio-group>

    <!-- 票方资方选择-顶部tab栏 -->
    <el-tabs
      v-model="activeTab"
      class="right-wrap"
      @tab-click="handleClickTab"
    >
      <el-tab-pane v-for="tab in tabsList" :key="tab.id" :name="`${tab.id}`">
        <span slot="label" class="label">
          {{ tab.label }}

          <el-badge
            v-if="+activeStatus === BARGAIN_STATUS.BARGAINING.id"
            :value="+tab.id === BARGAIN_TAB_LIST.SALE.id ? getCount(sellerRecord) : getCount(buyerRecord) "
            :class="{
              more: (+tab.id === BARGAIN_TAB_LIST.SALE.id ? sellerRecord : buyerRecord) > 99,
              mixmore: (+tab.id === BARGAIN_TAB_LIST.SALE.id ? sellerRecord : buyerRecord) <= 99 && (+tab.id === BARGAIN_TAB_LIST.SALE.id ? sellerRecord : buyerRecord) > 9,
            }"
          />
        </span>
        <div class="btn-wrap">
          <span class="refresh-btn" @click="handleRefresh">
            <icon class="icon" type="chengjie-refresh" />
            刷新
          </span>
        </div>
        <!-- 议价消息列表 -->
        <div
          v-infinite-scroll="getBargainList"
          class="data-list"
          :infinite-scroll-disabled="disabled"
          infinite-scroll-delay="500"
          :infinite-scroll-distance="50"
        >
          <template v-if="+tab.id === +activeTab && list.length > 0">
            <!-- 议价卡片 -->
            <BargainCard
              v-for="data in list"
              :key="data.id"
              :card-data="data"
              :active-tab="activeStatus"
              :sdm-info="sdmInfo"
              :seller-bank-account-info="{bankCardList: sellerBankAccountList, sellerBankAccount: Number(data.sellerBankAccountId) || sellerBankAccountId}"
              :type="2"
              @close="close"
              @overtime="overtime"
              @success="handleRefresh"
            />
            <el-divider class="mt50">
              <template v-if="loading">
                加载中
              </template>
              <template v-else-if="isNoMore">
                到底了
              </template>
            </el-divider>
          </template>

          <el-empty v-if="!loading && !list.length">
            <div slot="image" class="empty-box">
              <icon type="chengjie-empty" class="empty-img" />
            </div>
          </el-empty>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
/* eslint-disable no-magic-numbers */

import BargainCard from '../bargain-card/bargain-card.vue'
import {
  BARGAIN_TAB_LIST, // 议价方Tab
  BARGAIN_STATUS, // 议价状态
} from '@/constants/bargain'
import bargainApi from '@/apis/bargain' // api接口
import { handleBargainCardData } from '../bargain-card/bargain-card' // 议价数据处理
import { mapGetters } from 'vuex'
export default {
  name: 'bargain-list-dialog',
  components: {
    BargainCard, // 议价消息卡片

  },
  data() {
    return {
      loading: false,
      list: [], // 议价消息列表
      statusList: Object.values(BARGAIN_STATUS).filter(v => v.isShow), // 左侧议价状态列表
      activeStatus: `${BARGAIN_STATUS.BARGAINING.id}`, // 左侧议价状态列表-当前选中的议价状态 (默认选中议价中)
      tabsList: Object.values(BARGAIN_TAB_LIST), // 顶部tab选项列表
      activeTab: `${BARGAIN_TAB_LIST.SALE.id}`, // 顶部tab选项列表-当前选中的tab选项 (默认选中票方)
      BARGAIN_TAB_LIST, // 议价方Tab
      allRecord: 0, // 议价消息列表总数
      buyerRecord: 0, // 资方tab数量
      sellerRecord: 0, // 票方tab数量
      // 查询参数
      query: {
        pageNum: 1, // 当前页码
        pageSize: 10, // 每页条数
      },
      totalRecord: 0, // 总条数
      isNoMore: false, // 没有更多了
      isLoadError: false, // 是否请求加载失败（用来阻止触底加载数据等）
      BARGAIN_STATUS, // 议价状态
    }
  },
  computed: {
    ...mapGetters('user', {
      sellerBankAccountList: 'sellerBankAccountList', // 回款账户列表
      sdmInfo: 'sdmInfo', // 米账号信息
    }),
    sellerBankAccountId() { // 回款账户Id
      return this.$store.state.common.sellerBankAccountId
    },
    // 是否可请求列表数据
    disabled() {
      return this.loading || this.isNoMore || this.isLoadError
    }
  },

  created() {
    this.init()
  },
  methods: {
    getCount(val) {
      if (val > 99) {
        return '99+'
      }
      return val !== 0 ? val : ''
    },
    // 初始化
    init() {
      this.activeStatus = `${BARGAIN_STATUS.BARGAINING.id}` // 每次打开都默认选中议价中
      this.initData() // 初始化数据
    },

    // 初始化数据
    async initData() {
      this.clearData() // 重置数据
      await this.$store.dispatch('user/getPassedBankCardList')
      await this.$store.dispatch('common/getNewVersionDraftConfig')

      try {
        // this.loading = true

        await this.getBargainList() // 获取议价消息列表
        await this.getBargainTabNum() // 查询议价数量

        // this.loading = false
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log('议价error :>> ', error)

        // this.loading = false
      }
    },

    // 获取议价消息列表
    async getBargainList() {
      if (this.isNoMore || this.loading) return

      // 议价列表传参
      const params = {
        ...this.query, // 分页参数
        corpTradeRole: +this.activeTab, // 议价操作角色，1-资方，2-票方
        bargainStatus: +this.activeStatus // 议价状态 0-议价中，1-议价完成，2-议价失败
      }

      try {
        this.loading = true
        const data = await (bargainApi.traderCorpBargainList(params) || {})
        const { rowList, totalRecord } = data

        // 处理数据
        const newList = rowList.reduce((t, p) => {
          const item = handleBargainCardData(p) // 议价数据处理
          t.push(item)
          return t
        }, [])

        this.list = this.list.concat(newList)

        this.totalRecord = totalRecord
        this.query.pageNum++

        this.isNoMore = totalRecord === 0 || rowList.length === 0 ? true : (this.list.length > 0 && (this.list.length >= totalRecord))

        this.loading = false
        this.isLoadError = false
      } catch (error) {
        this.loading = false
        this.isLoadError = true
        // console.log('议价列表error :>> ', error)
        return error
      }
    },

    // 查询议价数量
    async getBargainTabNum() {
      try {
        // 议价列表传参
        const params = {
          corpTradeRole: +this.activeTab, // 议价操作角色，1-资方，2-票方
          bargainStatus: +this.activeStatus // 议价状态 0-议价中，1-议价完成，2-议价失败
        }

        const data = await (bargainApi.bargainTabNum(params) || {})

        this.allRecord = data?.all || 0 // 全部
        this.buyerRecord = data?.buyer || 0 // 资方
        this.sellerRecord = data?.seller || 0 // 票方

        // 议价中，回调议价中的所有议价数量
        if (params.bargainStatus === BARGAIN_STATUS.BARGAINING.id) {
          this.$store.commit('market/setBargainingCount', data?.all || 0)
        }
      } catch (error) {
        // console.log('议价数量error :>> ', error)
        return error
      }
    },

    // 查询议价中的数量
    async getBargainingTabNum() {
      // 议价列表传参
      const params = {
        corpTradeRole: +this.activeTab, // 议价操作角色，1-资方，2-票方
        bargainStatus: +BARGAIN_STATUS.BARGAINING.id // 议价状态 0-议价中
      }

      try {
        const data = await (bargainApi.bargainTabNum(params) || {})
        const {
          all, // 全部
        } = data

        // 议价中，回调议价中的所有议价数量
        this.$store.commit('market/setBargainingCount', all || 0)
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log('error :>> ', error)
      }
    },

    // 点击左侧选择议价状态
    handleChangeStatus() {
      this.handleRefresh()
    },

    // 点击tab选择票方资方
    handleClickTab(tab) {
      this.activeTab = tab?.name
      this.handleRefresh()
    },

    // 刷新
    handleRefresh() {
      // if (this.loading) return
      this.$nextTick().then(() => {
        this.initData() // 刷新数据
      })
    },

    // 清除数据
    clearData() {
      this.list = [] // 清除列表
      this.query.pageNum = 1
      this.totalRecord = 0
      this.isNoMore = false
      this.isLoadError = false
      this.loading = false
    },

    // 倒计时结束
    overtime() {
      this.handleRefresh()
    },
    // 关闭弹窗
    close() {
      this.$emit('close')
    }

  }
}
</script>
