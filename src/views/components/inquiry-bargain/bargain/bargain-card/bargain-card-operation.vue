
<!-- 议价-消息卡片底部操作区 -->
<style lang="scss" scoped>
.bargain-card-operation-wrap {
  width: 100%;
}

.bargain-card-operation {
  &.has-line {
    margin-top: 12px;
    border-top: 1px solid $color-D9D9D9;
    padding-top: 12px;
  }

  .countdown {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    color: $--color-primary;

    ::v-deep .time {
      font-size: 16px;
    }
  }

  .el-button {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 15px;

    // min-width: 64px;
    height: 40px;

    &:not(:last-child) {
      border: 1px solid  $--color-primary;
      color: $--color-primary;
      background: #FFFFFF;
    }
  }
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  // .left-wrap {
  // }

  .right-wrap {
    margin-left: 12px;
    flex: 1;
    min-width: 0;
  }

  .footer-right,
  .footer-left {
    display: flex;
    align-items: center;

    // width: 100%;
    min-width: 0;
    flex: 1 auto;
  }

  .footer-left {
    justify-content: flex-start;
  }

  .footer-right {
    justify-content: flex-end;

    // flex-wrap: wrap;
  }
}

.bold {
  font-weight: 600;
}

.fz16 {
  font-size: 16px;
}
</style>

<template>
  <div v-if="data && data.isHandled" class="bargain-card-operation-wrap">
    <div
      class="bargain-card-operation footer"
      :class='{
        "has-line": isShowBottomLine
      }'
    >
      <div v-if="!data.isBargainEnd" class="footer-left">
        <div
          v-if="data.isShowCountdown"
          class="countdown"
        >
          剩余<Countdown
            ref="countdownRef"
            :start-time="data.startTime"
            :end-time="data.endTime"
            :start="!data.isOvertime"
            @overtime="overtime"
          />
        </div>
        <div
          v-if="data.isShowBargainCountTips"
          class="text-primary bold fz16"
        >
          {{ data.isSale ? '资方今日内还能进行一次议价' : '您还能发起一次议价' }}
        </div>
      </div>
      <div class="footer-right">
        <transaction-tooltip-button
          v-if="data.isShowReject && !data.isBargainEnd"
          v-waiting="['post::loading::/draft/bargain/cancelBargain',
                      'post::loading::/draft/bargain/cancelDickerBargain']"
          type="primary"
          :types="[TRANSACTION_TOOLTIP_TYPE.HOLIDAY]"
          @click="handleReject"
        >
          拒绝
        </transaction-tooltip-button>

        <transaction-tooltip-button
          v-if="data.isShowRecall && !data.isBargainEnd"
          v-waiting="['post::loading::/draft/bargain/revokeBargain',
                      'post::loading::/draft/bargain/revokeDickerBargain']"
          type="primary"
          :types="[TRANSACTION_TOOLTIP_TYPE.HOLIDAY]"
          @click="handleRecall"
        >
          撤回{{ data.isBargain ? '议价' : '还价' }}
        </transaction-tooltip-button>

        <transaction-tooltip-button
          v-if="data.isShowReBargainDicker && !data.isBargainEnd"
          v-waiting="['post::loading::/draft/bargain/rePostBargain', 'post::loading::/draft/bargain/dickerBargain',
          ]"
          type="primary"
          :types="[TRANSACTION_TOOLTIP_TYPE.HOLIDAY]"
          @click="handleReBargain"
        >
          {{ data.isSale ? '还价' : '重新议价' }}
        </transaction-tooltip-button>

        <transaction-tooltip-button
          v-if="data.isShowConfirm && !data.isBargainEnd"
          v-waiting="['post::loading::/draft/bargain/confirmBargain',
                      'post::loading::/draft/bargain/confirmDickerBargain']"
          type="primary"
          :types="[TRANSACTION_TOOLTIP_TYPE.HOLIDAY]"
          @click="handleConfirm"
        >
          同意
        </transaction-tooltip-button>

        <transaction-tooltip-button
          v-if="data.isShowConfirmOriginal"
          v-waiting="['post::loading::/draft/bargain/confirmBargain',
                      'post::loading::/draft/bargain/confirmDickerBargain',
                      `get::loading::/draft/order/getDraftOrderInfo/${data.orderNo}`
          ]"
          type="primary"
          :types="[TRANSACTION_TOOLTIP_TYPE.HOLIDAY]"
          @click="handleConfirmOriginal"
        >
          原价接单
        </transaction-tooltip-button>

        <transaction-tooltip-button
          v-if="data.isShowCheckOrder"
          type="primary"
          :types="[TRANSACTION_TOOLTIP_TYPE.HOLIDAY]"
          @click="handleCheckOrder"
        >
          {{ data.confirmButtonText || '查看订单' }}
        </transaction-tooltip-button>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable no-magic-numbers */

import Countdown from '@/views/pages/draft-detail/components/countdown.vue' // 倒计时
import bargainApi from '@/apis/bargain' // 议价api
import { BARGAIN_DIALOG, RECEIVE_ORDER_DETAIL } from '@/event/modules/site'
import { TRANSACTION_TOOLTIP_TYPE } from '@/constants/transaction-tooltip'
import store from '@/store'
import userObj from '@/utils/user.js' // 用户对象操作
import { enjambmentConfirmOrderTips } from '@/utils/limit-order-taking.js'
import { yuan2wan } from '@/common/js/number'

// 议价操作（同意、撤回、取消）（议价，还价）
const BARGAIN_ACTION = {
  // 同意议价
  CONFIRM_BARGAIN: {
    id: 1,
    name: '同意议价',
    api: 'confirmBargain', //  接口
    successText: '已同意资方议价', // 接口成功文案
  },
  // 同意还价
  CONFIRM_DICKER_BARGAIN: {
    id: 2,
    name: '同意还价',
    api: 'confirmDickerBargain', //  接口
    successText: '同意还价', // 接口成功文案
  },
  // 撤回议价
  REVOKE_BARGAIN: {
    id: 3,
    name: '撤回议价',
    api: 'revokeBargain', //  接口
    successText: '撤回议价成功', // 接口成功文案
  },
  // 撤回还价
  REVOKE_DICKER_BARGAIN: {
    id: 4,
    name: '撤回还价',
    api: 'revokeDickerBargain', //  接口
    successText: '撤回还价成功', // 接口成功文案
  },
  // 取消(拒绝)议价
  CANCEL_BARGAIN: {
    id: 5,
    name: '取消(拒绝)议价',
    api: 'cancelBargain', //  接口
    successText: '已拒绝资方议价', // 接口成功文案
  },
  /// 取消(拒绝)还价
  CANCEL_DICKER_BARGAIN: {
    id: 6,
    name: '取消(拒绝)还价',
    api: 'cancelDickerBargain', //  接口
    successText: '拒绝还价', // 接口成功文案
  },
}
export default {
  name: 'bargain-card-operation',
  components: {
    Countdown, // 倒计时
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    sellerBankAccountId: { // 传入回款账户id 校验非空判断
      type: Number
    }
  },
  data() {
    return {
      TRANSACTION_TOOLTIP_TYPE, // 交易限制提示类型
      loading: false
    }
  },
  computed: {
    limitOrderInfo() { // 限制接单配置参数
      return store?.state?.common?.limitOrderInfo || null
    },
    // 是否展示底部横线
    isShowBottomLine() {
      const {
        isShowCountdown, // 是否显示倒计时
        isShowBargainCountTips, // 是否显示剩余次数文案
        isShowCheckOrder, // 是否显示查看订单
        isShowConfirmOriginal, // 是否显示原价接单
        isBargainEnd, // 该议价是否已结束
      } = this.data
      return isShowCountdown || (isShowBargainCountTips && !isBargainEnd) || isShowCheckOrder || isShowConfirmOriginal
    },

    // 资方-是否重新议价
    isRePostBargain() {
      const { isDicker, isBargain, isBuy } = this.data
      return (isDicker || isBargain) && isBuy
    },

    // 票方-是否还价
    isDickerBargain() {
      const { isBargain, isSale } = this.data
      return isBargain && isSale
    },

    // 是否倒计时
    isShowCountdown() {
      return (this.data || {}).isShowCountdown
    }
  },

  watch: {
    isShowCountdown: {
      handler(val) {
        if (val) {
          this.$nextTick().then(() => {
            this.$refs.countdownRef && this.$refs.countdownRef.init()
          })
        }
      },
      immediate: true,
    },
  },

  methods: {

    // 倒计时结束
    overtime(val) {
      // console.log('onTimeUp :>> 倒计时结束', val)
      this.$emit('overtime', val)
    },

    // 重新议价（还价）
    handleReBargain() {
      const { isDickerBargain, isRePostBargain } = this

      // 还价
      if (isDickerBargain) {
        // 更新当前议价数据的回款账户id
        Object.assign(this.data, { sellerBankAccountId: this.sellerBankAccountId })
        this.$event.emit(BARGAIN_DIALOG, this.data)// 打开还价弹窗
      }

      // 重新议价(目前改为使用发起议价弹窗的组件)
      if (isRePostBargain) {
        this.$event.emit(RECEIVE_ORDER_DETAIL, { ...this.data, isShowBargainDialog: true, bargainTitle: '重新议价' })// 打开重新议价弹窗
      }

      // 关闭该消息通知
      this.$emit('close-notification', true)
    },

    // 同意（议价，还价）
    async handleConfirm() {
      const { isBargain, paymentChannel } = this.data
      // E+渠道判断是否选择回款账户
      if (paymentChannel === 7 && !this.sellerBankAccountId) return
      const action = isBargain ? BARGAIN_ACTION.CONFIRM_BARGAIN : BARGAIN_ACTION.CONFIRM_DICKER_BARGAIN // （判断议价/还价）
      // 跨行转账限额同意议价确认提示
      const limitOrders = JSON.parse(JSON.stringify(this.limitOrderInfo))
      const params = Object.assign({}, limitOrders, {
        paymentChannel: this.data.paymentChannel, // 渠道
        draftAmountWan: yuan2wan(this.data.draftPaymentAmount), // 实付金额
        generalAccount: this.data.generalAccount, // 是否亿联一般户
        generalAccountZb: this.data.generalAccountZb, // 是否众邦一般户
        hasAllOrder: false, // 是否批量
        isCancel: true // 是否展示取消按钮
      })
      // 跨行转账限额
      await enjambmentConfirmOrderTips(params, this)
      // 发起请求
      this.postBargain(action)
    },

    // 撤回（议价/还价）
    handleRecall() {
      const { isBargain } = this.data
      // 发起请求
      const action = isBargain ? BARGAIN_ACTION.REVOKE_BARGAIN : BARGAIN_ACTION.REVOKE_DICKER_BARGAIN // （判断议价/还价）
      this.postBargain(action)
    },

    // 取消(拒绝)（议价/还价）
    handleReject() {
      const { rejectDialogText, isBargain } = this.data
      this.$confirm(rejectDialogText, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
      }).then(() => {
        // 发起请求
        const action = isBargain ? BARGAIN_ACTION.CANCEL_BARGAIN : BARGAIN_ACTION.CANCEL_DICKER_BARGAIN // （判断议价/还价）
        this.postBargain(action)
      })
    },

    /**
     *  根据传参发起不同的请求-（同意、撤回、取消）（议价，还价）
     * @param {Object} action 议价操作
     */
    async postBargain(action) {
      // console.log('议价操作action :>> ', action)
      try {
        if (!action) return
        const { isBargain } = this.data
        const fetchFunc = bargainApi[action.api] || ''
        if (!fetchFunc) return
        const {
          id, // 议价ID
          dickerLakhFee, // 还价的每十万扣款
          dickerAnnualInterest, // 还价的年利率真实年利率
          bidLakhFee, // 议价/重新议价的每十万扣款
          bidAnnualInterest, // 议价/重新议价的年利率真实年利率
        } = this.data
        const params = {
          bargainId: id, // 议价ID
          dickerLakhFee: isBargain ? bidLakhFee : dickerLakhFee, //  议价 还价每十万扣款,还价的时候需要填
          dickerAnnualInterest: isBargain ? bidAnnualInterest : dickerAnnualInterest, //  议价 还价年利率真实年利率
          sellerBankAccountId: this.sellerBankAccountId
        }

        this.loading = true
        await fetchFunc(params)
        // 回调成功,弹出toast,返回对应的toast文案
        this.$emit('success', true, action.successText)
        this.loading = false
        // 关闭该消息通知
        this.$emit('close-notification', true)
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log('（同意、撤回、取消）（议价，还价） error:>> ', error)

        this.loading = false
        // 关闭该消息通知
        this.$emit('close-notification', true)
      }
    },

    // 查看订单
    handleCheckOrder() {
      const { orderNo = '' } = this.data
      // 订单详情
      let path = `/user-center/draft-detail?orderNo=${orderNo}`

      // 关闭该消息通知
      this.$emit('close-notification', true)

      // 新开处理页面
      window.open(path)
    },

    // 原价接单
    async handleConfirmOriginal() {
      const { orderNo } = this.data

      if (!orderNo) return

      // 是否有渠道,没有则弹窗
      await store.dispatch('user/getNoBanAccountList')
      const hasChannel = !!store.state.user.noBanAccountList.length
      if (!hasChannel) {
        userObj.openCustomerServiceDialog()
        return
      }

      // 触发接单详情弹窗
      this.$event.emit(RECEIVE_ORDER_DETAIL, { orderNo, isShowReceiveOrderDetail: true })

      // 关闭该消息通知
      this.$emit('close-notification', true)
    }
  },
}
</script>
