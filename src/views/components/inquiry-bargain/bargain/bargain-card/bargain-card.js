// 议价通知组件-处理数据
import { dataType } from '@/common/js/util'
import {
  BARGAIN_STATUS, // 议价状态
  BARGAINING_STATUS,
  BARGAIN_TAB_LIST, // 议价中状态
} from '@/constants/bargain'
import { PAYMENT_CHANNEL_VALUE_MAP } from '@/constant.js'
// 议价描述列表
const rows = [
  // 票号
  {
    id: 0,
    isEllipsisText: true,
    list: [
      {
        id: 1,
        label: '票号',
        key: 'draftNo',
        value: null,
        isBold: true,
      },
    ],
  },
  // 承兑人
  {
    id: 1,
    isEllipsisText: true,
    list: [
      {
        id: 1,
        label: '承兑人',
        key: 'acceptorName',
        value: null,
        isBold: true,
      },
    ],
  },
  // 到期日
  {
    id: 2,
    isEllipsisText: true,
    list: [
      {
        id: 1,
        label: '到期日',
        bgClass: 'item-column-bag-cls',
        key: 'maturityDate',
        value: null,
        isBold: true,
      },
      {
        label: '支付方式',
        bgClass: 'item-column-bag-cls',
        key: 'paymentChannel',
        dictMap: PAYMENT_CHANNEL_VALUE_MAP,
        value: null,
        isBold: true,
      },
    ],
  },
  // 到期日 | 资方报价or还价
  {
    id: 3,
    isEllipsisText: true,
    list: [
      {
        id: 1,
        label: '票方报价', // 后面动态取值
        key: 'sale', // 自定义key，后面动态取值
        highlight: false, // 是否高亮
        value: null,
        isBold: true,
      },
      {
        id: 2,
        label: '资方报价', // 后面动态取值
        key: 'buy', // 自定义key，后面动态取值
        highlight: false, // 是否高亮
        value: null,
        isBold: true,
      },

    ],
  },
  // 票方报价or还价 ｜ 拆分金额
  {
    id: 4,
    isEllipsisText: true,
    list: [
      {
        id: 1,
        label: '票面金额',
        bgClass: 'item-column-bag-cls',
        highlight: false, // 是否高亮
        key: 'draftAmount',
        value: null,
        isBold: true,
      },
      {
        id: 2,
        label: '拆分金额',
        highlight: false, // 是否高亮
        key: 'splitAmount',
        value: null,
        isBold: true,
      },
    ],
  },

]

// 议价状态处理后的相关数据
/**
 *
 * @param {Object} data 议价消息数据
 * @param {String} key 需要取的值，不传则返回所有的数据
 * @returns {Object|String|Number} 默认返回处理后的所有数据，也可以单传key取指定的数据
 */
export const getBargainData = (data = {}, key = '') => {
  if (dataType(data) !== 'object') return {}
  // 传入的议价消息数据
  const {
    bargainStatus, // 议价状态
    bargainingState, // 议价中的子状态
    bargainCount, // 议价次数
    role, // 1-资方，2-票方
    isFinished, // 本次议价是否结束0-未结束1-结束
    lakhFee, // 每十万扣款
    bidLakhFee, // 出价每十万扣款
    dickerLakhFee, // 还价每十万扣款
    annualInterest, // 年利率真实年利率100
    bidAnnualInterest, // 出价年利率真实年利率100
    dickerAnnualInterest, // 还价年利率真实年利率100
    endTime, // 超时时间（时间戳-毫秒）
    updatedAt, // 更新时间
    frontImageUrl, // 票据截图
    isDeleted, // 该议价消息是否已失效(该字段用来控制某些按钮是否可用. 场景::当后面重新议价成功或已被接单等订单信息改变的情况发生后,第一次议价的某些按钮便不再适用,比如原价接单等)
    draftJson, // 识票助手票面数据
  } = data

  // 返回处理后的数据obj
  let res = {
    title: '', //  左侧议价状态标题
    bargainStatusTips: null, // 右则议价状态详细提示文案
    saleLabel: null, //  票方议价/还价label文案
    buyLabel: null, //  资方议价/还价label文案
    saleValue: null, //  票方议价/还价value
    buyValue: null, //  资方议价/还价value
    isShowReBargainDicker: false, // 是否展示重新议价、还价按钮
    isShowConfirmOriginal: false, //  是否展示原价接单按钮
    isShowConfirm: false, // 是否展示同意按钮
    isShowReject: false, // 是否展示拒绝按钮
    isShowRecall: false, // 是否展示撤回按钮
    isShowCheckOrder: false, // 是否展示查看订单按钮
    isShowCountdown: false, // 是否显示倒计时
    isShowBargainCountTips: false, // 是否展示剩余议价机会提示文案
    // recallDialogText: null, // 撤回议价/还价二次确认弹窗文案
    rejectDialogText: null, // 拒绝议价/还价二次确认弹窗文案
    isOvertime: false, // 是否已超时
    isSale: false, // 是否票方
    isBuy: false, // 是否资方
    startTime: null, // 开始计时时间（时间戳-毫秒）
    endTime: null, // 超时时间（时间戳-毫秒）
    draftImages: [], // 票据截图
    isDeleted: null, // 该议价消息是否已失效(该字段用来控制某些按钮是否可用. 场景::当后面重新议价成功或已被接单等订单信息改变的情况发生后,第一次议价的某些按钮便不再适用,比如原价接单等),
    confirmButtonText: '', // 确定按钮文字
  }

  const isBargainSuccess = bargainStatus === BARGAIN_STATUS.BARGAIN_SUCCESS.id // 是否议价成功
  const isBargainEnd = isFinished === 1 // 是否议价结束
  const isBargaining = bargainStatus === BARGAIN_STATUS.BARGAINING.id // 是否议价/还价中
  const isBargainFail = bargainStatus === BARGAIN_STATUS.BARGAIN_FAIL.id // 是否议价失败（第一次议价拒绝也处于该状态）
  const isSale = role === BARGAIN_TAB_LIST.SALE.id // 当前交易方-票方/资方 sale/buy
  const isBuy = role === BARGAIN_TAB_LIST.BUY.id // 当前交易方-票方/资方 sale/buy

  const isBargain = dickerLakhFee < 0// 议价
  const isDicker = dickerLakhFee >= 0 // 还价
  // const bargainLabel = isBargain ? '议价' : '还价'
  const MAX_COUNT = 3 // 最大议价次数

  res.startTime = updatedAt // 更新时间（时间戳-毫秒）
  res.endTime = endTime // 超时时间（时间戳-毫秒）
  res.isOvertime = res.startTime > res.endTime // 是否已经超时
  res.title = Object.values(BARGAIN_STATUS).filter(v => v.id === bargainStatus)[0]?.title // 消息头部标题
  res.draftJson = draftJson
  res.draftImages = frontImageUrl ? frontImageUrl.split(',') : []
  res.isSale = isSale
  res.isBuy = isBuy

  res.isBargain = isBargain
  res.isDicker = isDicker

  res.isBargainEnd = isBargainEnd
  res.isFinished = isFinished
  res.isDeleted = isDeleted
  res.isBargaining = isBargaining

  // *议价弹窗文案-撤回议价/还价二次确认弹窗文案
  // res.recallDialogText = `<p>是否撤回${bargainLabel}${bargainCount < MAX_COUNT ? `${isSale ? '，撤回后资方' : '。您'}还有 <span class="text-primary">一次${bargainLabel}机会</span>` : `，撤回后<span class="text-primary">${bargainLabel}结束</span>`} 。</p> `

  // *议价弹窗文案-拒绝议价/还价二次确认弹窗文案
  if (isSale) {
    // 第一次议价中票方拒绝
    if (bargainCount < MAX_COUNT) {
      res.rejectDialogText = `<p>是否拒绝议价？</p>
      <p>拒绝后，其它更高的资方报价将会被 <span class="text-primary">自动拒绝</span>。</p>
      <p>拒绝后，该资方还有 <span class="text-primary">一次议价机会</span>。</p>`
    }
    // 第二次
    if (bargainCount === MAX_COUNT) {
      res.rejectDialogText = '<p>是否拒绝，拒绝后交易关闭。</p>'
    }
  }
  if (isBuy) {
    // 第一次议价中票方拒绝
    if (bargainCount < MAX_COUNT) {
      res.rejectDialogText = '<p>是否拒绝，拒绝后您还有 <span class="text-primary">一次议价机会</span>。</p>'
    }
    // 第二次
    if (bargainCount === MAX_COUNT) {
      res.rejectDialogText = '<p>是否拒绝，拒绝后交易关闭。</p>'
    }
  }

  // **议价每十万扣款等数据展示-资方议价
  if (isBargain) {
    res.saleLabel = '票方报价'
    res.saleValue = `${lakhFee} 元 / ${annualInterest}%`
    res.buyLabel = '资方报价'
    res.buyValue = `${bidLakhFee} 元 / ${bidAnnualInterest}%`
  }

  // **议价每十万扣款等数据展示-票方还价
  if (isDicker) {
    res.saleLabel = '票方还价'
    res.saleValue = `${dickerLakhFee} 元 / ${dickerAnnualInterest}%`
    res.buyLabel = '资方报价'
    res.buyValue = `${bidLakhFee} 元 / ${bidAnnualInterest}%`
  }

  // 资方发起议价 / 议价,超时议价失败
  if (bargainingState === BARGAINING_STATUS.BUY_BARGAIN_APPLY.id || (isBargain && bargainingState === BARGAINING_STATUS.BARGAIN_FAIL_OVERTIME.id)) {
    res.bargainStatusTips = isSale ? '待确认资方议价' : '待票方确认' // 右上角议价状态文案
    res.isShowCountdown = true // 显示倒计时

    // 票方
    if (isSale) {
      // 未超时
      if (!res.isOvertime) {
        res.isShowReBargainDicker = true // 还价按钮
        res.isShowConfirm = true // 同意按钮
        res.isShowReject = true // 拒绝按钮
      } else {
        bargainCount < MAX_COUNT && (res.isShowBargainCountTips = true)// 展示剩余议价机会提示文案
      }
    }

    // 资方
    if (isBuy) {
      // 未超时
      if (!res.isOvertime) {
        res.isShowRecall = true // 撤回议价按钮
      } else {
        bargainCount < MAX_COUNT && (res.isShowReBargainDicker = true)// 重新议价按钮
        bargainCount < MAX_COUNT && (res.isShowBargainCountTips = true)// 展示剩余议价机会提示文案
      }

      res.isShowConfirmOriginal = !isDeleted// 原价接单按钮 (注意:该阶段展示原价接单,但若返回了isDeleted表明该议价消息已失效,不能操作按钮,故需隐藏原价接单)
    }

    // 超时
    if (res.isOvertime) {
      res.isShowCountdown = false // 关闭倒计时
      res.bargainStatusTips = `${isSale ? '' : '票方'}超时未确认` // 右上角议价状态文案
    }
  }

  // 资方撤回议价
  if (bargainingState === BARGAINING_STATUS.BUY_BARGAIN_RECALL.id) {
    res.bargainStatusTips = `${isSale ? '资方撤回议价' : '我已撤回'}` // 右上角议价状态文案
    // 资方
    if (isBuy) {
      bargainCount < MAX_COUNT && (res.isShowReBargainDicker = true) // 重新议价按钮
      res.isShowConfirmOriginal = !isDeleted// 原价接单按钮 (注意:该阶段展示原价接单,但若返回了isDeleted表明该议价消息已失效,不能操作按钮,故需隐藏原价接单)
      bargainCount < MAX_COUNT && (res.isShowCountdown = false) // 显示倒计时
    }

    bargainCount < MAX_COUNT && (res.isShowBargainCountTips = true) // 展示剩余议价机会提示文案
  }

  // 票方拒绝议价
  if (bargainingState === BARGAINING_STATUS.SALE_BARGAIN_REJECT.id) {
    res.bargainStatusTips = `${isSale ? '我已' : '票方'}拒绝` // 右上角议价状态文案

    // 票方
    if (isSale) {
      bargainCount < MAX_COUNT && (res.isShowBargainCountTips = true)// 展示剩余议价机会提示文案
    }

    // 资方
    if (isBuy) {
      bargainCount < MAX_COUNT && (res.isShowReBargainDicker = true)// 重新议价按钮
      res.isShowConfirmOriginal = !isDeleted// 原价接单按钮 (注意:该阶段展示原价接单,但若返回了isDeleted表明该议价消息已失效,不能操作按钮,故需隐藏原价接单)
      bargainCount < MAX_COUNT && (res.isShowBargainCountTips = true)// 展示剩余议价机会提示文案
    }
  }

  // 票方还价 / 还价,超时议价失败
  if (bargainingState === BARGAINING_STATUS.SALE_DICKER_APPLY.id || (isDicker && bargainingState === BARGAINING_STATUS.BARGAIN_FAIL_OVERTIME.id)) {
    res.bargainStatusTips = `${isSale ? '待资方确认' : '待确认票方还价'}` // 右上角议价状态文案

    // 票方
    if (isSale) {
      // 未超时
      if (!res.isOvertime) {
        res.isShowRecall = true // 撤回还价按钮
        res.isShowCountdown = true // 展示倒计时
      } else {
        bargainCount < MAX_COUNT && (res.isShowBargainCountTips = true)// 展示剩余议价机会提示文案
      }
    }

    // 资方
    if (isBuy) {
      // 未超时
      if (!res.isOvertime) {
        res.isShowConfirm = true // 同意按钮
        res.isShowReject = true // 拒绝按钮
        res.isShowCountdown = true // 展示倒计时
      } else {
        bargainCount < MAX_COUNT && (res.isShowReBargainDicker = true) // 重新议价按钮
        res.isShowConfirmOriginal = !isDeleted// 原价接单按钮 (注意:该阶段展示原价接单,但若返回了isDeleted表明该议价消息已失效,不能操作按钮,故需隐藏原价接单)
        bargainCount < MAX_COUNT && (res.isShowBargainCountTips = true)// 展示剩余议价机会提示文案
      }
    }

    // 超时
    if (res.isOvertime) {
      res.bargainStatusTips = `${isSale ? '资方' : ''}超时未确认` // 右上角议价状态文案
      res.isShowCountdown = false // 关闭倒计时
    }
  }

  // 票方撤回还价
  if (bargainingState === BARGAINING_STATUS.SALE_DICKER_RECALL.id) {
    res.bargainStatusTips = `${isSale ? '我已撤回' : '票方撤回还价'}` // 右上角议价状态文案

    // 票方
    if (isSale) {
      bargainCount < MAX_COUNT && (res.isShowBargainCountTips = true)// 展示剩余议价机会提示文案
    }

    // 资方
    if (isBuy) {
      res.isShowConfirmOriginal = !isDeleted// 原价接单按钮 (注意:该阶段展示原价接单,但若返回了isDeleted表明该议价消息已失效,不能操作按钮,故需隐藏原价接单)
      bargainCount < MAX_COUNT && (res.isShowReBargainDicker = true)// 重新议价按钮
    }
  }

  // 资方拒绝还价
  if (bargainingState === BARGAINING_STATUS.BUY_DICKER_REJECT.id) {
    res.bargainStatusTips = `${isSale ? '资方拒绝还价' : '我已拒绝'}` // 右上角议价状态文案

    // 票方
    if (isSale) {
      bargainCount < MAX_COUNT && (res.isShowBargainCountTips = true)// 展示剩余议价机会提示文案
    }

    // 资方
    if (isBuy) {
      res.isShowConfirmOriginal = !isDeleted// 原价接单按钮 (注意:该阶段展示原价接单,但若返回了isDeleted表明该议价消息已失效,不能操作按钮,故需隐藏原价接单)
      bargainCount < MAX_COUNT && (res.isShowReBargainDicker = true)// 重新议价按钮
    }
  }

  // 订单状态变更取消
  if (bargainingState === BARGAINING_STATUS.ORDER_STATUS_CANCELED.id) {
    res.bargainStatusTips = '订单信息已变更'
    res.isShowConfirmOriginal = false // 不显示原价接单按钮
  }

  // 议价成功
  if (isBargainSuccess) {
    res.bargainStatusTips = '议价成功'

    if (isBuy) {
      res.isShowCheckOrder = true // 查看订单按钮
      res.confirmButtonText = '查看订单'
    }
  }
  // 议价关闭-议价失败
  if (isBargainFail) {
    // res.bargainStatusTips = '议价关闭'
    res.title = '议价关闭'
  }

  res.tips = res.bargainStatusTips // 消息基础卡片组件所需要的字段
  res.isHandled = true // 是否已处理过数据

  res = Object.assign({}, data, res)
  return key && res[key] ? res[key] : res
}

// 议价卡片数据处理
export const handleBargainCardData = data => {
  let res = null
  res = getBargainData(data)
  const { saleLabel, buyLabel, saleValue, buyValue } = res
  // 消息描述列表
  const copyData = JSON.parse(JSON.stringify(rows))
  const newRows = copyData.map(v => {
    // data中不存在拆分金额splitAmount,则过滤掉拆分金额字段
    if (v.id === 4 && !data.splitAmount) {
      v.list = v.list.filter(item => item.key !== 'splitAmount')
    }

    v.list = v.list.map(item => {
      if (data[item.key]) {
        item.value = item.dictMap ? item.dictMap[data[item.key]] : data[item.key]
      }

      // 票方、资方报价or还价 数据处理
      const isSale = item.key === 'sale'
      const isBuy = item.key === 'buy'
      if (isSale || isBuy) {
        item.label = isSale ? saleLabel : buyLabel
        item.value = isSale ? saleValue : buyValue
        // item = handleSaleBuyBargainData(data, item)
      }

      // 议价 && 无拆分金额 => [票面金额 || 资方报价]高亮
      if (data.dickerLakhFee < 0 && !data.splitAmount) {
        if (item.key === 'draftAmount' || item.key === 'buy') {
          item.highlight = true
        }
      }
      // 议价 && 有拆分金额 => [拆分金额 || 资方报价]高亮
      if (data.dickerLakhFee < 0 && data.splitAmount) {
        if (item.key === 'splitAmount' || item.key === 'buy') {
          item.highlight = true
        }
      }

      // 还价 && 有拆分金额 => [票面金额 || 票方报价]高亮
      if (data.dickerLakhFee >= 0 && data.splitAmount) {
        if (item.key === 'sale' || item.key === 'splitAmount') {
          item.highlight = true
        }
      }

      // 还价 && 无拆分金额 => [票面金额 || 票方报价]高亮
      if (data.dickerLakhFee >= 0 && !data.splitAmount) {
        if (item.key === 'draftAmount' || item.key === 'sale') {
          item.highlight = true
        }
      }

      return item
    })
    return v
  })
  res.rows = JSON.parse(JSON.stringify(newRows))
  return res
}
