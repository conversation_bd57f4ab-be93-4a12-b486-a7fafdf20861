
<!-- 议价通知组件 -->
<style lang="scss" scoped>
@import "~/src/views/components/notification/components/notification-card-list/common.scss";

.bargain-card {
  ::v-deep .notification-card {
    .card-footer {
      margin-top: 0;
      border-top: 0;
      padding-top: 0;
    }
  }

  .draftNo {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .btn-preview {
    @include example-underline;

    margin-left: 10px;

    &.is-disabled {
      color: #BFBFBF;
      cursor: not-allowed;
    }
  }
}

.sdicon-copy {
  font-size: 18px;
  color: $color-text-light;
  cursor: pointer;

  &:hover {
    color: $--color-primary;
  }
}

.header-msg {
  margin-bottom: 8px;
  height: 16px;

  @include flex-sb;
}

.ismqtt {
  .ellipsis-text {
    line-height: 20px;
  }

  .item-label {
    display: inline-block;
  }

  .item-value {
    display: inline-block;
  }

  .item {
    padding: 0;
  }

  .item-column-2 {
    display: flex;
    border-right: 0;
    padding: 2px 4px;
    height: 24px !important;
    background-color: $background-color-disabled;
    flex: 1;

    .item-value {
      @include ellipsis;

      width: 125px;
    }
  }

  .item-column-1 {
    .item-value {
      display: contents;
    }
  }

  .desc-list .row .item-column-2:last-child {
    padding-left: 4px;
    box-sizing: border-box;
  }

  .desc-list .row .item-column-2:first-child {
    margin-right: 4px !important;
    margin-bottom: 4px !important;
    border-right: 0;
  }

  // .desc-list li div {
  //   margin-bottom: 0 !important;
  // }

  ::v-deep {
    .bargain-card-operation.has-line {
      border-top: 0;
      padding-top: 0;
    }

    .fz16 {
      font-size: 14px;
    }

    .el-button {
      height: 32px;
    }
  }
}

::v-deep {
  .ismqtt .notification-card {
    width: 438px !important;
  }
}

.sellerBankAccount-cls {
  padding-top: 15px;
}

.item-position {
  position: relative;

  ::v-deep .el-form-item__label {
    padding: 0;
  }

  .value {
    .el-tooltip {
      position: absolute;
      top: 1px;
      left: 65px;
    }
  }

  .max-width {
    width: 100%;
  }
}

.select-error {
  ::v-deep .el-select .el-input .el-input__inner {
    border-color: #EC3535 !important;
  }

  .error-tips {
    color: #EC3535;
  }
}

.seller-select-btn {
  font-size: 16px;
  text-align: center;
  color: $--color-primary;
  line-height: 40px;
  cursor: pointer;
}

.seller-bank-link {
  border-bottom: 1px solid $--color-primary;
  color: $--color-primary;
  cursor: pointer;
}

.sdm-box {
  padding-top: 15px;

  .num {
    font-weight: 600;
    color: $--color-primary;
  }

  .item {
    display: flex;
    margin-top: 5px;
    padding: 10px 5px;
    background-color: #F5F6F8;

    .item-content-bold {
      font-weight: 600;
    }

    .recharge {
      @include example-underline;

      line-height: 20px;
      margin-left: 6px;
    }
  }
}

.item-column-bag-cls {
  display: flex;
  border-right: 0;
  padding: 2px 4px;
  height: 24px !important;
  background-color: $background-color-disabled;
  flex: 1;
}
</style>

<template>
  <div class="bargain-card">
    <div v-if="isMQTT && myData && myData.id" class="ismqtt">
      <div class="header-msg">
        <div class="messageTile">
          <text-tooltip :content="myData.tips" />
        </div>
        <span
          class="btn-preview"
          :class="{
            'is-disabled': !myData.draftImages.length && (type === 1)
          }"
          @click="handleViewDraftImage"
        >
          查看票面
        </span>
      </div>
      <NotificationCard :data="myData" :is-mqtt="isMQTT">
        <ul
          slot="main"
          class="desc-list"
        >
          <li
            v-for="row, index in myData.rows"
            :key="row.id"
            :class="index > 1 && 'row'"
          >
            <div
              v-for="item in row.list"
              :key="item.key"
              :class="[`item-column-${row.list.length || 0}`, item.itemClass ? item.itemClass : '', item.bgClass ? item.bgClass : '', 'ellipsis-mb-4']"
            >
              <div class="item-label">
                {{ item.label }}：
              </div>
              <div class="item-value">
                <!-- 票面金额  || 拆分金额 -->
                <template v-if="['draftAmount', 'splitAmount'].includes(item.key)">
                  <text-tooltip :content="`${yuan2wan(item.value || 0)}万`" show-when-overflow>
                    <span :class="item.highlight ? 'text-primary' : ''">{{ `${yuan2wan(item.value || 0)}万` }}</span>
                  </text-tooltip>
                </template>
                <template v-else>
                  <text-tooltip :content="item.value" :class="item.highlight ? 'text-primary' : ''" show-when-overflow />
                  <icon
                    v-if="item.key === 'draftNo'"
                    v-copy="{ value: item.value, onSuccess }"
                    class="sdicon-copy"
                    type="chengjie-copy"
                  />
                </template>
              </div>
            </div>
          </li>
        </ul>
        <div slot="content">
          <!-- 票方同意议价冻结保证金提示 -->
          <div v-if="isSale && myData.margin" class="sdm-box">
            <div>资方选择带保证金接该笔订单，同意议价将冻结<span class="num"> {{ myData.marginAmt || 0 }}</span>{{ sdmUnit }}</div>
            <div class="item">
              <div class="item-label">
                {{ sdmName }}可用余额：
                <!-- <span class="tag-fan">返100%</span> -->
              </div>
              <div class="item-content item-content-bold">
                <span>{{ (myData.sdmInfo || {}).balanceAmt || '0.00' }}{{ sdmUnit }}</span>
                <span class="recharge" @click="recharge">充值</span>
              </div>
            </div>
          </div>
          <!-- 票方 && 邦+/E+/E++渠道 && bargainingState !== 6(6=>同意还价) 显示回款账户选项 -->
          <div v-if="isSale && [PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id, PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id, PAYMENT_CHANNEL.YL_PLUS.id].includes(myData.paymentChannel) && myData.bargainingState !== 6" class="sellerBankAccount-cls">
            <div class="txt">根据平台规则，您可修改回款账户为任意已绑定账户</div>
            <FormItem
              label="回款账户"
              class="form-item-block item-position"
              required
            >
              <el-tooltip
                placement="top"
                popper-class="issue-draft-tooltip"
              >
                <template slot="content">
                  <div>依照智付E+、E++渠道交易模式，票方发布票据必须选择回款账户(支持任意银行一般户)。需要在<span class="seller-bank-link" @click="linkTo">银行回款账户</span>页面绑定回款账户，交易完成后票款自动提现到账。</div>
                </template>
                <icon class="icon icon-question" type="chengjie-wenti" />
              </el-tooltip>

              <div :class="['pay-type-item', sellerBankAccount ? '' : 'select-error']">
                <el-select
                  ref="sellerBankAccount"
                  v-model="sellerBankAccount"
                  class="max-width"
                  :height="40"
                  clearable
                  placeholder="请选择回款账户"
                >
                  <el-option
                    v-for="item in myData.sellerBankAccountList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                  <div class="seller-select-btn" @click="linkTo"><i class="el-icon-plus" />添加回款账户</div>
                </el-select>
                <div v-if="!sellerBankAccount" class="error-tips">请选择回款账户</div>
              </div>
            </FormItem>
          </div>
        </div>
        <!-- 卡片底部操作区 -->
        <BargainCardOperation
          slot="footer"
          :is-mqtt="isMQTT"
          :data="myData"
          :seller-bank-account-id="sellerBankAccount ? Number(sellerBankAccount) : null"
          @overtime="overtime"
          @success="success"
          @fail="fail"
          @close-notification="closeNotification(notificationId)"
        />
      </NotificationCard>
    </div>
    <div v-else>
      <NotificationCard v-if="myData && myData.id" :data="myData">
        <template slot="header">
          <h2 class="card-header-title label">
            <text-tooltip :content="myData.title" />
          </h2>
          <div class="card-header-tip">
            <text-tooltip :content="myData.tips" />
          </div>
        </template>
        <ul
          slot="main"
          class="desc-list"
        >
          <li
            v-for="row in myData.rows"
            :key="row.id"
            class="row"
          >
            <div
              v-for="item in row.list"
              :key="item.key"
              :class="['item', `item-column-${row.list.length || 0}`, item.itemClass ? item.itemClass : '']"
            >
              <div class="item-label">
                <!-- 票号 -->
                <template v-if="item.key === 'draftNo'">
                  <div class="draftNo">
                    <span class="left">{{ item.label }}</span>
                    <span
                      class="right btn-preview"
                      :class="{
                        'is-disabled': !myData.draftImages.length && (type === 1)
                      }"
                      @click="handleViewDraftImage"
                    >查看票面</span>
                  </div>
                </template>
                <template v-else>
                  {{ item.label }}
                </template>
              </div>
              <div class="item-value" :class="[{ 'bold': item.isBold}]">
                <!-- 票面金额 -->
                <template v-if="['draftAmount', 'splitAmount'].includes(item.key)">
                  <text-tooltip :content="`${yuan2wan(item.value || 0)}万`" show-when-overflow>
                    <span :class="item.highlight ? 'text-primary' : ''">{{ `${yuan2wan(item.value || 0)}万` }}</span>
                  </text-tooltip>
                </template>
                <template v-else>
                  <div v-if="item.key === 'draftNo'">
                    {{ item.value }}
                    <icon
                      v-if="item.key === 'draftNo'"
                      v-copy="{ value: item.value, onSuccess }"
                      class="sdicon-copy"
                      type="chengjie-copy"
                    />
                  </div>
                  <text-tooltip v-else :class="item.highlight ? 'text-primary' : ''" :content="item.value" />
                </template>
              </div>
            </div>
          </li>
        </ul>
        <div v-if="activeTab === '0'" slot="content">
          <!-- 票方同意议价冻结保证金提示 -->
          <div v-if="isSale && myData.margin" class="sdm-box">
            <div>资方选择带保证金接该笔订单，同意议价将冻结<span class="num"> {{ myData.marginAmt || 0 }}</span>{{ sdmUnit }}</div>
            <div class="item">
              <div class="item-label">
                {{ sdmName }}可用余额：
                <!-- <span class="tag-fan">返100%</span> -->
              </div>
              <div class="item-content item-content-bold">
                <span>{{ (sdmInfo || {}).balanceAmt || '0.00' }}{{ sdmUnit }}</span>
                <span class="recharge" @click="recharge">充值</span>
              </div>
            </div>
          </div>
          <!-- E+渠道 && 没有回款账户id 显示回款账户选项 -->
          <div v-if="myData.isSale && bankAccountInfo && [PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id, PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id, PAYMENT_CHANNEL.YL_PLUS.id].includes(myData.paymentChannel)" class="sellerBankAccount-cls">
            <div class="txt">根据平台规则，您可修改回款账户为任意已绑定账户</div>
            <FormItem
              label="回款账户"
              class="form-item-block item-position"
              required
            >
              <el-tooltip
                placement="top"
                popper-class="issue-draft-tooltip"
              >
                <template slot="content">
                  <div>依照智付E+、E++渠道交易模式，票方发布票据必须选择回款账户(支持任意银行一般户)。需要在<span class="seller-bank-link" @click="() => { $router.replace('/user-center/bank-account?tabStatus=2');$emit('close'); }">银行回款账户</span>页面绑定回款账户，交易完成后票款自动提现到账。</div>
                </template>
                <icon class="icon icon-question" type="chengjie-wenti" />
              </el-tooltip>

              <div :class="['pay-type-item', sellerBankAccount ? '' : 'select-error']">
                <el-select
                  ref="sellerBankAccount"
                  v-model="sellerBankAccount"
                  class="max-width"
                  :height="40"
                  clearable
                  placeholder="请选择回款账户"
                >
                  <el-option
                    v-for="item in bankAccountInfo.bankCardList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                  <div class="seller-select-btn" @click="() => { $router.push('/user-center/bank-account?tabStatus=2'); $refs.sellerBankAccount.blur();$emit('close') }"><i class="el-icon-plus" />添加回款账户</div>
                </el-select>
                <div v-if="!sellerBankAccount" class="error-tips">请选择回款账户</div>
              </div>
            </FormItem>
          </div>
        </div>
        <!-- 卡片底部操作区 -->
        <BargainCardOperation
          slot="footer"
          :data="myData"
          :seller-bank-account-id="sellerBankAccount ? Number(sellerBankAccount) : null"
          @overtime="overtime"
          @success="success"
          @fail="fail"
          @close-notification="closeNotification(notificationId)"
        />
      </NotificationCard>
    </div>

    <!-- 票面截图 -->
    <ImgViewer
      v-if="myData.draftImages && myData.draftImages.length"
      v-model="imageViewerVisible"
      :url-list="myData.draftImages"
      :initial-index="0"
      :show-mini-image="true"
      :prev-btn="true"
      :next-btn="true"
      :copy-btn="true"
    />
    <!-- 充值 -->
    <Recharge ref="recharge" />
  </div>
</template>

<script>
import NotificationCard from '@/views/components/notification/components/notification-card/notification-card.vue' // 消息组件卡片
import BargainCardOperation from './bargain-card-operation.vue' // 消息通知卡片底部操作区组件
import { yuan2wan } from '@/common/js/number' // 金额单位转换
import { dataType } from '@/common/js/util' // 获取数据类型
import {
  handleBargainCardData // 议价卡片数据处理
} from './bargain-card'
import { notificationQueue } from '@/views/components/notification/js/notification' // 消息实例队列
import {
  MQTT_BARGAIN, // 议价消息-刷新议价消息列表
} from '@/event/modules/site'
import ImgViewer from '@/views/components/img-viewer/img-viewer.vue' //  票面截图组件
// 票据正面和背面截图生成
import imgGenerator from '@/views/components/market-draft-image-generator/market-draft-image-generator.js'
import FormItem from '@/recognize/components/issue-draft/components/form-item.vue'
import {
  BARGAIN_TAB_LIST, // 议价中状态
} from '@/constants/bargain'
import { router } from '@/router'
import { PAYMENT_CHANNEL } from '@/constant' // 会员等级 id 映射 名称
import Recharge from '@/views/components/user-center/recharge/recharge.vue' // 充值

export default {
  name: 'bargain-card',
  components: {
    NotificationCard, // 消息组件卡片
    BargainCardOperation, // 消息通知卡片底部操作区
    ImgViewer, // 票面截图组件
    FormItem,
    Recharge
  },
  props: {
    // vue.extend传入来的data数据（mqtt传入使用的数据）
    data: {
      type: Object,
      default: () => ({})
    },
    // 该消息组件id
    notificationId: {
      type: String,
      default: ''
    },
    // 议价列表传入使用的数据
    cardData: {
      type: Object,
      default: () => ({})
    },
    // 议价列表传入tab选中
    activeTab: {
      type: String,
      default: '0' // 默认议价中选中
    },
    // 议价列表传入使用的回款账户数据
    sellerBankAccountInfo: {
      type: Object,
      default: () => ({})
    },
    // 议价列表传入米账户信息
    sdmInfo: {
      type: Object,
      default: () => ({})
    },
    // 该议价消息卡片组件用途 1-MQTT推送 2-消息列表
    type: {
      type: Number,
      default: 1
    },

  },
  data() {
    return {
      myData: null, // 保存vue.extend传入来的data数据
      cardType: 'mqtt', //  该消息卡片的用途,默认mqtt消息推送
      imageViewerVisible: false, // 是否打开票面截图预览
      bankAccountInfo: null, // 议价列表传入的回款账户信息
      sellerBankAccount: null, // 下拉组件选中回款账户id
      PAYMENT_CHANNEL,
    }
  },
  computed: {
    // 该消息卡片组件是否用于mqtt
    isMQTT() {
      return this.type === 1 // 1-MQTT推送 2-消息列表
    },
    isSale() { // 是否票方
      return this.myData.role === BARGAIN_TAB_LIST.SALE.id
    }
  },
  watch: {
    // 监听该数据,若传入该数据表明该组件用在列表或其他地方上,而不是消息推送
    cardData: {
      handler(val) {
        if (val && this.type === 2) {
          this.cardType = 'list' // 该卡片用于列表
          this.myData = val
        }
      },
      immediate: true,
      deep: true
    },
    // 监听vue.extend传入来的data数据，再保存在自身数据myData上使用，实现数据响应式
    data: {
      handler(val) {
        if (val && this.type === 1) {
          this.myData = val
          this.sellerBankAccount = val.sellerBankAccount ? Number(val.sellerBankAccount) : null
        }
      },
      immediate: true,
      deep: true
    },
    // 监听议价列表传入的回款账户信息 非mqtt
    sellerBankAccountInfo: {
      handler(val) {
        if (val && this.type === 2) {
          this.bankAccountInfo = val
          this.sellerBankAccount = val.sellerBankAccount ? Number(val.sellerBankAccount) : null
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    yuan2wan,
    // 数据初始化
    initData(data) {
      if (!data) return

      // 传入的不是obj数据,无须处理数据，若是字符串消息返回,不是则跳出
      const dataTypeRes = dataType(data) // 数据类型
      if (data && dataTypeRes !== 'object') {
        if (dataTypeRes !== 'string') return // 不是消息字符串跳出
        return data
      }

      let res = null
      res = handleBargainCardData(data)
      return res
    },

    // 倒计时结束
    overtime(val) {
      if (this.cardType === 'mqtt') {
        // console.log('onTimeUp :>> 倒计时结束 mqtt', val)
        this.myData = handleBargainCardData(this.myData) // 更新数据
      }

      // 通知列表
      if (this.cardType === 'list') {
        // console.log('onTimeUp :>> 倒计时结束 list', val)
        this.myData = handleBargainCardData(this.myData) // 更新数据
        this.$emit('overtime', val)
      }
    },

    // 议价消息操作按钮成功
    success(isSuccess, toastText) {
      // 消息推送,关闭弹窗
      if (this.cardType === 'mqtt') {
        // 关闭消息
        this.closeNotification(this.notificationId)
      }

      // 通知列表
      if (this.cardType === 'list') {
        this.$emit('success', isSuccess, toastText)
      }

      // this.$message.closeAll()
      this.$message.success(toastText || '操作成功')

      // 刷新议价消息列表
      this.$event.emit(MQTT_BARGAIN)
    },

    // 议价消息操作按钮操作失败
    fail(msg = '') {
      this.$message.warning(msg)
    },

    // 关闭消息
    closeNotification(id) {
      notificationQueue.closeNotification(id)
    },
    // 复制成功
    onSuccess() {
      this.$message.success('复制成功')
    },

    // 点击查看票面
    handleViewDraftImage() {
      if (this.myData.draftJson) {
        this.generateScreenshot(this.myData.draftJson)
      }
      this.imageViewerVisible = true
    },

    // 生成票面图片
    async generateScreenshot(draftJson) {
      let frontImage
      const frontPromise = imgGenerator.screenshot(draftJson, 'front', 'canvas').then(frontData => {
        frontImage = frontData.toDataURL()
      })
      await Promise.all([frontPromise])
      this.myData.draftImages = [frontImage]
      return [frontImage]
    },
    linkTo() {
      router.push('/user-center/bank-account?tabStatus=2')
      this.$refs.sellerBankAccount.blur()
    },
    // 充值
    recharge() {
      this.$refs.recharge && this.$refs.recharge.init()
    },
  }
}
</script>
