<!-- 重新议价、还价、重新还价弹窗 -->
<style lang="scss" scoped>
.bargain-dicker-dialog {
  ::v-deep .dialog-footer {
    .el-button {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0;
      min-width: 84px;
      height: 40px;
    }
  }
}

.buy-bargain-card {
  margin-bottom: 12px;

  ::v-deep .desc-list .row {
    .item-column-2 {
      border-right: none;

      .item-label,
      .item-value {
        width: 100%;
        text-align: left;
      }

      &.bargain-actual-payment-amount {
        .item-label,
        .item-value {
          justify-content: flex-end;
          text-align: right;

          .text-primary {
            margin-right: 0;
          }
        }
      }
    }
  }
}

.text-primary {
  margin: 0 6px;
  font-weight: 600;
}
</style>

<template>
  <div>
    <el-dialog
      class="bargain-dicker-dialog"
      :title="bargainDickerTitle"
      :visible.sync="dialogVisible"
      width="600px"
      height="410px"
      :before-close="handleClose"
      :close-on-click-modal="false"
      append-to-body
      center
      :lock-scroll="false"
    >
      <div class="body">
        <!-- 资方报价 -->
        <Card title="资方报价" class="buy-bargain-card">
          <template slot="main">
            <ul class="desc-list">
              <li class="row no-line">
                <div class="item item-column-2">
                  <div class="item-label">
                    资方报价明细
                  </div>
                  <div class="item-value ">
                    每十万扣款<span class="text-primary">{{ data.bidLakhFee }}</span>元 / <span class="text-primary">{{ data.bidAnnualInterest }}%</span>
                  </div>
                </div>
                <div class="item item-column-2 bargain-actual-payment-amount">
                  <div class="item-label">
                    确认后实收金额
                  </div>
                  <div class="item-value bold">
                    <span class="text-primary bold">{{ buyBargainPayAmount }} 万元</span>
                  </div>
                </div>
              </li>
            </ul>
          </template>
        </Card>
        <!-- 我的议价/重新议价  -->
        <BargainDickerCard
          v-if="data && data.isHandled"
          ref="bargainDickerCard"
          :data="data"
          :type="2"
          @close="closeModel"
          @change="onChangeBargain"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          v-waiting="['post::loading::/draft/order/validateQuotedPriceDiff',
                      'post::loading::/draft/bargain/rePostBargain',
                      'post::loading::/draft/bargain/dickerBargain']"
          type="primary"
          class="btn-confirm"
          @click="handelConfirm"
        >
          确定
        </el-button>
      </div>
    </el-dialog>

    <!-- 实名认证弹窗 -->
    <!-- <VerifiedDialog v-if="isShowVerifiedDialog" ref="verifiedDialogRef" /> -->
  </div>
</template>

<script>
// TIPS 目前该组件只用于还价,重新议价已修改为议价申请弹窗
import BargainDickerCard from '../bargain-dicker-card/bargain-dicker-card.vue'
import bargainApi from '@/apis/bargain'
import {
  MQTT_BARGAIN, // 议价消息-刷新议价消息列表
} from '@/event/modules/site'
// import VerifiedDialog from '@/views/components/common/verified-dialog/verified-dialog.vue'// 实名认证弹窗
import Card from '@/views/pages/draft-detail/components/card'
import { yuan2wan } from '@/common/js/number'
import {
  lakhDeductionMath, // * 每十万扣款计算 => 年化利率 和 到账金额
} from '@/common/js/draft-math'
import { ISSUE_DRAFT_ERROR_CODE } from '@/constants/draft'

export default {
  name: 'bargain-apply-dialog',
  components: {
    Card,
    BargainDickerCard, // 议价-还价 卡片
    // VerifiedDialog, // 实名认证弹窗
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    },
    selectData: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      dialogVisible: false, // 弹窗显示
      form: {
        bargainLakhDeduction: null, // 议价后的每十万扣款款
        bargainAnnualInterest: null // 议价后的年利率
      },
      bargainAmountInput: null, // 输入的议价
      annualInterestInput: null, // 输入的年化
      serviceCharge: null, // 输入的每十万手续费
      isShowVerifiedDialog: false, // 是否显示实名失效弹窗
    }
  },
  computed: {
    // 标题(重新议价、还价、重新还价)
    bargainDickerTitle() {
      const {
        bargainCount, // 议价次数
        isBargain, // 是否议价
        isSale, // 是否票方
      } = (this.data || {})
      let dickerLabel = (bargainCount === 2 ? '重新还价' : '还价')
      let res = `${isBargain && isSale ? dickerLabel : '重新议价'}`
      return res
    },

    // 议价金额是否大于0
    isBargainInput() {
      let res = +this.bargainAmountInput > 0 && +this.annualInterestInput > 0
      return res
    },

    // 资方-是否重新议价
    isRePostBargain() {
      const {
        isDicker, // 是否还价
        isBargain, // 是否议价
        isBuy, // 是否资方
      } = this.data
      return (isDicker || isBargain) && isBuy
    },

    // 票方-是否还价
    isDickerBargain() {
      const { isBargain, isSale } = this.data
      return isBargain && isSale
    },

    // 资方报价-实付金额
    buyBargainPayAmount() {
      let res = null
      // 计算方式:每十万扣款
      let {
        bidLakhFee, // 资方报价的每十万扣款
        interestDays, // 计息天数
        draftAmount, //  票据金额
        splitAmount, // 拆分金额
      } = this.data
      // 优先取拆分议价金额 > 票面金额
      draftAmount = splitAmount || draftAmount
      const { receivedAmount } = lakhDeductionMath(draftAmount, bidLakhFee, interestDays)
      res = yuan2wan(receivedAmount || 0, { digits: 6, parseNumber: false, mode: 'round' })

      return res
    },

  },
  // watch: {
  //   // 接单详情带过来的数据
  //   selectData: {
  //     handler(val) {
  //       // console.log('接单详情带过来的数据selectData :>> ', val)
  //       // if (val) {}
  //     },
  //     deep: true,
  //     immediate: true
  //   }
  // },
  methods: {
    // 初始化
    init() {
      this.dialogVisible = true
      this.isShowVerifiedDialog = false
    },

    // 关闭弹窗
    handleClose() {
      this.isShowVerifiedDialog = false
      this.dialogVisible = false
    },

    // 清除数据
    clearData() {
      for (const key in this.form) {
        this.form[key] = null
      }
      this.$nextTick().then(() => {
        this.$refs.bargainDickerCard && this.$refs.bargainDickerCard.clearData()
      })

      this.isShowVerifiedDialog = false
    },

    // 确认申请议价
    handelConfirm() {
      const { isDickerBargain, isRePostBargain, data } = this
      // 还价更新选中的回款账户id
      this.form.sellerBankAccountId = data.sellerBankAccountId
      // 校验子表单
      let validateResult = true
      this.$nextTick().then(async() => {
        if (!this.$refs.bargainDickerCard) return

        validateResult = await this.$refs.bargainDickerCard.validateForm()
        if (!validateResult) return // 校验有误

        // 还价
        if (isDickerBargain) {
          this.dickerBargain()
        }

        // 重新议价
        if (isRePostBargain) {
          // this.reRostBargainApply() // 目前重新议价直接使用发起议价申请弹窗，调取发起议价接口，现在不会走到这一步
        }
      })
    },

    // 重新议价（目前重新议价流程已改为第一次发起议价，不需要该接口，但暂且保留以免后续产品再次改回）
    // async reRostBargainApply() {
    //   const {
    //     orderNo, // 订单no
    //     lakhFee, // 每十万扣款
    //     annualInterest, // 年利率
    //     id, // 议价消息id
    //   } = this.data
    //   const {
    //     bargainLakhDeduction, // 议价（还价）计算出的每十万扣款
    //     bargainAnnualInterest, // 议价（还价）计算出的年利率
    //   } = this.form
    //   // if (!orderNo) return
    //   const params = {
    //     orderNo,
    //     bargainId: id, // 第一次议价ID
    //     lakhFee, // 每十万扣款
    //     bidLakhFee: bargainLakhDeduction, // 议价（还价）计算出的每十万扣款
    //     annualInterest, // 年利率
    //     bidAnnualInterest: bargainAnnualInterest // 议价（还价）计算出的年利率
    //   }

    //   try {
    //     // console.log('重新议价 :>> ', params)
    //     await bargainApi.rePostBargain(params)
    //     this.$message.success('重新发起议价成功')
    //     this.clearData()
    //     this.dialogVisible = false
    //     this.$event.emit(MQTT_BARGAIN) // 刷新议价消息列表
    //   } catch (error) {
    //     console.log('重新议价 error:>> ', error)
    //     // this.dialogVisible = false

    //     // 对接口返回的错误code处理
    //     this.handleErrorCode(error)
    //   }
    // },

    // 还价
    async dickerBargain() {
      const { id } = this.data
      const { bargainLakhDeduction, bargainAnnualInterest, sellerBankAccountId } = this.form
      // if (!orderNo) return
      const params = {
        bargainId: id, // 第一次议价ID
        dickerLakhFee: bargainLakhDeduction, // 还价每十万扣款
        dickerAnnualInterest: bargainAnnualInterest, // 还价年利率真实年利率100
        sellerBankAccountId
      }

      try {
        // console.log('还价 重新还价 :>> ', params)
        await bargainApi.dickerBargain(params)
        this.$message.success('发起还价成功')
        this.clearData()
        this.dialogVisible = false
        // 刷新议价消息列表
        this.$event.emit(MQTT_BARGAIN)
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log('还价 重新还价 error:>> ', error)
        // this.dialogVisible = false

        // 对接口返回的错误code处理
        this.handleErrorCode(error)
      }
    },

    // 议价后的金额
    onChangeBargain(val) {
      // console.log('议价后的金额 :>> ', val)
      const {
        bargainLakhDeduction, // 议价后的每十万扣款款
        bargainAnnualInterest, // 议价后年利率
        bargainAmountInput, // 输入的议价
        annualInterestInput, // 输入的年化
        serviceCharge, // 输入的每十万手续费
        sellerBankAccountId // 回款账户
      } = val

      this.form.bargainLakhDeduction = bargainLakhDeduction
      this.form.bargainAnnualInterest = bargainAnnualInterest
      this.bargainAmountInput = bargainAmountInput
      this.annualInterestInput = annualInterestInput
      this.serviceCharge = serviceCharge
      this.form.sellerBankAccountId = sellerBankAccountId
    },

    // 还价接口错误处理
    handleErrorCode(error) {
      // 实名失效弹窗
      if (error?.data?.code === ISSUE_DRAFT_ERROR_CODE.REAL_NAME_EXPIRED) {
        this.$message.closeAll()
        this.isShowVerifiedDialog = true
        // this.$nextTick().then(() => {
        //   this.$refs.verifiedDialogRef && this.$refs.verifiedDialogRef.init()
        // })
      }
    },
    closeModel() {
      this.handleClose()
      this.$emit('close')
    }

  },
}
</script>
