<!-- 议价申请弹窗 -->
<style lang="scss" scoped>
.bargain-apply-dialog-wrap {
  .account-select {
    width: 100%;
  }

  .voucher-box {
    .item-label {
      display: flex;
      align-items: center;
    }

    .el-switch {
      margin-top: 2px;
    }
  }

  .pay-method-box {
    // height: 260px;
    width: 100%;
  }

  // 预计付款
  .expected-pay-box {
    // height: 204px;

    // ::v-deep .desc-list .row .item-column-2:first-child {
    // flex: 1;
    // }

    .desc-list .row {
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }

      .item {
        padding: 0 12px;

        &:first-child {
          padding-left: 0;
        }
      }
    }
  }

  .form {
    display: flex;
    justify-content: space-between;
    margin-top: 12px;
    width: 100%;
    text-align: left;

    .card {
      &.bargain-dicker-card {
        padding-bottom: 10px;
      }
    }

    .form-left,
    .form-right {
      display: flex;
      padding-bottom: 0;
      flex-direction: column;

      // width: 50%;

      >div {
        display: flex;
        margin-bottom: 12px;
        flex: auto;

        &:last-child {
          margin-bottom: 0;
        }

        ::v-deep .card {
          width: 100%;
        }
      }
    }

    .form-left {
      width: 46%;
    }

    .form-right {
      margin-left: 12px;
      width: 54%;
    }

    ::v-deep {
      .card {
        margin-bottom: 12px;
        padding: 12px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .card-main {
        margin-top: 12px;
      }

      .item-label {
        line-height: 22px;
      }
    }
  }

  .item-value {
    align-items: center;
    margin-top: 2px;
  }

  ::v-deep .pay-channel-radios {
    width: 100%;

    .el-radio-group {
      flex-wrap: wrap;
      display: flex;

      .el-radio-button {
        margin: 0  8px 8px 0;
        width: 107px;
      }

      .el-radio-button .el-radio-button__inner {
        padding: 0 15px;
      }
    }
  }
}

// ele组件样式
::v-deep {
  .el-alert {
    padding: 9px 13px;

    &--info {
      &.is-light {
        background-color: $color-E6F3F3;
      }
    }

    &__title {
      .title {
        font-size: 16px;
        font-weight: 400;
        text-align: left;
        font-style: normal;
        line-height: 22px;
        letter-spacing: 0;
        color: $color-text-primary;
      }

      .icon {
        margin-right: 2px;
        font-size: 17.5px;
        color: $font-color;
      }
    }

    &__content {
      padding: 0;
    }
  }

  .el-link.el-link--primary {
    color: $font-color;

    &:hover {
      color: $--color-primary;
    }
  }

  .el-link.is-underline {
    &:hover {
      color: $--color-primary;
    }

    &::after {
      border-color: $font-color;
    }
  }

  .el-switch {
    margin-right: 10px;
    width: 44px;

    .el-switch__core {
      width: 40px !important;
    }
  }

  .dialog-footer {
    .el-button {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0;
      min-width: 84px;
      height: 40px;
    }
  }

  .el-form-item__content {
    width: 100%;
    box-sizing: border-box;
  }
}

.mt16 {
  margin-top: 16px;
}

.mt08 {
  margin-top: 8px;
}

.bargain-amount {
  display: flex;
  justify-content: flex-start;
  flex: 70%;

  .text-primary {
    margin: 0 6px;
    font-weight: 600;
  }
}

.platform-service-fee-tips {
  margin-left: 6px;
  font-size: 14px;
  color: $--color-primary;
}

.icon-question {
  margin-left: 4px;
  font-size: 20px;
}

.service-amount {
  margin-right: 10px;
  padding-top: 4px;
  font-size: 14px;
}

.tooltip-color {
  color: $color-warning;
}

.mr-s {
  margin-right: 8px;
}

.link-txt {
  color: $--color-primary;
  cursor: pointer;
}

.flex-between {
  display: flex;
  justify-content: space-between;
}
</style>

<template>
  <div>
    <el-dialog
      class="bargain-apply-dialog-wrap"
      :title="title"
      :visible.sync="dialogVisible"
      width="1070px"
      height="664px"
      :before-close="handleClose"
      :close-on-click-modal="false"
      append-to-body
      center
      :lock-scroll="false"
    >
      <div class="body">
        <!-- 顶部提示语 -->
        <WarnContent class="tip-box" class-type="blue">
          若票方同意您的议价，系统将自动改价、接单和确认，进入打款环节。
        </WarnContent>
        <!--
          <el-alert>
          <h2 slot="title" class="title">
          <icon class="icon" type="sdicon-info-circle" />
          若票方同意您的议价，系统将自动改价、接单和确认，进入打款环节。
          </h2>
          </el-alert>
        -->
        <!-- 议价表单 -->
        <el-form ref="form" :model="form" class="form">
          <div class="form-left">
            <!-- 票方报价 -->
            <Card title="票方报价">
              <template slot="main">
                <div class="row flex-between">
                  <div class="item">
                    <div class="item-label">
                      每十万扣款 / 年利率
                    </div>
                    <div class="item-value bold">
                      {{ draftInfo.lakhFee }} 元 / {{ draftInfo.annualInterest }}%
                    </div>
                  </div>

                  <div class="item">
                    <div class="item-label">
                      票面金额
                    </div>
                    <div class="item-value bold">
                      {{ yuan2wan(draftInfo.draftAmount) }} 万元
                    </div>
                  </div>
                </div>
              </template>
            </Card>

            <el-form-item>
              <!-- 我的议价 -->
              <BargainDickerCard
                ref="bargainDickerCard"
                :data="draftInfo"
                :type="1"
                @change="onChangeBargain"
                @update-order-fee="onUpdateOrderFee"
              />
            </el-form-item>
          </div>
          <div class="form-right">
            <!-- 选择支付渠道 -->
            <Card title="支付方式" class="pay-method-box">
              <template slot="main">
                <div class="row">
                  <div class="item">
                    <div class="item-label">
                      支付方式
                    </div>
                    <div class="item-value" style="margin-left: 8px;">
                      <PayChannelRadio
                        ref="payChannelRadio"
                        :draft-info="draftInfo"
                        :select-data="form"
                        @change="onChangePayChannel"
                      />
                    </div>
                  </div>
                </div>
                <div class="row mt08">
                  <div class="item">
                    <div class="item-label">
                      签收账户
                      <el-tooltip
                        placement="top-start"
                        :disabled="!dialogVisible"
                      >
                        <template slot="content">
                          您也可以前往<span
                            class="link-txt"
                            @click="() => {
                              $router.replace('/user-center/bank-account?tabStatus=1');
                              $emit('close-receive-order', true)
                              handleClose();
                            }"
                          >
                            银行账户</span>页面维护账户是否支持签收新一代票据
                        </template>
                        <icon class="icon icon-question" size="18" type="chengjie-wenti" />
                      </el-tooltip>
                    </div>
                    <div class="item-value ">
                      <AccountListSelect
                        ref="accountListSelect"
                        :draft-info="draftInfo"
                        :select-data="form"
                        @change="onChangeAccount"
                      />
                    </div>
                  </div>
                </div>
              </template>
            </Card>
            <!-- 预计付款 -->
            <Card title="预计付款" class="expected-pay-box">
              <template slot="main">
                <ul class="desc-list">
                  <li class="row ">
                    <div class="item item-column-2">
                      <div class="item-label">
                        我议价后的报价
                      </div>
                      <div class="item-value bargain-amount">
                        <template v-if="isInputBargain">
                          每十万扣款<span class="text-primary">{{ form.bargainLakhDeduction }}</span>元 /<span class="text-primary">{{ form.bargainAnnualInterest }}%</span>
                        </template>
                        <template v-else>
                          -
                        </template>
                      </div>
                    </div>
                    <div class="item item-column-2">
                      <div class="item-label">
                        议价后实付金额
                      </div>
                      <div class="item-value">
                        <span v-if="isBargainInput" class="text-primary bold">{{ bargainPayAmountAll }} 万元</span>
                        <span v-else>-</span>
                      </div>
                    </div>
                  </li>
                  <li class="row ">
                    <!--
                      <div class="item item-column-2 voucher-box">
                      <div class="item-label">
                      <span>交易凭证</span>
                      <el-tooltip
                      placement="top-start"
                      popper-class="receive-draft-tooltip"
                      >
                      <template slot="content">
                      开启交易凭证开关，表示您需要该笔订单的交易凭证（服务费每份 <span class="tooltip-color">5</span> 元），凭证服务费将在交易完成时扣除。
                      您可以在支付电子账户内查看费用明细及下载交易凭证。交易凭证内容可点击查看右侧“示例”。
                      </template>
                      <icon class="icon icon-question" type="chengjie-wenti" />
                      </el-tooltip>
                      </div>
                      <div class="item-value">
                      <el-switch
                      v-model="form.tradeCertificate"
                      :width="60"
                      @change="updateData"
                      />
                      <span v-if="form.tradeCertificate" class="service-amount">
                      <span>凭证服务费：</span>
                      <span class="tooltip-color">5</span>
                      <span> 元</span>
                      </span>
                      <el-image :preview-src-list="[voucherImage]" img-text="示例" class="example" />
                      </div>
                      </div>
                    -->
                    <div class="item item-column-2">
                      <div class="item-label">
                        服务费
                        <ServiceFeeTooltip />
                      </div>
                      <div class="item-value">
                        {{ platformServiceFee }}{{ sdmUnit }}
                        <!-- <span v-if="configDefault.showServiceFeeDiscountFlag() && (form.payChannel === 5 || form.payChannel === 7)" class="g-warning-text">(限时活动)</span> -->
                      </div>
                    </div>
                  </li>
                </ul>
                <!-- 付费方式 -->
                <PayWays
                  v-if="isCouponPay"
                  ref="bargainPayWaysRef"
                  :order-no="draftInfo.orderNo"
                  :point-service-fee="platformServiceFee"
                  @change="onPayWaysChange"
                />
              </template>
            </Card>
          </div>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="mr-s" @click="handleClose">取消</el-button>
        <el-tooltip content="已选签收账户不支持新一代票据" :disabled="!disabledSubmitBtn" placement="top">
          <div class="line-block">
            <el-button
              v-waiting="['post::loading::/draft/order/validateQuotedPriceDiff',
                          'post::loading::/draft/bargain/postBargain']"
              type="primary"
              :disabled="disabledSubmitBtn || fastTradeOrderRule()"
              @click="handelConfirm"
            >
              确定
            </el-button>
          </div>
        </el-tooltip>
      </div>
    </el-dialog>

    <!-- 实名认证弹窗 -->
    <!-- <VerifiedDialog v-if="isShowVerifiedDialog" ref="verifiedDialogRef" /> -->
  </div>
</template>

<script>
/* eslint-disable no-magic-numbers */
import { mapGetters, mapActions } from 'vuex'
import WarnContent from '@/views/components/common/warn-content.vue' // 警告文本
import Card from '@/views/pages/draft-detail/components/card'
import BargainDickerCard from '../bargain-dicker-card/bargain-dicker-card.vue'
import PayChannelRadio from '@/views/pages/market/components/receive-order-detail/components/pay-channel-radio.vue'
import AccountListSelect from '@/views/pages/market/components/receive-order-detail/components/account-list-select.vue'
import PayWays from '@/views/pages/market/components/pay-ways/pay-ways.vue'
import ServiceFeeTooltip from '@/views/pages/market/components/service-fee-tooltip.vue'
import BigNumber from 'bignumber.js'
import { yuan2wan, wan2yuan } from '@/common/js/number'
import bargainApi from '@/apis/bargain'
// import VerifiedDialog from '@/views/components/common/verified-dialog/verified-dialog.vue'// 实名认证弹窗
import { MQTT_BARGAIN, RECEIVE_ORDER_DETAIL, OPEN_BANK_LOGIN_AUTH_DIALOG, OPEN_BANK_LOGIN_AUTH_SUCCESS } from '@/event/modules/site'
import { ISSUE_DRAFT_ERROR_CODE } from '@/constants/draft'
import payWaysMixins from '@/views/pages/market/components/pay-ways/pay-ways-mixins'
import { limitFastTradeOrderTaking, enjambmentReceivingOrderTips } from '@/utils/limit-order-taking.js' // E+是否限制接单工具函数
import { PAYMENT_CHANNEL, PAYMENT_CHANNEL_VALUE_MAP, E_PLUS_LOGIN_AUTH_KEY } from '@/constant'

import {
  isNull, // 判断是否为Null
} from '@/common/js/util'
const DEFAULT_TITLE = '议价申请'

export default {
  name: 'bargain-apply-dialog',
  components: {
    WarnContent,
    Card,
    BargainDickerCard, // 议价-还价 卡片
    PayChannelRadio, // 支付渠道选择
    AccountListSelect, // 签收账户选择
    ServiceFeeTooltip,
    PayWays
    // VerifiedDialog, // 实名认证弹窗
  },
  mixins: [payWaysMixins],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    draftInfo: {
      type: Object,
      default: () => ({})
    },
    selectData: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      limitFastTradeOrderTaking,
      yuan2wan,
      form: {
        serviceCharge: null, // 每十万手续费
        lakhDeduction: null, // 每十万扣款
        accountSelect: null, // 选择的签收账户
        tradeCertificate: null, // 是否需要凭证
        payChannel: null, // 已选的支付渠道
        bargainLimitTime: null, // 有效期
        bargainLakhDeduction: null, // 议价后的每十万扣款款
        bargainAnnualInterest: null // 议价后的年利率
      },
      voucherImage: 'https://oss.chengjie.red/web/imgs/draft/voucher.png', // 交易凭证示例
      dialogVisible: false, // 弹窗显示
      bargainPayAmount: null, // 议价后的实付金额
      bargainAmountInput: null, // 输入的议价
      annualInterestInput: null, // 输入的年化
      serviceCharge: null, // 输入的每十万手续费
      isShowVerifiedDialog: false, // 是否显示实名失效弹窗
      title: DEFAULT_TITLE,
      isOpenMargin: false, // 是否开启保证金
      currentBankAccount: null, // 当前选中的签收银行账户
      platformServiceFee: null, // 服务费
    }
  },
  computed: {
    ...mapGetters('user', {
      sdmInfo: 'sdmInfo', // 米账号信息
    }),
    ...mapGetters('common', {
      startCrawlerScheme: 'startCrawlerScheme', // E++是否开启爬虫方案 1开启 0关闭
    }),
    limitOrderInfo() { // E+限制接单参数
      return this.$store.state.common.limitOrderInfo
    },

    // 是否有输入议价金额
    isInputBargain() {
      const { bargainAmountInput, annualInterestInput } = this
      return !isNull(bargainAmountInput) || !isNull(annualInterestInput)
    },

    // 议价后的报价-实付金额
    bargainPayAmountAll() {
      let res = null
      const { tradeCertificate } = this.form // 是否需要凭证
      const tradeCertificateFee = 5 // 交易凭证费用
      const { bargainPayAmount } = this // 议价后实付金额（单位：万元）

      res = yuan2wan(
        new BigNumber(bargainPayAmount || 0).multipliedBy(1e4)
          .plus(tradeCertificate ? tradeCertificateFee : 0),
        { digits: 6, parseNumber: false, mode: 'round' }
      ).toFixed(6)
      return res
    },

    // 议价金额是否大于0
    isBargainInput() {
      let res = +this.bargainAmountInput > 0 || +this.annualInterestInput > 0
      return res
    },
    // 确认按钮-新票接单时签收银行不支持新票禁用
    disabledSubmitBtn() {
      return (!!this.draftInfo.draftType && this.currentBankAccount && this.currentBankAccount.newDraftFlag === 0)
    },

  },
  watch: {
    // 接单详情带过来的数据
    selectData: {
      handler(val) {
        if (val) {
          this.setSelectedData(val)
        }
      },
      deep: true,
    },
    draftInfo: {
      handler(val) {
        if (val) {
          this.platformServiceFee = val.platformServiceFee
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    ...mapActions('user', {
      getSdmInfo: 'getSdmInfo', // 米账号信息
    }),
    // 简易版网银登录成功回调 key=>参照枚举类型 E_PLUS_LOGIN_AUTH_KEY
    handleAuthSuccess(obj) {
      if (obj?.key === E_PLUS_LOGIN_AUTH_KEY.BARGAIN_APPLY) { // 议价标识触发
        this.postBargainApply()
      }
    },

    // 初始化
    init() {
      this.dialogVisible = true
      this.isShowVerifiedDialog = false
      this.$nextTick().then(() => {
        this.$refs.payChannelRadio.init() // 支付渠道选择
        this.$refs.accountListSelect.init(this.form.payChannel) // 签收账户选择
        this.$refs.bargainPayWaysRef && this.$refs.bargainPayWaysRef.init() // 付费方式选择
      })
      // E++开启爬虫方案时,登录校验成功
      this.$event.on(OPEN_BANK_LOGIN_AUTH_SUCCESS, this.handleAuthSuccess)
    },

    // 设置接口详情带入的数据
    setSelectedData(val) {
      const { accountSelect, tradeCertificate, payChannel } = val
      if (!accountSelect && !payChannel) return

      this.form.accountSelect = accountSelect // 选择的签收账户列表
      this.form.tradeCertificate = tradeCertificate // 是否需要凭证
      this.form.payChannel = payChannel // 已选的支付渠道
    },

    // 关闭弹窗
    handleClose() {
      this.clearData()
      this.$event.off(OPEN_BANK_LOGIN_AUTH_SUCCESS, this.handleAuthSuccess)
      this.isShowVerifiedDialog = false
      this.dialogVisible = false
    },

    // 清除输入的数据
    clearData() {
      this.isShowVerifiedDialog = false
      this.$nextTick().then(() => {
        if (this.$refs.bargainDickerCard) {
          this.$refs.bargainDickerCard.clearData()
        }
      })
    },

    // 选择支付渠道
    onChangePayChannel(val) {
      // console.log('选择支付渠道 :>> ', val)
      const { payChannel, currentAccount } = val
      this.form.payChannel = payChannel
      this.currentAccount = currentAccount // 当前电子账账户
      this.form.accountSelect = null // 清除选择的背书账号-使用用户设置的支付渠道对应的默认签收账户

      // 回传接单详情
      this.updateData()
    },

    // 选择签收账户列表
    onChangeAccount(val) {
      // console.log('选择签收账户列表 :>> ', val)
      const { accountSelect, currentBankAccount } = val
      this.form.accountSelect = accountSelect
      this.currentBankAccount = currentBankAccount
      // 回传接单详情
      this.updateData()
    },

    // 回传接单详情
    updateData() {
      // console.log('updateData :>> ', this.form)
      this.$emit('change', {
        accountSelect: this.form.accountSelect,
        tradeCertificate: Boolean(this.form.tradeCertificate),
        payChannel: this.form.payChannel,
        currentAccount: this.currentAccount,
        currentBankAccount: this.currentBankAccount
      })
    },

    // 确认申请议价
    handelConfirm() {
      const {
        // isOpenMargin,
        sdmInfo, // 米账号
        draftInfo, // 票据详情信息
        platformServiceFee, // 服务费
      } = this

      const isOpenMargin = draftInfo.margin === 1 // 是否开启保证金(带保订单)

      if (!this.form.payChannel) {
        this.$message.warning('请选择支付渠道')
        return
      }
      // E+/邦+/E++是否已经签约
      if ([PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id, PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id, PAYMENT_CHANNEL.YL_PLUS.id].includes(this.form.payChannel)) {
        const { traderAccounts } = draftInfo
        const channels = traderAccounts.filter(e => e.paymentChannel === this.form.payChannel)
        if (channels.length && channels[0].signStatus !== 2) return this.$message.warning(`请先完成${PAYMENT_CHANNEL_VALUE_MAP[this.form.payChannel]}渠道的签约！`)
      }
      if (!this.form.accountSelect) {
        this.$message.warning('请选择签收账户')
        return
      }

      // 校验子表单
      let validateResult = true
      this.$nextTick().then(async() => {
        validateResult = await this.$refs.bargainDickerCard.validateForm()
        if (!validateResult) return // 校验有误

        await this.getSdmInfo() // 获取米
        // 计算保证金和服务费需要的总额米
        let sdmAmount = 0
        // 用券付费后需要支付的云豆
        const { currencyAmount = 0, strategyType } = this.currentPayWay
        if (this.isCouponPay) {
          sdmAmount = isOpenMargin ? new BigNumber(draftInfo.marginAmount || 0) : new BigNumber(0)
          if (strategyType !== 1) {
            sdmAmount = sdmAmount.plus(currencyAmount || 0)
          }
        } else {
          // 未用券要支付的云豆
          sdmAmount = isOpenMargin ? new BigNumber(draftInfo.marginAmount || 0).plus(platformServiceFee) : platformServiceFee
        }

        if (sdmInfo?.balanceAmt < sdmAmount) {
          this.$message.error(`${this.sdmName}不足，请先充值再发起议价`)
          return
        }
        // 跨行回款户的接单提示
        const limitOrders = JSON.parse(JSON.stringify(this.limitOrderInfo))
        await enjambmentReceivingOrderTips(Object.assign({}, limitOrders, {
          paymentChannel: this.form.payChannel,
          fastTrade: this.draftInfo.fastTrade,
          generalAccount: this.draftInfo.generalAccount,
          generalAccountZb: this.draftInfo.generalAccountZb,
          draftAmountWan: this.bargainPayAmount
        }), this)
        // E++开启爬虫方案时,需要打开网银登录授权弹窗
        if (this.startCrawlerScheme && this.form.payChannel === PAYMENT_CHANNEL.YL_PLUS.id) {
          this.$event.emit(OPEN_BANK_LOGIN_AUTH_DIALOG, { key: E_PLUS_LOGIN_AUTH_KEY.BARGAIN_APPLY })
          return
        }
        // 发起申请议价
        this.postBargainApply()
      })
    },

    // 申请议价
    async postBargainApply() {
      const { orderNo, lakhFee, annualInterest, margin, splitFlag, splitType } = this.draftInfo
      const { payChannel, accountSelect, bargainLimitTime, tradeCertificate, bargainLakhDeduction, bargainAnnualInterest, splitAmount } = this.form
      // if (!orderNo) return
      const params = {
        orderNo, // 订单号
        lakhFee, // 每十万扣款
        paymentChannel: payChannel, // 支付类型
        traderCorpBankCardId: accountSelect, // 背书银行卡标识
        needVoucher: tradeCertificate ? 1 : 0, // 是否需要交易凭证，0-不需要，1需要
        limitTime: bargainLimitTime, // 议价有效期，11--5分钟12--10分钟
        bidLakhFee: bargainLakhDeduction, // 出价每十万扣款
        annualInterest, // 年利率
        bidAnnualInterest: bargainAnnualInterest, // 出价年利率
        margin, // 是否带保
        splitAmount: splitFlag && splitType === 0 ? splitAmount : null // 可拆分&&按可拆分金额拆分 传入拆分议价金额
      }
      // 优惠券开启模式-添加付费方式参数
      this.handleParams(params)

      try {
        await bargainApi.postBargain(params)
        this.$message.success('发起议价成功')
        this.$event.emit(MQTT_BARGAIN) // 刷新议价消息列表
        this.$emit('success', true)
        this.dialogVisible = false
        this.clearData() // 清除选择数据
      } catch (error) {
        // 对接口返回的错误code处理
        this.handleErrorCode(error)
      }
    },

    // 议价后的金额
    onChangeBargain(val) {
      // console.log('议价后的金额 :>> ', val)
      const {
        bargainPayAmount, // 议价后的实付金额
        bargainLakhDeduction, // 议价后的每十万扣款款
        bargainLimitTime, // 议价有效期
        bargainAnnualInterest, // 议价后年利率
        bargainAmountInput, // 输入的议价
        annualInterestInput, // 输入的年化
        serviceCharge, // 输入的每十万手续费
        splitAmount // 输入的拆分金额
      } = val

      this.bargainPayAmount = bargainPayAmount
      this.form.bargainLakhDeduction = bargainLakhDeduction
      this.form.bargainLimitTime = bargainLimitTime
      this.form.bargainAnnualInterest = bargainAnnualInterest
      this.bargainAmountInput = bargainAmountInput
      this.annualInterestInput = annualInterestInput
      this.serviceCharge = serviceCharge
      this.form.splitAmount = wan2yuan(splitAmount)
    },

    // 申请议价接口错误处理
    handleErrorCode(error) {
      // console.log('申请议价接口错误处理error :>> ', error)

      // 价格已更改
      const PRICE_CHANGE_ERROR_CODE = 3015 // 价格改变code
      if (error?.data?.code === PRICE_CHANGE_ERROR_CODE) {
        let price = error?.data?.msg
        this.handleChangePrice(price)
        return
      }

      if (error.data.code === ISSUE_DRAFT_ERROR_CODE.RECEIVE_MAX_LIMIT_REACHED) {
        this.$confirm('您的接单量已达到限额，如需调整额度，请联系客户经理', '提示', {
          type: 'warning',
          iconPosition: 'title',
          showClose: false,
          showCancelButton: false,
          confirmButtonText: '我知道了'
        })
        return
      }

      // 实名失效弹窗
      if (error?.data?.code === ISSUE_DRAFT_ERROR_CODE.REAL_NAME_EXPIRED) {
        this.$message.closeAll()

        this.isShowVerifiedDialog = true
        // this.$nextTick().then(() => {
        //   this.$refs.verifiedDialogRef && this.$refs.verifiedDialogRef.init()
        // })
      }
    },

    // 价格变更
    handleChangePrice(price = '') {
      this.$message.closeAll()
      let text = `<p>当前价格已更改至每十万扣款：<span class="text-primary">${price || '-'}</span> 元。</p><p>请重新填写议价。</p>`

      this.$alert(text, '价格已更改', {
        confirmButtonText: '我知道了',
        type: 'warning',
        dangerouslyUseHTMLString: true,
      }).then(() => {
        // 通知打开议价的接单弹窗
        this.$event.emit(RECEIVE_ORDER_DETAIL, {
          orderNo: this.draftInfo.orderNo || '',
          isShowBargainDialog: true // 是否打开议价弹窗
        })

        this.clearData()
        this.dialogVisible = false
        this.$emit('close-receive-order', true) // 关闭服务大厅的接单弹窗，打开议价里的接单弹窗
      })
    },
    // 光速E+订单批量接单限制规则判断
    fastTradeOrderRule() {
      const limitOrders = JSON.parse(JSON.stringify(this.limitOrderInfo))
      return limitFastTradeOrderTaking(Object.assign({}, limitOrders, { paymentChannel: this.form.payChannel, fastTrade: this.draftInfo.fastTrade, generalAccount: this.draftInfo.generalAccount, generalAccountZb: this.draftInfo.generalAccountZb, draftAmountWan: this.bargainPayAmount }))
    },
    // 更新服务费
    onUpdateOrderFee(val) {
      const { platformServiceFee, splitAmount } = val.data
      this.platformServiceFee = platformServiceFee
      this.$refs.bargainPayWaysRef && this.$refs.bargainPayWaysRef.splitPayWays(splitAmount || this.draftInfo.draftAmount)
    }
  },
}
</script>
