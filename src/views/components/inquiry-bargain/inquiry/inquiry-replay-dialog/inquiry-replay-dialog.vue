<style lang="scss" scoped>
.inquiry-msg {
  position: relative;
  padding: 12px 16px;
  background: $color-FFFFFF;

  .inquiry-msg-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    .title {
      max-width: 480px;
      font-weight: 600;
    }

    .over {
      position: absolute;
      top: 10px;
      right: 0;
      border-radius: 11px 0 0 11px;
      width: 61px;
      height: 23px;
      font-size: 12px;
      text-align: center;
      color: $color-text-regular;
      background: rgb(144 144 144 / 20%);
      line-height: 23px;
    }

    .buy-btn {
      margin-left: 8px;
      white-space: nowrap;
    }
  }

  .inquiry-msg-con {
    span {
      margin: 0 4px;
      color: #DDE0E9;
    }
  }
}

.inquiry-con {
  font-size: 14px;

  .inquiry-box {
    margin-top: 12px;
    padding: 12px 16px;
    background: $color-FFFFFF;

    .inquiry-con-flex {
      .title {
        margin-bottom: 12px;
        font-weight: 500;
      }

      .flex {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .flex-key {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: -12px;

        .flex-btn {
          display: flex;
          align-items: center;
          margin-right: 8px;
          margin-bottom: 12px;
          border-radius: 2px;
          padding: 2px 8px;
          background: $--color-primary-hover;

          &.wrong {
            background: $color-warning-sub;
          }
        }

        span {
          color: $color-warning;
        }

        .no {
          line-height: 24px;
        }

        .icon {
          margin-left: 4px;
          width: 12px;
          height: 12px;
        }
      }

      .piaoju {
        overflow-y: auto;
        border: 1px solid $color-D9D9D9;
        width: 100%;
        height: 200px;
      }
    }

    .history {
      margin-left: 10px;
      border-bottom: 1px solid $font-color;
      min-width: 57px;
      color: $font-color;
      cursor: pointer;
    }
  }
}

.flex1 {
  flex: 1;
}

.mb {
  margin-bottom: 0 !important;
}

.mt12 {
  margin-top: 12px;
}

.pointer {
  cursor: pointer;
  font-weight: 400;
}

::v-deep {
  .el-dialog__body {
    overflow-y: auto;
    height: 650px;
  }

  .el-button--mini {
    padding: 6px 12px;
    font-size: 14px;
  }
}

.warning-icon {
  font-size: 14px;
  color: $--color-warning;
}

.img-warp {
  overflow: hidden;
  overflow-y: auto;
  height: 200px;

  .img {
    width: 100%;
    height: 500px;
  }
}
</style>

<template>
  <el-dialog
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose"
    :close-on-click-modal="false"
    append-to-body
    center
    :lock-scroll="true"
  >
    <div slot="title" class="pointer" @click="handleClose">
      <icon v-if="!nowData.title" class="icon" type="chengjie-left" />
      {{ nowData.title ? nowData.title : '返回' }}
    </div>
    <!-- 小贴士在票方上方显示 -->
    <InquiryText v-if="INQUIRY_TAB_LIST.SALE.id === inquiryType" tips-type="title" />
    <!-- 信息 -->
    <div class="inquiry-msg">
      <div class="inquiry-msg-flex">
        <div class="title">{{ nowData.acceptorName }}</div>
        <!-- 票方--查看详情 -->
        <div v-if="INQUIRY_TAB_LIST.SALE.id === inquiryType">
          <el-button
            border
            size="mini"
            type="primary"
            @click="openOrder"
          >
            查看详情
          </el-button>
        </div>
        <!-- 资方--询单中 -->
        <div v-if="INQUIRY_TAB_LIST.BUY.id === inquiryType && nowData.consultStatus === 0" class="buy-btn">
          <el-button
            size="mini"
            border
            type="primary"
            @click="overOrder"
          >
            结束询单
          </el-button>
          <el-button
            size="mini"
            type="primary"
            @click="takeOrder"
          >
            接单
          </el-button>
        </div>
        <!-- 资方-询单结束 -->
        <div v-if="INQUIRY_TAB_LIST.BUY.id === inquiryType && nowData.consultStatus === 1" class="over">
          已失效
        </div>
      </div>
      <div class="inquiry-msg-con">
        {{ yuan2wan(nowData.draftAmount) }} 万<span>|</span>
        {{ nowData.maturityDate }} <span>|</span>
        票号后6位{{ nowData.draftNo.slice(-6) }} <span>|</span>
        每十万扣款{{ nowData.lakhFee }}元/年化{{ nowData.annualInterest }}%
      </div>
    </div>
    <!-- 资方 -->
    <div v-if="INQUIRY_TAB_LIST.BUY.id === inquiryType" class="inquiry-con">
      <div v-for="item in nowData.consultOrderDetails" :key="item.id" class="inquiry-box">
        <!-- 敏感字 -->
        <div v-if="item.consultType === INQUITY_TYPES.KEYWORD.id" class="inquiry-con-flex">
          <div class="title">咨询时间 {{ item.consultTime | filterInquiryTime }}</div>
          <div class="flex">
            <div>您咨询的问题：</div>
            <el-button
              v-if="item.replyStatus === 0 && nowData.consultStatus === 0"
              type="primary"
              size="mini"
              @click="goReplay(item)"
            >
              催回复
            </el-button>
            <InquiryReplayContent :reply-content="item.replyContent" inquiry-type="3" />
          </div>
          <div class="flex-key mt12">
            <div class="no">
              是否包含敏感字：
            </div>
            <div class="flex-key mb flex1">
              <div :class="['flex-btn', item.replyContent === '0' && 'wrong']">
                {{ item.consultContent }}  <img v-if="item.replyContent === '1'" src="https://oss.chengjie.red/web/imgs/inquiry/right.png" class="icon">
                <img v-if="item.replyContent === '0'" src="https://oss.chengjie.red/web/imgs/inquiry/wrong.png" class="icon">
              </div>
            </div>
          </div>
        </div>

        <!-- 个体户 -->
        <div v-if="item.consultType === INQUITY_TYPES.ONE.id" class="inquiry-con-flex">
          <div class="title">咨询时间 {{ item.consultTime | filterInquiryTime }}</div>
          <div class="flex">
            <div>请判断您的票据是否包含个体户？</div>
            <el-button
              v-if="item.replyStatus === 0 && nowData.consultStatus === 0"
              type="primary"
              size="mini"
              @click="goReplay(item)"
            >
              催回复
            </el-button>
            <InquiryReplayContent :reply-content="item.replyContent" inquiry-type="3" />
          </div>
        </div>

        <!-- 背书 -->
        <div v-if="item.consultType === INQUITY_TYPES.BEISHU.id" class="inquiry-con-flex">
          <div class="title">咨询时间 {{ item.consultTime | filterInquiryTime }}</div>
          <div class="flex">
            <div>您咨询的问题：</div>
            <el-button
              v-if="item.replyStatus === 0 && nowData.consultStatus === 0"
              type="primary"
              size="mini"
              @click="goReplay(item)"
            >
              催回复
            </el-button>
            <InquiryReplayContent :reply-content="item.replyContent" />
          </div>
          <div class="mt12 flex-key">
            <div v-if="item.grandTotal" class="flex-btn">累计不超过 <span>{{ item.grandTotal }}</span> 手</div>
            <div v-if="item.nearDay || item.nearDailyTotal" class="flex-btn">近<span>{{ item.nearDay }}</span>日，每天不超过<span>{{ item.nearDailyTotal }}</span>手</div>
            <div v-if="item.ticketingDailyTotal" class="flex-btn">自开票日起，每天不超过<span>{{ item.ticketingDailyTotal }}</span>手</div>
            <div v-if="item.todayTotal" class="flex-btn">今日不超过<span>{{ item.todayTotal }}</span>手</div>
          </div>
        </div>

        <!-- 票面 -->
        <div v-if="item.consultType === INQUITY_TYPES.IMG.id" class="inquiry-con-flex">
          <div class="title">咨询时间 {{ item.consultTime | filterInquiryTime }}</div>
          <div class="flex">
            <div>您想要的票面：</div>
            <el-button
              v-if="item.replyStatus === 0 && nowData.consultStatus === 0"
              type="primary"
              size="mini"
              @click="goReplay(item)"
            >
              催回复
            </el-button>
          </div>
          <div v-if="item.autoReply" class="mt12" @click="openImg(item.replyContent)">
            <MarketDraftImage
              ref="marketDraftImage"
              class="piaoju"
              :is-small-window="false"
              :data="item.replyContent"
              @mounted="toImage"
            />
          </div>
          <div v-if="!item.autoReply && (item.replyContent && Object.keys(item.replyContent).length)" class="img-warp">
            <el-image
              class="img"
              :src="item.replyContent.imgUrl"
              :preview-src-list="[item.replyContent.imgUrl]"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 票方 -->
    <div v-if="INQUIRY_TAB_LIST.SALE.id === inquiryType" class="inquiry-con">
      <div v-for="item in nowData.sellerConsultOrderDetailsList[currentIndex].consultOrderDetails" :key="item.id" class="inquiry-box">
        <!-- 敏感字 -->
        <div v-if="item.consultType === INQUITY_TYPES.KEYWORD.id" class="inquiry-con-flex">
          <div class="title">咨询时间 {{ item.consultTime | filterInquiryTime }} <span style="margin-left: 10px;">(对方所在地：{{ nowData.provinceName }}{{ nowData.cityName }})</span></div>
          <div class="flex">
            <div>您咨询的问题：</div>
            <div v-if="item.replyStatus === 0 && nowData.consultStatus === 0">
              <el-button
                size="mini"
                @click="goAnswer(item, false)"
              >
                包含
              </el-button>
              <el-button
                type="primary"
                size="mini"
                @click="goAnswer(item, true)"
              >
                不包含
              </el-button>
            </div>
            <InquiryReplayContent
              :reply-content="item.replyContent"
              inquiry-type="3"
              :is-sale="inquiryType"
              :can-update="item.canUpdate"
              :consult-status="nowData.consultStatus"
              :item-id="String(item.id)"
              @update-reply="updateReply"
            />
          </div>
          <div class="flex-key mt12">
            <div class="no">
              是否包含敏感字：
            </div>
            <div class="flex-key mb flex1">
              <div class="flex-btn">
                {{ item.consultContent }}  <img v-if="item.replyContent === '1'" src="https://oss.chengjie.red/web/imgs/inquiry/right.png" class="icon">
                <icon v-if="item.replyContent === '0'" src="https://oss.chengjie.red/web/imgs/inquiry/wrong.png" class="icon" />
              </div>
            </div>
          </div>
          <InquiryText />
        </div>

        <!-- 个体户 -->
        <div v-if="item.consultType === INQUITY_TYPES.ONE.id" class="inquiry-con-flex">
          <div class="title">咨询时间 {{ item.consultTime | filterInquiryTime }}<span style="margin-left: 10px;">(对方所在地：{{ nowData.provinceName }}{{ nowData.cityName }})</span></div>
          <div class="flex">
            <div>请判断您的票据是否包含个体户？</div>
            <div v-if="item.replyStatus === 0 && nowData.consultStatus === 0">
              <el-button
                size="mini"
                @click="goAnswer(item, false)"
              >
                不包含
              </el-button>
              <el-button
                type="primary"
                size="mini"
                @click="goAnswer(item, true)"
              >
                包含
              </el-button>
            </div>
            <InquiryReplayContent
              :reply-content="item.replyContent"
              inquiry-type="3"
              :is-sale="inquiryType"
              :consult-status="nowData.consultStatus"
              :can-update="item.canUpdate"
              :item-id="String(item.id)"
              @update-reply="updateReply"
            />
          </div>
          <InquiryText />
        </div>

        <!-- 背书 -->
        <div v-if="item.consultType === INQUITY_TYPES.BEISHU.id" class="inquiry-con-flex">
          <div v-if="nowData.endorseDateDefect" class="warning-icon">
            <i :class="['warning-icon', 'elicon', 'elicon-info-circle', 'info-circle-icon']" /> 此票面在网银中背书日期有缺失，请您手动回复是否符合
          </div>
          <div class="title flex">
            <div>咨询时间 {{ item.consultTime | filterInquiryTime }}<span style="margin-left: 10px;">(对方所在地：{{ nowData.provinceName }}{{ nowData.cityName }})</span></div>
            <div class="history" @click="goHistory">历史回复</div>
          </div>
          <div class="flex">
            <div>您咨询的问题：</div>
            <div v-if="item.replyStatus === 0 && nowData.consultStatus === 0">
              <el-button
                size="mini"
                @click="goAnswer(item, false)"
              >
                不符合
              </el-button>
              <el-button
                type="primary"
                size="mini"
                @click="goAnswer(item, true)"
              >
                符合
              </el-button>
            </div>
            <InquiryReplayContent
              :reply-content="item.replyContent"
              :is-sale="inquiryType"
              :is-replied="isReplied"
              :consult-status="nowData.consultStatus"
              :can-update="item.canUpdate"
              :item-id="String(item.id)"
              @update-reply="updateReply"
            />
          </div>
          <div class="mt12 flex-key">
            <div v-if="item.grandTotal" class="flex-btn">累计不超过 <span>{{ item.grandTotal }}</span> 手</div>
            <div v-if="item.nearDay || item.nearDailyTotal " class="flex-btn">近<span>{{ item.nearDay }}</span>日，每天不超过<span>{{ item.nearDailyTotal }}</span>手</div>
            <div v-if="item.ticketingDailyTotal" class="flex-btn">自开票日起，每天不超过<span>{{ item.ticketingDailyTotal }}</span>手</div>
            <div v-if="item.todayTotal" class="flex-btn">今日不超过<span>{{ item.todayTotal }}</span>手</div>
          </div>
          <InquiryText />
        </div>

        <!-- 票面 -->
        <div v-if="item.consultType === INQUITY_TYPES.IMG.id" class="inquiry-con-flex">
          <div class="title">咨询时间 {{ item.consultTime | filterInquiryTime }}<span style="margin-left: 10px;">(对方所在地：{{ nowData.provinceName }}{{ nowData.cityName }})</span></div>
          <div class="flex">
            <div>您想要的票面：</div>
            <div v-if="item.replyStatus === 0 && nowData.consultStatus === 0">
              <el-upload
                action=""
                :limit="1"
                accept=".jpg,.png"
                :before-upload="beforeUpload"
                :http-request="({ file, onSuccess}) => httpRequest({ file, onSuccess}, item) "
                :show-file-list="false"
              >
                <el-button size="small" type="primary">上传</el-button>
              </el-upload>
            </div>
            <InquiryReplayContent
              :reply-content="(item.replyContent && Object.keys(item.replyContent).length) ? item.replyContent.imgUrl : ''"
              :is-sale="inquiryType"
              :is-replied="isReplied"
              :consult-status="nowData.consultStatus"
              :can-update="item.canUpdate"
              :consult-type="item.consultType"
              :item-id="String(item.id)"
              @update-reply="updateReply"
            />
          </div>
          <div v-if="item.autoReply" class="mt12" @click="openImg(item.replyContent)">
            <MarketDraftImage
              ref="marketDraftImage"
              class="piaoju"
              :is-small-window="false"
              :data="item.replyContent"
              @mounted="toImage"
            />
          </div>
          <div v-if="!item.autoReply && (item.replyContent && Object.keys(item.replyContent).length)" class="img-warp">
            <el-image
              class="img"
              :src="item.replyContent.imgUrl"
              :preview-src-list="[item.replyContent.imgUrl]"
            />
          </div>
          <InquiryText :tips-text="'请上传完整票面，如果上传的票面包含其他无关信息，软件有权给与处罚。'" />
        </div>
      </div>
    </div>

    <!-- 接单-票据详情 -->
    <ReceiveOrderDetail v-if="isShowReceiveOrderDetail" ref="receiveOrderDetail" @success="refreshList" />

    <!-- 议价申请弹窗 -->
    <BargainApplyDialog
      v-if="isShowBargainApplyDialog"
      ref="bargainApplyDialogRef"
      :draft-info="draftInfo"
      @success="refreshList"
    />
    <!-- 询单--详情 -->
    <InquiryReplayDialog
      v-if="showInquiryReplayDialog"
      :card-data="nowData"

      @success="refreshList"
    />
    <MarketDraftImageGenerator v-if="draftJson" ref="marketDraftImageGenerate" :data="draftJson" />
    <!-- 放大图片 -->
    <ImgViewer
      v-model="showViewer"
      :url-list="[frontUrl]"
      :initial-index="0"
      :show-mini-image="false"
      :prev-btn="true"
      :next-btn="true"
      :copy-btn="false"
      :front-or-back-btn="false"
    />
    <!-- 历史回复 -->
    <InquiryHistory v-if="showHistory" :id="Number(id)" @close="showHistory = false" />
  </el-dialog>
</template>

<script>
import { yuan2wan } from '@/common/js/number'
import html2canvas from 'html2canvas'
import inquiryApi from '@/apis/inquiry'
import { INQUIRY_TAB_LIST, INQUITY_TYPES, INQUIRYREFRESH } from '@/constants/inquiry-bargain'
import ReceiveOrderDetail from '@/views/pages/market/components/receive-order-detail/receive-order-detail.vue' // 接单详情组件
import InquiryReplayDialog from '@/views/components/inquiry-bargain/inquiry/inquiry-replay-dialog/inquiry-replay-dialog.vue' // 询单--详情-组件
import BargainApplyDialog from '@/views/components/inquiry-bargain/bargain/bargain-apply-dialog/bargain-apply-dialog.vue' // 议价申请弹窗
import inquiry from '@/views/components/inquiry-bargain/inquiry/inuqiry-card/inquiry'
// 票据正面截图生成
import MarketDraftImage from '@/views/components/market-draft-image/market-draft-image.vue'
import InquiryText from './inquiry-text.vue'
import MarketDraftImageGenerator from '@/views/components/market-draft-image-generator/market-draft-image-generator.vue'
import ImgViewer from '@/views/components/img-viewer/img-viewer.vue'
import InquiryHistory from '@/views/components/inquiry-bargain/inquiry/inquiry-replay-dialog/inquiry-history.vue'
import InquiryReplayContent from '@/views/components/inquiry-bargain/inquiry/inquiry-replay-dialog/inquiry-reply-content.vue'
import { upload } from '@/utils/oss'
import {
  OSS_DIR
} from '@recognize/constant'
export default {
  name: 'inquiry-replay-dialog',
  components: {
    ReceiveOrderDetail,
    InquiryReplayDialog,
    BargainApplyDialog,
    MarketDraftImage,
    InquiryText,
    MarketDraftImageGenerator,
    ImgViewer,
    InquiryHistory,
    InquiryReplayContent,
  },
  mixins: [inquiry],
  props: {
    cardData: { // 当条数据
      type: Object,
      default: () => ({})
    },
    inquiryType: { // 票方/资方
      type: String,
      default: ''
    },
    currentIndex: { // 当条票方询单的资金方的索引值
      type: Number,
      default: 0
    },
  },
  data() {
    return {
      OSS_DIR,
      dialogVisible: true,
      INQUIRY_TAB_LIST,
      INQUITY_TYPES,
      showViewer: false, // 展示图片组件
      frontUrl: null, // 正面图
      draftJson: null, // 正面json
      showHistory: false,
      id: null,
      nowData: this.cardData,
      yuan2wan,
      isReplied: 0 // 是否已回复 0-否，1-是
    }
  },
  mounted() {
    // mqtt刷新列表
    this.$event.on(INQUIRYREFRESH, data => {
      const { id } = data
      if (`${this.nowData.id}` === `${id}`) {
        this.nowData = data
      }
    })
  },
  methods: {
    openImg(draftJson) {
      this.draftJson = draftJson
      setTimeout(async() => {
        const frontCanvas = await this.$refs.marketDraftImageGenerate.screenshot('front', 'canvas')
        this.frontUrl = await frontCanvas.toDataURL()
        this.showViewer = true
      }, 0)
    },
    toImage() {
      html2canvas(document.querySelector('.market-draft-image')).then(canvas => {
        let url = canvas.toDataURL('image/png')
        this.srcList = [url]
      })
    },
    // 历史回复
    goHistory() {
      this.id = this.nowData.billOrderId
      this.showHistory = true
    },
    // 催回复
    async goReplay({ id }) {
      let data = await inquiryApi.consultUrgeToReply(id)
      this.$message.success(data)
      this.$emit('success', true)
    },
    // 关闭弹窗
    handleClose() {
      this.clearData()
      this.$emit('success')
    },
    // 回复-符合/不符合
    async goAnswer({ id }, answer) {
      if (typeof (answer) === 'boolean') {
        answer = answer ? 1 : 0
      }
      let data = ''
      if (this.isReplied) { // 是否为回复后修改回复
        data = await inquiryApi.inquiryReplyUpdate({ billConsultOrderDetailId: id, replyContent: answer })
      } else {
        data = await inquiryApi.inquiryReply({ billConsultOrderDetailId: id, replyContent: answer })
      }
      this.$message.success(data)
      this.$emit('success')
    },
    // 回复后再次修改
    updateReply(id) {
      const idx = this.nowData.sellerConsultOrderDetailsList[this.currentIndex].consultOrderDetails.findIndex(item => String(item.id) === id)
      this.nowData.sellerConsultOrderDetailsList[this.currentIndex].consultOrderDetails[idx].replyStatus = 0
      this.nowData.sellerConsultOrderDetailsList[this.currentIndex].consultOrderDetails[idx].replyContent = null
      this.isReplied = 1 // 回复后再次修改
    },
    // 自定义上传
    httpRequest({ file, onSuccess }, item) {
      this.uploadFiles([file], onSuccess, item)
    },
    // 校验上传图片大小
    beforeUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$message.warning('上传票面大小不能超过10MB!')
      }
      return isLt10M
    },

    // 上传文件
    async uploadFiles(files, onSuccess, item) {
      try {
        const url = await upload(files[0], OSS_DIR.DRAFT)
        onSuccess({ url })
        try {
          typeof this.onSuccess === 'function' && await this.onSuccess(url, files[0])
          this.$message.success('上传成功')
          this.goAnswer(item, url)
        } catch (e) {
          // onSuccess 函数报错
        }
      } catch (e) {
        // eslint-disable-next-line
        console.log('上传失败')
        // this.$message.error('上传失败')
      }
    },

  },
}
</script>
