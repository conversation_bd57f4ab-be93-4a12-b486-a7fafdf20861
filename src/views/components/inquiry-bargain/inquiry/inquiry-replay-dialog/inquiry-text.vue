<style lang="scss" scoped>
  .tips {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 -16px 16px;
    padding: 12px;
    font-size: 12px;
    background: $color-FFF8F9;

    .tip-flex {
      display: flex;
      align-items: center;

      img {
        margin-right: 8px;
        width: 7px;
        height: 9px;
      }
    }

    .receive-btn {
      border-bottom: 1px solid $--color-primary;
      color: $--color-primary;
    }
  }

  .text {
    margin: 12px 0;
    color: $color-warning;
  }
</style>

<template>
  <div>
    <div v-if="tipsType !== 'title'" class="text">{{ tipsText }}</div>
    <div v-else class="tips">
      <div class="tip-flex">
        <img src="https://oss.chengjie.red/web/imgs/inquiry/down.png" alt="">
        小贴士:使用{{ discernName }}【单张识别】票面后，系统可自动回复：<span class="text-primary">背书手数、票面信息、敏感信息</span>。
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'inquiry-reply-text',
  props: {
    // 用来判断显示什么文案
    tipsType: {
      type: String,
      default: ''
    },
    tipsText: {
      type: String,
      default: '请您在有效期内如实回答，您的回答与实际不一致导致的纠纷，您将承担违约责任！'
    }
  },

}
</script>
