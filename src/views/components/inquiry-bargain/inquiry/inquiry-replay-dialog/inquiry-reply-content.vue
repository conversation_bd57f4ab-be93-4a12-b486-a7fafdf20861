<style lang="scss" scoped>
.replyContent {
  border-radius: 2px;
  padding: 4px 8px;
  background: $--color-primary-hover;

  .icon {
    margin-left: 2px;
    width: 12px;
    height: 12px;
  }

  &.wrong {
    background: $color-warning-sub;
  }
}

.update {
  cursor: pointer;
  margin-right: 15px;
  border-bottom: 1px solid;
  color: $--color-primary;
}
</style>

<template>
  <div v-if="replyContent" class="flex">
    <div v-if="isSale === INQUIRY_TAB_LIST.SALE.id && !Number(canUpdate) && !Number(consultStatus)" class="update" @click="itemUpdate">更改</div>
    <div v-if="consultType !== 0" :class="['replyContent', replyContent === '0' && 'wrong']">
      {{ types[inquiryType][replyContent] }}
      <img v-if="replyContent === '1'" src="https://oss.chengjie.red/web/imgs/inquiry/right.png" class="icon">
      <img v-if="replyContent === '0'" src="https://oss.chengjie.red/web/imgs/inquiry/wrong.png" class="icon">
    </div>
  </div>
</template>

<script>
import { INQUIRY_TAB_LIST } from '@/constants/inquiry-bargain'
const types = {
  1: {
    0: '不符合',
    1: '符合'
  },
  2: {
    0: '不包含',
    1: '包含'
  },
  // 敏感字，个体户逻辑特殊  是和其他问题的描述相反的
  3: {
    0: '包含',
    1: '不包含'
  }
}

export default {
  name: 'inquiry-reply-content',
  props: {
    replyContent: { // 回复内容
      type: String,
      default: ''
    },
    isSale: { // 是否票方
      type: String,
      default: '1'
    },
    inquiryType: { // 3=>敏感字/个体户 1=>背书 对应types数据字典
      type: String,
      default: '1'
    },
    itemId: { // 当前操作回复项的id
      type: String,
      default: ''
    },
    canUpdate: { // 是否为修改后回复 0否 1是
      type: Number,
      default: 0
    },
    consultStatus: { // 是否询单结束 0否 1是
      type: Number,
      default: 0
    },
    consultType: { // 咨询的问题类型 0=>票面
      type: Number,
    }
  },
  data() {
    return {
      types,
      INQUIRY_TAB_LIST
    }
  },
  methods: {
    itemUpdate() {
      if (this.itemId) {
        this.$emit('update-reply', String(this.itemId))
      }
    }
  }

}
</script>
