<style lang="scss" scoped>
  .body-beishu {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    border: 1px solid $color-D9D9D9;
    padding: 12px 16px;
    background: $color-FAFAFA;

    span {
      margin: 0 8px;
      color: $--color-primary;
    }

    li {
      margin-bottom: 12px;
      margin-left: 18px;
      list-style: disc;
    }

    li:last-child {
      margin-bottom: 0;
    }

    @mixin replyContent($color, $back) {
      border: 1px solid $color;
      border-radius: 2px;
      padding: 2px 12px;
      font-size: 14px;
      font-weight: 500;
      color: $color;
      background: $back;
    }

    .replyContent0 {
      @include replyContent($--color-primary, #FFE3E3);
    }

    .replyContent1 {
      @include replyContent(#2FC36B, #D5F5E2);
    }
  }

  ::v-deep {
    .el-empty {
      margin-top: 28%;

      .el-empty__image {
        width: auto;
      }

      .el-empty__description {
        margin-top: -20px;
      }

      .el-empty__description p {
        font-size: 18px;
        color: $color-text-secondary;
      }
    }

    .empty-box {
      .empty-img {
        font-size: 14em;
        color: #FFFFFF;
      }
    }
  }
</style>

<template>
  <el-dialog
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose"
    title="历史回复"
    :close-on-click-modal="false"
    append-to-body
    center
    :lock-scroll="true"
  >
    <div v-for="(item, index) in list" :key="index" class="body-beishu">
      <ul>
        <li v-if="item.billConsultOrderEndorse.grandTotal">
          累计不超过 <span>{{ item.billConsultOrderEndorse.grandTotal }}</span> 手(不含收款人)
        </li>
        <li v-if="item.billConsultOrderEndorse.nearDay || item.billConsultOrderEndorse.nearDailyTotal">
          近<span>{{ item.billConsultOrderEndorse.nearDay }}</span>日，每天不超过<span>{{ item.billConsultOrderEndorse.nearDailyTotal }}</span>手
        </li>
        <li v-if="item.billConsultOrderEndorse.ticketingDailyTotal">
          自开票日起，每天不超过<span>{{ item.billConsultOrderEndorse.ticketingDailyTotal }}</span>手
        </li>
        <li v-if="item.billConsultOrderEndorse.todayTotal">
          <div>
            今日不超过<span>{{ item.billConsultOrderEndorse.todayTotal }}</span>手
          </div>
        </li>
      </ul>
      <div :class="'replyContent' + item.replyContent">
        {{ item.replyContent === '0' ? '不符合' : '符合' }}
      </div>
    </div>
    <el-empty v-if="!list.length">
      <div slot="image" class="empty-box">
        <icon type="chengjie-empty" class="empty-img" />
      </div>
    </el-empty>
  </el-dialog>
</template>

<script>
import inquiryApi from '@/apis/inquiry'
export default {
  name: 'inquiry-history',
  props: {
    id: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      dialogVisible: true,
      list: []
    }
  },
  async mounted() {
    let data = await inquiryApi.historyInquiryEndorse(this.id) || []
    this.list = data.map(item => {
      for (let k in item.billConsultOrderEndorse) {
        let v = item.billConsultOrderEndorse[k]
        item.billConsultOrderEndorse[k] = v === 0 ? String(v) : v
      }
      return item
    })
  },
  methods: {
    handleClose() {
      this.$emit('close')
    }
  },
}
</script>
