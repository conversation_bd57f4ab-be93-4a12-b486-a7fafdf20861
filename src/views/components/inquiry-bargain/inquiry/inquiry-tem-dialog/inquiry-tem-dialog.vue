<!-- 接单-在线询单 -->
<style lang="scss" scoped>
@mixin body-box($back: $color-FFFFFF) {
  padding: 8px 16px;
  background: $back;
}

::v-deep {
  .dialog-footer .el-button {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0;
    min-width: 88px;
    height: 40px;
  }

  .body {
    .el-button--mini {
      font-size: 14px;
    }

    .body-flex {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 12px 0;
      font-weight: 500;

      @include body-box;
    }

    .title {
      margin-bottom: 12px;
      font-weight: 500;
    }

    .body-beishu {
      margin-bottom: 12px;
      padding: 12px 16px;
      background: $color-FFFFFF;

      .el-input {
        margin: 0 8px;
        width: 52px;
      }

      .el-input__inner {
        padding: 0;
        text-align: center;
      }

      li {
        margin-bottom: 12px;
        margin-left: 18px;
        list-style: disc;
      }

      li:last-child {
        margin-bottom: 0;
      }

      .body-beishu-li {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }

    .body-keyword {
      padding: 12px 16px;
      background: $color-FFFFFF;

      .con {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .el-select {
          flex: 1;
          margin: 0 16px 0 8px;
        }

        .el-input__inner::placeholder {
          font-size: 14px;
        }
      }
    }

    .templateName {
      margin-bottom: 12px;
    }
  }
}
</style>

<template>
  <div>
    <el-dialog
      class="inquiry-list-dialog"
      :title="title"
      :visible.sync="dialogVisible"
      width="600px"
      height="754px"
      :before-close="handleClose"
      :close-on-click-modal="false"
      append-to-body
      center
      :lock-scroll="true"
    >
      <div class="body">
        <!-- 名称 -->
        <div class="body-keyword templateName">
          <div class="title">模版名称</div>
          <div class="con">
            <el-input
              v-model="templateName"
              size="small"
              maxlength="10"
              placeholder="请输入模版名称"
            />
          </div>
        </div>
        <!-- 背书 -->
        <div class="body-beishu">
          <div class="title">请输入您对背书手数的要求，票方将告知您是否符合：</div>
          <ul>
            <li>
              累计不超过<el-input
                v-model="consultEndorse.grandTotal"
                size="mini"
                type="number"
                :number-format="numberFormat"
                maxlength="2"
              />手
            </li>
            <li>
              近<el-input
                v-model="consultEndorse.nearDay"
                size="mini"
                type="number"
                :number-format="numberFormat"
                maxlength="2"
                @input="handleInputDays"
              />日，每天不超过<el-input
                v-model="consultEndorse.nearDailyTotal"
                size="mini"
                type="number"
                :number-format="numberFormat"
                maxlength="2"
              />手
            </li>
            <li>
              自开票日起，每天不超过<el-input
                v-model="consultEndorse.ticketingDailyTotal"
                size="mini"
                type="number"
                maxlength="2"
              />手
            </li>
            <li>
              <div>
                今日不超过<el-input
                  v-model="consultEndorse.todayTotal"
                  size="mini"
                  type="number"
                  :number-format="numberFormat"
                  maxlength="2"
                />手
              </div>
            </li>
          </ul>
        </div>
        <!-- 敏感字 -->
        <div class="body-keyword">
          <div class="title">请选择您对敏感字的要求，票方将告知您是否包含:</div>
          <div class="con">
            不含
            <SearchSelect
              :options-data="sensitiveWords"
              :font-key-word="sensitiveWord"
              @change="v => sensitiveWord = v"
            />
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          v-waiting="['post::loading::/draft/consultTemplate/save', 'post::loading::/draft/consultTemplate/update']"
          type="primary"
          @click="handelConfirm('isSelectAll')"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import inquiryApi from '@/apis/inquiry'
import marketApi from '@/apis/market'
import SearchSelect from '@/views/components/search-select/search-select.vue' // 带搜索的筛选组件

export default {
  name: 'inquiry-tem-dialog',
  components: {
    SearchSelect,
  },
  props: {
    temData: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      sensitiveWords: [], // 敏感字
      templateName: '', // 模板名称
      consultEndorse: {
        grandTotal: null, // 累计不超多少手
        ticketingDailyTotal: null, // 自出票日起每日不超过
        nearDay: null, // 近几日
        nearDailyTotal: null, // 近几日,每日不超过
        todayTotal: null // 今日不超过
      },
      sensitiveWord: [], // 敏感词,多个以英文逗号分隔
      dialogVisible: true,
      // 输入框格式
      numberFormat: {
        decimal: false, // 是否支持小数
        negative: false, // 是否支持负数
        maxLength: 3, // 最大长度
        leadingZero: false, // 是否支持前导零，如 001
      },
      title: '新增'
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    async init() {
      this.getSensitiveWords()
      this.title = this.temData.id ? '修改' : '新增'
      if (!this.temData.id) return
      let data = await inquiryApi.consultTemplateSingle(this.temData.id)
      const { sensitiveWord, templateName } = data
      this.templateName = templateName
      this.consultEndorse = { ...data }
      if (sensitiveWord) {
        this.sensitiveWord = sensitiveWord.split(',') || []
      }
    },
    // 查询风险选项列表
    async getSensitiveWords() {
      const data = await marketApi.getSensitiveWords()
      this.sensitiveWords = data
    },
    // 限制输入天数必须大于0
    handleInputDays(val) {
      this.consultEndorse.nearDay = val.replace(/^0/, 1)
    },
    // 关闭弹窗
    handleClose() {
      this.$emit('close-inuqiry-tem-set')
    },
    // 确认
    async handelConfirm() {
      if (!this.templateName) return this.$message.error('请输入模版名称')
      const data = {
        ...this.consultEndorse,
        templateName: this.templateName,
        sensitiveWord: this.sensitiveWord.join(','),
        id: this.temData.id
      }
      !this.temData.id ? await inquiryApi.consultTemplateSave(data) : await inquiryApi.consultTemplateUpdate(data)
      this.$message.success('操作成功')
      this.handleClose()
    },
  },
}
</script>
