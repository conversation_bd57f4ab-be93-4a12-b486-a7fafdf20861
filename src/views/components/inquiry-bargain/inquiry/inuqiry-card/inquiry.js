import inquiryApi from '@/apis/inquiry'
import { formatTime } from '@/common/js/date'
export default {
  data() {
    return {
      isShowReceiveOrderDetail: false, // 接单-票据详情
      draftInfo: {}, // 接单-票据详情 - 票据数据
      isShowBargainApplyDialog: false, // 议价申请弹窗
      showInquiryReplayDialog: false, // 询单--详情
    }
  },
  filters: {
    filterInquiryTime(v) {
      if (!v) return ''
      try {
        return formatTime(v, 'hh:mm:ss')
      } catch (error) {
        // conosle.log(error)
      }
    },
  },
  methods: {
    // 订单详情
    openOrder() {
      let path = `/user-center/draft-detail?orderNo=${this.cardData.orderNo}`
      // 新开处理页面
      window.open(path)
    },
    // 接单
    takeOrder() {
      this.isShowReceiveOrderDetail = true
      this.$nextTick().then(async() => {
        try {
          await this.$refs.receiveOrderDetail.init(this.cardData.orderNo) // 接单详情弹窗组件初始化
        } catch (error) {
          // console.log('接单详情加载失败 :>> ', error)
        }
      })
    },
    clearData() {
      this.showInquiryReplayDialog = false
      this.isShowReceiveOrderDetail = false
      this.isShowBargainApplyDialog = false
    },
    // 刷新列表
    refreshList(isClose) {
      // false 表示 需要关闭， true为不关闭
      if (isClose) return
      this.clearData()
      this.$emit('success', isClose)
    },
    // 结束询单
    overOrder() {
      this.$confirm('你确定要结束询单吗？', '结束询单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(async() => {
        this.$message.success('操作成功')
        await inquiryApi.finishConsultOrder(this.cardData.id)
        this.refreshList()
      })
    },
  },
}
