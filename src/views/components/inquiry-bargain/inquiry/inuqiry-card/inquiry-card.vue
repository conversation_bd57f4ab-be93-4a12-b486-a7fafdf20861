<style lang="scss" scoped>
.inquiry-card {
  margin: 12px 0;

  .inquiry-msg {
    position: relative;
    padding: 16px;
    background: $color-FFFFFF;

    .inquiry-msg-flex {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .title {
        max-width: 460px;
        font-weight: 600;
      }

      .over-status {
        position: absolute;
        top: 14px;
        right: -12px;
      }

      .over {
        display: inline-block;
        border-radius: 11px 0 0 11px;
        width: 61px;
        height: 23px;
        font-size: 12px;
        text-align: center;
        color: $color-text-regular;
        background: rgb(144 144 144 / 20%);
        line-height: 23px;
      }

      .icon-delete {
        margin-right: 10px;
        color: #999999;
        cursor: pointer;
      }

      .buy-btn {
        margin-left: 8px;
        white-space: nowrap;
      }
    }

    .inquiry-msg-con {
      span {
        margin: 0 4px;
        color: #DDE0E9;
      }
    }
  }

  .inquiry-zijin {
    margin-top: 1px;
    padding: 16px;
    background: $--color-primary-hover;

    .flex {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .flex-btn {
        display: flex;
        align-items: center;

        .btn {
          position: relative;
          display: flex;
          align-items: center;
          margin-right: 8px;
          border-radius: 2px;
          padding: 2px 8px;
          color: $color-text-primary;
          background: rgba($color: $--color-primary, $alpha: 15%);
          cursor: pointer;
        }

        .replyStatus::after {
          position: absolute;
          top: 0;
          right: 0;
          border-radius: 50%;
          width: 5px;
          height: 5px;
          background: $color-warning;
          content: "";
        }
      }
    }

    .title {
      margin-bottom: 12px;
      font-weight: 600;
    }

    .icon {
      margin-left: 4px;
      width: 12px;
      height: 12px;
    }
  }
}

::v-deep {
  .el-button--mini {
    padding: 6px 12px;
    font-size: 14px;
  }
}
</style>

<template>
  <div class="inquiry-card">
    <!-- 信息 -->
    <div class="inquiry-msg">
      <div class="inquiry-msg-flex">
        <div class="title">{{ cardData.acceptorName }}</div>
        <!-- 票方--查看详情 -->
        <div v-if="INQUIRY_TAB_LIST.SALE.id === inquiryType">
          <el-button
            size="mini"
            border
            type="primary"
            @click="openOrder"
          >
            查看详情
          </el-button>
        </div>
        <!-- 资方--询单中 -->
        <div v-if="INQUIRY_TAB_LIST.BUY.id === inquiryType && cardData.consultStatus === 0" class="buy-btn">
          <el-button
            size="mini"
            border
            type="primary"
            @click="overOrder"
          >
            结束询单
          </el-button>
          <el-button
            size="mini"
            type="primary"
            @click="takeOrder"
          >
            接单
          </el-button>
        </div>
        <!-- 资方-询单结束  -->
        <div v-if="INQUIRY_TAB_LIST.BUY.id === inquiryType && cardData.consultStatus === 1" class="over-status">
          <span @click="deleteMsg"> <icon class="icon-delete" type="chengjie-a-lianji2" size="16" /></span>
          <div class="over">
            已失效
          </div>
        </div>
      </div>
      <div class="inquiry-msg-con">
        {{ yuan2wan(cardData.draftAmount) }} 万<span>|</span>
        {{ cardData.maturityDate }} <span>|</span>
        票号后6位{{ cardData.draftNo.slice(-6) }} <span>|</span>
        每十万扣款{{ cardData.lakhFee }}元/年化{{ cardData.annualInterest }}%
      </div>
    </div>
    <!-- 票方 -->
    <div v-if="INQUIRY_TAB_LIST.SALE.id === inquiryType && cardData.sellerConsultOrderDetailsList.length > 0" class="inquiry-zijin">
      <div v-for="(item, index) in cardData.sellerConsultOrderDetailsList" :key="item.billConsultOrderId">
        <div class="title">资金方{{ index + 1 }} {{ item.consultOrderDetails[0].consultTime | filterInquiryTime }}</div>
        <div class="flex">
          <div class="flex-btn">
            <div
              v-for="c in item.consultOrderDetails"
              :key="c.id"
              class="btn"
              :class="c.replyStatus === 0 ? 'replyStatus' : ''"
            >
              {{ INQUIRY_TYPES_VALUE_MAP[c.consultType] }}
            </div>
          </div>
          <el-button size="mini" type="primary" @click="openInquiryReplayDialog(index)">{{ checkReplyStatus(item.consultOrderDetails) ? '查看并回复' : '查看' }}</el-button>
        </div>
      </div>
    </div>
    <!-- 资方 -->
    <div v-if="INQUIRY_TAB_LIST.BUY.id === inquiryType && cardData.consultOrderDetails.length > 0" class="inquiry-zijin">
      <div class="title">我 {{ cardData.consultOrderDetails[0].consultTime | filterInquiryTime }}</div>
      <div>
        <div class="flex">
          <div class="flex-btn">
            <div
              v-for="item in cardData.consultOrderDetails"
              :key="item.id"
              class="btn"
              :class="item.replyStatus === 0 ? 'replyStatus' : ''"
            >
              {{ INQUIRY_TYPES_VALUE_MAP[item.consultType] }}
              <!--  回复内容      * 票面信息：保存的url      * 其他3中类型：0是不符合，1是符合 -->
              <img v-if="item.replyContent === '1'" src="https://oss.chengjie.red/web/imgs/inquiry/right.png" class="icon">
              <img v-if="item.replyContent === '0'" src="https://oss.chengjie.red/web/imgs/inquiry/wrong.png" class="icon">
            </div>
          </div>
          <el-button size="mini" type="primary" @click="openInquiryReplayDialog">查看</el-button>
        </div>
      </div>
    </div>

    <!-- 接单-票据详情 -->
    <ReceiveOrderDetail
      v-if="isShowReceiveOrderDetail"
      ref="receiveOrderDetail"
      @success="refreshList"
      @close-dialog="closeDialog"
    />

    <!-- 议价申请弹窗 -->
    <BargainApplyDialog
      v-if="isShowBargainApplyDialog"
      ref="bargainApplyDialogRef"
      :draft-info="draftInfo"
      @InquiryReplayDialog="refreshList"
    />
    <!-- 询单--详情 -->
    <InquiryReplayDialog
      v-if="showInquiryReplayDialog"
      :card-data="cardData"
      :current-index="currentIndex"
      :inquiry-type="inquiryType"
      @success="refreshList"
    />
  </div>
</template>

<script>
import { yuan2wan } from '@/common/js/number'
import { INQUIRY_TAB_LIST, INQUIRY_TYPES_VALUE_MAP } from '@/constants/inquiry-bargain'
import ReceiveOrderDetail from '@/views/pages/market/components/receive-order-detail/receive-order-detail.vue' // 接单详情组件
import InquiryReplayDialog from '@/views/components/inquiry-bargain/inquiry/inquiry-replay-dialog/inquiry-replay-dialog.vue' // 询单--详情-组件
import BargainApplyDialog from '@/views/components/inquiry-bargain/bargain/bargain-apply-dialog/bargain-apply-dialog.vue' // 议价申请弹窗
import inquiry from '@/views/components/inquiry-bargain/inquiry/inuqiry-card/inquiry'
import inquiryApi from '@/apis/inquiry'
export default {
  name: 'inquiry-card',
  components: {
    ReceiveOrderDetail,
    InquiryReplayDialog,
    BargainApplyDialog
  },
  mixins: [inquiry],
  props: {
    cardData: { // 当条数据
      type: Object,
      default: () => ({})
    },
    inquiryType: { // 票方/资方
      type: String,
      default: ''
    }
  },
  data() {
    return {
      yuan2wan,
      INQUIRY_TAB_LIST,
      INQUIRY_TYPES_VALUE_MAP,
      currentIndex: null, // 当条票方询单的资金方的索引值
    }
  },

  methods: {
    async deleteMsg() {
      await inquiryApi.buyerDeleteOrders(this.cardData.id)
      this.$message.success('已删除')
      this.$emit('success')
    },
    // 打开询单- 详情
    openInquiryReplayDialog(index) {
      this.currentIndex = INQUIRY_TAB_LIST.SALE.id === this.inquiryType ? index : null
      this.clearData()
      this.showInquiryReplayDialog = true
    },
    // 用于 接单页面点开 在线询单跳转之后 关闭接单页面
    closeDialog() {
      this.$emit('close-dialog')
    },
    // 检查咨询的问题列表中是否存在未回复的 replyStatus=>是否回复 0否 1是 canUpdate=>是否修改过 0否 1是
    checkReplyStatus(item) {
      return item.some(e => !e.replyStatus || !e.canUpdate)
    }
  },
}
</script>
