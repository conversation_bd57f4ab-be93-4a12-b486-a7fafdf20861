<!-- 询单-消息通知 -->
<style lang="scss" scoped>
@import "~/src/views/components/notification/components/notification-card-list/common.scss";

.messagetile {
  padding: 8px 12px;
  color: $color-warning;
  background: $color-FCE6E7;
}

.mt4 {
  margin-bottom: 4px;
}

.main {
  margin-bottom: 12px;
  border: 1px solid $color-D9D9D9;
  padding: 8px;
  font-size: 14px;
  background: $color-FFFFFF;
  box-shadow: 0 0 8px 0 rgb(0 0 0 / 10%);

  .flex {
    display: flex;
    align-items: center;
  }
}
</style>

<template>
  <div class="ismqtt">
    <div class="messageTile mt4">
      <text-tooltip :content="data.messageTitle" />
    </div>
    <div class="main">
      <div class="mt4 flex">
        <div class="item-label">咨询时间：</div>
        <div class="item-value">{{ formatTime(data.consultTime) }}</div>
      </div>
      <div class="mt4 flex">
        <div class="item-label">票号：</div>
        <div v-if="data.draftNo" class="item-value">{{ '********' + data.draftNo.slice(-6) }}</div>
      </div>
      <div class="flex">
        <div class="item-label">票面金额：</div>
        <div class="item-value">{{ yuan2wan(data.draftAmount) }}万</div>
      </div>
    </div>
    <div class="footer">
      <el-button
        height="32"
        width="88"
        type="primary"
        @click="onClickHandle"
      >
        {{ data.userRole === INQUIRY_TAB_LIST.SALE.id ? '立即处理' : '立即查看' }}
      </el-button>
    </div>
    <!-- userRole：0是票方，1是资金方 -->
  </div>
</template>

<script>
import { yuan2wan } from '@/common/js/number'
import { INQUITY_TYPES, INQUIRY_TAB_LIST } from '@/constants/inquiry-bargain'
import {
  XUANDAN_DETAIL_NOTIFICATION, // 消息里的询单详情
} from '@/event/modules/site' // 监听事件常量
import { formatTime } from '@/common/js/date'
export default {
  name: 'inquiry-replay-dialog',
  props: {
    // vue.extend传入来的data数据（mqtt传入使用的数据）
    data: {
      type: Object,
      default: () => ({})
    },
    // 该消息组件id
    notificationId: {
      type: String,
      default: ''
    },
    // 该交易消息卡片组件用途 1-MQTT推送 2-消息列表
    type: {
      type: Number,
      default: 1
    },
  },
  data() {
    return {
      myData: null, // 保存vue.extend传入来的data数据
      INQUIRY_TAB_LIST,
      yuan2wan
    }
  },
  methods: {
    formatTime,
    getConsultOrderDetails(data) {
      return data.map(child => {
        // 解析背书
        if (child.consultEndorseDesc) {
          let obj = JSON.parse(child.consultEndorseDesc)
          for (let k in obj) {
            if (obj[k] === 0) {
              obj[k] = '0'
            }
          }
          child = {
            ...child,
            ...obj
          }
        }
        // 解析票据
        if (child.consultType === INQUITY_TYPES.IMG.id) {
          child.replyContent = child.replyContent ? JSON.parse(child.replyContent) : {}
        }
        return child
      })
    },
    onClickHandle() {
      const { userRole, consultOrderDetails, sellerConsultOrderDetailsList } = this.data
      let cardData = Object.assign({}, this.data)
      let inquiryType = `${userRole}`
      if (`${userRole}` === INQUIRY_TAB_LIST.SALE.id) {
        if (sellerConsultOrderDetailsList) {
          cardData.sellerConsultOrderDetailsList = sellerConsultOrderDetailsList.map(seller => {
            if (seller.consultOrderDetails) {
              seller.consultOrderDetails = this.getConsultOrderDetails(seller.consultOrderDetails)
            }
            return seller
          })
        }
      }
      if (`${userRole}` === INQUIRY_TAB_LIST.BUY.id) {
        if (consultOrderDetails) {
          cardData.consultOrderDetails = this.getConsultOrderDetails(consultOrderDetails)
        }
      }
      cardData.title = '询单'
      this.$event.emit(XUANDAN_DETAIL_NOTIFICATION, { cardData, inquiryType })
    }
  },
}
</script>
