<!-- 询单弹窗列表 -->
<style lang="scss" scoped>
.data-list {
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  width: 100%;
  height: 590px;

  // >div {
  //   margin-bottom: 12px;
  //   width: 100%;

  //   &:last-child {
  //     margin-bottom: 0;
  //   }
  // }
  .bargain-card {
    margin-top: 0;
    margin-bottom: 12px;
  }

  ::v-deep .notification-card {
    width: 100%;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .row {
    margin-top: 0;
  }

  // .notification-card {
  // min-height: 288px;
  // }
}

.mt50 {
  margin-top: 50px;
}

// el组件样式
::v-deep {
  .el-dialog .el-dialog__body {
    padding: 20px;
  }

  .el-tabs__header {
    margin: 0;
  }

  .el-tabs__nav {
    display: flex;
    border-bottom: 0;
    width: 100%;

    .el-tabs__active-bar {
      display: none;
    }

    .el-tabs__item {
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px solid $color-D9D9D9;
      padding: 0;
      min-width: 0;
      height: 42px;
      font-weight: 400;
      background-color: $color-FFFFFF;
      flex: 1;
      line-height: 42px;

      &.is-active {
        border: 1px solid $--color-primary;
        color: #FFFFFF;
        background-color: $--color-primary;

        .el-badge {
          .el-badge__content {
            margin-left: 4px;
            font-size: 14px;
            color: $color-text-primary;
            color: $color-FFFFFF;
            background-color: $color-warning;
          }
        }
      }

      .label {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .el-badge {
        display: flex;
        align-items: center;

        .el-badge__content {
          margin-left: 2px;
          border: 0;
          border-color: transparent;
          border-radius: 50%;
          padding: 0;
          width: 18px;
          height: 18px;
          font-size: 12px;
          color: $color-FFFFFF;
          background-color: $color-warning;
          line-height: 18px;
        }

        &.more {
          .el-badge__content {
            border-radius: 18px;
            padding: 0 6px;
            width: auto;
          }
        }

        &.mixmore {
          .el-badge__content {
            border-radius: 18px;
            padding: 0 4px;
            width: auto;
          }
        }
      }

      &:nth-child(2) {
        border-radius: 2px 0 0 2px;
      }

      &:last-child {
        border-radius: 0 2px 2px 0;
      }
    }
  }

  .el-divider__text {
    color: $color-text-secondary;
    background-color: $color-F2F2F2;
  }

  .el-empty {
    margin-top: 28%;

    .el-empty__image {
      width: auto;
    }

    .el-empty__description {
      margin-top: -20px;
    }

    .el-empty__description p {
      font-size: 18px;
      color: $color-text-secondary;
    }
  }

  .empty-box {
    .empty-img {
      font-size: 14em;
      color: #FFFFFF;
    }
  }
}

@mixin body-box($back: $color-FFFFFF) {
  padding: 8px 16px;
  background: $back;
}

.body-waring {
  margin-top: 12px;

  @include body-box($color-FCE6E7);

  color: $color-warning;

  .error-tip {
    margin: 0 8px 0 0;
    font-size: 16px;
  }
}
</style>

<template>
  <div class="body">
    <!-- 票方资方选择-顶部tab栏 -->
    <el-tabs
      v-model="activeTab"
      @tab-click="handleClickTab"
    >
      <el-tab-pane v-for="tab in tabsList" :key="tab.id" :name="`${tab.id}`">
        <span slot="label" class="label">
          {{ tab.label }}

          <el-badge
            :value="tab.id === INQUIRY_TAB_LIST.SALE.id ? getCount(inquiryBargain.sellerTotalCount) : getCount(inquiryBargain.buyerTotalCount)"
            :class="{
              more: (tab.id === INQUIRY_TAB_LIST.SALE.id ? inquiryBargain.sellerTotalCount : inquiryBargain.buyerTotalCount) > 99,
              mixmore: (tab.id === INQUIRY_TAB_LIST.SALE.id ? inquiryBargain.sellerTotalCount : inquiryBargain.buyerTotalCount) >= 99 && (tab.id === INQUIRY_TAB_LIST.SALE.id ? inquiryBargain.sellerTotalCount : inquiryBargain.buyerTotalCount) < 9
            }"
          />
        </span>
        <!-- 询单消息列表 -->
        <div
          v-infinite-scroll="getInquiryList"
          class="data-list"
          :infinite-scroll-disabled="disabled"
          infinite-scroll-delay="500"
          :infinite-scroll-distance="50"
        >
          <template v-if="+tab.id === +activeTab && list.length > 0">
            <div v-if="activeTab === INQUIRY_TAB_LIST.BUY.id" class="body-waring">
              <icon class="icon question-icon error-tip" type="chengjie-exclamation-circle" />
              <!-- <icon class="icon question-icon error-tip" type="chengjie-exclamation-circle" /> -->
              询单超过10分钟或订单状态变化，询单结果自动失效，建议重新发起询单
            </div>
            <!-- 议价卡片 -->
            <InquiryCard
              v-for="data in list"
              :id="'inquiryiDv' + (data.orderNo)"
              :key="data.id"
              :inquiry-type="activeTab"
              :card-data="data"
              @success="handleRefresh"
              @scroll="scroll"
            />

            <el-divider class="mt50">
              <template v-if="loading">
                加载中
              </template>
              <template v-else-if="isNoMore">
                到底了
              </template>
            </el-divider>
          </template>

          <el-empty v-if="!loading && !list.length">
            <div slot="image" class="empty-box">
              <icon type="chengjie-empty" class="empty-img" />
            </div>
          </el-empty>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import inquiryApi from '@/apis/inquiry'
import { INQUIRY_TAB_LIST, INQUITY_TYPES, INQUIRYREFRESH } from '@/constants/inquiry-bargain'
import InquiryCard from '../inuqiry-card/inquiry-card.vue'
import { mapGetters } from 'vuex'
export default {
  name: 'inquiry-list-dialog',

  components: {
    InquiryCard
  },
  props: {
    // 智能助手询单的订单号
    bargainId: {
      type: String,
      default: null
    },
  },
  data() {
    return {
      activeTab: INQUIRY_TAB_LIST.SALE.id, // 当前tab
      tabsList: INQUIRY_TAB_LIST, // tabs
      INQUIRY_TAB_LIST,
      // sellerRecord: 0, // 票方数量
      // buyerRecord: 0, // 资方数量
      loading: false,
      list: [],
      isNoMore: false,
      // 查询参数
      query: {
        pageNum: 1, // 当前页码
        pageSize: 10, // 每页条数
      },
      totalRecord: 0,
      isLoadError: false, // 是否请求加载失败（用来阻止触底加载数据等）
    }
  },
  computed: {
    // 是否可请求列表数据
    disabled() {
      return this.loading || this.isNoMore || this.isLoadError
    },
    ...mapGetters('market', {
      inquiryBargain: 'inquiryBargain'
    }),
  },
  async mounted() {
    // 智能助手跳转过来的 tab 默认资方
    this.activeTab = this.bargainId ? INQUIRY_TAB_LIST.BUY.id : INQUIRY_TAB_LIST.SALE.id

    await this.initData()
    // mqtt刷新列表
    this.$event.on(INQUIRYREFRESH, data => {
      const { userRole, billOrderId } = data
      let index = this.list.findIndex(item => `${item.billOrderId}` === `${billOrderId}`)
      if (index === -1) return this.initData()
      if (this.activeTab === `${userRole}`) {
        let item = this.handleData(data)
        this.list.splice(index, 1, item)
        // 票方-已结束的要删除掉
        // if (this.activeTab === INQUIRY_TAB_LIST.SALE.id) {
        //   this.list = this.list.filter(t => t.consultStatus !== 1)
        // }
      }
    })
    this.scroll()
  },
  methods: {
    scroll() {
      if (this.bargainId) {
        // 如果被删除了 就不执行滚动效果
        let hasRecord = this.list.some(item => item.orderNo === this.bargainId)
        if (!hasRecord) return

        let scrollDiv = document.getElementById(`inquiryiDv${this.bargainId}`)
        // 如果当前 scrollDiv 子元素已经加载完毕 直接滚动
        if (scrollDiv.children.length > 0) {
          scrollDiv.scrollIntoView()
        } else {
          // 等待scrollDiv子元素加载完成后再执行滚动
          scrollDiv.addEventListener('load', () => {
            scrollDiv.scrollIntoView()
          })
        }
      }
    },
    getCount(val) {
      if (val > 99) {
        return '99+'
      }
      return val !== 0 ? val : ''
    },
    // 初始化
    async initData() {
      this.clearData()
      await this.getInquiryList()
    },
    // 切换tab
    handleClickTab() {
      this.initData()
    },
    getConsultOrderDetails(data) {
      return data.map(child => {
        // 解析背书
        if (child.consultEndorseDesc) {
          let obj = JSON.parse(child.consultEndorseDesc)
          for (let k in obj) {
            if (obj[k] === 0) {
              obj[k] = '0'
            }
          }
          child = {
            ...child,
            ...obj
          }
        }
        // 解析票据
        if (child.consultType === INQUITY_TYPES.IMG.id) {
          child.replyContent = child.replyContent ? JSON.parse(child.replyContent) : {}
        }
        return child
      })
    },
    handleData(item) {
      if (this.activeTab === INQUIRY_TAB_LIST.SALE.id) {
        item.sellerConsultOrderDetailsList = item.sellerConsultOrderDetailsList.map(seller => {
          seller.consultOrderDetails = this.getConsultOrderDetails(seller.consultOrderDetails)
          return seller
        })
      } else {
        item.consultOrderDetails = this.getConsultOrderDetails(item.consultOrderDetails)
      }
      return item
    },
    // 获取列表数据
    async getInquiryList() {
      if (this.isNoMore || this.loading) return
      try {
        this.loading = true
        let { rowList, totalRecord } = this.activeTab === INQUIRY_TAB_LIST.SALE.id ? await inquiryApi.sellerConsultOrders(this.query) : await inquiryApi.buyerConsultOrders(this.query)
        this.totalRecord = totalRecord
        rowList = rowList.map(item => this.handleData(item))

        this.list = this.list.concat(rowList)
        this.query.pageNum++
        this.isNoMore = totalRecord === 0 || rowList.length === 0 ? true : (this.list.length > 0 && (this.list.length >= totalRecord))
        this.loading = false
        this.isLoadError = false
      } catch (error) {
        this.loading = false
        this.isLoadError = true
        return error
      }
    },
    // 刷新
    handleRefresh() {
      this.$nextTick().then(() => {
        this.initData() // 刷新数据
      })
    },

    // 清除数据
    clearData() {
      this.list = [] // 清除列表
      this.query.pageNum = 1
      this.totalRecord = 0
      this.isNoMore = false
      this.isLoadError = false
      this.loading = false
    },
  },
}
</script>
