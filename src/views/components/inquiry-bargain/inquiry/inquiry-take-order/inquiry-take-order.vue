<!-- 接单-在线询单 -->
<style lang="scss" scoped>
@mixin body-box($back: $color-FFFFFF) {
  padding: 8px 16px;
  background: $back;
}

.over-sensitiveWord {
  overflow: hidden;
  width: 150px;
  height: 20px;
  text-overflow: ellipsis;
  white-space: nowrap;
}

::v-deep {
  .dialog-footer {
    .el-button {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0;
      min-width: 88px;
      height: 40px;
    }
  }

  .body {
    .body-waring {
      @include body-box($color-FCE6E7);

      color: $color-warning;

      .error-tip {
        margin: 0 8px 0 0;
        font-size: 16px;
      }
    }

    .el-button--mini {
      font-size: 14px;
    }

    .body-flex {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 12px 0;
      font-weight: 500;

      @include body-box;
    }

    .title {
      margin-bottom: 12px;
      font-weight: 500;
    }

    .body-beishu {
      margin-bottom: 12px;
      padding: 12px 16px;
      background: $color-FFFFFF;

      .el-input {
        margin: 0 8px;
        width: 52px;
      }

      .el-input__inner {
        padding: 0;
        text-align: center;
      }

      li {
        margin-bottom: 12px;
        margin-left: 18px;
        list-style: disc;
      }

      li:last-child {
        margin-bottom: 0;
      }

      .body-beishu-li {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }

    .body-keyword {
      padding: 12px 16px;
      background: $color-FFFFFF;

      .con {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .el-select {
          flex: 1;
          margin: 0 16px 0 8px;
        }

        .el-input__inner::placeholder {
          font-size: 14px;
        }
      }
    }
  }

  .inquiry-tem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .setting {
      margin-left: 8px;
      font-size: 16px;
      color: $font-color;
      cursor: pointer;
    }

    .tem {
      flex: 1;
      display: flex;
      align-items: center;
      cursor: pointer;

      .tem-btn {
        overflow: hidden;
        margin-right: 12px;
        border: $color-FFFFFF;
        border: 1px solid $--color-primary;
        border-radius: 2px;
        padding: 8px 14px;
        max-width: 121px;
        text-align: center;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: $--color-primary;
      }

      .tem-btn-primary {
        border: 1px solid $color-FFFFFF;
        color: $color-FFFFFF;
        background: $--color-primary;
      }

      .tem-btn:last-child {
        margin-right: 0;
      }
    }
  }

  .require-header,
  .sensitive-words-header {
    display: flex;
    justify-content: space-between;
  }

  li {
    margin-left: 18px;
    list-style: disc;
  }
}
</style>

<template>
  <div>
    <el-dialog
      class="inquiry-list-dialog"
      title="询单"
      :visible.sync="dialogVisible"
      width="600px"
      height="754px"
      :before-close="handleClose"
      :close-on-click-modal="false"
      append-to-body
      center
      :lock-scroll="true"
    >
      <div class="body">
        <!-- 模版 -->
        <div v-if="tems.length > 0" class="inquiry-tem">
          <div class="tem">
            <div
              v-for="(item, index) in tems"
              :key="item.id"
              class="tem-btn"
              :class="currentTemIndex === index ? 'tem-btn-primary' : ''"
              @click="chooseTem(item, index)"
            >
              <el-tooltip>
                <template slot="content">
                  <div v-if="item.grandTotal || item.nearDay || item.nearDailyTotal || item.todayTotal">背书手数</div>
                  <div>
                    <ul>
                      <li v-if="item.grandTotal ">
                        累计不超过 <span>{{ item.grandTotal }}</span> 手
                      </li>
                      <li v-if="item.nearDay || item.nearDailyTotal ">近<span>{{ item.nearDay }}</span>日，每天不超过<span>{{ item.nearDailyTotal }}</span>手</li>
                      <li v-if=" item.ticketingDailyTotal ">自开票日起，每天不超过<span>{{ item.ticketingDailyTotal }}</span>手</li>
                      <li v-if=" item.todayTotal">
                        <div>今日不超过<span>{{ item.todayTotal }}</span>手</div>
                      </li>
                    </ul>
                  </div>
                  <div v-if="item.sensitiveWord">
                    敏感字不含
                  </div>
                  <div v-if="item.sensitiveWord" class="over-sensitiveWord">{{ item.sensitiveWord }}</div>
                </template>
                <div> {{ item.templateName }}</div>
              </el-tooltip>
            </div>
          </div>
          <div class="setting" @click="goUserSetting(SETTING_USER.BUY.id, BUY_SETTING_OPTIONS.INQUIRY.id)">设置</div>
        </div>
        <div class="body-waring"><icon class="icon question-icon error-tip" type="chengjie-exclamation-circle" />询单超过10分钟或订单状态变化，询单结果自动失效，建议重新发起询单</div>
        <!-- 票据图片 -->
        <!-- <div v-if="discernType && autoConsult" class="body-flex"> -->
        <div class="body-flex">
          <div>咨询票面图片</div>
          <el-button
            type="primary"
            size="mini"
            width="74"
            :disabled="disabledImg"
            @click="handelConfirm(INQUITY_TYPES.IMG.id)"
          >
            {{ disabledImg ? INQUIRYBARGAIN_TEXT.YIZIXUN.label : INQUIRYBARGAIN_TEXT.ZIXUN.label }}
          </el-button>
        </div>
        <!-- 背书 -->
        <div class="body-beishu">
          <div class="title">请输入您对背书手数的要求，票方将告知您是否符合：</div>
          <ul>
            <li>
              累计不超过<el-input
                v-model="consultEndorse.grandTotal"
                size="mini"
                type="number"
                :disabled="disabledBeiShu"
                :number-format="numberFormat"
                maxlength="2"
              />手
            </li>
            <li>
              近<el-input
                v-model="consultEndorse.nearDay"
                size="mini"
                type="number"
                :disabled="disabledBeiShu"
                maxlength="2"
                :number-format="numberFormat"
                @input="handleInputDays"
              />日，每天不超过<el-input
                v-model="consultEndorse.nearDailyTotal"
                size="mini"
                type="number"
                :disabled="disabledBeiShu"
                maxlength="2"
                :number-format="numberFormat"
              />手
            </li>
            <li>
              自开票日起，每天不超过<el-input
                v-model="consultEndorse.ticketingDailyTotal"
                :disabled="disabledBeiShu"
                size="mini"
                type="number"
                maxlength="2"
              />手
            </li>
            <li>
              <div class="body-beishu-li">
                <div>
                  今日不超过<el-input
                    v-model="consultEndorse.todayTotal"
                    :disabled="disabledBeiShu"
                    size="mini"
                    type="number"
                    :number-format="numberFormat"
                    maxlength="2"
                  />手
                </div>
                <el-button
                  type="primary"
                  size="mini"
                  width="74"
                  :disabled="disabledBeiShu"
                  @click="handelConfirm(INQUITY_TYPES.BEISHU.id)"
                >
                  {{ disabledBeiShu ? INQUIRYBARGAIN_TEXT.YIZIXUN.label : INQUIRYBARGAIN_TEXT.ZIXUN.label }}
                </el-button>
              </div>
            </li>
          </ul>
        </div>
        <!-- 敏感字 -->
        <div class="body-keyword">
          <div class="require-header">
            <div class="title">请选择您对敏感字的要求，票方将告知您是否包含:</div>
            <div>
              <div class="text-link" type="text" @click="goUserSetting(SETTING_USER.BUY.id, BUY_SETTING_OPTIONS.TICKET_KEYWORD.id)">设置</div>
            </div>
          </div>
          <div class="con">
            是否包含
            <SearchSelect
              :options-data="sensitiveWords"
              :disabled-data="disabledKeyword"
              :font-key-word="sensitiveWord"
              @change="v => sensitiveWord = v"
            />
            <el-button
              type="primary"
              :disabled="disabledKeyword"
              size="mini"
              width="74"
              @click="handelConfirm(INQUITY_TYPES.KEYWORD.id)"
            >
              {{ disabledKeyword ? INQUIRYBARGAIN_TEXT.YIZIXUN.label : INQUIRYBARGAIN_TEXT.ZIXUN.label }}
            </el-button>
          </div>
        </div>
        <!-- 个体户 -->
        <div class="body-flex">
          <div>咨询票据是否包含个体户</div>
          <el-button
            type="primary"
            size="mini"
            width="74"
            :disabled="disabledOne"
            @click="handelConfirm(INQUITY_TYPES.ONE.id)"
          >
            {{ disabledOne ? INQUIRYBARGAIN_TEXT.YIZIXUN.label : INQUIRYBARGAIN_TEXT.ZIXUN.label }}
          </el-button>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          v-waiting="['post::loading::/api/order/draft/consult/inquiry']"
          type="primary"
          :disabled="disabledAll"
          @click="handelConfirm('isSelectAll')"
        >
          全部咨询
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import SearchSelect from '@/views/components/search-select/search-select.vue' // 带搜索的筛选组件
import inquiryApi from '@/apis/inquiry'
import { INQUITY_TYPES, INQUITY_TYPES_LIST, INQUIRYBARGAIN_TEXT, INQUIRYBARGAIN_DISCERNTYPE } from '@/constants/inquiry-bargain'
import {
  SETTING_USER,
  BUY_SETTING_OPTIONS, // 资方设置选项
} from '@/constants/setting'

import marketApi from '@/apis/market'

export default {
  name: 'inquiry-list-dialog',
  components: {
    SearchSelect,
  },
  inject: { takeOrderType: { value: 'takeOrderType', default: null } },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    billOrderNo: {
      type: String,
      default: ''
    },
    isBatchInquiry: { // 是否批量询单 默认false
      type: Boolean,
      default: false
    },
    batchOrderList: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      SETTING_USER,
      BUY_SETTING_OPTIONS,
      autoConsult: false, // 是否允许自动回复
      discernType: false, // 是否可以咨询票面
      publishSource: null, // 发布来源 1-pc，2-app，3-其他平台，4-H5，5-签手，6-秒贴
      INQUIRYBARGAIN_DISCERNTYPE,
      INQUIRYBARGAIN_TEXT,
      sensitiveWords: [],
      consultTypes: [], // 咨询问题集合: 0：票面信息，1：背书手数，2：敏感信息，3：个体户
      consultEndorse: {
        grandTotal: null, // 累计不超多少手
        ticketingDailyTotal: null, // 自出票日起每日不超过
        nearDay: null, // 近几日
        nearDailyTotal: null, // 近几日,每日不超过
        todayTotal: null // 今日不超过
      },
      sensitiveWord: [], // 敏感词,多个以英文逗号分隔
      INQUITY_TYPES,
      dialogVisible: true,
      tems: [], // 模版
      disabledImg: false, // 是否已咨询票据图片
      disabledBeiShu: false, // 是否已咨询背书
      disabledKeyword: false, // 是否已咨询敏感字
      disabledOne: false, // 是否已咨询个体户
      disabledAll: false, // 是否已全部咨询
      // 输入框格式
      numberFormat: {
        decimal: false, // 是否支持小数
        negative: false, // 是否支持负数
        maxLength: 3, // 最大长度
        leadingZero: false, // 是否支持前导零，如 001
      },
      currentTemIndex: null, // 当前选中的模版
      hasConsultTypes: [] // 已经咨询过的类型
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      // 非批量询单 查询询单状态
      !this.isBatchInquiry && (this.inquiryState())
      this.consultTemplateList()
      this.getSensitiveWords()
    },
    // 获取询单数据
    async inquiryState() {
      let data = await inquiryApi.inquiryState(this.billOrderNo)
      const { consultTypes, sensitiveWord, consultEndorseDesc, discernType, publishSource } = data
      // this.discernType = !!((discernType && INQUIRYBARGAIN_DISCERNTYPE.PILIANG.id !== discernType))
      this.discernType = discernType
      this.publishSource = publishSource
      this.hasConsultTypes = consultTypes || []
      if (consultTypes && consultTypes.length > 0) {
        this.disabledImg = consultTypes.includes(INQUITY_TYPES.IMG.id)
        this.disabledBeiShu = consultTypes.includes(INQUITY_TYPES.BEISHU.id)
        this.disabledKeyword = consultTypes.includes(INQUITY_TYPES.KEYWORD.id)
        this.disabledOne = consultTypes.includes(INQUITY_TYPES.ONE.id)
      }
      let length = Object.keys(INQUITY_TYPES).length - 1
      if (this.discernType) {
        length = length + 1
      }
      this.disabledAll = consultTypes && consultTypes.length === length
      if (sensitiveWord) {
        this.sensitiveWord = data.sensitiveWord ? data.sensitiveWord.split(',') : []
      }
      if (consultEndorseDesc) {
        this.consultEndorse = JSON.parse(consultEndorseDesc)
      }
      this.autoConsult = data.autoConsult
    },
    // 限制输入天数必须大于0
    handleInputDays(val) {
      this.consultEndorse.nearDay = val.replace(/^0/, 1)
    },
    // 获取模版数据
    async consultTemplateList() {
      let data = await inquiryApi.consultTemplateList() || []
      this.tems = data.slice(0, 4)
    },
    // 选择模版
    chooseTem(data, index) {
      // if (this.disabledAll) {
      //   return this.$message.error('您已全部咨询过')
      // }
      // if (this.disabledBeiShu && (data.nearDay || data.nearDailyTotal || data.grandTotal || data.ticketingDailyTotal || data.todayTotal)) {
      //   return this.$message.error('您已咨询过背书')
      // }
      // if (this.disabledKeyword && data.sensitiveWord) {
      //   return this.$message.error('您已咨询过敏感字')
      // }
      this.sensitiveWord = data.sensitiveWord ? data.sensitiveWord.split(',') : []
      for (let key in this.consultEndorse) {
        this.consultEndorse[key] = data[key]
      }
      this.currentTemIndex = index
    },

    // 跳到资方--2:询单  3：风险关键字 random-为了区分当前页面跳转当前页面 弹框不关闭的问题
    async goUserSetting(val, activeBuyTab) {
      let query = { activeContactTab: `${val}`, activeBuyTab: `${activeBuyTab}`, random: Math.floor(Math.random() * 100) }
      // 如果是AI 接单打开
      if (this.takeOrderType === 2) {
        const queryString = Object.keys(query).map(key => `${key}=${query[key]}`)
          .join('&')
        if (this.$ipc) {
          this.$ipc.send('PUSH_ROUTE_IN_MAIN_WINDOW', `/user-center/setting?${queryString}`)
        } else {
          // PC 新窗口打开
          this.orderDetailVisible = false
          this.$emit('close-dialog')
          setTimeout(() => {
            window.open(`/user-center/setting?${queryString}`, '_blank')
          }, 200)
        }
      } else {
        await this.$router.push({ name: 'setting', query })
      }
      this.$emit('close-dialog')
      this.dialogVisible = false
    },

    // 查询风险选析列表
    async getSensitiveWords() {
      const data = await marketApi.getSensitiveWords()
      this.sensitiveWords = data
    },
    // 关闭弹窗
    handleClose() {
      this.$emit('close-inuqiry-take-order')
    },
    // 确认在线询单
    async handelConfirm(type) {
      this.currentTemIndex = null
      let p = { billOrderNo: this.billOrderNo, orderNoList: this.batchOrderList, consultTypes: [type] }
      let isSelectAll = type === 'isSelectAll'
      if (isSelectAll) {
        let types = Object.values(INQUITY_TYPES_LIST).map(item => item.id)
        if (!this.discernType && !this.isBatchInquiry) { // 非批量询单 判断是否允许咨询票面
          types = types.filter(item => item !== INQUITY_TYPES.IMG.id)
        }
        p.consultTypes = []
        for (let i = 0; i < types.length; i++) {
          let index = this.hasConsultTypes.findIndex(item => item === types[i])
          if (index === -1) {
            p.consultTypes.push(types[i])
          }
        }
      }
      // 背书参数
      if (type === INQUITY_TYPES.BEISHU.id || isSelectAll) {
        let vals = Object.values(this.consultEndorse)
        let isNot = vals.every(item => item === '')
        if (isNot) {
          return this.$message.error('您至少输入一项背书选项')
        }
        p = {
          ...p,
          consultEndorse: this.consultEndorse
        }
      }
      // 敏感词
      if (type === INQUITY_TYPES.KEYWORD.id || isSelectAll) {
        if (this.sensitiveWord.length === 0) {
          return this.$message.error('您输入的敏感词不能为空')
        }
        p = {
          ...p,
          sensitiveWord: this.sensitiveWord.join(',')
        }
      }
      // 批量询单
      if (this.isBatchInquiry) {
        let data = await inquiryApi.inquiryBatch(p)
        this.$message.success(`您已发起批量询单操作，成功 ${data.successNum || 0} 笔，失败 ${data.failedNum || 0} 笔`)
      } else { // 单笔询单
        let data = await inquiryApi.inquiry(p)
        this.$message.success(data)
        this.inquiryState()
      }
    },
    // 去设置页
    goSetting() {
      const { href } = this.$router.resolve({
        path: '/user-center/setting',
        query: {
          activeBuyTab: BUY_SETTING_OPTIONS.INQUIRY.id
        }
      })
      window.open(href, '_blank')
    }
  },
}
</script>
