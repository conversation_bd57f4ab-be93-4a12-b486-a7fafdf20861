
<!-- 议价&询单-悬浮按钮 -->
<style lang="scss" scoped>
.inquiry-bargain {
  // 悬浮固定样式
  &.fixed-style {
    position: fixed;
    top: 176px;
    right: 0;
    z-index: 999;

    .count {
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 74px 0 0 74px;
      font-size: 12px;
      flex-direction: column;
      cursor: pointer;

      .icon {
        font-size: 18px;
      }

      .inquiry-bargain-icon {
        font-size: 50px;
        color: $--color-primary;

        &.hn {
          color: $--color-primary;
        }
      }

      .count-badge {
        position: absolute;
        top: -5px;
        right: -3px;

        ::v-deep .el-badge__content {
          border: none;
          border-radius: 50%;
          padding: 0;
          width: 20px;
          height: 20px;
          font-weight: 600;
          color: $color-FFFFFF;
          background-color: $color-warning;
          line-height: 20px;
        }
      }
    }
  }

  // 右侧工具栏样式
  &.tool-style {
    width: 60px;
    height: 73px;

    .count {
      display: flex;
      align-items: center;
      padding-top: 12px;
      width: 60px;
      height: 73px;
      font-size: 14px;
      color: $color-text-regular;
      flex-direction: column;
      cursor: pointer;

      .name {
        margin-top: 8px;
        font-size: 12px;
      }

      .icon {
        font-size: 26px;
      }

      .count-badge {
        position: absolute;
        top: 5px;
        right: 8px;

        ::v-deep .el-badge__content {
          border-color: $color-warning;
          border-radius: 50%;
          padding: 0;
          width: 20px;
          height: 20px;
          font-weight: 600;
          color: $color-FFFFFF;
          background-color: $color-warning;
          line-height: 19px;
          box-sizing: border-box;
        }

        &.more {
          right: 8px;

          ::v-deep .el-badge__content {
            border-radius: 30px;
            min-width: 26px;
            height: 20px;
            font-size: 12px;
            line-height: 20px;
          }
        }
      }
    }
  }
}
</style>

<template>
  <div
    class="inquiry-bargain"
    :class="{
      'fixed-style': styleType === 1,
      'tool-style': styleType === 3,
    }"
  >
    <div
      class="count"
      @click="handleShowInquireBargainListDialog"
    >
      <icon v-if="INQUIRYBARGAIN_NAME.YIJIA.id === showType" class="icon" :type="INQUIRYBARGAIN_NAME.YIJIA.icon" />
      <icon v-if="INQUIRYBARGAIN_NAME.XUNDAN.id === showType" class="icon" :type="INQUIRYBARGAIN_NAME.XUNDAN.icon" />

      <!--
        <img
        v-if="INQUIRYBARGAIN_NAME.ALL.id === showType"
        src="https://oss.chengjie.red/web/imgs/inquiry/inquiry-bargain.png"
        alt=""
        class="inquiry-bargain-icon"
        >
      -->
      <!-- 悬浮固定样式 -->
      <template v-if="INQUIRYBARGAIN_NAME.ALL.id === showType">
        <!-- 深度完整版/海南版（议价和询单） -->
        <icon class="inquiry-bargain-icon hn" type="chengjie-Notice" />
      </template>

      <span v-if="INQUIRYBARGAIN_NAME.ALL.id !== showType" class="name">{{ INQUIRY_NAMES_VALUE_MAP[showType] }}</span>
      <el-badge
        v-if="count > 0"
        class="count-badge"
        :class="{
          more: count > 99
        }"
        :value="count > 99 ? '99+' : count"
      />
    </div>
    <InquiryBargainDialog
      v-if="showInquiryBargainDialog"
      :show-type="showType"
      :visible.sync="visible"
      @close-inquire-bargain-list-dialog="closeInquireBargainListDialog"
    />
  </div>
</template>

<script>
import InquiryBargainDialog from './inquiry-bargain-dialog.vue'
import { INQUIRY_NAMES_VALUE_MAP, INQUIRYBARGAIN_NAME } from '@/constants/inquiry-bargain'
import { mapGetters } from 'vuex'
export default {
  name: 'inquiry-bargain',
  components: {
    InquiryBargainDialog
  },
  props: {
    // 按钮样式类型 1-悬浮固定样式 3-右侧工具栏样式
    styleType: {
      type: Number,
      default: 1
    },
    // 类型：1-询单&议价 2-议价 3-询单
    showType: {
      type: Number,
      default: 1
    },
  },
  data() {
    return {
      showInquiryBargainDialog: true,
      visible: false,
      INQUIRY_NAMES_VALUE_MAP,
      INQUIRYBARGAIN_NAME
    }
  },
  computed: {
    ...mapGetters('market', {
      bargainingCount: 'bargainingCount',
      inquiryBargain: 'inquiryBargain'
    }),
    count() {
      let val = 0
      switch (this.showType) {
        case 2:
          val = this.bargainingCount
          break
        case 3:
          val = this.inquiryBargain.totalCount
          break
        case 1:
        default:
          val = this.bargainingCount + this.inquiryBargain.totalCount
          break
      }
      return val
    }
  },
  methods: {
    // 默认打开 弹窗
    handleShowInquireBargainListDialog() {
      this.showInquiryBargainDialog = true
      this.visible = true
    },
    closeInquireBargainListDialog() {
      this.showInquiryBargainDialog = false
    }
  },
}
</script>
