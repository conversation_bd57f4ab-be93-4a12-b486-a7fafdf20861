<!-- 承兑人选择框 -->
<style lang="scss" scoped>
.button {
  margin: 0 12px 0 0;
  padding: 10px 12px;

  ::v-deep > span {
    @include flex-cc;
  }

  .button-flex {
    @include flex-cc;

    width: 100%;
    line-height: normal;

    .icon {
      margin-right: 8px;
    }

    .text {
      @include ellipsis;
    }
  }

  &:last-child {
    margin: 0;
  }
}

.icon {
  font-size: 16px;
}

.accepter-select {
  position: relative;
}

.collapse-box {
  position: absolute;
  top: 52px;
  z-index: 99;
  overflow: hidden;
  border-radius: 2px;
  width: 280px;
  background: $color-FFFFFF;
  box-shadow: 0 9px 28px 8px rgb(0 0 0 / 5%), 0 6px 16px rgb(0 0 0 / 8%), 0 3px 6px -4px rgb(0 0 0 / 12%);

  .collapse-input {
    padding: 10px 10px 0;
  }

  .collapse-tips {
    @include flex-vc;

    position: relative;
    padding: 8px 10px;
    font-size: 12px;

    &::after {
      position: absolute;
      bottom: 0;
      border-bottom: 1px solid $color-D9D9D9;
      width: calc(100% - 20px);
      content: "";
    }

    .icon {
      margin-right: 4px;
      font-size: 14px;
      color: $font-color;
    }
  }

  .collapse-content {
    overflow-x: hidden;
    overflow-y: auto;
    padding: 4px 0;
    height: 288px;

    .collapse-item {
      @include flex-vc;

      padding: 0 12px;
      height: 36px;
      cursor: default;

      &:hover {
        background-color: $--color-primary-hover;
      }

      .ellipsis {
        @include ellipsis;

        width: 256px;
      }
    }
  }

  .collapse-empty {
    height: 288px;
    text-align: center;
    flex-direction: column;

    @include flex-cc;

    .icon-empty {
      height: 128px;
      font-size: 154px;

      @include flex-cc;
    }

    .empty-text {
      margin-top: 12px;
      color: $color-text-secondary;
      line-height: 20px;
    }
  }

  .collapse-footer {
    @include flex-vc;

    justify-content: flex-end;
    border-top: 1px solid $color-F0F0F0;
    padding: 8px 12px;
  }
}
</style>

<template>
  <div v-click-outside="{listen: () => handleVisible, handler: handleClose}" class="accepter-select">
    <el-button
      :type="buttonOptions.type || ''"
      :height="buttonOptions.height || '32'"
      :size="buttonOptions.size || ''"
      :border="buttonOptions.border || true"
      :disabled="buttonOptions.disabled || false"
      class="button"
      :style="{minWidth: buttonOptions.width ? `${buttonOptions.width}px` : '118px'}"
      @click="handleVisible"
    >
      <div class="button-flex">
        <icon class="icon" :type="buttonOptions.icon || 'sdicon-search'" />
        <span class="text" :style="{maxWidth: buttonOptions.textMaxWidth ? `${buttonOptions.textMaxWidth}px` : '112px'}">{{ buttonText }}</span>
      </div>
    </el-button>
    <el-collapse-transition>
      <div v-show="visible" class="collapse-box">
        <div class="collapse-input">
          <el-input
            ref="inputRef"
            v-model="input"
            :placeholder="inputPlaceholder"
            clearable
            @input="handleInput"
          />
        </div>
        <div class="collapse-tips">
          <icon type="sdicon-info-circle" class="icon" />
          支持输入多个，逗号隔开，可回车快捷{{ confirmText }}
        </div>
        <div
          v-if="list.length"
          ref="collapseContent"
          v-infinite-scroll="loadMore"
          infinite-scroll-immediate="false"
          class="collapse-content"
        >
          <div
            v-for="item in list"
            :key="item"
            class="collapse-item"
            @click="handleSelect(item)"
          >
            <el-tooltip
              show-when-overflow
              placement="top"
              :content="item"
              :open-delay="500"
              :label="item"
            >
              <span class="ellipsis">{{ item }}</span>
            </el-tooltip>
          </div>
        </div>
        <div v-else class="collapse-empty">
          <icon type="chengjie-ellipsis" class="icon-empty" />
          <div v-if="isInputMultiple" class="empty-text">当输入多个条件时<br>请直接点击{{ confirmText }}进行承兑人{{ confirmText }}</div>
          <div v-else class="empty-text">暂无匹配的数据<br>您可以点击{{ confirmText }}进行承兑人{{ confirmText }}</div>
        </div>
        <div class="collapse-footer">
          <el-button width="60" @click="handleClose">取消</el-button>
          <el-button
            type="primary"
            width="60"
            border
            :disabled="!input"
            @click="handleInput('')"
          >
            清空
          </el-button>
          <el-button
            type="primary"
            width="60"
            @click="handleChange"
          >
            {{ confirmText }}
          </el-button>
        </div>
      </div>
    </el-collapse-transition>
  </div>
</template>

<script>
import { mapMutations } from 'vuex'
import { debounce } from '@/common/js/util'
import marketApi from '@/apis/market'
import {
  MARKET_ENTER_EVENT, // 监听服务大厅回车事件
} from '@/event/modules/site' // 监听事件常量
// 默认的承兑人名称
const DEFAULT_ACCEPTER = Object.freeze(['中国银行', '中国工商银行', '中国建设银行', '中国农业银行', '中国邮政储蓄银行', '中信银行', '平安银行', '华夏银行', '招商银行', '广发银行', '光大银行', '北京银行', '上海银行', '上海浦东发展银行', '无锡农村商业银行', '浙商银行', '杭州银行', '江苏银行'])

export default {
  name: 'accepter-select',
  props: {
    // 按钮配置项
    buttonOptions: {
      type: Object,
      default: () => ({})
    },
    // 输入框占位文字
    inputPlaceholder: String,
    // 确定选择文字
    confirmText: {
      type: String,
      default: '搜索'
    },
    // 默认选中值
    defaultValue: String,
    // 同级其他数据，用于重复数据判断
    otherValue: String
  },

  data() {
    return {
      visible: false, // 显示下拉框
      input: '', // 输入的内容
      list: DEFAULT_ACCEPTER, // 下拉框列表
      // 点击了确认保存一下数据，用于回显
      confirm: {
        input: '',
        list: []
      },
      allData: [], // 请求回来的前端数据，用于做前端分页
      pageNum: 1, // 当前页码
      pageSize: 20, // 每页多少条
    }
  },

  computed: {
    // 是不是输入多个值
    isInputMultiple() {
      return /[，]/g.test(this.input)
    },
    // 按钮显示文字
    buttonText() {
      return (this.defaultValue && this.defaultValue.replace(/[,]/g, '，')) || this.buttonOptions.text || '搜索承兑人'
    }
  },

  created() {
    this.debounceInput = debounce(val => {
      this.allData = []
      // 输入为空时
      if (!val) {
        this.list = [...DEFAULT_ACCEPTER]
        this.$emit('change', '')
        return
      }
      if (this.isInputMultiple) {
        this.list = []
      } else {
        this.getAccepterList(val)
      }
    })
    // 搜索框输入
    this.handleInput = val => {
      this.input = val.replace(/[\s|,]/g, '，')
      this.debounceInput(this.input)
    }
  },

  mounted() {
    // 监听服务大厅回车事件
    this.$event.on(MARKET_ENTER_EVENT, () => {
      this.visible && this.handleChange()
    })
  },

  methods: {
    ...mapMutations('market', {
      setAccepterSelectVisible: 'setAccepterSelectVisible',
    }),
    // 显示、隐藏下拉框
    handleVisible() {
      if (!this.visible) {
        this.$refs.collapseContent && (this.$refs.collapseContent.scrollTop = 0)
        if (this.defaultValue) {
          this.input = this.defaultValue.replace(/[,]/g, '，')
          if (this.isInputMultiple) {
            this.list = []
          } else {
            this.getAccepterList(this.input)
          }
        } else {
          this.input = ''
          this.list = [...DEFAULT_ACCEPTER]
        }
        this.allData = []
        // 延迟聚焦
        setTimeout(() => {
          this.$refs.inputRef && this.$refs.inputRef.focus()
        }, 100)
      }
      this.visible = !this.visible

      this.setAccepterSelectVisible(this.visible)
    },

    // 隐藏下拉框
    handleClose() {
      if (this.visible) {
        this.visible = false
        this.handleClearData()
        this.setAccepterSelectVisible(this.visible)
      }
    },

    // 获取承兑人名称
    async getAccepterList(val) {
      this.list = []
      this.allData = []
      this.$refs.collapseContent && (this.$refs.collapseContent.scrollTop = 0)
      const data = await marketApi.getAccepterList({ acceptorName: val })
      if (data && Array.isArray(data)) {
        this.list = data.slice(0, this.pageSize)
        this.allData = data
        this.pageNum = 1
      }
    },

    // 滚动加载更多
    loadMore() {
      if (this.allData.length > this.list.length) {
        this.pageNum++
        this.list = this.allData.slice(0, this.pageSize * this.pageNum)
      }
    },

    // 点击选择
    handleSelect(val) {
      this.input = val
      this.getAccepterList(val)
    },

    // 清空数据
    handleClearData() {
      this.input = ''
    },

    // 点击确定操作
    handleChange() {
      if (this.acceptCheck()) {
        this.$emit('change', this.input.replace(/[，]/g, ','))
      }
      this.handleClose()
    },

    // 校验承兑人
    acceptCheck() {
      let flag = true

      if (this.otherValue && this.input) {
        const inputArr = Array.from(new Set(this.input.replace(/[，]/g, ',').split(',')))
        const otherValueArr = this.otherValue.split(',')
        let repeatKeyWord = ''
        inputArr.forEach(item => {
          if (otherValueArr.indexOf(item) > -1) {
            repeatKeyWord = repeatKeyWord ? `${repeatKeyWord}，${item}` : item
          }
        })
        if (repeatKeyWord) {
          this.$confirm(`搜索承兑人与排除承兑人存在冲突，冲突内容为“${repeatKeyWord}”，将无法筛选！`, '提示', {
            confirmButtonText: '确认',
            type: 'warning',
            titleHasIcon: true,
            showClose: false,
            showCancelButton: false,
            dangerouslyUseHTMLString: true
          })
          flag = false
        }
      }
      return flag
    },
  }
}
</script>
