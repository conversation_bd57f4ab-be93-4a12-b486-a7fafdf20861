<!-- 发布失败-账户状态异常-弹窗 -->
<style lang="scss" scoped>
.pay-account-error {
  ::v-deep {
    .el-dialog__body {
      padding: 0 33px;
      color: $color-text-primary;
      background-color: $color-FFFFFF;
    }

    .el-dialog__header {
      border-bottom: none;
      height: 32px;
    }

    .el-dialog__footer {
      color: $color-text-primary;
      background-color: $color-FFFFFF;
    }

    .el-input__inner {
      font-size: 14px;
      text-align: right;
    }
  }
}

.title {
  @include flex-vc;

  margin-bottom: 8px;
  font-size: 18px;
  font-weight: bold;
}

.icon {
  margin-right: 16px;
  width: 20px;
  height: 20px;

  &.both {
    color: $--color-warning;
  }
}

.content {
  padding-left: 35px;
  font-size: 16px;

  .tips {
    margin-bottom: 16px;
  }

  .tips-item {
    position: relative;
    margin-bottom: 8px;
  }

  .tips-items {
    line-height: 24px;
  }

  .go-to-web {
    @include example-underline;
  }
}
</style>

<template>
  <el-dialog
    :visible.sync="dialogVisible"
    width="490px"
    :destroy-on-close="true"
    :show-close="false"
    append-to-body
    class="pay-account-error"
  >
    <header class="title">
      <icon type="chengjie-exclamation-circle" class="icon both" />
      提示
    </header>
    <main class="content">
      <div class="tips">
        当前{{ payType }}的账号状态异常，可按如下操作恢复正常
      </div>
      <div class="tips-item">
        <div class="tips-items">
          1.点击
          <span class="go-to-web" @click="toWeb(0)">用户中心-更新认证</span>
          ，主动更新实名认证并上传最新证件或所有受益人；
        </div>
      </div>
      <!--
        <div class="tips-item">
        <div class="tips-items">
        2.点击
        <span class="go-to-web" @click="toWeb(1)">票方设置-发布默认设置</span>
        ，取消{{ payType }}的默认发布设置；
        </div>
        </div>
      -->
      <div>2.其他异常情况请联系您的客服经理，由客服经理帮您处理。</div>
    </main>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleConfirm">我知道了</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { openWindow } from '@/common/js/util'

export default {
  name: 'pay-account-error-dialog',

  data() {
    return {
      dialogVisible: false, // 是否打开弹窗
      payType: '支付渠道', // 支付方式
    }
  },

  methods: {
    // 初始化
    init(data) {
      this.payType = data || '支付渠道'
      this.dialogVisible = true
    },

    // 点击前往
    toWeb(type) {
      let url = ['/user-center/info', '/user-center/setting']
      openWindow(url[type])
    },

    // 点击我知道了
    handleConfirm() {
      this.dialogVisible = false
    },
  },

}
</script>
