<!-- 大厅工具栏中 客户反馈弹窗 暂无 -->
<style lang="scss" scoped>
::v-deep {
  .el-form-item {
    margin-bottom: 12px;
  }
}

.manager-info-box {
  padding: 16px;
  background: $color-FFFFFF;
}

.qrcode-detail {
  display: flex;
  margin-top: 12px;
  height: 95px;
}

.qrcode {
  margin-right: 24px;
  width: 95px;
}

.detail {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;// 两端对齐
}

.name-phone {
  display: flex;
  height: 24px;

  .icon {
    margin-right: 8px;
    font-size: 24px;
    color: $--color-primary;
  }

  .name {
    margin-right: 50px;

    &::after {
      position: relative;
      bottom: 1px;
      left: 24px;
      content: "|";
      color: $color-text-light;
    }
  }

  .label {
    font-size: 16px;
    font-weight: 600;
    color: $color-text-primary;
    line-height: 24px;
  }
}

.tip {
  height: 39px;
}

.tip-title {
  margin-bottom: 2px;
  font-size: 14px;
  color: $color-text-primary;
  line-height: 20px;
}

.tip-desc {
  font-size: 12px;
  color: $color-text-secondary;
  line-height: 17px;
}

.form {
  margin-top: 12px;
  padding: 16px;
  background: $color-FFFFFF;
}

.satisfaction-label {
  font-size: 14px;
  line-height: 22px;
  color: $color-text-secondary;
}

.red-font {
  font-weight: 600;
  color: $color-warning;
}

.item-title {
  color: $color-text-light;
}

.evaluation-title {
  margin-bottom: 12px;
}

.code {
  display: inline-block;
  width: 95px;
}

.text-tip {
  font-size: 12px;
  text-align: center;
}
</style>

<template>
  <el-dialog
    title="客户反馈"
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose"
  >
    <!-- 客户经理信息start -->
    <div class="manager-info-box">
      <div class="g-title-small">客户经理</div>
      <div class="qrcode-detail">
        <div class="qrcode">
          <img class="code" :src="customerWechatContactImg || nodeCode" alt="二维码">
          <div v-if="!customerWechatContactImg" class="text-tip">暂无二维码</div>
        </div>
        <div class="detail">
          <div class="name-phone">
            <icon
              class="icon"
              type="chengjie-service"
            />
            <span class="label name">{{ customerManagerNickName || "" }}</span>
          </div>

          <div v-if="customerManagerEmployeeName" class="tip">
            <p class="tip-title">扫一扫添加客户经理微信</p>
            <p class="tip-desc">有任何问题都可联系您的客户经理，如对客户经理不满意可要求更换</p>
          </div>
          <div v-else class="tip">
            <p class="tip-title">当前暂无客户经理</p>
            <p class="tip-desc">您可联系桑经理，为您匹配专属客户经理</p>
          </div>
        </div>
      </div>
    </div>
    <!-- 客户经理信息end -->

    <!-- 评价表单start -->
    <el-form
      ref="form"
      class="form"
      :model="form"
      :rules="rules"
    >
      <div class="g-title-small evaluation-title">服务评价</div>
      <el-form-item prop="serviceAttitudeScore">
        <SatisfactionSelect v-model="form.serviceAttitudeScore">
          <div slot="label" class="satisfaction-label"> 您对客户经理的<span class="red-font"> 服务态度 </span>是否满意？ </div>
        </SatisfactionSelect>
      </el-form-item>
      <el-form-item prop="responseSpeedScore">
        <SatisfactionSelect v-model="form.responseSpeedScore">
          <div slot="label" class="satisfaction-label"> 您对客户经理的<span class="red-font"> 响应态度 </span>是否满意？ </div>
        </SatisfactionSelect>
      </el-form-item>
      <el-form-item prop="problemSloveScore">
        <SatisfactionSelect v-model="form.problemSloveScore">
          <div slot="label" class="satisfaction-label"> 您对客户经理的<span class="red-font"> 解决问题的能力 </span>是否满意？ </div>
        </SatisfactionSelect>
      </el-form-item>
      <el-form-item prop="advise">
        <div class="item-title">
          建议反馈
        </div>
        <el-input
          v-model="form.advise"
          type="textarea"
          :rows="3"
          maxlength="120"
          placeholder="请输入您对平台的建议反馈，120字以内"
        />
      </el-form-item>
      <el-form-item prop="contractInformation">
        <div class="item-title">
          联系方式
        </div>
        <el-input
          v-model="form.contractInformation"
          type="number"
          :number-format="{ maxLength: 11, decimal: false, negative: false}"
          placeholder="留下您的联系方式，便于我们及时响应您的需求"
        />
      </el-form-item>
    </el-form>
    <!-- 评价表单end -->

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="submit">提交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import SatisfactionSelect from './components/satisfaction-select/satisfaction-select.vue'
import userApi from '@/apis/user'
import {
  TOOLBAR_REFRESH, // 刷新消息中心列表事件
} from '@/event/modules/site'
const nodeCode = 'https://oss.chengjie.red/web/imgs/user-center/none-code.png'
export default {
  name: 'customer-feedback',
  components: { SatisfactionSelect },
  data() {
    const checkPhone = (rule, value, callback) => {
      if (value) { // 判断当输入的有值时，才校验
        if (!(/^[1][3-9][0-9]{9}$/.test(value))) {
          callback(new Error('手机号格式不正确'))
        } else {
          callback() // 注意这里要回调一下，不然提交不了
        }
      } else {
        callback()
      }
    }
    return {
      nodeCode,
      customerManagerNickName: '', // 用户昵称
      defaultManager: this.configDefault, // 默认经理
      dialogVisible: false,
      customerManagerEmployeeName: '', // 客维经理员工名称
      customerManagerMobile: '', // 客维经理手机号码
      customerWechatContactImg: '', // url
      form: {
        serviceAttitudeScore: 5, // 服务态度分数，5-非常满意，4-满意，3-一般，2-不满意，1-非常不满意，非空字段
        responseSpeedScore: 5, // 响应速度分数，5-非常满意，4-满意，3-一般，2-不满意，1-非常不满意，非空字段
        problemSloveScore: 5, // 解决问题能力分数，5-非常满意，4-满意，3-一般，2-不满意，1-非常不满意，非空字段
        advise: '', // 建议反馈
        contractInformation: '', // 联系方式
      },
      rules: { // 表单校验规则
        contractInformation: [{ required: false, validator: checkPhone, trigger: 'blur' }]
      }
    }
  },

  methods: {
    async init() {
      await this.getCorpInfo()
      this.dialogVisible = true
    },

    handleClose() {
      this.$refs.form && this.$refs.form.resetFields()
      this.$event.emit(TOOLBAR_REFRESH)
      this.dialogVisible = false
    },

    // 获取用户信息接口
    async getCorpInfo() {
      const res = await userApi.getCorpInfo()
      this.customerManagerEmployeeName = res?.customerManagerEmployeeName
      this.customerManagerMobile = res?.customerManagerMobile
      this.customerWechatContactImg = res?.customerWechatContactImg || ''
      this.customerManagerNickName = res?.customerManagerNickName
    },

    // 反馈接口
    async postCorpFeedback() {
      try {
        await userApi.postCorpFeedback(this.form)
        this.$message.success('提交成功')
        this.handleClose()
      } catch (error) {
        this.$message.warning(error.data.msg)
      }
    },

    async submit() {
      try {
        await this.$refs.form.validate()
        this.postCorpFeedback()
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log(error)
      }
    }
  }
}
</script>
