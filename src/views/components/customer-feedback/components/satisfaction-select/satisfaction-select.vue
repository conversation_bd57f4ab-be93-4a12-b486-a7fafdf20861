<!-- 满意度选择 -->
<style lang="scss" scoped>
.check-box {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  padding: 0 16px;
}

.item {
  display: flex;
  align-items: center;
  width: 62px;
  font-size: 12px;
  color: $color-text-primary;
  flex-direction: column;
  line-height: 18px;
  opacity: .4;

  .item-img {
    margin-bottom: 4px;
    width: 32px;
  }
}

.active {
  opacity: 1;
}
</style>

<template>
  <div v-if="satisfactionList">
    <slot name="label" />
    <div class="check-box">
      <div
        v-for="item in satisfactionList"
        :key="item.key"
        :class="['item', {active: currCheck === item.key}]"
        :lable="item.value"
        @click.stop="handleChange(item.key)"
      >
        <img :src="item.img" class="item-img">
        <div>{{ item.value }}</div>
      </div>
    </div>
  </div>
</template>

<script>
const satisfactionList = [
  // 非常满意
  {
    value: '非常满意',
    img: 'https://oss.chengjie.red/web/imgs/satisfaction/satisfied.png',
    key: 5,
  },
  // 满意
  {
    value: '满意',
    img: 'https://oss.chengjie.red/web/imgs/satisfaction/good.png',
    key: 4,
  },
  // 一般
  {
    value: '一般',
    img: 'https://oss.chengjie.red/web/imgs/satisfaction/commonly.png',
    key: 3,
  },
  // 不满意
  {
    value: '不满意',
    img: 'https://oss.chengjie.red/web/imgs/satisfaction/dissatisfaction.png',
    key: 2,
  },
  // 非常不满意
  {
    value: '非常不满意',
    img: 'https://oss.chengjie.red/web/imgs/satisfaction/anger.png',
    key: 1,
  },
]
export default {
  name: 'satisfaction-select',
  model: {
    prop: 'currCheck',
    event: 'click'
  },
  props: {
    currCheck: Number,
  },

  data() {
    return {
      satisfactionList
    }
  },
  methods: {
    handleChange(key) {
      this.$emit('click', key)
    }
  }
}
</script>
