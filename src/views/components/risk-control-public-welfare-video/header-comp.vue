<style lang="scss" scoped>
.count {
  margin-right: 20px;
  color: #F18A54;

  .text {
    cursor: pointer;
    font-size: 12px;
    line-height: 18px;
    font-weight: 500;

    &:hover {
      border-bottom: 1px solid #F18A54;
    }
  }

  .icon {
    margin-left: 2px;
    height: 20px;
    vertical-align: top;
  }
}
</style>

<template>
  <div class="count" @click="open">
    <span class="text">风险知识</span>
    <img class="icon" src="https://oss.chengjie.red/web/imgs/public/risk-control-video.png">
  </div>
</template>

<script>
import { OPEN_RISK_CONTROL_PUBLIC_WELFARE_VIDEO } from '@/event/modules/site'

export default {
  name: 'risk-control-public-welfare-video-in-header',
  methods: {
    open() {
      this.$event.emit(OPEN_RISK_CONTROL_PUBLIC_WELFARE_VIDEO, true)
    }
  },
}
</script>
