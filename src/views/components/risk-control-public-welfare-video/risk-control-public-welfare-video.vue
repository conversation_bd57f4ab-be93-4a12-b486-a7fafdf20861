<style lang="scss" scoped>
.text-center {
  position: absolute;
  top: 12px;
  left: 150px;

  .red {
    font-weight: 500;
    color: #F5222D;
  }
}

.vd {
  width: 100%;
}

.content {
  display: flex;

  .left {
    padding: 10px 4px;
    flex-shrink: 0;
    width: 108px;
    background: #FFFFFF;

    .btn {
      cursor: pointer;
      padding: 8px;
      font-size: 14px;
      font-weight: 600;
    }

    .check {
      border-radius: 4px;
      color: #FFFFFF;
      background: #5287D6;
    }
  }

  .right {
    padding: 20px;
    width: 100%;

    .box {
      display: flex;
      align-items: center;
    }

    .box-wrap {
      margin-left: 12px;
      padding: 12px;
      width: 450px;
      height: 430px;
      flex-shrink: 0;
      background: #FFFFFFFF;

      .title {
        font-size: 14px;
        font-weight: 600;
        line-height: 24px;
        text-align: center;
        color: #333333;
      }

      .item {
        display: flex;
        align-items: center;
        padding-bottom: 8px;

        .num {
          flex-shrink: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 50%;
          width: 22px;
          height: 22px;
          font-size: 12px;
          font-weight: 500;
          text-align: center;
          color: #FFFFFFFF;
          background: linear-gradient(180deg, #3697F0 -3%, #0076F6 100%);
        }

        .txt {
          line-height: 26px;
          display: inline-block;
          margin-left: 4px;
          font-size: 14px;
          font-weight: 500;
          color: #333333;
        }
      }
    }
  }
}

::v-deep {
  .el-dialog__footer {
    text-align: center;
  }
}
</style>

<style>
  .risk-dialog .el-dialog .el-dialog__body {
    padding: 0 !important;
  }

  .risk-dialog .el-dialog {
    top: 50% !important;
    margin-top: 0 !important;
    transform: translateY(-50%) !important;
  }
</style>

<template>
  <el-dialog
    class="risk-dialog"
    title="风险户视频/特征"
    :visible.sync="visible"
    :show-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    width="1100px"
    @open="onOpen"
    @close="onClose"
  >
    <div class="content">
      <!--
        <div class="left">
        <div :class="['btn', (type === 1 && 'check') || '']" @click="() => { type = 1 }">风险防范视频</div>
        <div :class="['btn', (type === 2 && 'check') || '']" @click="() => { type = 2;$refs.vd.pause() }">风险户特征</div>
        </div>
      -->
      <div class="right">
        <!--
          <div v-if="!hasFlag" class="text-center">
          <span>点击播放观看视频后，可手动关闭</span>
          <span v-if="countdown > 0">（还需观看<span class="red">{{ countdown }}s</span>）</span>
          </div>
        -->
        <div class="box">
          <div>
            <video
              ref="vd"
              class="vd"
              src="https://oss.chengjie.red/web/video/承接ERP风险视频.mp4"
              controls
              :height="'100%'"
              @play="onVdPlay"
              @pause="onVdPause"
            />
          </div>
          <div class="box-wrap">
            <div class="title">风险户特征</div>
            <div v-for="(item, index) in info" :key="item" class="item">
              <span class="num">{{ index + 1 }}</span><span class="txt">{{ item }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button type="primary" @click="visible = false">我知道了</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { RISK_CONTROL_PUBLIC_WELFARE_VIDEO_PLAYED, RISK_CONTROL_PUBLIC_WELFARE_VIDEO_PLAYED_DATE } from '@/constant-storage'
import { OPEN_RISK_CONTROL_PUBLIC_WELFARE_VIDEO } from '@/event/modules/site'
import Storage from '@/common/js/storage'
import { formatTime } from '@/common/js/date'
import { mapState } from 'vuex'

export default {
  name: 'risk-control-public-welfare-video',
  data() {
    return {
      visible: false,
      hasFlag: !!Storage.get(RISK_CONTROL_PUBLIC_WELFARE_VIDEO_PLAYED),
      hasDateFlag: Storage.get(RISK_CONTROL_PUBLIC_WELFARE_VIDEO_PLAYED_DATE) || null,
      countdown: 60,
      interval: null,
      timer: null,
      // type: 1, // 1=>风险防范视频 2=>风险户特征
      info: [
        'AI助手核户提示“未在软件注册”',
        '主动添加公司业务员微信，刚添加或近期添加后一直未联系过，而且朋友圈无任何动态记录',
        '陌生人打电话表示有用票需求，寻求合作',
        '同行、朋友、亲戚、客户转介绍，背书打款全程通过中间人交易',
        '自称是终端企业，无法提供仓库门头、发票合同、出入库清单、纳税记录',
        '通过抖音、小红书、快手等热门聊天软件咨询交易',
        '自称终端且先打款，利润虚高；购买金额大，动辄上百万',
        '没有过多沟通，打款速度快，交易速度快',
        '公司成立时间不长，在半年以内',
        '企业工商信息近6个月有变更',
      ]
    }
  },
  computed: {
    ...mapState('user', ['isLogined']),
    showClose() {
      return this.countdown <= 0 || (this.hasFlag)
    },
  },
  watch: {
    // 监听登录状态
    isLogined: {
      handler(val) {
        if (!val) {
          clearInterval(this.timer)
          this.timer = null
        } else {
          this.showDialog()
        }
      },
      immediate: true
    }
  },
  created() {
    this.$event.on(OPEN_RISK_CONTROL_PUBLIC_WELFARE_VIDEO, force => {
      // 获取当前页面的URL  如果是 小承助手 页面不展示
      let currentUrl = window.location.href
      if (currentUrl.includes('/chatai')) {
        return
      }
      this.getParams() // 获取Storage配置数据
      if (force) {
        this.visible = true
      } else if (this.hasFlag && this.hasDateFlag === formatTime(Date.now(), 'YYYY-MM-DD')) {
        // nothing to do
      } else {
        this.visible = true
      }
    })
  },
  methods: {
    formatTime,
    onVdPlay() {
      if (this.hasFlag) return
      this.interval = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          clearInterval(this.interval)
          this.interval = null
        }
      }, 1000)
    },
    onVdPause() {
      if (!this.interval) return
      clearInterval(this.interval)
    },
    onOpen() {
      this.getParams() // 获取Storage配置数据
    },
    getParams() {
      this.hasFlag = !!Storage.get(RISK_CONTROL_PUBLIC_WELFARE_VIDEO_PLAYED)
      this.hasDateFlag = Storage.get(RISK_CONTROL_PUBLIC_WELFARE_VIDEO_PLAYED_DATE)
    },
    onClose() {
      this.$refs.vd.currentTime = 0
      this.$refs.vd.pause()
      Storage.set(RISK_CONTROL_PUBLIC_WELFARE_VIDEO_PLAYED, true)
      Storage.set(RISK_CONTROL_PUBLIC_WELFARE_VIDEO_PLAYED_DATE, formatTime(Date.now(), 'YYYY-MM-DD')) // 时间标记每日弹出
    },
    // 风险户视频观看提示
    showDialog() {
      this.timer = setInterval(() => {
        const currentTime = new Date()
        const hours = currentTime.getHours()
        const minutes = currentTime.getMinutes()
        const popupTimes = [8, 12, 17] // 固定时间点弹出
        if (popupTimes.includes(hours) && minutes === 0) {
          this.visible = true
          // 设置显示强制观看
          this.$nextTick().then(() => {
            this.hasFlag = false
            if (!this.countdown) {
              this.countdown = 60
              this.$refs.vd.currentTime = 0
              this.$refs.vd.pause()
            }
          })
        }
      }, 60000) // 每分钟检查
    }
  },
}
</script>
