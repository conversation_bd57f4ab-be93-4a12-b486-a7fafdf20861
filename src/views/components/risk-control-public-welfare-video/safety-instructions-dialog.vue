<style lang="scss" scoped>
.safety-dialog {
  ::v-deep {
    color: $color-text-primary;

    .el-dialog .el-dialog__header {
      justify-content: center;
      background: $button-background;
    }

    .el-dialog .el-dialog__title {
      color: $color-FFFFFF;
    }
  }
}

.content-container {
  padding: 16px;
  font-size: 14px;
  font-weight: 400;
  background: $color-FFFFFF;

  .line-style {
    margin-top: 8px;
  }
}

.btn-style {
  justify-content: center;
  margin-top: 12px;
  margin-left: 180px;
}

.btn-accent {
  color: $color-FFFFFF;
  background: $button-background;
}
</style>

<template>
  <el-dialog
    class="safety-dialog"
    title="软件安全规则须知"
    :visible.sync="visible"
    :center="true"
    :show-close="false"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @open="onOpen"
  >
    <div class="content-container">
      <div>一、被政府机关或金融机构冻结或曾经被冻结过，经软件核实仍存在风险的用户及其关联同一实控人下所有用户；软件有权要求司法冻结客户无条件提供网银流水信息及票据背书信息；</div>
      <div class="line-style">二、软件通过多种途径发现客户交易时未遵守同名户划转规则，如发现出现过如下情形，包括不限于回款户与背书户不一致、回款至个人户的，则视为非同名户划转；</div>
      <div class="line-style">三、用户发布票据背书链中，匹配到与软件异常准入名单有直接交易的情形；</div>
      <div class="line-style">四、软件用户公司信息出现巨大变化，如企业近6个月内出现法人、股东变更等，且未与客户经理进行报备；</div>
      <div class="line-style">五、企业与近6个月内出现法人、股东变更或注册时间少于6个月的企业之间存在大量异常交易；</div>
      <div class="line-style">六、企业账户近1个月内出现多个大量交易的IP归属城市地址；</div>
      <div class="line-style">七、软件发现客户账户出现大额集中交易、分散转入-集中转出、集中转入-分散转出、沉默账户突发交易等异常情况；</div>
      <div style="margin-top: 12px;"><span style=" font-weight: 600; color: #F51818;">软件会基于上述规则触发情况确定是否对客户账户进行全面封控，同时对应客户基本信息将在软件首页进行风险公示。</span><span>相关规则解释权归软件所有。</span></div>
    </div>

    <el-button
      :class="['btn-style', showClose ? 'btn-accent' : '']"
      width="200"
      height="40"
      :disabled="!showClose"
      @click="onClose"
    >
      我已阅读并同意{{ !showClose ? `（${countdown}S）` : '' }}
    </el-button>
  </el-dialog>
</template>

<script>
import { SAFETY_INSTRUCTIONS_DIALOG_FLAG } from '@/constant-storage'
import { OPEN_RISK_CONTROL_PUBLIC_WELFARE_VIDEO, OPEN_SAFETY_INSTRUCTIONS_DIALOG } from '@/event/modules/site'
import Storage from '@/common/js/storage'

export default {
  name: 'safety-instructions-dialog',
  data() {
    return {
      visible: false,
      countdown: 30,
      interval: null
    }
  },
  computed: {
    showClose() {
      return this.countdown <= 0
    }
  },
  created() {
    this.$event.on(OPEN_SAFETY_INSTRUCTIONS_DIALOG, () => {
      let flag = Storage.get(SAFETY_INSTRUCTIONS_DIALOG_FLAG)
      if (flag) {
        this.$event.emit(OPEN_RISK_CONTROL_PUBLIC_WELFARE_VIDEO)
        return
      }
      this.visible = true
    })
  },
  methods: {
    onOpen() {
      this.countdown = 30
      this.interval = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          clearInterval(this.interval)
          this.interval = null
        }
      }, 1000)
    },
    onClose() {
      this.visible = false
      clearInterval(this.interval)
      this.interval = null
      Storage.set(SAFETY_INSTRUCTIONS_DIALOG_FLAG, true)
      this.$event.emit(OPEN_RISK_CONTROL_PUBLIC_WELFARE_VIDEO)
    }
  },
}
</script>
