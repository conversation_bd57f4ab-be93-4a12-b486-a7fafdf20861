// 通知组件
<style lang="scss" scoped>
.header-notice {
  margin-bottom: 12px;
  background: linear-gradient(180deg, #F8CFCB, #F8D4D0 50%, #FAE6E1 100%);

  .header-content {
    position: relative;
    display: flex;

    .icon-notice {
      font-size: 20px;
      color: $--color-primary;
    }

    .notice-text {
      display: flex;
      margin: 14px 0;
      font-weight: 500;
    }
  }
}

.notice-pointer {
  :hover {
    cursor: pointer;
  }
}

.logo {
  position: absolute;
  right: 0;
  bottom: 0;
}
</style>

<template>
  <!-- showNotice  domNotice  linkNotice 在user-center.js 中维护 -->
  <div v-if="$route.meta.showNotice && couponOpen" class="header-notice" @click="goCerter">
    <div
      class="header-content"
      :class="$route.meta.linkNotice && 'notice-pointer'"
    >
      <img src="https://oss.chengjie.red/web/imgs/new_erp/images/notice-left.png" alt="">
      <span class="notice-text">
        <slot name="text">
          <!-- eslint-disable vue/no-v-html -->
          <div v-html="$route.meta.domNotice" />
          <icon v-if="$route.meta.linkNotice" type="header-notice-logo" size="20" />
        </slot>
      </span>
      <img class="logo" src="https://oss.chengjie.red/web/imgs/new_erp/images/notice-rigth-new.png" alt="">
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: 'header-notice',
  computed: {
    ...mapGetters('user', {
      couponOpen: 'couponOpen', // 优惠券活动开关，1开启，0关闭
    }),

  },

  methods: {
    goCerter() {
      if (this.$route.meta.showNotice && this.$route.meta.linkNotice) {
        this.$router.push(this.$route.meta.linkNotice)
      }
    }
  }
}
</script>
