<!-- 客服信息组件 -->
<style lang="scss" scoped>
.no-channel {
  display: flex;
  padding: 16px;
  background: $color-FFFFFF;

  .qr-code-box {
    margin-right: 24px;
    width: 164px;
    font-size: 0;
    text-align: center;

    .node-code {
      margin: 0 auto;
      margin-bottom: 12px;
      width: 140px;
      height: 118px;
      background-size: 100% 100%;
      background-image: url("https://oss.chengjie.red/web/imgs/user-center/none-code.png");
    }

    .code {
      width: 100%;
    }
  }

  .no-channel-right {
    font-size: 16px;

    .channel-tips {
      margin: 32px 0 56px;
      font-weight: 600;
    }

    .name {
      margin-right: 8px;
    }
  }
}

.bold-font {
  font-size: 16px;
  font-weight: 600;
  color: $color-text-primary;
}

.gray-font {
  color: $color-text-secondary;
}

.qr-code {
  width: 164px;
}
</style>

<template>
  <div class="no-channel">
    <div class="qr-code-box">
      <img
        v-if="corpInfo && corpInfo.customerWechatContactImg && corpInfo.customerManagerEmployeeName"
        class="code"
        :src="corpInfo.customerWechatContactImg"
        alt="二维码"
      >
      <!-- 有客服-没二维码-显示空二维码样式 -->
      <template v-else-if="corpInfo.customerManagerEmployeeName && !corpInfo.customerWechatContactImg">
        <div class="node-code" />
        <div class="text-tip">暂无二维码</div>
      </template>
      <!-- 没客服的显示默认客服 -->
      <img v-else :src="configDefault.customerManagerQr1" class="qr-code">
    </div>
    <div class="no-channel-right">
      <div class="channel-tips">支付渠道的交易权限已被禁用，请联系客服重新启用</div>
      <div v-if="corpInfo">
        <template v-if="false">
          <!-- 深度展示 -->
          <span class="gray-font">联系方式：</span>
          <span class="bold-font name">{{ corpInfo.customerManagerEmployeeName }}</span>
          <span class="bold-font">{{ corpInfo.customerManagerMobile }}</span>
          <Copy :content="corpInfo.customerManagerEmployeeName + '-' + corpInfo.customerManagerMobile" />
        </template>
        <template v-else>
          <!-- 海南展示 -->
          <span class="gray-font">客户经理：</span>
          <span class="bold-font name">{{ corpInfo.customerManagerNickName || '--' }}</span>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import Copy from '@/views/components/common/copy/copy.vue' // 复制组件

export default {

  name: 'customer-service-detail',
  components: {
    Copy,
  },

  data() {
    // 默认客服信息
    return {
      defaultManager: this.configDefault, // 默认经理
    }
  },
  computed: {
    // 企业信息
    corpInfo() {
      return this.$store.state?.user?.corpInfo
    },
  },

  methods: {

  },
}
</script>
