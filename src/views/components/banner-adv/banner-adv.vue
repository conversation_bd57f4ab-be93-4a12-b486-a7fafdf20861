<!-- 广告横幅 -->
<style lang="scss" scoped>
.banner-tips {
  display: flex;
  align-items: center;
  border-radius: 2px;
  padding: 0 16px;
  height: 36px;
  background: $color-E6F3F3;

  .banner-tips-left {
    display: flex;
    align-items: center;
    margin-right: 10px;
  }

  .icon-tips {
    margin-right: 10px;
    font-size: 16px;
    color: $font-color;
  }

  .el-button {
    color: $font-color;
  }

  .content-text {
    margin-right: 10px;
    margin-left: 40px;
  }
}
</style>

<template>
  <!-- // 0616 紧急隐藏 -->
  <div v-if="isShow" class="banner-tips">
    <div class="banner-tips-left">
      <i v-if="isShowIcon" class="elicon elicon-info-circle icon-tips" />
      {{ majorText }}
    </div>
    <el-button v-if="isShowBtn" type="text" @click="openUrl(1)">{{ btn1 }}</el-button>
    <span v-if="text" class="content-text">{{ text1 }}</span>
    <el-button v-if="btn2" type="text" @click="openUrl(2)">{{ btn2 }}</el-button>
  </div>
</template>

<script>
export default {
  name: 'banner-adv',
  props: {
    text: { // 广告文案
      type: String,
      default: ''
    },
    isShowIcon: { // 是否展示前面的icon
      type: Boolean,
      default: true
    },
    isShowBtn: { // 是否展示按钮
      type: Boolean,
      default: true
    },
    btn1: {
      type: String,
      default: '领取奖励',
    },
    url1: { // 按钮1跳转连接
      type: String,
      default: '/fast-radar-act'
    },
    text1: { // 文案2
      type: String,
      default: ''
    },
    btn2: { // 文案按钮2
      type: String,
      default: ''
    },
    url2: { // 文案按钮2
      type: String,
      default: ''
    },
    isApi: { // 是否需要调用接口展示用户数据
      type: Boolean,
      default: true
    },
    isShow: { // 零时添加的  后期删除
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      majorText: ''
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    async init() {
      if (!this.isApi) {
        this.majorText = this.text || `${this.discernName}挂单天天领现金，每日最高400元，今日成交0单，已获得0元！`
        return
      }
      const data = await this.$store.dispatch('activity/getActivityCountAndAmt')
      const { todayNum, todayReward } = data
      this.majorText = `${this.discernName}挂单天天领现金，每日最高400元，今日成交${todayNum}单，已获得${todayReward}元！`
    },

    // 跳转url  type: 1:按钮1  2：按钮2
    openUrl(type) {
      let url = `${window.location.origin}${type === 1 ? this.url1 : this.url2}`
      if (type !== 1 && !this.url2) return
      window.open(url, '_blank')
    }
  }
}
</script>
