<style lang="scss" scoped>
.mini-img-list {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.mini-img-box {
  margin: 0 12px;
  border: 4px solid $color-FFFFFF;
  width: 125px;
  height: 80px;
  background: $color-FFFFFF;
  box-shadow: 0 0 0 0 $--color-primary;
  transition: box-shadow .1s;
  cursor: pointer;

  &.active,
  &:hover {
    box-shadow: 0 0 0 6px $--color-primary;
  }

  .img {
    width: auto;
    height: 100%;
  }
}
</style>

<template>
  <div class="mini-img-list">
    <div
      v-for="(url, idx) in urlList"
      :key="url"
      class="mini-img-box"
      :class="{ active: activeIndex === idx}"
      @click="() => { handleSelect(idx) }"
    >
      <el-image
        style="width: 117px; height: 72px;"
        :src="url"
        fit="cover"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'mini-img-list',
  model: {
    prop: 'activeIndex',
    event: 'change',
  },
  props: {
    urlList: {
      type: Array,
      default: () => [],
    },
    // 层级
    zIndex: {
      type: Number,
      default: 2000,
    },
    // 选中第几张
    activeIndex: {
      type: Number,
      default: 0,
    }
  },
  methods: {
    // 点击图片
    handleSelect(idx) {
      this.$emit('change', idx)
    }
  }
}
</script>
