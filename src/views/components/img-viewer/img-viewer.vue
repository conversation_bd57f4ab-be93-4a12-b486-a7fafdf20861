<style lang="scss" scoped>
$elImageViewerActionsHeight: 44px; // ElImageView 底部控件高度
$elImageViewerActionsBottom: 48px; // ElImageView 底部控件bottom
$miniImageListMarginBottom: 32px; // miniImageList 距离 ElImageView 底部控件距离
$miniImageListHeight: 80px;       // miniImageList 高度
$largeImageMarginBottom: 40px;    // 大图可视区域距离下方元素距离
$largeImageMarginHeight: 800px;    // 大图可视区域设计高度(非实际可视高度)

// miniImageListBottom bottom值
$miniImageListBottom: $elImageViewerActionsBottom + $elImageViewerActionsHeight + $miniImageListMarginBottom;

// 存在 miniImageList 时 可视区域的 bottom 值
$hasMiniImageListlargeImageBottom: $miniImageListBottom + $miniImageListHeight + $largeImageMarginBottom;

// 不村子 miniImageList 时 可视区域的 bottom 值
$largeImageBottom: $largeImageMarginBottom + $elImageViewerActionsHeight + $elImageViewerActionsBottom;

.img-viewer {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.mini-img-container {
  position: absolute;
  right: 0;
  bottom: $miniImageListBottom;
  left: 0;
}

::v-deep {
  $width: 816px;

  .el-image-viewer__wrapper {
    position: absolute;
  }

  // 样式修改
  .el-image-viewer__canvas {
    position: absolute;
    top: 110px;
    bottom: $largeImageBottom;
    left: 50%;
    align-items: flex-start;
    overflow: hidden;
    margin-left: -$width / 2;
    width: $width;
    height: auto;

    @media screen and (min-height: $largeImageMarginHeight + $largeImageBottom) {
      top: 200px;
    }
  }

  .el-image-viewer__actions {
    bottom: $elImageViewerActionsBottom;
  }

  .el-image-viewer__img {
    width: 100%;
  }

  .el-image-viewer__close {
    top: 110px - 40px;
    left: 50%;
    margin-left: $width / 2 + 45px;
    opacity: 1;
    transform: translateX(-50%);

    @media screen and (min-height: $largeImageMarginHeight + $largeImageBottom) {
      top: 200px - 40px;
    }
  }
}

// 有缩略图时的样式
.show-mini-image {
  ::v-deep .el-image-viewer__canvas {
    bottom: $hasMiniImageListlargeImageBottom;

    @media screen and (min-height: $largeImageMarginHeight + $hasMiniImageListlargeImageBottom) {
      top: 200px;
    }
  }
}

.no-prev {
  ::v-deep .el-image-viewer__prev {
    display: none;
  }
}

.no-next {
  ::v-deep .el-image-viewer__next {
    display: none;
  }
}

.operate {
  @include flex((justify: flex-end, align: center));

  position: absolute;
  top: 16px;
  right: 16px;

  .btn {
    margin-left: 16px;
    border-radius: 2px;
    padding: 7px 10px;
    font-size: 14px;
    color: $color-FFFFFF;
    background: rgb(38 38 38 / 90%);
    line-height: 20px;
    cursor: pointer;

    .icon {
      font-size: 20px;
      transform: translateY(2px);
    }

    &:hover {
      background: rgb(38 38 38 / 100%);
    }
  }
}

.copy-img {
  position: fixed;
  top: -9999px;
  left: -9999px;
  z-index: -1;
  opacity: 0;
  pointer-events: none;
}
</style>

<template>
  <div
    v-if="visible"
    class="img-viewer"
  >
    <ImageViewer
      ref="elImageViewer"
      :url-list="noReapetUrlList"
      :z-index="zIndex"
      :initial-index="initialIndex"
      :on-close="onElImageViewClose"
      :on-switch="handleChange"
      :append-to-body="true"
      :mask-closable="maskClosable"
      default-mode="original"
      :close-icon-follow="false"
      :class="{
        'show-mini-image': showMiniImage,
        'no-prev': !prevBtn,
        'no-next': !nextBtn
      }"
    >
      <template #canvas>
        <div class="operate">
          <slot name="operate" />
          <div v-if="copyBtn" class="btn" @click="handleCopyImg">
            <icon class="icon sdicon-copy" type="chengjie-copy" />
            复制票面
          </div>
          <!-- 如果只有一张图片 不展示切换正反面按钮 -->
          <div v-if="frontOrBackBtn && noReapetUrlList.length > 1" class="btn" @click="handleSwitchFrontOrBack">
            <icon class="icon sdicon-swap" type="chengjie-swap" />
            切换至{{ currentIndex ? '正' : '背' }}面
          </div>
        </div>
      </template>

      <div v-if="showMiniImage" class="mini-img-container" :style="{zIndex: zIndex + 2}">
        <MiniImgList
          :active-index="currentIndex"
          :url-list="noReapetUrlList"
          @change="handleChange"
        />
      </div>

      <slot />
    </ImageViewer>

    <!-- 复制票图使用 -->
    <img
      v-if="noReapetUrlList[currentIndex]"
      ref="draftImage"
      :src="urlSrc"
      alt="票面截图"
      class="copy-img"
      crossorigin="Anonymous"
    >
  </div>
</template>

<script>
import { ImageViewer } from '@shendu/element-ui'
import MiniImgList from './mini-img-list.vue'
import { browserType } from '@/common/js/env' // 判断浏览器类型
import { getBase64Image, base64ToFile } from '@/common/js/util'

export default {
  name: 'img-viewer',
  components: {
    ImageViewer,
    MiniImgList,
  },
  model: {
    prop: 'visible',
    event: 'toggle',
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    urlList: {
      type: Array,
      default: () => []
    },
    zIndex: {
      type: Number,
      default: 2000
    },
    initialIndex: {
      type: Number,
      default: 0
    },
    maskClosable: {
      type: Boolean,
      default: false,
    },

    // 上一张按钮
    prevBtn: {
      type: Boolean,
      default: true,
    },

    // 下一张按钮
    nextBtn: {
      type: Boolean,
      default: true,
    },

    // 是否使用迷你缩略图
    showMiniImage: {
      type: Boolean,
      default: false,
    },

    // 切换正反面按钮
    frontOrBackBtn: {
      type: Boolean,
      default: false,
    },

    // 复制图片按钮
    copyBtn: {
      type: Boolean,
      default: false,
    }

  },
  data() {
    return {
      currentIndex: this.initialIndex,
    }
  },

  computed: {
    // 复制的图片路径
    urlSrc() {
      let url = this.noReapetUrlList[this.currentIndex]
      return url.startsWith('data:image') ? url : `${url}?v=2`
    },
    // 过滤重复图片链接
    noReapetUrlList() {
      return this.urlList.filter((item, index) => item && this.urlList.indexOf(item) === index)
    }
  },

  watch: {
    visible(visible) {
      if (visible) {
        this.currentIndex = this.initialIndex
      }
    },
    initialIndex(idx) {
      this.currentIndex = idx
    }
  },

  methods: {
    // 隐藏组件
    onElImageViewClose() {
      this.$emit('toggle', false)
    },

    // 总控切换
    handleChange(idx) {
      this.currentIndex = idx
      this.$refs.elImageViewer.index = idx
      this.$emit('change', idx)
    },

    // 正面/背面切换
    handleSwitchFrontOrBack() {
      if (this.currentIndex === 0) {
        this.handleChange(1)
      } else {
        this.handleChange(0)
      }
    },

    // 复制图片
    async handleCopyImg() {
      try {
        // const domImg = document.getElementsByClassName('el-image-viewer__img')[0]
        const domImg = this.$refs?.draftImage
        if (!domImg) return
        const isIe = browserType.indexOf('IE') !== -1
        const img = await getBase64Image(domImg)
        if (!isIe) {
        // 除IE外的浏览器的复制方法
          if (navigator.clipboard) {
            const file = await base64ToFile(img)
            const blob = new Blob([file], { type: 'image/png' })
            await navigator.clipboard.write([
            // eslint-disable-next-line no-undef
              new ClipboardItem({
                [blob.type]: blob
              })
            ]).then(() => {
              this.$message.success('复制成功！')
            })
          } else if (window.require) {
            const { clipboard, nativeImage } = window.require('electron')
            const image = nativeImage.createFromDataURL(img) // res为base64图片数据
            clipboard.writeImage(image)
            this.$message.success('复制成功')
          } else {
            this.$message.warning('浏览器不支持复制票据，可在票据图片上点击右键复制')
          }
        } else {
        // IE浏览器不支持file方法，可以直接转blob
        // IE支持的方法
          // try {
          // // const blob = await this.dataURLtoBlob(img)
          // // console.log(window.clipboardData)
          // // console.log(window.clipboardData.setData('text', file))
          // } catch (err) {
          //   console.log(err)
          // }

          this.$message.warning('浏览器不支持复制票据，可在票据图片上点击右键复制')
        }
      } catch (err) {
        // eslint-disable-next-line no-console
        console.log(err)
        this.$message.warning('浏览器不支持复制票据，可在票据图片上点击右键复制')
      }
    },
  }
}
</script>
