<!-- 限制发布接口 -->
<style lang="scss" scoped>
.title-text {
  display: flex;
  align-items: center;
  font-size: 18px;
}

.el-dialog__wrapper.release-limit-box {
  position: absolute !important;
  z-index: 1999 !important;
  background: rgb(38 38 38 / 50%);

  ::v-deep {
    .el-dialog {
      top: 50vh !important;
      margin-top: 0 !important;
      background: #FFFFFF !important;
      transform: translateY(-50%) !important;
    }

    .el-dialog__body {
      padding: 0 33px 16px;
      background: #FFFFFF !important;
    }

    .el-dialog__header {
      margin-bottom: 16px;
      border: none;
      padding: 16px 32px 0;
    }

    .el-dialog__footer {
      padding: 0 32px 24px !important;
      background: #FFFFFF !important;
    }

    .el-button--primary {
      font-size: 16px;
      font-weight: 600;
    }
  }
}

.defect-setting-icon {
  cursor: pointer;
  margin: 0 4px;
  color: #3070F6;

  span {
    text-decoration: underline;
    color: #3070F6;
  }
}

p {
  font-size: 16px;
  color: #333333;
  line-height: 24px;

  span {
    color: $color-warning;
  }
}

.mb-8 {
  margin-bottom: 8px;
}

.el-icon-warning {
  margin-right: 10px;
  font-size: 21px;
  color: #FA8C16;
}
</style>

<template>
  <el-dialog
    width="482px"
    class="release-limit-box"
    :style="{top: absoluteTop}"
    :modal="false"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :modal-append-to-body="false"
    :append-to-body="false"
    :close-on-press-escape="false"
    :show-close="false"
  >
    <div slot="title" class="title-text">
      <i class="el-icon-warning" />
      <span>提示</span>
    </div>
    <div class="tips-box">
      <!-- <p class="mb-8">功能临时关闭，请在【<span>承接识票</span>】中使用【<span>单张识别</span>】识别票面后发布！</p> -->
      <p class="mb-8">您因触发平台机制，被限制普通发布，请在【<span>承接识票</span>】中使用【<span>单张识别</span>】识别票面后发布！</p>
      <p>
        可先在【承接识票】点击<span class="defect-setting-icon" @click="$refs.supportBankDialogRef.open()">
          <icon type="chengjie-bank" :size="24" />
          <span>支持银行</span>
        </span>功能查看支持的银行
      </p>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button v-if="isShowCloseBtn" @click="onClose">关闭</el-button>
      <el-button type="primary" @click="onBtnChange">{{ btnText }}</el-button>
    </span>
    <!-- 银行弹窗 -->
    <SupportBankDialog ref="supportBankDialogRef" />
  </el-dialog>
</template>

<script>
import SupportBankDialog from '@recognize/components/support-bank/support-bank-dialog.vue'
import { CLOSE_RELEASE_DIALOG } from '@/event/modules/site'
export default {
  name: 'release-limit-dialog',
  components: {
    SupportBankDialog
  },
  props: {
    // 按钮文字
    btnText: {
      type: String,
      default: '打开承接识票'
    },
    isShow: {
      type: Boolean,
      default: false
    },
    // 是否显示关闭按钮
    isShowCloseBtn: {
      type: Boolean,
      default: false
    },
    // 是否可以关闭弹窗
    isClose: {
      type: Boolean,
      default: false
    },
    // 弹窗的定位高度：如果在识票助手打开就是 0px
    absoluteTop: {
      type: String,
      default: '45px'
    },
    // 是否需要发送关闭事件
    isSendCloseEvent: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false
    }
  },
  created() {
    // 初始化页面的时候打开弹窗
    // this.visible = this.isShow
  },
  methods: {
    // 点击按钮
    onBtnChange() {
      if (!this.isClose) { // 不能关闭弹窗
        // 如果需要打开识票助手
        if (this.$ipc && typeof this.$ipc.send === 'function') {
          this.$ipc.send('OPEN_WINDOW_RECOGNIZE_HELPER')
        } else {
          this.$message.info('请在ERP客户端内使用此功能，您可联系客服获取安装包。')
        }
      } else {
        // 点击按钮可以关闭弹窗
        this.onClose()
      }
    },
    // 通过事件触发打开弹窗
    open() {
      this.visible = true
    },
    // 通过事件触发关闭弹窗
    onClose() {
      this.visible = false
      if (this.isSendCloseEvent) {
        this.$event.emit(CLOSE_RELEASE_DIALOG)
      }
    }
  },
}
</script>
