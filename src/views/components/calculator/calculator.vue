<!-- 新的计算器 -->
<style lang="scss" scoped>
::v-deep .drag-dialog {
  margin-right: 16px;
}

.box {
  border-radius: 10px;
  width: 66px;
  height: 22px;
}

.count {
  color: $color-FFFFFF;

  .text {
    cursor: pointer;

    &:hover {
      border-bottom: 1px solid $color-FFFFFF;
    }

    width: 37px;
    font-size: 12px;
    font-weight: 500;
    line-height: 18px;
  }

  .icon {
    margin-left: 2px;
    color: rgba($color:$color-FFFFFF, $alpha: 80%);
  }

  .svg-icon {
    vertical-align: top;
  }
}

.result-block {
  margin-bottom: 12px;
  padding: 12px 8px;
  background: $color-FFFFFF;

  .label {
    margin-bottom: 2px;
    font-size: 12px;
    color: $color-text-secondary;
    line-height: 20px;
  }

  .value {
    min-height: 20px;
    font-size: 14px;
    font-weight: 600;
    white-space: normal;
    color: $--color-font-main;
    line-height: 20px;

    &.font-weight-normal {
      font-weight: normal;
    }
  }
}

.result-amount {
  .label {
    font-size: 14px;
    line-height: 22px;
  }

  .value {
    min-height: 28px;
    font-size: 20px;
    line-height: 28px;
  }
}

.result-flex {
  @include flex-sb;

  .result-block {
    width: 96px;
  }
}

.form {
  padding: 16px;
  background: $color-FFFFFF;

  .el-form-item {
    margin-bottom: 10px;

    ::v-deep {
      .el-form-item__label {
        line-height: 22px;
        padding-bottom: 2px;
        color: $color-text-secondary;
      }

      .el-form-item__error {
        position: relative;
      }
    }

    .el-input {
      font-size: 16px;
    }

    .el-radio {
      margin-right: 0;
      width: 50%;

      ::v-deep {
        .el-radio__input {
          display: none;

          &.is-checked + .el-radio__label {
            border: 1px solid $--color-primary;
            font-weight: bold;
            background: $--color-primary-hover;
          }
        }

        .el-radio__label {
          border: 1px solid $color-D9D9D9;
          padding-left: 0;
          height: 40px;
          font-size: 16px;
          line-height: 26px;

          @include flex-cc;
        }
      }

      &.first ::v-deep .el-radio__label {
        border-radius: 2px 0 0 2px;
      }

      &.second ::v-deep .el-radio__label {
        border-left-color: transparent;
        border-radius: 0 2px 2px 0;
      }
    }

    .el-date-editor.el-input {
      width: 100%;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .flex {
    display: flex;

    .plus-icon {
      padding: 0 8px;
      width: 24px;
    }

    > .el-input {
      width: calc(50% - 12px);
    }

    .el-form-item {
      flex: 1;
    }

    .el-form-item:last-child {
      margin-left: 12px;
    }
  }
}
</style>

<template>
  <div class="drag-dialog">
    <DragDialog
      title="票据计算器"
      :visible="visible"
      top="40px"
      @toggle="handleDragDialog"
    >
      <template slot="button">
        <div class="count" @click="openDialog">
          <div class="box">
            <div class="box-content">
              <span class="text">计算器</span>
              <icon size="20" type="erp-jisuanqi" class="icon" />
            </div>
          </div>
        </div>
      </template>
      <div class="result-block result-amount">
        <div class="label">贴现金额 (万)</div>
        <div :class="['value', isNull(result.receivedAmount) && 'font-weight-normal']">{{ isNull(result.receivedAmount) ? '-' : parseNum(result.receivedAmount) }}</div>
      </div>
      <div class="result-flex">
        <div class="result-block">
          <div class="label">贴现利率(%)</div>
          <div :class="['value', isNull(result.annualInterest) && 'font-weight-normal']">{{ isNull(result.annualInterest) ? '-' : result.annualInterest }}</div>
        </div>
        <div class="result-block">
          <div class="label">每十万贴息(元)</div>
          <div :class="['value', isNull(result.lakhDeduction) && 'font-weight-normal']">{{ isNull(result.lakhDeduction) ? '-' : parseNum(result.lakhDeduction) }}</div>
        </div>
        <div class="result-block">
          <div class="label">贴息金额(元)</div>
          <div :class="['value', isNull(result.deductionAmount) && 'font-weight-normal']">{{ isNull(result.deductionAmount) ? '-' : parseNum(result.deductionAmount) }}</div>
        </div>
        <div class="result-block">
          <div class="label">计息天数(天)</div>
          <div :class="['value', isNull(result.interestAccrualDay) && 'font-weight-normal']">{{ isNull(result.interestAccrualDay) ? '-' : result.interestAccrualDay }}</div>
        </div>
      </div>
      <el-form
        ref="ruleForm"
        :model="form"
        :rules="rules"
        label-position="top"
        class="form"
      >
        <el-form-item
          label="票面金额"
          prop="draftAmount"
        >
          <el-input
            v-model="form.draftAmount"
            placeholder="输入票面金额"
            type="number"
            :number-format="{...numberFormat, maxDecimalLength: 6, maxIntegerLength: 5}"
          >
            <div slot="append">万</div>
          </el-input>
        </el-form-item>
        <el-form-item
          label="年化利率"
        >
          <div class="flex">
            <el-input
              v-model="form.annualInterest"
              placeholder="年化利率"
              type="number"
              :number-format="{...numberFormat, maxDecimalLength: 4, maxIntegerLength: 3}"
              @input="handleAnnualInterest"
            >
              <div slot="append">%</div>
            </el-input>
            <span class="plus-icon">+</span>
            <el-input
              v-model="form.serviceCharge"
              placeholder="每十万手续费"
              type="number"
              :number-format="{...numberFormat, maxDecimalLength: 2, maxIntegerLength: 5}"
            >
              <div slot="append">元</div>
            </el-input>
          </div>
        </el-form-item>
        <el-form-item>
          <el-radio-group v-model="form.dateRangeRadio" class="flex">
            <el-radio :label="DATE_RANGE_CODE.HALF_YEAR" class="first">3月电票</el-radio>
            <el-radio :label="DATE_RANGE_CODE.ONE_YEAR" class="second">6月电票</el-radio>
          </el-radio-group>
        </el-form-item>
        <div class="flex">
          <el-form-item
            label="贴现日期"
            prop="discountDate"
          >
            <el-date-picker
              v-model="form.discountDate"
              type="date"
              placeholder="选择日期"
              :picker-options="pickerDiscountDate"
              @change="handleChangeDate"
            />
          </el-form-item>
          <el-form-item
            label="到期日"
            prop="maturityDate"
          >
            <el-date-picker
              v-model="form.maturityDate"
              type="date"
              placeholder="选择日期"
              :picker-options="pickerMaturityDate"
              @change="handleChangeDate"
            />
          </el-form-item>
        </div>
        <el-form-item
          label="调整天数"
        >
          <el-input
            v-model="form.adjustDays"
            placeholder="调整天数"
            type="number"
            :number-format="{...numberFormat, decimal: false}"
          >
            <div slot="append">天</div>
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button
          type="primary"
          border
          @click="handleReset"
        >
          重置
        </el-button>
        <el-button type="primary" @click="handleClickCalc">计算</el-button>
      </div>
    </DragDialog>
  </div>
</template>

<script>
import DragDialog from '@/views/components/common/drag-dialog/drag-dialog.vue' // 可拖动弹窗
import {
  formatTime,
  dealTime, // 时间类型兼容
  afterMonths, // 获取多少个月后的今天
} from '@/common/js/date' // 时间处理函数
import {
  getAdjustDays, // 获取调整天数
  getInterestAccrualDay, // 获取计息天数
  interestRateMath, // 以利率计算 => 每十万扣款 年化利率 和 到账金额
  deductionAmountMath, // 计算贴息金额
} from '@/common/js/draft-math'
import { wan2yuan, yuan2wan, parseNum } from '@/common/js/number'
import { isNull } from '@/common/js/util'
import commonApi from '@/apis/common'
import { tracking } from '@/utils/util'

// 时间范围code
const DATE_RANGE_CODE = Object.freeze({
  HALF_YEAR: 3,
  ONE_YEAR: 6
})
// 默认表单字段
const defaultForm = () => ({
  draftAmount: '', // 票面金额
  annualInterest: '', // 年利率
  serviceCharge: '', // 手续费
  dateRangeRadio: DATE_RANGE_CODE.HALF_YEAR, // 当前选择的是3月票（3）还是6月票（6）
  discountDate: new Date(), // 贴现日期
  maturityDate: afterMonths(new Date(), DATE_RANGE_CODE.HALF_YEAR), // 到期日期
  adjustDays: '' // 调整天数
})
// 默认计算结果字段
const defaultResult = () => ({
  receivedAmount: '', // 贴现金额(万)
  annualInterest: '', // 贴现利率
  lakhDeduction: '', // 每十万贴息
  deductionAmount: '', // 贴息金额(元)
  interestAccrualDay: '', // 计息天数
})

export default {
  name: 'calculator',
  components: {
    DragDialog
  },

  data() {
    return {
      DATE_RANGE_CODE,
      // 表单字段
      form: defaultForm(),
      // 校验规则
      rules: {
        draftAmount: [{ required: true, message: '请输入票面金额', trigger: ['blur', 'change'] }],
        discountDate: [{ required: true, message: '请选择贴现日期', trigger: 'change' }],
        maturityDate: [{ required: true, message: '请选择到期日期', trigger: 'change' }]
      },
      // 默认的数字格式化
      numberFormat: {
        negative: false,
        leadingZero: false
      },
      // 计算的结果
      result: defaultResult(),
      holidays: [], // 节假日列表
      visible: false,
    }
  },

  computed: {
    // 贴现日期可选范围
    pickerDiscountDate() {
      let that = this
      return {
        disabledDate(time) {
          // eslint-disable-next-line no-magic-numbers
          return that.form.maturityDate ? dealTime(time) > dealTime(that.form.maturityDate) - 8.64e7 : false
        }
      }
    },
    // 到期日期可选范围
    pickerMaturityDate() {
      let that = this
      return {
        disabledDate(time) {
          // eslint-disable-next-line no-magic-numbers
          return that.form.discountDate ? dealTime(time) < dealTime(that.form.discountDate) : dealTime(time) < dealTime(new Date()) - 8.64e7
        }
      }
    },
  },

  watch: {
    'form.dateRangeRadio': {
      handler(val) {
        if (val) {
          const discountDate = this.form.discountDate ? this.form.discountDate : new Date()
          this.form.discountDate = discountDate
          this.form.maturityDate = afterMonths(discountDate, val)
          this.form.adjustDays = getAdjustDays(this.form.maturityDate, this.holidays)
        }
      },
      immediate: true
    },

  },

  created() {
    this.getHolidays()
  },

  methods: {
    isNull,
    parseNum,
    openDialog() {
      this.visible = true

      const { corpId, corpName } = this.$store.getters['user/userInfo']
      const time = formatTime(Date.now(), 'YYYY-MM-DD hh:mm:ss')
      tracking({
        calcOpen: `${corpName}在${time}点击了票据计算器`,
        corpId
      })
    },

    // 日期选择器回调
    handleChangeDate() {
      this.form.dateRangeRadio = null
      if (this.form.maturityDate) {
        // 获取调整天数
        this.form.adjustDays = getAdjustDays(this.form.maturityDate, this.holidays)
      }
    },

    // 获取未来节假日
    async getHolidays() {
      const data = await commonApi.getHolidays()
      this.holidays = data
      this.form.adjustDays = getAdjustDays(this.form.maturityDate, this.holidays)
    },

    // 计算结果
    getCalcResult() {
      const { discountDate, maturityDate, adjustDays, draftAmount, annualInterest, serviceCharge } = this.form
      // 计息天数
      this.result.interestAccrualDay = getInterestAccrualDay(discountDate, maturityDate, adjustDays)

      if (!annualInterest && !serviceCharge) {
        this.result.receivedAmount = draftAmount
        this.result.annualInterest = 0
        this.result.lakhDeduction = 0
        this.result.deductionAmount = 0
      } else {
        const data = interestRateMath(wan2yuan(draftAmount), annualInterest, serviceCharge, this.result.interestAccrualDay)
        this.result.receivedAmount = yuan2wan(data.receivedAmount)
        this.result.annualInterest = data.annualInterest
        this.result.lakhDeduction = data.lakhDeduction
        this.result.deductionAmount = deductionAmountMath(draftAmount, this.result.receivedAmount)
      }
    },

    // 计算
    handleClickCalc() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.getCalcResult()
        } else {
          return false
        }
      })

      const { corpId, corpName } = this.$store.getters['user/userInfo']
      const time = formatTime(Date.now(), 'YYYY-MM-DD hh:mm:ss')
      tracking({
        calcExecute: `${corpName}在${time}执行了票据计算器计算`,
        corpId
      })
    },

    // 重置
    handleReset() {
      this.form = defaultForm()
      this.$nextTick().then(() => {
        this.$refs.ruleForm.clearValidate()
      })
      this.result = defaultResult()
      this.form.adjustDays = getAdjustDays(this.form.maturityDate, this.holidays)
    },

    // 年化率输入 限制最大只能100
    handleAnnualInterest(val) {
      this.form.annualInterest = val > 100 ? 100 : val
    },

    // 拖动窗口显示回调
    handleDragDialog(val) {
      // 窗口关闭
      if (!val) {
        // 即使修改了toolbarType为null不被选中,但由于该组件处理Toolbar内,也会被el-radio重新赋值为选中状态,故加入setTimeout延迟修改toolbarType,避开el-radio的重新赋值
        setTimeout(() => {
          this.visible = false
        }, 0)
      }
    },
  }
}
</script>
