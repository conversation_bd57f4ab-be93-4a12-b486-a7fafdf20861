<!-- 图标组件 -->
<style lang="scss" scoped>
.svg-icon {
  display: inline-block;
  width: 1em;
  height: 1em;
  line-height: 1em;
  vertical-align: text-bottom;
  font-size: 16px;
}

.svg-icon-inner {
  overflow: hidden;
  width: 100%;
  height: 100%;
  fill: currentcolor;
  stroke: none;
}
</style>

<template>
  <i :class="['svg-icon', type]" :style="style" @click="handleClick">
    <svg class="svg-icon-inner" aria-hidden="true">
      <use :xlink:href="`#${type}`" />
    </svg>
  </i>
</template>

<script>
export default {
  name: 'icon',
  props: {
    // 类型，图标名
    type: {
      type: String,
      require: true
    },
    // 大小，px 值，如 16
    size: {
      type: [Number, String],
      // default: 16
    }
  },
  computed: {
    // svg 的样式
    style() {
      const style = {}
      if (this.size) {
        style.fontSize = `${this.size}px`
      }
      return style
    }
  },
  methods: {
    // 点击图标时触发
    handleClick(e) {
      this.$emit('click', e)
    }
  }
}
</script>
