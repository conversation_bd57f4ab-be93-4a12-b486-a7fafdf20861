<!-- 用户中心页面 -->
<style lang="scss" scoped>
.icon-test {
  padding: 20px 0;
}

.icon-list {
  color: $--color-primary;
}
</style>

<template>
  <div class="icon-test">
    <!--
      <script src="//at.alicdn.com/t/font_2895981_irukpbd4o2e.js" />
      <link rel="stylesheet" href="//at.alicdn.com/t/font_2895981_irukpbd4o2e.css">
      <script src="//at.alicdn.com/t/font_2343212_fzjyb8d8iyd.js" />
    -->
    <el-button type="primary" :loading="loading" @click="loading = !loading">测试按钮</el-button>
    <div class="icon-list">
      <span>SVG 图标: </span>
      <Icon type="zphticon-king" :size="28" />
      <Icon type="zphticon-calendar" />
      <span>test 1234</span>
      <Icon type="chengjie-icon_shangweibaojia" :size="20" />
    </div>
    <div class="icon-list">
      <span>字体图标: </span>
      <i class="zphticon zphticon-king" />
      <span>test 1234</span>
      <i class="zphticon zphticon-calendar" />
    </div>
  </div>
</template>

<script>
import Icon from './icon.vue'

export default {
  name: 'icon-test',
  components: {
    Icon
  },
  data() {
    return {
      loading: false
    }
  }
}
</script>
