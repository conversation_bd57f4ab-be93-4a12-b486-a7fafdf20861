/* eslint-disable no-magic-numbers */

// 图片生成器
const imgGenerator = {

  /**
   * 创建画布
   * @param {number} ratio 画布倍数，默认2
   * @param {number} w 画布宽度，默认992
   * @param {number} h 画布高度，默认620
   * @returns {document} canvas 画布
   */
  createCanvas(ratio = 2, w = 992, h = 620) {
    const canvas = document.createElement('canvas')
    canvas.width = w * ratio
    canvas.height = h * ratio
    canvas.style.width = `${w}px`
    canvas.style.height = `${h}px`
    return canvas
  },

  /**
   * 生成正面图片
   * @param {object|string} draftData 票据数据
   * @param {string} imageType 返回的图片类型，canvas/url
   * @param {number} base 画布倍数，默认2
   * @returns {string|object} blob/base64
   */
  async generateFrontImg(draftData, imageType = 'canvas', base = 2) {
    const hasSubRange = () => {
      const { front } = draftData
      return !!front.childTicketRange
    }

    const subHeight = hasSubRange() ? 20 : 0

    const canvas = this.createCanvas(base, 992, 620 + subHeight)
    const ctx = canvas.getContext('2d')
    const canvasPadding = 20
    const canvasWidth = ctx.canvas.width
    const canvasHeight = ctx.canvas.height

    // 换行方法
    const resetWrapText = () => {
      CanvasRenderingContext2D.prototype.wrapText = function(text, x, y, maxWidth, lineHeight) {
        if (typeof text != 'string' || typeof x != 'number' || typeof y != 'number') {
          return
        }

        // eslint-disable-next-line consistent-this
        let context = this
        let { canvas: ocanvas } = context

        if (typeof maxWidth == 'undefined') {
          maxWidth = (ocanvas && ocanvas.width) || 300
        }
        if (typeof lineHeight == 'undefined') {
          lineHeight = (ocanvas && parseInt(window.getComputedStyle(ocanvas).lineHeight)) || parseInt(window.getComputedStyle(document.body).lineHeight)
        }

        // 字符分隔为数组
        let arrText = text.split('')
        let line = ''

        for (let n = 0; n < arrText.length; n++) {
          let testLine = line + arrText[n]
          let metrics = context.measureText(testLine)
          let testWidth = metrics.width
          if (testWidth > maxWidth && n > 0) {
            context.fillText(line, x, y)
            line = arrText[n]
            y += lineHeight
          } else {
            line = testLine
          }
        }
        context.fillText(line, x, y)
      }
    }

    resetWrapText()

    // 填充背景色
    const fillBgColor = () => new Promise(resolve => {
      const { front } = draftData
      const isBankDraft = String(front.ticketNumber).startsWith('1') || String(front.ticketNumber).startsWith('5') || String(front.ticketNumber).startsWith('8')
      // 创建线性渐变对象
      let gradientLinear = ctx.createLinearGradient(0, canvasHeight / 2, canvasWidth, canvasHeight / 2)

      if (isBankDraft) {
        gradientLinear.addColorStop(0, '#DCF1FA')
        gradientLinear.addColorStop(0.5, '#FFEBDB')
        gradientLinear.addColorStop(1, '#DCF1FA')
      } else {
        gradientLinear.addColorStop(0, '#ECD8EC')
        gradientLinear.addColorStop(0.5, '#F7EEBC')
        gradientLinear.addColorStop(1, '#ECD8EC')
      }

      // 填充线性渐变
      ctx.fillStyle = gradientLinear
      ctx.fillRect(0, 0, canvasWidth, canvasHeight)
      resolve()
    })
    fillBgColor()

    // 填充logo
    const fillLogo = () => new Promise(resolve => {
      const img = new Image()
      img.src = require('@imgs/draft/draft-logo.png')
      img.onload = () => {
        const { width, height } = img
        const y = 25
        ctx.drawImage(img, canvasPadding * base, y * base, width * base, height * base)
        resolve()
      }
    })
    await fillLogo()

    // 标题
    const fillTitle = () => {
      ctx.fillStyle = '#000000'
      ctx.font = `${26 * base}px system-ui, Simhei`
      ctx.textAlign = 'center'
      ctx.fillText(`${draftData.front.AC01 || '票面信息'}`, canvasWidth / 2, 95 * base)
    }
    fillTitle()

    // 文字头部
    const fillTextHeader = () => {
      const fontSize = 15
      ctx.font = `${fontSize * base}px system-ui, Simhei`
      ctx.textAlign = 'left'
      const firstY = 125
      const secondY = 145
      // 左边
      ctx.fillText('出票日期 ：', canvasPadding * base, firstY * base)
      ctx.fillText('汇票到期日：', canvasPadding * base, secondY * base)
      // 右边
      ctx.fillText('票据状态：', canvasWidth / 2, firstY * base)
      ctx.fillText('票据号码：', canvasWidth / 2, secondY * base)
      if (hasSubRange()) {
        ctx.fillText('子票区间：', canvasWidth / 2, (secondY + subHeight) * base)
      }
    }
    fillTextHeader()

    const firstY = 155 + subHeight// 表格头部线条y轴
    const gridSize = 70
    const tableHeight = 450 // 表格高度
    const firstTableHeight = 135 // 出票人收款人表格高度-第一种表格高度
    const firstHeight = 28 // 第一种高度
    const secondHeight = 40 // 第二种高度

    // 填充表格
    const fillTable = () => {
      ctx.lineWidth = base // 表格线条宽度
      // 表格线
      ctx.rect(canvasPadding * base, firstY * base, canvasWidth - (canvasPadding * base * 2), tableHeight * base)
      ctx.stroke()

      ctx.beginPath()
      // 从左到右的竖线
      // 出票人竖线
      ctx.moveTo((canvasPadding + gridSize) * base, firstY * base)
      ctx.lineTo((canvasPadding + gridSize) * base, (firstY + firstTableHeight) * base)

      // 出票人账号竖线
      ctx.moveTo((canvasPadding + gridSize * 2) * base, firstY * base)
      ctx.lineTo((canvasPadding + gridSize * 2) * base, (firstY + tableHeight) * base)

      // 中竖线
      ctx.moveTo(canvasWidth / 2, firstY * base)
      ctx.lineTo(canvasWidth / 2, (firstY + tableHeight - firstHeight * 2) * base)

      // 收款人竖线
      ctx.moveTo(canvasWidth / 2 + gridSize * base, firstY * base)
      ctx.lineTo(canvasWidth / 2 + gridSize * base, (firstY + firstTableHeight) * base)

      // 收款人账号竖线
      ctx.moveTo(canvasWidth / 2 + gridSize * 2 * base, firstY * base)
      ctx.lineTo(canvasWidth / 2 + gridSize * 2 * base, (firstY + firstTableHeight) * base)

      // 承兑信息竖线
      ctx.moveTo(canvasWidth / 2 + gridSize * base, (firstY + firstHeight * 3.5 + secondHeight * 4) * base)
      ctx.lineTo(canvasWidth / 2 + gridSize * base, (firstY + firstHeight * 6.5 + secondHeight * 4) * base)

      // 评级信息-出票人竖线
      ctx.moveTo((canvasPadding + gridSize * 3) * base, (firstY + firstHeight * 5.5 + secondHeight * 6) * base)
      ctx.lineTo((canvasPadding + gridSize * 3) * base, (firstY + firstHeight * 6.5 + secondHeight * 6) * base)

      // 从上到下的横线
      // 出票人-账号底线
      ctx.moveTo((canvasPadding + gridSize) * base, (firstY + firstHeight) * base)
      ctx.lineTo((canvasWidth / 2), (firstY + firstHeight) * base)

      // 收款人-账号底线
      ctx.moveTo(canvasWidth / 2 + gridSize * base, (firstY + firstHeight) * base)
      ctx.lineTo(canvasWidth - canvasPadding * base, (firstY + firstHeight) * base)

      // 出票人-全称底线
      ctx.moveTo((canvasPadding + gridSize) * base, (firstY + firstHeight + secondHeight) * base)
      ctx.lineTo(canvasWidth / 2, (firstY + firstHeight + secondHeight) * base)

      // 收款人-全称底线
      ctx.moveTo(canvasWidth / 2 + gridSize * base, (firstY + firstHeight + secondHeight) * base)
      ctx.lineTo(canvasWidth - canvasPadding * base, (firstY + firstHeight + secondHeight) * base)

      // 出票人-开户行底线
      ctx.moveTo((canvasPadding + gridSize) * base, (firstY + firstHeight + secondHeight * 2) * base)
      ctx.lineTo(canvasWidth / 2, (firstY + firstHeight + secondHeight * 2) * base)

      // 收款人-开户行底线
      ctx.moveTo(canvasWidth / 2 + gridSize * base, (firstY + firstHeight + secondHeight * 2) * base)
      ctx.lineTo(canvasWidth - canvasPadding * base, (firstY + firstHeight + secondHeight * 2) * base)

      // 出票人底线
      ctx.moveTo(canvasPadding * base, (firstY + firstHeight * 2 + secondHeight * 2) * base)
      ctx.lineTo(canvasWidth - canvasPadding * base, (firstY + firstHeight * 2 + secondHeight * 2) * base)

      // 出票人保证信息底线
      ctx.moveTo(canvasPadding * base, (firstY + firstHeight * 2 + secondHeight * 3) * base)
      ctx.lineTo(canvasWidth - canvasPadding * base, (firstY + firstHeight * 2 + secondHeight * 3) * base)

      // 票据金额底线
      ctx.moveTo(canvasPadding * base, (firstY + firstHeight * 3 + secondHeight * 3) * base)
      ctx.lineTo(canvasWidth - canvasPadding * base, (firstY + firstHeight * 3 + secondHeight * 3) * base)

      // 承兑人底线
      ctx.moveTo(canvasPadding * base, (firstY + firstHeight * 3.5 + secondHeight * 4) * base)
      ctx.lineTo(canvasWidth - canvasPadding * base, (firstY + firstHeight * 3.5 + secondHeight * 4) * base)

      // 交易合同底线
      ctx.moveTo(canvasPadding * base, (firstY + firstHeight * 4.5 + secondHeight * 4) * base)
      ctx.lineTo(canvasWidth / 2, (firstY + firstHeight * 4.5 + secondHeight * 4) * base)

      // 承兑信息-出票人承诺底线
      ctx.moveTo(canvasWidth / 2 + gridSize * base, (firstY + firstHeight * 4.5 + secondHeight * 4) * base)
      ctx.lineTo(canvasWidth - canvasPadding * base, (firstY + firstHeight * 4.5 + secondHeight * 4) * base)

      // 承兑信息-承兑人承诺底线
      ctx.moveTo(canvasWidth / 2 + gridSize * base, (firstY + firstHeight * 5.5 + secondHeight * 4) * base)
      ctx.lineTo(canvasWidth - canvasPadding * base, (firstY + firstHeight * 5.5 + secondHeight * 4) * base)

      // 是否可转让底线
      ctx.moveTo(canvasPadding * base, (firstY + firstHeight * 6.5 + secondHeight * 4) * base)
      ctx.lineTo(canvasWidth - canvasPadding * base, (firstY + firstHeight * 6.5 + secondHeight * 4) * base)

      // 承兑人保证信息底线
      ctx.moveTo(canvasPadding * base, (firstY + firstHeight * 5.5 + secondHeight * 6) * base)
      ctx.lineTo(canvasWidth - canvasPadding * base, (firstY + firstHeight * 5.5 + secondHeight * 6) * base)

      // 评级信息底线
      ctx.moveTo(canvasPadding * base, (firstY + firstHeight * 6.5 + secondHeight * 6) * base)
      ctx.lineTo(canvasWidth - canvasPadding * base, (firstY + firstHeight * 6.5 + secondHeight * 6) * base)

      ctx.stroke()
    }
    fillTable()

    // 表格固定内容填充
    const fillTableFixedContent = () => {
      const fontSize = 15
      ctx.font = `${fontSize * base}px system-ui, Simhei`
      ctx.textAlign = 'center'
      ctx.textBaseline = 'middle'

      ctx.fillText('出票人', (canvasPadding + gridSize / 2) * base, (firstY + firstTableHeight / 2) * base)
      ctx.fillText('账号', (canvasPadding + gridSize + gridSize / 2) * base, (firstY + firstHeight / 2) * base)
      ctx.fillText('全称', (canvasPadding + gridSize + gridSize / 2) * base, (firstY + firstHeight + secondHeight / 2) * base)
      ctx.fillText('开户行', (canvasPadding + gridSize + gridSize / 2) * base, (firstY + firstHeight + secondHeight * 1.5) * base)
      ctx.fillText('开户行号', (canvasPadding + gridSize + gridSize / 2) * base, (firstY + firstHeight * 1.5 + secondHeight * 2) * base)

      ctx.fillText('收款人', canvasWidth / 2 + gridSize / 2 * base, (firstY + firstTableHeight / 2) * base)
      ctx.fillText('账号', canvasWidth / 2 + (gridSize + gridSize / 2) * base, (firstY + firstHeight / 2) * base)
      ctx.fillText('全称', canvasWidth / 2 + (gridSize + gridSize / 2) * base, (firstY + firstHeight + secondHeight / 2) * base)
      ctx.fillText('开户行', canvasWidth / 2 + (gridSize + gridSize / 2) * base, (firstY + firstHeight + secondHeight * 1.5) * base)
      ctx.fillText('开户行号', canvasWidth / 2 + (gridSize + gridSize / 2) * base, (firstY + firstHeight * 1.5 + secondHeight * 2) * base)

      ctx.fillText('出票人保证信息', (canvasPadding + gridSize) * base, (firstY + firstHeight * 2 + secondHeight * 2.5) * base)
      ctx.fillText('保证人账号：', (canvasPadding + gridSize * 3 - 15) * base, (firstY + firstHeight * 2 + secondHeight * 2.5 - 10) * base)
      ctx.fillText('保证人名称：', (canvasPadding + gridSize * 3 - 15) * base, (firstY + firstHeight * 2 + secondHeight * 2.5 + 10) * base)
      ctx.fillText('保证人开户行：', canvasWidth / 2 + (gridSize + 8) * base, (firstY + firstHeight * 2 + secondHeight * 2.5 - 10) * base)
      ctx.fillText('保证人开户行号：', canvasWidth / 2 + gridSize * base, (firstY + firstHeight * 2 + secondHeight * 2.5 + 10) * base)

      ctx.fillText('票据金额', (canvasPadding + gridSize) * base, (firstY + firstHeight * 2.5 + secondHeight * 3) * base)
      ctx.textAlign = 'right'
      ctx.fillText('小写：', (canvasPadding + gridSize * 3 + 30) * base, (firstY + firstHeight * 2.5 + secondHeight * 3) * base)
      ctx.textAlign = 'center'
      ctx.fillText('人民币（大写）：', canvasWidth / 2 + gridSize * base, (firstY + firstHeight * 2.5 + secondHeight * 3) * base)

      ctx.fillText('承兑人', (canvasPadding + gridSize) * base, (firstY + firstHeight * 2.5 + secondHeight * 4) * base)
      ctx.fillText('承兑人账号：', (canvasPadding + gridSize * 3 - 15) * base, (firstY + firstHeight * 1.85 + secondHeight * 4 + 5) * base)
      ctx.fillText('承兑人名称：', (canvasPadding + gridSize * 3 - 15) * base, (firstY + firstHeight * 2.8 + secondHeight * 4) * base)
      ctx.fillText('承兑人开户行：', canvasWidth / 2 + (gridSize + 8) * base, (firstY + firstHeight * 1.85 + secondHeight * 4 + 5) * base)
      ctx.fillText('承兑人开户行号：', canvasWidth / 2 + gridSize * base, (firstY + firstHeight * 3.1 + secondHeight * 4) * base)

      ctx.fillText('交易合同号', (canvasPadding + gridSize) * base, (firstY + firstHeight * 4 + secondHeight * 4) * base)

      ctx.fillText('承兑信息', canvasWidth / 2 + gridSize / 2 * base, (firstY + firstHeight * 5 + secondHeight * 4) * base)
      ctx.fillText('出票人承诺：', canvasWidth / 2 + gridSize * 1.7 * base, (firstY + firstHeight * 4 + secondHeight * 4) * base)
      ctx.fillText('承兑人承兑：', canvasWidth / 2 + gridSize * 1.7 * base, (firstY + firstHeight * 5 + secondHeight * 4) * base)
      ctx.fillText('承兑日期：', canvasWidth / 2 + gridSize * 1.8 * base, (firstY + firstHeight * 6 + secondHeight * 4) * base)

      ctx.fillText('是否可转让', (canvasPadding + gridSize) * base, (firstY + firstHeight * 5.5 + secondHeight * 4) * base)

      ctx.fillText('承兑人保证信息', (canvasPadding + gridSize) * base, (firstY + firstHeight * 4.5 + secondHeight * 6) * base)
      ctx.fillText('保证人账号：', (canvasPadding + gridSize * 3 - 15) * base, (firstY + firstHeight * 4 + secondHeight * 6 + 8) * base)
      ctx.fillText('保证人名称：', (canvasPadding + gridSize * 3 - 15) * base, (firstY + firstHeight * 4.5 + secondHeight * 6 + 12) * base)
      ctx.fillText('保证人开户行：', canvasWidth / 2 + (gridSize + 8) * base, (firstY + firstHeight * 4 + secondHeight * 6 + 8) * base)
      ctx.fillText('保证人开户行号：', canvasWidth / 2 + gridSize * base, (firstY + firstHeight * 4.5 + secondHeight * 6 + 12) * base)

      ctx.fillText('评级信息', (canvasPadding + gridSize) * base, (firstY + firstHeight * 6 + secondHeight * 6) * base)
      ctx.fillText('出票人', (canvasPadding + gridSize * 2.5) * base, (firstY + firstHeight * 6 + secondHeight * 6) * base)
      ctx.fillText('评级主体：', (canvasPadding + gridSize * 3.5 + 5) * base, (firstY + firstHeight * 6 + secondHeight * 6) * base)
      ctx.fillText('信用等级：', canvasWidth / 2 + (20 * base), (firstY + firstHeight * 6 + secondHeight * 6) * base)
      ctx.fillText('评级到期日：', canvasWidth / 2 + (gridSize * 4.5) * base, (firstY + firstHeight * 6 + secondHeight * 6) * base)

      ctx.fillText('备注', (canvasPadding + gridSize) * base, (firstY + firstHeight * 7 + secondHeight * 6) * base)
    }
    fillTableFixedContent()

    // 表格动态内容填充
    const fillTableContent = () => {
      const { front } = draftData
      ctx.textAlign = 'left'
      // 头部左边
      ctx.fillText(front.issueDateTime, canvasPadding + 90 * base, 120 * base)
      ctx.fillText(front.expiredDateTime, canvasPadding + 100 * base, 140 * base)
      // 头部右边
      ctx.fillText(front.ticketStatus, canvasWidth / 2 + 70 * base, 120 * base)
      ctx.fillText(front.ticketNumber, canvasWidth / 2 + 70 * base, 140 * base)
      if (hasSubRange()) {
        ctx.fillText(front.childTicketRange, canvasWidth / 2 + 70 * base, (140 + subHeight) * base)
      }

      // 出票人
      let ticketIssuerMaxWidth = 325
      ctx.fillText(front.ticketIssuer.accountNumber || '', (canvasPadding + gridSize * 2 + 10) * base, (firstY + firstHeight / 2) * base)
      let ticketIssuerFullName = ctx.measureText(front.ticketIssuer.fullName)
      ctx.wrapText(front.ticketIssuer.fullName || '', (canvasPadding + gridSize * 2 + 10) * base, (firstY + firstHeight * (ticketIssuerFullName.width > ticketIssuerMaxWidth * base ? 0.7 : 1) + secondHeight / 2) * base, ticketIssuerMaxWidth * base, 17 * base)
      let ticketIssuerBank = ctx.measureText(front.ticketIssuer.bankOfDeposit)
      ctx.wrapText(front.ticketIssuer.bankOfDeposit || '', (canvasPadding + gridSize * 2 + 10) * base, (firstY + firstHeight + secondHeight * (ticketIssuerBank.width > ticketIssuerMaxWidth * base ? 1.3 : 1.5)) * base, ticketIssuerMaxWidth * base, 17 * base)
      ctx.fillText(front.ticketIssuer.bankNo || '', (canvasPadding + gridSize * 2 + 10) * base, (firstY + firstHeight * 1.5 + secondHeight * 2) * base)

      // 收款人
      ctx.fillText(front.payee.accountNumber || '', canvasWidth / 2 + (gridSize * 2 + 10) * base, (firstY + firstHeight / 2) * base)
      let payeeFullName = ctx.measureText(front.payee.fullName)
      ctx.wrapText(front.payee.fullName || '', canvasWidth / 2 + (gridSize * 2 + 10) * base, (firstY + firstHeight * (payeeFullName.width > ticketIssuerMaxWidth * base ? 0.7 : 1) + secondHeight / 2) * base, ticketIssuerMaxWidth * base, 17 * base)
      let payeeBank = ctx.measureText(front.payee.bankOfDeposit)
      ctx.wrapText(front.payee.bankOfDeposit || '', canvasWidth / 2 + (gridSize * 2 + 10) * base, (firstY + firstHeight + secondHeight * (payeeBank.width > ticketIssuerMaxWidth * base ? 1.3 : 1.5)) * base, ticketIssuerMaxWidth * base, 17 * base)
      ctx.fillText(front.payee.bankNo || '', canvasWidth / 2 + (gridSize * 2 + 10) * base, (firstY + firstHeight * 1.5 + secondHeight * 2) * base)

      // 出票人保证信息
      ctx.fillText(front.ticketGuaranteeInformation.accountOfGuarantor || '', (canvasPadding + gridSize * 3.35) * base, (firstY + firstHeight * 2 + secondHeight * 2.5 - 10) * base)
      ctx.fillText(front.ticketGuaranteeInformation.nameOfGuarantor || '', (canvasPadding + gridSize * 3.35) * base, (firstY + firstHeight * 2 + secondHeight * 2.5 + 10) * base)
      ctx.fillText(front.ticketGuaranteeInformation.bankNameOfGuarantor || '', canvasWidth / 2 + gridSize * 1.8 * base, (firstY + firstHeight * 2 + secondHeight * 2.5 - 10) * base)
      ctx.fillText(front.ticketGuaranteeInformation.bankNoOfGuarantor || '', canvasWidth / 2 + gridSize * 1.8 * base, (firstY + firstHeight * 2 + secondHeight * 2.5 + 10) * base)

      // 票据金额
      ctx.fillText(front.amountOfTicket.Digits || '', (canvasPadding + gridSize * 3.35) * base, (firstY + firstHeight * 2.5 + secondHeight * 3) * base)
      ctx.fillText(front.amountOfTicket.Upper || '', canvasWidth / 2 + gridSize * 1.8 * base, (firstY + firstHeight * 2.5 + secondHeight * 3) * base)

      // 承兑人
      ctx.fillText(front.accepteeInformation.accountNumber || '', (canvasPadding + gridSize * 3.35) * base, (firstY + firstHeight * 1.85 + secondHeight * 4 + 5) * base)
      let acceptFullName = ctx.measureText(front.accepteeInformation.fullName)
      let acceptMaxWidth = 240
      ctx.wrapText(front.accepteeInformation.fullName || '', (canvasPadding + gridSize * 3.35) * base, (firstY + firstHeight * (acceptFullName.width > acceptMaxWidth * base ? 2.6 : 2.8) + secondHeight * 4) * base, acceptMaxWidth * base, 15 * base)
      let bankMaxWidth = 340
      ctx.wrapText(front.accepteeInformation.bankOfDeposit || '', canvasWidth / 2 + gridSize * 1.8 * base, (firstY + firstHeight * 2 + secondHeight * 4) * base, bankMaxWidth * base, 15 * base)
      ctx.fillText(front.accepteeInformation.bankNo || '', canvasWidth / 2 + gridSize * 1.8 * base, (firstY + firstHeight * 3.1 + secondHeight * 4) * base)

      // 交易合同号
      ctx.fillText(front.transactionContractNo || '', (canvasPadding + gridSize * 2 + 10) * base, (firstY + firstHeight * 4 + secondHeight * 4) * base)

      // 是否可转让
      ctx.fillText(front.transferred || '', (canvasPadding + gridSize * 2 + 10) * base, (firstY + firstHeight * 4 + secondHeight * 5) * base)

      // 承兑信息
      ctx.fillText(front.acceptanceInformation.promiseOfDrawer || '', canvasWidth / 2 + gridSize * 2.3 * base, (firstY + firstHeight * 4 + secondHeight * 4) * base)
      ctx.fillText(front.acceptanceInformation.acceptanceByAcceptor || '', canvasWidth / 2 + gridSize * 2.3 * base, (firstY + firstHeight * 5 + secondHeight * 4) * base)
      ctx.fillText(front.acceptanceInformation.dateOfAcceptance || '', canvasWidth / 2 + gridSize * 2.3 * base, (firstY + firstHeight * 6 + secondHeight * 4) * base)

      // 承兑人保证信息
      ctx.fillText(front.acceptanceGuaranteeInformation.accountOfGuarantor || '', (canvasPadding + gridSize * 3.35) * base, (firstY + firstHeight * 4 + secondHeight * 6 + 8) * base)
      ctx.fillText(front.acceptanceGuaranteeInformation.nameOfGuarantor || '', (canvasPadding + gridSize * 3.35) * base, (firstY + firstHeight * 4.5 + secondHeight * 6 + 12) * base)
      ctx.fillText(front.acceptanceGuaranteeInformation.bankNameOfGuarantor || '', canvasWidth / 2 + gridSize * 1.8 * base, (firstY + firstHeight * 4 + secondHeight * 6 + 8) * base)
      ctx.fillText(front.acceptanceGuaranteeInformation.bankNoOfGuarantor || '', canvasWidth / 2 + gridSize * 1.8 * base, (firstY + firstHeight * 4.5 + secondHeight * 6 + 12) * base)

      // 评级信息
      ctx.fillText(front.ratingInformation.ticketIssuer.ratingSubject || '', (canvasPadding + gridSize * 4) * base, (firstY + firstHeight * 6 + secondHeight * 6) * base)
      ctx.fillText(front.ratingInformation.ticketIssuer.creditRating || '', canvasWidth / 2 + gridSize * 0.78 * base, (firstY + firstHeight * 6 + secondHeight * 6) * base)
      ctx.fillText(front.ratingInformation.ticketIssuer.maturityDateOfRating || '', canvasWidth / 2 + (gridSize * 5.1) * base, (firstY + firstHeight * 6 + secondHeight * 6) * base)

      ctx.fillText(front.Notes || '', (canvasPadding + gridSize * 2 + 10) * base, (firstY + firstHeight * 7 + secondHeight * 6) * base)
    }
    fillTableContent()

    // 将canvas转成图片
    const convertCanvasToImage = img => {
      let imgUrl = img.toDataURL('image/png')
      return imgUrl
    }
    return imageType === 'canvas' ? canvas : convertCanvasToImage(canvas)
  },

  /**
   * 生成背面图片
   * @param {object|string} draftData 票据数据
   * @param {string} imageType 返回的图片类型，canvas/url
   * @param {number} base 画布倍数，默认2
   * @param {Boolean} isempty 画布倍数，背面为空是否返回票面
   * @returns {string|object} blob/base64
   */
  async generateBackImg(draftData, imageType = 'canvas', base = 2, isempty = false) {
    const { front, back } = draftData
    // 如果反面返回的是空对象 或者没有值 就直接返回null (isempt有值的不返回空)
    if (!isempty && (!back || !back.history)) {
      const fun = {
        toDataURL: () => null
      }
      return imageType === 'canvas' ? fun : null
    }

    const hasSubRange = () => !!front.childTicketRange
    const subHeight = hasSubRange() ? 20 : 0

    const canvas = this.createCanvas(base, 992, 620 + subHeight)
    const ctx = canvas.getContext('2d')
    const canvasPadding = 20
    const canvasWidth = ctx.canvas.width
    const headY = 130 // 头部高度
    const tdHeight = 25 // 单元格高度
    const tableY = headY + canvasPadding + subHeight// 表格距离顶部高度
    const titleX = 280 // 单元格标题宽度
    let canvasHeight = ctx.canvas.height
    let canvasNewHeight = (back.history.length * tdHeight * 5 + headY + canvasPadding * 2 + subHeight) * base
    canvas.height = canvasNewHeight > canvasHeight ? canvasNewHeight : canvasHeight
    canvasHeight = ctx.canvas.height

    // 填充背景色
    const fillBgColor = () => new Promise(resolve => {
      const isBankDraft = String(front.ticketNumber).startsWith('1') || String(front.ticketNumber).startsWith('5') || String(front.ticketNumber).startsWith('8')
      // 创建线性渐变对象
      let gradientLinear = ctx.createLinearGradient(0, canvasHeight / 2, canvasWidth, canvasHeight / 2)

      if (isBankDraft) {
        gradientLinear.addColorStop(0, '#DCF1FA')
        gradientLinear.addColorStop(0.5, '#FFEBDB')
        gradientLinear.addColorStop(1, '#DCF1FA')
      } else {
        gradientLinear.addColorStop(0, '#ECD8EC')
        gradientLinear.addColorStop(0.5, '#F7EEBC')
        gradientLinear.addColorStop(1, '#ECD8EC')
      }

      // 填充线性渐变
      ctx.fillStyle = gradientLinear
      ctx.fillRect(0, 0, canvasWidth, canvasHeight)
      resolve()
    })
    fillBgColor()

    // 填充logo
    const fillLogo = () => new Promise(resolve => {
      const img = new Image()
      img.src = require('@imgs/draft/draft-logo.png')
      img.onload = () => {
        const { width, height } = img
        const y = 25
        ctx.drawImage(img, canvasPadding * base, y * base, width * base, height * base)
        resolve()
      }
    })
    await fillLogo()

    // 标题
    const fillTitle = () => {
      ctx.fillStyle = '#000000'
      ctx.font = `${26 * base}px system-ui, Simhei`
      ctx.textAlign = 'center'
      ctx.fillText(`${front.AC01 || '票面信息'}`, canvasWidth / 2, 95 * base)
    }
    fillTitle()

    // 文字头部
    const fillTextHeader = () => {
      const fontSize = 15
      ctx.font = `${fontSize * base}px system-ui, Simhei`
      ctx.textAlign = 'center'
      ctx.fillText(`票据号码：${front.ticketNumber}`, canvasWidth / 2, headY * base)
      if (hasSubRange()) {
        ctx.fillText(`子票区间：${front.childTicketRange}`, canvasWidth / 2, (headY + subHeight) * base)
      }
    }
    fillTextHeader()

    // 填充表格
    const fillTable = () => {
      ctx.lineWidth = base // 表格线条宽度

      ctx.beginPath()
      // 头横线
      ctx.moveTo(canvasPadding * base, tableY * base)
      ctx.lineTo(canvasWidth - (canvasPadding * base), tableY * base)
      back.history.forEach((item, index) => {
        // 左竖线
        ctx.moveTo(canvasPadding * base, (tableY + tdHeight * 5 * index) * base)
        ctx.lineTo(canvasPadding * base, (tableY + tdHeight * 5 * (index + 1)) * base)
        // 右竖线
        ctx.moveTo(canvasWidth - canvasPadding * base, (tableY + tdHeight * 5 * index) * base)
        ctx.lineTo(canvasWidth - canvasPadding * base, (tableY + tdHeight * 5 * (index + 1)) * base)

        // 中竖线
        ctx.moveTo((canvasPadding + titleX) * base, (tableY + tdHeight * (5 * index + 1)) * base)
        ctx.lineTo((canvasPadding + titleX) * base, (tableY + tdHeight * 5 * (index + 1)) * base)

        for (let i = 1; i <= 5; i++) {
          ctx.moveTo(canvasPadding * base, (tableY + tdHeight * (i + 5 * index)) * base)
          ctx.lineTo(canvasWidth - (canvasPadding * base), (tableY + tdHeight * (i + 5 * index)) * base)
        }
      })

      ctx.stroke()
    }

    // 填充暂无数据样式
    const fillNoData = () => {
      ctx.font = `${48 * base}px system-ui, Simhei`
      ctx.textAlign = 'center'
      ctx.fillText('暂无背书信息', canvasWidth / 2, 250 * base)
    }

    // 背面标题名字
    const backTitle = (titleName = '') => {
      if (titleName.includes('保证')) {
        // 保证背书
        return {
          title1: '保证人名称',
          title2: '被保证人名称',
          title3: '保证人地址',
          title4: '保证日期'
        }
      } else if (titleName.includes('质押')) {
        // 质押背书
        return {
          title1: '出质人名称',
          title2: '质权人名称',
          title3: '出质日期',
          title4: '质押解除日期'
        }
      } else {
        // 装让背书
        return {
          title1: '背书人名称',
          title2: '被背书人名称',
          title3: '是否可转让',
          title4: '背书日期'
        }
      }
    }

    // 表格固定内容填充
    const fillTableFixedContent = () => {
      const fontSize = 15
      ctx.font = `${fontSize * base}px system-ui, Simhei`
      ctx.textAlign = 'right'
      ctx.textBaseline = 'middle'
      back.history.forEach((item, index) => {
        let num = 0
        let tdTitle = backTitle(item.Titile)
        for (let i in tdTitle) {
          ctx.fillText(`${tdTitle[i]}：`, (canvasPadding + titleX) * base, (tableY + tdHeight * (num + 1.5 + 5 * index)) * base)
          num += 1
        }
      })
    }

    // 表格动态内容填充
    const fillTableContent = () => {
      ctx.textAlign = 'left'
      let keys = ['nameOfEndorser', 'nameOfEndorsee', 'transferred', 'dateOfEndorsement']

      back.history.forEach((item, index) => {
        let num = 0
        for (let i in keys) {
          ctx.textAlign = 'center'
          // 背书标题
          num || ctx.fillText(`${item.Titile}`, (canvasWidth / 2), (tableY + tdHeight * (num + 0.5 + 5 * index)) * base)
          ctx.textAlign = 'left'
          num += 1
          ctx.fillText(`${item[keys[i]] ?? ''}`, (canvasPadding * 2 + titleX) * base, (tableY + tdHeight * (num + 0.5 + 5 * index)) * base)
        }
      })
    }

    if (back.history.length) {
      fillTable()
      fillTableFixedContent()
      fillTableContent()
    } else {
      fillNoData()
    }

    // 将canvas转成图片
    const convertCanvasToImage = img => {
      let imgUrl = img.toDataURL('image/png')
      return imgUrl
    }
    return imageType === 'canvas' ? canvas : convertCanvasToImage(canvas)
  },

  /**
   * 生成正面或背面截图，imageType 为对应要导出的图片类型，canvas/url
   * @param {object} data 票据信息
   * @param {string} type 类型，正面传 front，背面传 back
   * @param {string} imageType 返回的图片类型，canvas/url
   * @param {Boolean} isempty 画布倍数，背面为空是否返回票面
   * @returns {string|object} blob/base64
   */
  screenshot(data, type, imageType, isempty) {
    return new Promise(resolve => {
      data = typeof data === 'string' ? JSON.parse(data) : data
      if (type === 'front') {
        let res = this.generateFrontImg(data, imageType)
        resolve(res)
      } else {
        let res = this.generateBackImg(data, imageType, 2, isempty)
        resolve(res)
      }
    })
  }
}

export default imgGenerator
