<template>
  <!-- 票据截图生成组件 -->
  <div class="market-draft-image-generator" />
</template>

<script>
import imgGenerator from './market-draft-image-generator.js'

export default {
  name: 'market-draft-image-generator',
  props: {
    // 票据识别后 dll 返回的数据 TODO: 支持数组
    data: Object
  },
  data() {
    return {}
  },
  methods: {
    // 生成正面和背面截图，imageType 为对应要导出的图片类型，canvas/blob
    generate(imageType) {
      return Promise.all([
        this.screenshot('front', imageType), // 正面
        this.screenshot('back', imageType), // 背面
      ])
    },
    // 生成正面或背面截图
    screenshot(type, imageType) {
      return imgGenerator.screenshot(this.data, type, imageType)
    }
  }
}
</script>
