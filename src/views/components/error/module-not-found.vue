<style lang="scss" scoped>
.module-not-found {
  padding: 60px 20px;
  text-align: center;
  background-color: #FFFFFF;
}

.el-icon-warning {
  font-size: 60px;
  color: $color-warning;
}

.title {
  margin-top: 20px;
  font-size: 24px;
}

.desc {
  margin-top: 20px;
  margin-bottom: 40px;
  font-size: 16px;
  color: $color-text-secondary;
}
</style>

<template>
  <div class="module-not-found">
    <i class="el-icon-warning" />
    <div class="title">数据加载失败</div>
    <div class="desc">很抱歉，可能由于网络问题，此页面未加载成功，请再试一次</div>
    <el-button type="primary" @click="refresh">重新加载</el-button>
  </div>
</template>

<script>
export default {
  name: 'module-not-found',
  methods: {
    refresh() {
      if (typeof this.$ipc?.send === 'function') {
        this.$ipc.send('RELOAD_IGNORING_CACHE')
      } else {
        location.reload()
      }
    }
  },
}
</script>
