<!-- mqtt 连接 -->
<style lang="scss" scoped>
.mqtt-connect-btn.el-button.is-border {
  margin-right: 16px;
  border-color: $--color-primary;
  padding: 8px;
  font-size: 14px;
  color: $--color-primary;
  background: $color-warning-sub;

  ::v-deep {
    [class*="el-icon-"] + span {
      margin-left: 2px;
    }
  }
}
</style>

<template>
  <el-tooltip
    v-if="mqttStatus !== 'connected' && !isConnectFirstTime"
    class="item"
    effect="dark"
    placement="bottom"
    :disabled="mqttStatus !== 'disconnected'"
    @input="v => visible = v"
  >
    <div slot="content">当前消息通知服务已断开！<br>请立即点击重新连接，以免错过消息通知！</div>
    <el-button
      type="primary"
      border
      class="mqtt-connect-btn"
      :loading="mqttStatus === 'connecting' || loading"
      @click="connect"
    >
      {{ mqttStatus === 'connected' ? '已连接' : (visible ? '重新连接' : '通知断开') }}
    </el-button>
  </el-tooltip>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  name: 'mqtt-connect',
  data() {
    return {
      visible: false, // 是否显示 tooltip
      loading: false, // 是否正在加载，调试用
      isConnectFirstTime: true, // 是否首次连接，也就是刚登录的时候会先进行连接，在这之后再显示当前按钮
    }
  },
  computed: {
    ...mapState('user', [
      'mqttStatus', // mqtt 连接状态, disconnected(未连接)/connecting(连接中)/connected(已连接)
      'isLogined'
    ]),
  },
  watch: {
    isLogined() {
      this.isConnectFirstTime = true
    },
    mqttStatus(mqttStatus) {
      if (mqttStatus === 'connected' || mqttStatus === 'disconnected') {
        this.isConnectFirstTime = false
      }
    },
  },
  methods: {
    // 连接 mqtt
    ...mapActions('user', ['connectMqtt']),

    // 连接 mqtt
    async connect() {
      // this.loading = true
      // setTimeout(() => {
      //   this.loading = false
      // }, 3000)
      try {
        await this.connectMqtt()
        this.$message.success('重新连接成功')
      } catch (e) {
        this.$message.warning('连接失败，请重试')
      }
    }
  }
}
</script>
