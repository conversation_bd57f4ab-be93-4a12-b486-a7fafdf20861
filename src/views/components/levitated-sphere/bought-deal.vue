// 悬浮球组价
<style lang="scss" scoped>
.bought-deal {
  position: absolute;
  top: 6px;
  right: 34px;
  z-index: 1;
  display: flex;
  justify-content: flex-end;
  width: 600px;
  height: 68px;
}

.box {
  position: relative;
  width: 100%;
}

.el-progress {
  height: 64px;
}

.left {
  position: absolute;
  top: 11px;
  right: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 62px 0 0 62px;
  padding: 0 30px 0 20px;
  height: 48px;
  background: linear-gradient(90deg, #FFD2C8, #FFF5F3 100%);
  transition: width .5s;
  transition-delay: .1s;

  .keywords {
    font-weight: 600;
    color: $color-warning;
  }

  .highest {
    line-height: 18px;
  }

  .close-btn {
    cursor: pointer;
    color: $color-text-secondary;
  }
}

.colse {
  display: none;
  width: 0;
  outline: none;
  box-shadow: none;
}

.right {
  cursor: pointer;

  .btn {
    position: absolute;
    top: 43px;
    right: -17px;
    border-radius: 12px;
    width: 88px;
    height: 21px;
    font-size: 12px;
    text-align: center;
    color: $color-FFFFFF;
    background: linear-gradient(270deg, #FF3C3C 0%, #FF9736);
    box-shadow: 0 2px 8px 0 rgb(255 104 56 / 20%);
  }
}

::v-deep {
  .el-divider {
    margin: 0  8px 0 5px;
    vertical-align: sub;
    height: 16px;
    color: $color-text-secondary;
  }

  .el-progress__text {
    top: 40%;
    font-size: 18px !important;
    font-weight: 600;
    color: #FF5029;
  }
}

.circle-bg {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 3px solid  $color-FFFFFF;
  border-radius: 50%;
  width: 68px;
  height: 68px;
  color: $color-FFFFFF;
  background: linear-gradient(270deg, #FF3C3C 0%, #FF9736 99%);
  box-shadow: 0 2px 8px 0 rgb(255 104 56 / 20%);
}

.content-bottom {
  margin-top: 2px;
}

.detail-btn {
  margin-left: 12px;
  font-size: 14px;
  color: $color-text-secondary;
}

.el-button--text::after {
  position: absolute;
  bottom: 0;
  content: "";
  left: 0;
  border-bottom: 1px solid $color-text-secondary;
  width: 100%;
}

.go-icon {
  margin-left: 11px;
}
</style>

<template>
  <!-- 我收到的隐藏悬浮球 -->
  <div class="bought-deal">
    <div class="box">
      <div class="left" :class="isColse && 'colse'">
        <div>
          <span class="close-btn" @click="onColse"> <icon class="arrow-icon" type="chengjie-left" />收起   </span> <el-divider direction="vertical" />
        </div>
        <div v-if="type === 'sale'" class="highest">
          <!--
            已累计完成 <span class="keywords">{{ levelData.currentPeriodAchievement || 0 }}</span> 笔
            实际有效 <span class="keywords">{{ levelData.currentPeriodValidAchievement || 0 }}</span> 笔
            预计返现 <span class="keywords">{{ levelData.alreadyReward || 0 }}</span> 元
          -->
          <div>
            极速订单已奖励 <span class="keywords">{{ levelData.alreadyReward || 0 }}</span> 元
          </div>
        </div>
        <div v-else class="ordinary">
          极速订单笔笔有现金奖励，<span class="keywords">最高80元/笔</span>，上不封顶 ；
        </div>
        <slot />
      </div>
      <div class="right" @click="onColse">
        <div class="circle-bg">
          <icon type="chengjie-liwu" size="40" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { formatTime } from '@/common/js/date'
import { COMPLETE_DATE } from '@/constant-storage'
import Storage from '@/common/js/storage' // 本地缓存对象
export default {
  name: 'bought-deal',
  props: {
    type: {
      type: String,
      default: '' // 'sale' ：显示进度
    },
    levelData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      isColse: false, // 是否关闭收起
      completeData: {},

    }
  },

  methods: {
    init() {
      // 初始化数据
      this.completeData = Storage.get(COMPLETE_DATE)

      this.isColse = this.completeData.isColse
    },
    onColse() {
      let currentDate = formatTime(Date.now(), 'YYYY-MM-DD')
      this.isColse = !this.isColse
      // 收起悬浮球 修改 Storage 保存 首次打开悬浮球的值改成关闭
      if (this.isColse && !this.completeData.isColse) {
        Storage.set(COMPLETE_DATE, { date: currentDate, isColse: true })
      }
    },
    goCerter() {
      this.$router.push('/user-center/coupon?tabs=receiveCentre')
    }
  }
}
</script>
