// 悬浮球组价
<style lang="scss" scoped>
.levitated-sphere {
  position: absolute;
  top: 6px;
  right: 42px;
  z-index: 1;
  display: flex;
  justify-content: flex-end;
  width: 500px;
  height: 64px;
}

.el-progress {
  height: 64px;
}

.left {
  position: absolute;
  top: 0;
  right: 29px;
  border-radius: 4px 0 0 4px;
  padding: 10px 12px;
  height: 64px;
  background: linear-gradient(90deg, #FFD2C8, #FFF5F3 100%);
  transition: width .5s;
  transition-delay: .1s;

  .highest {
    line-height: 42px;
  }

  .keywords {
    font-weight: 600;
    color: $color-warning;
  }

  .close-btn {
    margin-right: 40px;
    cursor: pointer;
    color: $color-text-secondary;
  }
}

.colse {
  display: none;
  width: 0;
  outline: none;
  box-shadow: none;
}

.right {
  cursor: pointer;

  .btn {
    position: absolute;
    top: 43px;
    right: -17px;
    border-radius: 12px;
    width: 88px;
    height: 21px;
    font-size: 12px;
    text-align: center;
    color: $color-FFFFFF;
    background: linear-gradient(270deg, #FF3C3C 0%, #FF9736);
    box-shadow: 0 2px 8px 0 rgb(255 104 56 / 20%);
  }
}

::v-deep {
  .el-divider {
    margin: 0 12px;
    vertical-align: sub;
    height: 16px;
  }

  .el-progress__text {
    top: 40%;
    font-size: 18px !important;
    font-weight: 600;
    color: #FF5029;
  }
}

.circle-bg {
  position: absolute;
  border-radius: 50%;
  width: 54px;
  height: 54px;
  background: $color-FFFFFF;
}

.content-bottom {
  margin-top: 2px;
}

.detail-btn {
  margin-left: 12px;
  font-size: 14px;
  color: $color-text-secondary;
}

.el-button--text::after {
  position: absolute;
  bottom: 0;
  content: "";
  left: 0;
  border-bottom: 1px solid $color-text-secondary;
  width: 100%;
}
</style>

<template>
  <!-- 我收到的隐藏悬浮球 -->
  <div v-if="type !== 'buy'" class="levitated-sphere">
    <div class="left" :class="isColse && 'colse'">
      <div v-if="levelData.highestLevel" class="highest">
        您已达到最高等级！  <el-divider direction="vertical" /><span class="close-btn" @click="onColse">收起   <icon class="arrow-icon" type="chengjie-right" /></span>
      </div>
      <div v-else class="ordinary">
        距奖励 <span class="keywords">{{ levelData.nextLevelRewardAmount || 0 }}</span> 消费券，
        还需{{ type !== 'buy' ? "完成" : "消费" }} <span class="keywords">{{ levelData.nextLevelGap || 0 }}{{ type !== 'buy' ? "笔" : `${sdmName}` }}</span>
        <el-divider direction="vertical" /><span class="close-btn" @click="onColse">收起   <icon class="arrow-icon" type="chengjie-right" /></span>
        <div class="content-bottom">
          已奖励  <span class="keywords">{{ levelData.alreadyReward || 0 }}</span> 消费券
          <el-button
            class="detail-btn"
            type="text"
            @click="goCerter"
          >
            <slot>查看详情</slot>
          </el-button>
        </div>
      </div>
    </div>
    <div class="right" @click="onColse">
      <!-- 用来让进度条里面的变成空白色 -->
      <div class="circle-bg" />
      <el-progress
        type="dashboard"
        :percentage="levelData.progressPercentage || 0"
        color="#FF5029"
        :width="54"
        :stroke-width="4"
      />
      <div class="btn">{{ type === "buy" ? `消费${sdmName}送券` : "完成笔数送券" }}</div>
    </div>
  </div>
</template>

<script>
import { formatTime } from '@/common/js/date'
import { CONSUME_DATE, COMPLETE_DATE } from '@/constant-storage'
import Storage from '@/common/js/storage' // 本地缓存对象
export default {
  name: 'levitated-sphere',
  props: {
    type: {
      type: String,
      default: 'buy'
    },
    levelData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      isColse: false, // 是否关闭收起
      consumeData: {},
      completeData: {},

    }
  },

  methods: {
    init() {
      // 初始化数据
      this.consumeData = Storage.get(CONSUME_DATE)
      this.completeData = Storage.get(COMPLETE_DATE)
      this.isColse = (this.type === 'buy' ? this.consumeData.isColse : this.completeData.isColse)
    },
    onColse() {
      let currentDate = formatTime(Date.now(), 'YYYY-MM-DD')
      this.isColse = !this.isColse
      // 收起悬浮球 修改 Storage 保存 首次打开悬浮球的值改成关闭
      if (this.isColse && !(this.type === 'buy' ? this.consumeData.isColse : this.completeData.isColse)) {
        this.type === 'buy' ? Storage.set(CONSUME_DATE, { date: currentDate, isColse: true }) : Storage.set(COMPLETE_DATE, { date: currentDate, isColse: true })
      }
    },
    goCerter() {
      this.$router.push('/user-center/coupon?tabs=receiveCentre')
    }
  }
}
</script>
