<!-- 单复选项组件 -->
<style scoped lang="scss">
.defect-type {
  flex: 1;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-left: 0;
}

.el-checkbox {
  margin: 0 0 8px 8px;
  padding: 10px 0;
  width: 104px;
  text-align: center;
}

::v-deep {
  .el-checkbox.is-bordered.el-checkbox--small .el-checkbox__label {
    font-size: 14px;
  }

  .el-checkbox__label {
    padding: 0;
  }

  .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    color: $--color-primary;
    background-color: $--color-primary-hover;
  }

  .el-checkbox .el-checkbox__input.is-checked + .el-checkbox__label {
    color: $--color-primary !important;
  }
}

.link {
  margin-left: 4px;
  height: 18px;
  line-height: 18px;

  @include example-underline;
}
</style>

<template>
  <el-checkbox-group
    v-model="checkboxValue"
    class="defect-type"
    :style="{marginLeft: `-${offset}`}"
    @change="checkboxChoose"
  >
    <el-checkbox
      v-for="item in option"
      :key="item.value"
      :label="item.value"
      type="button"
      class="el-checkbox"
      size="small"
      :style="{ width: width, marginLeft: offset, marginBottom: offset }"
      :disabled="item.disabled || item.key === 'acceptLianlianpay' || item.key === 'acceptAipay'"
    >
      {{ item.label }}
      <span
        v-if="item.isShowOpen"
        class="link"
        @click="opening(item)"
      >开通</span>
    </el-checkbox>
  </el-checkbox-group>
</template>

<script>
import { SITE_OPEN_ACCOUNT, SITE_YLYH_OPEN_ACCOUNT } from '@/event/modules/site'
import { ACCOUNT_STATUS, PAYMENT_CHANNEL, REAL_NAME_AUTH_TYPE } from '@/constant'
import { mapGetters } from 'vuex'
import openAccountApi from '@/apis/open-account'
export default {
  name: '',
  props: {
    // 选项的宽度
    width: {
      type: String,
      default: '104px'
    },
    // 选项之间间距
    offset: {
      type: String,
      default: '8px'
    },
    checkboxData: {
      type: Array,
      default: () => [null]
    },
    // 选项
    option: Array,
    // 单选
    radio: Boolean,
  },

  data() {
    return {
      checkboxValue: [], // 选中数据
      checkboxValueCopy: [], // 选中数据备份
    }
  },

  computed: {
    ...mapGetters('user', {
      corpInfo: 'corpInfo', // 企业信息
    }),
  },

  watch: {
    checkboxData() {
      this.setData()
    }
  },

  created() {
    this.setData()
  },

  methods: {
    setData() {
      this.checkboxValue = this.checkboxData
      this.checkboxValueCopy = this.checkboxData
    },
    // 票据类型多选事件
    checkboxChoose(arr) {
      if (this.radio) { // 单选
        if (arr.length === 0) {
          this.checkboxValue = this.checkboxValueCopy
          return
        }
        this.checkboxValue = [arr[arr.length - 1]]
      } else if (arr.length && arr[arr.length - 1] === null) {
        this.checkboxValue = [null]
      } else if (arr.length > 1 && arr[0] === null) {
        this.checkboxValue.splice(0, 1)
      } else if (!arr.length) {
        this.checkboxValue = [null]
      }

      this.checkboxValueCopy = this.checkboxValue

      this.$emit('change', this.checkboxValue)
    },
    // 开通
    async opening(channel) {
      if (channel.accountStatus === ACCOUNT_STATUS.LOGOUT.id) {
        this.$message.warning('暂不支持已注销账户的重新开户！')
        return
      }

      //  亿联银行开通直接打开弹框
      if (channel.value === PAYMENT_CHANNEL.YI_LIAN_BANK.id) {
        this.$event.emit(SITE_YLYH_OPEN_ACCOUNT, channel)
        return
      }
      // 查询是否在途接口 isRealName=> 0:渠道开通 1:实名认证
      const isInTransit = await openAccountApi.getExistOnPassageApply({ isRealName: 0, payChannel: channel.value })
      let obj = {
        reAuthPaymentChannel: channel.value,
        realNameAuthType: REAL_NAME_AUTH_TYPE.FAIL
      }
      // 渠道开通是否存在在途流程
      if (isInTransit) {
        obj.startOver = true // 是否查询流步骤接口
      }

      // let obj = {
      //   realNameAuthType: REAL_NAME_AUTH_TYPE.FAIL,
      // }
      // // 企业状态不是已开通状态 和 渠道是开通中，就去开户查进度
      // if (this.corpInfo.newestCorpOpenInfoApplyStatus < APPLICATION_STATUS.HAVE_BEEN_THROUGH.id || channel.accountStatus === ACCOUNT_STATUS.PENDING.id) {
      //   obj.startOver = true
      // }
      this.$event.emit(SITE_OPEN_ACCOUNT, obj)
    },
  }
}
</script>
