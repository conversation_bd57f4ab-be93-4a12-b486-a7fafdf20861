<style lang="scss" scoped>
::v-deep .drag-dialog {
  margin-right: 14px;
}

.box {
  border-radius: 10px;
  width: 80px;
  height: 22px;
}

.count {
  position: relative;
  color: $color-FFFFFF;

  .text {
    cursor: pointer;

    &:hover {
      border-bottom: 1px solid $color-FFFFFF;
    }

    width: 50px;
    font-size: 12px;
    font-weight: 500;
    line-height: 18px;
  }

  .icon {
    margin-left: 2px;
    color: rgba($color:$color-FFFFFF, $alpha: 80%);
  }

  .svg-icon {
    vertical-align: top;
  }
}

.form-wrapper {
  margin-top: 16px;

  .form-block {
    margin-bottom: 12px;
    padding: 16px;
    background: $color-FFFFFF;

    &:not(:last-child) .el-form-item:last-child {
      margin-bottom: 0;
    }
  }
}

.mb-s {
  margin-bottom: 8px;
}

.new-tag {
  position: absolute;
  top: -16px;
  right: -1px;

  @include new-icon;
}

.btn-cls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

::v-deep .el-button--text {
  padding: 5px !important;
}

.required {
  color: #F56C6C;
}

.pad20 {
  padding-bottom: 25px !important;
}
</style>

<style lang="scss">
@import "./reset-checkbox.scss";

.form-item_height {
  line-height: none;
}
</style>

<template>
  <div class="drag-dialog">
    <DragDialog
      title="投诉建议"
      :visible="visible"
      top="40px"
      width="610px"
      @toggle="handleDragDialog"
    >
      <template slot="button">
        <div class="count" @click="visible = true">
          <el-badge :value="complaintsNum" :hidden="complaintsNum > 0 ? false : true" :max="99">
            <div class="box">
              <div class="box-content">
                <span class="text">投诉建议</span>
                <icon size="20" type="erp-tousujianyi" class="icon" />
              </div>
            </div>
            <!-- <div v-if="complaintsNum === 0" class="new-tag">NEW</div> -->
          </el-badge>
        </div>
      </template>
      <WarnContent class-type="blue">
        感谢您对承接的反馈，您的信息将绝对保密，我们可能会对您进行回访。
      </WarnContent>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        class="form-wrapper g-reset-form"
        label-position="top"
        size="small"
      >
        <section class="form-block">
          <div class="g-title-small mb-s">投诉内容</div>
          <el-form-item label="系统功能" prop="systemFunction">
            <el-checkbox-group
              v-model="form.systemFunction"
              class="g-checkbox-custom"
              size="small"
            >
              <el-checkbox-button
                v-for="system in systemList"
                :key="system.id"
                :label="system.id"
              >
                {{ system.label }}
              </el-checkbox-button>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="客户经理" prop="complaintCustomerManagerEmployee">
            <el-checkbox-group v-model="form.complaintCustomerManagerEmployee" size="small" class="g-checkbox-custom">
              <el-checkbox-button
                v-for="item in customerManagerArray"
                :key="item.id"
                :label="item.id"
              >
                {{ item.label }}
              </el-checkbox-button>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="投诉对象" prop="object">
            <el-input v-model="form.object" placeholder="请填写您投诉对象的名字" />
          </el-form-item>
        </section>
        <section class="form-block pad20">
          <div class="g-title-small mb-s"><span class="required">*</span><span>投诉建议</span></div>
          <el-form-item prop="advice" class="form-item_height">
            <el-input
              v-model="form.advice"
              :rows="4"
              type="textarea"
              maxlength="200"
              show-word-limit
              placeholder="请填写您的投诉建议，您的宝贵意见会让平台越来越好"
            />
          </el-form-item>
        </section>
        <section class="form-block">
          <div class="g-title-small mb-s">联系方式</div>
          <el-form-item prop="phone">
            <el-input v-model="form.phone" type="number" placeholder="请留下您的联系方式，便于我们及时了解和响应您的问题" />
          </el-form-item>
        </section>
      </el-form>
      <div slot="footer">
        <div class="btn-cls">
          <div class="note">
            <el-badge :value="complaintsNum" :hidden="complaintsNum > 0 ? false : true" :max="99">
              <el-button size="large" type="text" @click="viewRecords">投诉记录</el-button>
            </el-badge>
          </div>
          <div>
            <el-button
              type="primary"
              border
              @click="handleCancel"
            >
              取消
            </el-button>
            <el-button
              v-waiting="commonApi.postComplainSuggest"
              type="primary"
              @click="handleSubmit"
            >
              提交
            </el-button>
          </div>
        </div>
      </div>
    </DragDialog>
    <RecordList ref="recordListRef" @queryCountByCorpId="queryComplaintsNum" />
  </div>
</template>

<script>
import DragDialog from '@/views/components/common/drag-dialog/drag-dialog.vue'
import WarnContent from '@/views/components/common/warn-content.vue'
import RecordList from './record-list.vue'
import commonApi from '@/apis/common'
export default {
  name: 'complain-suggest',
  components: { DragDialog, WarnContent, RecordList },
  data() {
    const checkPhone = (rule, value, callback) => {
      let reg = /^1[2|3|4|5|6|7|8|9]\d{9}$/
      if (value && !reg.test(value)) {
        callback(new Error('请输入正确的手机号码'))
      }
      callback()
    }
    return {
      commonApi,
      visible: false,
      form: {
        systemFunction: [],
        complaintCustomerManagerEmployee: [],
        object: '',
        advice: '',
        phone: ''
      },
      rules: {
        phone: [{ validator: checkPhone, trigger: 'blur' }],
        advice: [
          {
            required: true,
            message: '请输入投诉建议',
            trigger: ['change', 'blur']
          }
        ]
      },
      // 系统功能选项
      systemList: [
        {
          label: '系统卡顿',
          id: 1
        },
        {
          label: '功能不好用',
          id: 2
        },
        {
          label: '页面排版不美观',
          id: 3
        },

      ],
      // 客户经理选项
      customerManagerArray: [
        {
          label: '服务态度不好',
          id: 1
        },
        {
          label: '响应速度慢',
          id: 2
        },
        {
          label: '解决问题能力差',
          id: 3
        }
      ],
      complaintsNum: 0, // 投诉建议回复数量
    }
  },
  watch: {
    '$store.state.user.isShowAi': {
      handler(val) {
        val && this.systemList.push(({
          label: 'AI助手',
          id: 4
        }))
      },
      immediate: true
    }
  },
  mounted() {
    this.queryComplaintsNum()
  },
  methods: {
    // 提交
    handleSubmit() {
      this.$refs.formRef.validate(async valid => {
        if (!valid) return
        const { systemFunction, complaintCustomerManagerEmployee, object, advice } = this.form
        if (systemFunction.length === 0 && complaintCustomerManagerEmployee.length === 0 && !object && !advice) {
          return this.$message.error('请填写投诉内容')
        }
        try {
          await commonApi.postComplainSuggest(this.form)
          this.$message.success('感谢您的反馈')
          this.handleCancel()
        } catch (error) {
        // eslint-disable-next-line
        console.log(error)
        }
      })
    },
    // 取消
    handleCancel() {
      this.visible = false
      this.clearForm()
    },
    // 清空表单数据
    clearForm() {
      this.form = {
        systemFunction: [],
        complaintCustomerManagerEmployee: [],
        object: '',
        advice: '',
        phone: ''
      }
    },
    // 查询投诉记录回复数
    async queryComplaintsNum() {
      const res = await commonApi.queryCountByCorpId()
      this.complaintsNum = res || 0
    },
    // 拖动窗口显示回调
    handleDragDialog(val) {
      // 窗口关闭
      if (!val) {
        this.clearForm()
        // 即使修改了toolbarType为null不被选中,但由于该组件处理Toolbar内,也会被el-radio重新赋值为选中状态,故加入setTimeout延迟修改toolbarType,避开el-radio的重新赋值
        setTimeout(() => {
          this.visible = false
        }, 0)
      } else {
        this.queryComplaintsNum()
      }
    },
    // 查看记录
    viewRecords() {
      this.$refs.recordListRef.init()
    },
    async updateComplaintsNum() {
      const res = await commonApi.updateCountByCorpId()
      this.complaintsNum = res || 0
    }

  }
}
</script>
