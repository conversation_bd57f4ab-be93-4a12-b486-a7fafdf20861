// 重置el-checkbox-group按钮样式
.g-checkbox-custom {
  .el-checkbox-button__inner {
    height: 32px;
    font-size: 14px;
    transition: none;
  }

  .el-checkbox-button + .el-checkbox-button {
    margin-left: 8px;

    .el-checkbox-button__inner:not(.is-checked) {
      border-left: 1px solid $--border-color-base;
    }
  }

  .el-checkbox-button.is-checked .el-checkbox-button__inner {
    border: none !important;
    color: $color-FFFFFF !important;
    background: $button-background;
    box-shadow: none;
  }

  .el-checkbox-button.is-focus:not(.is-checked) {
    .el-checkbox-button__inner {
      border: 1px solid $--border-color-base;
    }
  }
}

// 重置form-item
.g-reset-form.el-form--label-top {
  .el-form-item__label {
    padding: 0;
    line-height: 30px;
  }

  .el-form-item {
    margin-bottom: 12px;
  }
}
