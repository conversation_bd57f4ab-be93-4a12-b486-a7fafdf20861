<style lang="scss" scoped >
.content-warp {
  overflow: hidden;
  overflow-y: scroll;
  max-height: 640px;

  .item {
    margin-bottom: 15px;
    border-radius: 5px;
    padding: 10px;
    background: #FFFFFF;

    &:last-child {
      margin-bottom: 0;
    }

    .font-cls {
      padding-bottom: 8px;
      font-size: 16px;
    }

    .color {
      color: #376AF0;
    }

    .line {
      margin-bottom: 15px;
      border: 1px solid #F8F8F8;
    }
  }
}

.dialog-footer {
  justify-content: center !important;
}

.el-empty {
  .el-empty__image {
    width: auto;
  }

  .el-empty__description {
    margin-top: -20px;
  }

  .el-empty__description p {
    font-size: 18px;
    color: $color-text-secondary;
  }
}

.empty-box {
  display: flex;

  .empty-img {
    font-size: 14em;
    color: #FFFFFF;
  }
}
</style>

<template>
  <div>
    <DragDialog
      title="投诉记录"
      describe="（显示近90天的投诉记录）"
      :visible="visible"
      top="40px"
      width="700px"
      @toggle="handleDragDialog"
    >
      <template v-if="recordList.length">
        <div class="content-warp">
          <div v-for="(item, index) in recordList" :key="index" class="item">
            <div v-if="item.createdAt" class="font-cls">投诉时间：{{ formatTime(item.createdAt, 'YYYY-MM-DD hh:mm:ss') }}</div>
            <div v-if="item.content" class="font-cls">投诉内容：{{ item.content }}</div>
            <div v-if="item.object" class="font-cls">投诉对象：{{ item.object }}</div>
            <div v-if="item.advice" class="font-cls">投诉建议：{{ item.advice }}</div>
            <div v-if="item.phone" class="font-cls">联系方式：{{ item.phone }}</div>
            <!-- 平台回复才显示分割线 -->
            <div v-if="item.handleAt" class="line" />
            <div v-if="item.handleAt" class="font-cls color">{{ formatTime(item.handleAt, 'YYYY-MM-DD hh:mm:ss') }}</div>
            <div v-if="item.handleResult" class="font-cls color">平台回复：{{ item.handleResult }}</div>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-pagination
            :current-page.sync="query.pageNum"
            :page-size="query.pageSize"
            background
            layout="total,sizes, prev, pager, next"
            :total="totalRecord"
            :page-sizes="[10, 20, 50, 100]"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </span>
      </template>
      <el-empty v-else>
        <div slot="image" class="empty-box">
          <icon type="chengjie-empty" class="empty-img" />
        </div>
      </el-empty>
    </DragDialog>
  </div>
</template>

<script>
import DragDialog from '@/views/components/common/drag-dialog/drag-dialog.vue'
import commonApi from '@/apis/common'
import { formatTime } from '@/common/js/date' // 时间格式化
export default {
  name: 'record-list',
  components: {
    DragDialog
  },
  props: {},
  data() {
    return {
      formatTime,
      visible: false,
      recordList: [],
      query: {
        pageNum: 1, // 当前页码
        pageSize: 10, // 每页条数
      },
      totalRecord: 0, // 总条数
    }
  },
  methods: {
    async init() {
      this.visible = true
      await this.getList()
      // 更新投诉建议回复数
      await this.updateCountByCorpIdNum()
    },
    // 获取投诉记录列表
    async getList() {
      const res = await commonApi.queryListByCorpId(this.query)
      this.recordList = res.rowList
      this.totalRecord = res.totalRecord
    },
    // 更新投诉记录数量
    async updateCountByCorpIdNum() {
      await commonApi.updateCountByCorpId()
      // 通知查询新的建议回复数量
      // eslint-disable-next-line vue/custom-event-name-casing
      this.$emit('queryCountByCorpId')
    },
    // 更改每页条数
    handleSizeChange(val) {
      this.query.pageSize = val
      this.getList()
    },

    // 更改当前页
    handleCurrentChange(val) {
      this.query.pageNum = val
      this.getList()
    },
    // 拖动窗口显示回调
    handleDragDialog(val) {
      // 窗口关闭
      if (!val) {
        setTimeout(() => {
          this.visible = false
        }, 0)
      }
    },
  },
}
</script>
