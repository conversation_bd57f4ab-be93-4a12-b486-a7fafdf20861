<!-- 新的行号查询 -->
<style lang="scss" scoped>
::v-deep .drag-dialog {
  margin-right: 18px;
}

.box {
  border-radius: 10px;
  width: 76px;
  height: 22px;
}

.box-content {
  color: $color-FFFFFF;
}

.count {
  position: relative;
  color: $--color-primary;

  .text {
    cursor: pointer;

    &:hover {
      border-bottom: 1px solid $color-FFFFFF;
    }

    width: 49px;
    font-size: 12px;
    font-weight: 500;
    line-height: 18px;
  }

  .icon {
    margin-left: 2px;
    color: rgba($color:$color-FFFFFF, $alpha: 80%);
  }

  .svg-icon {
    vertical-align: top;
  }
}

.content {
  padding: 12px 16px;
  background-color: $color-FFFFFF;

  .query-box {
    display: flex;
    width: 100%;

    .input-query {
      width: 410px;
      flex: 1;

      ::v-deep .el-input__inner {
        border-color: $color-D9D9D9;
        font-size: 16px;
      }
    }

    .btn-query {
      margin-left: 12px;
      padding-right: 0;
      padding-left: 0;
      min-width: 96px;
    }
  }

  .table-box {
    margin-top: 12px;
  }

  .empty-box {
    transform: translateY(-20px);

    .empty-img {
      font-size: 10.35em;
      color: #FFFFFF;
    }

    .empty-text {
      transform: translateY(-30px);
      font-size: 18px;
      color: $color-text-secondary;
      line-height: 26px;
    }
  }
}

// el组件样式
::v-deep {
  .el-table {
    border-color: $color-D9D9D9;

    .el-table__cell {
      padding: 6px 0;
    }

    th.el-table__cell.is-leaf,
    td.el-table__cell {
      border-color: $color-D9D9D9;
    }
  }

  .el-table::before,
  .el-table--group::after,
  .el-table--border::after {
    background-color: $color-D9D9D9;
  }

  .form {
    .el-form-item {
      margin-bottom: 0;
    }

    .el-form-item__error {
      position: relative;
      top: auto;
      left: auto;
    }
  }
}
</style>

<template>
  <div class="drag-dialog">
    <DragDialog
      title="行号查询"
      width="600px"
      :visible="visible"
      @toggle="handleDragDialog"
    >
      <template slot="button">
        <div class="count" @click="visible = true">
          <div class="box">
            <div class="box-content">
              <span class="text">行号查询</span>
              <icon size="20" type="erp-hanghaochaxun" class="icon" />
            </div>
          </div>
        </div>
      </template>

      <div class="content">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          class="form"
        >
          <el-form-item
            prop="bankInput"
            class="query-box"
          >
            <el-input
              v-model="form.bankInput"
              class="input-query"
              placeholder="请输入银行联号或银行名称关键字"
              clearable
              @keyup.enter.native="submitForm('form')"
              @clear="clearData()"
            />
            <el-button
              v-waiting="openAccountApi.getBankSearch"
              class="btn-query"
              type="primary"
              @click="submitForm('form')"
            >
              查询
            </el-button>
          </el-form-item>
        </el-form>

        <el-table
          :data="bankBranchCodeTable"
          border
          style="width: 100%;"
          height="397"
          class="table-box"
        >
          <el-table-column
            prop="branchCnaps"
            label="行号"
            min-width="130"
            show-overflow-tooltip
          />
          <el-table-column
            prop="branchName"
            label="银行名称"
            min-width="389"
          />

          <template slot="empty">
            <div class="empty-box">
              <icon type="chengjie-empty" class="empty-img" />
              <p class="empty-text">暂无数据</p>
            </div>
          </template>
        </el-table>
      </div>
    </DragDialog>
  </div>
</template>

<script>
import DragDialog from '@/views/components/common/drag-dialog/drag-dialog.vue' // 可拖动弹窗
import openAccountApi from '@/apis/open-account'

export default {
  name: 'bank-branch-code-query',
  components: {
    DragDialog
  },

  data() {
    return {
      openAccountApi,
      form: {
        bankInput: null, // 银行联号或关键字输入
      },
      // 校验规则
      rules: {
        bankInput: [{ required: true, message: '请输入银行联号或银行名称关键字', trigger: 'none' }]
      },
      bankBranchCodeTable: [], // 行号查询结果表格
      visible: false,
    }
  },

  methods: {
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.queryBankBranchCode()
        } else {
          return false
        }
      })
    },

    // 查询行号
    async queryBankBranchCode() {
      const res = await openAccountApi.getBankSearch({
        branchName: this.form.bankInput,
      })

      this.bankBranchCodeTable = res
    },

    // 拖动窗口显示回调
    handleDragDialog(val) {
      // 窗口关闭，清空数据
      if (!val) {
        this.clearData()
        // 即使修改了toolbarType为null不被选中,但由于该组件处理Toolbar内,也会被el-radio重新赋值为选中状态,故加入setTimeout延迟修改toolbarType,避开el-radio的重新赋值
        setTimeout(() => {
          this.visible = false
        }, 0)
      }
    },

    // 清空数据
    clearData() {
      this.bankBranchCodeTable = []
      this.form.bankInput = null
    },

  }
}
</script>
