<style lang="scss" scoped>
.quotation-mt {
  border: 1px solid #ADE1C9;
  padding: 16px;
  width: 100%;
  background: linear-gradient(180deg, rgb(15 159 92 / 15%) 0%, rgb(17 165 96 / 0%) 28%), #FFFFFF;

  .quotation-mt-title {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .close-icon {
      cursor: pointer;
      color: #A6A4A4;
    }
  }

  .quotation-mt-title-text {
    font-size: 16px;
    font-weight: 500;

    .primary-text {
      color: $--color-primary;
    }
  }

  .desc {
    padding-top: 4px;
    font-size: 14px;
    color: #999999;
  }

  .split-line {
    margin: 8px 0;
    width: 100%;
    height: 2px;
    background: #F0F0F0;
  }

  .quotation-mt-content {
    display: flex;
    gap: 0 48px;

    .item {
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      font-weight: 500;

      .title {
        color: #999999;
      }

      .value {
        color: #333333;
      }

      .red {
        color: #FF0000;
      }
    }
  }
}

.hidden {
  display: none;
}

.flex-column {
  flex-direction: column;
}

.p-t-8 {
  padding-left: 8px;
}

.p-t-4 {
  padding-top: 4px;
}

.m-b-8 {
  margin-bottom: 8px;
}
</style>

<template>
  <div v-if="visible" class="quotation-mt">
    <div class="quotation-mt-title">
      <div class="quotation-mt-title-text">
        <span class="primary-text">秒贴</span>报价
      </div>
      <icon
        class="close-icon"
        size="18"
        type="chengjie-guanbi"
        @click="close"
      />
    </div>
    <div class="desc">如有意向，请联系客户经理贴现</div>
    <div class="split-line" />
    <div class="quotation-mt-content">
      <div :class="['item', isDialog ? 'flex-column' : '']">
        <div class="title">秒贴行</div>
        <div :class="['value', isDialog ? 'p-t-4' : 'p-t-8']">{{ quotation.discountBank || '-' }}</div>
      </div>
      <div :class="['item', isDialog ? 'flex-column' : '']">
        <div class="title">每十万扣款</div>
        <div :class="['value', isDialog ? 'p-t-4' : 'p-t-8']">{{ quotation.lakhFee || quotation.lakhFee === 0 ? quotation.lakhFee + '元' : '-' }}</div>
      </div>
      <div :class="['item', isDialog ? 'flex-column' : '']">
        <div class="title">年化利率</div>
        <div :class="['value', isDialog ? 'p-t-4' : 'p-t-8']">{{ quotation.annualInterest || quotation.annualInterest === 0 ? `${quotation.annualInterest}%` : '-' }}</div>
      </div>
      <div :class="['item', isDialog ? 'flex-column' : '']">
        <div class="title">预计到账金额</div>
        <div :class="['value', isDialog ? 'p-t-4' : 'p-t-8', 'red']">{{ quotation.reallyAmt || quotation.reallyAmt === 0 ? quotation.reallyAmt + '万元' : '-' }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import issueDraftApi from '@/apis/issue-draft' // 接口
export default {
  name: 'quotation-mt',
  props: {
    // 是否窗口
    isDialog: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      quotation: {},
      visible: false
    }
  },
  methods: {
    async getMtQuotation(params) {
      const res = await issueDraftApi.getMtQuotation(params)
      this.quotation = res
      this.visible = res.showQuotationFlag // 是否显示报价
    },
    close() {
      this.visible = false
    }
  }
}
</script>
