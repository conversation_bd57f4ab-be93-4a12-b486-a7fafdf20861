<style lang="scss" scoped>
.market-draft-image {
  padding: 20px;
  width: 992px;
  background-repeat: repeat-y;
  background-size: contain;
  background-image: url("https://oss.chengjie.red/web/imgs/draft/draft-bg.png");
}

.logo-container {
  margin-top: 10px;
}

.title {
  text-align: center;
}

.front-header {
  margin-top: 10px;
}

.header-item {
  line-height: 20px;
}

.header-left,
.header-right {
  display: inline-block;
  width: 50%;
}

.table {
  margin-top: 6px;
  border-width: 1px;
  width: 100%;
  font-size: 14px;
  text-align: left;
  color: $color-text-primary;
  border-collapse: collapse;
}

td {
  border: 1px solid $color-text-primary;
  padding: 2px 4px;
  box-sizing: border-box;
}

// border-collapse: collapse 在 html2canvas 中不生效，需手动将多余边框去掉
.table tr > td + td {
  border-left-width: 0;
}

.table tr + tr > td {
  border-top-width: 0;
}

.label {
  padding: 0 4px;
  text-align: center;
}

.content {
  // padding: 2px 4px;
  // min-height: 40px;
}

.front-table {
  .td-content {
    line-height: 22px;
  }

  .td-content-label {
    display: inline-block;
    text-align: right;
  }

  .left .td-content-label {
    width: 95px;
  }

  .right .td-content-label {
    width: 122px;
  }

  .placeholder {
    display: inline-block;
    width: 3em;
  }

  .div-td {
    display: inline-block;
    margin-right: 2px;
    border-right: 1px solid $color-text-primary;
    padding: 0 2px;
  }

  .rate-item {
    display: inline-block;
    width: 252px;
  }
}

.back-header {
  margin-top: 20px;
  text-align: center;
}

.back-sub-range {
  text-align: center;
}

.back-table {
  .tr-title {
    text-align: center;
  }

  .label {
    width: 280px;
    text-align: right;
  }
}

.no-back-data {
  margin-top: 45px;
  height: 60vh;
  font-size: 25px;
  text-align: center;
}

.small-window {
  .header-left {
    width: 35%;
    font-size: 13px;
  }

  .header-right {
    width: 65%;
    font-size: 13px;
  }

  .rate-item {
    width: 116px;
  }

  .placeholder {
    width: 2em;
  }

  .label {
    width: 180px;
  }
}
</style>

<template>
  <div v-if="draftData" class="market-draft-image" :class="isSmallWindow && 'small-window'">
    <div class="logo-container">
      <img src="https://oss.chengjie.red/web/imgs/draft/draft-logo.gif" alt="logo" class="logo">
    </div>
    <h2 class="title">{{ data.front.AC01 || '票面信息' }}</h2>
    <!-- 正面 -->
    <template v-if="type === 'front'">
      <div class="front-header">
        <div class="header-item">
          <div class="header-left">出票日期：{{ draftData.issueDateTime }}</div>
          <div class="header-right">票据状态：{{ draftData.ticketStatus }}</div>
        </div>
        <div class="header-item">
          <div class="header-left">汇票到期日：{{ draftData.expiredDateTime }}</div>
          <div class="header-right">票据号码：{{ draftData.ticketNumber }}</div>
        </div>
        <div v-if="draftData.childTicketRange" class="header-item">
          <div class="header-left" />
          <div class="header-right">子票区间：{{ draftData.childTicketRange }}</div>
        </div>
      </div>
      <table class="table front-table">
        <tbody>
          <tr>
            <td class="label" rowspan="4" style="border-right: none; width: 60px;">出票人</td>
            <td class="label" style=" border-left-width: 1px; width: 80px;">账号</td>
            <td class="content" style="width: 339px;">{{ draftData.ticketIssuer.accountNumber }}</td>
            <td class="label" rowspan="4" style="width: 60px;">收款人</td>
            <td class="label" style="width: 80px;">账号</td>
            <td class="content" style="width: 339px;">{{ draftData.payee.accountNumber }}</td>
          </tr>
          <tr>
            <td class="label" style=" height: 46px;">全称</td>
            <td class="content">{{ draftData.ticketIssuer.fullName }}</td>
            <td class="label">全称</td>
            <td class="content">{{ draftData.payee.fullName }}</td>
          </tr>
          <tr>
            <td class="label" style=" height: 46px;">开户行</td>
            <td class="content">{{ draftData.ticketIssuer.bankOfDeposit }}</td>
            <td class="label">开户行</td>
            <td class="content">{{ draftData.payee.bankOfDeposit }}</td>
          </tr>
          <tr>
            <td class="label">开户行号</td>
            <td class="content">{{ draftData.ticketIssuer.bankNo }}</td>
            <td class="label">开户行号</td>
            <td class="content">{{ draftData.payee.bankNo }}</td>
          </tr>
          <tr>
            <td class="label" colspan="2">出票人保证信息</td>
            <td class="content left">
              <div class="td-content">
                <div class="td-content-label">保证人账号：</div>
                <span class="td-content-content">{{ draftData.ticketGuaranteeInformation.accountOfGuarantor }}</span>
              </div>
              <div class="td-content">
                <div class="td-content-label">保证人名称：</div>
                <span class="td-content-content">{{ draftData.ticketGuaranteeInformation.nameOfGuarantor }}</span>
              </div>
              <div class="td-content" />
            </td>
            <td class="content right" colspan="3">
              <div class="td-content">
                <div class="td-content-label">保证人开户行：</div>
                <span class="td-content-content">{{ draftData.ticketGuaranteeInformation.bankNameOfGuarantor }}</span>
              </div>
              <div class="td-content">
                <div class="td-content-label">保证人开户行号：</div>
                <span class="td-content-content">{{ draftData.ticketGuaranteeInformation.bankNoOfGuarantor }}</span>
              </div>
            </td>
          </tr>
          <tr>
            <td class="label" colspan="2">票据金额</td>
            <td class="content left">
              <div class="td-content">
                <div class="td-content-label">小<div class="placeholder" />写：</div>
                <span class="td-content-content">{{ draftData.amountOfTicket.Digits }}</span>
              </div>
            </td>
            <td class="content right" colspan="3">
              <div class="td-content">
                <div class="td-content-label">人民币（大写）：</div>
                <span class="td-content-content">{{ draftData.amountOfTicket.Upper }}</span>
              </div>
            </td>
          </tr>
          <tr>
            <td class="label" colspan="2">承兑人</td>
            <td class="content left">
              <div class="td-content">
                <div class="td-content-label">承兑人账号：</div>
                <span class="td-content-content">{{ draftData.accepteeInformation.accountNumber }}</span>
              </div>
              <div class="td-content">
                <div class="td-content-label">承兑人名称：</div>
                <span class="td-content-content">{{ draftData.accepteeInformation.fullName }}</span>
              </div>
              <div class="td-content" />
            </td>
            <td class="content right" colspan="3">
              <div class="td-content">
                <div class="td-content-label">承兑人开户行：</div>
                <span class="td-content-content">{{ draftData.accepteeInformation.bankOfDeposit }}</span>
              </div>
              <div class="td-content">
                <div class="td-content-label">承兑人开户行号：</div>
                <span class="td-content-content">{{ draftData.accepteeInformation.bankNo }}</span>
              </div>
            </td>
          </tr>
          <tr>
            <td class="label" colspan="2">交易合同号：</td>
            <td class="content left">{{ draftData.transactionContractNo }}</td>
            <td class="label" rowspan="3" style="padding: 0;">承兑信息</td>
            <td class="content right" colspan="2">出票人承诺：{{ draftData.acceptanceInformation.promiseOfDrawer }}</td>
          </tr>
          <tr>
            <td class="label" rowspan="2" colspan="2">是否可转让：</td>
            <td class="content left" rowspan="2">{{ draftData.transferred }}</td>
            <td class="content right" colspan="2">承兑人承兑：{{ draftData.acceptanceInformation.acceptanceByAcceptor }}</td>
          </tr>
          <tr>
            <td class="content right" colspan="2">承兑日期：{{ draftData.acceptanceInformation.dateOfAcceptance }}</td>
          </tr>
          <tr>
            <td class="label" colspan="2">承兑人保证信息</td>
            <td class="content left">
              <div class="td-content">
                <div class="td-content-label">保证人账号：</div>
                <span class="td-content-content">{{ draftData.acceptanceGuaranteeInformation.accountOfGuarantor }}</span>
              </div>
              <div class="td-content">
                <div class="td-content-label">保证人名称：</div>
                <span class="td-content-content">{{ draftData.acceptanceGuaranteeInformation.nameOfGuarantor }}</span>
              </div>
              <div class="td-content" />
            </td>
            <td class="content right" colspan="3">
              <div class="td-content">
                <div class="td-content-label">保证人开户行：</div>
                <span class="td-content-content">{{ draftData.acceptanceGuaranteeInformation.bankNameOfGuarantor }}</span>
              </div>
              <div class="td-content">
                <div class="td-content-label">保证人开户行号：</div>
                <span class="td-content-content">{{ draftData.acceptanceGuaranteeInformation.bankNoOfGuarantor }}</span>
              </div>
            </td>
          </tr>
          <tr>
            <td class="label" colspan="2">评级信息</td>
            <td class="content" colspan="4" style="padding: 0;">
              <div class="div-td">出票人</div>
              <div class="rate-item">评级主体：{{ draftData.ratingInformation.ticketIssuer.ratingSubject }}</div>
              <div class="rate-item">信用等级：{{ draftData.ratingInformation.ticketIssuer.creditRating }}</div>
              <div class="rate-item">评级到期日：{{ draftData.ratingInformation.ticketIssuer.maturityDateOfRating }}</div>
            </td>
          </tr>
          <tr>
            <td class="label" colspan="2">备<div class="placeholder" />注</td>
            <td class="content" colspan="4">{{ draftData.Notes }}</td>
          </tr>
        </tbody>
      </table>
    </template>
    <!-- 背面 -->
    <template v-else-if="type === 'back'">
      <div class="back-header">票据号码：{{ data.front.ticketNumber }}</div>
      <div v-if="data.front.childTicketRange" class="back-sub-range">子票区间：{{ data.front.childTicketRange }}</div>
      <table class="table back-table">
        <div v-if="!draftData.history.length" class="no-back-data">
          暂无背书信息
        </div>
        <tbody>
          <template v-for="(item, index) in draftData.history">
            <tr :key="`Titile${index}`">
              <td class="tr-title" colspan="2">{{ item.Titile }}</td>
            </tr>
            <tr :key="`nameOfEndorser${index}`">
              <td class="label">{{ backTitle(item.Titile).title1 }}：</td>
              <td class="content">{{ item.nameOfEndorser }}</td>
            </tr>
            <tr :key="`nameOfEndorsee${index}`">
              <td class="label">{{ backTitle(item.Titile).title2 }}：</td>
              <td class="content">{{ item.nameOfEndorsee }}</td>
            </tr>
            <tr :key="`transferred${index}`">
              <td class="label">{{ backTitle(item.Titile).title3 }}：</td>
              <td class="content">{{ item.transferred }}</td>
            </tr>
            <tr :key="`dateOfEndorsement${index}`">
              <td class="label">{{ backTitle(item.Titile).title4 }}：</td>
              <td class="content">{{ item.dateOfEndorsement }}</td>
            </tr>
          </template>
        </tbody>
      </table>
    </template>
  </div>
</template>

<script>
import html2canvas from 'html2canvas'

const draftBg = 'https://oss.chengjie.red/web/imgs/draft/draft-bg.png'
const draftLogo = 'https://oss.chengjie.red/web/imgs/recognize/draft-logo.gif'

export default {
  name: 'market-draft-image',

  props: {
    // 显示类型，正面或背面
    type: {
      type: String,
      default: 'front',
      validator(type) {
        return ['front', 'back'].includes(type)
      }
    },
    // 票据识别后 dll 返回的数据
    data: {
      type: Object
    },
    // 是否是小窗口
    isSmallWindow: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      loadImgsPromise: null, // 加载图片的 promise
    }
  },

  computed: {
    // 正面或背面票据信息
    draftData() {
      return this.data[this.type]
    }
  },

  mounted() {
    this.$emit('mounted')
  },

  methods: {
    // 加载图片
    loadImages() {
      if (!this.loadImgsPromise) {
        // 加载单张图片
        const loadImg = src => new Promise((resolve, reject) => {
          const img = new Image()
          img.src = src
          img.addEventListener('load', () => resolve(src))
          img.addEventListener('error', () => reject(src))
        })
        this.loadImgsPromise = Promise.all([loadImg(draftLogo), loadImg(draftBg)])
      }
      return this.loadImgsPromise
    },

    // 生成截图, type 为 blob 或 canvas
    async screenshot(type = 'blob') {
      // 要先等图片加载完，否则渲染的 canvas 高度不对
      await this.loadImages()
      const canvas = await html2canvas(this.$el)
      if (type === 'canvas') {
        return canvas
      }
      return new Promise(resolve => {
        canvas.toBlob(blob => {
          // TODO: 将 blob 或转为 file 上传至 OSS
          resolve(blob)
        })
      })
    },

    // 背面标题名字
    backTitle(titleName) {
      if (titleName.includes('保证')) {
        // 保证背书
        return {
          title1: '保证人名称',
          title2: '被保证人名称',
          title3: '保证人地址',
          title4: '保证日期'
        }
      } else if (titleName.includes('质押')) {
        // 质押背书
        return {
          title1: '出质人名称',
          title2: '质权人名称',
          title3: '出质日期',
          title4: '质押解除日期'
        }
      } else if (titleName.includes('回购')) {
        // 回购式贴现
        return {
          title1: '贴出人',
          title2: '贴入人',
          title3: '贴现日期',
          title4: '回购日期'
        }
      } else {
        // 装让背书
        return {
          title1: '背书人名称',
          title2: '被背书人名称',
          title3: '是否可转让',
          title4: '背书日期'
        }
      }
    }

  }
}
</script>
