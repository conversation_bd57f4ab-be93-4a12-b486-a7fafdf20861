import { Message } from '@shendu/element-ui'
import { getStyle, getElementTop } from '@/common/js/dom'

// 存放已发起的 $message
let messageArray = []

// 包装参数，如果参数是字符串，则设置为 message
const formatOptions = opts => {
  if (typeof opts === 'string') {
    return { message: opts, type: 'info' }
  }
  return opts
}

// 新发起的消息会把已发起的内容相同的消息close，再发起新的消息，依赖于 element-ui/ElMessage
const noRepeatElMessage = function(opts) {
  // 转换参数
  opts = formatOptions(opts)

  // 发起消息前遍历之前发起的消息，如果有重复发起的，则把之前的 close
  messageArray.forEach((item, idx) => {
    if (item.message === opts.message && item.type === opts.type) {
      messageArray[idx].close() // 已发起的 close
    }
  })

  // 获取最高z-index 的弹窗的中心Y轴位置，如没有弹窗，则使用浏览器窗口中心位置
  opts.offset = getElDialogYCoord()

  const message = Message(opts) // 发起 $message 调用
  messageArray = messageArray.filter($m => $m.visible === true) // 每次调用完把已经消失的message实例清空，释放内存
  messageArray.push(message) // 把本次发起的message 推进 messageArray
}

// 获取最高z-index 的弹窗的中心Y轴位置，如没有弹窗，则使用浏览器窗口中心位置
const getElDialogYCoord = () => {
  const elDialogDom = document.querySelectorAll('.el-dialog__wrapper')

  let offset = 0 // 偏移量

  const arr = [...elDialogDom]
  if (arr.length) { // 遍历获取到zIndex最高的 el-dialog组件
    let indexMax = 0 // index最高的
    let zIndexMax = 0 // zIndex最大值
    let zIndex = 0
    arr.forEach((element, index) => {
      if (getStyle(element, 'display') !== 'none') {
        zIndex = getStyle(element, 'z-index')
        if (zIndex > zIndexMax) {
          zIndexMax = zIndex
          indexMax = index
        }
      }
    })
    if (zIndexMax) {
      // 获取到zIndex最高的 el-dialog组件
      const dom = arr[indexMax].querySelector('.el-dialog')
      // 获取元素的纵坐标位置 + 元素的高度 / 2
      offset = getElementTop(dom) + dom.clientHeight / 2
    } else {
      offset = window.innerHeight / 2
    }
  } else { // 没有弹窗，则使用浏览器窗口中心位置
    offset = window.innerHeight / 2
  }
  // -19 是message组件自己本身高度 38 / 2
  // eslint-disable-next-line no-magic-numbers
  return offset - 19
}

// 把 message 其余方法及属性放到 noRepeatElMessage 中
Object.keys(Message).forEach(key => {
  if (['success', 'warning', 'info', 'error'].includes(key)) { // 4 个 type 方法需要额外包装
    noRepeatElMessage[key] = function(opts) {
      // 转换参数
      opts = formatOptions(opts)
      const type = key
      noRepeatElMessage({ ...opts, type }) // 统一调用 noRepeatElMessage
    }
  } else {
    // close closeAll config
    noRepeatElMessage[key] = Message[key]
  }
})

const install = function(Vue) {
  Vue.prototype.$message = noRepeatElMessage
}

export default {
  install
}
