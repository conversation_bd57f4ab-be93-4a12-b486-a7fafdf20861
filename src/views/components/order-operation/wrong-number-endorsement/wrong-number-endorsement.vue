<!-- 申请/下载错号背书证明 -->
<template>
  <div class="wrong-endorsement line-block">
    <slot name="button">
      <el-button
        v-bind="$attrs"
        :type="$attrs.type || 'primary'"
        :width="$attrs.width || '142'"
        :height="$attrs.height || '40'"
        :disabled="order.wrongEndorse === 1 || order.wrongEndorse === 2"
        @click="onClick"
        v-on="$listeners"
      >
        <slot>{{ order.wrongEndorse === 3 ? '下载' : '申请' }}错号证明</slot>
      </el-button>
    </slot>
  </div>
</template>

<script>
import WrongNumberEndorsementDialog from './wrong-number-endorsement-dialog.vue'
import orderOperationMixin from '../order-operation-mixin'
import { download } from '@/common/js/util' // 下载文件
import orderApi from '@/apis/order'

export default {
  name: 'service-intervention',
  mixins: [orderOperationMixin(WrongNumberEndorsementDialog)],
  methods: {
    async onClick(event) {
      // wrongEndorse 0 可申请 1审核中(按钮置灰) 2审核完成(按钮置灰) 3签署完成（展示下载）
      const { wrongEndorse, orderNo } = this.order
      if (wrongEndorse === 3) {
        const { url } = await orderApi.downloadWrongNumber({ orderNo })
        if (this.$ipc) { // 客户端
          download(url)
          return
        }
        window.open(url, '_blank')
      } else if (!wrongEndorse) { // 申请错号背书
        this.init(event)
      }
    }
  }
}
</script>
