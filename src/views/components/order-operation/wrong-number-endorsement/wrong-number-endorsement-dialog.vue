<!-- 申请错号背书证明 -->
<style lang="scss" scoped>
.service-intervention {
  ::v-deep {
    .el-form-item {
      margin-bottom: 10px;
    }

    .el-form-item__content {
      line-height: 0;
    }

    .el-form-item__error {
      position: static;
    }
  }

  .main {
    padding: 16px;
    background: $color-FFFFFF;

    .item-lable {
      margin-bottom: 2px;
      height: 22px;
      font-size: 14px;
      color: $color-text-secondary;
      line-height: 22px;
    }
  }
}

.upload-item {
  width: 170px;
}

.uplode-voucher-box {
  display: flex;
  justify-content: space-between;
}

.star {
  &::before {
    position: relative;
    top: 3px;
    margin-right: 2px;
    font-size: 14px;
    color: $color-warning;
    line-height: 22px;
    content: "*";
  }
}

.warn-content {
  margin-bottom: 12px;

  // color: $color-warning;
}
</style>

<template>
  <el-dialog
    width="600px"
    :visible.sync="visible"
    title="申请错号证明"
    :before-close="handleClose"
    :close-on-click-modal="false"
    class="service-intervention order-operation-dialog"
  >
    <WarnContent class="warn-content">
      票面截图须包含实际背书票据的正面、背面，否则可能影响最终审核结果。
    </WarnContent>

    <el-form
      ref="ruleForm"
      :model="form"
      :rules="rules"
    >
      <div class="main">
        <div class="item-lable star">实际背书票据号</div>
        <el-form-item prop="draftNo">
          <el-input
            v-model="form.draftNo"
            type="number"
            maxlength="30"
            show-word-limit
            placeholder="请填写实际背书票据号"
          />
        </el-form-item>

        <!-- 上传凭证 -->
        <div class="item-lable star">实际背书票面截图（单个凭证大小不超过2M）</div>
        <el-form-item prop="frontImageUrl">
          <div class="uplode-voucher-box">
            <ImgUpload
              v-for="(item, index) in imgUrl"
              :key="item || index"
              :value="item"
              :height="75"
              class="upload-item"
              :size-limit="2"
              :dir="OSS_DIR.INTERVENTION_VOUCHER"
              @input="(src) => handleUploadBackImg(src, index)"
            >
              <div slot="empty">
                <p>点击或拖拽图片至此</p>
                <p>上传凭证</p>
              </div>
            </ImgUpload>
          </div>
        </el-form-item>
      </div>
    </el-form>
    <span slot="footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button v-waiting="'get::loading::/order/wrongNumberApply'" type="primary" @click="confirm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { OSS_DIR } from '@/constant' // 上传文件夹
import ImgUpload from '@/views/components/common/img-upload/img-upload.vue' // 图片上传组件
import orderApi from '@/apis/order'
import WarnContent from '../../common/warn-content.vue'
export default {
  name: 'wrong-number-dialog',
  components: {
    ImgUpload,
    WarnContent
  },
  data() {
    // 图片校验
    const validateFrontImageUrl = (rule, value, callback) => {
      const isFile = this.imgUrl.some(v => !!v)
      if (!isFile) {
        callback(new Error('请上传实际背书票面截图'))
      } else {
        callback()
      }
    }
    return {
      order: null, // 当前操作的订单
      OSS_DIR, // 上传文件夹
      visible: false, // 弹窗是否打开
      imgUrl: [null, null, null], // 凭证图片url集合
      form: {
        draftNo: '', // 实际背书票据号
      },
      // 表单校验规则
      rules: {
        draftNo: [
          { required: true, message: '请输入实际背书票据号', trigger: 'blur' },
          { min: 30, max: 30, message: '实际背书票据号长度为30位', trigger: 'blur' }
        ],
        frontImageUrl: [{ required: true, validator: validateFrontImageUrl, trigger: ['change', 'blur'] }], // 使用'none',是的不默认触发，只能通过方法触发
      },
    }
  },

  methods: {
    init() {
      this.visible = true
    },

    // 实际背书票面截图凭证上传
    handleUploadBackImg(src, index) {
      this.$set(this.imgUrl, [index], src || null)
    },

    // 确定提交
    async confirm() {
      try {
        await this.$refs.ruleForm.validate()
        let params = {
          draftNo: this.form.draftNo, // 实际背书票据号
          orderNo: this.order.orderNo, // 订单号，非空字段
          voucherUrls: this.imgUrl.filter(item => item).toString(), // 凭证图片url，多个以，隔开，非空字段
        }
        await orderApi.wrongNumberApply(params)
        this.$message({
          message: '申请成功，请等待客服处理',
          type: 'success'
        })

        this.$emit('success')
        this.handleClose()
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log(error)
      }
    },

    // 关闭
    handleClose() {
      // 清空
      this.form = {
        draftNo: '', // 实际背书票据号
      }
      this.imgUrl = [null, null, null]
      this.$nextTick().then(() => {
        this.$refs.ruleForm && this.$refs.ruleForm.clearValidate()
      })
      this.visible = false
      this.$emit('destroy')
    }
  }

}
</script>
