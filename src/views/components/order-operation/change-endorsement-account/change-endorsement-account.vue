<!-- 更改签收账户 -->
<style lang="scss" scoped>
.change-endorsement-account {
  display: inline-block;
}
</style>

<template>
  <div class="change-endorsement-account order-operation">
    <slot name="button">
      <el-button
        v-bind="$attrs"
        :type="$attrs.type || 'primary'"
        @click="init"
        v-on="$listeners"
      >
        <slot>变更签收账户</slot>
      </el-button>
    </slot>
  </div>
</template>

<script>
import ChangeEndorsementAccountDialog from './change-endorsement-account-dialog.vue'
import orderOperationMixin from '../order-operation-mixin'

export default {
  name: 'change-endorsement-account',
  mixins: [orderOperationMixin(ChangeEndorsementAccountDialog)],
}
</script>
