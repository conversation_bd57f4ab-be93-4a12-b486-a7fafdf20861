<!-- 变更签收账户 -->
<style lang="scss" scoped>
.change-endorsement-account {
  .main {
    padding: 16px;
    background: $color-FFFFFF;

    .item-lable {
      margin-bottom: 2px;
      height: 22px;
      font-size: 14px;
      color: $color-text-secondary;
      line-height: 22px;
    }

    .account-select {
      width: 100%;
    }
  }
}

.red-high-light {
  font-weight: 600;
  color: $--color-font-main;
}
</style>

<template>
  <el-dialog
    width="600px"
    :visible.sync="visible"
    :close-on-click-modal="false"
    title="变更签收账户"
    class="change-endorsement-account order-operation-dialog"
    :before-close="handleClose"
  >
    <div class="main">
      <div class="item-lable">签收账户</div>
      <el-select
        v-model="accountSelect"
        class="account-select order-operation-dialog"
        placeholder="请选择"
      >
        <el-option
          v-for="item in accountList"
          :key="item.id"
          :label="`${item.bankName}(${item.bankNo})`"
          :disabled="isAccountDisabled(item)"
          :value="item.id"
        >
          {{ `${item.bankName}(${item.bankNo})` }}
        </el-option>
      </el-select>
    </div>
    <span slot="footer">
      <el-button :disabled="loading" @click="handleClose">取消</el-button>
      <el-button
        v-waiting="orderApi.putUpdateEndorseBankCard"
        type="primary"
        :disabled="!accountList.length"
        @click="confirm"
      >确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
/* eslint-disable no-magic-numbers */
import { mapGetters, mapActions } from 'vuex'
import marketApi from '@/apis/market'
import orderApi from '@/apis/order'
import { PAYMENT_CHANNEL } from '@/constant'
export default {
  name: 'change-endorsement-account-dialog',

  data() {
    return {
      PAYMENT_CHANNEL,
      orderApi,
      order: null, // 当前操作的订单
      visible: false, // 弹窗是否打开
      accountList: [], // 签收账户列表
      accountSelect: null, // 选择的签收账户id
      loading: false,
    }
  },
  computed: {
    ...mapGetters('user', {
      signBankAccountList: 'signBankAccountList', // 已通过的签收账户列表
    }),
  },

  methods: {
    ...mapActions('user', {
      getPassedBankCardList: 'getPassedBankCardList', // 查询已通过的签收账户列表
    }),

    async init() {
      this.visible = true
      let data
      try {
        data = await marketApi.getDraftOrderInfo(this.order.orderNo)
      } catch (err) {
        this.visible = false
      }
      data && this.setAccountList()
    },

    // 设置签收账户列表
    async setAccountList() {
      await this.getPassedBankCardList()
      this.accountList = this.signBankAccountList.map(item => ({
        ...item,
        bankName: item.bankBranchName,
        bankNo: `**${item.bankAccount.substring(item.bankAccount.length - 4)}`,
        id: item.id
      }))
      if (this.order.draftType) {
        this.accountList = this.accountList.filter(item => item.newDraftFlag === 1)
      }

      // 回显值设置
      this.accountSelect = this.accountList.find(value => value.bankName === this.order.buyCorpBankName)?.id || null
    },

    // 确认
    confirm() {
      const BankCard = this.accountList.find(value => value.id === this.accountSelect)
      const h = this.$createElement
      this.$msgbox({
        title: '提示',
        dangerouslyUseHTMLString: true,
        message: h('p', null, [
          h('p', null, '您选择的签收账户为 '),
          h('p', { class: 'red-high-light' }, `${BankCard.bankName}(${BankCard.bankNo})`),
          h('p', null, '是否确认？')
        ]),
        confirmButtonText: '确定',
        showCancelButton: true,
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          const param = {
            traderCorpBankCardId: this.accountSelect, // 银行卡id(Integer)
            orderNo: [this.order.orderNo]// 订单编号id(Long)
          }
          await orderApi.putUpdateEndorseBankCard(param)
          this.$emit('success')
          this.$message({
            type: 'success',
            message: '修改成功!'
          })
          this.handleClose()
        } catch (error) {
          // eslint-disable-next-line no-console
          console.log('error', error)
        }
      })
    },

    handleClose() {
      this.visible = false
      this.$emit('destroy')
    },
    // 签收账户银行是否禁用
    isAccountDisabled(item) {
      // 亿联渠道
      // feat:E+渠道签收账户支持选择任意已绑定银行账户
      const yiLianPayChannel = this.order.paymentChannel === PAYMENT_CHANNEL.YI_LIAN_BANK.id && item.yilianGeneralFlag !== 1
      // 众邦渠道
      // const zhongBangPayChannel = this.order.paymentChannel === PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id && item.zbankPlusGeneralFlag !== 1
      return yiLianPayChannel
    },
  }

}
</script>
