<style lang="scss" scoped>
  .share {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 8px;
  }
  </style>

<template>
  <div class="share">
    <el-button
      v-bind="$attrs"
      :type="$attrs.type || 'primary'"
      :border="!$attrs.type"
      :height="$attrs.height || '40'"
      :width="$attrs.width || '68'"
      @click="handleShare"
    >
      <slot>分享</slot>
    </el-button>
    <ShareDialog v-model="showDialog" :list="list" />
  </div>
</template>

<script>
import { yuan2wan } from '@/common/js/number'
import orderApi from '@/apis/order'
import ShareDialog from './share-dialog.vue'
import {
  DRAFT_TYPE_VALUE_MAP, // 承兑人类型
  BACK_DEFECT_TYPE_NAME_MAP,
  BACK_DEFECT_TYPE_KEY_MAP,
} from '@/constant'
const ORIGIN = window.location.origin

export default {
  name: 'draft-sale-export',

  components: {
    ShareDialog,
  },

  props: {
    list: {
      type: [Object, Array]
    },
  },

  data() {
    return {
      showDialog: false,
      shareMsg: '', // 存放后端返回字段
      selectType: 'h5', // 选中的分享类型
    }
  },
  computed: {
    // 企业信息
    corpInfo() {
      return this.$store.state?.user?.corpInfo
    },
    // 用于提供 watch 是否重新获取分享key
    queryForGetKey() {
      const { selectType, phone, visible } = this
      return {
        visible,
        selectType,
        phone,
      }
    },

    // 选中的 row id 合集
    ids() {
      return Array.isArray(this.list) ? this.list.map(row => row.id) : [this.list.id ? this.list.id : this.list.orderNo]
    },

    // 二维码信息
    qrcodeMsg() {
      if (!this.shareKey) {
        return ''
      }
      return `${ORIGIN}/mobile.html/stock?type=${this.selectType}&shareKey=${this.shareKey}&shareForm=order&orderNo=${this.ids.length > 1 ? '' : this.list.orderNo}`
    },
  },

  methods: {
    yuan2wan, // 元转万
    async  handleShare() {
      await this.getMobile()
      await this.getShareCode()
      await this.getCopyText()
    },
    // 处理瑕疵
    setDefect(defects) {
      let showDefects = ''
      for (let i in defects) {
        if (defects[i] && BACK_DEFECT_TYPE_KEY_MAP[i]) {
          // 类型为 回出票人abca，回收款人，背书回头，背书重复，质押 时显示次数
          if (i === BACK_DEFECT_TYPE_NAME_MAP.ABCA.key
          || i === BACK_DEFECT_TYPE_NAME_MAP.HUI_SHOU_KUAN_REN.key
          || i === BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_HUI_TOU.key
          || i === BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_CHONG_FU.key
          || i === BACK_DEFECT_TYPE_NAME_MAP.ZHI_YA.key
          ) {
            showDefects = `${showDefects ? `${showDefects}，` : ''}${BACK_DEFECT_TYPE_KEY_MAP[i]}[${defects[i]}]`
            // 类型为其它时 显示其它瑕疵票描述
          } else if (i === BACK_DEFECT_TYPE_NAME_MAP.OTHER.key && defects.defectsOtherDesc) {
            // 只有在tooltips上才显示其他【】具体内容
            showDefects = `${showDefects ? `${showDefects}，` : ''}${BACK_DEFECT_TYPE_KEY_MAP[i]}[${defects.defectsOtherDesc}]`
          } else {
            showDefects = `${showDefects ? `${showDefects}，` : ''}${BACK_DEFECT_TYPE_KEY_MAP[i]}`
          }
        }
      }
      return showDefects
    },
    // 新的逻辑
    // 复制文本组装
    getCopyText() {
      let copyText = ''
      if (this.ids.length === 1) {
        let defects = this.setDefect(this.list.defects || {})
        // let copyMobile = this.list.mobile.length >= 1 ? this.list.mobileList.map(item => item.mobile).join(',') : ''
        // `订单号：${this.list.orderNo}\n`
        // + `票号（后六位）：${this.list.lastSixDraftNo}\n`
        // +
        copyText
        = `订单号：${this.list.orderNo}\n`
        + `票号（后六位）：${this.list.lastSixDraftNo}\n`
        + `承兑人：${this.list.acceptorName} 【${DRAFT_TYPE_VALUE_MAP[this.list.acceptorType]}】\n`
        + `出票日：${this.list.issueDate}\n`
        + `到期日：${this.list.maturityDate}（剩${this.list.interestDays || '--'}天）\n`
        + `票面金额：${this.yuan2wan(this.list.draftAmount) || '--'}万\n`
        + `价格： 每十万扣款${this.list.lakhFee || 0}元 /年化 ${this.list.annualInterest || 0 || 0}% \n`
        // + `瑕疵：${this.list.ticketFlawStr ? this.list.ticketFlawStr : '无瑕疵'}\n`
        + `瑕疵：${defects || '无瑕疵'}\n`
        + '----------------------------------------\n'
        + '【订单链接】\n'
        + `${this.qrcodeMsg}\n`
        + '请以详情页信息为准。\n'
        + '----------------------------------------\n'
        + `${this.corpInfo && this.corpInfo.corpInviteCode ? `票方定向码：${this.corpInfo.corpInviteCode}` : ''}\n`
        // + `${this.list.mobile ? `票方联系方式：${this.list.mobile}` : ''}`
      } else {
        copyText = `好友分享${this.ids.length}张票据信息，快去查看吧！\n`
        + '【订单链接】\n'
        + `${this.qrcodeMsg}\n`
        + '请以详情页信息为准。\n'
        + '----------------------------------------\n'
      }

      let $input = document.createElement('textarea')
      $input.style.opacity = '0'
      $input.value = copyText
      document.body.appendChild($input)
      $input.select()
      const isSuccess = document.execCommand('copy')
      isSuccess ? this.onSuccess() : this.onError()
      document.body.removeChild($input)
      $input = null
    },
    async getMobile() {
      const { mobile } = await this.$store.dispatch('user/getUserInfo')
      this.phone = mobile
    },

    async getShareCode() {
      const res = await orderApi.getShareNo({
        orderNo: this.ids,
        mobile: this.phone,
        shareType: 1,
      })
      this.shareKey = res
    },
    // 复制成功
    onSuccess() {
      this.$message.success('复制成功')
    },
    // 复制失败
    onError() {
      this.$message.error('复制失败')
    },
  }
}
</script>
