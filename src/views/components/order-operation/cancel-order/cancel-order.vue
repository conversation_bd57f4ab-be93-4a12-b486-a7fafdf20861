<!-- 取消订单 -->
<style lang="scss" scoped>
.change-endorsement-account {
  display: inline-block;

  .el-button {
    position: relative;

    &.has-tag::after {
      content: "";
      position: absolute;
      top: -1px;
      left: -1px;
      border-top: 27px solid $color-assist3;
      border-right: 28px solid transparent;
      width: 0;
      height: 0;
    }

    .rice-tag {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      font-size: 12px;
      font-weight: 600;
      line-height: 20px;
      transform: scale(.8);
      transform-origin: top;
      color: $color-FFFFFF;
    }
  }
}
</style>

<template>
  <div class="change-endorsement-account order-operation">
    <slot name="button">
      <el-button
        v-bind="$attrs"
        :type="$attrs.type || 'primary'"
        :width="$attrs.width || '104'"
        :class="hasTag && 'has-tag'"
        :height="$attrs.height || '40'"
        :border="!$attrs.type"
        @click="init"
        v-on="$listeners"
      >
        <span v-if="hasTag" class="rice-tag">{{ sdmUnit }}</span>
        <slot>取消订单</slot>
      </el-button>
    </slot>
  </div>
</template>

<script>
import CancelOrderDialog from './cancel-order-dialog.vue'
import orderOperationMixin from '../order-operation-mixin'

export default {
  name: 'change-endorsement-account',
  mixins: [orderOperationMixin(CancelOrderDialog)],
  props: {
    hasTag: [String, Number, Boolean]
  },
}
</script>
