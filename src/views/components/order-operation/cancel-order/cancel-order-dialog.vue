<!-- eslint-disable max-lines -->
<!-- 取消订单 -->
<style lang="scss" scoped>
.cancel-order {
  ::v-deep {
    .el-form-item {
      margin-bottom: 0;
    }

    .el-form-item__error {
      position: static;
    }

    .el-button--danger.is-border {
      font-size: 16px;
    }

    .el-textarea .el-input__count {
      bottom: -5px;
      background-color: rgb(0 0 0 / 0%);
    }
  }

  .main {
    padding: 16px;
    background: $color-FFFFFF;
  }
}

// 标题
.title-left-border {
  position: relative;
  margin-bottom: 10px;
  padding-left: 12px;
  height: 22px;
  font-size: 16px;
  font-weight: 600;
  line-height: 22px;

  &::before {
    position: absolute;
    top: 2px;
    left: 0;
    width: 4px;
    height: 16px;
    background: $--color-primary;
    content: "";
  }
}

// 单选框
.radio-box {
  display: flex;
  justify-content: space-between;
  width: 100%;

  ::v-deep {
    // 单选按钮样式 start
    .el-radio {
      margin: 0 8px 0 0;
      border: 1px solid $color-D9D9D9;
      border-radius: 2px;
      width: 170px;
      height: 40px;
      text-align: center;
      color: $color-text-primary;
      line-height: 40px;
    }

    .el-radio__input {
      display: block;
    }

    .el-radio__label {
      padding-left: 0;
      font-size: 16px;
      line-height: 24px;
    }

    .el-radio.is-bordered {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 8px 0 0;
      padding: 0 10px;

      &:last-child {
        margin-right: 0;
      }
    }

    .is-checked {
      border-color: $--color-primary;
      background-color: $--color-primary-hover;
    }

    // 单选按钮样式 end
  }
}

.second-radio-box {
  display: flex;
  flex-wrap: nowrap;
  justify-content: flex-start;

  // 单选按钮样式 start
  .el-radio {
    margin: 0 8px 0 0;
    border: 1px solid $color-D9D9D9;
    border-radius: 2px;
    height: 40px;
    text-align: center;
    color: $color-text-primary;
    line-height: 40px;
  }

  .el-radio.is-bordered {
    display: flex;
    align-items: center;
    margin: 8px 5px 0 0;
    padding: 0 7px;

    ::v-deep {
      .el-radio__input {
        display: block;
      }

      .el-radio__label {
        padding-left: 0;
        font-size: 16px;
        line-height: 24px;
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .is-checked {
    border-color: $--color-primary;
    background-color: $--color-primary-hover;
  }

  // 单选按钮样式 end
}

.margin-top-10 {
  margin-top: 10px;
}

.margin-left-8 {
  margin-left: 8px;
}

.warn-content {
  margin-bottom: 12px;
  font-size: 16px;
}

// 绿色高亮
.green-light-high {
  margin: 0 0 2px;
  font-weight: 600;
  color: $font-color;
}

// 红色高亮
.red-light-high {
  margin: 0 0 2px;
  font-weight: 600;
  color: $color-warning;
}

// 验证码接收行
.get-code-box {
  ::v-deep .el-form-item__content {
    &::after {
      display: none;
    }

    &::before {
      display: none;
    }
  }

  .get-code-btn {
    float: right;
  }
}

// 灰色标题
.gray-title {
  margin: 10px 0 2px;
  color: $color-text-secondary;
  line-height: 22px;
}

.gray-no-top {
  margin-bottom: 2px;
  color: $color-text-secondary;
}

.gray {
  margin: 10px 0 2px;
  color: $color-text-secondary;
}

// 左右布局
.second-main {
  display: flex;
  justify-content: space-between;

  .main-item {
    padding: 16px;
    width: 456px;
    background: $color-FFFFFF;
  }

  .radio-group {
    display: flex;
    width: 100%;
  }

  .tips {
    margin-bottom: 8px;
    font-size: 16px;
    line-height: 24px;
  }
}

// 合同按钮
.contract-btn {
  float: left;
  height: 42px;
  line-height: 38px;

  ::v-deep {
    .el-link--inner {
      height: 22px;
      font-size: 14px;
      line-height: 22px;
    }

    .el-link.el-link--primary {
      color: $font-color;

      @include example-underline;

      &::after {
        border: 0;
        color: $font-color-hover;
      }
    }
  }
}

.buy-waiting-sign-cancel {
  .second-radio-box .el-radio.is-bordered {
    margin-top: 0;
    margin-right: 8px;
    padding: 0 13px;
  }
}

.el-form {
  >.gray-title {
    &:first-child {
      margin-top: 0;
    }
  }
}

.get-voice-code {
  @include example-underline;
}

.confirm-dialog-body {
  padding: 16px;
  font-size: 14px;
  text-align: center;
  color: $color-text-primary;
  background: $color-FFFFFF;

  .title {
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: 600;
  }

  .keywords {
    font-weight: 600;
    color: $color-warning;
  }
}

.tips-p {
  margin-top: 8px;
  font-size: 14px;
  color: $color-warning;
}

.activity-box {
  padding-bottom: 10px;

  .red-font {
    cursor: pointer;
    border-bottom: 1px solid #EC3535;
    font-size: 16px;
    font-weight: 500;
    color: #EC3535;
  }
}

.theme-font {
  cursor: pointer;
  border-bottom: 1px solid $--color-primary;
  color: $--color-primary;
}

.red-text {
  font-weight: 500;
  color: $color-warning;
}
</style>

<style lang="scss">
.message-box-red {
  .el-icon-warning {
    color: $--color-primary !important;
  }
}
</style>

<template>
  <div>
    <el-dialog
      :width="dialogWidth"
      :visible.sync="visible"
      :title="order && titleDialog"
      :close-on-click-modal="false"
      class="cancel-order order-operation-dialog"
      :before-close="handleClose"
    >
      <!-- 文字提示 -->
      <template v-if="hasContent">
        <WarnContent class="warn-content">
          <!-- E++ 渠道资方同意取消 资方发起取消公共文案部分 -->
          <template v-if="isEPlusChannel && isBuy">
            <div>
              请先在企业网银内完成订单<span class="red-text">延时转账撤销为“成功”状态</span>，随后点击下方
              【确定取消】按钮完成平台订单操作。
            </div>
          </template>

          <!-- 待支付环节取消 -->
          <template v-if="(isSaleResponsible || isBuy) && waitingPay">
            取消订单后，将记录您中度违约，您可在
            <span class="red-light-high">24小时内</span>
            申诉。过期不申诉或终审判定违约的，将
            <!--
              记录扣
              <span class="red-light-high">2</span>
              分信用分,并
            -->
            <template v-if="!isRongyiOrder">
              扣除
              <span class="red-light-high">{{ deductSdm }}</span>
              {{ sdmName }}。
            </template>
          </template>
          <template v-if="isBuyResponsible && waitingPay">
            资方有效支付时间<span class="green-light-high">25</span>分钟，当前支付已超时，您可以取消。
          </template>

          <!-- 待背书环节取消 -->
          <template v-if="waitingEndorse">
            <template v-if="isBuy">
              票方有效背书时间<span class="green-light-high">{{ order.fastTrade || order.radarType ? "10" : "25" }}</span>分钟，当前背书已超时，您可以取消。
            </template>
            <!--
              <template v-else-if="isSale && isYlBankPaymentChannel">
              确认取消后，将由 <span class="red-light-high">客服人工介入</span>，请再次确认票在网银未背书或已撤回<br>
              客服会按实际情况处理取消订单，若记录您违约，您可在 <span class="red-light-high">24小时内</span>申诉。过期不申诉或终审判定违约的，将
              <template v-if="!isRongyiOrder">
              扣除
              <span class="red-light-high">{{ isSale ? order.sellerMarginAmount : order.marginAmount }}</span>
              {{ sdmName }}。
              </template>。
              </template>
            -->
            <template v-else>
              确认取消后，系统将 <span class="red-light-high">解冻资金</span>，请再次确认票在网银未背书或已撤回
              取消订单后，将记录您重度违约，您可在
              <span class="red-light-high">24小时内</span>
              申诉。过期不申诉或终审判定违约的，将
              <!--
                记录扣
                <span class="red-light-high">3</span>
                分信用分,并
              -->
              <template v-if="!isRongyiOrder">
                扣除
                <span class="red-light-high">{{ isSale ? order.sellerMarginAmount : order.marginAmount }}</span>
                {{ sdmName }}。
              </template>
            </template>
          </template>

          <!-- 待签收环节取消 -->
          <template v-if="waitingSubmission">
            <template v-if="isBuy">
              订单取消后，将根据取消原因，判定违约方重度违约。 <br> 如有异议可在 <span class="red-light-high">24小时内</span> 申诉，过期不申诉或终审判定违约，将扣除
              <!--
                <span class="red-light-high">3</span>
                分信用分 和
              -->
              <template v-if="!isRongyiOrder">
                <span class="red-light-high">100%</span> {{ sdmName }}。
              </template>
            </template>
            <!--
              <template v-else-if="isSale && isYlBankPaymentChannel">
              确认取消后，将由 <span class="red-light-high">客服人工介入</span>，请再次确认票在网银未背书或已撤回<br>
              客服会按实际情况处理取消订单，记录接单方重度违约，扣除接单方
              <template v-if="!isRongyiOrder">
              <span class="red-light-high">100%</span> {{ sdmName }}。
              </template>
              </template>
            -->
            <template v-else>
              确认取消后，系统将 <span :class="isGreenWarnContent ? 'green-light-high' : 'red-light-high'">解冻资金</span>，请再次确认票在网银未背书或已撤回。
            </template>
          </template>

          <!-- 取消中-待签收  -->
          <template v-if="cancelingSubmission">
            <!--
              <template v-if="isSaleResponsible && isYlBankPaymentChannel">
              确认取消后，将由 <span class="red-light-high">客服人工介入</span>，请再次确认票在网银未背书或已撤回<br>
              客服会按实际情况处理取消订单，记录违约方重度违约，如您对违约有异议可在 <span class="red-light-high">24小时内</span> 申诉。过期不申诉或终审判定违约的，将扣除
              <template v-if="!isRongyiOrder">
              <span class="red-light-high">100%</span> {{ sdmName }}。
              </template>
              </template>
            -->
            <template v-if="isSaleResponsible">
              确认取消后，系统将 <span class="red-light-high">解冻资金</span>，请再次确认票在网银未背书或已撤回
              取消订单后，将记录您重度违约，您可在
              <span class="red-light-high">24小时内</span>
              申诉。过期不申诉或终审判定违约的，将
              <!--
                记录扣
                <span class="red-light-high">3</span>
                分信用分,并
              -->
              <template v-if="!isRongyiOrder">
                扣除
                <span class="red-light-high">{{ isSale ? order.sellerMarginAmount : order.marginAmount }}</span>
                {{ sdmName }}。
              </template>
            </template>
            <template v-else>
              确认取消后，系统将 <span :class="isGreenWarnContent ? 'green-light-high' : 'red-light-high'">解冻资金</span>，请再次确认票在网银未背书或已撤回。
            </template>
          </template>

          <!-- 取消中-待背书  -->
          <template v-if="cancelingEndorse">
            <!--
              <template v-if="isYlBankPaymentChannel">
              确认取消后，将由 <span class="red-light-high">客服人工介入</span>，请再次确认票在网银未背书或已撤回<br>
              客服会按实际情况处理取消订单，若记录您违约，您可在 <span class="red-light-high">24小时内</span>申诉。过期不申诉或终审判定违约的，将
              <template v-if="!isRongyiOrder">
              扣除
              <span class="red-light-high">{{ isSale ? order.sellerMarginAmount : order.marginAmount }}</span>
              {{ sdmName }}。
              </template>。
              </template>
            -->
            确认取消后，系统将 <span class="red-light-high">解冻资金</span>，请再次确认票在网银未背书或已撤回
            取消订单后，将记录您重度违约，您可在
            <span class="red-light-high">24小时内</span>
            申诉。过期不申诉或终审判定违约的，将记
            <!--
              录扣
              <span class="red-light-high">3</span>
              分信用分,并
            -->
            <template v-if="!isRongyiOrder">
              扣除
              <span class="red-light-high">{{ isSale ? order.sellerMarginAmount : order.marginAmount }}</span>
              {{ sdmName }}。
            </template>
          </template>
          <!-- 取消中-待支付 E++渠道 -->
          <template v-if="cancelingPay && isEPlusChannel">
            确认取消后，系统将 <span class="red-light-high">解冻资金</span>，请再次确认票在网银未背书或已撤回。
          </template>
          <!-- 资方待确认状态3分钟~8分钟之间取消订单 -->
          <template v-if="isBuyWaitingConfirmCancel">
            票方超过<span class="red-light-high">3</span>分钟未确认订单，您可以取消。
          </template>
          <div style="margin-top: 10px;">
            <a
              class="text-link"
              target="_blank"
              :href="order.draftType ? PLATFORM_DEFAULT_RULESNEW_URL_NEW_DRAFT : PLATFORM_DEFAULT_RULESNEW_URL"
              rel="noopener noreferrer"
            >《平台订单违约规则》</a>
          </div>
        </WarnContent>
      </template>

      <!-- 资方待签收取消订单 -->
      <div v-if="isBuyWaitingSignCancel" class="main buy-waiting-sign-cancel">
        <el-form ref="form" :model="form" :rules="rules">
          <!-- 取消类型 -->
          <div class="title-left-border">请选择取消原因</div>
          <el-form-item prop="cancelType">
            <div v-for="item in buyWaitingSignCancelOption" :key="item.brokeStatus">
              <div class="gray-title">{{ item.brokeLabel }}</div>
              <el-radio-group v-model="form.cancelType" class="second-radio-box">
                <el-radio
                  v-for="v in item.list"
                  :key="v.value"
                  :label="v.value"
                  type="button"
                >
                  {{ v.label }}
                </el-radio>
              </el-radio-group>
            </div>
          </el-form-item>
          <!-- 选中背书自有户显示该提示 -->
          <!-- <p v-show="form.cancelType === 12" class="tips-p">若确认为背书自有户，订单取消后请联系您的客户经理申诉处理</p> -->
          <div class="gray">补充说明</div>
          <el-form-item prop="cancelMsg">
            <el-input
              v-model="form.cancelMsg"
              type="textarea"
              maxlength="50"
              :autosize="{ minRows: 3}"
              class="remark-textarea"
              show-word-limit
              placeholder="补充说明"
            />
          </el-form-item>
          <!--
            <div class="gray-title">支付密码</div>
            <el-form-item prop="payPassword">
            <el-input v-model="form.payPassword" type="password" :height="40" />
            </el-form-item>
          -->
        </el-form>
      </div>
      <!-- 资方待确认状态3分钟~8分钟之间取消订单 -->
      <div v-else-if="isBuyWaitingConfirmCancel" class="main buy-waiting-sign-cancel">
        <el-form ref="form" :model="form">
          <div class="gray">补充说明</div>
          <el-form-item prop="cancelMsg">
            <el-input
              v-model="form.cancelMsg"
              type="textarea"
              maxlength="50"
              :autosize="{ minRows: 3}"
              class="remark-textarea"
              show-word-limit
              placeholder="请填写补充说明"
            />
          </el-form-item>
        </el-form>
      </div>
      <!-- E++ && 取消中 && 资方同意取消 隐藏main class属性 -->
      <div v-else :class="isEPlusChannel && isBuy && canceling ? '' : 'main'">
        <el-form ref="form" :model="form" :rules="rules">
          <!-- 取消类型 -->
          <template v-if="hasCancelType">
            <div class="title-left-border">取消原因</div>
            <el-form-item prop="cancelType">
              <el-radio-group v-model="form.cancelType" class="radio-box">
                <el-radio
                  v-for="item in option"
                  :key="item.value"
                  :label="item.value"
                  type="button"
                >
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </template>
          <template v-if="hasUploadEvidence">
            <div class="gray-title">上传凭证（单个凭证大小不超过2M）</div>
            <el-form-item prop="evidence" class="margin-top-10" :rules="{type: 'array', required: true, message: '请上传凭证' }">
              <el-row type="flex" :gutter="8">
                <el-col v-for="(item, index) in 3" :key="item" :span="8">
                  <ImgUpload
                    v-model="form.evidence[index]"
                    :size-limit="2"
                    :dir="OSS_DIR.DRAFT"
                    height="85"
                  >
                    <div slot="empty">
                      <p>点击或拖拽图片至此</p>
                      <p>上传凭证</p>
                    </div>
                  </ImgUpload>
                </el-col>
              </el-row>
            </el-form-item>
          </template>
          <!-- 取消补充 -->
          <template v-if="hasCancelMsg">
            <!-- <div :class="[hasCancelMsgMarginTop ? 'gray' : 'gray-no-top']">补充说明</div> -->
            <el-form-item prop="cancelMsg" class="margin-top-10">
              <el-input
                v-model="form.cancelMsg"
                type="textarea"
                maxlength="50"
                :autosize="{ minRows: 4}"
                class="remark-textarea"
                show-word-limit
                placeholder="补充说明"
              />
            </el-form-item>
          </template>
          <!-- 手机验证码 -->
          <template v-if="hasGetCode">
            <div class="gray-title">接码手机号</div>
            <el-form-item prop="phone">
              <div class="get-code-box">
                <el-input
                  v-model="zfTradeMobile"
                  :width="408"
                  :height="40"
                  type="number"
                  :disabled="true"
                />
                <el-button
                  v-waiting="'post::loading::/user/updateCorpMemberMobile/sendPhoneVerifyCode'"
                  class="get-code-btn"
                  type="primary"
                  border
                  width="112"
                  height="40"
                  :disabled="!canGetCode"
                  @click="getCode(false)"
                >
                  {{ canGetCode ? "获取验证码" : `还剩 ${leftSeconds}s` }}
                </el-button>
              </div>
              <template v-if="getVoiceCodeVisible">
                <span>收不到短信验证码？点击</span>
                <span class="get-voice-code" @click="getCode(true)">获取语音验证码</span>
              </template>
            </el-form-item>
            <div class="gray-title">填写验证码</div>
            <el-form-item prop="validateCode">
              <el-input
                v-model="form.validateCode"
                :height="40"
                type="number"
                :number-format="{ maxLength: 6, decimal: false, negative: false }"
              />
            </el-form-item>
          </template>
          <!-- E++ 票方 待背书 待签收 取消中-待背书 取消中-待签收 显示支付密码 -->
          <template v-if="isEPlusChannel && isSale && (waitingEndorse || waitingSubmission || cancelingEndorse || cancelingSubmission)">
            <div class="gray-title">
              <span>支付密码</span>
              <el-tooltip effect="dark" placement="top">
                <div slot="content">
                  请前往<span class="theme-font" @click="linkToAccountCenter">账户中心-账户安全</span>页面设置/修改账户支付密码。
                </div>
                <icon class="icon icon-question" type="chengjie-wenti" />
              </el-tooltip>
            </div>
            <el-form-item prop="payPassword">
              <el-input
                v-model="form.payPassword"
                show-password
                placeholder="请输入支付密码"
                type="password"
                :height="40"
              />
            </el-form-item>
          </template>
        </el-form>
      </div>

      <span slot="footer">
        <span class="contract-btn">
          <!--
            <el-link
            type="primary"
            target="_blank"
            underline="always"
            :href="PLATFORM_DEFAULT_RULESNEW_URL"
            rel="noopener noreferrer"
            >
            《平台订单违约规则》
            </el-link>
          -->
          <!--
            <a
            class="text-link"
            target="_blank"
            :href="order.draftType ? PLATFORM_DEFAULT_RULESNEW_URL_NEW_DRAFT : PLATFORM_DEFAULT_RULESNEW_URL"
            rel="noopener noreferrer"
            >《平台订单违约规则》</a>
          -->
          <!-- E++ 爬虫方案关闭 资方 开启识单助手 -->
          <IdentificationAssistant
            v-if="isEPlusChannel && !startCrawlerScheme && isBuy"
            style="display: inline-block;margin-right: 10px;"
            :order="order"
            :type="E_PLUS_RECOGNITION_SCENE.ORDER_CANCEL.id"
            height="40"
            :check-data="checkData"
            @success="postCancelTrade"
          />

        </span>

        <el-tooltip
          placement="top-start"
          :disabled="!!form.cancelType || !isBuyWaitingSignCancel"
        >
          <div slot="content">请选择取消原因。订单取消后，<br>将根据取消原因判定违约方。</div>
          <span>
            <el-button
              v-waiting="[orderApi.postSingleExecuteOrderTradeStep, orderApi.postEvidence]"
              :disabled="hasCancelType && !form.cancelType"
              @click="onConfirmCancel"
            >
              确定取消
            </el-button>
          </span>
        </el-tooltip>
        <el-button
          class="margin-left-8"
          type="primary"
          :disabled="loading"
          @click="handleClose"
        >暂不取消</el-button>

      </span>
    </el-dialog>
    <el-dialog
      :visible.sync="confirmVisible"
      append-to-body
      width="688px"
      title="取消订单"
    >
      <div class="confirm-dialog-body">
        <img src="https://oss.chengjie.red/web/imgs/public/cancel-order-confirm.png" alt="">
        <div v-if="isSale && false" class="activity-box">
          <span class="red-font" @click="goCerter">卖方交易笔笔有现金奖励！</span>
        </div>
        <!-- E++渠道 && 资方 && （待支付 || 待支付-取消中）环节取消文案 -->
        <p v-if="isEPlusChannel && (waitingPay || cancelingPay) && isBuy">
          <span class="red-text">请确认未在企业网银内完成该笔订单的延时转账</span>，确定取消后平台订单将变为“已失败”状态。
        </p>
        <p v-else>您确定要取消订单吗？</p>
        <!--
          <template v-if="showAgainConfirmTipsText">
          <p>完成该笔订单后，距离获得 <span class="keywords">{{ isSale ? completeLevel.nextLevelRewardAmount : consumeLevel.nextLevelRewardAmount }}消费券 </span>奖励还有一步之遥！</p>
          <p>完成的订单越多，获得的奖励越高！</p>
          </template>
        -->
      </div>
      <div slot="footer" class="all-dialog-footer">
        <el-button
          v-waiting="[`post::/draft/order/singleExecuteOrderTradeStep`,
                      `post::/draft/order/dispute/saveEvidence`]"
          @click="confirm"
        >
          {{ isEPlusChannel && waitingPay ? '确认取消' : '仍要取消' }}
        </el-button>
        <el-button type="primary" @click="confirmVisible = false">暂不取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
/* eslint-disable no-magic-numbers */

import orderApi from '@/apis/order'// 订单接口
import { OSS_DIR, DRAFT_STATUS, EXECUTE_TRANSACTION_PROCESS, TRANSACTION_STATUS, BROKE_STATUS, CANCEL_TYPE_VALUE_MAP, CANCEL_TYPE, PAYMENT_CHANNEL, E_PLUS_LOGIN_AUTH_KEY, E_PLUS_RECOGNITION_SCENE } from '@/constant'
import WarnContent from '../../common/warn-content.vue'
import Storage from '@/common/js/storage' // 本地缓存对象
import { SEND_CODE_TIME } from '@/constant-storage' // 发送验证码倒计时key，操作名称拼订单号
import orderStatusMixin from '../order-status-mixin'
import listenerEventMixins from '../listener-event.mixin'
import { PLATFORM_DEFAULT_RULESNEW_URL, PLATFORM_DEFAULT_RULESNEW_URL_NEW_DRAFT } from '@/constants/oss-files-url' // 平台订单违约规则url
import ImgUpload from '@/views/components/common/img-upload/img-upload.vue' // 图片上传组件
import { mapGetters } from 'vuex'
import IdentificationAssistant from '@/views/components/identification-assistant/index.vue'
import { OPEN_BANK_LOGIN_AUTH_DIALOG, OPEN_BANK_LOGIN_AUTH_SUCCESS } from '@/event/modules/site'
let sendCodeTimeKey = ''

export default {
  name: 'cancel-order',
  components: { WarnContent, ImgUpload, IdentificationAssistant },
  mixins: [orderStatusMixin, listenerEventMixins],
  data() {
    return {
      PLATFORM_DEFAULT_RULESNEW_URL, // 平台订单违约规则url
      PLATFORM_DEFAULT_RULESNEW_URL_NEW_DRAFT, // 平台订单违约规则url
      E_PLUS_RECOGNITION_SCENE,
      OSS_DIR,
      orderApi,
      order: {
        // status: null, // 订单状态
        // responsibleParty: null, // 责任方 0 资方  1 票方
        // initiator: null, // 发起方 0 资方  1 票方
      }, // 当前订单对象
      visible: false, // 弹窗是否打开
      dialogWidth: '600px',
      clock: null, // 定时器
      leftSeconds: 0, // 剩余秒数
      getVoiceCodeVisible: false, // 获取语音验证码 是否展示
      DRAFT_STATUS, // 订单所有状态
      form: {
        cancelMsg: null, // 补充说明
        cancelType: null, // 原因选择
        phone: '', // 手机号
        validateCode: '', // 验证码
        evidence: [], // 票方取消凭证
      },
      rules: { // 表单校验规则
        cancelType: this.hasCancelType || [
          {
            required: true,
            message: '请选择取消原因',
            trigger: ['none', 'change', 'blur']
          },
        ],
        validateCode: [
          {
            required: true,
            pattern: /[0-9]{6}$/,
            message: '请填写6位验证码',
            trigger: ['change', 'blur']
          },
        ],
        payPassword: [
          {
            required: true,
            // pattern: /[0-9]{6}$/,
            // message: '请填写6位验证码',
            message: '请输入支付密码',
            trigger: ['change', 'blur']
          },
        ],
      },
      loading: false,
      TRANSACTION_STATUS, // 订单交易状态
      confirmVisible: false // 消费券二次确认
    }
  },
  computed: {
    ...mapGetters('user', {
      couponOpen: 'couponOpen', // 优惠券活动开关，1开启，0关闭
      consumeLevel: 'consumeLevel', // 消费云豆
      completeLevel: 'completeLevel' // 完成云豆

    }),
    ...mapGetters('common', {
      startCrawlerScheme: 'startCrawlerScheme', // E++是否开启爬虫方案 1开启 0关闭
    }),
    // 责任方 (当前取消订单/原因的责任方)  //  1 资方 2 票方 3 双方违约 4 双方无责
    responsibleParty() {
      const { isOvertime, billOrderCancelMsg } = (this.order || {})
      const { cancelType } = (billOrderCancelMsg || {})
      let res = null

      // 待支付
      if (this.waitingPay) {
        // 票方
        if (this.isSale) {
          res = isOvertime ? BROKE_STATUS.BUY.id : BROKE_STATUS.SALE.id
        }
      }

      // 待背书
      if (this.waitingEndorse) {
        // 资方
        if (this.isBuy) {
          res = isOvertime ? BROKE_STATUS.SALE.id : BROKE_STATUS.BUY.id // 超时责任方为票方
        }
      }

      // 待签收
      if (this.waitingSubmission) {
        // 票方
        if (this.isSale && isOvertime) {
          res = BROKE_STATUS.BUY.id // 资方超时未签收
        }
      }

      // 取消中-待背书
      if (this.cancelingEndorse) {
        res = BROKE_STATUS.SALE.id // 责任方为票方
      }

      // 取消中-待支付
      if (this.cancelingPay) {
        // 不出票类型 属于票方责任
        res = [CANCEL_TYPE.NO_DRAFT.id].includes(cancelType) ? BROKE_STATUS.SALE.id : BROKE_STATUS.BUY.id // 责任方为资方
      }

      // 取消中-待签收
      if (this.cancelingSubmission) {
        // 需要根据资方选择的取消原因来决定责任方
        this.buyWaitingSignCancelOption.forEach(v => {
          const reason = v.list.filter(k => k.value === cancelType)[0]?.value
          if (reason) {
            res = v.brokeStatus // 责任方
          }
        })
        // 取消原因是背书自有户
        if (cancelType === CANCEL_TYPE.ENDORSE_OWNER.id) {
          res = BROKE_STATUS.NO_BROKE.id // 双方无责
        }
      }
      return res
    },

    // 责任方文案
    responsiblePartyLabel() {
      return (Object.values(BROKE_STATUS).filter(v => v.id === this.responsibleParty)[0]?.responsibleLabel || '')
    },

    // 是否票方责任（当前取消订单/原因的责任方）
    isSaleResponsible() {
      return this.responsibleParty === BROKE_STATUS.SALE.id
    },

    // 是否资方责任（当前取消订单/原因的责任方）
    isBuyResponsible() {
      return this.responsibleParty === BROKE_STATUS.BUY.id
    },

    // 是否双方无责任（当前取消订单/原因的责任方）
    isNoResponsible() {
      return this.responsibleParty === BROKE_STATUS.NO_BROKE.id
    },

    // 弹窗标题
    titleDialog() {
      let title = '取消订单'
      const { billOrderCancelMsg } = (this.order || {})
      const { cancelType } = (billOrderCancelMsg || {})

      switch (true) {
        case (this.isBuyResponsible && (this.waitingPay || this.cancelingPay)): // 资方责任 && 待支付/取消中-待支付
          title = '取消原因（资方责任：超时未支付）'
          break
        case (this.isSaleResponsible && (this.waitingPay || this.cancelingPay)): // 票方责任 && 待支付/取消中-待支付
          title = '取消原因（票方责任：不出票）'
          break
        case (this.waitingConfirm && !!this.order.agentOrder): // 资方待确认 定向单
          title = '取消订单'
          break
        case (this.isSaleResponsible && (this.waitingEndorse || this.cancelingEndorse)): // 票方责任 && 待背书/取消中-待背书
          title = `取消订单（票方责任：${CANCEL_TYPE_VALUE_MAP[cancelType] || '超时未背书'}）`
          break
        case (this.isSale && this.isBuyResponsible && (this.waitingSubmission)): // 票方责任 && 资方责任 && 待签收
          title = '取消订单（资方责任：超时未签收）'
          break
        case (this.cancelingSubmission): // 取消中-待签收 （额外情况-超时未签收未包含在responsiblePartyLabel中 固定设置为资方责任）
          title = this.responsiblePartyLabel ? `取消订单（${this.responsiblePartyLabel}：${CANCEL_TYPE_VALUE_MAP[cancelType] || ''}）` : `取消订单（资方责任：${CANCEL_TYPE_VALUE_MAP[cancelType] || ''}）`
          break
        case (this.isBuyWaitingConfirmCancel):
          title = '取消订单（票方未确认订单）'
          break
        case this.isNoResponsible: // 双方无责
          title = `取消订单（双方无责：${CANCEL_TYPE_VALUE_MAP[cancelType] || ''}）`
          break
        default:
          break
          // code block
      }
      return title
    },

    // 接码手机号-留在jd的手机号，交易中发送验证码的
    zfTradeMobile() {
      return this.$store.state?.user?.userInfo?.zfTradeMobile
    },

    // 是否左右布局
    // leftRightLayout() {
    //   return DRAFT_STATUS.WAITING_SIGN.id === this.order.tabStatus && this.order.initiator === 0
    // },

    // 是否资方待签收取消订单
    isBuyWaitingSignCancel() {
      return DRAFT_STATUS.WAITING_SIGN.id === this.order.tabStatus && this.isBuy
    },

    // 是否资方待确认取消订单(非定向单)
    isBuyWaitingConfirmCancel() {
      return this.waitingConfirm && !this.isAgentOrder && this.isBuy
    },

    // 是否资方待签收取消订单
    buyWaitingSignCancelOption() {
      return [
        {
          brokeStatus: BROKE_STATUS.SALE.id, //  资方 2 票方 3 双方违约 4 双方无责
          brokeLabel: BROKE_STATUS.SALE.name,
          responsibleLabel: BROKE_STATUS.SALE.responsibleLabel,
          list: [
            { value: 7, label: '票面信息或背面不符' },
            { value: 8, label: '瑕疵描述不符' },
            { value: 9, label: '背书手数不符' },
            { value: 23, label: '错户背书' },
          ]
        },
        {
          brokeStatus: BROKE_STATUS.BUY.id, //  1 资方 2 票方 3 双方违约 4 双方无责
          brokeLabel: BROKE_STATUS.BUY.name,
          responsibleLabel: BROKE_STATUS.BUY.responsibleLabel,
          list: [
            { value: 10, label: '额度满' },
            { value: 11, label: '网银系统等问题无法签收' },
            { value: 12, label: '背书自有户' },
            { value: 13, label: '其他' },
          ]
        }
      ]
    },

    // 取消类型选项
    option() {
      let reasonType
      const { tabStatus } = this.order
      switch (tabStatus) {
        case DRAFT_STATUS.WAITING_CONFIRM.id:// 待确认1
          // 定向且资方
          if (this.isAgentOrder && this.isBuy) {
            reasonType = [
              { value: 6, label: '不想要' },
              { value: 5, label: '资金问题' },
              { value: 13, label: '其他' },
            ]
          } else if (this.isRadarType && this.isSale) {
            reasonType = [
              { value: 1, label: '票不在户' },
              { value: 2, label: '挂错户' },
              { value: 21, label: '不满足资方要求' },
              { value: 13, label: '其他' },
            ]
          } else {
            reasonType = [
              { value: 1, label: '票不在户' },
              { value: 2, label: '挂错户' },
              { value: 13, label: '其他' },
            ]
          }
          break
        case DRAFT_STATUS.WAITING_ENDORSE.id:// 待带背书4

          reasonType = this.isSale ? [
            { value: 1, label: '票不在户' },
            { value: 2, label: '挂错户' },
            { value: 3, label: '选错银行' },
            { value: 13, label: '其他' },
          ] : []
          break
        case DRAFT_STATUS.WAITING_PAY.id: // 待支付2
          reasonType = [
            { value: 6, label: '不想要' },
            { value: 5, label: '资金问题' },
            { value: 13, label: '其他' },
          ]
          break
        case DRAFT_STATUS.WAITING_SIGN.id:// 待签收4
          reasonType = this.buyWaitingSignCancelOption
          break
        default: reasonType = []
          // code block
      }
      return reasonType
    },

    // 是否有文字提醒
    hasContent() {
      const ticketHolderHas = [
        DRAFT_STATUS.WAITING_PAY.id, // 2
        DRAFT_STATUS.WAITING_ENDORSE.id, // 3
        DRAFT_STATUS.WAITING_SIGN.id, // 4
        DRAFT_STATUS.CANCELING.id// 6
      ]
      const receivingPartyHas = [
        DRAFT_STATUS.WAITING_SIGN.id, // 4
        DRAFT_STATUS.WAITING_PAY.id, // 2
        DRAFT_STATUS.WAITING_ENDORSE.id, // 3
        DRAFT_STATUS.CANCELING.id, // 6
      ]
      // 非定向单-资方待确认状态取消订单显示文字提醒
      !this.isAgentOrder && receivingPartyHas.push(DRAFT_STATUS.WAITING_CONFIRM.id)
      return this.isSale ? ticketHolderHas.includes(this.order.tabStatus) : (receivingPartyHas.includes(this.order.tabStatus))
    },

    // 是否有取消类型
    hasCancelType() {
      // 票方
      const ticketHolderHas = [
        DRAFT_STATUS.WAITING_CONFIRM.id, // 2
        DRAFT_STATUS.WAITING_ENDORSE.id, // 3
      ]
      // 资方
      const receivingPartyHas = [
        // DRAFT_STATUS.WAITING_CONFIRM.id, // 2
        DRAFT_STATUS.WAITING_PAY.id, // 3
        DRAFT_STATUS.WAITING_SIGN.id, // 5
      ]
      // 定向单-资方待确认状态取消订单
      this.isAgentOrder && receivingPartyHas.push(DRAFT_STATUS.WAITING_CONFIRM.id)
      return this.isSale ? ticketHolderHas.includes(this.order.tabStatus) : receivingPartyHas.includes(this.order.tabStatus)
    },

    // 是否需要上传凭证
    hasUploadEvidence() {
      // 1. 亿联银行渠道
      // 2. 票方
      // 3. 待背书取消，待签收取消
      // return this.isYlBankPaymentChannel && this.isSale && (this.waitingEndorse || this.waitingSubmission || this.cancelingEndorse || this.cancelingSubmission)
      return false
    },

    // 取消描述上边距
    hasCancelMsgMarginTop() {
      return !(this.isSale && this.waitingPay)
    },

    // 取消描述是否有
    hasCancelMsg() {
      const ticketHolderHas = [
        DRAFT_STATUS.WAITING_CONFIRM.id, // 1
        DRAFT_STATUS.WAITING_PAY.id, // 2
        DRAFT_STATUS.WAITING_ENDORSE.id, // 3
      ]
      const receivingPartyHas = [
        DRAFT_STATUS.WAITING_PAY.id, // 2
        DRAFT_STATUS.WAITING_ENDORSE.id // 3
      ]
      return this.isSale ? ticketHolderHas.includes(this.order.tabStatus) || this.hasUploadEvidence : (receivingPartyHas.includes(this.order.tabStatus) || (!!this.order.agentOrder && this.waitingConfirm))
    },

    // 是否要接收验证码 E++渠道不展示
    hasGetCode() {
      const ticketHolderHas = [
        DRAFT_STATUS.WAITING_ENDORSE.id, // 3
        DRAFT_STATUS.WAITING_SIGN.id, // 4
        DRAFT_STATUS.CANCELING.id, // 6
      ]
      const receivingPartyHas = [DRAFT_STATUS.CANCELING.id] // 6

      return this.isSale ? ticketHolderHas.includes(this.order.tabStatus) && !this.hasUploadEvidence && !this.isEPlusChannel : (receivingPartyHas.includes(this.order.tabStatus) && !this.isEPlusChannel)
    },

    // 是否可以获取验证码
    canGetCode() {
      return !(this.leftSeconds > 0)
    },

    // 文字提示是否为绿色
    isGreenWarnContent() {
      let res = false // 默认红色底

      // 自己无责/双方无责则为绿色底
      if ((this.isBuy && this.isSaleResponsible) || (this.isSale && this.isBuyResponsible) || this.isNoResponsible) {
        res = true
      }

      return res
    },

    // 中度违约扣米
    deductSdm() {
      let price = this.isSale ? this.order.sellerMarginAmount : this.order.marginAmount
      if (price > 10) {
        const halfPrice = price / 2
        price = halfPrice > 10 ? halfPrice : 10
      }
      return price
    },

    // 是否票容易发布的订单
    isRongyiOrder() {
      return this.order.publishSource === 7
    },

    // 是否是亿联银行渠道订单
    isYlBankPaymentChannel() {
      const { paymentChannel } = (this.order || {})
      return [PAYMENT_CHANNEL.YI_LIAN_BANK.id, PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id].includes(paymentChannel)
    },
    // 取消订单二次确认弹窗-是否显示提示文案，同意取消方不展示，发我取消方展示
    showAgainConfirmTipsText() {
      return !this.canceling
    },
    // 是否E++渠道
    isEPlusChannel() {
      return this.order.paymentChannel === PAYMENT_CHANNEL.YL_PLUS.id
    }
  },

  watch: {
    visible: {
      handler(val) {
        if (val) {
          sendCodeTimeKey = `cancelOrder_${this.order.orderNo}`
          this.leftSeconds = 0
          // 注册弹窗打开时，若上一次发送验证码的倒计时未结束，则继续开始倒计时
          if (val && Storage.get(SEND_CODE_TIME) && Storage.get(SEND_CODE_TIME)[sendCodeTimeKey]) {
            this.startCountDown()
          }
        }
      },
    }
  },

  methods: {
    // 简易版网银登录成功回调 key=>参照枚举类型 E_PLUS_LOGIN_AUTH_KEY
    handleAuthSuccess(obj) {
      if (obj?.key === E_PLUS_LOGIN_AUTH_KEY.CONFIRM_CANCEL_ORDER) { // 网银验证通过 执行取消订单
        this.postCancelTrade()
      }
    },
    init() {
      this.visible = true
      // E++开启爬虫方案时,登录校验成功
      this.$event.on(OPEN_BANK_LOGIN_AUTH_SUCCESS, this.handleAuthSuccess)
      this.$emit('init')
      this.$nextTick().then(() => {
        // console.log(this.order)
        // this.dialogWidth = this.waitingSubmission && this.order.initiator === 0 ? '960px' : '600px'
        // this.form.cancelType = this.option && this.option.length > 0 && this.option[0]?.value // 设置取消类型默认选项
      })
    },
    // 监听enter键盘事件
    listenerEvent(event) {
      if (event.keyCode === 13 && this.hasGetCode) {
        this.confirm()
      }
    },
    // 取消成功toast消息
    getSuccessMsg(status) {
      let msg
      switch (status) {
        case DRAFT_STATUS.CANCELING.id:// 取消中 同意6
          msg = '操作成功'
          break
        case DRAFT_STATUS.WAITING_SIGN.id:// 待签收4
          msg = this.isSale ? '您已取消订单' : '您已提交取消申请，等待票方确认'
          break
        case DRAFT_STATUS.WAITING_ENDORSE.id:// 待签收3
          msg = this.isSale ? '您已取消订单' : '您已提交取消申请，等待票方确认'
          break
        default:
          msg = '订单取消中，请稍后'
          // code block
      }
      return msg
    },

    // 获取订单操作场景值code
    getOrderStepsEnumCode() {
      let code
      // 待确认
      if (this.waitingConfirm) {
        code = this.isSale ? EXECUTE_TRANSACTION_PROCESS.SELLER_CANCEL_BE_CONFIRMED : EXECUTE_TRANSACTION_PROCESS.BUYERS_CANCEL_BE_CONFIRMED
      }
      // 待支付
      if (this.waitingPay) {
        code = this.isSale ? EXECUTE_TRANSACTION_PROCESS.SELLER_CANCEL_BE_PAY : EXECUTE_TRANSACTION_PROCESS.BUYERS_CANCEL_BE_PAY
      }
      // 待背书
      if (this.waitingEndorse) {
        code = this.isSale ? EXECUTE_TRANSACTION_PROCESS.SELLER_CANCEL_BE_ENDORSEMENT : EXECUTE_TRANSACTION_PROCESS.BUYERS_CANCEL_BE_ENDORSEMENT
      }
      // 待签收
      if (this.waitingSubmission) {
        code = this.isSale ? EXECUTE_TRANSACTION_PROCESS.SELLER_CANCEL_BE_SIGN : EXECUTE_TRANSACTION_PROCESS.BUYERS_CANCEL_BE_SIGN
      }
      // 取消中 (票方同意取消)
      if (this.canceling && this.isSale) {
        this.cancelingEndorse && (code = EXECUTE_TRANSACTION_PROCESS.CONFIRMED_CANCEL_BE_ENDORSEMENT)
        this.cancelingSubmission && (code = EXECUTE_TRANSACTION_PROCESS.CONFIRMED_CANCEL_BE_SIGN)
      }
      //  E++渠道 取消中-待支付 (资方同意取消)
      if (this.canceling && this.isBuy && this.isEPlusChannel) {
        this.cancelingPay && (code = EXECUTE_TRANSACTION_PROCESS.BUYER_AGREE_CANCEL_PENDING_PAY_ORDER) // 资方同意取消 待支付订单
        this.cancelingEndorse && (code = EXECUTE_TRANSACTION_PROCESS.BUYER_AGREE_CANCEL_PENDING_ENDORSE_ORDER) // 资方同意取消 待背书订单
        this.cancelingSubmission && (code = EXECUTE_TRANSACTION_PROCESS.BUYER_AGREE_CANCEL_PENDING_SIGN_ORDER) // 资方同意取消 待签收订单
      }
      return code
    },

    // 取消类型参数
    handleCancelRequest() {
      const cancelRequest = { ...this.form }

      // 支付密码不存放在cancelRequest中
      cancelRequest?.payPassword && delete cancelRequest.payPassword
      // *由于以下场景的取消订单没有取消原因选项，故手动补充对应的取消类型参数

      // 待确认-资方接单3分钟后取消订单（非定向单）
      if (this.waitingConfirm && !this.isAgentOrder && this.isBuy) {
        cancelRequest.cancelType = CANCEL_TYPE.NOT_CONFIRM_SELLER.id // 票方未确认
      }
      // 待支付-票方取消
      if (this.waitingPay && this.isSale) {
        cancelRequest.cancelType = this.order?.isOvertime ? CANCEL_TYPE.PAY_OVERTIME.id : CANCEL_TYPE.NO_DRAFT.id // 资方超时未支付 / 不出票
      }

      // 待背书-超时-资方取消
      if (this.waitingEndorse && this.isBuy && this.order?.isOvertime) {
        cancelRequest.cancelType = CANCEL_TYPE.ENDORSE_OVERTIME.id // 票方超时未背书
      }

      // 待签收-超时-票方取消
      if (this.waitingSubmission && this.isSale && this.order?.isOvertime) {
        cancelRequest.cancelType = CANCEL_TYPE.SUBMISSION_OVERTIME.id // 资方超时未签收
      }

      return cancelRequest
    },

    // 确认取消
    async onConfirmCancel() {
      // 如果是亿联银行渠道，待背书、待签收状态申请取消过一次，票方无法二次取消
      // if (this.isSale && this.isYlBankPaymentChannel && (this.order.disputeInfo?.invokeThirdPartyTimes >= 1 || this.order.disputeInfo?.isDisputeOrder)) {
      //   this.$alert('无法重复取消，若您仍需取消该笔订单请联系客户经理', '提示', {
      //     type: 'warning',
      //     confirmButtonText: '确定',
      //   })
      //   return
      // }

      await this.$refs.form.validate()
      // 如果消费券开关打开 需要有一个消费券二次弹框&& 不是最高等级
      if (this.couponOpen && !(this.isSale ? this.completeLevel.highestLevel : this.consumeLevel.highestLevel)) {
        this.confirmVisible = true
      } else {
        this.confirm()
      }
    },
    // 确认取消
    confirm() {
      try {
        // 亿连银行渠道，票方发起取消，或票方同意取消，直接调用争议接口
        if (this.hasUploadEvidence) {
          // 因为接口设计，这里需要将取消原因从value转为label ！！！
          const cancelTypeOptions = this.isBuyWaitingSignCancel ? this.buyWaitingSignCancelOption.reduce((acc, cur) => acc.concat(cur.list), []) : this.option
          const cancelTypeStr = cancelTypeOptions.find(item => item.value === this.form.cancelType)?.label
          // 资方发起取消，票方同意取消，取消类型参数：智联通 => value，智付E+ =>label
          const billOrderCancelType = this.order.paymentChannel === PAYMENT_CHANNEL.YI_LIAN_BANK.id ? this.order?.billOrderCancelMsg?.cancelType : CANCEL_TYPE_VALUE_MAP[this.order?.billOrderCancelMsg?.cancelType]
          const param = {
            orderNo: this.order.orderNo, // 订单号，非空字段
            evidenceImgUrl: this.form.evidence.filter(item => !!item).toString(), // 凭证图片url，多个以，隔开，非空字段
            disputeSource: 1, // 争议订单来源，1-票方，2-资方，非空字段
            reason: this.hasCancelType ? cancelTypeStr : billOrderCancelType, // 取消原因
            replenish: this.form.cancelMsg // 补充说明
          }
          this.postEvidence(param)
        } else {
          // E++渠道 资方 爬虫方案登录校验
          // eslint-disable-next-line no-lonely-if
          if (this.startCrawlerScheme && this.isEPlusChannel && this.isBuy) {
            this.$event.emit(OPEN_BANK_LOGIN_AUTH_DIALOG, { key: E_PLUS_LOGIN_AUTH_KEY.CONFIRM_CANCEL_ORDER })
          } else {
            this.postCancelTrade()
          }
          // this.postCancelTrade(param)
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log('error :>> ', error)
      }
    },

    // 取消请求
    async postCancelTrade() {
      const param = {
        payPassword: this.isEPlusChannel ? this.form.payPassword : null,
        orderNoList: [this.order.orderNo], // 订单编号,必填字段不可为空
        orderStepsEnumCode: this.getOrderStepsEnumCode(), // 必填字段,订单步骤
        cancelRequest: this.handleCancelRequest(), // 取消类型参数
      }
      try {
        const message = this.getSuccessMsg(this.order.tabStatus)
        this.loading = true
        await orderApi.postSingleExecuteOrderTradeStep(param)
        this.$refs.form && this.$refs.form.resetFields()
        this.$message({
          message,
          type: 'success'
        })
        this.$emit('success')
        this.loading = false
        this.confirmVisible = false
        this.handleClose()
      } catch (error) {
        this.confirmVisible = false
        this.loading = false
      }
    },

    // 亿连银行渠道，票方发起取消，或票方同意取消，直接调用争议接口
    async postEvidence(params) {
      try {
        this.loading = true
        await orderApi.postEvidence(params)
        this.$message({
          message: '操作成功，请等待客服处理',
          type: 'success'
        })

        this.$emit('success')
        this.loading = false
        this.confirmVisible = false
        this.handleClose()
      } catch (error) {
        this.confirmVisible = false
        this.loading = false
      }
    },

    // 获取验证码
    async getCode(isVoice) {
      const TEXT_CODE_TYPE = 1
      const VOICE_CODE_TYPE = 2
      if (!this.canGetCode) {
        return
      }
      try {
        await orderApi.getCancelVerifyCode({
          orderNo: this.order.orderNo,
          verifyCodeType: isVoice ? VOICE_CODE_TYPE : TEXT_CODE_TYPE
        })
        this.$message({
          message: `${isVoice ? '语音' : '短信'}验证码已发送，请注意查收`,
          type: 'success'
        })
        this.startCountDown()
      } catch (error) {
        // eslint-disable-next-line no-console
      }
    },

    // 开始倒计时
    startCountDown() {
      let nowTime = new Date().getTime() // 当前时间
      let sendCodeTime = Storage.get(SEND_CODE_TIME) || '' // 所有发送验证码时间的本地缓存对象
      let lastTime = sendCodeTime && sendCodeTime[sendCodeTimeKey] ? sendCodeTime[sendCodeTimeKey] : null // 上一次发送验证码时间
      let durationTime = 60 // 倒计时时间

      if (sendCodeTime) {
        // 上一次发送验证码的倒计时未结束
        if (lastTime) {
          durationTime = durationTime - Math.round(((nowTime - lastTime) / 1000)) // 计算还剩多少秒倒计时
        } else {
          sendCodeTime[sendCodeTimeKey] = nowTime
          Storage.set(SEND_CODE_TIME, sendCodeTime) // 本次的发送验证码倒计时添加入缓存对象
        }
      } else {
        sendCodeTime = {
          [sendCodeTimeKey]: nowTime
        }
        // 保存这次发送验证码的时间
        Storage.set(SEND_CODE_TIME, sendCodeTime)
      }

      this.leftSeconds = durationTime
      const countDown = () => {
        this.clock = setTimeout(() => {
          this.leftSeconds -= 1
          if (this.leftSeconds <= 0) {
            this.getVoiceCodeVisible = true // 第一次倒计时结束展示获取语音验证码
            sendCodeTime[sendCodeTimeKey] = null // 倒计时结束，清掉获取验证码倒计时缓存
            Storage.set(SEND_CODE_TIME, sendCodeTime)
          } else {
            countDown()
          }
        }, 1000)
      }
      countDown()
    },

    // 清除定时器
    clearTime() {
      clearTimeout(this.clock) // 清除
      this.clock = null
      // this.canGetCode = true
    },

    // 关闭之前
    handleClose() {
      this.$refs.form && this.$refs.form.resetFields()
      this.clearTime()
      this.visible = false
      this.$event.off(OPEN_BANK_LOGIN_AUTH_SUCCESS, this.handleAuthSuccess)
      this.$emit('destroy')
    },
    // 跳转至活动中心
    goCerter() {
      this.$router.push('/user-center/coupon?tabs=receiveCentre')
      this.confirmVisible = false
      this.visible = false
    },
    // 检查数据是否都填写完成
    async checkData() {
      let status = await this.$refs.form.validate()
      return status
    },
    // 跳转至账户中心
    linkToAccountCenter() {
      this.handleClose()
      this.$router.push('/user-center/safe-center')
    }
  }
}
</script>
