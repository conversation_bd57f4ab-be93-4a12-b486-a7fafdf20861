<!-- 未收到票 -->
<style lang="scss" scoped>
.order-not-received {
  display: inline;
}
</style>

<template>
  <div class="order-not-received order-operation">
    <slot name="button">
      <el-button
        v-waiting="[`post::/draft/order/executeOrderTradeStep?orderNo=${firstOrderNo}&type=orderNotReceived`,
                    `post::/draft/order/singleExecuteOrderTradeStep?orderNo=${firstOrderNo}&type=orderNotReceived`]"
        v-bind="$attrs"
        :type="$attrs.type || 'primary'"
        :width="$attrs.width || '104'"
        :height="$attrs.height || '40'"
        :border="!$attrs.type"
        :loading="loading"
        @click="init"
        v-on="$listeners"
      >
        <slot>未收到票</slot>
      </el-button>
    </slot>
  </div>
</template>

<script>
import { EXECUTE_TRANSACTION_PROCESS } from '@/constant'
import orderApi from '@/apis/order'
export default {
  name: 'order-not-received',
  props: {
    order: {
      type: [Object, Array],
      required: true
    },
  },
  data() {
    return {
      EXECUTE_TRANSACTION_PROCESS, // 所有操作场景
      loading: false
    }
  },

  computed: {
    // 是否批量
    hasAllOrder() {
      return this.order instanceof Array
    },
    // 订单号，数组时取第一条数据，用于loading时，不要所有按钮都显示loading状态
    firstOrderNo() {
      return this.hasAllOrder && this.order.length ? this.order[0].orderNo : this.order.orderNo
    }
  },

  methods: {
    init() {
      this.orderNotReceived(this.order)
    },

    // 发起未收到票请求
    async orderNotReceived(order) {
      try {
        const orderNoList = this.hasAllOrder ? order.map(i => i.orderNo) : [order.orderNo]
        const param = {
          type: 'orderNotReceived', // 用于点击约束误触发
          orderNoList, // 订单编号 ,Long
          orderStepsEnumCode: EXECUTE_TRANSACTION_PROCESS.TICKET_NOT_RECEIVED // 买家没收到单
        }
        if (this.hasAllOrder) {
          const data = await orderApi.postExecuteOrderTradeStep(param)
          if (!data.failNum) { // 失败数为0,即全部成功
            this.$message.success('操作成功')
          }
        } else {
          await orderApi.postSingleExecuteOrderTradeStep(param)
          this.$message({
            type: 'success',
            message: '操作成功'
          })
        }
        this.$emit('success')
      } catch (error) {
        // console.log('未收到票请求error :>> ', error)
      }
    }
  }
}
</script>
