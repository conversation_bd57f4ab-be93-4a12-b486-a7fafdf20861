<!-- 催单催单 -->
<style lang="scss" scoped>
.order-urge {
  display: inline;

  .el-button {
    position: relative;

    &.has-tag::after {
      position: absolute;
      top: -1px;
      left: -1px;
      border-top: 27px solid $color-assist3;
      border-right: 28px solid transparent;
      width: 0;
      height: 0;
      content: "";
    }

    .rice-tag {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      font-size: 12px;
      font-weight: 600;
      color: $color-FFFFFF;
      line-height: 20px;
      transform: scale(.8);
      transform-origin: top;
    }
  }

  ::v-deep .el-button [class*="el-icon-"] + span {
    margin-left: -5px;
  }

  .ellipsis-text {
    display: flex;
  }
}
</style>

<template>
  <div class="order-urge order-operation">
    <text-tooltip :disabled="(order.disputeInfo && !order.disputeInfo.dispute) || !order.disputeInfo" content="请等待争议处理结果">
      <slot name="button">
        <el-button
          v-waiting="`post::api/platform/bill/order/accelerateLog/reminders?orderNo=${order.orderNo}`"
          v-bind="$attrs"
          :type="$attrs.type || 'primary'"
          :width="$attrs.width || '70'"
          :class="hasTag && 'has-tag'"
          :height="$attrs.height || '40'"
          @click="init"
          v-on="$listeners"
        >
          <span v-if="hasTag" class="rice-tag">{{ sdmUnit }}</span>
          <slot>催单{{ order.disputeInfo && order.disputeInfo.dispute }}</slot>
        </el-button>
      </slot>
    </text-tooltip>
  </div>
</template>

<script>
import orderApi from '@/apis/order'

export default {
  name: 'order-urge',
  components: {

  },
  props: {
    order: {
      type: [Object, Array],
      required: true
    },
    hasTag: [String, Number, Boolean]
  },
  data() {
    return {
    }
  },
  methods: {
    init() {
      this.orderUrge(this.order)
    },

    // 发起催单
    async orderUrge(order) {
      const param = {
        orderNo: order.orderNo, // 订单编号 ,Long
        accelerateType: order.tabStatus || 0, // 催促场景，2 票方催单,请尽快确认 3，票方催单,请尽快支付 4，票方催单,请尽快背书 5票方催单,请尽快签收
      }
      await orderApi.postRemindersOrder(param)
      this.$emit('success')
      this.$message({
        type: 'success',
        message: '催单成功!'
      })
    }
  }
}
</script>
