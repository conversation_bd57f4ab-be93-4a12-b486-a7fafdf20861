<!-- 处理/查询变更票号 -->
<style lang="scss" scoped>
.c-btn-wrapper {
  display: inline-block;
}
</style>

<template>
  <div class="c-btn-wrapper order-operation" @click="init">
    <slot name="button">
      <el-button
        v-bind="$attrs"
        :type="$attrs.type || 'primary'"
        :width="$attrs.width || '140'"
        :height="$attrs.height || '40'"
        :size="$attrs.size || 'large'"
        v-on="$listeners"
      >
        <slot>{{ btnText }}</slot>
      </el-button>
    </slot>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import UpdateTicketNumberDialog from './handle-ticket-number-dialog.vue'
import orderOperationMixin from '../order-operation-mixin'

export default {
  name: 'handle-ticket-number',
  mixins: [orderOperationMixin(UpdateTicketNumberDialog)],
  props: {},
  computed: {
    ...mapGetters('draft-detail', {
      updateTicketInfo: 'updateTicketInfo'
    }),
    btnText() {
      let text = ''

      /**
       * 票方发起  申请状态 0申请  票方可以修改  资方可以同意
       * 资方发起  申请状态 0申请  票方可以同意、拒绝  资方可以修改
       */
      if (this.updateTicketInfo) {
        // status是0，资方同意|票方同意、拒绝；其他可查询
        const { status } = this.updateTicketInfo
        text = status === 0 ? '处理变更票号' : '查看变更票号'
      }
      return text
    }

  }
}
</script>
