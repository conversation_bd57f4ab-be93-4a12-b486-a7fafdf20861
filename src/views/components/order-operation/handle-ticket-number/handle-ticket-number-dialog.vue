<style lang="scss" scoped>
.c-form-wrapper {
  padding: 16px;
  background: $color-FFFFFF;

  .img-ticket-div {
    width: 200px;
  }

  ::v-deep {
    .el-form-item__label {
      float: none;
    }
  }
}

.tips-color {
  color: $color-warning;
}

.text-font {
  margin-top: 16px;
  font-weight: bold;
}

.draft-history {
  color: $--color-primary;
  cursor: pointer;
}

.footer-fix {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>

<style>
.custom-msg-box-cls {
  .el-message-box__content {
    padding: 0 32px 12px 60px !important;
  }
}
</style>

<template>
  <el-dialog
    width="560px"
    :visible.sync="visible"
    append-to-body
    :close-on-click-modal="false"
    :title="title"
    custom-class="default"
    :before-close="handleClose"
  >
    <el-form
      ref="formInstance"
      :model="form"
      class="c-form-wrapper"
    >
      <el-form-item :label="`实际${labelText}票据正面`" prop="draftImgUrl">
        <div class="img-ticket-div">
          <ImgUpload
            v-model="form.draftImgUrl"
            height="98"
            :dir="OSS_DIR.DRAFT"
            :size-limit="4"
            size-limit-message="上传票面图片大小不能超过 4MB！"
            disabled
          >
            <template slot="empty">
              点击或拖拽上传票据正面
            </template>
          </ImgUpload>
        </div>
      </el-form-item>
      <el-form-item :label="`实际${labelText}票据号码`" prop="draftNo">
        <span>{{ form.draftNo }}</span>
      </el-form-item>
      <el-form-item v-if="isNewDraft" label="子票区间" prop="draftNo">
        <span>{{ form.subTicketStart }}</span>-<span>{{ form.subTicketEnd }}</span>
      </el-form-item>
      <el-form-item v-if="isSale" label="备注" prop="rejectReason">
        <el-input v-model="form.rejectReason" :disabled="handleStatus === 2" placeholder="拒绝申请时可填写原因" />
      </el-form-item>
    </el-form>
    <div slot="footer">
      <div :class="updateTicketInfo.status === 0 ? 'footer-fix' : ''">
        <span v-if="updateTicketInfo.status === 0" class="draft-history" @click="updateDraftNoRecord">上次变更票号记录</span>
        <div>
          <el-button @click="handleClose">取消</el-button>
          <el-button
            v-if="isSale && handleStatus === 1"
            border
            type="primary"
            @click="onReject(4)"
          >
            拒绝
          </el-button>
          <el-button v-if="handleStatus !== 2" type="primary" @click="onConfirm(3)">同意</el-button>
        </div>
      </div>
    </div>
    <TicketNumberRecord ref="ticketNumberRecordRefs" />
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
// oss目录
import { OSS_DIR } from '@/constant'
import ImgUpload from '@/views/components/common/img-upload/img-upload.vue' // 上传图片
import orderApi from '@/apis/order'
import TicketNumberRecord from '../update-ticket-number/ticket-number-record.vue'
export default {
  name: 'handle-ticket-number-dialog',
  components: { ImgUpload, TicketNumberRecord },
  data() {
    return {
      visible: false,
      OSS_DIR,
      form: {},
      order: {}, // 当前订单对象
      tipsBuyer: '请您再次确认实际背书票据号码，您同意票方变更申请后，平台订单票号将会变更，若签收票据号码与变更的背书票号不同产生的相关不利法律后果由您自行承担。',
      tipsSeller: '请您再次确认实际签收票据号码，您同意资方变更申请后，平台订单票号将会变更，若实际背书票号与变更的签收票号不同产生的相关不利法律后果由您自行承担。'
    }
  },
  computed: {
    ...mapGetters('draft-detail', {
      isBuy: 'isBuy', // 是否资方
      isSale: 'isSale', // 是否票方
      isNewDraft: 'isNewDraft',
      waitingSubmission: 'waitingSubmission', // 待签收
      updateTicketInfo: 'updateTicketInfo'
    }),
    // 处理状态1:处理，2:查看
    handleStatus() {
      return this.updateTicketInfo?.status === 0 ? 1 : 2
    },
    title() {
      return this.updateTicketInfo?.status === 0 ? '处理变更票号' : '查看变更票号'
    },
    // 背书/签收文案
    labelText() {
    // 票方-签收/资方-背书|签收
      return (this.isSale || this.waitingSubmission) ? '签收' : '背书'
    }
  },
  mounted() {
    // 新票重置提示文本
    if (this.isNewDraft) {
      // 新票重置提示文本
      this.isSale ? this.tipsSeller = '请您再次确认实际背书票据号码、子票区间，您同意资方变更申请后，平台订单票号、子票区间将会变更，若签收票据号码、子票区间与变更的背书票号、子票区间不同产生的相关不利法律后果由您自行承担。'
        : this.tipsBuyer = '请您再次确认实际背书票据号码、子票区间，您同意票方变更申请后，平台订单票号、子票区间将会变更，若签收票据号码、子票区间与变更的背书票号、子票区间不同产生的相关不利法律后果由您自行承担。'
    }
  },
  methods: {
    init() {
      this.visible = true
      const { draftImgUrl, draftNo, rejectReason } = this.updateTicketInfo
      this.form = {
        draftImgUrl,
        draftNo,
        rejectReason
      }
      // 新票设置子票区间
      if (this.isNewDraft) {
        Object.assign(this.form, { subTicketStart: this.updateTicketInfo.subTicketStart, subTicketEnd: this.updateTicketInfo.subTicketEnd })
      }
    },
    // 拒绝
    onReject(flag) {
      this.handleSubmit(flag)
    },
    // 确认同意
    onConfirm(flag) {
      this.$refs.formInstance.validate(valid => {
        if (!valid) return
        let ticketNumberText = `实际${this.labelText}票号：${this.form.draftNo || ''}`
        // 新票设置子票区间
        let subTicketText
        let tipsBuyerText = `请您再次确认实际${this.labelText}票据号码，您同意票方变更申请后，平台订单票号将会变更，若签收票据号码与变更的${this.labelText}票号不同产生的相关不利法律后果由您自行承担。`
        if (this.isNewDraft) {
          subTicketText = `子票区间：${this.form.subTicketStart}-${this.form.subTicketEnd}`
          tipsBuyerText = this.tipsBuyer
        }
        let tips = this.isBuy ? tipsBuyerText : this.tipsSeller
        const h = this.$createElement
        this.$msgbox({
          title: '提示',
          message: h('div', [h('p', { class: 'tips-color' }, tips), h('p', { class: 'text-font' }, ticketNumberText), h('p', { class: 'text-font' }, subTicketText)]),
          showCancelButton: true,
          customClass: 'custom-msg-box-cls',
          cancelButtonText: '取消',
        }).then(() => {
          // 处理提交
          this.handleSubmit(flag)
        })
          .catch(err => {
            // eslint-disable-next-line
            console.log(err)
          })
      })
    },
    // 处理变更票号提交
    async handleSubmit(flag) {
      const { orderNo } = this.order
      // 拒绝时需要校验备注
      if (flag === 4 && !this.form.rejectReason) {
        return this.$message.warning('请填写拒绝原因')
      }
      let params = {
        ...this.form,
        orderNo,
        changeOperateType: flag,
        id: this.updateTicketInfo?.id
      }
      try {
        await orderApi.updateTicketNumber(params)
        this.$message.success('提交成功')
        // 查询变更票号状态
        this.$store.dispatch('draft-detail/queryUpdateTicketInfo', { orderNo })
        this.handleClose()
      } catch (err) {
        // eslint-disable-next-line
        console.log(err?.data?.msg)
      }
    },
    // 关闭弹窗
    handleClose() {
      this.visible = false
      this.$emit('destroy')
    },
    // 查看上一次变更票号记录
    updateDraftNoRecord() {
      this.$refs.ticketNumberRecordRefs.init(this.order.orderNo)
    },
  }
}
</script>
