<!-- 申请加时 -->
<style lang="scss" scoped>
.apply-overtime {
  display: inline;
}
</style>

<template>
  <div class="apply-overtime order-operation">
    <slot name="button">
      <el-button
        v-waiting="orderApi.postSingleExecuteOrderTradeStep"
        v-bind="$attrs"
        :type="$attrs.type || 'primary'"
        :border="!$attrs.type"
        @click="init"
        v-on="$listeners"
      >
        <slot>申请加时</slot>
      </el-button>
    </slot>
  </div>
</template>

<script>
/* eslint-disable no-magic-numbers */

import { EXECUTE_TRANSACTION_PROCESS } from '@/constant'
import orderApi from '@/apis/order'
export default {
  name: 'apply-overtime',
  props: {
    order: {
      type: [Object, Array],
      required: true
    },
  },
  data() {
    return {
      orderApi,
      EXECUTE_TRANSACTION_PROCESS, // 所有操作场景
    }
  },

  methods: {
    init() {
      this.postApplyOvertime(this.order)
    },

    // 申请加时请求
    async postApplyOvertime(order) {
      try {
        const param = {
          orderNoList: [order.orderNo], // 订单编号 ,Long
          orderStepsEnumCode: EXECUTE_TRANSACTION_PROCESS.APPLY_OVERTIME, // 待支付, 买家申请加时
          // applyExtraTime: 5, // 加时5分钟 （目前加时由后端加，无需传参）
        }
        await orderApi.postSingleExecuteOrderTradeStep(param)
        this.$message({
          type: 'success',
          message: '申请成功！系统已加时5分钟'
        })

        this.$emit('success')
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log(error)
      }
    }
  }
}
</script>
