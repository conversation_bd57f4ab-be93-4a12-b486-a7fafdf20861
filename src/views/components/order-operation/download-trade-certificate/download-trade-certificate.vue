<template>
  <div
    v-if="
      order.tabStatus === DRAFT_STATUS.DEAL_COMPLETED.id &&
        (order.needVoucher === NEED_VOUCHER.BOTH.id ||
          (isSale && order.needVoucher === NEED_VOUCHER.SELLER.id) ||
          (isBuy && order.needVoucher === NEED_VOUCHER.BUYER.id)
        )"
    class="order-operation"
  >
    <el-button
      v-bind="$attrs"
      :type="$attrs.type || 'primary'"
      :width="$attrs.width || '104'"
      :height="$attrs.height || '40'"
      size="large"
      @click="download"
    >
      下载凭证
    </el-button>
  </div>
</template>

<script>
import { DRAFT_STATUS, NEED_VOUCHER, PAYMENT_CHANNEL_ACCOUNT_TYPE_MAP } from '@/constant'
import { DOWNLOAD_CERTIFICATE } from '@/event/modules/site' // 监听事件常量
import userApi from '@/apis/user'
import { openWindow } from '@/common/js/util'
import { jumpJdPay } from '@/utils/jdpay.js'
export default {
  name: 'download-trade-certificate',
  props: {
    // 是否为票方订单
    isSale: {
      type: Boolean
    },
    // 是否为买方订单
    isBuy: {
      type: Boolean
    },
    // 订单详情
    order: {
      type: [Object, Array],
      required: true
    }
  },
  data() {
    return {
      DRAFT_STATUS,
      NEED_VOUCHER
    }
  },
  methods: {
    download() {
      // 监听打开下载交易凭证事件 通知筛选组件缓存数据
      if (this.$ipc) {
        this.$event.emit(DOWNLOAD_CERTIFICATE)
      }
      if (this.$store.state.user.isSdkLink) {
        let paymentChannel = this.order?.paymentChannel
        const accountType = PAYMENT_CHANNEL_ACCOUNT_TYPE_MAP[paymentChannel]
        // 通过SDK跳转
        let querys = {
          accountType
        }
        jumpJdPay({ querys })
        return
      }
      openWindow(async() => {
        const res = await userApi.getPaymentAccountUserCenterUrl('enterInto', 'YILLION')
        return res
      })
    }
  },
}
</script>
