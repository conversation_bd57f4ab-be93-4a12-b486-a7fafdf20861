<!-- 对方信用确认弹窗 -->
<style lang="scss" scoped>
.confirm-box {
  margin-top: 8px;
  padding: 16px;
  background-color: $color-FFFFFF;
}

.confirm {
  font-size: 14px;
  color: $color-text-primary;
}

.confirm-content {
  padding: 16px;
  background: $color-F8F8F8;
}

.flex {
  margin-top: 10px;

  @include flex-vc;

  .left,
  .right {
    flex: 1;
  }

  .left {
    border-right: 1px solid $color-D9D9D9;
  }

  .right {
    padding-left: 24px;
  }

  .label {
    margin-bottom: 2px;
    font-size: 14px;
    color: $color-text-secondary;
    line-height: 20px;
  }

  .value {
    font-weight: bold;
  }
}

.dialog-footer {
  justify-content: space-between;
}

.green-bottom-line {
  font-size: 14px;
  color: $font-color;

  @include example-underline;
}

.green-bottom-line-agreement {
  @extend .green-bottom-line;

  &::after {
    left: 50%;
    width: 86%;
    transform: translateX(-50%);
  }
}

.agree-protocol {
  display: flex;
  align-items: center;

  ::v-deep {
    .agree-text {
      display: flex;

      .el-checkbox__label {
        font-size: 14px;
        color: $color-text-primary;
      }
    }
  }
}

.item {
  margin-top: 4px;
  font-size: 14px;
  color: $color-text-primary;
}

// 红色高亮
.red-light-high {
  font-weight: 600;
  color: $color-warning;
}

.warn-tip {
  margin-bottom: 12px;
}
</style>

<template>
  <el-dialog
    title="确认订单"
    :visible.sync="dialogVisible"
    width="530px"
    :close-on-click-modal="false"
    class="defects-notify-dialog whead-gbody-dialog"
    append-to-body
  >
    <div>
      <RedWarnContent class-type="green" class="warn-tip">
        您的对方需要票号不一致的补充协议，请确认。
      </RedWarnContent>

      <div v-if="orderData">
        <template v-if="!orderData.totalPrice">
          <div class="confirm">
            {{ `${!orderData.agentOrder ? '对方已接单，' : ''}请您尽快确认订单！` }}
          </div>
        </template>

        <template v-else>
          <div class="confirm">对方已接单，请您尽快确认订单！</div>
          <div class="confirm-box">
            <div>请确认选中的订单信息已核对无误。</div>
            <div class="item">
              <span>共 </span>
              <span class="red-light-high">{{ orderData.length }}</span>
              <span> 张，票面金额总额：</span>
              <span class="red-light-high">{{ `${yuan2wan(orderData.totalPrice)}万` }}</span>
            </div>
            <div v-if="orderData.totalSplit" class="confirm confirm-content">
              <div>含 <span class="red-light-high">{{ orderData.totalSplit }}</span> 张新票拆分订单<span v-if="orderData.totalSplit === 1">，拆分接单金额：<span class="red-light-high">{{ `${yuan2wan(orderData.splitAmount)}万元` }}</span></span></div>
              <div style="margin-top: 2px;">确认拆分订单，则剩余金额需在该笔订单完成后继续交易</div>
            </div>
            <div class="item">批量确认后，订单将进入资方待支付环节。</div>
          </div>
        </template>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <div class="agree-protocol">
        <el-checkbox v-model="agree" class="agree-text">我已阅读并同意</el-checkbox>
        <a
          class="green-bottom-line-agreement"
          target="_blank"
          :href="OSS_FILES_URL.ASSET_TRANSFER_AUTHORIZATION"
          rel="noopener noreferrer"
        >
          《资产转让补充授权书》
        </a>
      </div>
      <div>
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="confirm"
        >
          确定
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import {
  CREDIT_LEVEL_ICON_MAP, // 等级id映射图标
} from '@/constants/credit'
import RedWarnContent from '@/views/components/common/warn-content.vue'
import { yuan2wan } from '@/common/js/number'
import { OSS_FILES_URL } from '@/constants/oss-files-url' // 平台违约规则url
export default {
  name: '',

  components: {
    RedWarnContent,
  },

  data() {
    return {
      OSS_FILES_URL, // 资产转让补充授权书
      dialogVisible: false, // 显示弹窗
      orderData: null, // 订单数据
      CREDIT_LEVEL_ICON_MAP, // 等级id映射图标
      agree: true, // 同意协议
    }
  },

  methods: {
    yuan2wan,
    init(orderData) {
      if (!orderData) {
        this.$message.error('没有订单信息')
        return
      }
      this.orderData = orderData
      this.dialogVisible = true
    },

    // 点击确认
    confirm() {
      if (!this.agree) {
        this.$message.warning('请阅读并同意服务协议')
        return
      }
      this.dialogVisible = false
      if (!this.orderData.totalPrice) {
        this.$emit('confirm', true)
      } else {
        this.$emit('confirm')
      }
    },

    // 关闭弹窗
    handleClose() {
      this.$emit('close')
      this.dialogVisible = false
    },
  }
}
</script>
