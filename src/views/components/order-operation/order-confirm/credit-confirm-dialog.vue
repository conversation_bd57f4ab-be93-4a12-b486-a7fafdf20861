<!-- 单个-确定订单弹窗 -->
<style lang="scss" scoped>
::v-deep {
  .el-dialog .el-dialog__body {
    padding: 10px 20px 0;
  }
}

.confirm-box {
  margin-top: 8px;
  padding: 16px;
  background-color: $color-F8F8F8;
}

.confirm {
  margin-top: 8px;
  padding: 16px;
  font-size: 14px;
  background: $color-FFFFFF;
}

.title-small {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  padding-left: 12px;
  font-size: 16px;
  font-weight: bold;
  line-height: 22px;

  &::before {
    position: absolute;
    top: 50%;
    left: 0;
    width: 2px;
    height: 16px;
    background: $--color-primary;
    transform: translateY(-50%);
    content: "";
  }
}

.credit-level {
  display: flex;
  align-items: center;
  height: 20px;
  font-weight: 600;
  color: $color-text-primary;
  line-height: 20px;

  .text-gray {
    font-weight: 400;
    color: $color-text-secondary;
  }
}

.credit-block {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;

  .block-item {
    padding: 12px;
    width: 210px;
    color: $color-text-secondary;
    background: $color-F8F8F8;

    .text-bold {
      margin-left: 8px;

      @include font(16px, $--color-primary, 600);
    }
  }
}

.dialog-footer {
  padding: 12px 0;
  text-align: right;
}

.primary-text {
  color: #EC3535;
}

.sellerBankAccount-cls {
  padding-top: 15px;

  .form-item-block {
    display: flex;
    align-items: center;

    ::v-deep .label {
      margin-right: 10px !important;
    }

    ::v-deep .value {
      width: 300px;
    }
  }
}

.item-position {
  position: relative;

  ::v-deep .el-form-item__label {
    padding: 0;
  }

  .value {
    .el-tooltip {
      position: absolute;
      top: 10px;
      right: 28px;
    }
  }

  .max-width {
    width: 100%;
  }
}

.activity-box {
  padding-bottom: 12px;
  text-align: center;

  img {
    margin-bottom: 8px;
  }

  .red-font {
    cursor: pointer;
    border-bottom: 1px solid #EC3535;
    font-size: 16px;
    font-weight: 500;
    color: #EC3535;
  }
}

.seller-select-btn {
  font-size: 16px;
  text-align: center;
  color: $--color-primary;
  line-height: 40px;
  cursor: pointer;
}

.seller-bank-link {
  border-bottom: 1px solid $--color-primary;
  color: $--color-primary;
  cursor: pointer;
}
</style>

<template>
  <el-dialog
    title="确认订单"
    :visible.sync="dialogVisible"
    width="500px"
    :close-on-click-modal="false"
    append-to-body
  >
    <div v-if="orderData">
      <RedWarnContent class-type="red" class="warn-tip">
        {{ `${!orderData.agentOrder ? '对方已接单，' : ''}请您尽快确认订单！` }}
      </RedWarnContent>
      <div v-if="orderData.parentOrderNo" class="confirm">
        <div>该笔为新票拆分订单，拆分接单金额<span class="primary-text">{{ `${yuan2wan(orderData.draftAmount)}万元` }}</span></div>
        <div style="margin-top: 2px;">确认拆分订单，则剩余金额需在该笔订单完成后继续交易</div>
      </div>
      <div class="confirm">
        <div v-if="isSale && false" class="activity-box">
          <img src="https://oss.chengjie.red/web/imgs/public/activity.png" alt="">
          <div><span class="red-font" @click="goCerter">卖方交易笔笔有现金奖励!</span></div>
        </div>
        <div class="title-small">
          对方综合信息
        </div>
        <div class="credit-level">
          <span class="text-gray">信用分等级：</span>
          <span>信用{{ CREDIT_LEVEL_NAME_MAP[orderData.oppositeCreditDTO.currentCreditType] }}</span>
          <span class="text-gray">&nbsp;(超过 </span>{{ orderData.oppositeCreditDTO.userProportion }}%<span class="text-gray">的用户)</span>
          <!--
            <el-divider direction="vertical" />
            <span class="text-gray">会员等级：</span>{{ orderData.oppositeMemberInfoDTO && MEMBER_LEVEL_VALUE_MAP[orderData.oppositeMemberInfoDTO.gradeCode] }}
          -->
        </div>
        <div class="credit-block">
          <div class="block-item">
            打款率 <span class="text-bold">{{ orderData.oppositeStatCorpTradeRecordDTO.payRate }}%</span>
          </div>
          <div class="block-item">
            签收率 <span class="text-bold">{{ orderData.oppositeStatCorpTradeRecordDTO.signRate }}%</span>
          </div>
        </div>
        <div v-if="[PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id, PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id, PAYMENT_CHANNEL.YL_PLUS.id].includes(orderData.paymentChannel)" class="sellerBankAccount-cls">
          <div class="txt">根据平台规则，您可修改回款账户为任意已绑定账户</div>
          <FormItem
            label="回款账户"
            class="form-item-block item-position"
            :required="false"
          >
            <el-tooltip
              placement="top"
              popper-class="issue-draft-tooltip"
            >
              <template slot="content">
                <div>依照智付E+、E++渠道交易模式，票方发布票据必须选择回款账户(支持任意银行一般户)。需要在<span class="seller-bank-link" @click="() => { $router.push('/user-center/bank-account?tabStatus=2');dialogVisible = false }">银行回款账户</span>页面绑定回款账户，交易完成后票款自动提现到账。</div>
              </template>
              <icon class="icon icon-question" type="chengjie-wenti" />
            </el-tooltip>

            <div class="pay-type-item">
              <el-select
                ref="sellerBankAccount"
                v-model="sellerBankAccount"
                class="max-width"
                :height="40"
                placeholder="请选择回款账户"
              >
                <el-option
                  v-for="item in sellerBankAccountList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
                <div class="seller-select-btn" @click="() => { $router.push('/user-center/bank-account?tabStatus=2'); $refs.sellerBankAccount.blur(); dialogVisible = false }"><i class="el-icon-plus" />添加回款账户</div>
              </el-select>
            </div>
          </FormItem>
        </div>
      </div>
    </div>
    <div class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button
        type="primary"
        @click="confirm"
      >
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  CREDIT_LEVEL_NAME_MAP,
} from '@/constants/credit'
import RedWarnContent from '@/views/components/common/warn-content.vue'
import { yuan2wan } from '@/common/js/number'
import { MEMBER_LEVEL_VALUE_MAP, PAYMENT_CHANNEL } from '@/constant' // 会员等级 id 映射 名称
import { showConfirmationDialog } from '../draftNo-Check'// 智付E+渠道订单确认弹窗
import FormItem from '@/recognize/components/issue-draft/components/form-item.vue'
import { mapGetters } from 'vuex'
import { tracking } from '@/utils/util'
import { formatTime } from '@/common/js/date'
import userApi from '@/apis/user' // 用户接口
export default {
  name: '',

  components: {
    RedWarnContent,
    FormItem
  },
  props: {
    isSale: Boolean
  },

  data() {
    return {
      PAYMENT_CHANNEL,
      CREDIT_LEVEL_NAME_MAP,
      MEMBER_LEVEL_VALUE_MAP,
      dialogVisible: false, // 显示弹窗
      orderData: null, // 订单数据
      sellerBankAccount: '', // 回款账户
    }
  },

  computed: {
    ...mapGetters('user', {
      sellerBankAccountList: 'sellerBankAccountList', // 回款账户列表
      signBankAccountList: 'signBankAccountList', // 签收账户列表
      userInfo: 'userInfo'
    }),
    // 当前订单回款账户id是否存在回款账户列表中
    isSeller() {
      return this.sellerBankAccountList.some(e => e.id === Number(this.orderData.sellerBankAccountId))
    },
    // 当前回款账户未添加到“银行回款账户”、未添加到“银行签收账户”
    isSign() {
      return this.signBankAccountList.some(e => e.id === Number(this.orderData.sellerBankAccountId))
    }
  },

  methods: {
    showConfirmationDialog,
    yuan2wan,
    init(orderData) {
      if (!orderData) {
        this.$message.error('没有订单信息')
        return
      }
      this.orderData = orderData
      this.sellerBankAccount = this.isSeller ? Number(orderData.sellerBankAccountId) : null
      this.dialogVisible = true
      // 只有智付E+ 渠道才强制提醒  ZHI_FU_YI_LIAN_PLUS
      if (orderData.paymentChannel === PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id) {
        this.showConfirmationDialog('背出', { type: '票方单张确认', info: orderData })
      }
    },

    // 点击确认判断
    confirm() {
      // if (!this.sellerBankAccount) return this.$message.warning('请选择回款账户')

      // 当前回款账户未添加到“银行回款账户”、未添加到“银行签收账户”
      if (!this.isSeller && !this.isSign) {
        return this.$message.info('请在银行账户内先添加该回款账户')
      }
      // 当前回款账户未添加到银行回款账户
      if (!this.isSeller) {
        this.$confirm('是否将订单银行账户同步至回款账户', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async() => {
          // 同步至回款账户列表
          await userApi.setBankCardSync({ bankCardId: Number(this.orderData.sellerBankAccountId), toAccountType: 2 })
          // 刷新store的账户列表
          this.$store.dispatch('user/getPassedBankCardList')
          this.handlerConfirm()
        })
      } else {
        this.handlerConfirm()
      }
    },

    // 确定订单操作
    handlerConfirm() {
      const { corpName } = this.userInfo
      const time = formatTime(Date.now(), 'YYYY-MM-DD hh:mm:ss')
      tracking({
        userAction: `${corpName}在${time}点击了单笔确认订单操作`,
        orderNos: this.orderData.orderNo
      })
      this.$emit('confirm', true)
    },

    // 关闭弹窗
    handleClose() {
      this.$emit('close')
      this.dialogVisible = false
    },
    // 跳转至活动中心
    goCerter() {
      this.$router.push('/user-center/coupon?tabs=receiveCentre')
      this.dialogVisible = false
    }
  }
}
</script>
