<!-- 订单确认按钮所有用到的弹窗 -->
<style lang="scss" scoped>
// 红色高亮
.red-light-high {
  margin: 0 4px;
  font-weight: 600;
  color: $color-warning;
}
</style>

<template>
  <div>
    <!-- 批量操作loading -->
    <LoadingDialog :visible="batchLoading" title="批量确认中" content="正在批量确认中，请耐心等待..." />

    <!-- 批量确认结果 -->
    <ResultDialog ref="resultDialogRef" handle-str="确认" @close="closeDialog" />

    <!-- 对方信用信息确认 -->
    <CreditDialog
      ref="creditDialog"
      :is-sale="isSale"
      @close="handleClose"
      @confirm="handleCreditConfirm"
    />

    <!-- 确认订单弹窗 -->
    <OrderConfirmDialog ref="orderConfirmDialog" @close="handleClose" @confirm="orderConfirmCallBack" />

    <!-- 批量确认订单弹窗 -->
    <BatchConfirmDialog
      ref="batchConfirmDialog"
      :is-sale="isSale"
      @close="handleClose"
      @confirm="postOrderConfirm"
    />

    <!-- 批量确认定向票订单弹窗 -->
    <AgentOrderConfirmDialog
      ref="agentOrderConfirmDialogRef"
      :order="Array.isArray(order) ? order : [order]"
      @success="closeDialog"
      @close="handleClose"
    />
    <!-- 自动订单-资方要求弹窗 -->
    <radarOrderBuyerNeedDialog
      ref="radarOrderBuyerNeedDialogRef"
      :order-info="orderData"
      @confirm="handleConfirmOrder"
      @success="closeDialog"
      @close="handleClose"
    />
  </div>
</template>

<script>
import ResultDialog from '@/views/components/common/result-dialog/result-dialog.vue' // 批量操作结果组件
import CreditDialog from './credit-dialog.vue' // 对方信用信息确认
import OrderConfirmDialog from './order-confirm-dialog.vue' // 确认订单弹窗
import BatchConfirmDialog from './batch-confirm-dialog.vue' // 批量确认订单弹窗
import AgentOrderConfirmDialog from './agent-order-confirm-dialog.vue' // 批量确认定向票订单弹窗
import userObj from '@/utils/user.js' // 用户对象操作
import BigNumber from 'bignumber.js'
import creditApi from '@/apis/credit'
import userApi from '@/apis/user'
import orderApi from '@/apis/order'
import settingApi from '@/apis/setting.js'
import { mapGetters } from 'vuex'
import OrderStatusMixin from '../order-status-mixin'
import { EXECUTE_TRANSACTION_PROCESS, PAYMENT_CHANNEL } from '@/constant'
import LoadingDialog from '@/views/components/common/loading-dialog/loading-dialog.vue' // 加载中组件
import radarOrderBuyerNeedDialog from './radar-order-buyer-need-dialog.vue' // 自动订单-资方要求弹窗
import { confirmOrderTips, enjambmentConfirmOrderTips } from '@/utils/limit-order-taking.js'
import { yuan2wan } from '@/common/js/number'
export default {
  name: 'order-confirm-dialog-list',

  components: {
    ResultDialog,
    CreditDialog,
    OrderConfirmDialog,
    AgentOrderConfirmDialog,
    LoadingDialog,
    radarOrderBuyerNeedDialog,
    BatchConfirmDialog,
  },

  mixins: [OrderStatusMixin],

  data() {
    return {
      batchLoading: false,
      PAYMENT_CHANNEL
    }
  },

  computed: {
    ...mapGetters('user', {
      isOpenAccount: 'isOpenAccount', // 米账号信息
    }),
    limitOrderInfo() { // E+限制接单参数
      return this.$store.state.common.limitOrderInfo
    },

    // 是否批量
    hasAllOrder() {
      return this.order instanceof Array
    },

    // 订单号列表
    orderNoList() {
      return this.hasAllOrder ? this.order.map(item => item.orderNo) : [this.order.orderNo]
    },

    // 订单号，数组时取第一条数据，用于loading时，不要所有按钮都显示loading状态
    firstOrderNo() {
      return this.orderData?.orderNo
    },

    // 对方企业id
    corpId() {
      return this.isAgentOrder ? this.orderData?.sellerCorpId : this.orderData?.buyerCorpId
    }
  },

  methods: {

    // 初始化
    init() {
      if (this.order && Array.isArray(this.order) && this.order.length > 99) {
        this.$message.warning(`每次批量操作最多可选择 ${99} 笔订单`)
        return
      }

      // 批量操作
      if (this.hasAllOrder) {
        this.handleBatchConfirmOrder()
      } else if (this.isRadarType) {
        // 自动订单，显示对方的接单规则
        this.$refs.radarOrderBuyerNeedDialogRef && this.$refs.radarOrderBuyerNeedDialogRef.init(this.orderData?.orderNo)
      } else {
        this.handleConfirmOrder()
      }
    },

    // 批量确认订单
    handleBatchConfirmOrder() {
      if (this.waitingConfirm && this.isAgentOrder) {
        if (!this.isOpenAccount) {
          userObj.checkAmountFailType()
        } else {
          // 定向票批量确认订单弹窗
          this.$nextTick().then(() => {
            this.$refs.agentOrderConfirmDialogRef && this.$refs.agentOrderConfirmDialogRef.init(this.orderNoList)
          })
        }
      } else {
        // 批量确认订单弹窗
        this.confirmDialog()
      }
    },
    // 将时分转为时间戳
    timeToSec(time) {
      if (time !== null) {
        let s = ''
        let hour = time.split(':')[0]
        let min = time.split(':')[1]
        const num = 3600
        s = Number(hour * num) + Number(min * 60)
        return s
      }
    },

    // 单个确认订单
    async handleConfirmOrder() {
      // 获取相关信用信息
      await this.handleGetCreditInfo()
      if (this.orderData.showProxy) {
        // 平台卖，经纪商买
        this.$refs.orderConfirmDialog.init(this.orderData)
      } else if (this.isAgentOrder) {
        this.$emit('receiving-order', this.order.orderNo)
      } else {
        // 票方接单确认提示
        const limitOrders = JSON.parse(JSON.stringify(this.limitOrderInfo))
        const params = Object.assign({}, limitOrders, {
          paymentChannel: this.order.paymentChannel, // 渠道
          draftAmountWan: yuan2wan(this.order.draftPaymentAmount), // 实付金额
          generalAccount: this.order.generalAccount, // 是否亿联一般户
          generalAccountZb: this.order.generalAccountZb, // 是否众邦一般户
          hasAllOrder: this.hasAllOrder // 是否批量
        })

        // 跨行转账限额
        await enjambmentConfirmOrderTips(params, this)
        // 延迟到账确认提示
        await confirmOrderTips(params, this)
        this.$refs.creditDialog.init(this.orderData)
      }
    },

    // 获取相关信用信息
    async handleGetCreditInfo() {
      // 列表数据没有返回信用信息
      if (!this.orderData?.oppositeCreditDTO) {
        this.orderData.oppositeCreditDTO = await creditApi.getByCorpId({ corpId: this.corpId })
      }
      // 列表数据没有返回统计信息
      if (!this.orderData?.oppositeStatCorpTradeRecordDTO) {
        this.orderData.oppositeStatCorpTradeRecordDTO = await userApi.getCorpStatDataByCorpId({ corpId: this.corpId })
      }
    },

    // 关闭弹窗
    handleClose() {
      this.clearData()
      this.$emit('close')
      this.$emit('destroy')
    },

    // 批量操作结果弹窗关闭回调
    closeDialog() {
      this.clearData()
      this.$emit('success')
      this.$emit('destroy')
    },

    // 对方信用弹窗确认回调 --sellerBankAccount确认订单时选择的回款账户
    handleCreditConfirm(sellerBankAccount, func) {
      if (this.waitingConfirm && this.isAgentOrder) {
        if (!this.isOpenAccount) {
          userObj.checkAmountFailType()
        } else {
          this.$emit('receiving-order', this.order.orderNo)
        }
      } else {
        this.postOrderConfirm(sellerBankAccount, func)
      }
    },

    // 确认订单请求
    async postOrderConfirm(sellerBankAccount, func) {
      try {
        const param = {
          isShowBatchError: false, // 是否统一处理批量操作结果
          orderNoList: this.orderNoList, // 订单编号 ,Long
          orderStepsEnumCode: EXECUTE_TRANSACTION_PROCESS.CONFIRM_ORDER,
          sellerBankAccountId: sellerBankAccount // 回款账户id
        }
        if (this.hasAllOrder) {
          this.batchLoading = true
          const data = await orderApi.postExecuteOrderTradeStep(param)
          this.batchLoading = false
          // 批量操作结果提示
          this.$refs.resultDialogRef.init(data)
          // 批量确认判断是否存在渠道是E+并且回款账户id为null的订单 用于更新回款账户
          const { order } = this.$refs.batchConfirmDialog.order
          let arr = order.filter(item => !item.sellerBankAccountId && item.paymentChannel === PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id)
          if (arr.length) {
            await settingApi.updateSellerBankAccount({ orderNo: arr.map(e => e.orderNo), sellerBankAccountId: sellerBankAccount })
          }
        } else {
          await orderApi.postSingleExecuteOrderTradeStep(param)
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          // 判断是否存在渠道是E+并且回款账户id为null的订单 用于更新回款账户
          if (!this.orderData.sellerBankAccountId && this.orderData.paymentChannel === PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id) {
            await settingApi.updateSellerBankAccount({ orderNo: [this.orderData.orderNo], sellerBankAccountId: sellerBankAccount })
          }
          if (func) {
            func()
          }
          this.closeDialog()
        }
        // 更新回款账户选中
        this.$store.dispatch('common/getNewVersionDraftConfig')
      } catch (error) {
        this.batchLoading = false
        // eslint-disable-next-line no-console
        // 票方带保确认 米不足 不关闭弹窗
        // 单个的操作失败需要销毁组件
        if (!this.hasAllOrder && !func) {
          this.handleClose()
        }
      }
    },

    // 确认弹窗
    async confirmDialog() {
      // 是否有符合 平台卖，经纪商买 的订单
      const isShowProxy = this.order.some(item => item.showProxy)
      if (isShowProxy) {
        let totalPrice = 0
        let totalSplit = 0 // 拆分数量
        let splitAmount = 0 // 拆分金额
        for (let item of this.order) {
          totalPrice = new BigNumber(item.draftAmount).plus(totalPrice)
          if (item.parentOrderNo) {
            // 有拆分
            totalSplit++
            splitAmount = item.draftAmount
          }
        }
        this.$refs.orderConfirmDialog.init({ totalPrice, length: this.order.length, totalSplit, splitAmount })
      } else {
        // 票方批量接单确认提示
        const list = this.order.map(e => Object.assign({ paymentChannel: e.paymentChannel, draftAmountWan: yuan2wan(e.draftPaymentAmount), generalAccount: e.generalAccount, generalAccountZb: e.generalAccountZb }))
        const limitOrders = JSON.parse(JSON.stringify(this.limitOrderInfo))
        const params = Object.assign({}, limitOrders, { list, hasAllOrder: this.hasAllOrder })

        // 跨行转账限额
        await enjambmentConfirmOrderTips(params, this)
        // 延迟到账确认提示
        await confirmOrderTips(params, this)
        this.showConfirmDialog()
      }
    },

    // 确认弹窗点击确认回调
    orderConfirmCallBack(type) {
      if (type) {
        this.handleCreditConfirm()
      } else {
        this.postOrderConfirm()
      }
    },

    // 显示批量确认弹窗
    showConfirmDialog() {
      this.$refs.batchConfirmDialog.init(this.order)
    },

    // 清除数据
    clearData() {
      this.batchLoading = false
    },
  },

}
</script>
