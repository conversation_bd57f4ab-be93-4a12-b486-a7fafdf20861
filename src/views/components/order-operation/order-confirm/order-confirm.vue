<!-- 确认 -->
<style lang="scss" scoped>
.order-confirm {
  display: inline;

  .el-button {
    position: relative;

    &.has-tag::before {
      content: "";
      position: absolute;
      top: -1px;
      left: -1px;
      border-top: 27px solid $color-assist3;
      border-right: 28px solid transparent;
      width: 0;
      height: 0;
    }

    .rice-tag {
      position: absolute;
      top: 0;
      left: 0;
      font-size: 12px;
      font-weight: 600;
      line-height: 20px;
      transform: scale(.8);
      transform-origin: top;
      color: $color-FFFFFF;
    }
  }
}
</style>

<template>
  <el-tooltip
    placement="top"
    popper-style="max-width: 210px;"
    :content="toolTipText"
    :disabled="!toolTipText"
  >
    <div class="order-confirm order-operation">
      <slot name="button">
        <el-button
          v-waiting="[`post::/draft/order/executeOrderTradeStep?orderNo=${firstOrderNo}`,
                      `post::/draft/order/singleExecuteOrderTradeStep?orderNo=${firstOrderNo}`]"
          v-bind="$attrs"
          :type="$attrs.type || 'primary'"
          :width="$attrs.width || '68'"
          :height="$attrs.height || '40'"
          :class="hasTag && 'has-tag'"
          @click="init"
          v-on="$listeners"
        >
          <span v-if="hasTag" class="rice-tag">{{ sdmUnit }}</span>
          <slot>确认</slot>
        </el-button>
      </slot>
    </div>
  </el-tooltip>
</template>

<script>
import orderOperationMixin from '../order-operation-mixin'
import OrderConfirmDialogList from './order-confirm-dialog-list.vue' // 订单确认按钮所用到的弹窗组件

export default {
  name: 'order-confirm',

  mixins: [orderOperationMixin(OrderConfirmDialogList)],

  props: {
    order: {
      type: [Object, Array],
      required: true
    },
    hasTag: [String, Number, Boolean], // 是否显示保证金米字标识
    toolTipText: String, // hover文案
  },

  computed: {
    // 订单号，数组时取第一条数据，用于loading时，不要所有按钮都显示loading状态
    firstOrderNo() {
      return this.orderData?.orderNo
    },
  }
}
</script>
