<!-- 批量确认定向票订单弹窗 -->
<style lang="scss" scoped>
.agent-order-confirm-dialog {
  // 内容区
  .body {
    padding: 0;
    font-size: 14px;
    line-height: 22px;
  }

  // 底部
  .dialog-footer {
    .right {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }

    .el-button {
      padding: 11px 23px;
    }
  }
}

.g-title {
  padding-left: 8px;
  font-size: 16px;
  line-height: 23px;

  &::before {
    width: 4px;
    height: 16px;
  }
}

.sdm-info-card {
  padding: 15px 12px;
  background-color: #FFFFFF;
}

.items {
  display: flex;
  flex-wrap: wrap;

  .item:first-child {
    margin-right: 12px;
    border-right: 1px solid $color-D9D9D9;
  }
}

.item {
  margin-top: 10px;
  width: 46%;
}

.item-label {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
  font-size: 14px;
  color: $color-text-secondary;
}

.item-content {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.rechargeBtn {
  @include example-underline;

  margin-left: 6px;
  font-size: 14px;
}

// el组件样式
::v-deep {
  .el-form {
    padding: 15px 12px;
    background-color: #FFFFFF;

    .el-form-item {
      margin-bottom: 0;

      &.el-form-item {
        margin-top: 14px;
      }

      .el-form-item__label {
        line-height: 22px;
        margin-bottom: 2px;
        padding-bottom: 0;
        width: 100%;
      }

      &:last-child {
        margin-bottom: 4px;
      }
    }

    .account-select {
      .el-input--small .el-input__inner {
        height: 36px;
        line-height: 36px;
      }
    }
  }
}

.mt12 {
  margin-top: 12px;
}

.g-require {
  color: #666666;

  &::before {
    margin-right: 4px;
    color: #F51818;
    content: "*";
  }
}

.link-txt {
  color: $--color-primary;
  cursor: pointer;
}
</style>

<template>
  <div>
    <el-dialog
      class="agent-order-confirm-dialog"
      title="批量确认订单"
      :visible.sync="dialogVisible"
      width="642px"
      height="426px"
      :before-close="handleClose"
      :close-on-click-modal="false"
      append-to-body
      center
      :lock-scroll="true"
    >
      <div class="body">
        <h3 class="text-primary">请仔细核对选中订单的票面信息，订单确认后将进入支付环节</h3>
        <p class="mt12">选择下方的支付方式,选中订单将使用该支付渠道的账户完成付款</p>
        <p>选择下方的签收账户,选中订单的票方会将票据背书至此账户</p>

        <el-form
          ref="form"
          v-loading="loading"
          class="mt12"
          :model="form"
          :rules="rules"
          label-position="top"
        >
          <p class="g-title">支付选项</p>
          <el-form-item label="选择支付渠道" prop="paymentChannel">
            <PayChannelRadio
              ref="payChannelRadio"
              :draft-info="{...draftInfo, traderAccountVOList: batchOrderInfo.traderAccountVOList, orderSupportPayChannel: batchOrderInfo.orderSupportPayChannel, orderList: order}"
              has-all-order
              :select-data="{ payChannel: form.paymentChannel}"
              @change="onChangePayChannel"
            />
          </el-form-item>

          <el-form-item prop="buyerTraderCorpBankCardId">
            <span class="g-require">选择签收账户</span>
            <el-tooltip
              placement="top-start"
              :disabled="!dialogVisible"
            >
              <template slot="content">
                您也可以前往<span
                  class="link-txt"
                  @click="() => {
                    $router.replace('/user-center/bank-account?tabStatus=1');
                    handleClose() ;
                  }"
                >银行账户</span>页面维护账户是否支持签收新一代票据
              </template>
              <icon class="icon-question" size="18" type="chengjie-wenti" />
            </el-tooltip>
            <AccountListSelect
              ref="accountListSelect"
              :draft-info="{...draftInfo, traderCorpBankCardVOList: batchOrderInfo.traderCorpBankCardVOList}"
              has-all-order
              :disabled="!form.paymentChannel"
              :select-data="{ accountSelect: form.buyerTraderCorpBankCardId, payChannel: form.paymentChannel}"
              @change="onChangeAccount"
            />
          </el-form-item>
        </el-form>

        <div class="sdm-info-card mt12">
          <div class="g-title">其他选项</div>
          <div class="items">
            <div class="item">
              <div class="item-label">
                服务费
                <el-tooltip
                  placement="top-start"
                  popper-class="receive-draft-tooltip"
                >
                  <template slot="content">
                    <p>服务费使用{{ sdmName }}收取，收费标准为：</p>
                    <p>{{ $store.state.common.yinServiceRule }}</p>
                    <p>{{ $store.state.common.caiServiceRule }}</p>
                    <p>{{ $store.state.common.shangServiceRule }}</p>
                  </template>
                  <icon class="icon icon-question" type="chengjie-wenti" />
                </el-tooltip>
              </div>
              <div class="item-content">
                {{ batchOrderInfo.totalPlatformServiceFee }}{{ sdmUnit }}（服务费合计金额）
              </div>
            </div>
            <div class="item">
              <div class="item-label">
                {{ sdmName }}可用余额
                <!-- <span class="tag-fan">返100%</span> -->
              </div>
              <div class="item-content">
                <span>{{ (sdmInfo || {}).balanceAmt || '0.00' }}{{ sdmUnit }}</span>
                <span class="rechargeBtn" @click="recharge">充值</span>
              </div>
            </div>
            <!-- 批量服务费付费方式选择 -->
            <div v-if="isCouponPay" class="item">
              <BatchPayWays :order-list="order" @selected-success="onPaySuccess" />
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <div class="right">
          <el-button @click="handleClose">取消</el-button>
          <el-button
            v-waiting="marketApi.postBatchAcceptAgentOrder"
            type="primary"
            :disabled="!isCanConfirm || fastTradeOrderDisabeld"
            @click="buyerSelfOwnedCheck"
          >
            批量确认接单
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 批量操作loading -->
    <LoadingDialog :visible="batchLoading" title="批量确认中" content="正在批量确认中，请耐心等待..." />
    <!-- 批量确认结果 -->
    <ResultDialog
      ref="resultDialogRef"
      handle-str="确认"
      error-message-key="orderTradeDTOList"
      @close="closeResultDialog"
    />
    <!-- 单个接单/批量接单的订单价格/类型变化确认接单弹窗 -->
    <OrderChangedDialog ref="orderChangedDialogRef" @close="closeResultDialog()" @success="handleConfirm()" />
    <!-- 充值 -->
    <Recharge ref="recharge" />
    <!-- 新一代票据提示弹窗 -->
    <NewVersionDraftTipDialog
      ref="refNewVersionDraftTipDialog"
      @success="newVersionTipSuccess"
    />
    <BuyerSelfOwnerDialog ref="buyerSelfOwnerDialogRef" @success="handleConfirm()" />
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import marketApi from '@/apis/market'
import ResultDialog from '@/views/components/common/result-dialog/result-dialog.vue' // 批量操作结果组件
import PayChannelRadio from '@/views/pages/market/components/receive-order-detail/components/pay-channel-radio.vue' // 支付渠道选择
import AccountListSelect from '@/views/pages/market/components/receive-order-detail/components/account-list-select.vue' // 签收账户(银行卡)选择
import OrderChangedDialog from '@/views/pages/market/components/receive-order-detail/components/order-changed-dialog.vue' // 单个接单/批量接单的订单价格/类型变化确认接单弹窗
import LoadingDialog from '@/views/components/common/loading-dialog/loading-dialog.vue' // 加载中组件
import { isNotVoid } from '@/common/js/util'
import user from '@/utils/user'
import { BAN_STATUS } from '@/constants/open-account'// 常量
import Recharge from '@/views/components/user-center/recharge/recharge.vue' // 充值
import { ISSUE_DRAFT_ERROR_CODE } from '@/constants/draft'
import BatchPayWays from '@/views/pages/market/components/batch-pay-ways/batch-pay-ways.vue'
import batchPayWaysMixins from '@/views/pages/market/components/batch-pay-ways/batch-pay-ways-mixins'
import NewVersionDraftTipDialog from '@/views/pages/market/components/receive-order-detail/new-version-draft-dialog/new-version-draft-tip-dialog.vue' // 新一代票据提示弹窗
import BuyerSelfOwnerDialog from '@/views/pages/market/components/receive-order-detail/components/buyer-self-owner-dialog.vue' // 自有户风险弹窗
import { limitOrderTakingTips, limitFastTradeOrderTaking, enjambmentReceivingOrderTips } from '@/utils/limit-order-taking.js' // E+是否限制接单提示弹窗
import { yuan2wan } from '@/common/js/number' // 金额单位转换
import {
  OPEN_BANK_LOGIN_AUTH_DIALOG,
  OPEN_BANK_LOGIN_AUTH_SUCCESS,
} from '@/event/modules/site'
import { PAYMENT_CHANNEL, E_PLUS_LOGIN_AUTH_KEY } from '@/constant'

const formInit = Object.freeze({
  orderList: [], // 订单号
  buyerTraderAccountId: '', // 资方电子交易账户id
  buyerTraderCorpBankCardId: '', // 资方银行卡id
  paymentChannel: '', // 支付渠道，1-智付亿联、2-智付百信、3-智付连连、4-智付合利宝
})

export default {
  name: 'agent-order-confirm-dialog',
  components: {
    ResultDialog,
    PayChannelRadio,
    AccountListSelect,
    OrderChangedDialog,
    LoadingDialog,
    Recharge,
    BatchPayWays,
    NewVersionDraftTipDialog,
    BuyerSelfOwnerDialog,
  },
  mixins: [batchPayWaysMixins],
  props: {
    order: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {
      limitFastTradeOrderTaking,
      dialogVisible: false,
      loading: false,
      form: { ...formInit },
      rules: {
        paymentChannel: [{ required: true, message: '请选择支付渠道', trigger: ['blur'] }],
        buyerTraderCorpBankCardId: [{ required: true, message: '请选择签收账户', trigger: ['blur'] }],
      },
      batchOrderInfo: {},
      batchLoading: false,
      marketApi,
      newOrderInfo: null, // 最新的订单信息（报价、订单类型）
      fastTradeOrderDisabeld: false, // 光速订单禁用规则字段
    }
  },

  computed: {
    ...mapGetters('user', {
      sdmInfo: 'sdmInfo', // 米账号信息
    }),
    ...mapGetters('common', {
      startCrawlerScheme: 'startCrawlerScheme', // E++是否开启爬虫方案 1开启 0关闭
    }),
    draftInfo() {
      return Array.isArray(this.order) ? this.order[0] : this.order
    },
    // 是否可以点击确认按钮
    isCanConfirm() {
      const { paymentChannel, buyerTraderCorpBankCardId } = this.form
      if (!paymentChannel || !buyerTraderCorpBankCardId) {
        return false
      }
      // 有新票需要判断账户是否支持
      if (this.batchOrderInfo && this.batchOrderInfo.orderList.some(v => !!v.draftType)) {
        const { traderCorpBankCardVOList } = this.batchOrderInfo
        const card = traderCorpBankCardVOList.filter(item => item.id === buyerTraderCorpBankCardId)[0]
        return !!card.newDraftFlag
      }
      return true
    },
    limitOrderInfo() { // E+限制接单参数
      return this.$store.state.common.limitOrderInfo
    },
  },

  methods: {
    ...mapActions('user', {
      getSdmInfo: 'getSdmInfo', // 米账号信息
      getPaymentAccountList: 'getPaymentAccountList', // 查询电子交易账户列表
    }),

    // 简易版网银登录成功回调
    handleAuthSuccess(obj) {
      if (obj?.key === E_PLUS_LOGIN_AUTH_KEY.BATCH_CONFIRM_AGENT_ORDER) {
        this.batchAcceptOrder()
      }
    },

    // 初始化
    init(orderList = []) {
      // E++开启爬虫方案时,登录校验成功 key=>参照枚举类型 E_PLUS_LOGIN_AUTH_KEY
      this.$event.on(OPEN_BANK_LOGIN_AUTH_SUCCESS, this.handleAuthSuccess)

      this.form.orderList = orderList
      this.getBatchOrderInfo(orderList)
      this.getSdmInfo() // 获取米
    },

    // 新一代票据提示弹窗回调
    newVersionTipSuccess(cardList) {
      // 更新新票状态
      for (let i in this.batchOrderInfo.traderCorpBankCardVOList) {
        let item = this.batchOrderInfo.traderCorpBankCardVOList[i]
        for (let j in cardList) {
          let card = cardList[j]
          if (item.id === card.bankCardId) {
            item.newDraftFlag = card.newDraftFlag
            break
          }
        }
      }
      this.$refs.accountListSelect && this.$refs.accountListSelect.updatePaymentAccountList()
    },

    // 关闭弹窗
    handleClose() {
      this.$event.off(OPEN_BANK_LOGIN_AUTH_SUCCESS, this.handleAuthSuccess)
      this.$emit('close')
      this.clearData()
      this.dialogVisible = false
    },

    // 清除数据
    clearData() {
      this.form = {}
      this.loading = false
      this.batchOrderInfo = {}
      this.batchLoading = false
      this.newOrderInfo = null
      this.$refs.form && this.$refs.form.resetFields()
    },

    // 批量操作结果弹窗关闭回调
    closeResultDialog() {
      this.$emit('success')
      this.handleClose()
    },

    // 选择支付渠道
    onChangePayChannel(val) {
      const { payChannel, currentAccount } = val

      payChannel && (this.form.paymentChannel = payChannel)// 支付渠道
      this.form.buyerTraderAccountId = (currentAccount || {})?.buyerTraderAccountId // 资方当前电子交易账户id
      this.form.buyerTraderCorpBankCardId = null // 清除选择的背书账号
      this.fastTradeOrderRule()
    },

    // 选择签收账户列表
    onChangeAccount(val) {
      const { accountSelect } = val
      this.form.buyerTraderCorpBankCardId = accountSelect // 资方当前签收账户id(银行卡id)
    },

    // 自有户校验
    async buyerSelfOwnedCheck() {
      const list = this.order.map(e => Object.assign({
        paymentChannel: this.form.paymentChannel,
        fastTrade: e.fastTrade,
        draftAmountWan: yuan2wan(e.draftActualAmount),
        generalAccount: e.generalAccount,
        generalAccountZb: e.generalAccountZb,
      }))
      const limitOrders = JSON.parse(JSON.stringify(this.limitOrderInfo))
      // 跨行回款户的接单提示
      await enjambmentReceivingOrderTips(Object.assign({}, limitOrders, { list, hasAllOrder: true }), this)

      limitOrderTakingTips(this.form.paymentChannel, this).then(res => {
        if (res === 'upload') { // 去上传佐证
          this.dialogVisible = false
          this.$router.push('/user-center/buy-draft?tab=9')
        }
        if (res === 'success') {
          if (this.batchOrderInfo.buyerSelfOwnedNames && this.batchOrderInfo.buyerSelfOwnedNames.length) {
            this.buyerSelfOwnedMsg()
          } else {
            this.handleConfirm()
          }
        }
      })
    },

    // 自有户提示框确认
    buyerSelfOwnedMsg() {
      this.$refs.buyerSelfOwnerDialogRef && this.$refs.buyerSelfOwnerDialogRef.open(this.batchOrderInfo)
    },

    // 确认
    async handleConfirm() {
      // 券后支付的云豆总额
      let c1 = this.isCouponPay && ((this.sdmInfo?.balanceAmt ?? 0) < this.totalAfterCouponAmount)
      // 直接支付的云豆总额
      let c2 = !this.isCouponPay && ((this.sdmInfo?.balanceAmt ?? 0) < this.batchOrderInfo.totalPlatformServiceFee)
      if (c1 || c2) {
        this.$message.error(`您的${this.sdmName}不足，请充值`)
        return
      }
      try {
        await this.$refs.form.validate()

        // 仅确认接单操作 - E++开启爬虫方案时，需要打开网银登录授权弹窗，其他类型接单在各自的窗口有校验逻辑
        if (this.form.paymentChannel === PAYMENT_CHANNEL.YL_PLUS.id && this.startCrawlerScheme) {
        // 区分确认接单和定向单确确认
          this.$event.emit(OPEN_BANK_LOGIN_AUTH_DIALOG, { key: E_PLUS_LOGIN_AUTH_KEY.BATCH_CONFIRM_AGENT_ORDER })
        } else {
          this.batchAcceptOrder()
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        // console.log('确认error', error)
      }
    },

    // 批量接单，目前只用于定向票
    async batchAcceptOrder() {
      // 获取报价
      const batchQuotedPriceList = (this.newOrderInfo || this.order).map(v => ({
        orderNo: v?.orderNo, // 订单id
        quotedPrice: v.quotedPrice || v?.lakhFee// 报价 目前传参每十万扣款
      }))

      try {
        this.batchLoading = true
        let params = {
          ...this.form,
          batchQuotedPriceList,
          isShowBatchError: false,
          linkId: user.getLinkId()
        }
        // 批量接单-券付费方式参数添加
        this.handleParams(params)
        const data = await marketApi.postBatchAcceptAgentOrder(params)
        this.batchLoading = false
        if (data && data.validateResults && data.validateResults.length) {
          // 保存最新报价、订单类型
          this.newOrderInfo = this.saveNewOrderChangedInfo(data.validateResults, this.newOrderInfo, this.order)
          // 打开订单报价/类型改变弹窗
          this.$refs.orderChangedDialogRef.init({ ...data, isBatch: true })
        } else {
        // 批量操作结果提示
          this.$refs.resultDialogRef.init(data)
        }
      } catch (error) {
        this.batchLoading = false
        if (error.data.code === ISSUE_DRAFT_ERROR_CODE.RECEIVE_MAX_LIMIT_REACHED) {
          this.$confirm('您的接单量已达到限额，如需调整额度，请联系客户经理', '提示', {
            type: 'warning',
            iconPosition: 'title',
            showClose: false,
            showCancelButton: false,
            confirmButtonText: '我知道了'
          })
          return
        }
        this.$message.error(error.data.msg)
        // console.log('批量接单，目前只用于定向票 :>> ', error)
        throw error
      }
    },

    // 保存最新报价、订单类型
    saveNewOrderChangedInfo(validateResults = [], newOrderInfo = [], orderList = []) {
      return orderList.map(v => {
        // 接口的最新报价、最新订单类型
        const { newQuotedPrice, newFastTrade } = (validateResults.find(k => k.orderNo === v.orderNo) || {})
        // 上次的接口最新报价、最新订单类型
        const { quotedPrice: lastNewQuotedPrice, fastTrade: lastNewFastTrade } = newOrderInfo ? (newOrderInfo.find(k => k.orderNo === v.orderNo) || {}) : ''

        return {
          orderNo: v.orderNo,
          quotedPrice: newQuotedPrice || lastNewQuotedPrice || v?.lakhFee, // 报价
          fastTrade: (isNotVoid(newFastTrade, false) ? newFastTrade : null) || (isNotVoid(lastNewFastTrade, false) ? lastNewFastTrade : null) || v?.fastTrade, // 订单类型
        }
      })
    },

    // 查询批量订单信息
    async getBatchOrderInfo(orderNoList) {
      try {
        this.loading = true
        const data = await marketApi.postAcceptDialogDetail({ orderNoList })
        this.batchOrderInfo = data
        const noBanAccountList = data?.traderAccountVOList ? data?.traderAccountVOList.filter(item => item.banStatus !== BAN_STATUS.DISABLE.id) : []// 未禁用的交易列表
        if (!noBanAccountList.length) {
          user.openCustomerServiceDialog()
          return
        }
        this.dialogVisible = true
        await this.getPaymentAccountList() // 用户设置-查询电子交易账户列表
        this.$refs.payChannelRadio && this.$refs.payChannelRadio.init() // 支付渠道选择
        this.$refs.accountListSelect && this.$refs.accountListSelect.init() // 签收账户选择
        if (data.orderList.some(v => !!v.draftType)) {
          this.showNewDraftTip()
        }

        this.loading = false
      } catch (error) {
        // eslint-disable-next-line no-console
        // console.log('查询批量订单信息error :>> ', error)
        this.loading = false
        this.handleClose()
      }
    },

    // 充值
    recharge() {
      this.$refs.recharge && this.$refs.recharge.init()
    },

    // 新票温馨提示弹窗
    showNewDraftTip() {
      this.$refs.refNewVersionDraftTipDialog && this.$refs.refNewVersionDraftTipDialog.init()
    },
    // 光速E+订单批量接单限制规则判断
    fastTradeOrderRule() {
      const list = this.order.map(e => Object.assign({
        paymentChannel: this.form.paymentChannel,
        fastTrade: e.fastTrade,
        draftAmountWan: yuan2wan(e.draftActualAmount),
        generalAccount: e.generalAccount,
        generalAccountZb: e.generalAccountZb,
      }))
      const limitOrders = JSON.parse(JSON.stringify(this.limitOrderInfo))
      setTimeout(() => {
        this.fastTradeOrderDisabeld = limitFastTradeOrderTaking(Object.assign({}, limitOrders, { list, hasAllOrder: true }))
      }, 0)
    }
  }
}
</script>
