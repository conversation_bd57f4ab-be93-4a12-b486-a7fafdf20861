<!-- 带保接单确认弹窗 -->

<style lang="scss">
.marin-receive-doalog {
  background: #FFFFFF !important;

  .number {
    font-weight: 400;
    color: $--color-primary;
  }

  .blod {
    display: inline-block;
    margin-left: 5px;
    border-bottom: 1px solid $--color-primary;
    font-weight: 600;
    color: $--color-primary;
    cursor: pointer;
  }

  .balance {
    font-weight: 600;
  }

  .txt {
    font-size: 14px;
  }

  .sellerBankAccount-cls {
    padding-top: 15px;
  }

  .item-position {
    position: relative;

    ::v-deep .el-form-item__label {
      padding: 0;
    }

    .value {
      .el-tooltip {
        position: absolute;
        top: 1px;
        left: 65px;
      }
    }

    .max-width {
      width: 100%;
    }
  }

  .btn-warp {
    margin-top: 10px;
    text-align: end;
  }
}

.seller-select-btn {
  font-size: 16px;
  text-align: center;
  color: $--color-primary;
  line-height: 40px;
  cursor: pointer;
}

.seller-bank-link {
  border-bottom: 1px solid $--color-primary;
  color: $--color-primary;
  cursor: pointer;
}
</style>

<template>
  <div class="marin-receive-doalog111">
    <el-dialog
      title="提示"
      custom-class="marin-receive-doalog"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      :show-close="false"
      :visible.sync="dialogVisible"
      width="500px"
    >
      <div v-if="orderData">
        <div class="txt">资方选择带保证金接该笔订单，确认订单将冻结<span class="number">{{ orderData.marginAmount || 0 }}</span>{{ sdmName }} </div>
        <div style="margin-top: 8px; padding: 12px;font-size: 14px;background: #F5F6F8;">
          {{ sdmName }}可用余额：
          <span class="balance">{{ (sdmInfo || {}).balanceAmt || 0 }}</span>
          <span class="blod" @click="handleRecharge">充值</span>
        </div>
        <!-- 邦+、E+渠道票方确认带保订单可以修改变更回款账户 -->
        <div v-if="[PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id, PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id, PAYMENT_CHANNEL.YL_PLUS.id].includes(orderData.paymentChannel)" class="sellerBankAccount-cls">
          <div class="txt">根据平台规则，您可修改回款账户为任意已绑定账户</div>
          <FormItem
            label="回款账户"
            class="form-item-block item-position"
            :required="false"
          >
            <el-tooltip
              placement="top"
              popper-class="issue-draft-tooltip"
            >
              <template slot="content">
                <div>依照智付E+、E++渠道交易模式，票方发布票据必须选择回款账户(支持任意银行一般户)。需要在<span class="seller-bank-link" @click="() => { $router.push('/user-center/bank-account?tabStatus=2');dialogVisible = false }">银行回款账户</span>页面绑定回款账户，交易完成后票款自动提现到账。</div>
              </template>
              <icon class="icon icon-question" type="chengjie-wenti" />
            </el-tooltip>

            <div class="pay-type-item">
              <el-select
                ref="sellerBankAccount"
                v-model="sellerBankAccount"
                class="max-width"
                :height="40"
                placeholder="请选择回款账户"
              >
                <el-option
                  v-for="item in sellerBankAccountList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
                <div class="seller-select-btn" @click="() => { $router.push('/user-center/bank-account?tabStatus=2'); $refs.sellerBankAccount.blur(); dialogVisible = false }"><i class="el-icon-plus" />添加回款账户</div>
              </el-select>
            </div>
          </FormItem>
        </div>
        <div class="btn-warp">
          <el-button @click="() => { dialogVisible = false }">暂不确认</el-button>
          <el-button type="primary" @click="submit">确认订单</el-button>
        </div>
      </div>
    </el-dialog>
    <Recharge ref="recharge" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Recharge from '@/views/components/user-center/recharge/recharge.vue' // 米充值
import FormItem from '@/recognize/components/issue-draft/components/form-item.vue'
import { PAYMENT_CHANNEL } from '@/constant'
import userApi from '@/apis/user' // 用户接口
export default {
  name: 'margin-receive-dialog',
  components: {
    FormItem,
    Recharge
  },
  props: {
  },
  data() {
    return {
      PAYMENT_CHANNEL,
      dialogVisible: false,
      orderData: null,
      sellerBankAccount: '', // 回款账户
    }
  },
  computed: {
    ...mapGetters('user', {
      sdmInfo: 'sdmInfo', // 米账号信息
      sellerBankAccountList: 'sellerBankAccountList', // 回款账户列表
      signBankAccountList: 'signBankAccountList', // 签收账户列表
    }),
    // 当前订单回款账户id是否存在回款账户列表中
    isSeller() {
      return this.sellerBankAccountList.some(e => e.id === Number(this.orderData.sellerBankAccountId))
    },
    // 当前回款账户未添加到“银行回款账户”、未添加到“银行签收账户”
    isSign() {
      return this.signBankAccountList.some(e => e.id === Number(this.orderData.sellerBankAccountId))
    }
  },
  methods: {
    init(val) {
      this.dialogVisible = true
      this.orderData = val
      this.sellerBankAccount = this.isSeller ? Number(val.sellerBankAccountId) : null
    },
    // 显示米充值弹窗
    handleRecharge() {
      this.$refs.recharge.init()
    },
    submit() {
      // if (!this.sellerBankAccount) return this.$message.warning('请选择回款账户')

      // 当前回款账户未添加到“银行回款账户”、未添加到“银行签收账户”
      if (!this.isSeller && !this.isSign) {
        return this.$message.info('请在银行账户内先添加该回款账户')
      }
      // 当前回款账户未添加到银行回款账户列表
      if (!this.isSeller) {
        this.$confirm('是否将该银行账户添加到回款账户', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async() => {
          // 同步至回款账户列表
          await userApi.setBankCardSync({ bankCardId: Number(this.orderData.sellerBankAccountId), toAccountType: 2 })
          // 刷新store的账户列表
          this.$store.dispatch('user/getPassedBankCardList')
          this.$emit('confirm', null)
          this.close()
        })
      } else {
        this.$emit('confirm', null)
        this.close()
      }
    },
    close() {
      this.dialogVisible = false
    }
  }
}
</script>
