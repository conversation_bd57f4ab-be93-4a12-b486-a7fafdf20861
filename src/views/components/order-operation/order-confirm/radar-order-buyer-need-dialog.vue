<!-- 自动订单-资方要求弹窗 -->
<style lang="scss" scoped>
  .main {
    padding: 12px;
    min-height: 64px;
    background-color: $color-FFFFFF;

    .item {
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }

      .high-light {
        margin: 0 4px;
        font-weight: bold;
        color: $--color-primary;
      }

      .theme-color {
        color: $color-warning;
      }
    }
  }

  .el-button {
    margin-left: 8px;
  }
</style>

<template>
  <el-dialog
    title="资方要求"
    :visible.sync="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    class="radar-order-buyer-need-dialog"
    append-to-body
    :before-close="handleClose"
  >
    <RedWarnContent class-type="green" class="warn-tip">
      请满足资方要求再确认订单，因不满足以下要求导致资方取消，需要您承担违约责任。
    </RedWarnContent>
    <div v-waiting="orderApi.getBillOrderNeedRule" class="main">
      <template v-if="info">
        <div class="item">
          <div class="text-gray">票据瑕疵</div>
          <div class="value">{{ defectsStr }}</div>
        </div>
        <div v-if="hasEndorseNeed" class="item">
          <div class="text-gray">背书要求</div>
          <div class="value">
            <template v-if="info.endorseCountMax || info.endorseCountMax === 0">累计不超过<span class="high-light">{{ info.endorseCountMax }}</span>手；</template>
            <template v-if="info.everyDayEndorseCount || info.everyDayEndorseCount === 0">自出票日起，每日不超过<span class="high-light">{{ info.everyDayEndorseCount }}</span>手；</template>
            <template v-if="info.numberOfDaysCountMax || info.numberOfDaysCountMax === 0">近<span class="high-light">{{ info.numberOfDays }}</span>日每日不超过<span class="high-light">{{ info.numberOfDaysCountMax }}</span>手；</template>
            <template v-if="info.todayEndorseCountMax || info.todayEndorseCountMax === 0">今日不超过<span class="high-light">{{ info.todayEndorseCountMax }}</span>手。</template>
          </div>
        </div>
        <div v-if="info.fontKeyWord || info.endorseKeyWord" class="item">
          <div class="text-gray">敏感字要求</div>
          <div v-if="info.fontKeyWord" class="value">风险关键字：<span class="theme-color">{{ info.fontKeyWord.split(',').join('、') }}</span>；</div>
          <!-- <div v-if="info.endorseKeyWord" class="value">背书敏感字：<span class="theme-color">{{ info.endorseKeyWord.split(',').join('、') }}</span>；</div> -->
        </div>
      </template>
    </div>
    <div slot="footer">
      <!-- 取消订单 -->
      <CancelOrder
        border
        :order="orderInfo"
        height="40"
        @init="handleCancelOrderInit"
        @success="handleCancelOrderSuccess"
        @destroy="handleClose"
      />
      <el-button type="primary" :disabled="!info" @click="handleConfirm">确认订单</el-button>
    </div>
  </el-dialog>
</template>

<script>
import orderApi from '@/apis/order'
import RedWarnContent from '@/views/components/common/warn-content.vue'
import CancelOrder from '@/views/components/order-operation/cancel-order/cancel-order.vue'
import {
  BACK_DEFECT_TYPE_KEY_MAP, // 瑕疵映射key
} from '@/constant'

export default {
  name: 'radar-order-buyer-need-dialog',
  components: {
    RedWarnContent,
    CancelOrder
  },
  props: {
    orderInfo: Object, // 订单信息
  },
  data() {
    return {
      orderApi,
      dialogVisible: false,
      hasEndorseNeed: false, // 有背书要求
      defectsStr: '', // 瑕疵内容
      info: null, // 资方要求信息
    }
  },

  methods: {
    init(orderNo) {
      this.dialogVisible = true
      this.getBillOrderNeedRule(orderNo)
    },
    // 获取订单的自动接单规则
    async getBillOrderNeedRule(orderNo) {
      if (!orderNo) {
        this.$message.error('没有订单号')
        return
      }
      const data = await orderApi.getBillOrderNeedRule(orderNo)
      if (!data) {
        return
      }
      let isSelectAllDefects = true // 是不是瑕疵全选
      let currentDefectsStr = '' // 当前处理后的瑕疵
      const endorseKeyList = ['endorseCountMax', 'todayEndorseCountMax', 'everyDayEndorseCount', 'numberOfDaysCountMax'] // 背书相关字段，用于判断是否有背书要求
      Object.keys(data).forEach(key => {
        // BACK_DEFECT_TYPE_KEY_MAP[key]有值表示是瑕疵字段，整理数据成需要显示样式
        if (BACK_DEFECT_TYPE_KEY_MAP[key] || key === 'defectsWithout') {
          if (data[key]) {
            currentDefectsStr = key === 'defectsWithout' ? '无瑕疵' : `${BACK_DEFECT_TYPE_KEY_MAP[key]}`
            this.defectsStr = `${this.defectsStr && `${this.defectsStr}，`}${currentDefectsStr}`
          } else {
            isSelectAllDefects = false
          }
        }
        // 判断是否有背书要求
        if (endorseKeyList.includes(key) && data[key] !== null) {
          this.hasEndorseNeed = true
        }
      })
      // 选择全部瑕疵时
      isSelectAllDefects && (this.defectsStr = '全部')
      this.info = data
    },

    // 取消订单弹窗初始化
    handleCancelOrderInit() {
      this.dialogVisible = false
    },

    // 确认订单
    handleConfirm() {
      this.dialogVisible = false
      this.$emit('confirm')
    },

    // 取消订单弹窗成功
    handleCancelOrderSuccess() {
      this.$emit('success')
    },

    // 关闭
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
