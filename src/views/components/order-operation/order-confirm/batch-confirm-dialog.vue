<!-- 批量确认订单弹窗 -->
<style lang="scss" scoped>
::v-deep {
  .el-dialog .el-dialog__body {
    padding: 10px 20px 0;
  }
}

.confirm-box {
  margin-top: 8px;
  padding: 16px;
  background-color: $color-F8F8F8;
}

.confirm {
  margin-top: 8px;
  padding: 16px;
  font-size: 14px;
  background: $color-FFFFFF;
}

.confirm-content {
  background: $color-F8F8F8;
}

.credit-level {
  display: flex;
  align-items: center;
  height: 20px;
  font-weight: 600;
  color: $color-text-primary;
  line-height: 20px;

  .text-gray {
    font-weight: 400;
    color: $color-text-secondary;
  }
}

.credit-block {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;

  .block-item {
    padding: 12px;
    width: 210px;
    color: $color-text-secondary;
    background: $color-F8F8F8;

    .text-bold {
      margin-left: 8px;

      @include font(16px, $--color-primary, 600);
    }
  }
}

.dialog-footer {
  padding: 12px 0;
  text-align: right;
}

.primary-text {
  color: #EC3535;
}

.sellerBankAccount-cls {
  padding-top: 15px;

  .form-item-block {
    display: flex;
    align-items: center;

    ::v-deep .label {
      margin-right: 10px !important;
    }

    ::v-deep .value {
      width: 300px;
    }
  }
}

.item-position {
  position: relative;

  ::v-deep .el-form-item__label {
    padding: 0;
  }

  .value {
    .el-tooltip {
      position: absolute;
      top: 10px;
      right: 28px;
    }
  }

  .max-width {
    width: 100%;
  }
}

.activity-box {
  padding-bottom: 12px;
  text-align: center;

  img {
    margin-bottom: 8px;
  }

  .red-font {
    cursor: pointer;
    border-bottom: 1px solid #EC3535;
    font-size: 16px;
    font-weight: 500;
    color: #EC3535;
  }
}

.seller-select-btn {
  font-size: 16px;
  text-align: center;
  color: $--color-primary;
  line-height: 40px;
  cursor: pointer;
}

.seller-bank-link {
  border-bottom: 1px solid $--color-primary;
  color: $--color-primary;
  cursor: pointer;
}
</style>

<template>
  <el-dialog
    title="批量确认订单"
    :visible.sync="dialogVisible"
    width="500px"
    :close-on-click-modal="false"
    append-to-body
  >
    <div>
      <RedWarnContent class-type="red" class="warn-tip">
        请确认选中的订单信息已核对无误
      </RedWarnContent>
      <div class="confirm">
        <div v-if="isSale && false" class="activity-box">
          <img src="https://oss.chengjie.red/web/imgs/public/activity.png" alt="">
          <div><span class="red-font" @click="goCerter">卖方交易笔笔有现金奖励!</span></div>
        </div>
        <div>共 <span class="primary-text">{{ order && order.length }}</span> 张，票面金额总额：<span class="primary-text">{{ `${yuan2wan(totalPrice)}万元` }}</span></div>
        <div v-if="totalSplit > 0" class="confirm confirm-content">
          <div>含 <span class="primary-text">{{ totalSplit }}</span> 张新票拆分订单<span v-if="totalSplit === 1">，拆分接单金额：<span class="primary-text">{{ `${yuan2wan(splitAmount)}万元` }}</span></span></div>
          <div style="margin-top: 2px;">确认拆分订单，则剩余金额需在该笔订单完成后继续交易</div>
        </div>
        <div style="margin-top: 2px;">批量确认后，订单将进入资方待支付环节</div>
        <div v-if="isShowSellerBankAccount" class="sellerBankAccount-cls">
          <div class="txt">{{ batchOrderSellerBankAccountState ? '根据平台规则，您可修改回款账户为任意已绑定账户' : '已选订单包含多个回款账户，您可选择回款账户进行统一修改或保留原回款账户直接确认订单' }}</div>
          <FormItem
            label="回款账户"
            class="form-item-block item-position"
            :required="false"
          >
            <el-tooltip
              placement="top"
              popper-class="issue-draft-tooltip"
            >
              <template slot="content">
                <div>依照智付E+、E++渠道交易模式，票方发布票据必须选择回款账户(支持任意银行一般户)。需要在<span class="seller-bank-link" @click="() => { $router.push('/user-center/bank-account?tabStatus=2');dialogVisible = false }">银行回款账户</span>页面绑定回款账户，交易完成后票款自动提现到账。</div>
              </template>
              <icon class="icon icon-question" type="chengjie-wenti" />
            </el-tooltip>

            <div class="pay-type-item">
              <el-select
                ref="sellerBankAccount"
                v-model="sellerBankAccount"
                class="max-width"
                :height="40"
                clearable
                placeholder="请选择回款账户"
              >
                <el-option
                  v-for="item in sellerBankAccountList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
                <div class="seller-select-btn" @click="() => { $router.push('/user-center/bank-account?tabStatus=2'); $refs.sellerBankAccount.blur(); dialogVisible = false }"><i class="el-icon-plus" />添加回款账户</div>
              </el-select>
            </div>
          </FormItem>
        </div>
      </div>
    </div>
    <div class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button
        type="primary"
        @click="confirm"
      >
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import RedWarnContent from '@/views/components/common/warn-content.vue'
import { yuan2wan } from '@/common/js/number'
import BigNumber from 'bignumber.js'
import { showConfirmationDialog } from '../draftNo-Check'// 智付E+渠道订单确认弹窗
import { PAYMENT_CHANNEL } from '@/constant'
import FormItem from '@/recognize/components/issue-draft/components/form-item.vue'
import { mapGetters } from 'vuex'
import { tracking } from '@/utils/util'
import { formatTime } from '@/common/js/date'
import userApi from '@/apis/user' // 用户接口
export default {
  name: '',

  components: {
    RedWarnContent,
    FormItem
  },
  props: {
    isSale: Boolean
  },
  data() {
    return {
      PAYMENT_CHANNEL,
      dialogVisible: false, // 显示弹窗
      totalPrice: 0,
      order: null,
      totalSplit: 0, // 拆分数量
      splitAmount: 0, // 拆分金额
      sellerBankAccount: '', // 回款账户
      batchOrderSellerBankAccountState: false // 批量订单回款账户是否一致
    }
  },

  computed: {
    ...mapGetters('user', {
      sellerBankAccountList: 'sellerBankAccountList', // 回款账户列表
      signBankAccountList: 'signBankAccountList', // 签收账户列表
      userInfo: 'userInfo'
    }),
    // 是否包含智付E+渠道  ZHI_FU_YI_LIAN_PLUS
    hasYiLianPlusChannel() {
      return this.order && this.order.some(order => order.paymentChannel === PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id)
    },
    // E+ 邦+渠道可修改变更回款账户
    isShowSellerBankAccount() {
      return this.order && this.order.some(e => e.draftOrderPay.acceptJdYlpay || e.draftOrderPay.acceptZbankPlus || e.draftOrderPay.acceptYiPlusPlus)
    },

  },
  methods: {
    showConfirmationDialog,
    yuan2wan,
    init(order) {
      if (!order) {
        this.$message.error('没有订单信息')
        return
      }
      this.order = order
      this.totalPrice = 0
      this.totalSplit = 0
      this.splitAmount = 0
      // 判断批量订单回款账户是否一致
      this.batchOrderSellerBankAccountState = this.order.every(e => e.sellerBankAccountId === this.order[0].sellerBankAccountId)
      this.batchOrderSellerBankAccountState ? this.sellerBankAccount = Number(this.order[0].sellerBankAccountId) : this.sellerBankAccount = null

      for (let item of this.order) {
        this.totalPrice = new BigNumber(item.draftAmount).plus(this.totalPrice)
        if (item.parentOrderNo) {
          // 有拆分
          this.totalSplit++
          this.splitAmount = item.draftAmount
        }
      }
      this.dialogVisible = true
      if (this.hasYiLianPlusChannel) {
        this.showConfirmationDialog('背出', { type: '票方批量确认', info: this.order })
      }
    },

    // 点击确认判断
    async confirm() {
      // 刷新银行账户列表数据 防止上一次操作批量确认多笔订单回款账户同步 存在个别已同步成功的回款账户
      await this.$store.dispatch('user/getPassedBankCardList')

      // 批量订单数据回款账户相同，回款账户必填校验
      // if (this.batchOrderSellerBankAccountState && !this.sellerBankAccount) return this.$message.warning('请选择回款账户')

      // 当前订单回款账户id是否存在回款账户列表中
      const isSeller = this.sellerBankAccountList.some(e => e.id === this.sellerBankAccount)
      // 当前订单回款账户id是否存在签收账户列表中
      const isSign = this.signBankAccountList.some(e => e.id === this.sellerBankAccount)
      // 批量订单回款账户相同 && 当前回款账户不在银行回款列表 && 当前回款账户不在银行签收列表
      if (this.batchOrderSellerBankAccountState && !isSeller && !isSign) return this.$message.info('请在银行账户内先添加该回款账户')

      // 批量订单回款账户是否存在不在银行回款账户列表中的订单 批量执行同步至回款账户
      // if (!this.batchOrderSellerBankAccountState) {
      const bankInfo = [] // 存储当前需要同步至银行回款账户列表的数据
      const sellerIds = this.sellerBankAccountList.map(e => Number(e.id)) // 银行回款账户列表id集合
      this.order.forEach(item => {
        const idx = sellerIds.indexOf(Number(item.sellerBankAccountId))
        if (idx < 0 && !bankInfo.filter(e => e.bankCardId === Number(item.sellerBankAccountId))) {
          bankInfo.push({ orderNo: item.orderNo, bankCardId: Number(item.sellerBankAccountId) })
        }
      })

      // 是否存在需要同步的回款账户 调用批量同步回款账户的接口处理
      if (bankInfo.length) {
        this.$confirm('是否将订单银行账户同步至回款账户', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async() => {
          // 同步至回款账户列表
          await userApi.batchBankCardBackAccountSync({ orders: bankInfo })
          // 刷新store的账户列表
          this.$store.dispatch('user/getPassedBankCardList')
          this.handlerConfirm()
        })
      } else {
        this.handlerConfirm()
      }
      // }
      // else {
      //   this.handlerConfirm()
      // }
    },

    // 确定订单操作
    handlerConfirm() {
      const { corpName } = this.userInfo
      const time = formatTime(Date.now(), 'YYYY-MM-DD hh:mm:ss')
      tracking({
        userAction: `${corpName}在${time}点击了批量确认订单操作`,
        orderNos: this.order.length && this.order.map(e => e.orderNo)
      })
      this.$emit('confirm', this.sellerBankAccount)
    },

    // 关闭弹窗
    handleClose() {
      this.$emit('close')
      this.dialogVisible = false
    },
    // 跳转至活动中心
    goCerter() {
      this.$router.push('/user-center/coupon?tabs=receiveCentre')
      this.dialogVisible = false
    }
  }
}
</script>
