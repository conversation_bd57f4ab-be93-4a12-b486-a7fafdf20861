<!-- 对方信用确认弹窗 -->
<style lang="scss" scoped>
.confirm-box {
  margin-top: 8px;
  padding: 16px;
  background-color: $color-F8F8F8;
}

.flex {
  margin-top: 10px;

  @include flex-vc;

  .left,
  .right {
    flex: 1;
  }

  .left {
    border-right: 1px solid $color-D9D9D9;
  }

  .right {
    padding-left: 24px;
  }

  .label {
    margin-bottom: 2px;
    font-size: 14px;
    color: $color-text-secondary;
    line-height: 20px;
  }

  .value {
    font-weight: bold;
  }
}
</style>

<style lang="scss" >
.myClass {
  width: 540px;
}
</style>

<template>
  <div>
    <!-- 米充值 -->
    <Recharge ref="recharge" />
    <!-- 确认订单弹窗 -->
    <CreditConfirmDialog ref="CreditConfirmDialog" :is-sale="isSale" @confirm="onConfirm()" />
    <!-- 带保订单确认 -->
    <MarginReceiveDialog ref="MarginReceiveDialog" @confirm="marginReceiveSubmit()" />
  </div>
</template>

<script>
import orderApi from '@/apis/order'
import { mapActions, mapGetters } from 'vuex'
import Recharge from '@/views/components/user-center/recharge/recharge.vue' // 米充值
import CreditConfirmDialog from './credit-confirm-dialog.vue' // 确认订单弹窗
import { PAYMENT_CHANNEL } from '@/constant' // 会员等级 id 映射 名称
import { showConfirmationDialog } from '../draftNo-Check'// 智付E+渠道订单确认弹窗
import MarginReceiveDialog from './margin-receive-dialog.vue' // 带保接单确认
export default {
  name: '',
  components: {
    Recharge,
    CreditConfirmDialog,
    MarginReceiveDialog
  },
  props: {
    isSale: Boolean
  },
  data() {
    return {
      PAYMENT_CHANNEL,

    }
  },
  computed: {
    ...mapGetters('user', {
      sdmInfo: 'sdmInfo', // 米账号信息
    }),
  },
  methods: {
    showConfirmationDialog,
    ...mapActions('user', {
      getSdmInfo: 'getSdmInfo', // 米账号信息
    }),
    async init(orderData) {
      if (!orderData) {
        this.$message.error('没有订单信息')
        return
      }
      await this.getSdmInfo()
      // 调用详情接口  只为了获取 会员等级
      if (orderData.oppositeMemberInfoDTO) {
        orderData.oppositeMemberInfoDTO = orderData.oppositeMemberInfoDTO || {}
      } else {
        const { oppositeMemberInfoDTO } = await orderApi.getTraderCorpOrderInfo(orderData.id || orderData.orderNo)
        orderData.oppositeMemberInfoDTO = oppositeMemberInfoDTO || {}
      }

      // const isAgentOrder = !!orderData.agentOrder
      if (orderData.showSellerConfirm) {
        // 只有智付E+ 渠道才强制提醒  ZHI_FU_YI_LIAN_PLUS
        if (orderData.paymentChannel === PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id) {
          this.showConfirmationDialog('背出', { type: '票方单张确认', info: orderData }).then(() => {
            this.margingbox(orderData)
          })
        } else {
          this.margingbox(orderData)
        }
      } else {
        // 确认订单弹窗
        this.$refs.CreditConfirmDialog.init(orderData)
        // const message = <div>
        //   {`${!isAgentOrder ? '对方已接单，' : ''}请您尽快确认订单！`}
        // </div>
        // this.$msgbox({
        //   title: '确认订单',
        //   dangerouslyUseHTMLString: true,
        //   message,
        //   confirmButtonText: '确认',
        //   showCancelButton: true,
        //   cancelButtonText: '取消',
        //   type: 'warning',
        // }).then(() => {
        //   this.$emit('confirm')
        // })
        // // eslint-disable-next-line no-empty-function
        //   .catch(() => {
        //     this.$emit('close')
        //   })
      }
    },

    margingbox(orderData) {
      this.$refs.MarginReceiveDialog.init(orderData)
      // const balanceAmt = (this.sdmInfo || {}).balanceAmt || 0
      // const frozenAmt = orderData.marginAmount || 0
      // const h = this.$createElement
      // this.$msgbox({
      //   title: '提示',
      //   message: h('div', {
      //     style: 'line-height: 24px;',
      //   }, [
      //     h('span', undefined, '资方选择带保证金接该笔订单，确认订单将冻结 '),
      //     h('span', {
      //       style: `color: ${colorprimary};font-weight: 400;`,
      //     }, frozenAmt),
      //     h('span', undefined, `${this.sdmName} `),
      //     h('div', {
      //       style: 'font-size: 14px; padding: 12px;background: #f5f6f8;margin-top:8px;',
      //     }, [
      //       h('span', undefined, `${this.sdmName}可用余额：`),
      //       h('span', {
      //         style: 'font-weight: 600;',
      //       }, ` ${balanceAmt} ${this.sdmUnit} `),
      //       h('span', {
      //         class: 'g-link-underline',
      //         on: {
      //           click: () => {
      //             this.handleRecharge()
      //           }
      //         }
      //       }, '充值 ')
      //     ]),
      //   ]),
      //   type: 'warning',
      //   customClass: 'myClass',
      //   showCancelButton: true,
      //   confirmButtonText: '确认订单',
      //   cancelButtonText: '暂不确认',
      //   beforeClose: (action, instance, done) => {
      //     if (action === 'confirm') {
      //       this.$emit('confirm', null, done)
      //     } else {
      //       this.$emit('close')
      //       done()
      //     }
      //   }
      // })
    },
    // 单个- 确认订单 确认 回调
    onConfirm() {
      const { sellerBankAccount } = this.$refs.CreditConfirmDialog
      this.$emit('confirm', sellerBankAccount)
    },
    // 显示米充值弹窗
    handleRecharge() {
      this.$refs.recharge.init()
    },

    marginReceiveSubmit() {
      const { sellerBankAccount } = this.$refs.MarginReceiveDialog
      this.$emit('confirm', sellerBankAccount)
    }

  }
}
</script>
