<!-- 置顶订单 -->
<style lang="scss" scoped>
.order-topping {
  display: inline;
}
</style>

<template>
  <el-tooltip
    content="同为待接单订单可批量刷新置顶"
    placement="top"
    :disabled="tooltipDisabled"
  >
    <div class="order-topping order-operation">
      <slot name="button">
        <el-button
          v-bind="$attrs"
          :type="$attrs.type || 'primary'"
          :border="!$attrs.type"
          :width="$attrs.width || '104'"
          :height="$attrs.height || '40'"
          :loading="loading"
          @click="init"
          v-on="$listeners"
        >
          <slot>批量置顶</slot>
        </el-button>
      </slot>
    </div>
  </el-tooltip>
</template>

<script>
import orderApi from '@/apis/order'
export default {
  name: 'order-topping',
  props: {
    order: {
      type: [Object, Array],
      required: true
    },
    tooltipDisabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
    }
  },
  computed: {
    // 是否批量
    hasAllOrder() {
      return this.order instanceof Array
    }
  },

  methods: {
    init() {
      if (this.order && Array.isArray(this.order) && this.order.length > 99) {
        this.$message.warning(`每次批量操作最多可选择 ${99} 笔订单`)
        return
      }

      this.$msgbox({
        title: '提示',
        dangerouslyUseHTMLString: true,
        message: '是否批量置顶订单？',
        confirmButtonText: '确认',
        showCancelButton: true,
        cancelButtonText: '再想想',
        type: 'warning',
      }).then(() => {
        this.postTopping(this.order)
      })
    },

    // 操作结果弹窗关闭回调
    closeResultDialog() {
      this.$emit('success')
    },

    // 置顶请求
    async postTopping(order) {
      try {
        this.loading = true
        const orderNos = this.hasAllOrder ? order.map(i => i.orderNo) : [order.orderNo]
        await orderApi.refreshOrderPublishTime({ orderNos })
        this.$message.success('已批量置顶订单')
        this.loading = false
        this.$emit('success')
      } catch (error) {
        this.loading = false
      }
    }
  }
}
</script>
