// 亿联银行订单状态校验结果
<style lang="scss" scoped>
.el-icon-loading,
.el-icon-error,
.el-icon-success {
  margin-right: 16px;
  font-size: 20px;
  line-height: 1em;
}

.el-icon-loading {
  color: #FFA941;
}

.el-icon-success {
  color: #37C187;
}

.el-icon-error {
  color: #F5222C;
}
</style>

<template>
  <i v-if="status === 'pending'" class="el-icon-loading" />
  <i v-else-if="status === 'passed'" class="el-icon-success" />
  <i v-else-if="status === 'failed'" class="el-icon-error" />
</template>

<script>
export default {
  name: 'yl-bank-check-status',
  props: {
    status: {
      type: String,
      default: 'pending'
    },
  },
}
</script>
