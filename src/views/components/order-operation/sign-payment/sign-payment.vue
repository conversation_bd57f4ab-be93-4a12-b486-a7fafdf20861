<!-- 签收解付 -->
<style lang="scss" scoped>
.sign-payment {
  display: inline-block;

  .el-button {
    position: relative;

    &.has-tag::after {
      content: "";
      position: absolute;
      top: -1px;
      left: -1px;
      border-top: 27px solid $color-assist3;
      border-right: 28px solid transparent;
      width: 0;
      height: 0;
    }

    .rice-tag {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      font-size: 12px;
      font-weight: 600;
      line-height: 20px;
      transform: scale(.8);
      transform-origin: top;
      color: $color-FFFFFF;
    }
  }
}
</style>

<template>
  <div class="sign-payment order-operation">
    <slot name="button">
      <el-button
        v-bind="$attrs"
        :type="$attrs.type || 'primary'"
        :class="hasTag && 'has-tag'"
        :width="$attrs.width || '104'"
        :height="$attrs.height || '40'"
        @click="init"
        v-on="$listeners"
      >
        <span v-if="hasTag" class="rice-tag">{{ sdmUnit }}</span>
        <slot>签收解付</slot>
      </el-button>
    </slot>
  </div>
</template>

<script>
import SignPaymentDialog from './sign-payment-dialog.vue'
import orderOperationMixin from '../order-operation-mixin'

export default {
  name: 'service-intervention',
  mixins: [orderOperationMixin(SignPaymentDialog)],
  props: {
    hasTag: [String, Number, Boolean]
  },
}
</script>
