<!-- eslint-disable max-lines -->
<!-- 签收解付弹窗 -->
<style lang="scss" scoped>
.sign-payment-dialog {
  ::v-deep {
    .el-form {
      background-color: $color-FFFFFF;
    }

    .el-form-item {
      margin-bottom: 0;
    }

    .el-form-item__error {
      position: static;
    }

    .el-input .el-input__clear {
      margin-right: 9px;
      font-size: 20px;
      color: $--color-primary;
    }

    .el-button--danger.is-border {
      font-size: 16px;
    }
  }
}

.header-flex {
  margin-bottom: 10px;

  @include flex-sbc;

  .title-left-border {
    margin-bottom: 0;
  }

  .text-btn {
    padding: 0;

    @include example-underline;
  }

  .smart-verify-tips {
    display: flex;
    align-items: center;
    padding-left: 16px;
    flex: 1;
  }
}

// 标题
.title-left-border {
  position: relative;
  margin-bottom: 10px;
  padding-left: 12px;
  height: 22px;
  font-size: 16px;
  font-weight: 600;
  line-height: 22px;

  &::before {
    position: absolute;
    top: 2px;
    left: 0;
    width: 4px;
    height: 16px;
    background: $--color-primary;
    content: "";
  }
}

// 红色高亮
.red-light-high {
  margin: 0 4px;
  font-weight: 600;
}

// 绿色高亮
.green-light-high {
  margin: 0 4px;
  font-weight: 600;
  color: $font-color;
}

.main {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;

  .batch-tips {
    margin-bottom: 12px;
    font-size: 16px;
  }

  .main-item {
    padding: 16px;
    width: 456px;
    background: $color-FFFFFF;
  }

  .order-info {
    margin-bottom: 12px;
    width: 100%;
  }

  .radio-group {
    display: flex;
    width: 100%;
  }

  .tips {
    margin-bottom: 8px;
    font-size: 16px;
    line-height: 24px;
  }

  .input-error ::v-deep {
    .el-input__inner {
      border: 1px solid $color-warning;
      background: $color-warning-sub;
    }
  }

  .input-error-msg {
    padding-top: 4px;
    font-size: 12px;
    color: $color-warning;
    line-height: 1;
  }
}

// 绿色按钮
.green-btn {
  border-bottom: 1px solid $font-color;
  height: 18px;
  font-size: 16px;
  color: $font-color;
  line-height: 18px;
  cursor: pointer;

  &:hover {
    color: $font-color-hover;
  }
}

// 复制按钮
.copy-btn {
  height: 18px;
  font-size: 16px;
  color: $font-color;
  line-height: 24px;
  cursor: pointer;

  &:hover {
    color: $font-color-hover;
  }
}

// 灰色标题
.gray-title {
  margin: 10px 0 2px;
  color: $color-text-secondary;
}

.gray {
  margin-bottom: 2px;
  color: $color-text-secondary;
}

// 验证码接收行
.get-code-box {
  ::v-deep .el-form-item__content {
    &::after {
      display: none;
    }

    &::before {
      display: none;
    }
  }

  .get-code-btn {
    float: right;
  }
}

.company-title {
  display: flex;
  justify-content: space-between;
  font-size: 16px;
  line-height: 24px;
}

.total-amount {
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
}

.el-table ::v-deep {
  .el-table__cell {
    padding: 8px 0;
    font-size: 14px;

    .cell {
      padding-right: 12px;
      padding-left: 12px;
      line-height: 20px;
    }
  }
}

.el-input {
  font-size: 16px;
}

.copy-icon {
  margin-top: 2px;
}

.red-high-light {
  margin: 0 4px;
  font-weight: bold;
  color: $--color-font-main;
}

.fail-text {
  line-height: 24px;
  margin-bottom: 8px;
}

.fail-content {
  overflow-y: auto;
  max-height: 480px;
}

.draft-no {
  margin-top: 8px;
}

.fail-item {
  display: flex;
  line-height: 24px;
  font-size: 16px;

  .value {
    flex: 1;
  }
}

.get-voice-code {
  @include example-underline;
}

.yl-bank-check-result {
  display: flex;
  align-items: center;
  margin-top: 12px;
  font-size: 16px;
}

.waring-cls {
  color: #EC3535 !important;
}

.waring-bold {
  font-weight: 600;
  color: #EC3535 !important;
}

::v-deep .risk-dialog {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  margin-top: 0 !important;

  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    background: $color-FFFFFF;
  }
}

.dialog-main {
  display: flex;
  margin-bottom: 24px;

  .icon {
    font-size: 24px;

    &.el-icon-warning {
      color: #FA8C16;
    }

    &.icon-error {
      color: #EC3535;
    }
  }

  .dialog-content {
    margin-left: 16px;
    flex: 1;
  }

  .label {
    min-width: 50px;
  }

  .title {
    margin-left: 8px;
    font-size: 18px;
    font-weight: bold;
    line-height: 24px;
  }

  .content {
    margin-top: 16px;
    font-size: 16px;
  }
}

.p-6 {
  margin-top: 6px;
  font-size: 16px;
}

.dialog-footer {
  margin-top: 16px;
}

.dialog-count-down {
  position: absolute;
  bottom: -30px;
  width: 100%;
  font-size: 18px;
  text-align: center;
  color: white;
}

.defect-setting-icon {
  margin: 0 4px;
  text-decoration: underline;
  color: #3070F6;
  cursor: pointer;
}

.close-icon {
  position: absolute;
  top: 16px;
  right: 16px;
  cursor: pointer;
  color: #BFBFBF;
}

.alert-title {
  display: flex;
}

.link-button {
  margin-left: 16px;
  text-decoration: underline;
  color: $color-008489;
  cursor: pointer;
  flex: auto;
}

.footer-btn {
  display: flex;
  justify-content: space-between;
}
</style>

<template>
  <div>
    <el-dialog
      width="964px"
      :visible.sync="visible"
      :title="title"
      class="sign-payment-dialog order-operation-dialog"
      :close-on-click-modal="false"
      :before-close="handleClose"
    >
      <div class="main">
        <template v-if="hasAllOrder">
          <WarnContent class="batch-tips">
            您正在对<span class="green-light-high">{{ tableData.length }}</span>笔订单进行批量签收解付操作，票面总金额<span class="green-light-high">{{ draftAmountTotal }}</span>万元。
          </WarnContent>
          <div class="order-info main-item">
            <div class="header-flex">
              <div class="title-left-border">订单信息</div>
              <el-button type="text" class="text-btn" @click="handleFillAll">一键填入</el-button>
            </div>
            <el-table
              id="tableBox"
              ref="tableRef"
              :data="tableData"
              border
              max-height="266"
            >
              <el-table-column label="票号" prop="draftNo" width="220">
                <div slot-scope="scope" style="display: flex; align-items: center;">
                  <span style="flex: 1;">{{ scope.row.draftNo }}</span>
                  <!-- <div v-if="scope.row.draftType"><span class="g-xinpiao">新票</span></div> -->
                </div>
              </el-table-column>
              <el-table-column label="对方" min-width="120">
                <template slot-scope="scope">
                  {{ scope.row.corpName }}
                  <SmartVerifyTicketTips v-if="scope.row.beforeSignDiscern" :order="scope.row" style="margin-left: 6px;" />
                  <Copy :content="scope.row.corpName" />
                </template>
              </el-table-column>
              <el-table-column label="背书企业名称" min-width="150">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.company"
                    placeholder="请输入背书企业名称"
                    :class="scope.row.errorMsg && 'input-error'"
                    @input="(val) => handleInputCompany(val, scope.$index, scope.row)"
                  />
                  <div v-if="scope.row.errorMsg" class="input-error-msg">{{ scope.row.errorMsg }}</div>
                </template>
              </el-table-column>
              <template v-if="hasYlBankPaymentChannel">
                <el-table-column v-if="!hasYlpayPaymentChannel" label="签收" width="70">
                  <template #default="{row}">
                    <YlBankCheckStatus :status="ylBankSignPaymentStatus[row.orderNo]" />
                  </template>
                </el-table-column>
                <el-table-column label="解付" width="70">
                  <template #default="{row}">
                    <YlBankCheckStatus :status="ylBankUnfreezeStatus[row.orderNo]" />
                  </template>
                </el-table-column>
              </template>
            </el-table>
          </div>
        </template>
        <!-- 单张签收确认显示票面信息 -->
        <template v-else>
          <WarnContent v-if="order.parentOrderNo" class="batch-tips">
            该笔为新票拆分订单，拆分交易票面金额<span class="red-light-high text-primary">{{ `${yuan2wan(order.draftAmount)}万元` }}</span>，请确认该金额与网银签收金额一致。
          </WarnContent>
          <div class="order-info main-item">
            <div class="header-flex">
              <div class="title-left-border">票面信息</div>
              <div class="smart-verify-tips">
                <icon type="chengjie-exclamation-circle1" :class="(order.beforeSignDiscern === 2 || !!order.existRisk) ? 'waring-cls' : ''" size="16" />
                <span>&nbsp;</span>
                <template v-if="order.beforeSignDiscern">
                  <span>点击查看</span>
                  <SmartVerifyTicketTips class="green-light-high" :order="order">「智能验票结果」</SmartVerifyTicketTips>
                </template>
                <template v-else>
                  <span>您可先通过</span>
                  <span class="green-light-high">「{{ discernName }}」</span>
                  <span>-</span>
                  <span class="green-light-high">「单张识别」</span>
                  <span>网银中的待签收票据，系统智能验票，瑕疵比对一目了然~</span>
                </template>
              </div>
            </div>
            <el-table
              id="tableBox"
              ref="tableRef"
              :data="ticketData"
              border
              max-height="266"
            >
              <el-table-column label="承兑人" prop="acceptorName" min-width="140" />
              <el-table-column label="票面金额(万)" align="right" prop="draftAmountWan" />
              <el-table-column label="到期日" align="right" prop="maturityDate" />
              <el-table-column label="票号" prop="draftNo" min-width="200">
                <div slot-scope="scope">
                  <span>{{ scope.row.draftNo }}</span>
                  <!-- <span v-if="order.draftType" class="g-xinpiao">新票</span> -->
                </div>
              </el-table-column>
            </el-table>
          </div>
        </template>
        <div class="left main-item">
          <el-form ref="form" :model="form" :rules="rules">
            <div class="title-left-border">签收信息</div>
            <template v-if="!hasAllOrder">
              <div v-if="[PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id, PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id].includes(order.paymentChannel)">
                <div class="gray-title">支付单号</div>
                <div v-if="order" class="company-title">
                  {{ order.businessSeqNo }}
                  <span v-copy="{ value: order.businessSeqNo, onSuccess, onError }" class="copy-btn">复制</span>
                </div>
              </div>

              <div class="gray-title">对方</div>
              <div v-if="order" class="company-title">
                {{ order.corpName || order.sellCorpName }}
                <span class="copy-btn" @click="onEnter(order.corpName || order.sellCorpName)">一键填入</span>
              </div>
              <div class="gray-title">背书企业名称</div>
              <el-form-item prop="company">
                <el-input
                  key="company"
                  v-model="form.company"
                  :height="40"
                />
              </el-form-item>
            </template>
            <!-- E++渠道隐藏 -->
            <template v-if="!hasYlBankPaymentChannel && !hasEPlusChannel">
              <div class="gray-title">接码手机号</div>
              <el-form-item prop="phone">
                <div class="get-code-box">
                  <el-input
                    key="phone"
                    :value="zfTradeMobile"
                    :width="304"
                    :height="40"
                    type="number"
                    :disabled="true"
                  />
                  <el-button
                    v-waiting="'post::loading::/user/updateCorpMemberMobile/sendPhoneVerifyCode'"
                    class="get-code-btn"
                    type="primary"
                    border
                    width="112"
                    height="40"
                    :disabled="!canGetCode"
                    @click="getCode(false)"
                  >
                    {{ canGetCode ? "获取验证码" : `还剩 ${leftSeconds}s` }}
                  </el-button>
                </div>
                <template v-if="getVoiceCodeVisible">
                  <span>收不到短信验证码？点击</span>
                  <span class="get-voice-code" @click="getCode(true)">获取语音验证码</span>
                </template>
              </el-form-item>
              <div class="gray-title">填写验证码</div>
              <el-form-item prop="validateCode">
                <el-input
                  key="validateCode"
                  v-model="form.validateCode"
                  :height="40"
                  type="number"
                  :number-format="{ maxLength: 6, decimal: false, negative: false }"
                />
              </el-form-item>
            </template>
          </el-form>
          <template v-if="hasYlBankPaymentChannel">
            <div v-if="!hasYlpayPaymentChannel" class="yl-bank-check-result">
              <YlBankCheckStatus :status="ylBankSignPaymentResult" />
              <div>签收确认校验</div>
            </div>
            <div class="yl-bank-check-result">
              <YlBankCheckStatus :status="ylBankUnfreezeResult" />
              <div>解付确认校验</div>
            </div>
          </template>
        </div>
        <div class="right main-item">
          <div class="title-left-border">风险提示</div>
          <div class="tips">
            1.在{{ hasYlBankPaymentChannel ? '智付E+网银' : hasEPlusChannel ? 'E++渠道企业网银' : '' }}
            签收票据和解付预付款前，请确认网银内的待签收或已签收票据的背书户与
            {{ hasAllOrder ? "" : '平台票方' }}
            <span class="red-light-high text-primary">{{ hasAllOrder ? '上述批量签收订单信息列表中的是否一致！' : `（ ${order && (order.corpName || order.sellCorpName)}）` }}</span>名称是否一致！
            若不一致请勿签收解付，<span class="red-light-high text-primary">否则签收后，因票款不对引起的法律风险请自行承担！</span><br>
            2.{{ hasYlBankPaymentChannel || hasEPlusChannel ? '请您在网银签收并解付后完成平台订单的签收解付确认，若您未在网银完成操作将无法进行平台订单的签收解付确认。超时未签收解付将记录您的违约责任。' : '' }}签收解付后，<span class="red-light-high text-primary">{{ order && draftPaymentAmount }}万元</span>冻结交易款立即解付至票方！如您网银未签收或网银签收复核后电票未在户请勿操作，否则带来的资金风险请自行承担!
          </div>
        </div>
      </div>
      <span slot="footer">
        <div class="footer-btn">
          <div>
            <!-- E++渠道 && 非爬虫方案 显示识单助手 -->
            <IdentificationAssistant
              v-if="hasEPlusChannel && !startCrawlerScheme"
              ref="identificationAssistantRefs"
              height="36"
              :order="order"
              :type="E_PLUS_RECOGNITION_SCENE.ORDER_SIGN.id"
              :check-data="checkData"
              @success="postSignPayment"
            />
          </div>
          <div>
            <el-button :disabled="loading" @click="handleClose">取消</el-button>
            <el-button
              v-waiting="['post::/draft/order/executeOrderTradeStep',
                          'post::/draft/order/singleExecuteOrderTradeStep']"
              type="primary"
              :disabled="hasYlBankPaymentChannel && !isYlBankCheckAllPassed"
              @click="confirm"
            >确定</el-button>
          </div>
        </div>
      </span>

      <LoadingDialog :visible="loading" title="批量签收中" content="正在批量签收中，请耐心等待..." />
      <ResultDialog ref="resultDialogRef" handle-str="签收" @close="closeResultDialog" />
      <el-dialog
        width="510px"
        :visible.sync="riskDialogVisible"
        :close-on-click-modal="false"
        :show-close="false"
        :append-to-body="true"
        :close-on-press-escape="false"
        custom-class="risk-dialog"
      >
        <div class="dialog-main">
          <div class="dialog-content">
            <div class="alert-title"><div :class="['icon', 'el-icon-warning', 'icon-error']" /><div class="title">风险提示</div></div>
            <slot name="content">
              <div class="content">「背书链有异常准入企业」「背书链有违反软件安全规则企业」的<span class="waring-bold">票据签收后不可发布</span>，如您继续签收，<span class="waring-bold">签收后将限制您的账户使用</span>。</div>
            </slot>
            <div class="fail-content">
              <slot name="text">
                <template v-for="item in riskList">
                  <div :key="item.orderNo" class="fail-item draft-no">
                    <div class="label">票号：</div>
                    <div class="value">{{ item.draftNo }}</div>
                  </div>
                  <div :key="`reason${item.draftNo}`" class="fail-item">
                    <div class="label">风险：</div>
                    <div class="value waring-bold">{{ item.riskName }}</div>
                  </div>
                </template>
              </slot>
            </div>
          </div>
        </div>
        <div class="dialog-footer">
          <slot name="footer">
            <el-button
              size="middle"
              :disabled="riskBtnCountDown > 0"
              @click="riskContinue"
            >
              继续签收{{ riskBtnCountDown ? `(${riskBtnCountDown}s)` : '' }}
            </el-button>
            <el-button
              type="primary"
              size="middle"
              @click="riskClose"
            >
              暂不签收
            </el-button>
          </slot>
        </div>
      </el-dialog>
    </el-dialog>
    <el-dialog
      width="520px"
      :visible.sync="discernDialog"
      :show-close="false"
      :append-to-body="true"
      custom-class="risk-dialog"
      :before-close="handleDiscernClose"
    >
      <div class="dialog-main">
        <div class="dialog-content">
          <div class="alert-title"><div :class="['icon', 'el-icon-warning']" /><div class="title">提示</div></div>
          <slot name="content">
            <div class="content">
              签收解付前必须使用
              <span class="waring-bold">【承接识票】</span>
              核验风险，请使用
              <span class="waring-bold">【单张识别】</span>
              识别票面后再来签收解付！
            </div>
            <div class="p-6">可先在【承接识票】点击<span class="defect-setting-icon" @click="$refs.supportBankDialogRef.open()"><icon type="chengjie-bank" style="vertical-align: bottom;" :size="24" />支持银行</span>功能查看支持的银行</div>
          </slot>
          <icon
            class="close-icon"
            type="chengjie-close"
            :size="20"
            @click="closeDiscern"
          />
          <div class="fail-content">
            <slot name="text">
              <div style="display: flex; margin-top: 8px; font-size: 16px;">
                <div class="label">票号：</div>
                <div>
                  <template v-for="item in discernList">
                    <div :key="item.orderNo">
                      <div class="value">{{ item.draftNo }}{{ item.draftType ? ` (${item.subTicketStart}-${item.subTicketEnd})` : '' }}</div>
                    </div>
                  </template>
                </div>
              </div>
            </slot>
          </div>
        </div>
      </div>
      <div class="dialog-footer">
        <div class="link-button" @click="jumpDiscern">跳过核验，直接签收</div>
        <slot name="footer">
          <el-button size="middle" @click="doneDiscern">已完成核验</el-button>
          <el-button
            type="primary"
            size="middle"
            @click="goDiscern"
          >
            去验票
          </el-button>
        </slot>
      </div>
      <!-- 银行弹窗 -->
      <SupportBankDialog ref="supportBankDialogRef" />
    </el-dialog>
  </div>
</template>

<script>
/* eslint-disable no-magic-numbers */

import { EXECUTE_TRANSACTION_PROCESS, PAYMENT_CHANNEL, E_PLUS_LOGIN_AUTH_KEY, E_PLUS_RECOGNITION_SCENE } from '@/constant'
import { yuan2wan } from '@/common/js/number'
import BigNumber from 'bignumber.js' // 交易凭证费用
import orderApi from '@/apis/order'// 订单接口
import Storage from '@/common/js/storage' // 本地缓存对象
import ResultDialog from '@/views/components/common/result-dialog/result-dialog.vue' // 批量操作结果组件
import { SEND_CODE_TIME } from '@/constant-storage' // 发送验证码倒计时key
import WarnContent from '@/views/components/common/warn-content.vue' // 警告文本组件
import Copy from '@/views/components/common/copy/copy.vue' // 复制组件
import LoadingDialog from '@/views/components/common/loading-dialog/loading-dialog.vue' // 加载中组件
import ylBankApi from '@/apis/yl-bank'
import YlBankCheckStatus from './yl-bank-check-status.vue'
import listenerEventMixins from '../listener-event.mixin'
import SmartVerifyTicketTips from '../smart-verify-ticket-tips/smart-verify-ticket-tips.vue'
import SupportBankDialog from '@recognize/components/support-bank/support-bank-dialog.vue'
import { OPEN_BANK_LOGIN_AUTH_DIALOG, OPEN_BANK_LOGIN_AUTH_SUCCESS } from '@/event/modules/site'
import { mapGetters } from 'vuex'
import IdentificationAssistant from '@/views/components/identification-assistant/index.vue'

// 亿联银行订单查询状态
const YLBANK_ORDER_CHECK_STATUS = {
  PENDING: 'pending', // 查询中
  PASSED: 'passed', // 已确认
  FAILED: 'failed', // 未通过
}
let sendCodeTimeKey = '' // 发送验证码倒计时key，操作名称拼订单号

export default {
  name: 'sign-payment-dialog',

  components: {
    WarnContent,
    Copy,
    LoadingDialog,
    ResultDialog,
    SmartVerifyTicketTips,
    YlBankCheckStatus,
    SupportBankDialog,
    IdentificationAssistant
  },
  mixins: [listenerEventMixins],

  data() {
    const validateCompany = (rule, value, callback) => {
      // 分别校验两种方式下的输入框
      if (value !== (this.order.corpName || this.order.sellCorpName)) {
        callback(new Error('请输入正确的背书企业名称'))
      } else {
        callback()
      }
    }
    return {
      form: { // 表单信息
        phone: '',
        validateCode: '',
        company: '',
      },
      PAYMENT_CHANNEL,
      E_PLUS_RECOGNITION_SCENE,
      order: null, // 当前订单对象
      visible: false, // 弹窗是否打开
      discernDialog: false, // 强制识票弹窗
      discernList: [], // 强制识票列表
      riskDialogVisible: false, // 风险提示弹窗
      riskBtnCountDown: 5, // 风险提示弹窗操作倒计时
      timer: null, // 计时器
      riskList: [], // 风险内容
      clock: null, // 定时器
      leftSeconds: 0, // 剩余秒数
      getVoiceCodeVisible: false, // 获取语音验证码 是否展示
      rules: {
        company: [
          {
            required: true,
            message: '请输入背书企业名称',
            trigger: ['change', 'blur']
          },
          {
            required: true,
            validator: validateCompany,
            message: '背书企业名称不正确',
            trigger: 'blur'
          }
        ],
        validateCode: [
          {
            required: true,
            pattern: /[0-9]{6}$/,
            message: '请填写6位验证码',
            trigger: ['change', 'blur']
          }
        ],
      },
      loading: false,
      tableData: [], // 订单信息表格数据
      ticketData: [], // 单个订单的票面数据，表格形式展示

      ylBankSignPaymentStatus: {}, // 查询亿联银行渠道订单签收状态
      ylBankUnfreezeStatus: {}, // 查询亿联银行渠道订单解付状态
    }
  },

  computed: {
    ...mapGetters('common', {
      startCrawlerScheme: 'startCrawlerScheme', // E++是否开启爬虫方案 1开启 0关闭
    }),
    // 是否批量
    hasAllOrder() {
      return this.order instanceof Array
    },

    limitReleaseInfo() { // 是否强制验票
      return this.$store.state.common.limitReleaseInfo
    },

    // 是否包含亿联银行渠道 (需要校验银行签收解付状态的渠道）
    hasYlBankPaymentChannel() {
      return (this.hasAllOrder ? this.order : [this.order]).some(order => order.paymentChannel === PAYMENT_CHANNEL.YI_LIAN_BANK.id || order.paymentChannel === PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id)
    },

    // 是否包含智付E+
    hasYlpayPaymentChannel() {
      return (this.hasAllOrder ? this.order : [this.order]).some(order => order.paymentChannel === PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id)
    },
    // 是否包含E++渠道
    hasEPlusChannel() {
      return (this.hasAllOrder ? this.order : [this.order]).some(order => order.paymentChannel === PAYMENT_CHANNEL.YL_PLUS.id)
    },

    // 标题
    title() {
      return this.hasAllOrder ? '批量签收解付' : '签收确认并解冻交易款'
    },

    // 接码手机号-留在jd的手机号，交易中发送验证码的
    zfTradeMobile() {
      return this.$store.state?.user?.corpInfo?.zfTradeMobile
    },

    // 到账金额
    draftPaymentAmount() {
      return this.getAmount(this.order, 'draftPaymentAmount')
    },

    // 票面总金额
    draftAmountTotal() {
      return this.getAmount(this.order, 'draftAmount')
    },

    // 是否可以获取验证码
    canGetCode() {
      return !(this.leftSeconds > 0)
    },

    // 亿联银行渠道订单签收状态查询结果
    ylBankSignPaymentResult() {
      const status = Object.values(this.ylBankSignPaymentStatus)

      if (status.some(item => item === YLBANK_ORDER_CHECK_STATUS.PENDING)) {
        return YLBANK_ORDER_CHECK_STATUS.PENDING
      }
      if (status.some(item => item === YLBANK_ORDER_CHECK_STATUS.FAILED)) {
        return YLBANK_ORDER_CHECK_STATUS.FAILED
      }
      return YLBANK_ORDER_CHECK_STATUS.PASSED
    },

    // 亿联银行渠道订单解付状态查询结果
    ylBankUnfreezeResult() {
      const status = Object.values(this.ylBankUnfreezeStatus)

      if (status.some(item => item === YLBANK_ORDER_CHECK_STATUS.PENDING)) {
        return YLBANK_ORDER_CHECK_STATUS.PENDING
      }
      if (status.some(item => item === YLBANK_ORDER_CHECK_STATUS.FAILED)) {
        return YLBANK_ORDER_CHECK_STATUS.FAILED
      }
      return YLBANK_ORDER_CHECK_STATUS.PASSED
    },

    // 亿联银行渠道校验是否通过
    isYlBankCheckAllPassed() {
      // 智付E+ 满足解付确认校验通过即可通过
      if (this.order.paymentChannel === PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id) {
        return this.ylBankUnfreezeResult === YLBANK_ORDER_CHECK_STATUS.PASSED
      } else {
        return this.ylBankSignPaymentResult === YLBANK_ORDER_CHECK_STATUS.PASSED && this.ylBankUnfreezeResult === YLBANK_ORDER_CHECK_STATUS.PASSED
      }
    },
    // 邦+批量签收解付限制订单笔数
    batchSignNum() {
      return this.$store.state.common.batchSignNum
    },
  },

  watch: {
    visible: {
      handler(val) {
        if (val) {
          sendCodeTimeKey = 'signPayment'
          if (this.hasAllOrder) {
            let orderNoList = this.order.map(i => i.orderNo)
            orderNoList = [...new Set(orderNoList)].sort() // 排序，避免顺序不同造成不同key
            sendCodeTimeKey = `${sendCodeTimeKey}_${orderNoList.join('_')}`
          } else {
            sendCodeTimeKey = `${sendCodeTimeKey}_${this.order.orderNo}`
          }
          this.leftSeconds = 0
          // 注册弹窗打开时，若上一次发送验证码的倒计时未结束，则继续开始倒计时
          if (val && Storage.get(SEND_CODE_TIME) && Storage.get(SEND_CODE_TIME)[sendCodeTimeKey]) {
            this.startCountDown()
          }
        }
      },
    }
  },

  methods: {
    yuan2wan,
    // 简易版网银登录成功回调 key=>参照枚举类型 E_PLUS_LOGIN_AUTH_KEY
    handleAuthSuccess(obj) {
      if (obj?.key === E_PLUS_LOGIN_AUTH_KEY.ORDER_SIGN) { // 订单签收
        this.postSignPayment()
      }
    },
    init() {
      // E++开启爬虫方案时,登录校验成功
      this.$event.on(OPEN_BANK_LOGIN_AUTH_SUCCESS, this.handleAuthSuccess)

      if (this.order && Array.isArray(this.order) && this.order.length > 99) {
        this.$message.warning(`每次批量操作最多可选择 ${99} 笔订单`)
        return
      }

      // 批量签收解付 智付邦+
      const isZbPayLen = this.hasAllOrder ? this.order.filter(e => e.paymentChannel === PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id).length : 0
      if (this.hasAllOrder && isZbPayLen > 0 && this.order.length !== isZbPayLen) {
        return this.$message.warning(`单次批量签收解付必须均为“${PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.name}”支付渠道`)
      }
      // 智付邦+批量签收解付订单笔数控制
      if (this.hasAllOrder && this.batchSignNum && isZbPayLen > this.batchSignNum) {
        return this.$message.warning(`最多支持${this.batchSignNum}笔智付邦+订单的批量签收解付`)
      }

      // 如果是批量支付 && 订单包含智联通订单 && 批量总数不等于智联通数量订单数量  判定韦先择的订单既包含智联通又包含京东订单
      const isYlPayLen = this.hasAllOrder ? this.order.filter(item => item.paymentChannel === PAYMENT_CHANNEL.YI_LIAN_BANK.id).length : 0
      if (this.hasAllOrder && isYlPayLen > 0 && this.order.length !== isYlPayLen) {
        this.$message.warning(`单次批量签收解付必须均为“${PAYMENT_CHANNEL.YI_LIAN_BANK.name}”支付渠道或是“京东智付”支付渠道`)
        return
      }

      // 强制智能验票
      if (this.limitReleaseInfo.signOrderForceCheckTicket) {
        const orders = this.hasAllOrder ? this.order : [this.order]
        this.discernList = orders.filter(item => !item.beforeSignDiscern)
        if (this.discernList.length > 0) {
          this.discernDialog = true
          return
        }
      }
      this.showDialog()
    },
    // 一键填入
    onEnter(val) {
      this.form.company = val
    },

    showDialog() {
      // 亿联银行状态查询
      if (this.hasYlBankPaymentChannel) {
        this.checkYlBankChannelOrderStatus()
      }
      this.tableData = this.hasAllOrder && JSON.parse(JSON.stringify(this.order))
      this.setTicketData()

      this.visible = true

      // 强制智能验票
      if (this.limitReleaseInfo.signOrderForceCheckTicket) {
        this.riskList.length = 0
        const orders = this.hasAllOrder ? this.order : [this.order]
        orderApi.getDiscernRiskByList({
          orderNoList: orders.map(order => order.orderNo),
        }).then(res => {
          const list = res.filter(item => !!item.abnormalAccessRisk || !!item.violateSoftwareSecurity)
          if (list.length > 0) {
            list.forEach(item => {
              const order = orders.filter(o => o.orderNo === item.orderNo)[0]
              if (order) {
                let { draftNo } = order
                if (order.draftType) {
                  draftNo = `${draftNo} (${order.subTicketStart}-${order.subTicketEnd})`
                }
                let riskName = ''
                if (item.abnormalAccessRisk) {
                  riskName = '背书链有异常准入企业'
                }
                if (item.violateSoftwareSecurity) {
                  if (riskName.length > 0) {
                    riskName = `${riskName}，`
                  }
                  riskName = `${riskName}背书链有违反软件安全规则企业`
                }
                this.riskList.push({ draftNo, riskName })
              }
            })
          }
          if (this.riskList.length > 0) {
            this.riskDialogVisible = true
            this.riskBtnCountDown = 5
            this.timer = setInterval(() => {
              if (this.riskBtnCountDown > 0) {
                this.riskBtnCountDown -= 1
              } else {
                clearInterval(this.timer)
              }
            }, 1000)
          }
        })
      }
    },

    // 单个订单-设置票面信息数据
    setTicketData() {
      if (!this.hasAllOrder) {
        const { acceptorName, draftAmount, maturityDate, draftNo } = this.order
        // 票据金额转为万
        let draftAmountWan = yuan2wan(draftAmount)
        let orderItem = {
          acceptorName,
          draftAmountWan,
          maturityDate,
          draftNo

        }
        this.ticketData = [orderItem]
      }
    },

    // 根据key值计算金额总额
    getAmount(order, key) {
      let amount = 0
      if (this.hasAllOrder) {
        for (let item of order) {
          amount = new BigNumber(item[key]).plus(amount)
        }
      } else {
        amount = new BigNumber(order[key])
      }
      return yuan2wan(amount)
    },

    // 亿联银行渠道订单状态校验
    async checkYlBankChannelOrderStatus() {
      // 银行侧签收状态
      const YL_BANK_SIGN_STATUS = {
        UNKNOWN: '00',
        WAITING: '01',
        CONFIRM: '02',
        REJECT: '03'
      }
      // 业务解付状态
      const FREEZE_STATUS = {
        WAITING: '0',
        FAILED: '1',
        PASSED: '2'
      }
      const orders = this.hasAllOrder ? this.order : [this.order]
      for (let order of orders) {
        this.$set(this.ylBankSignPaymentStatus, order.orderNo, YLBANK_ORDER_CHECK_STATUS.PENDING)
        this.$set(this.ylBankUnfreezeStatus, order.orderNo, YLBANK_ORDER_CHECK_STATUS.PENDING)
      }
      const result = await ylBankApi.isSignPaymentSuccess({ orderNos: orders.map(order => order.orderNo) })
      for (let item of result) {
        this.ylBankSignPaymentStatus[item.orderNo] = item.draftStatus === YL_BANK_SIGN_STATUS.CONFIRM ? YLBANK_ORDER_CHECK_STATUS.PASSED : YLBANK_ORDER_CHECK_STATUS.FAILED
        this.ylBankUnfreezeStatus[item.orderNo] = item.orderStatus === FREEZE_STATUS.PASSED ? YLBANK_ORDER_CHECK_STATUS.PASSED : YLBANK_ORDER_CHECK_STATUS.FAILED
      }
    },

    // 批量操作背书账号校验
    checkCompany() {
      let flag = true
      let index = '' // 第一个出现错误的下标
      for (let i = 0; i < this.tableData.length; i++) {
        if (!this.tableData[i].company) {
          flag = false
          index === '' && (index = i)
          this.$set(this.tableData[i], 'errorMsg', '请输入背书企业名称')
          continue
        }
        if (this.tableData[i].company !== (this.tableData[i].corpName || this.tableData[i].sellCorpName)) {
          flag = false
          index === '' && (index = i)
          this.$set(this.tableData[i], 'errorMsg', '请输入正确的背书企业名称')
          continue
        }
      }
      // 表格有错误时滚动至错误行
      this.$nextTick().then(() => {
        // 只有数据多于3条才操作
        // eslint-disable-next-line no-magic-numbers
        if (index !== '' && this.tableData.length > 3) {
          const rowList = document.getElementById('tableBox').querySelectorAll('.el-table__row')
          let scrollTop = 0
          for (let i = 0; i < rowList.length; i++) {
            if (i < index) {
              scrollTop = scrollTop + rowList[i].scrollHeight
            }
          }
          this.$refs.tableRef.bodyWrapper.scrollTop = scrollTop
        }
      })

      return flag
    },

    // 确定签收解付
    async confirm() {
      try {
        if (this.hasAllOrder && !this.checkCompany()) {
          return
        }
        await this.$refs.form.validate()
        // E++渠道 开启爬虫模式 需校验简易版网银登录
        if (this.startCrawlerScheme && this.hasEPlusChannel) {
          this.$event.emit(OPEN_BANK_LOGIN_AUTH_DIALOG, { key: E_PLUS_LOGIN_AUTH_KEY.ORDER_SIGN })
        } else {
          this.postSignPayment()
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log(error)
      }
    },

    // 复制成功
    onSuccess() {
      this.$message.success('复制成功')
    },

    // 复制失败
    onError() {
      this.$message.error('复制失败，请重试')
    },

    // 发起签收解付
    async postSignPayment() {
      const orderNoList = this.hasAllOrder ? this.order.map(i => i.orderNo) : [this.order.orderNo]
      const param = {
        orderNoList, // 订单编号,必填字段不可为空
        orderStepsEnumCode: EXECUTE_TRANSACTION_PROCESS.SIGN_PAYMENT, // 必填字段,订单步骤
        phone: this.form.phone, // 手机号
        validateCode: this.form.validateCode // 手机验证码
      }
      try {
        if (this.hasAllOrder) {
          param.orderStepsEnumCode && delete param.orderStepsEnumCode
          param.phone && delete param.phone
          this.loading = true
          const data = await orderApi.batchSign(param)
          this.loading = false
          // 批量操作结果提示
          this.$refs.resultDialogRef.init(data)
        } else {
          await orderApi.postSingleExecuteOrderTradeStep(param)
          this.$message({
            message: '签收解付成功',
            type: 'success'
          })
          this.$emit('success')
        }
        this.clearTime()
        this.$refs.form && this.$refs.form.resetFields()
        this.handleClose()
      } catch (error) {
        this.loading = false
      }
    },

    // 获取验证码
    async getCode(isVoice) {
      const TEXT_CODE_TYPE = 1
      const VOICE_CODE_TYPE = 2
      if (!this.canGetCode) {
        return
      }
      if (this.hasAllOrder && !this.checkCompany()) {
        return
      }
      if (!this.hasAllOrder) {
        let errorMsg = ''
        await this.$refs.form.validateField('company', error => {
          errorMsg = error
        })
        if (errorMsg) {
          return
        }
      }
      try {
        if (this.hasAllOrder) {
          await orderApi.postAllSignPaymentVerifyCode({
            orderNoList: this.order.map(i => i.orderNo),
            verifyCodeType: isVoice ? VOICE_CODE_TYPE : TEXT_CODE_TYPE
          })
        } else {
          await orderApi.getSignPaymentVerifyCode({
            orderNo: this.order.orderNo,
            verifyCodeType: isVoice ? VOICE_CODE_TYPE : TEXT_CODE_TYPE
          })
        }
        this.$message({
          message: `${isVoice ? '语音' : '短信'}验证码已发送，请注意查收`,
          type: 'success'
        })
        this.startCountDown()
      } catch (e) {
        // eslint-disable-next-line no-console
        console.log('e :>> ', e)
      }
    },

    // 开始倒计时
    startCountDown() {
      let nowTime = new Date().getTime() // 当前时间
      let sendCodeTime = Storage.get(SEND_CODE_TIME) || '' // 所有发送验证码时间的本地缓存对象
      let lastTime = sendCodeTime && sendCodeTime[sendCodeTimeKey] ? sendCodeTime[sendCodeTimeKey] : null // 上一次发送验证码时间
      let durationTime = 60 // 倒计时时间

      if (sendCodeTime) {
        // 上一次发送验证码的倒计时未结束
        if (lastTime) {
          durationTime = durationTime - Math.round(((nowTime - lastTime) / 1000)) // 计算还剩多少秒倒计时
        } else {
          sendCodeTime[sendCodeTimeKey] = nowTime
          Storage.set(SEND_CODE_TIME, sendCodeTime) // 本次的发送验证码倒计时添加入缓存对象
        }
      } else {
        sendCodeTime = {
          [sendCodeTimeKey]: nowTime
        }
        // 保存这次发送验证码的时间
        Storage.set(SEND_CODE_TIME, sendCodeTime)
      }

      this.leftSeconds = durationTime
      const countDown = () => {
        this.clock = setTimeout(() => {
          this.leftSeconds -= 1
          if (this.leftSeconds <= 0) {
            this.getVoiceCodeVisible = true
            sendCodeTime[sendCodeTimeKey] = null // 倒计时结束，清掉获取验证码倒计时缓存
            Storage.set(SEND_CODE_TIME, sendCodeTime)
          } else {
            countDown()
          }
        }, 1000)
      }
      countDown()
    },

    // 清除定时器
    clearTime() {
      clearTimeout(this.clock) // 清除
      this.clock = null
      // this.canGetCode = true
    },

    // 关闭之前
    handleClose() {
      this.clearTime()
      this.$refs.form && this.$refs.form.resetFields()
      this.$event.off(OPEN_BANK_LOGIN_AUTH_SUCCESS, this.handleAuthSuccess)
      this.visible = false
      !this.hasAllOrder && this.$emit('destroy')
      this.loading = false
    },

    riskClose() {
      this.riskDialogVisible = false
      clearInterval(this.timer)
      this.handleClose()
    },

    goDiscern() {
      if (this.$ipc && typeof this.$ipc.send === 'function') {
        this.$ipc.send('OPEN_WINDOW_RECOGNIZE_HELPER')
      } else {
        this.$message.info('请在ERP客户端内使用此功能，您可联系客服获取安装包。')
      }
    },

    handleDiscernClose() {
      this.closeDiscern()
    },

    doneDiscern() {
      orderApi.getDiscernRecordByList({
        orderNoList: this.discernList.map(order => order.orderNo),
      }).then(res => {
        if (this.hasAllOrder) {
          res.forEach(data => {
            this.order.forEach(item => {
              if (item.orderNo === data.orderNo) {
                item.beforeSignDiscern = data.beforeSignDiscern
                item.existRisk = data.existRisk
              }
            })
          })
        } else {
          this.order.beforeSignDiscern = res[0].beforeSignDiscern
          this.order.existRisk = res[0].existRisk
        }
        const notList = res.filter(item => !item.existDiscernRecord)
        if (notList.length > 0) {
          this.discernList = this.discernList.filter(item => notList.some(notItem => item.orderNo === notItem.orderNo))
          if (this.discernList.length > 0) {
            this.$message.error('未检测到此票的识别记录')
            return
          }
        }
        this.discernDialog = false
        this.showDialog()
      })
    },

    jumpDiscern() {
      this.discernDialog = false
      this.showDialog()
    },

    closeDiscern() {
      this.discernDialog = false
    },

    riskContinue() {
      this.riskDialogVisible = false
    },

    // 批量操作背书企业输入
    handleInputCompany(val, index, row) {
      this.tableData[index].errorMsg = val !== (row.corpName || row.sellCorpName) ? '请输入正确的背书企业名称' : ''
    },

    // 一键填入
    handleFillAll() {
      this.tableData.forEach((item, index) => {
        item.company = item.corpName
        item.errorMsg && (item.errorMsg = '')
        this.$set(this.tableData, index, { ...item })
      })
    },

    // 批量操作结果弹窗关闭回调
    closeResultDialog() {
      this.$emit('success')
      this.$emit('destroy')
    },

    // 校验父组件必填数据
    async checkData() {
      let status = await this.$refs.form.validate()
      if (this.hasAllOrder && !this.checkCompany()) {
        status = false
      }
      return status
    }
  },
}
</script>
