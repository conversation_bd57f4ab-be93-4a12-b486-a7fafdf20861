<!-- 改价 -->
<style lang="scss" scoped>
.change-price-account {
  display: inline-block;

  .el-button {
    position: relative;

    &.has-tag::after {
      position: absolute;
      top: -1px;
      left: -1px;
      border: 14px solid $color-market-mi-tag;
      border-color: $color-warning transparent transparent $color-warning;
      width: 0;
      height: 0;
      content: "";
      border-top-left-radius: 2px;
    }

    .rice-tag {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      font-size: 12px;
      font-weight: 600;
      line-height: 20px;
      transform: scale(.8);
      transform-origin: top;
      color: $color-FFFFFF;
    }
  }
}

// ::v-deep {
//   .el-button--primary.is-border {
//     border-color: $--color-primary;
//     color: $--color-primary;
//   }
// }
</style>

<template>
  <el-tooltip
    content="同为定向票可批量改价"
    placement="top"
    :disabled="tooltipDisabled"
    popper-style="max-width: 164px;"
  >
    <div class="change-price-account order-operation">
      <slot name="button">
        <el-button
          v-bind="$attrs"
          :type="$attrs.type || 'primary'"
          :border="!$attrs.type"
          :class="hasTag && 'has-tag'"
          :height="$attrs.height || '40'"
          :width="$attrs.width || '68'"
          @click="init"
          v-on="$listeners"
        >
          <span v-if="hasTag" class="rice-tag">{{ sdmUnit }}</span>
          <slot>改价</slot>
        </el-button>
      </slot>
    </div>
  </el-tooltip>
</template>

<script>
import ChangePriceDialog from './change-price-dialog.vue'
import orderOperationMixin from '../order-operation-mixin'

export default {
  name: 'change-price-account',
  mixins: [orderOperationMixin(ChangePriceDialog)],
  props: {
    hasTag: [String, Number, Boolean],
    tooltipDisabled: {
      type: Boolean,
      default: true
    }
  },
}
</script>
