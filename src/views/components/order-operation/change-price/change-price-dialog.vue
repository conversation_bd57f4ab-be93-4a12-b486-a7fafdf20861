<!-- 改价弹窗 -->
<style lang="scss" scoped>
.change-price-account-dialog {
  .w-item {
    width: 357px;
  }

  // .el-form {
  //   width: 357px;
  // }

  ::v-deep {
    .el-form-item {
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .el-form-item__label {
      line-height: 22px;
      padding-bottom: 2px;
      color: $color-text-secondary;
    }

    .el-form-item__error {
      position: static;
    }

    .el-input .el-input__clear {
      margin-right: 9px;
      font-size: 20px;
      color: $--color-primary;
    }
  }

  .main {
    padding: 16px;
    background: $color-FFFFFF;
  }
}

.radio-group {
  display: flex;

  .el-radio {
    margin-right: 0;
    padding: 0;
    text-align: center;
    flex: 1;

    @include flex-cc;

    &.is-checked {
      background: $--color-primary-hover;

      ::v-deep .el-radio__label {
        color: $--color-primary;
      }
    }

    ::v-deep .el-radio__label {
      font-size: 16px;
      color: $color-text-primary;
    }
  }
}

.add-radio {
  width: 173px;
}

.add-group {
  .lable {
    height: 24px;
    color: $color-text-secondary;
    line-height: 24px;
  }
}

.mb-10 {
  margin-bottom: 10px;
}

.header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2px;
}

.type-input-box {
  display: flex;

  .input-left,
  .input-right {
    flex: 1;
  }

  .icon-add {
    padding: 0 5px;
  }
}

.type-switch-box {
  display: flex;

  .switch-left,
  .switch-right {
    flex: 0 1 auto;
  }

  .switch-left {
    margin-top: 10px;
    width: 60px;
  }

  .switch-middle {
    width: 100px;
  }

  .switch-right {
    width: 240px;
  }
}

.el-input {
  font-size: 16px;
}

// 字体加粗
.font-bold {
  font-size: 16px;
  line-height: 24px;
  font-weight: 600;
}

.tips {
  margin-top: 10px;
  font-size: 12px;
  color: $color-warning;
}

.body {
  display: flex;

  .left {
    flex: 1;
    padding-right: 50px;
  }

  .left-broder {
    border-right: 1px solid $color-text-light;
  }

  .right {
    margin-left: 10px;
    padding-left: 10px;
    width: 50%;

    // 灰色标题
    .gray {
      margin-bottom: 2px;
      color: $color-text-secondary;
    }

    .item-detail {
      margin-bottom: 8px;
      font-size: 16px;
      color: $color-text-primary;
      line-height: 24px;
    }

    .key-text {
      color: $color-warning;
    }
  }
}

.flex-center {
  display: flex;
  align-items: center;

  .input {
    margin-left: 8px;
    width: 120px;
  }
}
</style>

<template>
  <el-dialog
    :width="hasAllOrder ? '1020px' : '729px'"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :title="hasAllOrder ? '批量改价' : '改价'"
    class="change-price-account-dialog order-operation-dialog"
    :before-close="handleClose"
  >
    <div class="main">
      <div class="body">
        <div :class="hasAllOrder ? 'left ' : 'left left-broder'">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-position="top"
          >
            <el-radio-group v-if="hasAllOrder" v-model="form.updatePriceType" class="mb-10 w-item">
              <el-radio-button v-for="item in priceQuoteOption" :key="item.value" :label="item.value">{{ item.label }}</el-radio-button>
            </el-radio-group>

            <template v-if="form.updatePriceType === 2 && hasAllOrder">
              <el-form-item label="加价方式">
                <div class="radio-group add-radio">
                  <el-radio
                    v-for="item in addPriceOption"
                    :key="item.value"
                    v-model="form.updatePriceType"
                    type="button"
                    :label="item.value"
                  >
                    {{ item.label }}
                  </el-radio>
                </div>
              </el-form-item>
              <div class="add-group w-item">
                <div class="lable">加价</div>
                <div class="flex-center">
                  <el-button
                    v-for="item in raisePriceOption"
                    :key="item.value"
                    type="button"
                    @click="onBatchRaisePrice(item.value)"
                  >
                    {{ item.label }}
                  </el-button>
                  <el-input
                    v-model="form.customAddLakhDeduction"
                    :number-format="addLakhNumberFormat"
                    type="number"
                    class="input"
                    placeholder="自定义"
                    :height="40"
                    @input="handelCustomAddLakhDeduction"
                  >
                    <template slot="append">元</template>
                  </el-input>
                </div>
              </div>
            </template>
            <template v-else>
              <el-form-item label="报价方式" class="w-item">
                <div class="radio-group">
                  <el-radio
                    v-for="item in changeTypeOption"
                    :key="item.value"
                    v-model="form.billingMethod"
                    type="button"
                    :label="item.value"
                  >
                    {{ item.label }}
                  </el-radio>
                </div>
              </el-form-item>
              <el-form-item label="报价" prop="input" class="w-item">
                <template v-if="form.billingMethod">
                  <div class="type-input-box">
                    <el-input
                      key="annualInterest"
                      v-model="form.annualInterest"
                      :number-format="annualInterestNumberFormat"
                      type="number"
                      placeholder="利率"
                      class="input-left"
                      :height="40"
                      @input="(val) => handleInput(val, 'annualInterest')"
                    >
                      <template slot="append">%</template>
                    </el-input>
                    <span class="icon-add">+</span>
                    <el-input
                      key="serviceCharge"
                      v-model="form.serviceCharge"
                      :number-format="yuanNumberFormat"
                      type="number"
                      placeholder="每十万手续费"
                      class="input-right"
                      :height="40"
                      @input="(val) => handleInput(val, 'serviceCharge')"
                    >
                      <template slot="append">元</template>
                    </el-input>
                  </div>
                </template>
                <template v-else>
                  <el-input
                    key="lakhDeduction"
                    v-model="form.lakhDeduction"
                    type="number"
                    :number-format="yuanNumberFormat"
                    placeholder="每十万扣款"
                    :height="40"
                    @input="(val) => handleInput(val, 'lakhDeduction')"
                  >
                    <template slot="append">元</template>
                  </el-input>
                </template>
              </el-form-item>

              <el-form-item v-if="!isShowTable" label="改价后总到账金额" class="w-item">
                <div v-if="order" class="font-bold">
                  {{ priceChangeAmount }} 万元
                </div>
              </el-form-item>
              <div v-if="!hasAllOrder" class="add-group mb-10 w-item">
                <div class="lable">加价</div>
                <div class="flex-center">
                  <el-button
                    v-for="item in raisePriceOption"
                    :key="item.value"
                    type="button"
                    @click="onRaisePrice(item.value)"
                  >
                    {{ item.label }}
                  </el-button>
                  <el-input
                    v-model="form.customAddLakhDeduction"
                    :number-format="addLakhNumberFormat"
                    type="number"
                    class="input"
                    placeholder="自定义"
                    :height="40"
                    @input="handelCustomAddLakhDeduction"
                  >
                    <template slot="append">元</template>
                  </el-input>
                </div>
              </div>
              <el-form-item
                v-if="!hasAllOrder && !agentOrder"
                label="议价设置"
                prop="bargainingLimit"
                class="w-item"
              >
                <div class="type-switch-box">
                  <el-switch
                    v-model="form.bargaining"
                    class="switch-left"
                    :active-value="1"
                    :inactive-value="0"
                  />
                  <div class="switch-middle">
                    {{ !form.bargaining ? "不" : "" }}接受议价
                  </div>
                  <el-input
                    v-if="form.bargaining"
                    key="bargainingLimit"
                    v-model="form.bargainingLimit"
                    :number-format="yuanNumberFormat"
                    type="number"
                    placeholder="请输入议价上限"
                    class="switch-right"
                    :height="40"
                  >
                    <template slot="append">元</template>
                  </el-input>
                </div>
              </el-form-item>
              <div v-if="isNoSame" class="tips">
                以下票据存在承兑人、票面金额、到期日不一致的情况，请谨慎批量改价。
              </div>
            </template>
            <!-- 回款账户 -->
            <div v-if="hasAllOrder" class="sellerBankAccount-cls">
              <el-form-item class="w-item">
                <SellerBankAccount
                  v-model="form.sellerBankAccountId"
                  :required="false"
                  size="medium"
                  class="seller-bank"
                  scene="custom"
                  label-position="top"
                  :icon-position="1"
                  @click-jump="() => { visible = false }"
                />
              </el-form-item>
              <div class="txt">{{ tipText }}</div>
            </div>
          </el-form>
        </div>
        <div v-if="!hasAllOrder" class="right">
          <div class="gray">每十万扣款（元）</div>
          <div class="item-detail font-bold">
            <template
              v-if="tableData[0].lakhFeeAfter || tableData[0].lakhFeeAfter"
            >
              {{ tableData[0].lakhFeeAfter || "-" }}<br>
            </template>
            <template v-else>-</template>
          </div>
          <div class="gray">利率</div>
          <div class="item-detail font-bold">
            {{ tableData[0].annualInterestAfter || 0 }}%
          </div>
          <div class="gray">到账金额（万）</div>
          <div class="item-detail font-bold key-text">
            {{ tableData[0].receivedAmountStr }}
          </div>
        </div>
      </div>
    </div>
    <TableList
      v-if="isShowTable && hasAllOrder"
      :key="tableKey"
      :table-data="tableData"
      :draft-amount-total="draftAmountTotal"
      :received-amount-total="receivedAmountTotal"
    />
    <span slot="footer">
      <el-button :disabled="loading" @click="handleClose">取消</el-button>
      <el-button
        v-waiting="[
          `post::/draft/order/executeOrderTradeStep`,
          `post::/draft/order/singleExecuteOrderTradeStep`,
        ]"
        type="primary"
        @click="confirm"
      >确定</el-button>
    </span>
    <!-- 批量操作loading -->
    <LoadingDialog
      :visible="batchLoading"
      title="批量改价中"
      content="正在批量改价中，请耐心等待..."
    />
    <!-- 批量操作结果 -->
    <ResultDialog
      ref="resultDialogRef"
      handle-str="改价"
      @close="closeResultDialog"
    />
  </el-dialog>
</template>

<script>
import BigNumber from 'bignumber.js'
import orderApi from '@/apis/order'
import {
  interestRateMath, // * 以利率计算 => 每十万扣款 年化利率 和 到账金额
  lakhDeductionMath, // * 每十万扣款计算 => 年化利率 和 到账金额
} from '@/common/js/draft-math'
import TableList from './components/table-list.vue' // 表格组件
import ResultDialog from '@/views/components/common/result-dialog/result-dialog.vue' // 批量操作结果组件
import {
  parseNum, // 添加逗号
  yuan2wan, // 元转万
} from '@/common/js/number'
import { BILLING_METHOD_CODE } from '@/constant' // 报价方式
import LoadingDialog from '@/views/components/common/loading-dialog/loading-dialog.vue' // 加载中组件
import { debounce } from '@/common/js/util'
import { ISSUE_DRAFT_ERROR_CODE } from '@/constants/draft'
import { windowCommunication } from '@/utils/window-event'
import SellerBankAccount from '@/views/components/seller-bank-account/seller-bank-account.vue'

export default {
  name: 'change-price-account-dialog',
  components: {
    TableList,
    ResultDialog,
    LoadingDialog,
    SellerBankAccount
  },
  data() {
    const validate = (rule, value, callback) => {
      // 批量改价 且 回款账户变更过 直接过校验
      if (this.hasAllOrder && this.currentSellerBankAccountId !== this.form.sellerBankAccountId) {
        callback()
        return
      }
      // 分别校验两种方式下的输入框
      if (this.priceChangeAmount === '-') {
        callback(new Error('请填入报价'))
      } else if (this.form.annualInterest === 0) {
        callback(new Error('报价不能为0'))
      } else {
        callback()
      }
    }
    return {
      // 年利率为单位的输入框格式
      annualInterestNumberFormat: {
        maxIntegerLength: 2,
        maxDecimalLength: 4,
        negative: false,
        leadingZero: false,
      },
      // 元为单位的输入框格式
      yuanNumberFormat: {
        maxIntegerLength: 5,
        maxDecimalLength: 2,
        negative: false,
        leadingZero: false,
      },
      // 加价输入框格式
      addLakhNumberFormat: {
        maxIntegerLength: 2,
        maxLength: 2,
        decimal: false,
        negative: false,
        leadingZero: false,
      },
      order: null, // 当前操作的订单
      form: {
        billingMethod: BILLING_METHOD_CODE.SHI_WAN_DISCOUNT, // 计费方式0-十万直扣1-年利率加手续费
        addLakhDeduction: null, // 加价每十万金额
        updatePriceType: 1, // 改价类型 1 普通  2 加价
        annualInterest: null, // 年利率
        serviceCharge: null, // 手续费
        lakhDeduction: null, // 每十万扣款
        bargaining: 0, // 是否接受议价，0-否、1-是
        bargainingLimit: null, // 议价上限值
        customAddLakhDeduction: null, // 自定义加价
        sellerBankAccountId: null // 回款账户ID
      },
      // 报价方式
      priceQuoteOption: [
        {
          value: 1,
          label: '按报价方式'
        },
        {
          value: 2,
          label: '按加价方式'
        }
      ],
      // 加价方式
      addPriceOption: [
        {
          value: 2,
          label: '批量加价'
        },
      ],
      // 改价相关入参 ,UpdateQuotedPriceRequest
      changeTypeOption: [ // 报价类型选项
        {
          value: 0,
          label: '每十万扣款'
        },
        {
          value: 1,
          label: '利率+每十万手续费'
        }
      ],
      raisePriceOption: [ // 加价类型选项
        {
          value: 5,
          label: '+5元'
        },
        {
          value: 10,
          label: '+10元'
        },
        {
          value: 20,
          label: '+20元'
        },
      ],
      rules: { // 表单校验规则
        input: [{ validator: validate, trigger: 'blur' }],
      },
      visible: false, // 弹窗是否打开
      loading: false,
      tableData: [{}], // 表格数据
      draftAmountTotal: '', // 票面总金额 在表格头部显示
      receivedAmountTotal: '', // 到账金额 在表格头部显示
      tableKey: 0, // 用于触发表格组件props更新
      batchLoading: false, // 批量loading
      agentOrder: 0, // 是否是定向票  0 否 1 是
      tipText: '', // 回款账户提示文本
      currentSellerBankAccountId: null // 记录当前回显的回款账户 校验回款账户是否变更过
    }
  },

  computed: {
    // 是否批量
    hasAllOrder() {
      return this.order instanceof Array
    },

    // 改价后到账金额 this.form.billingMethod 0-每十万扣款 1-以利率计算
    priceChangeAmount() {
      const { order, hasAllOrder, form: { billingMethod, annualInterest, serviceCharge, lakhDeduction } } = this
      // 如果是单个改价转为跟批量改价计算
      const orderList = hasAllOrder ? order : [order]
      if ((billingMethod && !annualInterest && !serviceCharge) || (!lakhDeduction && !billingMethod)) {
        return '-'
      }
      let receivedAmount = 0.00
      if (billingMethod) {
        orderList.forEach(element => {
          receivedAmount = new BigNumber(interestRateMath(element.draftAmount, annualInterest, serviceCharge, element.interestDays).receivedAmount).plus(receivedAmount)
        })
      } else {
        orderList.forEach(element => {
          receivedAmount = new BigNumber(lakhDeductionMath(element.draftAmount, lakhDeduction, element.interestDays).receivedAmount).plus(receivedAmount)
        })
      }
      return yuan2wan(receivedAmount, { digits: 6, parseNumber: false, mode: 'round' })
    },

    // 回价后到账金额
    returnAmount() {
      return ''
    },

    // 批量操作是否存在承兑人、票面金额、到期日不一致的情况
    isNoSame() {
      let flag = false
      if (this.hasAllOrder) {
        const map = new Map()
        for (let i = 0; i < this.order.length; i++) {
          const item = this.order[i]
          if (!map.has('acceptorName')
            && !map.has('draftAmount')
            && !map.has('maturityDate')) {
            map.set('acceptorName', item.acceptorName)
              .set('draftAmount', item.draftAmount)
              .set('maturityDate', item.maturityDate)
          }
          if (map.get('acceptorName') !== item.acceptorName
            || map.get('draftAmount') !== item.draftAmount
            || map.get('maturityDate') !== item.maturityDate) {
            flag = true
            break
          }
        }
      }
      return flag
    },

    // 是否显示表格
    isShowTable() {
      return true // 改为全部使用批量样式
    }
  },

  watch: {
    // 监听表单改动
    form: {
      handler() {
        this.isShowTable && this.getTableData()
      },
      deep: true
    },
    // 切换报价方式时清空表单校验
    'form.billingMethod'() {
      this.form.customAddLakhDeduction = null
      this.$refs.form && this.$refs.form.clearValidate()
    },
    // 监听回款账户 当回款账户变更时 清空表单校验
    'form.sellerBankAccountId'(val) {
      if (val !== this.currentSellerBankAccountId) {
        this.$refs.form && this.$refs.form.clearValidate()
      }
    }
  },

  created() {
    // 获取票号,加防抖防止多次请求
    this.debounceGetDetail = debounce(async(item, index) => {
      const data = await orderApi.getTraderCorpOrderInfo(item.orderNo)
      this.$set(this.tableData[index], 'lastSixDraftNo', data.draftNo.slice(-6))
    }, 0)
  },

  methods: {
    init() {
      if (this.order && Array.isArray(this.order) && this.order.length > 99) {
        this.$message.warning(`每次批量操作最多可选择 ${99} 笔订单`)
        return
      }
      this.agentOrder = this.order.agentOrder
      this.visible = true
      const { annualInterest, lakhFee, billingMethod, bargaining, bargainingLimit } = this.order
      this.form.billingMethod = billingMethod || BILLING_METHOD_CODE.SHI_WAN_DISCOUNT
      this.form.annualInterest = annualInterest
      this.form.lakhDeduction = lakhFee
      this.form.bargaining = bargaining
      this.form.bargainingLimit = bargainingLimit

      // 订单回款账户回显
      if (this.hasAllOrder) {
        // 选中订单回款账户是否相同
        this.tipText = '请确认选中订单的回款账户，若需修改可重新选择任意已绑定回款账户。'
        const status = this.order.every(e => Number(e.sellerBankAccountId) === Number(this.order[0].sellerBankAccountId))
        if (status) { // 回款户相同
          this.form.sellerBankAccountId = Number(this.order[0].sellerBankAccountId) || ''
          this.currentSellerBankAccountId = Number(this.order[0].sellerBankAccountId) || ''
        } else { // 回款户不同 回显为null 可选择统一修改回款账户
          this.form.sellerBankAccountId = ''
          this.currentSellerBankAccountId = ''
          this.tipText = '已选中订单包含多个回款账户，您可选择任意已绑定回款账户进行统一修改或仅批量修改订单价格。'
        }
      }

      this.$nextTick().then(() => {
        this.$refs.form && this.$refs.form.clearValidate()
        this.isShowTable && this.getTableData()
      })
    },

    // 清空表单
    clearForm() {
      this.form.annualInterest = '' // 年利率
      this.form.serviceCharge = '' // 手续费
      this.form.lakhDeduction = '' /// / 每十万扣款
      this.form.bargaining = '' // 是否接受议价
      this.form.bargainingLimit = '' /// / 议价上限值
      this.$refs.form && this.$refs.form.resetFields()
    },

    // 利率，手续费，每十万扣款输入事件
    handleInput(val, type) {
      // 批量时不计算，因为不同票据计算出来的结果不一样
      if (this.hasAllOrder) {
        return
      }
      const { draftAmount, interestDays, annualInterest, lakhFee } = this.order
      // 通过每十万扣计算利率
      if (type === 'lakhDeduction') {
        this.form.serviceCharge = ''
        if (!val) {
          this.form.annualInterest = ''
          return
        }
        // 输入的值等于原每十万扣时，使用接口返回的利率
        if (+val === lakhFee) {
          this.form.annualInterest = annualInterest
          return
        }
        const data = lakhDeductionMath(draftAmount, val, interestDays)
        this.form.annualInterest = data.annualInterest
      } else {
        if (!this.form.annualInterest && !this.form.serviceCharge) {
          this.form.lakhDeduction = ''
          return
        }
        // 输入的值等于原利率时，使用接口返回的每十万扣
        if (+val === annualInterest) {
          this.form.lakhDeduction = lakhFee
          return
        }
        const data = interestRateMath(draftAmount, this.form.annualInterest || 0, this.form.serviceCharge || 0, interestDays)
        this.form.lakhDeduction = data.lakhDeduction
      }
    },

    // 关闭前
    handleClose() {
      this.visible = false
      this.clearForm()
      this.form.billingMethod = BILLING_METHOD_CODE.SHI_WAN_DISCOUNT
      !this.hasAllOrder && this.$emit('destroy')
    },

    // 确认
    async confirm() {
      try {
        await this.$refs.form.validate()
        this.postChangePrice(this.order)
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log(error)
      }
    },

    // 发起改价
    async postChangePrice(order) {
      // 批量改价的加价 校验是否变动过金额 且 回款账户未变更过
      // 当回款账户下拉框内选中结果发生变更，可不修改报价、加价，直接提交回款账户的修改。
      if (this.hasAllOrder && this.form.updatePriceType === 2 && !this.form.addLakhDeduction && !this.form.customAddLakhDeduction && this.currentSellerBankAccountId === this.form.sellerBankAccountId) {
        this.$message.warning('请选择批量加价金额')
        return
      }

      try {
        this.loading = true
        this.hasAllOrder && (this.batchLoading = true)
        const orderNoList = this.hasAllOrder ? order.map(i => i.orderNo) : [order.orderNo]

        const param = {
          orderNoList, // 订单编号
          // updateQuotedPriceRequest: {
          billingMethod: this.form.updatePriceType === 2 ? null : this.form.billingMethod,
          lakhDeduction: this.form.lakhDeduction, /// / 每十万扣款
          annualInterest: this.form.annualInterest || 0, // 年利率
          serviceCharge: this.form.serviceCharge, // 手续费
          bargaining: this.form.bargaining, // 是否接受议价，0-否、1-是
          updatePriceType: this.form.updatePriceType, // 改价类型 1 普通  2 加价
          addLakhDeduction: this.form.addLakhDeduction, // 加价每十万金额
          bargainingLimit: parseFloat(this.form.bargainingLimit), // 议价上限值
          sellerBankAccountId: this.hasAllOrder ? this.form.sellerBankAccountId : null, // 批量操作 回款账户ID
          // },
        }
        const data = await orderApi.postUpdateQuotation(param)
        if (this.hasAllOrder) {
          // 批量操作结果提示
          this.$refs.resultDialogRef.init(data)
          this.batchLoading = false
        } else {
          this.$emit('success')
          this.$message.success('改价成功')
        }
        this.handleClose()
        this.loading = false
      } catch (error) {
        this.loading = false
        this.batchLoading = false
        if (error.data?.code === ISSUE_DRAFT_ERROR_CODE.GAO_MAI_DI_MAI) {
          this.$alert('您因触发平台机制，被限制登录，请联系您的客户经理', '提示', {
            confirmButtonText: '我知道了',
          }).then(() => {
            this.handleClose()
            return this.$store.dispatch('user/logout', { manual: true })
          })
            .then(() => {
              this.$message.closeAll()
              this.$router.push('/')
              windowCommunication.trigger()
            })
          return
        }
        this.$message.error(error.data?.msg ?? '系统繁忙，请稍后再试')
      }
    },

    // 获取表格数据
    getTableData() {
      const tableData = this.hasAllOrder ? this.order : [this.order]
      this.tableData = []
      const { billingMethod, annualInterest, serviceCharge, lakhDeduction, updatePriceType, addLakhDeduction } = this.form
      let receivedAmountTotal = 0
      let draftAmountTotal = 0
      tableData.forEach((item, i) => {
        if (!item.lastSixDraftNo) {
          this.debounceGetDetail(item, i)
        }
        item.draftActualAmountStr = parseNum(yuan2wan(item.draftActualAmount))
        item.draftAmountStr = parseNum(yuan2wan(item.draftAmount))
        item.lakhFeeStr = parseNum(item.lakhFee)

        if (updatePriceType === 1 && billingMethod && (annualInterest || serviceCharge)) {
          const obj = interestRateMath(item.draftAmount, annualInterest, serviceCharge, item.interestDays)
          item.lakhFeeAfter = parseNum(obj.lakhDeduction)
          item.annualInterestAfter = parseNum(obj.annualInterest)
          item.receivedAmount = obj.receivedAmount
          item.receivedAmountStr = parseNum(yuan2wan(obj.receivedAmount))
        } else if (updatePriceType === 1 && !billingMethod && lakhDeduction) {
          const obj = lakhDeductionMath(item.draftAmount, lakhDeduction, item.interestDays)
          item.lakhFeeAfter = parseNum(lakhDeduction)
          item.annualInterestAfter = parseNum(obj.annualInterest)
          item.receivedAmount = obj.receivedAmount
          item.receivedAmountStr = parseNum(yuan2wan(obj.receivedAmount))
        } else if (updatePriceType === 2 && !!addLakhDeduction) {
          // 加价的价格
          let changePrice = new BigNumber(this.form.addLakhDeduction)
          // 每十万扣款（改价后）= 加价的价格+每十万扣款（改价前）
          let lakhAfter = (new BigNumber(item.lakhFee).plus(changePrice))
            .toString()
          const obj = lakhDeductionMath(item.draftAmount, lakhAfter, item.interestDays)
          item.lakhFeeAfter = parseNum(lakhAfter)
          item.annualInterestAfter = parseNum(obj.annualInterest)
          item.receivedAmount = obj.receivedAmount
          item.receivedAmountStr = parseNum(yuan2wan(obj.receivedAmount))
        } else {
          item.lakhFeeAfter = ''
          item.annualInterestAfter = ''
          item.receivedAmount = item.draftActualAmount
          item.receivedAmountStr = parseNum(yuan2wan(item.draftActualAmount))
        }

        draftAmountTotal = new BigNumber(item.draftAmount).plus(draftAmountTotal)
        receivedAmountTotal = new BigNumber(item.receivedAmount).plus(receivedAmountTotal)
      })
      this.draftAmountTotal = parseNum(yuan2wan(draftAmountTotal))
      this.receivedAmountTotal = parseNum(yuan2wan(receivedAmountTotal))
      this.tableData = tableData
      this.tableKey++
    },
    // 批量操作结果弹窗关闭回调
    closeResultDialog() {
      this.$emit('success')
      this.$emit('destroy')
    },
    isValidNumber(num) {
      const numStr = num.toString()
      const [integerPart, decimalPart] = numStr.split('.')
      // 判断正数部分长度是否超过五位
      if (integerPart.length > 5) {
        return false
      }
      // 判断小数部分长度是否超过两位
      if (decimalPart && decimalPart.length > 2) {
        return false
      }
      return true
    },
    // 单个加价
    onRaisePrice(val) {
      const addNum = new BigNumber(val || 0)
      if (this.form.billingMethod) {
        // 每十万手续费
        let afterRaise = new BigNumber(this.form.serviceCharge || 0).plus(addNum)
        if (!this.isValidNumber(afterRaise)) {
          this.$message.warning('报价最高不能超过五位整数')
        } else {
          this.form.serviceCharge = afterRaise.toString()
        }
      } else {
        // 每十万扣款
        let afterRaise = new BigNumber(this.form.lakhDeduction || 0).plus(addNum)
        if (!this.isValidNumber(afterRaise)) {
          this.$message.warning('报价最高不能超过五位整数')
        } else {
          this.form.lakhDeduction = afterRaise.toString()
        }
      }
    },

    // 批量加价
    onBatchRaisePrice(val) {
      const addNum = new BigNumber(val || 0)
      let afterRaise = new BigNumber(this.form.addLakhDeduction || 0).plus(addNum)
      this.form.addLakhDeduction = afterRaise.toString()
    },
    // 自定义加价
    handelCustomAddLakhDeduction(val) {
      if (this.hasAllOrder) {
        this.form.addLakhDeduction = val || null
      } else {
        const num = new BigNumber(val || 0)
        let after = new BigNumber(num || 0).plus(this.order.lakhFee)
        if (this.form.billingMethod) {
          this.form.serviceCharge = val
        } else {
          this.form.lakhDeduction = after.toString()
        }
      }
    }
  }
}
</script>
