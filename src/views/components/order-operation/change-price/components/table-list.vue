<!-- 表格组件 -->
<style lang="scss" scoped>
.table-list {
  margin-top: 12px;
  background: $color-FFFFFF;
}

.table-header {
  border-bottom: 1px solid $color-F0F0F0;
  padding: 13px 16px;
  font-size: 16px;

  @include flex-vc;

  .icon {
    margin-right: 4px;
    font-size: 20px;
    color: $--color-primary;
  }
}

.red-high-light {
  margin: 0 4px;
  font-weight: 600;
  color: $--color-font-main;
}

.el-table {
  ::v-deep {
    .el-table__cell .cell {
      padding-right: 20px;
      padding-left: 20px;
      line-height: 22px;
    }

    thead .el-table__cell {
      padding: 4px 0;
      height: 54px !important;
      line-height: normal;

      // 这里表格头每十万扣款样式特殊设置

      &:nth-child(5),
      &:nth-child(6) {
        .cell {
          line-height: 22px !important;
        }
      }
    }
  }
}

.acceptor {
  text-align: left;

  @include ellipsis(2);
}
</style>

<template>
  <div class="table-list">
    <div class="table-header">
      <icon type="sdicon-info-circle" class="icon" />
      共 <span class="red-high-light">{{ tableData.length }}</span> 张票据，票面总金额
      <span class="red-high-light">{{ draftAmountTotal }}</span> 万元，到账金额
      <span class="red-high-light">{{ receivedAmountTotal }}</span> 万元
    </div>
    <el-table :data="tableData" max-height="550" class="change-price-table">
      <el-table-column label="承兑人" prop="acceptorName" min-width="160">
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.acceptorName" placement="top-start">
            <div class="acceptor">{{ scope.row.acceptorName || '-' }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="票面金额(万)"
        prop="draftAmountStr"
        min-width="150"
        align="right"
      />
      <el-table-column
        label="到期日"
        min-width="140"
        align="right"
      >
        <template slot-scope="scope">
          {{ scope.row.maturityDate }}<br>
          剩 {{ scope.row.interestDays }} 天
        </template>
      </el-table-column>
      <el-table-column
        label="票号后六位"
        prop="lastSixDraftNo"
        min-width="110"
        align="right"
      />
      <el-table-column prop="acceptorName" min-width="130" align="right">
        <template #header>
          每十万扣款<br>(改价前)
        </template>
        <template slot-scope="scope">
          {{ scope.row.lakhFeeStr }}<br>
          ({{ scope.row.annualInterest }}%)
        </template>
      </el-table-column>
      <el-table-column prop="acceptorName" min-width="130" align="right">
        <template #header>
          每十万扣款<br>(改价后)
        </template>
        <template slot-scope="scope">
          <template v-if="scope.row.lakhFeeAfter || scope.row.lakhFeeAfter">
            {{ scope.row.lakhFeeAfter || '-' }}<br>
            ({{ scope.row.annualInterestAfter || 0 }}%)
          </template>
          <template v-else>-</template>
        </template>
      </el-table-column>
      <el-table-column
        label="到账金额(万)"
        prop="receivedAmountStr"
        min-width="150"
        align="right"
      >
        <template slot-scope="scope">
          <span class="red-high-light">
            {{ scope.row.receivedAmountStr }}<br>
          </span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'table-list',
  props: {
    // 表格数据
    tableData: Array,
    // 票面总金额(万)
    draftAmountTotal: String,
    // 到账金额(万)
    receivedAmountTotal: String
  }
}
</script>
