<!-- 定向发布弹窗 -->
<style lang="scss" scoped>
.directional-dialog {
  ::v-deep {
    .el-form-item {
      margin-bottom: 0;
    }

    .el-form-item__error {
      position: static;
    }

    .el-button--danger.is-border {
      font-size: 16px;
    }
  }

  .main {
    padding: 16px;
    background: $color-FFFFFF;

    .item-label {
      margin-bottom: 2px;
      height: 22px;
      font-size: 14px;
      color: $color-text-secondary;
      line-height: 22px;
    }
  }

  .green-warn-content {
    margin-bottom: 12px;
    font-size: 16px;
  }

  .green-high-light {
    font-weight: bold;
    color: $font-color;
  }
}

.sellerBankAccount-cls {
  padding-top: 15px;
}

.item-position {
  position: relative;

  ::v-deep .el-form-item__label {
    padding: 0;
  }

  .value {
    .el-tooltip {
      position: absolute;
      top: 1px;
      left: 65px;
    }
  }

  .max-width {
    width: 100%;
  }
}

.seller-select-btn {
  font-size: 16px;
  text-align: center;
  color: $--color-primary;
  line-height: 40px;
  cursor: pointer;
}

.seller-bank-link {
  border-bottom: 1px solid $--color-primary;
  color: $--color-primary;
  cursor: pointer;
}

.m-t10 {
  margin-top: 10px;
}

.switch-btn {
  margin-left: 8px;
  border: 1px solid $color-D9D9D9;
  border-radius: 45px;
  padding: 0 10px;
  min-width: 52px;
  height: 32px;
  color: $--color-primary;
  background: $color-FFFFFF;

  &:hover {
    border-color: $--color-primary;
  }
}

.flex {
  display: flex;
  align-items: center;
}

.m-l-r5 {
  margin: 0 5px;
}

.offer-flex {
  display: flex;
  justify-content: space-between;

  .form-item-block {
    flex: 1;
    margin-right: 12px;

    .el-input {
      width: 100%;
    }
  }

  .form-item {
    .offer-bold {
      display: inline-block;
      width: 100%;
      font-weight: bold;
    }

    .amount {
      color: $color-warning;
    }
  }

  .plus-sign {
    line-height: 40px;
    padding: 0 8px;
    font-size: 18px;
  }
}

.blod-cls {
  font-weight: 500;
  color: $color-warning;
}
</style>

<template>
  <div>
    <el-dialog
      width="630px"
      :visible.sync="visible"
      :before-close="handleClose"
      :close-on-click-modal="false"
      title="定向推送"
      class="directional-dialog order-operation-dialog"
    >
      <WarnContent v-if="hasAllOrder">您正在对 <span class="green-high-light">{{ order.length }}</span> 笔订单进行批量定向推送操作</WarnContent>
      <el-form ref="form" :model="form" :rules="rules">
        <div class="main">
          <div class="item-label">对方</div>
          <el-form-item prop="phone">
            <el-input
              key="phone"
              v-model="form.phone"
              :height="40"
              placeholder="对方手机号或定向码"
              :readonly="loading"
            />
          </el-form-item>
          <div v-if="isShowSellerBankAccount()" class="sellerBankAccount-cls">
            <div class="txt">{{ tipText }}</div>
            <el-form-item
              label="回款账户"
              prop="sellerBankAccountId"
              class="form-item-block item-position"
              :required="sellerBankAccountRequired"
            >
              <el-tooltip
                placement="top"
                popper-class="issue-draft-tooltip"
              >
                <template slot="content">
                  <div>依照智付E+、E++渠道交易模式，票方发布票据必须选择回款账户(支持任意银行一般户)。需要在<span class="seller-bank-link" @click="() => { $router.push('/user-center/bank-account');visible = false }">银行回款账户</span>页面绑定回款账户，交易完成后票款自动提现到账。</div>
                </template>
                <icon class="icon icon-question" type="chengjie-wenti" />
              </el-tooltip>

              <div class="pay-type-item">
                <el-select
                  ref="sellerBankAccount"
                  v-model="form.sellerBankAccountId"
                  clearable
                  class="max-width"
                  :height="40"
                  placeholder="请选择回款账户"
                >
                  <el-option
                    v-for="item in sellerBankAccountList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                  <div class="seller-select-btn" @click="() => { $router.push('/user-center/bank-account?tabStatus=2'); $refs.sellerBankAccount.blur(); visible = false }"><i class="el-icon-plus" />添加回款账户</div>
                </el-select>
              </div>
            </el-form-item>
          </div>
          <!-- 单笔推送拆分报价设置 -->
          <div v-if="!hasAllOrder" class="split-quotatio m-t10">
            <div>待接单票面金额 <span class="blod-cls">{{ yuan2wan(order.draftAmount) }}</span> 万元，若本次需对票据做拆分定向，可在下方输入拆分定向的金额。</div>
            <el-form-item class="m-t10" prop="splitAmtIntValidate">
              <el-input
                v-model="form.splitAmt"
                placeholder="拆分金额"
                :disabled="isDisableSplitAmt"
                :width="240"
                type="number"
                :number-format="{
                  decimal: true,
                  negative: false,
                  leadingZero: false,
                  maxDecimalLength: 6,
                  maxIntegerLength: 4,
                }"
                @input="handleInputSplitAmount"
              >
                <span slot="append">万</span>
              </el-input>
            </el-form-item>
            <div class="item-label m-t10">报价方式</div>
            <el-form-item prop="billingMethodValidate">
              <div class="flex">
                <template v-if="form.billingMethod">
                  <el-input
                    v-model="form.annualInterestInput"
                    placeholder="利率"
                    :width="120"
                    type="number"
                    :number-format="{
                      maxDecimalLength: 4,
                      leadingZero: false,
                      negative: false,
                      maxLength: 7
                    }"
                    @input="handleInput()"
                  >
                    <template slot="append">%</template>
                  </el-input>
                  <div class="m-l-r5 split-icon">+</div>
                  <el-input
                    v-model="form.serviceCharge"
                    placeholder="每十万手续费"
                    :width="120"
                    type="number"
                    :number-format="{
                      maxDecimalLength: 2,
                      leadingZero: false,
                      negative: false,
                      maxLength: 8
                    }"
                    @input="handleInput()"
                  >
                    <template slot="append">元</template>
                  </el-input>
                </template>
                <template v-else>
                  <el-input
                    v-model="form.lakhDeduction"
                    placeholder="每十万扣款"
                    :width="240"
                    type="number"
                    :number-format="{
                      maxDecimalLength: 2,
                      leadingZero: false,
                      negative: false,
                      maxLength: 8
                    }"
                    @input="handleInput()"
                  >
                    <template slot="append">元</template>
                  </el-input>
                </template>
                <el-button class="switch-btn" @click="changeTradePrice">
                  <icon class="icon" type="dj-swap" />
                  <span>{{ form.billingMethod ? '每十万扣款' : '年化+每十万手续费' }}</span>
                </el-button>
              </div>
            </el-form-item>
            <div class="offer-flex m-t10">
              <FormItem label="每十万扣款：" class="flex m-l-r5">
                <span class="offer-bold">{{ form.lakhDeduction ? `${form.lakhDeduction}元` : '-' }}</span>
              </FormItem>
              <FormItem label="年化利率：" align="center" class="flex m-l-r5">
                <span class="offer-bold">{{ form.annualInterest || '0.00' }}%</span>
              </FormItem>
              <FormItem label="到账金额：" align="right" class="flex m-l-r5">
                <span class="offer-bold amount">{{ form.receivedAmount ? `${yuan2wan(form.receivedAmount)}万元` : '0 万元' }}</span>
              </FormItem>
            </div>
          </div>
        </div>
      </el-form>
      <span slot="footer">
        <el-button :disabled="loading" @click="handleClose">取消</el-button>
        <el-button
          v-waiting="[`post::/draft/order/executeOrderTradeStep`,
                      `post::/draft/order/singleExecuteOrderTradeStep`]"
          type="primary"
          @click="confirm"
        >确定</el-button>
      </span>
    </el-dialog>
    <LoadingDialog :visible="loading" title="批量定向推送中" content="正在批量定向推送中，请耐心等待..." />
    <ResultDialog ref="resultDialogRef" handle-str="定向推送" @close="closeResultDialog" />
    <!-- 信用低确认提醒 -->
    <CreditLowConfirm ref="creditLowConfirmRef" />
    <!-- 支付账户异常弹窗 -->
    <PayAccountError ref="payAccountErrorRef" />
  </div>
</template>

<script>
import WarnContent from '@/views/components/common/warn-content.vue' // 警告文本
import LoadingDialog from '@/views/components/common/loading-dialog/loading-dialog.vue' // 加载中组件
import ResultDialog from '@/views/components/common/result-dialog/result-dialog.vue' // 批量操作结果组件
import CreditLowConfirm from '@/views/pages/issue-draft/components/credit-low-confirm.vue' // 信用低确认提醒
import PayAccountError from '@/views/components/pay-account-error/pay-account-error.vue'// 支付账户异常弹窗
import orderApi from '@/apis/order'// 订单接口
import { EXECUTE_TRANSACTION_PROCESS, BILLING_METHOD_CODE } from '@/constant' // 执行交易流程场景值
import { ISSUE_DRAFT_ERROR_CODE } from '@/constants/draft.js' // 发布票据错误code
import issueDraftApi from '@/apis/issue-draft'
import { mapGetters } from 'vuex'
import FormItem from '@/recognize/components/issue-draft/components/form-item.vue'
import {
  lakhDeductionMath, interestRateMath
} from '@/common/js/draft-math'
// 金额单位转换
import { yuan2wan, wan2yuan } from '@/common/js/number'
import BigNumber from 'bignumber.js'

export default {
  name: 'directional-dialog',

  components: {
    WarnContent,
    LoadingDialog,
    ResultDialog,
    CreditLowConfirm,
    PayAccountError,
    FormItem
  },

  data() {
    // 报价信息校验
    const validateMethod = (rule, value, callback) => {
      const { billingMethod, lakhDeduction, annualInterest } = this.form

      // 分别校验两种方式下的输入框
      if (billingMethod === BILLING_METHOD_CODE.SHI_WAN_DISCOUNT) {
        if (!Number(lakhDeduction)) {
          callback(new Error('请输入报价信息'))
        }
        callback()
      }
      if (billingMethod === BILLING_METHOD_CODE.ANNUAL_INTEREST) {
        if (!Number(annualInterest)) {
          callback(new Error('请输入报价信息'))
        }
        callback()
      }
      callback()
    }
    // 校验拆分金额
    const validateSplitAmtInt = (rule, value, callback) => {
      if (!this.form.splitAmt) {
        callback(new Error('请输入拆分金额'))
      }
      // 订单票面金额>1万元时 拆分金额小于1万元提示 拆分定向的金额必须大于等于1万元
      if (new BigNumber(yuan2wan(this.order.draftAmount)).isGreaterThan(new BigNumber(1))) {
        if (this.form.splitAmt && new BigNumber(this.form.splitAmt).isLessThan(new BigNumber(1))) {
          callback(new Error('拆分定向的金额必须大于等于1万元'))
        }
        callback()
      }

      // if (this.form.splitAmt && new BigNumber(wan2yuan(this.form.splitAmt)).isLessThan(new BigNumber(1))) {
      //   callback(new Error('拆分金额必须大于等于1万元'))
      // }
      // 拆分金额大于订单票面金额提示 拆分金额必须小于等于订单票面金额
      if (this.form.splitAmt && new BigNumber(this.form.splitAmt).isGreaterThan(new BigNumber(yuan2wan(this.order.draftAmount)))) {
        callback(new Error('拆分定向的金额必须小于等于订单票面金额'))
      }
      callback()
    }
    return {
      wan2yuan,
      yuan2wan,
      form: {
        phone: '', // 对方手机号
        sellerBankAccountId: '', // 回款账户
        billingMethod: 0, // 报价方式
        lakhDeduction: '', // 每10万扣款
        annualInterest: '', // 计算得出的利率
        serviceCharge: '', // 每10万手续费
        receivedAmount: '', // 到账金额
        annualInterestInput: '', // 输入的利率
        splitAmt: '' // 拆分金额
      },
      order: null, // 当前操作的订单
      visible: false, // 弹窗是否打开
      sellerBankAccountRequired: true, // 回款账户是否必填
      tipText: '', // 提示文本
      rules: { // 表单校验规则
        phone: [
          {
            required: true,
            message: '请输入对方手机号或定向码',
            trigger: ['change', 'blur']
          }
        ],
        sellerBankAccountId: [
          {
            required: true,
            message: '请选择回款账户',
            trigger: ['change', 'blur']
          }
        ],
        billingMethodValidate: [{ required: true, validator: validateMethod, trigger: ['change', 'blur'] }], // 报价信息校验
        splitAmtIntValidate: [{ required: true, validator: validateSplitAmtInt, trigger: ['change', 'blur'] }] // 拆分金额校验
      },
      loading: false,
    }
  },
  computed: {
    ...mapGetters('user', {
      sellerBankAccountList: 'sellerBankAccountList' // 回款账户列表
    }),
    // sellerBankAccountId() {
    //   return this.$store.state.common.sellerBankAccountId
    // },
    // 是否批量
    hasAllOrder() {
      return this.order instanceof Array
    },
    // 单笔推送 票面金额<=1万元时禁用拆分金额输入框
    isDisableSplitAmt() {
      return !this.hasAllOrder && new BigNumber(yuan2wan(this.order.draftAmount)).isLessThanOrEqualTo(new BigNumber(1))
    }
  },

  methods: {
    // 是否包含E+邦+渠道订单 && 回款账户为null
    hasAcceptJdYlpay() {
      if (this.hasAllOrder) {
        return this.order.some(e => e.draftOrderPay.acceptJdYlpay === 1 && !e.sellerBankAccountId)
      } else {
        return this.order.draftOrderPay.acceptJdYlpay && !this.order.sellerBankAccountId
      }
    },
    // 是否显示回款账户(邦+ E+显示回款账户)
    isShowSellerBankAccount() {
      if (this.hasAllOrder) {
        return this.order.some(e => e.draftOrderPay.acceptJdYlpay === 1 || e.draftOrderPay.acceptZbankPlus === 1)
      } else {
        return this.order.draftOrderPay.acceptJdYlpay || this.order.draftOrderPay.acceptZbankPlus
      }
    },
    init() {
      if (this.order && Array.isArray(this.order) && this.order.length > 99) {
        this.$message.warning(`每次批量操作最多可选择 ${99} 笔订单`)
        return
      }
      this.tipText = '请您确认选中订单的回款账户，若需变更可在下方修改回款账户。'
      // 订单回款账户回显
      if (this.hasAllOrder) {
        // 选中订单回款账户是否相同
        const status = this.order.every(e => Number(e.sellerBankAccountId) === Number(this.order[0].sellerBankAccountId))
        if (status) { // 回款户相同
          this.form.sellerBankAccountId = Number(this.order[0].sellerBankAccountId)
        } else { // 回款户不同 组件为非必填 回显为null 可选择统一修改回款账户
          this.form.sellerBankAccountId = ''
          this.sellerBankAccountRequired = false
          this.rules.sellerBankAccountId[0].required = false
          this.tipText = '选中的订单包含不同回款账户，您可在下方选择修改为相同回款账户，若您直接确定定向推送则保留订单各自回款账户。'
        }
      } else {
        this.form.sellerBankAccountId = Number(this.order.sellerBankAccountId)
        // 单笔定向推送设置拆分金额
        this.form.splitAmt = yuan2wan(this.order.draftAmount)
        // 单笔定向推送 回显设置报价方式
        this.setBillingMethod()
      }

      this.visible = true
    },

    // 单笔定向推送 回显设置报价方式
    setBillingMethod() {
      const { billingMethod, lakhFee, annualInterest } = this.order
      Object.assign(this.form, {
        billingMethod,
        lakhDeduction: lakhFee,
        annualInterestInput: annualInterest,
      })
    },

    // 确认
    confirm() {
      const orderNoList = this.hasAllOrder ? this.order.map(i => i.orderNo) : [this.order.orderNo]
      const param = {
        orderNoList, // 订单编号,必填字段不可为空
        orderStepsEnumCode: EXECUTE_TRANSACTION_PROCESS.DIRECTIONAL, // 必填字段,订单步骤
        inviteCode: this.form.phone, // 定向码或手机号
        isShowBatchError: false, // 是否统一处理批量操作错误
        showError: false, // 全局错误时，是否使用统一的报错方式
        sellerBankAccountId: this.form.sellerBankAccountId // 回款账户
      }
      this.$refs.form.validate(valid => {
        if (valid) {
          this.postDirectional(param)
        }
      })
    },

    // 定向发布
    async postDirectional(param) {
      const isAreaInBlack = await issueDraftApi.validateAgentOrderArea({
        counterparty: param.inviteCode,
        limitType: 1
      })
      if (isAreaInBlack.isLimit) {
        await this.$confirm('对方在您设置的地区黑名单，请确认是否继续定向', '提示', {
          type: 'warning',
          iconPosition: 'title',
          showClose: false,
          showCancelButton: true,
          cancelButtonText: '取消',
          confirmButtonText: '确认'
        })
      }
      try {
        if (this.hasAllOrder) {
          this.loading = true
          const data = await orderApi.postExecuteOrderTradeStep(param)
          this.$refs.resultDialogRef.init(data)
          this.loading = false
        } else {
          const { splitAmt, billingMethod, lakhDeduction, annualInterestInput, serviceCharge } = this.form
          param.splitSettingRequest = {
            splitAmt: wan2yuan(splitAmt), billingMethod, lakhDeduction, annualInterest: annualInterestInput, serviceCharge
          }
          await orderApi.postSingleExecuteOrderTradeStep(param)
          this.$emit('success')
          this.$message.success('定向推送成功')
        }
        this.form.phone = ''
        this.$refs.form && this.$refs.form.resetFields()
        this.handleClose()
      } catch (error) {
        this.loading = false
        // 对接口返回的错误code处理
        this.handleErrorCode(error)
      }
    },

    // 接口请求返回错误处理
    handleErrorCode(errData) {
      const { code, msg } = errData.data || {}

      // 实名认证失效
      if (code === ISSUE_DRAFT_ERROR_CODE.REAL_NAME_EXPIRED) {
        // this.$refs.verifiedDialogRef.init()
        return
      }
      // 发布票据时匹配到风险承兑人提示
      if (code === ISSUE_DRAFT_ERROR_CODE.ORDER_EXIT_ACCEPT_RISK) {
        this.$alert(`
                <p>票据存在承兑风险。</p>
                <p>承兑人：${this.form.acceptorName}。 </p>
                <p>如有疑问请联系客户经理！ </p>
              `, '提示', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确认'
        })
        return
      }
      // 定向发布支付渠道对方不支持
      if (code === ISSUE_DRAFT_ERROR_CODE.PAY_CHANNEL_BUYER_NOT_SUPPORT) {
        this.$alert(`
                <p>您选择的支付渠道对方不支持，</p>
                <p>资方支持 <span class="alert-red-high-light">${msg}</span> 支付渠道。 </p>
                <p>请重新选择。</p>
              `, '提示', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '重新选择'
        })
        return
      }

      // 自身信用等级低
      if (code === ISSUE_DRAFT_ERROR_CODE.CREDIT_TYPE_GENERAL) {
        this.$refs.creditLowConfirmRef.init({
          useScenes: 'issue-draft',
          creditLevelName: '一般',
          trigger: code === ISSUE_DRAFT_ERROR_CODE.CREDIT_TYPE_GENERAL ? 'self' : 'other',
          confirmCallBack: () => {
            this.form.margin = 1
            this.otherOptionsForm = { ...this.form }
          }
        })
        return
      }

      // 1018 支付账户异常
      if (code === ISSUE_DRAFT_ERROR_CODE.PAY_CHANNEL_FAIL) {
        this.$refs.payAccountErrorRef.init(msg)
        return
      }

      this.$message.error(msg || '系统繁忙，请稍后再试')
    },

    // 关闭之前
    handleClose() {
      this.$refs.form && this.$refs.form.resetFields()
      this.visible = false
      this.form.phone = ''
      !this.hasAllOrder && this.$emit('destroy')
    },

    // 批量操作结果弹窗关闭回调
    closeResultDialog() {
      this.$emit('success')
      this.$emit('destroy')
    },
    // 拆分金额输入事件
    handleInputSplitAmount() {
      this.getAnnualInterest()
    },
    // 报价方式切换
    changeTradePrice() {
      this.form.billingMethod = this.form.billingMethod ? BILLING_METHOD_CODE.SHI_WAN_DISCOUNT : BILLING_METHOD_CODE.ANNUAL_INTEREST
      this.getAnnualInterest()
      // 利率+手续费切换数据初始化设置
      Object.assign(this.form, {
        serviceCharge: ''
      })
    },
    // 计算报价
    getAnnualInterest() {
      const { interestDays } = this.order // 计算利息天数

      // 每10万扣款 报价方式 输入的利率 每10万手续费
      const { lakhDeduction, billingMethod, annualInterestInput, serviceCharge } = this.form
      // 有拆分金额使用按拆分金额报价计算 || 按票面金额
      const amtInt = this.form.splitAmt || yuan2wan(this.order.draftAmount)

      if (billingMethod === BILLING_METHOD_CODE.SHI_WAN_DISCOUNT) {
        const { annualInterest, receivedAmount } = lakhDeductionMath(wan2yuan(amtInt), lakhDeduction, interestDays)
        // eslint-disable-next-line no-magic-numbers
        this.form.annualInterest = (+annualInterest).toFixed(4)
        this.form.annualInterestInput = (annualInterest && !isNaN(annualInterest)) ? (+annualInterest).toFixed(4) : null
        this.form.receivedAmount = receivedAmount
      } else {
        const { annualInterest, receivedAmount, lakhDeduction: rtLakhDeduction } = interestRateMath(wan2yuan(amtInt), annualInterestInput, serviceCharge, interestDays)
        // eslint-disable-next-line no-magic-numbers
        this.form.annualInterest = (+annualInterest).toFixed(4)
        this.form.receivedAmount = receivedAmount
        this.form.lakhDeduction = rtLakhDeduction ? (+rtLakhDeduction).toFixed(2) : null
      }
    },
    handleInput() {
      this.getAnnualInterest()
    }
  }

}
</script>
