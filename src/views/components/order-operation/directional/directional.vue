<!-- 定向发布 -->
<style lang="scss" scoped>
.directional {
  display: inline-block;
}

.el-button {
  position: relative;

  &.has-tag::after {
    content: "";
    position: absolute;
    top: -1px;
    left: -1px;
    border-top: 27px solid $color-assist3;
    border-right: 28px solid transparent;
    width: 0;
    height: 0;
  }

  .rice-tag {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    font-size: 12px;
    font-weight: 600;
    line-height: 20px;
    transform: scale(.8);
    transform-origin: top;
    color: $color-FFFFFF;
  }
}
</style>

<template>
  <div class="directional order-operation">
    <slot name="button">
      <transaction-tooltip-button
        v-bind="$attrs"
        :type="$attrs.type || 'primary'"
        :width="$attrs.width || '68'"
        :height="$attrs.height || '40'"
        :class="hasTag && 'has-tag'"
        :types="isFastTrade ? [TRANSACTION_TOOLTIP_TYPE.HOLIDAY, TRANSACTION_TOOLTIP_TYPE.DAILY] : [TRANSACTION_TOOLTIP_TYPE.HOLIDAY]"
        @click="init"
        v-on="$listeners"
      >
        <span v-if="hasTag" class="rice-tag">{{ sdmUnit }}</span>
        <slot>定向</slot>
      </transaction-tooltip-button>
    </slot>
  </div>
</template>

<script>
import DirectionalDialog from './directional-dialog.vue'
import orderOperationMixin from '../order-operation-mixin'
import { TRANSACTION_TOOLTIP_TYPE } from '@/constants/transaction-tooltip'

export default {
  name: 'directional',
  mixins: [orderOperationMixin(DirectionalDialog)],
  props: {
    hasTag: [String, Number, Boolean] // 是否显示保证金米字标识
  },
  data() {
    return {
      TRANSACTION_TOOLTIP_TYPE, // 交易限制提示类型
    }
  },
  computed: {
    // 是否为极速票（定向）或选了极速票(批量定向)
    isFastTrade() {
      if (this.order instanceof Array) {
        return this.order.some(v => !!v.fastTrade)
      } else {
        return !!this.order.fastTrade
      }
    }
  },

}
</script>
