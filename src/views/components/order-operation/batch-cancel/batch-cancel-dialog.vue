<!-- eslint-disable max-lines -->
<!-- 批量取消订单弹窗 -->
<style lang="scss" scoped>
.batch-cancel-dialog {
  .main {
    padding: 16px;
    background: $color-FFFFFF;
  }

  // 标题
  .title-left-border {
    position: relative;
    margin-bottom: 10px;
    padding-left: 12px;
    height: 22px;
    font-size: 16px;
    font-weight: 600;
    line-height: 22px;

    &::before {
      position: absolute;
      top: 2px;
      left: 0;
      width: 4px;
      height: 16px;
      background: $--color-primary;
      content: "";
    }
  }

  // 单选框
  .radio-box {
    display: flex;
    justify-content: space-between;
    width: 100%;

    ::v-deep {
      // 单选按钮样式 start
      .el-radio {
        margin: 0 8px 0 0;
        border: 1px solid $color-D9D9D9;
        border-radius: 2px;
        width: 170px;
        height: 40px;
        text-align: center;
        color: $color-text-primary;
        line-height: 40px;
      }

      .el-radio__input {
        display: block;
      }

      .el-radio__label {
        padding-left: 0;
        font-size: 16px;
        line-height: 24px;
      }

      .el-radio.is-bordered {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 8px 0 0;
        padding: 0 10px;

        &:last-child {
          margin-right: 0;
        }
      }

      .is-checked {
        border-color: $--color-primary;
        background-color: $--color-primary-hover;
      }

      // 单选按钮样式 end
    }
  }

  // 合同按钮
  .contract-btn {
    float: left;
    height: 42px;
    line-height: 38px;

    ::v-deep {
      .el-link--inner {
        height: 22px;
        font-size: 14px;
        line-height: 22px;
      }

      .el-link.el-link--primary {
        color: $font-color;

        @include example-underline;

        &::after {
          border: 0;
          color: $font-color-hover;
        }
      }
    }
  }
}

::v-deep {
  .el-form-item {
    margin-bottom: 0;

    & + .el-form-item {
      margin-top: 12px;
    }
  }

  .el-form-item__error {
    position: static;
  }

  .el-button--danger.is-border {
    font-size: 16px;
  }

  .el-textarea .el-input__count {
    bottom: -5px;
    background-color: rgb(0 0 0 / 0%);
  }
}
</style>

<template>
  <div>
    <el-dialog
      :width="dialogWidth"
      :visible.sync="dialogVisible"
      :title="order && titleDialog"
      :close-on-click-modal="false"
      class="batch-cancel-dialog order-operation-dialog"
      :before-close="handleClose"
      :lock-scroll="true"
      append-to-body
      center
    >
      <div class="main">
        <el-form ref="form" :model="form" :rules="rules">
          <!-- 取消类型 -->
          <template v-if="hasCancelType">
            <div class="title-left-border">取消原因</div>
            <el-form-item prop="cancelType">
              <el-radio-group v-model="form.cancelType" class="radio-box">
                <el-radio
                  v-for="item in option"
                  :key="item.value"
                  :label="item.value"
                  type="button"
                >
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </template>
          <!-- 取消补充 -->
          <el-form-item prop="cancelMsg">
            <el-input
              v-model="form.cancelMsg"
              type="textarea"
              maxlength="50"
              :autosize="{ minRows: 4}"
              class="remark-textarea"
              show-word-limit
              placeholder="补充说明"
            />
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <span class="left contract-btn">
          <!--
            <el-link
            type="primary"
            target="_blank"
            underline="always"
            rel="noopener noreferrer"
            :href="PLATFORM_DEFAULT_RULESNEW_URL"
            >
            《平台订单违约规则》
            </el-link>
          -->
          <a
            class="text-link"
            target="_blank"
            :href="PLATFORM_DEFAULT_RULESNEW_URL"
            rel="noopener noreferrer"
          >《平台订单违约规则》</a>
        </span>

        <div class="right">
          <el-button @click="handleClose">暂不取消</el-button>

          <el-button
            v-waiting="orderApi.postExecuteOrderTradeStep"
            type="primary"
            :disabled="hasCancelType && !form.cancelType"
            @click="handleConfirm"
          >
            确定取消
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 批量取消loading -->
    <LoadingDialog :visible="batchLoading" title="批量取消中" content="正在批量取消中，请耐心等待..." />
    <!-- 批量取消结果 -->
    <ResultDialog
      ref="resultDialogRef"
      handle-str="取消"
      error-message-key="orderTradeVOList"
      @close="closeResultDialog"
    />
  </div>
</template>

<script>
import orderApi from '@/apis/order'// 订单接口
import { CANCEL_TYPE, DRAFT_STATUS, EXECUTE_TRANSACTION_PROCESS } from '@/constant' // 常量
import orderStatusMixin from '../order-status-mixin' // 订单状态
import ResultDialog from '@/views/components/common/result-dialog/result-dialog.vue' // 批量操作结果组件
import LoadingDialog from '@/views/components/common/loading-dialog/loading-dialog.vue' // 加载中组件
import { PLATFORM_DEFAULT_RULESNEW_URL } from '@/constants/oss-files-url' // 平台订单违约规则url
const formInit = Object.freeze({
  cancelMsg: null, // 补充说明
  cancelType: null, // 原因选择
  // phone: '', // 手机号
  // validateCode: '', // 验证码
})

export default {
  name: 'batch-cancel-dialog',
  components: {
    ResultDialog,
    LoadingDialog
  },
  mixins: [orderStatusMixin],
  props: {
    type: String
  },
  data() {
    return {
      PLATFORM_DEFAULT_RULESNEW_URL, // 平台订单违约规则url
      order: [],
      orderApi,
      dialogVisible: false, // 弹窗是否打开
      dialogWidth: '600px',
      DRAFT_STATUS, // 订单所有状态
      form: { ...formInit },
      batchLoading: false,
      orderList: [], // 订单号
    }
  },
  computed: {

    // 弹窗标题
    titleDialog() {
      return this.hasCancelType ? '批量取消订单' : '批量取消订单（取消原因：其他）'
    },

    // 是否有取消类型
    hasCancelType() {
      return !(this.isAgentOrder && this.isBuy)
    },

    // 表单规则
    rules() {
      let res = {}

      if (this.hasCancelType) {
        res.cancelType = [
          {
            required: true,
            message: '请选择取消原因',
            trigger: ['none', 'change', 'blur']
          },
        ]
      }

      return res
    },

    // 取消类型选项
    option() {
      let res = [
        {
          value: CANCEL_TYPE.NO_TICKET.id,
          label: '票不在户'
        },
        {
          value: CANCEL_TYPE.WRONG_ACCOUNT.id,
          label: '挂错户'
        },
        {
          value: CANCEL_TYPE.OTHER.id,
          label: '其他'
        },
      ]

      // 定向票-待确认-票方取消
      if (this.isAgentOrder && this.waitingConfirm && this.isSale) {
        res.splice(2, 0, {
          value: CANCEL_TYPE.NOT_CONFIRM_BUY_AGENT.id,
          label: '资方未确认'
        })
      }

      // 自动订单-待确认-票方取消
      if (this.isRadarType && this.waitingConfirm && this.isSale) {
        res.splice(2, 0, {
          value: CANCEL_TYPE.NOT_SATISFY_BUYER.id,
          label: '不满足资方要求'
        })
      }

      return res
    }
  },

  methods: {
    init() {
      if (this.order && Array.isArray(this.order) && this.order.length > 99) {
        this.$message.warning(`每次批量操作最多可选择 ${99} 笔订单`)
        return
      }

      this.dialogVisible = true
      this.orderNoList = Array.isArray(this.order) ? this.order.map(v => v.orderNo) : [this.order.orderNo]
    },

    // 确认取消
    async handleConfirm() {
      try {
        await this.$refs.form.validate()
        this.postCancelTrade()
      } catch (error) {
        // eslint-disable-next-line no-console
        // console.log('error :>> ', error)
      }
    },

    // 取消类型参数
    handleCancelRequest() {
      const cancelRequest = { ...this.form }
      // *由于以下场景的取消订单没有取消原因选项，故手动补充对应的取消类型参数

      // 待确认-定向票-资方取消
      if (this.waitingConfirm && this.isAgentOrder && this.isBuy) {
        cancelRequest.cancelType = CANCEL_TYPE.OTHER.id
      }

      return cancelRequest
    },

    // 取消请求
    async postCancelTrade() {
      try {
        const params = {
          isShowBatchError: false,
          orderNoList: this.orderNoList, // 订单编号,必填字段不可为空
          orderStepsEnumCode: this.isSale ? EXECUTE_TRANSACTION_PROCESS.SELLER_CANCEL_BE_CONFIRMED : EXECUTE_TRANSACTION_PROCESS.BUYERS_CANCEL_BE_CONFIRMED, // 必填字段,订单步骤
          cancelRequest: this.handleCancelRequest(), // 取消类型参数
        }

        this.batchLoading = true
        const data = await orderApi.postExecuteOrderTradeStep(params)
        this.batchLoading = false

        // 批量操作结果提示
        this.$refs.resultDialogRef.init(data)
      } catch (error) {
        this.batchLoading = false
        throw error
      }
    },

    // 清除数据
    clearData() {
      this.form = {}
      this.orderList = []
      this.batchLoading = false
      this.$refs.form && this.$refs.form.resetFields()
    },

    // 关闭之前
    handleClose() {
      this.clearData()
      this.dialogVisible = false
      this.$emit('destroy')
    },

    // 批量操作结果弹窗关闭回调
    closeResultDialog() {
      this.$emit('success')
      this.handleClose()
    }
  }
}
</script>
