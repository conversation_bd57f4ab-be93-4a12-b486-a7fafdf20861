<!-- 批量取消订单 -->
<style lang="scss" scoped>
.change-endorsement-account {
  display: inline-block;

  .el-button {
    position: relative;
  }
}
</style>

<template>
  <el-tooltip
    placement="top"
    popper-style="max-width: 260px;"
    :content="toolTipText"
    :disabled="!toolTipText"
  >
    <div class="change-endorsement-account order-operation">
      <slot name="button">
        <el-button
          v-bind="$attrs"
          :type="$attrs.type || 'primary'"
          :width="$attrs.width || '104'"
          :height="$attrs.height || '40'"
          :border="!$attrs.type"
          @click="init"
          v-on="$listeners"
        >
          <slot>批量取消</slot>
        </el-button>
      </slot>
    </div>
  </el-tooltip>
</template>

<script>
import BatchCancelDialog from './batch-cancel-dialog.vue'
import orderOperationMixin from '../order-operation-mixin'

export default {
  name: 'change-endorsement-account',
  mixins: [orderOperationMixin(BatchCancelDialog)],
  props: {
    type: String,
    toolTipText: String, // hover文案
  },
}
</script>
