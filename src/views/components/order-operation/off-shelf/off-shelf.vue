<!-- 下架 -->
<style lang="scss" scoped>
.off-shelf {
  display: inline;
}
</style>

<template>
  <div class="off-shelf order-operation">
    <slot name="button">
      <el-button
        v-waiting="[isBatch ?
          `post::/draft/order/executeOrderTradeStep?orderNo=${firstOrderNo}&type=offShelf`
          : `post::/draft/order/singleExecuteOrderTradeStep?orderNo=${firstOrderNo}&type=offShelf`
        ]"
        v-bind="$attrs"
        :type="$attrs.type || 'primary'"
        :width="$attrs.width || '68'"
        :height="$attrs.height || '40'"
        :loading="loading"
        @click="init"
        v-on="$listeners"
      >
        <slot>下架</slot>
      </el-button>
    </slot>
  </div>
</template>

<script>
/* eslint-disable no-magic-numbers */

import { EXECUTE_TRANSACTION_PROCESS } from '@/constant'
import orderApi from '@/apis/order'
export default {
  name: 'off-shelf',
  props: {
    order: {
      type: [Object, Array],
      required: true
    },
    // 是不是批量，目前用于点击约束的区别
    isBatch: Boolean
  },
  data() {
    return {
      loading: false
    }
  },
  computed: {
    // 是否批量
    hasAllOrder() {
      return this.order instanceof Array
    },
    // 订单号，数组时取第一条数据，用于loading时，不要所有按钮都显示loading状态
    firstOrderNo() {
      return this.hasAllOrder && this.order.length ? this.order[0].orderNo : this.order.orderNo
    }
  },

  methods: {
    init() {
      if (this.order && Array.isArray(this.order) && this.order.length > 99) {
        this.$message.warning(`每次批量操作最多可选择 ${99} 笔订单`)
        return
      }
      // 非批量下架去除二次确认
      if (!this.isBatch) {
        // 确认
        this.postOffShelf(this.order)
        return
      }
      const h = this.$createElement
      this.$msgbox({
        title: '提示',
        dangerouslyUseHTMLString: true,
        message: h('p', null, '确定要下架吗？'),
        confirmButtonText: '确认',
        showCancelButton: true,
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        // 确认
        this.postOffShelf(this.order)
      })
    },

    // 下架请求
    async postOffShelf(order) {
      try {
        this.loading = true
        const orderNoList = this.hasAllOrder ? order.map(i => i.orderNo) : [order.orderNo]
        const param = {
          type: 'offShelf', // 用于点击约束误触发
          orderNoList, // 订单编号
          orderStepsEnumCode: EXECUTE_TRANSACTION_PROCESS.OFF_SHELF_ORDER
        }
        if (this.hasAllOrder) {
          param.type = 'batchOffShelf'
          const data = await orderApi.postExecuteOrderTradeStep(param)
          if (!data.failNum) { // 失败数为0,即全部成功
            this.$message.success('下架成功')
          }
        } else {
          await orderApi.postSingleExecuteOrderTradeStep(param)
          this.$message({
            type: 'success',
            message: '下架成功'
          })
        }
        this.$emit('success')
        this.loading = false
      } catch (error) {
        this.loading = false
      }
    }
  }
}
</script>
