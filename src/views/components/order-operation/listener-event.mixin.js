
import { debounce } from '@/common/js/util'

// 全局监听键盘事件
const listenerEventMixins = {
  created() {
    this.listenerEventdebounce = debounce(this.listenerEvent)
    window.addEventListener('keydown', this.listenerEventdebounce)
  },
  beforeD<PERSON>roy() {
    window.removeEventListener('keydown', this.listenerEventdebounce)
  },
  methods: {
    // 监听enter键盘事件
    listenerEvent(event) {
      if (event.keyCode === 13) {
        // 批量操作时判断loading状态，防止重复触发提交
        !this.loading && this.confirm()
      }
    },
  }
}

export default listenerEventMixins
