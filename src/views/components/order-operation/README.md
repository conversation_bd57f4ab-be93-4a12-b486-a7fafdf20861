## 订单操作相关组件介绍

### 接收参数

- order: `Order|Array<Order>` 订单对象，如果是批量操作传入订单列表  
  - orderNo：订单编号
  - tabStatus:订单状态(催单)
    - 0 待审核
    - 1 待确认
    - 2 待支付
    - 3 待背书
    - 4 待签收
    - 5 校验中
    - 6 取消中
    - 7 已完成
    - 8 已失败
  - responsibleParty：责任方
    - 0 资方
    - 1 票方 
  - initiator:发起方
    - 0 资方
    - 1 票方 
  - interestDays：计息天数（改价）
  - voucher：是否有交易凭证（改价）
  - draftAmount ：票价（改价）
- endorserName：背书企业名称（完成背书）
- tatalPrice：批量时候的总价(批量确认)
-- hasTag: `String|Number` 
- 所有 `el-button` 的 prop

### slot

- default: 自定义按钮内容

### 事件

- success: 操作成功
- 所有 `el-button` 的 事件

### 方法

- `init`: 打开弹窗