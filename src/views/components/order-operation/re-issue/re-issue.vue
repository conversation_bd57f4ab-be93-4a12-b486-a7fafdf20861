<!-- 重新发布 -->
<style lang="scss" scoped>
.re-issue {
  display: inline-block;

  .el-button {
    position: relative;

    &.has-tag::after {
      content: "";
      position: absolute;
      top: -1px;
      left: -1px;
      border-top: 27px solid $color-assist3;
      border-right: 28px solid transparent;
      width: 0;
      height: 0;
    }

    .rice-tag {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      font-size: 12px;
      font-weight: 600;
      line-height: 20px;
      transform: scale(.8);
      transform-origin: top;
      color: $color-FFFFFF;
    }
  }
}
</style>

<template>
  <div class="re-issue order-operation" @click="onReIssueChange($event)">
    <slot name="button">
      <el-button
        v-bind="$attrs"
        :type="$attrs.type || 'primary'"
        :width="$attrs.width || '104'"
        :height="$attrs.height || '40'"
        :size="$attrs.size || 'large'"
        v-on="$listeners"
      >
        <slot>重新发布</slot>
      </el-button>
    </slot>
  </div>
</template>

<script>
import ReIssueDialog from './re-issue-dialog.vue'
import orderOperationMixin from '../order-operation-mixin'

export default {
  name: 'service-intervention',
  mixins: [orderOperationMixin(ReIssueDialog)],
  props: {
    hasTag: [String, Number]
  },
  data() {
    return {
      isShowRelease: false
    }
  },
  computed: {
    limitReleaseInfo() { // 是否关闭重新发布
      return this.$store.state.common.limitReleaseInfo || {}
    }
  },
  methods: {
    // 点击重新发布 tabStatus 8:已下架，10:已失败
    onReIssueChange(event) {
      // 如果是：已下架 tab 下 && 不让重新发布 “||” 已失败 tab 下 && 不让重新发布，就弹出去识票助手弹窗
      // 针对非识票助手单张识别 订单，点击【重新发布  / 批量重新发布】弹窗提示，功能临时关闭；

      // 智付邦+ 限制重新发布
      if (this.order.draftOrderPay.acceptZbankPlus === 1) {
        this.$emit('open-release-limit')
        return
      }
      // 识票助手发布的单张识别/自动识别订单不限制重新发布
      if (this.order.publishSource === 5 && [1, 5].includes(this.order.orderType)) {
        this.init(event)
        return
      }

      // （republishFlag => 0 且 已下架）|| (republishFailFlag=>0 且 已失败)  允许重新发布
      if ((this.order.tabStatus === 8 && this.limitReleaseInfo.republishFlag === 0) || (this.order.tabStatus === 10 && this.limitReleaseInfo.republishFailFlag === 0)) {
        this.init(event)
      } else {
        this.$emit('open-release-limit')
      }

      // if (
      //   (this.order.tabStatus === 8 && this.limitReleaseInfo.republishFlag === 1)
      //   || (this.order.tabStatus === 10 && this.limitReleaseInfo.republishFailFlag === 1)
      //   || ((this.order.tabStatus === 8 || this.order.tabStatus === 10) && !([1, 5].includes(this.order.orderType) && this.order.publishSource === 5))
      //   || this.order.draftOrderPay.acceptZbankPlus === 1
      // ) {
      //   this.$emit('open-release-limit')
      // } else {
      //   this.init(event)
      // }
    }
  }
}
</script>
