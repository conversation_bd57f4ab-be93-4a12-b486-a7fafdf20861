<!-- eslint-disable max-lines -->
<!-- 重新发布弹窗 -->

<style lang="scss" scoped>
.tip-box {
  margin-bottom: 12px;
  font-size: 16px;
}

.body-block {
  overflow: hidden;
  padding: 12px 12px 0;
  background: $color-FFFFFF;

  & + .body-block {
    margin-top: 12px;
  }
}

.form {
  .g-title-small {
    margin-bottom: 10px;
  }

  ::v-deep {
    .el-form-item__content {
      line-height: 1;
    }

    .el-form-item__label {
      display: flex;
      align-items: center;
      margin-bottom: 4px;
      padding: 0;
      height: 22px;
      color: $color-text-secondary;
      line-height: 22px;

      &::before {
        font-weight: bold;
      }
    }

    .el-form-item__error--inline {
      display: inherit;
      margin-left: 0;
    }

    .el-input__inner,
    .el-textarea__inner {
      padding-right: 12px;
      padding-left: 12px;
      font-size: 14px;
    }

    .el-input--prefix .el-input__inner {
      padding-left: 30px;
    }

    .el-checkbox__label {
      font-weight: normal;
    }
  }

  .el-form-item {
    display: flex;
    margin-bottom: 12px;
    flex-flow: column;
  }
}

.small-input {
  margin-right: 8px;
  width: 159px;
}

.medium-input {
  margin-right: 8px;
  width: 237px;
}

::v-deep .ticket-flaw-checkbox {
  margin-top: -8px;
  margin-right: 0;

  .flaw-item {
    margin: 8px 8px 0 0 !important;
    width: 120.5px;

    &:nth-child(7n) {
      margin-right: 0 !important;
    }

    &:last-child {
      width: 249px;
    }
  }
}

::v-deep .issue-draft-block {
  margin-bottom: 0;

  & > .block-flex {
    display: block;
  }

  .half-block {
    width: 100%;
  }

  .block-switch-width {
    max-width: 100%;
  }

  .bargaining-limit {
    margin-left: 16px;
  }
}

.switch-button {
  border-color: $color-D9D9D9;
  padding: 8px 16px;
}

.el-dialog__footer .el-button {
  font-size: 14px;
}

.quoted-price-type {
  display: flex;
  align-items: center;
}

.line {
  display: inline-block;
  margin: 0 12px;
  width: 1px;
  height: 16px;
  background: $color-D9D9D9;
}

::v-deep {
  .price-btn {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 32px;

    span {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 30px;
      font-size: 14px;
      line-height: 21px;
    }

    .btn-icon {
      margin-right: 8px;
      width: 20px;
      height: 20px;
      line-height: 20px;
      vertical-align: middle;
    }
  }
}

.warn-content {
  margin-bottom: 12px;
}

.bught-sphere {
  position: relative;

  .bought-deal {
    top: 0 !important;
  }
}

.split-setting-header {
  margin-bottom: 10px;

  .m-l10 {
    margin-left: 10px;
  }
}

.split-setting-box {
  .title {
    margin-bottom: 4px;
    height: 22px;
  }

  .split-type-cls {
    display: flex;
    align-items: center;

    ::v-deep .el-form-item__error {
      position: absolute;
    }

    .split-line {
      margin: 0 4px;
      line-height: 32px;
    }
  }
}

::v-deep .split-type-btn {
  padding: 5px 10px;

  span {
    display: flex;
    justify-content: center;
    align-items: center;

    .chengjie-swap {
      margin-right: 3px;
    }
  }
}

.split-type-item {
  ::v-deep .el-form-item__error {
    position: absolute;
  }
}

.flx {
  display: flex;
  align-items: center;
}

.flx-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mlr4 {
  margin-right: 4px;
}

.mlr10 {
  margin-right: 10px;
}

.result-flex {
  display: flex;
  margin: 8px 0;

  // ::v-deep .el-form-item__content {
  //   font-weight: bold;
  //   line-height: 1;
  // }
  .result-label {
    margin-right: 8px;
    font-size: 14px;
    font-weight: 400;
    color: $color-text-light;
  }

  .result-value {
    font-size: 14px;
    font-weight: 600;
  }
}

.red-font {
  color: $color-warning;
}
</style>

<style lang="scss">
.alert-red-high-light {
  font-weight: 600;
  color: $--color-primary;
}
</style>

<template>
  <el-dialog
    width="964px"
    :visible.sync="visible"
    append-to-body
    :close-on-click-modal="false"
    title="重新发布"
    class="re-issue-dialog order-operation-dialog"
    :before-close="handleClose"
  >
    <div v-if="false" class="bught-sphere">
      <BoughtDeal ref="boughtDealRef">
        <div class="go-icon" @click="goCounpCerter">
          <icon type="header-notice-logo" size="20" />
        </div>
      </BoughtDeal>
    </div>
    <el-form
      ref="ruleForm"
      :model="form"
      :rules="rules"
      label-position="top"
      class="form"
      :inline-message="true"
      @validate="validateResult"
    >
      <div class="body-block">
        <div class="flx-between">
          <div class="flx">
            <div class="g-title-small">报价信息</div>
            <div class="split-setting-header flx">
              <el-switch
                v-model="form.splitFlag"
                class="m-l10"
                :active-value="1"
                :inactive-value="0"
                :width="40"
                @change="handelSplitFlag"
              />
              <span class="margin-left-8 m-l10">接受拆分</span>
              <el-button
                class="border-grey m-l10 split-type-btn"
                type="primary"
                size="small"
                :disabled="!form.splitFlag"
                round
                border
                @click="handlesplitType"
              >
                <icon class="icon icon-switch" type="chengjie-swap" />
                {{ form.splitType === 0 ? '按金额x张数' : '按可拆分金额' }}
              </el-button>
            </div>
          </div>
          <el-tooltip :disabled="!quotationBtnDisabled" content="票据到期日 ≤30 天或信息不完整，无法提供报价行情参考">
            <el-button
              v-waiting="'post::loading::/draft/order/enquiryPrice'"
              :class="quotationBtnDisabled && 'is-disabled'"
              class="price-btn"
              type="primary"
              border
              height="32"
              size="small"
              @click="!quotationBtnDisabled && showQuotationReferenceDialog()"
            >
              <icon type="chengjie-dynamic" class="btn-icon" />行情报价参考
            </el-button>
          </el-tooltip>
        </div>

        <div class="flx split-setting-box">
          <div class="flex-shrink">
            <div class="title">
              {{ form.splitType === 0 ? '设置允许资方拆分接单金额' : '设置允许资方拆分接单张数' }}
              <el-tooltip placement="top">
                <div slot="content" class="tips-content">
                  <span v-if="!form.splitType">您可设置新票允许拆分金额范围(最低可拆分金额 {{ getMinSplitConfig() }}
                    元)，资方将按您设置的金额范围拆分接单。票面金额小于 {{ getMinSplitConfig() }} 元时仅支持全额接单。选择定额拆分仅支持输入1及以上的整数。</span>
                  <span v-else>合计拆分金额须与票面总金额保持一致，系统将根据拆分规则发布连号票(子票区间随机生成)</span>
                </div>
                <icon class="icon-question" type="chengjie-wenti" />
              </el-tooltip>
            </div>
            <div class="split-type-cls">
              <!-- 按可拆分金额拆分 -->
              <el-form-item v-if="form.splitType === 0" class="split-type-item" prop="splitAmtMin">
                <template v-if="!form.splitMethod">
                  <el-input
                    v-model="form.splitAmtMin"
                    placeholder="最低可拆分金额"
                    :disabled="!draftAmount || !form.splitFlag"
                    :width="125"
                    type="number"
                    size="small"
                    :number-format="{
                      negative: false,
                      maxDecimalLength: 2,
                      maxIntegerLength: 8,
                    }"
                  >
                    <template slot="append">元</template>
                  </el-input>
                  <span class="split-line mlr4">-</span>
                  <el-input
                    v-model="form.splitAmtMax"
                    placeholder="最高可拆分金额"
                    class="mlr4"
                    :disabled="!draftAmount || !form.splitFlag"
                    :width="125"
                    type="number"
                    size="small"
                    :number-format="{
                      negative: false,
                      maxDecimalLength: 2,
                      maxIntegerLength: 8,
                    }"
                  >
                    <template slot="append">元</template>
                  </el-input>
                  <el-button
                    class="border-grey mlr4 split-type-btn"
                    type="primary"
                    round
                    border
                    :disabled="splitSettingDisabled || !form.splitFlag || wan2yuan(draftAmount) < getMinSplitConfig()"
                    @click="handleChangeSplitType"
                  >
                    <icon class="icon icon-switch" type="chengjie-swap" />
                    {{ form.splitMethod ? '区间拆分' : '定额拆分' }}
                  </el-button>
                </template>
                <!-- 定额拆分 -->
                <template v-else>
                  <el-input
                    v-model="form.splitAmtInt"
                    placeholder="金额"
                    :disabled="splitSettingDisabled || !form.splitFlag"
                    class="mlr4"
                    :width="70"
                    type="number"
                    size="small"
                    :number-format="{
                      decimal: false,
                      negative: false,
                      leadingZero: false,
                      maxDecimalLength: 0,
                      maxIntegerLength: 4,
                    }"
                    @input.native="handleInputSplitAmount"
                  >
                    <template slot="append">万</template>
                  </el-input>
                  <el-select
                    v-model="form.splitAmtIntSelect"
                    placeholder="--"
                    size="small"
                    style="width: 70px;"
                    :disabled="splitSettingDisabled || !form.splitFlag"
                    popper-class="default"
                    class="split-method-select mlr4"
                    @change="handleSelectSplitAmount"
                  >
                    <el-option
                      v-for="item in INTEGER_SPLIT_AMOUNT"
                      :key="item.id"
                      :label="item.label"
                      :value="item.id"
                    />
                  </el-select>
                  <el-tooltip placement="top">
                    <div slot="content" class="tips-content">
                      {{ form.intMultiple ? '表示支持按整数倍拆分' : '表示不支持按整数倍拆分' }}
                    </div>
                    <el-checkbox
                      v-model="form.intMultiple"
                      class="mlr4"
                      :disabled="splitSettingDisabled || !form.splitFlag"
                      :true-label="1"
                      :false-label="0"
                    >
                      支持整数倍
                    </el-checkbox>
                  </el-tooltip>
                  <el-button
                    class="border-grey mlr4 split-type-btn"
                    type="primary"
                    round
                    border
                    :disabled="!order.draftAmount || (splitSettingDisabled) || !form.splitFlag"
                    @click="handleChangeSplitType"
                  >
                    <icon class="icon icon-switch" type="chengjie-swap" />
                    {{ form.splitMethod ? '区间拆分' : '定额拆分' }}
                  </el-button>
                </template>
              </el-form-item>
              <!-- 按金额x张数拆分 -->
              <el-form-item v-else class="split-type-item" prop="splitAmtAndCount">
                <div class="flx">
                  <el-input
                    v-model="form.splitAmt"
                    placeholder="拆分金额"
                    :width="113"
                    type="number"
                    :disabled="splitSettingDisabled || !form.splitFlag"
                    size="small"
                    :number-format="{
                      negative: false,
                      maxDecimalLength: 6,
                      maxIntegerLength: 4,
                    }"
                    @input="(val) => handleBillingMethod(val)"
                  >
                    <template slot="append">万</template>
                  </el-input>
                  <span style="margin: 0 4px;">x</span>
                  <el-input
                    v-model="form.splitCount"
                    placeholder="拆分张数"
                    class="mlr4"
                    :disabled="splitSettingDisabled || !form.splitFlag"
                    :width="113"
                    type="number"
                    size="small"
                    :number-format="{
                      decimal: false,
                      negative: false,
                      leadingZero: false,
                      maxDecimalLength: 0,
                      maxIntegerLength: 4,
                    }"
                    @input="() => handleBillingMethod(form.splitAmt)"
                  >
                    <template slot="append">张</template>
                  </el-input>
                </div>
              </el-form-item>
            </div>
          </div>
          <div>
            <el-form-item label="报价方式" class="split-type-item" prop="billingMethodValidate">
              <div class="quoted-price-type">
                <template v-if="form.billingMethod">
                  <el-input
                    v-model="form.annualInterest"
                    placeholder="利率"
                    type="number"
                    size="small"
                    class="small-input"
                    :number-format="{
                      negative: false,
                      maxDecimalLength: 4,
                      maxIntegerLength: 2,
                      leadingZero: false
                    }"
                    @input="handleBillingMethod"
                  >
                    <template slot="append">%</template>
                  </el-input>
                  <el-input
                    v-model="form.serviceCharge"
                    placeholder="每十万手续费"
                    type="number"
                    size="small"
                    class="small-input"
                    :number-format="yuanNumberFormat"
                    @input="handleBillingMethod"
                  >
                    <template slot="append">元</template>
                  </el-input>
                </template>
                <template v-else>
                  <el-input
                    v-model="form.lakhDeduction"
                    placeholder="每十万扣款"
                    type="number"
                    size="small"
                    class="medium-input"
                    :number-format="yuanNumberFormat"
                    @input="handleBillingMethod"
                  >
                    <template slot="append">元</template>
                  </el-input>
                </template>
                <el-button
                  type="primary"
                  round
                  border
                  class="switch-button"
                  @click="handleChangeType"
                >
                  <icon class="icon icon-switch" type="chengjie-swap" />
                  {{ form.billingMethod ? '每十万扣款' : '利率+每十万手续费' }}
                </el-button>
              </div>
            </el-form-item>
          </div>
        </div>

        <div class="result-flex">
          <p class="mlr10">
            <span class="result-label">每十万扣款</span>
            <span class="result-value">{{ form.lakhDeduction || '0.00' }}元</span>
          </p>
          <p class="mlr10">
            <span class="result-label">年化利率</span>
            <span class="result-value">{{ annualInterestCalc || '0.00' }}%</span>
          </p>
          <!-- 拆分关闭 -->
          <p v-if="!form.splitFlag" class="mlr10">
            <span class="result-label">到账金额</span>
            <span class="result-value red-font">{{ receivedAmount || 0 }}万元</span>
          </p>
          <!-- 拆分开启 && 按金额*张数拆分 -->
          <p v-if="form.splitFlag && form.splitType === 1" class="mlr10">
            <span class="result-label">单张到账金额</span>
            <span class="result-value red-font">{{ singleReceivedAmount || 0 }}万元</span>
          </p>
          <!-- 拆分开启  -->
          <p v-if="form.splitFlag" class="mlr10">
            <span class="result-label">总到账金额</span>
            <span class="result-value red-font">{{ totalReceivedAmount || 0 }}万元</span>
          </p>
          <p class="mlr10">
            <span class="result-label">票面金额</span>
            <span class="result-value red-font">{{ yuan2wan(draftAmount) || 0 }}万元</span>
          </p>
        </div>
        <el-form-item label="瑕疵（可多选）" prop="ticketFlaw">
          <TicketFlawCheckbox
            ref="ticketFlawRefs"
            :is-disabled="isDiscernTag ? true : false"
            :default-value="ticketFlawForm"
            @change="handleChangeFlaw"
          />
        </el-form-item>
      </div>
      <OtherOptions
        v-if="visible"
        ref="otherOptionsRef"
        :is-agent-order="order && !!order.agentOrder"
        :is-re-issue="true"
        :draft-amount="yuan2wan(draftAmount)"
        :default-value="otherOptionsForm"
        :fast-trade="hasAllOrder ? !!order[0].fastTrade : !!order.fastTrade"
        :accepter-type="order && order.acceptorType"
        :is-disable-ylbank-channel="isDisableYlbankChannel"
        :is-new-version-draft="isNewVersionDraft"
        :seller-bank-account-id="Number(order.sellerBankAccountId)"
        @close-dialog="visible = false"
        @change-form="handleChangeForm"
      />
    </el-form>
    <template slot="footer">
      <el-button width="76" @click="handleClose">取消</el-button>
      <transaction-tooltip-button
        :types="form.fastTrade ? [TRANSACTION_TOOLTIP_TYPE.HOLIDAY, TRANSACTION_TOOLTIP_TYPE.DAILY] : [TRANSACTION_TOOLTIP_TYPE.HOLIDAY]"
        type="primary"
        border
        width="104"
        @click="handleConfirm(true)"
      >
        定向发布
      </transaction-tooltip-button>
      <transaction-tooltip-button
        v-waiting="[`post::/draft/order/executeOrderTradeStep?orderNo=${firstOrderNo}`,
                    `post::/draft/order/singleExecuteOrderTradeStep?orderNo=${firstOrderNo}`]"
        type="primary"
        width="76"
        :types="[TRANSACTION_TOOLTIP_TYPE.CREDIT]"
        @click="handleConfirm()"
      >
        确定
      </transaction-tooltip-button>
    </template>
    <!-- 实名认证 -->
    <!-- <VerifiedDialog ref="verifiedDialogRef" /> -->
    <!-- 定向发布弹窗 -->
    <el-dialog
      title="定向发布"
      :visible.sync="directionalVisible"
      :close-on-click-modal="false"
      :before-close="handleDirectionalDialogCancel"
      append-to-body
      width="600px"
    >
      <WarnContent class="warn-content">定向单暂不支持议价，继续发布定向单将以非议价订单发布！</WarnContent>
      <div class="body-block ">
        <el-form class="form" :inline-message="true">
          <el-form-item label="对方" :error="inviteCodeErrorMsg">
            <el-input v-model="inviteCode" placeholder="对方定向码/手机号" />
          </el-form-item>
        </el-form>
      </div>
      <template slot="footer">
        <el-button size="large" @click="handleDirectionalDialogCancel">取消</el-button>
        <el-button
          v-waiting="['get::/corp/blackConfig/searchCorp']"
          type="primary"
          size="large"
          :loading="directionalBtnStatus"
          @click="handleDirectionalDialogConfirm"
        >
          确定
        </el-button>
      </template>
    </el-dialog>
    <!-- 批量发布结果 -->
    <ResultDialog ref="resultDialogRef" handle-str="重新发布" @close="closeResultDialog" />
    <!-- 信用低确认提醒 -->
    <CreditLowConfirm ref="creditLowConfirmRef" />
    <!-- 支付账户异常弹窗 -->
    <PayAccountError ref="payAccountErrorRef" />
    <!-- 报价参考弹窗 -->
    <QuotationReferenceDialog ref="quotationReferenceDialogRef" />
    <!-- 西部信托利率提示弹窗 -->
    <RateTipsDialog ref="rateTipsRef" @on-publish="() => handleReIssue()" />
  </el-dialog>
</template>

<script>
import WarnContent from '@/views/components/common/warn-content.vue' // 警告文本
import { mapGetters, mapActions } from 'vuex'
import TicketFlawCheckbox from '@/views/components/issue-draft/ticket-flaw-checkbox.vue' // 瑕疵选择组件
import OtherOptions from '@/views/components/issue-draft/other-options.vue' // 其他选项
// import VerifiedDialog from '@/views/components/common/verified-dialog/verified-dialog.vue'// 实名认证弹窗
import ResultDialog from '@/views/components/common/result-dialog/result-dialog.vue' // 批量操作结果组件
import PublishCheck from '@/views/pages/issue-draft/mixins/publish-check.js'

// import WarnContent from '@/views/components/common/warn-content.vue' // 警告文本
import CreditLowConfirm from '@/views/pages/issue-draft/components/credit-low-confirm.vue' // 信用低确认提醒
import orderApi from '@/apis/order' // 接口
import spTradeApi from '@/apis/sp-trade'
import { yuan2wan, wan2yuan, fen2yuan, yuan2fen } from '@/common/js/number' // 元转万
import {
  interestRateMath, // * 以利率计算 => 每十万扣款 年化利率 和 到账金额
  lakhDeductionMath, // * 每十万扣款计算 => 年化利率 和 到账金额
} from '@/common/js/draft-math'
import {
  BILLING_METHOD_CODE, // 报价方式
  EXECUTE_TRANSACTION_PROCESS, // 执行交易流程场景值
  ORDER_STATUS, // 订单状态
  NEED_VOUCHER, // 交易凭证
  PAYMENT_CHANNEL
} from '@/constant.js' // 常量
import { ISSUE_DRAFT_ERROR_CODE, INTEGER_SPLIT_AMOUNT } from '@/constants/draft.js' // 发布票据错误code
import { CREDIT_LEVEL_CODE, CREDIT_LEVEL_NAME_MAP } from '@/constants/credit' // 信用等级
import { VALID_FAIL_TYPE } from '@/constants/sp-trade'
import store from '@/store'
import userObj from '@/utils/user.js' // 用户对象操作
import { isWealthCommerceDraft } from '@/utils/util'
import PayAccountError from '@/views/components/pay-account-error/pay-account-error.vue'// 支付账户异常弹窗
import { windowCommunication } from '@/utils/window-event' // 窗口通讯
import { TRANSACTION_TOOLTIP_TYPE } from '@/constants/transaction-tooltip'// 支付账户异常弹窗
import { PLATFORM_DEFAULT_RULESNEW_URL } from '@/constants/oss-files-url' // 平台订单违约规则url
// import directionPublishSwitchMixins from '@/mixins/direction-publish-switch'
import issueDraftApi from '@/apis/issue-draft'
import { getDateSpace } from '@/common/js/date'
import QuotationReferenceDialog from '@/views/components/quotation-reference-dialog/quotation-reference-dialog.vue' // 报价参考弹窗
import BoughtDeal from '@/views/components/levitated-sphere/bought-deal.vue'
import RateTipsDialog from '@/views/components/issue-draft/rate-tips-dialog.vue'
import BigNumber from 'bignumber.js'

// 初始表单字段
const defaultForm = {
  billingMethod: BILLING_METHOD_CODE.SHI_WAN_DISCOUNT, // 计费方式0-十万直扣1-年利率加手续费
  lakhDeduction: '', // 每十万扣款
  annualInterest: '', // 年利率
  serviceCharge: '', // 手续费
  defectsAba: 0, // 回出票人aba瑕疵票 （瑕疵字段，重新发布需要传所有瑕疵）
  defectsAbca: 0, // 回出票人abca瑕疵票
  defectsReturnFront: 0, // 回收款人瑕疵票
  defectsTurnAround: 0, // 背书回头瑕疵票
  defectsDuplicated: 0, // 背书重复瑕疵票
  defectsAbb: 0, // abb瑕疵票
  defectsPledgeEndorsement: 0, // 质押瑕疵票
  defectsInconformity: 0, // 上下不一致瑕疵票
  defectsPromise: 0, // 保证瑕疵票
  defectsOther: 0, // 其它瑕疵票
  defectsOtherDesc: '', // 其它瑕疵票描述

  splitFlag: 0, // 拆分开关
  splitType: 1, // 拆分类型 1-按金额*张数 0-按可拆分金额
  splitAmt: '', // 拆分金额
  splitCount: '', // 拆分张数
  splitMethod: 1, // 拆分模式 1-定额拆分 0-区间拆分
  splitAmtMin: '', // 最小拆分金额
  splitAmtMax: '', // 最大拆分金额
  splitAmtInt: '', // 定额拆分输入金额
  splitAmtIntSelect: '', // 定额拆分金额选项
  intMultiple: 1, // 支持整数倍
  splitSetting: [] // 按金额*张数拆分数据存储
}
export default {
  name: 're-issue-dialog',

  components: {
    TicketFlawCheckbox,
    OtherOptions,
    // VerifiedDialog,
    ResultDialog,
    WarnContent,
    CreditLowConfirm,
    PayAccountError,
    QuotationReferenceDialog,
    BoughtDeal,
    RateTipsDialog
  },
  mixins: [PublishCheck],
  provide() {
    return {
      closeReIssueDialog: this.handleClose
    }
  },
  data() {
    // 报价信息校验
    let validateMethod = (rule, value, callback) => {
      const { billingMethod, lakhDeduction, serviceCharge, annualInterest } = this.form
      // 分别校验两种方式下的输入框
      if ((billingMethod === BILLING_METHOD_CODE.SHI_WAN_DISCOUNT && !lakhDeduction)
      || (billingMethod === BILLING_METHOD_CODE.ANNUAL_INTEREST && !annualInterest && !serviceCharge)) {
        callback(new Error('请输入报价信息'))
      } else if ((billingMethod === BILLING_METHOD_CODE.SHI_WAN_DISCOUNT && lakhDeduction <= 0)
      || (billingMethod === BILLING_METHOD_CODE.ANNUAL_INTEREST && annualInterest <= 0 && serviceCharge <= 0)) {
        callback(new Error('请输入大于0的数字'))
      } else {
        callback()
      }
    }
    // 瑕疵校验
    const validateTicketFlaw = (rule, value, callback) => {
      // 分别校验两种方式下的输入框
      if (!this.noFlaw) {
        callback(new Error('请选择瑕疵'))
      } else {
        callback()
      }
    }

    // 拆分区间校验
    const validateSplitDraftRange = (rule, value, callback) => {
      if (this.form.splitFlag) {
        if (this.form.splitMethod) {
          if (!this.form.splitAmtInt) {
            callback(new Error('请输入拆分金额'))
          } else {
            let { draftAmount } = this
            let min = wan2yuan(this.form.splitAmtInt)
            // eslint-disable-next-line max-depth
            if (min < this.getMinSplitConfig()) {
              callback(new Error(`拆分金额需大于或等于${this.getMinSplitConfig()}元`))
            } else if (min > draftAmount) {
              callback(new Error('拆分金额不能大于票面金额'))
            } else {
              callback()
            }
          }
        } else {
          if (!this.form.splitAmtMin || !this.form.splitAmtMax) {
            callback(new Error('请输入可拆分金额区间'))
          }
          let { draftAmount } = this
          let min = Number(this.form.splitAmtMin)
          let max = Number(this.form.splitAmtMax)
          if (draftAmount > this.getMinSplitConfig()) {
            // eslint-disable-next-line max-depth
            if (min < this.getMinSplitConfig()) {
              callback(new Error(`最低可拆分金额需大于或等于${this.getMinSplitConfig()}元`))
            } else if (min > max) {
              callback(new Error('最高可拆分金额需大于或等于最低可拆分金额'))
            } else if (max > draftAmount) {
              callback(new Error('最高可拆分金额不能大于票面金额'))
            } else {
              callback()
            }
          } else if (min !== draftAmount) {
            callback(new Error(`票面金额小于${this.getMinSplitConfig()}元时,最低可拆分金额需等于票面金额`))
          } else if (max !== draftAmount) {
            callback(new Error(`票面金额小于${this.getMinSplitConfig()}元时,最高可拆分金额需等于票面金额`))
          } else {
            callback()
          }
        }
      } else {
        callback()
      }
    }
    const validateSplitAmtAndCount = (rule, value, callback) => {
      // 按金额*张数 拆分金额校验
      if ((!this.form.splitAmt || !this.form.splitCount) && this.form.splitFlag && !this.splitSettingDisabled) {
        callback(new Error('请填写拆分金额及张数'))
      }
      callback()
    }
    return {
      TRANSACTION_TOOLTIP_TYPE, // 交易限制提示类型
      INTEGER_SPLIT_AMOUNT,
      wan2yuan,
      visible: false, // 模态框是否显示
      order: null, // 当前操作的订单
      rules: {
        billingMethodValidate: [{ required: true, validator: validateMethod, trigger: 'none' }], // 报价信息校验，至少填写一个
        ticketFlaw: [{ required: true, validator: validateTicketFlaw, trigger: 'none' }],
        splitAmtMin: [{ required: false, validator: validateSplitDraftRange, trigger: ['change', 'blur'] }],
        splitAmtAndCount: [{ required: false, validator: validateSplitAmtAndCount, trigger: ['change', 'blur'] }]
      }, // 校验规则
      form: { ...defaultForm }, // 表单信息
      ticketFlawForm: {}, // 用于瑕疵组件回显
      formFailField: {}, // 存一下表单校验不通过的字段，true为不通过，通过监听表单的validate
      otherOptionsForm: {}, // 用于其他选项表单信息回显
      draftAmount: '', // 票据金额
      directionalVisible: false, // 定向发布弹窗
      inviteCode: '', // 定向标识(手机号或者定向码）
      inviteCodeErrorMsg: '', // 定向标识错误提示
      isNewVersionDraft: false, // 是否新票
      // 元 输入格式化
      yuanNumberFormat: {
        negative: false,
        maxDecimalLength: 2,
        maxIntegerLength: 5,
        leadingZero: false
      },
      isDiscernTag: null, // 是否带签标签
      loading: false,
      directionalBtnStatus: false, // 定向发布确认按钮 是否可点击
      annualInterestCalc: '', // 计算出来的利率
      receivedAmount: 0, // 到账金额
    }
  },

  computed: {
    // 是否批量
    hasAllOrder() {
      return this.order instanceof Array
    },
    // 订单号，数组时取第一条数据，用于loading时，不要所有按钮都显示loading状态
    firstOrderNo() {
      if (!this.order) {
        return
      }
      return this.hasAllOrder && this.order.length ? this.order[0].orderNo : this.order.orderNo
    },
    // 我的信用信息
    ...mapGetters('user', {
      creditInfo: 'creditInfo'
    }),
    // 无三要素之一不可点报价参考（票据到期日 ≤30 天或信息不完整，无法提供报价行情参考）
    quotationBtnDisabled() {
      const order = this.hasAllOrder ? this.order[0] : this.order
      const { acceptorName, draftAmount, maturityDate } = order
      const spaceDate = getDateSpace(maturityDate, new Date())
      return !acceptorName || !draftAmount || !maturityDate || spaceDate <= 30
    },
    // 大于500W的票禁用亿联银行支付渠道
    isDisableYlbankChannel() {
      const LIMIT_AMOUNT = 100000 * 10000
      return this.hasAllOrder ? this.order.some(item => item.draftAmount > LIMIT_AMOUNT) : this.order?.draftAmount > LIMIT_AMOUNT
    },
    // 拆分设置规则是否禁用
    splitSettingDisabled() {
      if (this.hasAllOrder) return false // 批量不处理
      return !this.order.draftAmount || !this.form.splitFlag || !this.order.interestDays
    },

    // 拆分开启-总到账金额
    totalReceivedAmount() {
      const { splitFlag, splitType } = this.form
      if (splitFlag) { // 拆分开启
        if (!splitType) { // 按可拆分金额拆分
          return this.receivedAmount || 0
        } else { // 按金额*张数拆分
          return new BigNumber(this.receivedAmount || 0).times(this.form.splitCount || 0)
            .toNumber()
        }
      }
      return 0
    },
    // 按金额*张数拆分-单张账金额 单张到账金额
    singleReceivedAmount() {
      const { splitFlag, splitType, splitAmt, splitCount } = this.form
      return splitFlag && splitType && splitAmt && splitCount ? new BigNumber(this.receivedAmount || 0)
        .toNumber() : 0
    },
    // 是否区间拆分 splitFlag === 1 && splitType === 0 按可拆分金额拆分 && integerMultiples === 0 为区间拆分
    isIntervalSplit() {
      if (this.hasAllOrder) return false // 批量不处理
      return this.order.splitFlag && this.order.splitType === 0 && this.order.integerMultiples === 0
    },
    // 是否定额拆分 splitFlag === 1 && splitType === 0 按可拆分金额拆分 && integerMultiples === 1 || 2 为定额拆分
    isQuotaSplit() {
      if (this.hasAllOrder) return false // 批量不处理
      return this.order.splitFlag && this.order.splitType === 0 && [1, 2].includes(this.order.integerMultiples)
    },
  },

  methods: {
    yuan2wan,
    ...mapActions('user', {
      getMyCredit: 'getMyCredit', // 获取我的信用信息
    }),
    goCounpCerter() {
      this.handleClose()
      this.$router.push('/user-center/coupon?tabs=receiveCentre')
    },
    // 初始化
    async init() {
      // 是否有渠道,没有则弹窗
      await store.dispatch('user/getNoBanAccountList')
      const hasChannel = !!store.state.user.noBanAccountList.length
      if (!hasChannel) {
        userObj.openCustomerServiceDialog()
        return
      }

      // 拦截“7”和“8”开头的新一代票据发布
      if ((this.hasAllOrder ? this.order : [this.order]).some(draft => /^[78]/.test(draft.draftNo))) {
        this.$message.error('暂不支持发布，银行暂不支持7、8开头的供应链票据流转')
        return
      }

      // 商票交易权限检测
      await this.checkSpTradeAccess()

      let order = {}
      // 有多张的时候默认拿第一张来显示
      order = this.hasAllOrder ? this.order[0] : this.order

      // 审核下架的订单，默认需要重新整张发布
      if (order?.orderStatus === ORDER_STATUS.REVIEW_OFF_SHELF.id) {
        this.handleOffShelf(order)
        return
      }

      this.isDiscernTag = order.discernTag
      this.form.billingMethod = order?.billingMethod
      this.form.lakhDeduction = order?.lakhFee
      this.form.annualInterest = order?.annualInterest
      this.form.serviceCharge = ''
      this.draftAmount = order?.draftAmount || 0
      this.isNewVersionDraft = order.draftType === 1

      // 拆分规则回显
      this.form.splitFlag = order?.splitFlag || 0 // 是否开启拆分
      if (order?.splitFlag) { // 拆分开启
        this.form.splitType = [0, 1].includes(order?.splitType) ? order?.splitType : 1 // 默认按金额*张数
      } else { // 拆分关闭
        this.form.splitType = 1 // 默认按金额*张数
      }
      // 是否是定额拆分
      if (this.isQuotaSplit) {
        this.form.splitMethod = 1 // 定额拆分
        this.form.intMultiple = order?.integerMultiples
        this.form.splitAmtInt = yuan2wan(fen2yuan(order?.splitAmtMin))
        this.form.splitAmtIntSelect = order?.splitAmtIntSelect ? String(order?.splitAmtIntSelect) : null
      }
      // 是否区间拆分
      if (this.isIntervalSplit) {
        this.form.splitMethod = 0
        this.form.splitAmtMin = fen2yuan(order?.splitAmtMin)
        this.form.splitAmtMax = fen2yuan(order?.splitAmtMax)
      }

      // 瑕疵对象中，无瑕疵是所有字段都不为null，空，0
      const noFlaw = !Object.keys(order?.defects).some(key => order?.defects[key] !== 0 && order?.defects[key] !== null && order?.defects[key] !== '')
      // 这里不能直接使用this.ticketFlawForm.noFlaw 赋值，会让子组件无法监听到对象的改变
      if (noFlaw) {
        this.ticketFlawForm = {
          noFlaw
        }
      } else {
        this.ticketFlawForm = { ...order?.defects }
      }
      this.otherOptionsForm = { ...order, ...order.draftOrderPay, needVoucher: Boolean([NEED_VOUCHER.SELLER.id, NEED_VOUCHER.BOTH.id].includes(order.needVoucher)) }
      await this.getMyCredit()
      this.visible = true
      // this.$nextTick().then(() => {
      //   this.$refs.boughtDealRef.init()
      // })
    },

    // 商票/财票--交易准入查询
    async checkSpTradeAccess() {
      // 判断是  商票或者是财票  都要加上白名单判断
      if (this.hasAllOrder ? this.order.every(draft => !isWealthCommerceDraft(draft.draftNo, draft.acceptorType)) : !isWealthCommerceDraft(this.order.draftNo, this.order.acceptorType)) return

      const corpId = this.$store.state?.user?.corpInfo?.id
      const { pass, failType } = await spTradeApi.whiteListCheck({
        corpId,
        accepterList: this.hasAllOrder ? this.order.filter(draft => isWealthCommerceDraft(draft.draftNo, draft.acceptorType)).map(draft => ({
          accepter: draft.acceptorName,
          amount: draft.draftAmount
        })) : [{ accepter: this.order.acceptorName, amount: this.order.draftAmount }]
      })
      if (pass) return
      // if (failType === VALID_FAIL_TYPE.NO_ACCESS.id) {
      //   await this.$confirm('暂无发布商票订单权限，仍需发布请先发起权限申请', '提示', {
      //     type: 'warning',
      //     confirmButtonText: '发起申请',
      //     cancelButtonText: '取消'
      //   })
      //   const hasApply = await spTradeApi.existUnderReviewAccessCorpApply(corpId)
      //   if (hasApply) {
      //     this.$message.warning('已有申请正在审核中，请耐心等待')
      //   } else {
      //     this.$router.push('/user-center/apply-sp-permission')
      //   }
      // } else
      if (failType === VALID_FAIL_TYPE.NO_CREDIT.id) {
        this.$alert('该承兑人不在授信白名单，请联系您的客户经理!', '提示')
        // await this.$confirm(`承兑人「${accepterNameList.map(item => item.accepter).join()}」暂不在授信名单内，仍需发布请先发起授信申请`, '提示', {
        //   type: 'warning',
        //   confirmButtonText: '发起申请',
        //   cancelButtonText: '取消'
        // })
        // const temp = window.open(`${this.$store.state.spbUrl}`)
        // this.$store.dispatch('corp-info/getCorpInfoSync', '/issue/white').then(res => {
        //   temp.location.href = res
        // })
      } else if (failType === VALID_FAIL_TYPE.NO_AMOUNT.id) {
        this.$alert('该承兑人授信额度不足，若仍需发布请联系您的客户经理', '提示')
      } else if (failType === VALID_FAIL_TYPE.NO_ABNORMAL.id) {
        this.$alert('该承兑人白名单状态异常，请联系您的客户经理!', '提示')
      }
      return Promise.reject(new Error('商票交易核验未通过'))
    },

    // 确定重新发布 isDirection => 是不是定向发布
    handleConfirm(isDirection) {
      let priceSettingValidate = true
      this.$refs.ruleForm.validate(valid => {
        priceSettingValidate = valid
      })
      const otherOptionValidate = this.$refs.otherOptionsRef.validateForm() // 其他选项 表单校验结果
      if (!otherOptionValidate || !priceSettingValidate) {
        return
      }

      // 拆分开启 且是 按金额*张数拆分 合计拆分金额 必须等于票面金额
      const { splitFlag, splitType, splitAmt, splitCount } = this.form
      if (splitFlag && splitType) {
        // 拆分金额*张数 === 票面金额
        if (new BigNumber(splitAmt).times(splitCount)
          .toNumber() !== yuan2wan(this.draftAmount)) {
          this.$message.warning('合计拆分金额必须等于票面金额')
          return
        }
      }
      // if (!this.form.mobile || !this.form.mobile.length) return this.$message.warning('请先设置联系方式')

      // 我的信用为一般时没开保证金提醒
      if ([CREDIT_LEVEL_CODE.NORMAL.id, CREDIT_LEVEL_CODE.MIDDLE.id].includes(this.creditInfo.currentCreditType) && !this.form.margin && !isDirection) {
        this.$refs.creditLowConfirmRef.init({
          useScenes: 'issue-draft',
          creditLevelName: CREDIT_LEVEL_NAME_MAP[this.creditInfo.currentCreditType],
          confirmCallBack: () => {
            this.otherOptionsForm.margin = 1
          }
        })
        return
      }

      // 智联通支付&&票面金额大于500万，贸易合同及发票必填
      let draftAmountWan = yuan2wan(this.draftAmount)
      if ((this.form[PAYMENT_CHANNEL.YI_LIAN_BANK.key] === 1) && parseFloat(draftAmountWan) > 500) {
        const { tradeContractUrl, invoiceUrl } = this.form
        if (!tradeContractUrl && invoiceUrl) {
          return this.$message.warning('贸易合同必须上传')
        } else if (tradeContractUrl && !invoiceUrl) {
          return this.$message.warning('发票必须上传')
        } else if (!tradeContractUrl && !invoiceUrl) {
          return this.$message.warning('贸易合同及发票必须上传')
        }
      }

      // 判断手机号未设置时，必须设置
      // if (this.contactList.length === 0) {
      //   this.$message.warning('请先设置手机号')
      //   return
      // }

      // const { contactList } = this.$refs.otherOptionsRef.$refs.ContactSettingField
      // if (contactList.length === 0) {
      //   this.$message.warning('请先设置手机号')
      //   return
      // }

      // 是不是定向发布
      if (isDirection) {
        this.directionalVisible = true
        // 议价按钮开启才显示
        // if (this.form.bargaining) {
        //   this.$confirm('若选择定向发布，将自动为您关闭议价选项', '提示', {
        //     confirmButtonText: '确认',
        //     cancelButtonText: '我再想想',
        //     type: 'warning'
        //   }).then(() => {
        //     this.directionalVisible = true
        //     this.otherOptionsForm.bargaining = 0
        //     this.otherOptionsForm.bargainingLimit = 0
        //   })
        // } else {
        //   this.directionalVisible = true
        // }
      } else {
        let acceptor = {
          acceptorName: this.order.acceptorName,
          annualInterest: this.form.annualInterest,
          interestDays: this.order.interestDays
        }
        this.checkIntersetRate({ acceptorOrders: [acceptor], type: 1 }, {}).then(res => {
          if (!res?.showPopUpFlag) {
            this.handleReIssue()
          }
        })
      }
    },

    // 重新发布请求接口
    async handleReIssue() {
      this.directionalBtnStatus = true
      const form = {
        isShowBatchError: false, // 是否统一处理批量操作结果
        showError: false, // 是否在axios中处理错误
      }
      // 拆分开启 且是 按金额*张数拆分 设置splitStting数据供接口使用
      const { splitFlag, splitType, splitAmt, splitCount, splitMethod, splitAmtMin, splitAmtMax, intMultiple, splitAmtInt, splitAmtIntSelect, lakhDeduction, serviceCharge, billingMethod } = this.form
      if (splitFlag && splitType) {
        this.form.splitSetting.length = 0
        this.form.splitSetting.push({
          splitAmt: wan2yuan(splitAmt),
          splitCount,
          lakhDeduction,
          annualInterest: this.annualInterestCalc, // 计算后的利率
          serviceCharge,
          billingMethod
        })
      }
      form.orderStepsEnumCode = EXECUTE_TRANSACTION_PROCESS.RE_PUBLISH // 执行订单步骤枚举code值
      form.republishOrderRequest = JSON.parse(JSON.stringify(this.form))

      // 拆分开启 且是 按可拆分金额 设置integerMultiples 、 splitAmtInt 、 splitAmtIntSelect 、 splitAmtMin 、 splitAmtMax 数据供接口使用
      if (splitFlag) {
        if (!splitType) {
          // 发布老逻辑 定额拆分 支持整倍数1 不支持2 区间拆分设置为0
          if (splitMethod) { // 定额拆分 接口使用传入splitAmtMin
            form.republishOrderRequest.integerMultiples = intMultiple ? 1 : 2
            form.republishOrderRequest.splitAmtIntSelect = splitAmtIntSelect ? yuan2wan(wan2yuan(splitAmtIntSelect)) : null
            form.republishOrderRequest.splitAmtInt = null
            form.republishOrderRequest.splitAmtMin = splitAmtInt ? yuan2fen(wan2yuan(splitAmtInt)) : null
          } else { // 区间拆分 接口使用传入splitAmtMin、splitAmtMax
            form.republishOrderRequest.integerMultiples = 0
            form.republishOrderRequest.splitAmtMin = splitAmtMin ? yuan2fen(splitAmtMin) : null
            form.republishOrderRequest.splitAmtMax = splitAmtMax ? yuan2fen(splitAmtMax) : null
          }
        }
      }

      // form.republishOrderRequest.orderNo = this.order.orderNo
      form.orderNoList = this.hasAllOrder ? this.order.map(i => i.orderNo) : [this.order.orderNo]
      // 如果没有输入定向标识，且表单中定向标识存在，则删除，转为普通票
      !this.inviteCode && form.republishOrderRequest.inviteCode && delete form.republishOrderRequest.inviteCode
      // 删除资方所在地黑名单中接口不需要的数据
      form.republishOrderRequest.blackRegionList && form.republishOrderRequest.blackRegionList.forEach(item => {
        delete item.label
      })
      // 联系方式数据格式处理
      // form.republishOrderRequest.mobile = form.republishOrderRequest.mobile.map(e => ({ mobile: e.mobile, mobileName: e.mobileName, id: e.id }))
      form.republishOrderRequest.needVoucher = 0 // 默认不需要凭证
      this.loading = true
      try {
        if (this.hasAllOrder) {
          const data = await orderApi.postExecuteOrderTradeStep(form, true)
          // 批量操作结果提示
          this.$refs.resultDialogRef.init(data)
          this.visible = false
          this.directionalVisible = false
        } else {
          // const { contactList } = this.$refs.otherOptionsRef.$refs.ContactSettingField
          // const mobile = contactList.map(item => item.mobile).join(',')
          // form.republishOrderRequest.mobile = mobile // 联系人
          await orderApi.postSingleExecuteOrderTradeStep(form, true)
          this.$message.success('发布成功')
          this.$emit('success')
          this.handleClose()
          this.directionalVisible = false
          // 刷新回款账户选中
          this.$store.dispatch('common/getNewVersionDraftConfig')
        }
      } catch (error) {
        // 对接口返回的错误code处理
        this.handleErrorCode({ api: 'reIssue', errData: error })
      }
      this.loading = false
      this.directionalBtnStatus = false
    },

    // 定向发布取消弹窗回调
    handleDirectionalDialogCancel() {
      this.inviteCode = ''
      this.directionalVisible = false
      this.inviteCodeErrorMsg = ''
    },

    // 定向发布确定弹窗
    async handleDirectionalDialogConfirm() {
      if (!this.inviteCode) {
        this.inviteCodeErrorMsg = '请输入对方定向码/手机号'
        return
      }

      const isAreaInBlack = await issueDraftApi.validateAgentOrderArea({
        counterparty: this.inviteCode,
        limitType: 1
      })
      if (isAreaInBlack.isLimit) {
        await this.$confirm('对方在您设置的地区黑名单，请确认是否继续定向', '提示', {
          type: 'warning',
          iconPosition: 'title',
          showClose: false,
          showCancelButton: true,
          cancelButtonText: '取消',
          confirmButtonText: '确认'
        })
      }
      this.form.inviteCode = this.inviteCode
      this.handleReIssue()
    },

    // 关闭之前
    handleClose() {
      this.clearForm()
      this.visible = false
      !this.hasAllOrder && this.$emit('destroy')
    },

    // 切换报价方式
    handleChangeType() {
      this.form.billingMethod = this.form.billingMethod ? BILLING_METHOD_CODE.SHI_WAN_DISCOUNT : BILLING_METHOD_CODE.ANNUAL_INTEREST
      this.$refs.ruleForm.clearValidate('billingMethodValidate')
    },

    // 瑕疵组件选择回调
    handleChangeFlaw(val) {
      this.noFlaw = !(!val.noFlaw && Object.keys(val).length === 0)
      const flaw = { ...val }
      flaw.noFlaw && delete flaw.noFlaw
      this.setDefaultFlawForm()
      if (Object.keys(flaw).length) {
        // 判断瑕疵的输入是否正确
        this.flawInputError = Object.keys(flaw).some(key => key !== 'defectsOtherDesc' && (Number(flaw[key]) === 0 || flaw[key] === ''))
        this.form = {
          ...this.form,
          ...flaw
        }
      } else {
        this.flawInputError = false
      }
      this.formFailField.ticketFlaw && this.$refs.ruleForm.validateField('ticketFlaw')
    },

    // 设置form表单中已有瑕疵字段为默认值，默认瑕疵字段是以defects开头的
    setDefaultFlawForm() {
      Object.keys(this.form).forEach(key => {
        // eslint-disable-next-line no-magic-numbers
        if (key.substr(0, 7) === 'defects') {
          if (key === 'defectsOtherDesc') {
            this.form[key] = ''
          } else {
            this.form[key] = 0
          }
        }
      })
    },

    //  报价计算
    handleBillingMethod() {
      const { draftAmount, interestDays } = this.order
      let amount = null
      if (this.form.splitFlag && this.form.splitType) {
        amount = wan2yuan(this.form.splitAmt)
      } else {
        amount = draftAmount
      }
      // 如果有校验不通过的，需要去重复校验一下
      this.formFailField.billingMethodValidate && this.$refs.ruleForm.validateField('billingMethodValidate')
      // 通过每十万扣计算利率
      if (this.form.billingMethod === BILLING_METHOD_CODE.SHI_WAN_DISCOUNT) {
        this.form.serviceCharge = ''
        if (!this.form.lakhDeduction) {
          this.form.annualInterest = ''
          this.annualInterestCalc = 0
          return
        }
        const data = lakhDeductionMath(amount, this.form.lakhDeduction || 0, interestDays)
        this.form.annualInterest = data.annualInterest
        this.annualInterestCalc = data.annualInterest // 计算得出利率回显
        this.receivedAmount = yuan2wan(data.receivedAmount) // 计算得出的到账金额
      } else { // 利率+手续费计算
        if (!this.form.annualInterest && !this.form.serviceCharge) {
          this.form.lakhDeduction = ''
          // this.form.annualInterest = ''
          this.annualInterestCalc = 0
          return
        }
        const data = interestRateMath(amount, this.form.annualInterest || 0, this.form.serviceCharge || 0, interestDays)
        this.form.lakhDeduction = data.lakhDeduction
        this.annualInterestCalc = data.annualInterest // 计算得出利率回显
        this.receivedAmount = yuan2wan(data.receivedAmount) // 计算得出的到账金额
      }
    },

    // // 利率，手续费，每十万扣款输入事件
    // handleInput(val, type) {
    //   const { draftAmount, interestDays } = this.order
    //   // 如果有校验不通过的，需要去重复校验一下
    //   this.formFailField.billingMethodValidate && this.$refs.ruleForm.validateField('billingMethodValidate')
    //   // 通过每十万扣计算利率
    //   if (type === 'lakhDeduction') {
    //     this.form.serviceCharge = ''
    //     if (!val) {
    //       this.form.annualInterest = ''
    //       return
    //     }
    //     const data = lakhDeductionMath(draftAmount, val, interestDays)
    //     this.form.annualInterest = data.annualInterest
    //   } else {
    //     if (!this.form.annualInterest && !this.form.serviceCharge) {
    //       this.form.lakhDeduction = ''
    //       return
    //     }
    //     const data = interestRateMath(draftAmount, this.form.annualInterest || 0, this.form.serviceCharge || 0, interestDays)
    //     this.form.lakhDeduction = data.lakhDeduction
    //   }
    // },

    // 将子组件的表单合并到父组件来
    handleChangeForm(val) {
      this.form = {
        ...this.form,
        ...val,
        needVoucher: val.needVoucher
      }
      // 由于有手动改过otherOptionsForm，所以此处需要保持一致
      this.otherOptionsForm.bargaining = val.bargaining
      this.otherOptionsForm.bargainingLimit = val.bargainingLimit
      this.otherOptionsForm.margin = val.margin
      this.otherOptionsForm.fastTrade = val.fastTrade
      this.otherOptionsForm.needVoucher = val.needVoucher
      this.otherOptionsForm.acceptYillionpay = val.acceptYillionpay
      this.otherOptionsForm.acceptHelipay = val.acceptHelipay
      this.otherOptionsForm.acceptLianlianpay = val.acceptLianlianpay
      this.otherOptionsForm.acceptAipay = val.acceptAipay
      this.otherOptionsForm.acceptZbankpay = val.acceptZbankpay
      // this.otherOptionsForm.mobile = val.mobile
    },

    // 监听表单校验信息
    validateResult(prop, value) {
      this.formFailField[prop] = !value
    },

    // 清空表单
    clearForm() {
      this.form = { ...defaultForm }
      this.$nextTick().then(() => {
        this.$refs.ruleForm && this.$refs.ruleForm.clearValidate()
        this.$refs.otherOptionsRef && this.$refs.otherOptionsRef.clearForm()
      })
    },

    // 接口请求返回错误处理
    handleErrorCode(obj) {
      const { api = '', errData = {} } = obj // api 指定的接口 errData后端抛出异常的数据
      const { code, msg } = errData.data || {}
      // 发布票据接口(包括定向票,批量,单张)
      if (api === 'reIssue') {
        // 高买低卖
        if (code === ISSUE_DRAFT_ERROR_CODE.GAO_MAI_DI_MAI) {
          this.$alert('您因触发平台机制，被限制登录，请联系您的客户经理', '提示', {
            confirmButtonText: '我知道了',
          }).then(() => {
            this.handleClose()
            return this.$store.dispatch('user/logout', { manual: true })
          })
            .then(() => {
              this.$message.closeAll()
              this.$router.push('/')
              windowCommunication.trigger()
            })
          return
        }

        if (code === ISSUE_DRAFT_ERROR_CODE.MAX_LIMIT_REACHED) {
          this.$confirm('您的出票量已达到限额，如需调整额度，请联系客户经理', '提示', {
            type: 'warning',
            iconPosition: 'title',
            showClose: false,
            showCancelButton: false,
            confirmButtonText: '我知道了'
          })
          return
        }

        if (code === ISSUE_DRAFT_ERROR_CODE.OTHER_MAX_LIMIT_REACHED) {
          this.$confirm('您的对方接单量已达到限额，如需帮助，请联系客户经理', '提示', {
            type: 'warning',
            iconPosition: 'title',
            showClose: false,
            showCancelButton: false,
            confirmButtonText: '我知道了'
          })
          return
        }

        // 实名失效code
        if (code === ISSUE_DRAFT_ERROR_CODE.REAL_NAME_EXPIRED) {
          // this.$refs.verifiedDialogRef.init()
          return
        }
        // 定向发布支付渠道对方不支持
        if (code === ISSUE_DRAFT_ERROR_CODE.PAY_CHANNEL_BUYER_NOT_SUPPORT) {
          this.$alert(`
                <p>您选择的支付渠道对方不支持，</p>
                <p>资方支持 <span class="alert-red-high-light">${msg}</span> 支付渠道。 </p>
                <p>请重新选择。</p>
              `, '提示', {
            dangerouslyUseHTMLString: true,
            confirmButtonText: '重新选择'
          })
          return
        }

        // 3018 "您已在确认环节取消此票据三次,今日该票在此户限制发布。"
        if (code === ISSUE_DRAFT_ERROR_CODE.SELLER_LIMIT_TRADE_ON_CANCEL_ORDER_PENDING_CONFIRM) {
          this.handleBreakContract(errData?.data)
          return
        }

        // 自身信用等级低
        if (code === ISSUE_DRAFT_ERROR_CODE.CREDIT_TYPE_GENERAL) {
          this.$refs.creditLowConfirmRef.init({
            useScenes: 'issue-draft',
            creditLevelName: '中等或一般',
            trigger: code === ISSUE_DRAFT_ERROR_CODE.CREDIT_TYPE_GENERAL ? 'self' : 'other',
            confirmCallBack: () => {
              this.otherOptionsForm.margin = 1
            }
          })
          return
        }

        // 1018 支付账户异常
        if (code === ISSUE_DRAFT_ERROR_CODE.PAY_CHANNEL_FAIL) {
          this.$refs.payAccountErrorRef.init(msg)
          return
        }

        this.$message.error(msg || '系统繁忙，请稍后再试')
      }
    },

    // 违约限制交易
    handleBreakContract(data) {
      this.$message.closeAll()

      const { msg } = data // msg 后端返回的违约文案
      const breakContractRules = PLATFORM_DEFAULT_RULESNEW_URL // 平台订单违约规则
      const breakContractRule = `<a target="_blank" rel="noopener noreferrer" href="${breakContractRules}" class="g-link-underline">查看平台订单违约规则</a>`
      const html = `<p>${msg || ''}</p><br/>${breakContractRule}`
      this.$alert(html, '警告', {
        customClass: 'alert-warning',
        confirmButtonText: '我知道了',
        cancelButtonText: '取消',
        type: 'warning',
        iconPosition: 'title',
        showClose: false,
        dangerouslyUseHTMLString: true
      })
    },

    // 待审核-下架后-重新发布:点击重新发布后，打开“普通发布”页面 /user-center/issue-draft?type=0
    handleOffShelf() {
      const path = '/user-center/issue-draft' // 发布票据页面
      // const isOpenFastType = 'fastTrade=1' // 发布票据页面是否展示极速交易设置按钮 0 不展示 1 展示
      // this.$router.push(order?.fastTrade ? `${path}?${isOpenFastType}` : path)
      this.$router.push(path)
      this.$message.warning('若审核未通过，重新发布需上传票面')
    },

    // 批量操作结果弹窗关闭回调
    closeResultDialog() {
      this.$emit('success')
      this.$emit('destroy')
    },
    // 显示报价参考弹窗
    showQuotationReferenceDialog() {
      const order = this.hasAllOrder ? this.order[0] : this.order
      const { orderNo, draftNo, acceptorName, accepterType, draftAmountWan, maturityDate, interestDays, endorseCount } = order
      const form = {
        orderNo, // 订单号
        draftNo, // 票号
        acceptorName, // 承兑人
        accepterType, // 承兑人类型
        draftAmount: draftAmountWan, // 票面金额 万
        maturityDate, // 到期日
        interestAccrualDay: interestDays, // 剩余天数
        endorseCount // 背书手数
      }
      this.$refs.quotationReferenceDialogRef.init(form)
    },
    handleOpenMiaotie() {
      if (this.$ipc) {
        this.$ipc.send('OPEN_URL_IN_DEFAULT_BROWSER', 'https://mec.lianwuxian.cn/bank-notes')
      } else {
        window.open('https://mec.lianwuxian.cn/bank-notes')
      }
    },
    // 初始化报价设置
    initBillingMethod() {
      Object.assign(this.form, {
        splitAmt: null,
        splitCount: null,
        splitAmtInt: null,
        splitAmtIntSelect: null,
        splitMethod: 1,
        splitAmtMin: null,
        splitAmtMax: null,
        splitSetting: []
      })
      this.handleBillingMethod()
    },

    // 拆分开关设置
    handelSplitFlag() {
      // 关闭拆分 初始化报价
      if (!this.form.splitFlag) {
        this.initBillingMethod()
      }
    },

    // 切换拆分类型
    handlesplitType() {
      if (this.form.splitType) {
        this.form.splitType = 0 // 按可拆分金额拆分
        this.initBillingMethod()
      } else {
        this.form.splitType = 1 // 按金额*张数拆分
      }
    },
    getMinSplitConfig() {
      return this.$store.state.common.newVersionDraftSplit || 10000
    },
    // 切换拆分方式
    handleChangeSplitType() {
      this.form.splitMethod = this.form.splitMethod ? 0 : 1
      if (this.form.splitMethod) { // 定额拆分
        this.form.splitAmtMin = wan2yuan(this.form.splitAmtInt)
        this.form.splitAmtMax = null
      } else { // 区间拆分
        this.form.splitAmtMin = this.draftAmount >= this.getMinSplitConfig() ? this.getMinSplitConfig() : this.draftAmount
        this.form.splitAmtMax = this.draftAmount
        this.draftAmount < this.getMinSplitConfig() && (this.form.splitMethod = 0)
      }
      this.$refs.ruleForm.clearValidate()
    },

    // 输入拆分金额事件 重置拆分金额选择
    handleInputSplitAmount() {
      this.form.splitAmtIntSelect = null
    },

    // 选择拆分金额事件
    handleSelectSplitAmount() {
      this.form.splitAmtInt = this.form.splitAmtIntSelect
      // 定额拆分设置splitAmtMin
      this.form.splitAmtMin = wan2yuan(this.form.splitAmtInt)
      this.form.splitAmtMax = null
    },
  }
}
</script>
