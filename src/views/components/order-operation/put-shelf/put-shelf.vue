<!-- 上架 -->
<style lang="scss" scoped>
.put-shelf {
  display: inline;

  .el-button {
    position: relative;

    &.has-tag::after {
      content: "";
      position: absolute;
      top: -1px;
      left: -1px;
      border-top: 27px solid $color-assist3;
      border-right: 28px solid transparent;
      width: 0;
      height: 0;
    }

    .rice-tag {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      font-size: 12px;
      font-weight: 600;
      line-height: 20px;
      transform: scale(.8);
      transform-origin: top;
      color: $color-FFFFFF;
    }
  }
}
</style>

<template>
  <div class="put-shelf order-operation">
    <slot name="button">
      <el-button
        v-waiting="[`post::/draft/order/executeOrderTradeStep?orderNo=${firstOrderNo}&type=putShelf`,
                    `post::/draft/order/singleExecuteOrderTradeStep?orderNo=${firstOrderNo}&type=putShelf`]"
        v-bind="$attrs"
        :type="$attrs.type || 'primary'"
        :width="$attrs.width || '68'"
        :height="$attrs.height || '40'"
        :class="hasTag && 'has-tag'"
        @click="init"
        v-on="$listeners"
      >
        <span v-if="hasTag" class="rice-tag">{{ sdmUnit }}</span>
        <slot>上架</slot>
      </el-button>
    </slot>
  </div>
</template>

<script>
/* eslint-disable no-magic-numbers */

import { EXECUTE_TRANSACTION_PROCESS } from '@/constant'
import orderApi from '@/apis/order'
import { isWealthCommerceDraft } from '@/utils/util'
import { VALID_FAIL_TYPE } from '@/constants/sp-trade'
import spTradeApi from '@/apis/sp-trade'
export default {
  name: 'put-shelf',
  props: {
    order: {
      type: [Object, Array],
      required: true
    },
    hasTag: [String, Number, Boolean] // 是否显示保证金米字标识
  },
  data() {
    return {
      loading: false
    }
  },
  computed: {
    // 是否批量
    hasAllOrder() {
      return this.order instanceof Array
    },
    // 订单号，数组时取第一条数据，用于loading时，不要所有按钮都显示loading状态
    firstOrderNo() {
      return this.hasAllOrder && this.order.length ? this.order[0].orderNo : this.order.orderNo
    }
  },
  methods: {
    async init() {
      if (this.order && Array.isArray(this.order) && this.order.length > 99) {
        this.$message.warning(`每次批量操作最多可选择 ${99} 笔订单`)
        return
      }
      try {
        // 单张发布走白名单校验，批量不走白名单校验保持原有逻辑不变，走的统一的批量接口报错处理
        if (!this.hasAllOrder) {
          // 商票/财票交易权限检测
          await this.checkSpTradeAccess()
        }
        this.postPutShelf(this.order)
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log(error)
      }
      // }
    },
    // 商票/财票--交易准入查询
    async checkSpTradeAccess() {
      // 判断是  商票或者是财票  都要加上白名单判断
      if (this.hasAllOrder ? this.order.every(draft => !isWealthCommerceDraft(draft.draftNo, draft.acceptorType)) : !isWealthCommerceDraft(this.order.draftNo, this.order.acceptorType)) return

      const corpId = this.$store.state?.user?.corpInfo?.id
      const { pass, failType } = await spTradeApi.whiteListCheck({
        corpId,
        accepterList: this.hasAllOrder ? this.order.filter(draft => isWealthCommerceDraft(draft.draftNo, draft.acceptorType)).map(draft => ({
          accepter: draft.acceptorName,
          amount: draft.draftAmount
        })) : [{ accepter: this.order.acceptorName, amount: this.order.draftAmount }]
      })
      if (pass) return
      if (failType === VALID_FAIL_TYPE.NO_CREDIT.id) {
        this.$alert('该承兑人不在授信白名单，请联系您的客户经理!', '提示')
      } else if (failType === VALID_FAIL_TYPE.NO_AMOUNT.id) {
        this.$alert('该承兑人授信额度不足，若仍需发布请联系您的客户经理', '提示')
      } else if (failType === VALID_FAIL_TYPE.NO_ABNORMAL.id) {
        this.$alert('该承兑人白名单状态异常，请联系您的客户经理!', '提示')
      }
      return Promise.reject(new Error('商票交易核验未通过'))
    },

    // 上架请求
    async postPutShelf(order) {
      try {
        this.loading = true

        const orderNoList = this.hasAllOrder ? order.map(i => i.orderNo) : [order.orderNo]
        const param = {
          type: 'putShelf', // 用于点击约束误触发
          orderNoList, // 订单编号
          orderStepsEnumCode: EXECUTE_TRANSACTION_PROCESS.PUT_SHELF
        }
        if (this.hasAllOrder) {
          const data = await orderApi.postExecuteOrderTradeStep(param)
          if (!data.failNum) { // 失败数为0,即全部成功
            this.$message.success('操作成功')
          }
        } else {
          await orderApi.postSingleExecuteOrderTradeStep(param)
          this.$message({
            type: 'success',
            message: '上架成功'
          })
        }
        this.$emit('success')

        this.loading = false
      } catch (error) {
        this.loading = false
      }
    }
  }
}
</script>
