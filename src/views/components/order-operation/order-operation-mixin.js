import Vue from 'vue'
import store from '@/store/index'

// 保存某个组件是否有实例的 map
const hasInstanceMap = new Map()

export default function createMixin(Component) {
  // @vue/component
  // eslint-disable-next-line vue/require-name-property
  const mixinOptions = {
    props: {
      // 订单对象或列表
      order: {
        type: [Object, Array],
        required: true
      },
    },
    methods: {
      // 初始化
      init(event) {
        // 按钮点击后失焦，解决回车重复触发按钮的点击事件问题
        event.target && event.target.blur()
        const Constructor = Vue.extend(Component)
        const instance = new Constructor({
          router: this.$router,
          store
        })
        instance.order = this.order
        // 事件穿透，可直接在按钮组件上监听弹窗组件 emit 的事件
        Object.entries(this.$listeners).forEach(([eventName, callback]) => {
          if (eventName !== 'click') {
            instance.$on(eventName, callback)
          }
        })
        instance.$mount()
        this.listenDestroyEvent(instance)
        document.body.appendChild(instance.$el)
        instance.init(this.$options.propsData)
      },
      // 当详情组件是弹窗且弹窗关闭时，销毁实例
      async listenDestroyEvent(instance) {
        await this.$nextTick()
        if (hasInstanceMap.get(Component)) {
          // eslint-disable-next-line no-console
          console.warn(`检测到组件 ${Component.name} 存在未销毁的实例，请检查是否有手动调用 this.$emit('destroy') 进行销毁`)
        }
        hasInstanceMap.set(Component, true)
        // 弹窗组件内部需通过 emit destroy 事件通知当前 mixin 将其销毁
        instance.$on('destroy', () => {
          // 关闭弹窗时，销毁当前实例
          instance.$destroy()
          instance.$el.parentNode && instance.$el.parentNode.removeChild(instance.$el)
          hasInstanceMap.set(Component, false)
          if (process.env.NODE_ENV === 'development') {
            // eslint-disable-next-line no-console
            console.log('当前组件已销毁')
          }
        })
      }
    },
  }
  return mixinOptions
}
