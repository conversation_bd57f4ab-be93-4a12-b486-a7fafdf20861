<!-- 极速 非极速转换 按钮 -->
<style lang="scss" scoped>
.fast-change-btn {
  margin-left: 6px;

  .el-button--text {
    color: #F51818;
  }

  .el-button--text::after {
    position: absolute;
    bottom: 0;
    content: "";
    left: 0;
    border-bottom: 1px solid #F51818;
    width: 100%;
  }
}
</style>

<template>
  <div v-if="isFast" class="fast-change-btn">
    <el-button type="text" @click="init">转极速</el-button>
  </div>
  <el-tooltip
    v-else
    class="item"
    effect="dark"
    placement="top"
  >
    <template slot="content">
      {{ fastTrade ? '极速' : '非极速' }}
      <Icon
        class="icon"
        type="chengjie-swap"
        @click="init"
      />
      {{ !fastTrade ? '极速' : '非极速' }}
    </template>
    <Icon
      class="icon text-color"
      type="chengjie-swap"
      @click="init"
    />
  </el-tooltip>
</template>

<script>
import orderTranstionDialog from './order-transtion-dialog.vue'
import orderOperationMixin from '../order-operation-mixin'
import Icon from '@/views/components/icon/icon.vue'
import { PLATFORM_DEFAULT_RULESNEW_URL } from '@/constants/oss-files-url' // 平台订单违约规则url

export default {
  name: 'order-transtion',
  components: {
    Icon,
  },
  mixins: [orderOperationMixin(orderTranstionDialog)],
  props: {
    isFast: { // 是否只转极速
      type: Boolean,
      default: false
    },
    fastTrade: {
      type: Number,
    }
  },
  data() {
    return {
      PLATFORM_DEFAULT_RULESNEW_URL, // 平台订单违约规则url
    }
  }
}
</script>
