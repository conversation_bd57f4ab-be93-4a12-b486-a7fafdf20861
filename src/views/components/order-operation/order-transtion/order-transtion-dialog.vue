<!-- 极速非极速转换弹窗 -->
<style lang="scss" scoped>
.container {
  padding-bottom: 20px;

  .second-title {
    line-height: 24px;
  }

  .wrapper {
    display: flex;
    margin-bottom: 12px;
    padding: 16px;
    font-size: 14px;
    background: $color-FFFFFF;
    flex-direction: column;
  }

  .footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}

.tips {
  margin: 10px 0;
  line-height: 24px;

  .margin-left {
    margin-left: 24px;
  }

  .recharge {
    margin-left: 12px;

    @include example_underline;
  }

  .icon-question {
    font-size: 20px;
  }
}

.agreement {
  @include flex-vc;

  font-size: 12px;

  .el-checkbox {
    display: inline-flex;
    align-items: center;

    ::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
      color: $color-text-primary;
    }
  }
}

.link {
  @include example-underline;

  font-size: 14px;
}

// 灰色标题
.gray {
  margin-bottom: 2px;
  color: $color-text-secondary;
}

.item-detail {
  margin-bottom: 8px;
  font-size: 16px;
  color: $color-text-primary;
  line-height: 24px;
}

.footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
</style>

<style>
.issue-draft-tooltip {
  width: 296px;
}
</style>

<template>
  <el-dialog
    width="600px"
    :visible.sync="visible"
    :close-on-click-modal="false"
    title="提示"
    class="change-price-account-dialog order-operation-dialog"
    :before-close="handleHidden"
  >
    <div class="container">
      <div class="wrapper">
        <div v-if="fastTrade" class="second-title">
          <span>请您确定是否要将极速订单转化为非极速订单，</span><span class="text-primary">转化后订单仍为带保订单，但可能会无法快速被接单!</span>
        </div>
        <div v-else>
          <span>请您确定是否要将非极速订单转化为极速订单?</span>
          <div class="tips">
            <span>
              <el-tooltip
                placement="top"
                popper-class="issue-draft-tooltip"
              >
                <template slot="content">
                  <div>
                    保证金是为防范双方交易违约行为缴纳的资金，从保证金账户冻结， 按票面金额的万分之 3 缴纳，最低 10 元，最高 600 元，交易若无违约将退回。 查看《<a
                      class="link"
                      :href="defaultRulesUrl"
                      target="_blank"
                      rel="noopener noreferrer"
                    >平台订单违约规则</a>》
                  </div>
                </template>
                <icon class="icon icon-question" type="chengjie-wenti" />
              </el-tooltip>
            </span>
            保证金： 需要缴纳 <span class="text-primary"> {{ needMarginAmount }} {{ sdmUnit }}</span>
            <span>
              <span class="margin-left">可用余额：<span class="text-primary"> {{ (sdmInfo || {}).balanceAmt || '0.00' }} {{ sdmUnit }}</span></span>
              <span class="recharge" @click="recharge">充值</span>
            </span>
          </div>
          <div class="agreement">
            <el-checkbox v-model="isAgree">我已阅读并同意</el-checkbox>
            <a
              :href="fastTradeUrl"
              target="_blank"
              class="link"
              rel="noopener noreferrer"
            >《极速出票规则》</a>
            <a
              :href="OSS_FILES_URL.LIGHT_SPEED_ORDER_AUTHORIZATION"
              target="_blank"
              class="link"
              rel="noopener noreferrer"
            >《极速出票授权委托书》</a>
          </div>
        </div>
      </div>
      <div class="footer">
        <el-button type="primary" border @click="handleHidden">取消</el-button>
        <el-button
          v-waiting="['post::loading::/discern/outStock', 'post::loading::/discern/batchOutStock']"
          :loading="handleLoading"
          type="primary"
          :disabled="!fastTrade && !isAgree"
          @click="handleConfirm"
        >
          确认
        </el-button>
      </div>
    </div>

    <!-- 充值 -->
    <Recharge ref="recharge" />
    <!-- 支付账户异常弹窗 -->
    <PayAccountError ref="payAccountErrorRef" />
  </el-dialog>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import orderApi from '@/apis/order' // 接口
import Recharge from '@/views/components/user-center/recharge/recharge.vue' // 充值
import { FASTTRADE_URL, PLATFORM_DEFAULT_RULESNEW_URL, OSS_FILES_URL } from '@/constants/oss-files-url'
import PayAccountError from '@/views/components/pay-account-error/pay-account-error.vue'// 支付账户异常弹窗
import { marginAmountMath } from '@/common/js/draft-math' // 保证金计算
import {
  keep2Decimals, // 万转元
} from '@/common/js/number'
export default {
  name: 'change-price-account-dialog',
  components: {
    Recharge,
    PayAccountError,
  },
  data() {
    return {
      OSS_FILES_URL, // 极速订单授权委托书
      handleLoading: false,
      visible: false, // 弹窗是否打开
      isAgree: true, // 是否同意协议
      fastTrade: false, // 是否是极速
      orderInfo: {}, // 票据信息
      fastTradeUrl: FASTTRADE_URL,
      defaultRulesUrl: PLATFORM_DEFAULT_RULESNEW_URL,
      needMarginAmount: 0
    }
  },
  computed: {
    ...mapGetters('user', {
      sdmInfo: 'sdmInfo', // 米账号信息
    }),
    ...mapGetters('draft-detail', {
      traderCorpOrderInfo: 'traderCorpOrderInfo', // 订单详情
    }),

  },
  methods: {
    ...mapActions('user', {
      getSdmInfo: 'getSdmInfo', // 米账号信息

    }),
    ...mapActions('draft-detail', {
      getTraderCorpOrderInfo: 'getTraderCorpOrderInfo', // 获取订单详情
    }),
    async init() {
      this.fastTrade = this.order.fastTrade !== 0
      this.orderInfo = this.order
      this.orderNo = this.orderInfo.orderNo
      this.getSdmInfo() // 获取米账户信息
      await this.getTraderCorpOrderInfo({ orderNo: this.orderNo, isHistory: this.isHistory }) // 获取订单信息 -- 保证金
      // 计算保证金
      let needMarginAmount = 0
      if (!this.traderCorpOrderInfo.margin) {
        needMarginAmount = needMarginAmount + marginAmountMath(this.traderCorpOrderInfo.draftAmount)
      }
      this.needMarginAmount = keep2Decimals(needMarginAmount)
      this.visible = true
    },

    // 充值
    recharge() {
      this.$refs.recharge && this.$refs.recharge.init()
    },
    async handleConfirm() {
      this.handleLoading = true
      const body = {
        fastTrade: this.fastTrade ? 0 : 1, // 0 转为非极速 1 转为极速
        orderNoList: [this.order.orderNo]
      }
      try {
        await orderApi.fastTradeOrderTransfer(body)
        setTimeout(() => {
          this.$emit('success')
          this.handleLoading = false
          this.$message.success('操作成功!')
          this.visible = false
        }, 2000)
      } catch (error) {
        this.handleLoading = false
      }
    },
    handleHidden() {
      this.visible = false
    },
  }
}
</script>
