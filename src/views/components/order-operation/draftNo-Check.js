// 智付E+ 强提示

import { tracking } from '@/utils/util'

export function showConfirmationDialog(message, data) {
  // data.info 格式是 {} 或者是[{},{}]
  if (!data && !data.info) {
    return
  }
  let trackInfo = {
    confirmType: data.type,
    confirmDaftNo: Array.isArray(data.info) ? data.info.map(obj => obj.draftNo).join() : data.info.draftNo,
  }
  return new Promise(resolve => {
    tracking(trackInfo)
    resolve()
  })
  // return new Promise(resolve => {
  //   this.$confirm(`<span >请确认${message}票号与订单票号一致，若因${message}不一致导致客服${message === '签收' ? '强制解付' : '强制处理'}，您将${message === '签收' ? '同步' : ''}承担违约责任。</span>`, '提示', {
  //     confirmButtonText: '确认',
  //     type: 'warning',
  //     closeOnClickModal: false,
  //     closeOnPressEscape: false,
  //     showCancelButton: false,
  //     distinguishCancelAndClose: true,
  //     cancelButtonText: '',
  //     showClose: false,
  //     customClass: 'single-confirm-dialog',
  //     dangerouslyUseHTMLString: true,
  //   }).then(() => {
  //     tracking(trackInfo)
  //     resolve()
  //   })
  // })
}
