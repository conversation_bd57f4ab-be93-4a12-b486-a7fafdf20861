<style lang="scss" scoped>
  .content-warp {
    padding: 25px;
    background: #FFFFFF;

    .tips {
      margin-bottom: 20px;
    }
  }
</style>

<template>
  <el-dialog
    title="批量设置联系人"
    :visible.sync="visible"
    width="600px"
    :before-close="handleClose"
  >
    <div class="content-warp">
      <div class="tips">
        <WarnContent>
          注意，确认提交数据之后，所选订单全部同步调整
        </WarnContent>
      </div>

      <contactItem @change-data="onChangeMobile" />
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import contactItem from '@/recognize/components/issue-draft/components/contact-item.vue'
import WarnContent from '@/views/components/common/warn-content.vue'
import orderApi from '@/apis/order'
export default {
  name: 'set-contacts',
  components: {
    contactItem,
    WarnContent
  },
  props: {
    order: {
      type: [Object, Array],
    }
  },
  data() {
    return {
      mobile: null,
      visible: false
    }
  },
  methods: {
    init() {
      this.visible = true
    },
    handleClose() {
      this.visible = false
    },
    validContact() {
      const { mobile } = this
      if (!mobile) {
        this.$message.info('请先设置联系方式')
        return false
      }
      const list = mobile.split(',')
      if (!list.every(item => {
        if (item.length < 11) {
          return false
        }
        return true
      })) {
        this.$message.info('请输入11位数手机号')
        return false
      }
      const uniqueSet = new Set(list)
      if (uniqueSet.size !== list.length) {
        this.$message.error('联系方式手机号不能重复')
        return false
      }
      return true
    },
    // 手机号修改
    onChangeMobile(data) {
      this.mobile = data
    },
    async confirm() {
      const status = this.validContact()

      if (status) {
        const { mobile } = this
        let orderNo = null
        if (this.order instanceof Array) {
          orderNo = this.order && this.order.map(e => e.orderNo)
        } else {
          orderNo = [this.order.orderNo]
        }

        await orderApi.beachUpdateMobile({ mobile, orderNo })
        this.$message.success('操作成功')
        this.visible = false
      }
    }
  },
}
</script>
