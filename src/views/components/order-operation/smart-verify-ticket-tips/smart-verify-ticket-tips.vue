<style lang="scss" scoped>
.verified {
  cursor: pointer;
  user-select: none;
}

.verified-icon {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #018080;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  color: #018080;
}

.waring-cls {
  border-color: #EC3535;
  color: #EC3535;
}
</style>

<template>
  <span class="verified" @click="openVerifyResult">
    <slot>
      <el-tooltip content="点击可查看「智能验票」结果" :open-delay="500">
        <span :class="['verified-icon', (order.beforeSignDiscern === 2 || !!order.existRisk) ? 'waring-cls' : '']">验</span>
      </el-tooltip>
    </slot>

    <SmartVerifyTicket ref="smartVerifyTicketRef" />
  </span>
</template>

<script>
import SmartVerifyTicket from '@/recognize/components/smart-verify-ticket/smart-verify-ticket.vue'
import orderApi from '@/apis/order'

export default {
  name: 'smart-verify-ticket-tips',
  components: {
    SmartVerifyTicket,
  },
  props: {
    // 订单对象或列表
    order: {
      type: Object,
      required: true
    },
  },
  methods: {
    // 打开只能验票结果
    async openVerifyResult() {
      // 接口查询识别记录中的数据
      const identifyData = await orderApi.getDiscernByDraftNo({ draftNos: [this.order.draftNo] })
      if (!Array.isArray(identifyData) || identifyData.length === 0) {
        this.$message.warning('没有查询到此票识别记录')
        return
      }
      // 接口查询订单详情数据
      const orderData = await orderApi.getTraderCorpOrderInfo(this.order.orderNo)
      orderData.sellerCorpName = orderData.sellCorpName
      // 调用IPC通信打开智能验票窗口
      this.$refs.smartVerifyTicketRef.open({
        identifyData: identifyData[0],
        orderData,
        hideButton: true, // 隐藏智能验票弹窗按钮
      })
    }
  },
}
</script>
