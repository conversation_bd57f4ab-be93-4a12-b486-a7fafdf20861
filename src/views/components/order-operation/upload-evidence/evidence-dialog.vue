<!-- ---------- 获取交易凭证 ----------- -->

<style lang="scss" scoped>
  .evidence-model-wrap {
    padding: 16px;
    background-color: #FFFFFF;

    .refer {
      font-size: 16px;
      font-weight: 400;
      line-height: 25px;
      color: #333333;

      .red {
        color: #EC3535;
      }
    }
  }

  .upload-cls {
    ::v-deep .el-form-item__content {
      line-height: 30px;
    }

    .upload-flex {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  ::v-deep .el-form-item {
    margin-bottom: 10px;
  }

  ::v-deep.el-form--label-top .el-form-item__label {
    padding: 0;
  }

  ::v-deep .el-upload-dragger {
    padding-top: 20px;
    width: 150px;
    height: 80px;

    .el-icon-upload {
      line-height: 0;
      margin: 0;
      font-size: 30px;
    }
  }

  ::v-deep .el-textarea__inner {
    height: 80px;
  }

  .bottom-cls {
    display: flex;
    justify-content: space-between;
    padding-top: 20px;

    .left {
      width: 50%;
    }
  }

  ::v-deep .el-checkbox {
    display: flex;
    align-items: center;

    .el-checkbox__label {
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      a {
        color: $--color-primary;
      }
    }
  }
</style>

<template>
  <el-dialog
    width="560px"
    :visible.sync="visible"
    append-to-body
    :close-on-click-modal="false"
    title="上传佐证材料"
    :before-close="handleClose"
  >
    <div class="evidence-model-wrap">
      <div class="refer">
        您必须在订单完成后<span class="red">6</span>天内完成佐证材料的上传，若超时未上传将会限制当前账户使用智付E+渠道接单。
      </div>
      <el-form
        ref="form"
        :rules="rules"
        label-position="top"
        :model="formState"
      >
        <el-form-item label="本单位与交易对手方公司的真实关系" prop="realRelationship">
          <el-radio-group v-model="formState.realRelationship">
            <el-radio :label="1">真实交易关系</el-radio>
            <el-radio :label="2">真实债权债务关系</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="本单位付款业务的真实业务背景为" prop="realBusinessBackground">
          <el-input
            v-model="formState.realBusinessBackground"
            maxlength="200"
            show-word-limit
            placeholder="请简要说明与交易对手方的交易背景（示例：本单位使用延时付款用于与交易对手方公司进行xx商品交易/xx服务贸易/xx项目交付。）"
            type="textarea"
          />
        </el-form-item>
        <el-form-item class="upload-cls" label="能够反映真实交易关系/债权债务关系的材料" prop="realMaterialList">
          <div class="upload-flex">
            <ImgUpload
              v-for="(item, index) in material"
              :key="index"
              v-model="item.url"
              :size-limit="10"
              :dir="OSS_DIR.EVIDENCE_MATERIALS_WEB"
              :height="80"
              style="width: 150px;"
              accept="application/pdf,image/jpeg,image/png"
              :is-pdf="item.isPdf"
              :on-success="onUploadSuccess"
            >
              <div slot="empty">
                <div>点击或拖动上传文件</div>
              </div>
            </ImgUpload>
          </div>
        </el-form-item>

        <el-form-item label="交易对手方公司概况/业务情况" prop="businessConditions">
          <el-input
            v-model="formState.businessConditions"
            maxlength="300"
            show-word-limit
            placeholder="请简要说明交易对手方主营业务或与交易有关的业务情况，以佐证双方有真实关系"
            type="textarea"
          />
        </el-form-item>
      </el-form>
    </div>
    <div class="bottom-cls">
      <div class="left">
        <el-checkbox v-model="formState.readAgreeSign">
          <span>我已阅读并同意使用电子签章签署</span>
          <a
            class="agree-link"
            target="_blank"
            :href="OSS_FILES_URL.RISK_STATEMENT"
            rel="noopener noreferrer"
          >《风险业务情况说明函》</a>
        </el-checkbox>
      </div>
      <div class="right">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="onSubmit">确认上传</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import ImgUpload from '@/views/components/common/img-upload/img-upload.vue'
import orderApi from '@/apis/order' // 接口
import {
  OSS_DIR
} from '@/constant.js'
import { OSS_FILES_URL } from '@/constants/oss-files-url.js'
export default {
  name: 'evidence-dialog',
  components: {
    ImgUpload
  },
  data() {
    const checkRealMaterial = (rule, value, callback) => {
      if (!value.length) {
        return callback(new Error('请上传能够反映真实交易关系/债权债务关系的材料'))
      }
      callback()
    }
    return {
      OSS_FILES_URL,
      OSS_DIR,
      visible: false,
      isPdf: false,
      formState: {
        readAgreeSign: false,
        realRelationship: null, // 本单位与交易对手方公司的真实关系:1-真实交易关系;2-真实债权债务关系
        realBusinessBackground: null, // 本单位付款业务的真实业务背景
        realMaterialList: [], // 能够反映真实交易关系/债权债务关系的材料
        businessConditions: null // 交易对手方公司概况/业务情况
      },
      material: [{ name: '', url: '', isPdf: false }, { name: '', url: '', isPdf: true }, { name: '', url: '', isPdf: false }], // 上传的材料
      rules: {
        realRelationship: [{ required: true, message: '请选择本单位与交易对手方公司的真实关系', trigger: 'change' }],
        realBusinessBackground: [{ required: true, message: '请填写本单位付款业务的真实业务背景', trigger: 'blur' }],
        realMaterialList: [{ required: true, validator: checkRealMaterial, trigger: 'change' }],
        businessConditions: [{ required: true, message: '请填写交易对手方公司概况/业务情况', trigger: 'blur' }],
      }
    }
  },
  computed: {
  },
  watch: {
    material: {
      handler() {
        this.material.forEach(e => {
          if (e.url === '') {
            e.name = ''
            e.size = ''
          }
          if (e.name.substr(e.name.lastIndexOf('.') + 1) === 'pdf') {
            e.isPdf = true
          } else {
            e.isPdf = false
          }
        })
        // 取出material数据集合中非空url的数据复制给formState
        const arr = this.material.filter(e => e.url !== '').map(({ name, url }) => ({ name, url }))
        this.formState.realMaterialList = arr
      },
      deep: true
    }
  },
  methods: {
    // 打开弹窗-初始化数据
    async init({ order }) {
      this.formState.orderNo = order.orderNo
      this.visible = true

      const resData = await orderApi.getLastRejectEvidenceDetail({ orderNo: order.orderNo })
      // 获取上一次提交的数据回显
      if (resData.orderNo !== '0') {
        this.setEvidenceDetailData(resData)
      }
    },

    setEvidenceDetailData(data) {
      Object.assign(this.formState, data, { readAgreeSign: false })
      data.realMaterialList.forEach((item, index) => {
        this.material[index].name = item.name
        this.material[index].url = item.url
      })
    },
    handleClose() {
      this.visible = false
      this.$emit('destroy')
    },
    async onSubmit() {
      await this.$refs.form.validate()
      if (!this.formState.readAgreeSign) return this.$message.warning('请先阅读并同意使用电子签章签署《风险业务情况说明函》')
      const fileTotalSize = this.formState.realMaterialList.reduce((acc, curr) => acc + curr.size, 0) / 1024 / 1024
      if (fileTotalSize > 10) return this.$message.warning('上传的材料文件总大小不能超过10M')
      const body = Object.assign(this.formState)
      body.readAgreeSign = body.readAgreeSign ? 1 : 2
      await orderApi.uploadEvidence(this.formState)
      this.visible = false
      this.$emit('success')
    },
    onUploadSuccess(url, file) {
      // 上传成功后更新material中上传数据
      this.$nextTick().then(() => {
        this.material.forEach(e => {
          if (e.url === url) {
            e.name = file.name
            e.size = file.size
            e.type = file.type
          }
        })
      })
    },
  }
}
</script>
