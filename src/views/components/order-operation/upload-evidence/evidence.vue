<template>
  <div class="evidence-wrap">
    <slot name="button">
      <el-button
        v-bind="$attrs"
        :type="$attrs.type || 'primary'"
        :width="$attrs.width || '104'"
        :height="$attrs.height || '40'"
        :size="$attrs.size || 'large'"
        @click="init"
        v-on="$listeners"
      >
        <slot>上传佐证</slot>
      </el-button>
    </slot>
  </div>
</template>

<script>
import EvidenceDialog from './evidence-dialog'
import orderOperationMixin from '../order-operation-mixin.js'

export default {
  name: 'service-intervention',
  mixins: [orderOperationMixin(EvidenceDialog)],
  props: {
    order: {
      type: Object,
    }
  },
  data() {
    return {
    }
  }
}
</script>
