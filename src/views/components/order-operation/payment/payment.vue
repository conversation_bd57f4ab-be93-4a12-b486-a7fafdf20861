<!-- 支付 -->
<style lang="scss" scoped>
.payment {
  display: inline;

  .el-button {
    position: relative;

    &.has-tag::after {
      content: "";
      position: absolute;
      top: -1px;
      left: -1px;
      border-top: 27px solid $color-assist3;
      border-right: 28px solid transparent;
      width: 0;
      height: 0;
    }

    .rice-tag {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      font-size: 12px;
      font-weight: 600;
      line-height: 20px;
      transform: scale(.8);
      transform-origin: top;
      color: $color-FFFFFF;
    }
  }
}
</style>

<template>
  <div class="payment order-operation">
    <slot name="button">
      <el-button
        v-waiting="orderApi.getCashierDeskUrl"
        v-bind="$attrs"
        :type="$attrs.type || 'primary'"
        :width="$attrs.width || '70'"
        :class="hasTag && 'has-tag'"
        :height="$attrs.height || '40'"
        :disabled="$attrs.disabled || false"
        @click="init"
        v-on="$listeners"
      >
        <span v-if="hasTag" class="rice-tag">{{ sdmUnit }}</span>
        <slot>支付</slot>
      </el-button>
    </slot>
  </div>
</template>

<script>
import PaymentTipsDialog from './payment-tips-dialog.vue' // 批量支付前的提示
import orderApi from '@/apis/order'
import orderOperationMixin from '../order-operation-mixin'

export default {
  name: 'payment',
  mixins: [orderOperationMixin(PaymentTipsDialog)],
  props: {
    hasTag: [String, Number, Boolean]
  },
  data() {
    return {
      orderApi,
    }
  },
}
</script>
