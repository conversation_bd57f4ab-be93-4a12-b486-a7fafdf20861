<!-- 批量支付提示弹窗 -->
<style lang="scss" scoped>
.confirm-content {
  overflow-y: auto;
  margin-top: 12px;
  padding: 16px;
  max-height: 480px;
  background-color: #FFFFFF;
}

.confirm-content-title {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.confirm-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  line-height: 24px;

  &:last-child {
    margin-bottom: 0;
  }

  .head,
  .copy {
    color: $color-text-secondary;
  }

  .label {
    padding-top: 3px;
    font-weight: bold;
  }

  .value {
    padding-top: 3px;
  }

  .copy {
    font-size: 20px;
  }
}

.confirm-item-draftno {
  justify-content: start;
}

.container {
  font-size: 16px;

  .icon {
    margin-right: 14px;
    font-size: 24px;
    color: $--color-warning;
  }

  .red-high-light {
    margin: 0 4px;
    font-weight: bold;
    color: $--color-font-main;
  }

  .handleBtn {
    padding-top: 20px;
    text-align: right;
  }

  .red {
    font-weight: 600;
    color: red;
  }
}

.footer-btn {
  display: flex;
  justify-content: space-between;
}
</style>

<template>
  <el-dialog
    title="支付提示"
    width="600px"
    :visible.sync="visible"
    :append-to-body="true"
    custom-class="batch-payment-tips-dialog"
    @closed="closed"
  >
    <div v-if="visible" class="container">
      <WarnContent>
        您正在对以下{{ hasAllOrder ? order && order.length || 0 : 1 }}笔订单进行支付操作，应付金额合计<span class="red-high-light">{{ draftPaymentAmount || 0 }}</span>元，{{ hasYlBankPaymentChannel ? '请先前往订单指定企业网银页面完成支付' : '确认后将跳转支付页面' }}。
        <!-- generalAccountFlag 0- 无变更 1- 变更为一般户 2- 一般户变更为非一般户 -->
        <div v-if="order.generalAccountFlag" class="red">请注意，票方确认订单时已修改为{{ [PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id, PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id].includes(order.paymentChannel) && order.generalAccountFlag === 1 ? '同行回款户' : '非同行回款户' }}</div>
      </WarnContent>
      <div v-if="order" class="confirm-content">
        <template v-if="hasAllOrder">
          <div
            v-for="item in order"
            :key="item.draftNo"
            class="confirm-item confirm-item-draftno"
          >
            <div class="label">票号：</div>
            <div class="value">{{ item.draftNo }}</div>
            <!-- <div v-if="item.draftType"><span class="g-xinpiao">新票</span></div> -->
          </div>
        </template>

        <template v-if="hasYlBankPaymentChannel">
          <div class="confirm-content-title">
            <div class="g-title-small">
              待支付订单
              <!-- interbankFlag 是否同行 1是 0否 -->
              <el-tooltip
                v-if="!!order.interbankFlag && [PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id, PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id].includes(order.paymentChannel)"
                popper-class="defects-notify-tooltip"
                placement="top"
              >
                <div class="g-xinpiao">同行</div>
                <div slot="content">
                  <div class="tip-out">
                    该笔订单回款户为
                    <span v-if="[PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id].includes(order.paymentChannel)">智付邦+</span>
                    <span v-else>智付E+</span>
                  </div>
                </div>
              </el-tooltip>
            </div>
            <!-- 智付E+ 绑+ -->
            <Copy v-if="[PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id, PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id].includes(order.paymentChannel)" is-button :content="`支付单号：${order.businessSeqNo}\n应付金额：${draftPaymentAmount}元\n付款账号：${paymentAccount}`">全部复制</Copy>
            <!-- E++ -->
            <Copy v-else-if="[PAYMENT_CHANNEL.YL_PLUS.id].includes(order.paymentChannel)" is-button :content="`订单号：${order.orderNo}\n应付金额：${draftPaymentAmount}元\n付款账号：${paymentAccount}`">全部复制</Copy>
            <!-- 其他 -->
            <Copy v-else is-button :content="`订单号：${order.orderNo}\n应付金额：${draftPaymentAmount}元`">全部复制</Copy>
          </div>
          <div class="confirm-item">
            <!-- 智付E+,邦+ 使用支付单号（businessSeqNo），保留智联通订单号显示逻辑 -->
            <div class="head">{{ [PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id, PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id].includes(order.paymentChannel) ? '支付单号' : '订单号' }}</div>
          </div>
          <div class="confirm-item">
            <div>
              {{ [PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id, PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id].includes(order.paymentChannel) ? order.businessSeqNo : order.orderNo }}
              <Copy :content="[PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id, PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id].includes(order.paymentChannel) ? order.businessSeqNo : order.orderNo" />
            </div>
          </div>
          <!-- 智付E+,邦+,E++ 订单展示付款帐号 -->
          <template v-if="[PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id, PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id, PAYMENT_CHANNEL.YL_PLUS.id].includes(order.paymentChannel)">
            <div class="confirm-item">
              <div class="head">付款账号</div>
              <div class="head">应付金额</div>
            </div>
            <div class="confirm-item">
              <div>
                {{ paymentAccount }}
                <Copy :content="paymentAccount.replaceAll(',', '')" />
              </div>
              <div>
                {{ draftPaymentAmount }}元
                <Copy :content="draftPaymentAmount.replaceAll(',', '')" />
              </div>
            </div>
          </template>
        </template>
      </div>
    </div>
    <template #footer>
      <div class="footer-btn">
        <div>
          <!-- E++渠道 && 非爬虫方案 显示识单助手 -->
          <IdentificationAssistant
            v-if="hasEPlusChannel && !startCrawlerScheme"
            ref="identificationAssistantRefs"
            height="36"
            :order="order"
            :type="E_PLUS_RECOGNITION_SCENE.PAYMENT_ORDER.id"
            @success="isPaymentSuccess"
          />
        </div>
        <div>
          <el-button size="medium" @click="() => { visible = false }">我再想想</el-button>
          <el-button
            v-if="hasYlBankPaymentChannel"
            v-waiting="orderApi.postSingleExecuteOrderTradeStep"
            type="primary"
            size="medium"
            @click="payConfirm"
          >
            支付完成
          </el-button>
          <el-button
            v-if="hasAllOrder"
            type="primary"
            size="medium"
            @click="handleToPay"
          >
            去支付
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import orderApi from '@/apis/order'
import BigNumber from 'bignumber.js'
import { EXECUTE_TRANSACTION_PROCESS, PAYMENT_CHANNEL, E_PLUS_LOGIN_AUTH_KEY, E_PLUS_RECOGNITION_SCENE } from '@/constant'
import { parseNum } from '@/common/js/number'
import { openWindow } from '@/common/js/util'
import { jumpJdPay } from '@/utils/jdpay.js'
import Copy from '@/views/components/common/copy/copy.vue' // 复制组件
import WarnContent from '../../common/warn-content.vue'
import { showConfirmationDialog } from '../draftNo-Check'// 智付E+渠道订单确认弹窗
import { mapGetters } from 'vuex'
import { OPEN_BANK_LOGIN_AUTH_DIALOG, OPEN_BANK_LOGIN_AUTH_SUCCESS } from '@/event/modules/site'
import IdentificationAssistant from '@/views/components/identification-assistant/index.vue'
export default {
  name: 'payment-tips-dialog',
  components: {
    Copy,
    WarnContent,
    IdentificationAssistant
  },
  data() {
    return {
      visible: false,
      order: {}, // 当前订单对象
      PAYMENT_CHANNEL,
      E_PLUS_RECOGNITION_SCENE,
      orderApi
    }
  },
  computed: {
    ...mapGetters({
      paymentAccountList: 'user/paymentAccountList', // 电子交易账户列表
    }),
    ...mapGetters('common', {
      startCrawlerScheme: 'startCrawlerScheme', // E++是否开启爬虫方案 1开启 0关闭
    }),
    // 是否批量
    hasAllOrder() {
      return this.order instanceof Array
    },
    // 应付金额
    draftPaymentAmount() {
      return this.getAmount(this.order, 'draftPaymentAmount')
    },
    // 是否包含亿联银行渠道
    hasYlBankPaymentChannel() {
      // return (this.hasAllOrder ? this.order : [this.order]).some(order => order.paymentChannel === PAYMENT_CHANNEL.YI_LIAN_BANK.id || order.paymentChannel === PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id)
      return (this.hasAllOrder ? this.order : [this.order]).some(order => [PAYMENT_CHANNEL.YI_LIAN_BANK.id, PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id, PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id, PAYMENT_CHANNEL.YL_PLUS.id].includes(order.paymentChannel))
    },
    // 是否包含智付E+渠道  ZHI_FU_YI_LIAN_PLUS
    hasYiLianPlusChannel() {
      return (this.hasAllOrder ? this.order : [this.order]).some(order => order.paymentChannel === PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id)
    },
    // 是否包含智付邦+渠道
    hasZbPlusChannel() {
      return (this.hasAllOrder ? this.order : [this.order]).some(order => order.paymentChannel === PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id)
    },
    // 是否包含E++渠道
    hasEPlusChannel() {
      return (this.hasAllOrder ? this.order : [this.order]).some(order => order.paymentChannel === PAYMENT_CHANNEL.YL_PLUS.id)
    },
    // 邦+ E+付款账号
    paymentAccount() {
      if (this.order.paymentChannel === PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id) {
        return this.order.jdylAccountNo
      } else {
        const arr = (this.paymentAccountList && this.paymentAccountList?.filter(e => e.paymentChannel === this.order.paymentChannel)) || []
        return arr.length ? arr[0].jdAccountNo : ''
      }
    }

  },

  methods: {
    showConfirmationDialog,
    // 简易版网银登录成功回调 key=>参照枚举类型 E_PLUS_LOGIN_AUTH_KEY
    handleAuthSuccess(obj) {
      if (obj?.key === E_PLUS_LOGIN_AUTH_KEY.PAYMENT_COMPLETE) { // 支付完成
        this.isPaymentSuccess()
      }
    },
    init() {
      // E++开启爬虫方案时,登录校验成功
      this.$event.on(OPEN_BANK_LOGIN_AUTH_SUCCESS, this.handleAuthSuccess)
      if (this.order && Array.isArray(this.order) && this.order.length > 99) {
        this.$message.warning(`每次批量操作最多可选择 ${99} 笔订单`)
        return
      }
      if (this.hasAllOrder && this.hasYlBankPaymentChannel) {
        // 批量支付选中列表包含的亿联渠道或者邦+渠道名称
        let channelName = [...new Set(this.order.filter(order => [PAYMENT_CHANNEL.YI_LIAN_BANK.id, PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id, PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id].includes(order.paymentChannel)).map(item => item.payType))]
        this.$message.warning(`暂不支持包含${channelName.join(',')}渠道订单的批量支付`)
        return
      }
      if (this.hasAllOrder || this.hasYlBankPaymentChannel) {
        // 智付E+、邦+渠道支付 businessSeqNo支付单号异常不存在时终止支付弹窗逻辑
        if (!this.hasAllOrder && [PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id, PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id].includes(this.order.paymentChannel) && !this.order.businessSeqNo) return this.$message.error('订单支付异常，请稍后重试')
        this.visible = true
        if (this.hasYiLianPlusChannel) {
        // 判断是不是智付E+
          this.showConfirmationDialog('签收', { type: `资方${this.hasAllOrder ? '批量' : '单张'}支付`, info: this.order })
        }
      } else {
        this.postPayment(this.order)
      }
    },

    // 支付请求
    postPayment(order) {
      if (!order) {
        return
      }

      try {
        const callbackUrl = `${window.location.protocol}//${window.location.host}/pay-success?url=/user-center/buy-draft?tab=3`
        const orderNoList = this.hasAllOrder ? order.map(i => i?.orderNo) : [order?.orderNo]
        if (this.$store.state.user.isSdkLink) { // 通过SDK跳转
          jumpJdPay({
            name: 2,
            platformOrderNo: orderNoList, // 平台单号集合
            querys: {
              cb: callbackUrl
            }
          })
          return
        }
        const param = {
          orderNoList, // 订单编号数组
          orderStepsEnumCode: EXECUTE_TRANSACTION_PROCESS.PAY,
          callbackUrl, // 支付成功回调地址目前使用默认地址
          authenticateFailUrl: callbackUrl, // TODO:认证失败回调地址目前使用默认地址
        }

        openWindow(async() => {
          const payUrl = await (orderApi.getCashierDeskUrl(param, this.hasAllOrder ? 30000 : 10000) || '/')
          return payUrl
        })
        this.$message({
          type: 'success',
          message: '正在跳转'
        })
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log('支付请求error :>> ', error)
      }
    },

    // 根据key值计算金额总额
    getAmount(order, key) {
      let amount = 0
      if (!this.order) {
        return
      }
      if (this.hasAllOrder) {
        for (let item of order) {
          amount = new BigNumber(item[key]).plus(amount)
        }
      } else {
        amount = new BigNumber(order[key])
      }
      return parseNum(amount)
    },

    // 去支付
    handleToPay() {
      this.postPayment(this.order)
      this.visible = false
    },

    // 支付完成确认校验
    payConfirm() {
      // E++ 爬虫方案 登录校验
      if (this.startCrawlerScheme && this.hasEPlusChannel) {
        this.$event.emit(OPEN_BANK_LOGIN_AUTH_DIALOG, { key: E_PLUS_LOGIN_AUTH_KEY.PAYMENT_COMPLETE })
      } else {
        this.isPaymentSuccess()
      }
    },

    // 支付完成校验
    async isPaymentSuccess() {
      const params = {
        orderNoList: this.hasAllOrder ? this.order.map(item => item.orderNo) : [this.order.orderNo],
        orderStepsEnumCode: EXECUTE_TRANSACTION_PROCESS.PAY,
      }
      if (this.hasAllOrder) {
        // 暂不支持亿联银行批量支付
        // await orderApi.postExecuteOrderTradeStep(params)
      } else {
        await orderApi.postSingleExecuteOrderTradeStep(params)
      }
      this.$message.success('支付成功')
      this.$emit('success') // 通知列表刷新
      this.visible = false
      this.closed()
    },

    // 关闭回调
    closed() {
      this.$event.off(OPEN_BANK_LOGIN_AUTH_SUCCESS, this.handleAuthSuccess)
      this.$emit('destroy')
    },
  }
}
</script>
