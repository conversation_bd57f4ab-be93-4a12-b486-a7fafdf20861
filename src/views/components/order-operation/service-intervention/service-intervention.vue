<!-- 客服介入 -->
<style lang="scss" scoped>
.service-intervention {
  display: inline-block;
}
</style>

<template>
  <div class="service-intervention order-operation">
    <slot name="button">
      <el-button
        v-bind="$attrs"
        :type="$attrs.type || 'primary'"
        :width="$attrs.width || '104'"
        :height="$attrs.height || '40'"
        :border="!$attrs.type"
        @click="init"
        v-on="$listeners"
      >
        <slot>客服介入</slot>
      </el-button>
    </slot>
  </div>
</template>

<script>
import ServiceInterventionDialog from './service-intervention-dialog.vue'
import orderOperationMixin from '../order-operation-mixin'

export default {
  name: 'service-intervention',
  mixins: [orderOperationMixin(ServiceInterventionDialog)],
}
</script>
