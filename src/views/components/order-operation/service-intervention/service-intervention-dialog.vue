<!-- 客服介入 -->
<style lang="scss" scoped>
.service-intervention {
  ::v-deep {
    .el-textarea__inner {
      padding: 8px 12px;
    }

    .el-form-item {
      margin-bottom: 10px;
    }

    .el-form-item__content {
      line-height: 0;
    }

    .el-form-item__error {
      position: static;
    }

    .el-textarea .el-input__count {
      bottom: 15px;
      background-color: rgb(0 0 0 / 0%);
    }

    // 单选按钮样式 start
    .el-radio {
      margin: 0 8px 0 0;
      border: 1px solid $color-D9D9D9;
      border-radius: 2px;
      min-width: 170px;
      height: 40px;
      text-align: center;
      color: $color-text-primary;
      line-height: 40px;
    }

    .el-radio__input {
      display: block;
    }

    .el-radio__label {
      padding-left: 0;
      font-size: 16px;
      line-height: 24px;
    }

    .el-radio.is-bordered {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 8px 0 0;
      padding: 0 13px;

      &:last-child {
        margin-right: 0;
      }
    }

    .is-checked {
      border-color: $--color-primary;
      background-color: $--color-primary-hover;
    }

    // 单选按钮样式 end
  }

  .main {
    padding: 16px;
    background: $color-FFFFFF;

    .item-lable {
      margin-bottom: 2px;
      height: 22px;
      font-size: 14px;
      color: $color-text-secondary;
      line-height: 22px;
    }

    .account-select {
      width: 100%;
    }
  }
}

.uplode-voucher-box {
  display: flex;
  justify-content: space-between;
}

.red-high-light {
  font-weight: 600;
  color: $--color-font-main;
}

.upload-item {
  width: 170px;
}

.star {
  &::before {
    position: relative;
    top: 3px;
    margin-right: 2px;
    font-size: 14px;
    color: $color-warning;
    line-height: 22px;
    content: "*";
  }
}

// 标题
.title-left-border {
  position: relative;
  margin-bottom: 10px;
  padding-left: 12px;
  height: 22px;
  font-size: 16px;
  font-weight: 600;
  line-height: 22px;

  &::before {
    position: absolute;
    top: 2px;
    left: 0;
    width: 4px;
    height: 16px;
    background: $--color-primary;
    content: "";
  }
}

// 合同按钮
.contract-btn {
  float: left;
  height: 42px;
  line-height: 38px;

  ::v-deep {
    .el-link--inner {
      height: 22px;
      font-size: 14px;
      line-height: 22px;
    }

    .el-link.el-link--primary {
      color: $font-color;

      @include example-underline;

      &::after {
        border: 0;
        color: $font-color-hover;
      }
    }
  }
}

.warn-content {
  margin-bottom: 12px;
  font-size: 16px;
}
</style>

<template>
  <el-dialog
    width="600px"
    :visible.sync="visible"
    :title="order && titleDialog"
    :before-close="handleClose"
    :close-on-click-modal="false"
    class="service-intervention order-operation-dialog"
  >
    <WarnContent class="warn-content">
      有交易纠纷可申请客服介入，客服会按实际情况判定违约并处理争议。
    </WarnContent>

    <el-form
      ref="ruleForm"
      :model="form"
      :rules="rules"
    >
      <div class="main">
        <!-- 介入原因选项 -->
        <template v-if="!isSupplement">
          <div class="title-left-border">介入原因</div>
          <el-form-item prop="interveneReason">
            <el-radio-group v-model="form.interveneReason">
              <el-radio
                v-for="item in option"
                :key="item.value"
                :label="item.label"
                type="button"
              >
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </template>
        <!-- 上传凭证 -->
        <div class="item-lable star">上传凭证（单个凭证大小不超过2M）</div>
        <el-form-item prop="frontImageUrl">
          <div class="uplode-voucher-box">
            <ImgUpload
              v-model="voucherUrlOne"
              :height="75"
              class="upload-item"
              :size-limit="2"
              :dir="OSS_DIR.INTERVENTION_VOUCHER"
              @input="(src) => handleUploadBackImg(src, 0)"
            >
              <div slot="empty">
                <p>点击或拖拽图片至此</p>
                <p>上传凭证</p>
              </div>
            </ImgUpload>
            <ImgUpload
              v-model="voucherUrlTwo"
              :height="75"
              class="upload-item"
              :size-limit="2"
              :dir="OSS_DIR.INTERVENTION_VOUCHER"
              @input="(src) => handleUploadBackImg(src, 1)"
            >
              <div slot="empty">
                <p>点击或拖拽图片至此</p>
                <p>上传凭证</p>
              </div>
            </ImgUpload>
            <ImgUpload
              v-model="voucherUrlThree"
              :height="75"
              class="upload-item"
              :size-limit="2"
              :dir="OSS_DIR.INTERVENTION_VOUCHER"
              @input="(src) => handleUploadBackImg(src, 2)"
            >
              <div slot="empty">
                <p>点击或拖拽图片至此</p>
                <p>上传凭证</p>
              </div>
            </ImgUpload>
          </div>
        </el-form-item>
        <div class="item-lable">补充说明</div>
        <el-form-item prop="evidenceNote">
          <el-input
            v-model="form.evidenceNote"
            type="textarea"
            maxlength="50"
            :autosize="{ minRows: 4}"
            class="remark-textarea"
            show-word-limit
            placeholder="请填写客服介入原因"
          />
        </el-form-item>
      </div>
    </el-form>
    <span slot="footer">
      <span class="contract-btn">
        <a
          class="text-link"
          target="_blank"
          :href="order.draftType ? PLATFORM_DEFAULT_RULESNEW_URL_NEW_DRAFT : PLATFORM_DEFAULT_RULESNEW_URL"
          rel="noopener noreferrer"
        >《平台订单违约规则》</a>
      </span>
      <el-button :disabled="loading" @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="confirm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { OSS_DIR, DRAFT_STATUS, TRANSACTION_STATUS } from '@/constant' // 上传文件夹
import ImgUpload from '@/views/components/common/img-upload/img-upload.vue' // 图片上传组件
import orderApi from '@/apis/order'
import WarnContent from '../../common/warn-content.vue'
import { PLATFORM_DEFAULT_RULESNEW_URL, PLATFORM_DEFAULT_RULESNEW_URL_NEW_DRAFT } from '@/constants/oss-files-url' // 平台订单违约规则url
export default {
  name: 'service-intervention-dialog',
  components: {
    ImgUpload,
    WarnContent
  },
  data() {
    // 图片校验
    const validateFrontImageUrl = (rule, value, callback) => {
      const { voucherUrlOne, voucherUrlTwo, voucherUrlThree } = this
      if (!(voucherUrlOne || voucherUrlTwo || voucherUrlThree)) {
        callback(new Error('请上传凭证'))
      } else {
        callback()
      }
    }
    return {
      PLATFORM_DEFAULT_RULESNEW_URL, // 平台订单违约规则url
      PLATFORM_DEFAULT_RULESNEW_URL_NEW_DRAFT,
      order: null, // 当前操作的订单
      OSS_DIR, // 上传文件夹
      visible: false, // 弹窗是否打开
      isSupplement: false, // 是否是补充凭证，默认false-不是
      voucherUrlOne: '',
      voucherUrlTwo: '',
      voucherUrlThree: '',
      form: {
        billOrderId: null,
        evidenceImgUrl: [], // 凭证图片url，多个以，隔开，非空字段
        interveneReason: '', // 介入原因选项
        evidenceNote: '', // 补充说明
        disputeSource: null// 争议订单来源，1-票方，2-资方，非空字段
      },
      loading: false,
      // 表单校验规则
      rules: {
        frontImageUrl: [{ required: true, validator: validateFrontImageUrl, trigger: ['change', 'blur'] }], // 使用'none',是的不默认触发，只能通过方法触发
      },
    }
  },

  computed: {
    // 取消中-待签收
    cancelingSubmission() {
      return this.order.tabStatus === DRAFT_STATUS.CANCELING.id && this.order?.transactionStatus === TRANSACTION_STATUS.SUBMISSION_BUY_CANCELING.id
    },

    // 取消中-待背书
    cancelingEndorse() {
      return this.order.tabStatus === DRAFT_STATUS.CANCELING.id && this.order?.transactionStatus === TRANSACTION_STATUS.ENDORSEMENT_SALE_CANCELING.id
    },

    titleDialog() {
      return this.isSupplement ? '补充凭证' : '申请客服介入'
    },

    // TODO:好像用不到，最后看看是否去掉
    // 倒计时时间
    endTime() {
      const data = this.order
      let endTime = 0
      // 不同状态显示不同的倒计时
      switch (data.tabStatus) {
        case DRAFT_STATUS.WAITING_RECEIVING.id: // 待接单
          endTime = new Date().getTime()
          break
        case DRAFT_STATUS.WAITING_CONFIRM.id: // 待确认
          endTime = data.verifyEndTime || 0
          break
        case DRAFT_STATUS.WAITING_PAY.id: // 待支付
          endTime = data.payEndTime || 0
          break
        case DRAFT_STATUS.WAITING_ENDORSE.id: // 待背书
          endTime = data.endorseEndTime || 0
          break
        case DRAFT_STATUS.WAITING_SIGN.id: // 待签收
          endTime = data.submissionEndTime || 0
          break
        case DRAFT_STATUS.CANCELING.id: // 取消中
          endTime = data.cancelEndTime || 0
          break
        default:
          break
      }
      // console.log('endTime :>> ', endTime)
      return endTime
    },

    // 计时开始时间
    startTime() {
      const data = this.order
      let startTime = 0
      // 不同状态显示不同的倒计时
      switch (data.tabStatus) {
        case DRAFT_STATUS.WAITING_RECEIVING.id: // 待接单
          startTime = data.createdAt || 0
          break
        default:
          startTime = (new Date().getTime() - this.diffTime)
          break
      }
      startTime = startTime > 0 ? startTime : 0
      // console.log('startTime :>> ', startTime)
      return startTime
    },

    // 是否已经超时/倒计时结束
    isOverTime: {
      get() {
        let res = false
        // 当倒计时的情况下，开始时间大于结束就证明倒计时已经结束
        if (this.startTime > this.endTime && this.isCountDown) {
          res = true
        }
        this.setState({
          isOvertime: res
        })
        return res
      },
      set(val) {
        this.setState({
          isOvertime: val
        })
      }
    },

    // 取消类型选项
    option() {
      let reasonType = []
      const tabStatus = this.order?.tabStatus
      const isOvertime = this.order?.isOvertime
      const initiator = this.order?.initiator // 1-票方，0-资方
      switch (tabStatus) {
        case DRAFT_STATUS.WAITING_ENDORSE.id: // 待背书
          reasonType = initiator ? [] : [
            {
              value: 1,
              label: '票方超时未背书'
            }
          ]
          break
        case DRAFT_STATUS.WAITING_SIGN.id: // 待签收解付
          if (initiator) {
            reasonType = isOvertime ? [
              {
                value: 1,
                label: '资方超时未解付'
              }
            ] : []
          }
          break
        case DRAFT_STATUS.CANCELING.id: // 取消中
          if (initiator) {
            reasonType = isOvertime ? [
              {
                value: 1,
                label: '票方超时未同意取消'
              }
            ] : [
              {
                value: 1,
                label: '取消方描述不符'
              }
            ]
          } else {
            reasonType = isOvertime ? [
              {
                value: 1,
                label: '票方超时未同意取消'
              }
            ] : []
          }

          // 取消中-待签收
          if (this.cancelingSubmission) {
            if (initiator) {
              reasonType = [
                {
                  value: 1,
                  label: '取消方描述不符'
                }
              ]
            }
          }
          break
        default:
          // code block
      }
      return reasonType
    },
  },

  methods: {
    init() {
      this.visible = true
      // console.log(this.order)
      this.$nextTick().then(() => {
        this.isSupplement = this.order?.isSupplement
        this.form.disputeSource = this.order.initiator ? 1 : 2// 争议订单来源，1-票方，2-资方，非空字段
        this.form.interveneReason = this.option[0]?.label // 设置取消类型默认选项
      })
    },

    // 介入凭证上传
    handleUploadBackImg(src, index) {
      this.form.evidenceImgUrl[index] = src
    },

    // 确定
    async  confirm() {
      try {
        await this.$refs.ruleForm.validate()
        let params = {
          orderNo: this.order.orderNo, // 订单号，非空字段
          evidenceImgUrl: this.form.evidenceImgUrl.filter(item => item.length > 0).toString(), // 凭证图片url，多个以，隔开，非空字段
          replenish: this.form.evidenceNote // 补充说明
        }
        if (this.isSupplement) { // 是否是补充凭证
          this.postReplenishEvidence(params)
        } else {
          const { interveneReason, disputeSource } = this.form
          params.reason = interveneReason
          params.disputeSource = disputeSource
          this.postEvidence(params)
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log(error)
      }
    },

    // 客服介入请求
    async postEvidence(params) {
      try {
        await orderApi.postEvidence(params)
        this.$message({
          message: '申请成功，请等待客服处理',
          type: 'success'
        })

        this.$emit('success')
        this.handleClose()
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log(error)
      }
    },

    // 补充凭证请求
    async postReplenishEvidence(params) {
      try {
        await orderApi.postReplenishEvidence(params)
        this.$message({
          message: '申请成功，请等待客服处理',
          type: 'success'
        })
        this.$emit('success')
        this.handleClose()
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log(error)
      }
    },

    // 关闭
    handleClose() {
      // 清空
      this.form = {
        billOrderId: null,
        evidenceImgUrl: [], // 凭证图片url，多个以，隔开，非空字段
        interveneReason: '', // 介入原因选项
        evidenceNote: '', // 补充说明
        disputeSource: null// 争议订单来源，1-票方，2-资方，非空字段
      }
      this.voucherUrlOne = ''
      this.voucherUrlTwo = ''
      this.voucherUrlThree = ''
      this.$nextTick().then(() => {
        this.$refs.ruleForm && this.$refs.ruleForm.clearValidate()
      })
      this.visible = false
      this.$emit('destroy')
    }
  }

}
</script>
