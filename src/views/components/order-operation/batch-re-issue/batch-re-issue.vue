<!-- 批量重新发布订单 -->
<style lang="scss" scoped>
.batch-re-issue {
  display: inline;
}
</style>

<template>
  <el-tooltip
    content="仅审核通过的订单可批量重新发布"
    placement="top"
    :disabled="tooltipDisabled"
  >
    <div class="batch-re-issue order-operation">
      <slot name="button">
        <transaction-tooltip-button
          v-waiting="[orderApi.batchRepublishAfterReview, orderApi.batchRepublishAfterFail]"
          v-bind="$attrs"
          :type="$attrs.type || 'primary'"
          :border="!$attrs.type"
          :width="$attrs.width || '140'"
          :height="$attrs.height || '40'"
          :types="[TRANSACTION_TOOLTIP_TYPE.CREDIT]"
          @click="onReIssueChange($event)"
          v-on="$listeners"
        >
          <slot>批量重新发布</slot>
        </transaction-tooltip-button>
      </slot>
    </div>
  </el-tooltip>
</template>

<script>
import BatchReIssueDialog from './batch-re-issue-dialog.vue'
import orderOperationMixin from '../order-operation-mixin'
import orderApi from '@/apis/order'
import { TRANSACTION_TOOLTIP_TYPE } from '@/constants/transaction-tooltip'

export default {
  name: 'change-endorsement-account',
  mixins: [orderOperationMixin(BatchReIssueDialog)],
  props: {
    type: String,
    status: Number,
    tooltipDisabled: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      TRANSACTION_TOOLTIP_TYPE,
      orderApi,
    }
  },
  computed: {
    limitReleaseInfo() { // 是否关闭重新发布
      return this.$store.state.common.limitReleaseInfo || {}
    }
  },
  methods: {
    // 点击重新发布 tabStatus 8:已下架，10:已失败
    // eslint-disable-next-line padded-blocks
    onReIssueChange(event) {

      /**
       * this.$route.query.tab => 8 已下架  this.$route.query.tab => 9 已失败
       * 选中订单包含识票助手单张识别/自动识别发布的订单不做限制处理 允许批量发布 做订单过滤处理
       * 选中订单全部为普通发布订单 且 当前用户不在白名单 拦截提示
       * 选中订单全部为普通发布订单 且 当前用户在白名单 允许发布
       */

      if ((this.order instanceof Array && this.order.some(e => e.publishSource === 5 && [1, 5].includes(e.orderType))) || (this.order.publishSource === 5 && [1, 5].includes(this.order.orderType))) {
        this.init(event)
        return
      }

      if (
        (Number(this.$route.query.tab) === 8 && this.limitReleaseInfo.republishFlag === 1)
        || (Number(this.$route.query.tab) === 10 && this.limitReleaseInfo.republishFailFlag === 1)
      ) {
        this.$emit('open-release-limit')
      } else {
        this.init(event)
      }
    }
  }
}
</script>
