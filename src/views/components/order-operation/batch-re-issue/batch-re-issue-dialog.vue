<!-- 批量重新发布订单弹窗 -->
<template>
  <div>
    <!-- 批量发布loading -->
    <LoadingDialog :visible="loading" title="批量重新发布中" content="正在重新发布中，请耐心等待..." />

    <!-- 批量发布结果 -->
    <ResultDialog ref="resultDialogRef" handle-str="批量发布" @close="closeResultDialog" />
  </div>
</template>

<script>
import orderApi from '@/apis/order'
import spTradeApi from '@/apis/sp-trade'
import ResultDialog from '@/views/components/common/result-dialog/result-dialog.vue' // 批量操作结果组件
import LoadingDialog from '@/views/components/common/loading-dialog/loading-dialog.vue' // 加载中组件
import { DRAFT_STATUS } from '@/constant'// 订单状态
import { ISSUE_DRAFT_ERROR_CODE } from '@/constants/draft'
import { VALID_FAIL_TYPE } from '@/constants/sp-trade'
import { isWealthCommerceDraft } from '@/utils/util'
import { windowCommunication } from '@/utils/window-event'

export default {
  name: 'batch-re-issue-dialog',
  components: {
    ResultDialog,
    LoadingDialog
  },
  props: {
    order: [Object, Array],
  },
  data() {
    return {
      loading: false,
      status: null,
      tooltipDisabled: null
    }
  },
  computed: {
    // 是否批量
    hasAllOrder() {
      return this.order instanceof Array
    },
    limitReleaseInfo() { // 是否关闭重新发布
      return this.$store.state.common.limitReleaseInfo || {}
    },

    /** 是否包含助手单张/自动识别订单 且 普通发布订单不在白名单
     *
     * publishSource 发布来源。 1-pc，2-app，3-其他平台，4-H5，5-签手，6-秒贴
     * orderType 1单张识别  2批量识别  3网银单张同步 4网银批量 5自动识别
     * 选中订单包含助手单张/自动识别订单 且 包含普通发布的订单当前用户不在白名单 需过滤不在白名单中的普通订单
     */

    hasAssistantFlag() {
      return this.hasAllOrder
              && this.order.some(item => ([1, 5].includes(item.orderType) && item.publishSource === 5))
              && ((Number(this.$route.query.tab) === 8 && this.limitReleaseInfo.republishFlag === 1)
                  || (Number(this.$route.query.tab) === 10 && this.limitReleaseInfo.republishFailFlag === 1))
    },
    // 是否需要过滤数组
    // isFIlterOrder() {
    // // publishSource 发布来源。 1-pc，2-app，3-其他平台，4-H5，5-签手，6-秒贴
    // // orderType 1单张识别  2批量识别  3网银单张同步 4网银批量 5自动识别

    //   return this.hasAssistantFlag
    //   // return Array.isArray(this.order) && (Number(this.$route.query.tab) === 10 || Number(this.$route.query.tab) === 8) && this.order.some(item => !([1, 5].includes(item.orderType) && item.publishSource === 5))
    // }
  },

  methods: {
    init(props) {
      if (this.order && Array.isArray(this.order) && this.order.length > 99) {
        this.$message.warning(`每次批量操作最多可选择 ${99} 笔订单`)
        return
      } else if (this.hasAssistantFlag) {
        this.$message.warning('已为您过滤掉不可发布的票据')
      }
      const { status, tooltipDisabled } = props
      this.status = status
      this.tooltipDisabled = tooltipDisabled
      this.$msgbox({
        title: '提示',
        dangerouslyUseHTMLString: true,
        message: '是否确认批量重新发布订单？',
        confirmButtonText: '确认',
        showCancelButton: true,
        cancelButtonText: '再想想',
        type: 'warning',
      }).then(() => {
        this.postReIssue(this.hasAssistantFlag ? this.order.filter(item => [1, 5].includes(item.orderType) && item.publishSource === 5) : this.order)
      })
        .catch(() => {
          this.$emit('destroy')
        })
    },

    // 操作结果弹窗关闭回调
    closeResultDialog() {
      this.$emit('success')
      this.$emit('destroy')
    },

    // 重新发布请求
    async postReIssue(order) {
      await this.checkSpTradeAccess(order)
      try {
        this.loading = true
        const orderNoList = this.hasAllOrder ? order.map(i => i.orderNo) : [order.orderNo]
        const apiObj = {
          [DRAFT_STATUS.OFF_SHELF.id]: orderApi.batchRepublishAfterReview,
          [DRAFT_STATUS.DEAL_FAIl.id]: orderApi.batchRepublishAfterFail,
        }
        const data = await apiObj[this.status]({ orderNoList })
        this.$refs.resultDialogRef.init(data)
        this.loading = false
      } catch (error) {
        this.loading = false
        if (error.data?.code === ISSUE_DRAFT_ERROR_CODE.GAO_MAI_DI_MAI) {
          this.$alert('您因触发平台机制，被限制登录，请联系您的客户经理', '提示', {
            confirmButtonText: '我知道了',
          }).then(() => this.$store.dispatch('user/logout', { manual: true }))
            .then(() => {
              this.$message.closeAll()
              this.$router.push('/')
              windowCommunication.trigger()
            })
          return
        }
        this.$message.error(error.data?.msg ?? '系统繁忙，请稍后再试')
      }
    },

    // 商票/财票--交易准入查询
    async checkSpTradeAccess(order) {
      // 判断是  商票或者是财票  都要加上白名单判断
      if (this.hasAllOrder ? order.some(draft => !isWealthCommerceDraft(draft.draftNo, draft.acceptorType)) : !isWealthCommerceDraft(order.draftNo, order.acceptorType)) return

      const corpId = this.$store.state?.user?.corpInfo?.id
      const { pass, failType } = await spTradeApi.whiteListCheck({
        corpId,
        accepterList: this.hasAllOrder ? order.filter(draft => isWealthCommerceDraft(draft.draftNo, draft.acceptorType)).map(draft => ({
          accepter: draft.acceptorName,
          amount: draft.draftAmount
        })) : [{ accepter: order.acceptorName, amount: order.draftAmount }]
      })
      if (pass) return
      // if (failType === VALID_FAIL_TYPE.NO_ACCESS.id) {
      //   await this.$confirm('暂无发布商票订单权限，仍需发布请先发起权限申请', '提示', {
      //     type: 'warning',
      //     confirmButtonText: '发起申请',
      //     cancelButtonText: '取消'
      //   })
      //   const hasApply = await spTradeApi.existUnderReviewAccessCorpApply(corpId)
      //   if (hasApply) {
      //     this.$message.warning('已有申请正在审核中，请耐心等待')
      //   } else {
      //     this.$router.push('/user-center/apply-sp-permission')
      //   }
      // } else
      if (failType === VALID_FAIL_TYPE.NO_CREDIT.id) {
        this.$alert('该承兑人不在授信白名单，请联系您的客户经理!', '提示')
        // await this.$confirm(`承兑人「${accepterNameList.map(item => item.accepter).join()}」暂不在授信名单内，仍需发布请先发起授信申请`, '提示', {
        //   type: 'warning',
        //   confirmButtonText: '发起申请',
        //   cancelButtonText: '取消'
        // })
        // const temp = window.open(`${this.$store.state.spbUrl}`)
        // this.$store.dispatch('corp-info/getCorpInfoSync', '/issue/white').then(res => {
        //   temp.location.href = res
        // })
      } else if (failType === VALID_FAIL_TYPE.NO_AMOUNT.id) {
        this.$alert('该承兑人授信额度不足，若仍需发布请联系您的客户经理', '提示')
      } else if (failType === VALID_FAIL_TYPE.NO_ABNORMAL.id) {
        this.$alert('该承兑人白名单状态异常，请联系您的客户经理!', '提示')
      }
      return Promise.reject(new Error('商票交易核验未通过'))
    },
  }
}
</script>
