<!-- 发起/修改变更票号 -->
<style lang="scss" scoped>
.c-btn-wrapper {
  display: inline-block;
}
</style>

<template>
  <div class="c-btn-wrapper order-operation" @click="init">
    <slot name="button">
      <el-button
        v-bind="$attrs"
        :type="$attrs.type || 'primary'"
        :width="$attrs.width || '140'"
        :height="$attrs.height || '40'"
        :size="$attrs.size || 'large'"
        v-on="$listeners"
      >
        <slot>{{ btnText }}</slot>
      </el-button>
    </slot>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import UpdateTicketNumberDialog from './update-ticket-number-dialog.vue'
import orderOperationMixin from '../order-operation-mixin'

export default {
  name: 'update-ticket-number',
  mixins: [orderOperationMixin(UpdateTicketNumberDialog)],
  props: {},
  computed: {
    ...mapGetters('draft-detail', {
      updateTicketStatus: 'updateTicketStatus'
    }),
    btnText() {
      let text = ''
      if (this.updateTicketStatus === 0) {
        text = '发起变更票号'
      } else if (this.updateTicketStatus === 1) {
        text = '修改变更票号'
      } else {
        text = '查看变更票号'
      }
      return text
    }
  },
  created() {
    // this.$store.dispatch('draft-detail/queryUpdateTicketInfo', { orderNo: this.order.orderNo })
  }
}
</script>
