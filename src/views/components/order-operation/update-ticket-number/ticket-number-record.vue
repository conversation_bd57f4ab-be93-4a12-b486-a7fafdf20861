
<!-- 上一次变更票号记录 -->
<style lang="scss" scoped>
  .c-form-wrapper {
    padding: 16px;
    background: $color-FFFFFF;

    .img-ticket-div {
      width: 200px;
    }

    ::v-deep {
      .el-form-item__label {
        float: none;
      }
    }
  }

  .tips-color {
    color: $color-warning;
  }

  .text-font {
    margin-top: 16px;
    font-weight: bold;
  }
</style>

<template>
  <el-dialog
    width="560px"
    :visible.sync="visible"
    append-to-body
    :close-on-click-modal="false"
    title="上次变更票号记录"
    custom-class="default"
    :before-close="handleClose"
  >
    <el-form
      ref="formInstance"
      :model="form"
      class="c-form-wrapper"
    >
      <el-form-item :label="`实际${labelText}票据正面`" prop="draftImgUrl">
        <div class="img-ticket-div">
          <ImgUpload
            v-model="form.draftImgUrl"
            height="98"
            :dir="OSS_DIR.DRAFT"
            :size-limit="4"
            size-limit-message="上传票面图片大小不能超过 4MB！"
            disabled
          >
            <template slot="empty">
              点击或拖拽上传票据正面
            </template>
          </ImgUpload>
        </div>
      </el-form-item>
      <el-form-item :label="`实际${labelText}票据号码`" prop="draftNo">
        <span>{{ form.draftNo }}</span>
      </el-form-item>
      <el-form-item v-if="isNewDraft" label="子票区间" prop="draftNo">
        <span>{{ form.subTicketStart }}-{{ form.subTicketEnd }}</span>
      </el-form-item>
      <el-form-item v-if="form.rejectReason" label="备注" prop="rejectReason">
        <span>{{ form.rejectReason }}</span>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
// oss目录
import { OSS_DIR } from '@/constant'
import ImgUpload from '@/views/components/common/img-upload/img-upload.vue' // 上传图片
import orderApi from '@/apis/order'
export default {
  name: 'handle-ticket-number-dialog',
  components: { ImgUpload },
  data() {
    return {
      visible: false,
      OSS_DIR,
      form: {},
    }
  },
  computed: {
    ...mapGetters('draft-detail', {
      isBuy: 'isBuy', // 是否资方
      isSale: 'isSale', // 是否票方
      waitingSubmission: 'waitingSubmission', // 待签收
      isNewDraft: 'isNewDraft', // 是否新票
    }),
    // 背书/签收文案
    labelText() {
    // 票方-签收/资方-背书|签收
      return (this.isSale || this.waitingSubmission) ? '签收' : '背书'
    }
  },
  methods: {
    async init(orderNo) {
      this.visible = true
      const res = await orderApi.getChangeDraftNoHistory({ orderNo })
      if (res && res.length) {
        let { draftImgUrl, draftNo, rejectReason, subTicketStart, subTicketEnd } = res[0]
        this.form = {
          draftImgUrl,
          draftNo,
          rejectReason,
          subTicketStart,
          subTicketEnd
        }
      }
    },
    // 关闭弹窗
    handleClose() {
      this.visible = false
      this.$emit('destroy')
    }
  }
}
</script>
