<!-- 变更票号弹框 -->
<style lang="scss" scoped>
.c-form-wrapper {
  padding: 16px;
  background: $color-FFFFFF;

  .img-ticket-div {
    width: 200px;
  }

  ::v-deep {
    .el-form-item__label {
      float: none;
    }

    // .el-message-box__message {
    //   .tips-color {
    //     color: $color-assist1;
    //   }
    // }
  }
}

.tips-color {
  color: $color-warning;
}

.text-font {
  margin-top: 16px;
  font-weight: bold;
}

.draft-history {
  color: $--color-primary;
  cursor: pointer;
}

.footer-fix {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>

<style>
.custom-msg-box-cls {
  .el-message-box__content {
    padding: 0 32px 12px 60px !important;
  }
}
</style>

<template>
  <!-- 发起/修改变更票号 -->
  <el-dialog
    width="560px"
    :visible.sync="visible"
    append-to-body
    :close-on-click-modal="false"
    :title="title"
    custom-class="default"
    :before-close="handleClose"
  >
    <el-form
      ref="formInstance"
      :model="form"
      :rules="updateTicketStatus !== 2 || isReApply ? rules : {}"
      class="c-form-wrapper"
    >
      <el-form-item :label="`实际${labelText}票据正面`" prop="draftImgUrl">
        <div class="img-ticket-div">
          <ImgUpload
            v-model="form.draftImgUrl"
            height="98"
            :dir="OSS_DIR.DRAFT"
            :on-success="handleUploadSuccess"
            :size-limit="4"
            size-limit-message="上传票面图片大小不能超过 4MB！"
            :disabled="updateTicketStatus === 2 && !isReApply"
            @input="handleUploadImage"
          >
            <template slot="empty">
              点击或拖拽上传票据正面
            </template>
          </ImgUpload>
        </div>
      </el-form-item>
      <el-form-item :label="`实际${labelText}票据号码`" prop="draftNo">
        <el-input
          v-if="updateTicketStatus !== 2 || isReApply"
          v-model="form.draftNo"
          maxlength="30"
          :placeholder="`请填写实际${labelText}票据号码`"
        />
        <span v-else>{{ form.draftNo }}</span>
      </el-form-item>
      <el-form-item v-if="isNewDraft" label="子票区间" prop="subTicketStart">
        <div style="display: flex;">
          <el-input
            v-model="form.subTicketStart"
            type="number"
            :number-format="{
              negative: false,
              decimal: false,
              maxIntegerLength: 12,
            }"
          />
          <span style="line-height: 30px; margin: 0 8px;">-</span>
          <el-input
            v-model="form.subTicketEnd"
            type="number"
            :number-format="{
              negative: false,
              decimal: false,
              maxIntegerLength: 12,
            }"
          />
        </div>
      </el-form-item>
      <!--
        <el-form-item v-if="showReApplyButton" label="备注" prop="rejectReason">
        <span>{{ updateTicketInfo.rejectReason }}</span>
        </el-form-item>
      -->
    </el-form>
    <div slot="footer">
      <div class="footer-fix">
        <span class="draft-history" @click="updateDraftNoRecord">上次变更票号记录</span>
        <div>
          <el-button @click="handleClose">取消</el-button>
          <!-- 修改状态下显示撤销按钮 -->
          <el-button
            v-if="updateTicketStatus === 1"
            border
            type="primary"
            @click="onRepeal(2)"
          >
            撤销
          </el-button>
          <!--
            <el-button
            v-if="showReApplyButton"
            border
            type="primary"
            @click="onReApply"
            >
            重新发起申请
            </el-button>
          -->
          <el-button v-if="updateTicketStatus !== 2 || isReApply" type="primary" @click="onConfirm">确定</el-button>
        </div>
      </div>
    </div>
    <TicketNumberRecord ref="ticketNumberRecordRefs" />
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
// oss目录
import { OSS_DIR } from '@/constant'
import issueDraftApi from '@/apis/issue-draft' // 接口
import ImgUpload from '@/views/components/common/img-upload/img-upload.vue' // 上传图片
import orderApi from '@/apis/order'
import TicketNumberRecord from './ticket-number-record'
export default {
  name: 'update-ticket-number-dialog',
  components: { ImgUpload, TicketNumberRecord },
  data() {
    // 子票区间校验
    const validateChildDraftRange = (rule, value, callback) => {
      if (!this.form.subTicketStart || !this.form.subTicketEnd) {
        callback(new Error('请输入子票区间'))
      } else {
        callback()
      }
    }
    return {
      visible: false,
      OSS_DIR,
      form: {},
      rules: {
        draftImgUrl: [{ required: true, message: '请上传票据正面', trigger: ['change'] }],
        subTicketStart: [{ required: true, validator: validateChildDraftRange, trigger: 'none' }],
        draftNo: [
          {
            required: true,
            pattern: /[0-9]{30}$/,
            message: '请填写实际背书票据号码',
            trigger: ['blur']
          }
        ],
      },
      order: {}, // 当前订单对象
      isReApply: false, // 资方是否重新发起申请
      // changeOperateType: 0, // 传给接口的操作类型字段，0发起  1修改  2撤销 3同意 4拒绝
      tipsSeller: '请您再次确认实际背书票据号码是否正确，您发起变更且资方同意后，平台订单票号将会变更，若因实际背书票号填写错误产生的相关不利法律后果由您自行承担。',
      tipsBuyer: '请您再次确认实际签收票据号码是否正确，您发起变更且票方同意后，平台订单票号将会变更，若因实际签收票号填写错误产生的相关不利法律后果由您自行承担。'
    }
  },
  computed: {
    ...mapGetters('draft-detail', {
      isSale: 'isSale', // 是否票方
      isBuy: 'isBuy', // 是否资方
      waitingSubmission: 'waitingSubmission', // 待签收
      updateTicketInfo: 'updateTicketInfo',
      updateTicketStatus: 'updateTicketStatus',
      isNewDraft: 'isNewDraft', // 是否新票
    }),
    title() {
      let text = ''
      if (this.updateTicketStatus === 0 || this.isReApply) {
        text = '发起变更票号'
      } else if (this.updateTicketStatus === 1) {
        text = '修改变更票号'
      } else if (this.updateTicketStatus === 2) {
        text = '查看变更票号'
      }
      return text
    },
    // 背书/签收文案
    labelText() {
    // 票方-背书/资方-签收
      return this.isSale && !this.waitingSubmission ? '背书' : '签收'
    },
    // 重新发起申请-资方&&待签收&&票方拒绝
    showReApplyButton() {
      return this.isBuy && this.waitingSubmission && this.updateTicketInfo?.status === 2 && !this.isReApply
    }
  },
  mounted() {
    this.rules.draftNo[0].message = `请填写实际${this.labelText}票据号码`
    // 新票重置提示文本
    if (this.isNewDraft) {
      this.isSale ? this.tipsSeller = '请您再次确认实际背书票据号码、子票区间是否正确，您发起变更且资方同意后，平台订单票号、子票区间将会变更，若因实际背书票号、子票区间填写错误产生的相关不利法律后果由您自行承担。'
        : this.tipsBuyer = '请您再次确认实际背书票据号码、子票区间是否正确，您发起变更且票方同意后，平台订单票号、子票区间将会变更，若因实际背书票号、子票区间填写错误产生的相关不利法律后果由您自行承担。'
    }
  },
  methods: {
    init() {
      this.visible = true
      if (this.updateTicketStatus !== 0) {
        const { draftImgUrl, draftNo, subTicketStart, subTicketEnd } = this.updateTicketInfo
        this.form = {
          draftImgUrl,
          draftNo,
          subTicketStart: null,
          subTicketEnd: null
        }
        // // 新票设置子票区间
        if (this.isNewDraft) {
          Object.assign(this.form, { subTicketStart: Number(subTicketStart), subTicketEnd: Number(subTicketEnd) })
        }
      }
    },
    // 票据正面上传
    handleUploadImage(src) {
      if (!src) {
        this.form = { draftImgUrl: '', draftNo: '' }
      }
    },
    // 获取ocr票据信息
    async getOcrDraftInfo(src) {
      let isError = false
      let api = 'ocrDraft'
      if (this.isNewDraft) {
        api = 'ocrNewDraft'
      }
      try {
        const data = await issueDraftApi[api]({ draftUrl: src })
        if (data) {
          // 上传票面ocr识别不到完整的票号时不再自动补充
          if (typeof data.draftNo === 'string' && data.draftNo.length !== 30) {
            this.$message.error('请上传完整票号的票面')
            isError = true
            return isError
          }
          this.$set(this.form, 'draftNo', data.draftNo)
          // 新票设置子票区间
          if (this.isNewDraft) {
            this.$set(this.form, 'subTicketStart', data.subTicketStart)
            this.$set(this.form, 'subTicketEnd', data.subTicketEnd)
          }
        }
      } catch (error) {
        if (Object.prototype.toString.call(error) === '[object Error]') {
          // HTTP 请求失败
          this.$message.error('服务器出错了，请刷新重试')
        } else if (error.data.code === 5008) {
          // 新票定制错误信息
          this.$message.error('暂不支持上传新一代票据')
        } else {
          // HTTP 请求成功，但业务状态码为处理失败的情况
          this.$message.error(error.data.msg || '业务处理失败')
        }
        isError = true
      }
      return isError
    },
    // 上传票据正面成功回调，用于ocr识别后根据结果判断是否删除图片和保持loading状态
    handleUploadSuccess(src) {
      return new Promise((resolve, reject) => {
        const isError = this.getOcrDraftInfo(src)
        isError.then(err => {
          if (err) {
            reject(new Error('ocr 识别失败'))
          } else {
            resolve('ocr 识别成功')
          }
        })
      })
    },
    // 确认
    onConfirm() {
      this.$refs.formInstance.validate(valid => {
        if (!valid) return
        let ticketNumberText = `实际${this.labelText}票号：${this.form.draftNo || ''}`
        let subTicketText
        let tipsSellerText = `请您再次确认实际${this.labelText}票据号码是否正确，您发起变更且资方同意后，平台订单票号将会变更，若因实际${this.labelText}票号填写错误产生的相关不利法律后果由您自行承担。`
        if (this.isNewDraft) {
          subTicketText = `子票区间：${this.form.subTicketStart}-${this.form.subTicketEnd}`
          tipsSellerText = this.tipsSeller
        }
        // 提示文本区分票方和资方
        let tips = this.isSale ? tipsSellerText : this.tipsBuyer
        const h = this.$createElement
        this.$msgbox({
          title: '提示',
          message: h('div', [h('p', { class: 'tips-color' }, tips), h('p', { class: 'text-font' }, ticketNumberText), h('p', { class: 'text-font' }, subTicketText)]),
          showCancelButton: true,
          customClass: 'custom-msg-box-cls',
          cancelButtonText: '取消',
        }).then(() => {
          // 发起提交
          this.applySubmit()
        })
          .catch(err => {
            // eslint-disable-next-line
            console.log(err)
          })
      })
    },
    // 撤销
    onRepeal(flag) {
      this.$confirm('确认撤销变更票号吗', '提示', {
        type: 'warning',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
      }).then(() => {
        this.applySubmit(flag)
      })
        .catch(err => {
          // eslint-disable-next-line
          console.log(err)
        })
    },
    // 发起变更票号提交
    async applySubmit(flag) {
      const { orderNo } = this.order
      let operateType = flag || this.updateTicketStatus
      let params = {
        ...this.form,
        orderNo,
        changeOperateType: this.isReApply ? 0 : operateType,
        id: this.updateTicketStatus !== 0 ? this.updateTicketInfo?.id : null,
        initiator: this.isSale ? 1 : 2
      }
      try {
        await orderApi.updateTicketNumber(params)
        let msg = ''
        if (flag) {
          msg = '撤销成功'
        } else if (this.updateTicketStatus === 0 || this.isReApply) {
          msg = '发起变更票号成功'
        } else if (this.updateTicketStatus === 1) {
          msg = '修改变更票号成功'
        }
        this.$message.success(msg)
        // 查询变更票号状态
        this.$store.dispatch('draft-detail/queryUpdateTicketInfo', { orderNo: this.order.orderNo })
        this.handleClose()
      } catch (err) {
        // eslint-disable-next-line
        console.log(err?.data?.msg)
      }
    },
    // 重新发起申请
    onReApply() {
      this.isReApply = true
    },
    // 查看上一次变更票号记录
    updateDraftNoRecord() {
      this.$refs.ticketNumberRecordRefs.init(this.order.orderNo)
    },
    // 关闭弹窗
    handleClose() {
      this.visible = false
      this.$emit('destroy')
    }
  }
}
</script>
