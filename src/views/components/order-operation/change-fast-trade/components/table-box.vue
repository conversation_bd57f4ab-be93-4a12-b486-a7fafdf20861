<!-- 表格内容 -->
<style lang="scss" scoped>
.table-box {
  padding: 16px;
  background-color: $color-FFFFFF;
}

.el-table {
  ::v-deep {
    thead .el-table__cell {
      height: 54px;
    }

    tbody .el-table__cell {
      padding: 18px 0;
      height: 84px;
    }

    .el-table__cell:not(.el-table-column--selection) .cell {
      padding: 0 52px;
    }

    .el-table__cell.draft-no-column .cell {
      padding-left: 32px;
    }
  }

  .draft-amount {
    font-weight: bold;
  }
}

.table-empty {
  @include flex-cc;

  height: 420px;
  font-size: 16px;
  line-height: 22px;
  flex-direction: column;

  .icon {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 8px;
    height: 104px;
    font-size: 166px;
  }

  .el-button {
    margin-top: 12px;
  }
}

.footer-pagination {
  text-align: center;

  .el-pagination {
    padding: 12px 0 0;
  }
}

.g-tag-feijisu {
  padding: 0 2px;
  height: 16px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  color: $color-FFFFFF;
  background: #BDBDBD;
  line-height: 16px;
}
</style>

<template>
  <div class="table-box">
    <el-table
      v-waiting="['get::loading::/draft/order/listTraderCorpOder']"
      border
      :data="tableData"
      max-height="474"
      @selection-change="handleSelectionChange"
    >
      <div slot="empty" class="table-empty">
        <icon class="icon" type="chengjie-empty" />
        <span>{{ queryError ? '当前输入的筛选条件有误，请检查' : '暂无可转换类型的订单' }}</span>
        <el-button
          v-if="queryError"
          type="primary"
          size="large"
          @click="() => $emit('clear')"
        >
          清空筛选条件
        </el-button>
      </div>
      <el-table-column
        type="selection"
        align="center"
        width="50"
      />
      <el-table-column
        label="承兑人"
        prop="acceptorName"
        min-width="298"
        class-name="has-tags-column acceptor-column"
      >
        <div slot-scope="scope" class="acceptor-column-content">
          <el-tooltip
            :disabled="true"
            content="票在户，免确认，效率高"
            placement="top-start"
          >
            <div div class="order-tag" style="left: -50px;">
              <span v-if="scope.row.fastTrade" class="g-tag-jisu">极速</span>
              <!-- <span v-if="!scope.row.fastTrade" class="g-tag-feijisu">极速</span> -->
            </div>
          </el-tooltip>

          {{ scope.row.acceptorName }}
        </div>
      </el-table-column>
      <el-table-column label="票面金额(万)" min-width="210">
        <template slot-scope="scope">
          <span class="draft-amount">{{ scope.row.draftAmountStr }}</span>
        </template>
      </el-table-column>
      <el-table-column label="到期日" min-width="190">
        <template slot-scope="scope">
          {{ scope.row.maturityDate }}
          <br>剩 {{ scope.row.interestDays }} 天
        </template>
      </el-table-column>
      <el-table-column
        label="票号后六位"
        min-width="160"
        class-name="draft-no-column"
      >
        <template slot-scope="scope">
          {{ scope.row.lastSixDraftNo }}
        </template>
      </el-table-column>
      <el-table-column label="每十万扣息" min-width="204">
        <template slot-scope="scope">
          {{ scope.row.lakhFeeStr }}
          <br>({{ scope.row.annualInterest }}%)
        </template>
      </el-table-column>
    </el-table>
    <div v-if="tableData.length" class="footer-pagination">
      <el-pagination
        :current-page.sync="page.pageNum"
        :page-size="page.pageSize"
        background
        layout="total, prev, pager, next, sizes, jumper"
        :total="totalRecord"
        :page-sizes="[10, 20, 30]"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import {
  handleOrderTypeLabelPosition, // 自动/极速标签位置
  handleOrderTypeLabel//  自动/极速标签显示
} from '@/common/js/util'
export default {
  name: 'table-box',
  props: {
    // 表格数据
    tableData: Array,
    // 默认分页组件参数
    defaultPageOptions: {
      type: Object,
      default: () => ({})
    },
    // 总页数
    totalRecord: {
      type: [String, Number],
      default: 0
    },
    // 搜索参数是否错误
    queryError: Boolean
  },

  data() {
    return {
      // 分页参数
      page: {
        pageNum: 1,
        pageSize: 10
      }
    }
  },

  watch: {
    defaultPageOptions: {
      handler(val) {
        val.pageNum && (this.page.pageNum = val.pageNum)
        val.pageSize && (this.page.pageSize = val.pageSize)
      }
    }
  },

  methods: {
    handleOrderTypeLabelPosition, // 自动/极速标签位置
    handleOrderTypeLabel, //  自动/极速标签显示
    // 多选回调
    handleSelectionChange(val) {
      this.$emit('selection-change', val)
    },

    // 更改每页数量
    handleSizeChange(val) {
      this.page.pageSize = val
      this.page.pageNum = 1
      this.$emit('change-page', this.page)
    },

    // 更改当前页码
    handleCurrentChange(val) {
      this.page.pageNum = val
      this.$emit('change-page', this.page)
    }
  }
}
</script>
