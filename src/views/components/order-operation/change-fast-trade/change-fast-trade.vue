<!-- 极速、非极速互转 -->
<style lang="scss" scoped>
.change-fast-trade {
  display: inline-block;
}
</style>

<template>
  <div v-if="!limitLight" class="change-fast-trade order-operation">
    <slot name="button">
      <transaction-tooltip-button
        v-bind="$attrs"
        :type="$attrs.type || 'primary'"
        :border="!$attrs.type"
        :height="$attrs.height || '40'"
        :width="$attrs.width || '140'"
        :types="[TRANSACTION_TOOLTIP_TYPE.FAST]"
        @click="init"
        v-on="$listeners"
      >
        <slot>
          极速<icon class="icon" type="chengjie-swap" />非极速
        </slot>
      </transaction-tooltip-button>
    </slot>
  </div>
</template>

<script>
import ChangeFastTradeDialog from './change-fast-trade-dialog.vue'
import orderOperationMixin from '../order-operation-mixin'
import { TRANSACTION_TOOLTIP_TYPE } from '@/constants/transaction-tooltip'// 支付账户异常弹窗
import { mapGetters } from 'vuex'

export default {
  name: 'change-fast-trade',
  mixins: [orderOperationMixin(ChangeFastTradeDialog)],
  data() {
    return {
      TRANSACTION_TOOLTIP_TYPE, // 交易限制提示类型
    }
  },
  computed: {
    ...mapGetters('user', {
      limitLight: 'limitLight', // 是否限制极速
    }),

  },
  created() {
    this.$store.dispatch('user/getCorpInfo') // 获取极速限制开关
  }
}
</script>
