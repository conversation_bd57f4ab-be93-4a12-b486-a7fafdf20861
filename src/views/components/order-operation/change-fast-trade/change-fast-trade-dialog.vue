<!-- 极速、非极速互转弹窗 -->
<style lang="scss" scoped>
.dialog-title {
  .icon {
    margin: 0 4px;
  }
}

.el-radio-group {
  display: flex;
  margin-bottom: 12px;
  width: 100%;

  .el-radio-button {
    flex: 1;
  }

  ::v-deep {
    .el-radio-button__inner {
      display: block;
      padding: 12px 20px;
      font-size: 16px;
      user-select: none;
    }

    .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      font-weight: bold;
      color: $--color-primary;
      background: $--color-primary-hover;
    }
  }
}

.tips {
  display: flex;
  margin-bottom: 12px;
  font-size: 16px;
  flex-direction: column;

  .left {
    flex: 1;

    @include flex-vc;
  }

  .right {
    display: flex;
    align-items: center;
    margin: 12px 0 0 27px;
  }

  .elicon {
    margin-right: 8px;
    font-size: 20px;
    color: $--color-primary;
  }

  .margin-left,
  .recharge {
    margin-left: 12px;
  }

  .recharge {
    @include example_underline;
  }
}

.red-high-light {
  font-weight: bold;
  color: $--color-font-main;
}

.filter {
  @include flex-vc;

  border-bottom: 1px solid $color-F0F0F0;
  padding: 12px 16px;
  background: $color-FFFFFF;

  .el-input {
    font-size: 16px;
  }

  .amount-input {
    width: 150px;
  }

  .connect-line {
    margin: 0 6px;
  }

  .other-input {
    margin-left: 10px;
    width: 240px;
  }

  .button-text {
    @include example-underline;

    margin-left: 10px;
  }
}

.footer {
  @include flex-sbc;

  .agreement {
    @include flex-vc;

    .el-checkbox {
      display: inline-flex;
      align-items: center;

      ::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
        color: $color-text-primary;
      }
    }

    .link {
      @include example-underline;
    }
  }
}
</style>

<template>
  <el-dialog
    width="1170px"
    :visible.sync="visible"
    :close-on-click-modal="false"
    title="极速订单"
    class="order-operation-dialog"
    @close="handleClose"
  >
    <div slot="title" class="dialog-title">极速订单<icon class="icon" type="chengjie-swap" />非极速订单</div>
    <el-radio-group v-model="query.fastTrade">
      <el-radio-button :label="DRAFT_FAST_TRADE_CODE.FAST_TRADE">极速订单</el-radio-button>
      <el-radio-button :label="DRAFT_FAST_TRADE_CODE.NOT_FAST_TRADE">非极速订单</el-radio-button>
    </el-radio-group>
    <!-- 提示 -->
    <div class="tips">
      <WarnContent class="tip-box" class-type="bule">
        <div class="left">
          已选择 <span class="red-high-light">{{ selectionData.length }}</span> 笔订单{{ isFastTradeTab ? '由“极速订单”转为“非极速订单”，转换后极速交易开关关闭，保证金开关保持打开。' : '由“非极速订单”转为“极速订单”，转换后极速交易开关开启，保证金开关开启。' }}
        </div>
        <div v-if="!isFastTradeTab" class="right">
          <span>补缴保证金：<span class="red-high-light">{{ needMarginAmount }}</span> {{ sdmUnit }}</span>
          <span class="margin-left">可用余额：<span class="red-high-light">{{ (sdmInfo || {}).balanceAmt || '0.00' }}</span> {{ sdmUnit }}</span>
          <span class="recharge" @click="recharge">充值</span>
        </div>
      </WarnContent>
    </div>
    <!-- 筛选 -->

    <div class="filter">
      <el-input
        v-model="query.draftAmountMin"
        placeholder="最小金额"
        type="number"
        :number-format="numberFormat"
        class="amount-input"
        @input="debounceGetList"
      >
        <template slot="append">万</template>
      </el-input>
      <span class="connect-line">-</span>
      <el-input
        v-model="query.draftAmountMax"
        placeholder="最大金额"
        type="number"
        :number-format="numberFormat"
        class="amount-input"
        @input="debounceGetList"
      >
        <template slot="append">万</template>
      </el-input>
      <el-input
        v-model="query.acceptorName"
        placeholder="承兑人关键字"
        class="other-input"
        @input="debounceGetList"
      />
      <el-input
        v-model="query.lastSixDraftNo"
        type="number"
        placeholder="票号后六位"
        class="other-input"
        :number-format="{
          decimal: false,
          maxLength: 6,
          negative: false
        }"
        @input="debounceGetList"
      />
      <el-button
        type="text"
        class="button-text"
        size="medium"
        @click="handleClear"
      >
        清空
      </el-button>
    </div>

    <TableBox
      :table-data="tableData"
      :total-record="totalRecord"
      :query-error="queryError"
      :default-page-options="{
        pageNum: query.pageNum,
        pageSize: query.pageSize,
      }"
      @change-page="handleChangePage"
      @selection-change="handleSelectionChange"
      @clear="handleClear"
    />
    <div slot="footer" class="footer">
      <div class="agreement">
        <template v-if="!isFastTradeTab">
          <el-checkbox v-model="isAgree">我已阅读并同意</el-checkbox>
          <a
            :href="fastTradeUrl"
            target="_blank"
            class="link"
            rel="noopener noreferrer"
          >《极速出票规则》</a>
          <a
            :href="OSS_FILES_URL.LIGHT_SPEED_ORDER_AUTHORIZATION"
            target="_blank"
            class="link"
            rel="noopener noreferrer"
          >《极速出票授权委托书》</a>
        </template>
      </div>
      <div class="right">
        <el-button size="large" border @click="handleClose">取消</el-button>
        <transaction-tooltip-button
          v-waiting="'post::/draft/order/fastTradeOrderTransfer'"
          type="primary"
          :types="[TRANSACTION_TOOLTIP_TYPE.CREDIT]"
          :disabled="confirmBtnDisabled"
          @click="handleConfirm"
        >
          转为{{ isFastTradeTab ? '非' : '' }}极速订单
        </transaction-tooltip-button>
      </div>
    </div>
    <!-- 充值 -->
    <Recharge ref="recharge" />
    <!-- 批量操作结果 -->
    <ResultDialog ref="resultDialogRef" handle-str="转换" @close="handleCloseResultDialog" />
    <!-- 支付账户异常弹窗 -->
    <PayAccountError ref="payAccountErrorRef" />
  </el-dialog>
</template>

<script>
import TableBox from './components/table-box.vue' // 表格内容
import Recharge from '@/views/components/user-center/recharge/recharge.vue' // 充值
import ResultDialog from '@/views/components/common/result-dialog/result-dialog.vue' // 批量操作结果组件
import { marginAmountMath } from '@/common/js/draft-math' // 保证金计算
import { mapGetters, mapActions } from 'vuex'
import orderApi from '@/apis/order' // 接口
import { FASTTRADE_URL, OSS_FILES_URL } from '@/constants/oss-files-url'
import { DRAFT_FAST_TRADE_CODE, ISSUE_DRAFT_ERROR_CODE } from '@/constants/draft' // 光/非关速单code
import PayAccountError from '@/views/components/pay-account-error/pay-account-error.vue'// 支付账户异常弹窗
import WarnContent from '@/views/components/common/warn-content.vue' // 警告文本组件
import {
  debounce, // 防抖
  isNull, // 判断是不是null
} from '@/common/js/util'
import {
  parseNum, // 金额添加逗号
  yuan2wan, // 元转万
  wan2yuan, // 万转元
  keep2Decimals, // 保留两位小数
} from '@/common/js/number'
import { TRANSACTION_TOOLTIP_TYPE } from '@/constants/transaction-tooltip'
// 默认参数
const defaultQuery = () => ({
  pageNum: 1,
  pageSize: 10,
  fastTrade: DRAFT_FAST_TRADE_CODE.FAST_TRADE, // 0-非光订单  1-极速订单
  draftAmountMin: '', // 最小金额
  draftAmountMax: '', // 最大金额
  acceptorName: '', // 承兑人
  lastSixDraftNo: '', // 票号后六位
})// 支付账户异常弹窗

export default {
  name: 'change-fast-trade-dialog',
  components: {
    TableBox,
    WarnContent,
    Recharge,
    ResultDialog,
    PayAccountError,
  },
  data() {
    return {
      OSS_FILES_URL, // 极速订单授权委托书
      TRANSACTION_TOOLTIP_TYPE, // 交易限制提示类型
      DRAFT_FAST_TRADE_CODE,
      visible: false, // 弹窗是否打开
      fastTradeUrl: FASTTRADE_URL,
      numberFormat: {
        negative: false,
        maxDecimalLength: 6,
        maxLength: 12,
        leadingZero: false
      },
      // 传参
      query: defaultQuery(),
      isAgree: true, // 是否同意协议
      tableData: [], // 表格数据
      totalRecord: 0, // 数据
      needMarginAmount: 0, // 补缴保证金
      selectionData: [], // 多选的数据
      queryError: false, // 搜索参数是否错误
    }
  },

  computed: {
    ...mapGetters('user', {
      sdmInfo: 'sdmInfo', // 米账号信息
    }),
    // 是不是极速订单tab
    isFastTradeTab() {
      return this.query.fastTrade === DRAFT_FAST_TRADE_CODE.FAST_TRADE
    },
    // 确认按钮是否置灰
    confirmBtnDisabled() {
      // 判断是否有多选数据
      if (!this.selectionData.length) {
        return true
      }
      // 极速订单直接返回了
      if (this.isFastTradeTab) {
        return false
      }
      // 非极速订单判断是否勾了协议
      if (!this.isAgree) {
        return true
      }
      // 非极速订单判断待缴保证金
      return this.needMarginAmount > ((this.sdmInfo || {}).balanceAmt || 0)
    },
    // 需要监听的参数
    queryWatch() {
      const { pageNum, pageSize, fastTrade } = this.query
      return { pageNum, pageSize, fastTrade }
    }
  },

  watch: {
    // 监听传参 清空时会走这个
    queryWatch: {
      handler() {
        this.getFastTradeOrderList()
      },
      deep: true
    }
  },

  created() {
    this.debounceGetList = debounce(this.getFastTradeOrderList)
  },

  methods: {
    ...mapActions('user', {
      getSdmInfo: 'getSdmInfo', // 获取米账号信息
    }),

    // 初始化
    init() {
      this.visible = true
      this.getFastTradeOrderList()
      // TODO: 获取米余额，为了实时性，每次打开都请求
      this.getSdmInfo()
    },

    // 获取待接单数据
    async getFastTradeOrderList() {
      const query = { ...this.query }
      // 判断最小金额和最大金额是否输入错误
      this.queryError = !!(!isNull(query.draftAmountMin) && !isNull(query.draftAmountMax) && +query.draftAmountMin > +query.draftAmountMax)
      if (this.queryError) {
        this.tableData = []
        this.totalRecord = 0
        return
      }
      // 金额需要转换为元
      query.draftAmountMin && (query.draftAmountMin = wan2yuan(query.draftAmountMin))
      query.draftAmountMax && (query.draftAmountMax = wan2yuan(query.draftAmountMax))

      const data = await orderApi.getFastTradeOrderList(query)
      if (data) {
        data.rowList.forEach(item => {
          item.draftAmountStr = parseNum(yuan2wan(item.draftAmount))
          item.lakhFeeStr = parseNum(item.lakhFee)
        })
        this.tableData = data.rowList
        this.totalRecord = data.totalRecord
      }
    },

    // 点击转换
    async handleConfirm() {
      const body = {
        fastTrade: this.query.fastTrade ? 0 : 1, // 0 转为非极速 1 转为极速
        orderNoList: this.selectionData.map(item => item.orderNo)
      }
      try {
        const data = await orderApi.fastTradeOrderTransfer(body)
        // 非光=>极速 成功需要更新米信息
        if (!this.query.fastTrade) {
          this.getSdmInfo()
        }
        // 批量操作结果提示
        this.$refs.resultDialogRef.init(data)
      } catch (error) {
        // 对接口返回的错误code处理
        this.handleErrorCode({ errData: error })
      }
    },

    // 接口请求返回错误处理
    handleErrorCode(obj) {
      const { errData = {} } = obj // api 指定的接口 errData后端抛出异常的数据
      const { code, msg } = errData.data || {}
      // 1018 支付账户异常
      if (code === ISSUE_DRAFT_ERROR_CODE.PAY_CHANNEL_FAIL) {
        this.$refs.payAccountErrorRef.init(msg)
        return
      }

      this.$message.error(msg || '系统繁忙，请稍后再试')
    },

    // 多选数据回调
    handleSelectionChange(list) {
      this.selectionData = list
      if (!this.isFastTradeTab) {
        let needMarginAmount = 0
        list.forEach(item => {
          if (!item.margin) {
            needMarginAmount = needMarginAmount + marginAmountMath(item.draftAmount)
          }
        })
        this.needMarginAmount = keep2Decimals(needMarginAmount)
      }
    },

    // 分页参数更改回调
    handleChangePage(options) {
      Object.assign(this.query, options)
    },

    // 清空
    handleClear() {
      this.query = { ...defaultQuery(), fastTrade: this.query.fastTrade }
    },

    // 关闭
    handleClose() {
      this.visible = false
      this.handleClear()
      this.$emit('destroy')
    },

    // 充值
    recharge() {
      this.$refs.recharge && this.$refs.recharge.init()
    },

    // 操作结果弹窗关闭回调
    handleCloseResultDialog() {
      this.getFastTradeOrderList()
      this.$emit('success')
    }
  }
}
</script>
