<!-- 获取凭证(申请事后凭证) -->
<style lang="scss" scoped>
.section-voucher {
  display: flex;

  .sub-voucher {
    display: flex;
    flex-direction: column;
    margin-left: 4px;
  }

  ::v-deep {
    .el-link--inner {
      color: $font-color;
    }

    .el-link.el-link--primary::after {
      border-color: $font-color;
    }
  }
}
</style>

<template>
  <div>
    <slot name="button">
      <section class="section-voucher">
        <el-button
          v-bind="$attrs"
          :type="$attrs.type || 'primary'"
          :width="$attrs.width || '104'"
          :height="$attrs.height || '40'"
          :size="$attrs.size || 'large'"
          v-on="$listeners"
          @click="init"
        >
          <slot>{{ btnText }}</slot>
        </el-button>
        <!-- 详情页面展示提示/示例 -->
        <div v-if="showTooltip" class="sub-voucher">
          <el-tooltip
            placement="top-start"
            popper-class="issue-draft-tooltip"
          >
            <template slot="content">
              若您需要该笔订单的交易凭证则需要支付5{{ sdmName }}服务费，支付后您可以在支付电子账户内查看并下载该笔订单的交易凭证。
            </template>
            <icon class="icon icon-question" size="20" type="chengjie-wenti" />
          </el-tooltip>
          <el-image :preview-src-list="[voucherImage]" img-text="示例" />
        </div>
      </section>
    </slot>
  </div>
</template>

<script>
import ApplyDealVoucherDialog from './apply-deal-voucher-dialog.vue'
import orderOperationMixin from '../order-operation-mixin'

export default {
  name: 'service-intervention',
  mixins: [orderOperationMixin(ApplyDealVoucherDialog)],
  props: {
    // 详情页面显示提示
    showTooltip: {
      type: Boolean,
      default: false
    },
    btnText: {
      type: String,
      default: '获取凭证'
    },
    // 凭证后补用户1:卖方 2:买方
    userType: {
      type: [Number, String],
      default: 1
    }
  },
  data() {
    return {
      voucherImage: 'https://oss.chengjie.red/web/imgs/draft/voucher.png',
    }
  }
}
</script>
