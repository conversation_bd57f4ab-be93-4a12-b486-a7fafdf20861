<!-- ---------- 获取交易凭证 ----------- -->
<style lang="scss" scoped>
.sec-wrapper {
  padding: 16px;
  background: $color-FFFFFF;

  .p-text {
    font-size: 16px;
    color: $color-text-primary;
    line-height: 26px;
  }

  .red-value {
    color: $color-warning;
  }

  .block-div {
    margin-top: 12px;

    .block-label {
      display: block;
      margin-bottom: 5px;
      font-size: 14px;
      color: $color-text-secondary;
    }

    .block-value {
      font-size: 16px;
      font-weight: 600;
      color: $color-text-primary;
    }

    .pay-button {
      margin-left: 16px;
      text-decoration: underline;
      color: $font-color;
      cursor: pointer;
    }
  }
}

.form-item-title {
  margin-bottom: 5px;
  font-size: 14px;
  color: $color-text-secondary;
  line-height: 22px;
}

.el-form-item {
  margin-bottom: 12px;
}

.get-code-box {
  .get-code-btn {
    float: right;
  }
}
</style>

<template>
  <el-dialog
    width="560px"
    :visible.sync="visible"
    append-to-body
    :close-on-click-modal="false"
    :title="hasAllOrder ? '批量获取交易凭证' : '获取交易凭证'"
    :before-close="handleClose"
  >
    <section class="sec-wrapper">
      <p class="p-text">若您需要此单的交易凭证则需支付 <span class="red-value">{{ paymentMoney }}{{ sdmName }}</span>，支付完成后可前往您的智付账户下载此单的交易凭证</p>
      <div v-if="!hasAllOrder" class="block-div">
        <span class="block-label">承兑人</span>
        <div class="block-value">{{ order.acceptorName }}</div>
      </div>
      <div v-if="!hasAllOrder" class="block-div">
        <span class="block-label">票号后六位</span>
        <div class="block-value">{{ order.lastSixDraftNo }}</div>
      </div>
      <div class="block-div">
        <span class="block-label">可用{{ sdmName }}</span>
        <div class="block-value">
          {{ sdmInfo ? sdmInfo.balanceAmt : 0.00 }} {{ sdmUnit }}
          <span class="pay-button" @click="onRecharge">充值</span>
        </div>
      </div>
      <div class="block-div">
        <el-form ref="formData" :model="formData" :rules="rules">
          <div class="form-item-title">手机号码</div>
          <el-form-item prop="oldMobile">
            <el-input
              :value="mobile"
              placeholder="请输入手机号码"
              :disabled="true"
            />
          </el-form-item>
          <div class="form-item-title">验证码</div>
          <el-form-item class="get-code-box" prop="code" :error="errorCodeMsg">
            <el-input
              v-model="formData.code"
              placeholder="请输入验证码"
              :width="320"
              :height="40"
              type="number"
              :number-format="{ maxLength: 6, decimal: false, negative: false }"
              @change="codeChange"
            />
            <el-button
              v-waiting="'post::loading::/api/platform/user/updateCorpMemberMobile/sendPhoneVerifyCode'"
              class="get-code-btn"
              type="primary"
              width="132"
              height="40"
              :disabled="!canGetCode"
              @click="getCode"
            >
              {{ canGetCode ? "获取验证码" : `还剩 ${leftSeconds}s` }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </section>
    <!-- todo -->
    <div slot="footer">
      <el-button size="large" @click="handleClose">
        取消
      </el-button>
      <el-button
        v-waiting="orderApi.postDealVoucherSubmit"
        type="primary"
        size="large"
        @click="onComfirm"
      >
        确认支付
      </el-button>
    </div>
    <!-- 米充值 -->
    <Recharge ref="recharge" />
  </el-dialog>
</template>

<script>
import Recharge from '@/views/components/user-center/recharge/recharge.vue' // 米充值
import orderApi from '@/apis/order'
import { mapGetters, mapActions } from 'vuex'
import Storage from '@/common/js/storage' // 本地缓存对象
import { SEND_CODE_TIME } from '@/constant-storage'
import {
  NEED_VOUCHER,
} from '@/constant'
const SEND_CODE_TIME_KEY = 'fixMobile' // 发送验证码倒计时key
export default {
  name: 'apply-deal-voucher-dialog',
  components: { Recharge },
  data() {
    return {
      formData: {},
      errorCodeMsg: null, // 验证码的错误提示
      leftSeconds: 0, // 剩余秒数
      orderApi,
      paymentMoney: 5, // 需支付的云豆
      visible: false, // 弹窗是否打开
      userType: 1, // 1卖方 2买方
      orderdData: null, // 批量获取凭证处理后的订单数据存储
      rules: { // 表单校验规则
        code: [
          {
            required: true,
            pattern: /[0-9]{6}$/,
            message: '请输入6位验证码',
            trigger: ['blur']
          },
        ],
      },
    }
  },
  computed: {
    // 用户登录手机号码
    mobile() {
      return this.$store.state?.user?.userInfo?.mobile
    },
    ...mapGetters('user', {
      // 米账号信息
      sdmInfo: 'sdmInfo'
    }),
    // 是否可以获取验证码
    canGetCode() {
      return !(this.leftSeconds > 0)
    },
    // 是否批量
    hasAllOrder() {
      return this.order instanceof Array
    }
  },
  watch: {
    visible: {
      handler(val) {
        // 注册弹窗打开时，若上一次发送验证码的倒计时未结束，则继续开始倒计时
        if (val && Storage.get(SEND_CODE_TIME) && Storage.get(SEND_CODE_TIME)[SEND_CODE_TIME_KEY]) {
          this.startCountDown()
        }
      },
    }
  },
  methods: {
    ...mapActions('user', {
      getSdmInfo: 'getSdmInfo'
    }),
    // 打开弹窗-初始化数据
    init({ userType }) {
      this.getSdmInfo()
      this.userType = userType
      this.visible = true
      // 批量获取凭证设置需支付的云豆
      if (this.order instanceof Array) {
        if (userType === 1) { // 票方
          this.orderdData = this.order.filter(e => e.needVoucher === NEED_VOUCHER.NO.id || e.needVoucher === NEED_VOUCHER.BUYER.id)
        } else { // 资方
          this.orderdData = this.order.filter(e => e.needVoucher === NEED_VOUCHER.NO.id || e.needVoucher === NEED_VOUCHER.SELLER.id)
        }
        // 过滤处理已经获取过凭证的订单
        this.paymentMoney = this.orderdData.length * this.paymentMoney
      }
    },
    // 确认支付提交
    async onComfirm() {
      this.errorCodeMsg = null
      // await this.$refs.formData.validate()
      let params = {
        // 凭证后补用户 1：卖方 2：买方
        whichSide: this.userType,
        // orderNo: this.order.orderNo, // 订单号
        verificationCode: this.formData.code
      }
      if (this.hasAllOrder) { // 是否批量 过滤出已经获取过凭证的订单
        params.orderNos = this.orderdData.map(e => e.orderNo)
      } else {
        params.orderNo = this.order.orderNo // 订单号
      }
      try {
        this.hasAllOrder ? await orderApi.postBatchDealVoucherSubmit(params) : await orderApi.postDealVoucherSubmit(params)
        this.$message.success('提交成功')
        this.$emit('success')
        this.handleClose()
      } catch (error) {
        // 短信错误码
        const codeError = 1008
        const codeInvalid = 1025
        if (error.data.code === codeError || error.data.code === codeInvalid) {
          this.errorCodeMsg = error.data.msg
        }
      }
    },
    // 充值事件
    onRecharge() {
      this.$refs.recharge.init()
    },
    // 关闭弹窗
    handleClose() {
      this.visible = false
      this.$emit('destroy')
    },
    codeChange() {
      this.errorCodeMsg = null
    },
    // 获取验证码
    async getCode() {
      this.errorMobileMsg = null
      try {
        await orderApi.getVerificationCode({ mobile: this.mobile })
        this.$message({
          message: '短信验证码已发送，请注意查收',
          type: 'success'
        })
        this.startCountDown()
      } catch (err) {
        // eslint-disable-next-line no-console
        console.log(err)
      }
    },

    // 开始倒计时
    startCountDown() {
      let nowTime = new Date().getTime() // 当前时间
      let sendCodeTime = Storage.get(SEND_CODE_TIME) || '' // 所有发送验证码时间的本地缓存对象
      let lastTime = sendCodeTime && sendCodeTime[SEND_CODE_TIME_KEY] ? sendCodeTime[SEND_CODE_TIME_KEY] : null // 上一次发送验证码时间
      let durationTime = 60 // 倒计时时间

      if (sendCodeTime) {
        // 上一次发送验证码的倒计时未结束
        if (lastTime) {
          durationTime = durationTime - Math.round(((nowTime - lastTime) / 1000)) // 计算还剩多少秒倒计时
        } else {
          sendCodeTime[SEND_CODE_TIME_KEY] = nowTime
          Storage.set(SEND_CODE_TIME, sendCodeTime) // 本次的发送验证码倒计时添加入缓存对象
        }
      } else {
        sendCodeTime = {
          [SEND_CODE_TIME_KEY]: nowTime
        }
        // 保存这次发送验证码的时间
        Storage.set(SEND_CODE_TIME, sendCodeTime)
      }

      this.leftSeconds = durationTime
      const countDown = () => {
        this.clock = setTimeout(() => {
          this.leftSeconds -= 1
          if (this.leftSeconds <= 0) {
            // this.canGetCode = true

            sendCodeTime[SEND_CODE_TIME_KEY] = null // 倒计时结束，清掉获取验证码倒计时缓存
            Storage.set(SEND_CODE_TIME, sendCodeTime)
          } else {
            countDown()
          }
        }, 1000)
      }
      countDown()
    },

    // 清除定时器
    clearTime() {
      clearTimeout(this.clock) // 清除
      this.clock = null
      // this.canGetCode = true
    },

  }
}
</script>
