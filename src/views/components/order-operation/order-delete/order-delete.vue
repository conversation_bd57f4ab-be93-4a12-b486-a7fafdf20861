<!-- 删除订单 -->
<style lang="scss" scoped>
.order-delete {
  display: inline;
}
</style>

<template>
  <div class="order-delete order-operation">
    <slot name="button">
      <el-button
        v-waiting="[`post::/draft/order/executeOrderTradeStep?orderNo=${firstOrderNo}&type=delete`,
                    `post::/draft/order/singleExecuteOrderTradeStep?orderNo=${firstOrderNo}&type=delete`]"
        v-bind="$attrs"
        :type="$attrs.type || 'primary'"
        :border="!$attrs.type"
        @click="init"
        v-on="$listeners"
      >
        <slot>删除</slot>
      </el-button>
    </slot>

    <!-- 批量操作loading -->
    <LoadingDialog :visible="batchLoading" title="批量删除中" content="正在批量删除中，请耐心等待..." />
  </div>
</template>

<script>
/* eslint-disable no-magic-numbers */
import LoadingDialog from '@/views/components/common/loading-dialog/loading-dialog.vue' // 加载中组件

import orderApi from '@/apis/order'
import { EXECUTE_TRANSACTION_PROCESS } from '@/constant'
export default {
  name: 'order-delete',
  components: { LoadingDialog },
  props: {
    order: {
      type: [Object, Array],
      required: true
    },
  },
  data() {
    return {
      EXECUTE_TRANSACTION_PROCESS, // 所有操作场景
      batchLoading: false
    }
  },

  computed: {
    // 是否批量
    hasAllOrder() {
      return this.order instanceof Array
    },

    // 订单号，数组时取第一条数据，用于loading时，不要所有按钮都显示loading状态
    firstOrderNo() {
      return this.hasAllOrder && this.order.length ? this.order[0].orderNo : this.order.orderNo
    }
  },

  methods: {
    init() {
      if (this.order && Array.isArray(this.order) && this.order.length > 99) {
        this.$message.warning(`每次批量操作最多可选择 ${99} 笔订单`)
        return
      }

      const h = this.$createElement
      let message
      if (this.hasAllOrder) {
        message = h('p', null, '请确认是否批量删除订单。')
      } else {
        message = h('p', null, [
          h('p', null, '请确认是否删除。'),
          h('p', null, '删除后该订单信息将从列表移去，不可恢复。')
        ])
      }
      this.$msgbox({
        title: '提示',
        dangerouslyUseHTMLString: true,
        message,
        confirmButtonText: '确认',
        showCancelButton: true,
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        // 确认
        this.postDelete(this.order)
      })
    },

    // 删除请求
    async postDelete(order) {
      try {
        const orderNoList = this.hasAllOrder ? order.map(i => i.orderNo) : [order.orderNo]
        const param = {
          orderNoList, // 订单编号
          type: 'delete', // 用于点击约束误触发
          orderStepsEnumCode: EXECUTE_TRANSACTION_PROCESS.DELETE_ORDER // 执行订单步骤枚举code值,0审核,1卖家下架待审核订单,2卖家重新发布审核下架订单,3卖方手动下架待接单订单,4重新上架审核后的订单（待接单中的）,5买家接单,6卖家确认订单,7卖家取消待确认订单,8买家取消待确认订单,9买家催单，催卖家确认,10买家付款,11买家取消待付款订单,12卖家取消待付款订单,13卖家催单，催买家付款,14卖家背书,15卖家取消待背书订单,16买家取消超时的待背书订单,17买家催单，催卖家背书,18卖家同意取消待背书订单,19卖家不同意待背书的订单,20买家签收,21卖家取消待签收订单,22买家取消待签收订单,23卖家催单，催买家签收,24卖家同意取消待签收订单,25卖家不同意取消待签收订单,26删除
        }
        if (this.hasAllOrder) {
          this.batchLoading = true
          const data = await orderApi.postExecuteOrderTradeStep(param)
          this.batchLoading = false
          if (!data.failNum) { // 失败数为0,即全部成功
            this.$message.success('删除成功')
          }
        } else {
          await orderApi.postSingleExecuteOrderTradeStep(param)
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
        }
        this.$emit('success')
      } catch (error) {
        this.batchLoading = false
      }
    }
  }
}
</script>
