<!-- 继续等待 -->
<style lang="scss" scoped>
.off-shelf {
  display: inline;
}
</style>

<template>
  <div class="off-shelf order-operation">
    <slot name="button">
      <el-button
        v-bind="$attrs"
        :type="$attrs.type || 'primary'"
        :width="$attrs.width || '104'"
        :height="$attrs.height || '40'"
        :border="!$attrs.type"
        :disabled="$attrs.disabled"
        @click="init"
        v-on="$listeners"
      >
        <slot>继续等待</slot>
      </el-button>
    </slot>
  </div>
</template>

<script>
export default {
  name: 'off-shelf',
  methods: {
    init() {
      this.$emit('success')
      this.$message({
        type: 'success',
        message: '您已继续等待，请耐心等待'
      })
    }
  }
}
</script>
