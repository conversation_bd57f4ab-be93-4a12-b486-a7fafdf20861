<!-- 不同被背书账号显示弹窗 -->
<style lang="scss" scoped>
.diff-endorsed-dialog ::v-deep {
  .el-dialog__body {
    overflow: hidden;
  }
}

.title {
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
}

.red-high-light {
  font-weight: 600;
  color: $--color-font-main;
}

.list {
  overflow-y: auto;
  margin-bottom: 16px;
  max-height: 588px;
}

.item {
  margin-bottom: 12px;
  padding: 16px;
  background: $color-FFFFFF;

  &:last-child {
    margin-bottom: 0;
  }

  .el-table {
    margin-top: 8px;

    ::v-deep {
      .el-table__cell {
        padding: 8px 0;

        .cell {
          padding: 0 12px;
          font-size: 14px;
          line-height: 20px;
        }
      }
    }
  }
}

.item-title {
  @include flex-sbc;

  .g-title-small {
    flex: 1;
    margin-right: 12px;
  }
}
</style>

<template>
  <el-dialog
    width="964px"
    :visible.sync="visible"
    title="批量背书"
    class="diff-endorsed-dialog"
  >
    <div class="title">该批 <span class="red-high-light">{{ orderListLength }}</span> 笔连号票订单已按<span class="red-high-light">【被背书账户】</span>区分展示，请按各个背书账户来完成订单的批量背书操作。</div>
    <div class="list">
      <div v-for="(value, key) in listMap" :key="key" class="item">
        <div class="item-title">
          <div class="g-title-small">被背书账户：{{ value.corpName }}</div>
          <el-button
            type="primary"
            width="72px"
            height="30px"
            @click="handleToEndorse(value.children)"
          >
            前往背书
          </el-button>
        </div>
        <el-table :data="value.children" border>
          <el-table-column label="序号" width="60">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="draftNo" label="票号" width="290" />
          <el-table-column prop="acceptorName" label="承兑人" width="290" />
          <el-table-column
            prop="draftAmountStr"
            label="金额(万)"
            align="right"
            width="128"
          />
          <el-table-column prop="maturityDateStr" label="到期日" align="right" />
        </el-table>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import {
  parseNum, // 金额加逗号
  yuan2wan, // 元转万
} from '@/common/js/number'
// 时间处理
import {
  formatTime, // 格式化时间
} from '@/common/js/date'
export default {
  name: 'diff-endorsed-dialog',
  data() {
    return {
      visible: false,
      orderListLength: 0, // 所有订单条数
      listMap: {}, // 按照需要的格式处理后的数据，对象格式，方便操作， key 为待背书账号
    }
  },
  methods: {
    init(list) {
      this.orderListLength = list.length
      this.visible = true
      this.listMap = {}
      list.forEach(item => {
        if (item.endorseAccount) {
          item.draftAmountStr = parseNum(yuan2wan(item.draftAmount))
          item.maturityDateStr = formatTime(item.maturityDate, 'YYYY-MM-DD')
          if (this.listMap[item.endorseAccount.bankAccount]) {
            this.listMap[item.endorseAccount.bankAccount].children.push(item)
          } else {
            this.listMap[item.endorseAccount.bankAccount] = {
              corpName: item.corpName,
              children: [item]
            }
          }
        }
      })
    },

    // 前往背书
    handleToEndorse(list) {
      this.$emit('to-endorse', list.length === 1 ? list[0] : list)
      this.visible = false
    }
  }
}
</script>
