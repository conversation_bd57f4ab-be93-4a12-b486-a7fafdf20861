<!-- 已背书 -->
<style lang="scss" scoped>
.main {
  margin-top: 12px;
  padding: 16px 16px 4px;
  background: $color-FFFFFF;
}

.tips {
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 600;
  line-height: 22px;
}

.red-high-light {
  font-weight: 600;
  color: $--color-font-main;
}

// 标题
.title-left-border {
  position: relative;
  padding-left: 12px;
  height: 22px;
  font-size: 16px;
  font-weight: 600;
  line-height: 22px;

  &::before {
    position: absolute;
    top: 2px;
    left: 0;
    width: 4px;
    height: 16px;
    background: $--color-primary;
    content: "";
  }
}

// 灰色标题
.gray {
  margin-bottom: 2px;
  color: $color-text-secondary;
}

.item-detail {
  margin-bottom: 8px;
  font-size: 16px;
  color: $color-text-primary;
  line-height: 24px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.body {
  display: flex;

  ::v-deep {
    .copy-icon {
      margin-left: 7px;
    }
  }
}

.left {
  flex: 1;
  border-right: 1px solid $color-text-light;
  padding-right: 50px;
}

.right {
  padding-left: 12px;
  flex: 1;
}

.item-box {
  display: flex;
  justify-content: space-between;
}

.font-bold {
  font-weight: 600;
}

.dialog-thin-text {
  font-size: 16px;
  font-weight: 300;
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  border-top: 1px solid $color-F0F0F0;
  background: $color-F2F2F2;

  .footer-left {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
  }

  .footer-right {
    .el-button {
      font-size: 16px;
    }
  }
}
</style>

<style lang="scss">
.warning-alert {
  width: 550px;
}
</style>

<template>
  <div>
    <el-dialog
      width="964px"
      :visible.sync="visible"
      title="背书提示"
      class="'endorsed-dialog order-operation-dialog"
      :before-close="handleClose"
    >
      <WarnContent class="warn-content">
        请您确认背出票票号与平台显示票号一致，若不一致，请勿背书，否则背书后相关法律风险将由您自行承担。
      </WarnContent>
      <div class="main">
        <div class="header">
          <div class="title-left-border">被背书账户</div>
          <el-button
            v-copy="{ value: copyCurrentItem(form), onSuccess, onError }"
            type="primary"
            border
            width="72"
            height="30"
          >
            全部复制
          </el-button>
        </div>
        <div class="body">
          <div class="left">
            <template v-if="hasAllOrder">
              <div class="gray">银行账户</div>
              <div class="item-detail">{{ form.bankAccount }}<Copy :content="form.bankAccount" /></div>
              <div class="gray">银行行号</div>
              <div class="item-detail">{{ form.bankCode }}<Copy :content="form.bankCode" /></div>
            </template>
            <template v-else>
              <div class="gray">票号</div>
              <div class="item-detail">{{ form.draftNo }}<Copy :content="form.draftNo" /></div>
              <div class="gray">承兑人</div>
              <div class="item-detail font-bold">{{ form.acceptorName }}<Copy :content="form.acceptorName" /></div>
              <div class="gray">票面金额 / 到账金额</div>
              <div class="item-detail font-bold ">{{ yuan2wan(form.draftAmount) }} 万 /{{ yuan2wan(form.draftActualAmount) }}万<Copy :content="yuan2wan(form.draftAmount)" /></div>
              <div class="item-box">
                <div>
                  <div class="gray">到期日</div>
                  <div class="item-detail">{{ form.maturityDate }}<Copy :content="form.maturityDate" /></div>
                </div>
                <div>
                  <div class="gray">每十万扣款 / 年息</div>
                  <div class="item-detail font-bold">{{ form.lakhFee }} 元 / {{ form.annualInterest }}%</div>
                </div>
              </div>
            </template>
          </div>
          <div class="right">
            <div class="gray">企业名称</div>
            <div class="item-detail font-bold">{{ form.buyCorpName }}<Copy :content="form.buyCorpName" /></div>
            <template v-if="!hasAllOrder">
              <div class="gray">银行账户</div>
              <div class="item-detail">{{ form.bankAccount }}<Copy :content="form.bankAccount" /></div>
            </template>
            <div class="gray">银行名称</div>
            <div class="item-detail">{{ form.buyCorpBankName }}<Copy :content="form.buyCorpBankName" /></div>
            <template v-if="!hasAllOrder">
              <div class="gray">银行行号</div>
              <div class="item-detail">{{ form.bankCode }}<Copy :content="form.bankCode" /></div>
            </template>
          </div>
        </div>
      </div>

      <div class="footer">
        <div class="footer-left">
          <div v-if="hasAllOrder">您正在对 <span class="red-high-light">{{ order.length }}</span> 笔订单进行批量背书操作，</div>
          请确认是从<span class="red-high-light">{{ `【${form.sellCorpName}】` }}</span>账户背出
        </div>
        <div class="footer-right">
          <el-button
            v-waiting="[`post::/draft/order/executeOrderTradeStep`,
                        `post::/draft/order/singleExecuteOrderTradeStep`]"
            type="primary"
            @click="confirm"
          >
            我已背书完成
          </el-button>
        </div>
      </div>
      <LoadingDialog :visible="loading" title="批量背书中" content="正在批量背书中，请耐心等待..." />
      <ResultDialog ref="resultDialogRef" handle-str="背书" @close="closeResultDialog" />
    </el-dialog>
    <!-- 连号票批量背书显示不同列表 -->
    <DiffEndorsedDialog ref="diffEndorsedDialogRef" @to-endorse="handleToEndorse" />
  </div>
</template>

<script>
import { EXECUTE_TRANSACTION_PROCESS, PAYMENT_CHANNEL } from '@/constant'
import WarnContent from '../../common/warn-content.vue'
import Copy from '@/views/components/common/copy/copy.vue'
import { yuan2wan } from '@/common/js/number'
import orderApi from '@/apis/order'
import DiffEndorsedDialog from './diff-endorse-dialog.vue' // 连号票批量背书显示不同列表
import LoadingDialog from '@/views/components/common/loading-dialog/loading-dialog.vue' // 加载中组件
import ResultDialog from '@/views/components/common/result-dialog/result-dialog.vue' // 批量操作结果组件
import listenerEventMixins from '../listener-event.mixin'
import { showConfirmationDialog } from '../draftNo-Check'// 智付E+渠道订单确认弹窗
import { mapGetters } from 'vuex'

export default {
  name: 'endorsed-dialog',
  components: {
    WarnContent,
    Copy,
    DiffEndorsedDialog,
    LoadingDialog,
    ResultDialog
  },
  mixins: [listenerEventMixins],

  data() {
    return {
      form: {
        buyCorpName: '', // 背书企业名称
        buyCorpBankName: '', // 背书银行名称
        bankAccount: '', // 背书银行账户
        bankCode: '', // 背书银行行号
        draftNo: '', // 票号
        acceptorName: '', // 承兑人名称
        draftAmount: 0, // 票据金额，单位元
        draftActualAmount: 0, // 单张实收票据金额
        maturityDate: '', // 到期日
        lakhFee: 0, // 每十万扣款，单位元
        annualInterest: 0 // 年利率
      },
      order: null, // 当前操作的订单
      visible: false, // 弹窗是否打开
      accountList: [], // 背书账户列表
      accountSelect: null, // 选择的背书账户列表
      loading: false, // 加载中
    }
  },

  computed: {
    ...mapGetters('user', {
      userInfo: 'userInfo', // 用户信息
    }),
    // 复制内容
    copyCurrentItem() {
      return function(form) {
        const otherInfo = !this.hasAllOrder ? `承兑人：${form.acceptorName}\n`
        + `票面金额：${yuan2wan(form.draftAmount)}万\n`
        + `票号：${form.draftNo}`
          : ''

        return `${`企业名称：${form.buyCorpName}\n`
        + `银行账户：${form.bankAccount}\n`
        + `银行名称：${form.buyCorpBankName}\n`
        + `银行行号：${form.bankCode}\n`}${
          otherInfo}`
      }
    },
    // 是否批量
    hasAllOrder() {
      return this.order instanceof Array
    },
    // 是否包含亿联银行渠道
    hasYlBankPaymentChannel() {
      return (this.hasAllOrder ? this.order : [this.order]).some(order => order.paymentChannel === PAYMENT_CHANNEL.YI_LIAN_BANK.id || order.paymentChannel === PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id)
    },
    // 是否包含智付E+渠道  ZHI_FU_YI_LIAN_PLUS
    hasYiLianPlusChannel() {
      return (this.hasAllOrder ? this.order : [this.order]).some(order => order.paymentChannel === PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id)
    }
  },

  methods: {
    showConfirmationDialog,
    yuan2wan,
    init() {
      if (this.order && Array.isArray(this.order) && this.order.length > 99) {
        this.$message.warning(`每次批量操作最多可选择 ${99} 笔订单`)
        return
      }

      // 批量背书时判断被背书账号是否相同，只有相同才能批量
      if (this.hasAllOrder) {
        let isEndorseDiff = false // 被背书账号是否相同
        let bankAccount = '' // 被背书账号
        let serialDraftCodeMap = {} // 连号票关联code，用来判断是否是多组连号票
        for (let i = 0; i < this.order.length; i++) {
          if (!isEndorseDiff) {
            isEndorseDiff = !!(this.order[i].endorseAccount && bankAccount && this.order[i].endorseAccount.bankAccount !== bankAccount)
            bankAccount = this.order[i].endorseAccount ? this.order[i].endorseAccount.bankAccount : bankAccount
          }
          if (this.order[i].serialDraftCode) {
            serialDraftCodeMap[this.order[i].serialDraftCode] = true
          }
        }
        if (isEndorseDiff) {
          // 连号票批量操作显示过渡页
          if (Object.keys(serialDraftCodeMap).length === 1) {
            this.$refs.diffEndorsedDialogRef.init(this.order)
          } else {
            const content = <div>选择批量完成背书的订单必须为 <span class="red-high-light">同一被背书账户</span> ，请重试。</div>
            this.$confirm(content, '提示', {
              confirmButtonText: '我知道了',
              type: 'warning',
              iconPosition: 'title',
              showClose: false,
              dangerouslyUseHTMLString: true,
              showCancelButton: false
            })
          }
          return
        }
        const currentOrder = this.order[0] // 默认取第一个数据来回显
        this.form.sellCorpName = (this.userInfo || {}).corpName
        this.form.buyCorpName = currentOrder.corpName
        this.form.buyCorpBankName = (currentOrder.endorseAccount || {}).bankBranchName
        this.form.bankAccount = (currentOrder.endorseAccount || {}).bankAccount
        this.form.bankCode = (currentOrder.endorseAccount || {}).bankBranchCode
        this.showConfirmDialog(this.order)
      } else {
        this.getBillOrderEndorseAccount(this.order)
      }
      this.visible = true
      if (this.hasYiLianPlusChannel) {
        this.showConfirmationDialog('背出', { type: `票方${this.hasAllOrder ? '批量' : '单张'}背书`, info: this.order })
      }
    },

    // 从不同列表选择同一被背书账户的数据
    handleToEndorse(list) {
      list && (this.order = list)
      this.init()
    },

    // 复制成功
    onSuccess() {
      if (this.success) {
        this.success()
      } else {
        this.$message.success('复制成功')
      }
    },

    // 复制失败
    onError() {
      if (this.error) {
        this.error()
      } else {
        this.$message.error('复制失败，请重试')
      }
    },

    // 确认
    confirm() {
      this.postEndorsed(this.order)
    },

    // 根据订单号获取企业背书账户信息
    async getBillOrderEndorseAccount(order) {
      const { orderNo } = order
      const data = await orderApi.getBillOrderEndorseAccount(orderNo)
      this.form = data
      this.order.parentDraftAmount = data.parentDraftAmount
      this.showConfirmDialog(this.order)
    },

    // 确认背书请求
    async postEndorsed(order) {
      const orderNoList = this.hasAllOrder ? order.map(i => i.orderNo) : [order.orderNo]
      const param = {
        isShowBatchError: false, // 是否统一处理批量操作的错误
        orderNoList, // 订单编号
        orderStepsEnumCode: EXECUTE_TRANSACTION_PROCESS.SELLER_ENDORSEMENT,
        bankAccount: this.hasAllOrder ? (order[0].endorseAccount || {}).bankAccount : (order.endorseAccount || {}).bankAccount, // 银行账号
      }
      try {
        if (this.hasAllOrder) {
          this.loading = true
          const data = await orderApi.postExecuteOrderTradeStep(param)
          this.loading = false
          // 批量操作结果提示
          this.$refs.resultDialogRef.init(data)
        } else {
          await orderApi.postSingleExecuteOrderTradeStep(param)
          this.$emit('success')
          this.$message({
            type: 'success',
            message: '背书完成'
          })
        }
        this.handleClose()
      } catch (error) {
        this.loading = false
      }
    },
    // 批量操作结果弹窗关闭回调
    closeResultDialog() {
      this.$emit('success')
      this.$emit('destroy')
    },
    handleClose() {
      this.visible = false
      !this.hasAllOrder && this.$emit('destroy')
    },

    // 新票确认弹窗
    showConfirmDialog(order) {
      if (this.hasAllOrder || !order.parentOrderNo) {
        // 批量和非新票拆分不显示弹窗了
        return
      }
      const h = this.$createElement

      let firstLine = []
      if (this.hasAllOrder) {
        firstLine.push(h('span', null, '您正在对 '))
        firstLine.push(h('span', { class: 'red-high-light' }, order.length))
        firstLine.push(h('span', null, ' 笔订单进行批量背书操作，'))
      }

      let msgLine = []
      msgLine.push(h('p', null, firstLine))
      if (!this.hasAllOrder) {
        if (order.parentOrderNo) {
          // 新票拆分订单
          msgLine.push(h('p', null, [
            h('span', { class: 'dialog-thin-text' }, '该笔为新票拆分订单，票号'),
            h('span', { class: 'red-high-light' }, order.lastSixDraftNo),
            h('span', { class: 'dialog-thin-text' }, '票面金额'),
            h('span', { class: 'red-high-light' }, `${yuan2wan(order.parentDraftAmount)}万`),
            h('span', { class: 'dialog-thin-text' }, '，拆分交易金额'),
            h('span', { class: 'red-high-light' }, `${yuan2wan(order.draftAmount)}万元`),
            h('span', { class: 'dialog-thin-text' }, '，请确认网银实际背出金额应为'),
            h('span', { class: 'red-high-light' }, `${yuan2wan(order.draftAmount)}万元`),
          ]))
        }
      }
      let message = h('p', null, msgLine)
      this.$msgbox({
        title: '提示',
        dangerouslyUseHTMLString: true,
        message,
        confirmButtonText: '确定',
        showCancelButton: false,
        type: 'warning',
        customClass: 'warning-alert'
      })
    },

    // 清除数据
    clearData() {
      this.batchLoading = false
    },
  },
}
</script>
