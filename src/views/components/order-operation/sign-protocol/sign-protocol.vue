
<style lang="scss">
.sign-btn {
  padding: 16px;
}
</style>

<template>
  <div>
    <el-button
      class="sign-btn"
      :type="$attrs.type || 'primary'"
      :width="$attrs.width || '140'"
      :height="$attrs.height || '40'"
      size="large"
      :disabled="isDisabled"
      :loading="btnLoading"
      @click="onSign"
    >
      {{ getButtonText() }}
    </el-button>
  </div>
</template>

<script>
import orderApi from '@/apis/order'
import { INITIATOR_SIGN_STATUS, INITIATOR_SIGN_STATUS_MAP, RECIPIENT_SIGN_STATUS, RECIPIENT_SIGN_STATUS_MAP } from '@/constants/transaction-tooltip.js'
import { mapGetters } from 'vuex'
import { downloadFile } from '@/common/js/util' // 下载文件

export default {
  name: 'sign-protocol',
  props: {},
  data() {
    return {
      INITIATOR_SIGN_STATUS_MAP,
      RECIPIENT_SIGN_STATUS_MAP,
      btnLoading: false
    }
  },
  computed: {
    ...mapGetters('draft-detail', {
      traderCorpOrderInfo: 'traderCorpOrderInfo', // 订单详情
      isSale: 'isSale', // 是否票方
      isBuy: 'isBuy', // 是否资方
    }),
    // 是否是合同签署发起者，是true，否false
    isInitiator() {
      const { initiator } = this.traderCorpOrderInfo
      return (this.isSale && initiator === 1) || (this.isBuy && initiator === 2)
    },
    isDisabled() {
      const { initiator, processStatus } = this.traderCorpOrderInfo
      // 可操作按钮情况有：发起方(发起签署申请,下载合同) 接收方(发起签署申请,同意签署,下载合同)
      let initiatorBtn = [INITIATOR_SIGN_STATUS.APPLY_SIGN.id, INITIATOR_SIGN_STATUS.SIGN_FINISH.id]
      let recipientBtn = [RECIPIENT_SIGN_STATUS.APPLY_SIGN.id, RECIPIENT_SIGN_STATUS.AGREE_SIGN.id, RECIPIENT_SIGN_STATUS.SIGN_FINISH.id]
      let bool = false
      if (initiator === 0) {
        // 初始状态-发起申请
        bool = false
      } else if (this.isInitiator) {
        // 发起者
        bool = !initiatorBtn.includes(processStatus)
      } else {
        // 接收者
        bool = !recipientBtn.includes(processStatus)
      }
      return bool
    }
  },
  methods: {
    // 不同合同状态下的按钮文本
    getButtonText() {
      const { initiator, processStatus } = this.traderCorpOrderInfo || {}
      if (initiator === 0 || !initiator) {
        return '发起签署合同'
      } else if ((this.isSale && initiator === 1) || (this.isBuy && initiator === 2)) {
        // 合同发起者
        return INITIATOR_SIGN_STATUS_MAP[processStatus]
      } else {
        // 接收者
        return RECIPIENT_SIGN_STATUS_MAP[processStatus]
      }
    },
    // 签署合同
    async onSign() {
      try {
        const { initiator, orderNo, processStatus, contractUrl } = this.traderCorpOrderInfo
        if (processStatus !== INITIATOR_SIGN_STATUS.SIGN_FINISH.id) {
          // 合同签署未申请状态，initiator需要判断发起方是持票方还是接单方，发起后从详情接口取值
          let launch = initiator
          if (!initiator) {
            // 发起方 1-持票方 2-接单方
            launch = this.isSale ? 1 : 2
          }
          // 同意签署
          let params = {
            orderNo,
            initiator: launch,
            processStatus: this.getStatus(),
            contractType: 'draft20241122'
          }
          this.btnLoading = true
          await orderApi.postSignOrderProtocol(params)
          this.$message.success('操作成功')
          this.$emit('success')
        } else {
          // 合同签署完成，下载合同
          downloadFile(contractUrl, {}, '票据收益权转让合同', 'pdf')
          this.$message.success('下载完成')
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log(error)
      } finally {
        this.btnLoading = false
      }
    },
    // 状态转换需要传给接口，1-发起方申请 2-接收方签署 3-接收方完成 4-发起方完成
    getStatus() {
      const { initiator, processStatus } = this.traderCorpOrderInfo
      let status = null
      if (!initiator) {
        // 申请
        status = 1
      } else if (!this.isInitiator && processStatus === RECIPIENT_SIGN_STATUS.AGREE_SIGN.id) {
        // 接收方签署
        status = 2
      }
      return status
    }
  }
}
</script>
