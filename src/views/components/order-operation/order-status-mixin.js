import {
  DRAFT_STATUS, // 订单状态tab
  TRANSACTION_STATUS, // 订单交易状态
  ROLE, // 角色方
  ORDER_BROKE_STATUS, // 订单违约状态
} from '@/constant'

// 订单状态
export default {
  data() {
    return {
      name: 'order-status-mixin'
    }
  },
  computed: {
    // 订单对象数据
    orderData() {
      if (!this.order) return {}
      return Array.isArray(this.order) ? this.order[0] : this.order
    },
    // 是否票方操作 （当前订单操作方（当前操作方未必是订单的取消方））
    isSale() {
      return this.orderData.role === ROLE.SALE.id
    },
    // 是否接票方操作（当前订单操作方（当前操作方未必是订单的取消方））
    isBuy() {
      return this.orderData.role === ROLE.BUY.id
    },
    // 待审核
    waitingReview() {
      return this.orderData?.tabStatus === DRAFT_STATUS.WAITING_REVIEW.id
    },
    // 待接单
    waitingReceiving() {
      return this.orderData?.tabStatus === DRAFT_STATUS.WAITING_RECEIVING.id
    },
    // 待确认
    waitingConfirm() {
      return this.orderData?.tabStatus === DRAFT_STATUS.WAITING_CONFIRM.id
    },
    // 待支付
    waitingPay() {
      return this.orderData?.tabStatus === DRAFT_STATUS.WAITING_PAY.id
    },
    // 待背书
    waitingEndorse() {
      return this.orderData?.tabStatus === DRAFT_STATUS.WAITING_ENDORSE.id
    },
    // 待签收
    waitingSubmission() {
      return this.orderData?.tabStatus === DRAFT_STATUS.WAITING_SIGN.id
    },
    // 取消中
    canceling() {
      return this.orderData?.tabStatus === DRAFT_STATUS.CANCELING.id
    },
    // 取消中-待签收
    cancelingSubmission() {
      return this.canceling && [TRANSACTION_STATUS.SUBMISSION_BUY_CANCELING.id, TRANSACTION_STATUS.SUBMISSION_SALE_CANCELING.id].includes(this.orderData?.transactionStatus)
    },
    // 取消中-待背书
    cancelingEndorse() {
      return this.canceling && [TRANSACTION_STATUS.ENDORSEMENT_SALE_CANCELING.id, TRANSACTION_STATUS.ENDORSEMENT_BUY_CANCELING.id].includes(this.orderData?.transactionStatus)
    },
    // 取消中-待支付
    cancelingPay() {
      return this.canceling && [TRANSACTION_STATUS.PAYING_BUY_CANCELING.id, TRANSACTION_STATUS.PAYING_SALE_CANCELING.id].includes(this.orderData?.transactionStatus)
    },
    // 已失败
    failed() {
      return this.orderData?.tabStatus === DRAFT_STATUS.DEAL_FAIl.id
    },
    // 已完成
    finished() {
      return this.orderData?.tabStatus === DRAFT_STATUS.DEAL_COMPLETED.id
    },
    // 是否定向票
    isAgentOrder() {
      return !!this.orderData?.agentOrder
    },
    // 是否连号票
    isSerialDraft() {
      return !!this.orderData?.serialDraft
    },
    // 是否极速票
    isFastTrade() {
      return !!this.orderData?.fastTrade
    },
    // 是否自动类型
    isRadarType() {
      return !!this.orderData?.radarType
    },
    // 是否违约
    isBroke() {
      return (this.order?.orderBrokeStatus === ORDER_BROKE_STATUS.MY.id) || (this.order?.orderBrokeStatus === ORDER_BROKE_STATUS.OTHER.id)
    },

  },
}
