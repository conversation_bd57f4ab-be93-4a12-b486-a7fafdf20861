<style lang="scss" scoped>
.custom-alert {
  align-items: flex-start;
  margin-bottom: 12px;
  color: $color-text-primary;

  ::v-deep {
    .el-alert__icon {
      font-size: 18px;
      color: $font-color;
    }

    .el-alert__title {
      font-size: 16px;
      line-height: 22px;
    }
  }
}

.card {
  margin-bottom: 12px;
  padding: 12px 16px;
  background: $color-FFFFFF;

  &:last-child {
    margin-bottom: 0;
  }

  .title {
    margin-bottom: 20px;
    border-left: 4px solid $--color-primary;
    padding-left: 8px;
    font-size: 16px;
    font-weight: bold;
  }

  .temp a {
    color: $font-color;

    span {
      margin-left: 6px;
      font-size: 16px;
      text-decoration: underline;
    }
  }
}
</style>

<template>
  <el-dialog
    :visible="dialogVisible"
    width="600px"
    :title="title"
    :close-on-click-modal="false"
    :before-close="close"
    destroy-on-close
  >
    <slot />
    <!--
      <el-alert
      v-if="tips"
      class="custom-alert"
      type="warning"
      :title="tips"
      :closable="false"
      show-icon
      />
    -->
    <WarnContent class="custom-alert" class-type="blue">{{ tips }}</WarnContent>
    <div class="card">
      <div class="title">
        <span>下载模板</span>
      </div>
      <div class="temp">
        <a :href="temp.href" target="_self" rel="noopener noreferrer">
          <icon type="chengjie-filet-ext" size="16" />
          <span>{{ temp.name }}</span>
        </a>
      </div>
    </div>
    <div class="card">
      <div class="title">
        {{ uploadTitle }}
      </div>
      <el-upload
        accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        action=""
        :limit="1"
        :http-request="httpRequest"
        :before-upload="beforeUpload"
        :on-error="handleError"
        :on-exceed="handleExceed"
        :on-remove="handleError"
      >
        <el-button border type="primary">
          请选择上传文件
        </el-button>
      </el-upload>
      <div v-if="fileFormat">
        {{ fileFormat }}
      </div>
    </div>

    <template #footer>
      <div>
        <el-button @click="close">
          取消
        </el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { upload } from '@/utils/oss'
import WarnContent from '@/views/components/common/warn-content.vue' // 警告文本
import { parseExcel } from '@/utils/parse-excel'
export default {
  name: 'file-upload-dialog',
  components: { WarnContent },
  props: {
    // 弹窗标题
    title: {
      type: String,
      default: '导入文件',
    },
    // 警示文案 不传就不展示提示
    tips: {
      type: String,
      default: '',
    },
    // OSS UPLOADER DIR
    dir: {
      type: String
    },
    // 文件模板 不传href就不展示下载模版
    temp: {
      type: Object,
      default: () => ({
        name: '',
        href: '',
      }),
    },
    // 上传按钮的标题
    uploadTitle: {
      type: String,
      default: '上传文件'
    },
    // 上传文件格式提示语 不传不展示
    fileFormat: {
      type: String,
      default: ''
    },
    // 限制的文件大小，单位为 M
    sizeLimit: {
      type: Number,
      default: 10
    },
    submitLoading: {
      type: Boolean,
      default: false
    },
    // 限制行数
    rowCountLimit: {
      type: Number,
    },

  },
  data() {
    return {
      dialogVisible: false,
      uploading: false, // 是否正在上传
      fileList: [],
      currentUrl: '',
    }
  },
  methods: {
    fileSizeCheck(file) {
      // eslint-disable-next-line no-magic-numbers
      const size = this.sizeLimit * 1024 * 1024 // 最大限制 10Mb 的文件
      const isLt = file.size < size
      if (!isLt) {
        this.$message.error(`上传文件大小不能超过 ${this.sizeLimit}MB`)
      }
      return isLt
    },

    // 上传之前的校验
    beforeUpload(file) {
      if (!this.fileSizeCheck(file)) return Promise.reject(new Error())
      if (!this.dir) {
        this.$message.error('请指定文件夹')
        return Promise.reject(new Error('请指定文件夹'))
      }
      return true
    },
    // 验证文件
    validateFiles(files) {
      if (this.accept) {
        files = [].slice.call(files).filter(file => {
          const { type, name } = file
          const extension = name.indexOf('.') > -1
            ? `.${name.split('.').pop()}`
            : ''
          const baseType = type.replace(/\/.*$/, '')
          return this.accept.split(',')
            .map(t => t.trim())
            .filter(Boolean)
            .some(acceptedType => {
              if (/\..+$/.test(acceptedType)) {
                return extension === acceptedType
              }
              if (/\/\*$/.test(acceptedType)) {
                return baseType === acceptedType.replace(/\/\*$/, '')
              }
              if (/^[^/]+\/[^/]+$/.test(acceptedType)) {
                return type === acceptedType
              }
              return false
            })
        })
        if (!files.length) {
          this.$message.warning('不支持该文件类型')
        }
      }
      if (!files || !files.length) return
      const file = files[0]
      if (this.sizeLimit) {
        // eslint-disable-next-line no-magic-numbers
        const fileSize = file.size / 1024 / 1024
        if (fileSize >= this.sizeLimit) {
          this.$message.error(this.sizeLimitMessage || `上传的文件大小不能超过 ${this.sizeLimit}M`)
          return
        }
      } else if (typeof this.beforeUpload === 'function' && !this.beforeUpload(file)) {
        return
      }
      return file
    },
    httpRequest({ file, onSuccess, onError }) {
      this.uploadFiles([file], onSuccess, onError)
    },

    // 上传文件
    async uploadFiles(files, onSuccess, onError) {
      const fileToUpload = this.validateFiles(files)
      if (!fileToUpload) {
        return
      }
      this.uploading = true
      try {
        const url = await upload(fileToUpload, this.dir)
        const dataTemp = await parseExcel(url) // 解析excl数据
        if (dataTemp.length > this.rowCountLimit) {
          onError()
          this.$message.warning(`单次最多可执行${this.rowCountLimit}条，请拆分成多个文件`)
          return
        }
        this.currentUrl = url
        onSuccess({ url })

        // onSuccess 可以返回一个 promise，如果返回 promise，上传结果会取决于 promise 最终的状态
        try {
          typeof this.onSuccess === 'function' && await this.onSuccess(url, fileToUpload)
          this.$emit('input', url)
        } catch (e) {
          // onSuccess 函数报错
        }
      } catch (e) {
        // eslint-disable-next-line
        console.log('上传失败')
        // this.$message.error('上传失败')
      } finally {
        this.uploading = false
      }
    },

    handleError() {
      // 删除数据
      this.currentUrl = ''
      this.$emit('input', '')
    },

    handleExceed() {
      this.$message.error('超出上传数量限制')
      return false
    },

    handleSubmit() {
      if (!this.currentUrl) {
        this.$message.error('请选择要上传的文件')
        return
      }
      this.$emit('submit', this.currentUrl)
    },

    open() {
      this.dialogVisible = true
      // this.fileList = []
    },
    close() {
      this.dialogVisible = false
      this.currentUrl = null
      this.$emit('close')
    }

  }
}
</script>
