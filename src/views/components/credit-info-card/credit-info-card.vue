<!-- 对方信用信息 -->
<style lang="scss" scoped>
.credit-info-container {
  background: $color-FFFFFF;

  .icon-box {
    display: flex;
    align-items: flex-end;

    .icon-level {
      width: 94px;
      height: 26px;
    }

    .icon-tips {
      margin-left: 8px;
    }
  }

  .high-light {
    font-weight: 600;
  }

  ::v-deep .desc-list .row .item-column-2:last-child {
    padding-left: 16px;
  }
}

.credit-info-receive-order {
  .icon-box {
    .icon-level {
      width: 80px;
      height: 22px;
    }
  }

  .item-value {
    font-size: 14px;
  }
}

.receive-order {
  .level-item {
    display: flex;
    align-items: flex-end;
    flex-wrap: nowrap;

    .item-label {
      margin-right: 8px;
    }

    .icon-level {
      width: 94px;
      height: 26px;
    }
  }

  &-list {
    display: flex;
    margin-top: 12px;
    border-radius: 2px;
    padding: 12px;
    background-color: $color-F5F6F8;

    .item {
      border-left: 1px solid $color-D9D9D9;
      padding: 0 24px;

      &:first-child {
        border-left: none;
        padding-left: 0;
      }

      &-value {
        margin-top: 4px;
        font-size: 14px;
      }
    }
  }
}

.credit-level {
  display: flex;
  align-items: center;
  margin-top: 8px;
  height: 20px;
  font-weight: 600;
  color: $color-text-primary;
  line-height: 20px;

  .text-gray {
    font-weight: 400;
    color: $color-text-secondary;
  }
}
</style>

<template>
  <div :class="['credit-info-container', `credit-info-${whereToUse}`]">
    <Card title="对方信用信息">
      <template slot="main">
        <div v-if="whereToUse === 'receive-order'" class="receive-order">
          <div v-if="isShare !== 'share'" class="level-item">
            <div class="credit-level">
              <span class="text-gray">信用分等级：</span>
              <!-- <el-image :src="CREDIT_LEVEL_ICON_MAP[levelItem.value]" class="icon-level" /> -->
              <span>信用{{ CREDIT_LEVEL_NAME_MAP[levelItem.value] }}</span>
              <span class="text-gray">&nbsp;(超过 </span>{{ levelItem.userProportion }}%<span class="text-gray">的用户)</span>
              <!-- 接单页面和确认页面的会员等级去掉 -->
              <!--
                <el-divider direction="vertical" />
                <span class="text-gray">会员等级：</span>{{ MEMBER_LEVEL_VALUE_MAP[levelItem.gradeCode ] }}
              -->
            </div>
            <!-- 以前样式 -->
            <!--
              <span class="item-value icon-box">
              <el-image :src="CREDIT_LEVEL_ICON_MAP[levelItem.value]" class="icon-level" />
              <span class="icon-tips">(超过 <span class="high-light text-primary">{{ levelItem.userProportion }}%</span> 的用户) </span>
              </span>
            -->
          </div>

          <ul class="receive-order-list">
            <li
              v-for="item in list"
              :key="item.id"
              class="item"
            >
              <div class="item-label">
                {{ item.label }}
              </div>
              <div :class="['item-value', `${item.isBold ? 'bold' : ''}`]">
                {{ item.value }}
              </div>
            </li>
          </ul>
        </div>
        <div v-else class="main">
          <ul class="desc-list">
            <li
              v-for="row in list"
              :key="row.id"
              class="row"
            >
              <div
                v-for="item in row.list"
                :key="item.id"
                :class="['item', `item-column-${row.list.length || 0}`, item.itemClass ? item.itemClass : '']"
              >
                <div class="item-label">
                  {{ item.label }}
                </div>
                <div v-if="item.key === 'level'" class="item-value icon-box">
                  <el-image :src="CREDIT_LEVEL_ICON_MAP[item.value]" class="icon-level" />
                  <span class="icon-tips">(超过 <span class="high-light text-primary">{{ item.userProportion }}%</span> 的用户) </span>
                </div>
                <div v-else class="item-value" :class="[`${item.isBold ? 'bold' : ''}`]">
                  {{ item.value }}
                </div>
              </div>
            </li>
          </ul>
        </div>
      </template>
    </Card>
  </div>
</template>

<script>
import Card from '@/views/pages/draft-detail/components/card.vue'
import {
  CREDIT_LEVEL_ICON_MAP, // 等级id映射图标
  CREDIT_LEVEL_NAME_MAP,
} from '@/constants/credit'
import { MEMBER_LEVEL_VALUE_MAP } from '@/constant' // 会员等级 id 映射 名称
export default {
  name: 'credit-info',

  components: {
    Card
  },

  props: {
    draftInfo: Object, // 票据信息 只需要对方信用信息和统计信
    // 在哪里使用
    whereToUse: {
      default: 'draft-detail', // draft-detail: 在普通订单详情使用, agent-draft-detail: 定向订单详情， receive-order: 在接单弹窗
      type: String
    },
    isShare: {
      default: 'noShare', // 是否是分享页面
      type: String
    }
  },

  data() {
    return {
      // 信用等级图标
      CREDIT_LEVEL_ICON_MAP,
      CREDIT_LEVEL_NAME_MAP, // 排版列表
      MEMBER_LEVEL_VALUE_MAP,
      list: [],
      levelItem: {}
    }
  },

  watch: {
    draftInfo: {
      handler(val) {
        this.setListData(val)
      },
      immediate: true,
      deep: true
    }
  },

  methods: {
    // 设置列表数据
    setListData(val) {
      const { gradeCode } = (val?.oppositeMemberInfoDTO || {})
      const { currentCreditType, userProportion } = (val?.oppositeCreditDTO || {})
      const { confirmRate, payRate, endorseRate, signRate, lightBrokeTime, middleBrokeTime, heavyBrokeTime } = (val?.oppositeStatCorpTradeRecordDTO || {})
      const levelItem = {
        label: '信用分等级',
        key: 'level',
        value: currentCreditType || 0,
        userProportion: userProportion || 0,
        gradeCode: gradeCode || 0
      }
      const confirmRateItem = {
        label: '确认率',
        value: `${confirmRate || 0}%`,
        key: 'confirmRate',
        isBold: true,
      }
      const payRateItem = {
        label: '打款率',
        value: `${payRate || 0}%`,
        key: 'payRate',
        isBold: true,
      }
      const endorseRateItem = {
        label: '背书率',
        value: `${endorseRate || 0}%`,
        key: 'endorseRate',
        isBold: true,
      }
      const signRateItem = {
        label: '签收率',
        value: `${signRate || 0}%`,
        key: 'signRate',
        isBold: true,
      }
      const brokeTimeItem = {
        label: '违约次数（轻度 / 中度 / 重度）',
        key: 'brokeTime',
        value: `${lightBrokeTime || 0}次 / ${middleBrokeTime || 0}次 / ${heavyBrokeTime || 0}次`,
      }

      switch (this.whereToUse) {
        case 'receive-order':
          this.levelItem = levelItem
          this.list = [brokeTimeItem, confirmRateItem, endorseRateItem]
          break
        case 'agent-draft-detail':
          this.list = [
            {
              id: 1,
              list: [levelItem, confirmRateItem]
            }, {
              id: 2,
              list: [brokeTimeItem, endorseRateItem]
            }
          ]
          break
        case 'draft-detail':
          this.list = [
            {
              id: 1,
              list: [levelItem, payRateItem]
            }, {
              id: 2,
              list: [brokeTimeItem, signRateItem]
            }
          ]
          break
        default:
          break
      }
    }
  }
}
</script>
