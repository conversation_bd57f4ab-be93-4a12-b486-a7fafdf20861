import axios from 'axios'
import { v4 as uuidv4 } from 'uuid'
import { OSS_DIR, OSS_DIR_NAME } from '@/constant'
import commonDiscernApi from '@recognize/apis/common'

/**
 * 文件上传接口
 * @param {object} fileObj 文件对象
 * @param {string} directory 所属文件夹
 * @param {object} options 参数
 * @param {string} options.fileName 文件名
 * @param {string} options.prefix 文件名前缀
 * @param {function} options.onProgress 上传进度回调函数
 * @returns {Promise<any>} 操作后的promise回调
 */
export async function upload(fileObj, directory, options = {}) {
  if (!directory) {
    // eslint-disable-next-line no-console
    console.error('请指定文件夹')
  }
  const image = Object.keys(OSS_DIR).find(dir => OSS_DIR[dir] === directory)
  if (process.env.NODE_ENV === 'development' && !image) {
    const keys = Object
      .keys(OSS_DIR)
      .map(key => `OSS_DIR.${key}`)
      .join('、')
    throw new Error(`directory 属性必须为 ${keys} 其中一个`)
  }
  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line no-console
    console.log(`上传至【${OSS_DIR_NAME[directory]}】文件夹`)
  }
  // 获取policy信息
  const res = await commonDiscernApi.getUploadPolicy({ image })
  const { accessKeyId, dir, policy, signature, host } = res
  const getFileName = () => {
    let fileName = options.fileName || fileObj.name
    // 剔除文件名称携带的空格
    fileName = fileName.replace(/\s+/g, '')
    // 防止url超过数据库的255位限制
    fileName = fileName.substring(fileName.length - 30, fileName.length)
    // 要上传到后端返回的指定路径下，否则报无权限
    return `${dir}/${uuidv4()}_${fileName}`
  }
  const fileName = getFileName()
  // 拼接文件上传请求
  const fileReq = new FormData()
  fileReq.append('key', fileName)
  fileReq.append('policy', policy)
  fileReq.append('OSSAccessKeyId', accessKeyId)
  fileReq.append('success_action_status', '200')
  fileReq.append('signature', signature)
  fileReq.append('file', fileObj)
  await axios.post(
    host,
    fileReq,
    {
      headers: { 'Content-Type': 'multipart/form-data' }
    }
  )
  return `${host}/${encodeURIComponent(fileName)}` // encodeURIComponent  阿里云后台会二次转码 所以对应的前端也要转码
}
