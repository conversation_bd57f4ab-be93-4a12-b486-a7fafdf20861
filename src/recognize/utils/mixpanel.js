/* eslint-disable valid-jsdoc */
import Storage from '@/common/js/storage'
// import mixpanel from 'mixpanel-browser'
import { USER_INFO } from '../constant-storage'

// const TOKEN = 'fc53e33ea5ce048f0aac7b3674d22a68' // 票店后台生产环境的 token
// const QA_TOKEN = '71d9c12abb2dc02d4671b583f5f7742f' // 票店后台 qa 环境的 token

class Mixpanel {
  constructor() {
    // this.mixpanel = null
    this.mixpanel = {}
  }

  /**
   * 初始化
   * @returns {promise} 标识初始化是否完成的 promise
   */
  init() {
    if (this.mixpanel) {
      return Promise.resolve(this.mixpanel)
    } else {
      // const token = process.env.VUE_APP_API_ENV === 'production' ? TOKEN : QA_TOKEN
      return new Promise(resolve => {
        resolve(this.mixpanel)
        // mixpanel.init(token, {
        //   debug: process.env.VUE_APP_API_ENV !== 'production',
        //   loaded: () => {
        //     resolve(this.mixpanel)
        //   }
        // })
        // this.mixpanel = mixpanel
      })
    }
  }

  /**
   * 上报数据
   * @param {string} eventName 事件名
   * @param {any} properties 属性
   * @param  {...any} args 其他参数
   */
  async track(eventName, properties, ...args) {
    if (!this.mixpanel) {
      await this.init()
    }
    if (!properties) {
      properties = {}
    }
    const mobile = Storage.get(USER_INFO)?.mobile
    properties.用户ID = mobile || 'visitor'
    if (mobile) {
      properties.手机号 = mobile
    }
    properties.登录渠道 = '签手'
    // console.log('eventName', eventName, properties)
    // eslint-disable-next-line no-unused-vars
    let userCallback
    const userCallbackIndex = args.findIndex(arg => typeof arg === 'function')
    if (userCallbackIndex > -1) {
      userCallback = args[userCallbackIndex]
      args.splice(userCallbackIndex, 1)
    }
    return new Promise(resolve => {
      resolve()
      // 发送完成的回调
      // const callback = function(...callbackArgs) {
      //   // console.log('callback')
      //   userCallback && userCallback.apply(this, callbackArgs)
      //   resolve(callbackArgs)
      // }
      // this.mixpanel.track(eventName, properties, ...args.concat(callback))
    })
  }

  /** 打开签手 */
  run() {
    this.track('签手登录_主窗口_登录_打开')
  }

  /**
   * 登录
   * @param {boolean} isLoggedIn 是否登录过
   * @param {number} mobile 电话
   * @param {string} corpName 企业名称
   * @param {string} corpId 企业id
   */
  login(isLoggedIn, mobile, corpName) {
    // await this.init()
    // const id = Mixpanel.getUserId(corpId || mobile)
    // if (isLoggedIn) {
    //   this.mixpanel.identify(id)
    // } else {
    //   this.mixpanel.alias(id)
    // }
    const props = {
      手机号: mobile,
      $name: mobile
    }
    if (corpName) {
      props['企业名称'] = corpName
    }
    // this.mixpanel.people.set(props)
    this.track('签手登录_主窗口_登录_点击', {
      企业名称: corpName
    })
  }

  /**
   * 切换用户
   * @param {object} props track 参数
   * @param {string} props.mobile 电话
   * @param {string} props.corpId 企业id
   */
  async changeUser(props) {
    await this.init()
    // eslint-disable-next-line no-unused-vars
    const id = Mixpanel.getUserId(props.corpId || props.mobile)
    // this.mixpanel.identify(id)
    // this.mixpanel.people.set({
    //   mobile: props.mobile
    // })
  }

  /**
   * 单张识别
   * @param {object} properties 参数
   * @param {string} properties.draft_no 票号
   * @param {string} properties.draft_amount 票面金额
   * @param {string} properties.acceptor 承兑人
   * @param {string} properties.expiry_date 到期日
   * @param {string} properties.defect 瑕疵情况
   * @param {string} properties.bank 银行名称
   * @param {string} properties.identify_failed 识别失败原因
   * @param {string} properties.defect_notification 瑕疵提示
   * @param {number} properties.identify_time 单张识别时间
   * @param {number} properties.identify_bare_ticket 识别光票
   * @param {number} properties.manual_bare_ticket 手动光票
   */
  identifyDraft(properties) {
    let queryBody = {
      票号: properties.draft_no,
      票面金额: properties.draft_amount,
      承兑人: properties.acceptor,
      到期日: properties.expiry_date,
      瑕疵情况: properties.defect,
      银行名称: properties.bank,
      识别失败原因: properties.identify_failed,
      瑕疵提示: properties.defect_notification,
      单张识别时间: properties.identify_time,
      '是否光票(手动)': properties.manual_bare_ticket ? '是' : '否',
      '是否光票(识别)': properties.identify_bare_ticket ? '是' : '否'
    }
    this.track('签手识别票据_主窗口_单张识别_结果', queryBody)
  }

  /**
   * 智能验票
   * @param {object} properties 参数
   * @param {string} properties.draft_no 票号
   * @param {string} properties.draft_amount 票面金额
   * @param {string} properties.acceptor 承兑人
   * @param {string} properties.expiry_date 到期日
   * @param {string} properties.defect 瑕疵情况
   * @param {string} properties.confirm 比对信息确认 点击关闭弹窗为 0，[确认无误]为 1，[不是此票]为 2
   */
  intelligenceCheck(properties) {
    let queryBody = {
      票号: properties.draft_no,
      票面金额: properties.draft_amount,
      承兑人: properties.acceptor,
      到期日: properties.expiry_date,
      瑕疵情况: properties.defect,
      比对信息确认: properties.confirm,
    }
    this.track('签手识别票据_主窗口_智能验票_触发', queryBody)
  }

  /**
   * 单张发布
   * @param {object} properties 数据
   * @param {string} properties.票号
   * @param {string} properties.票面金额
   * @param {string} properties.承兑人
   * @param {string} properties.到期日
   * @param {string} properties.瑕疵情况
   * @param {string} properties.每十万扣息
   * @param {string} properties.年利率
   * @param {string} properties.支付渠道
   * @param {string} properties.是否光速交易
   * @param {string} properties.是否带保订单
   * @param {string} properties.是否定向
   * @param {string} properties.是否议价
   * @param {string} properties.议价信息
   * @param {string} properties.是否出票人打码
   * @param {string} properties.是否收款人打码
   * @param {string} properties.是否连号票
   * @param {string} properties.发布失败原因
   * @param {number} properties.单张发布时间
   * @param {string} properties.企业名称
   */
  publishDraft(properties) {
    this.track('签手发布票据_单张发布_确定发布_点击', properties)
  }

  /**
   * 批量识别
   * @param {object} properties 参数
   * @param {string} properties.bank 银行名称
   * @param {string} properties.draft_count 票据数量
   * @param {string} properties.multi_identify_failed 批量识别失败原因
   * @param {number} properties.identify_time 识别用时
   * @param {string} properties.corpName 企业名称
   */
  multiIdentifyDraft(properties) {
    let queryBody = {
      银行名称: properties.bank,
      票据数量: properties.draft_count,
      批量识别失败原因: properties.multi_identify_failed,
      批量识别时间: properties.identify_time,
      企业名称: properties.corpName,
    }
    this.track('签手识别票据_主窗口_批量识别_点击', queryBody)
  }

  /**
   * 批量发布
   * @param {object} properties 参数
   * @param {string} properties.支付渠道
   * @param {string} properties.发布失败原因
   * @param {string} properties.票据数量
   * @param {string} properties.票面总金额
   * @param {string} properties.批量发布成功票据数量
   * @param {string} properties.批量发布失败票据数量
   * @param {string} properties.是否定向
   * @param {number} properties.批量发布时间
   * @param {string} properties.企业名称
   */
  multiPublishDraft(properties) {
    this.track('签手发布票据_批量发布_确定发布_点击', properties)
  }

  /**
   * 网银同步
   * @param {object} properties 参数
   * @param {string} properties.bank 银行名称
   * @param {string} properties.autosync_failed 自动同步失败原因
   */
  autoSync(properties) {
    let queryBody = {
      银行名称: properties.bank,
      自动同步失败原因: properties.autosync_failed,
    }
    this.track('签手识别票据_主窗口_自动同步_结果', queryBody)
  }

  /**
   * 点击[自动同步]右侧按钮
   * @param {object} properties 参数
   * @param {string} properties.linked_bank_count 已连接银行数量
   * @param {string} properties.bank 银行名称
   */
  enterBankList(properties) {
    let queryBody = {
      银行名称: properties.bank,
      已连接银行数量: properties.linked_bank_count,
    }
    this.track('签手识别票据_自动同步_同步银行列表_点击', queryBody)
  }

  /**
   * 点击[计算器]
   */
  enterCalculator() {
    this.track('签手工具箱_主窗口_计算器_点击')
  }

  /**
   * 点击[服务大厅]
   */
  enterHall() {
    this.track('签手工具箱_主窗口_服务大厅_点击')
  }

  /**
   * 点击[消息通知]
   */
  enterNotify() {
    this.track('签手工具箱_主窗口_消息通知_点击')
  }

  /**
   * 点击[瑕疵设置]
   */
  enterDefectSetting() {
    this.track('签手工具箱_主窗口_瑕疵设置_点击')
  }

  /**
   * 账户切换
   * @param {object} properties 参数
   * @param {string} properties.enterprise_count 已登录企业数量
   */
  switchEnterprise(properties) {
    this.track('签手账户切换_主窗口_账户切换_点击', properties)
  }

  /**
   * 点击[票据库存]
   */
  enterInventory() {
    this.track('签手票据库存_主窗口_票据库存_点击')
  }

  /**
   * 票据库存-点击[分享]
   */
  inventoryShare() {
    this.track('签手票据库存_库存列表_分享_点击')
  }

  /**
   * 票据库存-点击[图片分享]
   */
  inventorySharePicture() {
    this.track('签手票据库存_库存列表_图片分享_点击')
  }

  /**
     * 票据库存-点击[纯文本分享]
     */
  inventoryShareText() {
    this.track('签手票据库存_库存列表_纯文本分享_点击')
  }

  /**
     * 票据库存-点击[导出]
     */
  inventorExport() {
    this.track('签手票据库存_库存列表_导出_点击')
  }

  /**
     * 票据库存-点击[出库]
     * @param {object} properties 参数
     * @param {string} properties.出库备注
     */
  inventoryOutBound(properties) {
    this.track('签手票据库存_库存列表_出库_点击', properties)
  }

  /**
   * 主窗口-刷新按钮
   */
  refreshTicket() {
    this.track('签手发布票据_主窗口_刷新按钮_点击')
  }

  /**
   * 更新为签订单
   * @param {object} properties 参数
   * @param {string} properties.draftNo 票号
   * @param {string} properties.draftAmount 票面金额
   * @param {string} properties.acceptor 承兑人
   * @param {string} properties.expiryDate 到期日
   * @param {string} properties.defect 瑕疵情况
   * @param {string} properties.bank 银行名称
   */
  replaceQianOrder(properties) {
    let queryData = {
      票号: properties.draftNo,
      票面金额: properties.draftAmount,
      承兑人: properties.acceptor,
      到期日: properties.expiryDate,
      瑕疵情况: properties.defect,
      银行名称: properties.bank,
    }
    this.track('签手识别票据_主窗口_更新签订单_触发', queryData)
  }

  /**
   * 退出
   * @returns {promise} 是否已完全退出
   */
  logout() {
    // await this.init()
    // console.log('before', this.mixpanel.get_distinct_id())
    // this.mixpanel && this.mixpanel.reset()
    // this.mixpanel = null
    // console.log('after', this.mixpanel.get_distinct_id())
    // console.log(decodeURIComponent(document.cookie))
    // setTimeout(() => {
    //   console.log('after 111', this.mixpanel.get_distinct_id())
    // }, 100)
    return new Promise(resolve => setTimeout(resolve, 100))
  }

  /**
   * 获取 userId，为了用户 id 重复，添加前缀区分
   * @param {number} userId 用户 id
   * @returns {string} 转换后的用户 id
   */
  static getUserId(userId) {
    return `shendu_${userId}`
  }
}

export default new Mixpanel()
