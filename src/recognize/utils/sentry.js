/* eslint-disable no-magic-numbers */
import * as Sentry from '@sentry/vue'
import { Integrations } from '@sentry/tracing'
import Vue from 'vue'

// 所有事件数
let eventCount = 0
// 判断当前事件是否应该上报，返回 true/false
// eslint-disable-next-line no-unused-vars
const shouldSend = event => {
  eventCount++
  if (eventCount <= 5) {
    return true
  } else if (eventCount <= 20) {
    return Math.random() < 0.3
  } else if (eventCount <= 100) {
    return Math.random() < 0.1
  } else {
    return Math.random() < 0.05
  }
}

// 是否打包出来的应用
const isBuilt = process.env.NODE_ENV === 'production'

export default {
  init(router) {
    if (!isBuilt) {
      return
    }
    Vue.prototype.$sentry = Sentry
    Sentry.init({
      Vue,
      dsn: 'https://<EMAIL>/6620632', // 客户端密钥
      integrations: [
        new Integrations.BrowserTracing({
          routingInstrumentation: Sentry.vueRouterInstrumentation(router),
          // tracingOrigins: ['localhost', 'shop-qa.piaodian.net', /^\//], // 给对应 xhr 请求添加 sentry-trace 请求头
        }),
      ],
      logErrors: true, // 是否打印错误信息
      enabled: true, // 是否启用
      environment: process.env.VUE_APP_API_ENV, // 环境
      // 设为 1.0 可捕获 100% 的 transactions，用于性能监控。生产环境可按需调整
      tracesSampleRate: 1.0,
      release: process.env.APP_VERSION, // 发布版本
      // 事件上报拦截
      beforeSend(event) {
        return shouldSend(event) ? event : null
      },
      // 面包屑上报拦截
      beforeBreadcrumb(breadcrumb) {
        // 判断是否 Electron-log 的参数
        const isElectronLogArg = arg => typeof arg === 'string' && /^\d{2}:\d{2}:\d{2}.\d{3,}\s›\s/.test(arg)
        if (breadcrumb.category === 'category' && isElectronLogArg(breadcrumb.data.arguments[0])) {
          return null
        }
        return breadcrumb
      }
    })
  }
}
