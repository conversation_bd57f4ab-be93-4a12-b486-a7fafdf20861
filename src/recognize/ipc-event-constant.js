// 窗口操作事件
export const MINIMIZE = 'MINIMIZE' // 最小化事件
export const CLOSE = 'CLOSE' // 关闭事件
export const SET_MAIN_WINDOW_TOPPING = 'SET_MAIN_WINDOW_TOPPING' // 设置窗口置顶
export const OPEN_URL_IN_IE = 'OPEN_URL_IN_IE' // 通过IE打开某个页面
export const OPEN_URL_IN_DEFAULT_BROWSER = 'OPEN_URL_IN_DEFAULT_BROWSER' // 通过默认浏览器打开URL

// 所有可能打开的窗口名称
export const WINDOW_DRAFT_PREVIEW = 'WINDOW_DRAFT_PREVIEW' // 票据图片预览窗口

// 票据识别相关常量
export const MARKET_DRAFT_INSTALL_ERROR = 'MARKET_DRAFT_INSTALL_ERROR' // dll安装失败
export const MARKET_DRAFT_RECOGNIZE_SHOW_LAYER = 'MARKET_DRAFT_RECOGNIZE_SHOW_LAYER' // 显示票据识别选择框
export const MARKET_DRAFT_RECOGNIZE_DRAFT_SELECTED = 'MARKET_DRAFT_RECOGNIZE_DRAFT_SELECTED' // 票据识别窗口已选择
export const MARKET_DRAFT_RECOGNIZE_DRAFT_RECOGNIZED = 'MARKET_DRAFT_RECOGNIZE_DRAFT_RECOGNIZED' // 票据信息已识别
export const MARKET_DRAFT_ADD_MONITOR_BANK = 'MARKET_DRAFT_ADD_MONITOR_BANK' // 网银识别，增加需要监控的IE窗口
export const MARKET_DRAFT_ADD_MONITOR_BANK_SUCCESS = 'MARKET_DRAFT_ADD_MONITOR_BANK_SUCCESS' // 网银识别，增加识别成功的银行
export const MARKET_DRAFT_DELETE_BANK_SYNC = 'MARKET_DRAFT_DELETE_BANK_SYNC' // 网银识别，删除网银公司
export const MARKET_DRAFT_GET_MONITOR_BANK_SYNCED_LIST = 'MARKET_DRAFT_GET_MONITOR_BANK_SYNCED_LIST' // 网银识别，获取所有正在监控的网银公司
export const MARKET_DRAFT_GET_NEW_BILL_LIST = 'MARKET_DRAFT_GET_NEW_BILL_LIST' // 网银识别，获取新增的票据
export const MARKET_DRAFT_GET_BANK_SYNC_DATAS = 'MARKET_DRAFT_GET_BANK_SYNC_DATAS' // 网银识别，获取单张票据正背面信息
export const MARKET_DRAFT_ADAPTER_IDENTIFICATION = 'MARKET_DRAFT_ADAPTER_IDENTIFICATION' // 网银适配
export const MARKET_DRAFT_RECOGNIZE_MULTI_RECOGNIZED = 'MARKET_DRAFT_RECOGNIZE_MULTI_RECOGNIZED' // 批量识别识别成功事件
export const MARKET_DRAFT_START_COUNTDOWN = 'MARKET_DRAFT_START_COUNTDOWN' // 开始识别倒计时
export const MARKET_DRAFT_RECONSTRUCTION_DESKTOP_ICON = 'MARKET_DRAFT_RECONSTRUCTION_DESKTOP_ICON' // 重建桌面快捷方式图标

// 窗口间信息传递
export const TOKEN_EXPIRED = 'TOKEN_EXPIRED' // token 过期
export const DOWNLOAD_FILES = 'DOWNLOAD_FILES' // 下载文件事件
export const TICKET_STATUS_CHANGE = 'TICKET_STATUS_CHANGE' // 发布成功,刷新同步列表
export const GET_COMPUTER_INFO = 'GET_COMPUTER_INFO' // 获取电脑信息

// 快捷键
export const REGISTER_SHORTCUT = 'REGISTER_SHORTCUT' // 注册快捷键事件
export const EMIT_SHORTCUT = 'EMIT_SHORTCUT' // 触发快捷键事件
export const SHORTCUT_RECOGNIZE = 'SHORTCUT_RECOGNIZE' // 单张识别票据快捷键
export const SHORTCUT_MULIT_RECOGNIZE = 'SHORTCUT_MULIT_RECOGNIZE' // 批量识别票据快捷键
export const SHORTCUT_AUTO_SYNC = 'SHORTCUT_AUTO_SYNC' // 自动同步快捷键
export const SHORTCUT_OPEN_RECOGNIZE_WINDOW = 'OPEN_RECOGNIZE_WINDOW' // 打开识票助手快捷键

// 设置
export const CONFIG_SETTING_CHANGE = 'CONFIG_SETTING_CHANGE' // 触发常规设置、通知设置修改

// 通知
export const NOTIFICATION = 'NOTIFICATION' // 触发notify

// 在线更新相关
export const UPDATE_ERROR = 'UPDATE_ERROR' // 更新出错
export const UPDATE_CHECKING = 'UPDATE_CHECKING' // 检测更新中
export const UPDATE_AVAILABLE = 'UPDATE_AVAILABLE' // 检测到有效新版本
export const UPDATE_NOT_AVAILABLE = 'UPDATE_NOT_AVAILABLE' // 检测不到新版本
export const UPDATE_DOWNLOADING = 'UPDATE_DOWNLOADING' // 下载中
export const UPDATE_DOWNLOADED = 'UPDATE_DOWNLOADED' // 下载完成
export const UPDATE_VERSION = 'UPDATE_VERSION' // 通知主进程更新版本号
export const UPDATE_CHECK_UPDATE = 'UPDATE_CHECK_UPDATE' // 检测更新
export const UPDATE_UPDATE = 'UPDATE_UPDATE' // 执行更新
export const QUIT_AND_INSTALL = 'QUIT_AND_INSTALL' // 执行安装
export const RETRY = 'RETRY' // 重试

// 工具箱
export const BLACK_LIST_UPDATE = 'BLACK_LIST_UPDATE' // 黑名单列表更新
