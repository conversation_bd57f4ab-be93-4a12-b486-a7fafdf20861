import userApi from '@recognize/apis/user'
import { TOKEN } from '@/constant-storage'
import Storage from '@/common/js/storage'

/* eslint-disable valid-jsdoc */

// eslint-disable-next-line
export default class UserModel {

  /** 登录 */
  static async login(data) {
    let res = await userApi.postMobilePasswordLogin(data)
    return res
  }

  /** 清除 token */
  static clearToken() {
    Storage.remove(TOKEN)
  }

  /** 设置 token */
  static setToken(token) {
    Storage.set(TOKEN, token)
  }

  /** 获取 token */
  static getToken() {
    return Storage.get(TOKEN)
  }
}
