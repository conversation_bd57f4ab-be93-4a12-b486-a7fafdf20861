// 获取 id 映射信息
function getIdMap(nameMap) {
  let list = Object.values(nameMap)
  let result = {}
  list.forEach(item => {
    result[item.id] = item
  })
  return Object.freeze(result)
}

// 获取映射信息
export function getKeyToValueMap(nameMap, key = 'id', value = 'name') {
  let list = Object.values(nameMap)
  let result = {}
  list.forEach(item => {
    result[item[key]] = item[value]
  })
  return Object.freeze(result)
}

// 票据瑕疵类型
export const BACK_DEFECT_TYPE_NAME_MAP = Object.freeze({
  ABA: {
    id: 1,
    name: '回出票人aba',
    key: 'defectsAba',
  },
  ABCA: {
    id: 2,
    name: '回出票人abca',
    isShowNum: true,
    key: 'defectsAbca',
  },
  ABB: {
    id: 3,
    name: 'abb',
    key: 'defectsAbb',
  },
  HUI_SHOU_KUAN_REN: {
    id: 4,
    name: '回收款人',
    isShowNum: true,
    key: 'defectsReturnFront',
  },
  BEI_SHU_HUI_TOU: {
    id: 5,
    name: '背书回头',
    isShowNum: true,
    key: 'defectsTurnAround',
  },
  BEI_SHU_CHONG_FU: {
    id: 6,
    name: '背书重复',
    isShowNum: true,
    key: 'defectsDuplicated',
  },
  ZHI_YA: {
    id: 7,
    name: '质押',
    isShowNum: true,
    key: 'defectsPledgeEndorsement',
  },
  BAO_ZHENG: {
    id: 8,
    name: '保证',
    key: 'defectsPromise'
  },
  SHANG_XIA_BU_YI_ZHI: {
    id: 9,
    name: '不一致',
    key: 'defectsInconformity',
  },
  OTHER: {
    id: 10,
    name: '其他',
    key: 'defectsOther',
    descKey: 'defectsOtherDesc',
    isShowNum: true,
  },
  ZI_YOU_HU: {
    id: 11,
    name: '自有户',
  },
  BAO_ZHENG_FRONT: {
    id: 12,
    name: '保证（正面）',
  },
  BAO_ZHENG_BACK: {
    id: 13,
    name: '保证（背面）',
  },
  BAO_ZHENG_DAI_QIAN_SHOU: {
    id: 14,
    name: '保证待签收',
    notAllowedToPublish: true,
  },
  TE_SHU_ZI_FU_FRONT: {
    id: 15,
    name: '特殊字符（正面）'
  },
  TE_SHU_ZI_FU_BACK: {
    id: 16,
    name: '特殊字符（背面）'
  },
  ZHUANG_RANG_BEI_SHU_BANK: {
    id: 100,
    name: '转让背书银行',
    notAllowedToPublish: true,
  },
  BU_KE_ZHUANG_RANG: {
    id: 101,
    name: '不可转让',
    notAllowedToPublish: true,
  },
  YIN_PIAO_QI_YE_CHENG_DUI: {
    id: 102,
    name: '银票企业承兑',
  },
  SHANG_PIAO_YIN_HANG_CHENG_DUI: {
    id: 103,
    name: '商票银行承兑',
    notAllowedToPublish: true,
  },
  DAI_KAI_PIAO: {
    id: 104,
    name: '代开票',
  },
  TE_SHU_ZI_FU: {
    id: 105,
    name: '特殊字符',
  },
  BEI_SHU_BU_LIAN_XU: {
    id: 106,
    name: '背书不连续'
  },
  HUI_GOU_TIE_XIAN: {
    id: 21,
    name: '回购式贴现',
    key: 'defectsRepoDiscount',
    isShowNum: true,
  }
})

// 票据瑕疵类型 id 映射 对象
export const BACK_DEFECT_TYPE_ID_MAP = getIdMap(BACK_DEFECT_TYPE_NAME_MAP)
// 票据瑕疵类型 id 映射 名称
export const BACK_DEFECT_TYPE_VALUE_MAP = getKeyToValueMap(BACK_DEFECT_TYPE_NAME_MAP)
// 票据瑕疵类型 id 映射 isShowNum 是否显示数字
export const BACK_DEFECT_TYPE_SHOW_NUM_MAP = getKeyToValueMap(BACK_DEFECT_TYPE_NAME_MAP, 'id', 'isShowNum')
// 票据瑕疵类型 id 映射 notAllowedToPublish 是否可发布
export const BACK_DEFECT_TYPE_NOT_ALLOW_NUM_MAP = getKeyToValueMap(BACK_DEFECT_TYPE_NAME_MAP, 'id', 'notAllowedToPublish')
// 票据瑕疵类型 id 映射 key
export const BACK_DEFECT_TYPE_KEY_MAP = getKeyToValueMap(BACK_DEFECT_TYPE_NAME_MAP, 'id', 'key')

// 订单详情-交易状态
// export const TRANSACTION_STATUS = Object.freeze({
//   WAITING_TRANSACTION: {
//     id: 11,
//     name: '未交易'
//   },
//   WAITING_CONFIRM: {
//     id: 12,
//     name: '待确认'
//   },
//   WAITING_PAY: {
//     id: 13,
//     name: '待支付'
//   },
//   PAYING: {
//     id: 14,
//     name: '支付中'
//   },
//   WAITING_ENDORSE: {
//     id: 15,
//     name: '待背书'
//   },
//   WAITING_SIGN: {
//     id: 16,
//     name: '待签收'
//   },
//   CHECKING: {
//     id: 17,
//     name: '校验中'
//   },
//   DEAL_COMPLETED: {
//     id: 18,
//     name: '交易完成'
//   },
//   CONFIRMATION_SALE_CANCELLED: {
//     id: 21,
//     name: '确认阶段持票方已取消'
//   },
//   CONFIRMATION_BUY_CANCELLED: {
//     id: 22,
//     name: '确认阶段接单方已取消'
//   },
//   CONFIRMATION_PLATFORM_CANCELLED: {
//     id: 23,
//     name: '确认阶段平台已取消'
//   },
//   PAYMENT_SALE_CANCELLED: {
//     id: 24,
//     name: '支付阶段持票方已取消'
//   },
//   PAYMENT_BUY_CANCELLED: {
//     id: 25,
//     name: '支付阶段接单方已取消'
//   },
//   PAYMENT_PLATFORM_CANCELLED: {
//     id: 26,
//     name: '支付阶段平台已取消'
//   },
//   ENDORSEMENT_SALE_CANCELING: {
//     id: 27,
//     name: '背书阶段持票方取消中'
//   },
//   ENDORSEMENT_SALE_CANCELLED: {
//     id: 28,
//     name: '背书阶段持票方已取消'
//   },
//   ENDORSEMENT_PLATFORM_CANCELLED: {
//     id: 29,
//     name: '背书阶段平台已取消'
//   },
//   SUBMISSION_BUY_CANCELING: {
//     id: 30,
//     name: '签收阶段接单方取消中'
//   },
//   SUBMISSION_BUY_CANCELLED: {
//     id: 31,
//     name: '签收阶段接单方已取消'
//   },
//   SUBMISSION_SALE_CANCELLED: {
//     id: 32,
//     name: '签收阶段持票方已取消'
//   },
//   SUBMISSION_PLATFORM_CANCELLED: {
//     id: 33,
//     name: '签收阶段平台已取消'
//   },
// })

// 交易状态 id 映射 名称
// export const TRANSACTION_STATUS_VALUE_MAP = getKeyToValueMap(TRANSACTION_STATUS)

// 承兑人类型
export const ACCEPTOR_TYPE = Object.freeze({
  GUO_GU: {
    id: 1,
    name: '国股'
  },
  DA_SHANG: {
    id: 2,
    name: '大商'
  },
  CHEN_SHANG: {
    id: 3,
    name: '城商'
  },
  SAN_NONG: {
    id: 4,
    name: '三农'
  },
  CUN_ZHEN: {
    id: 5,
    name: '村镇'
  },
  WAI_ZI: {
    id: 6,
    name: '外资'
  },
  MIN_YING: {
    id: 7,
    name: '民营'
  },
  CAI_PIAO: {
    id: 8,
    name: '财票'
  },
  SHANG_PIAO: {
    id: 9,
    name: '商票'
  },
  QI_TA: {
    id: 0,
    name: '其他'
  },
  WEI_ZHI: {
    id: 99,
    name: '未知'
  }
})

// 承兑人类型 id 映射 名称
export const ACCEPTOR_TYPE_VALUE_MAP = getKeyToValueMap(ACCEPTOR_TYPE)

// 支付账户类型
export const ACCOUNT_PAY_TYPE = Object.freeze({
  ZHI_FU_YI_LIAN: {
    id: 1,
    name: '智付E'
  },
  ZHI_FU_BAI_XIN: {
    id: 2,
    name: '智付百信'
  },
  ZHI_FU_LIAN_LIAN: {
    id: 3,
    name: '智付连连'
  },
  ZHI_FU_HE_LI_BAO: {
    id: 4,
    name: '智付宝'
  },
  ZHI_FU_ZHONG_BANG: {
    id: 5,
    name: '智付邦'
  },
  YI_LIAN_YIN_HANG: {
    id: 6,
    name: '智联通'
  },
  ZHI_FU_YI_LIAN_PLUS: {
    id: 7,
    name: '智付E+'
  },
  ZHI_FU_ZHONG_BANG_PLUS: {
    id: 8,
    name: '智付邦+'
  },
  YL_PLUS: {
    id: 9,
    name: 'E++'
  }
})

// 支付账户类型 id 映射 名称
export const ACCOUNT_PAY_TYPE_VALUE_MAP = getKeyToValueMap(ACCOUNT_PAY_TYPE)

// 报价类型
export const OFFER_TYPE_CODE = {
  MEI_SHI_WAN_ZHI_KOU: 0, // 每十万直扣
  NIAN_LI_LV_JI_SUAN: 1 // 年利率计算
}

// 拆分接单类型
export const SPLIT_TYPE_CODE = {
  INTERVAL: 0, // 区间拆分
  QUOTA: 1, // 定额拆分
  DIRECT: 2, // 直接拆分
}

// 议价上限按钮数字
export const BARGAINING_BUTTON_NUMBER_CODE = {
  TEN: 10,
  TWENTY: 20,
  THIRTY: 30
}

// 识别类型
export const IDENTIFY_TYPE = Object.freeze({

  /** 单张识别 */
  SIGNAL: {
    id: 1,
    name: '单张识别',
    tagColor: 'blue',
  },

  /** 批量识别 */
  MULTIPLE: {
    id: 2,
    name: '批量识别',
    tagColor: 'purple',
  },

  /** 自动同步 */
  AUTO: {
    id: 3,
    name: '自动同步',
    tagColor: 'orange',
  },

})
// 识别类型 id 映射 名称
export const IDENTIFY_TYPE_MAP = getKeyToValueMap(IDENTIFY_TYPE)
// 识别类型 id 映射 标签颜色
export const IDENTIFY_TYPE_COLOR_MAP = getKeyToValueMap(IDENTIFY_TYPE, 'id', 'tagColor')

/** 上传文件夹 */
export const OSS_DIR = {

  /** 身份证 */
  ID_CARD: 'id_card',

  /** 营业执照 */
  BUSINESS_LICENSE: 'business_license',

  /** 授权委托书 */
  POWER_OF_ATTORNEY: 'power_of_attorney',

  /** 票据 */
  DRAFT: 'draft',

  /** 凭证 */
  INTERVENTION_VOUCHER: 'intervention_voucher',

  /** 问题反馈 */
  FEEDBACK: 'feedback'
}

/** 文件上传 oss 文件夹名称 */
export const OSS_DIR_NAME = {
  [OSS_DIR.ID_CARD]: '身份证',
  [OSS_DIR.BUSINESS_LICENSE]: '营业执照',
  [OSS_DIR.POWER_OF_ATTORNEY]: '授权委托书',
  [OSS_DIR.DRAFT]: '票据',
  [OSS_DIR.INTERVENTION_VOUCHER]: '介入凭证',
  [OSS_DIR.FEEDBACK]: '问题反馈'
}

// 黑名单类型
export const BLACKLIST_TYPE = Object.freeze({
  ACCEPTOR: {
    id: 1,
    name: '承兑人',
    tips: '（待签收状态下，承兑人名称包含自定义的黑名单中给出提示）'
  },
  PAYEE: {
    id: 2,
    name: '收款人',
    tips: '（待签收状态下，收款人包含自定义的黑名单中给出提示）'
  },
  ENDORSER: {
    id: 3,
    name: '背书人',
    tips: '（待签收状态下，背书人、被背书人、出质人、质权人、保证人、被保证人包含自定义的黑名单中给出提示）'
  },
  SENSITIVE: {
    id: 4,
    name: '敏感行业',
    tips: '（待签收状态下，出票人、收款人、背书人、被背书人、出质人、质权人、保证人、被保证人包含敏感行业关键字给出提示。）'
  },
  OWN_ACCOUNT: {
    id: 5,
    name: '自有户',
    tips: '（待签收状态下，出票人、收款人、背书人、被背书人、出质人、质权人、保证人、被保证人中出现用户自定义的自有户给出提示）'
  },
})

// 黑名单类型 id 映射 名称
export const BLACKLIST_TYPE_NAME_MAP = getKeyToValueMap(BLACKLIST_TYPE)
// 黑名单类型 id 映射 提示
export const BLACKLIST_TYPE_TIPS_MAP = getKeyToValueMap(BLACKLIST_TYPE, 'id', 'tips')

// 订单状态
export const ORDER_STATUS = Object.freeze({
  WAITING_REVIEW: {
    id: 0,
    name: '待审核'
  },
  REVIEW_OFF_SHELF: {
    id: 1,
    name: '审核下架'
  },
  NOT_LISTED: {
    id: 2,
    name: '待上架'
  },
  WAITING_LISTED: {
    id: 3,
    name: '待接单'
  },
  ON_LISTED: {
    id: 4,
    name: '接单中'
  },
  DEAL_COMPLETED: {
    id: 5,
    name: '已完成'
  },
  DEAL_FAIl: {
    id: 6,
    name: '交易失败'
  },
  ORDER_OFF_SHELF: {
    id: 7,
    name: '订单下架'
  }
})

// 订单状态 id 映射 名称
export const ORDER_STATUS_VALUE_MAP = getKeyToValueMap(ORDER_STATUS)

// 交易限制提示类型
export const TRANSACTION_TOOLTIP_TYPE = Object.freeze({
  HOLIDAY: 'holiday', // 节假日闭市
  DAILY: 'daily', // 日常闭市
  RADAR: 'radar', // 雷达接单
  FAST: 'fast', // 光速订单
  CREDIT: 'credit', // 信用分
})

// 禁用状态
export const BAN_STATUS = Object.freeze({
  ENABLE: {
    id: 0,
    name: '启用'
  },
  DISABLE: {
    id: 1,
    name: '禁用'
  }
})
