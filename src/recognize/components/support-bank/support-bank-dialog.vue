<style lang="scss" scoped>
::v-deep {
  .el-radio-group {
    display: flex;
    width: 100%;
  }

  .el-radio-button {
    flex: 1;
  }

  .el-radio-button__inner {
    width: 100%;
  }
}

.search {
  margin-top: 12px;
}

.banks {
  display: flex;
  align-content: flex-start;
  overflow-y: scroll;
  margin-top: 12px;
  padding-top: 12px;
  padding-bottom: 4px;
  padding-left: 16px;
  height: 418px;
  background-color: #FFFFFF;
  flex-wrap: wrap;

  &::-webkit-scrollbar-track-piece {
    background-color: #FFFFFF;
  }
}

.bank-item {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  margin: 0 8px 8px 0;
  border: 1px solid $color-D9D9D9;
  border-radius: 2px;
  width: 126px;
  height: 40px;

  &:last-child {
    margin-right: 0;
    margin-bottom: 12px;
  }

  &:nth-child(4n) {
    margin-right: 0;
    margin-bottom: 12px;
  }

  .bank-name {
    margin-left: 6px;
    width: 62px;
    user-select: none;

    @include ellipsis;
  }
}

.empty-tips {
  padding-top: 12px;
  padding-bottom: 20px;
  width: 100%;
  text-align: center;
  color: $color-text-secondary;
}
</style>

<template>
  <el-dialog
    title="支持银行"
    :visible.sync="dialogVisible"
    width="600px"
    append-to-body
  >
    <div class="bank-adaptation-content">
      <el-radio-group v-model="currTab" size="medium">
        <!-- 18065:下线ECDS票据相关内容 -->
        <el-radio-button label="newDraft">识别票据支持银行</el-radio-button>
        <el-radio-button label="autoSync">自动同步支持银行</el-radio-button>

        <!--
          <el-radio-button label="default">识别票据支持银行</el-radio-button>
          <el-radio-button label="autoSync">自动同步支持银行</el-radio-button>
          <el-radio-button label="newDraft">新一代票据支持银行</el-radio-button>
        -->
      </el-radio-group>
      <div class="search">
        <el-input
          v-model="searchKeyword"
          prefix-icon="el-icon-search"
          :placeholder="`点击搜索目标银行（已支持${visibleBanks.length}家）`"
          clearable
        />
      </div>
      <div class="banks">
        <template v-if="filteredBanks.length > 0">
          <div
            v-for="item in filteredBanks"
            :key="item.id"
            class="bank-item"
            @click="() => { onClick(item.bankUrl) }"
          >
            <el-image
              class="bank-icon"
              style="width: 24px; height: 24px;"
              :src="item.iconPath"
            />
            <el-tooltip>
              <div slot="content">
                <span>{{ item.bankName }}</span>
                <span v-if="item.specialIllustration">：</span>
                <br>
                <span v-if="item.specialIllustration">{{ item.specialIllustration }}</span>
              </div>
              <span class="bank-name">{{ item.bankName }}</span>
            </el-tooltip>
          </div>
        </template>
        <div v-else class="empty-tips">
          <span>如未找到目标银行，请联系客户经理</span>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import commonApi from '@recognize/apis/common'
import { OPEN_URL_IN_DEFAULT_BROWSER } from '@recognize/ipc-event-constant'

export default {
  name: 'support-bank',

  data() {
    return {
      dialogVisible: false,
      currTab: 'newDraft',
      searchKeyword: '',
      bankList: null,
      defaultBanks: [],
      autoSyncBanks: [],
      newDraftBanks: []
    }
  },

  computed: {
    visibleBanks() {
      switch (this.currTab) {
        case 'default':
          return this.defaultBanks
        case 'autoSync':
          return this.autoSyncBanks
        case 'newDraft':
          return this.newDraftBanks
        default:
          return []
      }
    },
    filteredBanks() {
      return this.visibleBanks.filter(bank => bank.bankName.indexOf(this.searchKeyword) > -1)
    }
  },

  methods: {
    open() {
      this.dialogVisible = true
      if (!this.bankList) {
        this.getData()
      }
    },
    async getData() {
      let res = await commonApi.getSupportBank()
      this.bankList = res
      this.defaultBanks.length = 0
      for (let item of res) {
        if (item.ecdsSingleDiscern || item.ecdsBatchDiscern || item.ecdsAutoSync || item.ecdsChromeMode) {
          this.defaultBanks.push(item)
          if (item.ecdsAutoSync) {
            this.autoSyncBanks.push(item)
          }
        }
        if (item.cpesSingleDiscern || item.cpesBatchDiscern || item.cpesAutoSync || item.cpesChromeMode) {
          this.newDraftBanks.push(item)
        }
      }
    },
    // 默认打开 弹窗
    onClick(url) {
      if (!url) {
        return
      }
      if (this.$ipc) {
        this.$ipc.send(OPEN_URL_IN_DEFAULT_BROWSER, url)
      } else {
        window.open(url)
      }
    },
  },
}
</script>
