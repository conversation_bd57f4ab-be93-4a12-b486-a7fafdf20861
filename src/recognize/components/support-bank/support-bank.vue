<style lang="scss" scoped>
.support-bank {
  padding: 26px 0;
  width: 100%;
  flex-shrink: 0;

  .support-bank-warp {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .support-bank-tips {
    margin-bottom: 4px;
    font-size: 14px;
    text-align: center;
    color: $color-text-secondary;
  }

  .support-bank-title {
    font-size: 16px;
    color: $color-text-secondary;
  }

  .support-bank-img {
    margin-right: 8px;

    i {
      margin-right: 12px;

      &:last-child {
        margin-right: 7px;
      }
    }
  }

  .support-bank-more {
    margin-right: 9px;
    font-size: 20px;
    color: $color-text-secondary;
  }
}
</style>

<template>
  <div class="support-bank">
    <div class="support-bank-tips">仅展示90天内识别的记录，您可在票据库存中查询更多</div>
    <div class="support-bank-warp">
      <div class="support-bank-title">支持银行：</div>
      <div class="support-bank-img">
        <el-image
          v-for="item in discernBank"
          :key="item.bankName"
          style="margin: 0 8px;width: 32px; height: 32px;"
          :src="item.iconPath"
        />
        <!--
          <icon type="chengjie-jianshe-bank-gray" :size="32" />
          <icon type="chengjie-zhongguo-bank-gray" :size="32" />
          <icon type="chengjie-nongye-bank-gray" :size="32" />
          <icon type="chengjie-zhongxin-bank-gray" :size="32" />
          <icon type="chengjie-zhaoshang-bank-gray" :size="32" />
        -->
      </div>
      <div class="support-bank-more">···</div>
      <div class="text-link" @click="$refs.supportBankDialog.open()">
        查看更多
      </div>
    </div>

    <SupportBankDialog ref="supportBankDialog" />
  </div>
</template>

<script>
import SupportBankDialog from './support-bank-dialog.vue'
import commonApi from '@recognize/apis/common'

export default {
  name: 'support-bank',

  components: {
    SupportBankDialog,
  },
  data() {
    return {
      discernBank: [],
    }
  },
  created() {
    this.getBankList()
  },
  methods: {
    async getBankList() {
      let res = await commonApi.getSupportBank()
      let showBankList = []
      if (res) {
        res.forEach(item => {
          if (item.cpesSingleDiscern || item.cpesBatchDiscern || item.cpesAutoSync || item.cpesChromeMode) {
            showBankList.unshift(item)
          }
        })
        this.discernBank = showBankList.splice(0, 5)
      }
    }
  }
}
</script>
