<style scoped lang="scss">
.page {
  border: none;
  width: 100%;
  height: 100%;
  background: $color-FFFFFF;
}
</style>

<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="1000px"
    append-to-body
  >
    <iframe class="page" :src="url" />
  </el-dialog>
</template>

<script>
export default {
  name: 'preview-file',

  data() {
    return {
      dialogVisible: false,
      title: '', // 窗口标题
      url: '', // 文件路径
      fileType: 'application/pdf', // 文件类型
    }
  },

  methods: {
    open(file) {
      if (!file) return
      if (typeof file === 'object') {
        this.title = file.title
        this.url = file.url
      }
      this.dialogVisible = true
    }
  }
}
</script>
