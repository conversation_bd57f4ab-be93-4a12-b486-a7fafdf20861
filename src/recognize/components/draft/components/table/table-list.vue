// 识别列表 - 表格形式
<style lang="scss" scoped>
.table-container {
  padding: 20px 16px;
  background: #FFFFFF;
}

.has-tags-column-flex {
  display: flex;
  align-items: center;
  text-align: right;

  .tags {
    display: flex;
    margin-right: 6px;
    width: 20px;
    flex-wrap: wrap;
    flex: 0 0 20px;

    .tag {
      margin-bottom: 4px;
      border: 1px solid transparent;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      font-size: 12px;
      text-align: center;
      line-height: 18px;

      &:last-child {
        margin-bottom: 0;
      }

      &.single {
        border-color: $--color-success;
        color: $--color-success;
      }

      &.batch {
        border-color: $color-9254DE;
        color: $color-9254DE;
      }

      &.auto {
        border-color: $color-AF772D;
        color: $color-AF772D;
      }
    }
  }

  .acceptor {
    line-height: 24px;
    text-align: left;

    @include ellipsis(2);
  }
}

.has-ticket-flaw {
  max-height: 48px;
  font-weight: 600;
  color: $color-warning;
  line-height: 24px;

  @include ellipsis(2);
}

.theme-color {
  color: $color-warning;
}

.no-support {
  display: block;
  overflow: hidden;
  max-width: 80px;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.g-can-not-split {
  position: absolute;
  top: 3px;
  left: 65px;
  border-radius: 10px 10px 10px 0;
  width: 48px;
  height: 20px;
  font-size: 10px;
  text-align: center;
  color: $color-FFFFFF;
  background: #F51818;
  line-height: 20px;
}

.btn-right {
  margin-left: 8px;
}

.warning-text {
  color: $color-warning;
}
</style>

<template>
  <div class="table-container">
    <el-table
      ref="tableRef"
      v-waiting="['post::loading::/discern/list']"
      :data="tableData"
      border
      @selection-change="handleSelectionChange"
    >
      <template slot="empty">
        <TableEmpty />
      </template>

      <el-table-column
        key="0"
        type="selection"
        width="40"
      />
      <el-table-column
        key="1"
        prop="discernTime"
        label="识别时间"
        min-width="118"
      >
        <template slot-scope="scope">
          <template v-if="scope.row.discernTime">
            {{ formatTime(scope.row.discernTime, 'YYYY-MM-DD') }}
            <br>
            {{ formatTime(scope.row.discernTime, 'hh:mm:ss') }}
          </template>
          <template v-else>--</template>
        </template>
      </el-table-column>
      <el-table-column
        key="2"
        label="票号后六位"
        min-width="125"
      >
        <div slot-scope="scope" class="has-tags-column-flex">
          <el-tooltip
            placement="top"
            :disabled="!scope.row.draftType"
            :content="`子票区间：${scope.row.subTicketStart || ''} - ${scope.row.subTicketEnd || ''}`"
          >
            <div>{{ scope.row.lastSixDraftNo || String(scope.row.draftNo).substring(String(scope.row.draftNo).length - 6) }}</div>
            <!-- <div><span class="g-xinpiao">新票</span></div> -->
          </el-tooltip>
        </div>
      </el-table-column>
      <el-table-column
        key="3"
        label="承兑人"
        width="160"
        class-name="has-tags-column acceptor-column"
      >
        <div slot-scope="scope" class="has-tags-column-flex">
          <div class="tags">
            <span v-if="scope.row.discernType === 1" class="tag single">单</span>
            <span v-else-if="scope.row.discernType === 2" class="tag batch">批</span>
            <span v-else-if="scope.row.discernType === 3" class="tag auto">同</span>
          </div>
          <el-tooltip
            popper-style="max-width: 362px;"
            placement="top"
            :content="scope.row.acceptorName"
          >
            <div class="acceptor two-row-hidden">{{ scope.row.acceptorName }}</div>
          </el-tooltip>
        </div>
      </el-table-column>
      <el-table-column
        key="4"
        prop="draftAmount"
        label="票面金额(万)"
        min-width="130"
        class-name="bold large-font-column"
      >
        <template slot-scope="scope">
          {{ yuan2wan(scope.row.draftAmount) }}
        </template>
      </el-table-column>
      <el-table-column
        key="5"
        prop="interestDays"
        label="到期日"
        min-width="118"
      >
        <template slot-scope="scope">
          <div>
            <p>
              {{ scope.row.maturityDate ? formatTime(scope.row.maturityDate, 'YYYY-MM-DD') : '-' }}
            </p>
            <p v-if="scope.row.interestDays > 0">剩余{{ scope.row.interestDays }}天</p>
            <p v-else class="theme-color">已过期</p>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        key="7"
        prop="discernSource"
        label="所在银行"
        min-width="130"
        class-name="bold"
      />
      <el-table-column
        key="8"
        prop="ticketAmount"
        label="瑕疵"
        min-width="116"
      >
        <div slot-scope="scope">
          <el-tooltip v-if="scope.row.discernType === 2" position="top" content="批量识别不支持瑕疵识别">
            <span class="no-support">不支持瑕疵识别</span>
          </el-tooltip>
          <DefectsTooltip v-else-if="scope.row.originalDefects" :draft-discern-id="String(scope.row.draftDiscernId)" :original-defects="scope.row.originalDefects" />
          <div v-else class="ticket-flaw">无瑕疵</div>
        </div>
      </el-table-column>
      <el-table-column
        key="9"
        label="背书手数"
        min-width="90"
      >
        <template slot-scope="scope">
          <span v-if="!scope.row.endorseCount && scope.row.endorseCount !== 0" class="endorse-number">--</span>
          <span class="theme-color">{{ scope.row.endorseCount }}</span>
          <PreSignEndorseTooltip style="margin-left: 8px;" :detail="scope.row" />
        </template>
      </el-table-column>
      <el-table-column
        key="20"
        prop="interestDaysSort"
        label="最后一手背书"
        min-width="120"
      >
        <template slot-scope="scope">
          {{ scope.row.lastEndorse }}
        </template>
      </el-table-column>
      <el-table-column
        key="21"
        prop="interestDaysSort"
        label="风险"
        min-width="150"
      >
        <template slot-scope="scope">
          <DefectsNotifyTooltip :detail="scope.row.defectsNotifyTemp" type="list" />
        </template>
      </el-table-column>
      <el-table-column
        key="12"
        label="操作"
        fixed="right"
        :width="150"
        class-name="handle-column"
      >
        <template slot-scope="scope">
          <!-- 票面 -->
          <el-button type="text" @click="openTicketDetail(scope.row.draftDiscernId)">查看票面</el-button>
          <!-- 一键发布 -->
          <el-button
            v-if="!scope.row.inRelease"
            type="text"
            class="btn-right"
            :disabled="scope.row.notAllow"
            @click=" issueDraft(scope.row)"
          >
            <span v-if="!scope.row.notAllow">一键发布</span>
            <el-tooltip v-else popper-class="not-allow-tip-custom" placement="top">
              <div v-if="!scope.row.riskCompany" slot="content">
                <p>「保证待签收」「转让背书银</p>
                <p>行」「不可转让」「商票银行</p>
                <p>承兑」「已过期」「黑名单」</p>
                <p>的票据不可发布</p>
              </div>
              <div v-else slot="content" style="width: 200px;">
                该票背书链中有
                <span v-if="scope.row.riskCompany === 1 || scope.row.riskCompany === 3" class="warning-text">异常准入企业</span>
                <span v-if="scope.row.riskCompany === 1 || scope.row.riskCompany === 3">，</span>
                <span v-if="scope.row.riskCompany === 2 || scope.row.riskCompany === 3" class="warning-text">违反软件安全规则企业</span>
                <span v-if="scope.row.riskCompany === 2 || scope.row.riskCompany === 3">，</span>
                不允许发布！鼠标移至 “风险” 位置可查看详情。
              </div>
              <span>一键发布</span>
            </el-tooltip>
          </el-button>
          <el-button
            v-else
            type="text"
            class="btn-right"
            :disabled="true"
          >
            <span>已发布</span>
          </el-button>
          <span v-if="scope.row.draftType && scope.row.subTicketEnd === '0'" class="g-can-not-split">不可分包</span>
        </template>
      </el-table-column>
    </el-table>
    <div class="footer-pagination">
      <el-pagination
        :current-page.sync="query.pageNum"
        :page-size="query.pageSize"
        background
        layout="total, prev, pager, next, sizes, jumper"
        :total="totalRecord"
        :page-sizes="[10, 20, 30, 50]"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import TableEmpty from './table-empty.vue' // 缺省页面
import { yuan2wan } from '@/common/js/number'
import {
  isOverdue,
  formatTime, // 格式化时间
} from '@/common/js/date'
import DefectsNotifyTooltip from '../defects-notify-tooltip.vue'
import {
  toDefectsNotifyTempNew,
  toDefectStr // 将原始瑕疵字符串转为渲染字符串
} from '@/common/js/draft-flaw'
import ticketApi from '@recognize/apis/ticket'
import {
  BACK_DEFECT_TYPE_NOT_ALLOW_NUM_MAP, // 票据瑕疵类型 id 映射 notAllowedToPublish 是否可发布
} from '@recognize/constant'
import DefectsTooltip from '@recognize/components/draft/components/defects-tooltip.vue'
import PreSignEndorseTooltip from '../pre-sign-endorse-tooltip'

// 默认查询参数
const defaultQuery = {
  pageSize: 10,
  pageNum: 1,
}

export default {
  name: 'draft-table',

  components: {
    TableEmpty,
    DefectsNotifyTooltip,
    DefectsTooltip,
    PreSignEndorseTooltip
  },

  props: {
    // 查询参数
    searchObj: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: true, // 加载状态
      query: Object.assign({}, defaultQuery), // 请求参数
      tableData: [], // 表格数据
      totalRecord: 0, // 数据总条数
    }
  },
  created() {
    this.getList()
  },
  methods: {
    yuan2wan,
    toDefectStr,
    formatTime,
    // 发布票据
    issueDraft(data) {
      this.$emit('issue-draft', data)
    },
    // 点击票面
    openTicketDetail(id) {
      this.$emit('open-ticket-detail', id)
    },
    // 获取列表数据
    async getList() {
      this.loading = true
      const data = await ticketApi.getDraftList({
        ...this.query,
        ...this.searchObj,
      })
      data.rowList.forEach(item => {
        // item.draftJson = item.draftJson ? JSON.parse(item.draftJson) : {}
        item.selected = false // 是否已勾选
        item.defectsNotifyTemp = item.defectsNotify ? toDefectsNotifyTempNew(item.defectsNotify, item.fontSelfOwn, item.backSelfOwn) : []
        item.riskCompany = this.fetchRiskCompany(item.defectsNotify) // 是否检测到风险企业
        item.notAllow = this.validErrorNotAllow(item.defectsNotify) || this.validDeffectNotAllow(item.originalDefects) || isOverdue(item.maturityDate) || !!item.inRelease || !!item.riskCompany || !!item.inRiskBlack
      })
      this.loading = false
      this.totalRecord = data.totalRecord
      this.tableData = data.rowList
      this.$emit('api-loaded')
    },
    // 重置查询
    reset() {
      this.query = Object.assign({}, defaultQuery)
      this.getList()
    },
    // 更改每页条数
    handleSizeChange(val) {
      this.query.pageSize = val
      this.query.pageNum = 1
      this.getList()
    },
    // 更改当前页
    handleCurrentChange(val) {
      this.query.pageNum = val
      this.getList()
    },
    // 多选回调
    handleSelectionChange(val) {
      this.$emit('selection-change', val)
    },
    // 是否含有不允许发布的票据异常
    validErrorNotAllow(defectsNotify) {
      if (!defectsNotify) {
        return false
      }
      defectsNotify = JSON.parse(defectsNotify)
      return Array.isArray(defectsNotify.riskCorps) && defectsNotify.riskCorps.length > 0
    },
    // 校验不允许发布的瑕疵
    validDeffectNotAllow(originalDefects) {
      if (!originalDefects) {
        return false
      }
      const originalDefectList = originalDefects.split('|').filter(item => item)
      const notAllow = originalDefectList.some(defect => {
        const type = defect.split('_')[0]
        return BACK_DEFECT_TYPE_NOT_ALLOW_NUM_MAP[type]
      })
      return notAllow
    },
    fetchRiskCompany(defectsNotify) {
      let riskCompany = 0
      if (!defectsNotify) {
        return riskCompany
      }
      defectsNotify = JSON.parse(defectsNotify)
      if (Array.isArray(defectsNotify.abnormalAccessRiskCorps) && defectsNotify.abnormalAccessRiskCorps.length > 0) {
        riskCompany = 1
      }
      if (Array.isArray(defectsNotify.violateSoftwareSecurityCorps) && defectsNotify.violateSoftwareSecurityCorps.length > 0) {
        riskCompany += 2
      }
      return riskCompany
    }
  },
}
</script>
