<!-- 暂无数据组件 -->
<style lang="scss" scoped>
.no-data {
  margin: 0 auto;
  padding-top: 80px;
  padding-bottom: 24px;
  font-size: 14px;
  text-align: center;
  color: $color-text-secondary;
}

.no-data-tip {
  margin: -45px 0 32px;
}

.no-data-highlight {
  font-weight: 600;
  color: $--color-primary;
}

.no-data-issue {
  margin-top: 30px;
  margin-left: 85px;
  text-align: left;
}

.no-data-identify {
  margin: auto;
  padding-bottom: 20px;
  width: 388px;
  text-align: left;
}

.no-data-tip-title {
  position: relative;
  font-size: 20px;
  font-weight: 600;
  color: $color-text-primary;
}

.no-data-tip-item {
  position: relative;
  margin-top: 6px;
  font-size: 16px;
  font-weight: 400;
  text-indent: 24px;
  color: $color-text-secondary;

  &::before {
    position: absolute;
    top: 2px;
    left: 0;
    border: 1px solid $--color-primary;
    border-radius: 50%;
    width: 14px;
    height: 14px;
    content: "";
  }

  &::after {
    position: absolute;
    top: 5px;
    left: 3.2px;
    border-radius: 50%;
    width: 10.2px;
    height: 10.2px;
    background: $--color-primary;
    content: "";
  }
}

.no-data-tip-item-first {
  margin-top: 15px;
}
</style>

<template>
  <div class="no-data">
    <img
      src="https://oss.chengjie.red/web/imgs/recognize/recognition-no-data-hn.png"
      width="338"
      height="149"
      alt="暂无数据"
    >
    <div class="no-data-tip">
      当前没有数据，<span class="text-link" @click="refresh">点击刷新</span>
      <div>仅展示90天内识别的记录，您可在票据库存中查询更多</div>
    </div>

    <div class="no-data-identify">
      <div class="no-data-tip-title">您可以</div>
      <div class="no-data-tip-item no-data-tip-item-first">
        点击<span class="no-data-highlight">「单张识别」</span>开始识票
      </div>
      <div class="no-data-tip-item">
        点击<span class="no-data-highlight">「批量识别」</span>来识别网银列表票据并批量发布
      </div>
      <div class="no-data-tip-item">
        点击<span class="no-data-highlight">「自动同步」</span>来自动同步网银里的票据
      </div>
      <div class="no-data-tip-item">
        点击<span class="no-data-highlight">「票据库存」</span>来管理和分享票据
      </div>
    </div>

    <HomeBanner />
  </div>
</template>

<script>
import HomeBanner from '@recognize/components/home-banner/home-banner.vue'
import { RECOFNITION_LISR_REFRESH } from '@recognize/event/modules/ticket' // 识别列表刷新事件

export default {
  name: 'no-data',

  components: {
    HomeBanner,
  },

  methods: {
    // 点击刷新
    refresh() {
      // 全局列表刷新事件
      this.$message.success('已刷新！')
      this.$event.emit(RECOFNITION_LISR_REFRESH)
    }
  }
}
</script>
