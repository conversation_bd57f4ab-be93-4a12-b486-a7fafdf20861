<style lang="scss" scoped>
.e-bank-merge {
  position: relative;
  display: flex;
  justify-content: left;
  align-items: center;
  border-color: $color-F4F5F6 !important;
  border-radius: 4px;
  padding: 0 !important;
  width: 174px;

  &-left {
    position: absolute;
    left: 0;
    z-index: 1;
    border: 1px solid $--color-primary;
    border-radius: 4px;
    width: 100%;
    height: 48px;
    text-align: center;
    line-height: 48px;
    box-sizing: border-box;
    cursor: pointer;

    &--all {
      border-radius: 4px;
    }

    &:hover {
      background: $--color-primary;

      .operating-title {
        color: $color-FFFFFF;
      }
    }
  }

  &-right {
    position: absolute;
    right: 0;
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid $--color-primary;
    border-radius: 0 4px 4px 0;
    min-width: 28px;
    max-width: 40px;
    height: 48px;
    color: $--color-primary;
    background: white;
    flex-direction: column;

    &:hover {
      color: $color-FFFFFF;
      background: $--color-primary;
    }

    .spicon-sync {
      font-size: 22px;
      font-weight: 500;
    }

    .number {
      width: 100%;
      font-size: 14px;

      @include ellipsis;
    }

    &-active {
      border-color: $color-008489;
      color: $color-008489;
      background: $color-FFFFFF;

      &:hover {
        color: $color-FFFFFF;
        background: $color-008489;
      }
    }
  }

  .operating-title {
    display: inline-flex;
    align-items: center;
    margin-bottom: 3px;
    font-size: 20px;
    font-weight: 500;
    color: $--color-primary;
  }

  .operating-tip {
    font-size: 13px;
    color: $color-text-secondary;
  }

  .operating-icon {
    margin-right: 4px;
    vertical-align: text-top;
  }
}
</style>

<template>
  <div class="e-bank-merge">
    <div class="e-bank-merge-left" :class="!syncedBank.length && 'e-bank-merge-left--all'" @click="clickEBankMerge">
      <p class="operating-title">
        <icon class="operating-icon" type="chengjie-sync" :size="24" />
        <span>自动同步</span>
      </p>
    </div>
    <div
      v-show="syncedBank.length"
      class="e-bank-merge-right"
      @click="openSyncedBankWindow"
    >
      <div class="number">{{ syncedBank.length }}</div>
    </div>

    <SyncedBank ref="syncedBankRef" />
  </div>
</template>

<script>
import {
  MARKET_DRAFT_RECOGNIZE_SHOW_LAYER, // 显示票据识别遮罩层
  MARKET_DRAFT_ADD_MONITOR_BANK, // 网银识别，增加需要监控的IE窗口
  MARKET_DRAFT_GET_MONITOR_BANK_SYNCED_LIST, // 网银识别，获取所有正在监控的网银公司
  MARKET_DRAFT_GET_NEW_BILL_LIST, // 网银识别，获取新增的票据列表
  MARKET_DRAFT_ADD_MONITOR_BANK_SUCCESS, // 网银识别，增加识别成功的银行
  MARKET_DRAFT_GET_BANK_SYNC_DATAS, // 网银识别，获取单张票据正背面信息
  MARKET_DRAFT_DELETE_BANK_SYNC, // 网银识别，删除网银公司
} from '@recognize/ipc-event-constant'
import { OPEN_USAGE_NOTICE } from '@recognize/event/modules/account' // 打开使用须知
import SyncedBank from '@recognize/components/synced-bank/synced-bank.vue'
import toolsApi from '@recognize/apis/tools'
import ticketApi from '@recognize/apis/ticket'
import { RECOFNITION_LISR_REFRESH } from '@recognize/event/modules/ticket' // 识别列表刷新事件

const IDENTIFY_MODE = {
  C: 0, // C++解析
  C_HTML: 1, // C++传输HTML
}

export default {
  name: 'e-bank-merge',

  components: {
    SyncedBank,
  },

  data() {
    return {
      syncedBank: [], // 识别到的银行
      firstRecogny: true, // 是否第一次开始循环网银同步识别
      canRecogny: true, // 是否可以开始循环网银同步识别
      intervalTime: 15000, // 定时调用dll时间
      monitorBankTime: null, // 监控公司定时器
      billTime: null, // 监控票据定时器
      billNum: [], // 新增票号存储
    }
  },

  created() {
    if (this.$ipc) {
      this.$ipc.on(MARKET_DRAFT_ADD_MONITOR_BANK, this.addMonitorBank)
      this.$ipc.on(MARKET_DRAFT_GET_MONITOR_BANK_SYNCED_LIST, this.getMonitorBankList)
      this.$ipc.on(MARKET_DRAFT_GET_NEW_BILL_LIST, this.getBillList)
      this.$ipc.on(MARKET_DRAFT_DELETE_BANK_SYNC, this.removeMonitorBank)
    }
  },

  beforeDestroy() {
    if (this.$ipc) {
      this.$ipc.removeListener(MARKET_DRAFT_ADD_MONITOR_BANK, this.addMonitorBank)
      this.$ipc.removeListener(MARKET_DRAFT_GET_MONITOR_BANK_SYNCED_LIST, this.getMonitorBankList)
      this.$ipc.removeListener(MARKET_DRAFT_GET_NEW_BILL_LIST, this.getBillList)
      this.$ipc.removeListener(MARKET_DRAFT_DELETE_BANK_SYNC, this.removeMonitorBank)
    }
  },

  methods: {
    // 点击网银同步
    clickEBankMerge() {
      if (!this.$store.state['recognize-common'].isAgreeUsageNotice) {
        this.$event.emit(OPEN_USAGE_NOTICE)
        this.$emit('set-can-short-cut')
        return
      }
      if (!this.$ipc) {
        this.$message.warning('请在客户端内进行识别操作')
        this.$emit('set-can-short-cut')
        return
      }
      if (navigator.platform.indexOf('Win') === -1) {
        this.$message.error('当前系统不支持该功能！')
        this.$emit('set-can-short-cut')
        return
      }
      this.clearTime()
      this.$ipc.send(MARKET_DRAFT_RECOGNIZE_SHOW_LAYER, '', 1)
    },
    // 打开已同步银行窗口
    openSyncedBankWindow() {
      if (!this.$ipc) {
        this.$message.warning('请在客户端内进行识别操作')
        return
      }
      this.$refs.syncedBankRef.open(this.syncedBank)
    },
    // 获取新增的网银/窗口
    addMonitorBank(_, data) {
      this.$emit('set-can-short-cut')
      if (!data) {
        this.$message.error('取消同步')
        this.restart()
        return
      }
      const { code, infos, msg } = data
      const { bankType, companyName } = infos
      if (code === -1) {
        this.$emit('identify-tip')
        this.restart()
      } else if (code === 0) {
        const isRepeat = this.syncedBank.some(item => item.companyName === companyName && item.bankType === bankType)
        if (isRepeat) {
          this.$message.warning('请勿重复添加！')
          this.restart()
          return
        }
        this.syncedBank.push({ companyName, bankType, switch: true })
        this.$message.success(`已经成功与${bankType}同步`)
        this.$ipc.send(MARKET_DRAFT_ADD_MONITOR_BANK_SUCCESS, this.syncedBank)
        if (this.firstRecogny) {
          this.firstRecogny = false
          this.canRecogny = true
          this.intervalTime = 0
          this.setTimeoutMonitorBankSyncList()
          this.setTimeoutNewBillList()
          this.intervalTime = 15000
        } else {
          this.restart()
        }
      } else if (code === -3) {
        this.$message.warning('暂不支持该银行，可在设置→问题反馈中提交银行适配需求')
      } else {
        this.$message.error(msg || '同步失败，请重试')
      }
    },
    // 获取到所有的公司列表
    getMonitorBankList(_, data) {
      if (this.syncedBank.length && data.legth !== this.syncedBank.length) {
        let libData = data.map(item => `${item.bankType}-${item.companyName}`)
        let syncedBank = this.syncedBank.map(item => `${item.bankType}-${item.companyName}`)
        let delSyncedBank = syncedBank.filter(item => !libData.includes(item))
        this.syncedBank.forEach((item, index) => {
          delSyncedBank.forEach(delItem => {
            if (`${item.bankType}-${item.companyName}` === delItem) {
              this.syncedBank.splice(index, 1)
              this.$message.warning(`${item.bankType}已掉线，请重新同步`)
            }
          })
        })
        this.$ipc.send(MARKET_DRAFT_ADD_MONITOR_BANK_SUCCESS, this.syncedBank)
      }
      this.restart()
    },
    // 获取到的新增票据号
    async getBillList(_, data) {
      try {
        if (!Array.isArray(data) || data.length === 0) return

        const diffData = data.filter(add => !this.billNum.find(old => add.billNum === old.billNum)) // 去重
        if (diffData.length === 0) return // 没有新增票据直接返回

        this.clearTime() // 先暂停同步

        const cLibData = { infos: [] } // C++ 识别返回
        const remoteData = [] // 后端识别
        for (let item of diffData) {
          this.billNum.push({ billNum: item.billNum, companyName: item.companyName })

          const billDetail = await this.$ipc.invoke(MARKET_DRAFT_GET_BANK_SYNC_DATAS, { companyName: item.companyName, ticketNum: item.billNum })
          if (billDetail.code !== 0) continue
          if (billDetail.html === '') continue
          if (billDetail.html) {
            remoteData.push(billDetail)
            continue
          }
          if (billDetail?.infos?.[0]?.front) cLibData.infos.push(...billDetail.infos)
        }

        const discernIds = []
        if (cLibData.infos.length > 0) {
          const ids = await this.postJavaAnalysis({ identifyMode: IDENTIFY_MODE.C, ...cLibData })
          if (ids) discernIds.push(...ids)
        }
        for (let item of remoteData) {
          const ids = await this.postJavaAnalysis({ identifyMode: IDENTIFY_MODE.C_HTML, ...item })
          if (ids) discernIds.push(...ids)
        }

        if (discernIds.length > 0) {
          this.$message.success(`成功同步${discernIds.length}张票据`)
          this.$event.emit(RECOFNITION_LISR_REFRESH)
          const identifyData = await ticketApi.getRecordByDraftNo({ draftDiscernIds: discernIds })
          this.$emit('check-risks', identifyData)
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error(error) // 异常情况
      } finally {
        this.restart() // 重启同步
      }
    },
    // 定时获取当前已经发起监控的公司列表信息，看有没有掉了的公司
    setTimeoutMonitorBankSyncList() {
      if (!this.canRecogny) {
        return
      }
      this.monitorBankTime = setTimeout(() => {
        this.$ipc.send(MARKET_DRAFT_GET_MONITOR_BANK_SYNCED_LIST)
      }, this.intervalTime)
    },
    // 定时获取新增的票据列表
    setTimeoutNewBillList() {
      if (!this.canRecogny) {
        return
      }
      this.billTime = setTimeout(() => {
        this.$ipc.send(MARKET_DRAFT_GET_NEW_BILL_LIST)
      }, this.intervalTime)
    },
    // 清空定时器
    clearTime() {
      this.canRecogny = false
      this.firstRecogny = true
      clearTimeout(this.monitorBankTime)
      clearTimeout(this.billTime)
    },
    // 重新开始循环调用
    restart() {
      this.clearTime()
      if (this.syncedBank.length) {
        this.canRecogny = true
        this.setTimeoutMonitorBankSyncList()
        this.setTimeoutNewBillList()
      }
    },
    // 删除已同步的网银
    removeMonitorBank(_, data) {
      this.syncedBank = data.listData
    },
    // 自动同步票据数据提交
    async postJavaAnalysis(data = {}) {
      try {
        const api = {
          [IDENTIFY_MODE.C]: 'autoSyncParseData',
          [IDENTIFY_MODE.C_HTML]: 'JavaParseHtml',
        }[data.identifyMode]
        const params = (() => {
          if (data.identifyMode === IDENTIFY_MODE.C) {
            return {
              bankName: data.infos[0]?.bankType ?? '',
              draftJsonList: JSON.stringify(data.infos.map(item => ({
                front: item.front,
                back: item.back
              })).filter(item => item.front && item.back)),
            }
          }
          if (data.identifyMode === IDENTIFY_MODE.C_HTML) {
            return {
              bankName: data.bankName,
              bankCode: data.bankCode,
              bankUrl: data.bankUrl,
              html: data.html,
              discernType: 3
            }
          }
        })()
        const res = await toolsApi[api](params)

        if (data.identifyMode === IDENTIFY_MODE.C) {
          return res.draftDiscernIds
        }
        if (data.identifyMode === IDENTIFY_MODE.C_HTML) {
          return [res.draftDiscernId]
        }
      } catch (error) {
        // do nothing
      }
    }
  }
}
</script>
