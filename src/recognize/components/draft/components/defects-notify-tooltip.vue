<!-- 瑕疵提示 -->
<style lang="scss" scoped>
.defect-text {
  display: inline-block;
  border-radius: 120px;
  padding: 5px 12px;
  max-width: 100%;
  font-size: 16px;
  font-weight: 600;
  color: #F5222D;
  background: #FFF1F0;

  .svg-icon {
    color: #F5222D;
  }

  &.noBg {
    margin: 0;
    padding: 0;
    font-size: 14px;
    font-weight: normal;
    color: $color-text-primary;
    background: rgba(0 0 0 / 0%);
  }
}
</style>

<style lang="scss" >
.defects-notify-tooltip {
  z-index: 9999 !important;
  max-height: 98vh;

  .tip-out {
    overflow-y: scroll;
    max-height: 95vh;

    &::-webkit-scrollbar {
      width: 0 !important;
      height: 0;
    }
  }

  .tip-content {
    padding: 8px;

    .title {
      margin-bottom: 8px;
      height: 22px;
      font-size: 16px;
      font-weight: 600;
      vertical-align: middle;

      &-num {
        display: inline-block;
        margin-right: 8px;
        border-radius: 50%;
        width: 16px;
        height: 16px;
        font-size: 12px;
        text-align: center;
        color: $color-text-primary;
        background: $color-FFFFFF;
        line-height: 16px;
      }

      &-content {
        line-height: 22px;
      }
    }

    .effect {
      font-size: 14px;
      font-weight: 400;
      line-height: 24px;
    }

    & + .tip-content {
      border-top: 1px solid $color-FFFFFF;
    }
  }
}
</style>

<template>
  <el-tooltip
    v-if="(detail && detail.length > 0) || type !== 'card'"
    popper-class="defects-notify-tooltip"
    placement="top-start"
    :popper-style="{'max-width': '362px'}"
  >
    <span v-if="detail && detail.length > 0" class="defect-text g-ellipsis" :class="type !== 'card' && 'noBg'">
      <icon
        type="chengjie-exclamation-circle"
        :size="type !== 'card' ? 16 : 18"
      />

      {{ defect }}
    </span>
    <span v-else-if="type !== 'card'">--</span>

    <div slot="content">
      <div class="tip-out">
        <div v-for="item, index in detail" :key="item.id" class="tip-content">
          <p class="title">
            <span class="title-num">{{ index + 1 }}</span>
            <span class="title-content">{{ item.title }}</span>
          </p>
          <template v-if="item.key === 'endorseDefect'">
            <div :class="['effect', item.key]">
              <span v-for="outter, oindex in item.list" :key="outter.id" :style="outter.style">
                <span v-for="inner in outter" :key="inner.id" :style="inner.style">
                  {{ inner.content }}
                </span>
                {{ oindex === item.list.length - 1 ? '' : '；' }}
              </span>
            </div>
          </template>
          <template v-else-if="item.key === 'riskCorps'">
            <div class="effect">
              <p v-for="corp in item.list" :key="corp">{{ corp }}</p>
            </div>
          </template>
          <template v-else>
            <p :class="['effect', item.key]">{{ item.list && item.list.join('；') }}</p>
          </template>
        </div>
      </div>
    </div>
  </el-tooltip>
</template>

<script>
export default {
  name: 'defects-notify-tooltip',

  props: {
    detail: {
      type: Array,
      default: () => ([])
    },
    type: {
      type: String,
      default: 'card'
    }

  },

  data() {
    return {
    }
  },
  computed: {
    defect() {
      const res = Object.keys(this.detail)?.map(key => this.detail[key].title)
        ?.join('，')
      return res || ''
    }
  },

  methods: {
  }
}
</script>
