<style lang="scss" scoped>
.defects {
  font-weight: 600;
  color: $color-warning;
  vertical-align: top;

  @include ellipsis;
}
</style>

<style lang="scss">
.defects-tooltip {
  z-index: 9999 !important;
  max-height: 260px;

  .loading {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 362px;
    height: 100%;

    i {
      margin-bottom: 10px;
      font-size: 24px;
    }
  }

  .tip-out {
    overflow-y: scroll;
    max-height: 240px;

    &::-webkit-scrollbar {
      width: 0 !important;
      height: 0;
    }
  }

  .tip-content {
    padding: 8px;

    .title {
      margin-bottom: 8px;
      height: 22px;
      font-size: 16px;
      font-weight: 600;
      vertical-align: middle;

      &-num {
        display: inline-block;
        margin-right: 8px;
        border-radius: 50%;
        width: 16px;
        height: 16px;
        font-size: 12px;
        text-align: center;
        color: $color-text-primary;
        background: $color-FFFFFF;
        line-height: 16px;
      }

      &-content {
        line-height: 22px;
      }
    }

    .effect {
      font-size: 14px;
      font-weight: 400;
      line-height: 24px;
    }

    & + .tip-content {
      border-top: 1px solid $color-FFFFFF;
    }

    .defects-location {
      flex-shrink: 0;
      margin-left: 20px;
    }
  }
}
</style>

<template>
  <el-tooltip
    popper-class="defects-tooltip"
    placement="top-start"
    :popper-style="{'max-width': '362px'}"
  >
    <template #content>
      <div v-if="loading" class="loading">
        <i class="el-icon-loading" />
        <div>正在加载中...</div>
      </div>
      <div v-else-if="Array.isArray(defects) && defects.length > 0" class="tip-out">
        <div v-for="item, index in defects" :key="item.type" class="tip-content">
          <p class="title">
            <span class="title-num">{{ index + 1 }}</span>
            <span class="title-content">{{ item.typeDesc }}</span>
          </p>
          <div class="effect">
            <p
              v-for="(corp, corpIndex) in item.contents"
              :key="`${corp.name}${corp.date}${corpIndex}`"
              :style="{ display: 'flex', justifyContent: 'space-between' }"
            >
              <span>{{ corp.name }}</span>
              <span class="defects-location">{{ corp.date }}</span>
            </p>
          </div>
        </div>
      </div>
      <div v-else>{{ toDefectStr(originalDefects) }}</div>
    </template>
    <div class="defects" @mouseenter="getDefectsDetail">
      {{ toDefectStr(originalDefects) }}
    </div>
  </el-tooltip>
</template>

<script>
import ticketApi from '@recognize/apis/ticket'
import { toDefectStr } from '@/common/js/draft-flaw' // 将原始瑕疵字符串转为渲染字符串

export default {
  name: 'defects-tooltip',
  props: {
    draftDiscernId: String,
    originalDefects: String
  },
  data() {
    return {
      loading: true, // 是否正在加载瑕疵详情
      defects: [], // 瑕疵详情
    }
  },
  methods: {
    toDefectStr,
    async getDefectsDetail() {
      if (!this.draftDiscernId || this.defects.length > 0) return
      const { defects } = await ticketApi.getDefectsDetail({
        draftDiscernId: this.draftDiscernId,
      })
      this.defects = defects
      this.loading = false
    }
  },
}
</script>
