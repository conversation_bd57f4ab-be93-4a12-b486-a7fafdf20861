<!-- 不可分包标签 -->
<style lang="scss" scoped>
.g-can-not-split {
  display: inline-block;
  margin-left: 8px;
  border-radius: 10px;
  width: 60px;
  height: 16px;
  font-size: 12px;
  text-align: center;
  color: $color-FFFFFF;
  background: #F51818;
  line-height: 16px;
}
</style>

<template>
  <span v-if="draftInfo.draftType && draftInfo.subTicketEnd === '0'" class="g-can-not-split">不可分包</span>
</template>

<script>
export default {
  name: 'can-not-split-label',

  components: {
  },

  props: {
    draftInfo: Object, // 票据信息
  },
}
</script>
