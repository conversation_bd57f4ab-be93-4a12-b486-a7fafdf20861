<!-- 识别列表卡片 -->
<style lang="scss" scoped>
.market-draft-card {
  overflow: hidden;
  border-radius: 2px;
  padding: 0 16px;
  height: 100%;
  background: $color-FFFFFF;
  box-sizing: border-box;
}

.card-header,
.card-header-title {
  @include flex-sbc;
}

.card-header {
  border-bottom: 1px solid $color-D9D9D9;
  height: 46px;
  line-height: 46px;
}

.card-header-title {
  flex: 1;

  .time {
    font-weight: 400;
    white-space: nowrap;
    color: $color-text-secondary;
  }
}

.card-body {
  border-bottom: 1px solid $color-F0F0F0;
  padding: 10px 0;
  height: 289px;

  .card-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
  }

  .item-label {
    margin-right: 6px;
    margin-bottom: 2px;
    font-size: 14px;
    white-space: nowrap;
    color: $color-text-secondary;
    line-height: 22px;
  }

  .item-content {
    width: 0;
    font-size: 16px;
    color: $color-text-primary;
    line-height: 24px;
    flex: 1;
  }
}

.card-optating {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;
  padding: 6px 0;
}

.last-endorse {
  cursor: pointer;
}

.bold {
  font-weight: 600;
}

.deffects-row {
  font-weight: 600;
  color: $color-warning;
  vertical-align: top;

  @include ellipsis;
}

.defects-notify {
  margin: 0 2px 10px;
  font-size: 16px;
  line-height: 24px;
}

.defect-icon {
  margin-left: 5px;
  cursor: pointer;
}

.pointer {
  cursor: pointer;
}

.no-support {
  color: $color-warning;
}

.acceptor-tag {
  vertical-align: baseline;
}

.draft-no {
  display: flex;

  .draft-no-text {
    font-size: 16px;
    font-weight: 600;
    white-space: nowrap;
    color: $color-text-primary;
    cursor: pointer;
  }
}

.g-xinpiao {
  margin-left: 0;
}
</style>

<template>
  <div class="market-draft-card">
    <!-- header -->
    <header class="card-header">
      <slot name="checkbox" />
      <div class="card-header-title">
        <div style="display: flex; align-items: center;">
          <span v-if="IDENTIFY_TYPE_MAP[data.discernType]" :class="`g-tag--${IDENTIFY_TYPE_COLOR_MAP[data.discernType]}`">{{ IDENTIFY_TYPE_MAP[data.discernType].slice(0, 2) }}</span>
          <span v-if="ACCEPTOR_TYPE_VALUE_MAP[data.acceptorType]" class="g-tag--green acceptor-tag">{{ ACCEPTOR_TYPE_VALUE_MAP[data.acceptorType] }}</span>
          <!-- 18065:下线ECDS票据相关内容 -->
          <!-- <span v-if="data.draftType" class="g-xinpiao">新票</span> -->
          <CanNotSplitLabel :draft-info="data" />
        </div>
        <span class="time">{{ formatTime(data.discernTime, 'MM-DD hh:mm ') }}</span>
      </div>
    </header>
    <!-- body -->
    <section class="card-body">
      <div class="card-row">
        <div class="item-label">票号</div>
        <div class="item-content draft-no">
          <span v-copy="{value: data.draftNo, onSuccess: () => { $message.success('已复制票号到剪贴板') }, onError: () => $message.success('复制失败')}" class="draft-no-text">{{ data.draftNo }}</span>
        </div>
      </div>

      <div v-if="data.draftType" class="card-row">
        <div class="item-label">子票区间</div>
        <div class="item-content">
          <span class="item-content">{{ `${data.subTicketStart || ''} - ${data.subTicketEnd || ''}` }}</span>
        </div>
      </div>

      <div class="card-row">
        <div class="item-label">承兑</div>
        <div class="item-content">
          <span v-copy="{value: data.acceptorName, onSuccess: () => { $message.success('已复制承兑人名称到剪贴板') }, onError: () => copyError}" class="item-content bold pointer">{{ data.acceptorName }}</span>
        </div>
      </div>

      <div class="card-row">
        <div class="item-label">金额</div>
        <div class="item-content bold">{{ yuan2wan(data.draftAmount) }} 万</div>
      </div>
      <div class="card-row">
        <div class="item-label">到期</div>
        <div class="item-content">
          {{ formatTime(data.maturityDate, 'YYYY-MM-DD') }}
          {{ (+data.interestDays > 0) ? `（剩  ${data.interestDays}  天）` : '（已过期）' }}
        </div>
      </div>
      <div class="card-row">
        <div class="item-label">背书</div>
        <div class="item-content">
          <span> {{ data.discernType === 2 ? '批量识别不支持' : `${data.endorseCount || 0}手` }}</span>
          <span v-if="data.discernType !== 2 && data.lastEndorse" v-copy="{value: data.lastEndorse, onSuccess: () => { $message.success('已复制') }, onError: () => copyError}" class="item-content last-endorse">（{{ data.lastEndorse }}）</span>
          <PreSignEndorseTooltip :detail="data" />
        </div>
      </div>

      <div class="card-row">
        <div class="item-label">瑕疵</div>
        <div class="item-content">
          <span v-if="data.discernType === 2" class="no-support">批量识别不支持瑕疵识别，如需瑕疵识别，请点击单张识别</span>
          <DefectsTooltip v-else-if="data.originalDefects" :draft-discern-id="String(data.draftDiscernId)" :original-defects="data.originalDefects" />
          <span v-else class="bold">无瑕疵</span>
        </div>
      </div>
      <div v-if="data.defectsNotifyTemp && data.defectsNotifyTemp.length > 0" class="card-row">
        <div class="item-label">风险</div>
        <div class="item-content">
          <defectsNotifyTooltip :detail="data.defectsNotifyTemp" />
        </div>
      </div>
    </section>
    <div class="card-optating">
      <slot name="optating" />
    </div>

    <defects-notify-dialog ref="defectDialog" />
    <DraftPreview ref="draftPreviewRef" />
  </div>
</template>

<script>
// 时间格式化
import { formatTime, isToday } from '@/common/js/date'
// 将元为单位的金额转为以万为单位
import { yuan2wan } from '@/common/js/number'
import {
  BACK_DEFECT_TYPE_SHOW_NUM_MAP, // 票据瑕疵类型 id 映射 是否显示数字
  BACK_DEFECT_TYPE_VALUE_MAP, // 票据瑕疵类型 id 映射 名称
  IDENTIFY_TYPE_MAP, // 识别类型 id 映射 名称
  IDENTIFY_TYPE_COLOR_MAP, // 识别类型 id 映射 标签颜色
  ACCEPTOR_TYPE_VALUE_MAP, // 承兑人类型 id 映射 名称
} from '@recognize/constant'
import { toDefectStr } from '@/common/js/draft-flaw' // 将原始瑕疵字符串转为渲染字符串
import defectsNotifyDialog from '@recognize/components/defects-notify-dialog/defects-notify-dialog.vue'
import DefectsTooltip from '@recognize/components/draft/components/defects-tooltip.vue'
import defectsNotifyTooltip from './defects-notify-tooltip.vue'
import PreSignEndorseTooltip from './pre-sign-endorse-tooltip'
import CanNotSplitLabel from './can-not-split-label'
import DraftPreview from '@recognize/components/draft-preview/draft-preview.vue'

export default {
  name: 'market-draft-card',
  components: {
    DefectsTooltip,
    defectsNotifyDialog,
    defectsNotifyTooltip,
    DraftPreview,
    PreSignEndorseTooltip,
    CanNotSplitLabel,
  },

  props: {
    data: Object, // 票据信息
    showCreateTime: {
      type: Boolean,
      default: true,
    }
  },

  data() {
    return {
      BACK_DEFECT_TYPE_SHOW_NUM_MAP,
      BACK_DEFECT_TYPE_VALUE_MAP, // 票据瑕疵类型 id 映射 名称
      IDENTIFY_TYPE_MAP, // 识别类型 id 映射 名称
      IDENTIFY_TYPE_COLOR_MAP, // 识别类型 id 映射 标签颜色
      ACCEPTOR_TYPE_VALUE_MAP, // 承兑人类型 id 映射 名称
    }
  },

  methods: {
    // 将原始瑕疵字符串转为渲染字符串
    toDefectStr,

    // 是否是今天
    isToday,

    // 时间格式化
    formatTime,

    // 将元为单位的金额转为以万为单位
    yuan2wan,

    // 打开背面
    showBack() {
      if (!this.data.backImgUrl && !this.data.back) {
        this.$message.error('无背面图片')
        return
      }
      this.$refs.draftPreviewRef.open({
        type: 'back',
        data: this.data
      })
    },

    // 复制成功
    copySuccess(type = 1) {
      if (type === 1) {
        this.$message.success('已复制票号到剪贴板')
      } else {
        this.$message.success('已复制承兑人名称到剪贴板')
      }
    },

    // 复制失败
    copyError() {
      this.$message.error('复制失败')
    },

    // 显示瑕疵提示弹窗
    showDefectDialog(obj) {
      this.$refs.defectDialog.toggle(obj)
    }

  }
}
</script>
