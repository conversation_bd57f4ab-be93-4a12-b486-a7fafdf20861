<style lang="scss" scoped>
.recognition-operation {
  display: flex;
  flex-wrap: wrap-reverse;
  justify-content: space-between;
  align-items: center;
  margin: 12px 0;
  padding: 16px;
  background-color: #FFFFFF;

  .operation {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-shrink: 0;

    .selectded {
      & > label {
        font-weight: 600;
        color: $color-warning;
      }
    }
  }

  .search-form {
    display: flex;
    align-items: center;
    gap: 12px;

    .status-select {
      width: 102px;
      vertical-align: middle;
    }

    .status-select2 {
      width: 120px;
    }

    .spicon-refresh {
      margin-right: 4px;
      font-size: 14px;
      color: $--color-primary;
      vertical-align: top;
    }

    .spicon-refresh-active {
      animation: load .5s normal;
    }

    @keyframes load {
      from {
        transform: rotate(0);
      }

      to {
        transform: rotate(360deg);
      }
    }
  }

  @media screen and(max-width: 1112px) {
    .operation {
      margin-top: 8px;
    }
  }
}
</style>

<template>
  <div class="recognition-list">
    <div class="recognition-operation">
      <div class="operation">
        <el-checkbox
          v-if="cardMode"
          v-model="selectedAll"
          :indeterminate="!selectedAll && hasSelected"
        />
        <div class="selectded">
          已选 <label>{{ selectedLength }}</label> 条
        </div>
        <div>
          <el-button
            class="is-border"
            type="primary"
            size="medium"
            @click="!hasSelected ? $message.info('请先选择票据') : remove()"
          >
            删除
          </el-button>
          <el-button
            class="is-border"
            type="primary"
            size="medium"
            @click="!hasSelected ? publishAll() : publishSelected()"
          >
            {{ !hasSelected ? '发布全部' : '发布所选' }}
          </el-button>
        </div>
      </div>

      <div class="search-form">
        <Search @search-ticket="search" />
        <el-select
          v-model="searchObj.inRelease"
          class="status-select"
          size="medium"
          placeholder="请选择"
          @change="handleResetList"
        >
          <el-option
            v-for="item in publishStatusOptions"
            :key="item.label"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <!-- 18065:下线ECDS票据相关内容 -->
        <!--
          <el-select
          v-model="searchObj.draftType"
          class="status-select status-select2"
          size="medium"
          @change="handleResetList"
          >
          <el-option
          v-for="item in draftTypeOptions"
          :key="item.label"
          :label="item.label"
          :value="item.value"
          />
          </el-select>
        -->
        <el-button
          class="is-border"
          type="primary"
          size="medium"
          @click="refresh(true)"
        >
          <icon
            class="spicon spicon-refresh"
            :class="refreshBtnActive && 'spicon-refresh-active'"
            type="chengjie-refresh"
          />
          <span>刷新列表</span>
        </el-button>
        <div>
          <el-button
            class="is-border"
            type="primary"
            size="medium"
            :loading="changeTypeLoading"
            @click="changeType"
          >
            <icon v-if="cardMode" class="spicon spicon-refresh" type="chengjie-kapian" />
            <icon v-if="tableMode" class="spicon spicon-refresh" type="chengjie-liebiao" />
            <span>切换</span>
          </el-button>
        </div>
      </div>
    </div>

    <CardList
      v-if="cardMode"
      ref="cardListRef"
      :search-obj="searchObj"
      :has-search-args="hasSearchArgs"
      @selection-change="handleSelectionChange"
      @open-ticket-detail="openTicketDetail"
      @issue-draft="issueDraft"
      @publish-selected="publishSelected"
      @api-loaded="changeTypeLoading = false"
    />
    <TableList
      v-if="tableMode"
      ref="tableListRef"
      :search-obj="searchObj"
      @selection-change="handleSelectionChange"
      @open-ticket-detail="openTicketDetail"
      @issue-draft="issueDraft"
      @api-loaded="changeTypeLoading = false"
    />

    <SingleIssue ref="singleIssueRef" />
    <BatchIssue ref="batchIssueRef" />
    <DraftPreview ref="draftPreviewRef" />
  </div>
</template>

<script>
import {
  // OPEN_URL_IN_DEFAULT_BROWSER,
  TICKET_STATUS_CHANGE, // 发布后修改票据状态通知
} from '@recognize/ipc-event-constant'
import {
  isOverdue
} from '@/common/js/date.js'
import ticketApi from '@recognize/apis/ticket'
import TableList from './table/table-list.vue' // 表格内容
import CardList from './card/card-list.vue'
import Storage from '@/common/js/storage'
import Search from './search.vue' // 搜索组件
import { RECOFNITION_LISR_REFRESH } from '@recognize/event/modules/ticket' // 识别列表刷新事件
import {
  BACK_DEFECT_TYPE_NOT_ALLOW_NUM_MAP, // 票据瑕疵类型 id 映射 notAllowedToPublish 是否可发布
  BAN_STATUS, // 禁用状态
  ACCEPTOR_TYPE
} from '@recognize/constant'
import mixpanel from '@recognize/utils/mixpanel'
import SingleIssue from '@recognize/components/issue-draft/single-issue/single-issue.vue'
import BatchIssue from '@recognize/components/issue-draft/batch-issue/batch-issue.vue'
import DraftPreview from '@recognize/components/draft-preview/draft-preview.vue'
import { RECOGNIZE_VISIBLE_MODE } from '@recognize/constant-storage'
import { OPEN_USAGE_NOTICE } from '@recognize/event/modules/account' // 打开使用须知
// 列表展示模式
const VISIBLE_MODE = {
  CARD: 'card',
  TABLE: 'table',
}

export default {
  name: 'recognition-list',
  components: {
    Search,
    CardList,
    TableList,
    SingleIssue,
    BatchIssue,
    DraftPreview,
  },
  props: {
    holidaysList: {
      type: Array,
      default: () => []
    } // 节假日列表
  },
  data() {
    return {
      cardMode: true, // 卡片模式
      tableMode: false, // 表格模式

      // 识别选择框的选项
      publishStatusOptions: [
        {
          value: null,
          label: '全部'
        },
        {
          value: '0',
          label: '待发布'
        },
        {
          value: '1',
          label: '已发布'
        }
      ],
      // 是否新票
      draftTypeOptions: [
        {
          value: null,
          label: '全部'
        },
        {
          value: 1,
          label: '新一代'
        },
        {
          value: 0,
          label: 'ECDS'
        }
      ],
      selectedList: [], // 记录表格选中的数据
      searchObj: {
        inRelease: null,
        draftType: null
      }, // 搜索对象
      hasData: false, // 是否有数据
      refreshBtnActive: false, // 刷新按钮动画
      changeTypeLoading: false, // 切换模式按钮 loading
    }
  },
  computed: {
    // 识别记录是否全选，仅限卡片模式
    selectedAll: {
      get() {
        return this.selectedLength === 12
      },
      set(selected) {
        this.$refs.cardListRef.list.forEach(item => {
          item.selected = selected
        })
        if (selected) {
          this.selectedList = [...this.$refs.cardListRef.list]
        } else {
          this.selectedList = []
        }
      }
    },
    // 是否有已选择的识别记录
    hasSelected() {
      return this.selectedList.length > 0
    },
    // 已选条数
    selectedLength() {
      return this.selectedList.length
    },
    // 是否存在筛选参数
    hasSearchArgs() {
      return Object.keys(this.searchObj).some(key => this.searchObj[key])
    },
    // 是否关闭前端发布
    limitReleaseInfo() {
      return this.$store.state.common.limitReleaseInfo || {}
    }
  },
  created() {
    // 设置展示模式
    const storagedVisibleMode = Storage.get(RECOGNIZE_VISIBLE_MODE) ?? VISIBLE_MODE.CARD
    this.tableMode = storagedVisibleMode === VISIBLE_MODE.TABLE
    this.cardMode = storagedVisibleMode === VISIBLE_MODE.CARD

    this.$emit('get-holidays')
    this.$event.on(TICKET_STATUS_CHANGE, item => this.tickRefresh(item))
    // 全局列表刷新事件
    this.$event.on(RECOFNITION_LISR_REFRESH, this.handleResetList)
  },
  beforeDestroy() {
    this.$event.off(TICKET_STATUS_CHANGE, this.tickRefresh)
  },
  methods: {
    // 表格多选
    handleSelectionChange(val) {
      this.selectedList = [...val]
    },
    // 点击搜索
    async search(obj) {
      this.searchObj = Object.assign({}, this.searchObj, obj)
      await this.$nextTick() // 等待计算属性更新
      this.handleResetList()
    },
    // 点击刷新icon
    refresh(needTip) {
      if (needTip) {
        this.refreshBtnActive = true
        this.$message.success('已刷新！')
        setTimeout(() => {
          this.refreshBtnActive = false
        }, 500)
      }
      this.selectedList = []
      this.handleResetList()
      mixpanel.refreshTicket()
    },
    // 切换数据展示类型
    changeType() {
      this.changeTypeLoading = true
      this.tableMode = !this.tableMode
      this.cardMode = !this.cardMode
      this.tableMode && Storage.set(RECOGNIZE_VISIBLE_MODE, VISIBLE_MODE.TABLE)
      this.cardMode && Storage.set(RECOGNIZE_VISIBLE_MODE, VISIBLE_MODE.CARD)
      this.selectedList = []
    },
    // 票据刷新
    tickRefresh(obj) {
      if (obj?.msg) {
        this.$message.success(obj.msg)
      }
      this.handleResetList()
    },
    // 删除识别记录
    async remove() {
      await this.$confirm('<p class="message">确定要删除当前所选的记录吗？</p>', '删除提示', {
        dangerouslyUseHTMLString: true,
        type: 'warning',
        showClose: false,
        customClass: 'no-header-msg',
        confirmButtonText: '确认'

      })
      this.removeConfirm()
    },
    // 确认删除
    async removeConfirm() {
      if (!this.selectedList.length) {
        this.$message.info('请重新选择票据')
        return
      }
      await ticketApi.delDraft({
        list: this.selectedList.map(item => item.id)
      })
      this.$message.success('该记录已删除')
      this.handleResetList()
    },
    // 校验不允许发布的瑕疵
    validDeffectNotAllow(originalDefects) {
      if (!originalDefects) {
        return false
      }
      const originalDefectList = originalDefects.split('|').filter(item => item)
      const notAllow = originalDefectList.some(defect => {
        const type = defect.split('_')[0]
        return BACK_DEFECT_TYPE_NOT_ALLOW_NUM_MAP[type]
      })
      return notAllow
    },
    // 是否含有不允许发布的票据异常
    validErrorNotAllow(defectsNotify) {
      if (!defectsNotify) {
        return false
      }
      defectsNotify = JSON.parse(defectsNotify)
      return Array.isArray(defectsNotify.riskCorps) && defectsNotify.riskCorps.length > 0
    },
    // 判断背书链是否有异常准入企业
    hasAbnormalAccessRiskCorpsNotAllow(risks) {
      risks = JSON.parse(risks)
      return Array.isArray(risks?.abnormalAccessRiskCorps) && risks.abnormalAccessRiskCorps.length > 0
    },
    // 判断背书链是否有违反软件安全规则企业
    hasViolateSoftwareSecurityCorpsNotAllow(risks) {
      risks = JSON.parse(risks)
      return Array.isArray(risks?.violateSoftwareSecurityCorps) && risks.violateSoftwareSecurityCorps.length > 0
    },
    // 校验支付渠道
    async checkPaymentAccount() {
      const data = await this.$store.dispatch('user/getPaymentAccountList')
      if (data.filter(i => i.banStatus === BAN_STATUS.DISABLE.id).length !== data.length) {
        return
      }
      this.$message.error('操作失败，全部渠道的交易权限被禁用，请重新操作或联系客服解决')
      return Promise.reject(new Error('全部渠道的交易权限被禁用'))
    },
    // 点击一键发布
    async issueDraft(data) {
      // 点击意见发布：如果不允许批量发布 并且是 批量识别的票
      if (this.limitReleaseInfo.discernBatchPostFlag === 1 && data.discernType === 2) {
        this.$emit('open-release-limit')
        return
      }
      // 须知弹窗
      if (!this.$store.state['recognize-common'].isAgreeUsageNotice) {
        this.$event.emit(OPEN_USAGE_NOTICE)
        return
      }

      try {
        // 判断支付渠道
        await this.checkPaymentAccount()
        // 判断商票交易权限
        await this.checkSpTradeAccess([data])
        // 获取票面信息
        const res = await ticketApi.searchDiscernDetail({ list: [data.draftDiscernId] })
        if (res[0]) {
          data.draftJson = JSON.parse(res[0].draftJson)
          // 适配合适窗口大小
          let minWidth = 1260
          if (this.$ipc && document.body.clientWidth < minWidth) {
            this.$ipc.send('SET_SIZE', minWidth, document.body.clientHeight)
          }
          this.$refs.singleIssueRef.open({ type: 'recognition', data })
        }
      } catch {
        return Promise.reject(new Error('接口调用失败'))
      }
    },
    // 发布所选
    async publishSelected() {
      // 须知弹窗
      if (!this.$store.state['recognize-common'].isAgreeUsageNotice) {
        this.$event.emit(OPEN_USAGE_NOTICE)
        return
      }
      await this.checkPaymentAccount()
      let msg = null
      // 过滤掉批量识别的票
      if (this.limitReleaseInfo.discernBatchPostFlag === 1) {
        const filtered = this.selectedList.filter(item => item.discernType === 1)
        if (filtered.length === 0) {
          this.$message.warning('暂无可发布票据')
          return
        } else {
          this.selectedList = filtered
        }
        msg = '已为您过滤掉不可发布的票据'
      }
      let draftDiscernIdList = this.selectedList.map(item => item.draftDiscernId)
      const ticketData = await ticketApi.getRecordByDraftNo({
        draftDiscernIds: draftDiscernIdList
      })
      // eslint-disable-next-line max-len
      const publishList = ticketData.filter(item => !(this.validErrorNotAllow(item.defectsNotify) || this.validDeffectNotAllow(item.originalDefects) || isOverdue(item.maturityDate)) && !item.inRelease && !item.inRelease && !this.hasAbnormalAccessRiskCorpsNotAllow(item.defectsNotify) && !this.hasViolateSoftwareSecurityCorpsNotAllow(item.defectsNotify) && !item.inRiskBlack)

      if (publishList.length !== draftDiscernIdList.length) {
        msg = '已为您过滤掉不可发布的票据'
      }
      if (!publishList.length) {
        return this.$message.warning('暂无可发布票据')
      }

      // 判断商票交易权限
      const passedList = await this.checkSpTradeAccess(publishList)
      // 适配合适窗口大小
      let minWidth = 1300
      if (this.$ipc && document.body.clientWidth < minWidth) {
        this.$ipc.send('SET_SIZE', minWidth, document.body.clientHeight)
      }
      this.$refs.batchIssueRef.open({ type: 'recognitionList', publishList: passedList, msg })
    },
    // 发布全部
    async publishAll() {
      // 须知弹窗
      if (!this.$store.state['recognize-common'].isAgreeUsageNotice) {
        this.$event.emit(OPEN_USAGE_NOTICE)
        return
      }
      await this.checkPaymentAccount()

      const res = await ticketApi.publishAll()
      let publishList = []
      // 过滤掉批量识别的票
      if (this.limitReleaseInfo.discernBatchPostFlag === 1) {
        publishList = res.filter(item => item.discernType === 1)
      } else {
        publishList.push(...res)
      }
      // eslint-disable-next-line max-len
      publishList = publishList.filter(item => !(this.validErrorNotAllow(item.defectsNotify) || this.validDeffectNotAllow(item.originalDefects) || isOverdue(item.maturityDate)) && !item.inRelease && !item.inRelease && !this.hasAbnormalAccessRiskCorpsNotAllow(item.defectsNotify) && !this.hasViolateSoftwareSecurityCorpsNotAllow(item.defectsNotify) && !item.inRiskBlack)
      if (!publishList.length) {
        return this.$message.warning('暂无可发布票据')
      }
      if (publishList.length < res.length) {
        this.$message.warning('已为您过滤掉不可发布的票据')
      }

      // 判断商票交易权限
      const passedList = await this.checkSpTradeAccess(publishList)
      // 适配合适窗口大小
      let minWidth = 1300
      if (this.$ipc && document.body.clientWidth < minWidth) {
        this.$ipc.send('SET_SIZE', minWidth, document.body.clientHeight)
      }
      this.$refs.batchIssueRef.open({ type: 'recognitionList', publishList: passedList })
    },
    // 商票交易准入查询
    async checkSpTradeAccess(drafts) {
      const isCommercialDraft = (draftNumber, acceptorType) => String(draftNumber).startsWith('2') || String(draftNumber).startsWith('6') || String(draftNumber).startsWith('7') || Number(acceptorType) === ACCEPTOR_TYPE.CAI_PIAO.id

      // 没有商票/财票
      if (!drafts.some(draft => isCommercialDraft(draft.draftNo, draft.acceptorType))) return drafts

      const corpId = this.$store.state?.user?.userInfo?.corpId
      let { pass, failType, accepterNameList } = await ticketApi.whiteListCheck({
        corpId,
        accepterList: drafts.filter(draft => isCommercialDraft(draft.draftNo, draft.acceptorType)).map(draft => ({
          accepter: draft.acceptorName,
          amount: draft.draftAmount
        }))
      })

      if (pass) return drafts

      accepterNameList = accepterNameList || []
      if (failType === 0) {
      // 可发布的票据列表
        const passDrafts = drafts.filter(draft => !isCommercialDraft(draft.draftNo))
        // 是否还有可发布的票据
        const hasPassDrafts = passDrafts.length > 0
        let errMsg = '暂无发布商票订单权限，仍需发布请先发起权限申请。'
        if (hasPassDrafts) {
          errMsg += '如点击【跳过并发布】将跳过商票，发布剩余的票据'
        }
        try {
          await this.$confirm(errMsg, '提示', {
            iconPosition: 'title',
            type: 'warning',
            confirmButtonText: '发起申请',
            cancelButtonText: hasPassDrafts ? '跳过并发布' : '取消',
            distinguishCancelAndClose: true
          })
          const hasApply = await ticketApi.existUnderReviewAccessCorpApply(corpId)
          if (hasApply) {
            this.$message.warning('已有申请正在审核中，请耐心等待')
          } else if (this.$ipc) {
            this.$ipc.send('PUSH_ROUTE_IN_MAIN_WINDOW', '/user-center/apply-sp-permission')
          } else {
            this.$router.push('/user-center/apply-sp-permission')
          }
          return Promise.reject(new Error('无商票发布权限'))
        } catch (action) {
          if (!hasPassDrafts || action !== 'cancel') {
            return Promise.reject(new Error('无商票发布权限'))
          }
          return passDrafts
        }
      } else if (failType === 1) {
        // 是否还有可发布的票据
        const hasPassDrafts = accepterNameList.length < new Set(drafts.map(draft => draft.acceptorName)).size
        // 可发布的票据列表
        const passDrafts = drafts.filter(draft => accepterNameList.findIndex(item => item.accepter === draft.acceptorName) === -1)
        // let errMsg = `承兑人「${accepterNameList.map(i => i.accepter).join()}」暂不在授信名单内，仍需发布请先发起授信申请。`
        let errMsg = '该承兑人不在授信白名单，请联系您的客户经理。'
        if (hasPassDrafts) {
          errMsg += '如点击【跳过并发布】将跳过不在授信名单内的商票，发布剩余的票据'
        }
        try {
          await this.$confirm(errMsg, '提示', {
            iconPosition: 'title',
            type: 'warning',
            confirmButtonText: '确认',
            showCancelButton: hasPassDrafts,
            cancelButtonText: hasPassDrafts ? '跳过并发布' : '取消',
            distinguishCancelAndClose: true
          })
          // const spbUrl = await this.$store.dispatch('recognize-corpInfo/getCorpInfoSync', 'issue/white')
          // if (this.$ipc) {
          //   this.$ipc.send(OPEN_URL_IN_DEFAULT_BROWSER, spbUrl)
          // } else {
          //   window.open(spbUrl)
          // }
          return Promise.reject(new Error('承兑人不在白名单内'))
        } catch (action) {
          if (!hasPassDrafts || action !== 'cancel') {
            return Promise.reject(new Error('承兑人不在白名单内'))
          }
          return this.checkSpTradeAccess(passDrafts)
        }
      } else if (failType === 2) {
        // 是否还有可发布的票据
        const hasPassDrafts = accepterNameList.length < new Set(drafts.map(draft => draft.acceptorName)).size
        // 可发布的票据列表
        const passDrafts = drafts.filter(draft => accepterNameList.findIndex(item => item.accepter === draft.acceptorName) === -1)
        let errMsg = `承兑人「${accepterNameList.map(i => i.accepter).join()}」剩余额度不足，若仍需发布请联系您的客户经理`
        if (hasPassDrafts) {
          errMsg += '如点击【跳过并发布】将跳过额度不足的商票，发布剩余的票据。'
          try {
            await this.$confirm(errMsg, '提示', {
              iconPosition: 'title',
              type: 'warning',
              confirmButtonText: '跳过并发布',
              cancelButtonText: '取消',
            })
            return passDrafts
          } catch {
            return Promise.reject(new Error('承兑人额度不足'))
          }
        } else {
          this.$confirm(errMsg, '提示', {
            iconPosition: 'title',
            type: 'warning',
            confirmButtonText: '我知道了',
            showCancelButton: false
          })
          return Promise.reject(new Error('承兑人额度不足'))
        }
      } else if (failType === 3) {
        // 是否还有可发布的票据
        const hasPassDrafts = accepterNameList.length < new Set(drafts.map(draft => draft.acceptorName)).size
        // 可发布的票据列表
        const passDrafts = drafts.filter(draft => accepterNameList.findIndex(item => item.accepter === draft.acceptorName) === -1)
        let errMsg = `承兑人「${accepterNameList.map(i => i.accepter).join()}」白名单异常，若仍需发布请联系您的客户经理`
        if (hasPassDrafts) {
          errMsg += '如点击【跳过并发布】将跳过白名单异常的商票，发布剩余的票据。'
          try {
            await this.$confirm(errMsg, '提示', {
              iconPosition: 'title',
              type: 'warning',
              confirmButtonText: '跳过并发布',
              cancelButtonText: '取消',
            })
            return passDrafts
          } catch {
            return Promise.reject(new Error('白名单异常'))
          }
        } else {
          this.$confirm(errMsg, '提示', {
            iconPosition: 'title',
            type: 'warning',
            confirmButtonText: '我知道了',
            showCancelButton: false
          })
          return Promise.reject(new Error('白名单异常'))
        }
      }
      return Promise.reject(new Error('未知错误'))
    },
    // 操作完成后重置列表
    handleResetList() {
      if (this.cardMode) {
        this.$refs.cardListRef.reset()
      }
      if (this.tableMode) {
        this.$refs.tableListRef.reset()
      }
    },
    // 打开票据详情
    async openTicketDetail(id) {
      try {
        const res = await ticketApi.searchDiscernDetail({ list: [id] })
        res[0] && this.$refs.draftPreviewRef.open({ data: JSON.parse(res[0].draftJson) })
      } catch {
        return Promise.reject(new Error('打开票据失败'))
      }
    },
  }
}
</script>
