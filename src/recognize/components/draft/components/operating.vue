<style lang="scss" scoped>
.operating {
  padding: 16px;
  background-color: #FFFFFF;
}

.operating-head {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.operating-button-group {
  display: flex;
  gap: 12px;
  align-items: center;
  flex: 0 1 624px;
}

.operating-button {
  border: 1px solid $--color-primary;
  border-radius: 4px;
  flex: 0 1 200px;
  height: 48px;
  line-height: 48px;
  text-align: center;
  background: $color-FFFFFF;
  box-sizing: border-box;
  cursor: pointer;

  .operating-title {
    display: inline-flex;
    align-items: center;
    font-size: 20px;
    font-weight: 500;
    color: $--color-primary;
  }

  .operating-icon {
    margin-right: 6px;
  }

  &--hover {
    &:hover {
      background: $--color-primary;

      .operating-title {
        color: $color-FFFFFF;
      }
    }
  }
}

.operating-config {
  display: flex;
  align-items: center;
}

.loading {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99999;
  width: 100vw;
  height: 100vh;
  text-align: center;
  background: rgba($color: #000000, $alpha: 50%);
}

.loading-text {
  position: absolute;
  top: 50%;
  left: 50%;
  border-radius: 10px;
  width: 284px;
  height: 50px;
  font-size: 18px;
  color: $color-FFFFFF;
  background: rgba($color: #000000, $alpha: 75%);
  transform: translate(-50%);
  line-height: 50px;
}
</style>

<style lang="scss">
.bare-ticket-dialog {
  .el-message-box__content {
    .red-text {
      font-weight: 600;
      color: $color-warning;
    }
  }
}
</style>

<template>
  <div class="operating">
    <div class="operating-head">
      <div class="operating-button-group">
        <div class="operating-button operating-button--hover" @click="() => singleIdentify(1)">
          <p class="operating-title">
            <icon class="operating-icon" type="chengjie-bill" :size="24" />
            <span>单张识别</span>
          </p>
        </div>
        <div class="operating-button operating-button--hover" @click="multiIdentify">
          <p class="operating-title">
            <icon class="operating-icon" type="chengjie-batch" :size="24" />
            <span>批量识别</span>
          </p>
        </div>
        <EbankMerge
          ref="eBankMerge"
          class="operating-button"
          @check-risks="noPublishAlert"
          @identify-tip="$refs.recognizeTip.identifyFail()"
          @set-can-short-cut="canShortcut = true"
        />
      </div>

      <div class="operating-config">
        <SupportedBanks />
        <VideoGuide />
        <DefectSettingIcon />
      </div>
    </div>

    <div v-if="identifying" class="loading">
      <div class="loading-text">正在识别票据信息，请稍后... {{ countdown ? `${countdown}s` : '' }}</div>
    </div>

    <RecognizeTip ref="recognizeTip" />
    <UpdateQianTag ref="updateQianTag" />
    <OrderSyncUpdate ref="orderSyncUpdateRef" />
    <SmartVerifyTicket ref="smartVerifyTicketRef" />
    <SingleIssue ref="singleIssueRef" />
    <BatchIssue ref="batchIssueRef" />
  </div>
</template>

<script>
import EbankMerge from './e-bank-merge.vue'
import UpdateQianTag from './update-qian-tag.vue'
import SupportedBanks from '@/views/pages/draft-stock/components/supported-banks/supported-banks.vue'
import VideoGuide from '@/views/pages/draft-stock/components/video-guide/video-guide.vue'
import RecognizeTip from '@recognize/components/recognize-tip/recognize-tip.vue'
import SmartVerifyTicket from '@recognize/components/smart-verify-ticket/smart-verify-ticket.vue'
import SingleIssue from '@recognize/components/issue-draft/single-issue/single-issue.vue'
import BatchIssue from '@recognize/components/issue-draft/batch-issue/batch-issue.vue'
import OrderSyncUpdate from '@recognize/components/order-sync-update/order-sync-update.vue'
import DefectSettingIcon from '@recognize/components/defect-setting/defect-setting-icon.vue'
import {
  BACK_DEFECT_TYPE_VALUE_MAP, // 票据瑕疵类型 id 映射 名称
  BACK_DEFECT_TYPE_NOT_ALLOW_NUM_MAP, // 票据瑕疵类型 id 映射 notAllowedToPublish 是否可发布
  BAN_STATUS, // 渠道禁用状态
} from '@recognize/constant'
import {
  WINDOW_TOPPING
} from '@recognize/constant-storage'
import {
  MARKET_DRAFT_RECOGNIZE_SHOW_LAYER, // 显示票据识别遮罩层
  MARKET_DRAFT_RECOGNIZE_DRAFT_RECOGNIZED, // 票据信息已识别
  MARKET_DRAFT_RECOGNIZE_MULTI_RECOGNIZED, // 批量识别成功
  MARKET_DRAFT_INSTALL_ERROR, // Dll安装错误事件
  MARKET_DRAFT_START_COUNTDOWN, // 开始识别倒计时
  SET_MAIN_WINDOW_TOPPING, // 设置窗口置顶
  SHORTCUT_RECOGNIZE, // 触发单张识别快捷键事件
  SHORTCUT_MULIT_RECOGNIZE, // 触发批量识别快捷键
  SHORTCUT_AUTO_SYNC, // 自动同步快捷键
  SHORTCUT_OPEN_RECOGNIZE_WINDOW, // 打开识别窗口快捷键
} from '@recognize/ipc-event-constant'
import { RECOFNITION_LISR_REFRESH } from '@recognize/event/modules/ticket' // 识别列表刷新事件
import { OPEN_USAGE_NOTICE } from '@recognize/event/modules/account' // 打开使用须知
import ticketApi from '@recognize/apis/ticket'
import toolsApi from '@recognize/apis/tools'
import Storage from '@/common/js/storage'
import { isOverdue } from '@/common/js/date'
import { showError } from '@/utils/axios'
import { tracking } from '@/utils/util'

const IDENTIFY_MODE = {
  C: 0, // C++解析
  C_HTML: 1, // C++传输HTML
  CHROME_PLUGIN: 2, // 浏览器插件
}
const IDENTIFY_TYPE = {
  SINGLE: 1, // 单张识别
  BATCH: 2, // 批量识别
}

const isWindows = navigator.platform.indexOf('Win') > -1

// 判断是否包含不允许发布的瑕疵
const hasNotAllowDeffect = defects => {
  if (typeof defects !== 'string' || defects.length === 0) return false
  return defects.split('|').filter(item => item)
    .some(defect => {
      const type = defect.split('_')[0]
      return BACK_DEFECT_TYPE_NOT_ALLOW_NUM_MAP[type]
    })
}
// 判断是否包含不允许发布的风险
const hasNotAllowRisk = risks => {
  risks = JSON.parse(risks)
  return Array.isArray(risks?.riskCorps) && risks.riskCorps.length > 0
}
// 判断背书链是否有异常准入企业
const hasAbnormalAccessRiskCorpsNotAllow = risks => {
  risks = JSON.parse(risks)
  return Array.isArray(risks?.abnormalAccessRiskCorps) && risks.abnormalAccessRiskCorps.length > 0
}
// 判断背书链是否有违反软件安全规则企业
const hasViolateSoftwareSecurityCorpsNotAllow = risks => {
  risks = JSON.parse(risks)
  return Array.isArray(risks?.violateSoftwareSecurityCorps) && risks.violateSoftwareSecurityCorps.length > 0
}

export default {
  name: 'operating',
  components: {
    EbankMerge,
    UpdateQianTag,
    SupportedBanks,
    VideoGuide,
    RecognizeTip,
    SmartVerifyTicket,
    SingleIssue,
    BatchIssue,
    OrderSyncUpdate,
    DefectSettingIcon,
  },
  props: {
    userConfig: Object, // 用户设置信息
  },
  data() {
    return {
      identifying: false, // 是否显示识别票据的loading
      isContinueIdentify: false, // 是否为继续识别操作
      identifyStartTime: null, // 识别开始时间
      identifyEndTime: null, // 识别结束时间
      timer: null, // 计时器
      countdown: 0, // 识别倒计时
      canShortcut: true, // 快捷键是否可用
    }
  },
  computed: {
    limitReleaseInfo() { // 是否关闭前端发布
      return this.$store.state.common.limitReleaseInfo || {}
    }
  },
  created() {
    // 接收窗口传来的参数
    const handler = () => {
      const shortcut = localStorage.getItem('EMIT_SHORTCUT')
      const extra = localStorage.getItem('EMIT_SHORTCUT_TEMPDATA')
      if (shortcut) {
        localStorage.removeItem('EMIT_SHORTCUT')
        localStorage.removeItem('EMIT_SHORTCUT_TEMPDATA')
        this.handleShortcutEmit(shortcut, extra)
      }
    }

    if (this.$ipc) {
      // 监听窗口打开事件，用来触发快捷键
      this.$ipc.on('DID_FINISH_LOAD', handler)
      this.$ipc.on('ACTIVATE_WINDOW', handler)
      // 监听票据已识别事件
      this.$ipc.on(MARKET_DRAFT_RECOGNIZE_DRAFT_RECOGNIZED, this.handleSingleIdentify)
      this.$ipc.on(MARKET_DRAFT_RECOGNIZE_MULTI_RECOGNIZED, this.handleMultiIdentify)
      // 监听dll安装错误事件
      this.$ipc.on(MARKET_DRAFT_INSTALL_ERROR, this.dllInstallError)
      this.$ipc.on(MARKET_DRAFT_START_COUNTDOWN, this.identifyCountdown)
      // 监听插件激活窗口
      this.$ipc.on('EXTENSION_ACTIVATE', this.handleExtensionEmit)
    }
  },
  beforeDestroy() {
    if (this.$ipc) {
      this.$ipc.removeListener(MARKET_DRAFT_RECOGNIZE_DRAFT_RECOGNIZED, this.handleSingleIdentify)
      this.$ipc.removeListener(MARKET_DRAFT_RECOGNIZE_MULTI_RECOGNIZED, this.handleMultiIdentify)
      this.$ipc.removeListener(MARKET_DRAFT_INSTALL_ERROR, this.dllInstallError)
      this.$ipc.removeListener(MARKET_DRAFT_START_COUNTDOWN, this.identifyCountdown)
      this.$ipc.removeListener('EXTENSION_ACTIVATE', this.handleExtensionEmit)
    }
  },
  methods: {
    // 处理快捷键事件
    handleShortcutEmit(name, extra) {
      if (!this.canShortcut) return
      this.canShortcut = false
      if (name === SHORTCUT_RECOGNIZE) {
        this.singleIdentify()
      } else if (name === SHORTCUT_MULIT_RECOGNIZE) {
        if (extra && JSON.parse(extra)?.discernBatchPostFlag === 1) {
          this.canShortcut = true
          this.$emit('open-release-limit')
          return
        }
        if (extra && !JSON.parse(extra)?.postOrderFlag) {
          this.canShortcut = true
          return this.$message.warning('功能临时关闭，请在【承接识票】中使用【单张识别】识别票面后发布！')
        }
        this.multiIdentify()
      } else if (name === SHORTCUT_AUTO_SYNC) {
        this.$refs.eBankMerge.clickEBankMerge()
      } else if (name === SHORTCUT_OPEN_RECOGNIZE_WINDOW) {
        this.canShortcut = true
      } else {
        this.canShortcut = true
      }
    },
    // 处理插件事件
    handleExtensionEmit(_, url) {
      if (typeof url !== 'string' || !url.startsWith('cj-erp')) return

      if (url.startsWith('cj-erp://single-recognize/')) {
        this.postJavaSingleAnalysis({
          identifyMode: IDENTIFY_MODE.CHROME_PLUGIN,
          domId: url.replace('cj-erp://single-recognize/', '')
        }, {
          userClickFastTrade: true,
        })
      } else if (url.startsWith('cj-erp://batch-recognize/')) {
        this.postJavaBatchAnalysis({
          identifyMode: IDENTIFY_MODE.CHROME_PLUGIN,
          domId: url.replace('cj-erp://batch-recognize/', '')
        })
      }
    },
    // 识别倒计时
    identifyCountdown() {
      this.countdown = 40
      this.timer = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown -= 1
        } else {
          this.identifying = false
          clearInterval(this.timer)
        }
      }, 1000)
    },
    // DLL加载异常
    dllInstallError() {
      this.identifying = false
      this.canShortcut = true
      this.$refs.recognizeTip.appAbnormal()
    },
    // 识别前环境检查
    identifyPrecheck() {
      if (!this.$store.state['recognize-common'].isAgreeUsageNotice) {
        this.$event.emit(OPEN_USAGE_NOTICE)
        this.canShortcut = true
        return false
      }
      if (!this.$ipc) {
        this.$message.warning('请在客户端内进行识别操作')
        this.canShortcut = true
        return false
      }
      if (!isWindows) {
        this.$message.error('当前系统不支持该功能！')
        this.canShortcut = true
        return false
      }
      return true
    },
    // 点击单张识别 type: 1=>单张识别 2=>背面识别
    singleIdentify(type = 1) {
      if (!this.identifyPrecheck()) return

      this.identifying = true
      this.identifyStartTime = Date.now()
      try {
        // 调用 dll，打开识别窗口
        if (type === 1) {
          this.$ipc.send(MARKET_DRAFT_RECOGNIZE_SHOW_LAYER, '', 0)
        } else {
          this.$ipc.send(MARKET_DRAFT_RECOGNIZE_SHOW_LAYER, '', 4)
        }
      } catch (e) {
        this.canShortcut = true
        this.identifying = false
        clearInterval(this.timer)
      }
    },
    // 点击批量识别
    async multiIdentify() {
      if (!this.identifyPrecheck()) return
      await this.$store.dispatch('common/getNewVersionDraftConfig')
      await this.$nextTick()
      if (this.limitReleaseInfo.discernBatchPostFlag === 1) {
        this.canShortcut = true
        this.$emit('open-release-limit')
        return
      }

      // 后管配置白名单内可操作批量识别 postOrderFlag => 是否发布白名单内
      if (!this.limitReleaseInfo.postOrderFlag) {
        this.canShortcut = true
        return this.$message.warning('功能临时关闭，请在【承接识票】中使用【单张识别】识别票面后发布！')
      }

      this.identifying = true
      this.identifyStartTime = Date.now()
      try {
        this.$ipc.send(MARKET_DRAFT_RECOGNIZE_SHOW_LAYER, '', 3)
      } catch (e) {
        // 设置是否能用快捷键
        this.canShortcut = true
        this.identifying = false
        clearInterval(this.timer)
      }
    },
    // 处理单张票据识别数据
    handleSingleIdentify(_, data) {
      clearInterval(this.timer)
      if (!Storage.get(WINDOW_TOPPING)) {
        this.$ipc.send(SET_MAIN_WINDOW_TOPPING, true)
        this.$ipc.send(SET_MAIN_WINDOW_TOPPING, Storage.get(WINDOW_TOPPING))
      }
      this.canShortcut = true
      this.identifying = false
      this.identifyEndTime = Date.now()

      // 取消识别
      if (!data) {
        this.isContinueIdentify = false
        return this.$message.error('取消识别')
      }
      // 识别失败
      if (data.code !== 0) {
        this.isContinueIdentify = false
        return this.$refs.recognizeTip.identifyFail()
      }
      if (data.bankName === '暂不支持此银行') {
        return this.$message.error(data.bankName)
      }
      // C++传输HTML给后端识别
      if (typeof data.html === 'string') {
        if (data.html.length === 0) {
          return this.$refs.recognizeTip.identifyFail()
        }
        return this.postJavaSingleAnalysis({ identifyMode: IDENTIFY_MODE.C_HTML, ...data })
      }
      // C++解析识别
      if (Array.isArray(data.infos)) {
        if (data.infos.length === 0) {
          return this.$refs.recognizeTip.identifyFail()
        }
        return this.postJavaSingleAnalysis({ identifyMode: IDENTIFY_MODE.C, ...data })
      }
      // 异常处理
      this.$refs.recognizeTip.identifyFail()
    },
    // 处理批量票据识别数据
    handleMultiIdentify(_, data) {
      clearInterval(this.timer)
      if (!Storage.get(WINDOW_TOPPING)) {
        this.$ipc.send(SET_MAIN_WINDOW_TOPPING, true)
        this.$ipc.send(SET_MAIN_WINDOW_TOPPING, Storage.get(WINDOW_TOPPING))
      }
      this.canShortcut = true
      this.identifying = false
      this.identifyEndTime = Date.now()

      // 取消识别
      if (!data) {
        return this.$message.error('取消识别')
      }
      // 识别失败
      if (data.code !== 0) {
        return this.$refs.recognizeTip.identifyFail()
      }
      // C++传输HTML给后端识别
      if (typeof data.html === 'string') {
        if (data.html.length === 0) {
          return this.$refs.recognizeTip.identifyFail()
        }
        return this.postJavaBatchAnalysis({ identifyMode: IDENTIFY_MODE.C_HTML, ...data })
      }
      // C++解析识别
      if (Array.isArray(data.infos)) {
        if (data.infos.length === 0) {
          return this.$refs.recognizeTip.identifyFail()
        }
        if (data.infos[0].bankType === '暂不支持此银行') {
          return this.$refs.recognizeTip.identifyFail()
        }
        return this.postJavaBatchAnalysis({ identifyMode: IDENTIFY_MODE.C, ...data })
      }
      // 异常处理
      this.$refs.recognizeTip.identifyFail()
    },

    // 继续识别确认弹窗
    continueIdentify() {
      this.$confirm('<div>未获取到该票据的背面信息。</div><div>请先在网银中 <span class="red-text">打开该票的背面，然后点击下方的【已打开背面，继续识别】</span>按钮。</div>', '提示', {
        distinguishCancelAndClose: true,
        dangerouslyUseHTMLString: true,
        type: 'warning',
        iconPosition: 'title',
        confirmButtonText: '已打开背面，继续识别',
        cancelButtonText: '取消',
        showClose: false,
        closeOnClickModal: false,
        closeOnPressEscape: false,
        customClass: 'bare-ticket-dialog'
      }).then(() => {
        this.isContinueIdentify = true
        this.singleIdentify(2)
      })
    },
    // 光票确认弹窗
    bareTicket(data, modify) {
      this.$confirm('<div>未获取到该票据的背面信息。</div><div>请确认该票是否是光票（即没有任何背书转让记录）。</div><div>若该票不是光票，请先在网银中 <span class="red-text">打开该票的背面，然后点击下方【已打开背面，继续识别】。</span></div>', '提示', {
        distinguishCancelAndClose: true,
        dangerouslyUseHTMLString: true,
        type: 'warning',
        iconPosition: 'title',
        confirmButtonText: '已打开背面，继续识别',
        cancelButtonText: '这是光票',
        showClose: false,
        closeOnClickModal: false,
        closeOnPressEscape: false,
        customClass: 'bare-ticket-dialog'
      }).then(() => {
        if (data.identifyMode === IDENTIFY_MODE.CHROME_PLUGIN) {
          this.$message.error('识别失败，请联系您的客户经理')
          return
        }
        this.isContinueIdentify = true
        this.singleIdentify(2)
      })
        .catch(action => {
          if (action === 'cancel') {
            // 光票，提交数据
            this.postJavaSingleAnalysis(data, {
              ...modify,
              userClickFastTrade: true
            })
          }
        })
    },
    // 正背面不连续确认弹窗
    discontinuousTicket(data, modify, { front, back }) {
      this.$confirm(`<div>识别到该票据<span class="red-text">正面收票人</span>和<span class="red-text">背面第一手背书人</span>不一致。<br>请先在网银中确认本次识别的正背面是否属于同一张票？<br>若不是同一张票，请先点击【取消】，并刷新网银页面后再次识别。<br>如果还无法解决问题，请联系客服。<br><br>正面：${front ?? '-'}<br>背面：${back ?? '-'}</div>`, '提示', {
        distinguishCancelAndClose: true,
        dangerouslyUseHTMLString: true,
        type: 'warning',
        iconPosition: 'title',
        confirmButtonText: '取消',
        cancelButtonText: '已确认，是同一张票',
        showClose: false,
        closeOnClickModal: false,
        closeOnPressEscape: false,
        customClass: 'bare-ticket-dialog'
      }).then(() => {
        // nothing to do
      })
        .catch(action => {
          if (action === 'cancel') {
            // 同一张票，提交数据
            this.postJavaSingleAnalysis(data, {
              ...modify,
              userClickFrontBackSameDraft: true
            })
          }
        })
    },

    // java解析单张数据
    async postJavaSingleAnalysis(data = {}, modify = {}) {
      const {
        userClickFrontBackSameDraft, // 正背面不一致标识
        userClickNotSign, // 未签收标识
        userClickFastTrade = false, // 光票标识
      } = modify
      try {
        const api = {
          [IDENTIFY_MODE.C]: 'webSwitchJavaAnalysis',
          [IDENTIFY_MODE.C_HTML]: 'JavaParseHtml',
          [IDENTIFY_MODE.CHROME_PLUGIN]: 'JavaParseHtml'
        }[data.identifyMode]
        const params = (() => {
          if (data.identifyMode === IDENTIFY_MODE.C) {
            return {
              bankName: data.infos[0]?.bankType ?? '',
              draftJson: JSON.stringify({
                front: data.infos[0]?.front,
                back: data.infos[0]?.back
              }),
            }
          }
          if (data.identifyMode === IDENTIFY_MODE.C_HTML) {
            return {
              type: IDENTIFY_MODE.C_HTML,
              bankName: data.bankName,
              bankCode: data.bankCode,
              bankUrl: data.bankUrl,
              html: data.html
            }
          }
          if (data.identifyMode === IDENTIFY_MODE.CHROME_PLUGIN) {
            return {
              type: IDENTIFY_MODE.CHROME_PLUGIN,
              domId: data.domId
            }
          }
        })()

        const res = await toolsApi[api]({
          ...params,
          ...(typeof userClickNotSign === 'boolean' ? { userClickNotSign } : {}),
          userClickFastTrade,
          ...(typeof userClickFrontBackSameDraft === 'boolean' ? { userClickFrontBackSameDraft } : {}),
        })
        // errorCode:  1、票据要素不齐全 2、新票没有子票区间
        if (res.errorCode === 1 || res.errorCode === 2) {
          this.isContinueIdentify = false
          this.$refs.recognizeTip.identifyFail()
          return
        }
        // 3、正背面不连续
        if (res.errorCode === 3) {
          this.isContinueIdentify = false
          this.discontinuousTicket(data, modify, { front: res.frontPayeeFullName, back: res.backFirstEndorserName })
          return
        }
        // 点击继续识别且没有背书信息或者没有票号，识别失败
        if (this.isContinueIdentify && (res.emptyBackDraftNo || res.emptyBackEndorseHistory)) {
          this.$refs.recognizeTip.continueIdentifyFail()
          this.isContinueIdentify = false
          return
        }
        // 没有背面票号，是否走继续识别逻辑
        if (res.emptyBackDraftNo) {
          if (data.identifyMode === IDENTIFY_MODE.CHROME_PLUGIN) {
            this.$message.error('识别失败，请联系您的客户经理')
            return
          }
          // 显示继续识别弹窗
          this.continueIdentify(data)
          return
        }
        // 没有背书信息，是否走光票逻辑
        if (res.emptyBackEndorseHistory) {
          this.bareTicket(data, modify)
          return
        }

        this.isContinueIdentify = false
        this.afterJavaAnalysis(res, IDENTIFY_TYPE.SINGLE, modify)

        // 埋点
        if (data.identifyMode === IDENTIFY_MODE.CHROME_PLUGIN) {
          tracking({ type: '单张', discernId: res.draftDiscernId })
        }
      } catch (error) {
        const { code, msg } = error?.data ?? {}
        if (code === 700) {
          this.$confirm(`<div>网银中此票据状态显示为：【${msg}】</div><div style="color:red">请确认此票据是否已在网银中签收？</div>`, '提示', {
            dangerouslyUseHTMLString: true,
            type: 'warning',
            showClose: true,
            iconPosition: 'title',
            cancelButtonText: '未签收',
            confirmButtonText: '已签收',
            distinguishCancelAndClose: true,
          }).then(() => {
            this.postJavaSingleAnalysis(data, {
              ...modify,
              userClickNotSign: false,
            })
          })
            .catch(action => {
              if (action === 'close') return
              this.postJavaSingleAnalysis(data, {
                ...modify,
                userClickNotSign: true,
              })
            })
          return
        }
        showError(error)
      }
    },
    // java解析批量数据
    async postJavaBatchAnalysis(data) {
      try {
        const api = {
          [IDENTIFY_MODE.C]: 'batchwebSwitchJavaAnalysis',
          [IDENTIFY_MODE.C_HTML]: 'batchJavaParseHtml',
          [IDENTIFY_MODE.CHROME_PLUGIN]: 'batchJavaParseHtml'
        }[data.identifyMode]
        const params = (() => {
          if (data.identifyMode === IDENTIFY_MODE.C) {
            return {
              bankName: data.infos[0]?.bankType ?? '',
              draftJson: JSON.stringify(data.infos.map(item => ({
                front: item.front,
                back: item.back
              })).filter(item => item.front && item.back)),
            }
          }
          if (data.identifyMode === IDENTIFY_MODE.C_HTML) {
            return {
              type: IDENTIFY_MODE.C_HTML,
              bankName: data.bankName,
              bankCode: data.bankCode,
              bankUrl: data.bankUrl,
              html: data.html
            }
          }
          if (data.identifyMode === IDENTIFY_MODE.CHROME_PLUGIN) {
            return {
              type: IDENTIFY_MODE.CHROME_PLUGIN,
              domId: data.domId
            }
          }
        })()
        const res = await toolsApi[api](params)
        // errorCode:  1、票据要素不齐全 2、没有票据 3、全部是商票 4、新一代票据缺少子票区间信息
        if (res.errorCode === 1) return this.$refs.recognizeTip.identifyFailAcceptorName()
        if (res.errorCode === 2) return this.$refs.recognizeTip.identifyFail()
        if (res.errorCode === 3) return this.$refs.recognizeTip.identifyFailCommercial()
        if (res.errorCode === 4) return this.$refs.recognizeTip.identifyFailNewDraft()
        this.afterJavaAnalysis(res, IDENTIFY_TYPE.BATCH, {})

        // 埋点
        if (data.identifyMode === IDENTIFY_MODE.CHROME_PLUGIN) {
          tracking({ type: '批量', discernId: res.draftDiscernId })
        }
      } catch (error) {
        showError(error)
      }
    },
    // 票据解析成功后逻辑处理
    async afterJavaAnalysis(res, identifyType, modify) {
      const {
        userClickFrontBackSameDraft, // 正背面不一致标识
        userClickNotSign, // 未签收标识
        userClickFastTrade = false, // 光票标识
      } = modify
      this.$message.success('识别成功')
      this.$event.emit(RECOFNITION_LISR_REFRESH) // 全局列表刷新事件

      if (identifyType === IDENTIFY_TYPE.SINGLE) {
        const identifyData = await ticketApi.getRecordByDraftNo({ draftDiscernIds: [res.draftDiscernId] })
        if (typeof userClickNotSign === 'boolean') {
          identifyData[0].draftSignInStatus = Number(userClickNotSign) // 用户手动确认网银票据状态
        }
        if (userClickFrontBackSameDraft) {
          // 手动点击正背面一致时埋点
          tracking({ manualSetFrontBackSameDraft: `用户：${this.$store.state.user?.corpInfo?.corpName ?? '未知'},票号：${identifyData[0].draftNo},子票区间：${identifyData[0].subTicketStart}-${identifyData[0].subTicketEnd},操作时间：${Date.now()}` })
        }
        if (userClickFastTrade) {
          // 手动点击这是光票时埋点
          tracking({ manualSetBareTicket: `用户：${this.$store.state.user?.corpInfo?.corpName ?? '未知'},票号：${identifyData[0].draftNo},子票区间：${identifyData[0].subTicketStart}-${identifyData[0].subTicketEnd},操作时间：${Date.now()}` })
        }
        this.updateOrderQianTag(res.draftDiscernId, identifyData[0])
        this.isRunSmartVerifyTicket(res.draftDiscernId, identifyData[0])
        this.noPublishAlert(identifyData)
        return
      }

      if (identifyType === IDENTIFY_TYPE.BATCH) {
        this.triggerBatchIssue(res.draftDiscernIds)
      }
    },

    // 查询是否可更新为签订单
    async updateOrderQianTag(draftDiscernId, identifyData) {
      const orderData = await ticketApi.getUpdateTagOrders(draftDiscernId)

      if (orderData.length === 1) {
        // 老票、新票 票号 子票区间全部一致 走单张更新
        if (!identifyData.draftType
        || (identifyData.draftType && orderData[0].draftNo === identifyData.draftNo && orderData[0].subTicketStart === identifyData.subTicketStart && orderData[0].subTicketEnd === identifyData.subTicketEnd)
        ) {
          this.$refs.updateQianTag.open({
            orderNos: orderData.map(item => item.orderNo),
            draftDiscernId,
            draftSignInStatus: identifyData.draftSignInStatus
          })
          return
        }

        // 否则走批量更新
        this.$refs.orderSyncUpdateRef.open({
          orderData,
          draftData: { ...identifyData, draftId: draftDiscernId }
        })
      } else if (orderData.length > 1) {
        this.$refs.orderSyncUpdateRef.open({
          orderData,
          draftData: { ...identifyData, draftId: draftDiscernId }
        })
      }
    },
    // 查询是否存在待签收订单，走智能验票流程
    async isRunSmartVerifyTicket(draftDiscernId, identifyData) {
      if (!identifyData) return
      if (this.userConfig?.openDiscernVerify) {
        try {
          const orderData = await ticketApi.getWaitSignOrder(draftDiscernId)
          // 0签收前识别 1签收后识别
          if (orderData && orderData.draftSignIn === 0) {
            this.$refs.smartVerifyTicketRef.open({
              orderData,
              identifyData,
            })
          }
        } catch (err) {
          // console.log(err)
        }
      }
    },
    // 不允许发布提示弹窗
    noPublishAlert(identifyData) {
      const defectsList = []
      identifyData.forEach(item => {
        const risks = []
        if (item.originalDefects) {
          const originalDefectList = item.originalDefects.split('|').filter(defect => defect)
          originalDefectList.forEach(defect => {
            const type = defect.split('_')[0]
            if (BACK_DEFECT_TYPE_NOT_ALLOW_NUM_MAP[type]) {
              risks.push(BACK_DEFECT_TYPE_VALUE_MAP[type])
            }
          })
        }
        if (isOverdue(item.maturityDate)) {
          risks.push('已过期')
        }
        if (hasNotAllowRisk(item.defectsNotify)) {
          risks.push('疑似异常')
        }
        if (hasAbnormalAccessRiskCorpsNotAllow(item.defectsNotify)) {
          risks.push('背书链有异常准入企业')
        }
        if (hasViolateSoftwareSecurityCorpsNotAllow(item.defectsNotify)) {
          risks.push('背书链有违反软件安全规则企业')
        }
        if (risks.length) {
          defectsList.push({
            draftNo: item.draftNo,
            riskStr: risks.join('，')
          })
        }
      })

      if (defectsList.length) {
        let content = '<p class="message">「保证待签收」「转让背书银行」「不可转让」「商票银行承兑」「已过期」「背书链有异常准入企业」「背书链有违反软件安全规则企业」的票据不可发布。</p>'
        defectsList.forEach(data => {
          content = `${content}<p class="message" style="margin-top:8px">票号：${data.draftNo}</p>`
          content = `${content}<div style="display:flex"><p style="flex-shrink:0">风险：</p><p class="message" style="color:red">${data.riskStr}</p></div>`
        })
        this.$confirm(content, '提示', {
          dangerouslyUseHTMLString: true,
          type: 'warning',
          showClose: false,
          customClass: 'no-header-msg',
          confirmButtonText: '我知道了',
          showCancelButton: false,
          width: 550
        })
      }
    },
    // 触发批量发布
    async triggerBatchIssue(draftDiscernIds) {
      const ticketList = await ticketApi.getRecordByDraftNo({
        draftDiscernIds
      })
      // eslint-disable-next-line max-len
      const publishList = ticketList.filter(item => !hasNotAllowRisk(item.defectsNotify) && !hasNotAllowDeffect(item.originalDefects) && !isOverdue(item.maturityDate) && !item.inRelease && !hasAbnormalAccessRiskCorpsNotAllow(item.defectsNotify) && !hasViolateSoftwareSecurityCorpsNotAllow(item.defectsNotify) && !item.inRiskBlack)
      if (!publishList.length) {
        return this.$message.warning('暂无可发布票据')
      }

      const msg = publishList.length !== draftDiscernIds.length ? '已经为您过滤不可发布票据！' : null
      // 票据信息是否有出票人、收款人
      const isShowNoInfoTip = !publishList.some(item => {
        const { front } = JSON.parse(item.draftJson)
        return front?.payee?.fullName && front?.ticketIssuer?.fullName
      })

      // 是否都被禁用了支付渠道，是-弹出联系客服弹窗，否-继续正常逻辑
      const data = await this.$store.dispatch('user/getPaymentAccountList')
      // 禁用渠道等于所有渠道数量则没有可用交易渠道
      if (data.filter(i => i.banStatus === BAN_STATUS.DISABLE.id).length === data.length) {
        this.$message.error('操作失败，全部渠道的交易权限被禁用，请重新操作或联系客服解决')
        return
      }

      this.$refs.batchIssueRef.open({
        publishList,
        msg,
        isShowNoInfoTip
      })
    },
  }
}
</script>
