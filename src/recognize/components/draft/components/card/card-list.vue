// 识别列表 - 卡片形式
<style lang="scss" scoped>
.ticket-info {
  display: flex;
  margin-right: -6px;
  margin-bottom: -12px;
  margin-left: -6px;
  flex-wrap: wrap;

  .ticket-item {
    margin-bottom: 12px;
    padding: 0 6px;
    width: 50%;
  }

  @media screen and (min-width: 1060px) {
    .ticket-item {
      width: 33.3333%;
    }
  }

  @media screen and (min-width: 1580px) {
    .ticket-item {
      width: 25%;
    }
  }

  .checkbox {
    margin-right: 14px;

    ::v-deep {
      .el-checkbox__inner {
        z-index: 0;
      }

      .el-checkbox__label {
        font-size: 16px;
        font-weight: 600;
        color: $color-text-primary;
      }
    }
  }
}

.ticket-detail {
  font-size: 14px;
  white-space: nowrap;
  color: $--color-primary;

  .svg-icon {
    margin-right: 3px;
    vertical-align: bottom;
  }

  &-btn {
    &.see-one {
      margin-left: 12px;
    }

    cursor: pointer;
  }
}

.no-search-data {
  margin: 124px auto 0;
  text-align: center;

  .no-search-data-tip {
    margin-top: -40px;
    font-size: 14px;
    color: $color-text-secondary;
  }
}

.btn-style {
  font-size: 16px;
}

.has-published {
  border-color: $color-D9D9D9;
  color: $color-text-light;
  background-color: $color-F4F5F6;
  cursor: not-allowed;
}

.more {
  margin-top: 30px;
  text-align: center;
  color: $color-warning;
}

.warning-text {
  color: $color-warning;
}
</style>

<style lang="scss">
.not-allow-tip-custom {
  p {
    margin-bottom: 5px;
    font-size: 16px;
  }

  .line {
    margin: 6px 0;
    height: 1px;
    background-color: $color-D9D9D9;
  }

  .del-btn {
    float: right;
    border-radius: 2px;

    ::v-deep.el-button--primary:not(.is-plain):not(.is-border) {
      font-weight: normal;
    }
  }
}
</style>

<template>
  <div>
    <!-- 票据信息卡片 -->
    <div
      v-infinite-scroll="load"
      infinite-scroll-disabled="noMore"
      :infinite-scroll-distance="20"
      class="ticket-info"
    >
      <div v-for="draft in list" :key="draft.id" class="ticket-item">
        <MarketDraftCard :data="draft">
          <template slot="checkbox">
            <el-checkbox v-model="draft.selected" class="checkbox" @change="handleSelectionChange" />
          </template>

          <!-- optating插槽 -->
          <template slot="optating">
            <div class="ticket-detail">
              <span class="ticket-detail-btn" @click="openTicketDetail(draft.draftDiscernId)">
                <icon type="chengjie-picture" :size="20" />票面
              </span>
              <span v-if="draft.inRelease" class="ticket-detail-btn see-one" @click="toSeeOne">
                <icon type="chengjie-jump" :size="20" />查看
              </span>
            </div>
            <!-- 未发布 -->
            <el-button
              v-if="!draft.inRelease"
              height="40px"
              width="96px"
              type="primary"
              :disabled="draft.notAllow"
              @click="!draft.selected ? issueDraft(draft) : publishSelected() "
            >
              <span v-if="!draft.notAllow"> {{ !draft.selected ? '一键发布' : '发布所选' }}</span>
              <el-tooltip v-else popper-class="not-allow-tip-custom" placement="top">
                <div v-if="!draft.riskCompany" slot="content">
                  <p>「保证待签收」「转让背书银</p>
                  <p>行」「不可转让」「商票银行</p>
                  <p>承兑」「已过期」「黑名单」</p>
                  <p>的票据不可发布</p>
                </div>
                <div v-else slot="content" style="width: 200px;">
                  该票背书链中有
                  <span v-if="draft.riskCompany === 1 || draft.riskCompany === 3" class="warning-text">异常准入企业</span>
                  <span v-if="draft.riskCompany === 1 || draft.riskCompany === 3">，</span>
                  <span v-if="draft.riskCompany === 2 || draft.riskCompany === 3" class="warning-text">违反软件安全规则企业</span>
                  <span v-if="draft.riskCompany === 2 || draft.riskCompany === 3">，</span>
                  不允许发布！鼠标移至 “风险” 位置可查看详情。
                </div>
                <span>{{ !draft.selected ? '一键发布' : '发布所选' }}</span>
              </el-tooltip>
            </el-button>
            <!-- 已发布  -->
            <el-button
              v-else
              class="btn-style has-published"
              height="40px"
              width="96px"
              @click="toSeeOne"
            >
              已发布
            </el-button>
          </template>
        </MarketDraftCard>
      </div>
    </div>

    <!-- 暂无搜索结果 -->
    <template v-if="!list.length && !loading">
      <div v-if="hasSearchArgs" class="no-search-data">
        <img
          src="https://oss.chengjie.red/web/imgs/recognize/recognition-no-data-hn.png"
          width="338"
          height="149"
          alt="暂无数据"
        >
        <div class="no-search-data-tip">暂无搜索结果</div>
      </div>

      <!-- noData -->
      <NoData v-else />
    </template>

    <p v-if="loading && list.length > 0" class="more">加载中...</p>

    <!-- 底部支持的银行 -->
    <SupportBank v-if="list.length" />
  </div>
</template>

<script>
import MarketDraftCard from '../market-draft-card.vue'
import NoData from '../no-data.vue' // 暂无数据组件
// 金额单位转换
import { yuan2wan } from '@/common/js/number'
// 时间处理
import {
  isOverdue,
  formatTime, // 格式化时间
} from '@/common/js/date'
import {
  toDefectsNotifyTempNew,
  toDefectStr // 将原始瑕疵字符串转为渲染字符串
} from '@/common/js/draft-flaw'
import ticketApi from '@recognize/apis/ticket'
import { throttle } from '@/common/js/util'
import SupportBank from '@recognize/components/support-bank/support-bank.vue' // 支持银行组件
import {
  BACK_DEFECT_TYPE_NOT_ALLOW_NUM_MAP, // 票据瑕疵类型 id 映射 notAllowedToPublish 是否可发布
} from '@recognize/constant'

// 默认查询参数
const defaultQuery = {
  pageSize: 12,
  pageNum: 1,
}

export default {
  name: 'draft-card',

  components: {
    MarketDraftCard,
    NoData,
    SupportBank
  },

  props: {
    // 是否存在查询参数
    hasSearchArgs: {
      type: Boolean,
      default: false
    },
    // 查询参数
    searchObj: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      loading: true, // 加载状态
      query: Object.assign({}, defaultQuery), // 请求参数
      list: [], // 识别记录列表数据
      totalRecord: 0, // 数据总条数
      noMore: false, // 没有更多了
    }
  },
  created() {
    // this.getList()
  },
  methods: {
    yuan2wan,
    toDefectStr,
    formatTime,
    handleSelectionChange() {
      this.$emit('selection-change', this.list.filter(item => item.selected))
    },
    // 发布票据
    issueDraft(data) {
      this.$emit('issue-draft', data)
    },
    publishSelected() {
      this.$emit('publish-selected')
    },
    // 点击票面
    openTicketDetail(id) {
      this.$emit('open-ticket-detail', id)
    },
    // 增加接口节流
    load() {
      throttle(this.getList, 1000, true)
    },
    // 获取列表数据
    async getList() {
      if (this.noMore) return
      this.loading = true
      const data = await ticketApi.getDraftList({
        ...this.query,
        ...this.searchObj,
      })
      data.rowList.forEach(item => {
        // item.draftJson = item.draftJson ? JSON.parse(item.draftJson) : {}
        item.selected = false // 是否已勾选
        item.defectsNotifyTemp = item.defectsNotify ? toDefectsNotifyTempNew(item.defectsNotify, item.fontSelfOwn, item.backSelfOwn) : []
        item.riskCompany = this.fetchRiskCompany(item.defectsNotify) // 是否检测到风险企业
        item.notAllow = this.validErrorNotAllow(item.defectsNotify) || this.validDeffectNotAllow(item.originalDefects) || isOverdue(item.maturityDate) || !!item.inRelease || !!item.riskCompany || item.inRiskBlack
      })
      this.loading = false
      this.query.pageNum++
      this.totalRecord = data.totalRecord
      this.list.push(...data.rowList)
      this.noMore = data.pageNum >= data.totalPage
      this.$emit('api-loaded')
    },
    // 重置查询
    reset() {
      this.query = Object.assign({}, defaultQuery)
      this.list = []
      this.noMore = false
      this.getList()
    },
    toSeeOne() {
      if (this.$ipc) {
        this.$ipc.send('PUSH_ROUTE_IN_MAIN_WINDOW', '/user-center/sale-draft?tab=1')
      } else {
        this.$router.push('/user-center/sale-draft?tab=1')
      }
    },
    // 是否含有不允许发布的票据异常
    validErrorNotAllow(defectsNotify) {
      if (!defectsNotify) {
        return false
      }
      defectsNotify = JSON.parse(defectsNotify)
      return Array.isArray(defectsNotify.riskCorps) && defectsNotify.riskCorps.length > 0
    },
    // 校验不允许发布的瑕疵
    validDeffectNotAllow(originalDefects) {
      if (!originalDefects) {
        return false
      }
      const originalDefectList = originalDefects.split('|').filter(item => item)
      const notAllow = originalDefectList.some(defect => {
        const type = defect.split('_')[0]
        return BACK_DEFECT_TYPE_NOT_ALLOW_NUM_MAP[type]
      })
      return notAllow
    },
    fetchRiskCompany(defectsNotify) {
      let riskCompany = 0
      if (!defectsNotify) {
        return riskCompany
      }
      defectsNotify = JSON.parse(defectsNotify)
      if (Array.isArray(defectsNotify.abnormalAccessRiskCorps) && defectsNotify.abnormalAccessRiskCorps.length > 0) {
        riskCompany = 1
      }
      if (Array.isArray(defectsNotify.violateSoftwareSecurityCorps) && defectsNotify.violateSoftwareSecurityCorps.length > 0) {
        riskCompany += 2
      }
      return riskCompany
    }
  },
}
</script>
