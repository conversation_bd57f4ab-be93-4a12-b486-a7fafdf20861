<!-- 签收前识别-背书确实提示 -->
<style lang="scss" scoped>
.defect-text {
  display: inline-block;
  border-radius: 4px;
  width: 20px;
  height: 20px;
  font-size: 12px;
  font-weight: bold;
  text-align: center;
  color: #FFFFFF;
  background: #FA8C16;
  line-height: 20px;
  vertical-align: middle;
  cursor: pointer;

  // .svg-icon {
  //   color: #FA8C16;
  // }

  // &.noBg {
  //   margin: 0;
  //   padding: 0;
  //   color: $color-text-primary;
  //   background: rgba(0 0 0 / 0%);
  // }
}
</style>

<style lang="scss" >
.defects-notify-tooltip {
  z-index: 9999 !important;
  max-height: 98vh;

  .tip-out {
    overflow-y: scroll;
    max-height: 95vh;

    &::-webkit-scrollbar {
      width: 0 !important;
      height: 0;
    }
  }
}
</style>

<template>
  <el-tooltip
    v-if="detail && (detail.discernType === 1 || detail.discernType === 3) && detail.draftSignIn === 0"
    popper-class="defects-notify-tooltip"
    placement="top"
    :popper-style="{'max-width': '362px'}"
  >
    <span class="defect-text g-ellipsis">
      <!--
        <icon
        type="chengjie-exclamation-circle"
        size="16"
        />
      -->
      前
    </span>
    <div slot="content">
      <div class="tip-out">
        <span>友情提示：部分票据签收前存在背书缺失情况，导致识别结果不准确，为避免因此产生的违约，<span style="color: #FA8C16;">建议您签收后核对背书或再次识别后挂单。</span>若识别当日网银实际没操作签收，隔天需重新识别发布。</span>
      </div>
    </div>
  </el-tooltip>
</template>

<script>
export default {
  name: 'defects-notify-tooltip',

  props: {
    detail: {
      type: Object
    },
  },

  data() {
    return {
    }
  },

  methods: {
  }
}
</script>
