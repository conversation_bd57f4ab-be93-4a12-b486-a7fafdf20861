<!-- 搜索组件 -->
<style lang="scss" scoped>
.search {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.input-with-select {
  display: flex;
  align-items: center;

  ::v-deep {
    .el-input-group__prepend {
      color: $color-text-primary;
      background-color: $color-FFFFFF;
    }

    .el-select {
      .el-input__inner {
        padding-left: 12px;
        width: 117px;
        color: $color-text-primary;
      }
    }

    .el-input {
      .el-input__inner {
        height: 36px;
        line-height: 36px;
      }
    }

    .el-select__caret {
      line-height: 36px;
    }

    .spicon-search {
      font-size: 22px;
    }

    .search-icon {
      margin-top: 11px;
      margin-left: 6px;
      color: $color-text-light;
    }
  }
}

.search-btn {
  font-size: 18px;
}
</style>

<style lang="scss" >
.input-with-select .el-select {
  .el-input__inner {
    margin: 0 -1px;
    width: 129px;
  }
}
</style>

<template>
  <div class="search">
    <div class="input-with-select">
      <el-select
        v-model="select"
        placeholder="请选择"
        @change="handleSelect"
      >
        <el-option
          v-for="item in searchConditionOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>

      <el-input
        v-if="!isTimeSelect"
        v-model="keyword"
        placeholder="请输入内容"
        class="input-with-select"
        :clearable="true"
        size="medium"
        @keyup.enter.native="search"
        @input="handleInput"
      >
        <icon
          slot="prefix"
          class="search-icon"
          type="chengjie-search"
        />
      </el-input>
      <el-date-picker
        v-else
        v-model="keyword"
        style="width: 260px;"
        type="daterange"
        size="medium"
        value-format="yyyy-MM-dd"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="handleDateChange"
      />
    </div>

    <!--
      <el-button
      class="search-btn"
      type="primary"
      width="90"
      @click="search"
      >
      搜索
      </el-button>
    -->
  </div>
</template>

<script>
import { wan2yuan } from '@/common/js/number'
import { debounce } from '@/common/js/util'

// 防抖时间
const debounceTime = 500
export default {
  name: 'search',

  data() {
    return {
      // 搜索的下拉选项
      searchConditionOptions: [
        {
          value: 'acceptorName',
          label: '承兑人',
        },
        {
          value: 'draftNo',
          label: '票号后六位',
        },
        {
          value: 'draftAmount',
          label: '金额（万）',
        },
        // 识别时间
        {
          value: 'recognizeTime',
          label: '识别时间',
        },
      ],
      keyword: '', // 搜索关键字
      select: 'acceptorName', // 下拉选择
    }
  },
  computed: {
    isTimeSelect() {
      return ['recognizeTime', 'publishTime'].includes(this.select)
    }
  },

  created() {
    // 防抖查询回调
    this.debounceSearch = debounce(
      this.search
      , debounceTime
    )
  },
  methods: {
    handleSelect() {
      if (this.keyword) {
        this.keyword = ''
        this.debounceSearch()
      }
    },
    handleDateChange(val) {
      this.keyword = val
      this.debounceSearch()
    },

    // 控制只能输入小数点后2位
    handleInput(val) {
      let value = val
      if (this.select === 'draftAmount') {
        value = (val.match(/^\d*(\.?\d{0,6})/g)[0]) || null// 通过正则过滤小数点后六位
      } else if (this.select === 'draftNo') {
        value = (val.match(/^\d{0,6}/g)[0]) || null// 通过正则过滤小数点后六位
      }
      this.keyword = value
      this.debounceSearch()
    },
    // 点击搜索
    search() {
      const obj = {
        acceptorName: '', // 承兑人 支持模糊搜索 可以为空表示全部
        draftNo: '', // 票号 支持模糊搜索 可以为空表示全部
        draftAmount: null, // 票面金额 精准搜索 可以为空表示全部
        discernStartTime: '', // 识别开始时间
        discernEndTime: '', // 识别结束时间
      }
      obj[this.select] = this.select === 'draftAmount' ? wan2yuan(this.keyword) : this.keyword
      if (this.select === 'recognizeTime' && this.keyword) {
        obj.discernStartTime = this.keyword[0]
        obj.discernEndTime = this.keyword[1]
      }
      obj.recognizeTime && delete obj.recognizeTime
      // else if (this.select === 'publishTime') {
      //   obj.publishStartTime = this.keyword[0]
      //   obj.publishEndTime = this.keyword[1]
      // }
      this.$emit('search-ticket', obj)
    }
  }
}
</script>
