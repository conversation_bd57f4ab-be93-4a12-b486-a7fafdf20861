<style lang="scss" scoped>
.qian-dialog {
  .header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 18px;
    font-weight: 600;
    color: $color-text-primary;
  }

  .icon-exclamation-circle {
    margin-right: 10px;
    color: $color-assist3;
  }

  .center {
    padding-left: 34px;
    font-size: 16px;
    color: $color-text-primary;

    .tip {
      display: flex;
      margin-bottom: 4px;
    }

    .icon-label {
      margin: 0 4px;
      color: #008489;
    }
  }

  ::v-deep {
    .el-dialog__header {
      display: none;
    }

    .el-dialog__body {
      padding: 32px;
    }
  }
}
</style>

<template>
  <el-dialog
    class="qian-dialog"
    :visible.sync="visible"
    :show-close="false"
    width="490px"
    @close="params = null"
  >
    <div class="header">
      <icon type="chengjie-exclamation-circle" class="icon-exclamation-circle" :size="24" />
      提示
    </div>
    <div class="center">
      <div class="tip">您本次单张识别的票据存在已发布订单。</div>
      <div class="tip">
        <span>是否自动为您更新为</span>
        <icon type="chengjie-tag-shi" class="icon-label" :size="20" />
        <span>订单？</span>
      </div>
      <div class="tip">更新后，订单成交几率将会翻倍哦~</div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">暂不更新</el-button>
      <el-button type="primary" @click="confirm">确认更新</el-button>
    </div>
  </el-dialog>
</template>

<script>
import ticketApi from '@recognize/apis/ticket'

export default {
  name: 'update-qian-tag',
  data() {
    return {
      visible: false,
      params: null
    }
  },
  methods: {
    open(params) {
      this.visible = true
      this.params = params
    },
    async confirm() {
      try {
        const res = await ticketApi.updateDiscernTag({
          ...this.params,
          forceUpdateTag: 0
        })
        this.visible = false

        const { failCount, failList } = res
        if (!failCount) {
          this.$message.success('更新成功！')
        } else {
          this.$message.error(failList[0]?.failReason || '更新失败')
        }
      } catch (e) {
        const { msg } = e.data
        this.$message.error(msg || '更新失败！')
      }
    }
  },
}
</script>
