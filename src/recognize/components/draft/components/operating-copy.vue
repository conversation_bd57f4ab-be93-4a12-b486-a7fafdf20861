<!-- eslint-disable max-lines -->
<!-- 操作按钮 -->
<style lang="scss" scoped>
.operating {
  padding: 16px;
  background-color: #FFFFFF;
}

.operating-head {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.operating-button-group {
  display: flex;
  gap: 12px;
  align-items: center;
  flex: 0 1 624px;
}

.operating-button {
  border: 1px solid $--color-primary;
  border-radius: 4px;
  flex: 0 1 200px;
  height: 48px;
  line-height: 48px;
  text-align: center;
  background: $color-FFFFFF;
  box-sizing: border-box;
  cursor: pointer;

  .operating-title {
    display: inline-flex;
    align-items: center;
    font-size: 20px;
    font-weight: 500;
    color: $--color-primary;
  }

  .operating-icon {
    margin-right: 6px;
  }

  &--hover {
    &:hover {
      background: $--color-primary;

      .operating-title {
        color: $color-FFFFFF;
      }
    }
  }
}

.operating-config {
  display: flex;
  align-items: center;
}

// @media screen and (max-width: 840px) {
//   .operating-button-group {
//     width: 100%;
//   }

//   .operating-button {
//     flex-grow: 1;
//     flex-shrink: 0;
//   }

//   .operating-config {
//     display: none;
//   }
// }

.loading {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99999;
  width: 100vw;
  height: 100vh;
  text-align: center;
  background: rgba($color: #000000, $alpha: 50%);
}

.loading-text {
  position: absolute;
  top: 50%;
  left: 50%;
  border-radius: 10px;
  width: 284px;
  height: 50px;
  font-size: 18px;
  color: $color-FFFFFF;
  background: rgba($color: #000000, $alpha: 75%);
  transform: translate(-50%);
  line-height: 50px;
}

// 更新为签手提示
::v-deep {
  .qian-dialog {
    .el-dialog__body {
      padding: 0 32px;

      .header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 18px;
        font-weight: 600;
        color: $color-text-primary;
      }

      .icon-exclamation-circle {
        margin-right: 10px;
        color: $color-assist3;
      }

      .center {
        padding-left: 34px;
        font-size: 16px;
        color: $color-text-primary;

        .tip {
          display: flex;
          margin-bottom: 4px;
        }

        .icon-label {
          margin: 0 4px;
          color: $color-008489;
        }
      }
    }

    .el-dialog__footer {
      .dialog-footer {
        margin-top: 10px;
      }
    }
  }
}
</style>

<style lang="scss">
.bare-ticket-dialog {
  .el-message-box__content {
    .redText {
      font-weight: 600;
      color: $color-warning;
    }
  }
}

.nowrap-message {
  .el-message__content {
    white-space: nowrap;
  }
}
</style>

<template>
  <div class="operating">
    <div class="operating-head">
      <div class="operating-button-group">
        <div class="operating-button operating-button--hover" @click="() => singleIdentify(1)">
          <p class="operating-title">
            <icon class="operating-icon" type="chengjie-bill" :size="24" />
            <span>单张识别</span>
          </p>
        </div>
        <div class="operating-button operating-button--hover" @click="multiIdentify">
          <p class="operating-title">
            <icon class="operating-icon" type="chengjie-batch" :size="24" />
            <span>批量识别</span>
          </p>
        </div>
        <!-- 网银同步 -->
        <EbankMerge
          ref="eBankMerge"
          :submit-draft="submitDraft"
          @identify-tip="identifyTip"
          @set-can-short-cut="canShortcut = true"
        />
        <!--
          <div class="operating-button operating-button--hover" @click="draftStockCb">
          <p class="operating-title">
          <icon class="operating-icon" type="chengjie-folderopen" :size="24" />
          <span>票据库存</span>
          </p>
          </div>
        -->
      </div>

      <div class="operating-config">
        <SupportedBanks />
        <VideoGuide />
        <DefectSettingIcon />
      </div>
    </div>

    <!-- loading -->
    <div v-show="showLoading" class="loading">
      <div class="loading-text">正在识别票据信息，请稍后... {{ countdown ? `${countdown}s` : '' }}</div>
    </div>

    <!-- 识别异常提示 -->
    <RecognizeTip ref="recognizeTip" />

    <!-- 展示更新签手订单 -->
    <el-dialog
      title=""
      :visible.sync="isShowQianDialog"
      width="490px"
      append-to-body
      :show-close="false"
      custom-class="qian-dialog"
    >
      <div class="header">
        <icon type="chengjie-exclamation-circle" class="icon-exclamation-circle" :size="24" />
        提示
      </div>
      <div class="center">
        <div class="tip">您本次单张识别的票据存在已发布订单。</div>
        <div class="tip">
          <span>是否自动为您更新为</span>
          <icon type="chengjie-tag-shi" class="icon-label" :size="20" />
          <span>订单？</span>
        </div>
        <div class="tip">更新后，订单成交几率将会翻倍哦~</div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isShowQianDialog = false">暂不更新</el-button>
        <el-button type="primary" @click="updateDiscernTag">确认更新</el-button>
      </div>
    </el-dialog>

    <SmartVerifyTicket ref="smartVerifyTicketRef" />
    <SingleIssue ref="singleIssueRef" />
    <BatchIssue ref="batchIssueRef" />
    <OrderSyncUpdate ref="orderSyncUpdateRef" />
  </div>
</template>

<script>
import {
  MARKET_DRAFT_RECOGNIZE_SHOW_LAYER, // 显示票据识别遮罩层
  MARKET_DRAFT_RECOGNIZE_DRAFT_RECOGNIZED, // 票据信息已识别
  MARKET_DRAFT_RECOGNIZE_MULTI_RECOGNIZED, // 批量识别成功
  MARKET_DRAFT_INSTALL_ERROR, // Dll安装错误事件
  MARKET_DRAFT_START_COUNTDOWN, // 开始识别倒计时
  SET_MAIN_WINDOW_TOPPING, // 设置窗口置顶
  SHORTCUT_RECOGNIZE, // 触发单张识别快捷键事件
  SHORTCUT_MULIT_RECOGNIZE, // 触发批量识别快捷键
  SHORTCUT_AUTO_SYNC, // 自动同步快捷键
  SHORTCUT_OPEN_RECOGNIZE_WINDOW, // 打开识别窗口快捷键
} from '@recognize/ipc-event-constant'
import EbankMerge from './e-bank-merge.vue' // 网银同步组件
import DefectSettingIcon from '@recognize/components/defect-setting/defect-setting-icon.vue'
import VideoGuide from '@/views/pages/draft-stock/components/video-guide/video-guide.vue'
import SupportedBanks from '@/views/pages/draft-stock/components/supported-banks/supported-banks.vue'
import RecognizeTip from '@recognize/components/recognize-tip/recognize-tip.vue' // 票据识别提示组件
import ticketApi from '@recognize/apis/ticket'
import { RECOFNITION_LISR_REFRESH } from '@recognize/event/modules/ticket' // 识别列表刷新事件
import { OPEN_USAGE_NOTICE } from '@recognize/event/modules/account' // 打开使用须知
import {
  BACK_DEFECT_TYPE_SHOW_NUM_MAP, // 票据瑕疵类型 id 映射 是否显示数字
  BACK_DEFECT_TYPE_VALUE_MAP, // 票据瑕疵类型 id 映射 名称
  BACK_DEFECT_TYPE_NOT_ALLOW_NUM_MAP, // 票据瑕疵类型 id 映射 notAllowedToPublish 是否可发布
  BAN_STATUS, // 渠道禁用状态
} from '@recognize/constant'
import { isOverdue } from '@/common/js/date'
import UserModel from '@recognize/models/user/index'
import toolsApi from '@recognize/apis/tools'
import { WINDOW_TOPPING } from '@recognize/constant-storage'
import Storage from '@/common/js/storage'
import { showError } from '@/utils/axios'
import SmartVerifyTicket from '@recognize/components/smart-verify-ticket/smart-verify-ticket.vue'
import SingleIssue from '@recognize/components/issue-draft/single-issue/single-issue.vue'
import BatchIssue from '@recognize/components/issue-draft/batch-issue/batch-issue.vue'
import OrderSyncUpdate from '@recognize/components/order-sync-update/order-sync-update.vue'
import { tracking } from '@/utils/util'
const isWindows = navigator.platform.indexOf('Win') > -1

export default {
  name: 'operating',

  components: {
    EbankMerge,
    RecognizeTip,
    DefectSettingIcon,
    VideoGuide,
    SmartVerifyTicket,
    SingleIssue,
    BatchIssue,
    OrderSyncUpdate,
    SupportedBanks
  },

  props: {
    userConfig: Object, // 用户设置信息
  },

  data() {
    return {
      showLoading: false, // 是否显示识别票据的loading
      isShowQianDialog: false, // 是否显示更新签手订单
      updateDiscernData: {}, // 更新签手标签的票面数据
      isContinueIdentify: false, // 是否为继续识别操作
      identifyStartTime: null, // 识别开始时间
      identifyEndTime: null, // 识别结束时间
      timer: null, // 计时器
      countdown: 0, // 识别倒计时
      canShortcut: true, // 快捷键是否可用
    }
  },

  computed: {
    // 是否已登录
    isLogined() {
      return true
    }
  },

  created() {
    // 接收窗口传来的参数
    const handler = () => {
      const shortcut = localStorage.getItem('EMIT_SHORTCUT')
      if (shortcut) {
        localStorage.removeItem('EMIT_SHORTCUT')
        this.handleShortcutEmit(shortcut)
      }
    }

    if (this.$ipc) {
      // 监听窗口打开事件，用来触发快捷键
      this.$ipc.on('DID_FINISH_LOAD', handler)
      this.$ipc.on('ACTIVATE_WINDOW', handler)
      // 监听票据已识别事件
      this.$ipc.on(MARKET_DRAFT_RECOGNIZE_DRAFT_RECOGNIZED, this.handlesingleIdentify)
      this.$ipc.on(MARKET_DRAFT_RECOGNIZE_MULTI_RECOGNIZED, this.handleMultiIdentify)
      // 监听dll安装错误事件
      this.$ipc.on(MARKET_DRAFT_INSTALL_ERROR, this.dllInstallError)
      this.$ipc.on(MARKET_DRAFT_START_COUNTDOWN, this.identifyCountdown)

      this.$ipc.on('EXTENSION_ACTIVATE', this.handleExtensionEmit)
    }
  },

  beforeDestroy() {
    if (this.$ipc) {
      this.$ipc.removeListener(MARKET_DRAFT_RECOGNIZE_DRAFT_RECOGNIZED, this.handlesingleIdentify)
      this.$ipc.removeListener(MARKET_DRAFT_RECOGNIZE_MULTI_RECOGNIZED, this.handleMultiIdentify)
      this.$ipc.removeListener(MARKET_DRAFT_INSTALL_ERROR, this.dllInstallError)
      this.$ipc.removeListener(MARKET_DRAFT_START_COUNTDOWN, this.identifyCountdown)
      this.$ipc.removeListener('EXTENSION_ACTIVATE', this.handleExtensionEmit)
    }
  },

  methods: {

    /**
     * 识别上报埋点
     * @param {Sting} brt // 上报类型描述
     * @param {Sting} bbn 银行名称
     * @param {number} bht 处理类型(1 单张 single / 2 批量 batch)
     * @param {Sting} bit 识别类型 C、JAVA、
     */
    cnzzFun(brt, bbn = '', bht = 1, bit = 'C') {
      // eslint-disable-next-line no-console
      console.log(bbn, bht, bit)
      // window.shukeCnzz && window.shukeCnzz.track({
      //   levelTwoTopic: 'draft_identify', // 二级标题
      //   brt,
      //   bbn: encodeURIComponent(bbn),
      //   bht: bht === 1 ? 'single' : 'batch',
      //   bit,
      // })
    },
    // 处理快捷键事件
    handleShortcutEmit(name) {
      if (!this.canShortcut) return
      this.canShortcut = false
      if (name === SHORTCUT_RECOGNIZE) {
        this.singleIdentify()
      } else if (name === SHORTCUT_MULIT_RECOGNIZE) {
        this.multiIdentify()
      } else if (name === SHORTCUT_AUTO_SYNC) {
        this.$refs.eBankMerge.clickEBankMerge()
      } else if (name === SHORTCUT_OPEN_RECOGNIZE_WINDOW) {
        this.canShortcut = true
      } else {
        this.canShortcut = true
      }
    },
    // 处理插件事件
    handleExtensionEmit(_, url) {
      if (typeof url !== 'string' || !url.startsWith('cr-erp')) return

      if (url.startsWith('cr-erp://single-recognize/')) {
        this.getJavaAnalysis({ domId: url.replace('cr-erp://single-recognize/', '') }, undefined, true)
      } else if (url.startsWith('cr-erp://batch-recognize/')) {
        this.getBatchJavaAnalysis({ domId: url.replace('cr-erp://batch-recognize/', '') }, true)
      }
    },

    // 识别倒计时
    identifyCountdown() {
      this.countdown = 40
      this.timer = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown -= 1
        } else {
          this.showLoading = false
          clearInterval(this.timer)
        }
      }, 1000)
    },

    // 判断是否是浏览器环境，禁用识别功能
    isBrowser() {
      if (this.$ipc) {
        return false
      }
      this.$message.warning('请在客户端内进行识别操作')
      return true
    },

    // 点击单张识别 type: 1=> 点击单张识别，2=》点击背面继续识别
    singleIdentify(type = 1) {
      if (!this.$store.state['recognize-common'].isAgreeUsageNotice) {
        this.$event.emit(OPEN_USAGE_NOTICE)
        this.canShortcut = true
        return
      }
      if (this.isBrowser()) {
        this.canShortcut = true
        return
      }
      if (!isWindows) {
        this.$message.error('当前系统不支持该功能！')
        // 设置是否能用快捷键
        this.canShortcut = true
        return
      }
      this.showLoading = true
      this.identifyStartTime = Date.now()
      try {
        // 调用 dll，打开识别窗口
        if (type === 1) {
          this.$ipc.send(MARKET_DRAFT_RECOGNIZE_SHOW_LAYER, '', 0)
        } else {
          this.$ipc.send(MARKET_DRAFT_RECOGNIZE_SHOW_LAYER, '', 4)
        }
      } catch (e) {
        // 设置是否能用快捷键
        this.canShortcut = true
        this.showLoading = false
        clearInterval(this.timer)
      }
    },

    // 单张票据识别成功
    handlesingleIdentify(event, data) {
      clearInterval(this.timer)
      if (!Storage.get(WINDOW_TOPPING)) {
        this.$ipc.send(SET_MAIN_WINDOW_TOPPING, true)
        this.$ipc.send(SET_MAIN_WINDOW_TOPPING, Storage.get(WINDOW_TOPPING))
      }
      // 设置是否能用快捷键
      this.canShortcut = true
      this.showLoading = false
      this.identifyEndTime = Date.now()
      if (!data) {
        this.isContinueIdentify = false
        this.$message.error('取消识别')
        this.cnzzFun('取消识别')
      } else if (data.code === 0) {
        // 判断有没有html字段 有的话 调用走后端解析逻辑
        if (data.bankName === '暂不支持此银行') {
          this.$message.error(data.bankName)
          return
        }
        if (data.html === '') return this.$refs.recognizeTip.identifyFail()
        if (data.html) return this.getJavaAnalysis(data)
        if (!data.infos?.length) return this.$refs.recognizeTip.identifyFail()
        this.getJavaAnalysis({ ...data.infos[0], isWeb: true })
      } else {
        this.isContinueIdentify = false
        // 识别失败
        this.$refs.recognizeTip.identifyFail()
        this.cnzzFun('未识别网银！', data.BankPoint)
      }
    },

    /**
     * java识别解析Html
     * @param {array} data 票据数据
     * @param {boolean} userClickNotSign 是否手动设置未签收状态
     * @param {boolean} fromExtension 数据是否来自插件
     * @param {boolean} userClickFastTrade 用户是否点击光票 false:未点击 true:点击
     * @param {boolean} isWeb 是否C++解析
     */
    async getJavaAnalysis(data = {}, userClickNotSign, fromExtension = false, userClickFastTrade = false) {
      try {
        const { bankType, bankName, bankCode, bankUrl, html, domId, isWeb } = data
        const isManualUnsigned = typeof (userClickNotSign) === 'boolean' ? { userClickNotSign } : {}
        let pasarm = { bankName, bankCode, bankUrl, html, type: 1 }
        if (fromExtension) pasarm = { type: 2, domId }
        if (isWeb) {
          const { front, back } = data || {}
          if (!front && !back) return this.$refs.recognizeTip.identifyFail()
          const draftJson = JSON.stringify({ front, back })
          pasarm = { draftJson, bankName: bankType }
        }
        pasarm = { ...pasarm, ...isManualUnsigned, userClickFastTrade }
        const api = isWeb ? 'webSwitchJavaAnalysis' : 'JavaParseHtml'
        const res = await toolsApi[api](pasarm)
        // errorCode:  1、票据要素不齐全 2、新票没有子票区间
        if (res.errorCode === 1 || res.errorCode === 2) {
          this.isContinueIdentify = false
          this.$refs.recognizeTip.identifyFail()
          return
        }

        // 点击继续识别且没有背书信息或者没有票号，识别失败
        if (this.isContinueIdentify && (res.emptyBackDraftNo || res.emptyBackEndorseHistory)) {
          this.$refs.recognizeTip.continueIdentifyFail()
          this.isContinueIdentify = false
          return
        }

        // 没有背面票号，是否走继续识别逻辑
        if (res.emptyBackDraftNo) {
          // 显示继续识别弹窗
          if (fromExtension) {
            this.$message.error('识别失败，请联系您的客户经理')
            return
          }
          this.continueIdentify(data)
          return
        }

        // 没有背书信息，是否走光票逻辑
        if (res.emptyBackEndorseHistory) {
          this.bareTicket(data, userClickNotSign, fromExtension)
          return
        }

        this.isContinueIdentify = false
        this.javaSubmitDraft(res, 1, userClickNotSign)
        // 统计插件的识别次数
        if (fromExtension) {
          tracking({
            type: '单张',
            discernId: res.draftDiscernId
          })
        }
      } catch (error) {
        const { code, msg } = error?.data ?? {}
        // eslint-disable-next-line no-magic-numbers
        if (code === 700) {
          this.$confirm(`<div>网银中此票据状态显示为：【${msg}】</div><div style="color:red">请确认此票据是否已在网银中签收？</div>`, '提示', {
            dangerouslyUseHTMLString: true,
            type: 'warning',
            showClose: true,
            iconPosition: 'title',
            cancelButtonText: '未签收',
            confirmButtonText: '已签收',
            distinguishCancelAndClose: true,
          }).then(() => {
            this.getJavaAnalysis(data, false, fromExtension, userClickFastTrade)
          })
            .catch(action => {
              if (action === 'close') return
              this.getJavaAnalysis(data, true, fromExtension, userClickFastTrade)
            })
          return
        }
        showError(error)
      }
    },

    /**
     * java识别 接口返回值判断
     * @param {array} parseHtmlData 票据解析后数据
     * @param {number} identifyType  1=>单张识别；2=>批量识别
     * @param {boolean} isManualUnsigned 签收状态 签收前后
     */
    async javaSubmitDraft(parseHtmlData, identifyType = 1, isManualUnsigned) {
      // 全局列表刷新事件
      this.$event.emit(RECOFNITION_LISR_REFRESH)
      if (identifyType === 1) {
        this.$message.success('识别成功')
        const identifyData = await ticketApi.getRecordByDraftNo({ draftDiscernIds: [parseHtmlData.draftDiscernId] })
        if (typeof (isManualUnsigned) === 'boolean') {
          identifyData.draftSignInStatus = Number(isManualUnsigned) // 用户手动确认网银票据状态
        }
        // 是否更新为带签标签的票
        this.getUpdateOrder(parseHtmlData.draftDiscernId, identifyData)

        // 判断是否是待签收状态-是否需要打开智能验票
        try {
          if (this.userConfig?.openDiscernVerify) {
            // eslint-disable-next-line max-depth
            if (!Array.isArray(identifyData) || identifyData.length === 0) {
            // eslint-disable-next-line no-console
              console.log('未从识别记录中找到此张票')
              return
            }
            const orderData = await ticketApi.getWaitSignOrder(parseHtmlData.draftDiscernId)
            // draftSignIn 0:签收前识别 1:签收后识别
            // eslint-disable-next-line max-depth
            if (orderData && orderData.draftSignIn === 0) {
              this.$refs.smartVerifyTicketRef.open({ identifyData: identifyData[0], orderData })
            }
          }
        } catch (err) {
          // eslint-disable-next-line no-console
          console.log(err)
        }
        this.notAllowPublishAlert(identifyData)
      } else if (identifyType === 2) {
        // 待开发
        this.$message.success('识别成功')
        this.getMulitList(parseHtmlData.draftDiscernIds)
      }
    },

    // 点击批量识别
    multiIdentify() {
      if (!this.$store.state['recognize-common'].isAgreeUsageNotice) {
        this.$event.emit(OPEN_USAGE_NOTICE)
        this.canShortcut = true
        return
      }
      if (this.isBrowser()) {
        this.canShortcut = true
        return
      }
      if (!isWindows) {
        this.$message.error('当前系统不支持该功能！')
        // 设置是否能用快捷键
        this.canShortcut = true
        return
      }

      this.showLoading = true
      this.identifyStartTime = Date.now()
      try {
        this.$ipc.send(MARKET_DRAFT_RECOGNIZE_SHOW_LAYER, '', 3)
      } catch (e) {
        // 设置是否能用快捷键
        this.canShortcut = true
        this.showLoading = false
        clearInterval(this.timer)
      }
    },

    // 批量识别失败提示
    identifyFailMsg(text, funName = 'identifyFail') {
      this.$refs.recognizeTip[funName]()
    },

    // 处理批量识别票据事件
    handleMultiIdentify(event, data) {
      // async handleMultiIdentify(event) {
      clearInterval(this.timer)
      if (!Storage.get(WINDOW_TOPPING)) {
        this.$ipc.send(SET_MAIN_WINDOW_TOPPING, true)
        this.$ipc.send(SET_MAIN_WINDOW_TOPPING, Storage.get(WINDOW_TOPPING))
      }
      // 设置是否能用快捷键
      this.canShortcut = true
      this.showLoading = false
      this.identifyEndTime = Date.now()
      if (!data) {
        this.$message.error('取消识别')
        this.cnzzFun('取消识别', '', 2)
      } else if (data.code === -3) {
        // 批量识别不支持银行
        this.$refs.recognizeTip.identifyFail()
        this.cnzzFun('暂不支持该银行', data.BankPoint, 2)
      } else if (data.code === 0) {
        // 判断有没有html字段 有的话 调用走后端解析逻辑
        if (data?.infos && data?.infos[0]?.bankType === '暂不支持此银行') return this.$refs.recognizeTip.identifyFail()
        if (data.html === '') return this.identifyFailMsg('未识别到票据')
        if (data.html) return this.getBatchJavaAnalysis(data)
        // 前端解析逻辑
        if (!data.infos?.length) return this.$refs.recognizeTip.identifyFail()
        this.getBatchJavaAnalysis({ ...data, isWeb: true })
      } else {
        // 识别失败
        this.identifyFailMsg('未识别网银！')
        this.cnzzFun('未识别网银！', data.BankPoint, 2)
      }
    },

    /**
     * java批量识别解析Html
     * @param {array} data 票据数据
     * @param {boolean} fromExtension 数据是否来自插件
     */
    async getBatchJavaAnalysis(data, fromExtension = false) {
      try {
        const { bankName, bankCode, bankUrl, html, domId, infos, isWeb } = data
        let pasarm = { type: 1, bankName, bankCode, bankUrl, html }
        if (fromExtension) pasarm = { type: 2, domId }
        if (isWeb) {
          const { bankType } = infos[0]
          const draftJson = []
          for (const i in infos) {
            const { front, back } = infos[i] || {}
            front && back && draftJson.push({ front, back })
          }
          pasarm = { draftJson: JSON.stringify(draftJson), bankName: bankType }
        }
        const api = isWeb ? 'batchwebSwitchJavaAnalysis' : 'batchJavaParseHtml'
        const res = await toolsApi[api](pasarm)
        // errorCode:  1、有票据要素不齐全2、没有票据 3、全部是商票4、新一代票据缺少子票区间信息
        if (res.errorCode === 1) return this.identifyFailMsg('识别不到票据/票据三要素不齐全', 'identifyFailAcceptorName')
        if (res.errorCode === 2) return this.identifyFailMsg('未识别到票据')
        if (res.errorCode === 3) return this.identifyFailMsg('商票不支持批量识别', 'identifyFailCommercial')
        if (res.errorCode === 4) return this.identifyFailMsg('新一代票据缺少子票区间信息', 'identifyFailNewDraft')
        this.javaSubmitDraft(res, 2)
        if (fromExtension) {
          tracking({
            type: '批量',
            discernId: res.draftDiscernId
          })
        }
      } catch (error) {
        showError(error)
      }
    },

    /**
     * 自动同步
     * @param {array} data 票据数据
     */
    async submitDraft(data) {
      const { infos = [] } = data
      const { bankType } = infos[0]
      const draftJson = []
      for (const i in infos) {
        const { front, back } = infos[i] || {}
        draftJson.push({ front, back })
      }
      const pasarm = { draftJsonList: JSON.stringify(draftJson), bankName: bankType }
      const res = await toolsApi.autoSyncParseData(pasarm)
      if (res?.successCount) return this.$message.success(`成功同步${res.successCount}张票据`)
      this.$message.error('同步票据失败')
    },

    notAllowPublishAlert(list) {
      let defectsList = []
      list.forEach(data => {
        let riskName = ''
        if (data.originalDefects) {
          const originalDefectList = data.originalDefects.split('|').filter(item => item)
          originalDefectList.forEach(defect => {
            const type = defect.split('_')[0]
            if (BACK_DEFECT_TYPE_NOT_ALLOW_NUM_MAP[type]) {
              riskName = riskName + (riskName.length > 0 ? '，' : '') + BACK_DEFECT_TYPE_VALUE_MAP[type]
            }
          })
        }
        if (isOverdue(data.maturityDate)) {
          riskName = `${riskName + (riskName.length > 0 ? '，' : '')}已过期`
        }
        if (this.validErrorNotAllow(data.defectsNotify)) {
          riskName = `${riskName + (riskName.length > 0 ? '，' : '')}疑似异常`
        }
        if (riskName.length) {
          defectsList.push({
            draftNo: data.draftNo,
            riskName
          })
        }
      })
      if (defectsList.length) {
        let content = '<p class="message">「保证待签收」「转让背书银行」「不可转让」「商票银行承兑」「已过期」「疑似异常」的票据不可发布。</p>'
        defectsList.forEach(data => {
          content = `${content}<p class="message" style="margin-top:8px">票号：${data.draftNo}</p>`
          content = `${content}<div style="display:flex"><p style="flex-shrink:0">风险：</p><p class="message" style="color:red">${data.riskName}</p></div>`
        })
        this.$confirm(content, '提示', {
          dangerouslyUseHTMLString: true,
          type: 'warning',
          showClose: false,
          customClass: 'no-header-msg',
          confirmButtonText: '我知道了',
          showCancelButton: false,
          width: 490
        })
      }
    },

    // 获取批量识别的票据让后打开批量发布窗口
    async getMulitList(draftNoList) {
      const ticketData = await ticketApi.getRecordByDraftNo({
        draftDiscernIds: draftNoList
      })
      const ticketList = ticketData.filter(item => !this.validErrorNotAllow(item.defectsNotify) && !this.validDeffectNotAllow(item.originalDefects) && !isOverdue(item.maturityDate) && !item.inRelease)
      let msg = ticketList.length !== draftNoList.length ? '已经为您过滤不可发布票据！' : null
      if (!ticketList.length) {
        return this.$message.warning('暂无可发布票据')
      }
      // 票据信息是否有出票人、收款人
      let isShowNoInfoTip = ticketList.some(item => {
        let draftJson = JSON.parse(item.draftJson)
        const { front } = draftJson
        return front?.payee?.fullName && front?.ticketIssuer?.fullName
      })

      // 是否都被禁用了支付渠道，是-弹出联系客服弹窗，否-继续正常逻辑
      const data = await this.$store.dispatch('user/getPaymentAccountList')
      // 禁用渠道等于所有渠道数量则没有可用交易渠道
      if (data.filter(i => i.banStatus === BAN_STATUS.DISABLE.id).length === data.length) {
        this.$message.error('操作失败，全部渠道的交易权限被禁用，请重新操作或联系客服解决')
        return
      }

      this.$refs.batchIssueRef.open({ publishList: ticketList, msg, isShowNoInfoTip: !isShowNoInfoTip })
    },

    // dll加载异常
    dllInstallError() {
      this.showLoading = false
      this.canShortcut = true
      this.$refs.recognizeTip.appAbnormal()
    },

    // 查询是否可更新为签订单
    async getUpdateOrder(draftId, draftData) {
      const res = await ticketApi.getUpdateTagOrders(draftId)
      if (!draftData.length) return
      draftData = draftData[0]
      let updateDiscernData = {
        orderData: res,
        draftData: { ...draftData, draftId }
      }

      if (res.length === 1) {
        // 只有一张时增加票号和子票区间的判断
        const history1 = res[0]
        if (draftData.draftNo === history1.draftNo) {
          // 票号相同的情况下如果是老票则直接弹单张,新票需要判断子票区间
          if (draftData.draftType) {
            // eslint-disable-next-line max-depth
            if (draftData.subTicketStart !== history1.subTicketStart || draftData.subTicketEnd !== history1.subTicketEnd) {
              // 子票区间相同弹单张,不同弹批量
              this.$refs.orderSyncUpdateRef.open(updateDiscernData)
              return
            }
          }
        } else {
          // 票号不同直接弹批量
          this.$refs.orderSyncUpdateRef.open(updateDiscernData)
          return
        }

        const { draftNo, draftAmount, acceptorName, maturityDate, originalDefects, discernSource, draftSignInStatus } = draftData
        this.replaceQianOrderData = {
          draftNo,
          draftAmount,
          acceptorName,
          expiryDate: maturityDate,
          defect: this.toDefectStr(originalDefects) || '无瑕疵',
          bank: discernSource,
        }
        this.updateDiscernData = {
          orderNos: [res[0].orderNo],
          draftDiscernId: draftId,
          draftSignInStatus
        }
        this.isShowQianDialog = true
      } else if (res.length > 1) {
        this.$refs.orderSyncUpdateRef.open(updateDiscernData)
      }
    },

    // 更新签手订单
    async updateDiscernTag() {
      try {
        const res = await ticketApi.updateDiscernTag({ ...this.updateDiscernData, forceUpdateTag: 0 })
        const { failCount, failList } = res
        this.isShowQianDialog = false
        if (!failCount) {
          this.$message.success('更新成功！')
        } else {
          this.$message.error(failList[0]?.failReason || '更新失败')
        }
      } catch (e) {
        const { msg } = e.data
        this.$message.error(msg || '更新失败！')
      }
    },

    // 瑕疵显示转换
    toDefectStr(str) {
      let res = str && str.split('|').map(item => BACK_DEFECT_TYPE_VALUE_MAP[item.split('_')[0]] + (BACK_DEFECT_TYPE_SHOW_NUM_MAP[item.split('_')[0]] ? `(${item.split('_')[1]})` : ''))
        .join('，')
      return res || ''
    },

    // 网银识别提示
    identifyTip() {
      this.$refs.recognizeTip.identifyFail()
    },

    // 点击票据库存回调
    draftStockCb() {
      if (!this.$store.state['recognize-common'].isAgreeUsageNotice) {
        this.$event.emit(OPEN_USAGE_NOTICE)
        return
      }
      if (!this.isLogined) {
        UserModel.openLoginDialog()
        return
      }
      if (this.$ipc) {
        this.$ipc.send('PUSH_ROUTE_IN_MAIN_WINDOW', '/user-center/draft-stock')
      } else {
        this.$router.push('/user-center/draft-stock')
      }
    },

    // 继续识别确认弹窗
    continueIdentify(data) {
      this.$confirm('<div>未获取到该票据的背面信息。</div><div>请先在网银中 <span class="redText">打开该票的背面，然后点击下方的【已打开背面，继续识别】</span>按钮。</div>', '提示', {
        distinguishCancelAndClose: true,
        dangerouslyUseHTMLString: true,
        type: 'warning',
        iconPosition: 'title',
        confirmButtonText: '已打开背面，继续识别',
        cancelButtonText: '取消',
        showClose: false,
        closeOnClickModal: false,
        closeOnPressEscape: false,
        customClass: 'bare-ticket-dialog'
      }).then(() => {
        // 继续识别
        this.isContinueIdentify = true
        this.singleIdentify(2)
        this.cnzzFun('背面没有背书列表和票号', data.BankPoint)
      })
    },

    // 光票确认弹窗
    bareTicket(data, userClickNotSign, fromExtension) {
      this.$confirm('<div>未获取到该票据的背面信息。</div><div>请确认该票是否是光票（即没有任何背书转让记录）。</div><div>若该票不是光票，请先在网银中 <span class="redText">打开该票的背面，然后点击下方【已打开背面，继续识别】。</span></div>', '提示', {
        distinguishCancelAndClose: true,
        dangerouslyUseHTMLString: true,
        type: 'warning',
        iconPosition: 'title',
        confirmButtonText: '已打开背面，继续识别',
        cancelButtonText: '这是光票',
        showClose: false,
        closeOnClickModal: false,
        closeOnPressEscape: false,
        customClass: 'bare-ticket-dialog'
      }).then(() => {
        this.cnzzFun('点击不是光票', data.BankPoint)
        // 再次识别
        if (fromExtension) {
          this.$message.error('识别失败，请联系您的客户经理')
          return
        }
        this.isContinueIdentify = true
        this.singleIdentify(2)
      })
        .catch(err => {
          if (err === 'cancel') {
            // 光票，提交数据
            // 不是浏览器插件  没有html字段 就是前端识别
            this.getJavaAnalysis(data, userClickNotSign, fromExtension, true)
          }
        })
    },

    // 校验不允许发布的瑕疵
    validDeffectNotAllow(originalDefects) {
      if (!originalDefects) {
        return false
      }
      const originalDefectList = originalDefects.split('|').filter(item => item)
      const notAllow = originalDefectList.some(defect => {
        const type = defect.split('_')[0]
        return BACK_DEFECT_TYPE_NOT_ALLOW_NUM_MAP[type]
      })
      return notAllow
    },

    // 是否含有不允许发布的票据异常
    validErrorNotAllow(defectsNotify) {
      defectsNotify = JSON.parse(defectsNotify)
      return Array.isArray(defectsNotify.riskCorps) && defectsNotify.riskCorps.length > 0
    }
  }
}
</script>
