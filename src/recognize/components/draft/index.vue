<style lang="scss" scoped>
.recognition-page {
  overflow: hidden;
  height: 100%;
}
</style>

<template>
  <div class="recognition-page">
    <!-- 推送通知组件 -->
    <!-- <Notification v-if="userConfig" :user-config="userConfig" /> -->

    <!-- 头部操作按钮 -->
    <Operating
      ref="operating"
      :user-config="userConfig"
      @open-release-limit="$refs.releaseLimitDialogRef.open()"
    />

    <!-- 识别列表 -->
    <RecognitionList
      ref="recognitionList"
      @get-holidays="getHolidays"
      @set-ticket-expire-days="setTicketExpireDays"
      @open-release-limit="$refs.releaseLimitDialogRef.open()"
    />

    <!-- 使用须知组件 -->
    <UsageNotice />

    <!-- 限制发布弹窗 -->
    <ReleaseLimitDialog
      ref="releaseLimitDialogRef"
      btn-text="我知道了"
      absolute-top="0px"
      :is-close="true"
    />
  </div>
</template>

<script>
// import Notification from '@recognize/components/websocket-notification/websocket-notification.vue'
import Operating from './components/operating.vue' // 头部操作按钮组件
import RecognitionList from './components/recognition-list.vue' // 识别列表组件

import {
  CONFIG_SETTING_CHANGE, // 触发常规设置、通知设置修改
} from '@recognize/ipc-event-constant'
import userApi from '@recognize/apis/user'
import { RECOFNITION_LISR_REFRESH } from '@recognize/event/modules/ticket' // 识别列表刷新事件
// import { EVENT_SITE_RERENDER } from '@recognize/event/modules/site'
import commonApi from '@recognize/apis/common'
import { GENERAL_SETTING_USER, USER_MOBILE, IS_ASYNC_GENERAL_SETTING } from '@recognize/constant-storage'
import Storage from '@/common/js/storage'
import UsageNotice from '@recognize/components/usage-notice/usage-notice.vue' // 使用须知组件
import ReleaseLimitDialog from '@/views/components/release-limit-dialog/release-limit-dialog.vue' // 不允许批量识别弹窗
import {
  getAdjustDays, // 获取调整天数
} from '@/common/js/draft-math'

export default {
  name: 'draft-page',

  components: {
    Operating,
    RecognitionList,
    // Notification,
    UsageNotice,
    ReleaseLimitDialog
  },

  data() {
    return {
      userInfo: null, // 用戶信息
      userConfig: null, // 用户配置信息
      holidays: [], // 节假日列表
    }
  },

  async created() {
    this.$event.on(CONFIG_SETTING_CHANGE, this.getConfigFresh)
    if (this.$ipc) {
      this.$ipc.on(CONFIG_SETTING_CHANGE, this.getConfigFresh)
    }

    await this.getLoaclSetting()
    this.getConfig()
  },

  destroyed() {
    if (this.$ipc) {
      this.$ipc.removeListener(CONFIG_SETTING_CHANGE, this.getConfigFresh)
    }
  },

  methods: {
    // 获取节假日列表
    async getHolidays(callback) {
      // 节假日列表
      this.holidays = await commonApi.getHolidays()
      callback && callback()
    },
    // 修改到期时间
    setTicketExpireDays(val, callback) {
      let adjustDays = getAdjustDays(val, this.holidays)
      // 计息天数
      callback(adjustDays)
    },

    // 获取用户配置信息
    async getConfig() {
      const res = await userApi.getConfig()
      this.userConfig = res
    },

    // 配置更新时获取,若修改保留天数,刷新列表
    async getConfigFresh(event, obj) {
      await this.getConfig()
      if (obj?.type === 'days') {
        this.$event.emit(RECOFNITION_LISR_REFRESH)
      }
      // 刷新默认回款账户配置参数
      this.$store.dispatch('common/getNewVersionDraftConfig')
    },

    // 获取常规配置本地数据
    async getLoaclSetting() {
      const isAsyncGeneralSetting = Storage.get(IS_ASYNC_GENERAL_SETTING, 'boolean', false)
      if (!isAsyncGeneralSetting) {
        const mobile = Storage.get(USER_MOBILE, 'string')
        const localSetting = Storage.get(GENERAL_SETTING_USER) || {}
        const setting = localSetting[mobile] || {}
        await this.asyncLocal2Server(setting)
      }
    },

    // 同步本地设置数据
    async asyncLocal2Server(setting = {}) {
      if (Object.keys(setting).length) {
        const {
          recognitionRecordKeepDays,
          singleShortcutKeyEnable,
          singleShortcutKey,
          multiShortcutKeyEnable,
          multiShortcutKey,
          autoSyncKeyEnable,
          autoSyncKey,
          mosaicSet: { receiverMosaic, drawerMosaic },
          textRemindPendingConfirm,
          textRemindPendingPay,
          textRemindPendingEndorse,
          textRemindPendingSignIn,
          autoDelete,
        } = setting
        await userApi.putConfig({
          recognitionRecordKeepDays, // 识别记录保留时长
          singleShortcutKeyEnable, // 是否启用单张识别快捷键
          singleShortcutKey, // 单张识别快捷键
          multiShortcutKeyEnable, // 是否启用批量识别快捷键
          multiShortcutKey, // 用批量识别快捷键
          autoSyncKeyEnable, // 是否启用自动同步快捷键
          autoSyncKey, // 用自动同步快捷键
          buyerMosaic: receiverMosaic ? 1 : 0, // 收款人打码
          sellerMosaic: drawerMosaic ? 1 : 0, // 出票人打码
          textRemindPendingConfirm, // 待确认消息提醒
          textRemindPendingPay, // 待打款消息提醒
          textRemindPendingEndorse, // 待背书消息提醒
          textRemindPendingSignIn, // 待签收消息提醒
          autoDeleteAfterRelease: autoDelete, // 发布后自动删除
        })
        Storage.set(IS_ASYNC_GENERAL_SETTING, true)
      }
    },
  }
}
</script>
