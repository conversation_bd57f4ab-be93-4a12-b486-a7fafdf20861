<!-- 瑕疵设置 -->
<style lang="scss" scoped>
.defect-setting-page ::v-deep {
  .layout-header {
    background: $color-FFFFFF;
  }
}

.defect-setting-container {
  .top-tips {
    padding: 9px 16px;
    font-size: 16px;
    line-height: 24px;
    background-color: $color-E6F3F3;

    @include flex-sbc;

    .left {
      @include flex-cc;
    }

    .icon {
      margin-right: 10px;
      font-size: 20px;
      color: $color-008489;
    }

    .btn {
      @include example-underline;
    }
  }
}

// 覆盖默认的单选样式
.el-radio-group {
  display: flex;
  margin-top: 12px;

  .el-radio-button {
    flex: 1;
  }

  ::v-deep {
    .el-radio-button__inner {
      border-color: $color-D9D9D9;
      width: 100%;
      height: 42px;
      font-size: 16px;
      user-select: none;

      @include flex-cc;
    }
  }
}

::v-deep .el-dialog__body {
  padding-bottom: 20px;
}

.main {
  overflow-y: auto;
  margin-top: 12px;
  padding-bottom: 16px;
  max-height: 540px;
}
</style>

<template>
  <el-dialog
    title="风险设置"
    :visible.sync="dialogVisible"
    append-to-body
    width="1024px"
    class="defect-setting-page"
  >
    <div class="defect-setting-container special-tabs">
      <div class="top-tips">
        <div class="left">
          <icon type="sdicon-info-circle" class="icon" />
          <p>支持自定义添加自有户、背书手数、承兑人黑名单、收款人黑名单、背书人黑名单、敏感行业风险提示。</p>
        </div>
        <div class="btn" @click="openSyncConfig">同步配置到其他账号</div>
      </div>
      <el-radio-group v-model="currentSelect" @change="changeStatus">
        <el-radio-button v-for="item in tabList" :key="item.value" :label="item.value">{{ item.label }}</el-radio-button>
      </el-radio-group>
      <div class="main">
        <component :is="getListValue('component', currentSelect)" ref="componentRef" :type="getListValue('type', currentSelect)" />
      </div>
    </div>

    <!-- 同步配置到其他账户弹窗 -->
    <SyncConfig ref="syncConfig" />
  </el-dialog>
</template>

<script>
import DefectExplain from './components/defect-explain.vue' // 瑕疵说明
import SyncConfig from './components/sync-config.vue' // 同步配置到其他账户弹窗
import EndorseCount from './components/endorse-count.vue' // 背书手数
import BlacklistSetting from './components/blacklist-setting.vue' // 黑名单设置
import {
  BLACKLIST_TYPE // 黑名单类型
} from '@recognize/constant'

export default {
  name: 'defect-setting',
  components: {
    DefectExplain,
    EndorseCount,
    BlacklistSetting,
    SyncConfig,
  },
  data() {
    return {
      dialogVisible: false,
      // 当前选中的tab
      currentSelect: 1,
      // tab列表
      tabList: [
        {
          label: '瑕疵说明',
          value: 1,
          component: 'DefectExplain'
        }, {
          label: '自有户',
          value: 2,
          component: 'BlacklistSetting',
          type: BLACKLIST_TYPE.OWN_ACCOUNT.id,
        }, {
          label: '背书手数',
          value: 3,
          component: 'EndorseCount'
        }, {
          label: '承兑人黑名单',
          value: 4,
          component: 'BlacklistSetting',
          type: BLACKLIST_TYPE.ACCEPTOR.id,
        }, {
          label: '收款人黑名单',
          value: 5,
          component: 'BlacklistSetting',
          type: BLACKLIST_TYPE.PAYEE.id,
        }, {
          label: '背书人黑名单',
          value: 6,
          component: 'BlacklistSetting',
          type: BLACKLIST_TYPE.ENDORSER.id,
        }, {
          label: '敏感行业',
          value: 7,
          component: 'BlacklistSetting',
          type: BLACKLIST_TYPE.SENSITIVE.id,
        },
      ]
    }
  },
  methods: {
    open(type) {
      this.dialogVisible = true
      if (type) {
        this.currentSelect = type
      }
    },

    /**
     * 返回数组某个字段的值
     * @param {string} key //需要返回的字段
     * @param {number} value // 要返回是哪个对象中的
     * @returns {string} 返回对应字段的值
     */
    getListValue(key, value) {
      const current = this.tabList.filter(item => item.value === value)
      return current.length ? current[0][key] : ''
    },
    // 切换tab 重置页码
    changeStatus() {
      this.$refs.componentRef.handleCurrentChange && this.$refs.componentRef.handleCurrentChange(1)
    },

    // 点击同步到其他账号
    openSyncConfig() {
      this.$refs.syncConfig.init()
    }
  }
}
</script>
