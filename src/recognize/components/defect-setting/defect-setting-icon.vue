<style lang="scss" scoped>
.defect-setting-icon {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-left: 16px;

  span {
    margin-left: 4px;
  }
}
</style>

<template>
  <div>
    <el-tooltip content="风险设置" placement="bottom">
      <span class="defect-setting-icon" @click="$refs.defectSettingDialogRef.open()">
        <icon type="chengjie-tool" :size="24" />
      <!-- <span>风险设置</span> -->
      </span>
    </el-tooltip>
    <DefectSettingDialog ref="defectSettingDialogRef" />
  </div>
</template>

<script>
import DefectSettingDialog from './defect-setting-dialog.vue'

export default {
  name: 'defect-setting-icon',
  components: {
    DefectSettingDialog,
  },
}
</script>
