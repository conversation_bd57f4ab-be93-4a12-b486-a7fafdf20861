<!-- 同步配置到其他账号弹窗 -->
<style lang="scss" scoped>
// 覆盖默认的单选样式
.el-radio-group {
  display: flex;
  margin-top: 12px;

  .el-radio-button {
    flex: 1;
  }

  ::v-deep {
    .el-radio-button__inner {
      border-color: $color-D9D9D9;
      width: 100%;
      height: 42px;
      font-size: 16px;

      @include flex-cc;
    }

    .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      border-color: $--color-primary;
      font-weight: bold;
      color: $--color-primary;
      background: $--color-primary-hover;
    }
  }
}

.center {
  margin-top: 12px;
  padding: 16px;
  background-color: #FFFFFF;
}

// 表格样式
.table {
  overflow-y: auto;
  padding: 16px;
  height: 369px;
  background: $color-FFFFFF;

  .radio-circle {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    border: 1px solid $color-D9D9D9;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    cursor: pointer;

    .radio-center {
      margin: auto;
      border-radius: 50%;
      width: 8px;
      height: 8px;
      background: $--color-primary;
    }

    &-active {
      border-color: $--color-primary;
    }
  }
}

// 登录框样式
.account {
  padding: 16px;
  background: $color-FFFFFF;
}

// 脚步样式
.footer {
  padding: 12px 0;
  text-align: right;

  .el-button {
    font-size: 18px;
  }
}

.top-tips {
  padding: 9px 16px;
  font-size: 16px;
  line-height: 24px;
  background-color: $color-E6F3F3;

  @include flex-sbc;

  .left {
    @include flex-cc;
  }

  .icon {
    margin-right: 10px;
    font-size: 20px;
    color: $color-008489;
  }

  .btn {
    @include example-underline;
  }
}

.acc-btn {
  margin-left: 6px;
  font-size: 16px;
  cursor: pointer;
}

.empty-flex {
  @include flex-cc;

  flex-direction: column;
  line-height: 36px;

  .icon {
    @include flex-cc;

    height: 118px;
  }
}
</style>

<template>
  <div class="sync-config-page">
    <el-dialog
      title="同步配置到其他账号"
      class="whead-gbody-dialog sync-config-dialog"
      width="600px"
      :visible.sync="isShowSyncConfigDialog"
      top="80px"
      append-to-body
      :close-on-click-modal="false"
    >
      <div class="top-tips">
        <div class="left">
          <icon type="sdicon-info-circle" class="icon" />
          <p>同步风险设置到已绑定的其他用户；</p>
        </div>
        <div class="btn">
          <icon type="chengjie-bangding1" />
          <span class="acc-btn" @click="onBind">绑定账户</span>
        </div>
      </div>

      <div class="center">
        <el-table
          ref="tableRef"
          :data="tableData"
          border
          @selection-change="handleSelectionChange"
        >
          <div slot="empty" class="empty-flex">
            <icon clas="icon" type="chengjie-empty" size="150" />
            暂无数据，请先绑定账户
          </div>
          <el-table-column
            type="selection"
            width="55"
          />

          <el-table-column
            prop="corpName"
            label="企业名称"
            width="320"
          />
          <el-table-column
            prop="mobile"
            label="手机号"
          />
        </el-table>
      </div>

      <div class="footer">
        <el-button
          width="84"
          height="42"
          border
          @click="colseDialog"
        >
          取消
        </el-button>
        <el-button
          v-waiting="['post::loading::/user/login/mobilePasswordLogin', 'post::loading::/switchGroup/bindCorpMember', 'post::loading::/systemConfig/config/async']"
          type="primary"
          width="120"
          height="42"
          @click="syncConfig"
        >
          立即同步
        </el-button>
      </div>
    </el-dialog>
    <!-- 绑定新账户 -->
    <BindAccountDialog ref="bindAccountRef" @success="getAccount" />
  </div>
</template>

<script>
import toolsApi from '@recognize/apis/tools'
import BindAccountDialog from '@/views/pages/switch-account/bind-account-dialog.vue' // 登录表单

export default {
  name: 'sync-config',

  components: {
    BindAccountDialog,
  },
  data() {
    return {
      isShowSyncConfigDialog: false, // 是否显示同步配置弹窗
      // 表格数据
      tableData: [],
      multipleSelection: []// 已勾选
    }
  },
  methods: {
    // 初始化
    init() {
      this.isShowSyncConfigDialog = true
      this.getAccount()
    },

    handleSelectionChange(val) {
      this.multipleSelection = val
    },

    // 获取账号列表
    async getAccount() {
      const res = await toolsApi.getUserGroupNew()
      this.tableData = res
    },

    // 绑定账户
    onBind(row) {
      this.$refs.bindAccountRef.init(row)
    },

    // 立即同步
    async syncConfig() {
      try {
        let id = []
        id = this.multipleSelection.map(item => item.uid)
        if (!id.length) {
          this.$message.warning('未选择企业')
          return
        }
        await toolsApi.configAsyncNEW({
          list: id,
        })
        this.$message.success('同步成功')
        this.colseDialog()
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log(error)
      }
    },

    colseDialog() {
      this.multipleSelection = []
      this.$refs.tableRef.clearSelection()
      this.isShowSyncConfigDialog = false
    }
  }
}
</script>
