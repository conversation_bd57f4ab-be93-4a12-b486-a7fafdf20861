<!-- 黑名单设置 包括自由户，承兑人黑名单，收款人黑名单，背书人黑名单，敏感行业 -->
<style lang="scss" scoped>
.blacklist-setting {
  padding: 16px;
  background: $color-FFFFFF;

  .c-title {
    display: grid;
    grid-template-columns: max-content 1fr;
    align-items: center;
  }

  .top-box {
    display: grid;
    grid-template-columns: 1fr max-content;
    gap: 8px;

    .tips {
      margin-left: 10px;
      max-width: 700px;
      font-size: 14px;
      font-weight: 400;
      line-height: 1.4;
      color: $color-text-secondary;
    }
  }

  .handle-btn {
    .el-button--primary {
      padding: 8px 15px;
    }

    .icon {
      margin-right: 8px;
      font-size: 24px;
    }
  }
}

.el-table {
  margin-top: 12px;

  .icon-empty {
    font-size: 160px;
  }

  .empty-text {
    font-size: 18px;
    color: $color-text-secondary;
    line-height: 26px;
  }

  .default-text {
    font-size: 16px;
    color: $color-text-secondary;
  }
}

.footer-pagination {
  margin-top: 12px;
  text-align: center;
}

.dialog-form-label {
  margin-bottom: 2px;
  color: $color-text-secondary;
  line-height: 22px;
}

.dialog-main {
  padding: 16px;
  background-color: $color-FFFFFF;
}
</style>

<template>
  <div class="blacklist-setting">
    <div class="top-box">
      <div class="g-title c-title">
        {{ title }}
        <span class="tips">{{ typeTips }}</span>
      </div>
      <div class="handle-btn">
        <template v-if="[BLACKLIST_TYPE.ACCEPTOR.id, BLACKLIST_TYPE.PAYEE.id, BLACKLIST_TYPE.ENDORSER.id, BLACKLIST_TYPE.SENSITIVE.id].includes(type)">
          <el-button
            v-waiting="'post::loading::/discern/batchDelete'"
            border
            height="36"
            :disabled="selectionData.length === 0"
            @click="handleBatchDelete(selectionData)"
          >
            批量删除
          </el-button>
          <el-button
            v-waiting="'post::loading::/discern/batchDelete'"
            border
            type="primary"
            height="36"
            @click="onImport()"
          >
            导入名单
          </el-button>
        </template>
        <el-button
          type="primary"
          border
          height="36"
          @click="handleAdd"
        >
          <icon type="chengjie-add" class="icon" size="16" />新增
        </el-button>
      </div>
    </div>
    <el-table
      v-waiting="`get::loading::/systemConfig/getBlackList/${type}`"
      :data="tableData"
      height="350"
      outer-border
      @selection-change="handleSelectionChange"
    >
      <template slot="empty">
        <icon type="chengjie-empty" class="icon-empty" />
        <div class="empty-text">当前没有数据，您可手动添加</div>
      </template>
      <el-table-column
        :key="type"
        type="selection"
        align="center"
        width="50"
      />
      <el-table-column
        :label="typeName"
        prop="blackContent"
        width="464"
        class-name="name-column"
      />
      <el-table-column label="更新时间" prop="updatedAt" width="284">
        <template slot-scope="scope">
          <template v-if="scope.row.id === 0">默认</template>
          <template v-else>{{ formatTime(scope.row.updatedAt, 'YYYY-MM-DD hh:mm') }}</template>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <div v-if="scope.row.id === 0" class="default-text">默认值</div>
          <el-button
            v-else
            v-waiting="`delete::loading::/systemConfig/deleteBlack/${scope.row.id}`"
            type="primary"
            border
            width="68"
            height="36"
            @click="handleDelete(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="tableData.length" class="footer-pagination">
      <el-pagination
        :total="totalNum"
        :current-page.sync="currentPage"
        :page-size="pageSize"
        background
        layout="total, prev, pager, next, jumper"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-dialog
      :title="'新增' + title"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      custom-class="whead-gbody-dialog"
      append-to-body
      width="450px"
    >
      <div class="dialog-main">
        <div class="dialog-form-label">{{ typeName }}</div>
        <el-input v-model="form.blackContent" :placeholder="`请输入${typeName}`" />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="large" @click="dialogVisible = false">取消</el-button>
        <el-button
          v-waiting="`post::loading::/systemConfig/addBlack`"
          type="primary"
          size="large"
          @click="handleConfirmAdd"
        >确定</el-button>
      </span>
    </el-dialog>
    <!-- 导入文件 -->
    <UploadFileDialog
      ref="uploadFileDialogRef"
      :close-on-click-modal="false"
      title="批量导入"
      tips="请先下载EXCEL模板，录入企业信息；单次最多执行100条。"
      :size-limit="1"
      :row-count-limit="100"
      :temp="fileTemp"
      :loading="submitLoading"
      :dir="OSS_DIR.DISCERN_USER_BLACK_CONFIG"
      @submit="fileSubmit"
    />
  </div>
</template>

<script>
import {
  BLACKLIST_TYPE, // 黑名单类型
  BLACKLIST_TYPE_NAME_MAP, // 黑名单类型映射名称
  BLACKLIST_TYPE_TIPS_MAP // 黑名单类型映射提示
} from '@recognize/constant'
import { BLACK_LIST_UPDATE } from '@recognize/ipc-event-constant'
import toolsApi from '@recognize/apis/tools'
import { formatTime } from '@/common/js/date'
import { BLACKLIST_PANEL_TEMPLATE_URL } from '@/constants/oss-files-url'
import UploadFileDialog from '@/recognize/components/file-upload-dialog/file-upload-dialog.vue'
import { OSS_DIR } from '@/constant.js' // 上传文件夹

export default {
  name: 'blacklist-setting',
  components: {
    UploadFileDialog
  },
  props: {
    type: Number // 黑名单类型
  },
  data() {
    return {
      OSS_DIR,
      BLACKLIST_TYPE,
      fileTemp: {
        name: '点此下载，名单导入模版',
        href: BLACKLIST_PANEL_TEMPLATE_URL
      },
      submitLoading: false,
      userInfo: {}, // 当前登录用户信息
      currentPage: 1, // 当前页
      pageSize: 10, // 每页数量
      totalNum: 100, // 总数据数
      tableData: [], // 表格数据
      dialogVisible: false, // 新增弹窗
      form: {
        blackContent: '' // 新增弹窗表单输入字段
      },
      selectionData: []
    }
  },
  computed: {
    // 标题
    title() {
      if (['承兑人', '收款人', '背书人'].includes(BLACKLIST_TYPE_NAME_MAP[this.type])) {
        return `${BLACKLIST_TYPE_NAME_MAP[this.type]}黑名单`
      }
      return this.type ? BLACKLIST_TYPE_NAME_MAP[this.type] : ''
    },
    // 类型添加名称
    typeName() {
      return this.type ? `${BLACKLIST_TYPE_NAME_MAP[this.type]}名称` : '名称'
    },
    // 类型提示语
    typeTips() {
      return this.type ? `${BLACKLIST_TYPE_TIPS_MAP[this.type]}` : ''
    },
  },

  watch: {
    // 类型改变重新加载新数据
    type(val) {
      if (val === BLACKLIST_TYPE.OWN_ACCOUNT.id) {
        this.getUserInfo()
      } else {
        this.getBlackList()
      }
    }
  },

  created() {
    // 自有户第一条数据为当前登录账号
    if (this.type === BLACKLIST_TYPE.OWN_ACCOUNT.id) {
      this.getUserInfo()
    } else {
      this.getBlackList()
    }
  },

  methods: {
    formatTime,
    // 获取黑名单配置
    async getBlackList() {
      this.tableData = this.type === BLACKLIST_TYPE.OWN_ACCOUNT.id && this.currentPage === 1 ? [
        {
          id: 0,
          blackContent: this.userInfo?.corpName
        }
      ] : []
      const { rowList = [], totalRecord = 0 } = await toolsApi.getBlackListByPage(this.type, {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
      })
      this.totalNum = totalRecord
      this.tableData = [...this.tableData, ...rowList]
    },

    // 获取当前登录信息
    async getUserInfo() {
      this.userInfo = await this.$store.dispatch('recognize-user/getUserInfo')
      this.getBlackList()
    },

    // 点击添加显示弹窗
    handleAdd() {
      this.form.blackContent = ''
      this.dialogVisible = true
    },
    // 更改当前页
    handleCurrentChange(val) {
      this.currentPage = val
      this.getBlackList()
    },
    // 确认添加
    async handleConfirmAdd() {
      this.form.blackContent = this.form.blackContent.trim()
      // 当前输入的名称中文括号转换英文半角
      const text = this.form.blackContent.replaceAll('（', '(').replaceAll('）', ')')
      // 当前输入的名称是否存在
      const contains = this.tableData.filter(item => item.blackContent.replaceAll('（', '(').replaceAll('）', ')') === text).length
      if (contains) return this.$message.warning(`${this.typeName}已存在`)
      if (!this.form.blackContent) {
        this.$message.warning('请输入内容！')
        return
      }
      try {
        this.form.blackContentType = this.type
        await toolsApi.addBlackList(this.form)
        this.dialogVisible = false
        this.getBlackList()
        this.$event.emit(BLACK_LIST_UPDATE)
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log(error)
      }
    },
    onImport() {
      this.$refs.uploadFileDialogRef.open()
    },
    async fileSubmit(url) {
      if (url) {
        this.submitLoading = true
        try {
          const res = await toolsApi.importUserBlackConfig({ url, blackContentType: this.type })
          let alertText = `<div>${res.msg}</div>`
          if (res.failureReasonList.length) {
            alertText += '<br>'
            res.failureReasonList.forEach(item => {
              alertText += `<div>${item}</div>`
            })
          }
          this.$alert(alertText, '导入提示', {
            type: 'warning',
            confirmButtonText: '我知道了',
            dangerouslyUseHTMLString: true,
          })
          this.getBlackList()
          this.$refs.uploadFileDialogRef.close()
          this.$event.emit(BLACK_LIST_UPDATE)
        } finally {
          this.submitLoading = false
        }
      } else {
        this.$refs.uploadFileDialogRef.open()
      }
    },
    // 表格选择
    handleSelectionChange(selection) {
      this.selectionData = selection
    },
    // 批量黑名单
    handleBatchDelete(rows) {
      if (!rows.length) {
        this.$message.warning('请勾选需要删除的数据')
        return
      }
      this.$confirm('确定删除所选内容？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
        iconPosition: 'title',
        showClose: false,
      }).then(async() => {
        await toolsApi.batchDeleteBlackList({ ids: rows.map(item => item.id) })
        const delPage = Math.ceil((this.totalNum - 1) / this.pageSize)
        if (delPage > 0 && delPage < this.currentPage) {
          this.currentPage = delPage
        } else if (delPage <= 0) {
          this.currentPage = 1
        }
        this.getBlackList()
        this.$message.success('操作成功')
        this.$event.emit(BLACK_LIST_UPDATE)
      })
    },

    // 删除黑名单
    handleDelete(id) {
      this.$confirm('此操作不可撤回，数据将永久删除。', '确定要删除吗？', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
        iconPosition: 'title',
        showClose: false,
      }).then(async() => {
        await toolsApi.deleteBlackList(id)
        const delPage = Math.ceil((this.totalNum - 1) / this.pageSize)
        if (delPage > 0 && delPage < this.currentPage) {
          this.currentPage = delPage
        } else if (delPage <= 0) {
          this.currentPage = 1
        }
        this.getBlackList()
        this.$message.success('已删除')
        this.$event.emit(BLACK_LIST_UPDATE)
      })
    }
  }
}
</script>
