<!-- 背书手数 -->
<style lang="scss" scoped>
.endorse-count {
  min-height: 462px;
}

.endorse-count-item {
  margin-bottom: 12px;
  padding: 16px;
  background: $color-FFFFFF;

  @include flex-sbc;

  &:last-child {
    margin-bottom: 0;
  }

  .left-flex {
    @include flex-cc;
  }

  .el-input {
    width: 197px;
  }

  .el-date-editor {
    width: 150px;

    // ::v-deep {
    //   .el-input__inner {
    //     font-size: 16px;
    //     color: $color-text-primary;
    //   }
    // }
  }

  .connect-text {
    padding: 0 6px;
    color: $color-text-secondary;
  }

  .label {
    margin-bottom: 2px;
    color: $color-text-secondary;
    line-height: 22px;
  }

  .value {
    @include flex-cc;

    .tips {
      margin-right: 12px;
      color: $color-text-secondary;
      line-height: 22px;
    }
  }

  .right {
    @include flex-cc;

    font-size: 16px;

    .el-switch {
      margin-right: 12px;

      ::v-deep .el-switch__core {
        border-radius: 11px;
        width: 44px !important;
        height: 22px;

        &::after {
          left: 2px;
          width: 18px;
          height: 18px;
        }
      }

      &.is-checked ::v-deep .el-switch__core::after {
        left: 100%;
        margin-left: -20px;
      }
    }
  }
}

.top-box {
  padding: 16px 16px 0;
  background: $color-FFFFFF;

  .g-title .tips {
    font-size: 14px;
    font-weight: 400;
    color: $color-text-secondary;
  }

  & + .endorse-count-item {
    padding-top: 12px;
  }
}

.page-error {
  height: calc(100vh - 202px);
  background: $color-FFFFFF;
  flex-direction: column;

  @include flex-cc;

  .icon {
    font-size: 180px;
  }

  .error-text {
    margin-top: 4px;
    font-size: 18px;
    color: $color-text-secondary;
    line-height: 25px;
  }
}
</style>

<template>
  <div v-waiting="'get::loading::/systemConfig/getEndorseCountConfig'" class="endorse-count">
    <template v-if="form && Object.keys(form).length">
      <div class="top-box">
        <div class="g-title">
          背书手数
          <span class="tips">（待签收状态下，背书手数超过配置的上限给出提示）</span>
        </div>
      </div>
      <div class="endorse-count-item">
        <div class="left">
          <div class="label">累计背书不超过</div>
          <div class="value">
            <el-input
              v-model="form.endorseCountMax"
              placeholder="背书手数"
              :disabled="!form.endorseCountMaxEnable"
              type="number"
              :number-format="{decimal: false, negative: false, leadingZero: false}"
            >
              <template slot="append">手</template>
            </el-input>
            <div class="tips">（待签收状态下，累计背书手数超过配置的上限会给出提示）</div>
          </div>
        </div>
        <div class="right">
          <el-switch
            v-model="form.endorseCountMaxEnable"
            :active-value="1"
            :inactive-value="0"
          />
          {{ form.endorseCountMaxEnable ? '已开启' : '已关闭' }}
        </div>
      </div>
      <div class="endorse-count-item">
        <div class="left">
          <div class="label">今日背书不超过</div>
          <div class="value">
            <el-input
              v-model="form.todayEndorseCountMax"
              placeholder="背书手数"
              :disabled="!form.todayEndorseCountMaxEnable"
              type="number"
              :number-format="{decimal: false, negative: false, leadingZero: false}"
            >
              <template slot="append">手</template>
            </el-input>
            <div class="tips">（待签收状态下，今日背书手数超过配置的上限会给出提示）</div>
          </div>
        </div>
        <div class="right">
          <el-switch
            v-model="form.todayEndorseCountMaxEnable"
            :active-value="1"
            :inactive-value="0"
          />
          {{ form.todayEndorseCountMaxEnable ? '已开启' : '已关闭' }}
        </div>
      </div>
      <div class="endorse-count-item">
        <div class="left-flex">
          <div class="item">
            <div class="label">前</div>
            <div class="value">
              <el-input
                v-model="form.everyDayEndorseCountMaxByDaysPreDays"
                placeholder="天数"
                :disabled="!form.everyDayEndorseCountMaxByDaysEnable"
                type="number"
                :number-format="{decimal: false, negative: false, leadingZero: false}"
              >
                <template slot="append">天内</template>
              </el-input>
              <div class="tips">（不含今日，支持大于0的整数）</div>
            </div>
          </div>
          <div class="item">
            <div class="label">每日背书不超过</div>
            <div class="value">
              <el-input
                v-model="form.everyDayEndorseCountMaxByDays"
                placeholder="背书手数"
                :disabled="!form.everyDayEndorseCountMaxByDaysEnable"
                type="number"
                :number-format="{decimal: false, negative: false, leadingZero: false}"
              >
                <template slot="append">手</template>
              </el-input>
            </div>
          </div>
        </div>
        <div class="right">
          <el-switch
            v-model="form.everyDayEndorseCountMaxByDaysEnable"
            :active-value="1"
            :inactive-value="0"
          />
          {{ form.everyDayEndorseCountMaxByDaysEnable ? '已开启' : '已关闭' }}
        </div>
      </div>
      <div class="endorse-count-item">
        <div class="left-flex">
          <div class="item">
            <div class="label">选择日期</div>
            <div class="value">
              <el-date-picker
                v-model="form.everyDayEndorseCountMaxByRangeBeginDate"
                type="date"
                placeholder="开始日期"
                value-format="yyyy-MM-dd"
                :disabled="!form.everyDayEndorseCountMaxByRangeEnable"
                :picker-options="pickerOptionsBegin"
              />
              <span class="connect-text">至</span>
              <el-date-picker
                v-model="form.everyDayEndorseCountMaxByRangeEndDate"
                type="date"
                placeholder="截止日期"
                value-format="yyyy-MM-dd"
                :disabled="!form.everyDayEndorseCountMaxByRangeEnable"
                :picker-options="pickerOptionsEnd"
              />
              <div class="tips">（支持只设置开始日期或截止日期）</div>
            </div>
          </div>
          <div class="item">
            <div class="label">每日背书不超过</div>
            <div class="value">
              <el-input
                v-model="form.everyDayEndorseCountMaxByRange"
                placeholder="背书手数"
                :disabled="!form.everyDayEndorseCountMaxByRangeEnable"
                type="number"
                :number-format="{decimal: false, negative: false, leadingZero: false}"
              >
                <template slot="append">手</template>
              </el-input>
            </div>
          </div>
        </div>
        <div class="right">
          <el-switch
            v-model="form.everyDayEndorseCountMaxByRangeEnable"
            :active-value="1"
            :inactive-value="0"
          />
          {{ form.everyDayEndorseCountMaxByRangeEnable ? '已开启' : '已关闭' }}
        </div>
      </div>
      <div class="endorse-count-item">
        <div class="left">
          <div class="label">今日背书超过（含）</div>
          <div class="value">
            <el-input
              v-model="form.todayEndorseCountFixedMax"
              placeholder="背书手数"
              :disabled="true"
              type="number"
              :number-format="{decimal: false, negative: false, leadingZero: false}"
            >
              <template slot="append">手</template>
            </el-input>
            <div class="tips">（待签收状态下，今日背书手数符合或超过此数值会给出提示，此指标不允许自定义修改。）</div>
          </div>
        </div>
      </div>
    </template>
    <div v-else class="page-error">
      <template v-if="!loading">
        <icon type="chengjie-error-illustration" class="icon" />
        <div class="error-text">数据加载异常，请重试</div>
      </template>
    </div>
  </div>
</template>

<script>
import { BLACK_LIST_UPDATE } from '@recognize/ipc-event-constant'
import toolsApi from '@recognize/apis/tools'
import {
  debounce // 防抖
} from '@/common/js/util'
import {
  dealTime, // 处理时间
} from '@/common/js/date'

// 防抖时间
const DEBOUNCE_TIME = 300

export default {
  name: 'endorse-count',
  data() {
    const that = this
    return {
      loading: false, // 加载中
      // 表单信息
      form: {},
      isWatchForm: false, // 是否监听form，避免第一次赋值触发更新操作
      // 限制开始日期不能大于截止日期
      pickerOptionsBegin: {
        disabledDate(time) {
          // everyDayEndorseCountMaxByRangeEndDate 默认是08:00:00的
          // eslint-disable-next-line no-magic-numbers
          return !that.form.everyDayEndorseCountMaxByRangeEndDate ? false : time.getTime() + 8.64e7 > dealTime(that.form.everyDayEndorseCountMaxByRangeEndDate) - 2.88e7
        }
      },
      pickerOptionsEnd: {
        disabledDate(time) {
          // eslint-disable-next-line no-magic-numbers
          return !that.form.everyDayEndorseCountMaxByRangeBeginDate ? false : time.getTime() - 8.64e7 < dealTime(that.form.everyDayEndorseCountMaxByRangeBeginDate) - 2.88e7
        }
      },
    }
  },

  watch: {
    form: {
      handler() {
        this.isWatchForm && this.updateEndorseCountConfig()
      },
      deep: true
    }
  },

  created() {
    this.getEndorseCountConfig()
    // 更新背书手数
    this.updateEndorseCountConfig = debounce(async() => {
      await toolsApi.updateEndorseCountConfig(this.form)
      this.$event.emit(BLACK_LIST_UPDATE)
    }, DEBOUNCE_TIME)
  },

  methods: {
    // 获取背书手数设置
    async getEndorseCountConfig() {
      this.loading = true
      try {
        const data = await toolsApi.getEndorseCountConfig()
        this.form = data
        this.$nextTick().then(() => {
          this.isWatchForm = true
        })
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log(error)
      }
      this.loading = false
    }
  }
}
</script>
