<!-- 瑕疵说明组件 -->
<style lang="scss" scoped>
.defect-explain {
  @include flex-sb;

  align-items: flex-start;

  .left {
    margin-right: 12px;
  }

  .left,
  .right {
    padding: 16px;
    background: $color-FFFFFF;
    flex: 1;
  }

  .g-title {
    padding-bottom: 2px;
  }

  .small-title {
    margin-top: 10px;
    margin-bottom: 2px;
    font-size: 16px;
    font-weight: 600;
    line-height: 22px;
  }

  p {
    font-size: 16px;
    color: $color-text-secondary;
    line-height: 22px;
  }
}
</style>

<template>
  <div class="defect-explain">
    <div class="left">
      <div class="g-title">正面</div>
      <template v-for="item in front">
        <div :key="item.title" class="small-title">{{ item.title }}</div>
        <p v-for="text in item.textList" :key="text">{{ text }}</p>
      </template>
    </div>
    <div class="right">
      <div class="g-title">背面</div>
      <template v-for="item in back">
        <div :key="item.title" class="small-title">{{ item.title }}</div>
        <p v-for="text in item.textList" :key="text">{{ text }}</p>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'defect-explain',
  data() {
    return {
      front: [
        {
          title: '不一致',
          textList: [
            '银票：出票人开户行和承兑人全称不属于同一个银行系统；',
            '财票：出票人开户行全称和承兑人全称不一致；',
            '商票：出票人全称与承兑人全称不一致。'
          ]
        },
        {
          title: '保证',
          textList: ['票据正面有出票保证人或承兑保证人，或票据背面出现保证背书。']
        },
        {
          title: '不可转让',
          textList: ['票据正面出现“不*转让”字眼。']
        },
        {
          title: '银票企业承兑',
          textList: ['电子银行承兑汇票，承兑人是企业。']
        },
        {
          title: '商票银行承兑',
          textList: ['电子商业承兑汇票，承兑人是银行。']
        },
        // {
        //   title: '代开票',
        //   textList: ['银行承兑人全称与开户行名称不属于同一个银行系统。']
        // },
        {
          title: '特殊字符',
          textList: ['出票人、收款人、承兑人、背书人名称、被背书人名称中出现特殊字符。', '-中文半角/全角：~！@#%……&*{}|《》；【】“。', '-英文半角/全角：~!@#$%^&*<>?|`=[];\'']
        },
        {
          title: '保证待签收',
          textList: ['保证待签收不是瑕疵，而是一种骗子行为，需要识别，若存在则需要强烈提醒拒签。']
        }
      ],
      back: [
        {
          title: '回出票人',
          textList: ['转让背书信息中的被背书人名称为出票人。', '-aba：第一手转让背书中的被背书人为出票人', '-abca：除第一手转让背书外，后续转让背书中的被背书人中出现出票人']
        },
        {
          title: 'abb',
          textList: ['第一手转让背书中的背书人名称与被背书人名称同名，且为收款人。']
        },
        {
          title: '回收款人',
          textList: ['除第一手转让背书外，后续转让背书中的被背书人为收款人。']
        },
        {
          title: '背书回头',
          textList: ['隔手背书中的被背书人名称（除出票人和收款人外）出现名称重复。']
        },
        {
          title: '背书重复',
          textList: ['除第一手转让背书外，同一手转让背书中的背书人名称与被背书人名称同名。']
        },
        {
          title: '回购式贴现',
          textList: ['被背书人为银行，且在票据到期之前，可将该票从银行手里回购回来。']
        },
        {
          title: '背书不连续',
          textList: ['上一手转让背书中的被背书人名称（含正面收款人）与下一手转让背书中的背书人名称不一致。']
        },
        {
          title: '质押',
          textList: ['质押背书。']
        },
        {
          title: '保证',
          textList: ['票据正面有出票保证人或承兑保证人，或票据背面出现保证背书。']
        },
        // {
        //   title: '不可转让',
        //   textList: ['票面出现“不*转让”字眼。']
        // },
        {
          title: '转让背书银行',
          textList: ['转让背书中，背书人或被背书人是银行。']
        },
        {
          title: '特殊字符',
          textList: ['出票人、收款人、承兑人、背书人名称、被背书人名称中出现特殊字符。', '-中文半角/全角：~！@#%……&*{}|《》；【】“。', '-英文半角/全角：~!@#$%^&*<>?|`=[];\'']
        }
      ]
    }
  }
}
</script>
