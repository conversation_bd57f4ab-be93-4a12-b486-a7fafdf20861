<style lang="scss" scoped>
.window-draft-detail {
  padding-top: 74px;
}

.content-image {
  position: relative;
  margin: 0 auto;
  border-radius: 2px;
  padding: 12px 16px;
  width: 560px;
  height: 372px;
  background: $color-FFFFFF;
  box-sizing: border-box;

  .pic-title-margin {
    margin-bottom: 12px;
  }

  .turn-over {
    position: absolute;
    top: 60px;
    right: 28px;
    border-radius: 2px;
    width: 126px;
    height: 32px;
    font-size: 14px;
    text-align: center;
    color: $color-FFFFFF;
    background: $color-text-primary;
    opacity: .8;
    line-height: 32px;
    cursor: pointer;

    .icon-swap {
      margin-right: 10px;
    }
  }

  .market-draft-image {
    overflow: auto;
    width: 526px;
    height: 306px;
    cursor: pointer;
  }
}

.content-detail {
  margin: 12px auto 0;
  padding: 16px 16px 0;
  width: 560px;
  height: 364px;
  background: $color-FFFFFF;
  box-sizing: border-box;

  .detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .accpetor {
      display: flex;
      justify-content: left;
      align-items: center;
      font-size: 16px;
      font-weight: 550;
      color: $color-text-primary;

      &-text {
        max-width: 315px;

        @include ellipsis;
      }
    }
  }

  .card-body {
    position: relative;
    display: flex;
    padding: 11px 0 16px;
  }

  .item-left {
    text-align: left;
  }

  .item-right {
    text-align: right;
  }

  .item-label {
    margin-bottom: 2px;
    font-size: 14px;
    color: $color-text-secondary;
    line-height: 22px;
  }

  .item-content {
    margin-bottom: 4px;
    width: 100%;
    font-size: 16px;
    color: $color-text-primary;
    line-height: 24px;

    @include ellipsis;

    &:last-child {
      margin: 0;
    }

    &.big {
      margin-bottom: 6px;
    }

    &.bold {
      font-weight: 600;
    }
  }

  .item-sub {
    margin-bottom: 10px;
    font-size: 14px;
  }

  .card-left,
  .card-right {
    width: auto;
    width: 263px;
    height: 219px;
  }

  .card-left {
    .item-right {
      margin-right: 16px;
    }
  }

  .card-right {
    border-left: 1px solid $color-F0F0F0;
    padding-left: 12px;
  }

  .red {
    width: 100%;
    font-weight: 500;
    color: $color-warning;

    @include ellipsis;
  }
}

.theme-color {
  color: $--color-primary;
}

.draft-no-container {
  display: flex;
  align-items: center;
}

.g-xinpiao {
  padding: 0;
  height: 16px;
  line-height: 16px;
}
</style>

<template>
  <el-dialog
    class="window-draft-detail"
    title="票据详情"
    :visible.sync="dialogVisible"
    width="600px"
    append-to-body
  >
    <div v-if="draftData" class="content-image">
      <div class="g-title pic-title-margin">票面截图</div>

      <div class="turn-over" @click="turnOver">
        <icon type="chengjie-swap" class="icon-swap" />
        <span>切换至{{ type === 'front' ? '背面' : '正面' }}</span>
      </div>
      <!-- 票据截图生成组件 -->
      <div v-if="draftData.draftJson && (draftData.draftJson.front || draftData.draftJson.back)" class="market-draft-image" @click="openPicturePreview">
        <MarketDraftImage
          ref="marketDraftImage"
          :is-small-window="true"
          :data="draftData.draftJson"
          :type="type"
        />
      </div>
    </div>
    <div v-if="draftData" class="content-detail">
      <div class="draft-no-container">
        <span class="item-label">票号</span>
        <!-- <span v-if="draftData.draftType" class="g-xinpiao">新票</span> -->
        <CanNotSplitLabel :draft-info="draftData" />
      </div>
      <p class="item-content big">
        <span>{{ draftData.draftNo }}</span>
        <icon
          v-copy="{value: draftData.draftNo, onSuccess: () => copySuccess(1), onError: () => copyError}"
          type="chengjie-copy"
          class="g-copy"
          :size="20"
        />
      </p>
      <p v-if="draftData.draftType" class="item-sub">子票区间：{{ draftData.subTicketStart || '' }} - {{ draftData.subTicketEnd || '' }} </p>
      <div class="detail-item">
        <div class="item-left">
          <div class="item-label">承兑人</div>
          <div class="accpetor">
            <el-tooltip
              :content="draftData.acceptorName"
              show-when-overflow
            >
              <span class="accpetor-text">{{ draftData.acceptorName }}</span>
            </el-tooltip>
            <icon
              v-copy="{value: draftData.acceptorName, onSuccess: () => copySuccess(2), onError: () => copyError}"
              type="chengjie-copy"
              class="g-copy"
              :size="20"
            />
            <span class="g-tag--green">{{ ACCEPTOR_TYPE_VALUE_MAP[draftData.acceptorType] }}</span>
            <!-- <span :class="draftData.inRelease ? 'g-tag--green' : 'g-tag--red'">{{ draftData.inRelease ? '已发布' : '待发布' }}</span> -->
            <span v-if="IDENTIFY_TYPE_MAP[draftData.discernType]" class="g-tag--blue" :class="`g-tag--${IDENTIFY_TYPE_COLOR_MAP[draftData.discernType]}`">{{ IDENTIFY_TYPE_MAP[draftData.discernType] }}</span>
          </div>
        </div>
      </div>
      <section class="card-body">
        <div class="card-left">
          <p class="item-label">到期日</p>
          <p class="item-content">{{ formatTime(draftData.maturityDate, 'YYYY-MM-DD') }}</p>

          <p class="item-label">单张票面金额</p>
          <p class="item-content red">{{ yuan2wan(draftData.draftAmount) }}万元</p>

          <p class="item-label">每十万扣/利率</p>
          <p class="item-content bold">
            <template v-if="draftData.lakhFee || draftData.annualInterest">
              {{ draftData.lakhFee ? `${draftData.lakhFee} 元` : "-" }}
              / {{ draftData.annualInterest ? `${draftData.annualInterest} %` : "-" }}
            </template>
            <template v-else>-</template>
          </p>

          <p class="item-label">背书企业</p>
          <p class="item-content">{{ draftData.lastEndorse || '暂无提供' }}</p>
        </div>
        <div class="card-right">
          <p class="item-label">开票日期</p>
          <p class="item-content">{{ formatTime(draftData.issueDate, 'YYYY-MM-DD') }}</p>

          <p class="item-label">剩余天数</p>
          <p class="item-content">
            <template v-if="draftData.interestDays > 0">{{ draftData.interestDays }} 天</template>
            <span v-else class="theme-color">已过期</span>
          </p>

          <p class="item-label">
            瑕疵
            <icon
              v-if="draftData.defectsNotifyTemp && (draftData.defectsNotifyTemp.other.length > 0 || draftData.defectsNotifyTemp.endorseDefect)"
              class="icon icon-question"
              type="chengjie-wenti"
              @click="showDefectDialog(draftData.defectsNotifyTemp)"
            />
          </p>
          <div class="item-content">
            <el-tooltip
              v-if="draftData.originalDefects"
              :content="toDefectStr(draftData.originalDefects)"
              class="red"
              show-when-overflow
            >
              <div>
                {{ toDefectStr(draftData.originalDefects) }}
              </div>
            </el-tooltip>
            <div v-else>无瑕疵</div>
          </div>

          <p class="item-label">背书手数</p>
          <p class="item-content">{{ draftData.endorseCount ? `${draftData.endorseCount}手` : '持票方未提供' }}</p>
        </div>
      </section>
    </div>

    <defects-notify-dialog ref="defectDialog" />
    <DraftPreview ref="draftPreviewRef" />
    <SingleIssue ref="singleIssueRef" />
  </el-dialog>
</template>

<script>
import {
  ACCEPTOR_TYPE_VALUE_MAP, // 承兑人类型 id 映射 名称
  IDENTIFY_TYPE_MAP, // 识别类型 id 映射 名称
  IDENTIFY_TYPE_COLOR_MAP, // 识别类型 id 映射 标签颜色
  BACK_DEFECT_TYPE_VALUE_MAP, // 票据瑕疵类型 id 映射 名称
  BACK_DEFECT_TYPE_SHOW_NUM_MAP, // 票据瑕疵类型 id 映射 是否显示数字
} from '@recognize/constant'
import { formatTime } from '@/common/js/date'
import { yuan2wan } from '@/common/js/number'
import MarketDraftImage from '@/views/components/market-draft-image/market-draft-image.vue'
import defectsNotifyDialog from '@/views/components/defects-notify-dialog/defects-notify-dialog.vue'
import { toDefectStr } from '@/common/js/draft-flaw' // 将原始瑕疵字符串转为渲染字符串
import DraftPreview from '@recognize/components/draft-preview/draft-preview.vue'
import SingleIssue from '@recognize/components/issue-draft/single-issue/single-issue.vue'
import CanNotSplitLabel from '@/recognize/components/draft/components/can-not-split-label.vue'

export default {
  name: 'draft-detail',

  components: {
    MarketDraftImage,
    defectsNotifyDialog,
    DraftPreview,
    SingleIssue,
    CanNotSplitLabel
  },

  data() {
    return {
      ACCEPTOR_TYPE_VALUE_MAP, // 承兑人类型 id 映射 名称
      IDENTIFY_TYPE_MAP, // 识别类型 id 映射 名称
      IDENTIFY_TYPE_COLOR_MAP, // 识别类型 id 映射 标签颜色
      BACK_DEFECT_TYPE_VALUE_MAP, // 票据瑕疵类型 id 映射 名称
      BACK_DEFECT_TYPE_SHOW_NUM_MAP, // 票据瑕疵类型 id 映射 是否显示数字
      draftData: null, // 票据数据
      type: 'front', // 图片类型，正面或背面。front/back
      dialogVisible: false
    }
  },

  methods: {
    open(draftData) {
      if (!draftData) return
      this.draftData = draftData
      this.dialogVisible = true
    },

    // 瑕疵字符串转换
    toDefectStr,

    // 时间格式化
    formatTime,

    // 将元为单位的金额转为以万为单位
    yuan2wan,

    // 点击发布
    issueDraft() {
      // const res = await ticketApi.getPublishing()
      // if (res.length) {
      //   this.$message.warn('您当前有正在发布中的票据，请稍后')
      //   return
      // }
      this.$refs.singleIssueRef.open({ type: 'recognition', data: this.draftData })
    },

    // 点击取消，关闭窗口
    onWindowClose() {
      this.$refs.windowLayout.close()
    },

    // 翻面
    turnOver() {
      this.type = this.type === 'front' ? 'back' : 'front'
    },

    // 点击票据图片
    openPicturePreview() {
      this.$refs.draftPreviewRef.open({ data: this.draftData?.draftJson, type: this.type })
    },

    // 复制成功
    copySuccess(type = 1) {
      if (type === 1) {
        this.$message.success('已复制票号到剪贴板')
      } else {
        this.$message.success('已复制承兑人名称到剪贴板')
      }
    },

    // 显示瑕疵提示弹窗
    showDefectDialog(obj) {
      this.$refs.defectDialog.toggle(obj)
    }
  }
}
</script>
