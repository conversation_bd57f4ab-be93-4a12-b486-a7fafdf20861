<style lang="scss" scoped>
.home-banner {
  margin: auto;
  padding-top: 80px;
  width: 78%;
}

.banner-title {
  .el-divider {
    height: 2px;
    background-color: $--color-primary;
  }

  .support-no-one {
    margin-bottom: 10px;
    text-align: center;
  }

  .support-num {
    position: relative;
    margin-right: 5px;
    font-size: 16px;

    &::before {
      position: absolute;
      top: 50%;
      left: -56px;
      width: 40px;
      height: 2px;
      background: $--color-primary;
      content: "";
      transform: translateY(-50%);
    }
  }

  .No-1 {
    position: relative;
    font-size: 22px;
    font-weight: 600;

    &::before {
      position: absolute;
      top: 50%;
      right: -56px;
      width: 40px;
      height: 2px;
      background: $--color-primary;
      content: "";
      transform: translateY(-50%);
    }
  }

  color: $color-text-primary;
}

.bank-logo-list {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}

.bank-logo {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  margin: 0 8px 8px 0;
  border: 1px solid $color-D9D9D9;
  border-radius: 2px;
  width: 126px;
  height: 40px;
  background: $color-FFFFFF;

  &-name {
    margin-left: 6px;
    width: 74px;
    text-align: left;
    user-select: none;

    @include ellipsis;
  }
}

.support-tip {
  margin-bottom: 14px;
  font-size: 14px;
  text-align: center;
  color: $color-text-secondary;
}

.blod {
  font-weight: 600;
  color: $--color-primary;
}
</style>

<template>
  <div class="home-banner">
    <div class="banner-title">
      <div class="support-no-one">
        <span class="support-num">支持银行数量</span>
        <span class="No-1">全网领先</span>
      </div>
      <div class="support-tip">目前已支持 <span class="blod">90+</span> 家银行，且支持多种识别方式</div>
    </div>
    <div class="bank-logo-list">
      <div
        v-for="discern in discernBank"
        :key="discern.name"
        class="bank-logo"
      >
        <el-image
          style="width: 24px; height: 24px;"
          :src="discern.iconPath"
        />
        <span class="bank-logo-name">{{ discern.bankName }}</span>
      </div>
    </div>
    <div>
      <span class="text-link" @click="$refs.supportBankDialog.open()">查看更多</span>
    </div>

    <SupportBankDialog ref="supportBankDialog" />
  </div>
</template>

<script>
import SupportBankDialog from '@recognize/components/support-bank/support-bank-dialog.vue'
import commonApi from '@recognize/apis/common'

export default {
  name: 'home-banner',
  components: {
    SupportBankDialog,
  },
  data() {
    return {
      discernBank: [],
      // discernBank: [
      //   { name: '中信银行', icon: 'zhongxin', mulit: true },
      //   { name: '招商银行', icon: 'zhaoshang', mulit: true },
      //   { name: '中国银行', icon: 'zhongguo', mulit: true },
      //   { name: '平安银行', icon: 'pingan', mulit: true },
      //   { name: '浦发银行', icon: 'pufa', mulit: true },
      //   { name: '民生银行', icon: 'minsheng', mulit: true },
      //   { name: '农业银行', icon: 'nongye', mulit: true },
      //   { name: '光大银行', icon: 'guangda', mulit: true },
      //   { name: '广发银行', icon: 'guangfa', mulit: true },
      //   { name: '齐鲁银行', icon: 'qilu', mulit: true },
      //   { name: '莱商银行', icon: 'laishang', mulit: true },
      //   { name: '邮政银行', icon: 'youzheng', mulit: true },
      // ]
    }
  },
  created() {
    this.getBankList()
  },
  methods: {
    async getBankList() {
      let res = await commonApi.getSupportBank()
      let showBankList = []
      if (res) {
        res.forEach(item => {
          if (item.cpesSingleDiscern || item.cpesBatchDiscern || item.cpesAutoSync || item.cpesChromeMode) {
            showBankList.unshift(item)
          }
        })
        this.discernBank = showBankList.splice(0, 12)
      }
    }
  }
}
</script>
