<style lang="scss" scoped>
.delete-tip {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  height: 42px;
  background: $--color-primary-hover;

  .delete-icon {
    margin: 0 7px 0 16px;
    vertical-align: text-bottom;
  }

  span {
    font-size: 16px;
  }
}
</style>

<!-- 票据库存 -->
<template>
  <div class="draft-stock-page">
    <el-tabs v-model="activeName" class="g-tabs--nav" :before-leave="handleClick">
      <el-tab-pane label="近90天记录" :name="HISTORY_TABS_CODE.NORMAL" />
      <el-tab-pane label="历史记录" :name="HISTORY_TABS_CODE.HISTORY" />
    </el-tabs>
    <Tabs
      ref="tabsRef"
      :in-stock="inStock"
      :is-history="activeName === HISTORY_TABS_CODE.HISTORY"
      type="sale"
      @change="handleChangeInStock"
    />
    <TableFilter
      ref="tableFilter"
      :in-stock="inStock"
      :is-history="activeName === HISTORY_TABS_CODE.HISTORY"
      :multiple-selection="multipleSelection"
      :total-record="totalRecord"
      @query-change="changeQuery"
      @error="handleQueryError"
      @clear="handleClear"
      @handle-success="handleSuccess"
      @change-tab-num="handleChangeTabNum"
    />
    <TableList
      ref="tableList"
      :in-stock="inStock"
      :is-history="activeName === HISTORY_TABS_CODE.HISTORY"
      :filter-query="query"
      :query-error="queryError"
      :is-auto-delete="isAutoDelete"
      @query-change="changeQuery"
      @selection-change="handleSelectionChange"
      @change-tab-num="handleChangeTabNum"
      @change-total-record="changeTotalRecord"
    />
    <!-- 出库每日提醒 -->
    <!-- <OutStockTips /> -->
  </div>
</template>

<script>
import Tabs from './components/table/tabs.vue' // 顶部切换tabs
import TableFilter from './components/table/table-filter.vue' // 表格过滤条件
import TableList from './components/table/table-list.vue' // 表格内容
// import OutStockTips from './components/out-stock-btn/out-stock-tips.vue'
import userApi from '@recognize/apis/user'
const HISTORY_TABS_CODE = Object.freeze({
  NORMAL: '0',
  HISTORY: '1',
})
export default {
  name: 'draft-stock',
  components: {
    Tabs,
    TableFilter,
    TableList,
    // OutStockTips,
  },

  data() {
    return {
      HISTORY_TABS_CODE,
      activeName: HISTORY_TABS_CODE.NORMAL, // 切换正常单和历史订单
      inStock: 1, // 是否在库
      query: {}, // 过滤参数
      multipleSelection: [], // 多选列表
      queryError: false, //  参数错误
      totalRecord: 0, // 总数
      isAutoDelete: false, // 是否开启发布成功自动删除
    }
  },

  created() {
    this.getConfig()
  },

  methods: {
    // 切换tab 历史记录
    handleClick() {
      this.handleChangeInStock(1)
    },
    // 切换状态
    handleChangeInStock(val) {
      this.inStock = val
      this.changeQuery({
        pageSize: 10,
        pageNum: 1,
      })
    },

    // 搜索
    changeQuery(val) {
      this.query = { ...this.query, ...val }
    },

    // 列表总数更新
    changeTotalRecord(val) {
      this.totalRecord = val
      this.$refs.tabsRef.getTabNum(this.query)
    },

    // 搜索参数错误
    handleQueryError(val) {
      this.queryError = val
      val && (this.totalRecord = 0)
    },

    // 过滤组件的清空回调
    handleClear() {
      this.query = {}
      this.queryError = false
      this.handleChangeTabNum({})
    },

    // 多选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },

    // 重新获取tab的数量
    handleChangeTabNum(query) {
      this.$refs.tabsRef.getTabNum(query)
    },

    // 批量操作成功刷新列表
    handleSuccess() {
      this.$refs.tableList.handleSuccess(this.multipleSelection.length)
    },

    // 获取用户配置信息
    async getConfig() {
      const res = await userApi.getConfig()
      this.isAutoDelete = res.autoDeleteAfterRelease
    },
  },
}
</script>
