<template>
  <el-button
    v-waiting="'post::loading::/draft/batchDelete'"
    v-bind="$attrs"
    type="primary"
    border
    width="104"
    @click="handleBatchDelete"
  >
    批量删除
  </el-button>
</template>

<script>
import draftStockApi from '@recognize/apis/draft-stock'

export default {
  name: 'dtaft-stock-batch-delete',

  props: {
    list: {
      type: Array,
      default: () => [], // [row]
    },
    // 是否是历史记录
    isHistory: {
      type: Boolean,
      default: false
    },
  },

  data() {
    return {

    }
  },

  computed: {
    ids() {
      return this.list.map(row => row.id)
    }
  },

  methods: {
    // 批量删除
    async handleBatchDelete() {
      if (!this.ids.length) {
        this.$message.warning('请勾选需要删除的票据')
        return
      }
      try {
        await this.$confirm('确认要批量删除吗？', '提示', {
          type: 'warning',
        })
        let apiName = this.isHistory ? 'discernBatchHistoryDelete' : 'discernBatchDelete'
        // 调用api
        await draftStockApi[apiName]({ recordIds: this.ids })

        this.$message.success('批量删除成功')
        this.$emit('success')
      } catch (error) {
        if (error && error.data) {
          const CAN_NOT_DELETE_CODE = 1001 // 待后端确定
          if (error.data.code === CAN_NOT_DELETE_CODE) {
            this.$message.warning('勾选的票据中，有不可删除的票据')
          } else {
            this.$message.error(error.data.msg)
          }
          this.$emit('fail')
        }
      }
    }
  }
}
</script>
