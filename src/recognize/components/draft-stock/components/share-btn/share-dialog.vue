<style lang="scss" scoped>
::v-deep {
  .el-radio-group {
    display: block;
    margin-bottom: 12px;

    @include flex-sb;

    .el-radio-button {
      flex: 1;

      .el-radio-button__inner {
        display: block;
        padding: 12px 20px;
        font-size: 16px;
      }

      &.is-active {
        .el-radio-button__inner {
          font-weight: 600;
          color: $--color-primary;
          background: $--color-primary-hover;
        }
      }
    }
  }

  .el-alert {
    align-items: flex-start;
    padding-right: 0;

    .el-alert__content {
      padding: 3px 12px;
      padding-right: 20px;
    }

    .el-alert__title {
      font-size: 16px;
      color: $color-text-primary;
    }

    .el-alert__icon {
      margin-top: 3px;
      font-size: 20px;

      &::before {
        content: "\e7a3";
      }
    }
  }

  .el-dialog__body {
    padding-top: 20px;
    padding-bottom: 0;
    background: $color-F2F2F2;
  }
}

.container {
  padding-bottom: 20px;
}

.wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  padding: 16px;
  font-size: 16px;
  background: $color-FFFFFF;
  flex-direction: column;

  .msg {
    margin-top: 6px;
  }

  .share-link {
    @include flex-vc;

    .link {
      width: 443px;

      @include ellipsis;
    }
  }
}

.qrcode-box {
  // padding: 18px;
  width: 170px;
  height: 170px;

  .qrcode {
    width: 100%;
    height: 100%;
  }
}
</style>

<template>
  <el-dialog
    title="分享"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    width="600px"
    append-to-body
  >
    <div class="container">
      <el-radio-group v-model="selectType">
        <el-radio-button
          v-for="item in typesList"
          :key="item.type"
          :label="item.type"
        >
          {{ item.name }}
        </el-radio-button>
      </el-radio-group>

      <el-alert
        :title="typesMap[selectType].desc"
        type="error"
        :show-icon="true"
        :closable="false"
      />
      <div class="wrapper">
        <div class="qrcode-box">
          <Qrcode
            v-if="qrcodeMsg"
            tag="img"
            class="qrcode"
            :value="qrcodeMsg"
            :options="{width: 170}"
          />
        </div>

        <div class="">
          <SimpleEditInput
            v-model="phone"
            label="联系方式："
            :max-length="11"
            input-width="110px"
            input-type="text"
            :check="checkPhone"
          />
        </div>
        <div class="msg">请打开微信，使用“扫一扫”</div>
        <div class="share-link">
          <div class="link">分享链接：{{ qrcodeMsg }}</div>
          <icon
            v-if="qrcodeMsg"
            v-copy="{ value: qrcodeMsg, onSuccess, onError }"
            class="g-copy"
            type="chengjie-copy"
          />
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import Qrcode from '@chenfengyuan/vue-qrcode'
import SimpleEditInput from '@/views/components/simple-edit-input/simple-edit-input.vue'
import draftStockApi from '@recognize/apis/draft-stock'
import { debounce } from '@/common/js/util'
import BigNumber from 'bignumber.js'
import mixpanel from '@recognize/utils/mixpanel'
import { isPhone } from '@/common/js/validator' // 验证规则

const ORIGIN = window.location.origin
export default {
  name: 'share-dialog',

  components: {
    SimpleEditInput,
    Qrcode,
  },

  model: {
    prop: 'visible',
    event: 'toggle',
  },

  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    // 筛选条件
    query: {
      type: Object,
      default: () => ({}),
    },

    // 多选框 [row]
    list: {
      type: Array,
      default: () => ([])
    },
    // 是否是历史记录
    isHistory: {
      type: Boolean,
      default: false
    },
  },

  data() {
    return {
      // 是否弹窗
      dialogVisible: false,
      // 文案配置
      typesMap: {
        h5: {
          id: 1,
          type: 'h5',
          name: '微信分享',
          desc: '扫码生成H5页面，分享给好友或者分享到朋友圈。',
        },
        image: {
          id: 2,
          type: 'image',
          name: '图片分享',
          desc: '扫码生成图片，长按保存，分享给好友或者分享到朋友圈，最多分享列表或勾选项的前 10 条。',
        },
        text: {
          id: 3,
          type: 'text',
          name: '纯文本分享',
          desc: '扫码生成纯文本，一键复制，分享给好友或者分享到朋友圈，最多分享列表或勾选项的前 10 条。',
        },
      },
      selectType: 'h5', // 选中的分享类型
      phone: '', // 手机号

      shareMsg: null, // 记录后端接口返回的分享信息
    }
  },

  computed: {
    // 用于提供 watch 是否重新获取分享key
    queryForGetKey() {
      const { selectType, phone, visible } = this
      return {
        visible,
        selectType,
        phone,
      }
    },

    // 选中的 row id 合集
    ids() {
      return this.list.map(row => row.id)
    },

    // 文案配置数组类型
    typesList() {
      return Object.values(this.typesMap)
    },

    // 二维码信息
    qrcodeMsg() {
      if (!this.shareMsg) {
        return ''
      }
      return `${ORIGIN}/mobile.html/stock?type=${this.selectType}&mobile=${this.shareMsg.mobile}&shareKey=${this.shareMsg.shareKey}`
    }
  },

  watch: {
    selectType(val) {
      if (val === 'image') {
        mixpanel.inventorySharePicture()
      }
      if (val === 'text') {
        mixpanel.inventoryShareText()
      }
    },
    visible(val) {
      if (val) {
        this.dialogVisible = true
        this.getMobile()
      }
      this.$emit('toggle', val)
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('toggle', val)
      }
    },

    queryForGetKey: {
      handler(val) {
        if (val.visible) {
          this.debounceGetShareCode()
        }
      },
      deep: true,
    },
  },

  created() {
    this.debounceGetShareCode = debounce(this.getShareCode, 100)
  },

  methods: {

    // 获取用户手机号
    async getMobile() {
      const { mobile } = await this.$store.dispatch('recognize-user/getUserInfo')
      this.phone = mobile
    },

    // 获取分享code
    async getShareCode() {
      const query = { ...this.query }
      // 查询金额需要转换为元
      query.minDraftAmount && (query.minDraftAmount = new BigNumber(query.minDraftAmount).multipliedBy(10000))
      query.maxDraftAmount && (query.maxDraftAmount = new BigNumber(query.maxDraftAmount).multipliedBy(10000))
      let apiName = this.isHistory ? 'discernShareHistoryUrl' : 'discernShareUrl'

      const res = await draftStockApi[apiName]({
        ...query,
        recordIds: this.ids,
        mobile: this.phone,
        shareType: this.typesMap[this.selectType].id,
      })
      this.shareMsg = res
    },
    checkPhone(val) {
      let flag = true
      if (!isPhone(val)) {
        flag = false
        this.$message.error('请输入正确的手机号')
      }
      return flag
    },
    // 复制成功
    onSuccess() {
      this.$message.success('复制成功')
    },
    // 复制失败
    onError() {
      this.$message.error('复制失败')
    },
  }
}
</script>
