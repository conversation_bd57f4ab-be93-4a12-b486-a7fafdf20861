<style lang="scss" scoped>
.share {
  margin-left: 8px;
}

.tooltip {
  width: 454px;
}
</style>

<template>
  <div class="share">
    <el-tooltip
      effect="dark"
      content=""
      placement="bottom-end"
    >
      <template #content>
        <div class="tooltip">
          <p>1、分享支持H5、图片、纯文本三种方式；</p>
          <p>2、H5分享可以按照默认、勾选、筛选进行分享列表中所有的票据；</p>
          <p>3、图片和纯文本分享只可分享默认、勾选、筛选列表的前 10 条票据。</p>
        </div>
      </template>
      <el-button
        v-bind="$attrs"
        type="primary"
        border
        width="68"
        @click="handleShare"
      >
        分享
      </el-button>
    </el-tooltip>
    <ShareDialog
      v-model="showDialog"
      :query="query"
      :list="list"
      :is-history="isHistory"
    />
  </div>
</template>

<script>
import ShareDialog from './share-dialog.vue'
import mixpanel from '@recognize/utils/mixpanel'

export default {
  name: 'draft-stock-export',

  components: {
    ShareDialog,
  },

  props: {
    // 筛选条件
    query: {
      type: Object,
      default: () => ({}),
    },

    // 多选框 [row]
    list: {
      type: Array,
      default: () => ([])
    },
    // 是否是历史记录
    isHistory: {
      type: Boolean,
      default: false
    },
  },

  data() {
    return {
      showDialog: false,
      shareMsg: null, // 存放后端返回字段
    }
  },

  methods: {
    handleShare() {
      mixpanel.inventoryShare()
      this.showDialog = true
    }
  }
}
</script>
