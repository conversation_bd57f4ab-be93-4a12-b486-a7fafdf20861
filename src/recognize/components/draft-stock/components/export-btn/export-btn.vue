<template>
  <el-button
    v-waiting="'post::loading::/draft/exportList'"
    v-bind="$attrs"
    type="primary"
    border
    width="68"
    @click="handleExport"
  >
    导出
  </el-button>
</template>

<script>
import DraftStockApi from '@recognize/apis/draft-stock'
import BigNumber from 'bignumber.js'
import { download } from '@/common/js/util'
import mixpanel from '@recognize/utils/mixpanel'

export default {
  name: 'draft-stock-export',

  props: {
    // 筛选条件
    query: {
      type: Object,
      default: () => ({}),
    },

    // 多选框 [row]
    list: {
      type: Array,
      default: () => ([])
    },
    // 是否是历史记录
    isHistory: {
      type: Boolean,
      default: false
    },
  },

  computed: {
    // 选中的 row id 合集
    ids() {
      return this.list.map(row => row.id)
    }
  },

  methods: {
    async handleExport() {
      try {
        mixpanel.inventorExport()
        const query = { ...this.query }
        // 查询金额需要转换为元
        query.minDraftAmount && (query.minDraftAmount = new BigNumber(query.minDraftAmount).multipliedBy(10000))
        query.maxDraftAmount && (query.maxDraftAmount = new BigNumber(query.maxDraftAmount).multipliedBy(10000))
        let apiName = this.isHistory ? 'discernExportHistoryList' : 'discernExportList'
        const res = await DraftStockApi[apiName]({
          ...query,
          recordIds: this.ids,
        })
        const fileName = `${query.inStock ? '已入库' : '已出库'}${Date.now()}`
        download(res, fileName, 'xlsx')
      } catch (err) {
        // eslint-disable-next-line no-console
        console.error(err)
      }
    },
  }
}
</script>
