<!-- 表格过滤组件 -->
<style lang="scss" scoped>
.draft-stock-filter {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  min-height: 75px;
  background: $color-FFFFFF;
  flex-wrap: wrap;

  .filter-left {
    padding: 1px 0;
    width: 100%;
    font-size: 16px;
    flex: 1;

    @include flex-vc;

    .el-date-editor {
      margin-right: 8px;
      width: 286px;
    }

    .el-input-group--append {
      width: 145px;
    }

    .amount-bar {
      margin: 0 6px;
    }

    .time-type-select {
      width: 112px;

      ::v-deep .el-input__inner {
        border-color: $--border-color-base !important;
        border-right: 1px solid transparent !important;
      }
    }

    .defect-type-select {
      margin-right: 8px;
      width: 128px;
    }

    .draft-type-select {
      margin-left: 8px;
      width: 128px;
    }

    .input-with-select {
      margin-left: 8px;
      width: 290px;

      ::v-deep .el-input-group__prepend {
        width: 128px;
        color: $color-text-primary;
        background-color: $color-FFFFFF;
      }
    }
  }

  .filter-right {
    padding-left: 8px;
    flex: 1;
    width: 520px;

    @media only screen and (max-width: 1900px) {
      justify-content: flex-end;
      margin-top: 8px;
      padding-left: 0;
      min-width: 100%;
    }

    .filter-right-right {
      display: flex;
      justify-content: flex-end;
      align-items: center;

      @media only screen and (max-width: 1919px) {
        justify-content: flex-end;
      }

      // ::v-deep {
      //   .el-button--primary.is-border.is-disabled,
      //   .el-button--primary.is-border.is-disabled:hover,
      //   .el-button--primary.is-border.is-disabled:focus,
      //   .el-button--primary.is-border.is-disabled:active {
      //     border-color: #D9D9D9;
      //     color: #BFBFBF;
      //     background: #F5F5F5;
      //   }
      // }
    }
  }

  // .el-button + .el-button {
  //   margin-left: 8px;
  // }

  .order-operation {
    margin-left: 8px;
  }
}
</style>

<style lang="scss" >
.input-with-select .el-select {
  .el-input__inner {
    margin: 0 -1px;
    width: 129px;
  }
}
</style>

<template>
  <div class="draft-stock-filter">
    <div class="filter-left">
      <el-select
        slot="prepend"
        v-model="query.dateFilterType"
        placeholder="时间筛选"
        class="time-type-select"
        @change="handleDateFilterTypeChange"
      >
        <el-option
          v-for="item in timeList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="timestamp"
        :default-value="getDefaultDate"
        :default-time="['00:00:00', '23:59:59']"
        @change="handleChangeDate"
      />

      <el-select
        slot="prepend"
        v-model="query.isDefected"
        placeholder="瑕疵情况"
        class="defect-type-select"
        clearable
        @change="handleDefectedChange"
      >
        <el-option
          v-for="item in defectList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>

      <el-input
        v-model="query.minDraftAmount"
        placeholder="最小金额"
        type="number"
        :number-format="{
          negative: false,
          maxDecimalLength: 6,
          maxLength: 12,
          leadingZero: false
        }"
        @input="(val) => { handleInput(val, 'minDraftAmount') }"
      >
        <template slot="append">万</template>
      </el-input>
      <div class="amount-bar">-</div>
      <el-input
        v-model="query.maxDraftAmount"
        placeholder="最大金额"
        type="number"
        :number-format="{
          negative: false,
          maxDecimalLength: 6,
          maxLength: 12,
          leadingZero: false
        }"
        @input="(val) => { handleInput(val, 'maxDraftAmount') }"
      >
        <template slot="append">万</template>
      </el-input>
      <el-input
        v-model="typeInput"
        :placeholder="typeList[currentType].placeholder"
        class="input-with-select"
        :type="currentType === 'lastSixDraftNo' ? 'number' : 'text'"
        :number-format="currentType === 'lastSixDraftNo' ? {
          negative: false,
          decimal: false,
          maxLength: 6
        } : {}"
        @input="(val) => { handleInput(val, currentType) }"
      >
        <el-select
          slot="prepend"
          v-model="currentType"
          placeholder="请选择"
        >
          <el-option
            v-for="(item, key) of typeList"
            :key="key"
            :label="item.label"
            :value="key"
          />
        </el-select>
      </el-input>
      <el-select
        slot="prepend"
        v-model="query.draftType"
        placeholder="全部"
        class="draft-type-select"
        clearable
        @change="handleDefectedChange"
      >
        <el-option
          v-for="item in draftTypeOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>
    <div class="filter-right">
      <div class="filter-right-right">
        <el-button
          class="text-btn"
          @click="handleRefresh"
        >
          刷新
        </el-button>
        <el-button
          class="text-btn"
          @click="handleClear"
        >
          清空
        </el-button>
        <!-- 批量删除 -->
        <BatchDeleteBtn
          :is-history="isHistory"
          :disabled="totalRecord === 0"
          :list="multipleSelection"
          @success="handleSuccess"
        />

        <!-- 导出 -->
        <ExportBtn
          :is-history="isHistory"
          :disabled="totalRecord === 0"
          :list="multipleSelection"
          :query="{ ...query, inStock }"
        />

        <!-- 分享 -->
        <ShareBtn
          v-if="inStock === 1"
          :is-history="isHistory"
          :disabled="totalRecord === 0"
          :list="multipleSelection"
          :query="{ ...query, inStock }"
        />

        <!-- 批量出库 -->
        <OutStockBtn
          v-if="inStock === 1"
          :is-history="isHistory"
          :is-batch="true"
          :order="multipleSelection"
          @success="handleSuccess"
        />
      </div>
    </div>
  </div>
</template>

<script>
import BatchDeleteBtn from '../batch-delete-btn/batch-delete-btn.vue'
import OutStockBtn from '../out-stock-btn/out-stock-btn.vue'
import ExportBtn from '../export-btn/export-btn.vue'
import ShareBtn from '../share-btn/share-btn.vue'
import { debounce } from '@/common/js/util' // 防抖
import { formatTime, getTimestampBeforeDays } from '@/common/js/date' // 格式化时间

// 默认过滤参数
const defaultQuery = {
  dateFilterType: 1, // 时间筛选类型
  startDate: '', // 开始日期
  endDate: '', // 结束时间
  isDefected: '', // 是否有瑕疵
  minDraftAmount: '', // 最小票面金额，单位元
  maxDraftAmount: '', // 最大票面金额，单位元
  acceptorName: '', // 承兑人名称
  lastSixDraftNo: '', // 票据后六位
  pageNum: 1, // 页码
  pageSize: 10 // 每页条数
}
// 防抖时间
const debounceTime = 300
// 默认选中的类型
const defaultType = 'acceptorName'

export default {
  name: 'draft-stock-filter',

  components: {
    BatchDeleteBtn,
    ExportBtn,
    ShareBtn,
    OutStockBtn
  },

  props: {
    // 是否是历史记录
    isHistory: {
      type: Boolean,
      default: false
    },
    inStock: Number, // 是否出库
    multipleSelection: Array, // 多选数据
    filterQuery: Object, // 过滤的参数
    totalRecord: Number, // 表格总数
  },

  data() {
    return {
      datePickerOptions: {
        disabledDate(time) {
          const before45Days = new Date(Date.now() - 3600 * 1000 * 24 * (91))
          return time.getTime() >= before45Days.getTime()
        }
      },
      // 搜索参数
      query: { ...defaultQuery },
      dateRange: [Date.now(), Date.now()], // 时间
      // 下拉框搜索类型
      typeList: {
        acceptorName: {
          label: '承兑人',
          placeholder: '承兑人关键字'
        },
        lastSixDraftNo: {
          label: '票号后六位',
          placeholder: '票号后六位'
        }
      },
      // 当前选择的下拉框类型
      currentType: defaultType,
      typeInput: '', // 下拉框输入内容
      // 时间筛选
      timeList: [
        {
          label: '识别时间',
          value: 1
        }, {
          label: '出票日',
          value: 2
        }, {
          label: '到期日',
          value: 3
        }
      ],
      defectList: [
        {
          label: '有瑕疵',
          value: 1
        }, {
          label: '无瑕疵',
          value: 0
        },
      ],
      // 是否新票
      draftTypeOptions: [
        {
          value: null,
          label: '全部'
        },
        {
          value: 1,
          label: '新一代'
        },
        {
          value: 0,
          label: 'ECDS'
        }
      ],
    }
  },
  computed: {
    // 默认下拉框打开的时候是91天前的那个日期
    getDefaultDate() {
      return formatTime(getTimestampBeforeDays(91), 'YYYY-MM-DD')
    }
  },

  watch: {
    // 监听过滤参数改变
    filterQuery(val) {
      if (val && Object.keys(val).length) {
        this.query = Object.assign(this.query, val)
      } else {
        this.query = JSON.parse(JSON.stringify(defaultQuery))
      }
    },
    // 下拉类型改变 清空右边输入的值
    currentType() {
      this.query.acceptorName = ''
      this.query.lastSixDraftNo = ''
      this.typeInput = ''
      this.$emit('query-change', this.query)
      this.$emit('change-tab-num', this.query)
    },
    isHistory(val) {
      if (val) {
        this.dateRange = [getTimestampBeforeDays(121), getTimestampBeforeDays(91)] // 时间
      } else {
        this.dateRange = [Date.now(), Date.now()] // 时间
      }
      this.handleChangeDate(this.dateRange)
    }
  },
  created() {
    // 输入回调
    this.handleInput = debounce((val, name) => {
      this.query[name] = val
      const { minDraftAmount, maxDraftAmount } = this.query
      // 判断最小金额大于最大金额显示参数错误
      if (minDraftAmount && maxDraftAmount && +minDraftAmount > +maxDraftAmount) {
        this.$emit('error', true)
      } else {
        this.$emit('query-change', this.query)
        this.$emit('change-tab-num', this.query)
        this.$emit('error', false)
      }
    }, debounceTime)
  },

  mounted() {
    // 页面加载完成时触发一次搜索
    this.handleChangeDate(this.dateRange)
  },

  methods: {
    handleDateFilterTypeChange() {
      this.$emit('query-change', this.query)
      this.$emit('change-tab-num', this.query)
    },
    // 时间改变回调
    handleChangeDate(val) {
      if (val && val.length) {
        this.query.startDate = formatTime(val[0], 'YYYY-MM-DD')
        this.query.endDate = formatTime(val[1], 'YYYY-MM-DD')
      } else {
        this.query.startDate = ''
        this.query.endDate = ''
      }
      this.$emit('query-change', this.query)
      this.$emit('change-tab-num', this.query)
    },

    handleDefectedChange() {
      this.$emit('query-change', this.query)
      this.$emit('change-tab-num', this.query)
    },

    // 清空
    handleClear() {
      this.query = { ...defaultQuery }
      this.dateRange = []

      // 防止触发 defaultType watcher 导致调用两次接口
      if (this.currentType !== defaultType) {
        this.currentType = defaultType
      } else {
        this.typeInput = ''
        this.$emit('clear', this.query)
      }
    },

    handleRefresh() {
      this.query.pageNum = 1
      this.$emit('query-change', this.query)
      this.$emit('change-tab-num', this.query)
    },

    // 操作成功回调
    handleSuccess() {
      this.$emit('handle-success')
    }
  }
}
</script>
