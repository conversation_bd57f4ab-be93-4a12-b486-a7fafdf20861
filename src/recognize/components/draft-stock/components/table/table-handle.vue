<!-- 表格操作按钮 -->
<template>
  <div class="table-handle-container">
    <el-button
      width="68"
      size="large"
      border
    >
      详情
    </el-button>

    <el-button
      v-if="inStock === 1"
      width="68"
      size="large"
      type="primary"
      border
    >
      删除
    </el-button>

    <el-button
      v-if="inStock === 1"
      width="68"
      size="large"
      type="primary"
    >
      出库
    </el-button>
  </div>
</template>

<script>
export default {
  name: 'table-handle',
  props: {
    // 行信息
    row: {
      default: () => ({}),
      type: Object
    },
    inStock: [String, Number]
  },
}
</script>
