<!-- 背书手数 hover 和弹窗 组件 -->
<style lang="scss" scoped>
.tooltip-label {
  @include flex-cc;
}

.tooltip-content {
  font-size: 16px;

  .endorse-setting {
    margin-bottom: 10px;
  }

  .color {
    color: $--color-warning;
  }

  .key-words-list {
    margin-bottom: 10px;
  }

  .key-words {
    display: flex;

    .label {
      font-weight: bold;
    }

    .value {
      flex: 1;
    }
  }

  .endorse-count-history {
    margin-bottom: 8px;
  }

  .endorse-count-line {
    margin-top: 12px;
    border-top: 1px solid $color-FFFFFF;
    padding-top: 12px;
  }

  .history-list {
    font-size: 16px;
    line-height: 24px;

    .item {
      display: inline-block;
      margin-bottom: 8px;
      width: 50%;

      &:nth-child(2n) {
        text-align: right;
      }

      .value {
        display: inline-block;
        min-width: 26px;
        text-align: left;
      }
    }
  }

  .check-all {
    font-size: 16px;
    color: $--color-primary;
    line-height: 24px;
    cursor: pointer;
    user-select: none;
  }
}

::v-deep .question-dialog {
  background: $color-FFFFFF;

  .el-dialog__body {
    padding: 8px 32px;
  }

  .el-dialog__header {
    border: none;
    padding: 32px 32px 0;
    font-weight: 600;
    text-align: left;
    line-height: initial;
  }
}

.question-body {
  .content {
    margin-top: 16px;
    font-size: 16px;
    line-height: 24px;
  }
}

.question-dialog-footer {
  padding: 0 12px 12px;
  text-align: right;
}

.all-dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

::v-deep .all-endorse-dialog {
  .el-dialog__header {
    font-size: 16px;
    font-weight: 600;
  }

  .el-dialog__body {
    padding-bottom: 0;
  }
}

.empty-flex {
  height: 280px;
  font-size: 18px;
  color: $color-text-secondary;
  flex-direction: column;
  line-height: 26px;

  @include flex-cc;

  .icon {
    display: flex;
    margin-bottom: 12px;
    height: 100px;
    font-size: 162px;
  }
}

.dialog-footer-count {
  font-size: 16px;
  line-height: 26px;

  .bold {
    font-weight: bold;
  }
}

.bold {
  font-weight: 600;
}
</style>

<style>
.endorse-tooltip {
  max-width: 362px;
}
</style>

<template>
  <div class="table-endorse-tooltip">
    <el-tooltip
      popper-class="endorse-tooltip"
      placement="top-start"
    >
      <div slot="content" class="tooltip-content">
        <!-- 背书手数瑕疵 -->
        <slot name="endorseConfig" />
        <div :class="['endorse-count-history', discernEndorseConfig && discernEndorseConfig.riskMarker && 'endorse-count-line']">
          近 5 日背书手数（签收{{ draftSignIn ? '后' : '前' }}识别
          <icon class="icon question-icon" type="chengjie-wenti" @click="questionDialogVisible = true" />）
        </div>
        <div class="history-list">
          <span v-for="item in lastFiveDaysEndorseCount" :key="item.date" class="item">
            {{ item.endorseTime }}：<span class="value">{{ item.endorseNum }}</span>
          </span>
        </div>
        <div class="check-all" @click="allDialogVisible = true">查看历史背书手数</div>
      </div>
      <span class="tooltip-label">
        <slot />
      </span>
    </el-tooltip>
    <!-- 点击问号弹出 -->
    <el-dialog
      title="什么是签收前识别和签收后识别?"
      :visible.sync="questionDialogVisible"
      width="490px"
      :show-close="false"
      append-to-body
      custom-class="question-dialog"
      center
    >
      <div class="question-body">
        <div class="content">
          <p>1、签收前识别：</p>
          <p>用户在票据签收前使用{{ discernName }}发布,<span style="color: red;">最后一手背书的实际签收日期未知,系统会在{{ discernName }}识别当日自动增加1手背书</span><span class="text-primary bold">（若识别当日网银实际没操作签收，隔天需重新识别发布）</span>,仅供参考。（提示收票待签收不会加1手背书）</p>
        </div>
        <div class="content">
          <p>2、签收后识别：</p>
          <p>用户在票据签收后使用{{ discernName }}发布,背书日期、背书手数准确。</p>
        </div>
      </div>
      <div slot="footer" class="question-dialog-footer">
        <el-button type="secondary" size="medium" @click="questionDialogVisible = false">我知道了</el-button>
      </div>
    </el-dialog>
    <!-- 全部背书手数 -->
    <el-dialog
      :visible.sync="allDialogVisible"
      append-to-body
      width="470px"
      custom-class="all-endorse-dialog"
      class="whead-gbody-dialog all-endorse-dialog"
    >
      <div slot="title">
        全部背书手数（签收{{ draftSignIn ? '后' : '前' }}识别
        <icon
          type="chengjie-wenti"
          :size="20"
          class="icon-question"
          @click="questionDialogVisible = true"
        />  ）
      </div>
      <div class="all-dialog-body">
        <el-table
          :data="tableData"
          border
          max-height="330"
        >
          <template slot="empty">
            <div class="empty-flex">
              <icon class="icon" type="chengjie-empty" />
              暂无数据
            </div>
          </template>
          <el-table-column
            prop="endorseTime"
            label="时间"
          />
          <el-table-column
            prop="endorseNum"
            label="背书手数"
          />
        </el-table>
      </div>
      <div slot="footer" class="all-dialog-footer">
        <span class="dialog-footer-count">累计背书手数：<span class="bold">{{ endorseCount }} 手</span></span>
        <el-button type="primary" size="large" @click="allDialogVisible = false">我知道了</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  formatTime, // 时间格式化
} from '@/common/js/date'

export default {
  name: 'table-endorse-tooltip',
  props: {

    // 当前订单风险设置信息
    discernEndorseConfig: Object,
    draftSignIn: [Number || undefined], // 签收前签收后识别
  },

  data() {
    return {
      questionDialogVisible: false, // 点击问号弹窗
      allDialogVisible: false, // 历史背书手数弹窗
      tableData: [], // 历史背书手数
      lastFiveDays: [], // 最近五天日期数组
      lastFiveDaysEndorseCount: [], // 最近五天背书手数
      endorseCount: 0, // 历史背书手数统计
    }
  },

  mounted() {
    this.getLastFiveDay()
    this.lastFiveDaysEndorseCount = []
    this.tableData = []
    if (this.discernEndorseConfig) {
      if (this.discernEndorseConfig.draftEndorseInfos && this.discernEndorseConfig.draftEndorseInfos.length) {
        this.endorseCount = 0
        this.tableData = this.discernEndorseConfig.draftEndorseInfos
        this.tableData.forEach(item => {
          this.endorseCount += item.endorseNum
        })
        this.lastFiveDays.forEach(item => {
          this.lastFiveDaysEndorseCount.push({ endorseTime: item, endorseNum: this.getEndorseCount(item) })
        })
      }
    } else {
      // discernEndorseConfig为null时表示没有背书信息，此时默认为0手
      this.lastFiveDays.forEach(item => {
        this.lastFiveDaysEndorseCount.push({ endorseTime: item, endorseNum: '无' })
      })
    }
  },

  methods: {
    // 是否有值或者为0
    hasValue(val) {
      return val || val === 0
    },

    // 获取对应日期的
    getEndorseCount(date) {
      const current = this.discernEndorseConfig.draftEndorseInfos.filter(item => item.endorseTime === date)
      return current.length ? `${current[0].endorseNum} 手` : '无'
    },

    // 获取最近5天的日期
    getLastFiveDay() {
      const currentTime = new Date().getTime()
      this.lastFiveDays = []
      // 循环获取最近五天的时间
      /* eslint-disable no-magic-numbers */
      for (let i = 0; i < 5; i++) {
        this.lastFiveDays.push(formatTime(currentTime - i * 86400000, 'YYYY-MM-DD'))
      }
    },
  }
}
</script>
