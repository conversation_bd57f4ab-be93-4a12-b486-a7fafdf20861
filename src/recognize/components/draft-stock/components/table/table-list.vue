<!-- 我的订单表格列表 -->
<style lang="scss" scoped>
.draft-stock-table-container {
  padding: 0 16px 20px;
  background: #FFFFFF;
}

.el-table ::v-deep {
  min-height: 464px;
  background: $color-FFFFFF;

  th.el-table__cell > .cell {
    text-overflow: initial;
    white-space: nowrap;
  }

  // thead {
  // .el-table__cell {
  //   padding: 10px 0;
  //   font-size: 14px;
  //   font-weight: 600;
  // }

  // .has-tags-column .cell {
  //   padding-right: 16px;
  //   padding-left: 26px;
  // }
  // }

  // .el-table__cell {
  //   font-size: 16px;
  //   color: $color-text-primary;

  //   .cell {
  //     padding-left: 13px;
  //   }
  // }

  // .has-tags-column .cell {
  //   padding-right: 16px;
  //   padding-left: 0;
  // }

  // .small-padding-column .cell {
  //   padding-right: 12px;
  //   padding-left: 2px;
  // }

  .el-table-column--selection {
    .cell {
      text-overflow: unset;
    }
  }

  // tbody {
  //   .el-table__cell {
  //     padding: 0;
  //     height: 84px;
  //   }

  //   .bold {
  //     font-weight: 600;
  //   }

  //   .large-font-column {
  //     font-size: 20px;
  //   }

  //   .handle-column .cell {
  //     display: flex;
  //     align-items: center;
  //   }

  //   .acceptor-column .cell {
  //     overflow: unset;
  //   }
  // }
}

.has-tags-column-flex {
  display: flex;
  align-items: center;

  .tags {
    display: flex;
    margin-right: 6px;
    width: 20px;
    flex-wrap: wrap;
    flex: 0 0 20px;

    .tag {
      margin-bottom: 4px;
      border: 1px solid transparent;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      font-size: 12px;
      text-align: center;
      line-height: 18px;

      &:last-child {
        margin-bottom: 0;
      }

      &.single {
        border-color: $color-13C2C2;
        color: $color-13C2C2;
      }

      &.batch {
        border-color: $color-9254DE;
        color: $color-9254DE;
      }

      &.auto {
        border-color: $color-AF772D;
        color: $color-AF772D;
      }
    }
  }

  .acceptor {
    line-height: 24px;
    font-weight: bold;
  }
}

.has-ticket-flaw {
  max-height: 48px;
  font-weight: 600;
  color: $color-warning;
  line-height: 24px;
}

.two-row-hidden {
  /* stylelint-disable-next-line value-no-vendor-prefix */
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  /* stylelint-disable-next-line property-no-vendor-prefix */
  -webkit-box-orient: vertical;
}

.footer-pagination {
  padding: 22px 0 0;
  text-align: center;
}

.theme-color {
  color: $--color-primary;
}

.question-icon {
  margin-left: 5px;
}

.endorse-config {
  margin-bottom: 12px;
  border-bottom: 1px solid $color-FFFFFF;
  padding-bottom: 12px;
}

.g-xinpiao {
  margin-left: 0;
}
</style>

<template>
  <!-- eslint-disable vue/no-v-html -->
  <div class="draft-stock-table-container">
    <el-table
      ref="tableRef"
      v-waiting="['post::loading::/draft/list']"
      border
      :data="tableData"
      @selection-change="handleSelectionChange"
    >
      <template slot="empty">
        <TableEmpty />
      </template>

      <el-table-column
        key="0"
        type="selection"
        align="center"
        :min-width="inStock === 1 ? 42 : 40"
      />
      <el-table-column
        key="1"
        prop="discernTime"
        label="识别时间"
        min-width="120"
      >
        <template slot-scope="scope">
          <template v-if="scope.row.discernTime">
            {{ formatTime(scope.row.discernTime, 'YYYY-MM-DD') }}
            <br>
            {{ formatTime(scope.row.discernTime, 'hh:mm:ss') }}
          </template>
          <template v-else>-</template>
        </template>
      </el-table-column>
      <el-table-column
        key="2"
        label="票号后六位"
        min-width="135"
        align="left"
        class-name="small-padding-column"
      >
        <div slot-scope="scope">
          <div>{{ scope.row.lastSixDraftNo || String(scope.row.draftNo).substring(String(scope.row.draftNo).length - 6) }}</div>
          <div>
            <!--
              <el-tooltip
              v-if="scope.row.draftType"
              placement="top"
              :content="`子票区间：${scope.row.subTicketStart || ''} - ${scope.row.subTicketEnd || ''}`"
              >
              <span class="g-xinpiao">新票</span>
              </el-tooltip>
            -->
            <CanNotSplitLabel :draft-info="scope.row" />
          </div>
        </div>
      </el-table-column>
      <el-table-column
        key="3"
        label="承兑人"
        min-width="192"
        class-name="has-tags-column acceptor-column"
      >
        <div slot-scope="scope" class="has-tags-column-flex">
          <div class="tags">
            <span v-if="scope.row.discernType === 1" class="tag single">单</span>
            <span v-else-if="scope.row.discernType === 2" class="tag batch">批</span>
            <span v-else-if="scope.row.discernType === 3" class="tag auto">同</span>
          </div>
          <el-tooltip
            popper-style="max-width: 362px;"
            placement="top"
            :disabled="scope.row.acceptorName.length <= 12"
            :content="scope.row.acceptorName"
          >
            <div class="acceptor two-row-hidden">{{ scope.row.acceptorName }}</div>
          </el-tooltip>
        </div>
      </el-table-column>
      <el-table-column
        key="4"
        prop="amountSort"
        label="票面金额(万)"
        min-width="124"
        align="right"
        class-name="bold large-font-column"
      >
        <template slot-scope="scope">
          {{ scope.row.draftAmountWan }}
        </template>
      </el-table-column>
      <el-table-column
        key="5"
        prop="interestDaysSort"
        label="出票日/到期日"
        min-width="125"
        align="right"
      >
        <template slot-scope="scope">
          {{ scope.row.issueDate ? formatTime(scope.row.issueDate, 'YYYY-MM-DD') : '-' }}
          <br>
          {{ scope.row.maturityDate ? formatTime(scope.row.maturityDate, 'YYYY-MM-DD') : '-' }}
        </template>
      </el-table-column>
      <el-table-column
        key="6"
        prop="interestDaysSort"
        label="剩余天数"
        min-width="80"
        align="right"
        class-name="small-padding-column"
      >
        <template slot-scope="scope">
          <template v-if="!isOverdue(scope.row.maturityDate)">{{ scope.row.interestDays }}</template>
          <span v-else class="theme-color">已过期</span>
        </template>
      </el-table-column>
      <el-table-column
        key="7"
        prop="interestDaysSort"
        label="所在银行"
        min-width="150"
        align="left"
        class-name="bold"
      >
        <template slot-scope="scope">
          {{ scope.row.discernSource }}
        </template>
      </el-table-column>
      <el-table-column
        key="dorsement"
        prop="lastEndorse"
        label="最后1手背书"
        min-width="160"
        align="left"
      >
        <template slot-scope="scope">
          <el-tooltip
            popper-style="max-width: 362px;"
            placement="top"
            :disabled="!scope.row.lastEndorse"
            :content="scope.row.lastEndorse"
          >
            <div class="two-row-hidden">{{ scope.row.lastEndorse || '-' }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        key="8"
        prop="ticketAmount"
        label="瑕疵"
        :min-width="inStock === 1 ? 94 : 88"
      >
        <div slot-scope="scope">
          <div v-if="!scope.row.originalDefects" class="ticket-flaw">无瑕疵</div>
          <el-tooltip
            v-else
            popper-style="max-width: 362px;"
            placement="top"
            :disabled="toDefectStr(scope.row.originalDefects).length <= 8"
            :content="toDefectStr(scope.row.originalDefects)"
          >
            <div class="has-ticket-flaw two-row-hidden">{{ toDefectStr(scope.row.originalDefects) }}</div>
          </el-tooltip>
        </div>
      </el-table-column>
      <el-table-column
        key="9"
        label="背书手数"
        :min-width="inStock === 1 ? 80 : 76"
        align="center"
        class-name="small-padding-column"
      >
        <template slot-scope="scope">
          <span v-if="!scope.row.endorseCount && scope.row.endorseCount !== 0" class="endorse-number">-</span>
          <TableEndorseTooltip
            v-else
            :discern-endorse-config="scope.row.discernEndorseConfig"
            :draft-sign-in="scope.row.draftSignIn"
          >
            <div
              v-if="scope.row.defectsNotifyTemp.endorseDefect"
              slot="endorseConfig"
              :class="['endorse-config']"
              v-html="scope.row.defectsNotifyTemp.endorseDefect"
            />
            <span class="theme-color">{{ scope.row.endorseCount }}</span>
            <icon v-if="scope.row.defectsNotifyTemp.endorseDefect" class="icon icon-question question-icon" type="chengjie-wenti" />
          </TableEndorseTooltip>
        </template>
      </el-table-column>
      <el-table-column
        v-if="inStock === 0"
        key="10"
        label="备注"
        min-width="106"
        align="left"
      >
        <template slot-scope="scope">
          <el-tooltip
            v-if="scope.row.draftRemarks"
            popper-style="max-width: 362px;"
            :content="scope.row.draftRemarks"
            placement="top"
            show-when-overflow
          >
            <div class="two-row-hidden">{{ scope.row.draftRemarks }}</div>
          </el-tooltip>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="inStock === 0"
        key="11"
        prop="outStockTime"
        label="出库时间"
        min-width="120"
      >
        <template slot-scope="scope">
          <template v-if="scope.row.outStockTime">
            {{ formatTime(scope.row.outStockTime, 'YYYY-MM-DD') }}
            <br>
            {{ formatTime(scope.row.outStockTime, 'hh:mm:ss') }}
          </template>
          <template v-else>-</template>
        </template>
      </el-table-column>
      <el-table-column
        key="12"
        label="操作"
        :min-width="inStock === 1 ? 130 : 100"
        fixed="right"
        class-name="handle-column"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="large"
            @click="showTicketDetail(scope.row)"
          >
            详情
          </el-button>

          <!-- 删除 -->
          <SingleDeleteBtn
            v-if="inStock === 1"
            :is-history="isHistory"
            :disabled="scope.row.orderStatus === ORDER_STATUS.ON_LISTED.id"
            :order="scope.row"
            size="large"
            @success="handleSuccess(1)"
          />

          <!-- 出库 -->
          <OutStockBtn
            :is-history="isHistory"
            :is-remark="inStock !== 1"
            :disabled="inStock === 1 && (scope.row.orderStatus === ORDER_STATUS.ON_LISTED.id || scope.row.interestDays <= 0)"
            :order="scope.row"
            size="large"
            @success="handleSuccess(1)"
          />
        </template>
      </el-table-column>
    </el-table>
    <div v-if="tableData.length" class="footer-pagination">
      <el-pagination
        :current-page.sync="query.pageNum"
        :page-size="query.pageSize"
        background
        layout="prev, pager, next, jumper"
        :total="totalRecord"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <DefectsNotifyDialog ref="defectDialog" />
    <DraftDetail ref="draftDetailRef" />
  </div>
</template>

<script>
import BigNumber from 'bignumber.js'
import draftStockApi from '@recognize/apis/draft-stock' // 接口
import TableEmpty from './table-empty.vue' // 缺省页面
// 金额单位转换
import { yuan2wan } from '@/common/js/number'
// 时间处理
import {
  formatTime, // 格式化时间
  isOverdue,
} from '@/common/js/date'
import DefectsNotifyDialog from '@/views/components/defects-notify-dialog/defects-notify-dialog.vue'
import {
  toDefectStr, // 将原始瑕疵字符串转为渲染字符串
  toDefectsNotifyTemp // 将瑕疵提示字段转换
} from '@/common/js/draft-flaw'
import OutStockBtn from '../out-stock-btn/out-stock-btn.vue'
import SingleDeleteBtn from '../single-delete/single-delete.vue'
import { ORDER_STATUS } from '@recognize/constant'
import TableEndorseTooltip from './table-endorse-tooltip.vue' // 背书手数提示组件
import DraftDetail from '@recognize/components/draft-detail/draft-detail.vue'
import CanNotSplitLabel from '@/recognize/components/draft/components/can-not-split-label.vue'

// 默认请求参数
const defaultQuery = {
  pageNum: 1,
  pageSize: 10
}

export default {
  name: 'draft-table',

  components: {
    TableEmpty,
    OutStockBtn,
    SingleDeleteBtn,
    DefectsNotifyDialog,
    TableEndorseTooltip,
    DraftDetail,
    CanNotSplitLabel,
  },

  props: {
    // 当前状态
    inStock: {
      default: '',
      type: [String, Number]
    },
    // 是否是历史记录
    isHistory: {
      type: Boolean,
      default: false
    },
    // 过滤的参数
    filterQuery: Object,
    // 过滤参数错误
    queryError: Boolean,
    // 是否显示头部提示
    isAutoDelete: [Boolean, Number],
  },

  data() {
    return {
      loading: false, // 加载状态
      query: {}, // 请求参数
      tableData: [], // 表格数据
      totalRecord: 0, // 数据总条数
      ORDER_STATUS, // 订单状态
    }
  },

  watch: {
    // 监听过滤参数改变
    filterQuery(val) {
      if (val && Object.keys(val).length) {
        this.query = Object.assign({}, this.query, val)
      } else {
        this.query = Object.assign({}, defaultQuery)
      }
      this.getStockList()
    },

    // 监听参数错误
    queryError(val) {
      val && (this.tableData = [])
    }
  },
  activated() {
    this.getStockList()
  },

  methods: {
    toDefectStr,
    formatTime,
    isOverdue,
    // 按钮操作回调
    handleSuccess(delNum) {
      const delPageNum = Math.ceil((this.totalRecord - delNum) / this.query.pageSize)
      if (delPageNum < this.query.pageNum && delPageNum > 0) {
        this.query.pageNum = delPageNum
      } else if (delPageNum <= 0) {
        this.query.pageNum = 1
      }
      this.getStockList()
      this.$emit('change-tab-num', this.query)
    },
    // 获取列表数据
    async getStockList() {
      // 参数错误时不请求数据
      if (this.queryError) {
        return
      }
      this.tableData = []
      const query = { ...this.query, inStock: this.inStock }
      // 查询金额需要转换为元
      /* eslint-disable no-magic-numbers */
      query.minDraftAmount && (query.minDraftAmount = new BigNumber(query.minDraftAmount).multipliedBy(10000))
      query.maxDraftAmount && (query.maxDraftAmount = new BigNumber(query.maxDraftAmount).multipliedBy(10000))
      let apiName = this.isHistory ? 'getStockHistoryList' : 'getStockList'
      const data = await draftStockApi[apiName](query)
      if (data) {
        data.rowList.forEach(item => {
          item.draftAmountWan = yuan2wan(item.draftAmount) // 票据金额转为万
          item.defectsNotifyTemp = item.defectsNotify ? toDefectsNotifyTemp(item.defectsNotify) : [] // 瑕疵提示对象
          item.discernEndorseConfig = item.draftEndorseInfos ? { draftEndorseInfos: item.draftEndorseInfos } : {} // 背书信息
        })
        this.$nextTick().then(() => {
          this.tableData = data.rowList
          this.totalRecord = data.totalRecord
          this.$emit('change-total-record', data.totalRecord)
        })
      }
    },

    // 清空过滤参数
    handleClearQuery() {
      this.$emit('query-change', Object.assign({}, defaultQuery))
    },

    // 更改每页条数
    handleSizeChange(val) {
      this.$emit('query-change', { pageSize: val })
    },

    // 更改当前页
    handleCurrentChange(val) {
      this.$emit('query-change', { pageNum: val })
    },

    // 多选回调
    handleSelectionChange(val) {
      this.$emit('selection-change', val)
    },

    // 显示瑕疵提示弹窗
    showDefectDialog(obj) {
      this.$refs.defectDialog.toggle(obj)
    },

    // 打开票据详情
    async showTicketDetail(row) {
      let apiName = this.isHistory ? 'discernHistoryDetail' : 'discernDetail'
      let data = await draftStockApi[apiName](row.id)
      data.draftJson = JSON.parse(data.draftJson)
      data = Object.assign(row, data)
      this.$refs.draftDetailRef.open(data)
    },
  }
}
</script>
