<!-- 顶部tab切换状态 -->
<style lang="scss" scoped>
.el-radio-group {
  margin-bottom: 12px;
  padding: 12px 16px;
  width: 100%;
  background: $color-FFFFFF;

  .el-radio-button {
    flex: 1;

    &:last-child {
      margin-right: 0;
    }

    ::v-deep .el-radio-button__inner {
      padding: 10px 20px;
      width: 100%;
      font-size: 14px;
      line-height: 20px;
      user-select: none;
    }

    .count {
      display: inline-block;
      margin-left: 4px;
      border-radius: 10px;
      padding: 0 6px;

      // min-width: 22px;
      height: 18px;
      font-size: 12px;
      text-align: center;
      color: $color-FFFFFF;
      background: $color-warning;
      line-height: 18px;

      &.active:not(.hn) {
        color: $color-warning;
        background: $color-FFFFFF;
      }

      .plus {
        margin-top: -4px;
      }
    }
  }
}
</style>

<template>
  <div class="draft-stock-tabs special-tabs">
    <el-radio-group v-model="isCurStock" @change="$emit('change', isCurStock)">
      <el-radio-button
        v-for="item of tabsList"
        :key="item.id"
        :label="item.id"
      >
        {{ item.name }}
        <span v-if="item.count > 0" :class="['count', isCurStock === item.id && 'active']">
          {{ item.count > 99 ? 99 : item.count }}<span v-if="item.count > 99" class="plus">+</span>
        </span>
      </el-radio-button>
    </el-radio-group>
  </div>
</template>

<script>
import BigNumber from 'bignumber.js'
import draftStockApi from '@recognize/apis/draft-stock'

export default {
  name: 'draft-stock-tabs',
  // 当前选中的状态
  props: {
    inStock: {
      type: Number,
      default: 1
    },
    // 是否是历史记录
    isHistory: {
      type: Boolean,
      default: false
    },
  },

  data() {
    return {
      isCurStock: 1, // 当前选中的状态
      // 状态列表
      tabsList: {
        inStock: {
          id: 1,
          name: '已入库',
          count: 0,
        },
        outStock: {
          id: 0,
          name: '已出库',
          count: 0
        }
      }
    }
  },
  watch: {
    // 更新一下 当前选中状态
    inStock: {
      handler(val) {
        this.isCurStock = val
      }
    },
  },

  methods: {
    // 获取待办数目
    async getTabNum(query) {
      const queryCopy = { ...query }
      // 查询金额需要转换为元
      /* eslint-disable no-magic-numbers */
      queryCopy.minDraftAmount && (queryCopy.minDraftAmount = new BigNumber(queryCopy.minDraftAmount).multipliedBy(10000))
      queryCopy.maxDraftAmount && (queryCopy.maxDraftAmount = new BigNumber(queryCopy.maxDraftAmount).multipliedBy(10000))
      let apiName = this.isHistory ? 'getStockHistoryTabNum' : 'getStockTabNum'
      const data = await draftStockApi[apiName](queryCopy)
      Object.keys(data).forEach(key => {
        this.tabsList[key].count = data[key]
      })
    }
  }
}
</script>
