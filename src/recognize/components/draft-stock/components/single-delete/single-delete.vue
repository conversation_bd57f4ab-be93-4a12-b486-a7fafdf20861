<template>
  <el-button
    v-waiting="'post::loading::/draft/batchDelete'"
    v-bind="$attrs"
    type="text"
    :size="size"
    border
    @click="handleDelete"
  >
    删除
  </el-button>
</template>

<script>
import draftStockApi from '@recognize/apis/draft-stock'

export default {
  name: 'dtaft-stock-batch-delete',

  props: {
    order: {
      type: Object,
      default: () => ({}),
    },
    size: {
      type: String,
      default: null
    },
    // 是否是历史记录
    isHistory: {
      type: Boolean,
      default: false
    },
  },

  methods: {
    // 批量删除
    async handleDelete() {
      try {
        await this.$confirm('确认要删除吗？', '提示', {
          type: 'warning',
        })
        let apiName = this.isHistory ? 'discernBatchHistoryDelete' : 'discernBatchDelete'
        // 调用api
        await draftStockApi[apiName]({ recordIds: [this.order.id] })

        this.$message.success('删除成功')
        this.$emit('success')
      } catch (error) {
        if (error && error.data) {
          const CAN_NOT_DELETE_CODE = 3001 // 待后端确定
          if (error.data.code === CAN_NOT_DELETE_CODE) {
            this.$message.warning('该票据不可删除')
          } else {
            this.$message.error(error.data.msg)
          }
          this.$emit('fail')
        }
      }
    }
  }
}
</script>
