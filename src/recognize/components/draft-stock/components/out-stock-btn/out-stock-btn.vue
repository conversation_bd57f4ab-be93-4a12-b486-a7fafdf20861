<style lang="scss" scoped>
.out-stock {
  display: inline-block;
  margin-left: 8px;
}

.out-stock-btn {
  position: relative;

  &.show-tips {
    position: fixed;
    z-index: 1000;

    &::before {
      position: absolute;
      top: -5px;
      right: -8px;
      bottom: -5px;
      left: -8px;
      z-index: 10;
      display: block;
      border-radius: 2px;
      box-shadow: 0 0 0 10000px rgb(0 0 0 / 50%);
      content: "";
    }

    &::after {
      position: absolute;
      top: -11px;
      right: -14px;
      bottom: -11px;
      left: -14px;
      z-index: 11;
      display: block;
      border: 1px dashed $color-FFFFFF;
      border-radius: 2px;
      content: "";
    }
  }
}
</style>

<template>
  <div class="out-stock order-operation">
    <el-button
      v-bind="$attrs"
      :type="isBatch ? 'primary' : 'text'"
      :border="!isBatch"
      :size="size"
      class="out-stock-btn"
      @click="outStockClick"
      v-on="$listeners"
    >
      <span v-if="isRemark">备注</span>
      <span v-else-if="isBatch">批量出库</span>
      <span v-else>出库</span>
    </el-button>
  </div>
</template>

<script>
import OutStockRemark from './out-stock-remark.vue'
import orderOperationMixin from '@/common/js/order-operation-mixin'

export default {
  name: 'out-stock',

  mixins: [orderOperationMixin(OutStockRemark)],

  props: {
    order: {
      type: [Object, Array],
      default: () => ({}),
    },
    // 批量出库类型标志
    isBatch: {
      type: Boolean,
      default: false
    },
    // 已出库备注标志
    isRemark: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: null
    },
    // 是否是历史记录
    isHistory: {
      type: Boolean,
      default: false
    },
  },
  computed: {
    width() {
      return this.isBatch ? '104' : '68'
    }
  },
  methods: {
    outStockClick() {
      // 批量标记处理
      if (this.isBatch) {
        if (!this.order || this.order.length === 0) {
          this.$message.warning('请勾选需要批量出库的票据')
          return
        }
      }
      this.init()
    }
  }
}
</script>
