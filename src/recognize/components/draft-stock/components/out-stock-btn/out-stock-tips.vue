<style lang="scss" scoped>
.container {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;

  .wrapper {
    position: absolute;

    // top: 155px;
    // right: 160px;
  }

  .content {
    width: 788px;
    font-size: 16px;
    font-weight: 600;
    color: $color-FFFFFF;
    line-height: 26px;
  }

  .confirm-btn {
    margin-top: 12px;
  }

  .icon-a-guidearrow {
    position: absolute;
    top: 105px;
    right: 0;
    font-size: 95px;
    color: $color-FFFFFF;
  }
}
</style>

<template>
  <div v-if="visible" class="container">
    <div class="wrapper" :style="{ left: wrapperLeft, top: wrapperTop }">
      <div class="content">
        <p>当库存中的票据未在{{ appName }}完成交易之前就已经背书给其他交易对手时，可以点击“出库”操作进行手动出库</p>
        <p>1. 未发布的票据出库后，票据状态变为已出库。</p>
        <p>2. 已发布的票据，但未被接单或者自动下架时，允许出库，出库后订单状态变为已下架，票据状态变为已出库。</p>
        <p>3. 已发布的票据，但被下架或者订单交易失败时，允许出库，出库后订单状态不变，票据状态变为已出库。</p>
        <p>4. 已发布正在交易中时，不允许出库。</p>
        <p>5. 票据交易成功，将自动出库。</p>
      </div>
      <el-button
        type="primary"
        size="large"
        class="confirm-btn"
        @click="hideTips"
      >
        我知道了
      </el-button>
      <div class="icon-box">
        <icon type="chengjie-a-guidearrow" class="icon icon-a-guidearrow" />
      </div>
    </div>
  </div>
</template>

<script>
import Storage from '@/common/js/storage'
import { OUT_STOCK_TIPS_LAST_TIME } from '@recognize/constant-storage'
import { isToday } from '@/common/js/date'

let domFirstOutStockBtn = null

// 记录 body 的 overflow 属性值
let bodyStyleOverflow = document.body.style.overflow

export default {
  name: 'out-stock-tips',

  data() {
    return {
      visible: false,
      wrapperLeft: '', // 文案 x
      wrapperTop: '', // 文案 y
    }
  },

  mounted() {
    setTimeout(() => {
      this.checkVisible()
    }, 1000)
  },

  methods: {
    // 判断是否要显示
    checkVisible() {
      // 对比时间戳，如果今天已经弹起，则不再弹起
      const prevShowTime = Storage.get(OUT_STOCK_TIPS_LAST_TIME)
      const hasShow = isToday(prevShowTime)

      // 今天已经弹起过了，不再弹起
      if (hasShow) {
        return
      }

      // 记录本次提示时间戳
      // 放在这里的原因是：如果今天第一次进入页面时无数据，则不弹起
      // 即使今天下次进入后又有数据，今天也不再弹起
      Storage.set(OUT_STOCK_TIPS_LAST_TIME, Date.now())

      // 弹起提示
      this.showTips()
    },

    // 显示提示
    showTips() {
      const domOutStockBtns = document.getElementsByClassName('out-stock-btn')
      if (domOutStockBtns.length < 2) { // 跳过批量出库按钮
        return
      }
      domFirstOutStockBtn = domOutStockBtns[1]
      const { x, y } = domFirstOutStockBtn.getBoundingClientRect()
      domFirstOutStockBtn.style.left = `${x}px`
      domFirstOutStockBtn.style.top = `${y}px`
      domFirstOutStockBtn.classList.add('show-tips')

      const WRAPPER_WIDTH = 808
      const WRAPPER_HEIGHT = 194
      this.wrapperLeft = `${x - WRAPPER_WIDTH}px`
      this.wrapperTop = `${y - WRAPPER_HEIGHT}px`

      document.body.style.overflow = 'hidden'
      this.visible = true
    },

    // 隐藏提示
    hideTips() {
      domFirstOutStockBtn.classList.remove('show-tips')
      domFirstOutStockBtn.style.left = ''
      domFirstOutStockBtn.style.right = ''
      domFirstOutStockBtn.style.top = ''
      document.body.style.overflow = bodyStyleOverflow
      this.visible = false
    },
  },
}
</script>
