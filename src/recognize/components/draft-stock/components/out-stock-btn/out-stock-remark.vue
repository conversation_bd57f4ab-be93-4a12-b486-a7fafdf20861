<style lang="scss" scoped>
.container {
  padding-bottom: 12px;

  .second-title {
    margin-bottom: 2px;
    font-size: 14px;
    color: $color-text-secondary;
    line-height: 22px;
  }

  .wrapper {
    display: flex;
    margin-bottom: 12px;
    padding: 16px;
    font-size: 16px;
    background: $color-FFFFFF;
    flex-direction: column;
  }

  .footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}

::v-deep {
  .el-dialog__header {
    padding: 16px 20px;

    @include flex-sbc;

    .el-dialog__headerbtn {
      position: static;
    }
  }

  .el-dialog__body {
    padding-top: 20px;
    padding-bottom: 0;
    background: $color-F2F2F2;
  }
}
</style>

<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="visible"
    :close-on-click-modal="false"
    width="600px"
    append-to-body
  >
    <el-alert
      v-if="isRemark"
      type="warning"
      show-icon
      :closable="false"
      title="备注更新会覆盖原备注，如需保留请不要删除已备注文本"
    />
    <div class="container">
      <div class="wrapper">
        <div class="second-title">备注</div>
        <el-input
          v-model="remark"
          type="textarea"
          :autosize="{ minRows: 4, maxRows: 6 }"
          :placeholder="placeholderText"
          :maxlength="50"
          :show-word-limit="true"
        />
      </div>
      <div class="footer">
        <el-button type="primary" border @click="handleHidden">取消</el-button>
        <el-button
          v-waiting="[
            'post::loading::/draft/outStock',
            'post::loading::/draft/batchOutStock',
            'post::loading::/draft/addRemark'
          ]"
          type="primary"
          @click="handleConfirm"
        >
          确认
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import draftStockApi from '@recognize/apis/draft-stock'
import mixpanel from '@recognize/utils/mixpanel'

export default {
  name: 'out-stock-remark',

  data() {
    return {
      isBatch: false,
      isRemark: false,
      placeholderText: '可输入出库渠道/经办人/出库价格等备注信息（非必填，50字以内）',
      visible: false,
      remark: ''
    }
  },

  computed: {
    dialogTitle() {
      const { isBatch, isRemark } = this

      if (isRemark) return '备注'
      if (isBatch) return '批量出库'
      return '出库'
    }
  },

  methods: {
    // 初始化
    init(params) {
      this.isRemark = params.isRemark
      this.isBatch = params.isBatch
      if (params.isRemark) {
        this.remark = this.order?.draftRemarks ?? ''
      }
      this.visible = true
    },

    // 隐藏
    handleHidden() {
      this.visible = false
      this.isBatch = false
      this.isRemark = false
    },

    // 确认
    async handleConfirm() {
      if (!this.isBatch && !this.order.id) {
        return
      }
      mixpanel.inventoryOutBound({
        出库备注: this.remark
      })
      if (this.isRemark) {
        let apiName = this.isHistory ? 'addRemarkHistory' : 'addRemark'
        await draftStockApi[apiName]({
          id: this.order.id,
          draftRemark: this.remark
        })
      } else if (this.isBatch) {
        let apiName = this.isHistory ? 'batchDiscernOutStockHistory' : 'batchDiscernOutStock'
        // 批量出库
        let idArray = Array.isArray(this.order) && this.order.map(item => item.id)
        await draftStockApi[apiName]({
          ids: idArray,
          draftRemarks: this.remark,
        })
      } else {
        let apiName = this.isHistory ? 'discernOutStockHistory' : 'discernOutStock'

        const { id } = this.order
        await draftStockApi[apiName]({ id, draftRemarks: this.remark })
      }
      this.$message.success(this.isRemark ? '已备注' : '出库成功')
      this.handleHidden()
      this.$emit('success')
    }
  }
}
</script>
