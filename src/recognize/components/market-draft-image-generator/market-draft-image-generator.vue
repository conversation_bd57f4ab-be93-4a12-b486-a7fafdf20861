<template>
  <!-- 票据截图生成组件 -->
  <div class="market-draft-image-generator" />
</template>

<script>
import MarketDraftImage from '@/views/components/market-draft-image/market-draft-image.vue'
import Vue from 'vue'

const MarketDarftImageConstructor = Vue.extend(MarketDraftImage)

export default {
  name: 'market-draft-image-generator',
  props: {
    // 票据识别后 dll 返回的数据 TODO: 支持数组
    data: Object
  },
  data() {
    return {}
  },
  methods: {
    // 生成正面和背面截图，imageType 为对应要导出的图片类型，canvas/blob
    generate(imageType) {
      return Promise.all([
        this.screenshot('front', imageType), // 正面
        this.screenshot('back', imageType), // 背面
      ])
    },
    // 生成正面或背面截图
    async screenshot(type, imageType) {
      // 票据图片容器
      const draftImgContainer = document.createElement('div')
      draftImgContainer.style.cssText = 'position:relative;overflow:hidden;width:0;height:0;'
      this.$el.appendChild(draftImgContainer)
      const el = document.createElement('div')
      draftImgContainer.appendChild(el)
      const { data } = this
      const instance = new MarketDarftImageConstructor({
        props: {
          // 指定票据截图类型
          type: {
            type: String,
            default: type,
            // default: 'front',
          },
          data: {
            type: Object,
            default() {
              return data
            }
          }
        }
      })
      instance.$mount(el)
      instance.$el.style.cssText = 'position:absolute;left:9999px;top:9999px'
      const result = await instance.screenshot(imageType)
      draftImgContainer.remove()
      return result
    }
  }
}
</script>
