<style lang="scss" scoped>
.identification-setting {
  padding: 20px 0 8px 16px;

  .title {
    font-size: 16px;
  }

  .title-margin {
    margin-bottom: 12px;
  }

  .line {
    margin: 16px 24px 16px 0;
    height: 1px;
    background: $color-D9D9D9;
  }

  .adapt-flex {
    display: flex;
    align-items: center;

    .is-border {
      margin-right: 20px;
      margin-left: 20px;
    }
  }
}
</style>

<template>
  <div class="identification-setting">
    <div class="g-title title-margin">辅助网银适配</div>
    <div class="adapt-flex">
      <span>招商银行客户端一键适配</span>
      <el-button
        class="is-border"
        :loading="adapting"
        type="primary"
        size="mini"
        @click="adaptZhaoShangBank"
      >
        一键适配
      </el-button>
      <el-tooltip content="一键适配后，如客户端因此无法使用，可点击此处一键还原" placement="bottom">
        <el-button type="text" @click="cancelAdaptZhaoShangBank">一键还原</el-button>
      </el-tooltip>
    </div>
    <div class="line" />
  </div>
</template>

<script>
import { MARKET_DRAFT_RECONSTRUCTION_DESKTOP_ICON } from '@recognize/ipc-event-constant'

const isWindows = navigator.platform.indexOf('Win') > -1

export default {
  name: 'identification-setting',
  data() {
    return {
      adapting: false
    }
  },
  created() {
    this.$ipc.on(MARKET_DRAFT_RECONSTRUCTION_DESKTOP_ICON, this.adaptCallback)
  },
  beforeDestroy() {
    this.$ipc.removeListener(MARKET_DRAFT_RECONSTRUCTION_DESKTOP_ICON, this.adaptCallback)
  },
  methods: {
    // 设置招商银行快捷方式 --force-renderer-accessibility 参数
    adaptZhaoShangBank() {
      if (!isWindows) {
        this.$message.error('当前系统不支持该功能！')
        return
      }
      this.adapting = true
      this.$ipc.send(MARKET_DRAFT_RECONSTRUCTION_DESKTOP_ICON, false)
    },
    // 取消快捷方式参数
    cancelAdaptZhaoShangBank() {
      if (!isWindows) {
        this.$message.error('当前系统不支持该功能！')
        return
      }
      this.$ipc.send(MARKET_DRAFT_RECONSTRUCTION_DESKTOP_ICON, true)
    },
    // 设置结果回调
    adaptCallback(_, result) {
      this.adapting = false
      if (result?.code === 0) {
        this.$message({
          message: result?.isCancel ? '已还原，退出重新打开招商银行客户端生效' : '适配成功，退出重新打开招商银行客户端即可操作识别',
          type: 'success'
        })
      } else {
        this.$message({
          message: result?.isCancel ? '还原失败，请联系客服反馈问题~' : '适配失败，请联系客服反馈问题~',
          type: 'error'
        })
      }
    },
  },
}
</script>
