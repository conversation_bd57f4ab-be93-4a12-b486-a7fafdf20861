<style lang="scss" scoped>
// 通知设置
.notice-page {
  padding: 20px 20px 16px 16px;

  .line {
    margin: 16px 0;
    height: 1px;
    background: $color-D9D9D9;
  }

  ::v-deep {
    .check-title {
      .el-checkbox__label {
        font-weight: 600;
      }
    }

    .el-checkbox__label {
      color: $color-text-primary;
    }

    .el-checkbox__input.is-checked + .el-checkbox__label {
      color: $color-text-primary;
    }
  }
}
</style>

<template>
  <div class="notice-page">
    <el-checkbox
      :value="Object.keys(text).every(v => text[v])"
      class="check-title"
      :indeterminate="isTextIndeterminate"
      @change="handleCheckAllText"
    >
      消息提醒
    </el-checkbox>
    <div style="margin: 15px 0;" />

    <el-checkbox
      v-for="(item, index) in Object.keys(text)"
      :key="item"
      v-model="text[item]"
      :true-label="1"
      :false-label="0"
      @change="handleChangeOne"
    >
      {{ optionLabels[index] }}
    </el-checkbox>

    <div class="line" />
    <!-- 暂时不用声音设置功能 -->
    <!--
      <el-checkbox
      :value="Object.keys(voice).every(v => voice[v])"
      class="check-title"
      :indeterminate="isVoiceIndeterminate"
      @change="handleCheckAllVoice"
      >
      声音提醒
      </el-checkbox>
      <div style="margin: 15px 0;" />
      <el-checkbox
      v-for="(item, index) in Object.keys(voice)"
      :key="item"
      v-model="voice[item]"
      :true-label="1"
      :false-label="0"
      @change="handleChangeOne"
      >
      {{ optionLabels[index] }}
      </el-checkbox>
    -->
  </div>
</template>

<script>
import { throttle } from '@/common/js/util'
import userApi from '@recognize/apis/user'
import {
  CONFIG_SETTING_CHANGE, // 触发常规设置、通知设置修改
} from '@recognize/ipc-event-constant'

const optionLabels = ['待确认', '待打款', '待背书', '待签收']
export default {
  name: 'notice-log',
  data() {
    return {
      checkAllText: false,
      checkAllVoice: false,
      optionLabels,
      text: {
        textRemindPendingConfirm: 0, // 待确认消息提醒
        textRemindPendingPay: 0, // 待打款消息提醒
        textRemindPendingEndorse: 0, // 待背书消息提醒
        textRemindPendingSignIn: 0, // 待签收消息提醒
      },
      // voice: {
      //   voiceRemindPendingConfirm: 0, // 待确认声音提醒
      //   voiceRemindPendingPay: 0, // 待打款声音提醒
      //   voiceRemindPendingEndorse: 0, // 待背书声音提醒
      //   voiceRemindPendingSignIn: 0 // 待签收声音提醒
      // }
    }
  },

  computed: {
    // 消息提醒是否全选
    isTextIndeterminate() {
      const keyList = Object.keys(this.text)
      return keyList.every(v => !this.text[v]) ? false : keyList.some(v => !this.text[v])
    },

    // 声音提醒是否全选
    isVoiceIndeterminate() {
      const keyList = Object.keys(this.voice)
      return keyList.every(v => !this.voice[v]) ? false : keyList.some(v => !this.voice[v])
    }
  },

  created() {
    this.getConfig()
  },

  methods: {
    // 获取用户设置
    async getConfig() {
      const res = await userApi.getConfig()
      for (let i in this.text) {
        this.text[i] = res[i]
      }
      // 暂时不用声音设置功能
      // for (let i in this.voice) {
      //   this.voice[i] = res[i]
      // }
    },

    // 监听全选
    handleCheckAllText(val) {
      Object.keys(this.text).forEach(key => {
        this.text[key] = val ? 1 : 0
      })
      throttle(this.set, 1000)
    },

    // 监听声音全选
    handleCheckAllVoice(val) {
      Object.keys(this.voice).forEach(key => {
        this.voice[key] = val ? 1 : 0
      })
      throttle(this.set, 1000)
    },

    // 监听其中一个改变
    handleChangeOne() {
      throttle(this.set, 1000)
    },

    // 提交设置
    async set() {
      await userApi.putConfig({ ...this.text, ...this.voice })
      this.$event.emit(CONFIG_SETTING_CHANGE, { type: 'notice' })
    }

  }
}
</script>
