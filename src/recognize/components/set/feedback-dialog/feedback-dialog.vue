<!-- 问题反馈弹窗 -->
<style lang="scss" scoped>
.feedback-dialog {
  ::v-deep .el-dialog {
    margin-bottom: 0;
  }

  .main {
    padding: 16px;
    background-color: $color-FFFFFF;

    .label {
      margin: 10px 0 2px;
      height: 22px;
      font-size: 14px;
      color: $color-text-secondary;
      line-height: 22px;

      &:first-child {
        margin-top: 0;
      }
    }

    .required {
      &::before {
        display: inline-block;
        margin-top: 6px;
        margin-right: 2px;
        font-size: 18px;
        color: $color-warning;
        vertical-align: top;
        line-height: 14px;
        content: "*";
      }
    }

    .upload-drag {
      ::v-deep .el-upload-dragger {
        display: flex;
        align-items: center;
        border: 1px dashed #FF5A5F;
        border-radius: 2px;
        width: 170px;
        height: 74px;
        flex-direction: column;
      }

      .upload-text {
        width: 100%;
        font-size: 14px;
        text-align: center;
        color: $color-text-secondary;
      }

      .upload-tip {
        padding: 8px 14px 0;
      }

      .upload-img {
        ::v-deep.el-image__inner {
          border-radius: 2px;
        }
      }
    }

    .upload-flex {
      display: flex;
      justify-content: space-between;
    }

    ::v-deep .el-textarea__inner {
      height: 72px;
    }

    .upload-tip-text {
      white-space: nowrap;
    }
  }
}
</style>

<template>
  <el-dialog
    title="问题反馈"
    :visible.sync="dialogVisible"
    width="600px"
    top="70px"
    append-to-body
    :close-on-click-modal="false"
    :before-close="onClose"
    class="feedback-dialog whead-gbody-dialog"
  >
    <main class="main">
      <p class="label required">问题描述</p>
      <el-input
        v-model="form.proposalDesc"
        type="textarea"
        :rows="3"
        :maxlength="200"
        placeholder="输入问题描述，200字以内"
      />
      <p class="label">上传图片（单个凭证大小不超过 2 M）</p>
      <div class="upload-flex">
        <el-upload
          v-for="(img, index) in form.proposalImgList"
          :key="index"
          class="upload-drag"
          drag
          action=""
          :multiple="false"
          :http-request="({file}) => uploadHttp(file, index)"
          :before-upload="beforeAvatarUpload"
          :on-remove="handleRemove"
          :show-file-list="false"
        >
          <el-image
            v-if="img"
            class="upload-img"
            :src="img"
          />
          <div v-else class="upload-tip">
            <icon type="chengjie-plus" :size="20" color-theme="primary" />
            <p class="upload-tip-text">点击上传图片</p>
            <p class="upload-tip-text">(支持 .jpg/.jpeg/.png)</p>
          </div>
        </el-upload>
      </div>
      <p class="label">您的建议</p>
      <el-input
        v-model="form.proposalContent"
        type="textarea"
        :rows="3"
        placeholder="输入您的建议，200字以内"
        :maxlength="200"
      />
      <p class="label">您的联系方式</p>
      <el-input
        v-model="form.userContact"
        placeholder="请输入您的联系方式，方便我们及时解决您的问题"
      />
    </main>
    <footer slot="footer" class="dialog-footer">
      <el-button size="large" @click=" onClose()">
        取消
      </el-button>
      <el-button type="secondary" size="large" @click="handleConfirm">确定</el-button>
    </footer>
  </el-dialog>
</template>

<script>
import { upload } from '@/utils/oss.js'
import userApi from '@recognize/apis/user'

export default {
  name: 'batch-edit-dialog',

  components: {
  },

  data() {
    return {
      dialogVisible: false,
      form: {
        proposalDesc: '', // 问题描述
        proposalImgList: ['', '', ''], // 上传图片列表 ,String
        proposalContent: '', // 使用建议
        userContact: '' // 联系方式
      }
    }
  },

  methods: {
    // 切换显示隐藏
    toggle() {
      this.dialogVisible = !this.dialogVisible
    },

    // 点击确定
    async handleConfirm() {
      const { proposalDesc, proposalImgList, proposalContent, userContact } = this.form
      if (!proposalDesc) {
        this.$message.error('请填写问题描述！')
        return
      }
      const imgList = proposalImgList.filter(item => item)
      await userApi.postFeedback({
        proposalDesc,
        proposalContent,
        proposalImgList: imgList,
        userContact
      })
      this.$message.success('反馈成功！')
      this.onClose()
    },

    // 上传
    async uploadHttp(file, index) {
      const res = await upload(file, 'feedback')
      this.$set(this.form.proposalImgList, index, res)
    },

    // 图片限制
    beforeAvatarUpload(file) {
      const isJPG = file.name.split('.')[1] === 'jpg'
      const isPNG = file.name.split('.')[1] === 'png'
      const isJPEG = file.name.split('.')[1] === 'jpeg'
      // eslint-disable-next-line no-magic-numbers
      const isLtmit2M = file.size / 1024 / 1024 <= 2
      if (!isJPG && !isPNG && !isJPEG) {
        this.$message.error('只能上传 jpg/jpeg/png 格式')
      }
      if (!isLtmit2M) {
        this.$message.error('单个凭证大小不超过 2M')
      }
      return (isJPG || isPNG || isJPEG) && isLtmit2M
    },

    // 移除图片
    handleRemove(file) {
      // eslint-disable-next-line no-console
      console.log('移除图片回调', file)
    },
    onClose() {
      this.dialogVisible = false
      this.form = {
        proposalDesc: '', // 问题描述
        proposalImgList: ['', '', ''], // 上传图片列表 ,String
        proposalContent: '', // 使用建议
        userContact: '' // 联系方式
      }
    }
  }
}
</script>
