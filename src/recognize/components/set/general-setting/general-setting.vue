<style lang="scss" scoped>
// 常规设置
.general-setting-page {
  padding: 20px 0 8px 16px;

  .title {
    font-size: 16px;
  }

  .title-margin {
    margin-bottom: 12px;
  }

  .line {
    margin: 16px 24px 16px 0;
    height: 1px;
    background: $color-D9D9D9;
  }

  .radio-group {
    padding: 0 12px;
    width: 100%;

    ::v-deep .el-radio {
      margin-right: 43px;
    }
  }

  .checkbox-group {
    margin-bottom: 8px;
    padding: 0 6px 0 12px;

    &:last-of-type {
      margin-bottom: 16px;
    }

    >.checkbox-group-flex {
      display: flex;
      align-items: center;
      height: 28px;
    }

    .quickText {
      width: 135px;
    }

    ::v-deep {
      .el-checkbox__input.is-checked + .el-checkbox__label {
        font-weight: normal;
        color: $color-text-primary;
      }

      .el-checkbox__input,
      .el-checkbox,
      .el-checkbox__label {
        display: flex;
        align-items: center;
      }

      .tip {
        margin-top: -4px;
        font-size: 12px;
        color: $color-text-primary;
        line-height: 17px;
        vertical-align: middle;

        .text {
          color: $color-text-secondary;
          transform: scale(.85);
          transform-origin: left;
          line-height: 14px;
        }

        .red-text {
          color: $color-warning;
        }
      }
    }

    .setShortcut {
      display: inline-block;
      margin: 0 10px;
      border: 1px solid $color-D9D9D9;
      border-radius: 2px;
      width: 95px;
      height: 28px;
      text-align: center;
      background: $color-FFFFFF;
      line-height: 28px;
    }
  }

  .switch-group {
    margin-bottom: 8px;
    padding: 0 12px;

    .small-title {
      margin: 10px 0 2px;
      font-size: 14px;
      color: $color-text-secondary;
      line-height: 22px;
    }

    .switch-text {
      margin-left: 10px;
      font-size: 14px;
      line-height: 22px;
    }
  }

  .pay-flex {
    display: flex;
    flex-wrap: wrap;
    padding: 0 0 0 8px;

    .el-radio-button {
      margin-right: 8px;

      &:nth-child(4n) {
        margin-right: 0;
      }

      &:last-of-type {
        margin-top: 8px;
      }

      ::v-deep .el-radio-button__inner {
        border: 1px solid $color-D9D9D9;
        border-radius: 2px;
        padding: 11px 0;
        width: 128px;
        box-shadow: none;
        user-select: none;
      }

      .open {
        color: $color-008489;
        cursor: pointer;
      }

      ::v-deep .el-radio-button__orig-radio:checked + .el-radio-button__inner {
        border: 1px solid $--color-primary;
        color: $--color-primary;
        background-color: $--color-primary-hover;
        box-shadow: none;
      }

      ::v-deep .el-radio-button__orig-radio:disabled + .el-radio-button__inner {
        color: $color-text-secondary;
        background: $color-F4F5F6;
      }
    }
  }
}

.close-item {
  padding: 0 12px;
}

.radio-check-color {
  ::v-deep .el-radio__input.is-checked + .el-radio__label {
    color: $color-text-primary;
  }
}

.help-btn {
  margin: 0 8px;
  border-color: $--color-primary;
  color: $--color-primary;

  &.is-disabled {
    border-color: $--color-primary;
    color: $--color-primary;
    background-color: $color-FFFFFF;
    opacity: .3;
  }
}

.el-input-number::v-deep {
  .el-input::after {
    position: absolute;
    top: 0;
    right: 64px;
    display: inline-block;
    content: "%";
    height: 40px;
    line-height: 40px;
  }
}

.repeat-input {
  color: $color-warning;
}

.serial-draft {
  padding: 0 12px;
  color: $color-text-primary;
  user-select: none;

  &-text {
    margin: 0 12px;
  }

  &-icon {
    margin-right: 5px;
  }

  &-open {
    color: $--color-primary;
  }
}

.code-setting {
  padding: 10px 15px;
}
</style>

<template>
  <div class="general-setting-page">
    <div class="g-title title-margin">开机自启动设置</div>
    <p class="switch-group">
      <el-switch v-model="startupLogin" @change="startupLoginChange" />
      <span class="switch-text">{{ startupLogin ? '已开启，系统启动时自动打开软件' : '已关闭，系统启动时不自动打开软件' }}</span>
    </p>
    <div class="line" />
    <div class="g-title title-margin">识别记录保留时长</div>
    <el-radio-group
      v-model="recognizeDuration"
      class="radio-group radio-check-color"
      type="radio"
      @change="radioChange"
    >
      <el-radio :label="RADIOS.TODAY">仅当天</el-radio>
      <el-radio :label="RADIOS.THREE_DAYS">近三天</el-radio>
      <el-radio :label="RADIOS.FOREVER">永久</el-radio>
    </el-radio-group>
    <div class="line" />
    <div class="g-title title-margin">发布后自动删除识别记录</div>
    <p class="switch-group">
      <el-switch v-model="autoDelete" @change="autoDeleteChange" />
      <span class="switch-text">{{ autoDelete ? '已开启，票据发布后，会自动删除识别记录' : '已关闭，不会自动删除已发布的识别记录' }}</span>
    </p>
    <div class="line" />
    <template v-if="$ipc">
      <div class="g-title title-margin">自定义页面缩放比例</div>
      <el-input-number
        v-model="zoom"
        :min="minZoomFactor"
        :max="maxZoomFactor"
        :step="10"
        :step-strictly="true"
      />
      <div class="line" />
      <div class="g-title title-margin">快捷键设置</div>
      <div v-for="(item, index) in setKeyList" :key="item.name" class="checkbox-group">
        <div class="checkbox-group-flex">
          <el-checkbox v-model="item.enable" @change="checkboxChange(index)">
            <span class="quickText">启用{{ item.nameZh }}快捷键</span>
            <input
              ref="refShortcut"
              class="setShortcut"
              :class="item.isRepeat && 'repeat-input'"
              :value="item.keyStr"
              readonly
              @click.prevent="setShortcut(index)"
              @focus="focus(index)"
              @blur="shortcutBlur(index)"
            >
          </el-checkbox>
          <div v-if="item.isFocus && !item.keyStr" class="tip">
            <div>请按键盘两个键设置</div>
            <div class="text">请以Shift/Alt/Ctrl键开头</div>
          </div>
          <div v-if="item.isRepeat" class="tip">
            <div class="red-text">检测到快捷键重复/冲突</div>
            <div class="text">请重新设置快捷键</div>
          </div>
        </div>
      </div>
      <div class="line" />
    </template>
    <div class="g-title title-margin">默认打码设置</div>
    <div class="switch-group">
      <p class="small-title">出票人打码</p>
      <p><el-switch v-model="mosaicSet.sellerMosaic" @change="mosaicChange" /><span class="switch-text">{{ mosaicSet.sellerMosaic ? '已开启，订单中展示的正面图片，将默认为出票人打码' : '已关闭，订单中展示的正面图片，将默认不会为出票人打码' }}</span></p>
      <div v-if="mosaicSet.sellerMosaic" class="code-setting">
        <el-radio-group v-model="sellerMosaicType" class="radio-check-color" @change="codeSettingChange()">
          <el-radio :label="1"> 打码全称、账号、开户行</el-radio>
          <el-radio :label="2">仅打码全称、帐号后4位</el-radio>
        </el-radio-group>
      </div>
      <p class="small-title">收款人打码</p>
      <p><el-switch v-model="mosaicSet.buyerMosaic" @change="mosaicChange" /><span class="switch-text">{{ mosaicSet.buyerMosaic ? '已开启，订单中展示的正面图片，将默认为收款人打码' : '已关闭，订单中展示的正面图片，将默认不会为收款人打码' }}</span></p>
      <div v-if="mosaicSet.buyerMosaic" class="code-setting">
        <el-radio-group v-model="buyerMosaicType" class="radio-check-color" @change="codeSettingChange()">
          <el-radio :label="1"> 打码全称、账号、开户行</el-radio>
          <el-radio :label="2">仅打码全称、帐号后4位</el-radio>
        </el-radio-group>
      </div>
    </div>
    <div class="line" />
    <div class="g-title title-margin">智能验票设置</div>
    <p class="switch-group">
      <el-switch v-model="ticketChecking" @change="ticketCheckingChange" />
      <span class="switch-text">{{ ticketChecking ? '已开启，单张识别待签收票据时，验票条件符合将自动验票' : '已关闭，智能验票已关闭，开启后可自动验票' }}</span>
    </p>
    <div class="line" />
    <div class="g-title title-margin">连号票标签设置</div>
    <p class="serial-draft">
      <el-switch v-model="serialTag" @change="serialChange" />
      <span class="serial-draft-text" :class="serialTag && 'serial-draft-open'">{{ serialTag ? '已开启' : '已关闭' }}</span>
      <icon class="serial-draft-icon" type="chengjie-wenti" />
      <span>{{ serialTag ? '批量发布时自动检测是否有连号票，有则自动开启连号票标签' : '批量发布时如有连号票，需手动开启连号票标签' }}</span>
    </p>
    <div class="line" />
    <div class="g-title title-margin">关闭按钮默认操作</div>
    <el-radio-group
      v-model="closeAction"
      class="close-item radio-check-color"
      type="radio"
      @change="closeActionChange"
    >
      <el-radio :label="CLOSE_ACTIONS.MINIMIZE">最小化到系统托盘，不退出程序</el-radio>
      <el-radio :label="CLOSE_ACTIONS.CLOSE">退出程序</el-radio>
    </el-radio-group>
    <div class="line" />
    <div class="g-title title-margin">帮助与反馈</div>
    <el-button class="help-btn" height="38px" @click="$refs.FeedbackDialogRef.toggle()">问题反馈</el-button>
    <FeedbackDialog ref="FeedbackDialogRef" />
  </div>
</template>

<script>
// 接口
import userApi from '@recognize/apis/user'
import {
  REGISTER_SHORTCUT, // 注册快捷键
  SHORTCUT_RECOGNIZE, // 单张识别票据快捷键
  SHORTCUT_MULIT_RECOGNIZE, // 批量识别票据快捷键
  SHORTCUT_AUTO_SYNC, // 自动同步快捷键
  SHORTCUT_OPEN_RECOGNIZE_WINDOW,
  CONFIG_SETTING_CHANGE, // 触发常规设置、通知设置修改
  MINIMIZE,
  CLOSE
} from '@recognize/ipc-event-constant'
import { throttle } from '@/common/js/util'
import FeedbackDialog from '../feedback-dialog/feedback-dialog.vue'
import { tracking } from '@/utils/util'
import { mapState } from 'vuex'
// import Store, { KEYS, MIN_ZOOM_FACTOR, MAX_ZOOM_FACTOR } from '@/electron/src/electron-store'

// 识别记录保留时长 radio
const RADIOS = {
  TODAY: 1, // 当天
  THREE_DAYS: 3, // 近三天
  FOREVER: -1, // 永久
}
// 窗口关闭动作
export const CLOSE_ACTIONS = {
  MINIMIZE, // 最小化
  CLOSE // 退出
}

export default {
  name: 'general-setting-page',

  components: {
    FeedbackDialog, // 定向发布弹窗
  },
  data() {
    return {
      CLOSE_ACTIONS,
      RADIOS,
      recognizeDuration: RADIOS.TODAY, // 识别记录保留时长
      minZoomFactor: 0.8 * 100, // 最小缩放值
      // minZoomFactor: MIN_ZOOM_FACTOR * 100, // 最小缩放值
      maxZoomFactor: 1.2 * 100, // 最大缩放值
      // maxZoomFactor: MAX_ZOOM_FACTOR * 100, // 最大缩放值
      zoom: 100, // 缩放百分比
      setKeyList: [
        {
          name: 'single',
          nameZh: '单张识别',
          enable: true,
          keyStr: 'Alt+W',
          copy: 'Alt+W',
          isFocus: false,
          isRepeat: false,
        },
        {
          name: 'mulit',
          nameZh: '批量识别',
          enable: true,
          keyStr: 'Alt+E',
          copy: 'Alt+E',
          isFocus: false,
          isRepeat: false,
        },
        {
          name: 'auto',
          nameZh: '自动同步',
          enable: true,
          keyStr: 'Alt+Z',
          copy: 'Alt+Z',
          isFocus: false,
          isRepeat: false,
        },
        {
          name: 'recognize',
          nameZh: this.discernName,
          enable: true,
          keyStr: 'Alt+S',
          copy: 'Alt+S',
          isFocus: false,
          isRepeat: false,
        }
      ], // 快捷键设置列表
      nowFocus: -1, // 当前快捷键输入框编辑下标

      closeAction: '', // 关闭按钮默认操作
      // defaultPaymentChannel: null, // 默认支付渠道
      serialTag: false, // 是否连号票
      sellerMosaicType: 1, // 出票人打码方式
      buyerMosaicType: 1, // 收款人打码方式
      mosaicSet: {
        buyerMosaic: false, // 收款人打码
        sellerMosaic: false, // 出票人打码
      }, // 打码默认设置
      autoDelete: false, // 发布后自动删除
      ticketChecking: true, // 是否开启智能验票
      startupLogin: false, // 是否开机自启
    }
  },
  computed: {
    ...mapState('user', ['userInfo']),
  },

  created() {
    this.syncZoom()
    this.syncStartupConf()
  },

  async mounted() {
    // 获取用户设置
    await this.getConfig()
    // 组合键触监听函数
    this.keyCodeForEvent()
  },

  methods: {
    // 同步缩放值
    async syncZoom() {
      if (!this.$ipc) return
      // const store = await Store.init()
      this.zoom = (await this.$ipc.invoke('STORE_GET', 'ZOOM_FACTOR')) * 100
      this.$watch('zoom', newVal => {
        this.$ipc.invoke('STORE_SET', 'ZOOM_FACTOR', newVal / 100)
        this.$ipc.invoke('STORE_SET', 'SET_ZOOM_FACTOR_MANUAL', true)
      })
    },
    // 同步开机自启配置
    async syncStartupConf() {
      if (!this.$ipc) return
      this.startupLogin = await this.$ipc.invoke('GET_START_ON_LOGIN')
    },

    // 获取用户设置
    async getConfig() {
      const res = await userApi.getConfig()
      this.recognizeDuration = res.recognitionRecordKeepDays // 识别记录保留时长
      this.setKeyList = [
        {
          name: 'single',
          nameZh: '单张识别',
          enable: !!res.singleShortcutKeyEnable,
          keyStr: res.singleShortcutKey || 'Alt+W',
          copy: res.singleShortcutKey || 'Alt+W',
          isFocus: false,
          isRepeat: false,
        },
        {
          name: 'mulit',
          nameZh: '批量识别',
          enable: !!res.multiShortcutKeyEnable,
          keyStr: res.multiShortcutKey || 'Alt+E',
          copy: res.multiShortcutKey || 'Alt+E',
          isFocus: false,
          isRepeat: false,
        },
        {
          name: 'auto',
          nameZh: '自动同步',
          enable: !!res.autoSyncKeyEnable,
          keyStr: res.autoSyncKey || 'Alt+Z',
          copy: res.autoSyncKey || 'Alt+Z',
          isFocus: false,
          isRepeat: false,
        },
        {
          name: 'recognize',
          nameZh: this.discernName,
          enable: !!res.openRecognizeWindowKeyEnable,
          keyStr: res.openRecognizeWindowShortcutKey || 'Alt+S',
          copy: res.openRecognizeWindowShortcutKey || 'Alt+S',
          isFocus: false,
          isRepeat: false,
        }
      ]
      this.serialTag = res.serialTag === 1
      this.mosaicSet = {
        buyerMosaic: res.buyerMosaic === 1, // 收款人打码
        sellerMosaic: res.sellerMosaic === 1, // 出票人打码
      }
      // 打码设置
      this.buyerMosaicType = res.buyerMosaicType
      this.sellerMosaicType = res.sellerMosaicType
      this.autoDelete = !!res.autoDeleteAfterRelease
      this.ticketChecking = !!res.openDiscernVerify
      this.closeAction = res.defaultCloseAction
    },

    // 修改用户设置
    async putConfig(type = '') {
      await userApi.putConfig({
        recognitionRecordKeepDays: this.recognizeDuration, // 识别记录保留时长
        singleShortcutKeyEnable: this.setKeyList[0].enable ? 1 : 0, // 是否启用单张识别快捷键
        singleShortcutKey: this.setKeyList[0].keyStr, // 单张识别快捷键
        multiShortcutKeyEnable: this.setKeyList[1].enable ? 1 : 0, // 是否启用批量识别快捷键
        multiShortcutKey: this.setKeyList[1].keyStr, // 用批量识别快捷键
        autoSyncKeyEnable: this.setKeyList[2].enable ? 1 : 0, // 是否启用自动同步快捷键
        autoSyncKey: this.setKeyList[2].keyStr, // 用自动同步快捷键
        openRecognizeWindowKeyEnable: this.setKeyList[3].enable ? 1 : 0, // 是否启用识票助手快捷键
        openRecognizeWindowShortcutKey: this.setKeyList[3].keyStr, // 识票助手快捷键
        serialTag: this.serialTag ? 1 : 0, // 连号票
        buyerMosaic: this.mosaicSet.buyerMosaic ? 1 : 0, // 收款人打码
        sellerMosaic: this.mosaicSet.sellerMosaic ? 1 : 0, // 出票人打码
        autoDeleteAfterRelease: this.autoDelete ? 1 : 0, // 发布后自动删除
        openDiscernVerify: this.ticketChecking ? 1 : 0, // 是否开启智能验票
        defaultCloseAction: this.closeAction, // 关闭按钮默认行为
        sellerMosaicType: this.sellerMosaicType, // 出票人打码方式
        buyerMosaicType: this.buyerMosaicType, // 收款人打码方式
      })
      this.$event.emit(CONFIG_SETTING_CHANGE, { type })
      if (this.$ipc) {
        this.$ipc.send(CONFIG_SETTING_CHANGE, { type })
      }
    },

    // 选择识别记录保留时长
    radioChange(val) {
      this.recognizeDuration = val
      throttle(this.putConfig('days'), 1000)
    },

    // 连号票改变
    serialChange(val) {
      this.serialTag = val
      throttle(this.putConfig('serial'), 1000)
    },

    // 是否开启单张识别快捷键复选框监听
    checkboxChange(index) {
      this.setGlobalShortcut(this.setKeyList[index].enable ? this.setKeyList[index].keyStr : '', index)
    },

    setShortcut(index) {
      this.nowFocus = index
      this.setKeyList[index].keyStr && (this.setKeyList[index].copy = this.setKeyList[index].keyStr)
      this.setKeyList[index].keyStr = ''
      this.setKeyList[index].isRepeat = false
      this.setKeyList[index].isFocus = true
    },

    // 获取单张识别焦点
    focus(index) {
      this.setKeyList.forEach((item, i) => {
        if (i === index) {
          item.isFocus = true
        }
        item.isFocus = false
      })
    },

    // 快捷键失去焦点
    shortcutBlur(index) {
      const item = this.setKeyList[index]
      this.setKeyList[index].keyStr = item.keyStr.indexOf('+') > -1 && !item.isRepeat ? item.keyStr : item.copy
      this.setKeyList[index].isFocus = false
      this.setKeyList[index].isRepeat = false
    },

    // 设置全局快捷键，通知 Electron 主进程,type: 0=>单张识别快捷键；1=>批量识别快捷键,3=>自动同步快捷键；
    async setGlobalShortcut(shortcut, type = 0) {
      const keyList = [SHORTCUT_RECOGNIZE, SHORTCUT_MULIT_RECOGNIZE, SHORTCUT_AUTO_SYNC, SHORTCUT_OPEN_RECOGNIZE_WINDOW]
      // 当只有一个键时不注册
      if (shortcut && !shortcut.includes('+')) return
      // 这里要将 shortcut 再做一下转换，然后作为第三个参数传给主进程
      // const globalShortcut = shortcut.replace(/Ctrl/g, 'Control')
      let isRegistered
      isRegistered = await this.$ipc.invoke(REGISTER_SHORTCUT, keyList[type], shortcut)
      if (!isRegistered) {
        this.setKeyList[type].keyStr = this.setKeyList[type].copy
        this.$message.error('快捷键已被占用')
      } else {
        throttle(this.putConfig('key'), 1000)
      }
    },

    // 组合键触监听函数
    keyCodeForEvent() {
      let keyObj = {}
      document.onkeydown = event => {
        const { nowFocus, setKeyList } = this
        this.nowFocus !== -1 && event.preventDefault()
        if ((event.altKey || event.ctrlKey || event.shiftKey || event.metaKey) && (this.nowFocus !== -1)) {
          keyObj[this.titleCase(this.keyFilter(event.code))] = 1
          let shortcut = this.keyJoin(keyObj)
          const hasRepeat = setKeyList.findIndex(v => v.keyStr === shortcut)
          if (hasRepeat > -1 && hasRepeat !== nowFocus) { // 重复
            this.setKeyList[nowFocus].isRepeat = true
            this.setKeyList[nowFocus].keyStr = shortcut
            this.nowFocus = -1
            return
          }
          this.setKeyList[nowFocus].keyStr = shortcut
          this.setKeyList[nowFocus].enable && this.setGlobalShortcut(shortcut, nowFocus)
          // 超过两个key直接返回
          if (Object.keys(keyObj).length >= 2) {
            keyObj = {}
            this.nowFocus = -1
            return this.$refs.refShortcut[nowFocus].blur()
          }
        }
      }
      document.onkeyup = event => {
        event.preventDefault()
        delete keyObj[this.titleCase(this.keyFilter(event.code))]
      }
    },

    closeActionChange() {
      throttle(this.putConfig(''), 1000)
    },

    // 打码改变回调
    mosaicChange() {
      throttle(this.putConfig('mosaic'), 1000)
    },

    // key过滤
    keyFilter(key) {
      const result = key.replace(/(Shift|Alt|Meta).*/, '$1')
        .replace(/Control.*/, 'Ctrl')
        .replace(/Key/, '')
      return result
    },

    // key拼接
    keyJoin(keyObj) {
      return Object.keys(keyObj).join('+')
    },

    // 首字母大写
    titleCase(str) {
      return str.slice(0, 1).toUpperCase() + str.slice(1).toLowerCase()
    },

    // 点击了默认支付渠道
    paymentChange() {
      throttle(this.putConfig('pay'), 1000)
    },

    // 自动删除改变
    autoDeleteChange(autoDelete) {
      if (autoDelete) {
        this.autoDelete = false
        this.$confirm('<p class="message">发布后自动删除识别记录？</p><p class="message">开启后，已发布的票据将自动从识别列表删除</p><p class="message">（开启之前的记录不受影响，您可手动删除）</p>', '提示', {
          dangerouslyUseHTMLString: true,
          width: '490px',
          type: 'warning',
          showClose: false,
          iconPosition: 'title',
          confirmButtonText: '确认',
        }).then(() => {
          this.autoDelete = true
          throttle(this.putConfig(''), 1000)
        })
          .catch(err => {
            // eslint-disable-next-line no-console
            console.log(err)
          })
      } else {
        throttle(this.putConfig(''), 1000)
      }
    },

    // 智能验票开关改变
    ticketCheckingChange() {
      throttle(this.putConfig(''), 1000)
    },
    // 出票人/收款人打码方式设置
    codeSettingChange() {
      throttle(this.putConfig(''), 1000)
    },
    // 自启动配置修改
    startupLoginChange(value) {
      if (!this.$ipc) return
      this.$ipc.invoke('SET_START_ON_LOGIN', value)
      // 日志上报
      tracking({ automaticStr: value ? 'open' : 'close' })
    }
  }
}
</script>
