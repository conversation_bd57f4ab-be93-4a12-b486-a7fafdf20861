<style lang="scss" scoped>
.window-set-page {
  display: flex;
  overflow-y: hidden;
  padding-bottom: 16px;
  width: 100%;
  height: 100%;

  .left {
    overflow-y: auto;
    width: 150px;
    height: 592px;
    flex: none;

    .inner {
      height: 100%;
      background: $color-FFFFFF;
    }

    .item {
      padding-left: 16px;
      height: 40px;
      line-height: 40px;
      cursor: pointer;

      &.active {
        font-weight: 600;
        color: $--color-primary;
        background-color: $--color-primary-hover !important;
      }

      &:hover {
        background-color: $--color-primary-hover;
      }
    }
  }

  .right {
    overflow-y: auto;
    margin-left: 10px;
    height: 592px;
    background: $color-FFFFFF;
    flex: 1;
  }
}
</style>

<template>
  <el-dialog
    title="设置"
    :visible.sync="dialogVisible"
    width="734px"
    append-to-body
  >
    <div class="window-set-page">
      <div class="left">
        <div class="inner">
          <div
            v-for="item in tab.filter(item => item.visible)"
            :key="item.key"
            :class="['item', item.key === currentKey ? 'active' : '']"
            @click="taggle(item.key)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
      <div class="right">
        <!-- 常规设置 -->
        <div v-if="currentKey === tab[0].key" :key="tab[0].key">
          <GeneralSetting />
        </div>
        <!-- 通知设置 -->
        <div v-if="currentKey === tab[1].key" :key="tab[1].key">
          <Notice />
        </div>
        <!-- 网银设置 -->
        <div v-if="currentKey === tab[2].key" :key="tab[2].key">
          <IdentificationSetting />
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import GeneralSetting from './general-setting/general-setting.vue'
import Notice from './notice/notice.vue'
import IdentificationSetting from './identification-setting/identification-setting.vue'

export default {
  name: 'window-set-page',
  components: {
    GeneralSetting,
    Notice,
    IdentificationSetting
  },
  data() {
    return {
      dialogVisible: false,
      tab: [
        { name: '常规设置', active: true, key: 1, visible: true },
        { name: '通知设置', active: false, key: 2, visible: false },
        { name: '网银设置', active: false, key: 3, visible: !!this.$ipc },
      ],
      currentKey: 1, // 当前tab
    }
  },
  methods: {
    open() {
      this.dialogVisible = true
    },
    // 切换左边
    taggle(key) {
      this.currentKey = key
    },
  }
}
</script>
