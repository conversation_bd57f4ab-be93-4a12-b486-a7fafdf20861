<!-- 批量修改弹窗 -->
<style lang="scss" scoped>
::v-deep {
  .el-dialog__body {
    padding: 12px 14px 0;
    color: $color-text-primary;
    background-color: $color-F2F2F2;
  }

  .el-dialog__footer {
    color: $color-text-primary;
    background-color: $color-F2F2F2;
  }

  .el-input__inner {
    font-size: 14px;
    text-align: right;
  }
}

.main {
  overflow-y: auto;
  height: auto;
}

.block-box {
  margin-bottom: 12px;
  padding: 12px 16px;
  background-color: $color-FFFFFF;
}

.contacts-box {
  width: 60%;

  ::v-deep {
    .el-input__inner {
      font-size: 14px;
      text-align: left;
    }
  }
}

.offer-flex {
  display: flex;
  align-items: flex-end;

  .el-input {
    margin-right: 10px;
    width: 170px;
  }

  .form-item-block {
    .el-input {
      margin-right: 10px;
      width: 350px;
    }
  }

  .toggle-type {
    display: flex;
    align-items: center;
    margin-left: 3px;
    border: 1px solid $color-D9D9D9;
    border-radius: 20px;
    padding: 0 18px;
    height: 40px;
    color: $--color-primary;
    cursor: pointer;

    &:active {
      background-color: $--color-primary-hover;
    }

    .icon-swap {
      margin-right: 10px;
    }
  }
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin-top: 12px;

  .item {
    margin-bottom: 12px;
    width: 50%;
  }

  .switch-box {
    @include flex-vc;

    .el-switch {
      margin-right: 10px;
    }

    .red {
      @include bold;
    }

    .el-input {
      margin-left: 10px;
      width: 140px;
    }
  }

  .example {
    @include example-underline;

    margin-left: 8px;
  }
}

.black-list-box {
  margin-bottom: 8px;
}

.subtitle {
  margin-bottom: 4px;
  color: $color-text-secondary;

  @include flex-vc;

  .svg-icon {
    margin-left: 5px;
  }
}

.bargain {
  position: relative;

  .bargain-input {
    position: absolute;
    left: 217px;
  }
}
</style>

<template>
  <el-dialog
    title="批量设置"
    :visible.sync="dialogVisible"
    style-type="border"
    width="964px"
    append-to-body
    :close-on-click-modal="false"
    top="5vh"
    custom-class="custom-class"
    :before-close="handleClose"
  >
    <div class="main">
      <div class="block-box">
        <div class="g-title">瑕疵（可多选）</div>
        <FormItem label="">
          <TicketFlawForm
            :defect-set="form.defectSet"
            @change="handleChangeFlaw"
            @change-defect-set="handleDefectSet"
          />
        </FormItem>
      </div>
      <div class="block-box">
        <div class="title-flex">
          <div class="g-title">报价填写</div>
        </div>
        <div class="offer-flex">
          <template
            v-if="form.billingMethod === OFFER_TYPE_CODE.NIAN_LI_LV_JI_SUAN"
          >
            <FormItem label="年化利率">
              <el-input
                v-model="form.annualInterest"
                type="number"
                placeholder="年化利率"
                :number-format="{
                  maxDecimalLength: 4,
                  leadingZero: false,
                  negative: false,
                  maxLength: 7,
                }"
              >
                <span slot="append">%</span>
              </el-input>
            </FormItem>
            <FormItem label="每十万手续费">
              <el-input
                v-model="form.serviceCharge"
                type="number"
                placeholder="每十万手续费"
                :number-format="{
                  maxDecimalLength: 2,
                  leadingZero: false,
                  negative: false,
                  maxLength: 8,
                }"
              >
                <span slot="append">元</span>
              </el-input>
            </FormItem>
          </template>
          <FormItem v-else label="每十万扣款" class="form-item-block">
            <el-input
              v-model="form.lakhDeduction"
              type="number"
              placeholder="每十万扣款"
              :number-format="{
                maxDecimalLength: 2,
                leadingZero: false,
                negative: false,
                maxLength: 8,
              }"
            >
              <span slot="append">元</span>
            </el-input>
          </FormItem>
          <div class="toggle-type" @click="OfferTypeToggle">
            <icon type="chengjie-swap" class="icon-swap" />
            {{
              form.billingMethod === OFFER_TYPE_CODE.NIAN_LI_LV_JI_SUAN
                ? "每十万扣款"
                : "年化利率+每十万手续费"
            }}
          </div>
        </div>
      </div>
      <div class="block-box">
        <div class="g-title">其他设置</div>
        <div class="row">
          <!-- 经纪商没有光速雷达 -->
          <div v-if="!limitLight" class="item">
            <transaction-tooltip-button
              :types="[TRANSACTION_TOOLTIP_TYPE.FAST]"
              @change="
                (val) =>
                  val === TRANSACTION_TOOLTIP_TYPE.FAST &&
                  (form.fastTrade = false)
              "
            >
              <template v-slot:content="{ disabled }">
                <div class="subtitle">极速出票</div>
                <div class="switch-box" style="align-items: flex-start;">
                  <el-switch
                    v-model="form.fastTrade"
                    :disabled="disabled"
                    @change="(val) => (form.margin = val)"
                  />
                  <template v-if="form.fastTrade">
                    极速出票开启，票在户，免确认，请
                    <span class="red">10分钟</span> 内背书
                  </template>
                  <template v-else>
                    开启极速出票，享受订单优先展示特权。
                  </template>
                  <span class="example" @click="look">详情</span>
                </div>
              </template>
            </transaction-tooltip-button>
          </div>

          <div class="item">
            <div class="subtitle">保证金</div>
            <div class="switch-box">
              <el-switch v-model="form.margin" />
              极速交易订单必须开启保证金，保障双方权益。
            </div>
          </div>

          <div class="item">
            <div class="subtitle">议价</div>
            <div class="switch-box bargain">
              <el-switch v-model="form.bargaining" />
              <template v-if="form.bargaining">
                接受议价，请输入议价上限
                <el-input
                  v-model="form.bargainingLimit"
                  class="bargain-input"
                  placeholder="每十万扣款"
                  type="number"
                  :number-format="{
                    maxDecimalLength: 2,
                    leadingZero: false,
                    negative: false,
                    maxLength: 8,
                  }"
                >
                  <span slot="append">元</span>
                </el-input>
              </template>
              <template v-else> 不接受议价 </template>
            </div>
          </div>
          <div class="item">
            <div class="subtitle">
              连号票标签
              <el-tooltip effect="dark" placement="top">
                <div slot="content">
                  连号票订单由同一票方发布，且承兑人、票面金额、到期日一致
                </div>
                <icon class="icon icon-question" type="chengjie-wenti" />
              </el-tooltip>
            </div>
            <div class="switch-box">
              <el-switch
                v-model="form.serialTag"
                :disabled="sameList.length === 0"
              />
              开启后可展示连号票标签。
            </div>
          </div>
          <div class="item">
            <div class="subtitle">出票人打码</div>
            <div class="switch-box">
              <el-switch v-model="form.drawerMosaic" />
              开启后，订单中展示的正面图片，将自动为出票人打码。
            </div>
          </div>
          <div class="item">
            <div class="subtitle">收款人打码</div>
            <div class="switch-box">
              <el-switch v-model="form.receiverMosaic" />
              开启后，订单中展示的正面图片，将自动为收款人打码。
            </div>
          </div>
        </div>
        <div class="black-list-box">
          <div class="subtitle">
            资方所在地黑名单
            <el-tooltip effect="dark" placement="top">
              <div slot="content">
                添加黑名单地区后，对应地区的用户将看不到您发布的票据。
              </div>
              <icon class="icon icon-question" type="chengjie-wenti" />
            </el-tooltip>
          </div>
          <!-- 黑名单列表 -->
          <BlackList v-model="form.blackRegionList" />
        </div>
      </div>
      <!--
        <div class="block-box">
        <ContactItem class="contacts-box" @change-data="onChangeMobile" />
        </div>
      -->
      <!--
        <ContactSettingField
        ref="ContactSettingField"
        v-model="form.mobile"
        class="contact-field"
        :icon-size="23"
        :is-border="true"
        @close="closeAll"
        >
        <template #label>
        <span class="g-title">联系方式</span>
        </template>
        </ContactSettingField>
      -->
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="large" @click="handleClose">取消</el-button>
      <el-button
        type="secondary"
        size="large"
        @click="handleConfirm"
      >确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import FormItem from '../components/form-item.vue' // 自定义表单
import TicketFlawForm from '../components/ticket-flaw-form.vue' // 瑕疵
import {
  OFFER_TYPE_CODE, // 报价类型
  TRANSACTION_TOOLTIP_TYPE, // 交易限制提示类型
} from '@recognize/constant'
import BlackList from '../components/black-list.vue' // 黑名单列表
// import ContactSetting from '../components/contact-setting.vue'
import { mapGetters } from 'vuex'
import { FASTTRADE_URL } from '@/constants/oss-files-url'
// import ContactItem from '../components/contact-item.vue' // 拆分规则
// import ContactSettingField from '@/views/pages/setting/components/contact-setting-field.vue'

export default {
  name: 'batch-edit-dialog',

  components: {
    FormItem, // 自定义表单
    TicketFlawForm, // 瑕疵
    BlackList, // 黑名单列表
    // ContactSetting
    // ContactItem,
    // ContactSettingField
  },

  props: {
    defaultConfig: Object, // 默认配置
  },

  data() {
    return {
      TRANSACTION_TOOLTIP_TYPE, // 交易限制提示类型
      OFFER_TYPE_CODE,
      dialogVisible: false,
      form: {
        billingMethod: OFFER_TYPE_CODE.MEI_SHI_WAN_ZHI_KOU, // 报价方式
        lakhDeduction: '', // 每十万扣息
        annualInterest: '', // 输入的利率
        serviceCharge: '', // 每十万手续费
        fastTrade: 0, // 是否光速订单
        serialTag: 0, // 是否连号票，0-否、1-是
        margin: 0, // 是否需要保证金，0-否、1-是
        bargaining: 0, // 是否接受议价，0-否、1-是
        needVoucher: 0, // 是否需要交易凭证，0-否、1-是
        bargainingLimit: '', // 议价上限值
        blackRegionList: [], // 屏蔽的省市列表 ,Region
        receiverMosaic: false, // 收款人打码
        drawerMosaic: false, // 出票人打码
        // mobile: []// 联系方式
      },
      defectObj: {
        ticketFlawobj: {}, // 瑕疵对象
        defects: '', // 瑕疵字符串
        defectSet: false, // 瑕疵设置
      },
      sameList: [], // 连号票号列表
      enterSerialTag: false, // 进来时的连号票标签开关
    }
  },

  computed: {
    ...mapGetters('user', {
      limitLight: 'limitLight', // 是否限制光速
    }),
    // ...mapGetters('common', {
    //   mobilePattern: 'mobilePattern', // 是否开启联系方式模式 1开启 0关闭
    // }),
  },

  watch: {
    // 监听议价开关
    'form.bargaining'(v) {
      !v && (this.form.bargainingLimit = '')
    },
    // 监听保证金
    'form.margin'(v) {
      if (!v) {
        this.form.fastTrade = false
      }
    },
    // 监听默认配置
    defaultConfig: {
      handler(n) {
        n = JSON.parse(JSON.stringify(n))
        // 经纪商ERP没有光速（fastTrade）  只有承让有
        this.form.margin = n?.fastTrade === 1 || n?.releaseMargin === 1
        this.form.fastTrade = n?.fastTrade === 1
        this.form.bargaining = n?.bargain === 1
        this.form.bargainingLimit = n?.bankDraftBargainLimit
        this.form.serialTag = n?.serialTag === 1
        this.form.blackRegionList = n.areaBlackConfig
        this.form.receiverMosaic = n.receiverMosaic
        this.form.drawerMosaic = n.drawerMosaic
      },
      deep: true,
    },
  },

  methods: {
    // 切换显示隐藏
    toggle(sameList) {
      this.sameList = sameList
      sameList.length === 0 && (this.form.serialTag = 0)
      this.dialogVisible = !this.dialogVisible
      this.enterSerialTag = this.form.serialTag
    },

    // 选择完瑕疵 两种数据格式
    handleChangeFlaw(obj, defects) {
      this.defectObj.ticketFlawobj = obj
      this.defectObj.defects = defects
    },

    // 更新瑕疵设置状态
    handleDefectSet(val) {
      this.defectObj.defectSet = val
    },

    // 输入利率/每十万手续费/每十万扣息
    handleInput(val, key) {
      this.form[key] = val
    },

    // 报价类型切换
    OfferTypeToggle() {
      this.form.billingMethod
        = this.form.billingMethod === OFFER_TYPE_CODE.NIAN_LI_LV_JI_SUAN
          ? OFFER_TYPE_CODE.MEI_SHI_WAN_ZHI_KOU
          : OFFER_TYPE_CODE.NIAN_LI_LV_JI_SUAN
    },

    // 点击确定
    handleConfirm() {
      // const mobile = this.$refs.contactSetting.mobiles.join()
      // erp没有设置联系方式
      // if (!mobile) {
      //   this.$message.info('请设置联系方式')
      //   return
      // }
      // this.$emit('confirm', { ...this.form, mobile }, this.defectObj)
      // if (!this.validContact(this.form)) {
      //   return
      // }
      this.dialogVisible = false
      this.$emit('confirm', { ...this.form }, this.defectObj)
    },

    // 查看详情
    look() {
      window.open(FASTTRADE_URL)
    },
    // validContact(obj) {
    //   const { mobile } = obj
    //   // 联系方式模式
    //   if (this.mobilePattern) {
    //     if (!mobile) {
    //       this.$message.info('请先设置联系方式')
    //       return false
    //     }
    //   }

    //   // 非联系方式模式
    //   if (!this.mobilePattern) {
    //     if (!mobile) return true // 未填写不校验
    //   }

    //   // 手机号校验位数
    //   const list = mobile.split(',')
    //   if (!list.every(item => item.length === 11)) {
    //     this.$message.info('请输入11位数手机号')
    //     return false
    //   }

    //   // 手机号校验重复
    //   const uniqueSet = new Set(list)
    //   if (uniqueSet.size !== list.length) {
    //     this.$message.error('联系方式手机号不能重复')
    //     return false
    //   }
    //   return true
    // },

    // 关闭回调,恢复原来的连号票标签设置
    handleClose(done) {
      this.form.serialTag = this.enterSerialTag
      typeof done === 'function' ? done() : (this.dialogVisible = false)
    },
    // 联系人方式默认设置关闭所有窗口
    closeAll() {
      // 网页端关闭 客户端不关
      if (!this.$ipc) {
        this.form.serialTag = this.enterSerialTag
        this.dialogVisible = false
        this.$emit('close')
      }
    }

    // 手机号修改
    // onChangeMobile(data) {
    //   this.form.mobile = data
    // },
  },
}
</script>
