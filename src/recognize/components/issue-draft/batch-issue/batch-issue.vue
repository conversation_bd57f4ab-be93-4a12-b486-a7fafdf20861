<!-- eslint-disable max-lines -->
<!-- 批量发布 -->
<style lang="scss" scoped>
.batch-issue-page {
  .batch-issue-container {
    width: 100%;
    background: $color-F2F2F2;

    .payment-channel {
      display: flex;
      margin-bottom: 12px;
      padding: 12px 16px;
      background-color: $color-FFFFFF;

      .form-item {
        margin: 0 0 0 22px;
      }
    }

    .container-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid $color-F0F0F0;
      padding: 12px 16px;
      font-size: 18px;
      background-color: $color-FFFFFF;
      line-height: 26px;

      .num {
        font-weight: 500;
        color: $color-warning;
      }
    }
  }

  .search-box {
    display: flex;

    .discern-type-box {
      margin-right: 8px;
    }
  }

  ::v-deep {
    .el-input-group__prepend {
      background-color: $color-FFFFFF;
    }

    .el-select {
      .el-input__inner {
        padding-left: 12px;
        width: 127px;
        color: $color-text-primary;
      }
    }

    .spicon-search {
      font-size: 22px;
      line-height: 22px;
    }

    .el-button--secondary.is-disabled,
    .el-button--secondary.is-disabled:hover,
    .el-button--secondary.is-disabled:focus,
    .el-button--secondary.is-disabled:active {
      border-color: #D9D9D9;
      font-weight: 500;
      color: #BFBFBF;
      background: #F5F5F5;
    }
  }

  .footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid $color-F0F0F0;
    height: 66px;
    color: $color-text-primary;
    background: $color-F2F2F2;

    .footer-left {
      display: flex;
      align-items: center;
    }

    &-text {
      font-size: 16px;
      font-weight: 600;
      color: $color-text-primary;
    }

    &-icon {
      vertical-align: text-bottom;
      margin-right: 7px;
    }

    &-red {
      color: $color-warning;
    }

    .protocol-tip {
      color: $color-text-primary;
    }
  }
}

.item-position {
  position: relative;
  display: flex;
  align-items: center;

  .value {
    .el-tooltip {
      position: absolute;
      top: 10px;
      right: -34px;
    }
  }

  ::v-deep .selle-bank-account {
    margin-left: 10px;
    width: 100%;

    .el-input__inner {
      width: 100%;
    }
  }
}

.seller-select-btn {
  font-size: 16px;
  text-align: center;
  color: $--color-primary;
  line-height: 40px;
  cursor: pointer;
}

.seller-bank-link {
  border-bottom: 1px solid $--color-primary;
  color: $--color-primary;
  cursor: pointer;
}

.flex-box {
  display: flex;
  align-items: center;
}
</style>

<style lang="scss">
.offsetTop {
  top: 100px !important;
}

.el-message-box__wrapper {
  .el-message-box .el-message-box__content {
    .red-soso {
      color: $color-warning;
    }
  }
}

.alert-red-high-light {
  font-weight: 600;
  color: $color-warning;
}
</style>

<template>
  <el-dialog
    title="批量发布"
    :visible.sync="dialogVisible"
    width="1268px"
    append-to-body
    destroy-on-close
    :before-close="handleCancel"
  >
    <div class="batch-issue-page">
      <div class="batch-issue-container">
        <!-- 支付渠道 -->
        <div class="payment-channel">
          <div class="g-title">支付渠道</div>
          <FormItem>
            <!-- 支付渠道组件 -->
            <pay-radio :is-new-draft="nowSelHasNewDraft" :type="defaultConfig.defaultPaymentChannelList" @change="handlePayChange" />
          </FormItem>

          <FormItem
            v-if="payInfo.acceptJdYlpay || payInfo.acceptZbankPlus || payInfo.acceptYiPlusPlus "
            label="回款账户"
            class="form-item-block item-position"
            required
          >
            <el-tooltip
              placement="top"
              popper-class="issue-draft-tooltip"
            >
              <template slot="content">
                <div>
                  依照智付E+、E++渠道交易模式，票方发布票据必须选择回款账户(支持任意银行一般户)。需要在
                  <span
                    class="seller-bank-link"
                    @click="() => {
                      if ($ipc) {
                        $ipc.send('PUSH_ROUTE_IN_MAIN_WINDOW', '/user-center/bank-account?tabStatus=2')
                      } else {
                        $router.push('/user-center/bank-account?tabStatus=2')
                        dialogVisible = false
                      }
                    }"
                  >银行回款账户</span>页面绑定回款账户，交易完成后票款自动提现到账。
                </div>
              </template>
              <icon class="icon icon-question" type="chengjie-wenti" />
            </el-tooltip>

            <div class="pay-type-item">
              <el-select
                ref="sellerBankAccount"
                v-model="sellerBankAccountId"
                class="selle-bank-account"
                placeholder="请选择回款账户"
                size="small"
              >
                <el-option
                  v-for="item in sellerBankAccountList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
                <div
                  class="seller-select-btn"
                  @click="() => {
                    if ($ipc) {
                      $ipc.send('PUSH_ROUTE_IN_MAIN_WINDOW', '/user-center/bank-account?tabStatus=2')
                      $refs.sellerBankAccount.blur();
                    } else {
                      $router.push('/user-center/bank-account?tabStatus=2')
                      $refs.sellerBankAccount.blur();
                      dialogVisible = false
                    }
                  }"
                >
                  <i class="el-icon-plus" />添加回款账户
                </div>
              </el-select>
            </div>
          </FormItem>
        </div>

        <div class="container-header">
          <div class="total">
            共选中 <span class="num">{{ nowSel.length }}</span> 张票据，总金额 <span class="num">{{ selectAmount }}</span> 万元
          </div>
          <div class="search-box">
            <div class="discern-type-box">
              <el-select v-model="discernType" placeholder="请选择">
                <el-option
                  v-for="item in discernTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  popper-class="identify-type"
                />
              </el-select>
            </div>
            <el-input
              v-model="keyword"
              placeholder="请输入搜索内容"
              class="discern-type-box"
              :clearable="true"
              prefix-icon="el-icon-search"
            >
              <i slot="prefix" class="el-input__icon spicon spicon-search" />
              <el-select slot="prepend" v-model="searchSelect" placeholder="请选择">
                <el-option
                  v-for="item in searchConditionOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-input>
            <!-- 18065:下线ECDS票据相关内容 -->
            <!--
              <el-select
              v-model="draftType"
              class="status-select status-select2"
              >
              <el-option
              v-for="item in draftTypeOptions"
              :key="item.label"
              :label="item.label"
              :value="item.value"
              />
              </el-select>
            -->
          </div>
          <div class="flex-box">
            <div style="display: inline-block;margin-right: 8px;">
              <QueryOrderPrice :height="42" />
            </div>
            <el-button
              type="primary"
              border
              size="large"
              :disabled="nowSel.length === 0"
              @click="handleDel"
            >
              删除
            </el-button>
            <el-button
              type="primary"
              border
              size="large"
              @click="handleBatchEdit"
            >
              批量设置
            </el-button>
          </div>
        </div>
        <div class="table-box">
          <BatchIssueTable
            ref="tableRef"
            :table-data="draftList"
            :select-list="selectDraftNoList"
            @input="handleInput"
            @change-type="handleChangeType"
            @handle-edit="handleEdit"
            @selection-change="handleSelectionChange"
            @show-quotation="handleShowQuotation"
          />
        </div>
      </div>

      <div class="footer">
        <!--
          <p class="footer-text">
          <icon
          class="footer-icon"
          type="chengjie-exclamation-circle"
          color-theme="primary"
          :size="20"
          />
          <template v-if="!isShowNoInfoTip">
          批量识别的票据，可在票据右侧的<span class="footer-red"> 设置 </span>里修改单张的瑕疵信息
          </template>
          <template v-else>
          未检测到网银票据列表中的出票人信息/收款人信息，如需补充，请使用单张识别。
          </template>
          </p>
        -->
        <!-- 隐藏新一代票据收益权转让合同/票据收益权转让合同 -->
        <div class="footer-left">
          <!--
            <el-checkbox v-model="isAgreeProtocol">
            <span class="protocol-tip">我已阅读并同意</span>
            </el-checkbox>
          -->
          <!--
            <span v-if="nowSelHasOldDraft" class="text-link" @click="openProtocol(OSS_FILES_URL.AGREEMENT_OF_TRANSFER)">《票据收益权转让合同》</span>
            <span v-if="nowSelHasNewDraft" class="text-link" @click="openProtocol(OSS_FILES_URL.AGREEMENT_OF_TRANSFER_NEW_DRAFT)">《新一代票据收益权转让合同》</span>
          -->
          <!-- <NewDraftTipView v-if="nowSelHasNewDraft" ref="newDraftTipViewRef" :is-batch="true" /> -->
        </div>
        <div>
          <el-button size="large" @click="handleCancel">取消</el-button>
          <transaction-tooltip-button
            v-if="!mobilePattern"
            border
            size="large"
            :types="nowSelHasFastTrade ? [TRANSACTION_TOOLTIP_TYPE.HOLIDAY, TRANSACTION_TOOLTIP_TYPE.DAILY] : [TRANSACTION_TOOLTIP_TYPE.HOLIDAY]"
            @click="handleDirectionShow"
          >
            定向发布
          </transaction-tooltip-button>
          <transaction-tooltip-button
            type="secondary"
            size="large"
            :types="[TRANSACTION_TOOLTIP_TYPE.CREDIT]"
            @click="confirmIssue('')"
          >
            确认发布
          </transaction-tooltip-button>
        </div>
      </div>
    </div>

    <!-- 批量设置弹窗 -->
    <BatchEditDialog
      ref="batchDialogRef"
      :default-config="defaultConfig"
      @close="handleCancel"
      @confirm="handleConfirmBatch"
    />

    <!-- 设置弹窗 -->
    <EditDialog
      ref="EditDialogRef"
      :has-yl-bank-payment="payInfo.acceptYlpay === 1"
      @close="handleCancel"
      @confirm="handleConfirmEdit"
    />

    <!-- 定向发布弹窗 -->
    <DirectionDialogRef
      ref="directionDialogRef"
      :drafts="nowSel"
      @confirm="handleConfirmDirection"
      @open-margin="openMargin"
    />

    <!-- 发布结果状态弹窗 -->
    <ResultStatusDialog ref="resultStatusDialogRef" @confirm="handleConfirmResultStatus" />

    <!-- 支付账户异常弹窗 -->
    <PayAccountError ref="payAccountErrorRef" />

    <!-- 光速交易订单开启确认弹窗 -->
    <FastTradeDialog
      ref="fastTradeDialogRef"
      @publish="confirmIssue"
      @direction-publish="handleDirectionShow"
      @agree="agreeFastTrade"
    />
    <!-- 报价参考弹窗 -->
    <QuotationReferenceDialog ref="quotationReferenceDialogRef" scene="multiple" />
    <!-- 西部信托利率提示弹窗 -->
    <RateTipsDialog ref="rateTipsRef" scene="multiple" @on-publish="({list, inviteCode}) => issueDraft(list, inviteCode)" />
  </el-dialog>
</template>

<script>
// 窗口布局
import payRadio from '../components/pay-radio.vue' // 支付渠道组件
import BatchIssueTable from './batch-issue-table.vue' // 表格内容组件
import BatchEditDialog from './batch-edit-dialog.vue' // 批量设置弹窗
import EditDialog from '../components/edit-dialog.vue' // 设置弹窗
import DirectionDialogRef from '../components/direction-issue-dialog.vue' // 定向发布弹窗
import ResultStatusDialog from '../components/result-status-dialog.vue' // 发布结果状态弹窗
import FormItem from '../components/form-item.vue' // 自定义表单
// 票据正面和背面截图生成组件
import imgGenerator from '@/views/components/market-draft-image-generator/market-draft-image-generator.js'
// import { OPEN_URL_IN_DEFAULT_BROWSER } from '@recognize/ipc-event-constant'
import {
  OFFER_TYPE_CODE, // 报价类型
  IDENTIFY_TYPE, // 识别类型
  BACK_DEFECT_TYPE_SHOW_NUM_MAP, // 票据瑕疵类型 id 映射 是否显示数字
  ACCEPTOR_TYPE, // 承兑人类型
  TRANSACTION_TOOLTIP_TYPE,
  BAN_STATUS, // 禁用状态
} from '@recognize/constant'
import BigNumber from 'bignumber.js'
import { yuan2wan, wan2yuan, yuan2fen } from '@/common/js/number' // 金额单位转换
import { mapGetters } from 'vuex'
import ticketApi from '@recognize/apis/ticket' // 接口
import {
  lakhDeductionMath, interestRateMath, lakhDeductionMathWithBargain, interestRateMathWithBargain
} from '@/common/js/draft-math'
import { defectStrToObj } from '@/common/js/draft-flaw'
import mixpanel from '@recognize/utils/mixpanel'
import userApi from '@recognize/apis/user'
import PayAccountError from '../components/pay-account-error.vue'
import FastTradeDialog from '../components/fast-trade-dialog.vue' // 光速交易订单开启确认弹窗
import QuotationReferenceDialog from '../components/quotation-reference-dialog.vue'
import { windowCommunication } from '@/utils/window-event'
import { PLATFORM_DEFAULT_RULESNEW_URL, OSS_FILES_URL } from '@/constants/oss-files-url' // 平台订单违约规则url
// import NewDraftTipView from '../components/new-draft-tip-view-rec.vue' // 新票提示控件
import RateTipsDialog from '@/views/components/issue-draft/rate-tips-dialog.vue'
import PublishCheck from '@/views/pages/issue-draft/mixins/publish-check.js'
import QueryOrderPrice from '@/views/components/query-order-price/query-order-price.vue'

export default {
  name: 'batch-issue-draft',

  components: {
    payRadio, // 支付渠道组件
    BatchIssueTable, // 表格内容组件
    BatchEditDialog, // 批量设置弹窗
    EditDialog, // 设置弹窗
    DirectionDialogRef, // 定向发布弹窗
    FormItem, // 自定义表单
    ResultStatusDialog, // 发布结果状态弹窗
    PayAccountError, // 支付账户异常弹窗
    FastTradeDialog, // 光速交易订单开启确认弹窗
    QuotationReferenceDialog, // 报价参考弹窗
    // NewDraftTipView,
    RateTipsDialog,
    QueryOrderPrice
  },
  mixins: [PublishCheck],
  data() {
    return {
      PLATFORM_DEFAULT_RULESNEW_URL,
      OSS_FILES_URL,
      TRANSACTION_TOOLTIP_TYPE, // 交易限制提示类型
      BACK_DEFECT_TYPE_SHOW_NUM_MAP, // 票据瑕疵类型 id 映射 是否显示数字

      dialogVisible: false,
      payInfo: {}, // 支付渠道对象
      draftList: [], // 表格数据 票据数组
      draftListCopy: [], // 复制一份
      discernType: '', // 识别类型
      discernTypeOptions: [ // 识别类型下来选项
        {
          value: '',
          label: '全部',
        },
        {
          value: 1,
          label: '单张识别',
        },
        {
          value: 2,
          label: '批量识别',
        },
        {
          value: 3,
          label: '自动同步',
        },
      ],
      // 搜索的下拉选项
      searchConditionOptions: [
        {
          value: 'acceptorName',
          label: '承兑人',
        },
        {
          value: 'draftNo',
          label: '票号后六位',
        },
        {
          value: 'draftAmount',
          label: '金额（万）',
        },
      ],
      // 是否新票
      draftTypeOptions: [
        {
          value: null,
          label: '全部'
        },
        {
          value: 1,
          label: '新一代'
        },
        {
          value: 0,
          label: 'ECDS'
        }
      ],
      keyword: '', // 搜索关键字
      draftType: null, // 是否新票
      searchSelect: 'acceptorName', // 下拉选择
      inviteCode: '', // 定向码
      recognitionData: null, // 生成截图的数据
      // 年利率上限
      annualInterestLimit: {
        银票: 12,
        财票: 25,
        商票: 36,
      },
      selectDraftNoList: [],
      defaultConfig: {}, // 默认配置对象
      newData: true, // 新数据加载完
      isShowNoInfoTip: false, // 是否显示脚步出票人收款人缺失提示
      isDelDraft: true, // 点击发布后是否删除所选票据
      publishStartTime: null, // 发布开始时间
      isAgreeFastTrade: false, // 是否已确认光速交易
      isAgreeProtocol: true, // 是否已同意协议
      userInfo: null, // 用户信息
      // defaultMobiles: '', // 联系方式信息
      sellerBankAccountId: null, // 回款账户
      defaultContact: null, // 默认联系方式
    }
  },

  computed: {

    ...mapGetters('user', {
      limitLight: 'limitLight', // 是否限制极速
      sellerBankAccountList: 'sellerBankAccountList'// 已通过的回款账户列表
    }),
    ...mapGetters('common', {
      mobilePattern: 'mobilePattern', // 是否开启联系方式模式 1开启 0关闭
    }),
    // 选择总金额
    selectAmount() {
      let amount = 0
      this.nowSel.length && this.nowSel.forEach(item => {
        amount = new BigNumber(item.draftAmount).plus(amount)
      })
      return yuan2wan(amount)
    },
    // sellerBankAccount() {
    //   return this.$store.state.common.sellerBankAccountId
    // },
    ...mapGetters({
      // 节假日列表
      holidayList: 'common/holidays'
    }),
    // 查询条件
    searchSection() {
      return `${this.searchSelect}${this.keyword}${this.discernType}${this.draftType}`
    },
    // 当前选中的数据
    nowSel() {
      const list = []
      this.selectDraftNoList.forEach(num => {
        const item = this.draftList.find(draft => draft.draftDiscernId === num)
        item && list.push(item)
      })
      return list
    },
    // 三要素一致的票（承兑人、金额、到期日）
    sameList() {
      return this.getSameDraft(this.nowSel)
    },
    // 当前选中是否有光速订单
    nowSelHasFastTrade() {
      return this.nowSel.some(v => !!v.fastTrade)
    },

    // 当前选中是否有普通票
    nowSelHasOldDraft() {
      return this.nowSel.some(v => !v.draftType)
    },

    // 当前选中是否有新票
    nowSelHasNewDraft() {
      return this.nowSel.some(v => !!v.draftType)
    }
  },

  watch: {
    // sellerBankAccount(val) {
    //   if (!this.sellerBankAccountId) {
    //     this.sellerBankAccountId = val
    //   }
    // },
    // 监听查询
    searchSection() {
      let temp = JSON.parse(JSON.stringify(this.draftListCopy))
      if (this.discernType) {
        temp = temp.filter(item => item.discernType === this.discernType)
      }
      if (this.keyword) {
        if (['acceptorName', 'draftNo'].includes(this.searchSelect)) { // 承兑人、票号后六位
          temp = temp.filter(item => item[this.searchSelect].indexOf(this.keyword) > -1)
        } else if (this.searchSelect === 'draftAmount') { // 金额（万）
          temp = temp.filter(item => yuan2wan(item.draftAmount) === +this.keyword)
        }
      }

      if (this.draftType === 1) {
        temp = temp.filter(item => item.draftType === this.draftType)
      } else if (this.draftType === 0) {
        temp = temp.filter(item => (!item.draftType || (item.draftType === this.draftType)))
      }
      // 查询的时候 把页数重置成0
      this.$refs.tableRef.getData('search')
      this.draftList = temp
    },
    nowSelHasNewDraft() {
      this.$nextTick().then(() => {
        this.$refs.newDraftTipViewRef && this.$refs.newDraftTipViewRef.init()
      })
    }
  },

  methods: {
    open(obj) {
      this.isShowNoInfoTip = obj.isShowNoInfoTip || false
      if (obj.msg) {
        setTimeout(() => {
          this.$message({
            customClass: 'offsetTop',
            message: obj.msg,
            type: 'warning'
          })
        }, 0)
      }
      this.init(obj && obj.publishList)
      this.$store.dispatch('common/getCloseMarket')
      this.$store.dispatch('recognize-common/getFastLimit')
      this.$store.dispatch('recognize-user/getCorpInfo')
      this.$store.dispatch('recognize-user/getShowCreditRemind')
      this.userInfo = this.$store.dispatch('recognize-user/getUserInfo')

      // this.getDefaultContactSetting()

      this.dialogVisible = true
    },

    // 数据初始化处理
    async init(list) {
      // 有批量发布的票才弹提示
      const hasBatch = list.some(v => +v.discernType === IDENTIFY_TYPE.MULTIPLE.id)
      hasBatch && this.$message.warning('批量识别的票据不支持识别瑕疵，请手动选择瑕疵')
      // this.selectDraftNoList = []
      this.newData = false
      await this.$nextTick()
      this.newData = true
      this.defaultConfig = await this.getConfig()

      // 设置默认的回款账户
      this.sellerBankAccountId = this.$store.state.common.sellerBankAccountId || null

      // 在默认设置的列表中，过滤出未禁用渠道
      const res = this.$store.state.user.paymentAccountList
      const defaultPaymentChannelList = this.defaultConfig.defaultPaymentChannelList || []
      const defaultList = defaultPaymentChannelList.filter(i => {
        const item = res.find(v => v.paymentChannel === i) || {}
        return item.banStatus !== BAN_STATUS.DISABLE.id
      })
      this.defaultConfig.defaultPaymentChannelList = defaultList || []

      this.draftList = list
      const sameList = this.getSameDraft(this.draftList)
      // const mobileTemp = Storage.get(TEMP_CONTACT_LIST_DATA)
      this.draftList.length && this.draftList.forEach((item, index) => {
        item.ticketFlawobj = defectStrToObj(item.defects) // 瑕疵格式化
        !item.billingMethod && (item.billingMethod = OFFER_TYPE_CODE.MEI_SHI_WAN_ZHI_KOU) // 计费方式0-十万直扣1-年利率加手续费
        !item.lakhDeduction && (item.lakhDeduction = '') // 每十万扣息
        !item.annualInterest && (item.annualInterest = '') // 输入的利率
        !item.serviceCharge && (item.serviceCharge = '') // 每十万手续费
        item.receivedAmount = '' // 实际到账金额

        item.receiverMosaic = this.defaultConfig.receiverMosaic || false // 收款人打码
        item.drawerMosaic = this.defaultConfig.drawerMosaic || false // 出票人打码

        item.fastTrade = this.defaultConfig?.fastTrade === 1 // 光速订单
        item.margin = this.defaultConfig?.fastTrade === 1 || this.defaultConfig?.releaseMargin === 1 // 是否保证金
        item.bargaining = this.defaultConfig?.bargain === 1 // 是否接受议价
        item.bargainingLimit = this.getBargainLimitConfig(this.defaultConfig, item.acceptorType) // 议价上限
        // 连号票即将加上  暂不处理
        // eslint-disable-next-line no-nested-ternary
        item.serialTag = sameList.includes(item.draftDiscernId) && this.defaultConfig.serialTag ? 1 : 0 // 连号票标签
        item.blackRegionList = this.defaultConfig.areaBlackConfig ? JSON.parse(JSON.stringify(this.defaultConfig.areaBlackConfig)) : [] // 黑名单
        // 新票默认拆分设置
        if (item.draftType) {
          let { draftAmount, subTicketStart, subTicketEnd } = item
          if (draftAmount < this.getMinSplitConfig() || (subTicketStart === '0' && subTicketEnd === '0')) {
            item.splitFlag = 0
          } else {
            item.splitAmtMin = this.getMinSplitConfig()
            item.splitAmtMax = draftAmount
            item.splitFlag = this.defaultConfig?.splitFlag
          }
          item.splitMethod = 1// 拆分方式
          item.splitAmtIntSelect = 1 // 定额拆分金额选项
          item.splitAmtInt = '1'// 定额拆分金额
          item.intMultiple = 1 // 支持整数倍
        }

        // 票据到期日期
        if (item.maturityDate) {
          this.getAnnualInterest(index)
        }
        if (typeof (item.draftJson) === 'string') {
          item.draftJson = JSON.parse(item.draftJson) // 原始json，打码用
        }
        item.originJson = item.draftJson // 原始json，打码用
        // item.mobile = mobileTemp // 缓存的联系方式
      })
      // 设置默认的联系方式
      // this.setDefaultContactSetting()
      this.draftListCopy = JSON.parse(JSON.stringify(this.draftList))
      if (sameList.length && this.defaultConfig.serialTag) {
        this.$message.success('检测到连号票，已自动开启连号票标签，可在批量设置中修改')
      }
      this.$refs.tableRef.toggleAllSelection(true)
    },
    async getConfig() {
      try {
        const data = await userApi.getPostOrderConfig()
        const postConfig = { ...data, ...data.traderCorpConfig }
        // const maxBlackLength = 10
        if (postConfig?.areaBlackConfig?.length) {
          postConfig.areaBlackConfig = postConfig.areaBlackConfig.map(v => ({
            cityCode: v.cityCode,
            cityName: v.cityName,
            provinceCode: v.provinceCode,
            provinceName: v.provinceName,
          }))
        }
        postConfig.receiverMosaic = postConfig.buyerMosaic === 1
        postConfig.drawerMosaic = postConfig.sellerMosaic === 1
        return postConfig
      } catch (error) {
        const REAL_NAME_INVALID = 1014 // 企业JD实名认证失效
        const { code } = error.data
        if (code === REAL_NAME_INVALID) {
          // this.$confirm('<p class="title">该账号未认证或认证状态过期，请先前往账户中心完成企业认证</p>', '', {
          //   dangerouslyUseHTMLString: true,
          //   type: 'warning',
          //   showClose: false,
          //   customClass: 'no-header-msg',
          //   confirmButtonText: '确认',
          //   showCancelButton: false,
          //   width: 490
          // }).then(() => {
          this.handleCancel()
          // })
        }
        return Promise.reject(new Error(error))
      }
    },

    getMinSplitConfig() {
      return this.$store.state.common.newVersionDraftSplit || 10000
    },

    /**
     * 获取默认议价上限
     * @date 2022-01-04
     * @param {any} config 默认配置
     * @param {any} acceptorType 承兑人类型
     * @returns {any}
     */

    getBargainLimitConfig(config, acceptorType) {
      // 默认不议价，返回空
      if (config?.bargain !== 1) {
        return null
      }
      if (acceptorType === ACCEPTOR_TYPE.CAI_PIAO.id) {
        return config?.financialDraftBargainLimit || null
      } else if (acceptorType === ACCEPTOR_TYPE.SHANG_PIAO.id) {
        return config?.commercialDraftBargainLimit || null
      } else {
        return config?.bankDraftBargainLimit || null
      }
    },

    // 删除
    handleDel() {
      this.$confirm('确定要删除所选识别记录吗？', '删除提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'no-header-msg',
        type: 'warning'
      }).then(() => {
        // 删除票据数据
        this.handelDelDraftList()
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
      })
        .catch(() => {
          this.$message({
            type: 'warning',
            message: '已取消删除'
          })
        })
    },

    // 删除票据数据
    handelDelDraftList() {
      this.nowSel.forEach(sel => {
        this.draftList.splice(this.draftList.findIndex(v => v.draftDiscernId === sel.draftDiscernId), 1)
        this.draftListCopy.splice(this.draftListCopy.findIndex(v => v.draftDiscernId === sel.draftDiscernId), 1)
      })
      this.$refs.tableRef.clearSelection()
      this.selectDraftNoList = []
    },

    // 点击批量修改
    handleBatchEdit() {
      const { nowSel } = this
      if (!nowSel.length) {
        return this.$message.warning('请选择票据')
      }
      this.$refs.batchDialogRef.toggle(this.sameList)
    },

    // 点击批量修改弹窗确认按钮回调事件
    handleConfirmBatch(form, defectObj) {
      this.nowSel.map(item => {
        // 瑕疵只对批量识别的票据设置有效
        if (item.discernType === IDENTIFY_TYPE.MULTIPLE.id) {
          item = Object.assign(item, JSON.parse(JSON.stringify(defectObj)))
        }

        item = Object.assign(item, JSON.parse(JSON.stringify(form)))
        item.serialTag = this.sameList.includes(item.draftDiscernId) && form.serialTag ? 1 : 0
        const i = this.getListIndex(item.draftDiscernId)

        if (i > -1) {
          this.$set(this.draftList, i, item)
          this.$set(this.draftListCopy, i, item)
        }
        // 计算报价
        this.getAnnualInterest(i)
        return item
      })
    },

    // 根据票号返回数组的下标
    getListIndex(draftDiscernId) {
      let i = this.draftList.length
      while (i--) {
        if (this.draftList[i].draftDiscernId === draftDiscernId) {
          return i
        }
      }
      return -1
    },

    // 行数据改变事件
    handleInput(value, index, key) {
      const currentRow = this.draftList[index]
      currentRow[key] = value
      // 光速订单
      if (key === 'lightOrder' && value) {
        currentRow.margin = 1
      }
      // 保证金
      if (key === 'margin' && !value) {
        currentRow.lightOrder = 0
      }
      this.$set(this.draftList, index, currentRow) // Vue 不能检测数组改变index的变化，所以要这么写
      this.$set(this.draftListCopy, index, currentRow) // Vue 不能检测数组改变index的变化，所以要这么写
      this.getAnnualInterest(index)
    },

    // 改变报价类型
    handleChangeType(index, row) {
      const currentRow = this.draftList[index]
      currentRow.billingMethod = row.billingMethod === OFFER_TYPE_CODE.MEI_SHI_WAN_ZHI_KOU ? OFFER_TYPE_CODE.NIAN_LI_LV_JI_SUAN : OFFER_TYPE_CODE.MEI_SHI_WAN_ZHI_KOU
      // 切换计算
      if (currentRow.billingMethod === OFFER_TYPE_CODE.NIAN_LI_LV_JI_SUAN) {
        +currentRow.annualInterest === 0 && (currentRow.annualInterest = null)
        currentRow.serviceCharge = null
      } else {
        +currentRow.lakhDeduction === 0 && (currentRow.lakhDeduction = null)
        const { draftAmount, lakhDeduction, interestDays } = currentRow
        const { annualInterest: rtAnnualInterest } = lakhDeductionMath(draftAmount, lakhDeduction, interestDays)
        // eslint-disable-next-line no-magic-numbers
        currentRow.annualInterest = (+rtAnnualInterest).toFixed(4)
      }
      this.$set(this.draftList, index, currentRow) // Vue 不能检测数组改变index的变化，所以要这么写
      this.$set(this.draftListCopy, index, currentRow) // Vue 不能检测数组改变index的变化，所以要这么写
    },

    // 点击设置按钮打开设置弹窗
    handleEdit(data) {
      this.$refs.EditDialogRef.toggle(data)
    },

    // 设置弹窗确认回调
    handleConfirmEdit(data) {
      const i = this.getListIndex(data.draftDiscernId)
      if (i > -1) {
        this.$set(this.draftList, i, data)
        this.$set(this.draftListCopy, i, data)
      }
    },

    // 多选回调
    handleSelectionChange(list) {
      this.selectDraftNoList = list.map(v => v.draftDiscernId)
    },

    // 点击取消
    handleCancel() {
      this.selectDraftNoList = []
      this.discernType = ''
      this.keyword = ''
      this.draftType = null
      this.searchSelect = 'acceptorName'
      this.$refs.newDraftTipViewRef && this.$refs.newDraftTipViewRef.hideTip()
      this.dialogVisible = false
    },

    // 获取年利率
    getAnnualInterest(index) {
      // 用每十万直扣计算
      const { draftAmount, lakhDeduction, interestDays, billingMethod, annualInterest, serviceCharge } = this.draftList[index]
      if (billingMethod === OFFER_TYPE_CODE.MEI_SHI_WAN_ZHI_KOU) {
        const { annualInterest: rtAnnualInterest } = lakhDeductionMath(draftAmount, lakhDeduction, interestDays)
        // eslint-disable-next-line no-magic-numbers
        this.draftList[index].annualInterest = (+rtAnnualInterest).toFixed(4)
      } else {
        const { lakhDeduction: rtLakhDeduction } = interestRateMath(draftAmount, annualInterest, serviceCharge, interestDays)
        this.draftList[index].lakhDeduction = (+rtLakhDeduction).toFixed(2)
      }
    },

    // 支付渠道选择回调
    handlePayChange(obj) {
      this.payInfo = obj
    },

    // 定向发布弹窗显示
    handleDirectionShow() {
      const { nowSel } = this
      if (!this.validInput(nowSel)) {
        return
      }
      if (!this.validFastTrade(nowSel, 'direction')) return
      if (!this.validProtocol()) return
      // 是否有未开启保证金
      const noMargin = nowSel.some(v => !v.margin)
      const hasBargaing = this.draftList.some(v => v.bargaining)
      if (hasBargaing) {
        this.$confirm('<div>若选择定向发布，将自动为您关闭议价选项</div>', '提示', {
          dangerouslyUseHTMLString: true,
          type: 'warning',
          showClose: false,
          iconPosition: 'title',
          cancelButtonText: '再想想',
          confirmButtonText: '确认',
        }).then(() => {
          this.draftList.forEach(v => {
            v.bargaining = 0
            v.bargainingLimit = ''
          })
          this.$refs.directionDialogRef.toggle(!noMargin)
        })
      } else {
        this.$refs.directionDialogRef.toggle(!noMargin)
      }
    },

    // 定向发布弹窗确认
    handleConfirmDirection(inviteCode) {
      this.confirmIssue(inviteCode)
    },

    // 校验利率
    validRate(selList) {
      let list = []
      selList.forEach(obj => {
        const { draftNo, acceptorType, annualInterest, billingMethod, lakhDeduction, interestDays, bargainingLimit, serviceCharge } = obj
        let newAnnualInterest = billingMethod === OFFER_TYPE_CODE.MEI_SHI_WAN_ZHI_KOU
          ? lakhDeductionMathWithBargain(lakhDeduction, interestDays, bargainingLimit || 0)
          : interestRateMathWithBargain(annualInterest, serviceCharge, interestDays || 0, bargainingLimit || 0)
        if (
          (acceptorType === ACCEPTOR_TYPE.CAI_PIAO.id && newAnnualInterest > this.annualInterestLimit['财票'])
        || (acceptorType === ACCEPTOR_TYPE.SHANG_PIAO.id && newAnnualInterest > this.annualInterestLimit['商票'])
        || (acceptorType !== ACCEPTOR_TYPE.CAI_PIAO.id && acceptorType !== ACCEPTOR_TYPE.SHANG_PIAO.id && newAnnualInterest > this.annualInterestLimit['银票'])) {
          // eslint-disable-next-line no-magic-numbers
          list.push(`<p>票号：${draftNo.substr(-6, 6)}</p>`)
        }
      })
      if (list.length) {
        this.$confirm(`<p>以下票据，年化利率超过发布限制 (银票 12%/财票 25%/商票 36%)，请重新输入：</p>${list.join('')}`, '提示', {
          dangerouslyUseHTMLString: true,
          type: 'warning',
          showClose: false,
          iconPosition: 'title',
          confirmButtonText: '知道了',
          showCancelButton: false,
          width: '600px',
          customClass: 'custom-confirm'
        })
        return false
      } else {
        return true
      }
    },
    // 校验输入
    validInput(nowSel) {
      this.$refs.tableRef.isValid = true
      if (!nowSel.length) {
        this.$message.warning('请选择票据')
        return false
      }

      if (!nowSel.every(item => {
        if (item.billingMethod === 0 && (!item.lakhDeduction || +item.lakhDeduction === 0)) {
          return false
        }
        if (item.billingMethod === 1 && (!item.annualInterest || (+item.annualInterest === 0 && +item.serviceCharge === 0))) {
          return false
        }
        return true
      })) {
        this.$message.info('有未填写报价信息的票据')
        return false
      }

      if (!nowSel.every(item => {
        const limit = 5000000
        if (item.draftAmount > limit && this.payInfo.acceptYlpay === 1 && (!item.tradeContractUrl || !item.invoiceUrl)) {
          return false
        }
        return true
      })) {
        this.$message.info('选择智联通支付渠道发布500万以上票据必须上传贸易合同和发票')
        return false
      }

      return true
    },

    // // 校验联系方式
    // validMobile(nowSel) {
    //   if (!nowSel.every(item => {
    //     if (!item.mobile.length) {
    //       return false
    //     }
    //     return true
    //   })) {
    //     this.$message.info('请先设置联系方式')
    //     return false
    //   }
    //   return true
    // },

    // 确认发布
    async confirmIssue(inviteCode) {
      this.isDelDraft = true
      if (!this.payInfo.payChannelName) {
        this.$message.info('请选择支付渠道')
        return
      }
      if ((this.payInfo.acceptJdYlpay === 1 || this.payInfo.acceptZbankPlus === 1) && !this.sellerBankAccountId) {
        this.$message.info('请选择回款账户')
        return
      }
      const { nowSel } = this

      // 拦截“7”和“8”开头的新一代票据发布
      if (nowSel.some(draft => /^[78]/.test(draft.draftNo))) {
        this.$message.error('暂不支持发布，银行暂不支持7、8开头的供应链票据流转')
        return
      }

      if (!this.validInput(nowSel)) {
        return
      }

      if (!this.validRate(nowSel)) {
        return
      }

      if (!this.validFastTrade(nowSel)) {
        return
      }

      if (!this.validProtocol()) {
        return
      }

      // if (!this.validMobile(nowSel)) {
      //   return
      // }

      if (inviteCode) {
        this.issueDraft(nowSel, inviteCode)
      } else {
        let acceptorList = nowSel.map(item => ({
          acceptorName: item.acceptorName,
          annualInterest: item.annualInterest,
          interestDays: item.interestDays
        }))
        let rateRes = await this.checkIntersetRate({ acceptorOrders: acceptorList, type: 2 }, { list: nowSel, inviteCode })
        if (!rateRes.showPopUpFlag) {
          this.issueDraft(nowSel, inviteCode)
        }
      }
    },
    async issueDraft(nowSel, inviteCode) {
      try {
        this.handleResultStatusDialogShow()
        const reqList = []
        for (let i = 0; i < nowSel.length; i++) {
          let item = JSON.parse(JSON.stringify(nowSel[i]))
          // 联系方式格式处理
          // item.mobile = item.mobile.map(e => ({ mobile: e.mobile, mobileName: e.mobileName, id: e.id }))
          // if (i === 0 && item.mobile) {
          // // 缓存联系方式
          //   Storage.set(TEMP_CONTACT_LIST_DATA, item.mobile)
          // }
          // 添加瑕疵，只添加批量类型的
          if (item.discernType === IDENTIFY_TYPE.MULTIPLE.id) {
            item = { ...item, ...item.defectObj }
          }
          // 背书手数，单张识别为空时传0
          let endorseCount
          if (item.discernType === IDENTIFY_TYPE.SIGNAL.id) {
            endorseCount = item.endorseCount || 0
          } else {
            endorseCount = item.endorseCount || null
          }
          const obj = {
            draftDiscernId: item.draftDiscernId, // 签手id，非空字段
            serialTag: item.serialTag || 0, // 连号标签
            backImageUrl: '', // 签手的背面是单张
            frontImageUrl: '', // 票据正面，非空字段
            draftNo: item.draftNo || '', // 票号，非空字段
            draftAmount: item.draftAmount, // 票面金额，非空字段
            maturityDate: item.maturityDate || '', // 到期日2022-01-01，非空字段
            issueDate: item.issueDate || '', // 出票日2022-01-01，非空字段
            acceptorName: item.acceptorName, // 承兑人，非空字段
            endorseCount, // 背书手数
            billingMethod: item.billingMethod, // 计费方式0-十万直扣1-年利率加手续费
            lakhDeduction: item.lakhDeduction, // 每十万直扣
            annualInterest: item.annualInterest, // 年利率
            serviceCharge: item.serviceCharge, // 手续费
            margin: item.margin ? 1 : 0, // 是否需要保证金，0-否、1-是
            inviteCode, // 定向标识(手机号或者定向码）
            fastTrade: item.fastTrade ? 1 : 0, // 是否光速票，0-否、1-是
            bargaining: item.bargaining ? 1 : 0, // 是否接受议价，0-否、1-是
            bargainingLimit: item.bargainingLimit, // 议价上限值
            ...item.ticketFlawobj, // 瑕疵
            ...this.payInfo,
            blackRegionList: item.blackRegionList || [], // 黑名单
            sellerMosaic: item.drawerMosaic ? 1 : 0, // 出票人打码
            buyerMosaic: item.receiverMosaic ? 1 : 0, // 收款人打码
            acceptorNotes: item.acceptorNotes, // 承兑人标签
            // mobile: item.mobile,
            tradeContractUrl: item.tradeContractUrl, // 贸易合同
            invoiceUrl: item.invoiceUrl, // 发票
            draftType: item.draftType, // 拆分开关
            splitFlag: item.splitFlag, // 拆分开关
            sellerBankAccountId: this.sellerBankAccountId // 回款账户
          }
          if (item.draftType && item.splitFlag) {
            // eslint-disable-next-line max-depth
            if (item.splitMethod) {
              // 区间拆分
              // eslint-disable-next-line max-depth
              if (item.splitAmtInt) {
                obj.splitAmtMin = wan2yuan(yuan2fen(item.splitAmtInt))
              }
              // eslint-disable-next-line max-depth
              if (item.intMultiple) {
                obj.integerMultiples = 1
              } else {
                obj.integerMultiples = 2
              }
            } else {
              obj.integerMultiples = 0
              // eslint-disable-next-line max-depth
              if (item.splitAmtMin) {
                obj.splitAmtMin = yuan2fen(item.splitAmtMin)
              }
              // eslint-disable-next-line max-depth
              if (item.splitAmtMax) {
                obj.splitAmtMax = yuan2fen(item.splitAmtMax)
              }
            }
          }
          reqList.push(obj)
        }
        const publishStartTime = Date.now()
        const res = await ticketApi.postBatchPostOrder({ list: reqList })
        const publishEndTime = Date.now()
        const publishTime = (publishEndTime - publishStartTime) / 1000
        this.handleResultStatusDialogShow(res)
        this.mixPanelMultiPublishDraft(reqList, res, inviteCode, publishTime)
      } catch (error) {
        this.publishFailTip(error)
      } finally {
        this.isDelDraft && this.handelDelDraftList()
        // 更新记忆回款账户选中
        this.$store.dispatch('common/getNewVersionDraftConfig')
      }
    },

    mixPanelMultiPublishDraft(reqList, res, inviteCode, publishTime) {
      const failStr = res.failList ? res.failList.reduce((prev, cur) => `票号：${cur.draftNo} 失败原因：${cur.failReason};${prev}`, '') || '' : ''
      mixpanel.multiPublishDraft({
        支付渠道: this.payInfo.payChannelName,
        发布失败原因: failStr,
        票据数量: reqList.length,
        票面总金额: wan2yuan(this.selectAmount),
        批量发布成功票据数量: res.successCount,
        批量发布失败票据数量: res.failCount,
        是否定向: inviteCode ? '是' : '否',
        批量发布时间: publishTime,
        企业名称: this.userInfo.corpName,
      })
    },

    // 发布失败提示
    publishFailTip(error) {
      const { code, msg } = error.data
      const PAY_CHANNEL_BUYER_NOT_SUPPORT = 1016 // 定向交易对手不支持所选支付方式
      const PAY_CHANNEL_FAIL = 1018 // 支付渠道失效
      const CREDIT_TYPE_GENERAL = 3026 // 当前用户信用等级一般
      const GAO_MAI_DI_MAI = 5036 // 高买低卖

      this.$refs.resultStatusDialogRef.close()
      this.isDelDraft = false
      if (code === GAO_MAI_DI_MAI) {
        this.$alert('您因触发平台机制，被限制登录，请联系您的客户经理', '提示', {
          confirmButtonText: '我知道了',
        }).then(() => this.$store.dispatch('user/logout', { manual: true }))
          .then(() => {
            this.dialogVisible = false
            this.$message.closeAll()
            if (this.$ipc) {
              this.$ipc.send('PUSH_ROUTE_IN_MAIN_WINDOW', '/login')
              this.$ipc.send('CLOSE')
            } else {
              this.$router.push('/')
            }
            windowCommunication.trigger()
          })
      } else if (code === PAY_CHANNEL_FAIL) {
        this.$refs.payAccountErrorRef.init(msg)
      } else if (code === CREDIT_TYPE_GENERAL) {
        this.$confirm('<div>您当前的信用等级为 <span class="red-soso">一般</span>。只可发布带保证金订单。</div><div>前往 <span class="red-soso">账户信息-我的信用</span> 可以查看信用分规则。</div>', '提示', {
          dangerouslyUseHTMLString: true,
          type: 'warning',
          showClose: false,
          iconPosition: 'title',
          cancelButtonText: '取消',
          confirmButtonText: '开启保证金',
        }).then(() => {
          this.openMargin()
        })
      } else if (code === PAY_CHANNEL_BUYER_NOT_SUPPORT) {
        this.$message.error('交易对手暂未开通该支付渠道，无法发起订单交易。')
      } else {
        this.$message.error(msg)
      }
    },

    // 切换收款人和出票人打码时，生成对应图片
    async createFrontImage(item) {
      const mosaicObj = {
        accountNumber: '******',
        bankOfDeposit: '******',
        fullName: '******'
      }
      const newJson = JSON.parse(JSON.stringify(item.originJson))
      item.receiverMosaic && (newJson.front.payee = mosaicObj)
      item.drawerMosaic && (newJson.front.ticketIssuer = mosaicObj)
      this.recognitionData = newJson

      let frontImage
      let backImage
      const frontPromise = imgGenerator.screenshot(this.recognitionData, 'front', 'canvas').then(front => {
        frontImage = front.toDataURL()
        item.frontImgUrl = frontImage
      })
      let backPromise = imgGenerator.screenshot(this.recognitionData, 'back', 'canvas').then(back => {
        // 有背面信息才生成图片
        if (newJson.back.history.length > 0) {
          backImage = back.toDataURL()
          item.backImgUrl = backImage
        }
      })
      await Promise.all([frontPromise, backPromise])
      return item
    },

    // 发布结果状态弹窗
    handleResultStatusDialogShow(data) {
      this.$refs.resultStatusDialogRef.init(data)
    },

    // 发布结果状态弹窗确认回调
    handleConfirmResultStatus() {
      // 没有票才关闭
      if (!this.draftList.length) {
        this.handleCancel()
      }
    },

    // 获取三要素一致的票
    getSameDraft(list) {
      let sameList = []
      for (let x = 0; x < list.length; x++) {
        for (let y = x + 1; y < list.length; y++) {
          const { acceptorName, draftAmount, maturityDate, draftDiscernId } = list[x]
          const { acceptorName: nextName, draftAmount: nextAmount, maturityDate: nextDate, draftDiscernId: nextDraftDiscernId } = list[y]
          // 放开新票可设置连号票
          // if (draftType || nextDraftType) {
          // // 有新票时直接返回空,新票暂不能设置连号票
          // return []
          // }
          if (acceptorName === nextName && draftAmount === nextAmount && maturityDate === nextDate) {
            // eslint-disable-next-line max-depth
            sameList.indexOf(draftDiscernId) === -1 && sameList.push(draftDiscernId)
            sameList.indexOf(nextDraftDiscernId) === -1 && sameList.push(nextDraftDiscernId)
            break
          }
        }
      }
      return sameList
    },

    // 打开保证金
    openMargin() {
      this.selectDraftNoList.forEach(num => {
        const index = this.draftList.findIndex(draft => draft.draftDiscernId === num)
        if (index > -1) {
          const item = this.draftList[index]
          item.margin = true
          this.$set(this.draftList, index, item)
        }
      })
    },

    // 校验打开光速交易订单开启确认弹窗 {limitLight 开关打开才判断}
    validFastTrade(obj, from) {
      // 是否有开启光速订单的
      const hasFastTrade = obj.some(v => v.fastTrade)
      if (!this.limitLight && hasFastTrade && !this.isAgreeFastTrade) {
        this.$refs.fastTradeDialogRef.init(from)
        return false
      }
      return true
    },

    // 是否已确认光速交易
    agreeFastTrade() {
      this.isAgreeFastTrade = !this.isAgreeFastTrade
    },

    // 打开服务协议
    openProtocol(url) {
      window.open(url)
    },

    // 检查是否勾选服务协议
    validProtocol() {
      if (!this.isAgreeProtocol) {
        this.$message.info('请勾选服务协议')
        return false
      }
      return true
    },

    // 显示报价弹窗
    handleShowQuotation(row) {
      const {
        draftNo,
        acceptorName,
        acceptorType: accepterType,
        draftAmount,
        maturityDate,
        interestDays: interestAccrualDay,
        endorseCount
      } = row
      this.$refs.quotationReferenceDialogRef.init({
        draftNo, // 票号
        acceptorName, // 承兑人
        accepterType, // 承兑人类型
        draftAmount: yuan2wan(draftAmount), // 票面金额
        maturityDate, // 到期日
        interestAccrualDay, // 剩余天数
        endorseCount // 背书手数
      })
    },

    // 获取默认联系方式
    // async getDefaultContactSetting() {
    //   const data = await userApi.getMobileList()
    //   if (Array.isArray(data)) {
    //     this.defaultMobiles = data.map(item => item.mobile).join()
    //   }
    // },

    // 设置默认的联系方式
    // async setDefaultContactSetting() {
    //   this.defaultContact = await this.$store.dispatch('user/getContactList')
    //   this.draftList.forEach(item => {
    //     // 设置默认的联系方式
    //     item.mobile = (this.defaultContact && this.defaultContact.filter(e => e.defaultFlag === 1)) || []
    //     // 未设置默认下 取最新的签3条作为默认联系方式
    //     if (!item.mobile.length) {
    //       item.mobile = this.defaultContact && this.defaultContact.length > 3 ? this.defaultContact.slice(0, 3) : this.defaultContact
    //     }
    //   })
    // }

  }
}
</script>
