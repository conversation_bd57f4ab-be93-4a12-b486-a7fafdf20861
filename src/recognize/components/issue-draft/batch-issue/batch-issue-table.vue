<!-- 批量发布表格 -->
<style lang="scss" scoped>
.el-table--border {
  border-color: $color-F0F0F0;

  &::after {
    background-color: $color-F0F0F0;
  }
}

.el-table::before {
  background-color: $color-F0F0F0;
}

.el-table ::v-deep {
  td.el-table__cell,
  th.el-table__cell.is-leaf {
    border-bottom: 1px solid $color-F0F0F0;
  }

  th.el-table__cell > .cell {
    text-overflow: initial;
    white-space: nowrap;
  }

  .cell {
    padding-right: 4px !important;
    padding-left: 14px !important;
  }

  .el-table__cell {
    color: $color-text-primary;
  }

  tbody .el-table__cell {
    font-size: 16px;
  }

  .quick-order-col {
    padding-right: 2px !important;
    padding-left: 6px !important;
    white-space: nowrap;
  }

  .acceptor {
    .bold {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .tags {
      display: flex;
    }

    .el-tag {
      overflow: hidden;
      margin-right: 4px;
      text-overflow: ellipsis;
      flex-shrink: 1;

      &:last-child {
        margin-right: 0;
      }
    }

    .cell {
      padding-left: 4px !important;
    }
  }
}

.tag-out {
  display: flex;
  justify-content: space-around;
  height: 100%;
  flex-direction: column;
}

.tag {
  margin: 2px 0;
  border: 1px solid $color-13C2C2;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
  color: $color-13C2C2;
  line-height: 18px;

  &.batch {
    border-color: $color-9254DE;
    color: $color-9254DE;
  }

  &.auto {
    border-color: $color-AF772D;
    color: $color-AF772D;
  }

  &.serial {
    border-color: $color-1890FF;
    color: $color-1890FF;
  }
}

.issue-amount {
  font-size: 20px;
  font-weight: 600;
  line-height: 34px;
}

.bold {
  font-weight: 600;
}

.days-remaining {
  .black-color {
    color: $color-text-primary;
  }
}

.has-flaw {
  font-weight: 600;
  color: $--color-primary;
}

.offer-flex {
  display: flex;

  .offer-input-row {
    min-width: 219px;
  }

  ::v-deep {
    .el-input__inner {
      padding: 0 8px;
      height: 32px;
      font-size: 14px;
      text-align: right;
    }
  }

  .offer-input1 {
    width: 98.5px;
  }

  .offer-input2 {
    width: 219px;
  }

  .add-icon {
    margin: 0 5px;
  }

  ::v-deep .el-input-group__append {
    padding: 0;
    min-width: 30px;
    font-size: 14px;
    text-align: center;
    color: $color-text-primary;
    background: $color-FAFAFA;
  }

  .change-icon {
    margin-top: 6px;
    margin-left: 9px;
    font-size: 20px;
    color: $--color-primary;
    cursor: pointer;
    flex: none;

    &.is-disabled {
      color: $color-text-light;
    }
  }

  .tip {
    margin-top: 5px;
    font-size: 12px;

    .red {
      @include bold;
    }
  }
}

.amount-received {
  font-size: 24px;
  font-weight: 600;
  line-height: 34px;
}

.pay-type {
  display: inline-block;
  margin: 0 4px 4px 0;
  border-radius: 14px;
  padding: 2px 6px;
  font-size: 12px;
  font-weight: 600;
  color: $color-008489;
  background: $color-E6F3F3;
  line-height: 17px;
}

.edit-btn {
  font-size: 18px;
}

.two-wrap-hidden {
  /* stylelint-disable-next-line value-no-vendor-prefix */
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  /* stylelint-disable-next-line property-no-vendor-prefix */
  -webkit-box-orient: vertical;
}

.el-table::v-deep .el-table__cell .cell {
  height: 72px !important;
}

.acceptor {
  flex: 1;

  .acceptor-name {
    width: 100%;

    @include ellipsis(1);

    height: auto;
  }

  .acceptor-remarks-box {
    display: inline-block;
    max-width: 100%;
    line-height: 0;
  }

  .acceptor-remarks {
    margin-top: 2px;
    border: 1px solid $color-warning;
    border-radius: 2px;
    padding: 0 6px;
    max-width: 100%;
    font-size: 12px;
    font-weight: bold;
    color: $color-warning;
    background: $color-FFEAE1;
    line-height: 18px;

    @include ellipsis;
  }

  .acceptor-remarks-tooltip {
    width: 195px;
    line-height: 24px;
  }
}

.acceptor-column {
  .has-tags-column-flex {
    position: relative;

    .tags {
      position: absolute;
    }
  }

  &-content {
    width: 100%;
  }
}

.draft-no-row {
  line-height: 16px;
}

.g-xinpiao {
  margin-left: 0;
}

.not-split-label {
  margin-left: 0;
}

.flex-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mt-price {
  overflow: hidden;
  margin-top: 5px;
  width: 110px;
  font-size: 12px;
  font-weight: 500;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.mt-price-content {
  padding: 12px;

  .mt-price-head {
    .title {
      font-size: 14px;
      font-weight: 500;
      color: #FFFFFF;
    }

    .sub-title {
      font-size: 12px;
      color: #999999;
    }
  }

  .split-line {
    margin: 8px 0;
    width: 100%;
    height: 1px;
    background-color: #4D4D4D;
  }

  .mt-price-body {
    .item {
      display: flex;
      align-items: center;
      padding-bottom: 4px;

      &:last-child {
        padding-bottom: 0;
      }

      .label {
        display: inline-block;
        width: 100px;
        font-size: 12px;
        color: #999999;
      }

      .value {
        font-size: 12px;
        font-weight: 500;
        color: #FFFFFF;
      }

      .red {
        color: #FF0000;
      }
    }
  }
}
</style>

<template>
  <div>
    <el-table
      ref="multipleTable"
      :data="pageData"
      row-key="draftDiscernId"
      @selection-change="handleSelectionChange"
    >
      <template slot="empty">
        <icon type="chengjie-empty" :size="186" />
        <div class="empty-text">暂无数据</div>
      </template>
      <el-table-column
        type="selection"
        width="32"
        align="right"
        reserve-selection
      />
      <el-table-column
        prop="draftNo"
        label="票号后六位"
        width="88"
        align="center"
      >
        <template slot-scope="scope">
          <div>
            <el-tooltip
              :disabled="!scope.row.draftType"
              :content="`子票区间：${scope.row.subTicketStart} - ${scope.row.subTicketEnd}` "
              placement="bottom"
            >
              <div class="draft-no-row">
                {{ scope.row.draftNo.substr(-6, 6) }}
              </div>
            </el-tooltip>
            <div>
              <CanNotSplitLabel class="not-split-label" :draft-info="scope.row" />
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label=""
        align="right"
        width="38"
      >
        <template slot-scope="scope">
          <div class="tag-out">
            <span v-if="scope.row.discernType === IDENTIFY_TYPE.SIGNAL.id" class="tag">单</span>
            <span v-if="scope.row.discernType === IDENTIFY_TYPE.MULTIPLE.id" class="tag batch">批</span>
            <span v-if="scope.row.discernType === IDENTIFY_TYPE.AUTO.id" class="tag auto">同</span>
            <span v-if="scope.row.serialTag" class="tag serial">连</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="承兑人"
        align="left"
      >
        <div slot-scope="scope" class="acceptor-column-content">
          <div class="acceptor">
            <el-tooltip
              placement="top"
              show-when-overflow
            >
              <div slot="content" class="acceptor-name-content">
                {{ scope.row.acceptorName }}
              </div>
              <div class="acceptor-name" style=" height: auto;">{{ scope.row.acceptorName }}</div>
            </el-tooltip>
            <!-- 承兑人标签 -->
            <el-tooltip
              v-if="scope.row.acceptorNotes && scope.row.acceptorNotes.length && scope.row.acceptorNotes[0]"
              placement="top"
            >
              <div slot="content" class="acceptor-remarks-tooltip">
                (标签为持票方添加，仅供参考)
                <div v-for="item in scope.row.acceptorNotes" :key="item">
                  {{ item }}
                </div>
              </div>
              <div class="acceptor-remarks-box"><span class="acceptor-remarks">{{ scope.row.acceptorNotes.join('/') }}</span></div>
            </el-tooltip>
          </div>
        </div>
      </el-table-column>
      <el-table-column
        label="票面金额(万)"
        align="right"
        width="132"
      >
        <span slot-scope="scope" class="issue-amount">{{ yuan2wan(scope.row.draftAmount) }}</span>
      </el-table-column>

      <el-table-column
        label="到期日"
        align="right"
        width="126"
      >
        <div slot-scope="scope">
          {{ formatTime(scope.row.maturityDate, 'YYYY-MM-DD') }}
          <div class="days-remaining">剩 <span class="black-color">{{ scope.row.interestDays }}</span> 天</div>
        </div>
      </el-table-column>
      <el-table-column
        label="背书手数"
        align="right"
        width="76"
      >
        <template slot-scope="scope">
          <span>
            {{ scope.row.endorseCount || scope.row.endorseCount === 0 ? scope.row.endorseCount : '-' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="瑕疵"
        align="left"
        width="92"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.defects" class="has-flaw two-wrap-hidden">
            <el-tooltip :disabled="toDefectStr(scope.row.defects).length <= 10" :content="toDefectStr(scope.row.defects)">
              <div>{{ toDefectStr(scope.row.defects) }}</div>
            </el-tooltip>
          </span>
          <template v-else>{{ scope.row.discernType === IDENTIFY_TYPE.MULTIPLE.id && !scope.row.defectSet ? '-' : '无瑕疵' }}</template>
        </template>
      </el-table-column>
      <el-table-column
        label="报价(元)"
        align="left"
        :width="!limitLight ? 258 : 288"
      >
        <div slot-scope="scope" class="offer-flex">
          <div class="g-no-wrap offer-input-row">
            <template v-if="scope.row.billingMethod === OFFER_TYPE_CODE.NIAN_LI_LV_JI_SUAN">
              <el-input
                v-model="scope.row.annualInterest"
                placeholder="年利率"
                class="offer-input1"
                :class="selectList && selectList.includes(scope.row.draftDiscernId) && (!scope.row.annualInterest || +scope.row.annualInterest === 0) && isValid && 'is-require-input'"
                type="number"
                :number-format="{maxDecimalLength: 4, leadingZero: false, negative: false, maxLength: 7}"
                @input="value => handleInput(value, scope.$index, 'annualInterest', scope.row)"
              >
                <span slot="append">%</span>
              </el-input>
              <span class="add-icon">+</span>
              <el-input
                v-model="scope.row.serviceCharge"
                placeholder="手续费"
                type="number"
                class="offer-input1"
                :number-format="{maxDecimalLength: 4, leadingZero: false, negative: false, maxLength: 8}"
                @input="value => handleInput(value, scope.$index, 'serviceCharge', scope.row)"
              >
                <span slot="append">元</span>
              </el-input>
            </template>
            <el-input
              v-else
              v-model="scope.row.lakhDeduction"
              placeholder="每十万扣款"
              :class="selectList && selectList.includes(scope.row.draftDiscernId) && (!scope.row.lakhDeduction || +scope.row.lakhDeduction === 0) && isValid && 'is-require-input'"
              type="number"
              class="offer-input2"
              :number-format="{maxDecimalLength: 2, leadingZero: false, negative: false, maxLength: 8}"
              @input="value => handleInput(value, scope.$index, 'lakhDeduction', scope.row)"
            >
              <span slot="append">元</span>
            </el-input>
            <div class="flex-row">
              <div v-if="scope.row.billingMethod === OFFER_TYPE_CODE.NIAN_LI_LV_JI_SUAN && scope.row.lakhDeduction" class="tip">
                每十万扣款：<span class="red">{{ scope.row.lakhDeduction }}</span>元
              </div>
              <div v-if="scope.row.billingMethod === OFFER_TYPE_CODE.MEI_SHI_WAN_ZHI_KOU && scope.row.annualInterest" class="tip">
                年利率：<span class="red">{{ scope.row.annualInterest }}</span>%
              </div>
              <el-tooltip
                v-if="![DRAFT_TYPE.SHANG_PIAO.id, DRAFT_TYPE.CAI_PIAO.id, DRAFT_TYPE.OTHER.id].includes(scope.row.acceptorType) && scope.row.showDiscountQuotationFlag"
                class="item"
                placement="top"
              >
                <div slot="content">
                  <div class="mt-price-content">
                    <div class="mt-price-head">
                      <div class="title">秒贴报价</div>
                      <div class="sub-title">如有意向，请联系客户经理贴现</div>
                    </div>
                    <div class="split-line" />
                    <div class="mt-price-body">
                      <div class="item">
                        <span class="label">秒贴行</span>
                        <span class="value">{{ scope.row.discountBank || '-' }}</span>
                      </div>
                      <div class="item">
                        <span class="label">每十万扣款</span>
                        <span class="value">{{ scope.row.discountLakhFee || scope.row.discountLakhFee === 0 ? scope.row.discountLakhFee + '元' : '-' }}</span>
                      </div>
                      <div class="item">
                        <span class="label">年化利率</span>
                        <span class="value">{{ scope.row.discountAnnualInterest || scope.row.discountAnnualInterest === 0 ? scope.row.discountAnnualInterest + '%' : '-' }}</span>
                      </div>
                      <div class="item">
                        <span class="label">预计到账金额</span>
                        <span class="value red">{{ scope.row.discountReallyAmt || scope.row.discountReallyAmt === 0 ? scope.row.discountReallyAmt + '万元' : '-' }}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="mt-price"> 秒贴报价：<span>{{ scope.row.discountLakhFee || scope.row.discountLakhFee === 0 ? scope.row.discountLakhFee + '元' : '-' }}</span></div>
              </el-tooltip>
            </div>
          </div>
          <div>
            <el-tooltip
              class="item"
              :content="scope.row.billingMethod === OFFER_TYPE_CODE.MEI_SHI_WAN_ZHI_KOU ? '切换为以年利率计算' : '切换为每十万扣款'"
              placement="top"
            >
              <icon type="chengjie-swap" class="change-icon" @click="handleChangeOfferType(scope.$index, scope.row)" />
            </el-tooltip>
            <el-tooltip
              class="item"
              :content="getQuotationBtnDisabled(scope.row) ? '票据到期日 ≤30 天或信息不完整，无法提供报价行情参考' : '点击查看报价行情参考' "
              placement="bottom"
            >
              <icon
                :class="getQuotationBtnDisabled(scope.row) && 'is-disabled'"
                type="chengjie-dynamic"
                class="change-icon"
                @click="!getQuotationBtnDisabled(scope.row) && handleShowQuotation(scope.row)"
              />
            </el-tooltip>
          </div>
        </div>
      </el-table-column>
      <el-table-column
        v-if="!limitLight"
        prop="fastTrade"
        label="极速出票"
        width="72"
        label-class-name="quick-order-col"
        align="center"
      >
        <template slot-scope="scope">
          <transaction-tooltip-button :types="[TRANSACTION_TOOLTIP_TYPE.FAST]" @change="(val) => val === TRANSACTION_TOOLTIP_TYPE.FAST && (scope.row.fastTrade = false)">
            <template v-slot:content="{disabled}">
              <el-switch
                v-model="scope.row.fastTrade"
                :disabled="disabled"
                @change="value => handleInput(value, scope.$index, 'fastTrade', scope.row)"
              />
            </template>
          </transaction-tooltip-button>
        </template>
      </el-table-column>
      <el-table-column
        prop="margin"
        label="保证金"
        width="72"
        align="center"
      >
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.margin"
            @change="value => handleInput(value, scope.$index, 'margin', scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="96"
      >
        <template slot-scope="scope">
          <el-button
            type="primary"
            border
            class="edit-btn"
            width="68px"
            height="42px"
            @click="handleEdit(scope.row)"
          >
            设置
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="tableData.length" class="footer-pagination">
      <el-pagination
        :total="total"
        :current-page.sync="currentPage"
        :page-size="pageSize"
        background
        layout="total,prev, pager, next, jumper"
        hide-on-single-page
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import {
  OFFER_TYPE_CODE, // 报价类型
  IDENTIFY_TYPE, // 识别类型
  TRANSACTION_TOOLTIP_TYPE, // 交易限制提示类型
} from '@recognize/constant'
import { yuan2wan, keep2Decimals } from '@/common/js/number' // 金额单位转换
import { formatTime, getDateSpace } from '@/common/js/date' // 时间格式化
import { toDefectStr } from '@/common/js/draft-flaw' // 将原始瑕疵字符串转为渲染字符串
import { mapGetters } from 'vuex'
import CanNotSplitLabel from '@/recognize/components/draft/components/can-not-split-label.vue'
import { DRAFT_TYPE } from '@/constant'

export default {
  name: 'batch-issue-table',
  components: {
    CanNotSplitLabel
  },
  props: {
    tableData: Array,
    selectList: Array
  },
  data() {
    return {
      TRANSACTION_TOOLTIP_TYPE, // 交易限制提示类型
      OFFER_TYPE_CODE, // 报价类型
      DRAFT_TYPE,
      IDENTIFY_TYPE, // 识别类型
      firstAllSelect: true, // 第一次全选
      isValid: false, // 是否校验过
      pageData: [],
      total: 0,
      currentPage: 1,
      pageSize: 100,
      pageNum: 1,
    }
  },
  computed: {
    ...mapGetters('user', {
      limitLight: 'limitLight', // 是否限制光速
    }),
  },
  watch: {
    tableData(newVal) {
      // 当前总页数
      let currentNum = Math.ceil(newVal.length / this.pageSize)
      // currentNum>this.pageSize 页数不变
      // 确认发布，数据变动减少，分页改变，最后一页删除,往前一页
      if (currentNum < this.pageNum) {
        this.currentPage -= 1
        this.pageNum -= 1
      }
      // currentNum ===0 && pageNum===1  下一次查询 currentNum、pageNum 一直会是 0
      if (this.pageNum <= 1) {
        this.currentPage = 1
        this.pageNum = 1
      }

      this.getData()
    },
    deep: true,
    immediate: true,
  },

  methods: {
    toDefectStr,
    yuan2wan,
    formatTime,
    keep2Decimals,
    // 改变报价类型
    handleChangeOfferType(index, row) {
      // 获取改变的 数据 在全部数据里面 的位置
      let curIndex = this.tableData.findIndex(value => row.draftDiscernId === value.draftDiscernId)
      this.$emit('change-type', curIndex, row)
    },

    handleShowQuotation(row) {
      this.$emit('show-quotation', row)
    },
    // 前端分页
    getData(val) {
      // 查询的时候 重置 页数
      if (val === 'search') {
        this.currentPage = 1
        this.pageNum = 1
        // 返回第一页要清空勾选 重新勾选当前页 -- 搜索 数据为空，清空搜索框，数据会变成200条
        this.clearSelection()
        this.$refs.multipleTable.toggleAllSelection(true)
      }
      this.$nextTick().then(() => {
        this.pageData = this.tableData.slice((this.pageNum - 1) * this.pageSize, this.pageNum * this.pageSize)
        this.total = this.tableData.length
      })
    },

    // 更改当前页
    handleCurrentChange(val) {
      this.pageNum = val
      // 清空全选 重新选择当前页面
      this.clearSelection()
      this.$refs.multipleTable.toggleAllSelection(true)
      this.getData()
    },
    // 输入利率/每十万手续费/每十万扣款
    handleInput(val, index, key, row) {
      // 获取改变的 数据 在全部数据里面 的位置
      let curIndex = this.tableData.findIndex(value => row.draftDiscernId === value.draftDiscernId)
      if (key === 'fastTrade') {
        this.$emit('input', val, curIndex, 'margin')
      }
      if (key === 'margin' && !val) {
        this.$emit('input', false, curIndex, 'fastTrade')
      }
      this.$emit('input', val, curIndex, key)
    },

    // 选择
    handleSelectionChange(list) {
      this.$emit('selection-change', list)
    },

    // 修改
    handleEdit(row) {
      this.$emit('handle-edit', row)
    },

    // 全选/反全选
    toggleAllSelection(selected) {
      this.$refs.multipleTable.toggleAllSelection(selected)
    },

    // 切换选中
    toggleRowSelection(rows) {
      if (rows) {
        rows.forEach(row => {
          this.$refs.multipleTable.toggleRowSelection(row, true)
        })
      } else {
        this.clearSelection()
      }
    },

    // 清空多选
    clearSelection() {
      this.$refs.multipleTable.clearSelection()
    },

    // 获取参考报价是否被禁用 （票据到期日 ≤30 天或信息不完整，无法提供报价行情参考）
    getQuotationBtnDisabled(row) {
      if (row.quotationBtnDisabled === undefined) {
        const { acceptorName, draftAmount, maturityDate } = row
        const spaceDate = getDateSpace(maturityDate, new Date())
        // eslint-disable-next-line no-magic-numbers
        row.quotationBtnDisabled = !acceptorName || !draftAmount || !maturityDate || spaceDate <= 30
      }
      return row.quotationBtnDisabled
    }
  }
}
</script>
