
<style lang="scss" scoped>
.content-wrap {
  margin-top: 8px;
  padding: 16px;
  background: #FFFFFFFF;
}

.m-l-r5 {
  margin: 0 5px;
}

.item-label {
  height: 32px;
  line-height: 32px;
}

.flex {
  display: flex;
  align-items: center;
}

.split-icon {
  height: 32px;
  line-height: 32px;
}

.offer-flex {
  display: flex;
  justify-content: space-between;

  .form-item-block {
    flex: 1;
    margin-right: 12px;

    .el-input {
      width: 100%;
    }
  }

  .form-item {
    .offer-bold {
      display: inline-block;
      width: 100%;
      font-weight: bold;
    }

    .amount {
      color: $color-warning;
    }
  }

  .plus-sign {
    line-height: 40px;
    padding: 0 8px;
    font-size: 18px;
  }
}

.form-item {
  flex-shrink: 0;
  line-height: 10px;

  .label {
    line-height: 10px;
  }
}

.del-icon-cls {
  cursor: pointer;
  margin: 0  5px 0 0;
  color: #999999;
}

.total-cls {
  padding-top: 16px;
  font-size: 14px;
  color: #999999;

  .value {
    margin-right: 18px;
    font-size: 14px;
    font-weight: 600;
    color: #333333;
  }

  .major {
    color: #EC3535;
  }
}

.btn-warp {
  display: flex;
}

.m-r10 {
  margin-right: 10px;
}

.dialog-footer {
  padding-top: 12px;
}

.offer-bold {
  display: inline-block;
  max-width: 120px;
  line-height: 20px;
}

.head-title {
  display: flex;
  font-size: 16px;
  font-weight: 500;

  .icon::before {
    display: inline-block;
    margin-top: 2px;
    margin-right: 10px;
    width: 2px;
    height: 16px;
    background: $--color-primary;
    content: " ";
  }

  .w-280 {
    display: flex;
    align-items: center;
    width: 280px !important;
  }
}
</style>

<style lang="scss">
  .split-rules-dialog-cls {
    .el-input {
      height: 32px !important;
    }

    .el-form-item {
      margin-bottom: 16px !important;
    }
  }
</style>

<template>
  <el-dialog
    title="拆分规则设置"
    :visible.sync="visible"
    class="split-rules-dialog-cls"
    width="1050px"
    :append-to-body="true"
    :before-close="handleClose"
  >
    <div>
      <WarnContent>
        系统依据下方拆分规则拆分订单，随机生成子票区间；金额相同将以“连”号票订单发布
      </WarnContent>
      <div class="content-wrap">
        <el-form
          ref="form"
          :model="form"
          label-position="top"
          label-width="80px"
        >
          <div class="head-title">
            <span class="icon w-280">拆分</span>
            <span class="icon w-280">报价</span>
          </div>
          <div v-for="(item, index) in form.splitSetting" :key="index" class="flex">
            <el-form-item :error="item.requireTxt">
              <!-- <div v-if="index === 0" class="item-label">拆分</div> -->
              <div class="flex">
                <icon
                  v-if="form.splitSetting.length > 1"
                  class="del-icon-cls"
                  size="20"
                  type="chengjie-a-lianji2"
                  @click="delSplitSeiitingItem(index)"
                />

                <el-input
                  v-model="item.splitAmt"
                  placeholder="拆分金额"
                  :width="90"
                  type="number"
                  size="small"
                  :number-format="{
                    decimal: true,
                    negative: false,
                    leadingZero: false,
                    maxDecimalLength: 6,
                    maxIntegerLength: 4,
                  }"
                  @input="handleInput(index)"
                >
                  <template slot="append">万</template>
                </el-input>
                <span class="split-icon m-l-r5">X</span>
                <el-input
                  v-model="item.splitCount"
                  placeholder="拆分张数"
                  :width="80"
                  type="number"
                  size="small"
                  :number-format="{
                    decimal: false,
                    negative: false,
                    leadingZero: false,
                    maxDecimalLength: 0,
                    maxIntegerLength: 4,
                  }"
                  @input="handleInput(index, 'splitCount')"
                >
                  <template slot="append">张</template>
                </el-input>
              </div>
            </el-form-item>

            <!-- -----------------------报价-------------------------------------------- -->
            <el-form-item>
              <!-- <div v-if="index === 0" class="item-label">报价</div> -->
              <div class="flex m-l-r5">
                <template v-if="item.billingMethod">
                  <el-input
                    v-model="item.annualInterestInput"
                    placeholder="利率"
                    :width="80"
                    type="number"
                    size="small"
                    :number-format="{
                      maxDecimalLength: 4,
                      leadingZero: false,
                      negative: false,
                      maxLength: 7
                    }"
                    @input="handleInput(index)"
                  >
                    <template slot="append">%</template>
                  </el-input>
                  <div class="m-l-r5 split-icon">+</div>
                  <el-input
                    v-model="item.serviceCharge"
                    placeholder="每十万手续费"
                    :width="110"
                    type="number"
                    size="small"
                    :number-format="{
                      maxDecimalLength: 2,
                      leadingZero: false,
                      negative: false,
                      maxLength: 8
                    }"
                    @input="handleInput(index)"
                  >
                    <template slot="append">元</template>
                  </el-input>
                </template>
                <template v-else>
                  <el-input
                    v-model="item.lakhDeduction"
                    placeholder="每十万扣款"
                    :width="140"
                    type="number"
                    size="small"
                    :number-format="{
                      maxDecimalLength: 2,
                      leadingZero: false,
                      negative: false,
                      maxLength: 8
                    }"
                    @input="handleInput(index)"
                  >
                    <template slot="append">元</template>
                  </el-input>
                </template>

                <el-tooltip :content="item.billingMethod ? '切换为每十万扣款计算' : '切换为以年利率计算' " placement="bottom">
                  <el-button
                    class="m-l-r5"
                    type="primary"
                    size="mini"
                    round
                    border
                    @click="handleChangeType(index)"
                  >
                    <icon class="icon icon-switch" type="chengjie-swap" />
                  </el-button>
                </el-tooltip>

                <div class="offer-flex">
                  <FormItem label="每十万扣息" class="m-l-r5">
                    <span class="offer-bold">{{ item.lakhDeduction ? `${item.lakhDeduction}元` : '-' }}</span>
                  </FormItem>
                  <FormItem label="年化利率" align="center" class="m-l-r5">
                    <span class="offer-bold">{{ item.annualInterest }}%</span>
                  </FormItem>
                  <FormItem label="单张到账金额" align="right" class="m-l-r5">
                    <span class="offer-bold amount">{{ item.receivedAmount ? `${yuan2wan(item.receivedAmount)}万元` : '0 万元' }}</span>
                  </FormItem>
                </div>
              </div>
            </el-form-item>
          </div>
          <div class="btn-warp">
            <el-tooltip content="可设置10条拆分规则" placement="top">
              <el-button
                type="primary"
                class="m-r10"
                :disabled="form.splitSetting.length === 10"
                @click="addRules"
              >
                添加规则
              </el-button>
            </el-tooltip>

            <el-upload
              action=""
              class="upload-btn m-r10"
              :show-file-list="false"
              :before-upload="beforeUpload"
              :http-request="httpRequest"
              accept=".xls,.xlsx"
            >
              <el-button>批量导入</el-button>
            </el-upload>
            <el-button @click="downloadTemplate">下载模版</el-button>
          </div>
          <div class="total-cls">
            <span>合计拆分金额：<span class="value">{{ totalSplitAmount || 0 }}万元</span></span>
            <span>合计拆分张数：<span class="value">{{ totalSplitNum || 0 }}张</span></span>
            <span>合计到账金额：<span class="value major">{{ totalReceivedAmount ? `${yuan2wan(totalReceivedAmount)}万元` : '0万元' }}</span></span>
            <span>票面总金额：<span class="value major">{{ draftInfo.draftAmount ? `${yuan2wan(draftInfo.draftAmount)}万元` : '0万元' }}</span></span>
          </div>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="onSubmit">确 定</el-button>
      </span>
    </div>
  </el-dialog>
</template>

<script>
import BigNumber from 'bignumber.js'
import { BILLING_METHOD_CODE, OSS_DIR } from '@/constant.js' // 常量
import WarnContent from '@/views/components/common/warn-content.vue' // 提示
import FormItem from '../components/form-item.vue'
import { SPLIT_SETTING_IMPORT_URL } from '@/constants/oss-files-url' // 平台订单违约规则url
import {
  lakhDeductionMath, interestRateMath
} from '@/common/js/draft-math'
// 金额单位转换
import { yuan2wan, wan2yuan } from '@/common/js/number'
import {
  OFFER_TYPE_CODE, // 报价类型
} from '@recognize/constant'
import { mapGetters } from 'vuex'
import {
  download, // 下载文件
} from '@/common/js/util' // 防抖
import { parseExcel } from '@/utils/parse-excel'
import { upload } from '@/utils/oss'

const MAPPING = {
  '拆分金额（万元）': 'splitAmt',
  拆分张数: 'splitCount',
  '每十万扣款（元）': 'lakhDeduction',
  '利率（%）': 'annualInterest',
  '每十万手续费（元）': 'serviceCharge'
}

export default {
  name: 'split-rules-dialog',
  components: {
    WarnContent,
    FormItem
  },

  props: {
    draftInfo: {
      type: Object,
    }
  },

  data() {
    return {
      MAPPING,
      visible: false,
      dir: OSS_DIR.PARSE_XLS,
      form: {
        splitSetting: [{ splitAmt: '', splitCount: '', billingMethod: 0, serviceCharge: '', lakhDeduction: '', annualInterest: '', annualInterestInput: '', receivedAmount: '' }],
        // splitAmt: '', // 拆分金额
        // splitCount: '', // 拆分张数，
        // lakhDeduction: '', // 每10万扣款
        // billingMethod: 1, // 报价方式
        // annualInterest: '', // 计算得出的利率
        // serviceCharge: '', // 每10万手续费
        // receivedAmount: '', // 到账金额
        // annualInterestInput: '', // 输入的利率

      },
    }
  },

  computed: {
    ...mapGetters('issue-draft', ['splitRulesArr']),
    // 合计拆分金额 拆分金额  * 张数
    // 合计拆分金额 拆分金额  * 张数
    totalSplitAmount() {
      const result = this.form.splitSetting.reduce((accumulator, currentValue) => {
        const splitAmt = new BigNumber(currentValue.splitAmt || 0) // 拆分金额
        const splitCount = new BigNumber(currentValue.splitCount || 0) // 拆分张数
        const partialAmount = splitAmt.times(splitCount) // 拆分金额*拆分张数
        return accumulator.plus(partialAmount)
      }, new BigNumber(0))
      const adjustedResult = result.isNaN() ? 0 : Number(result.toString())
      return adjustedResult
    },
    // 合计拆分张数
    totalSplitNum() {
      return this.form.splitSetting.reduce((accumulator, currentValue) => Number(accumulator) + Number(currentValue.splitCount), 0) || 0
    },
    // 合计到账金额 单张到账金额 * 张数
    totalReceivedAmount() {
      return this.form.splitSetting.reduce(
        (accumulator, currentValue) =>
          accumulator.plus(new BigNumber(currentValue.receivedAmount || 0).times(currentValue.splitCount || 0)),
        new BigNumber(0)
      ).toNumber() || 0
    }
  },

  watch: {
  },
  methods: {
    yuan2wan,
    // 获取年利率
    getAnnualInterest(index) {
      const { interestDays } = this.draftInfo // 计算利息天数
      // 拆分金额 每10万扣款 报价方式 输入的利率 每10万手续费
      const { splitAmt, lakhDeduction, billingMethod, annualInterestInput, serviceCharge } = this.form.splitSetting[index]

      if (billingMethod === OFFER_TYPE_CODE.MEI_SHI_WAN_ZHI_KOU) {
        const { annualInterest, receivedAmount } = lakhDeductionMath(wan2yuan(splitAmt), lakhDeduction, interestDays)
        // eslint-disable-next-line no-magic-numbers
        this.form.splitSetting[index].annualInterest = (+annualInterest).toFixed(4)
        this.form.splitSetting[index].receivedAmount = receivedAmount
      } else {
        const { annualInterest, receivedAmount, lakhDeduction: rtLakhDeduction } = interestRateMath(wan2yuan(splitAmt), annualInterestInput, serviceCharge, interestDays)
        // eslint-disable-next-line no-magic-numbers
        this.form.splitSetting[index].annualInterest = (+annualInterest).toFixed(4)
        this.form.splitSetting[index].receivedAmount = receivedAmount
        this.form.splitSetting[index].lakhDeduction = rtLakhDeduction ? (+rtLakhDeduction).toFixed(2) : null
      }
    },
    init() {
      this.$nextTick().then(() => {
        if (this.splitRulesArr.length) {
          this.form.splitSetting = []
          this.form.splitSetting = [...this.form.splitSetting, ...this.splitRulesArr]
        }
      })
      this.visible = true
    },
    handleClose() {
      this.$store.commit('issue-draft/setSplitRulesArr', this.form.splitSetting)
      this.visible = false
    },
    // 切换报价方式
    handleChangeType(idx) {
      this.form.splitSetting[idx].billingMethod = this.form.splitSetting[idx].billingMethod ? BILLING_METHOD_CODE.SHI_WAN_DISCOUNT : BILLING_METHOD_CODE.ANNUAL_INTEREST

      this.form.splitSetting[idx].lakhDeduction = ''
      this.form.splitSetting[idx].annualInterest = ''
      this.form.splitSetting[idx].serviceCharge = ''
      this.form.splitSetting[idx].annualInterestInput = ''
    },
    // 报价计算
    handleInput(index, filedName) {
      // 拆分张数只能输入>0的整数
      if (filedName === 'splitCount') {
        this.form.splitSetting[index].splitCount < 1 && (this.form.splitSetting[index].splitCount = '')
      }
      this.getAnnualInterest(index)
    },
    // 添加规则
    addRules() {
      this.form.splitSetting.push({ splitAmt: '', splitCount: '', billingMethod: 0, lakhDeduction: '', annualInterest: '', annualInterestInput: '', receivedAmount: '' })
    },
    onSubmit() {
      const require = this.form.splitSetting.every(e => {
        if (e.billingMethod === 1) {
          return e.splitAmt && e.splitCount && e.annualInterestInput
        } else {
          return e.splitAmt && e.splitCount && e.lakhDeduction
        }
      })
      if (!require) return this.$message.warning('请完善拆分规则设置')
      const { totalSplitAmount, totalSplitNum, totalReceivedAmount } = this
      // 合计拆分金额必须等于票面金额
      if (totalSplitAmount !== Number(yuan2wan(this.draftInfo.draftAmount))) return this.$message.warning('合计拆分金额必须等于票面金额')
      const splitSetting = JSON.parse(JSON.stringify(this.form.splitSetting))
      // eslint-disable-next-line vue/custom-event-name-casing
      this.$emit('setSplitRulesData', { splitSetting, totalSplitAmount, totalSplitNum, totalReceivedAmount })
      this.$store.commit('issue-draft/setSplitRulesArr', this.form.splitSetting)
      this.visible = false
    },
    delSplitSeiitingItem(idx) {
      if (this.form.splitSetting.length > 1) {
        this.form.splitSetting.splice(idx, 1)
      }
    },
    // 下载模版
    downloadTemplate() {
      download(SPLIT_SETTING_IMPORT_URL)
    },
    // 文件大小校验
    fileSizeCheck(file) {
      // eslint-disable-next-line no-magic-numbers
      const size = 10 * 1024 * 1024 // 最大限制 10Mb 的文件
      const isLt = file.size < size
      if (!isLt) {
        this.$message.error(`上传文件大小不能超过 ${10}MB`)
      }
      return isLt
    },
    // 上传前校验
    beforeUpload(file) {
      if (!this.fileSizeCheck(file)) return Promise.reject(new Error())
      if (!this.dir) {
        this.$message.error('请指定文件夹')
        return Promise.reject(new Error('请指定文件夹'))
      }
      return true
    },
    // 上传excel
    async httpRequest(param) {
      let { file, onSuccess } = param
      this.uploading = true
      let url = ''
      try {
        url = await upload(file, this.dir)
        onSuccess({ url })
      } catch (e) {
        // eslint-disable-next-line
        console.log('上传失败')
      } finally {
        this.uploading = false
      }
      let dataTemp = await parseExcel(url)

      // 判断是否有修改表头固定文案
      const expectedFields = ['拆分金额（万元）', '拆分张数', '每十万扣款（元）', '利率（%）', '每十万手续费（元）']
      const hasInvalidFields = !dataTemp.every(item => {
        const keys = Object.keys(item)
        return keys.every(key => expectedFields.includes(key))
      })
      if (hasInvalidFields) {
        return this.$message.error('模板格式错误，请不要修改模板')
      }
      // 数据处理
      dataTemp = dataTemp.map(item => {
        const obj = {}
        for (const key in item) {
          // eslint-disable-next-line no-prototype-builtins
          if (this.MAPPING.hasOwnProperty(key)) {
            obj[this.MAPPING[key]] = parseFloat(item[key])
          }
        }
        // 到账金额设置为初始值
        obj.receivedAmount = ''
        // 每十万扣款类型
        if (obj.lakhDeduction) {
          obj.billingMethod = 0
        }
        // 利率+每十万手续费类型
        if (obj.annualInterest || (obj.annualInterest && obj.serviceCharge)) {
          obj.annualInterestInput = obj.annualInterest // 设置输入的利率
          obj.billingMethod = 1
        }
        // 若同一条拆分规则中同时输入每十万扣款和利率+每十万手续费，则系统自动取每十万扣款作为报价
        if (obj.lakhDeduction && (obj.annualInterest || (obj.annualInterest && obj.serviceCharge))) {
          obj.billingMethod = 0
        }
        return obj
      })
      if (dataTemp.length < 1) {
        this.$message.error('暂未识别到数据')
        return
      }
      if (dataTemp.length > 10) return this.$message.error('最多可导入10条拆分规则')

      this.form.splitSetting = []
      this.form.splitSetting = [...this.form.splitSetting, ...dataTemp.slice(-10)] // 最多10个

      // 计算每条数据报价数据
      this.$nextTick().then(() => {
        this.form.splitSetting.forEach((item, index) => {
          this.getAnnualInterest(index)
        })
      })
    },
  }
}
</script>
