<!-- 新票提示控件 -->
<style lang="scss" scoped>
.container {
  // margin-left: 8px;

  .title-label {
    // border-left: 1px solid $color-D9D9D9;
    // padding-left: 12px;
    height: 16px;
    text-align: right;
    line-height: 16px;
    cursor: pointer;
  }

  .icon-label {
    background-image: url("https://oss.chengjie.red/web/imgs/public/new-draft-tip-icon.png");
    margin-left: 4px;
  }
}

.tooltip-content {
  width: 244px;
  height: 43px;

  &-text {
    text-decoration: underline;
    color: $--color-primary;
    cursor: pointer;
  }

  &-icon {
    position: absolute;
    top: 10px;
    cursor: pointer;
    background-image: url("https://oss.chengjie.red/web/imgs/public/new-draft-tip-close.png");
    right: 12px;
  }
}
</style>

<template>
  <div class="container">
    <span class="title-label" @click="tipShow = !tipShow">新票指南</span>
    <el-tooltip
      v-model="tipShow"
      placement="top"
      manual
    >
      <div slot="content" class="tooltip-content">
        {{ isBatch ? '发布列表中包含新一代票据' : '您当前选择的是新一代票据' }}
        <div style="display: flex;">
          查看
          <span class="tooltip-content-text" @click="clickGuide">新一代票据指南</span>，了解具体操作
        </div>
        <icon class="tooltip-content-icon" @click="hideTip" />
      </div>
      <icon class="icon-label" @click="tipShow = !tipShow" />
    </el-tooltip>
  </div>
</template>

<script>
import { OSS_FILES_URL } from '@/constants/oss-files-url'

export default {
  name: 'new-draft-tip-view',

  props: {
    // 是否是批量发布
    isBatch: {
      type: Boolean,
      default: false
    },
  },

  data() {
    return {
      tipShow: false
    }
  },

  methods: {
    init() {
      // 初始化为false
      this.tipShow = false
    },

    // 新票指南
    clickGuide() {
      window.open(OSS_FILES_URL.NEW_DRAFT_GUIDE)
    },

    hideTip() {
      this.tipShow = false
    },
  }
}
</script>
