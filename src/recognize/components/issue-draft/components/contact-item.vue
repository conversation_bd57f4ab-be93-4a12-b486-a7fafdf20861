<!-- 联系人控件 -->
<style lang="scss" scoped>
::v-deep .el-input {
  &.text-left {
    input {
      margin-top: 10px;
      height: 32px;
    }
  }
}

::v-deep .el-input__suffix {
  top: 6px;
}

.tooltip-content {
  width: 244px;
  height: 43px;

  &-text {
    text-decoration: underline;
    color: $--color-primary;
    cursor: pointer;
  }

  &-icon {
    position: absolute;
    top: 10px;
    cursor: pointer;
    background-image: url("https://oss.chengjie.red/web/imgs/public/new-draft-tip-close.png");
    right: 12px;
  }
}
</style>

<template>
  <div class="container-right-one">
    <div class="title-flex">
      <div class="g-title">
        <span>联系方式</span>
        <span>&nbsp;</span>
        <el-tooltip popper-class="other-tip-custom">
          <div slot="content">
            <p>交易对手可通过您下方<br>预留的联系方式联系到<br>您，联系方式{{ mobilePattern ? '' : '非' }}必填。</p>
          </div>
          <icon
            type="chengjie-wenti"
            :size="23"
            color-theme="primary"
          />
        </el-tooltip>
      </div>
    </div>
    <el-row type="flex" :gutter="8">
      <el-col :span="8">
        <el-input
          v-model="mobile1"
          class="text-left"
          placeholder="请输入手机号"
          clearable
          type="number"
          :number-format="{maxDecimalLength: 0, leadingZero: false, negative: false, maxLength: 11}"
        />
      </el-col>
      <el-col :span="8">
        <el-input
          v-model="mobile2"
          class="text-left"
          placeholder="请输入手机号"
          clearable
          type="number"
          :number-format="{maxDecimalLength: 0, leadingZero: false, negative: false, maxLength: 11}"
        />
      </el-col>
      <el-col :span="8">
        <el-input
          v-model="mobile3"
          class="text-left"
          placeholder="请输入手机号"
          clearable
          type="number"
          :number-format="{maxDecimalLength: 0, leadingZero: false, negative: false, maxLength: 11}"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { TEMP_CONTACT_LIST_DATA } from '@/constant-storage'
import Storage from '@/common/js/storage'
import { mapGetters } from 'vuex'
export default {
  name: 'contact-item',
  data() {
    return {
      mobile1: '',
      mobile2: '',
      mobile3: '',
    }
  },
  computed: {
    ...mapGetters('common', {
      mobilePattern: 'mobilePattern', // 是否开启联系方式模式 1开启 0关闭
    }),
  },

  watch: {
    mobile1() {
      this.updateForm()
    },
    mobile2() {
      this.updateForm()
    },
    mobile3() {
      this.updateForm()
    },
  },

  created() {
    this.getDefaultContactSetting()
  },

  methods: {
    getDefaultContactSetting() {
      let mobile = Storage.get(TEMP_CONTACT_LIST_DATA)
      this.updateMobile(mobile)
    },

    updateMobile(mobile) {
      this.mobile1 = ''
      this.mobile2 = ''
      this.mobile3 = ''
      if (mobile) {
        const list = mobile.split(',')
        list.forEach((item, index) => {
          if (index === 0) {
            this.mobile1 = item
          } else if (index === 1) {
            this.mobile2 = item
          } else {
            this.mobile3 = item
          }
        })
      }
    },

    updateForm() {
      let mobileList = []
      if (this.mobile1) {
        mobileList.push(this.mobile1)
      }
      if (this.mobile2) {
        mobileList.push(this.mobile2)
      }
      if (this.mobile3) {
        mobileList.push(this.mobile3)
      }
      this.$emit('change-data', mobileList.join(','))
    },
  }
}
</script>
