<style lang="scss" scoped>
.contact-block {
  display: flex;
  align-items: center;

  .el-icon-warning {
    margin-right: 4px;
    font-size: 16px;
    vertical-align: text-bottom;
    color: $--color-warning;
  }
}

.contact-mobile {
  display: flex;
  align-items: center;
  border: 1px solid #D9D9D9;
  border-radius: 2px;
  padding: 4px 8px;

  & + & {
    margin-left: 8px;
  }
}

.del-button {
  margin-left: 4px;
  font-size: 16px;
  color: #363636;
  cursor: pointer;
}

.link-button {
  margin-left: 8px;
  text-decoration: underline;
  color: $color-008489;
  cursor: pointer;
}
</style>

<template>
  <div class="contact-block">
    <template v-if="mobiles.length > 0">
      <div v-for="(item) in filtered" :key="item" class="contact-mobile">
        <span>{{ item }}</span>
        <i v-if="mobiles.length > 1" class="el-icon-close del-button" @click="delMobile(item)" />
      </div>
    </template>
    <template v-else>
      <i class="el-icon-warning" />
      <span>未设置联系方式</span>
    </template>
    <span class="link-button" @click="setContact">默认设置</span>
  </div>
</template>

<script>
import userApi from '@recognize/apis/user'

export default {
  name: 'contact-setting-dialog',
  props: {
    preset: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // 父组件也在用，不要改名
      mobiles: []
    }
  },
  computed: {
    filtered() {
      if (!this.preset) {
        return [...this.mobiles]
      } else {
        return [...this.mobiles.filter(item => this.preset.indexOf(item) > -1)]
      }
    }
  },
  created() {
    this.getDefaultContactSetting()
  },
  methods: {
    async getDefaultContactSetting() {
      const data = await userApi.getMobileList()
      if (Array.isArray(data)) {
        this.mobiles = data.map(item => item.mobile)
      }
    },
    delMobile(item) {
      const index = this.mobiles.findIndex(mobile => mobile === item)
      index > -1 && this.mobiles.splice(index, 1)
    },
    setContact() {
      if (this.$ipc) {
        this.$ipc.send('PUSH_ROUTE_IN_MAIN_WINDOW', '/user-center/setting?activeContactTab=4')
      } else {
        this.$router.push('/user-center/setting?activeContactTab=4')
      }
    }
  },
}
</script>
