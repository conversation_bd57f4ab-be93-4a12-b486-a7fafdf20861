<!-- 报价行情参考弹窗 -->
<style lang="scss" scoped>
.quotation-reference-dialog {
  .card-out {
    margin-bottom: 12px;
    padding: 12px;
    background: $color-FFFFFF;
  }

  ::v-deep {
    .el-dialog__body {
      padding: 12px 16px;
    }
  }

  .row {
    margin: 10px 0;
  }

  .accpetor {
    display: flex;
    align-items: center;

    &-name {
      margin: 0 8px;
      max-width: 425px;

      @include ellipsis;
    }
  }

  .items-bg {
    display: flex;
    margin-top: 12px;
    border-radius: 2px;
    padding: 12px;
    background-color: $color-F5F6F8;

    .item-label {
      margin-bottom: 4px;
      color: $color-text-secondary;
    }

    .item-content-bold {
      font-weight: 600;
    }

    .item {
      margin-top: 0;
      border-left: 1px solid $color-D9D9D9;
      padding: 0 24px;

      &:first-child {
        border-left: none;
        padding-left: 0;
        min-width: 144px;
      }

      &-value {
        font-size: 16px;
      }
    }
  }

  .quotation-title {
    display: flex;
    justify-content: space-between;

    .refresh {
      font-size: 14px;
      font-weight: 500;
      color: $color-text-secondary;

      &-time {
        color: $color-text-primary;
      }

      &-icon {
        margin-left: 6px;
        color: $--color-primary;
        cursor: pointer;
        transform: rotate(360deg);
        transition: .5s;

        &:active {
          transform: rotate(0);
          transition: 0s;
        }
      }
    }
  }

  .quotation-items {
    display: flex;
    border-bottom: 1px solid $color-D9D9D9;
    padding: 12px 0;

    .item {
      margin-right: 22px;
      width: 120px;

      &:last-child {
        margin-right: 0;
      }

      .item-label {
        color: $color-text-secondary;
      }

      .item-content {
        font-weight: 600;
        color: $color-text-primary;
      }
    }
  }

  .quotation-chart {
    margin: 0 -20px -45px;
    width: 620px;
    height: 322px;
  }

  .empty-flex {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 342px;
    font-size: 14px;
    color: $color-text-secondary;
    flex-direction: column;
    line-height: 26px;

    .icon {
      display: flex;
      margin-bottom: 12px;
      height: 84px;
      font-size: 132px;
    }
  }

  .tip {
    margin-bottom: 12px;

    ::v-deep {
      .green-icon {
        font-size: 20px;
      }
    }
  }
}
</style>

<template>
  <el-dialog
    width="600px"
    :visible.sync="visible"
    :before-close="() => { visible = false }"
    :close-on-click-modal="false"
    append-to-body
    title="报价行情参考"
    class="quotation-reference-dialog whead-gbody-dialog"
  >
    <WarnContent class="tip">
      报价行情仅供参考，个别票据价格可能不具备参考价值，请注意甄别！
    </WarnContent>

    <div class="card-out">
      <div class="g-red-title-small">票面信息</div>
      <p class="accpetor row">
        <span class="text-gray">承兑人</span>
        <el-tooltip :content="draftData.acceptorName" show-when-overflow>
          <span class="accpetor-name">{{ draftData.acceptorName }}</span>
        </el-tooltip>
        <span v-if="draftData.accepterType" class="g-tag--green">{{ ACCEPTOR_TYPE_VALUE_MAP[draftData.accepterType] }}</span>
      </p>
      <div class="items-bg">
        <div class="item">
          <div class="item-label">
            <span>票面金额</span>
          </div>
          <div class="item-content item-content-bold">
            {{ draftData.draftAmount || "--" }} 万
          </div>
        </div>
        <div class="item">
          <div class="item-label">到期日</div>
          <div class="item-content">
            {{
              `${draftData.maturityDate || "--"}（剩${
                draftData.interestAccrualDay || "--"
              }天）`
            }}
          </div>
        </div>
        <div class="item">
          <div class="item-label">背书手数</div>
          <div class="item-content  item-content-bold">
            {{
              isNull(draftData.endorseCount)
                ? "未提供"
                : draftData.endorseCount
            }}
          </div>
        </div>
      </div>
    </div>

    <div class="card-out">
      <div class="g-red-title-small quotation-title">
        <div>价格参考</div>
        <div class="refresh">
          行情时间：
          <span class="refresh-time">{{ formatTime(quotationData.refreshTime, 'hh:mm:ss') }}</span>
          <el-tooltip content="点击刷新" placement="top">
            <icon
              class="refresh-icon"
              type="chengjie-refresh"
              @click="refresh"
            />
          </el-tooltip>
        </div>
      </div>

      <div v-if="!hasData" class="empty-flex">
        <icon class="icon" type="chengjie-empty" />
        暂无参考报价
      </div>
      <template v-else>
        <div class="quotation-items">
          <div class="item">
            <div
              class="item-label"
            >
              同类型票据张数
              <el-tooltip
                placement="top"
                content="根据当前票据在同样三要素区间，且同为待接单的票据。 同时已为您自动过滤到期日少于 30 天或利率低于 1% 的票据。"
                :popper-style="{maxWidth: '272px'}"
              >
                <icon class="icon icon-question" type="chengjie-wenti" />
              </el-tooltip>
            </div>
            <div class="item-content">{{ quotationData.orderCounts }}</div>
          </div>
          <div class="item">
            <div
              class="item-label"
            >
              最高每十万扣 (元)
            </div>
            <div class="item-content">{{ quotationData.maxLakhFee }}</div>
          </div>
          <div class="item">
            <div
              class="item-label"
            >
              最低每十万扣 (元)
            </div>
            <div class="item-content">{{ quotationData.minLakhFee }}</div>
          </div>
          <div class="item">
            <div
              class="item-label"
            >
              中位数报价 (元)
            </div>
            <div class="item-content">{{ quotationData.medianLakhFee }}</div>
          </div>
        </div>

        <div ref="quotationChartRef" class="quotation-chart" />
      </template>
    </div>
  </el-dialog>
</template>

<script>
import {
  ACCEPTOR_TYPE_VALUE_MAP, // 承兑人类型 id 映射 名称
} from '@recognize/constant'
import {
  isNull, // 判断是否非空值
} from '@/common/js/util'
import { wan2yuan } from '@/common/js/number'
import echarts from '@/utils/echarts.js'
import ticketApi from '@recognize/apis/ticket'
import { formatTime } from '@/common/js/date'
import WarnContent from '@recognize/components/warn-content/warn-content.vue' // 警告文本

export default {
  name: 'quotation-reference-dialog',
  components: {
    WarnContent
  },
  data() {
    return {
      visible: false, // 弹窗是否打开
      ACCEPTOR_TYPE_VALUE_MAP, // 承兑人类型 id 映射 名称
      draftData: {
        draftNo: null, // 票号
        acceptorName: null, // 承兑人
        accepterType: null, // 承兑人类型
        draftAmount: null, // 票面金额 万
        maturityDate: null, // 到期日
        interestAccrualDay: null, // 剩余天数
        endorseCount: null // 背书手数
      },
      quotationData: {
        orderCounts: 0, // 订单数
        maxLakhFee: 0, // 最高10w扣
        minLakhFee: 0, //  最低10w扣
        medianLakhFee: 0, // 中位数10w扣
        priceIntervalDTOS: [], // 价格区间集合：当订单数大于5时写入改值。订单数小于等于5不写入该值
        lakhFeeList: [], // 10w扣的集合：订单数小于等于5写入该值。大于5不写入该值
        refreshTime: '', // 刷新时间
      },
      hasData: false, // 是否有数据
      quotationChart: null, // 报价图表
    }
  },
  methods: {
    formatTime,
    isNull,
    wan2yuan,
    init(draftData) {
      this.draftData = draftData
      this.refresh()
    },
    refresh() {
      const {
        draftNo,
        acceptorName,
        draftAmount,
        maturityDate
      } = this.draftData
      ticketApi.enquiryPrice({
        draftNo,
        acceptorName,
        draftAmount: wan2yuan(draftAmount), // 传元
        maturityDate
      }).then(data => {
        if (data) {
          Object.keys(this.quotationData).forEach(key => {
            this.quotationData[key] = data[key] || null
          })
        } else {
          this.quotationData.refreshTime = new Date().valueOf()
        }
        this.hasData = !!data
        this.visible = true
        this.$nextTick().then(() => {
          this.hasData && this.echartsInit()
        })
      })
        .catch(err => {
          if (err?.data?.code !== 1000) {
            // eslint-disable-next-line no-console
            console.error(err)
          }
        })
    },

    // 数据转换
    transformList() {
      const { priceIntervalDTOS, lakhFeeList } = this.quotationData
      const xData = []
      const yData = []
      if (priceIntervalDTOS && priceIntervalDTOS.length) { // 订单数大于等于5,x每一项都是数组区间
        priceIntervalDTOS.forEach(item => {
          const { nums, minValue, maxValue } = item
          xData.push([minValue, maxValue])
          yData.push(nums)
        })
      } else if (lakhFeeList && lakhFeeList.length) { // 订单数小于等于5,无区间
        let tempObj = {}
        lakhFeeList.forEach(num => {
          tempObj[num] = tempObj[num] ? tempObj[num] + 1 : 1
        })
        Object.keys(tempObj).forEach(key => {
          xData.push(key)
          yData.push(tempObj[key])
        })
      }
      return {
        xData,
        yData
      }
    },

    // 图表初始化
    echartsInit() {
      const { xData, yData } = this.transformList()
      // 防止重新初始化
      if (!this.quotationChart) {
        let chartDom = this.$refs.quotationChartRef
        this.quotationChart = echarts.init(chartDom)
      }

      let option = {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0, color: '#FFBCBE' // 0% 处的颜色
            }, {
              offset: 1, color: '#FF8C90' // 100% 处的颜色
            }
          ],
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            label: {
              formatter({ value }) {
                return `${value.split(',').join(' - ')}`
              }
            }
          },
          valueFormatter(value) {
            return `${value}张`
          }
        },

        xAxis: {
          name: '每十万扣',
          nameTextStyle: {
            color: '#8C8C8C',
            verticalAlign: 'top',
            lineHeight: 1,
            padding: [0, 0, 0, 10]
          },
          data: xData,
          splitArea: {
            show: false
          },
          splitLine: {
            show: false
          },
          axisLabel: {
            interval: 0,
            color: '#262626',
            fontWeight: 600,
            margin: 13,
            formatter(value, index) {
              if (value.includes(',')) {
                if (index === 0) {
                  return `${value.split(',').join('               ')}`
                } else {
                  return `                        ${value.split(',')[1]}`
                }
              } else {
                return value
              }
            }
          },
          axisTick: {
            show: false
          },
          axisLine: {
            lineStyle: {
              color: '#D9D9D9'
            }
          }
        },
        yAxis: {
          name: '待接单张数',
          nameTextStyle: {
            color: '#8C8C8C',
            padding: [0, 0, 10, -16]
          },
          minInterval: 1,
          max(value) {
            return value.max + (value.min > 5 ? 10 : 3)
          },
          splitLine: {
            show: false
          },
          axisLabel: {
            color: '#262626',
            fontWeight: 600,
            margin: 16,
          },
        },
        series: [
          {
            data: yData,
            type: 'bar',
            backgroundStyle: {
              color: 'rgba(255,255,255,1)'
            },
            barWidth: 78, // 柱图宽度
          }
        ],
        grid: {
          top: '18%',
          right: '24%'
        }
      }
      this.quotationChart.setOption(option)
    },
  }

}
</script>
