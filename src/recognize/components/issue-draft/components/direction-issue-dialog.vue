<!-- 定向发布弹窗 -->
<style lang="scss" scoped>
.direction-issue-dialog {
  .tip-title {
    display: inline-block;
    padding: 12.25px 17.25px;
    width: 100%;
    font-size: 16px;
    color: $color-text-primary;
    background-color: $--color-primary-hover;

    .icon {
      margin-right: 11.25px;
    }
  }

  .input-out {
    margin-top: 12px;
    padding: 6px 16px 16px;
    background-color: $color-FFFFFF;
  }
}

.sellerBankAccount-cls {
  padding-top: 15px;

  .seller-select-btn {
    font-size: 16px;
    text-align: center;
    color: $--color-primary;
    line-height: 40px;
    cursor: pointer;
  }

  .seller-bank-link {
    border-bottom: 1px solid $--color-primary;
    color: $--color-primary;
    cursor: pointer;
  }
}

.item-position {
  position: relative;

  ::v-deep .el-form-item__label {
    padding: 0;
  }

  .value {
    .el-tooltip {
      position: absolute;
      top: 1px;
      left: 65px;
    }
  }

  .max-width {
    width: 100%;
  }
}
</style>

<style lang="scss">
.el-message-box__wrapper {
  .el-message-box .el-message-box__content {
    .red-soso {
      color: $color-warning;
    }
  }
}
</style>

<template>
  <el-dialog
    title="定向发布"
    :visible.sync="dialogVisible"
    width="600px"
    append-to-body
    :close-on-click-modal="false"
    class="direction-issue-dialog whead-gbody-dialog"
  >
    <WarnContent>定向单暂不支持议价，继续发布将会以非议价订单发布！ </WarnContent>
    <div class="input-out">
      <FormItem label="交易对手">
        <el-input v-model="number" placeholder="交易对手定向码/手机号" />
      </FormItem>
      <!-- 定向发布弹窗隐藏回款账户选项 -->
      <!--
        <div v-if="isShowSellerBankAccount" class="sellerBankAccount-cls">
        <div class="txt">选中的订单包含支持智付E+渠道，根据平台规则请选择回款账户</div>
        <FormItem
        label="回款账户"
        class="form-item-block item-position"
        required
        >
        <el-tooltip
        placement="top"
        popper-class="issue-draft-tooltip"
        >
        <template slot="content">
        <div>依照智付E+渠道交易模式，票方发布票据必须选择回款账户(支持任意银行一般户)。需要在<span class="seller-bank-link" @click="() => { $router.push('/user-center/bank-account?tabStatus=2');dialogVisible = false }">银行回款账户</span>页面绑定回款账户，交易完成后票款自动提现到账。</div>
        </template>
        <icon class="icon icon-question" type="chengjie-wenti" />
        </el-tooltip>

        <div class="pay-type-item">
        <el-select
        v-model="sellerBankAccount"
        class="max-width"
        :height="40"
        placeholder="请选择回款账户"
        >
        <el-option
        v-for="item in sellerBankAccountList"
        :key="item.value"
        :label="item.label"
        :value="item.value"
        />
        <div class="seller-select-btn" @click="() => { $router.push('/user-center/bank-account?tabStatus=2'); $refs.sellerBankAccount.blur(); dialogVisible = false }"><i class="el-icon-plus" />添加回款账户</div>
        </el-select>
        </div>
        </FormItem>
        </div>
      -->
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="large" @click="dialogVisible = false">取消</el-button>
      <el-button
        v-waiting="['post::loading::https://shendu-qa.oss-cn-hangzhou.aliyuncs.com/', 'get::loading::/operation/common/upload/policy', 'get::loading::/draft/postOrder']"
        type="secondary"
        size="large"
        @click="handleConfirm"
      >确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import FormItem from './form-item.vue'
import ticketApi from '@recognize/apis/ticket'
import BigNumber from 'bignumber.js'
import WarnContent from '@/views/components/common/warn-content.vue' // 提示
// import { mapGetters } from 'vuex'

export default {
  name: 'batch-edit-dialog',
  components: {
    FormItem,
    WarnContent
  },

  props: {
    drafts: [Array, Object], // 交易限额
    isShowSellerBankAccount: { // 支付渠道是否存在E+邦+
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      number: null,
      isMargin: null, // 是否开启了保证金
      // sellerBankAccount: '', // 回款账户
    }
  },

  computed: {
    // ...mapGetters('user', {
    //   sellerBankAccountList: 'sellerBankAccountList' // 回款账户列表
    // }),
    // sellerBankAccountId() {
    //   return this.$store.state.common.sellerBankAccountId
    // },
    draftAmount() {
      const { drafts } = this
      if (!drafts) return 0
      if (Array.isArray(drafts)) { // 批量发布
        return +drafts.reduce((prev, curr) => prev.plus(curr?.draftAmount ?? 0), new BigNumber(0))
      }
      return drafts?.draftAmount ?? 0 // 单张发布
    }
  },

  // created() {
  //   this.sellerBankAccount = this.sellerBankAccount || this.sellerBankAccountId
  // },

  methods: {
    // 切换显示隐藏
    toggle(data) {
      this.dialogVisible = !this.dialogVisible
      this.isMargin = data
    },

    // 点击确定
    async handleConfirm() {
      if (!this.number) {
        this.$message.error('请输入交易对手定向码/手机号')
        return
      }
      // if (!this.sellerBankAccount) return this.$message.error('请选择回款账户')
      try {
        await ticketApi.validateAgentOrder({
          inviteCode: this.number,
          draftAmount: this.draftAmount
        })
        const isAreaInBlack = await ticketApi.validateAgentOrderArea({
          counterparty: this.number,
          limitType: 1
        })
        if (isAreaInBlack.isLimit) {
          await this.$confirm('对方在您设置的地区黑名单，请确认是否继续定向', '提示', {
            type: 'warning',
            iconPosition: 'title',
            showClose: false,
            showCancelButton: true,
            cancelButtonText: '取消',
            confirmButtonText: '确认'
          }).then(
            () => Promise.resolve(),
            // eslint-disable-next-line prefer-promise-reject-errors
            () => Promise.reject({ data: { code: '', msg: '对方在地区黑名单中' } })
          )
        }
        this.dialogVisible = false
        this.$emit('confirm', this.number)
      } catch (err) {
        const { code, msg } = err.data
        this.validErrorMsg(code, msg)
      }
    },

    /**
     * 校验错误提示
     * @param {String} code 错误码
     * @param {String} msg 提示
     */
    validErrorMsg(code, msg) {
      const SELLER_BLACK_LIST_ERROR = 3011 // 在对方黑名单错误码
      const PUBLISH_BLACK_LIST_ERROR = 3013 // 在自己黑名单错误码
      const CREDIT_TYPE_GENERAL = 3026 // 当前用户信用等级一般
      const BUYER_MAX_LIMIT = 3039 // 交易对手接单量已达到限额

      if (code === SELLER_BLACK_LIST_ERROR) {
        this.$message.error('交易对手已暂停与您定向交易的权限！')
      } else if (code === PUBLISH_BLACK_LIST_ERROR) {
        this.$message.error('您已将交易对手列入黑名单，请先将其移出接单方黑名单后再交易！')
      } else if (code === CREDIT_TYPE_GENERAL) {
        this.$confirm('<div>您当前的信用等级为 <span class="red-soso">一般</span>。只可发布带保证金订单。</div><div>前往 <span class="red-soso">账户信息-我的信用</span> 可以查看信用分规则。</div>', '提示', {
          dangerouslyUseHTMLString: true,
          type: 'warning',
          showClose: false,
          iconPosition: 'title',
          cancelButtonText: '取消',
          confirmButtonText: '开启保证金',
        }).then(() => {
          this.$emit('open-margin')
        })
      } else if (code === BUYER_MAX_LIMIT) {
        this.$confirm('您的交易对手接单量已达到限额，如需帮助，请联系客户经理', '提示', {
          type: 'warning',
          iconPosition: 'title',
          showClose: false,
          showCancelButton: false,
          confirmButtonText: '我知道了'
        })
      } else {
        this.$message.error(`发布失败：${msg}`)
      }
    },
  }
}
</script>
