<!-- 黑名单列表 -->
<style lang="scss" scoped>
.black-list {
  display: flex;
  overflow: hidden;
  overflow-y: scroll;
  margin-top: -8px;
  margin-right: -8px;
  max-height: 96px;
  flex-wrap: wrap;

  .black-item {
    position: relative;
    margin-top: 8px;
    margin-right: 8px;
    border: 1px solid $color-D9D9D9;
    border-radius: 2px;
    padding: 0 18px 0 5px;
    width: 98px;
    height: 40px;
    line-height: 40px;

    @include ellipsis;

    .delete-icon {
      position: absolute;
      top: 50%;
      right: 7px;
      transform: translateY(-50%);
      cursor: pointer;
    }
  }

  .el-button {
    margin-top: 8px;
    font-size: 16px;
  }

  .add-icon {
    display: inline-block;
    margin-top: -1px;
    margin-right: 8px;
    font-size: 18px;
    color: $color-008489;
    vertical-align: middle;
  }
}
</style>

<template>
  <div class="black-list">
    <template v-for="(item, index) in blacklist">
      <el-tooltip
        :key="item + index"
        :content="item"
        placement="top"
      >
        <div class="black-item">
          <span>{{ item }}</span>

          <icon
            class="delete-icon"
            type="chengjie-close"
            :size="10"
            color="black"
            @click="handleDeleteBlacklist(index)"
          />
        </div>
      </el-tooltip>
    </template>
    <el-popover
      v-if="!blacklist || blacklist.length < 30"
      v-model="showPopover"
      placement="bottom-end"
      trigger="click"
      popper-class="custom-popover"
      :visible-arrow="false"
      :offset="60"
    >
      <el-button
        slot="reference"
        size="medium"
        height="40"
        plain
      >
        <icon type="chengjie-plus-square" :size="20" /> 添加
      </el-button>
      <div style="min-width: 365px;">
        <el-cascader-panel
          ref="region"
          v-model="selectedRegion"
          :options="region"
          :props="{value: 'label', label: 'value', maxLevel: 2, checkStrictly: true}"
          @change="handleChangeRegion"
        >
          <template slot-scope="{ data }">
            <span>{{ data.label }}</span>
          </template>
        </el-cascader-panel>
      </div>
    </el-popover>
  </div>
</template>

<script>
import region from '@/common/json/region-code.json' // 地址库

export default {
  name: 'black-list',

  props: {
    value: Array, // 黑名单列表
  },

  data() {
    return {
      region,
      selectedRegion: [], // 地址级联选框选中值，用于在选择后再次打开时不回显
      blacklist: [],
      showPopover: false, // 地址选择框是否显示
      blackRes: [],
    }
  },

  watch: {
    value(v) {
      if (v && v[0] && v[0].provinceName) {
        this.blacklist = v.map(item => `${item.provinceName}${item.cityName ? `,${item.cityName}` : ''}`)
        this.blackRes = v
      }
    },
    blackRes(n) {
      this.$emit('input', n)
    }
  },

  mounted() {
    if (this.value && this.value[0] && this.value[0].provinceName) {
      this.blacklist = this.value.map(item => `${item.provinceName}${item.cityName ? `,${item.cityName}` : ''}`)
      this.blackRes = this.value
    } else {
      this.blacklist = []
      this.blackRes = []
    }
  },

  methods: {
    // 地址级联选择器改变回调
    handleChangeRegion(value) {
      if (!value.length) return

      const node = this.$refs.region.getCheckedNodes()[0]
      const obj = {
        provinceCode: node.pathLabels[0],
        provinceName: node.path[0],
      }
      if (value.length === 2) { // 选择了市级
        Object.assign(obj, {
          cityCode: node.pathLabels[1], // 省市编码，非空字段，只给市
          cityName: node.path[1] // 省市名称，非空字段，只给市
        })
      }

      // 判断是否重复
      if (this.blacklist.includes(value.join())) {
        this.$message.warning('请勿重复选择！')
        return
      }
      if (value.length === 2 && this.blacklist.includes(value[0])) {
        this.$message.warning(`已选择${value[0]}，请勿重复选择！`)
        return
      }
      if (value.length === 1 && this.blacklist.some(item => item.indexOf(value[0]) > -1)) {
        for (let i = this.blacklist.length - 1; i >= 0; i--) {
          if (this.blacklist[i].indexOf(value[0]) > -1) {
            this.handleDeleteBlacklist(i)
          }
        }
        this.$message.warning(`已自动去重${value[0]}下城市`)
      }
      this.blacklist.push(value.join())
      this.blackRes.push(obj)
      this.showPopover = false
      this.selectedRegion = []
    },
    // 删除黑名单
    handleDeleteBlacklist(index) {
      this.blacklist.splice(index, 1)
      this.blackRes.splice(index, 1)
    },
  }
}
</script>
