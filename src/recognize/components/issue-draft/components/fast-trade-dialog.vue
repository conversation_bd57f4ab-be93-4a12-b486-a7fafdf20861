
<!-- 光速交易订单开启确认弹窗 -->
<style lang="scss" scoped>
.content {
  .tips {
    display: flex;
    align-items: center;
    margin: 0 0 12px;
    padding: 0 16px;
    width: 100%;
    height: 32px;
    color: $color-text-primary;
    background: $color-E6F3F3;
    box-sizing: border-box;

    .tips-icon {
      margin-right: 9px;
      color: $color-008489;
    }

    .tips-text {
      font-size: 16px;
      line-height: 24px;
    }
  }

  .title-green {
    &::before {
      background: #008489;
    }
  }

  .items {
    padding: 12px;
    box-sizing: border-box;
    background: $color-FFFFFF;

    &:last-child {
      margin-top: 12px;
    }

    .title {
      font-size: 16px;
      font-weight: 600;
      color: $color-text-primary;

      &::before {
        height: 16px;
      }
    }

    .item-center {
      padding: 0 0 0 14px;
      font-size: 14px;
      color: $color-text-primary;

      .item {
        margin-top: 4px;
      }

      .item-title {
        margin-top: 12px;
        font-size: 16px;
        font-weight: 600;
      }

      .item-bottom {
        margin-bottom: 10px;
      }

      .green-item {
        font-weight: 600;
        color: $color-008489;
      }

      .red-item {
        font-weight: 600;
        color: $color-warning;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;

  .footer-left {
    text-align: left;
  }

  .protocol-text {
    font-size: 14px;
    color: $color-text-primary;
  }

  .protocol {
    .protocol-title {
      color: $color-008489;
      cursor: pointer;
    }
  }

  .el-button {
    font-size: 18px;
  }
}
</style>

<template>
  <el-dialog
    title="极速出票订单接单确认"
    :visible.sync="fastTradeVisible"
    class="whead-gbody-dialog"
    width="600px"
    append-to-body
  >
    <div class="content">
      <div class="tips">
        <icon type="chengjie-exclamation-circle" class="tips-icon" :size="14" />
        您已开启极速出票模式（同时会开启保证金），开启后您将有以下权益和责任。
      </div>
      <div class="items">
        <div class="g-title title title-green">权益</div>
        <div class="item-center">
          <!--
            <div class="item-title green-item">曝光更多</div>
            <div class="item">享「智能助手」「为您推荐」优先展示特权，额外获得10~50倍曝光，提高成交率</div>
          -->
          <div class="item-title green-item">交易提速</div>
          <div class="item">
            <span>接单方「打款时限」缩短至 </span>
            <span class="green-item">15分钟</span>
            <span>,「签收时限」缩短至 </span>
            <span class="green-item">15分钟</span>
          </div>
        </div>
      </div>
      <div class="items">
        <div class="g-title title">责任</div>
        <div class="item-center">
          <div class="item-title red-item">票须在户</div>
          <div class="item">票不在户产生的违约会扣除保证金</div>
          <div class="item-title red-item">保证金</div>
          <div class="item">发布需要带保证金</div>
          <div class="item-title red-item">背书提速</div>
          <div class="item">
            需在 <span class="red-item">10分钟</span> 内完成背书，否则将扣除保证金
          </div>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <div class="footer-left">
        <el-checkbox v-model="checked">
          <span class="protocol-text">15天内不再提醒</span>
        </el-checkbox>
        <div class="protocol">
          <el-checkbox v-model="agree">
            <span class="protocol-text">我已阅读并同意</span>
          </el-checkbox>
          <span class="protocol-title" @click="openRule">《极速出票订单规则》</span>
          <span class="protocol-title" @click="openProtocol">《“极速出票”授权委托书》</span>
        </div>
      </div>
      <div>
        <el-button
          width="104"
          height="36"
          type="primary"
          @click="submitConfirm"
        >
          我知道了
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import Storage from '@/common/js/storage' // 本地缓存对象
import { FAST_TRADE_NO_REMIND_FOR_15 } from '@recognize/constant-storage' // 光速交易确认框15天不再提醒
import { getDateSpace } from '@/common/js/date'
import { OSS_FILES_URL, FASTTRADE_URL } from '@/constants/oss-files-url'

export default {
  name: 'fast-trade-dialog',

  props: {},

  data() {
    return {
      fastTradeVisible: false, // 是否显示弹窗
      checked: false, // 15天不再提醒
      agree: true, // 是否同意阅读协议
      fromWhere: '', // 来自哪个操作
    }
  },

  methods: {
    // 初始化
    init(from) {
      this.fromWhere = from
      if (this.isTimeout()) {
        this.fastTradeVisible = true
      } else {
        this.emitPublish()
      }
    },

    // 点击我知道了
    submitConfirm() {
      if (this.checked) {
        this.setNoRemind()
      }
      if (!this.agree) {
        this.$message.warning('请阅读并同意服务协议')
        return
      }
      this.fastTradeVisible = false
      this.emitPublish()
    },

    // 调用发布
    emitPublish() {
      this.$emit('agree')
      if (this.fromWhere === 'direction') {
        this.$emit('direction-publish')
      } else {
        this.$emit('publish')
      }
    },

    // 设置15天不再提醒
    setNoRemind() {
      Storage.set(FAST_TRADE_NO_REMIND_FOR_15, new Date().getTime())
    },

    // 确认是否到时间弹出光速交易确认框
    isTimeout() {
      const pastTime = Storage.get(FAST_TRADE_NO_REMIND_FOR_15)
      // 没有勾选直接返回true
      if (!pastTime) {
        return true
      }
      const dateSpace = getDateSpace(pastTime, new Date().getTime())
      const countdownTime = 15
      return dateSpace >= countdownTime
    },

    // 打开委托书
    openProtocol() {
      window.open(OSS_FILES_URL.LIGHT_SPEED_ORDER_AUTHORIZATION)
    },

    // 打开交易规则
    openRule() {
      window.open(FASTTRADE_URL)
    },

  }
}
</script>
