<!-- 瑕疵表单 -->
<style lang="scss" scoped>
.form-flex {
  display: flex;
  flex-wrap: wrap;

  // justify-content: space-between;
  margin-right: -14px;
}

.flaw-item {
  display: inline-flex;
  align-items: flex-start;
  margin-right: 8px;
  margin-bottom: 6px;

  .flaw {
    border: 1px solid $color-D9D9D9;
    border-radius: 2px;
    width: 98px;
    height: 40px;
    text-align: center;
    line-height: 40px;
    cursor: pointer;
    user-select: none;

    &.active {
      border-color: $--color-primary;
      color: $--color-primary;
      background: $--color-primary-hover;
    }

    &.disabled {
      border-color: $color-D9D9D9;
      color: $color-text-light;
      background: $color-F4F5F6;
      cursor: not-allowed;
    }
  }

  .el-input {
    margin-left: 8px;
    width: 98px;

    &.other-input {
      width: 204px;

      ::v-deep .el-input__inner {
        font-size: 14px;
        text-align: left;
      }
    }

    ::v-deep .el-input__inner {
      padding: 0 12px;
    }
  }
}
</style>

<template>
  <div class="form-flex">
    <div v-for="(item, index) in ticketFlawList" :key="item.name" class="flaw-item">
      <div
        :disabled="true"
        :class="['flaw', item.active && 'active', !item.active && identifyOne && 'disabled']"
        @click="!identifyOne && handleCheckbox(index)"
      >
        {{ item.name }}
      </div>
      <el-tooltip :content="getTooltipContent(item)" :disabled="disabledTooltip(item)" placement="bottom">
        <el-input
          v-if="item.showInput && (item.active || item.name === BACK_DEFECT_TYPE_NAME_MAP.OTHER.name)"
          v-model="item.input"
          :maxlength="10"
          :placeholder="item.name === BACK_DEFECT_TYPE_NAME_MAP.OTHER.name ? '瑕疵描述（最大长度为10）' : '次数'"
          :class="{'other-input': item.name === BACK_DEFECT_TYPE_NAME_MAP.OTHER.name}"
          :disabled="identifyOne || (!identifyOne && !item.active)"
          @input="item.name === BACK_DEFECT_TYPE_NAME_MAP.OTHER.name ? () => item.input = item.input : item.input = item.input.replace(/\D|^0/g, '')"
        >
          <span v-if="item.name !== BACK_DEFECT_TYPE_NAME_MAP.OTHER.name" slot="append">次</span>
        </el-input>
      </el-tooltip>
    </div>
  </div>
</template>

<script>
import { BACK_DEFECT_TYPE_NAME_MAP } from '@recognize/constant' // 票据瑕疵类型

export default {
  name: 'ticket-flaw-form',

  props: {
    defects: String, // 瑕疵字符串
    identifyOne: Boolean, // 是否单张识别,单张不允许设置
    defectSet: Boolean // 是否已经设置瑕疵
  },

  data() {
    return {
      BACK_DEFECT_TYPE_NAME_MAP,
      // 瑕疵情况
      ticketFlawList: [
        {
          name: '无瑕疵',
          active: false,
          value: '',
        },
        {
          name: BACK_DEFECT_TYPE_NAME_MAP.ABA.name, // 回出票人aba
          active: false,
          value: BACK_DEFECT_TYPE_NAME_MAP.ABA.id,
          keyName: BACK_DEFECT_TYPE_NAME_MAP.ABA.key,
        },
        {
          name: BACK_DEFECT_TYPE_NAME_MAP.ABCA.name, // 回出票人abca
          active: false,
          value: BACK_DEFECT_TYPE_NAME_MAP.ABCA.id,
          input: 1, // 次数
          showInput: BACK_DEFECT_TYPE_NAME_MAP.ABCA.isShowNum,
          keyName: BACK_DEFECT_TYPE_NAME_MAP.ABCA.key,
        },
        {
          name: BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_HUI_TOU.name, // 背书回头
          active: false,
          value: BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_HUI_TOU.id,
          input: 1, // 次数
          showInput: BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_HUI_TOU.isShowNum,
          keyName: BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_HUI_TOU.key,
        },

        {
          name: BACK_DEFECT_TYPE_NAME_MAP.HUI_SHOU_KUAN_REN.name, // 回收款人
          active: false,
          value: BACK_DEFECT_TYPE_NAME_MAP.HUI_SHOU_KUAN_REN.id,
          input: 1, // 次数
          showInput: BACK_DEFECT_TYPE_NAME_MAP.HUI_SHOU_KUAN_REN.isShowNum,
          keyName: BACK_DEFECT_TYPE_NAME_MAP.HUI_SHOU_KUAN_REN.key,
        },
        {
          name: BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_CHONG_FU.name, // 背书重复
          active: false,
          value: BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_CHONG_FU.id,
          input: 1, // 次数
          showInput: BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_CHONG_FU.isShowNum,
          keyName: BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_CHONG_FU.key
        },
        {
          name: BACK_DEFECT_TYPE_NAME_MAP.ABB.name, // abb
          active: false,
          value: BACK_DEFECT_TYPE_NAME_MAP.ABB.id,
          keyName: BACK_DEFECT_TYPE_NAME_MAP.ABB.key
        },
        {
          name: BACK_DEFECT_TYPE_NAME_MAP.ZHI_YA.name, // 质押
          active: false,
          value: BACK_DEFECT_TYPE_NAME_MAP.ZHI_YA.id,
          input: 1, // 次数
          showInput: BACK_DEFECT_TYPE_NAME_MAP.ZHI_YA.isShowNum,
          keyName: BACK_DEFECT_TYPE_NAME_MAP.ZHI_YA.key,
        },
        {
          name: BACK_DEFECT_TYPE_NAME_MAP.BAO_ZHENG.name, // 保证
          active: false,
          value: BACK_DEFECT_TYPE_NAME_MAP.BAO_ZHENG.id,
          keyName: BACK_DEFECT_TYPE_NAME_MAP.BAO_ZHENG.key,
        },
        {
          name: BACK_DEFECT_TYPE_NAME_MAP.SHANG_XIA_BU_YI_ZHI.name, // 不一致
          active: false,
          value: BACK_DEFECT_TYPE_NAME_MAP.SHANG_XIA_BU_YI_ZHI.id,
          keyName: BACK_DEFECT_TYPE_NAME_MAP.SHANG_XIA_BU_YI_ZHI.key,
        },
        {
          name: BACK_DEFECT_TYPE_NAME_MAP.HUI_GOU_TIE_XIAN.name, // 回购式贴现
          active: false,
          value: BACK_DEFECT_TYPE_NAME_MAP.HUI_GOU_TIE_XIAN.id,
          keyName: BACK_DEFECT_TYPE_NAME_MAP.HUI_GOU_TIE_XIAN.key,
        },
        {
          name: BACK_DEFECT_TYPE_NAME_MAP.OTHER.name, // 其他
          active: false,
          value: BACK_DEFECT_TYPE_NAME_MAP.OTHER.id,
          input: '', // 原因
          showInput: BACK_DEFECT_TYPE_NAME_MAP.OTHER.isShowNum,
          keyName: BACK_DEFECT_TYPE_NAME_MAP.OTHER.key,
        },
      ]
    }
  },

  watch: {
    // 监听接收的数据，用于回显
    defects: {
      handler(val) {
        // 根据数组长度判断是否有瑕疵
        if (val) {
          this.ticketFlawList[0].active = false
          const list = val.split('|')
          list.forEach(str => {
            const item = str.split('_')
            const key = item[0] || -1
            const num = item[1] || ''
            const index = this.ticketFlawList.findIndex(v => v.value === parseInt(key))
            if (index > -1) {
              this.ticketFlawList[index].showInput && (this.ticketFlawList[index].input = num)
              this.ticketFlawList[index].active = true
            }
          })
        } else {
          this.ticketFlawList[0].active = true
          // (this.defectSet || this.identifyOne) && (this.ticketFlawList[0].active = true)
        }
      },
      immediate: true
    },

    // 监听列表，用于返回父组件
    ticketFlawList: {
      handler(val) {
        let resObj = {}
        let defects = []
        val.forEach((item, index) => {
          if (item.keyName === BACK_DEFECT_TYPE_NAME_MAP.OTHER.key) {
            resObj[BACK_DEFECT_TYPE_NAME_MAP.OTHER.key] = item.active ? 1 : 0
            resObj[BACK_DEFECT_TYPE_NAME_MAP.OTHER.descKey] = item.input
          } else if (index !== 0) {
            resObj[item.keyName] = item.active ? parseInt(item.input) || 1 : 0
          }
          const num = item.keyName === BACK_DEFECT_TYPE_NAME_MAP.OTHER.key ? item.input : item.input || 1
          index !== 0 && item.active && defects.push(`${item.value}_${num}`)
        })
        // 返回两种数据格式
        this.$emit('change', resObj, defects.join('|'))
      },
      deep: true,
      immediate: true
    }
  },

  methods: {
    // 点击瑕疵
    handleCheckbox(index) {
      !this.defectSet && this.$emit('change-defect-set', true) // 更新设置状态
      // 当前选中'无瑕疵', 则其他默认不选中。 当前选择其它时，默认'无瑕疵'为不选中状态
      if (index === 0) {
        this.ticketFlawList.slice(1, this.ticketFlawList.length).forEach(item => {
          item.active = false
          if (item.name === BACK_DEFECT_TYPE_NAME_MAP.OTHER.name) {
            item.input && (item.input = '')
          } else {
            item.input && (item.input = 1)
          }
        })
        this.ticketFlawList[index].active = true
      } else {
        this.ticketFlawList[0].active = false
        this.ticketFlawList[index].active = !this.ticketFlawList[index].active
        // 取消置空
        if (index === this.ticketFlawList.length - 1) {
          !this.ticketFlawList[index].active && (this.ticketFlawList[index].input = '')
        } else {
          !this.ticketFlawList[index].active && this.ticketFlawList[index].input && (this.ticketFlawList[index].input = 1)
        }
        const allCancel = this.ticketFlawList.every(v => !v.active) // 全部为空时取消更新设置
        allCancel && this.$emit('change-defect-set', false)
      }
    },
    getTooltipContent(item) {
      return this.identifyOne && item.name === BACK_DEFECT_TYPE_NAME_MAP.OTHER.name && item.input ? `其他[${item.input}]` : ''
    },
    disabledTooltip(item) {
      // 单张识别&&其他选项&&瑕疵描述有值，才会展示tooltip提示，其他不展示
      return !(this.identifyOne && item.name === BACK_DEFECT_TYPE_NAME_MAP.OTHER.name && item.input)
    }
  }
}
</script>
