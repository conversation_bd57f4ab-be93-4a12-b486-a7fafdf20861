<!-- 自定义表单 -->
<style lang="scss" scoped>
  .form-item {
    margin-top: 10px;

    .label {
      display: flex;
      justify-content: space-between;
      margin-bottom: 2px;
      font-size: 14px;
      color: $color-text-secondary;
      line-height: 22px;
    }

    .star-icon {
      display: inline-block;
      margin-top: 6px;
      margin-right: 2px;
      font-size: 18px;
      color: $color-warning;
      vertical-align: top;
      line-height: 14px;
    }

    .label-right {
      color: $color-008489;
    }
  }
</style>

<template>
  <div class="form-item" :style="`text-align:${align}`">
    <div class="label" :style="flexAlign">
      <div class="label-left">
        <span v-if="required" class="star-icon">*</span>
        <slot name="label">{{ label }}</slot>
      </div>
      <div class="label-right">
        <slot name="label-right" />
      </div>
    </div>
    <div class="value">
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  name: 'form-item',
  props: {
    label: String, // 标签文本
    // 是否必填，用于显示星号
    required: {
      default: false,
      type: Boolean
    },
    // 对齐方式，label 和 value 都对齐
    align: {
      default: 'left',
      type: String
    }
  },

  computed: {
    flexAlign() {
      if (this.align === 'center') {
        return 'justify-content: center'
      } else if (this.align === 'right') {
        return 'justify-content: flex-end'
      } else {
        return ''
      }
    }
  }
}
</script>
