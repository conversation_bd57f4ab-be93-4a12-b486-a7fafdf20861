 <!-- 批量修改弹窗 -->
<style lang="scss" scoped>
::v-deep {
  .el-dialog__body {
    padding: 20px 20px 0;
    color: $color-text-primary;
    background-color: $color-F2F2F2;
  }

  .el-dialog__footer {
    color: $color-text-primary;
    background-color: $color-F2F2F2;
  }

  .el-input__inner {
    font-size: 14px;
    text-align: right;
  }
}

.block-box {
  margin-bottom: 12px;
  padding: 12px 16px;
  background-color: $color-FFFFFF;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin-top: 12px;

  .item {
    margin-bottom: 12px;
    width: 50%;
  }

  .switch-box {
    @include flex-vc;

    .el-switch {
      margin-right: 10px;
    }

    .el-input {
      margin-left: 10px;
      width: 140px;
    }
  }

  .split-box {
    @include flex-vc;

    .el-input {
      width: 140px;
    }

    .margin-right-8 {
      margin-right: 8px;
    }
  }
}

.subtitle {
  margin-bottom: 4px;
  color: $color-text-secondary;

  @include flex-vc;

  .svg-icon {
    margin-left: 5px;
  }

  .black-list-box {
    margin-bottom: -8px;
  }
}

@mixin tag-disabled() {
  border-color: $color-D9D9D9;
  color: $color-text-primary;
  background: $color-F4F5F6;
}

.acceptor-remarks-select {
  width: 100%;

  ::v-deep {
    .el-input__inner {
      width: 100% !important;
      text-align: left !important;
    }

    .el-tag {
      margin-left: 4px;
      border-color: $--color-primary;
      border-radius: 2px;
      padding: 0 6px;
      font-size: 12px;
      color: $--color-primary;
      background-color: $color-FFFFFF;

      &:first-child {
        margin-left: 12px;
      }

      .el-select__tags-text {
        flex: 1;

        @include ellipsis;
      }

      .el-icon-close {
        right: -4px;
        color: $--color-primary;
        background: transparent;
        transform: scale(1.2);
      }

      &:hover {
        background-color: #FFEFEF;
      }
    }

    .el-select__input {
      margin-left: 12px;
    }
  }

  &.no-close-tag1 {
    ::v-deep {
      .el-tag:first-child {
        @include tag-disabled;

        .el-icon-close {
          display: none;
        }
      }
    }
  }

  &.no-close-tag2 {
    ::v-deep {
      .el-tag:first-child,
      .el-tag:nth-child(2) {
        @include tag-disabled;

        .el-icon-close {
          display: none;
        }
      }
    }
  }
}

.split-change {
  ::v-deep.el-radio-button {
    margin-right: 8px;

    .el-radio-button__inner { // 修改按钮样式
      border: 0 !important;
      border-radius: 11px;
      width: 40px;
      height: 22px;
      color: $color-FFFFFF;
      background: $color-D9D9D9;
    }

    .el-radio-button__orig-radio:checked + .el-radio-button__inner {// 修改按钮激活样式
      border: 0 !important;
      color: #FFFFFF;
      background-color: $--color-primary;
    }
  }
}

.int-multiple {
  border: 1px solid $--color-primary;
  border-radius: 4px;
  padding: 2px 8px;
}

.contacts-box {
  width: 60%;

  ::v-deep {
    .el-input__inner {
      font-size: 14px;
      text-align: left;
    }
  }
}
</style>

<template>
  <el-dialog
    title="设置"
    :visible.sync="dialogVisible"
    style-type="border"
    width="964px"
    append-to-body
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <div class="block-box">
      <div class="g-title">瑕疵（可多选）</div>
      <div class="row">
        <TicketFlawForm
          v-if="newData"
          :identify-one="form.discernType !== IDENTIFY_TYPE.MULTIPLE.id"
          :defects="form.defects"
          :defect-set="form.defectSet"
          @change="handleChangeFlaw"
          @change-defect-set="handleDefectSet"
        />
      </div>
    </div>
    <div class="block-box">
      <div class="g-title">交易设置</div>
      <div class="row">
        <div class="item">
          <div class="subtitle">议价</div>
          <div class="switch-box">
            <el-switch
              v-model="form.bargaining"
              @change="handleBargainChange"
            />
            <template v-if="form.bargaining">
              接受议价，请输入议价上限
              <el-input
                v-model="form.bargainingLimit"
                type="number"
                placeholder="每十万扣款"
                :number-format="{maxDecimalLength: 2, leadingZero: false, negative: false, maxLength: 8}"
              >
                <span slot="append">元</span>
              </el-input>
            </template>
            <template v-else>
              不接受议价
            </template>
          </div>
        </div>
        <div class="item">
          <div class="subtitle">收款人打码</div>
          <div class="switch-box">
            <el-switch
              v-model="form.receiverMosaic"
            />
            开启后，订单中展示的正面图片，将自动为收款人打码。
          </div>
        </div>
        <div class="item">
          <div class="subtitle">出票人打码</div>
          <div class="switch-box">
            <el-switch
              v-model="form.drawerMosaic"
            />
            开启后，订单中展示的正面图片，将自动为出票人打码。
          </div>
        </div>
        <div class="item" />
        <div v-if="draftType" class="item">
          <div class="subtitle">拆分接单</div>
          <div class="switch-box">
            <el-switch
              v-model="form.splitFlag"
              :active-value="1"
              :inactive-value="0"
              :disabled="splitDisabled()"
              @change="splitChange"
            />
            <template v-if="form.splitFlag">
              接受资方拆分金额接单，需设置允许拆分接单的金额
            </template>
            <template v-else>
              不接受拆分，资方无法自由选择接单金额
            </template>
          </div>
        </div>
        <div v-if="form.splitFlag" class="item">
          <el-radio-group
            v-model="form.splitMethod"
            style="margin-bottom: 12px;"
            size="mini"
            class="radio-mini"
          >
            <el-radio-button :label="0">区间拆分</el-radio-button>
            <el-radio-button :label="1">定额拆分</el-radio-button>
          </el-radio-group>
          <div v-if="!form.splitMethod" class="split-box">
            <el-input
              v-model="form.splitAmtMin"
              class="margin-right-8"
              placeholder="最低可拆分"
              type="number"
              :number-format="{maxDecimalLength: 2, leadingZero: false, negative: false, maxIntegerLength: 8}"
            >
              <span slot="append">元</span>
            </el-input>
            <span class="margin-right-8">-</span>
            <el-input
              v-model="form.splitAmtMax"
              class="split-input"
              placeholder="最高可拆分"
              type="number"
              :number-format="{maxDecimalLength: 2, leadingZero: false, negative: false, maxIntegerLength: 8}"
            >
              <span slot="append">元</span>
            </el-input>
          </div>
          <div v-if="form.splitMethod" class="split-change">
            <el-radio-group
              v-model="form.splitAmtIntSelect"
              size="mini"
              class="radio-mini"
              @change="splitAmtIntChange"
            >
              <el-radio-button :label="1">1万</el-radio-button>
              <el-radio-button :label="5">5万</el-radio-button>
              <el-radio-button :label="10">10万</el-radio-button>
            </el-radio-group>
            <el-input
              v-model="form.splitAmtInt"
              placeholder="拆分金额"
              class="text-right"
              size="small"
              :width="110"
              type="number"
              :number-format="{
                decimal: false,
                negative: false,
                leadingZero: false,
                maxDecimalLength: 0,
                maxIntegerLength: 4,
              }"
              @input="handleInputSplitAmount"
              @change="handleChangeSplitAmount"
            >
              <span slot="append">万</span>
            </el-input>
            <el-tooltip
              placement="top"
              style="margin-left: 10px;"
              content="勾选后，代表可按设置金额的整数倍拆分接单"
            >
              <el-checkbox
                v-model="form.intMultiple"
                :true-label="1"
                :false-label="0"
                class="int-multiple"
              >
                整数倍
              </el-checkbox>
            </el-tooltip>
          </div>
        </div>
      </div>
      <div class="black-list-box">
        <div class="subtitle">
          资方所在地黑名单
          <el-tooltip
            effect="dark"
            placement="top"
          >
            <div slot="content">
              添加黑名单地区后，对应地区的用户将看不到您发布的票据。
            </div>
            <icon class="icon icon-question" type="chengjie-wenti" />
          </el-tooltip>
        </div>
        <!-- 黑名单列表 -->
        <BlackList v-if="newData" :key="form.draftNo" v-model="form.blackRegionList" />
      </div>
      <div class="row">
        <div class="item">
          <div class="subtitle">承兑人备注（最多选择 3 条）</div>
          <div>
            <el-select
              v-model="form.acceptorNotes"
              multiple
              placeholder="请准确选择标签，错误标签可能会对您的信誉产生影响"
              size="small"
              popper-class="default"
              :multiple-limit="3"
              :class="['acceptor-remarks-select', customSelectClass]"
            >
              <el-option
                v-for="item in acceptorRemarksList"
                :key="item.id"
                :label="item.label"
                :value="item.label"
              />
            </el-select>
          </div>
        </div>
      </div>
    </div>
    <!--
      <div class="block-box">
      <ContactItem ref="contactItemRef" class="contacts-box" @change-data="onChangeMobile" />
      </div>
    -->
    <!--
      <ContactSettingField
      v-if="dialogVisible"
      ref="ContactSettingField"
      v-model="form.mobile"
      class="contact-field"
      :icon-size="23"
      :is-border="true"
      @close="closeModel"
      >
      <template #label>
      <span class="g-title">联系方式</span>
      </template>
      </ContactSettingField>
    -->
    <div v-if="corpInfo.yilianPayOpen === 1" class="block-box">
      <div class="g-title">
        <span>贸易合同及发票</span>
        <span>&nbsp;</span>
        <el-tooltip popper-class="other-tip-custom">
          <div slot="content">
            <p>上传您与出票人的贸易背景或您<br>与上一手企业的贸易合同及发票</p>
          </div>
          <icon
            type="chengjie-wenti"
            :size="23"
            color-theme="primary"
          />
        </el-tooltip>
      </div>
      <div class="row" />
      <el-row type="flex" :gutter="8">
        <el-col :span="7">
          <FormItem label="贸易合同" :require="isMoreThan500W">
            <ImgUpload
              v-model="form.tradeContractUrl"
              :size-limit="20"
              :dir="OSS_DIR.DRAFT"
              :height="60"
              accept="application/pdf"
              is-pdf
            >
              <div slot="empty">
                <div>点击或拖拽PDF至此上传贸易合同</div>
                <div>（支持pdf格式，不超过20M）</div>
              </div>
            </ImgUpload>
          </FormItem>
        </el-col>
        <el-col :span="7">
          <FormItem label="发票" :require="isMoreThan500W">
            <ImgUpload
              v-model="form.invoiceUrl"
              :size-limit="20"
              :dir="OSS_DIR.DRAFT"
              :height="60"
              accept="application/pdf"
              is-pdf
            >
              <div slot="empty">
                <div>点击或拖拽PDF至此上传发票</div>
                <div>（支持pdf格式，不超过20M）</div>
              </div>
            </ImgUpload>
          </FormItem>
        </el-col>
      </el-row>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="large" @click="dialogVisible = false">取消</el-button>
      <el-button type="secondary" size="large" @click="handleConfirm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import BlackList from './black-list.vue' // 黑名单列表
import TicketFlawForm from '../components/ticket-flaw-form.vue' // 瑕疵
// import ContactSetting from '../components/contact-setting.vue'
import {
  IDENTIFY_TYPE, // 识别类型
  ACCEPTOR_TYPE,
  OSS_DIR
} from '@recognize/constant'
import commonApi from '@recognize/apis/common'
import ImgUpload from '@/views/components/common/img-upload/img-upload.vue'
import { mapGetters } from 'vuex'
import FormItem from '../components/form-item.vue' // 自定义表单
import { wan2yuan } from '@/common/js/number'
// import ContactItem from '../components/contact-item.vue' // 拆分规则
// import ContactSettingField from '@/views/pages/setting/components/contact-setting-field.vue'

export default {
  name: 'edit-dialog',

  components: {
    BlackList, // 黑名单列表
    TicketFlawForm, // 瑕疵
    // ContactSetting,
    ImgUpload,
    FormItem, // 自定义表单
    // ContactSettingField
    // ContactItem,
  },

  props: {
    // 是否包含亿联银行支付渠道
    hasYlBankPayment: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      OSS_DIR,
      dialogVisible: false, // 是否打开弹窗
      form: {
        bargaining: 0, // 是否接受议价，0-否、1-是
        drawerMosaic: false, // 出票人打码
        receiverMosaic: false, // 收票人打码
        bargainingLimit: '', // 议价上限值
        blackRegionList: [], // 屏蔽的省市列表 ,Region
        defects: '', // 瑕疵
        discernType: '', // 识别类型
        acceptorNotes: [], // 承兑人标签
        // mobile: [] // 联系方式
      },
      defectObj: {},
      newData: false, // 刷新瑕疵数据
      IDENTIFY_TYPE, // 识别类型
      acceptorRemarksList: [], // 承兑人备注列表
      customSelectClass: '', // 承兑人备注下拉框class 使用class来控制已选标签是否可删除
      draftType: 0, // 票据类型
    }
  },
  computed: {
    ...mapGetters('user', {
      corpInfo: 'corpInfo',
    }),
    // 是否是大于500万的票据
    isMoreThan500W() {
      const limit = 5000000
      return this.form?.draftAmount > limit
    },
  },
  watch: {
    'form.splitFlag': {
      handler(val) {
        if (val) {
          this.form.splitMethod = 1
        }
      },
    },
  },
  methods: {
    // 切换显示隐藏
    toggle(data) {
      this.draftType = data.draftType
      this.newData = false
      this.$nextTick().then(() => {
        this.newData = true
      })
      this.dialogVisible = !this.dialogVisible
      const obj = JSON.parse(JSON.stringify(data))
      this.form = obj
      if (data.acceptorType === ACCEPTOR_TYPE.SHANG_PIAO.id) {
        this.getAcceptorCommercialLabel(data.acceptorName)
      }
      this.getAcceptorCommonLabel(data.acceptorType)
      if (data.splitAmtMin) {
        this.form.splitAmtMin = data.splitAmtMin
      }
      if (data.splitAmtMax) {
        this.form.splitAmtMax = data.splitAmtMax
      }
    },

    getMinSplitConfig() {
      return this.$store.state.common.newVersionDraftSplit || 10000
    },

    splitChange(val) {
      const { draftAmount } = this.form
      if (val && draftAmount < this.getMinSplitConfig()) {
        this.form.splitFlag = 0
        this.$message.warning(`票面金额小于${this.getMinSplitConfig()}元的票不可拆分`)
      }
    },

    splitAmtIntChange(val) {
      this.form.splitAmtInt = val
    },

    handleInputSplitAmount() {
      if (this.form.splitAmtInt === '0') {
        this.form.splitAmtInt = ''
      }
      this.form.splitAmtIntSelect = this.form.splitAmtInt
    },

    handleChangeSplitAmount(val) {
      if (!val) {
        this.form.splitAmtInt = 1
        this.form.splitAmtIntSelect = 1
        this.$message.warning('拆分金额必填！')
      }
    },

    splitDisabled() {
      const { subTicketStart, subTicketEnd } = this.form
      return subTicketStart === '0' && subTicketEnd === '0'
    },

    // 选择完瑕疵 两种数据格式
    handleChangeFlaw(obj, defects) {
      this.defectObj.ticketFlawobj = obj
      this.defectObj.defects = defects
    },

    // 更新瑕疵设置状态
    handleDefectSet(val) {
      this.defectObj.defectSet = val
    },

    handleBargainChange(val) {
      !val && (this.form.bargainingLimit = '')
    },

    // 获取通用票标签列表
    async getAcceptorCommonLabel(accepterType) {
      const DRAFT_TYPE_CODE = {
        SILVER_NOTE: 1, // 银票
        COMMERCIAL_TICKET: 2, // 商票
        FINANCIAL_TICKET: 3, // 财票
      }
      let draftType = null

      switch (accepterType) {
        case ACCEPTOR_TYPE.CAI_PIAO.id:
          draftType = DRAFT_TYPE_CODE.FINANCIAL_TICKET
          break
        case ACCEPTOR_TYPE.SHANG_PIAO.id:
          draftType = DRAFT_TYPE_CODE.COMMERCIAL_TICKET
          break
        default:
          draftType = DRAFT_TYPE_CODE.SILVER_NOTE
          break
      }
      const res = await commonApi.getAcceptorCommonLabel({ draftType })
      this.acceptorRemarksList = res.rowList
    },

    // 获取承兑人商票标签列表
    async getAcceptorCommercialLabel(acceptorName) {
      this.form.acceptorNotes = []
      const data = await commonApi.getAcceptorCommercialLabel({ acceptorName })
      if (data.rowList && data.rowList.length) {
        if (data.rowList[0].firstLabel) {
          this.customSelectClass = 'no-close-tag1'
          this.form.acceptorNotes.push(data.rowList[0].firstLabel)
        }
        if (data.rowList[0].secondLabel) {
          this.customSelectClass = 'no-close-tag2'
          this.form.acceptorNotes.push(data.rowList[0].secondLabel)
        }
      }
    },

    // 点击确定
    handleConfirm() {
      if (this.isMoreThan500W && this.hasYlBankPayment && (!this.form.tradeContractUrl || !this.form.invoiceUrl)) {
        this.$message.info('选择智联通支付渠道发布500万以上票据必须上传贸易合同和发票')
        return
      }
      if (!this.validSplitAmt(this.form)) {
        return
      }

      this.dialogVisible = false
      this.$emit('confirm', Object.assign(this.form, this.defectObj))
    },

    // 校验输入值
    validSplitAmt(obj) {
      if (obj.splitFlag) {
        if (obj.splitMethod) {
          if (!obj.splitAmtInt) {
            this.$message.info('请输入拆分金额')
            return false
          } else {
            let { draftAmount } = obj
            let min = wan2yuan(obj.splitAmtInt)
            // eslint-disable-next-line max-depth
            if (min < this.getMinSplitConfig()) {
              this.$message.info(`拆分金额需大于或等于${this.getMinSplitConfig()}元`)
              return false
            } else if (min > draftAmount) {
              this.$message.info('请输入小于票面金额的正整数！')
              return false
            }
          }
        } else {
          if (!obj.splitAmtMin || !obj.splitAmtMax) {
            this.$message.info('拆分金额必须填写')
            return false
          }
          let { draftAmount } = obj
          let min = Number(obj.splitAmtMin)
          let max = Number(obj.splitAmtMax)
          if (draftAmount > this.getMinSplitConfig()) {
            if (min < this.getMinSplitConfig()) {
              this.$message.info(`最低可拆分金额需大于或等于${this.getMinSplitConfig()}元`)
              return false
            } else if (min > max) {
              this.$message.info('最高可拆分金额需大于或等于最低可拆分金额')
              return false
            } else if (max > draftAmount) {
              this.$message.info('最高可拆分金额不能大于票面金额')
              return false
            }
          } else if (min !== draftAmount) {
            this.$message.info(`票面金额小于${this.getMinSplitConfig()}元时,最低可拆分金额需等于票面金额`)
            return false
          } else if (max !== draftAmount) {
            this.$message.info(`票面金额小于${this.getMinSplitConfig()}元时,最高可拆分金额需等于票面金额`)
            return false
          }
        }
      }

      return true
    },

    // 弹窗关闭
    handleClosed() {
      this.acceptorRemarksList = []
      this.customSelectClass = ''
    },
    closeModel() {
      // 网页端关闭 客户端不关
      if (!this.$ipc) {
        this.handleClosed()
        this.dialogVisible = false
        this.$emit('close')
      }
    }

    // // 手机号修改
    // onChangeMobile(data) {
    //   this.form.mobile = data
    // },
  }
}
</script>
