<!-- 发布结果状态弹窗 -->
<style lang="scss" scoped>
::v-deep {
  .el-dialog__body {
    padding: 20px 33px;
    color: $color-text-primary;
    background-color: $color-FFFFFF;
  }

  .el-dialog__footer {
    color: $color-text-primary;
    background-color: $color-FFFFFF;
  }

  .el-input__inner {
    font-size: 14px;
    text-align: right;
  }
}

.title {
  @include flex-vc;

  margin-bottom: 8px;
  font-size: 18px;
  font-weight: bold;
}

.loading-icon {
  position: relative;
  display: inline-block;
  margin-right: 16px;
  width: 20px;
  height: 20px;

  &::before {
    position: absolute;
    top: 0%;
    left: 0%;
    width: 100%;
    height: 100%;
    background-size: 100%;
    content: "";
    background-image: url("https://oss.chengjie.red/web/imgs/recognize/circle.png");
    translate: all .5s;
    animation: around 1s linear infinite;
  }

  &::after {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 6px;
    height: 8.5px;
    background-size: 100%;
    transform: translateX(-50%) translateY(-50%);
    content: "";
    background-image: url("https://oss.chengjie.red/web/imgs/recognize/lightning.png");
  }
}

.icon {
  margin-right: 16px;
  width: 20px;
  height: 20px;

  &.success {
    color: $color-008489;
  }

  &.both {
    color: $color-assist3;
  }

  &.fail {
    color: $color-warning;
  }
}

@keyframes around {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.content {
  padding-left: 35px;

  .red {
    @include bold;
  }

  .item-box {
    margin-bottom: 4px;
  }

  .item {
    .label {
      font-weight: bold;
    }
  }

  .wait-text {
    margin-bottom: 88px;
  }
}

.tip-out {
  overflow-y: auto;
  max-height: 535px;
}
</style>

<template>
  <el-dialog
    :visible.sync="dialogVisible"
    width="536px"
    append-to-body
    :close-on-click-modal="false"
    :show-close="false"
    :destroy-on-close="true"
  >
    <header class="title">
      <i v-if="!info" class="loading-icon" />
      <template v-else>
        <icon v-if="info.successCount && !info.failCount" type="chengjie-check-circle" class="icon success" />
        <icon v-else-if="info.successCount && info.failCount" type="sdicon-info-circle" class="icon both" />
        <icon v-else-if="!info.successCount && info.failCount" type="chengjie-close-circle" class="icon fail" />
      </template>
      {{ info ? '提示' : title }}
    </header>
    <main class="content">
      <div v-if="!info" class="wait-text">
        正在发布中，请耐心等待...
      </div>
      <div v-else-if="info.successCount && !info.failCount">
        成功发布了 <span class="red">{{ info.successCount }}</span> 张票据。
      </div>
      <div v-else-if="info.successCount && info.failCount">
        本次发布  <span class="red">{{ info.successCount + info.failCount }}</span> 张票据，成功发布了 <span class="red">{{ info.successCount }}</span> 张票据，以下 <span class="red">{{ info.failCount }}</span>张票据发布失败:
      </div>
      <div v-else-if="!info.successCount && info.failCount">
        以下 <span class="red">{{ info.failCount }}</span>张票据发布失败:
      </div>
      <div v-if="info && info.failList" class="tip-out">
        <div v-for="(item, index) in info.failList" :key="index" class="item-box">
          <div class="item">
            <label class="label">票号：</label>
            <span class="text">{{ item.draftNo }}</span>
          </div>
          <div class="item">
            <label class="label">失败原因：</label>
            <span class="text">{{ reasonFormat(item) }} </span>
          </div>
        </div>
      </div>
    </main>
    <span v-if="info" slot="footer" class="dialog-footer">
      <el-button type="secondary" @click="handleConfirm">我知道了</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  TICKET_STATUS_CHANGE // 发布后修改票据状态通知
} from '@recognize/ipc-event-constant'

export default {
  name: 'result-status-dialog',
  props: {
    title: {
      type: String,
      default: '批量发布中'
    }
  },
  data() {
    return {
      dialogVisible: false, // 是否打开弹窗
      info: '',
    }
  },
  methods: {
    close() {
      this.dialogVisible = false
    },
    // 切换显示隐藏
    init(data) {
      this.dialogVisible = true
      this.info = data
    },
    // 点击确定
    handleConfirm() {
      this.dialogVisible = false
      this.$event.emit(TICKET_STATUS_CHANGE)
      this.$emit('confirm')
    },

    // 原因格式修改
    reasonFormat(item) {
      const ORDER_EXIT_ACCEPT_RISK = 1015 // 存在承兑风险
      const PAY_CHANNEL_BUYER_NOT_SUPPORT = 1016 // 定向交易对手不支持所选支付方式
      if (item.failCode === ORDER_EXIT_ACCEPT_RISK) {
        return '票据存在承兑风险，如有疑问请联系客服经理。'
      }
      if (item.failCode === PAY_CHANNEL_BUYER_NOT_SUPPORT) {
        return '交易对手暂未开通该支付渠道，无法发起订单交易。'
      }
      return item.failReason
    }
  }
}
</script>
