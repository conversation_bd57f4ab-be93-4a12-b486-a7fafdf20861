<!-- 支付渠道 -->
<style lang="scss" scoped>
.pay-radio {
  display: flex;
  justify-content: left;
  align-items: center;

  .pay-btn {
    position: relative;
    margin-right: 8px;
    border: 1px solid $color-D9D9D9;
    border-radius: 2px;
    width: 98px;
    height: 40px;
    font-size: 14px;
    text-align: center;
    color: $color-text-primary;
    background: $color-FFFFFF;
    line-height: 40px;
    cursor: pointer;
    flex: none;
  }

  .pay-btn-active {
    // fix:默认禁用所有支付渠道--背景、字体、边框颜色样式穿透
    border-color: $--color-primary !important;
    font-weight: 500;
    color: $--color-primary !important;
    background: $--color-primary-hover !important;
  }

  .pay-btn-disabled {
    border: 1px solid $color-D9D9D9;
    color: $color-text-light;
    background: $color-F4F5F6;
    pointer-events: none;
    cursor: not-allowed;
  }
}

.payment-channel-new-tag {
  position: absolute;
  top: -16px;
  right: -1px;

  @include new-icon;
}

.custom-disabled-btn-cls {
  position: absolute;
  top: 0;
  left: 0;
  border: 1px solid $color-D9D9D9;
  border: none;
  width: 100%;
  height: 40px;
  height: 38px;
  color: $color-text-light;
  background: $color-F4F5F6;
  cursor: not-allowed;
}
</style>

<template>
  <div class="pay-radio">
    <!-- fix:默认禁用所有支付渠道:class="[!item.isOpen && 'pay-btn-disabled', payType.includes(item.value) && 'pay-btn-active']" -->
    <div
      v-for="(item, index) in noBanChannelNameList"
      :key="index"
      class="pay-btn"
      :class="[item.value === PAYMENT_CHANNEL.ZHI_FU_YI_LIAN.id ? '' : 'pay-btn-disabled', (payType.includes(item.value) && item.isOpen && item.value !== PAYMENT_CHANNEL.ZHI_FU_YI_LIAN.id) && 'pay-btn-active']"
      @click="choosePayType(item.value)"
    >
      <el-tooltip
        v-if="item.value === PAYMENT_CHANNEL.ZHI_FU_YI_LIAN.id"
        class="item"
        effect="dark"
        content="不支持此支付渠道"
        placement="top"
      >
        <div class="custom-disabled-btn-cls">{{ item.name }}</div>
        <!-- <span> {{ item.name }}</span> -->
      </el-tooltip>
      <template v-else>
        {{ item.name }}
        <span v-if="item.hasNewTag" class="payment-channel-new-tag">NEW</span>
      </template>
    </div>
  </div>
</template>

<script>
// 支付类型
import {
  ACCOUNT_PAY_TYPE, // 支付类型
  BAN_STATUS, // 禁用状态
} from '@recognize/constant'
import { PAYMENT_CHANNEL } from '@/constant'

export default {
  name: 'pay-radio',

  props: {
    type: Array, // 选择的支付渠道
    apiPayList: Array, // 接口返回的支付渠道列表
    isNewDraft: Boolean, // 是否新票/是否包含新票

  },

  data() {
    return {
      PAYMENT_CHANNEL,
      payType: [],
      // 支付渠道
      payList: [
        // 智付亿联
        // {
        //   name: ACCOUNT_PAY_TYPE.ZHI_FU_YI_LIAN.name,
        //   value: ACCOUNT_PAY_TYPE.ZHI_FU_YI_LIAN.id,
        //   isOpen: false,
        //   keyName: 'acceptYillionpay',
        //   hasChannel: false,
        // },
        // 智付合利宝
        {
          name: PAYMENT_CHANNEL.ZHI_FU_HE_LI_BAO.name,
          value: PAYMENT_CHANNEL.ZHI_FU_HE_LI_BAO.id,
          isOpen: false,
          keyName: PAYMENT_CHANNEL.ZHI_FU_HE_LI_BAO.key,
          hasChannel: false,
        },
        // 智付连连
        // {
        //   name: ACCOUNT_PAY_TYPE.ZHI_FU_LIAN_LIAN.name,
        //   value: ACCOUNT_PAY_TYPE.ZHI_FU_LIAN_LIAN.id,
        //   isOpen: false,
        //   keyName: 'acceptLianlianpay',
        //   hasChannel: false,
        // },
        // 智付百信
        // {
        //   name: ACCOUNT_PAY_TYPE.ZHI_FU_BAI_XIN.name,
        //   value: ACCOUNT_PAY_TYPE.ZHI_FU_BAI_XIN.id,
        //   isOpen: false,
        //   keyName: 'acceptAipay',
        //   hasChannel: false,
        // },
        // 智付众邦
        // {
        //   name: ACCOUNT_PAY_TYPE.ZHI_FU_ZHONG_BANG.name,
        //   value: ACCOUNT_PAY_TYPE.ZHI_FU_ZHONG_BANG.id,
        //   isOpen: false,
        //   keyName: 'acceptZbankpay',
        //   hasChannel: false
        // },
        // 亿联银行
        {
          name: PAYMENT_CHANNEL.YI_LIAN_BANK.name,
          value: PAYMENT_CHANNEL.YI_LIAN_BANK.id,
          isOpen: false,
          keyName: PAYMENT_CHANNEL.YI_LIAN_BANK.key,
          hasChannel: false
        },
        // 智付E+
        {
          name: PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.name,
          value: PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.id,
          isOpen: false,
          keyName: PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.key,
          hasChannel: false,
          hasNewTag: PAYMENT_CHANNEL.ZHI_FU_YI_LIAN_PLUS.hasNewTag,
        },
        // 智付邦+
        {
          name: PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.name,
          value: PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.id,
          isOpen: false,
          keyName: PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.key,
          hasChannel: false,
          hasNewTag: PAYMENT_CHANNEL.ZHI_FU_ZHONG_BANG_PLUS.hasNewTag,
        },
        {
          name: PAYMENT_CHANNEL.YL_PLUS.name,
          value: PAYMENT_CHANNEL.YL_PLUS.id,
          isOpen: false,
          keyName: PAYMENT_CHANNEL.YL_PLUS.key,
          hasChannel: false,
          hasNewTag: PAYMENT_CHANNEL.YL_PLUS.hasNewTag,
        }
      ],
    }
  },

  computed: {
    noBanChannelNameList() {
      return this.payList.filter(i => i.hasChannel)
    }
  },

  watch: {
    // 绑定传入的支付值
    type: {
      handler(val) {
        if (val?.length) {
          this.payType = JSON.parse(JSON.stringify(val))
        }
      },
      immediate: true
    },
    // 监听支付改变，传值给父组件
    payType: {
      handler(n) {
        this.setCheckedPayChannel(n)
      },
      deep: true,
      immediate: true
    }
  },
  async created() {
    await this.getPayAccountList()
  },
  methods: {
    setCheckedPayChannel(n) {
      const resObj = {
        acceptYillionpay: 0, // 是否支持亿联支付1-是0-否
        acceptHelipay: 0, // 是否支持合利宝支付
        acceptLianlianpay: 0, // 是否支持连连支付
        acceptAipay: 0, // 是否支持百信支付
        acceptZbankpay: 0, // 是否支持众邦
        acceptYlpay: 0, // 是否支持亿联银行
        acceptJdYlpay: 0, // 是否支持智付E+
        acceptZbankPlus: 0, // 是否支持智付邦+
        acceptYiPlusPlus: 0 // 是否支持E++
      }
      const noBanChannelNameList = []
      // 限制新票发布使用智付E+
      // if (this.isNewDraft) {
      //   n = n.filter(value => value !== ACCOUNT_PAY_TYPE.ZHI_FU_YI_LIAN_PLUS.id)
      // }
      n.forEach(pay => {
        const item = this.payList.find(v => v.value === pay)
        if (item) {
          item.hasChannel && item.isOpen && (resObj[item.keyName] = 1) // 该渠道不被禁用的 才选被选
          noBanChannelNameList.indexOf(item.name) === -1 && item.hasChannel && noBanChannelNameList.push(item.name) // 没有禁用的渠道列表
        }
      })
      resObj.payChannelName = noBanChannelNameList.join(',')
      this.$emit('change', resObj)
    },
    choosePayType(index) {
      if (index === PAYMENT_CHANNEL.ZHI_FU_YI_LIAN.id) return // 禁用智付E
      if (this.payType.includes(index)) {
        const payTypeIndex = this.payType.findIndex(item => item === index)
        this.payType.splice(payTypeIndex, 1)
      } else {
        this.payType.push(index)
      }
    },
    // 获取支付渠道开通列表
    getPayAccountList() {
      // 因业务原因，关闭的支付渠道
      const disabledChannel = [ACCOUNT_PAY_TYPE.ZHI_FU_BAI_XIN.id, ACCOUNT_PAY_TYPE.ZHI_FU_LIAN_LIAN.id]

      let data = this.apiPayList || []
      if (data.length === 0) {
        data = this.$store.state.user.paymentAccountList
      }
      data.forEach(account => {
        const index = this.payList.findIndex(v => v.value === account.paymentChannel)
        index !== -1 && (this.payList[index].isOpen = account.accountStatus === 2 && !disabledChannel.includes(this.payList[index].value)) // 已开通（status为2) 设置为显示
        // 不能写banStatus === 0 显示，因为在没有任何设置时，banStatus=null 实际也是可正常使用 null -正常 0 -正常 1-禁用
        index !== -1 && (this.payList[index].hasChannel = account.banStatus !== BAN_STATUS.DISABLE.id) // 未被禁用（banStatus不等于1）过滤出来显示 isOpen为false时应该是灰色不可选
      })
    },
  }
}
</script>
