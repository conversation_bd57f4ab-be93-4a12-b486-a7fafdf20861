<!-- eslint-disable max-lines -->
<!-- 发布票据内容 -->
<style lang="scss" scoped>
.draft-info-container {
  display: flex;
  height: 100%;

  .container-left {
    margin-right: 12px;
    width: 554px;

    .container-left-box {
      margin-bottom: 12px;
      padding: 14px 16px;
      background: $color-FFFFFF;

      &.center {
        height: 175px;
      }
    }

    &.no-contract {
      height: auto;
    }
  }

  .container-right {
    width: 554px;

    .lian-btn {
      border: 1px solid $--color-primary;
      color: $--color-primary;
    }

    .container-right-one {
      margin-bottom: 12px;
      padding: 14px 16px;
      background: $color-FFFFFF;

      &.top {
        height: 108px;

        .lian-icon {
          margin: 0 7px 0 11px;
        }

        .lian-tip {
          font-size: 14px;
          font-weight: 500;
        }

        .no-lian-tip {
          margin-left: 9px;
          font-size: 14px;
          color: $color-text-secondary;
        }
      }

      &.center {
        height: auto;

        .bargain-item {
          height: 0;
        }
      }
    }
  }

  .col-flex {
    display: flex;
    justify-content: space-between;

    .form-item:nth-child(2) {
      margin-left: 8px;
    }
  }

  .image-box {
    position: relative;
    border: 1px solid $color-D9D9D9;
    border-radius: 2px;
    padding: 8px;
    width: 256px;
    height: 82px;

    .img {
      width: 100%;
      height: 100%;
    }

    &.no-data {
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px dashed $color-D9D9D9;
      font-size: 12px;
      color: $color-text-secondary;
      background: $color-F4F5F6;
      line-height: 16px;
    }

    ::v-deep .el-image__preview-mask {
      pointer-events: none;
    }
  }

  ::v-deep .el-input {
    &.text-right {
      input {
        text-align: right;
      }
    }

    .el-input-group__append {
      padding: 0 10px;
      color: $color-text-primary;
      background: $color-FAFAFA;
    }

    .el-input__inner {
      font-size: 16px;
    }

    &.is-disabled .el-input__inner {
      border-color: $color-D9D9D9;
      color: $color-text-primary;
      background-color: $color-F4F5F6;
    }
  }

  .date-box {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-right: 8px;
    border: 1px solid $color-D9D9D9;
    border-radius: 2px;
    padding: 0 12px;
    width: 168px;
    height: 40px;
    font-size: 16px;
    color: $color-text-primary;
    background-color: $color-F4F5F6;
    cursor: no-drop;

    .icon {
      margin-right: 12px;
      color: $color-text-light;
    }
  }

  .other-margin-top {
    margin-top: 0;
  }

  .sub-ticket-char {
    margin-right: 8px;
    margin-left: 8px;
    line-height: 40px;
  }

  .slot-label {
    display: flex;

    .icon {
      margin-left: 4px;
      font-size: 20px;
      color: $color-008489;
    }
  }

  .title-flex {
    display: flex;
    justify-content: space-between;
    margin-top: 0;
  }

  .quotation-flex {
    align-items: flex-end;
  }

  .offer-flex {
    display: flex;
    justify-content: space-between;

    .form-item-block {
      flex: 1;
      margin-right: 12px;

      .el-input {
        width: 100%;
      }
    }

    .form-item {
      .offer-bold {
        display: inline-block;
        width: 100%;
        font-weight: bold;
      }

      .amount {
        color: $color-warning;
      }
    }

    .plus-sign {
      line-height: 40px;
      padding: 0 8px;
      font-size: 18px;
    }
  }

  .other-row {
    flex-wrap: wrap;
  }

  .black-list {
    overflow: hidden;
    overflow-y: scroll;
    max-height: 96px;
    flex-wrap: wrap;
  }

  ::v-deep {
    .price-btn {
      &.is-disabled {
        border: 1px solid $color-D9D9D9;
        color: $color-text-light;
        background: $color-F4F5F6;
        cursor: not-allowed;
      }

      span {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 38px;
        font-size: 16px;
      }

      .btn-icon {
        margin-right: 8px;
        width: 20px;
        height: 21px;
        line-height: 20px;
        vertical-align: middle;
      }
    }
  }
}

@mixin tag-disabled() {
  border-color: $color-D9D9D9;
  color: $color-text-primary;
  background: $color-F4F5F6;
}

.acceptor-remarks-select {
  width: 100%;

  ::v-deep {
    .el-tag {
      margin-left: 4px;
      border-color: $--color-primary;
      border-radius: 2px;
      padding: 0 6px;
      font-size: 12px;
      color: $--color-primary;
      background-color: $color-FFFFFF;

      &:first-child {
        margin-left: 12px;
      }

      .el-select__tags-text {
        flex: 1;

        @include ellipsis;
      }

      .el-icon-close {
        right: -4px;
        color: $--color-primary;
        background: transparent;
        transform: scale(1.2);
      }

      &:hover {
        background-color: #FFEFEF;
      }
    }

    .el-select__input {
      margin-left: 12px;
    }
  }

  &.no-close-tag1 {
    ::v-deep {
      .el-tag:first-child {
        @include tag-disabled;

        .el-icon-close {
          display: none;
        }
      }
    }
  }

  &.no-close-tag2 {
    ::v-deep {
      .el-tag:first-child,
      .el-tag:nth-child(2) {
        @include tag-disabled;

        .el-icon-close {
          display: none;
        }
      }
    }
  }
}

.item-position {
  position: relative;

  .value {
    .el-tooltip {
      position: absolute;
      top: 1px;
      left: 70px;
    }
  }

  .max-width {
    width: 100%;
  }
}

.split-change {
  ::v-deep.el-radio-button {
    margin-right: 8px;

    .el-radio-button__inner { // 修改按钮样式
      border: 0 !important;
      border-radius: 11px;
      width: 40px;
      height: 22px;
      color: $color-FFFFFF;
      background: $color-D9D9D9;
    }

    .el-radio-button__orig-radio:checked + .el-radio-button__inner {// 修改按钮激活样式
      border: 0 !important;
      color: #FFFFFF;
      background-color: $--color-primary;
    }
  }
}

.int-multiple {
  border: 1px solid $--color-primary;
  border-radius: 4px;
  padding: 2px 8px;
}
</style>

<style lang="scss">
.custom-popover {
  margin-top: -40px !important;
  border: none;
  padding: 0;
}

.other-tip-custom {
  p {
    margin-bottom: 5px;
  }
}

.sub-ticket-range {
  position: absolute;
  top: 2px;
  left: 56px;
  padding: 0;
  height: 16px;
  line-height: 16px;
}

.can-not-split-label {
  position: absolute;
  top: 2px;
  left: 98px;
}

.seller-select-btn {
  font-size: 16px;
  text-align: center;
  color: $--color-primary;
  line-height: 40px;
  cursor: pointer;
}

.seller-bank-link {
  border-bottom: 1px solid $--color-primary;
  color: $--color-primary;
  cursor: pointer;
}

.require-font {
  color: #EC3535;

  &::before {
    content: "*";
    color: #EC3535;
  }
}

.split-item-warp {
  margin-top: 12px;
  padding: 8px;
  font-size: 14px;
  color: #333333;
  background: #F4F4F4;

  .content {
    display: flex;
    font-weight: 600;
  }
}

.w-150 {
  display: inline-block;
  line-height: 30px;
  width: 150px;
}

.w-350 {
  display: inline-block;
  line-height: 30px;
  width: 350px;
}

.m-r-40 {
  display: inline-block;
  min-width: 100px;

  // margin-right: 40px;
}

.m-l5 {
  margin-left: 5px;
}

.split-text-item {
  margin-left: 20px;

  li {
    list-style-type: disc;
  }
}

.contact-field {
  margin-bottom: 12px;
  padding: 14px 16px;
  background: $color-FFFFFF;
}
</style>

<template>
  <div v-if="form" class="draft-info-container">
    <div class="container-left" :class="{'no-contract': corpInfo.yilianPayOpen !== 1}">
      <div class="container-left-box">
        <div class="g-title">票面信息</div>
        <div class="col-flex">
          <FormItem label="票据正面">
            <div :class="['image-box', !form.frontImgUrl && 'no-data']">
              <el-image
                v-if="form.frontImgUrl"
                fit="cover"
                class="img"
                :src="defaultFrontUrl"
                :preview-src-list="[form.frontImgUrl]"
              >
                <template slot="preview">
                  <icon type="chengjie-eye" :size="25" />
                </template>
              </el-image>
              <p v-else>
                {{ imgLoading.front ? '加载中...' : '未识别到票据正面信息' }}
              </p>
            </div>
          </FormItem>
          <FormItem label="票据背面">
            <div :class="['image-box', !form.backImgUrl && 'no-data']">
              <el-image
                v-if="form.backImgUrl"
                fit="cover"
                class="img"
                :src="defaultBackUrl"
                preview-text="点击查看大图"
                :preview-src-list="[form.backImgUrl]"
              >
                <template slot="preview">
                  <icon type="chengjie-eye" :size="25" class="no-pointor" />
                </template>
              </el-image>
              <p v-else>
                <template v-if="imgLoading.back">加载中...</template>
                <template v-else>
                  <span>未识别到票据背面信息</span>

                  <el-tooltip popper-class="other-tip-custom">
                    <div slot="content">
                      <p class="other-tip">
                        「单张识别」和「自动同步」的票据
                      </p>
                      <p class="other-tip">
                        可自动识别票据背面信息，并且发布
                      </p>
                      <p class="other-tip">
                        时带有「识」标签。
                      </p>
                    </div>
                    <icon class="icon icon-question" :size="16" type="chengjie-wenti" />
                  </el-tooltip>
                </template>
              </p>
            </div>
          </FormItem>
        </div>
        <FormItem label="承兑人名称">
          <el-input v-model="form.acceptorName" placeholder="填写承兑人名称" disabled />
        </FormItem>
        <div v-if="form.draftType" style="position: relative;">
          <FormItem label="子票区间">
            <div class="col-flex">
              <el-input v-model="form.subTicketStart" disabled />
              <span class="sub-ticket-char">~</span>
              <el-input v-model="form.subTicketEnd" disabled />
            </div>
          </FormItem>
          <!-- <span class="g-xinpiao sub-ticket-range">新票</span> -->
          <CanNotSplitLabel class="can-not-split-label" :draft-info="form" />
        </div>
        <FormItem label="票据号码">
          <el-input v-model="form.draftNo" placeholder="填写票据号码" disabled />
        </FormItem>
        <div class="col-flex">
          <FormItem label="票面金额">
            <el-input
              class="text-right"
              :value="yuan2wan(form.draftAmount)"
              placeholder="填写票面金额"
              disabled
            >
              <span slot="append">万</span>
            </el-input>
          </FormItem>
          <FormItem label="到期日">
            <div class="date-box">
              <icon type="chengjie-date" class="icon" :size="19" />
              {{ formatTime(String(form.maturityDate), 'YYYY-MM-DD') }}
            </div>
          </FormItem>
          <FormItem label="背书手数">
            <el-input
              v-model="form.endorseCount"
              placeholder="背书手数"
              class="hands-input text-right"
              :disabled="true"
              @input="value => handleInput(value, 'endorseCount')"
            >
              <span slot="append">手</span>
            </el-input>
          </FormItem>
        </div>
        <FormItem label="瑕疵（可多选）">
          <ticket-flaw-form
            v-if="newData"
            :defects="form.defects"
            :identify-one="data.discernType !== IDENTIFY_TYPE.MULTIPLE.id"
            @change="handleChangeFlaw"
          />
        </FormItem>
      </div>
      <div class="container-left-box center">
        <div class="g-title">支付渠道</div>
        <FormItem>
          <pay-radio
            :api-pay-list="apiPayList"
            :type="defaultPaymentChannelList"
            @change="handlePayChange"
          />
        </FormItem>
        <!-- 智付邦+、智付E+ 显示回款该账户 -->
        <FormItem
          v-if="form.acceptJdYlpay || form.acceptZbankPlus"
          label="回款账户"
          class="form-item-block item-position"
          required
        >
          <el-tooltip
            placement="top"
            popper-class="issue-draft-tooltip"
          >
            <template slot="content">
              <div>
                依照智付E+、E++渠道交易模式，票方发布票据必须选择回款账户(支持任意银行一般户)。需要在
                <span
                  class="seller-bank-link"
                  @click="() => {
                    if ($ipc) {
                      $ipc.send('PUSH_ROUTE_IN_MAIN_WINDOW', '/user-center/bank-account?tabStatus=2')
                    } else {
                      $router.push('/user-center/bank-account?tabStatus=2')
                      $emit('close')
                    }
                  }"
                >银行回款账户</span>页面绑定回款账户，交易完成后票款自动提现到账。
              </div>
            </template>
            <icon class="icon icon-question" type="chengjie-wenti" />
          </el-tooltip>

          <div class="pay-type-item">
            <el-select
              ref="sellerBankAccount"
              v-model="form.sellerBankAccountId"
              class="max-width"
              placeholder="请选择回款账户"
              size="small"
            >
              <el-option
                v-for="item in sellerBankAccountList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
              <div
                class="seller-select-btn"
                @click="() => {
                  if ($ipc) {
                    $ipc.send('PUSH_ROUTE_IN_MAIN_WINDOW', '/user-center/bank-account?tabStatus=2')
                    $refs.sellerBankAccount.blur();
                  } else {
                    $router.push('/user-center/bank-account?tabStatus=2')
                    $refs.sellerBankAccount.blur()
                    $emit('close')
                  }
                }"
              >
                <i class="el-icon-plus" />添加回款账户
              </div>
            </el-select>
          </div>
        </FormItem>
      </div>
    </div>
    <div class="container-right">
      <!-- TODO 连号票即将开启 -->
      <div v-if="false" class="container-right-one top">
        <div
          class="g-title"
        >
          <span>连号票</span>
          <span v-show="form.serialDraft">
            <icon
              class="lian-icon"
              type="sdicon-info-circle"
              :size="14"
              color-theme="secondary"
            />
            <span class="lian-tip">填入张数能顺延票号进行连号发布</span>
          </span>
        </div>
        <FormItem>
          <el-button
            v-if="!form.serialDraft"
            class="lian-btn"
            width="168px"
            height="42px"
            @click="form.serialDraft = true"
          >
            <icon type="chengjie-serial" :size="18" />
            发布连号票
          </el-button>
          <div v-else>
            <el-input
              v-model.number="form.serialDraftNum"
              placeholder="填写连号票张数，最多99"
              class="text-right lian-input"
              :width="257"
              type="number"
              :number-format="{
                decimal: false,
                negative: false,
                leadingZero: false,
                maxLength: 2,
              }"
            >
              <span slot="append">张</span>
            </el-input>
            <span class="no-lian-tip">若不填写则默认按照正常票发布</span>
          </div>
        </FormItem>
      </div>
      <div class="container-right-one center">
        <div class="g-title other-margin-top g-title--green">
          其他设置（非必填）

          <el-tooltip popper-class="other-tip-custom">
            <div slot="content">
              <p class="other-tip">极速出票：票在户，免确认，享受订单优先展示特权。需 10 分钟内背书。</p>
              <p class="other-tip">保证金：开启后，可保证双方权益。</p>
              <p class="other-tip">收款人打码：开启后，订单中展示的正面图片，将自动为收款人打码。</p>
              <p class="other-tip">出票人打码：开启后，订单中展示的正面图片，将自动为出票人打码。</p>
              <p class="other-tip">议价：开启后，您将接受议价，同时需要输入议价上限。</p>
              <p v-if="form.draftType" class="other-tip">拆分接单：开启后，您将接受拆分接单，需设置允许拆分接单的方式。</p>
              <ul v-if="form.draftType" class="split-text-item">
                <li>直接拆分：主动拆单，可单独设置报价，金额相同可连号发布。</li>
                <li>区间拆分：整单发布，对手在设置的区间内自由拆分接单。</li>
                <li>定额拆分：整单发布，对手按设置的固定金额或固定金额倍数拆分接单。</li>
              </ul>
              <p class="other-tip">资方所在地黑名单：添加黑名单地区后，对应地区的用户将看不到您发布的票据。</p>
              <!-- <p class="other-tip">联系方式：交易对手可通过您下方预留的联系方式联系到您，联系方式必填。</p> -->
            </div>
            <icon class="icon icon-question" :size="24" type="chengjie-wenti" />
          </el-tooltip>
        </div>
        <el-row type="flex" class="other-row">
          <el-col v-if="!limitLight" :span="4">
            <transaction-tooltip-button :offset="190" :types="[TRANSACTION_TOOLTIP_TYPE.FAST]" @change="(val) => val === TRANSACTION_TOOLTIP_TYPE.FAST && (form.fastTrade = false)">
              <template v-slot:content="{disabled}">
                <FormItem label="极速出票">
                  <el-switch
                    v-model="form.fastTrade"
                    :disabled="disabled"
                    @change="(val) => form.margin = val"
                  />
                </FormItem>
              </template>
            </transaction-tooltip-button>
          </el-col>
          <el-col :span="4">
            <FormItem label="保证金">
              <el-switch
                v-model="form.margin"
              />
            </FormItem>
          </el-col>
          <el-col :span="4">
            <FormItem label="收款人打码">
              <el-switch
                v-model="form.receiverMosaic"
              />
            </FormItem>
          </el-col>
          <el-col :span="4">
            <FormItem label="出票人打码">
              <el-switch
                v-model="form.drawerMosaic"
              />
            </FormItem>
          </el-col>
          <el-col :span="4">
            <FormItem label="议价">
              <el-switch
                v-model="form.bargaining"
                @change="(val) => !val && (form.bargainingLimit = '')"
              />
            </FormItem>
          </el-col>
          <el-col v-if="form.draftType" :span="4">
            <FormItem label="拆分接单">
              <el-switch
                v-model="form.splitFlag"
                :active-value="1"
                :inactive-value="0"
                :disabled="splitDisabled()"
                @change="splitChange"
              />
            </FormItem>
          </el-col>
        </el-row>
        <FormItem v-show="form.bargaining">
          <el-input
            v-model="form.bargainingLimit"
            placeholder="议价上限，每十万扣款"
            class="text-right"
            size="small"
            :width="200"
            type="number"
            :number-format="{maxDecimalLength: 2, leadingZero: false, negative: false, maxLength: 8}"
          >
            <span slot="append">元</span>
          </el-input>
        </FormItem>
        <FormItem v-show="form.draftType && form.splitFlag">
          <el-radio-group
            v-model="form.splitMethod"
            style="margin-bottom: 12px;"
            size="mini"
            class="radio-mini"
          >
            <el-radio-button :label="SPLIT_TYPE_CODE.DIRECT">直接拆分</el-radio-button>
            <el-radio-button :label="SPLIT_TYPE_CODE.INTERVAL">区间拆分</el-radio-button>
            <el-radio-button :label="SPLIT_TYPE_CODE.QUOTA">定额拆分</el-radio-button>
          </el-radio-group>
          <!-- 直接拆分 -->
          <div v-if="form.splitMethod === SPLIT_TYPE_CODE.DIRECT">
            <el-button
              type="primary"
              plain
              round
              size="mini"
              @click="showSplistRules"
            >
              拆分规则
            </el-button>
            <span class="require-font">设置拆分及报价</span>
          </div>
          <!-- 区间拆分 -->
          <div v-if="form.splitMethod === SPLIT_TYPE_CODE.INTERVAL">
            <el-input
              v-model="form.splitAmtMin"
              placeholder="最低可拆分"
              class="text-right"
              size="small"
              :width="123"
              type="number"
              :number-format="{maxDecimalLength: 2, leadingZero: false, negative: false, maxIntegerLength: 8}"
            >
              <span slot="append">元</span>
            </el-input>
            <span style="margin: 0 5px;">-</span>
            <el-input
              v-model="form.splitAmtMax"
              placeholder="最高可拆分"
              class="text-right"
              size="small"
              :width="131"
              type="number"
              :number-format="{maxDecimalLength: 2, leadingZero: false, negative: false, maxIntegerLength: 8}"
            >
              <span slot="append">元</span>
            </el-input>
          </div>
          <!-- 定额拆分 -->
          <div v-if="form.splitMethod === SPLIT_TYPE_CODE.QUOTA" class="split-change">
            <el-radio-group
              v-model="form.splitAmtIntSelect"
              size="mini"
              class="radio-mini"
              @change="splitAmtIntChange"
            >
              <el-radio-button :label="1">1万</el-radio-button>
              <el-radio-button :label="5">5万</el-radio-button>
              <el-radio-button :label="10">10万</el-radio-button>
            </el-radio-group>
            <el-input
              v-model="form.splitAmtInt"
              placeholder="拆分金额"
              class="text-right"
              size="small"
              :width="110"
              type="number"
              :number-format="{
                decimal: false,
                negative: false,
                leadingZero: false,
                maxDecimalLength: 0,
                maxIntegerLength: 4,
              }"
              @input="handleInputSplitAmount"
              @change="handleChangeSplitAmount"
            >
              <span slot="append">万</span>
            </el-input>
            <el-tooltip
              placement="top"
              style="margin-left: 10px;"
              content="勾选后，代表可按设置金额的整数倍拆分接单"
            >
              <el-checkbox
                v-model="form.intMultiple"
                :true-label="1"
                :false-label="0"
                class="int-multiple"
              >
                整数倍
              </el-checkbox>
            </el-tooltip>
          </div>
        </FormItem>
        <FormItem>
          <div slot="label" class="slot-label">
            资方所在地黑名单
          </div>
          <black-list v-if="newData" v-model="form.blackRegionList" class="black-list" />
        </FormItem>

        <FormItem>
          <div slot="label" class="slot-label">
            承兑人备注（最多选择 3 条）
          </div>
          <el-select
            v-model="form.acceptorNotes"
            multiple
            placeholder="请准确选择标签，错误标签可能会对您的信誉产生影响"
            size="small"
            popper-class="default"
            :multiple-limit="3"
            :class="['acceptor-remarks-select', customSelectClass]"
          >
            <el-option
              v-for="item in acceptorRemarksList"
              :key="item.id"
              :label="item.label"
              :value="item.label"
            />
          </el-select>
        </FormItem>
        <!--  erp没有设置联系方式 只有深度有  深度项目已经分离开了 -->
        <!--
          <FormItem">
          <div slot="label" class="slot-label">
          联系方式
          </div>
          <ContactSetting ref="contactSetting" />
          </FormItem>
        -->
      </div>
      <div class="container-right-one bottom">
        <div class="title-flex">
          <div class="g-title">报价填写</div>
          <QueryOrderPrice :height="35" />
          <!--
            <el-tooltip
            :disabled="!quotationBtnDisabled"
            content="票据到期日 ≤30 天或信息不完整，无法提供报价行情参考"
            placement="top"
            >
            <el-button
            v-waiting="'post::loading::/draft/order/enquiryPrice'"
            :class="quotationBtnDisabled && 'is-disabled'"
            class="price-btn"
            type="primary"
            border
            width="156"
            height="40"
            @click="!quotationBtnDisabled && showQuotationReferenceDialog()"
            >
            <icon type="chengjie-dynamic" class="btn-icon" />行情报价参考
            </el-button>
            </el-tooltip>
          -->
          <!--
            <el-radio-group v-model="form.billingMethod" size="mini" class="radio-mini">
            <el-radio-button :label="0">每十万扣款</el-radio-button>
            <el-radio-button :label="1">以利率计算</el-radio-button>
            </el-radio-group>
          -->
        </div>
        <!-- 非拆分接单 区间拆分 定额拆分 -->
        <template v-if="!form.splitFlag || [SPLIT_TYPE_CODE.INTERVAL, SPLIT_TYPE_CODE.QUOTA].includes(form.splitMethod)">
          <div class="offer-flex quotation-flex">
            <template v-if="form.billingMethod === OFFER_TYPE_CODE.NIAN_LI_LV_JI_SUAN">
              <FormItem label="利率" required>
                <el-input
                  v-model="form.annualInterestInput"
                  class="text-right"
                  :class="(!form.annualInterestInput || +form.annualInterestInput === 0) && isValid && 'is-require-input'"
                  placeholder="请输入利率"
                  type="number"
                  :number-format="{maxDecimalLength: 4, leadingZero: false, negative: false, maxLength: 7}"
                >
                  <span slot="append">%</span>
                </el-input>
              </FormItem>
              <span class="plus-sign">+</span>
              <FormItem label="每十万手续费">
                <el-input
                  v-model="form.serviceCharge"
                  class="text-right"
                  placeholder="请输入每十万手续费"
                  type="number"
                  :number-format="{maxDecimalLength: 2, leadingZero: false, negative: false, maxLength: 8}"
                >
                  <span slot="append">元</span>
                </el-input>
              </FormItem>
            </template>
            <template v-else>
              <FormItem
                label="每十万扣款"
                class="form-item-block"
                required
              >
                <el-input
                  v-model="form.lakhDeduction"
                  class="text-right"
                  :class="(!form.lakhDeduction || +form.lakhDeduction === 0) && isValid && 'is-require-input'"
                  placeholder="每十万扣款"
                  type="number"
                  :number-format="{maxDecimalLength: 2, leadingZero: false, negative: false, maxLength: 8}"
                >
                  <span slot="append">元</span>
                </el-input>
              </FormItem>
            </template>
            <el-button
              class="border-grey icon-m m-l5"
              type="primary"
              round
              border
              @click="handleChangeType"
            >
              <icon class="icon icon-switch" type="chengjie-swap" />
              {{ form.billingMethod ? '每十万扣款' : '以利率计算' }}
            </el-button>
          <!--
            <el-radio-group v-model="form.billingMethod" size="mini" class="radio-mini">
            <el-radio-button :label="0">每十万扣款</el-radio-button>
            <el-radio-button :label="1">以利率计算</el-radio-button>
            </el-radio-group>
          -->
          </div>
          <div class="offer-flex">
            <FormItem label="每十万扣息">
              <span class="offer-bold">{{ form.lakhDeduction ? `${form.lakhDeduction}元` : '-' }}</span>
            </FormItem>
            <FormItem label="年化利率" align="center">
              <span class="offer-bold">{{ form.annualInterest }}%</span>
            </FormItem>
            <FormItem label="到账金额" align="right">
              <span class="offer-bold amount">{{ form.receivedAmount ? `${yuan2wan(form.receivedAmount)}万元` : '0 万元' }}</span>
            </FormItem>
          </div>
        </template>
        <template v-else>
          <div v-if="splitRulesData && !splitRulesData.splitSetting.length" class="require-font">请先设置拆分规则</div>
          <div class="split-item-warp">
            <div v-if="splitRulesData && splitRulesData.splitSetting.length">
              <div class="title">
                <span class="w-150">拆分</span>
                <span class="w-300">报价</span>
              </div>
              <div v-for="(item, index) in splitRulesData.splitSetting" :key="index" class="content">
                <span class="w-150">{{ item.splitAmt }} 万 X {{ item.splitCount }} 张</span>
                <div class="w-350">
                  <span class="m-r-40">{{ item.lakhDeduction || 0 }} 元</span>
                  <span class="m-r-40">{{ item.annualInterest }} %</span>
                  <span class="m-r-40">{{ yuan2wan(item.receivedAmount) || 0 }} 万</span>
                </div>
              </div>
            </div>
            <div v-if="splitRulesData" class="offer-flex">
              <FormItem label="合计拆分金额">
                <span class="offer-bold amount">{{ splitRulesData.totalSplitAmount ? `${splitRulesData.totalSplitAmount}万元` : '0万元' }}</span>
              </FormItem>
              <FormItem label="合计拆分张数" align="center">
                <span class="offer-bold amount">{{ splitRulesData.totalSplitNum || 0 }}张</span>
              </FormItem>
              <FormItem label="合计到账金额" align="right">
                <span class="offer-bold amount">{{ splitRulesData.totalReceivedAmount ? `${yuan2wan(splitRulesData.totalReceivedAmount)}万元` : '0 万元' }}</span>
              </FormItem>
            </div>
          </div>
        </template>
      </div>
      <!-- 秒贴报价 -->
      <QuotationMt ref="quotationMtRef" style="margin-bottom: 12px;" :is-dialog="true" />
      <div v-if="corpInfo.yilianPayOpen === 1" class="container-right-one contract">
        <div class="title-flex">
          <div class="g-title">
            <span>贸易合同及发票</span>
            <span>&nbsp;</span>
            <el-tooltip popper-class="other-tip-custom">
              <div slot="content">
                <p>上传您与出票人的贸易背景或您<br>与上一手企业的贸易合同及发票</p>
              </div>
              <icon
                type="chengjie-wenti"
                :size="23"
                color-theme="primary"
              />
            </el-tooltip>
          </div>
        </div>
        <el-row type="flex" :gutter="8">
          <el-col :span="12">
            <FormItem label="贸易合同" :required="false">
              <ImgUpload
                v-model="form.tradeContractUrl"
                :size-limit="20"
                :dir="OSS_DIR.DRAFT"
                :height="80"
                accept="application/pdf"
                is-pdf
              >
                <div slot="empty">
                  <div>点击或拖拽PDF至此上传贸易合同</div>
                  <div>（支持pdf格式，不超过20M）</div>
                </div>
              </ImgUpload>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem label="发票" :required="false">
              <ImgUpload
                v-model="form.invoiceUrl"
                :size-limit="20"
                :dir="OSS_DIR.DRAFT"
                :height="80"
                accept="application/pdf"
                is-pdf
              >
                <div slot="empty">
                  <div>点击或拖拽PDF至此上传发票</div>
                  <div>（支持pdf格式，不超过20M）</div>
                </div>
              </ImgUpload>
            </FormItem>
          </el-col>
        </el-row>
      </div>
      <!-- 设置联系方式 -->
      <!-- <ContactItem @change-data="onChangeMobile" /> -->
      <!--
        <ContactSettingField
        ref="ContactSettingField"
        v-model="form.mobile"
        class="contact-field"
        :icon-size="23"
        :is-border="true"
        @close="() => { $emit('close') }"
        >
        <template #label>
        <span class="g-title">联系方式</span>
        </template>
        </ContactSettingField>
      -->
    </div>
    <QuotationReferenceDialog ref="quotationReferenceDialogRef" />
    <SplitRules ref="splitRulesDialogRef" :draft-info="form" @setSplitRulesData="setSplitRulesData" />
  </div>
</template>

<script>
import FormItem from '../components/form-item.vue'
import ticketFlawForm from '../components/ticket-flaw-form.vue' // 瑕疵多选框
import payRadio from '../components/pay-radio.vue' // 支付类型
import blackList from '../components/black-list.vue' // 黑名单列表
// 金额单位转换
import { yuan2wan } from '@/common/js/number'
// 时间格式化
import { formatTime, getDateSpace } from '@/common/js/date'
// 票据正面和背面截图生成组件
import imgGenerator from '@/views/components/market-draft-image-generator/market-draft-image-generator.js'
import QuotationReferenceDialog from '../components/quotation-reference-dialog.vue'
// import ContactSetting from '../components/contact-setting.vue'
import {
  BACK_DEFECT_TYPE_NAME_MAP, // 票据瑕疵类型
  OFFER_TYPE_CODE, // 报价类型
  IDENTIFY_TYPE, // 识别类型
  ACCEPTOR_TYPE, // 承兑人类型
  TRANSACTION_TOOLTIP_TYPE, // 交易限制提示类型
  BAN_STATUS, // 禁用状态
  OSS_DIR,
  SPLIT_TYPE_CODE // 拆分类型code
} from '@recognize/constant'
import {
  lakhDeductionMath, interestRateMath
} from '@/common/js/draft-math'
import commonApi from '@recognize/apis/common'
import userApi from '@recognize/apis/user' // 打码图片延迟
import { mapGetters } from 'vuex'
import ImgUpload from '@/views/components/common/img-upload/img-upload.vue'
import CanNotSplitLabel from '@/recognize/components/draft/components/can-not-split-label.vue'
import { BILLING_METHOD_CODE, DRAFT_TYPE } from '@/constant.js' // 常量
import SplitRules from '../components/split-rules-dialog.vue' // 拆分规则
// import ContactItem from '../components/contact-item.vue' // 拆分规则
// 联系方式
// import ContactSettingField from '@/views/pages/setting/components/contact-setting-field.vue'
import QueryOrderPrice from '@/views/components/query-order-price/query-order-price.vue'
import QuotationMt from '@/views/components/quotation-mt/quotation-mt.vue'

const MOSAIC_TIME = 0 // 打码图片延迟
const defaultHandsNumber = 5 // 默认展示的背书手数

const frontImgUrl = 'https://oss.chengjie.red/web/imgs/recognize/positive.png' // 票据正面
const backImgUrl = 'https://oss.chengjie.red/web/imgs/recognize/back.png' // 票据背面

export default {
  name: 'draft-info',

  components: {
    FormItem,
    ticketFlawForm,
    payRadio,
    blackList,
    QuotationReferenceDialog,
    // ContactSetting
    ImgUpload,
    CanNotSplitLabel,
    SplitRules,
    // ContactItem,
    // ContactSettingField,
    QueryOrderPrice,
    QuotationMt
  },

  props: {
    // 票据信息
    data: {
      default: () => ({}),
      type: Object
    }
  },

  data() {
    return {
      OSS_DIR,
      TRANSACTION_TOOLTIP_TYPE, // 交易限制提示类型
      SPLIT_TYPE_CODE,
      yuanFormat: {
        decimal: true,
        maxDecimalLength: 2,
        maxLength: 8,
      },
      // 正面背景图片地址
      defaultFrontUrl: frontImgUrl,
      // 背面背景图片地址
      defaultBackUrl: backImgUrl,
      recognitionData: null, // 生成截图的数据
      defaultHandsNumber, // 默认展示的背书手数
      BACK_DEFECT_TYPE_NAME_MAP, // 票据瑕疵类型
      OFFER_TYPE_CODE, // 报价类型
      IDENTIFY_TYPE, // 识别类型
      // 联系方式信息
      contactInfo: {
        contactFlag: 0,
        contactUrl: '',
        mobile: '',
      },
      form: {}, // 表单信息
      image: {
        origin: '', // 收款人打码的原图
        receiver: '', // 收款人打码的图片
        drawer: '', // 出票人打码的图片
        receiverdrawer: '', // 收款人及出票人一起打码的图片
      },
      originJson: {}, // 原始票据json

      newData: true, // 新数据加载完
      defaultPaymentChannelList: [], // 默认配置支付渠道

      imgLoading: { // 票据图片loading
        front: false,
        back: false
      },
      isValid: false,
      apiPayList: null, // 接口返回的支付渠道列表
      acceptorRemarksList: [], // 承兑人备注列表
      customSelectClass: '', // 承兑人备注下拉框class 使用class来控制已选标签是否可删除
      sellerMosaicType: 1, // 出票人打码设置 1打码全称、开户行、账号 2仅打码全称、账号的后4位数
      buyerMosaicType: 1, // 收款人打码设置 1打码全称、开户行、账号 2仅打码全、账号的后4位数
      splitRulesData: {
        splitSetting: [],
        totalSplitAmount: 0,
        totalSplitNum: 0,
        totalReceivedAmount: 0
      }, // 拆分设置规则数据
    }
  },

  computed: {
    ...mapGetters('user', {
      corpInfo: 'corpInfo',
      limitLight: 'limitLight', // 是否限制光速
      sellerBankAccountList: 'sellerBankAccountList'// 已通过的回款账户列表
    }),
    // ...mapGetters('common', {
    //   mobilePattern: 'mobilePattern', // 是否开启联系方式模式 1开启 0关闭
    // }),
    sellerBankAccountId() {
      return this.$store.state.common.sellerBankAccountId
    },
    // 根据票面金额，到期日期,每十万扣组合
    annualInterestComputed() {
      return [this.form.draftAmount, this.form.maturityDate, this.form.lakhDeduction]
        .map(val => String(val))
        .join('+')
    },
    // 根据票面金额，输入利率 ，到期日期, 每十万手续费组合
    interestRate() {
      return [this.form.draftAmount, this.form.annualInterestInput, this.form.maturityDate, this.form.serviceCharge]
        .map(val => String(val))
        .join('+')
    },
    // 无三要素之一不可点报价参考（票据到期日 ≤30 天或信息不完整，无法提供报价行情参考）
    quotationBtnDisabled() {
      const { acceptorName, draftAmount, maturityDate } = this.form
      const spaceDate = getDateSpace(maturityDate, new Date())
      // eslint-disable-next-line no-magic-numbers
      return !acceptorName || !draftAmount || !maturityDate || spaceDate <= 30
    },
    // 是否是大于500W的票
    isMoreThan500W() {
      const limit = 5000000
      return this.form.draftAmount > limit && this.form.acceptJdYlpay === 1
    }
  },

  watch: {
    data: {
      immediate: true,
      deep: true,
      async handler(val) {
        const configObj = await this.getConfig()
        this.newData = false
        this.image = {
          origin: '', // 收款人打码的原图
          receiver: '', // 收款人打码的图片
          drawer: '', // 出票人打码的图片
          receiverdrawer: '', // 收款人及出票人一起打码的图片
        }
        // 设置打码类型
        this.sellerMosaicType = configObj.sellerMosaicType
        this.buyerMosaicType = configObj.buyerMosaicType
        // 背书手数，单张识别为空时传0
        let endorseCount
        if (val.discernType === IDENTIFY_TYPE.SIGNAL.id) {
          endorseCount = val.endorseCount || 0
        } else {
          endorseCount = val.endorseCount || null
        }
        this.form = {
          ...this.form,
          draftDiscernId: val.draftDiscernId, // 票据id

          serialDraft: false, // 是否连号票
          serialDraftNum: null, // 连号票张数

          draftNo: val.draftNo, // 票号
          draftAmount: val.draftAmount, // 票面金额
          maturityDate: val.maturityDate, // 到期日
          issueDate: val.issueDate, // 出票日
          acceptorName: val.acceptorName, // 承兑人

          endorseCount, // 背书手数
          billingMethod: 0, // 计费方式0-十万直扣1-年利率加手续费

          lakhDeduction: '', // 每十万扣息
          annualInterest: '0.0000', // 年利率
          serviceCharge: '', // 每十万手续费

          margin: configObj?.fastTrade === 1 || configObj?.releaseMargin === 1, // 是否保证金
          fastTrade: configObj?.fastTrade === 1, // 是否光速票，0-否、1-是
          inviteCode: '', // 定向标识(手机号或者定向码）
          bargaining: configObj?.bargain === 1, // 是否接受议价
          bargainingLimit: this.getBargainLimitConfig(configObj, val.acceptorType), // 议价上限

          blackRegionList: configObj.areaBlackConfig ? JSON.parse(JSON.stringify(configObj.areaBlackConfig)) : [], // 黑名单
          acceptorNotes: val.acceptorNotes ?? [], // 承兑人备注

          frontImgUrl: '', // 前图
          backImgUrl: '', // 后图

          receiverMosaic: configObj?.receiverMosaic || false, // 收款人打码
          drawerMosaic: configObj?.drawerMosaic || false, // 出票人打码

          annualInterestInput: '', // 输入的利率
          receivedAmount: '', // 实际到账金额
          interestDays: val.interestDays, // 到期日

          defects: val.defects || '', // 瑕疵
          originalDefects: val.originalDefects || '', // 原瑕疵
          acceptorType: val.acceptorType, // 承兑人类型
          platformServiceFee: val.platformServiceFee, // 支付平台的服务费，单位：云豆

          tradeContractUrl: '', // 贸易合同文件
          invoiceUrl: '', // 发票文件
          draftType: val.draftType, // 票据类型
          subTicketStart: val.subTicketStart, // 子票区间开始
          subTicketEnd: val.subTicketEnd, // 子票区间结束
          splitFlag: configObj?.splitFlag, // 拆分开关
          splitMethod: '', // 拆分方式
          splitAmtIntSelect: 1, // 定额拆分金额选项
          splitAmtInt: '1', // 定额拆分金额
          intMultiple: 1, // 支持整数倍
          splitType: 0, // 后端区分拆分类型字段 0=>区间/定额拆分  1=>直接拆分
          splitAmtMin: 0, // 最低拆分金额
          splitAmtMax: 0, // 最大拆分金额
        }
        if (!val.draftType) {
          this.form.splitFlag = 0
        }
        this.originJson = val.draftJson
        this.getAnnualInterest()
        if (val.acceptorType === ACCEPTOR_TYPE.SHANG_PIAO.id) {
          this.getAcceptorCommercialLabel(val.acceptorName)
        }
        // 新票填充拆分区间
        if (val.draftType) {
          let { draftAmount, subTicketStart, subTicketEnd } = val
          if (draftAmount < this.getMinSplitConfig()) {
            this.form.splitFlag = 0
          } else if (subTicketStart === '0' && subTicketEnd === '0') {
            this.form.splitFlag = 0
          } else {
            this.form.splitAmtMin = this.getMinSplitConfig()
            this.form.splitAmtMax = draftAmount
          }
        }
        this.getAcceptorCommonLabel(val.acceptorType)
        await this.$nextTick()
        this.newData = true

        this.imgLoading = {
          front: true,
          back: !!val.draftJson.back.history.length
        }

        setTimeout(async() => {
          // 生成canvas base64 图片
          if (!val.frontImgUrl) {
            this.recognitionData = val.draftJson
            await this.$nextTick()
            const frontPromise = this.createFrontImage()
            let backImage
            const backPromise = imgGenerator.screenshot(this.recognitionData, 'back', 'canvas').then(back => {
              backImage = back.toDataURL()
            })
            await Promise.all([frontPromise, backPromise])
            if (val.draftJson.back.history.length) {
              this.form.backImgUrl = backImage
            }
            this.imgLoading = {
              front: false,
              back: false
            }
          }
        }, MOSAIC_TIME)
        // 银票 获取秒贴报价 除了商票 财票 其他 剩下都是银票
        if (![DRAFT_TYPE.SHANG_PIAO.id, DRAFT_TYPE.CAI_PIAO.id, DRAFT_TYPE.OTHER.id].includes(val.acceptorType)) {
          const { acceptorName, draftAmount, maturityDate } = this.form
          if (acceptorName && draftAmount && maturityDate) {
            this.$refs.quotationMtRef.getMtQuotation({
              acceptorName,
              draftAmount: yuan2wan(draftAmount),
              maturityDate
            })
          }
        }
      }
    },
    'form.splitFlag': {
      handler(val) {
        if (val) {
          this.form.splitMethod = 2
        }
      },
    },
    // 设置回款账户
    sellerBankAccountId(val) {
      if (!this.form.sellerBankAccountId) {
        this.form.sellerBankAccountId = val
      }
    },

    // 根据票面金额，到期日期,每十万扣组合
    annualInterestComputed: {
      immediate: true,
      handler() {
        this.getAnnualInterest()
      }
    },

    // 根据票面金额，年化率，每十万手续费组合
    interestRate: {
      immediate: true,
      handler() {
        this.getAnnualInterest()
      }
    },
    // 监听保证金
    'form.fastTrade'(v) {
      this.$emit('fast-change', v)
    },

    // 监听保证金
    'form.margin'(v) {
      if (!v) {
        this.form.fastTrade = false
      }
    },

    // 监听收款人打码
    'form.receiverMosaic'(v, o) {
      if (o !== undefined) {
        setTimeout(() => {
          this.createFrontImage()
        }, MOSAIC_TIME)
      }
    },

    // 监听出票人打码
    'form.drawerMosaic'(v, o) {
      if (o !== undefined) {
        setTimeout(() => {
          this.createFrontImage()
        }, MOSAIC_TIME)
      }
    },

    // 监听报价切换
    'form.billingMethod'(v) {
      if (v === OFFER_TYPE_CODE.NIAN_LI_LV_JI_SUAN) {
        this.form.annualInterestInput = +this.form.annualInterest === 0 ? null : this.form.annualInterest
        this.form.serviceCharge = null
      }
    },
    // 监听拆分类型
    'form.splitMethod'(v) {
      // 直接拆分
      if (v === SPLIT_TYPE_CODE.DIRECT) {
        this.form.splitType = 1
      } else {
        this.form.splitType = 0
      }
    }

  },
  created() {
    this.form.sellerBankAccountId = this.form.sellerBankAccountId || this.sellerBankAccountId
  },

  methods: {
    // 元转为万单位
    yuan2wan,
    formatTime,

    getMinSplitConfig() {
      return this.$store.state.common.newVersionDraftSplit || 10000
    },

    splitChange(val) {
      const { draftAmount } = this.form
      if (val && draftAmount < this.getMinSplitConfig()) {
        this.form.splitFlag = 0
        this.$message.warning(`票面金额小于${this.getMinSplitConfig()}元的票不可拆分`)
      }
    },

    splitAmtIntChange(val) {
      this.form.splitAmtInt = val
    },

    handleInputSplitAmount() {
      if (this.form.splitAmtInt === '0') {
        this.form.splitAmtInt = ''
      }
      this.form.splitAmtIntSelect = this.form.splitAmtInt
    },

    handleChangeSplitAmount(val) {
      if (!val) {
        this.form.splitAmtInt = 1
        this.form.splitAmtIntSelect = 1
        this.$message.warning('拆分金额必填！')
      }
    },

    splitDisabled() {
      const { subTicketStart, subTicketEnd } = this.form
      return subTicketStart === '0' && subTicketEnd === '0'
    },

    // 获取通用票标签列表
    async getAcceptorCommonLabel(accepterType) {
      const DRAFT_TYPE_CODE = {
        SILVER_NOTE: 1, // 银票
        COMMERCIAL_TICKET: 2, // 商票
        FINANCIAL_TICKET: 3, // 财票
      }
      let draftType = null

      switch (accepterType) {
        case ACCEPTOR_TYPE.CAI_PIAO.id:
          draftType = DRAFT_TYPE_CODE.FINANCIAL_TICKET
          break
        case ACCEPTOR_TYPE.SHANG_PIAO.id:
          draftType = DRAFT_TYPE_CODE.COMMERCIAL_TICKET
          break
        default:
          draftType = DRAFT_TYPE_CODE.SILVER_NOTE
          break
      }
      const res = await commonApi.getAcceptorCommonLabel({ draftType })
      this.acceptorRemarksList = res.rowList
    },

    // 获取承兑人商票标签列表
    async getAcceptorCommercialLabel(acceptorName) {
      this.form.acceptorNotes = []
      const data = await commonApi.getAcceptorCommercialLabel({ acceptorName })
      if (data.rowList && data.rowList.length) {
        if (data.rowList[0].firstLabel) {
          this.customSelectClass = 'no-close-tag1'
          this.form.acceptorNotes.push(data.rowList[0].firstLabel)
        }
        if (data.rowList[0].secondLabel) {
          this.customSelectClass = 'no-close-tag2'
          this.form.acceptorNotes.push(data.rowList[0].secondLabel)
        }
      }
    },

    // 显示报价参考弹窗
    showQuotationReferenceDialog() {
      const {
        draftNo,
        acceptorName,
        acceptorType: accepterType,
        draftAmount,
        maturityDate,
        interestDays: interestAccrualDay,
        endorseCount
      } = this.form
      this.$refs.quotationReferenceDialogRef.init({
        draftNo, // 票号
        acceptorName, // 承兑人
        accepterType, // 承兑人类型
        draftAmount: yuan2wan(draftAmount), // 票面金额
        maturityDate, // 到期日
        interestAccrualDay, // 剩余天数
        endorseCount // 背书手数
      })
    },

    // 获取发布配置
    async getConfig() {
      try {
        const data = await userApi.getPostOrderConfig()
        const postConfig = { ...data, ...data.traderCorpConfig }
        Object.keys(this.contactInfo).forEach(key => {
          this.contactInfo[key] = postConfig[key]
        })
        // 在默认设置的列表中，过滤出未禁用渠道
        const res = this.$store.state.user.paymentAccountList
        this.apiPayList = res
        const defaultPaymentChannelList = postConfig.defaultPaymentChannelList || []
        const list = defaultPaymentChannelList.filter(i => {
          const item = res.find(v => v.paymentChannel === i) || {}
          return item.banStatus !== BAN_STATUS.DISABLE.id
        })
        this.defaultPaymentChannelList = list || []
        const maxBlackLength = 30
        if (postConfig?.areaBlackConfig?.length) {
          postConfig.areaBlackConfig = postConfig.areaBlackConfig.map(v => ({
            cityCode: v.cityCode,
            cityName: v.cityName,
            provinceCode: v.provinceCode,
            provinceName: v.provinceName,
          })).slice(0, maxBlackLength)
        }
        postConfig.receiverMosaic = postConfig.buyerMosaic === 1
        postConfig.drawerMosaic = postConfig.sellerMosaic === 1
        return postConfig
      } catch (error) {
        const REAL_NAME_INVALID = 1014 // 企业JD实名认证失效
        const { code } = error.data
        if (code === REAL_NAME_INVALID) {
          // this.$confirm('<p class="title">该账号未认证或认证状态过期，请先前往账户中心完成企业认证</p>', '', {
          //   dangerouslyUseHTMLString: true,
          //   type: 'warning',
          //   showClose: false,
          //   customClass: 'no-header-msg',
          //   confirmButtonText: '确认',
          //   showCancelButton: false,
          //   width: 490
          // }).then(() => {
          this.$emit('close')
          // })
        }
        return Promise.reject(new Error(error))
      }
    },

    /**
     * 获取默认议价上限
     * @date 2022-01-04
     * @param {any} config 默认配置
     * @param {any} acceptorType 承兑人类型
     * @returns {any}
     */

    getBargainLimitConfig(config, acceptorType) {
      // 默认不议价，返回空
      if (config?.bargain !== 1) {
        return null
      }
      if (acceptorType === ACCEPTOR_TYPE.CAI_PIAO.id) {
        return config?.financialDraftBargainLimit || null
      } else if (acceptorType === ACCEPTOR_TYPE.SHANG_PIAO.id) {
        return config?.commercialDraftBargainLimit || null
      } else {
        return config?.bankDraftBargainLimit || null
      }
    },

    // 输入整数
    handleInput(val, key) {
      let value = val.replace(/\D/g, '')
      this.form[key] = value
    },

    // 获取年利率
    getAnnualInterest() {
      const { draftAmount, lakhDeduction, interestDays, billingMethod, annualInterestInput, serviceCharge } = this.form
      if (billingMethod === OFFER_TYPE_CODE.MEI_SHI_WAN_ZHI_KOU) {
        const { annualInterest, receivedAmount } = lakhDeductionMath(draftAmount, lakhDeduction, interestDays)
        // eslint-disable-next-line no-magic-numbers
        this.form.annualInterest = (+annualInterest).toFixed(4)
        this.form.receivedAmount = receivedAmount
      } else {
        const { annualInterest, receivedAmount, lakhDeduction: rtLakhDeduction } = interestRateMath(draftAmount, annualInterestInput, serviceCharge, interestDays)
        // eslint-disable-next-line no-magic-numbers
        this.form.annualInterest = (+annualInterest).toFixed(4)
        this.form.receivedAmount = receivedAmount
        this.form.lakhDeduction = rtLakhDeduction ? (+rtLakhDeduction).toFixed(2) : null
      }
    },

    // 瑕疵选择回调
    handleChangeFlaw(obj, str) {
      this.form.defects = str
      Object.assign(this.form, obj)
    },
    // 支付渠道选择回调
    handlePayChange(obj) {
      Object.assign(this.form, obj)
    },

    // 切换收款人和出票人打码时，生成对应图片
    async createFrontImage() {
      const key = !this.form.receiverMosaic && !this.form.drawerMosaic
        ? 'origin'
        : `${this.form.receiverMosaic ? 'receiver' : ''}${this.form.drawerMosaic ? 'drawer' : ''}`
      if (this.image[key]) {
        this.form.frontImgUrl = this.image[key]
        return
      }
      const newJson = JSON.parse(JSON.stringify(this.originJson))
      // 收款人数据打码设置
      const receiverMosaicObj = {
        accountNumber: this.buyerMosaicType === 1 ? '******' : newJson.front.payee.accountNumber.replace(/\d{4}$/, '****'),
        bankOfDeposit: this.buyerMosaicType === 1 ? '******' : newJson.front.payee.bankOfDeposit,
        fullName: '******'
      }
      // 出票人数据打码设置
      const drawerObj = {
        accountNumber: this.sellerMosaicType === 1 ? '******' : newJson.front.ticketIssuer.accountNumber.replace(/\d{4}$/, '****'),
        bankOfDeposit: this.sellerMosaicType === 1 ? '******' : newJson.front.ticketIssuer.bankOfDeposit,
        fullName: '******'
      }

      this.form.receiverMosaic && (newJson.front.payee = receiverMosaicObj)
      this.form.drawerMosaic && (newJson.front.ticketIssuer = drawerObj)
      this.recognitionData = newJson

      // 更新图片
      await imgGenerator.screenshot(this.recognitionData, 'front', 'canvas').then(front => {
        const frontImage = front.toDataURL()
        this.image[key] = frontImage
        this.form.frontImgUrl = frontImage
      })
    },
    // 切换报价方式
    handleChangeType() {
      this.form.billingMethod = this.form.billingMethod ? BILLING_METHOD_CODE.SHI_WAN_DISCOUNT : BILLING_METHOD_CODE.ANNUAL_INTEREST
      this.$refs.ruleForm && this.$refs.ruleForm.clearValidate()
    },
    // 设置拆分规则
    showSplistRules() {
      this.$refs.splitRulesDialogRef && this.$refs.splitRulesDialogRef.init()
    },
    setSplitRulesData(data) {
      this.splitRulesData = data
      this.form.splitSetting = data.splitSetting
    },
    // 手机号修改
    // onChangeMobile(data) {
    //   this.form.mobile = data
    // },
  }
}
</script>
