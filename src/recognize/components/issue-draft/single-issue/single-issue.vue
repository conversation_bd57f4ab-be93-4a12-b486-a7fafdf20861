<!-- 单张发布票据 -->
<style lang="scss" scoped>
.single-issue-page {
  .footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid $color-F0F0F0;
    background: $color-F2F2F2;

    .footer-a {
      color: $color-008489;
    }

    .footer-left {
      display: flex;
      align-items: center;

      .protocol-tip {
        color: $color-text-primary;
      }
    }

    .footer-right {
      .el-button {
        font-size: 16px;
      }
    }

    .directional-btn {
      border-color: $--color-primary;
      color: $--color-primary;

      &.is-disabled {
        border-color: #EBEEF5;
        color: #C0C4CC;
      }
    }
  }

  .jisu-btn {
    position: relative;
  }

  .cash-award-tag {
    position: absolute;
    top: -16px;
    right: -1px;

    @include new-icon(65px,20px);
  }

  .bught-sphere {
    position: relative;

    .bought-deal {
      top: -35px !important;
    }
  }
}
</style>

<style>
.alert-red-high-light {
  font-weight: 600;
  color: #FF5A5F;
}
</style>

<template>
  <el-dialog
    class="single-issue-page"
    title="发布票据"
    :visible.sync="dialogVisible"
    append-to-body
    width="1140px"
    :before-close="handleCancel"
  >
    <div class="single-issue-container">
      <div v-if="false" class="bught-sphere">
        <BoughtDeal ref="boughtDealRef">
          <div class="go-icon" @click="goCounpCerter"> <icon type="header-notice-logo" size="20" /></div>
        </BoughtDeal>
      </div>
      <DraftInfo
        :key="dialogVisible"
        ref="DraftInfo"
        :data="draftData"
        @fast-change="(v) => { isFastTrade = v }"
        @close="handleCancel"
      />
    </div>
    <div class="footer">
      <div class="footer-left">
        <!--
          <el-checkbox v-model="isAgreeProtocol">
          <span class="protocol-tip">我已阅读并同意</span>
          </el-checkbox>
        -->
        <!--
          <span v-if="draftData.draftType" class="text-link" @click="openProtocol(OSS_FILES_URL.AGREEMENT_OF_TRANSFER_NEW_DRAFT)">《新一代票据收益权转让合同》</span>
          <span v-else class="text-link" @click="openProtocol(OSS_FILES_URL.AGREEMENT_OF_TRANSFER)">《票据收益权转让合同》</span>
        -->
        <!-- <NewDraftTipView v-if="draftData.draftType" ref="newDraftTipViewRef" /> -->
      </div>
      <div class="footer-right">
        <el-button size="large" @click="handleCancel">取消</el-button>
        <transaction-tooltip-button
          v-if="!limitLight && !mobilePattern"
          v-waiting="['post::loading::https://shendu-qa.oss-cn-hangzhou.aliyuncs.com/', 'get::loading::/operation/common/upload/policy', 'post::loading::/draft/postOrder']"
          class="jisu-btn"
          size="large"
          border
          :types="[TRANSACTION_TOOLTIP_TYPE.CREDIT]"
          @click="handleFastTrade('')"
        >
          极速发布
          <!-- <div class="cash-award-tag">现金奖励</div> -->
        </transaction-tooltip-button>
        <transaction-tooltip-button
          v-if="!mobilePattern"
          v-waiting="['post::loading::https://shendu-qa.oss-cn-hangzhou.aliyuncs.com/', 'get::loading::/operation/common/upload/policy', 'post::loading::/draft/postOrder']"
          class="directional-btn"
          size="large"
          border
          :types="isFastTrade ? [TRANSACTION_TOOLTIP_TYPE.HOLIDAY, TRANSACTION_TOOLTIP_TYPE.DAILY] : [TRANSACTION_TOOLTIP_TYPE.HOLIDAY]"
          @click="handleDirectionShow"
        >
          定向发布
        </transaction-tooltip-button>
        <transaction-tooltip-button
          v-waiting="['post::loading::https://shendu-qa.oss-cn-hangzhou.aliyuncs.com/', 'get::loading::/operation/common/upload/policy', 'post::loading::/draft/postOrder']"
          size="large"
          :types="[TRANSACTION_TOOLTIP_TYPE.CREDIT]"
          @click="handlePublish('')"
        >
          {{ type === 'recognition' ? '确认发布' : '确认修改' }}
        </transaction-tooltip-button>
      </div>
    </div>
    <!-- 定向发布弹窗 -->
    <DirectionIssueDialog
      ref="DirectionIssueDialogRef"
      :drafts="draftData"
      :is-show-seller-bank-account="isShowSellerBankAccount"
      @confirm="handleConfirmDirection"
      @open-margin="openMargin"
    />

    <!-- 发布结果状态弹窗 -->
    <ResultStatusDialog ref="resultStatusDialogRef" title="连号票发布中" @confirm="handleCancel" />

    <!-- 支付账户异常弹窗 -->
    <PayAccountError ref="payAccountErrorRef" />

    <!-- 光速交易订单开启确认弹窗 -->
    <FastTradeDialog
      ref="fastTradeDialogRef"
      @publish="handlePublish"
      @direction-publish="handleDirectionShow"
      @agree="agreeFastTrade"
    />
    <!-- 西部信托利率提示弹窗 -->
    <RateTipsDialog ref="rateTipsRef" @on-publish="({form, reqObj}) => issueDraft(form, reqObj)" />
  </el-dialog>
</template>

<script>
import DraftInfo from './draft-info.vue'
import DirectionIssueDialog from '../components/direction-issue-dialog.vue' // 定向发布弹窗
import ResultStatusDialog from '../components/result-status-dialog.vue' // 发布结果状态弹窗
import { TICKET_STATUS_CHANGE } from '@recognize/ipc-event-constant' // 发布后修改票据状态通知
import { PLATFORM_DEFAULT_RULESNEW_URL, OSS_FILES_URL } from '@/constants/oss-files-url' // 平台订单违约规则url
// 金额单位转换
import { lakhDeductionMathWithBargain, interestRateMathWithBargain } from '@/common/js/draft-math'
import {
  ACCEPTOR_TYPE, // 承兑人类型
  OFFER_TYPE_CODE, // 报价类型
  BACK_DEFECT_TYPE_NOT_ALLOW_NUM_MAP, // 票据瑕疵类型 id 映射 notAllowedToPublish 是否可发布
  TRANSACTION_TOOLTIP_TYPE, // 交易限制提示类型
  SPLIT_TYPE_CODE
} from '@recognize/constant'
import ticketApi from '@recognize/apis/ticket'
import mixpanel from '@recognize/utils/mixpanel'
import { toDefectStr } from '@/common/js/draft-flaw' // 将原始瑕疵字符串转为渲染字符串
import PayAccountError from '../components/pay-account-error.vue'
import FastTradeDialog from '../components/fast-trade-dialog.vue' // 光速交易订单开启确认弹窗
import { windowCommunication } from '@/utils/window-event'
import { yuan2fen, wan2yuan, yuan2wan } from '@/common/js/number'
import { mapGetters } from 'vuex'
// import NewDraftTipView from '../components/new-draft-tip-view-rec.vue' // 新票提示控件
import BoughtDeal from '@/views/components/levitated-sphere/bought-deal.vue'
import RateTipsDialog from '@/views/components/issue-draft/rate-tips-dialog.vue'
import PublishCheck from '@/views/pages/issue-draft/mixins/publish-check.js'

export default {
  name: 'single-issue',

  components: {
    DraftInfo, // 票面信息
    DirectionIssueDialog, // 定向发布弹窗
    ResultStatusDialog, // 发布结果状态弹窗
    PayAccountError, // 支付账户异常弹窗
    FastTradeDialog, // 光速交易订单开启确认弹窗
    // NewDraftTipView,
    BoughtDeal,
    RateTipsDialog
  },
  mixins: [PublishCheck],
  data() {
    return {
      SPLIT_TYPE_CODE, // 拆分类型
      OSS_FILES_URL,
      PLATFORM_DEFAULT_RULESNEW_URL,
      TRANSACTION_TOOLTIP_TYPE, // 交易限制提示类型
      dialogVisible: false,
      isFastTrade: false, // 是否光速交易
      type: 'recognition', // 类型 recognition 为发布票据 recognitionEdit 为编辑票据
      read: true, // 是否已阅读
      draftData: {}, // 票据信息
      // 年利率上限
      annualInterestLimit: {
        银票: 12,
        财票: 25,
        商票: 36,
      },
      publishStartTime: null, // 发布开始时间
      publishEndTime: null, // 发布结束时间
      isAgreeFastTrade: false, // 是否已确认光速交易
      isAgreeProtocol: true, // 是否已同意协议
      userInfo: null, // 用户信息
      isAcceptJdYlpay: false,
      isShowSellerBankAccount: false // 定向发布是否显示汇款账户（邦+、E+显示）
    }
  },
  computed: {
    ...mapGetters('user', { limitLight: 'limitLight' }), // 是否限制极速
    ...mapGetters('common', {
      mobilePattern: 'mobilePattern', // 是否开启联系方式模式 1开启 0关闭
    }),
  },
  created() {
    this.userInfo = this.$store.dispatch('recognize-user/getUserInfo')
  },

  methods: {
    open(obj) {
      // 新手客户不熟悉签手批量识别规则，批量识别不识别瑕疵，所以需要在 单张发布页面弹出之后给与 Toast提示
      if (obj?.data?.discernType === 2) {
        this.$message.warning('批量识别的票据，不支持识别瑕疵，请手动修改瑕疵')
      }

      this.draftData = obj && obj.data
      this.type = obj.type
      this.$store.dispatch('common/getCloseMarket')
      this.$store.dispatch('recognize-common/getFastLimit')
      this.$store.dispatch('recognize-user/getCorpInfo')
      this.$store.dispatch('recognize-user/getShowCreditRemind')
      this.$nextTick().then(() => {
        this.$refs.newDraftTipViewRef && this.$refs.newDraftTipViewRef.init()
      })
      this.dialogVisible = true

      // this.$nextTick().then(() => {
      //   this.$refs.boughtDealRef.init()
      // })
    },

    goCounpCerter() {
      this.handleCancel()
      const url = '/user-center/coupon?tabs=receiveCentre'
      if (this.$ipc) {
        this.$ipc.send('PUSH_ROUTE_IN_MAIN_WINDOW', url)
        this.$ipc.send('CLOSE')
      } else {
        this.$router.push(url)
      }
    },
    // 点击取消
    handleCancel() {
      this.$refs.newDraftTipViewRef && this.$refs.newDraftTipViewRef.hideTip()
      // 清除拆分设置缓存数据
      this.$store.commit('issue-draft/setSplitRulesArr', null)
      this.dialogVisible = false
    },
    // 定向发布弹窗显示
    handleDirectionShow() {
      const { form } = this.$refs.DraftInfo
      // 定向发布是否显示汇款账户（邦+、E+显示）
      this.isShowSellerBankAccount = !!form.acceptJdYlpay || !!form.acceptZbankPlus
      const reqObj = JSON.parse(JSON.stringify(form))
      if (!this.validInput(reqObj)) return
      if (!this.validFastTrade(reqObj, 'direction')) return
      if (!this.validProtocol()) return
      if (form.bargaining) {
        this.$confirm('<div>若选择定向发布，将自动为您关闭议价选项</div>', '提示', {
          dangerouslyUseHTMLString: true,
          type: 'warning',
          showClose: false,
          iconPosition: 'title',
          cancelButtonText: '再想想',
          confirmButtonText: '确认',
        }).then(() => {
          this.$refs.DraftInfo.form.bargaining = false
          this.$refs.DraftInfo.form.bargainingLimit = undefined
          this.$refs.DirectionIssueDialogRef.toggle(this.$refs.DraftInfo.form.margin)
        })
      } else {
        this.$refs.DirectionIssueDialogRef.toggle(this.$refs.DraftInfo.form.margin)
      }
    },
    // 定向发布弹窗确认
    handleConfirmDirection(inviteCode) {
      this.handlePublish(inviteCode)
    },
    // 极速发布
    async handleFastTrade(inviteCode) {
      // 极速发布之前打开极速按钮和带保接单按钮
      await this.openMargin()
      await this.openFastTrade()
      await this.handlePublish(inviteCode)
    },
    // 确认发布
    async handlePublish(inviteCode) {
      const { form } = this.$refs.DraftInfo
      // const { mobiles } = this.$refs.DraftInfo.$refs.contactSetting
      // 拦截“7”和“8”开头的新一代票据发布
      if (/^[78]/.test(form.draftNo)) {
        this.$message.error('暂不支持发布，银行暂不支持7、8开头的供应链票据流转')
        return
      }
      if (!form.payChannelName) {
        this.$message.info('请选择支付渠道')
        return
      }
      if (form.acceptJdYlpay && !form.sellerBankAccountId) {
        this.$message.info('请选择回款账户')
        return
      }
      // if (!form.mobile || !form.mobile.length) {
      //   this.$message.info('请先添加联系方式')
      //   return
      // }
      // form.mobile = form.mobile.map(e => ({ mobile: e.mobile, mobileName: e.mobileName, id: e.id }))
      const reqObj = JSON.parse(JSON.stringify(form))
      if (
        this.validDeffectNotAllow(reqObj.originalDefects)
      && this.validInput(reqObj)
      && this.validRate(reqObj)
      && this.validFastTrade(reqObj)
      && this.validProtocol()
      && this.validSplitAmt(reqObj)
      // && this.validContact(reqObj)
      ) {
        // // 缓存联系方式
        // if (form.mobile) {
        //   Storage.set(TEMP_CONTACT_LIST_DATA, form.mobile)
        // }
        if (this.isFastTrade) {
          this.agreeFastTrade()
        }
        // // 连号票发布显示loading
        // if (reqObj.serialDraftNum) {
        //   this.handleResultStatusDialogShow()
        // }

        reqObj.annualInterest = reqObj.annualInterestInput
        reqObj.sellerMosaic = reqObj.drawerMosaic ? 1 : 0
        reqObj.buyerMosaic = reqObj.receiverMosaic ? 1 : 0
        delete reqObj.drawerMosaic
        delete reqObj.receiverMosaic
        delete reqObj.receivedAmount
        delete reqObj.annualInterestInput
        delete reqObj.frontImgUrl
        delete reqObj.backImgUrl

        reqObj.bargaining = reqObj.bargaining ? 1 : 0
        reqObj.fastTrade = reqObj.fastTrade ? 1 : 0
        reqObj.margin = reqObj.margin ? 1 : 0
        reqObj.serialDraft = reqObj.serialDraft && reqObj.serialDraftNum ? 1 : 0
        reqObj.inviteCode = inviteCode
        if (reqObj.draftType && reqObj.splitFlag) {
          // 定额拆分
          if (reqObj.splitMethod === SPLIT_TYPE_CODE.QUOTA) {
            // eslint-disable-next-line max-depth
            if (reqObj.splitAmtInt) {
              reqObj.splitAmtMin = wan2yuan(yuan2fen(reqObj.splitAmtInt))
            }
            // eslint-disable-next-line max-depth
            if (reqObj.intMultiple) {
              reqObj.integerMultiples = 1
            } else {
              reqObj.integerMultiples = 2
            }
            reqObj.splitAmtMax = null
            reqObj.splitSetting = null // 区间定额拆分 拆分规则数据初始化为null
          }
          // 区间拆分
          if (reqObj.splitMethod === SPLIT_TYPE_CODE.INTERVAL) {
            reqObj.integerMultiples = 0
            reqObj.splitAmtMin = yuan2fen(reqObj.splitAmtMin)
            reqObj.splitAmtMax = yuan2fen(reqObj.splitAmtMax)
            reqObj.splitSetting = null // 区间定额拆分 拆分规则数据初始化为null
          }

          // 直接拆分
          if (reqObj.splitMethod === SPLIT_TYPE_CODE.DIRECT) {
            reqObj.splitSetting = reqObj.splitSetting.map(item => ({
              splitAmt: wan2yuan(item.splitAmt) || 0,
              splitCount: item.splitCount,
              billingMethod: item.billingMethod,
              lakhDeduction: item.lakhDeduction,
              annualInterest: item.annualInterest,
              serviceCharge: item.serviceCharge
            }))
          }
        }
        reqObj.splitMethod = null
        reqObj.splitAmtInt = null
        reqObj.splitAmtIntSelect = null
        reqObj.intMultiple = null

        if (inviteCode) {
          this.issueDraft(form, reqObj)
        } else {
          let acceptor = {
            acceptorName: form.acceptorName,
            annualInterest: form.annualInterest,
            interestDays: form.interestDays
          }
          let rateRes = await this.checkIntersetRate({ acceptorOrders: [acceptor], type: 1 }, { form, reqObj })
          if (!rateRes.showPopUpFlag) {
            this.issueDraft(form, reqObj)
          }
        }
      }
    },
    async issueDraft(form, reqObj) {
      // 连号票发布与单张发布不同接口
      if (reqObj.serialDraftNum) {
        try {
          this.handleResultStatusDialogShow()
          this.publishStartTime = Date.now()
          const res = await ticketApi.postSerialDraftOrder(reqObj)
          this.handleResultStatusDialogShow(res)
          this.publishEndTime = Date.now()
          this.mixpanelPublish(form, reqObj)
        } catch (err) {
          const { code, msg } = err.data
          this.validErrorMsg(code, msg, form, reqObj)
          this.$refs.resultStatusDialogRef.close()
        }

        return
      }
      try {
        this.publishStartTime = Date.now()
        await ticketApi.postRelease(reqObj)
        this.publishEndTime = Date.now()
        this.mixpanelPublish(form, reqObj)
        this.handleCancel()
        this.$event.emit(TICKET_STATUS_CHANGE, {
          msg: '发布成功'
        })
        this.dialogVisible = false
        // 更新回款账户选中
        this.$store.dispatch('common/getNewVersionDraftConfig')
      } catch (err) {
        const { code, msg } = err.data
        this.validErrorMsg(code, msg, form, reqObj)
      }
    },

    // 发布结果状态弹窗
    handleResultStatusDialogShow(data) {
      this.$refs.resultStatusDialogRef.init(data)
    },

    /**
     * 校验错误提示
     * @param {String} code 错误码
     * @param {String} msg 提示
     * @param {Object} form 表达obj
     * @param {Object} reqObj 请求obj
     */
    validErrorMsg(code, msg, form, reqObj) {
      const SELLER_BLACK_LIST_ERROR = 3011 // 在对方黑名单错误码
      const PUBLISH_BLACK_LIST_ERROR = 3013 // 在自己黑名单错误码
      const ORDER_EXIT_ACCEPT_RISK = 1015 // 存在承兑风险
      const PAY_CHANNEL_BUYER_NOT_SUPPORT = 1016 // 定向交易对手不支持所选支付方式
      const PAY_CHANNEL_FAIL = 1018 // 支付渠道失效
      const CREDIT_TYPE_GENERAL = 3026 // 当前用户信用等级一般
      const REAL_NAME_INVALID = 1014 // 企业JD实名认证失效
      const PAY_CHANNEL_BAN = 1020 // 支付渠道禁用
      const GAO_MAI_DI_MAI = 5036 // 高买低卖

      if (code === GAO_MAI_DI_MAI) {
        this.$alert('您因触发平台机制，被限制登录，请联系您的客户经理', '提示', {
          confirmButtonText: '我知道了',
        }).then(() => this.$store.dispatch('user/logout', { manual: true }))
          .then(() => {
            this.handleCancel()
            this.$message.closeAll()
            if (this.$ipc) {
              this.$ipc.send('PUSH_ROUTE_IN_MAIN_WINDOW', '/login')
              this.$ipc.send('CLOSE')
            } else {
              this.$router.push('/')
            }
            windowCommunication.trigger()
          })
        return
      } else if (code === REAL_NAME_INVALID) {
        // this.$confirm('<p class="title">该账号未认证或认证状态过期，请先前往账户中心完成企业认证</p>', '', {
        //   dangerouslyUseHTMLString: true,
        //   type: 'warning',
        //   showClose: false,
        //   customClass: 'no-header-msg',
        //   confirmButtonText: '确认',
        //   showCancelButton: false,
        //   width: 490
        // })
      } else if (code === SELLER_BLACK_LIST_ERROR) {
        this.$message.error('交易对手已暂停与您定向交易的权限！')
      } else if (code === PUBLISH_BLACK_LIST_ERROR) {
        this.$message.error('您已将交易对手列入黑名单，请先将其移出接单方黑名单后再交易！')
      } else if (code === PAY_CHANNEL_BUYER_NOT_SUPPORT) {
        this.$message.error('交易对手暂未开通该支付渠道，无法发起订单交易。')
      } else if (code === ORDER_EXIT_ACCEPT_RISK) {
        this.$confirm(`<div>票据存在承兑风险</div><div>承兑人：${reqObj.acceptorName}</div><div>如有疑问请联系客户经理</div>`, '提示', {
          dangerouslyUseHTMLString: true,
          type: 'warning',
          showClose: false,
          iconPosition: 'title',
          confirmButtonText: '确定',
          showCancelButton: false,
        })
      } else if (code === PAY_CHANNEL_FAIL) {
        this.$refs.payAccountErrorRef.init(msg)
      } else if (code === CREDIT_TYPE_GENERAL) {
        this.$confirm('<div>您当前的信用等级为 <span class="red-soso">一般</span>。只可发布带保证金订单。</div><div>前往 <span class="red-soso">账户信息-我的信用</span> 可以查看信用分规则。</div>', '提示', {
          dangerouslyUseHTMLString: true,
          type: 'warning',
          showClose: false,
          iconPosition: 'title',
          cancelButtonText: '取消',
          confirmButtonText: '开启保证金',
        }).then(() => {
          this.openMargin()
        })
      } else if (code === PAY_CHANNEL_BAN) {
        this.$message.error(msg)
      } else {
        this.$message.error(`发布失败：${msg}`)
      }
      this.handleCancel()
      this.$event.emit(TICKET_STATUS_CHANGE)
      this.mixpanelPublish(form, reqObj, msg)
    },

    /**
     * 埋点
     * @param {Object} form 表达obj
     * @param {Object} reqObj 请求obj
     * @param {*} failReason 失败原因
     */
    mixpanelPublish(form, reqObj, failReason) {
      mixpanel.publishDraft({
        票号: form.draftNo,
        票面金额: form.draftAmount,
        承兑人: form.acceptorName,
        到期日: form.maturityDate,
        瑕疵情况: toDefectStr(form.defects) || '无瑕疵',
        每十万扣息: form.lakhDeduction,
        年利率: `${form.annualInterest}%`,
        支付渠道: form.payChannelName || '',
        是否光速交易: form.fastTrade ? '是' : '否',
        是否带保订单: form.margin ? '是' : '否',
        是否定向: reqObj.inviteCode ? '是' : '否',
        是否议价: form.bargaining ? '是' : '否',
        议价信息: form.bargainingLimit ? form.bargainingLimit : '',
        是否出票人打码: form.drawerMosaic ? '是' : '否',
        是否收款人打码: form.receiverMosaic ? '是' : '否',
        是否连号票: form.serialDraftNum ? '是' : '否',
        发布失败原因: failReason || '',
        单张发布时间: failReason || ((this.publishEndTime - this.publishStartTime) / 1000),
        企业名称: this.userInfo.corpName,
      })
    },
    // 校验不允许发布的瑕疵
    validDeffectNotAllow(originalDefects) {
      if (!originalDefects) {
        return true
      }
      const originalDefectList = originalDefects.split('|').filter(item => item)
      const notAllow = originalDefectList.some(defect => {
        const type = defect.split('_')[0]
        return BACK_DEFECT_TYPE_NOT_ALLOW_NUM_MAP[type]
      })
      if (notAllow) {
        this.$confirm('<span class="title">提示</span><p class="message">「保证待签收」「转让背书银行」「不可转让」「商票银行承兑」的票据不可发布。</p>', '', {
          dangerouslyUseHTMLString: true,
          type: 'warning',
          showClose: false,
          customClass: 'no-header-msg',
          confirmButtonText: '我知道了',
          showCancelButton: false,
          width: 490
        })
        return false
      }
      return true
    },

    // 校验输入值
    validInput(obj) {
      this.$refs.DraftInfo.isValid = true

      // 报价是否填写
      const billing = (obj.billingMethod === OFFER_TYPE_CODE.MEI_SHI_WAN_ZHI_KOU && (!obj.lakhDeduction || +obj.lakhDeduction === 0))
       || (obj.billingMethod === OFFER_TYPE_CODE.NIAN_LI_LV_JI_SUAN && (!obj.annualInterest || +obj.annualInterest === 0))

      // 拆分模式打开 且 是非直接拆分
      const splitStatus = obj.splitFlag && obj.splitMethod !== SPLIT_TYPE_CODE.DIRECT

      // 拆分模式关闭 或者 拆分模式打开非直接拆分类型 校验报价信息
      if ((!obj.splitFlag || splitStatus) && billing) {
        this.$message.info('有未填写报价信息的票据')
        return false
      }
      // if ((obj.billingMethod === OFFER_TYPE_CODE.MEI_SHI_WAN_ZHI_KOU && (!obj.lakhDeduction || +obj.lakhDeduction === 0))
      //  || (obj.billingMethod === OFFER_TYPE_CODE.NIAN_LI_LV_JI_SUAN && (!obj.annualInterest || +obj.annualInterest === 0))
      // ) {
      //   this.$message.info('有未填写报价信息的票据')
      //   return false
      // }

      // 拆分模式为直接拆分
      if (obj.splitFlag && obj.splitMethod === SPLIT_TYPE_CODE.DIRECT) {
        // 未设置拆分规则
        if ((!obj?.splitSetting || !obj?.splitSetting.length)) {
          this.$message.info('请先设置拆分规则')
          return false
        }
        // 拆分规则张数大于200
        if (this.$refs.DraftInfo.splitRulesData.totalSplitNum > 200) {
          this.$message.info('合计拆分张数不得超过200张，请修改设置')
          return false
        }
        // 合计拆分金额必须等于票面金额
        if (this.$refs.DraftInfo.splitRulesData.totalSplitAmount !== yuan2wan(obj.draftAmount)) {
          this.$message.info('合计拆分金额必须等于票面总金额，请修改设置')
          return false
        }
      }

      if (this.$refs.DraftInfo.isMoreThan500W && (obj.acceptYlpay === 1) && (!obj.tradeContractUrl || !obj.invoiceUrl)) {
        let names = obj.payChannelName.split(',')
        // eslint-disable-next-line max-statements-per-line
        if (names.length) { names = names.filter(item => item === '智联通') }
        this.$message.info(`选择${names.join(',')}支付渠道发布500万以上票据必须上传贸易合同和发票`)
        return false
      }

      return true
    },

    // 校验利率
    validRate(obj) {
      const { acceptorType, annualInterestInput, billingMethod, lakhDeduction, interestDays, bargaining, bargainingLimit, serviceCharge } = obj
      let newAnnualInterest = billingMethod === OFFER_TYPE_CODE.MEI_SHI_WAN_ZHI_KOU
        ? lakhDeductionMathWithBargain(lakhDeduction, interestDays, bargainingLimit || 0)
        : interestRateMathWithBargain(annualInterestInput, serviceCharge, interestDays || 0, bargainingLimit || 0)
      if (
        (acceptorType === ACCEPTOR_TYPE.CAI_PIAO.id && newAnnualInterest > this.annualInterestLimit['财票'])
        || (acceptorType === ACCEPTOR_TYPE.SHANG_PIAO.id && newAnnualInterest > this.annualInterestLimit['商票'])
        || (acceptorType !== ACCEPTOR_TYPE.CAI_PIAO.id && acceptorType !== ACCEPTOR_TYPE.SHANG_PIAO.id && newAnnualInterest > this.annualInterestLimit['银票'])) {
        this.$message({
          dangerouslyUseHTMLString: true,
          message: `${bargaining
            ? '<p style="white-space: nowrap;">议价上限 + 报价超过年化利率发布限制(银票 12%/财票 25%/商票 36%)，请重新输入</p>'
            : '<p>年化利率超过发布限制(银票 12%/财票 25%/商票 36%)，请重新输入'}</p>`,
        })
        return false
      }
      return true
    },

    // 校验输入值
    validSplitAmt(obj) {
      if (obj.draftType && obj.splitFlag) {
        // 定额拆分
        if (obj.splitMethod === SPLIT_TYPE_CODE.QUOTA) {
          if (!obj.splitAmtInt) {
            this.$message.info('请输入拆分金额')
            return false
          } else {
            let { draftAmount } = obj
            let min = wan2yuan(obj.splitAmtInt)
            // eslint-disable-next-line max-depth
            if (min < this.getMinSplitConfig()) {
              this.$message.info(`拆分金额需大于或等于${this.getMinSplitConfig()}元`)
              return false
            } else if (min > draftAmount) {
              this.$message.info('请输入小于票面金额的正整数！')
              return false
            }
          }
        } else if (obj.splitMethod === SPLIT_TYPE_CODE.INTERVAL) { // 区间拆分
          if (!obj.splitAmtMin || !obj.splitAmtMax) {
            this.$message.info('拆分金额必须填写')
            return false
          }
          let { draftAmount } = obj
          let min = Number(obj.splitAmtMin)
          let max = Number(obj.splitAmtMax)
          if (draftAmount > this.getMinSplitConfig()) {
            if (min < this.getMinSplitConfig()) {
              this.$message.info(`最低可拆分金额需大于或等于${this.getMinSplitConfig()}元`)
              return false
            } else if (min > max) {
              this.$message.info('最高可拆分金额需大于或等于最低可拆分金额')
              return false
            } else if (max > draftAmount) {
              this.$message.info('最高可拆分金额不能大于票面金额')
              return false
            }
          } else if (min !== draftAmount) {
            this.$message.info(`票面金额小于${this.getMinSplitConfig()}元时,最低可拆分金额需等于票面金额`)
            return false
          } else if (max !== draftAmount) {
            this.$message.info(`票面金额小于${this.getMinSplitConfig()}元时,最高可拆分金额需等于票面金额`)
            return false
          }
        }
      }
      return true
    },

    getMinSplitConfig() {
      return this.$store.state.common.newVersionDraftSplit || 10000
    },

    // 打开保证金
    openMargin() {
      this.$refs.DraftInfo.form.margin = true
    },
    // 打开极速发布
    openFastTrade() {
      this.$refs.DraftInfo.form.fastTrade = true
    },

    // 校验打开光速交易订单开启确认弹窗 {limitLight 开关打开才判断}
    validFastTrade(obj, from) {
      if (!this.limitLight && obj.fastTrade && !this.isAgreeFastTrade) {
        this.$refs.fastTradeDialogRef.init(from)
        return false
      }
      return true
    },

    // 是否已确认光速交易
    agreeFastTrade() {
      this.isAgreeFastTrade = !this.isAgreeFastTrade
    },

    // 打开服务协议
    openProtocol(url) {
      window.open(url)
    },

    // 检查是否勾选服务协议
    validProtocol() {
      if (!this.isAgreeProtocol) {
        this.$message.info('请勾选服务协议')
        return false
      }
      return true
    },
  }
}
</script>
