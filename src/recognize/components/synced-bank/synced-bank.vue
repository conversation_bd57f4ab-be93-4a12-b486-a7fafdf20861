<style lang="scss" scoped>
.working {
  display: flex;
  padding: 13px 0 18px;
  font-size: 16px;
  color: $color-text-primary;

  .bank-num {
    margin: 0 4px;
    font-weight: 600;
    color: $--color-primary;
  }
}

.spicon-question {
  margin-left: 5px;
}

.question-tip {
  font-size: 14px;

  .question-tip-first {
    margin-bottom: 5px;
  }
}

.bank-list {
  border: 1px solid #F0F0F0;
  border-bottom: none;
  width: 100%;

  ::v-deep {
    .el-table .el-table__header th,
    .el-table .el-table__body td {
      color: $color-text-primary;
    }
  }
}

.bank-list-items {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid $color-D9D9D9;
  padding: 0 12px;
  height: 38px;
  box-sizing: border-box;
}

.sync-btn {
  border-color: $--color-primary;
  color: $--color-primary;
}
</style>

<template>
  <el-dialog
    title="自动同步"
    :visible.sync="dialogVisible"
    width="600px"
    append-to-body
  >
    <div class="synced-bank">
      <div class="working">
        <div>
          当前有<b class="bank-num">{{ listData.length }}</b>家银行已连接
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content" class="bargaining-tip">
              <p>请保持网银连接</p>
              <p>可以自动同步新的待签收票据。</p>
            </div>
            <icon class="icon icon-question spicon-question" type="chengjie-wenti" />
          </el-tooltip>
        </div>
      </div>
      <div class="bank-list">
        <el-table
          :data="listData"
          height="352"
        >
          <el-table-column
            prop="bankType"
            label="银行名称"
            width="138"
          />
          <el-table-column
            prop="companyName"
            label="企业名称"
            width="284"
          />
          <el-table-column
            label="操作"
          >
            <template v-slot:default="{ row }">
              <el-button class="sync-btn" @click="cancelMerge(row)">取消同步</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import {
  MARKET_DRAFT_DELETE_BANK_SYNC, // 删除已经同步的网银
  MARKET_DRAFT_ADD_MONITOR_BANK_SUCCESS, // 同步识别成功的网银
} from '@recognize/ipc-event-constant'

export default {
  name: 'synced-bank',

  data() {
    return {
      dialogVisible: false,
      value: null,
      listData: [
        {
          bankType: '111',
          companyName: '111',
        }
      ], // 列表数据
    }
  },

  created() {
    if (this.$ipc) {
      this.$ipc.on(MARKET_DRAFT_ADD_MONITOR_BANK_SUCCESS, this.addMonitorBank)
    }
  },

  methods: {
    open(data) {
      if (!data) return
      this.listData = data
      this.dialogVisible = true
    },

    // 取消同步,删除不在需要监控的网银同步
    cancelMerge(row) {
      this.$confirm('确定要取消该网银同步吗？', '取消同步', {
        type: 'warning',
        showClose: false,
        confirmButtonText: '确认'

      })
        .then(() => {
          this.confirmCancelMerge(row)
        })
    },

    // 确认取消同步
    async confirmCancelMerge(row) {
      await setTimeout(() => {
        this.listData.forEach((item, index) => {
          if (row.companyName === item.companyName) {
            this.listData.splice(index, 1)
          }
        })
        this.$ipc.send(MARKET_DRAFT_DELETE_BANK_SYNC, { companyName: row.companyName, bankType: row.bankType, listData: this.listData })
      }, 300) // eslint-disable-line
    },

    // 更新列表
    addMonitorBank(event, data) {
      this.listData = data
    }
  }
}
</script>
