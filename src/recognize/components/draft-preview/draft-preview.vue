<style lang="scss" scoped>
// 滚动条优化
.window-draft-detail {
  overflow: hidden;
  padding-bottom: 12px;

  ::v-deep .content {
    overflow-x: hidden;
    overflow-y: auto;
  }

  ::-webkit-scrollbar {
    width: 12px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    padding: 2px;
    background-color: $color-F4F5F6;
  }

  ::-webkit-scrollbar-track-piece {
    border: 2px solid transparent;
    border-radius: 12px;
    background: none;
    box-shadow: 8px 0 0 $border-color-lighter inset;
  }

  // 滑块
  ::-webkit-scrollbar-thumb {
    border: 2px solid transparent;
    border-radius: 12px;
    background: none;
    box-shadow: 8px 0 0 $color-D9D9D9 inset;
  }

  ::-webkit-scrollbar-thumb:hover {
    box-shadow: 8px 0 0 $color-D9D9D9 inset;
  }
}

.operation {
  position: absolute;
  top: 84px;
  right: 36px;
  display: flex;
}

.turn-over,
.download-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 2px;
  width: 108px;
  height: 34px;
  font-size: 14px;
  text-align: center;
  color: $color-FFFFFF;
  background: $color-text-primary;
  opacity: .9;
  line-height: 32px;
  cursor: pointer;

  .icon-swap,
  .icon-download {
    margin-right: 10px;
  }
}

.turn-over {
  margin-right: 12px;
}
</style>

<template>
  <el-dialog
    title="票面截图"
    :visible.sync="dialogVisible"
    width="1024px"
    append-to-body
  >
    <div class="window-draft-detail">
      <div class="operation">
        <div class="turn-over" @click="turnOver">
          <icon class="icon-swap" type="chengjie-swap" :size="18" />
          <span>切至{{ type === 'front' ? '背面' : '正面' }}</span>
        </div>
        <div class="download-btn" @click="copyImg">
          <icon class="icon-download" type="chengjie-copy" :size="18" />
          <span>复制票面</span>
        </div>
      </div>
      <!-- 票据截图生成组件 -->

      <MarketDraftImage
        v-if="draftData && (draftData.front || draftData.back)"
        ref="marketDraftImage"
        class="market-draft-image"
        :data="draftData"
        :type="type"
      />
    </div>
  </el-dialog>
</template>

<script>
import {
  DOWNLOAD_FILES, // 文件下载事件
} from '@recognize/ipc-event-constant'
// 票据正面和背面截图生成组件
import MarketDraftImage from '@/views/components/market-draft-image/market-draft-image.vue'
import imgGenerator from '@/views/components/market-draft-image-generator/market-draft-image-generator.js'
import { blobToBuffer, base64ToFile } from '@/common/js/util'
import { browserType } from '@/common/js/env'
export default {
  name: 'draft-preview',

  components: {
    MarketDraftImage,
  },

  data() {
    return {
      dialogVisible: false,
      draftData: null, // 票据数据
      type: 'front', // 图片类型，正面或背面。front/back
    }
  },

  methods: {
    open(obj) {
      if (!obj) return
      const { data, type } = obj
      this.draftData = data
      this.type = type || 'front'
      this.dialogVisible = true
    },

    // 翻面
    turnOver() {
      this.type = this.type === 'front' ? 'back' : 'front'
    },

    // 保存正背面图片
    async downloadImg() {
      if (!this.$ipc) return
      if (this.type === 'front') {
        const res = await imgGenerator.screenshot(this.draftData, 'front', 'canvas')
        const result = await blobToBuffer(res)
        const saveResult = await this.$ipc.invoke(DOWNLOAD_FILES, { window: 'WINDOW_DRAFT_PREVIEW', fileName: '票据正面.png', file: result.target.result })
        this.$message.success(saveResult)
      } else {
        const res = await imgGenerator.screenshot(this.draftData, 'back', 'canvas')
        const result = await blobToBuffer(res)
        const saveResult = await this.$ipc.invoke(DOWNLOAD_FILES, { window: 'WINDOW_DRAFT_PREVIEW', fileName: '票据背面.png', file: result.target.result })
        this.$message.success(saveResult)
      }
    },

    // 复制票面
    async copyImg() {
      const img = await imgGenerator.screenshot(this.draftData, this.type, 'url', true)
      const isIe = browserType.indexOf('IE') !== -1
      if (!isIe) {
        // 除IE外的浏览器的复制方法
        if (navigator.clipboard) {
          const file = await base64ToFile(img)
          const blob = new Blob([file], { type: 'image/png' })
          await navigator.clipboard.write([
            // eslint-disable-next-line no-undef
            new ClipboardItem({
              [blob.type]: blob
            })
          ]).then(() => {
            this.$message.success('复制成功！')
          })
        } else if (window.require) {
          const { clipboard, nativeImage } = window.require('electron')
          const image = nativeImage.createFromDataURL(img) // res为base64图片数据
          clipboard.writeImage(image)
          this.$message.success('复制成功')
        } else {
          this.$message.warning('浏览器不支持复制票据，可在票据图片上点击右键复制')
        }
      } else {
        // IE浏览器不支持file方法，可以直接转blob
        // IE支持的方法
        // try {
        // // const blob = await this.dataURLtoBlob(img)
        // // console.log(window.clipboardData)
        // // console.log(window.clipboardData.setData('text', file))
        // } catch (err) {
        //   console.log(err)
        // }

        this.$message.warning('浏览器不支持复制票据，可在票据图片上点击右键复制')
      }
    }
  }
}
</script>
