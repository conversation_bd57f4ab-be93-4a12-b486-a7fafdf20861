<!-- websocket 消息通知组件 -->
<template>
  <div class="websocket-notification">
    <!-- <el-button type="primary" style="position: fixed; top: 190px; left: 250px; width: 100px; height: 40px;" @click="showNotificationTest">显示通知</el-button> -->
  </div>
</template>

<script>
import websocket from '@/websocket/index'
import { WEB_SOCKET_EVENT } from '@/websocket/constant'
import ipc from '@/electron/ipc/renderer'
import {
  NOTIFICATION // 触发notify
} from '@recognize/ipc-event-constant'
// import Audio from '@/common/js/audio'
import {
  TRANSACTION_STATUS, // 交易状态
} from '@/constant'
import { yuan2wan } from '@/common/js/number'
import { formatTime } from '@/common/js/date'
import { openExternal } from '@/common/js/util'

export default {
  name: 'websocket-notification',

  props: {
    // 用户配置信息
    userConfig: Object,
  },

  data() {
    return {
      notification: null, // 通知对象
      // 用户配置信息
      commonConfig: {
        textRemindPendingConfirm: 1, // 待确认消息提醒
        textRemindPendingPay: 1, // 待打款消息提醒
        textRemindPendingEndorse: 1, // 待背书消息提醒
        textRemindPendingSignIn: 1, // 待签收消息提醒
        voiceRemindPendingConfirm: 1, // 待确认声音提醒
        voiceRemindPendingPay: 1, // 待打款声音提醒
        voiceRemindPendingEndorse: 1, // 待背书声音提醒
        voiceRemindPendingSignIn: 1 // 待签收声音提醒
      },
      // 交易状态
      draftStatus: {
        // 待确认
        [TRANSACTION_STATUS.WAITING_CONFIRM.id]: {
          name: TRANSACTION_STATUS.WAITING_CONFIRM.name,
          id: TRANSACTION_STATUS.WAITING_CONFIRM.id,
          key: 'RemindPendingConfirm',
          text: '已有接单方，请尽快确认',
          url: 'user-center/sale-draft',
          tab: '2'
        },
        // 待打款
        [TRANSACTION_STATUS.WAITING_PAY.id]: {
          name: TRANSACTION_STATUS.WAITING_PAY.name,
          id: TRANSACTION_STATUS.WAITING_PAY.id,
          key: 'RemindPendingPay',
          text: '持票方已确认，请尽快打款',
          url: 'user-center/buy-draft',
          tab: '3'
        },
        // 待背书
        [TRANSACTION_STATUS.WAITING_ENDORSE.id]: {
          name: TRANSACTION_STATUS.WAITING_ENDORSE.name,
          id: TRANSACTION_STATUS.WAITING_ENDORSE.id,
          key: 'RemindPendingEndorse',
          text: '接单方已打款，请尽快背书',
          url: 'user-center/sale-draft',
          tab: '4'
        },
        // 待签收
        [TRANSACTION_STATUS.WAITING_SIGN.id]: {
          name: TRANSACTION_STATUS.WAITING_SIGN.name,
          id: TRANSACTION_STATUS.WAITING_SIGN.id,
          key: 'RemindPendingSignIn',
          text: '持票方已背书，请尽快签收',
          url: 'user-center/buy-draft',
          tab: '5'
        },

      }
    }
  },

  watch: {
    userConfig: {
      handler() {
        this.setCommonConfig()
      },
      deep: true
    },
  },

  created() {
    this.setCommonConfig()
    websocket.on(WEB_SOCKET_EVENT.ORDER_STATUS_REFRESH, this.orderStatusRefresh)
  },

  beforeDestroy() {
    websocket.off(WEB_SOCKET_EVENT.ORDER_STATUS_REFRESH, this.orderStatusRefresh)
  },

  methods: {
    // 处理 mqtt 消息-交易状态变更
    orderStatusRefresh(data) {
      const { sendTime, draftAmount, lastSixDraftNo, transactionStatus, role } = data
      if (!this.draftStatus[transactionStatus]?.isShowNotify) {
        return
      }
      if ((role === 2) && (transactionStatus === TRANSACTION_STATUS.WAITING_PAY.id)) {
        return
      }
      // 暂时不用声音功能
      // this.draftStatus[transactionStatus]?.isPlayAudio && Audio.play('/audios/notification.wav')
      let notifyObj = {
        action: true,
        title: this.draftStatus[transactionStatus]?.text || '',
        content: {
          time: formatTime(sendTime, 'YYYY-MM-DD  hh:mm:ss'),
          ticketNo: lastSixDraftNo,
          draftAmount: `${yuan2wan(draftAmount)} 万`
        },
        clickFun: () => {
          openExternal({
            url: `${this.draftStatus[transactionStatus].url}`,
            otherParam: {
              tab: this.draftStatus[transactionStatus].tab
            }
          })
        },
      }
      this.showNotification(notifyObj)
    },

    // 获通知设置
    setCommonConfig() {
      const res = this.userConfig
      Object.assign(this.commonConfig, res)
      for (let i in this.draftStatus) {
        this.$set(this.draftStatus[i], 'isShowNotify', res[`text${this.draftStatus[i].key}`])
        this.$set(this.draftStatus[i], 'isPlayAudio', res[`voice${this.draftStatus[i].key}`])
      }
    },

    // 显示系统通知
    showNotification(notification) {
      ipc.request(NOTIFICATION, {
        title: notification.title,
        content: notification.content,
        showViewBtn: !!notification.action // 是否显示查看按钮
      })
        .then(closeReason => {
          // timeout（超时）, replacedByNewNotification（被新消息替换）, view（点击查看）, close（点击关闭）
          if (closeReason === 'view') {
            notification.clickFun && notification.clickFun()
          }
        })
    },

    // 测试显示通知
    showNotificationTest() {
      // 单纯测试 通知推送
      const data = {
        acceptorName: '张三', // 承兑人名称
        orderNo: '123541231232', // 订单编号
        draftAmount: 10000, //  票据金额，单位元
        msg: 10, // 消息内容
        draftActualAmount: 9500, // 订单实收金额
        draftPaymentAmount: 9000, // 订单实付金额
        sendTime: 1639152000000, // 确认时间（消息发送的时间）
        transactionStatus: 12
      }
      websocket.emit(WEB_SOCKET_EVENT.ORDER_STATUS_REFRESH, data)
    }
  }
}
</script>
