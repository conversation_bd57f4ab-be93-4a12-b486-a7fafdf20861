<!-- 智能验票窗口 -->
<style lang="scss" scoped>
.tip {
  display: flex;
  align-items: center;
  padding-left: 17px;
  width: 100%;
  height: 42px;
  font-size: 16px;
  color: $color-text-primary;
  background: $--color-primary-hover;

  .icon-exclamation-circle {
    margin-right: 12px;
    font-size: 18px;
    color: $color-warning;
  }

  .blod {
    font-weight: 600;
    color: $color-warning;
  }
}

.tip-success {
  background: $color-E6F3F3;

  .icon-exclamation-circle {
    color: $color-008489;
  }
}

.verify-card {
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
  width: 100%;
}

.verify-card-left,
.verify-card-right {
  padding: 12px 16px;
  box-sizing: border-box;
  width: 456px;
  height: 520px;
  background: $color-FFFFFF;

  .item {
    position: relative;
    margin-top: 10px;

    .item-title {
      margin-bottom: 4px;
      font-size: 14px;
      color: $color-text-secondary;
    }

    .item-content {
      height: 24px;
      font-size: 16px;
      color: $color-text-primary;
    }

    .blod {
      font-weight: 600;
    }

    .red-blod {
      max-width: 90%;
      font-weight: 500;
      color: $color-warning;
      cursor: pointer;

      ::v-deep {
        .el-tooltip {
          max-width: 100%;

          @include ellipsis;
        }
      }
    }

    .decide {
      position: absolute;
      top: 50%;
      right: 0;
      transform: translateY(-50%);
      font-size: 20px;

      .icon-close-circle {
        color: $color-warning;
      }

      .icon-check-circle {
        color: $color-008489;
      }
    }
  }
}

.footer {
  margin-top: 12px;
  text-align: right;

  .footer-btn {
    font-size: 18px;
  }
}

.hasicon {
  min-width: 85px;
}
</style>

<template>
  <el-dialog
    class="smart-verify-ticket"
    title="智能验票"
    :visible.sync="dialogVisible"
    width="964px"
    append-to-body
    @closed="onWindowClose"
  >
    <div>
      <div class="tip" :class="!inconsistentCount.length && !defectsNotifyTemp.length && 'tip-success'">
        <icon type="chengjie-exclamation-circle" class="icon-exclamation-circle" />
        <div v-if="inconsistentCount.length || defectsNotifyTemp.length">
          <span v-if="inconsistentCount.length" class="fail-tip">
            <span class="blod">{{ inconsistentCount.join('、') }}</span>
            <span> 共 </span>
            <span class="blod">{{ inconsistentCount.length }}</span>
            <span> 项票据信息不一致</span>
          </span>
          <span v-else>识别的票据信息与待签收的订单票据信息一致</span>
          <!--
            <span>，</span>
            <span v-if="defectsNotifyTemp.length" class="blod">有风险提示</span>
            <span v-else>无风险提示</span>
          -->
          <span>，请确认是否签收。</span>
        </div>
        <div v-else class="success-tip">
          识别的票据信息与待签收的订单票据信息一致，可放心签收。
        </div>
      </div>

      <div class="verify-card">
        <!-- 网银识别 -->
        <div class="verify-card-left">
          <div class="g-title">
            <span class="hasicon">网银识别</span>
            <DefectsNotifyTooltip :detail="defectsNotifyTemp" type="card" />
          </div>
          <div v-for="item in identifyData" :key="item.label" class="item">
            <div class="item-title">{{ item.label }}</div>
            <div
              class="item-content"
              :class="[item.isBlod && 'blod', (item.isDefect && item.value !== '无瑕疵') && 'red-blod']"
            >
              <el-tooltip
                v-if="item.isDefect && item.value !== '无瑕疵'"
                effect="dark"
                placement="top"
              >
                <div slot="content">
                  {{ item.value }}
                </div>
                <div>
                  {{ item.value }}
                </div>
              </el-tooltip>
              <span v-else>
                {{ item.value }}
              </span>
              <span v-if="item.isDate">{{ draftInterestDays }}</span>
            </div>
          </div>
        </div>
        <!-- 平台订单 -->
        <div class="verify-card-right">
          <div class="g-title">平台订单</div>
          <div v-for="order in orderData" :key="order.label" class="item">
            <div class="item-title">{{ order.label }}</div>
            <div
              class="item-content"
              :class="[order.isBlod && 'blod', (order.isDefect && order.value !== '无瑕疵') && 'red-blod']"
            >
              <el-tooltip
                v-if="order.isDefect && order.value !== '无瑕疵'"
                effect="dark"
                placement="top"
              >
                <div slot="content">
                  {{ order.value }}
                </div>
                <div>
                  {{ order.value }}
                </div>
              </el-tooltip>
              <span v-else-if="order.label === '背书手数'">
                {{ order.value === null ? '未提供' : order.value }}
              </span>
              <span v-else>
                {{ order.value }}
              </span>
              <span v-if="order.isDate">{{ interestDays }}</span>
            </div>
            <div class="decide">
              <icon v-if="order.check" type="chengjie-check-circle" class="icon-check-circle" />
              <icon v-else type="chengjie-close-circle" class="icon-close-circle" />
            </div>
          </div>
        </div>
      </div>

      <div v-if="operationVisible" class="footer">
        <el-button
          class="footer-btn"
          type="primary"
          border
          width="192"
          height="42"
          @click="jump2Web(1)"
        >
          不是此票，去取消
        </el-button>
        <el-button
          class="footer-btn"
          type="primary"
          width="192"
          height="42"
          @click="jump2Web(2)"
        >
          确认无误，去签收
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import {
  BACK_DEFECT_TYPE_NAME_MAP, // 瑕疵类型
  BACK_DEFECT_TYPE_KEY_MAP, // 票据瑕疵类型 id 映射 key
} from '@recognize/constant'
import { yuan2wan } from '@/common/js/number'
import { formatTime } from '@/common/js/date'
import mixpanel from '@recognize/utils/mixpanel'
import commonApi from '@recognize/apis/common'
import DefectsNotifyTooltip from '@/recognize/components/draft/components/defects-notify-tooltip.vue'
import {
  toDefectsNotifyTempNew,
} from '@/common/js/draft-flaw'
import { getAdjustDays, getInterestAccrualDay } from '@/common/js/draft-math'

const getDefaultData = () => ({
  // 数据项
  draftItems: {
    draftNo: {
      label: '票号',
      value: '',
    },
    subTicketRange: {
      label: '子票区间',
      value: '',
    },
    acceptorName: {
      label: '承兑人',
      value: '',
      showTooltip: true,
    },
    draftAmount: {
      label: '票面金额',
      value: '',
      isBlod: true,
    },
    maturityDate: {
      label: '到期日',
      value: '',
      isDate: true,
    },
    defect: {
      label: '瑕疵',
      value: '',
      isDefect: true
    },
    endorseCount: {
      label: '背书手数',
      value: ''
    },
    sellerCorpName: {
      label: '持票企业',
      value: ''
    }
  },
  inconsistentCount: [], // 不一致的项
  interestDays: '', // 计息天数
  draftInterestDays: '', // 识别计息天数
})

export default {
  name: 'smart-verify-ticket',
  components: {
    DefectsNotifyTooltip,
  },
  data() {
    return {
      defectsNotifyTemp: [], // 风险
      dialogVisible: false,
      identifyData: {}, // 网银识别数据
      orderData: {}, // 平台订单
      // 瑕疵列表
      defectList: {
        // 回出票人aba
        [BACK_DEFECT_TYPE_NAME_MAP.ABA.key]: {
          name: BACK_DEFECT_TYPE_NAME_MAP.ABA.name,
          key: BACK_DEFECT_TYPE_NAME_MAP.ABA.key,
          isShowNum: BACK_DEFECT_TYPE_NAME_MAP.ABA.isShowNum
        },
        // 回出票人abca
        [BACK_DEFECT_TYPE_NAME_MAP.ABCA.key]: {
          name: BACK_DEFECT_TYPE_NAME_MAP.ABCA.name,
          key: BACK_DEFECT_TYPE_NAME_MAP.ABCA.key,
          isShowNum: BACK_DEFECT_TYPE_NAME_MAP.ABCA.isShowNum
        },
        // abb
        [BACK_DEFECT_TYPE_NAME_MAP.ABB.key]: {
          name: BACK_DEFECT_TYPE_NAME_MAP.ABB.name,
          key: BACK_DEFECT_TYPE_NAME_MAP.ABB.key,
          isShowNum: BACK_DEFECT_TYPE_NAME_MAP.ABB.isShowNum
        },
        // 回收款人
        [BACK_DEFECT_TYPE_NAME_MAP.HUI_SHOU_KUAN_REN.key]: {
          name: BACK_DEFECT_TYPE_NAME_MAP.HUI_SHOU_KUAN_REN.name,
          key: BACK_DEFECT_TYPE_NAME_MAP.HUI_SHOU_KUAN_REN.key,
          isShowNum: BACK_DEFECT_TYPE_NAME_MAP.HUI_SHOU_KUAN_REN.isShowNum
        },
        // 背书回头
        [BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_HUI_TOU.key]: {
          name: BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_HUI_TOU.name,
          key: BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_HUI_TOU.key,
          isShowNum: BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_HUI_TOU.isShowNum
        },
        // 背书重复
        [BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_CHONG_FU.key]: {
          name: BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_CHONG_FU.name,
          key: BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_CHONG_FU.key,
          isShowNum: BACK_DEFECT_TYPE_NAME_MAP.BEI_SHU_CHONG_FU.isShowNum
        },
        // 质押
        [BACK_DEFECT_TYPE_NAME_MAP.ZHI_YA.key]: {
          name: BACK_DEFECT_TYPE_NAME_MAP.ZHI_YA.name,
          key: BACK_DEFECT_TYPE_NAME_MAP.ZHI_YA.key,
          isShowNum: BACK_DEFECT_TYPE_NAME_MAP.ZHI_YA.isShowNum
        },
        // 保证
        [BACK_DEFECT_TYPE_NAME_MAP.BAO_ZHENG.key]: {
          name: BACK_DEFECT_TYPE_NAME_MAP.BAO_ZHENG.name,
          key: BACK_DEFECT_TYPE_NAME_MAP.BAO_ZHENG.key,
          isShowNum: BACK_DEFECT_TYPE_NAME_MAP.BAO_ZHENG.isShowNum
        },
        // 不一致
        [BACK_DEFECT_TYPE_NAME_MAP.SHANG_XIA_BU_YI_ZHI.key]: {
          name: BACK_DEFECT_TYPE_NAME_MAP.SHANG_XIA_BU_YI_ZHI.name,
          key: BACK_DEFECT_TYPE_NAME_MAP.SHANG_XIA_BU_YI_ZHI.key,
          isShowNum: BACK_DEFECT_TYPE_NAME_MAP.SHANG_XIA_BU_YI_ZHI.isShowNum
        },
        // 其他
        [BACK_DEFECT_TYPE_NAME_MAP.OTHER.key]: {
          name: BACK_DEFECT_TYPE_NAME_MAP.OTHER.name,
          key: BACK_DEFECT_TYPE_NAME_MAP.OTHER.key,
          isShowNum: BACK_DEFECT_TYPE_NAME_MAP.OTHER.isShowNum
        },
      },
      holidays: [], // 节假日列表
      operationVisible: false, // 是否展示操作按钮
      ...getDefaultData(), // 初始化数据
    }
  },

  methods: {
    open(data) {
      Object.assign(this, getDefaultData())
      this.bindData(data.orderData, data.identifyData)
      this.operationVisible = !data.hideButton
      this.dialogVisible = true
    },

    // 绑定数据
    async bindData(orderData, identifyData) {
      // 风险
      this.defectsNotifyTemp = identifyData.defectsNotify ? toDefectsNotifyTempNew(identifyData.defectsNotify, identifyData.fontSelfOwn, identifyData.backSelfOwn) : []

      this.holidays.length || await this.getHolidays()
      // 设置平台订单数据
      this.orderData = JSON.parse(JSON.stringify(this.draftItems))
      // 是否比对背书手数
      if (!this.isVerifyEndorseCount(orderData.endorseCount)) {
        delete this.orderData.endorseCount
      }
      for (let i in this.orderData) {
        this.orderData[i].value = orderData[i]
        this.orderData[i].check = true
      }
      this.orderData.draftAmount.value = `${yuan2wan(this.orderData.draftAmount.value)} 万`
      this.orderData.maturityDate.value = formatTime(this.orderData.maturityDate.value, 'YYYY-MM-DD')
      // 瑕疵设置
      this.orderData.defect.value = this.orderDefectSetting(orderData.defects)
      this.interestDays = `（剩${orderData.interestDays}天）`
      // 设置识别数据
      this.identifyData = JSON.parse(JSON.stringify(this.draftItems))
      // 是否比对背书手数
      if (!this.isVerifyEndorseCount(orderData.endorseCount)) {
        delete this.identifyData.endorseCount
      }
      identifyData.endorseCount = identifyData.endorseCount || 0
      identifyData.maturityDate = formatTime(identifyData.maturityDate, 'YYYY-MM-DD')
      identifyData.sellerCorpName = identifyData.lastEndorse || identifyData.takerName
      for (let i in this.identifyData) {
        this.identifyData[i].value = identifyData[i]
      }
      this.identifyData.draftAmount.value = `${yuan2wan(this.identifyData.draftAmount.value)} 万`
      // 瑕疵设置
      if (!identifyData.defects) {
        this.identifyData.defect.value = '无瑕疵'
      } else {
        this.identifyData.defect.value = this.identifyDefectSetting(identifyData)
      }
      this.draftInterestDays = `（剩${this.setTicketExpireDays(this.identifyData.maturityDate.value)}天）`
      // 新票比对子票区间
      if (orderData.draftType) {
        this.orderData.subTicketRange.value = `${orderData.subTicketStart || ''} - ${orderData.subTicketEnd || ''}`
        this.identifyData.subTicketRange.value = `${identifyData.subTicketStart || ''} - ${identifyData.subTicketEnd || ''}`
      } else {
        delete this.orderData.subTicketRange
        delete this.identifyData.subTicketRange
      }
      this.verifyTicket()
    },

    // 平台订单瑕疵设置
    orderDefectSetting(data) {
      let defectArr = []
      for (let i in this.defectList) {
        if (i !== 'defectsOther' && i !== 'defectsOtherDesc' && data[i]) {
          defectArr.push(this.defectList[i].name + (this.defectList[i].isShowNum ? `(${data[i]})` : ''))
        } else if (i === 'defectsOther' && data[i]) {
          defectArr.push(this.defectList[i].name + ((data.defectsOtherDesc && data.defectsOtherDesc !== 'null' && data.defectsOtherDesc !== 'undefined') ? `(${data.defectsOtherDesc})` : ''))
        }
      }
      return defectArr.length ? defectArr.join('，') : '无瑕疵'
    },

    // 网银识别瑕疵设置
    identifyDefectSetting(data) {
      let defects = data.defects.split('|').map(item => ({
        id: item.split('_')[0],
        times: item.split('_')[1]
      }))
      let defectArr = []
      for (let i in this.defectList) {
        defects.forEach(item => {
          if (i === BACK_DEFECT_TYPE_KEY_MAP[item.id]) {
            defectArr.push(this.defectList[i].name + (this.defectList[i].isShowNum ? `(${item.times})` : ''))
          }
        })
      }
      return defectArr.join('，')
    },

    // 数据比对
    verifyTicket() {
      let failList = []
      for (let i in this.orderData) {
        // 承兑人、持票企业 中文括号转换成英文半角后再对比
        if (i === 'acceptorName' || i === 'sellerCorpName') {
          this.orderData[i].value.replaceAll('（', '(').replaceAll('）', ')') !== this.identifyData[i].value.replaceAll('（', '(').replaceAll('）', ')') && failList.push(i)
          continue
        }
        if (this.orderData[i].value !== this.identifyData[i].value) {
          failList.push(i)
        }
      }
      failList.forEach(item => {
        this.orderData[item].check = false
        this.inconsistentCount.push(this.orderData[item].label)
      })
    },

    // 跳转到官网
    jump2Web(type) {
      const { draftNo, draftAmount, acceptorName, maturityDate, defect } = this.orderData
      let confirm = type === 1 ? 2 : 1
      mixpanel.intelligenceCheck({
        draft_no: draftNo.value,
        draft_amount: draftAmount.value,
        acceptor: acceptorName.value,
        expiry_date: maturityDate.value,
        defect: defect.value,
        confirm,
      })
      if (this.$ipc) {
        this.$ipc.send('PUSH_ROUTE_IN_MAIN_WINDOW', '/user-center/buy-draft?tab=5')
      } else {
        this.$router.push('/user-center/buy-draft?tab=5')
      }
      this.dialogVisible = false
    },

    // 窗口关闭回调
    onWindowClose() {
      const { draftAmount, acceptorName, maturityDate, defect } = this.orderData
      mixpanel.intelligenceCheck({
        draft_amount: draftAmount.value,
        acceptor: acceptorName.value,
        expiry_date: maturityDate.value,
        defect: defect.value,
        confirm: 0,
      })
      this.dialogVisible = false
    },

    // 是否比对背书手数
    isVerifyEndorseCount(orderEndorse) {
      return orderEndorse !== null
    },

    // 获取节假日列表
    async getHolidays() {
      // 节假日列表
      this.holidays = await commonApi.getHolidays()
    },

    // 修改到期时间
    setTicketExpireDays(val) {
      return getInterestAccrualDay(new Date(), val, getAdjustDays(val, this.holidays))
    },
  },

}
</script>
