<!-- 使用须知 -->
<style lang="scss" scoped>
.center {
  overflow-y: auto;
  padding: 15px 10px;
  max-height: 380px;
  font-size: 16px;
  text-align: start;
  color: $color-text-primary;
  background: $color-FFFFFF;

  p {
    margin-bottom: 4px;
    line-height: 23px;
  }

  .sign {
    margin-top: 10px;
    text-align: right;
  }
}

::v-deep .el-dialog__footer {
  padding: 12px 20px;
}

.footer {
  text-align: center;

  .btn {
    font-size: 16px;
  }
}
</style>

<template>
  <el-dialog
    :title="`${discernName}使用须知`"
    :visible.sync="visible"
    style-type="border"
    width="480px"
    append-to-body
    class="whead-gbody-dialog"
    :close-on-click-modal="false"
  >
    <div class="center">
      <p>一、{{ discernName }}票据发布功能系{{ themeName }}研发，旨在提高用户票据发布效率、维护用户票据信息安全。</p>
      <p>二、用户使用{{ discernName }}功能时，需使用{{ themeName }}注册的平台账号进行登录，如{{ themeName }}账号出现异常情况（包括但不限于注销、暂停使用、转让等），则无法使用{{ discernName }}。</p>
      <p>三、用户通过{{ discernName }}发布票据，{{ themeName }}不会向用户收取任何费用。</p>
      <p>四、通过{{ discernName }}识别票据，{{ discernName }}将获取票据号码、出票人、收款人、承兑人、背书人等票面信息，对于出票人和收款人信息，用户有权做隐藏处理。信息成功获取后，{{ discernName }}将根据用户的授权将相关票据信息在{{ themeName }}中进行展示。</p>
      <p>五、用户通过{{ discernName }}发布的票据信息会在{{ themeName }}中进行展示。</p>
      <p>六、{{ themeName }}不会采用诱导、欺骗等方式要求用户上传票面信息，用户依自愿原则使用{{ discernName }}发布票据信息。</p>
      <p>七、用户须仔细阅读并同意《{{ themeName }}注册服务协议》、《隐私协议》、《{{ discernName }}使用须知》，方可使用{{ discernName }}票据发布功能，否则，将被禁止使用{{ discernName }}发布票据信息。</p>
      <p>八、{{ discernName }}仅根据用户授权进行识别票据信息，并不会篡改票面信息。如用户发现{{ discernName }}所识别出的票面信息有误，可改用其他方式进行票据信息发布。</p>
      <p>九、用户使用{{ discernName }}功能时，{{ discernName }}可能会获取您操作设备的MAC地址和网络IP地址，用于记录您已阅读并同意本须知。</p>
      <p>十、用户使用{{ discernName }}发布的过程中，应对所发布信息的真实性及合法性负责，如因此造成其他用户及{{ themeName }}的损失，用户应对此承担责任。</p>
      <p>十一、用户在使用{{ discernName }}过程中，违反法律的规定及《{{ themeName }}注册服务协议》、《隐私协议》及本须知的任何约定，导致其他用户及{{ themeName }}的损失，用户应承担全部责任，{{ themeName }}不为此承担任何责任。</p>
      <p>十二、{{ themeName }}倡导：用户应提高信息安全和信息保护意识，以防范票据信息及商业秘密被泄露和滥用的风险，避免自身权益受损。</p>
      <p>十三、{{ discernName }}将按现有技术水平，尽力保障{{ discernName }}的正确、持续、及时和安全，但{{ discernName }}对无法合理预知和不可控制的意外风险（如计算机病毒、木马或其他恶意程序、黑客攻击，设备、系统、硬件软件和通信线路故障、自然灾害等）、产品技术瑕疵、服务的稳定性以及由此产生的任何损失或损害不作任何形式的保证，亦不负任何赔偿责任，但法律另有规定的除外。</p>
      <p>十四、用户使用{{ discernName }}识别、发布、分享、存储的任何信息及做出的任何行为，视为用户的个人行为，用户应对使用{{ discernName }}的行为负全部责任。</p>
      <p>十五、为了向用户提供更好的服务，或者基于法律规定、主管部门要求、业务开展情况等因素的更新与变化，{{ themeName }}将适时对本须知内容进行修订，该等修订内容一经公布立即生效，{{ themeName }}将通过{{ discernName }}发出更新版本，如用户继续使用{{ discernName }}的，视为用户同意更新后的《{{ discernName }}使用须知》，否则，用户应立即停止使用{{ discernName }}。</p>
      <br>
      <div class="sign">{{ themeName }}</div>
      <div class="sign">2024年4月15日</div>
    </div>

    <div slot="footer" class="footer">
      <el-button
        class="btn"
        type="primary"
        height="42"
        @click="agree"
      >
        我已阅读并同意《{{ discernName }}使用须知》
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { AGREE_USAGE_NOTICE } from '@recognize/constant-storage'
import Storage from '@/common/js/storage' // 本地缓存对象
import {
  GET_COMPUTER_INFO, // 获取电脑信息
} from '@recognize/ipc-event-constant'
import { OPEN_USAGE_NOTICE } from '@recognize/event/modules/account' // 打开使用须知
import commonApi from '@recognize/apis/common'

export default {
  name: 'usage-notice',
  data() {
    return {
      visible: false, // 是否显示
      repeat: false, // 防止重复
    }
  },
  watch: {
    $route: {
      handler(to) {
        const { path, query } = to
        if (path === '/user-center/issue-draft' && query.type === '6' && !this.repeat) {
          this.repeat = true
          this.init()
        }
      },
      immediate: true,
    },
  },

  created() {
    // 全局列表刷新事件
    this.$event.on(OPEN_USAGE_NOTICE, this.init)
  },

  methods: {
    // 初始化
    init() {
      let isAgree = Storage.get(AGREE_USAGE_NOTICE)
      if (!isAgree) {
        this.visible = true
      }
    },

    // 获取网关信息
    handler(data) {
      const { mac, address } = data || {}
      if (mac) {
        commonApi.postVisitRecord({
          mac,
          ip: address
        })
      }
    },

    // 点击同意
    agree() {
      Storage.set(AGREE_USAGE_NOTICE, true)
      this.$store.commit('recognize-common/updateIsAgree', true)
      if (this.$ipc) {
        this.$ipc.invoke(GET_COMPUTER_INFO).then(this.handler)
      }
      this.visible = false
    }
  }
}
</script>
