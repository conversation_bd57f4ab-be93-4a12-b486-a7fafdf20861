<!-- 识别操作后各种提示 -->
<style scoped lang="scss">
.el-button {
  font-size: 16px;
}
</style>

<style lang="scss">
.commercial-message {
  .el-message__icon {
    color: $color-008489;
  }

  .el-message__content {
    white-space: nowrap;
  }
}

.look-terms {
  font-weight: 600;
  text-decoration: underline;
  color: $--color-primary;
  cursor: pointer;
}
</style>

<template>
  <div>
    <!-- dll安装出错了弹窗 当前弹框已经废弃 改成appAbnormal方法执行了 -->
    <el-dialog
      title="程序模块加载异常"
      :visible.sync="showAppAbnormal"
      type="error"
      append-to-body
      center
    >
      <span>程序模块加载异常，您可重新下载最新版本客户端或者重新打开程序解决。点击联系客服，技术人员将会与您联系解决。</span>
      <span slot="footer">
        <el-button width="144" height="42" @click="showAppAbnormal = false">联系我们</el-button>
        <el-button
          type="primary"
          width="144"
          height="42"
          @click="showAppAbnormal = false"
        >重新安装</el-button>
      </span>
    </el-dialog>

    <Setting ref="settingRef" />
  </div>
</template>

<script>
import Setting from '@recognize/components/set/set.vue'

export default {
  name: 'recognize-tip',

  components: {
    Setting,
  },

  data() {
    return {
      showAppAbnormal: false, // 是否显示程序加载异常
    }
  },
  created() {
    window.lookTerms = this.lookTerms
    // console.log(this.$msgbox)
  },

  methods: {
    // 查看解决办法
    lookTerms() {
      const url = '/user-center/common-problem?id=7'
      if (this.$ipc) {
        this.$ipc.send('PUSH_ROUTE_IN_MAIN_WINDOW', url)
        // this.$ipc.send('CLOSE')
      } else {
        this.$router.push(url)
      }
      // 关闭当前弹框
      this.$msgbox.close()
    },
    // 初始化
    init(type) {
      type && (this[type] = true)
    },

    // 识别不到票面
    identifyFail() {
      this.$confirm('<div>当前窗口未检测到票面。</div><div>请检查是否打开了网银页面。</div><div>建议使用IE浏览器或360浏览器兼容模式。</div><div>部分不支持IE模式的银行可<span class="look-terms" onclick="lookTerms()">点击查看解决办法~</span>。</div>', '提示', {
        dangerouslyUseHTMLString: true,
        type: 'warning',
        showClose: false,
        iconPosition: 'title',
        confirmButtonText: '知道了',
        showCancelButton: false,
      })
    },

    // 继续识别，识别不到票面
    continueIdentifyFail() {
      this.$confirm('<div>与上一次识别的票据信息不匹配/未识别到背面。</div><div>请打开票据正面重新开始识别！</div>', '提示', {
        dangerouslyUseHTMLString: true,
        type: 'warning',
        showClose: false,
        iconPosition: 'title',
        confirmButtonText: '知道了',
        showCancelButton: false,
      }).catch(err => {
        // eslint-disable-next-line no-console
        console.log(err)
      })
    },

    // 商票不支持批量识别
    identifyFailCommercial() {
      this.$message.warning({ message: '商票不支持批量识别，如需识别商票，请点击单张识别', customClass: 'commercial-message' })
    },

    // 过滤了新票缺少子票区间的票据
    identifyFailNewDraft() {
      this.$confirm('<div>当前网银页面中，部分新一代票据缺少子票区间信息，请尝试使用单张识别。</div>', '提示', {
        dangerouslyUseHTMLString: true,
        type: 'warning',
        showClose: false,
        iconPosition: 'title',
        confirmButtonText: '知道了',
        showCancelButton: false,
      })
    },

    // 未检测到票号或承兑人名称
    identifyFailAcceptorName() {
      this.$confirm('<div>当前网银页面未检测到票号或承兑人名称，请尝试单张识别票据。</div>', '提示', {
        dangerouslyUseHTMLString: true,
        type: 'warning',
        showClose: false,
        iconPosition: 'title',
        confirmButtonText: '确定',
        showCancelButton: false,
      })
    },

    // 程序异常弹窗
    appAbnormal() {
      this.$confirm('<div>程序模块加载异常，请联系您的客户经理获取最新的安装包，重新安装之后即可解决此异常。找不到客户经理可点击问题反馈，提供报错截图及您联系方式。</div>', '程序模块加载异常', {
        distinguishCancelAndClose: true,
        dangerouslyUseHTMLString: true,
        type: 'error',
        iconPosition: 'title',
        confirmButtonText: '我知道了',
        cancelButtonText: '问题反馈',
      }).then(() => {
        // TODO: 打开签手下载页面
        // console.log('打开签手下载页面')
      })
        .catch(err => {
          if (err === 'cancel') {
            this.$refs.settingRef.open({ showFeedBack: true })
          }
        })
    }

  }
}
</script>
