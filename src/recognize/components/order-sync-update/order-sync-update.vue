<!-- 订单同步更新窗口 -->
<style lang="scss" scoped>
.order-sync-update-page {
  .order-sync-update-container {
    width: 100%;
    height: 660px;

    .tips {
      display: flex;
      align-items: center;
      padding: 0 16px;
      width: 100%;
      height: 66px;
      background: $--color-primary-hover;
      box-sizing: border-box;

      .tips-icon {
        margin-top: -24px;
        margin-right: 10px;
        color: $color-warning;
      }

      .tips-text {
        line-height: 24px;
        font-size: 16px;
      }

      .tips-bold {
        font-weight: 600;
        color: $color-warning;
      }
    }

    .draft-card {
      margin: 12px 0;
      padding: 12px 16px;
      background: $color-FFFFFF;

      .item-top {
        margin-top: 10px;
      }

      .item-label {
        margin-bottom: 2px;
        font-size: 14px;
        color: $color-text-secondary;
      }

      .item-value {
        font-size: 16px;
        color: $color-text-primary;
      }

      .accept-value {
        display: flex;
      }

      .item-bold {
        font-weight: 600;
      }

      .acceptor-tag {
        margin-left: 6px;
        vertical-align: text-top;
      }

      .draft-info {
        display: flex;
        margin-top: 10px;

        .item-left {
          border-right: 1px solid $color-D9D9D9;
          width: 50%;
        }

        .item-right {
          margin-left: 16px;
          width: 50%;
        }
      }
    }

    .order-table {
      padding: 12px 0;
      background: $color-FFFFFF;

      .table-search {
        display: flex;
        align-items: center;
        border-bottom: 1px solid $color-D9D9D9;
        padding: 0 16px;
        width: 100%;
        height: 64px;
        font-size: 16px;

        .search-input {
          width: 310px;
          height: 40px;
        }

        .search-icon {
          margin: 0 6px 0 16px;
          color: $color-warning;
        }
      }

      ::v-deep {
        .el-table__body-wrapper {
          overflow-y: auto;
        }
      }

      .bold {
        font-weight: 600;
        color: $color-text-primary;
      }

      .draft-no {
        overflow: hidden;
        width: 220px;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .tag-out {
        display: flex;
        justify-content: space-around;
        align-items: center;
        height: 100%;
      }

      .tag {
        margin-right: 6px;
        border: 1px solid $color-13C2C2;
        border-radius: 50%;
        width: 22px;
        height: 22px;
        font-size: 12px;
        font-weight: 600;
        text-align: center;
        color: $color-13C2C2;
        line-height: 18px;

        &.batch {
          border-color: $color-9254DE;
          color: $color-9254DE;
        }

        &.auto {
          border-color: $color-AF772D;
          color: $color-AF772D;
        }

        &.serial {
          border-color: $color-1890FF;
          color: $color-1890FF;
        }
      }

      .item-sub-ticket {
        font-size: 12px;
        color: $color-text-secondary;
      }
    }
  }

  .footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid $color-F0F0F0;
    color: $color-text-primary;
    background: $color-F2F2F2;

    &-text {
      font-size: 16px;
      color: $color-text-primary;
    }

    &-icon {
      vertical-align: text-bottom;
      margin-right: 7px;
    }

    &-red {
      font-weight: 600;
      color: $color-warning;
    }
  }
}

.two-wrap-hidden {
  /* stylelint-disable-next-line value-no-vendor-prefix */
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  /* stylelint-disable-next-line property-no-vendor-prefix */
  -webkit-box-orient: vertical;
}
</style>

<template>
  <el-dialog
    class="order-sync-update-page"
    title="订单同步更新提示"
    :visible.sync="dialogVisible"
    width="1268px"
    top="10vh"
    append-to-body
  >
    <div class="order-sync-update-container">
      <div class="tips">
        <icon type="chengjie-exclamation-circle" class="tips-icon" :size="25" />
        <div class="tips-text">
          为您找到 {{ orderAmount }} 张相同承兑人、金额、到期日的订单。
          <span class="tips-bold">请确保所有的票据背面信息一致，不一致的可以从列表移除，若因不一致导致的交易取消，可能会判您违约哦 ~ </span>
          若其中有已发布、未接单订单，将同步更新订单详情。
        </div>
      </div>

      <div class="draft-card">
        <div class="item-label">承兑人</div>
        <div class="item-value accept-value">
          <span class="item-bold">{{ draftData.acceptorName }}</span>
          <span class="g-tag--green acceptor-tag">{{ ACCEPTOR_TYPE_VALUE_MAP[draftData.acceptorType] }}</span>
        </div>
        <div class="draft-info">
          <div class="item-left">
            <div class="item-label">票面金额</div>
            <div class="item-value item-bold">{{ yuan2wan(draftData.draftAmount) }} 万</div>
            <div class="item-label item-top">到期日</div>
            <div class="item-value">{{ draftData.maturityDate }}（剩 {{ draftData.interestDays || '-' }} 天）</div>
          </div>
          <div class="item-right">
            <div class="item-label">瑕疵</div>
            <div class="item-value">{{ toDefectStr(draftData.defects) || '无瑕疵' }}</div>
            <div class="item-label item-top">背书手数</div>
            <div class="item-value">{{ draftData.endorseCount || 0 }}</div>
          </div>
        </div>
      </div>
      <div class="order-table">
        <div class="table-search">
          <el-input
            v-model="keyword"
            placeholder="请输入 30 位完整票号进行搜索"
            :clearable="true"
            class="search-input"
          />
          <icon type="chengjie-exclamation-circle" class="search-icon" :size="20" />
          <div>温馨提示：如果存在信息不同的票据，请移除对应票据后，再确认同步，因手动没移除，信息更新错误，责任自己承担。</div>
        </div>
        <el-table
          ref="orderTable"
          :data="tableData"
          height="304"
        >
          <template slot="empty">
            <icon type="chengjie-empty" :size="186" />
            <div class="empty-text">暂无数据</div>
          </template>
          <el-table-column
            prop="draftNo"
            label="票号"
            min-width="220"
            :show-overflow-tooltip="true"
            header-align="right"
          >
            <template slot-scope="scope">
              <div>
                <div class="tag-out">
                  <span v-if="scope.row.discernType === IDENTIFY_TYPE.SIGNAL.id" class="tag">单</span>
                  <span v-else-if="scope.row.discernType === IDENTIFY_TYPE.MULTIPLE.id" class="tag batch">批</span>
                  <span v-else-if="scope.row.discernType === IDENTIFY_TYPE.AUTO.id" class="tag auto">同</span>
                  <el-tooltip :content="scope.row.draftNo" placement="top">
                    <div class="draft-no">{{ scope.row.draftNo }}</div>
                  </el-tooltip>
                </div>
                <el-tooltip v-if="scope.row.draftType" :content="`子票区间：${scope.row.subTicketStart || ''}-${scope.row.subTicketEnd || ''} `" placement="top">
                  <span class="draft-no item-sub-ticket">{{ `子票区间：${scope.row.subTicketStart || ''}-${scope.row.subTicketEnd || ''}` }}</span>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="承兑人"
            align="left"
            min-width="210"
            class-name="acceptor"
            header-align="left"
          >
            <template slot-scope="scope">
              <span class="bold">{{ scope.row.acceptorName }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="票面金额(万)"
            width="115"
            header-align="right"
            align="right"
          >
            <span slot-scope="scope" class="bold">{{ yuan2wan(scope.row.draftAmount) }}</span>
          </el-table-column>

          <el-table-column
            label="到期日"
            width="115"
            header-align="right"
            align="right"
          >
            <template slot-scope="scope">
              {{ formatTime(scope.row.maturityDate, 'YYYY-MM-DD') }}
            </template>
          </el-table-column>
          <el-table-column
            label="背书手数"
            width="128"
            header-align="left"
          >
            <template slot-scope="scope">
              {{ typeof scope.row.endorseCount === 'number' ? scope.row.endorseCount : (scope.row.endorseCount || '-') }}
            </template>
          </el-table-column>
          <el-table-column
            label="瑕疵"
            width="150"
            header-align="left"
          >
            <template slot-scope="scope">
              <div class="has-flaw two-wrap-hidden">
                <el-tooltip :disabled="scope.row.flaw.length <= 8" :content="scope.row.flaw">
                  <div>{{ scope.row.flaw }}</div>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            header-align="left"
            width="115px"
          >
            <template slot-scope="scope">
              <el-button
                type="secondary"
                border
                class="edit-btn"
                width="68px"
                height="38px"
                @click="handleDel(scope.row.draftNo)"
              >
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <div class="footer">
      <div class="footer-text">
        共<span class="footer-red"> {{ tableData.length }} </span>
        张票据，总金额
        <span class="footer-red"> {{ yuan2wan(draftAmount) }} </span>
        万元
      </div>
      <div>
        <el-button size="large" @click="dialogVisible = false">取消</el-button>
        <el-button
          type="secondary"
          size="large"
          @click="updateDiscernTag"
        >
          确认同步
        </el-button>
      </div>
    </div>

    <!-- 发布结果状态弹窗 -->
    <ResultStatusDialog ref="resultStatusDialogRef" @confirm="dialogVisible = false" />
  </el-dialog>
</template>

<script>
import {
  ACCEPTOR_TYPE_VALUE_MAP, // 承兑人类型 id 映射 名称
  IDENTIFY_TYPE, // 识别类型
  BACK_DEFECT_TYPE_NAME_MAP, // 瑕疵类型
  getKeyToValueMap, // 获取映射信息
  BACK_DEFECT_TYPE_SHOW_NUM_MAP, // 票据瑕疵类型 id 映射 是否显示数字
  BACK_DEFECT_TYPE_VALUE_MAP, // 票据瑕疵类型 id 映射 名称
} from '@recognize/constant'
import { yuan2wan } from '@/common/js/number' // 金额单位转换
import mixpanel from '@recognize/utils/mixpanel'
import { formatTime } from '@/common/js/date' // 时间格式化
import ResultStatusDialog from './result-status-dialog.vue' // 发布结果状态弹窗
import ticketApi from '@recognize/apis/ticket'

// 瑕疵类型 key 映射 id
const BACK_DEFECT_TYPE_KEY_ID_MAP = getKeyToValueMap(BACK_DEFECT_TYPE_NAME_MAP, 'key', 'id')

export default {
  name: 'order-sync-update',

  components: {
    ResultStatusDialog, // 发布结果状态弹窗
  },

  data() {
    return {
      ACCEPTOR_TYPE_VALUE_MAP, // 承兑人类型 id 映射 名称
      IDENTIFY_TYPE, // 识别类型
      dialogVisible: false,
      recognitionData: null, // 生成截图的数据
      orderAmount: 0, // 订单数量
      orderData: [], // 订单数据
      draftData: {}, // 识别的票据数据
      holidayList: [], // 节假日列表
      keyword: '', // 搜索参数
    }
  },

  computed: {
    // 总金额
    draftAmount() {
      const amountArr = this.tableData.map(item => item.draftAmount)
      const initialValue = 0
      return amountArr.reduce(
        (previousValue, currentValue) => previousValue + currentValue,
        initialValue
      )
    },

    // 表格数据
    tableData() {
      return this.keyword.trim() ? this.orderData.filter(item => item.draftNo === this.keyword.trim()) : this.orderData
    }
  },

  methods: {
    yuan2wan,
    formatTime,

    open(data) {
      this.keyword = ''
      this.$refs.resultStatusDialogRef && this.$refs.resultStatusDialogRef.close()
      this.getOrderData(data.orderData)
      this.draftData = data.draftData
      this.dialogVisible = true
      // this.getHolidays(this.draftData.maturityDate)
    },

    // 获取订单数据
    getOrderData(orderData) {
      this.orderData = JSON.parse(JSON.stringify(orderData))
      this.orderData.forEach(item => {
        item.flaw = this.toDefectStr(this.defectsToStr(item.defects)) || '无瑕疵'
      })
      this.orderAmount = orderData.length
    },

    // 瑕疵转换
    defectsToStr(defects) {
      let strArr = []
      for (let i in defects) {
        if (i !== 'defectsOther' && i !== 'defectsOtherDesc' && defects[i]) {
          strArr.push(`${BACK_DEFECT_TYPE_KEY_ID_MAP[i]}_${defects[i]}`)
        } else if (i === 'defectsOther' && defects[i]) {
          strArr.push(`${BACK_DEFECT_TYPE_KEY_ID_MAP[i]}_${defects.defectsOtherDesc}`)
        }
      }
      return strArr.join('|')
    },

    // 移除
    handleDel(draftNo) {
      this.$confirm('确定要移除该票据吗？', '删除提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'no-header-msg',
        type: 'warning'
      }).then(() => {
        // 删除票据数据
        let deleteOrderIndex = this.orderData.findIndex(item => item.draftNo === draftNo)
        this.orderData.splice(deleteOrderIndex, 1)
        this.$message({
          type: 'success',
          message: '移除成功!'
        })
      })
        .catch(() => {
          this.$message({
            type: 'warning',
            message: '已取消'
          })
        })
    },

    // 更新签手订单
    async updateDiscernTag() {
      this.$refs.resultStatusDialogRef.init()
      const { acceptorName, draftNo, draftId, draftAmount, maturityDate, originalDefects, discernSource, draftSignInStatus } = this.draftData
      let updateOrderNo = this.tableData.map(item => (item.orderNo))
      try {
        const res = await ticketApi.updateDiscernTag({
          orderNos: updateOrderNo,
          draftDiscernId: draftId,
          forceUpdateTag: 1,
          draftSignInStatus
        })
        this.$refs.resultStatusDialogRef.init(res)
        mixpanel.replaceQianOrder({
          draftNo,
          draftAmount,
          acceptor: acceptorName,
          expiryDate: maturityDate,
          defect: (this.toDefectStr(originalDefects) || '无瑕疵'),
          bank: discernSource,
        })
      } catch (e) {
        this.$refs.resultStatusDialogRef && this.$refs.resultStatusDialogRef.close()
      }
    },

    // 瑕疵显示转换
    toDefectStr(str) {
      let res = str && str.split('|').map(item => {
        if (Number(item.split('_')[0]) === BACK_DEFECT_TYPE_NAME_MAP.OTHER.id && (item.split('_')[1] === 'null' || item.split('_')[1] === 'undefined' || !item.split('_')[1])) {
          return BACK_DEFECT_TYPE_VALUE_MAP[item.split('_')[0]]
        }
        return BACK_DEFECT_TYPE_VALUE_MAP[item.split('_')[0]] + (BACK_DEFECT_TYPE_SHOW_NUM_MAP[item.split('_')[0]] ? `(${item.split('_')[1]})` : '')
      })
        .join('，')
      return res || ''
    },

  }
}
</script>
