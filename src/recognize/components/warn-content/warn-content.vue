<!-- 警告文本 -->
<style lang="scss" scoped>
.green-warn-content {
  overflow: hidden;
  border-radius: 2px;
  padding: 6px 13px;
  width: 100%;
  font-size: 14px;
  color: $color-text-primary;
  line-height: 24px;
}

.info-circle-icon {
  float: left;
  margin-right: 9px;
  font-size: 14px;
}

.red-icon {
  color: $color-warning;
}

.green-icon {
  color: $color-008489;
}

.red-bg {
  background-color: $--color-primary-hover;
}

.green-bg {
  background-color: $color-E6F3F3;
}
</style>

<template>
  <div :class="[classType === 'red' ? 'red-bg' : 'green-bg', 'green-warn-content']">
    <i :class="[classType === 'red' ? 'red-icon' : 'green-icon', 'elicon', 'elicon-info-circle', 'info-circle-icon']" />
    <div class="content"><slot /></div>
  </div>
</template>

<script>
export default {
  name: 'warn-content',
  props: {
    classType: String, // 样式类型，默认绿色，可选红色
  },
}
</script>
