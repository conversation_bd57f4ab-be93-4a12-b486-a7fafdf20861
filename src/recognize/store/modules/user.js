import creditApi from '@recognize/apis/credit'
import userApi from '@recognize/apis/user'
import user from '@/utils/user'

let getUserPromise = null

export default {
  namespaced: true,
  state() {
    return {
      userInfo: null, // 用户信息
      isLogined: !!user.getToken(), // 是否已经登录
      corpInfo: null, // 企业信息
      creditInfo: {}, // 我的信用信息
      showCreditRemind: 0, // 是否显示信用分提示
    }
  },
  getters: {
    limitLight: state => !!state?.corpInfo?.limitLight, // 是否限制光速交易
  },
  mutations: {
    // 更新用户信息
    updateUser(state, payload) {
      state.isLogined = !!payload
      state.userInfo = payload
    },
    // 设置企业信息
    setCorpInfo(state, payload) {
      state.corpInfo = payload
    },
    // 赋值函数
    setState: (state, objects) => {
      Object.keys(objects).forEach(key => {
        state[key] = objects[key]
      })
    },
    // 设置信用分提示
    setShowCreditRemind(state, showCreditRemind) {
      state.showCreditRemind = showCreditRemind
    },
  },
  actions: {
    // 获取当前用户信息
    async getUserInfo(context, payload) {
      // forceUpdate 表示是否强制更新
      if (context.state.userInfo && !payload?.forceUpdate) {
        return context.state.userInfo
      }
      if (!getUserPromise || payload?.forceUpdate) {
        getUserPromise = userApi.getUserInfo()
      }
      // 获取用户信息
      try {
        const userInfo = await getUserPromise
        context.commit('updateUser', userInfo)
      } finally {
        // eslint-disable-next-line require-atomic-updates
        getUserPromise = null
      }
      return context.state.userInfo
    },

    // 登出
    logout(context) {
      // 是否手动退出
      // const { manual } = payload
      // if (manual) {
      // 调用 postLogout 接口会导致登录该账户的其他电脑也退出，根据产品要求，这里注释掉
      //   await userApi.postLogout()
      // }
      user.clearToken()
      context.commit('updateUser', null)
      window.location.reload()
    },
    // 获取企业信息
    async getCorpInfo(context) {
      const res = await userApi.getCorpInfo()
      context.commit('setCorpInfo', res)
      return res
    },

    // 获取我信用信息
    async getMyCredit({ state, commit }, payload) {
      if (state.creditInfo && Object.keys(state.creditInfo).length && !payload?.forceUpdate) {
        return state.creditInfo
      }
      const data = await creditApi.getMyCredit()
      commit('setState', {
        creditInfo: data || {},
      })
      return data || {}
    },

    // 获取是否显示信用分提醒
    async getShowCreditRemind({ commit, dispatch, state }) {
      await dispatch('getMyCredit')
      const { creditInfo } = state
      try {
        // 信用分在500-550之间，再请求接口
        // eslint-disable-next-line no-magic-numbers
        if (creditInfo && creditInfo.currentCreditPoints >= 500 && creditInfo.currentCreditPoints <= 550) {
          const data = await creditApi.getCreditRemind()
          commit('setShowCreditRemind', data || 0)
        } else {
          commit('setShowCreditRemind', 0)
        }
      } catch (err) {
        commit('setShowCreditRemind', 0)
      }
    }
  }
}
