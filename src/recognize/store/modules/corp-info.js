import Storage from '@/common/js/storage'
import { USER_INFO } from '@/constant-storage'
import API from '@recognize/apis/user'

export default {
  namespaced: true,
  state() {
    return {
      spAxiosSucc: false,
      spFlag: '',
      spToken: '',
    }
  },
  getters: {
  },
  mutations: {
    SET_SP_FLAG(state, data) {
      state.spAxiosSucc = data
    },
    SET_SP_CODE(state, data) {
      state.spFlag = data.flag
      state.spToken = data.token
    },
  },
  actions: {
    // 商票板企业信息同步并获取token
    getCorpInfoSync({ commit, state }, path) {
      const spbUrl = process.env.VUE_APP_SPB_URL
      const userInfo = Storage.get(USER_INFO) || {}
      return new Promise(resolve => {
        if (state.spAxiosSucc) {
          let url = `${spbUrl}${path}?flag=${state.spFlag}&token=${state.spToken}`
          resolve(url)
          return
        }
        // 5分钟内直接取state的值  5分钟后请求在调用接口
        let flag = false
        setTimeout(() => {
          if (!flag) {
            flag = true
            let url = `${spbUrl}${path}`
            resolve(url)
          }
        }, 5000)
        const corpInfo = {
          corpId: userInfo.corpId,
          mobile: userInfo.mobile
        }
        API.getCorpInfoSync(corpInfo)
          .then(data => {
            try {
              if (!flag) {
                flag = true
                if (data && data.flag === 0 && data.token) {
                  commit('SET_SP_CODE', data)
                  commit('SET_SP_FLAG', true)
                  let href = `${spbUrl}${path}?flag=${data.flag}&token=${data.token}`
                  setTimeout(() => {
                    commit('SET_SP_FLAG', false)
                  }, 300000)
                  resolve(href)
                } else {
                  resolve(`${spbUrl}${path}`)
                }
              }
            } catch (err) {
              if (!flag) {
                flag = true
                resolve(`${spbUrl}${path}`)
              }
            }
          })
          .catch(() => {
            if (!flag) {
              flag = true
              resolve(`${spbUrl}${path}`)
            }
          })
      })
    }
  },
}
