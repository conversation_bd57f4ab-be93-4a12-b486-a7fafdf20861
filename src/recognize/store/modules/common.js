import commonApi from '@recognize/apis/common'
import { formatTime, dealTime } from '@/common/js/date'
import { AGREE_USAGE_NOTICE } from '@recognize/constant-storage'
import Storage from '@/common/js/storage' // 本地缓存对象

export default {
  namespaced: true,
  state() {
    return {
      holidays: [], // 节假日列表
      diffTime: 0, // 本地与服务器的时间差 单位是毫秒，大于0表示，本地时间比服务器时间快
      closeMarket: 0, // 当前是否闭市
      closeMarketStartTime: 0, // 闭市开始时间
      closeMarketEndTime: 0, // 闭市结束时间
      radarLimit: {}, // 雷达限制信息
      fastLimit: {}, // 光速限制信息
      isAgreeUsageNotice: Storage.get(AGREE_USAGE_NOTICE), // 是否同意使用须知
    }
  },
  getters: {
    holidays: state => state.holidays,
  },
  mutations: {
    // 更新节假日列表
    updateHolidays(state, payload) {
      state.holidays = payload
    },
    // 更新服务器与本地的时间差
    updateDiffTime(state, payload) {
      state.diffTime = payload
    },
    // 更新闭市
    updateCloseMarket(state, closeObj) {
      state.closeMarket = closeObj?.closeMarket || 0
      state.closeMarketStartTime = closeObj?.startTime || 0
      state.closeMarketEndTime = closeObj?.endTime || 0
    },
    // 更新违约限制雷达交易
    updateRadarLimit(state, obj) {
      const { punishIng = 0, triggerPunishTime = '', day } = obj
      let startTime = ''
      let endTime = ''
      if (punishIng) {
        startTime = dealTime(triggerPunishTime)
        // 计算恢复时间
        const startDate = new Date(startTime)
        endTime = startDate.setDate(startDate.getDate() + 1 + day)
        // 格式化
        startTime = formatTime(startTime, 'YYYY-MM-DD')
        endTime = formatTime(endTime, 'YYYY-MM-DD')
      }
      state.radarLimit = {
        ...obj,
        startTime,
        endTime
      }
    },
    // 获取违约限制光速交易
    updateFastLimit(state, obj) {
      const { punishIng = 0, triggerPunishTime = '', day } = obj
      let startTime = ''
      let endTime = ''
      if (punishIng) {
        startTime = dealTime(triggerPunishTime)
        // 计算恢复时间
        const startDate = new Date(startTime)
        endTime = startDate.setDate(startDate.getDate() + 1 + day)
        // 格式化
        startTime = formatTime(startTime, 'YYYY-MM-DD')
        endTime = formatTime(endTime, 'YYYY-MM-DD')
      }
      state.fastLimit = {
        ...obj,
        startTime,
        endTime
      }
    },
    // 更新同意使用须知
    updateIsAgree(state, payload) {
      state.isAgreeUsageNotice = payload
    },
  },
  actions: {
    // 获取节假日列表
    async getHolidays(context) {
      try {
        const list = await commonApi.getHolidays()
        context.commit('updateHolidays', list)
      } catch (err) {
        // eslint-disable-next-line no-console
        console.log(err)
      }
    },
    // 获取当前服务器的时间
    async getSysCurrentTime(context) {
      try {
        const sysTime = await commonApi.getSysCurrentTime()
        const localTime = new Date().getTime()
        context.commit('updateDiffTime', localTime - sysTime)
      } catch (err) {
        // eslint-disable-next-line no-console
        console.log(err)
      }
    },

    // 获取当前闭市的配置
    async getCloseMarket(context) {
      try {
        const res = await commonApi.getCloseMarket()
        context.commit('updateCloseMarket', res)
      } catch (err) {
        // eslint-disable-next-line no-console
        console.log(err)
      }
    },
    // 获取违约限制雷达交易
    async getRadarLimit(context) {
      try {
        const res = await commonApi.getRadarLimit()
        context.commit('updateRadarLimit', res || {})
      } catch (err) {
        // eslint-disable-next-line no-console
        console.log(err)
      }
    },
    // 获取违约限制光速交易
    async getFastLimit(context) {
      try {
        const res = await commonApi.getFastLimit()
        context.commit('updateFastLimit', res || {})
      } catch (err) {
        // eslint-disable-next-line no-console
        console.log(err)
      }
    },
  }
}
