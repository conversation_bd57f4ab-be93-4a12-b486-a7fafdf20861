import { api } from '@/utils/axios'

const userApi = {
  // 通过手机号码获取验证码
  getVerifyCode(phone) {
    return api.get('/user/getVerifyCode', { phone }, {
      mock: false
    })
  },

  // 用户登录
  postMobilePasswordLogin(body) {
    return api.post('/api/discern/user/login/mobilePasswordLogin', body, {
      mock: false,
      returnOrigin: true,
      withoutCheck: true,
      showError: false
    })
  },

  // 用户退出登录
  postLogout() {
    return api.post('/api/discern/user/logout', null, {
      mock: false
    })
  },

  // 新增用户平台账号
  postPlatformAccount(body) {
    return api.post('/user/platformAccount', body, {
      mock: false
    })
  },

  // 发布票据时查询可用的平台账号列表
  getPlatformAccount() {
    return api.get('/user/platformAccount', null, {
      mock: false
    })
  },

  // 获取新增平台发布账号是否成功
  getPlatformAccountResult(accountId) {
    return api.get(`/user/platformAccount/result/${accountId}`, null, {
      mock: false
    })
  },

  // 查询用户平台账号列表
  getPlatformAccountList(body) {
    return api.post('/user/platformAccount/list', body, {
      mock: false
    })
  },

  // 删除用户平台账号
  delPlatformAccount(id) {
    return api.delete(`/user/platformAccount/${id}`, null)
  },

  // 获取用户设置
  getConfig() {
    const os = navigator.platform.indexOf('Win') > -1 ? 'window' : 'mac'
    return api.get(`/api/discern/systemConfig/getCommonConfig?os=${os}`, null, {
      mock: false
    })
  },

  // 修改用户设置
  putConfig(body) {
    return api.post('/api/discern/systemConfig/updateCommonConfig', body, {
      mock: false
    })
  },

  // 新增用户自有户
  postOwnCompany(body) {
    return api.post('/api/discern/systemConfig/addOwnCorp', body, {
      showError: false
    })
  },

  // 查询自有户列表
  getOwnCompany(body) {
    return api.get('/api/discern/systemConfig/getOwnCorps', body, {
      mock: false
    })
  },

  // 删除用户自有户
  delOwnCompany(id) {
    return api.delete(`/api/discern/systemConfig/deleteOwnCorp/${id}`, null, {
      mock: false
    })
  },

  // 获取用户信息
  getUserInfo() {
    return api.post('/api/discern/user/getLoginUserInfo', null, {
      mock: false
    })
  },

  // 查询商户开户信息
  getCorpOpenInfo() {
    return api.get('/api/discern/corpOpenInfo', null, {
      mock: false
    })
  },

  // 获取用户分组
  getUserGroup() {
    return api.get('/api/platform/switchGroup/corpMember/corpMemberList', null, {
      mock: false
    })
  },

  // 移除用户分组
  delUserGroup(uid) {
    return api.delete(`/api/platform/switchGroup/removeCorpMember/${uid}`, null, {
      mock: false
    })
  },

  // 分组用户切换
  changeUserGroup(uid) {
    return api.post(`/api/platform/switchGroup/switchCorpMember${uid}`, null, {
      mock: false,
      returnOrigin: true,
      withoutCheck: true,
    })
  },

  // 签手使用建议（问题反馈）
  postFeedback(body) {
    return api.post('/api/discern/systemConfig/proposal', body, {
      mock: false
    })
  },

  // 获取发布配置
  getPostOrderConfig() {
    return api.get('/api/discern/systemConfig/getPostOrderConfig', null, {
      mock: false,
      showError: false
    })
  },

  // 获取企业信息
  getCorpInfo() {
    return api.get('/api/discern/corp/corpInfo', null, {
      mock: false,
      isCancelRequest: false
    })
  },

  // 同步商票版用户信息
  getCorpInfoSync(param) {
    return api.post('/api/discern/spb/corpInfoSync', param, {
      mock: false,
    })
  },

  // 获取用户联系方式配置
  getMobileList() {
    return api.get('/api/discern/order/mobile/getMobileList', null, {
      mock: false
    })
  }
}

export default userApi
