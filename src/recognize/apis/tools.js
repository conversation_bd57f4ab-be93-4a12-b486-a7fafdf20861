import { api } from '@/utils/axios'

const toolsApi = {
  // 获取背书手数配置
  getEndorseCountConfig(params) {
    return api.get('/api/discern/systemConfig/getEndorseCountConfig', params, {
      mock: false,
      withoutCheck: true,
    })
  },

  // 更新背书手数配置
  updateEndorseCountConfig(body) {
    return api.post('/api/discern/systemConfig/updateEndorseCountConfig', body, {
      mock: false,
      canEmpty: true
    })
  },

  // 配置同步
  configAsync(body) {
    return api.post('/api/discern/systemConfig/config/async', body, {
      mock: false,
    })
  },
  // 配置同步--NEW
  configAsyncNEW(body) {
    return api.post('/api/platform/systemConfig/config/async', body, {
      mock: false,
    })
  },

  // 获取账户列表
  getAccountList() {
    return api.get('/api/platform/switchGroup/corpMember/corpMemberList', null, {
      mock: false,
    })
  },

  // 获取账户列表-New
  getUserGroupNew() {
    return api.get('/api/platform/systemConfig/user/group', null, {
      mock: false
    })
  },
  // 获取签手黑名单设置
  getBlackList(type, params) {
    return api.get(`/api/discern/systemConfig/getBlackList/${type}`, params, {
      mock: false
    })
  },

  // 黑名单分页
  getBlackListByPage(type, params) {
    return api.post('/api/discern/systemConfig/getBlackListByPage', {
      blackContentType: type,
      ...params
    }, {
      mock: false
    })
  },

  // 新增签手黑名单
  addBlackList(body) {
    return api.post('/api/discern/systemConfig/addBlack', body, {
      mock: false
    })
  },

  // 删除签手黑名单
  deleteBlackList(id, body) {
    return api.delete(`/api/discern/systemConfig/deleteBlack/${id}`, body, {
      mock: false
    })
  },

  // 批量删除签手黑名单
  batchDeleteBlackList(body) {
    return api.post('/api/discern/systemConfig/deleteByIds', body, {
      mock: false
    })
  },

  // 导入签手黑名单
  importUserBlackConfig(body) {
    return api.post('/api/discern/systemConfig/importUserBlackConfig', body, {
      mock: false
    })
  },

  // 获取白名单传给C++ 区分是否java识别
  getJavaBlackList(id, body) {
    return api.get('/api/discern/parse/bankList', body, {
      mock: false
    })
  },
  // 后端单张识别
  JavaParseHtml(body) {
    return api.post('/api/discern/parse/parseHtml', body, {
      mock: false,
      showError: false,
      headers: {
        'is-check-attack': false
      }
    })
  },
  // 后端识别  批量识别
  batchJavaParseHtml(body) {
    return api.post('/api/discern/parse/batchParseHtml', body, {
      mock: false,
      showError: false,
      headers: {
        'is-check-attack': false
      }
    })
  },
  // 前端解析 后端识别批量发布
  batchwebSwitchJavaAnalysis(body) {
    return api.post('/api/discern/parse/verifyBatchParseData', body, {
      mock: false,
      showError: false,
      headers: {
        'is-check-attack': false
      }
    })
  },
  // 前端解析 后端识别单张发布
  webSwitchJavaAnalysis(body) {
    return api.post('/api/discern/parse/verifyParseData', body, {
      mock: false,
      showError: false,
      headers: {
        'is-check-attack': false
      }
    })
  },

  // 自动同步接口
  autoSyncParseData(body) {
    return api.post('/api/discern/parse/autoSyncParseData', body, { showError: false })
  }
}

export default toolsApi
