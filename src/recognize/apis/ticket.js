import { api } from '@/utils/axios'

const ticketApi = {
  // 获取同步记录
  getDraftList(body) {
    return api.post('/api/discern/draft/search', body, {
      mock: false
    })
  },
  // 提交识别到的票据信息
  saveDrafts(body) {
    return api.post('/api/discern/draft/save', body, {
      mock: false,
      timeout: 20000,
      showError: false
    })
  },

  // 将订单更新为签手标签
  updateDiscernTag(body) {
    return api.put('/api/discern/draft/updateDiscernTag', body, {
      mock: false,
    })
  },

  // 单张发布票据
  postRelease(body) {
    return api.post('/api/discern/draft/postOrder', body, {
      mock: false,
      showError: false,
    })
  },

  // 连号票发布票据
  postSerialDraftOrder(body) {
    return api.post('/api/discern/draft/postSerialDraftOrder', body, {
      mock: false,
      showError: false,
    })
  },

  // 批量发布票据
  postBatchPostOrder(body) {
    return api.post('/api/discern/draft/batchPostOrder', body, {
      mock: false,
      showError: false,
    })
  },

  // 获取深度待签收订单详情，用于智能验票
  getWaitSignOrder(id) {
    return api.get(`/api/discern/draft/order/${id}`, null, {
      mock: false,
      showError: false,
    })
  },

  // 删除票据
  delDraft(body) {
    return api.delete('/api/discern/draft/delete', body, {
      mock: false
    })
  },

  // 票面详情
  searchDiscernDetail(body) {
    return api.post('/api/discern/draft/searchDiscernDetail', body)
  },

  // 校验定向码
  validateAgentOrder(body) {
    return api.post('/api/discern/draft/validateAgentOrder', body, {
      mock: false,
      showError: false
    })
  },
  // 校验地区黑名单
  validateAgentOrderArea(body) {
    return api.post('/api/platform/draft/order/agentLimit', body, {
      mock: false,
      showError: false
    })
  },

  // 发布全部未发布票据
  publishAll() {
    return api.get('/api/discern/draft/allRecord', null, {
      mock: false,
    })
  },

  // 通过票号获取票据记录
  getRecordByDraftNo(body) {
    return api.post('/api/discern/draft/getRecordByDraftNo', body, {
      mock: false,
    })
  },

  // 获取可更新签手标签订单
  getUpdateTagOrders(id) {
    return api.get(`/api/discern/draft/getUpdateDiscernTagRecord/${id}`, null, {
      mock: false,
    })
  },

  // 报价行情参考
  enquiryPrice(body) {
    return api.post('/api/discern/draft/enquiryPrice', body, {
      mock: false
    })
  },

  // 商票白名单权限校验
  whiteListCheck(body) {
    return api.post('/api/discern/operation/commercialBillWhiteList/whiteListCheck', body, {
      mock: false
    })
  },

  // 商票交易权限申请是否在途查询
  existUnderReviewAccessCorpApply(corpId) {
    return api.get(`/api/discern/operation/commercialBillWhiteList/existUnderReviewAccessCorpApply/${corpId}`, {}, {
      mock: false
    })
  },

  // 获取票据瑕疵的详细信息，包含瑕疵出现的位置
  getDefectsDetail(params) {
    return api.get('/api/discern/draft/draftDefectsDetail', params, {
      mock: false
    })
  },
}
export default ticketApi
