import { api } from '@/utils/axios'

const commonApi = {
  // 获取节假日列表(无鉴权)
  getHolidays() {
    return api.get('/api/discern/common/getHoliday', null, {
      mock: false,
    })
  },

  // 获取承兑人类型
  getAcceptorType(data) {
    return api.get(`/api/discern/common/getAcceptorInfo/${data}`, null, {
      mock: false,
    })
  },

  // 承兑人通用标签列表根据票据类型搜索
  getAcceptorCommonLabel(body) {
    return api.post('/api/discern/common/acceptorCommonLabel/list', body, {
      mock: false,
    })
  },

  // 承兑人商票标签列表根据承兑人名称搜索
  getAcceptorCommercialLabel(body) {
    return api.post('/api/discern/common/acceptorCommercialLabel/list', body, {
      mock: false,
    })
  },

  // 网银同步埋点数据，暂无使用
  buriedPointData() {
    return api.post('/user/ebank/buriedPointData')
  },

  // 文件上传-获取policy授权信息
  getUploadPolicy(body) {
    return api.get('/api/discern/operation/common/upload/policy', body, {
      mock: false,
    })
  },
  // 获取服务器当前时间戳
  getSysCurrentTime(params) {
    return api.get('/api/discern/common/getSysCurrentTime', params, {
      mock: false,
      showError: false,
    })
  },

  // 获取运营闭市配置信息
  getCloseMarket() {
    return api.get('/api/discern/common/getCloseMarket', {}, {
      mock: false,
    })
  },
  // 获取违约限制雷达交易
  getRadarLimit() {
    return api.get('/api/discern/punish/order/limit/accept/radar', {}, {
      mock: false,
    })
  },
  // 获取违约限制光速交易
  getFastLimit() {
    return api.get('/api/discern/punish/order/limit/publish/light', {}, {
      mock: false,
    })
  },
  // 用户访问场景记录
  postVisitRecord(body) {
    return api.post('/api/discern/common/visit/record', body, {
      mock: false,
    })
  },
  // 识票助手支持银行列表
  getSupportBank(body) {
    return api.post('/api/discern/discernBank/supportBankList', body, {
      mock: false,
    })
  }
}

export default commonApi
