import { api } from '@/utils/axios'

const draftStockApi = {
  // 票据库存列表查询
  getStockList(body) {
    return api.post('/api/discern/draft/list', body, {
      mock: false
    })
  },
  // 票据库存列表查询--历史
  getStockHistoryList(body) {
    return api.post('/api/discern/draft/history/list', body, {
      mock: false
    })
  },
  // 获取票据库存总数量
  getStockTabNum(body) {
    return api.post('/api/discern/draft/tabNum', body, {
      mock: false
    })
  },
  // 获取票据库存总数量--历史
  getStockHistoryTabNum(body) {
    return api.post('/api/discern/draft/history/tabNum', body, {
      mock: false
    })
  },
  // 导出库存列表数据
  discernExportList(body) {
    return api.post('/api/discern/draft/exportList', body, {
      mock: false,
      responseType: 'blob',
      withoutCheck: true
    })
  },
  // 导出库存列表数据--历史
  discernExportHistoryList(body) {
    return api.post('/api/discern/draft/history/exportList', body, {
      mock: false,
      responseType: 'blob',
      withoutCheck: true
    })
  },

  // 分享库存列表数据
  discernShareUrl(body) {
    return api.post('/api/discern/draft/shareUrl', body, {
      mock: false,
    })
  },
  // 分享库存列表数据--历史
  discernShareHistoryUrl(body) {
    return api.post('/api/discern/draft/history/shareUrl', body, {
      mock: false,
    })
  },

  // 查询库存票据详情
  discernDetail(discernId) {
    return api.get(`/api/discern/draft/${discernId}`, null, {
      mock: false,
    })
  },
  // 查询库存票据详情--历史
  discernHistoryDetail(discernId) {
    return api.get(`/api/discern/draft/history/${discernId}`, null, {
      mock: false,
    })
  },

  // 票据出库
  discernOutStock(body) {
    return api.post('/api/discern/draft/outStock', body, {
      mock: false,
    })
  },
  // 票据出库--历史
  discernOutStockHistory(body) {
    return api.post('/api/discern/draft/history/outStock', body, {
      mock: false,
    })
  },

  // 批量出库
  batchDiscernOutStock(body) {
    return api.post('/api/discern/draft/batchOutStock', body, {
      mock: false
    })
  },

  // 批量出库--历史
  batchDiscernOutStockHistory(body) {
    return api.post('/api/discern/draft/history/batchOutStock', body, {
      mock: false
    })
  },

  // 已出库备注
  addRemark(body) {
    return api.post('/api/discern/draft/addRemark', body, {
      mock: false
    })
  },
  // 已出库备注--历史
  addRemarkHistory(body) {
    return api.post('/api/discern/draft/history/addRemark', body, {
      mock: false
    })
  },

  // 删除库存
  discernBatchDelete(body) {
    return api.post('/api/discern/draft/batchDelete', body, {
      mock: false,
      showError: false,
    })
  },
  // 删除库存--历史
  discernBatchHistoryDelete(body) {
    return api.post('/api/discern/draft/history/batchDelete', body, {
      mock: false,
      showError: false,
    })
  },
}

export default draftStockApi
