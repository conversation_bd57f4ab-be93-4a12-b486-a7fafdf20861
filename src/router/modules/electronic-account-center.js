import ModuleNotFound from '@/views/components/error/module-not-found.vue'
import { setCommonMeta } from '../index'
import AccountLayout from '@/views/pages/electronic-account-center/account-layout.vue'

// 添加全局配置信息
const commonMeta = {
  requiredLogin: true, // 该路由需要登录才可访问
}

const electronicAccountRouter = [
  {
    path: '/',
    component: AccountLayout,
    children: [
      // 账户首页
      {
        path: '/electronic-account-home',
        name: 'electronicAccountHome',
        component: () => import(/* webpackChunkName: "electronic-account" */ '@/views/pages/electronic-account-center/account-home/account-home.vue').catch(() => ModuleNotFound)
      },
      // 我的订单详情
      {
        path: '/electronic-account-order-detail',
        name: 'electronicAccountOrderDetail',
        component: () => import(/* webpackChunkName: "electronic-account" */ '@/views/pages/electronic-account-center/account-order-detail/account-order-detail.vue')
      },
      // 银行交易明细
      {
        path: '/electronic-account-transaction-detail',
        name: 'electronicAccountTransactionDetail',
        component: () => import(/* webpackChunkName: "electronic-account" */ '@/views/pages/electronic-account-center/transaction-detail/transaction-detail.vue')
      },
      // 账户管理
      {
        path: '/electronic-account-manage',
        name: 'electronicAccountManage',
        component: () => import(/* webpackChunkName: "electronic-account" */ '@/views/pages/electronic-account-center/account-manage/account-manage.vue')
      },
      // 银行充值
      {
        path: '/electronic-account-recharge',
        name: 'electronicAccountRecharge',
        component: () => import(/* webpackChunkName: "electronic-account" */ '@/views/pages/electronic-account-center/account-recharge/account-recharge.vue')
      },
    ]
  }
]

setCommonMeta(electronicAccountRouter, commonMeta)
export default electronicAccountRouter
