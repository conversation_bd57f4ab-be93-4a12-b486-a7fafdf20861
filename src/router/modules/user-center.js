import ModuleNotFound from '@/views/components/error/module-not-found.vue'
import store from '@/store'
import { setCommonMeta } from '../index'
// import { SDM_NAME } from '@/constant'

// 添加全局配置信息
const commonMeta = {
  requiredLogin: true, // 该路由不需要登录也可访问
}
// 智能助手、我收到的页面上方跑马灯文案
const cuteNotice = '小票服务费<span style="color:#ec3535;font-weight: 700;"> 最高享100%全免</span> ，省出全年利润！具体可至活动详情查看！'

// 云豆账户
const sdmNotice = '充值赠送活动来袭，<span style="color:#ec3535;font-weight: 700;"> 最高赠送25%</span>，上不封顶！'
// 发布票据、我发布的页面上方跑马灯文案
const saleNotice = '极速订单笔笔有奖励，<span style="color:#ec3535;font-weight: 700;">最高80元/笔</span>；实时赠送，上不封顶！'

const routerConfigs = [
  {
    path: '/user-center',
    name: 'userCenter',
    redirect: '/user-center/welcome',
    component: () => import(/* webpackChunkName: "user-center-layout" */ '@/views/layouts/user-center-layout/user-center-layout.vue').catch(() => ModuleNotFound),
    children: [
      // {
      //   path: '/welcome',
      //   name: 'welcome',
      //   component: () => import(/* webpackChunkName: "page-index" */ '@/views/pages/welcome/welcome.vue').catch(() => ModuleNotFound),
      // },
      {
        path: 'welcome',
        name: 'welcome',
        component: () => import(/* webpackChunkName: "welcome" */ '@/views/pages/welcome/welcome.vue').catch(() => ModuleNotFound),
        meta: {
          title: '欢迎页'
        }
      },
      {
        path: 'info',
        name: 'userCenterInfo',
        component: () => import(/* webpackChunkName: "info" */ '@/views/pages/user-center/user-center.vue').catch(() => ModuleNotFound),
        meta: {
          title: '账户信息',
          type: 'info'
        }
      },
      {
        path: 'account',
        name: 'userCenterAccount',
        component: () => import(/* webpackChunkName: "info" */ '@/views/pages/user-center/user-center.vue'),
        meta: {
          title: '电子交易账户',
          type: 'account'
        }
      },
      // {
      //   path: 'invite-friends',
      //   name: 'invite-friends',
      //   component: () => import(/* webpackChunkName: "info" */ '@/views/pages/user-center/invite-friends/invite-friends.vue').catch(() => ModuleNotFound),
      //   meta: {
      //     title: '邀请好友'
      //   }
      // },
      // 发布票据
      {
        path: 'issue-draft',
        name: 'issueDraft',
        component: () => import(/* webpackChunkName: "issue-draft" */ '@/views/pages/issue-draft/issue-draft.vue').catch(() => ModuleNotFound),
        meta: {
          title: '发布票据',
          requiredOpenAccount: true, // 必须开户才能访问
          requiredCloseMarket: true, // 需要获取闭市信息
          keepAlive: true,
          showNotice: false, // 是否展示通知
          domNotice: saleNotice, // 通知文案
          linkNotice: '/user-center/coupon?tabs=receiveCentre' // 通知跳转路由
        }
      },
      // 我的订单（票方）
      {
        path: 'sale-draft',
        name: 'saleDraft',
        component: () => import(/* webpackChunkName: "sale-draft" */ '@/views/pages/draft-list/sale-draft.vue').catch(() => ModuleNotFound),
        meta: {
          title: '我发布的',
          requiredOpenAccount: false, // 必须开户才能访问
          requiredCloseMarket: true, // 需要获取闭市信息
          requiredFastLimit: true, // 需要请求违约限制极速信息
          requiredCreditRemind: true, // 需要获取信用分提醒信息
          keepAlive: true,
          showNotice: false, // 是否展示通知
          domNotice: saleNotice, // 通知文案
          linkNotice: '/user-center/coupon?tabs=receiveCentre' // 通知跳转路由
        },
      },
      // 票据库存
      {
        path: 'draft-stock',
        name: 'draftStock',
        component: () => import(/* webpackChunkName: "draft-stock" */ '@/views/pages/draft-stock/draft-stock-recognize.vue').catch(() => ModuleNotFound),
        meta: {
          title: '票据库存',
          requiredOpenAccount: false, // 必须开户才能访问
          keepAlive: true,
        },
      },
      // 网银票据
      {
        path: 'online-bank-draft',
        name: 'onlineBankDraft',
        component: () => import(/* webpackChunkName: "online-bank-draft" */ '@/views/pages/online-bank-draft/online-bank-draft.vue').catch(() => ModuleNotFound),
        meta: {
          title: '网银票据',
          requiredOpenAccount: true,
          keepAlive: true,
        }
      },
      // 我的订单（80000)
      {
        path: 'buy-draft',
        name: 'buyDraft',
        component: () => import(/* webpackChunkName: "buy-draft" */ '@/views/pages/draft-list/buy-draft.vue').catch(() => ModuleNotFound),
        meta: {
          title: '我收到的',
          requiredOpenAccount: false, // 必须开户才能访问
          keepAlive: true,
          showNotice: true, // 是否展示通知
          // domNotice: '服务费限时活动，单笔起收<span style="color:#ec3535;font-weight: 700;">5元/笔</span>；', // 通知文案
          domNotice: cuteNotice, // 通知文案
          // 指定通道完成订单，资方笔笔返<span style="color:#ec3535;font-weight: 700;">100%月消费券</span>； 资方消费${SDM_NAME}，最高<span style="color:#ec3535;font-weight: 700;">返70%${SDM_NAME}。 </span>
          linkNotice: '/user-center/coupon?tabs=receiveCentre' // 通知跳转路由
        }
      },
      // 订单详情
      {
        path: 'draft-detail',
        name: 'draft-detail',
        component: () => import(/* webpackChunkName: "draft-detail" */ '@/views/pages/draft-detail').catch(() => ModuleNotFound),
        meta: {
          title: '订单详情',
          requiredOpenAccount: true,
          orderDetail: true, // 订单详情
          showBackBar: true, // 展示返回按钮
        }
      },
      // 用户设置
      {
        path: 'setting',
        name: 'setting',
        component: () => import(/* webpackChunkName: "setting" */ '@/views/pages/setting/setting.vue').catch(() => ModuleNotFound),
        meta: {
          title: '账户设置',
          requiredOpenAccount: true,
        }
      },
      // 争议订单
      {
        path: 'disputed-order',
        name: 'disputed-order',
        component: () => import(/* webpackChunkName: "disputed-order" */ '@/views/pages/user-center/disputed-order/disputed-order.vue').catch(() => ModuleNotFound),
        meta: {
          title: '争议订单',
          requiredOpenAccount: true,
          keepAlive: true,
        }
      },
      // 我的违约
      {
        path: 'my-breach',
        name: 'my-breach',
        component: () => import(/* webpackChunkName: "/my-breach" */ '@/views/pages/breach/index.vue').catch(() => ModuleNotFound),
        meta: {
          title: '违约订单',
          requiredOpenAccount: true,
          keepAlive: true,
        }
      },
      // 我关注的
      {
        path: 'focus-on',
        name: 'focus-on',
        component: () => import(/* webpackChunkName: "focus-on" */ '@/views/pages/focus-on/index.vue').catch(() => ModuleNotFound),
        meta: {
          title: '我关注的',
          requiredOpenAccount: true,
        }
      },
      {
        path: 'cute-hand',
        name: 'cute-hand',
        component: () => import(/* webpackChunkName: "cute-hand" */ '@/views/pages/cute-hand/cute-hand.vue').catch(() => ModuleNotFound),
        meta: {
          title: '智能助手',
          requiredCloseMarket: true, // 需要获取闭市信息
          keepAlive: true,
          showNotice: true, // 是否展示通知
          domNotice: cuteNotice, // 通知文案
          linkNotice: '/user-center/coupon?tabs=receiveCentre' // 通知跳转路由

        }
      },
      {
        path: 'cute-hand/:id',
        name: 'cute-hand',
        component: () => import(/* webpackChunkName: "cute-hand" */ '@/views/pages/cute-hand/cute-hand.vue').catch(() => ModuleNotFound),
        meta: {
          title: '智能助手',
          requiredCloseMarket: true, // 需要获取闭市信息
          keepAlive: true,
          showNotice: true, // 是否展示通知
          domNotice: cuteNotice, // 通知文案
          linkNotice: '/user-center/coupon?tabs=receiveCentre' // 通知跳转路由
        }
      },
      {
        path: 'market-new',
        name: 'market-new',
        component: () => import(/* webpackChunkName: "market" */ '@/views/pages/market-new/market-new.vue').catch(() => ModuleNotFound),
        meta: {
          title: '我要找票',
          requiredCloseMarket: true, // 需要获取闭市信息
          keepAlive: true,
        }
      },
      // 对方违约
      // {
      //   path: 'other-breach',
      //   name: 'other-breach',
      //   component: () => import(/* webpackChunkName: "other-breach" */ '@/views/pages/breach/other-breach.vue').catch(() => ModuleNotFound),
      //   meta: {
      //     title: '对方违约',
      //     requiredOpenAccount: true,
      //   }
      // },
      // 银行账户
      {
        path: 'bank-account',
        name: 'bank-account',
        component: () => import(/* webpackChunkName: "bank-account" */ '@/views/pages/user-center/bank-account/bank-account').catch(() => ModuleNotFound),
        meta: {
          title: '银行账户',
          requiredOpenAccount: false,
        }
      },
      // 米账户
      {
        path: 'sdm-info',
        name: 'sdm-info',
        component: () => import(/* webpackChunkName: "sdm-info" */ '@/views/pages/user-center/sdm-info/sdm-info').catch(() => ModuleNotFound),
        meta: {
          title: `${store.state.sdmName}账户`,
          requiredOpenAccount: false,
          showNotice: false, // 是否展示通知
          domNotice: sdmNotice, // 通知文案
          linkNotice: '/user-center/coupon?tabs=receiveCentre' // 通知跳转路由
        }
      },
      {
        path: 'coupon',
        name: 'coupon',
        component: () => import(/* webpackChunkName: "coupon" */ '@/views/pages/user-center/coupon/coupon').catch(() => ModuleNotFound),
        meta: {
          title: '福利活动',
          requiredOpenAccount: false,
        }
      },
      // {
      //   path: 'welfare-activity',
      //   name: 'welfare-activity',
      //   component: () => import(/* webpackChunkName: "welfare-activity" */ '@/views/pages/user-center/welfare-activity/welfare-activity').catch(() => ModuleNotFound),
      //   meta: {
      //     title: '福利活动',
      //     requiredOpenAccount: false,
      //   }
      // },
      // 票据库存
      // {
      //   path: 'draft-stock',
      //   name: 'draft-stock',
      //   component: () => import(/* webpackChunkName: "draft-stock" */ '@/views/pages/draft-stock/draft-stock').catch(() => ModuleNotFound),
      //   meta: {
      //     title: '票据库存',
      //     requiredOpenAccount: true,
      //   }
      // },
      // 商票交易记录
      // {
      //   path: 'commercial-bill-transaction',
      //   name: 'commercial-bill-transaction',
      //   component: () => import(/* webpackChunkName: "commercial-bill-transaction" */ '@/views/pages/user-center/commercial-bill-transaction/commercial-bill-transaction').catch(() => ModuleNotFound),
      //   meta: {
      //     title: '商票交易记录',
      //     requiredOpenAccount: true,
      //   }
      // },
      // 账户渠道对比说明
      {
        path: 'payment-channel-contrast',
        name: 'payment-channel-contrast',
        component: () => import(/* webpackChunkName: "payment-channel-contrast" */ '@/views/pages/user-center/payment-channel-contrast/payment-channel-contrast').catch(() => ModuleNotFound),
        meta: {
          title: '账户对比说明',
          showBackBar: true, // 展示返回按钮
        }
      },
      // 安全中心
      {
        path: 'safe-center',
        name: 'safa-center',
        component: () => import(/* webpackChunkName: "safe-center" */ '@/views/pages/user-center/safe-center').catch(() => ModuleNotFound),
        meta: {
          title: '账户安全',
        }
      },
      // 商票交易权限申请
      // {
      //   path: 'apply-sp-permission',
      //   name: 'apply-sp-permission',
      //   component: () => import(/* webpackChunkName: "apply-sp-permission" */ '@/views/pages/user-center/apply-sp-permission').catch(() => ModuleNotFound),
      //   meta: {
      //     title: '商票交易权限申请',
      //   }
      // },
      // 信息披露
      {
        path: 'leakage-info',
        name: 'leakage-info',
        component: () => import(/* webpackChunkName: "leakage-info" */ '@/views/pages/user-center/leakage-info/leakage-info').catch(() => ModuleNotFound),
        meta: {
          title: '信息披露',
        }
      },
      // 异常准入名单
      // {
      //   path: 'company-blacklist',
      //   name: 'company-blacklist',
      //   component: () => import(/* webpackChunkName: "company-blacklist" */'@/views/pages/user-center/company-blacklist/company-blacklist').catch(() => ModuleNotFound),
      //   meta: {
      //     title: '异常准入名单',
      //   }
      // },
      // // 违反软件安全规则名单
      // {
      //   path: 'violation-list',
      //   name: 'violation-list',
      //   component: () => import(/* webpackChunkName: "violation-list" */'@/views/pages/user-center/violation-list/violation-list').catch(() => ModuleNotFound),
      //   meta: {
      //     title: '违反软件安全规则名单',
      //   }
      // },
      // 通知公告
      {
        path: 'notice',
        name: 'notice',
        component: () => import(/* webpackChunkName: "notice" */ '@/views/pages/user-center/platform-rules/platform-rules').catch(() => ModuleNotFound),
        meta: {
          title: '通知公告',
        }
      },
      // 业务规则
      {
        path: 'platform-rules',
        name: 'rules',
        component: () => import(/* webpackChunkName: "platform-rules" */ '@/views/pages/user-center/platform-rules/platform-rules').catch(() => ModuleNotFound),
        meta: {
          title: '业务规则',
        }
      },
      // 常见问题
      {
        path: 'common-problem',
        name: 'faq',
        component: () => import(/* webpackChunkName: "common-problem" */ '@/views/pages/user-center/platform-rules/platform-rules').catch(() => ModuleNotFound),
        meta: {
          title: '常见问题',
        }
      },
      // 相关下载
      {
        path: 'related-downloads',
        name: 'download',
        component: () => import(/* webpackChunkName: "related-downloads" */ '@/views/pages/user-center/platform-rules/platform-rules').catch(() => ModuleNotFound),
        meta: {
          title: '相关下载',
        }
      },
      // 信息详情
      {
        path: 'news-detail',
        name: 'news-detail',
        component: () => import(/* webpackChunkName: "news-detail" */ '@/views/pages/user-center/platform-rules/detail/detail').catch(() => ModuleNotFound),
        meta: {
          title: '信息详情',
          showBackBar: true // 展示返回按钮
        }
      },
      // 详情
      {
        path: 'ticket-info',
        name: 'ticket-info',
        component: () => import(/* webpackChunkName: "ticket-info" */'@/views/pages/market/components/receive-order-detail/ticket-detail').catch(() => ModuleNotFound),
        meta: {
          title: '订单详情',
          requiredLogin: false,
        },
      },
      // 库存管理
      {
        path: 'stock-manage',
        name: 'stock-manage',
        component: () => import(/* webpackChunkName: "stock-manage" */ '@/views/pages/erp/stock-manage.vue').catch(() => ModuleNotFound),
        meta: {
          title: '库存管理',
        }
      },
      // 财务报表
      {
        path: 'financial-report',
        name: 'financial-report',
        component: () => import(/* webpackChunkName: "financial-report" */ '@/views/pages/erp/financial-report.vue').catch(() => ModuleNotFound),
        meta: {
          title: '财务报表',
        }
      }
    ]
  }
]

setCommonMeta(routerConfigs, commonMeta)

export default routerConfigs
