import ModuleNotFound from '@/views/components/error/module-not-found.vue'
import { setCommonMeta } from '../index'

// 添加全局配置信息
const commonMeta = {
  requiredLogin: true, // 该路由需要登录才可访问
}

const routerConfigs = [
  {
    path: '/fast-radar-act',
    name: 'fast-radar-act',
    component: () => import(/* webpackChunkName: "article-layout" */ '@/views/layouts/activity-layout/activity-layout.vue').catch(() => ModuleNotFound),
    children: [
      // 活动详情
      {
        path: '/fast-radar-act',
        component: () => import(/* webpackChunkName: "marketing-activities" */ '@/views/pages/marketing-activities/activities/fast-radar-act.vue').catch(() => ModuleNotFound),
      },
    ]

  },

  {
    path: '/revenue-details',
    name: 'revenue-details',
    component: () => import(/* webpackChunkName: "article-layout" */ '@/views/layouts/activity-layout/activity-layout.vue').catch(() => ModuleNotFound),
    children: [
      // 收益详情
      {
        path: '/revenue-details',
        component: () => import(/* webpackChunkName: "marketing-activities" */ '@/views/pages/marketing-activities/activities/revenue-details.vue').catch(() => ModuleNotFound),
      },
    ]
  },
  {
    path: '/fast-radar-send',
    name: 'fast-radar-send',
    component: () => import(/* webpackChunkName: "article-layout" */ '@/views/layouts/activity-layout/activity-layout.vue').catch(() => ModuleNotFound),
    children: [
      // 秒贴送好礼
      {
        path: '/fast-radar-send',
        component: () => import(/* webpackChunkName: "marketing-activities" */ '@/views/pages/marketing-activities/activities/fast-radar-send.vue').catch(() => ModuleNotFound),
      },
    ]
  },
  // {
  //   path: '/ticket-info',
  //   name: 'ticket-info',
  //   meta: {
  //     requiredLogin: false,
  //   },
  //   component: () => import(/* webpackChunkName: "marketing-activities" */ '@/views/pages/market/components/receive-order-detail/ticket-detail.vue').catch(() => ModuleNotFound),
  // },
]

setCommonMeta(routerConfigs, commonMeta)

export default routerConfigs
