import { NEWS, HELP, ABOUT } from '@/constants/article'
import ModuleNotFound from '@/views/components/error/module-not-found.vue'
import { setCommonMeta } from '../index'

// 添加全局配置信息
const commonMeta = {
  requiredLogin: true, // 该路由不需要登录也可访问
}

const routerConfigs = [
  // 新闻动态
  {
    path: '/news',
    name: 'news',
    redirect: NEWS.ANNOUNCEMENT.path,
    component: () => import(/* webpackChunkName: "user-center-layout" */ '@/views/layouts/user-center-layout/user-center-layout.vue').catch(() => ModuleNotFound),
    children: [
      // 新闻动态下的列表页面
      {
        path: '/news/:type',
        component: () => import(/* webpackChunkName: "article-news" */ '@/views/pages/article/news/news.vue').catch(() => ModuleNotFound),
        meta: {
          // 诚信企业表彰榜需登录
          // requiredLogin: route => route.params.type === NEWS.COMMEND.name
        }
      },
      // 新闻动态下的详情页面
      {
        path: '/news/:type/:id',
        component: () => import(/* webpackChunkName: "article-detail" */ '@/views/pages/article/detail/detail.vue').catch(() => ModuleNotFound),
        meta: {
          // 诚信企业表彰榜详情需登录
          // requiredLogin: route => route.params.type === NEWS.COMMEND.name
        }
      },
    ]
  },
  // 关于深度
  {
    path: '/about',
    name: 'about',
    redirect: ABOUT.INTRO.path,
    component: () => import(/* webpackChunkName: "user-center-layout" */ '@/views/layouts/user-center-layout/user-center-layout.vue').catch(() => ModuleNotFound),
    children: [
      // 新闻动态下的列表页面
      {
        path: '/about/:type',
        component: () => import(/* webpackChunkName: "article-detail" */ '@/views/pages/article/detail/detail.vue').catch(() => ModuleNotFound),
      },
    ]
  },
  // 帮助中心
  {
    path: '/help',
    name: 'help',
    redirect: HELP.GUIDE.path,
    component: () => import(/* webpackChunkName: "user-center-layout" */ '@/views/layouts/user-center-layout/user-center-layout.vue').catch(() => ModuleNotFound),
    children: [
      // 风险承兑人列表
      {
        path: HELP.RISK_ACCEPTOR.path,
        component: () => import(/* webpackChunkName: "risk-acceptor" */ '@/views/pages/article/help/risk-acceptor/risk-acceptor.vue').catch(() => ModuleNotFound),
        meta: {
          requiredLogin: true,
          title: '信息披露'
        }
      },
      // 常见问题列表
      {
        path: HELP.FAQ.path,
        component: () => import(/* webpackChunkName: "faq" */ '@/views/pages/article/help/faq/faq.vue').catch(() => ModuleNotFound),
        meta: {
          title: '常见问题'
        }
      },
      // 帮助中心下的其他列表页面
      {
        path: '/help/:type',
        component: () => import(/* webpackChunkName: "article-news" */ '@/views/pages/article/news/news.vue').catch(() => ModuleNotFound),
      },
      // 帮助中心下的详情页面
      {
        path: '/help/:type/:id',
        component: () => import(/* webpackChunkName: "article-detail" */ '@/views/pages/article/detail/detail.vue').catch(() => ModuleNotFound),
      },
    ]
  },
]

setCommonMeta(routerConfigs, commonMeta)

export default routerConfigs
