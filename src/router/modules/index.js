import ModuleNotFound from '@/views/components/error/module-not-found.vue'
const mainStyle = {
  padding: 0,
  margin: 0,
  width: '100%'
}
const routerConfigs = [
  // {
  //   path: '/',
  //   name: 'index',
  //   component: () => import(/* webpackChunkName: "page-index" */ '@/views/pages/welcome/welcome.vue').catch(() => ModuleNotFound),
  //   meta: {
  //     // 布局主内容样式
  //     mainContentStyle: mainStyle
  //   }
  // },
  {
    path: '/radar',
    name: 'radar',
    component: () => import(/* webpackChunkName: "page-radar" */ '@/views/pages/index/hot-product.vue').catch(() => ModuleNotFound),
    meta: {
      mainContentStyle: mainStyle
    }
  },
  {
    path: '/fast',
    name: 'fast',
    component: () => import(/* webpackChunkName: "page-fast" */ '@/views/pages/index/hot-product.vue').catch(() => ModuleNotFound),
    meta: {
      mainContentStyle: mainStyle
    }
  }
]

export default routerConfigs
