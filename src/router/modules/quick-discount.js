import ModuleNotFound from '@/views/components/error/module-not-found.vue'
import { setCommonMeta } from '../index'

// 添加全局配置信息
const commonMeta = {
  requiredLogin: true, // 该路由需要登录才可访问
}

const routerConfigs = [
  // 秒贴
  {
    path: '/quick-discount',
    name: 'quickDiscount',
    component: () => import(/* webpackChunkName: "quick-discount" */ '@/views/pages/quick-discount/quick-discount.vue').catch(() => ModuleNotFound),
    meta: {
      title: '银企秒贴',
      // 布局主内容样式
      mainContentStyle: {
        padding: 0,
        margin: 0,
        width: '100%'
      }
    }
  },
]

setCommonMeta(routerConfigs, commonMeta)

export default routerConfigs
