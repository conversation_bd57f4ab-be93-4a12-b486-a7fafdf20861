import ModuleNotFound from '@/views/components/error/module-not-found.vue'
import { setCommonMeta } from '../index'

// 添加全局配置信息
const commonMeta = {
  requiredLogin: true, // 该路由需要登录才可访问
  requiredCloseMarket: true, // 该路由需要获取闭市信息
  requiredRadarLimit: true, // 该路由需要获取违约限制自动信息
  requiredCreditRemind: true, // 需要获取信用分提醒信息
}

const routerConfigs = [
  {
    path: '/market',
    name: 'market',
    component: () => import(/* webpackChunkName: "market" */ '@/views/pages/market/market.vue').catch(() => ModuleNotFound),
    meta: {
      title: '服务大厅'
    }
  },
]

setCommonMeta(routerConfigs, commonMeta)

export default routerConfigs
