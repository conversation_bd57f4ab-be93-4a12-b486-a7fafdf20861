// 没有分类的普通页面理由 如：识票助手宣传页 渠道推广页
import ModuleNotFound from '@/views/components/error/module-not-found.vue'
import { setCommonMeta } from '../index'

// 添加全局配置信息
const commonMeta = {
  requiredLogin: false, // 该路由需要登录才可访问
}
const mainStyle = {
  padding: 0,
  margin: 0,
  width: '100%'
}
const routerConfigs = [
  { // 识票助手宣传页
    path: '/publicity-signer-app',
    name: 'publicitySignerApp',
    component: () => import(/* webpackChunkName: "publicity-signer-app" */ '@/views/pages/publicity-signer-app/publicity-signer-app.vue').catch(() => ModuleNotFound),
    meta: {
      // 布局主内容样式
      mainContentStyle: mainStyle
    }
  },
  { // 经济+
    path: '/broker-plus',
    name: 'brokerPlus',
    component: () => import(/* webpackChunkName: "broker-plus" */ '@/views/pages/broker-plus/broker-plus.vue').catch(() => ModuleNotFound),
    meta: {
      // 布局主内容样式
      mainContentStyle: mainStyle
    }
  },
  { // 经济+二级页面
    path: '/broker-plus/introduce',
    name: 'introduce',
    component: () => import(/* webpackChunkName: "broker-plus" */ '@/views/pages/broker-plus/introduce/introduce.vue').catch(() => ModuleNotFound),
    meta: {
      // 布局主内容样式
      mainContentStyle: mainStyle
    },
  },
  { // 渠道推广页
    path: '/channel-compliance',
    name: 'channelCompliance',
    component: () => import(/* webpackChunkName: "channel-compliance" */ '@/views/pages/channel-compliance/channel-compliance.vue').catch(() => ModuleNotFound),
    meta: {
      // 布局主内容样式
      mainContentStyle: mainStyle
    },
  },
  { // 支付成功回调页面
    path: '/pay-success',
    name: 'paySuccess',
    component: () => import(/* webpackChunkName: "pay-success" */ '@/views/pages/pay-success/pay-success.vue').catch(() => ModuleNotFound),
    meta: {
      mainContentStyle: mainStyle
    }
  }
]

setCommonMeta(routerConfigs, commonMeta)

export default routerConfigs
