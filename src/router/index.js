/* eslint-disable array-bracket-newline */
/* eslint-disable max-statements-per-line */
import Vue from 'vue'
import VueRouter from 'vue-router'
import store from '@/store'
import MainLayout from '@/views/layouts/main-layout/main-layout'
import login from '@/views/pages/login/login'
import ModuleNotFound from '@/views/components/error/module-not-found.vue'
import { WEB_NAME, DISCERN_NAME } from '@/constant'

// import indexRouterConfigs from './modules/index'
import userCenterRouterConfigs from './modules/user-center'
import electronicAccountRouter from './modules/electronic-account-center'

// import serviceHallRouterConfigs from './modules/market'
// import quickDiscountHallRouterConfigs from './modules/quick-discount'
// import marketingActivitiesHallRouterConfigs from './modules/marketing-activities'
// import articleRouterConfigs from './modules/article'
// import commonRouterConfigs from './modules/common'
// import activitiesConfigs from './modules/activities'
import userObj from '@/utils/user.js' // 用户对象操作
// import { BAN_STATUS } from '@/constants/open-account'

// 解决路由重复报错bug，还是各自try catch比较好
const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location, onResolve, onReject) {
  try {
    if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject)
    return originalPush.call(this, location).catch(err => {
      // eslint-disable-next-line no-console
      console.log(`@Method VueRouter.prototype.push: ${location.path} ${err.name}`)
    })
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log('VueRouter error :>> ', error)
  }
}
const originalReplace = VueRouter.prototype.replace
VueRouter.prototype.replace = function replace(location, onResolve, onReject) {
  try {
    if (onResolve || onReject) return originalReplace.call(this, location, onResolve, onReject)
    return originalReplace.call(this, location).catch(err => {
      // eslint-disable-next-line no-console
      console.log(`@Method VueRouter.prototype.replace: ${location.path} ${err.message}`)
    })
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log('VueRouter error :>> ', error)
  }
}

Vue.use(VueRouter)

const children = [
  // ...indexRouterConfigs, // 首页
  // ...serviceHallRouterConfigs, // 服务大厅
  // ...commonRouterConfigs, //  宣传类通用页面
  // ...marketingActivitiesHallRouterConfigs, // 营销活动
  // ...quickDiscountHallRouterConfigs // 秒贴
]

const routes = [
  {
    path: '/',
    component: MainLayout,
    children,
    meta: {
      requiredLogin: true
    }
  },
  {
    path: '/login',
    name: 'login',
    component: () => import(/* webpackChunkName: "page-login" */ '@/views/pages/login/login.vue').catch(() => ModuleNotFound),
  },
  ...electronicAccountRouter,
  ...userCenterRouterConfigs, // 用户中心
  {
    path: '/recognize-helper',
    component: () => import(/* webpackChunkName: "recognize-helper" */ '@/views/pages/recognize-helper/recognize-helper.vue').catch(() => ModuleNotFound),
    meta: {
      title: DISCERN_NAME
    }
  },
  // ...articleRouterConfigs, // 文章相关
  // ...activitiesConfigs, // 活动详情
  {
    path: '*',
    redirect: '/',
  },
  {
    path: '/chatai',
    name: 'chatai',
    component: () => import(/* webpackChunkName: "chatai" */ '@/views/pages/ai/ai-dialog.vue').catch(() => ModuleNotFound),
    meta: {
    }
  },
]

if (process.env.VUE_APP_API_ENV === 'development') {
  routes[0].children.push(...[
    {
      path: '/test',
      component: () => import(/* webpackChunkName: "test" */ '@/views/pages/test/test.vue'),
    },
    {
      path: '/test-icon',
      component: () => import(/* webpackChunkName: "test-icon" */ '@/views/pages/test/test-icon.vue'),
    }
  ])
}

const routerInstance = new VueRouter({
  mode: 'history',
  base: '/',
  routes,
})

// 监听路由错误重定向
routerInstance.onError(error => {
  const pattern = /Loading chunk (\d)+ failed/g
  const isChunkLoadFailed = error.message.match(pattern)
  const targetPath = routerInstance.history.pending.fullPath
  if (isChunkLoadFailed) {
    // eslint-disable-next-line no-console
    console.log('路由错误重定向', targetPath)
    routerInstance.replace(targetPath)
  }
})

/**
 * 为路由配置对象设置全局参数
 * @param {object[]} routerObj 路由对象数组
 * @param {object} commonMeta 需要统一设置的 meta
 * @param {boolean} commonMeta.requiredLogin 访问当前页面是否需要登录
 * @param {boolean} commonMeta.requiredOpenAccount 访问当前页面是否需要开户权限
 * @param {object} commonMeta.mainContentStyle 布局主内容样式
 * @param {boolean} commonMeta.keepAlive 页面是否缓存 默认false
 * @param {string[]} ignore 需要忽略的路由名称
 * @param {boolean} commonMeta.requiredChannel 访问当前页面是否需要有渠道
 */
export function setCommonMeta(routerObj, commonMeta, ignore) {
  routerObj.forEach(item => {
    const isIgnored = ignore && ignore.length && item.name && ignore.includes(item.name)
    if (!isIgnored) {
      item.meta = Object.assign({}, commonMeta, item.meta)
    }
    if (item.children && item.children.length > 0) {
      setCommonMeta(item.children, commonMeta, ignore)
    }
  })
}

// 检查某个路由是否需要登录才能访问 默认是要登陆可见的
export const isRequiredLogin = route => {
  const { requiredLogin } = route.meta || true
  return typeof requiredLogin === 'function' ? requiredLogin(route) : requiredLogin
}

// 全局路由前置守卫
routerInstance.beforeEach(async(to, from, next) => {
  // 设置返回顶部
  document.documentElement.scrollTop = 0
  const { isLogined } = store.state.user
  // 如果待跳转的页面要求登录且当前用户未登录，则打开登录弹窗
  if (!isLogined && isRequiredLogin(to)) {
    login.init('login', to)
  }
  // 已登陆输入域名直接到欢迎页面
  if (to.path === '/') {
    // 新客户端直接到我要找票页面
    return next('/user-center')
  }
  // 大厅最多可以选中5个tab 直接访问大厅的路由 重定向
  if (to.path === '/user-center/cute-hand') {
    const fromPath = from.path
    const { marketWinList, marketWinLastStr } = store.state.market
    let marketWin = marketWinList[0] || marketWinLastStr
    // 如果来源页不是大厅 直接打开大厅上一次的大厅页面
    if (!fromPath.includes('/user-center/cute-hand')) {
      marketWin = marketWinLastStr
    }
    if (marketWin) {
      let path = `${to.path}/${marketWin}`
      return next(path)
    }
    return
  }

  try {
    // 登录状态下判断是跳转到需要校验闭市路由，请求闭市信息
    if (isLogined && to.meta.requiredCloseMarket) {
      await store.dispatch('common/getCloseMarket')
    }
    // 登录状态下判断是不是需要开户才能访问，没有开户就弹窗开户
    if (isLogined && to.meta.requiredOpenAccount && !store.state.user.isOpenAccount) {
      // 获取是否已开户
      await store.dispatch('user/getIsOpenAccount')
      if (!store.state.user.isOpenAccount) {
        userObj.checkAmountFailType()
        return next(from)
      }
    }
    // 登录状态下判断是跳转到需要校验自动限制路由，请求自动限制信息
    // if (isLogined && to.meta.requiredRadarLimit) {
    //   await store.dispatch('common/getRadarLimit')
    // }
    // 登录状态下判断是跳转到需要校验极速交易限制路由，请求极速限制信息
    // if (isLogined && to.meta.requiredFastLimit) {
    //   await store.dispatch('common/getFastLimit')
    // }
    // 登录状态下判断是跳转到需要信用分提醒路由，请求信用分提醒信息
    if (isLogined && to.meta.requiredCreditRemind) {
      await store.dispatch('user/getShowCreditRemind')
    }
  } catch (e) {
    return next('/')
  }

  // 登录实名信用分验证我弄成后 如果是直接访问重定向后的路由  删除store中对应元素
  if (to.path.includes('/user-center/cute-hand/')) {
    let marketWinPath = to.path.replace('/user-center/cute-hand/', '')
    store.commit('market/setEelMarketWinList', marketWinPath)
  }

  next()
})

// 拦截每一个路由跳转
routerInstance.afterEach(route => {
  // 设置页面标题
  document.title = route.meta.title || WEB_NAME
})

export const router = routerInstance

export default router
