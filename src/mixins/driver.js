import Driver from 'driver.js'
import 'driver.js/src/driver.scss'
import { formatTime } from '@/common/js/date'
import Storage from '@/common/js/storage' // 本地缓存对象
import { DRIVER_VISIBLE } from '@/constant-storage'
import { colorprimary } from '@/common/scss/export.scss' // 引入样式变量
import settingtApi from '@/apis/setting'
import { ZHI_FU_ZHONG_BANG_PLUS_GUIDE_URL } from '@/constants/oss-files-url' // 智付邦+操作指引
const driverMixins = {
  data() {
    return {
      colorprimary,
      driver: null,
      isShowDriver: true
    }
  },

  beforeDestroy() {
    if (this.driver) {
      this.driver.reset()
    }
  },
  created() {
    window.viewGuide = this.viewGuide
  },
  watch: {
    '$store.state.user.paymentAccountList': {
      async handler(val) {
        if (!Array.isArray(val) || val.length === 0) return
        // 获取配置的是否显示引导 tradingPopWin 0显示引导 1不显示引导
        if (this.getLightGuide()) {
          let res = await settingtApi.tradingHallConfig()
          if (!res.tradingPopWin) {
            this.$nextTick(() => {
              setTimeout(() => {
                this.lend()
              })
            })
          }
        }
      }
    }
  },

  methods: {
    viewGuide() {
      window.open(ZHI_FU_ZHONG_BANG_PLUS_GUIDE_URL)
    },
    // 创建引导
    lend() {
      this.setLightGuide()
      // 智付E+
      // const htmlStr = '<div>'
      //                 + '<div style="padding-bottom:10px;"><div style="font-size: 16px;font-weight: bold;">什么是智付E+</div><div>智付E+是采用企业网银延时付款模式的支付渠道，有效保障账户资金安全</div></div> '
      //                 + '<div style="padding-bottom:10px;"><div style="font-size: 16px;font-weight: bold;">智付E+与智付E的区别</div><div>使用智付E+须绑定亿联银行一般户，在企业网银完成订单支付、票据签收、资金解冻</div></div>'
      //                 + '<div style="padding-bottom:10px;"><div style="font-size: 16px;font-weight: bold;">智付E+怎么开通</div><div>按页面指引账户实名认证成功后自动开通智付E+支付渠道</div></div>'
      //                 + '<div style="padding-bottom:10px;"><div style="font-size: 16px;font-weight: bold;">智付E+怎么使用</div><div>详情请查阅<a onClick="viewGuide()" style="color:#0076F6;cursor: pointer;">《智付E+操作指引》</a></div></div>'
      //                 + '</div>'

      // 智付邦+
      const htmlStr = '<div>'
        + '<div style="padding-bottom:10px;"><div style="font-size: 16px;font-weight: bold;">什么是智付邦+</div><div>智付邦+是采用企业网银担保支付模式的支付渠道，有效保障账户资金安全</div></div> '
        + '<div style="padding-bottom:10px;"><div style="font-size: 16px;font-weight: bold;">智付邦+与智付邦的区别</div><div>使用智付邦+须绑定智付邦+一般户，在企业网银完成订单支付、票据签收</div></div>'
        + '<div style="padding-bottom:10px;"><div style="font-size: 16px;font-weight: bold;">智付邦+怎么开通</div><div>按页面指引账户实名认证成功后自动开通智付邦+支付渠道</div></div>'
        + '<div style="padding-bottom:10px;"><div style="font-size: 16px;font-weight: bold;">智付邦+怎么使用</div><div>详情请查阅<a onClick="viewGuide()" style="color:#0076F6;cursor: pointer;">《智付邦+操作指引》</a></div></div>'
        + '</div>'
      let steps = [
        // 智付E+
        // {
        //   element: '#zfEplusDriver',
        //   popover: {
        //     className: 'zfEplusDriver-popover-cls',
        //     title: '智付E+支付渠道上线啦！',
        //     description: htmlStr,
        //     position: 'right',
        //     closeBtnText: '跳过',
        //   }
        // },
        // 智付邦+
        {
          element: '#zfBplusDriver',
          popover: {
            className: 'zfEplusDriver-popover-cls',
            title: '智付邦+支付渠道上线啦！',
            description: htmlStr,
            position: 'right',
            closeBtnText: '跳过',
          }
        },
      ]
      const driverInst = new Driver({
        opacity: 0.6,
        allowClose: false, // 禁止点击阴影关闭
        doneBtnText: '跳过',
        onHighlightStarted: () => {
          this.preventScroll(true)
          this.isShowDriver = true
        },
        onHighlighted: inst => {
          // 众邦引导窗口个性化配置
          setTimeout(() => {
            const { node } = inst
            const { closeBtnNode, prevBtnNode } = inst.popover
            // closeBtnNode.style.display = 'none'
            prevBtnNode.style.display = 'none'
            node.style.zIndex = '999999'
            closeBtnNode.style.fontSize = '14px'
            closeBtnNode.style.textShadow = 'none'
            closeBtnNode.style.fontWeight = '500'
            closeBtnNode.style.color = '#fff'
            closeBtnNode.style.padding = '6px 16px'
            closeBtnNode.style.background = this.colorprimary
          }, 0)
        },
        onDeselected: inst => {
          this.preventScroll(false)
          const { node } = inst
          node.style.zIndex = '9'
        },
      })
      driverInst.defineSteps(steps)
      driverInst.start()
      this.driver = driverInst
    },

    // 阻止鼠标及键盘控制的滚动条滑动
    preventScroll(flag) {
      document.body.style.maxHeight = flag ? '100vh' : 'auto'
      document.body.style.overflow = flag ? 'hidden' : 'auto'
    },

    getLightGuide() {
      let flag = false // 默认不显示
      let lightGuide = Storage.get(DRIVER_VISIBLE)
      let currentDate = formatTime(Date.now(), 'YYYY-MM-DD')
      if (lightGuide !== currentDate) {
        flag = true
      }
      return flag
    },

    setLightGuide() {
      let lightGuide = Storage.get(DRIVER_VISIBLE)
      let currentDate = formatTime(Date.now(), 'YYYY-MM-DD')
      if (lightGuide !== currentDate) {
        Storage.set(DRIVER_VISIBLE, currentDate)
      }
    }
  },
}

export default driverMixins
