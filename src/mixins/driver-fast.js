import Driver from 'driver.js'
import 'driver.js/src/driver.scss'
import { formatTime } from '@/common/js/date'
import Storage from '@/common/js/storage' // 本地缓存对象
import { FAST_DRIVER_VISIBLE_NEW } from '@/constant-storage'
import { colorprimary } from '@/common/scss/export.scss' // 引入样式变量

const driverMixins = {
  data() {
    return {
      driver: null,
      isShowDriver: false
    }
  },

  beforeDestroy() {
    if (this.driver) {
      this.driver.reset()
    }
  },

  methods: {
    // 创建引导
    lend() {
      let steps = [
        {
          element: document.querySelector('.fastChangeDriver'),
          popover: {
            className: 'fast-popover-cls',
            title: '非极速订单点击可以快捷转极速啦~',
            position: 'bottom',
            closeBtnText: '关闭',
            doneBtnText: '不再提示',
          }
        }
      ]
      const driverInst = new Driver({
        opacity: 0.6,
        allowClose: false, // 禁止点击阴影关闭
        onHighlightStarted: () => {
          this.preventScroll(true)
          this.isShowDriver = true
        },
        onHighlighted: inst => {
          // 众邦引导窗口个性化配置
          setTimeout(() => {
            const { closeBtnNode, nextBtnNode } = inst.popover
            closeBtnNode.classList.remove('driver-close-only-btn')
            closeBtnNode.style.fontSize = '14px'
            closeBtnNode.style.textShadow = 'none'
            closeBtnNode.style.fontWeight = '500'
            closeBtnNode.style.color = '#fff'
            closeBtnNode.style.padding = '6px 16px'
            closeBtnNode.style.background = colorprimary
            closeBtnNode.style.borderColor = colorprimary
            closeBtnNode.style.position = ''

            nextBtnNode.style.display = 'inline-block'
            nextBtnNode.style.color = '#999999'
            nextBtnNode.style.border = 'none'
            nextBtnNode.style.background = 'none'
            nextBtnNode.style.borderColor = 'none'
            nextBtnNode.style.fontSize = '14px'
            nextBtnNode.style.textShadow = 'none'
            nextBtnNode.style.padding = '0'
            nextBtnNode.style.fontWeight = '500'
            nextBtnNode.style.borderBottom = '1px solid #999999'
          }, 0)
        },
        onDeselected: inst => {
          this.preventScroll(false)
          const { node } = inst
          node.style.zIndex = '9'
          this.setLightGuide()
        },
        onNext: () => {
          this.setLightGuide()
        },
      })
      driverInst.defineSteps(steps)
      driverInst.start()
      this.driver = driverInst
    },

    // 阻止鼠标及键盘控制的滚动条滑动
    preventScroll(flag) {
      document.body.style.maxHeight = flag ? '100vh' : 'auto'
      document.body.style.overflow = flag ? 'hidden' : 'auto'
    },
    // 设置当天关闭
    setLightGuide() {
      let lightGuide = Storage.get(FAST_DRIVER_VISIBLE_NEW)
      let currentDate = formatTime(Date.now(), 'YYYY-MM-DD')
      // 当前值不等于 永久关闭，就设置当天的时间
      if (lightGuide !== 'isColse' && lightGuide !== currentDate) {
        Storage.set(FAST_DRIVER_VISIBLE_NEW, currentDate)
      } else {
        // 设置永久关闭
        Storage.set(FAST_DRIVER_VISIBLE_NEW, 'isColse')
      }
    }
  },
}

export default driverMixins
