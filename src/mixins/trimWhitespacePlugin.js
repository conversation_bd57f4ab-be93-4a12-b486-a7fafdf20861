// input去除两边空格插件
export default {
  install(Vue) {
    Vue.mixin({
      updated() {
        this.$nextTick().then(() => {
          if (!this.$el || !this.$el.querySelectorAll) return
          // 获取当前组件所有el-input__inner节点
          const inputs = this.$el.querySelectorAll('.el-input__inner')
          for (let i = 0; i < inputs.length; i++) {
            inputs[i].addEventListener('blur', () => {
              // 去除空格
              inputs[i].value = inputs[i].value && inputs[i].value.trim()
              // 触发输入框的 input 事件 更新数据
              inputs[i].dispatchEvent(new Event('input', { bubbles: true }))
            })
          }
        })
      }
    })
  }
}
