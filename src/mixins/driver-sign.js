import Driver from 'driver.js'
import 'driver.js/src/driver.scss'
import Storage from '@/common/js/storage' // 本地缓存对象
import { SIGN_DRIVER_VISIBLE } from '@/constant-storage'
import { colorprimary } from '@/common/scss/export.scss' // 引入样式变量

const driverMixins = {
  data() {
    return {
      driver: null,
      isShowDriver: false
    }
  },

  beforeDestroy() {
    if (this.driver) {
      this.driver.reset()
    }
  },

  methods: {
    // 创建引导
    singLend() {
      if (!document.querySelector('.signChangeDriver')) {
        return
      }
      const htmlStr = '<div>'
      + '<div style="padding-bottom:6px;">点击立即签约，绑定智付邦+银行一般户，即可使用该渠道接单!</div> '
      + '<div >尊享服务费免费、网银快捷支付，快来体验吧!</div>'
      + '</div>'
      let steps = [
        {
          element: document.querySelector('.signChangeDriver'),
          popover: {
            className: 'sign-popover-cls',
            title: '智付邦+支付渠道上线啦!',
            description: htmlStr,
            position: 'top',
            closeBtnText: '跳过',
          }
        }
      ]
      const driverInst = new Driver({
        opacity: 0.6,
        allowClose: false, // 禁止点击阴影关闭
        onHighlightStarted: () => {
          this.preventScroll(true)
          this.isShowDriver = true
        },
        onHighlighted: inst => {
          // 众邦引导窗口个性化配置
          setTimeout(() => {
            const { closeBtnNode } = inst.popover
            closeBtnNode.style.fontSize = '14px'
            closeBtnNode.style.textShadow = 'none'
            closeBtnNode.style.fontWeight = '500'
            closeBtnNode.style.color = '#fff'
            closeBtnNode.style.padding = '6px 16px'
            closeBtnNode.style.background = colorprimary
            closeBtnNode.style.borderColor = colorprimary
            closeBtnNode.style.position = ''
          }, 0)
        },
        onDeselected: inst => {
          this.preventScroll(false)
          const { node } = inst
          node.style.zIndex = '9'
          this.setLightGuide()
        },
        onNext: () => {
          this.setLightGuide()
        },
      })
      driverInst.defineSteps(steps)
      driverInst.start()
      this.driver = driverInst
    },

    // 阻止鼠标及键盘控制的滚动条滑动
    preventScroll(flag) {
      document.body.style.maxHeight = flag ? '100vh' : 'auto'
      document.body.style.overflow = flag ? 'hidden' : 'auto'
    },
    // 设置当天关闭
    setLightGuide() {
      Storage.set(SIGN_DRIVER_VISIBLE, true)
    }
  },
}

export default driverMixins
