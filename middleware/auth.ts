/**
 * 认证中间件
 * 检查用户是否已登录，未登录则跳转到登录页
 */
export default defineNuxtRouteMiddleware((to) => {
  // 获取 token
  const token = useCookie('access_token')
  
  // 如果没有 token，跳转到登录页
  if (!token.value) {
    // 保存当前要访问的页面，登录后跳转回来
    const redirectPath = to.fullPath
    
    // 如果是服务端渲染，使用 navigateTo
    if (process.server) {
      return navigateTo(`/login?redirect=${encodeURIComponent(redirectPath)}`)
    }
    
    // 客户端使用 router push
    return navigateTo({
      path: '/login',
      query: { redirect: redirectPath }
    })
  }
  
  // 可以在这里添加 token 验证逻辑
  // 比如检查 token 是否过期等
})
