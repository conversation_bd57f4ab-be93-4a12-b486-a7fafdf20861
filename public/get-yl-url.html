<!DOCTYPE html>
<html lang="zh-cmn-Hans">
  <!-- erp获取亿联银行网关页面url的中间页 兼容IE 弹框打开方式  新页面打开直接用toGetwayPage.js -->
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" />
    <meta http-equiv="X-UA-Compatible" content="IE=9;IE=8;IE=7;ie=edge">
    <title></title>
    <link rel="icon" href="" />
    <link rel="stylesheet" href="/css/common.css">
  </head>
  <body id="body">
    <!-- loading -->
    <div id="loading-container">
      <div id="app-loading">
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>
    <div id="app">
      <div class="toast" id="my-toast"></div>
    </div>
    <script src="/js/jquery.1.12.js"></script>
    <script src="/js/util.js"></script>
    <script>
      // 环境变量的集合
      var baseUrlList = {
        development: "https://shendu-dev3.sdpjw.cn", // 开发环境
        staging: "https://staging-api.lianwuxian.cn", // 预发环境
        production: "https://api.lianwuxian.cn", // 生产环境 
      };

      var query = getUrlCode();
      var env = query.env
      var appType = query.appType
      // 设置网站图标
      let link = document.querySelector('link[rel*="icon"]')
      link.href = appType === 'hn' ? '/favicon.ico' : '/favicon1.ico'
      // m默认是经纪商服务
      var api = '/api/broker/bank/yilian/getTokenAndUrl'
      if(appType === 'hn'){ // 如果是承让erp调用qiqilian服务
        api = '/api/platform/bank/yilian/getTokenAndUrl'
      }
      var baseUrl = baseUrlList[env || "development"] + api
      
      // 处理参数
      var params = {}
      for (const key in query) {
        if(key !== 'env' && key !== "T") { // 过滤时间戳和环境变量参数
          params[key] = query[key]
        }
      }


      $.ajax({
        url: baseUrl,
        timeout: 10 * 1000, // 单位毫秒，超时时间
        type:"POST",
        dataType:"JSON",
        data: JSON.stringify(params),
        beforeSend:function (request) {
          request.setRequestHeader("accessToken",JSON.parse(localStorage.getItem('TOKEN')));
          request.setRequestHeader("Content-Type","application/json;charset=utf-8");
        },
        success:function(data){
          console.log("hshshshs", data);
          if(data.code === 200 && data.data){
            var res = data.data
            if (res.url) {
              // 添加域名检验字段
              // var paramsMap = res.body || {}
              createFormToYl({
                url: res.url,
                target: '_self',
                params: res.body
              })
            } else {
              Toast(data.msg)
            }
          }else{
            Toast(data.msg, function(){
              // 通知页面关闭弹框
              window.parent.iframeCallback()
            })
          }
        }
      });

      // createFormToYl({
      //   url: 'https://www.baidu.com',
      //   target: '_self',
      //   params: {
      //     a: 1,
      //     b: 2,
      //     c: 3,
      //     d: 4,
      //   }
      // })


    </script>
  </body>
</html>
