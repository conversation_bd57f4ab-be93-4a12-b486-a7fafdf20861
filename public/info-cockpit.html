<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <title></title>
  <meta name="keywords" content="">
  <meta name="description" content="">
  <link rel="icon">
  <title>信息驾驶舱</title>


  <style>
    :root {
      --header-bg: #202B2A;
      --text-light: #FFFFFF;
      --hover-blue: #1890FF;
      --danger-red: #FF4444;
      --transition-speed: 0.3s;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    .main-container {
      display: flex;
      flex-direction: column;
      height: 100vh;
      background: #F0F2F5;
    }

    .header {
      height: 60px;
      background: var(--header-bg);
      padding: 0 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      position: relative;
      z-index: 100;
      -webkit-app-region: drag;
    }

    .logo {
      color: var(--text-light);
      font-size: 20px;
      font-weight: 500;
    }

    .window-controls {
      display: flex;
      gap: 15px;
      align-items: center;
      -webkit-app-region: no-drag;
    }

    .control-btn {
      cursor: pointer;
      padding: 8px;
      border-radius: 4px;
      display: flex;
      align-items: center;
    }

    .control-btn img {
      width: 20px;
      height: 20px;
    }

    .control-btn:hover {
      background: rgba(255, 255, 255, 0.15);
      transform: scale(1.1);
    }

    .close-btn:hover {
      background: var(--danger-red) !important;
    }

    .content-container {
      flex: 1;
      position: relative;
      overflow: hidden;
    }

    .loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #666;
      font-size: 18px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;
    }

    .retry-btn {
      padding: 8px 16px;
      background: var(--hover-blue);
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }

    .responsive-iframe {
      width: 100%;
      height: 100%;
      border: none;
      background: white;
      transition: opacity 0.5s ease;
    }
  </style>
</head>

<body>
  <div class="main-container">
    <header class="header">
      <div class="logo">信息驾驶舱</div>
      <div class="window-controls">
        <div class="control-btn minimize-btn" title="最小化">
          <img src="https://static-file.duijie.org.cn/frontend-file/frontend/imgs/welcom/minus1.png">
        </div>
        <div class="control-btn maximize-btn" title="最大化">
          <img src="https://static-file.duijie.org.cn/frontend-file/frontend/imgs/welcom/window-maximize.png">
        </div>
        <div class="control-btn close-btn" title="关闭">
          <img src="https://static-file.duijie.org.cn/frontend-file/frontend/imgs/welcom/close.png">
        </div>
      </div>
    </header>

    <div class="content-container">
      <div class="loading">大屏加载中...</div>
      <iframe class="responsive-iframe" title="信息驾驶舱" sandbox="allow-scripts allow-same-origin allow-popups"
        loading="lazy" referrerpolicy="strict-origin-when-cross-origin" style="visibility: hidden;"></iframe>
    </div>
  </div>

  <script src="/js/jquery.1.12.js"></script>
  <script src="/js/util.js"></script>
  <script>
    (function () {
      // 环境变量的集合
      var baseUrlList = {
        development: "https://screen.yiyuan.red", // 开发环境
        staging: "https://screen.yiyuan.red", // 预发环境
        production: "https://scr.yiyuan.red", // 生产环境 
      };

      var query = getUrlCode();
      var env = query.env
      // 获取信息驾驶舱大屏地址
      var baseUrl = baseUrlList[env || "development"]


      const utils = {
        getQueryParam(name) {
          try {
            return new URLSearchParams(window.location.search).get(name);
          } catch {
            return null;
          }
        },

        safeRequire(module) {
          try {
            return window.require(module);
          } catch {
            return null;
          }
        }
      };


      // 操作栏控制模块
      const WindowControl = (() => {
        let electron;
        let isMaximized = false;

        const init = () => {
          electron = utils.safeRequire('electron');
          // 不是客户端 不展示 操作栏按钮
          if (!electron) {
            document.querySelector('.window-controls').remove();
            return;
          }

          bindEvents();
          setupStateListener();
        };

        const setupAutoClose = () => {
          let timer;
          const resetTimer = () => {
            clearTimeout(timer);
            timer = setTimeout(() => {
              electron.ipcRenderer.send('CLOSE');
            }, 5 * 60 * 1000);
          };

          resetTimer();
        };

        const bindEvents = () => {
          // 绑定事件
          document.querySelector('.window-controls').addEventListener('click', (e) => {
            const btn = e.target.closest('.control-btn');
            if (!btn) return;

            const type = btn.classList.contains('minimize-btn') ? 'MINIMIZE' :
              btn.classList.contains('maximize-btn') ? 'MAXIMIZE' :
                'CLOSE';

            handleControl(type);
          });
        };
        // 点击触发的方法
        const handleControl = (type) => {
          if (!electron) return;

          switch (type) {
            case 'MINIMIZE':
              electron.ipcRenderer.send('MINIMIZE');
              break;
            case 'MAXIMIZE':
              isMaximized = !isMaximized;
              electron.ipcRenderer.send('MAXIMIZE');
              updateMaximizeUI();
              break;
            case 'CLOSE':
              electron.ipcRenderer.send('CLOSE');
              break;
          }
        };

        const setupStateListener = () => {
          electron.ipcRenderer.on('WINDOW_STATE', (_, state) => {
            isMaximized = state === 'maximized';
            updateMaximizeUI();
          });
        };

        const updateMaximizeUI = () => {
          const maxBtn = document.querySelector('.maximize-btn');
          if (!maxBtn) return;

          const img = maxBtn.querySelector('img');
          img.src = isMaximized
            ? 'https://static-file.duijie.org.cn/frontend-file/frontend/imgs/welcom/window-restore.png'
            : 'https://static-file.duijie.org.cn/frontend-file/frontend/imgs/welcom/window-maximize.png';
          maxBtn.title = isMaximized ? '还原' : '最大化';
        };
        return { init, setupAutoClose };
      })();

      // Iframe 加载模块
      const IframeLoader = (() => {
        const token = utils.getQueryParam('token');
        const iframe = document.querySelector('.responsive-iframe');
        const loading = document.querySelector('.loading');

        // 环境检测
        if (window.self !== window.top) {
          console.warn("当前页面被嵌入在 iframe 中");
          document.querySelector('.header').style.display = 'none';
        }

        const init = () => {
          const token = utils.getQueryParam('token');
          if (!token) {
            showError('参数错误：缺少必要的token参数');
            return;
          }

          loadIframe(token);
        };

        const loadIframe = (token) => {
          iframe.onerror = () => {
            loading.innerHTML = `
            <div>内容加载失败</div>
            <button class="retry-btn" onclick="window.location.reload()">重试</button>
          `;
          };

          iframe.src = `${baseUrl}?token=${encodeURIComponent(token)}`;

          iframe.onload = () => {
            iframe.style.visibility = 'visible';
            loading.style.display = 'none';
          };

        };

        return { init };
      })();

      // 主初始化流程
      const init = () => {
        // 开始倒计时
        WindowControl.setupAutoClose();
        // 优先初始化操作栏（以防操作栏被ifame影响）
        WindowControl.init();

        // 独立初始化iframe
        try {
          IframeLoader.init();
        } catch (error) {
          console.error('Iframe加载失败:', error);
        }
      };

      document.addEventListener('DOMContentLoaded', init);
    })();
  </script>
</body>

</html>