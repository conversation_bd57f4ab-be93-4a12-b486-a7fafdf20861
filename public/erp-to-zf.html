<!DOCTYPE html>
<html lang="zh-cmn-Hans">
  <!-- erp客户端通过网页打开账户中心的中间页 -->
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" />
    <meta http-equiv="X-UA-Compatible" content="IE=9;IE=8;IE=7;ie=edge">
    <title></title>
    <link rel="icon" href="" />
    <link rel="stylesheet" href="/css/common.css">
  </head>
  <body>
    <div id="app">
      <!-- loading -->
      <div id="loading-container">
        <div id="app-loading">
          <span></span>
          <span></span>
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
      <div class="toast" id="my-toast"></div>
    </div>
    <!-- jd sdk 跳转收银台 -->
    <script src="https://static-ftcms.jd.com/p/files/sdk-1.0.0.js"></script>
    <script src="./js/jquery.1.12.js"></script>
    <script>
      // 和APP交互文档地址： http://yapi.sdpjw.com/project/592/interface/api/28363
      // 环境变量的集合
      var baseUrlList = {
        development: "https://shendu-dev3.sdpjw.cn", // 开发环境
        staging: "https://staging-api.lianwuxian.cn", // 预发环境
        production: "https://api.lianwuxian.cn", // 生产环境
      };

      var query = getUrlCode();
      var env = query.env;
      var token = query.token;
      var accountType = query.accountType;
      var appType = query.appType
      // 设置网站图标
      let link = document.querySelector('link[rel*="icon"]')
      link.href = appType === 'hn' ? '/favicon.ico' : '/favicon1.ico'
      // 京东回调地址
      var cdUrl = window.location.href
      jumpJdPay()

      /** 截取url中的code方法
       * @returns {Object}
       */
       function getUrlCode() {
        var url = window.location.search;
        var theRequest = new Object();
        if (url.indexOf("?") != -1) {
          var str = url.split("?")[1];
          var strs = str.split("&");
          for (var i = 0; i < strs.length; i++) {
            theRequest[strs[i].split("=")[0]] = strs[i].split("=")[1];
          }
        }
        return theRequest;
      }

      // 返回app页面
      function backApp() {
        window.location.href = cdUrl
      }
      
      // 打开toast提示 2秒后自动返回AP
      function Toast (text){
        var dome = document.getElementById('my-toast')
        dome.innerText = text
        dome.style.opacity = 1
        setTimeout(function() {
          dome.style.opacity = 0
        }, 2000);
      }

      function jumpJdPay() {
        // 配置SDK环境 预发环境和生产一致，开发环境要配置环境参数 
        if (!env || (env !== "prod" && env !== "erp")) {
          window.jpmt.env = "development";
          window.jpmt.getDomain = function() {
            return "https://piaoju-test.jd.com";
          } 
        }
        // m默认是经纪商服务
        var api = '/api/broker/jdSignature/getSignature'
        if(appType === 'hn'){ // 如果是承让erp调用qiqilian服务
          api = '/api/platform/jdSignature/getSignature'
        }
        var url = baseUrlList[env || "development"] + api
        $.ajax({
          url: url,
          timeout: 10 * 1000, // 单位毫秒，超时时间
          type:"post",
          dataType:"JSON",
          data:{url: window.location.origin},
          beforeSend:function (request) {
            request.setRequestHeader("accessToken",token);
            request.setRequestHeader("accept","application/json, text/plain");
            request.setRequestHeader("Content-Type","application/json;charset=utf-8");
          },
          success:function(data){
            console.log("hshshshs", data);
            if(data.code === 200 && data.data){
              var res = data.data
              var sendData = {
                platformId: res.platformId,
                platformUserId: res.platformUserId,
                saltStr: res.saltStr,
                signature: res.signature,
                timestamp: res.timestamp,
                url: res.url,
                name: 'zpm-ucenter', // 跳转到智付账户中心页面名称
                backUrl: cdUrl, // 跳转异常的时候返回的地址
                loginUrl: cdUrl, // 如果是未登录情况下 跳转到用户中心会自动触发登录弹框
                querys: {
                  accountType: accountType
                }
              }
            console.log("调用SDK入参", sendData);
            let url = window.jpmt.computePageUrl(sendData);
            if (url) {
              console.log("SDK返回的的地址", url);
              window.location.href = url
            } else {
              Toast('通过SDk获取地址失败，请稍后再试')
            }
          }else{
            Toast(data.msg)
          }
          }
        });
      }
    </script>
  </body>
</html>
