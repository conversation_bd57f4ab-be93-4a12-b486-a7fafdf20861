.toast {
  position: fixed;
  top: 40%;
  left: 50%;
  z-index: 1000;
  border-radius: 5px;
  padding: 10px;
  max-width: 80%;
  text-align: center;
  color: #FFFFFF;
  background: rgb(0 0 0 / 70%);
  opacity: 0;
  transition: opacity .3s linear;
  box-sizing: border-box;
  transform: translate(-50%, -50%);
}

#loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  height: calc(100vh - 20px);
}

#app-loading {
  width: 150px;
  height: 15px;
}

#app-loading span {
  display: inline-block;
  margin-right: 5px;
  border-radius: 50%;
  width: 15px;
  height: 100%;
  background: #366AF0;
  animation: app-loading 1.04s ease infinite;
}

#app-loading span:last-child {
  margin-right: 0;
}

@keyframes app-loading {
  0% {
    opacity: 1;
    transform: scale(1.3);
  }

  100% {
    opacity: .2;
    transform: scale(.3);
  }
}

#app-loading span:nth-child(1) {
  animation-delay: .13s;
}

#app-loading span:nth-child(2) {
  animation-delay: .26s;
}

#app-loading span:nth-child(3) {
  animation-delay: .39s;
}

#app-loading span:nth-child(4) {
  animation-delay: .52s;
}

#app-loading span:nth-child(5) {
  animation-delay: .65s;
}
