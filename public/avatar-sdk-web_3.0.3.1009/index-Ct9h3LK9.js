function e(e,r,n){return r=u(r),function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return d(e)}(e,t()?Reflect.construct(r,n||[],u(e).constructor):r.apply(e,n))}function t(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(<PERSON>olean,[],(function(){})))}catch(e){}return(t=function(){return!!e})()}function r(){r=function(){return t};var e,t={},n=Object.prototype,o=n.hasOwnProperty,i=Object.defineProperty||function(e,t,r){e[t]=r.value},a="function"==typeof Symbol?Symbol:{},s=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function l(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,r){return e[t]=r}}function f(e,t,r,n){var o=t&&t.prototype instanceof g?t:g,a=Object.create(o.prototype),s=new W(n||[]);return i(a,"_invoke",{value:S(e,r,s)}),a}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var v="suspendedStart",h="suspendedYield",p="executing",y="completed",m={};function g(){}function w(){}function b(){}var _={};l(_,s,(function(){return this}));var x=Object.getPrototypeOf,k=x&&x(x(T([])));k&&k!==n&&o.call(k,s)&&(_=k);var j=b.prototype=g.prototype=Object.create(_);function O(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function M(e,t){function r(n,i,a,s){var c=d(e[n],e,i);if("throw"!==c.type){var u=c.arg,l=u.value;return l&&"object"==typeof l&&o.call(l,"__await")?t.resolve(l.__await).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):t.resolve(l).then((function(e){u.value=e,a(u)}),(function(e){return r("throw",e,a,s)}))}s(c.arg)}var n;i(this,"_invoke",{value:function(e,o){function i(){return new t((function(t,n){r(e,o,t,n)}))}return n=n?n.then(i,i):i()}})}function S(t,r,n){var o=v;return function(i,a){if(o===p)throw new Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:e,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var c=E(s,n);if(c){if(c===m)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===v)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=p;var u=d(t,r,n);if("normal"===u.type){if(o=n.done?y:h,u.arg===m)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(o=y,n.method="throw",n.arg=u.arg)}}}function E(t,r){var n=r.method,o=t.iterator[n];if(o===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,E(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var i=d(o,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,m;var a=i.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,m):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function A(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function P(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function W(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(A,this),this.reset(!0)}function T(t){if(t||""===t){var r=t[s];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function r(){for(;++n<t.length;)if(o.call(t,n))return r.value=t[n],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(typeof t+" is not iterable")}return w.prototype=b,i(j,"constructor",{value:b,configurable:!0}),i(b,"constructor",{value:w,configurable:!0}),w.displayName=l(b,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,l(e,u,"GeneratorFunction")),e.prototype=Object.create(j),e},t.awrap=function(e){return{__await:e}},O(M.prototype),l(M.prototype,c,(function(){return this})),t.AsyncIterator=M,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new M(f(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},O(j),l(j,u,"Generator"),l(j,s,(function(){return this})),l(j,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=T,W.prototype={constructor:W,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(P),!t)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(n,o){return s.type="throw",s.arg=t,r.next=n,o&&(r.method="next",r.arg=e),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var c=o.call(a,"catchLoc"),u=o.call(a,"finallyLoc");if(c&&u){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),P(r),m}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;P(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:T(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),m}},t}function n(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,n(o.key),o)}}function s(e,t,r){return t&&a(e.prototype,t),r&&a(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function c(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&l(e,t)}function u(e){return u=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},u(e)}function l(e,t){return l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},l(e,t)}function f(e){var r="function"==typeof Map?new Map:void 0;return f=function(e){if(null===e||!function(e){try{return-1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}}(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==r){if(r.has(e))return r.get(e);r.set(e,n)}function n(){return function(e,r,n){if(t())return Reflect.construct.apply(null,arguments);var o=[null];o.push.apply(o,r);var i=new(e.bind.apply(e,o));return n&&l(i,n.prototype),i}(e,arguments,u(this).constructor)}return n.prototype=Object.create(e.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),l(n,e)},f(e)}function d(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function v(){return v="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,r){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=u(e)););return e}(e,t);if(n){var o=Object.getOwnPropertyDescriptor(n,t);return o.get?o.get.call(arguments.length<3?e:r):o.value}},v.apply(this,arguments)}function h(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,s=[],c=!0,u=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw o}}return s}}(e,t)||y(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(e){return function(e){if(Array.isArray(e))return m(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||y(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(e,t){if(e){if("string"==typeof e)return m(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?m(e,t):void 0}}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function g(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r}function w(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{c(n.next(e))}catch(e){i(e)}}function s(e){try{c(n.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}c((n=n.apply(e,t||[])).next())}))}function b(e,t,r,n){if("a"===r&&!n)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)}function _(e,t,r,n,o){if("m"===n)throw new TypeError("Private method is not writable");if("a"===n&&!o)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!o:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?o.call(e,r):o?o.value=r:t.set(e,r),r}var x,k,j,O,M,S,E;"function"==typeof SuppressedError&&SuppressedError,function(e){e[e.start=0]="start",e[e.intermediate=1]="intermediate",e[e.end=2]="end"}(x||(x={})),function(e){e[e.disconnected=0]="disconnected",e[e.connecting=2]="connecting",e[e.connected=1]="connected"}(k||(k={})),function(e){e[e.start=0]="start",e[e.processing=1]="processing",e[e.stop=2]="stop"}(j||(j={})),function(e){e[e.append=0]="append",e[e.break=1]="break"}(O||(O={})),function(e){e[e.offline=0]="offline",e[e.realtime=1]="realtime"}(M||(M={})),function(e){e.live="live",e.genneral="genneral"}(S||(S={})),function(e){e.action="action"}(E||(E={}));var A=function(t){function r(t,n,o,a,s){var c;return i(this,r),(c=e(this,r,[t])).name=o,c.code=n,c.request_id=s||"",c.sid=a||"",c}return c(r,f(Error)),s(r)}();function P(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)})).replace(/-/g,"")}var W=function(){var e={resolve:function(){},reject:function(){}};return{promise:new Promise((function(t,r){e.resolve=t,e.reject=r})),controller:e}};function T(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.reduce((function(e,t){for(var r in t)if(t.hasOwnProperty(r)){var n=t[r],i=e[r];"object"===o(n)&&null!==n&&"object"===o(i)&&null!==i?Array.isArray(n)?e[r]=p(n):e[r]=T(i,n):e[r]=n}return e}),{})}const I="function"==typeof Buffer,C=("function"==typeof TextDecoder&&new TextDecoder,"function"==typeof TextEncoder&&new TextEncoder,Array.prototype.slice.call("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=")),L=(e=>{let t={};return C.forEach(((e,r)=>t[e]=r)),t})(),R=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,U=String.fromCharCode.bind(String),z=("function"==typeof Uint8Array.from&&Uint8Array.from.bind(Uint8Array),e=>e.replace(/=/g,"").replace(/[+\/]/g,(e=>"+"==e?"-":"_"))),B=e=>e.replace(/[^A-Za-z0-9\+\/]/g,""),F=e=>{let t,r,n,o,i="";const a=e.length%3;for(let a=0;a<e.length;){if((r=e.charCodeAt(a++))>255||(n=e.charCodeAt(a++))>255||(o=e.charCodeAt(a++))>255)throw new TypeError("invalid character found");t=r<<16|n<<8|o,i+=C[t>>18&63]+C[t>>12&63]+C[t>>6&63]+C[63&t]}return a?i.slice(0,a-3)+"===".substring(a):i},D="function"==typeof btoa?e=>btoa(e):I?e=>Buffer.from(e,"binary").toString("base64"):F,N=I?e=>Buffer.from(e).toString("base64"):e=>{let t=[];for(let r=0,n=e.length;r<n;r+=4096)t.push(U.apply(null,e.subarray(r,r+4096)));return D(t.join(""))},q=(e,t=!1)=>t?z(N(e)):N(e),V=e=>{if(e=e.replace(/\s+/g,""),!R.test(e))throw new TypeError("malformed base64.");e+="==".slice(2-(3&e.length));let t,r,n,o="";for(let i=0;i<e.length;)t=L[e.charAt(i++)]<<18|L[e.charAt(i++)]<<12|(r=L[e.charAt(i++)])<<6|(n=L[e.charAt(i++)]),o+=64===r?U(t>>16&255):64===n?U(t>>16&255,t>>8&255):U(t>>16&255,t>>8&255,255&t);return o},H=q;var G,K,$={code:"600000",message:"必要参数缺失"},Z={code:"600003",message:"连接异常"},J={code:"600004",message:"无效的响应"},X={code:"999999",message:"未知错误"},Y={EmptyStreamError:{code:"700000",message:"无效的流数据"},PlayNotAllowed:{code:"700006",message:"播放不允许"},MissingPlayerLibsError:{code:"700001",message:"缺失播放插件"},H264NotSupported:{code:"700002",message:"当前设备不支持 H.264"},Unknown:{code:"700005",message:"播放失败"}},Q={code:"800000",message:"不支持的环境"},ee={code:"800001",message:"未找到指定约束的设备"},te={code:"800002",message:"设备访问权限异常/无法请求使用源设备"},re={code:"800003",message:"暂时无法访问摄像头/麦克风，请确保当前没有其他应用请求访问设备，并重试"},ne={code:"800004",message:"无效的设备请求参数"},oe={code:"800005",message:"未知原因操作已终止"},ie={code:"800006",message:"当前页面未处于激活状态"},ae={code:"800007",message:"页面未发生用户交互，请求被终止"};!function(e){e.InvalidParam="InvalidParam",e.InvalidResponse="InvalidResponse",e.ContextError="ContextError",e.NetworkError="NetworkError",e.ConnectError="ConnectError",e.InvalidConnect="InvalidConnect",e.MediaError="MediaError",e.UserMediaError="UserMediaError"}(G||(G={})),function(e){e[e.verbose=0]="verbose",e[e.debug=1]="debug",e[e.info=2]="info",e[e.warn=3]="warn",e[e.error=4]="error",e[e.none=5]="none"}(K||(K={}));var se,ce,ue,le,fe,de,ve,he,pe,ye,me,ge,we,be=K.warn,_e=function(){function e(){i(this,e)}return s(e,null,[{key:"setLogLevel",value:function(e){be=e}},{key:"record",value:function(e){var t,r,n,o,i;if(e>=be){for(var a=arguments.length,s=new Array(a>1?a-1:0),c=1;c<a;c++)s[c-1]=arguments[c];switch(e){case K.verbose:(t=console).log.apply(t,["[SDK] [VERBOSE] "].concat(s));break;case K.debug:(r=console).log.apply(r,["[SDK] [DEBUG] "].concat(s));break;case K.info:(n=console).log.apply(n,["[SDK] [INFO] "].concat(s));break;case K.warn:(o=console).warn.apply(o,["[SDK] [WARN] "].concat(s));break;case K.error:(i=console).error.apply(i,["[SDK] [ERROR] "].concat(s))}}}}]),e}(),xe=function(){function e(t,r){var n=this;i(this,e),ce.set(this,void 0),ue.set(this,se.CLOSED),le.set(this,"web"),fe.set(this,void 0),de.set(this,void 0),ve.set(this,void 0),he.set(this,void 0),"undefined"!=typeof wx&&wx.env&&_(this,le,"miniprogram","f"),_e.record(K.debug,"[ws]",b(this,le,"f"),t),_(this,ce,void 0,"f"),_(this,ue,se.CONNECTING,"f"),"miniprogram"===b(this,le,"f")?(_(this,ce,wx.connectSocket({url:encodeURI(t)}),"f"),b(this,ce,"f").onOpen((function(){var e,t;_e.record(K.debug,"[ws]","channel open"),_(n,ue,se.OPEN,"f");for(var r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];null===(t=b(n,fe,"f"))||void 0===t||(e=t).call.apply(e,[n].concat(o))})),b(this,ce,"f").onMessage((function(){for(var e,t,r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];null===(t=b(n,de,"f"))||void 0===t||(e=t).call.apply(e,[n].concat(o))})),b(this,ce,"f").onClose((function(){var e,t;_e.record(K.debug,"[ws]","channel closed"),_(n,ue,se.CLOSED,"f");for(var r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];null===(t=b(n,he,"f"))||void 0===t||(e=t).call.apply(e,[n].concat(o))})),b(this,ce,"f").onError((function(e){var t;_e.record(K.error,"[ws]","channel error",e),null===(t=b(n,ve,"f"))||void 0===t||t.call(n,e)}))):(_(this,ce,new WebSocket(t),"f"),(null==r?void 0:r.binaryData)&&(_e.record(K.debug,"[ws]","binaryType:ab"),b(this,ce,"f").binaryType="arraybuffer"),b(this,ce,"f").onopen=function(){var e,t;_e.record(K.debug,"[ws]","channel open");for(var r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];null===(t=b(n,fe,"f"))||void 0===t||(e=t).call.apply(e,[n].concat(o))},b(this,ce,"f").onmessage=function(){for(var e,t,r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];null===(t=b(n,de,"f"))||void 0===t||(e=t).call.apply(e,[n].concat(o))},b(this,ce,"f").onclose=function(){var e,t;_e.record(K.debug,"[ws]","channel closed");for(var r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];null===(t=b(n,he,"f"))||void 0===t||(e=t).call.apply(e,[n].concat(o))},b(this,ce,"f").onerror=function(e){var t;_e.record(K.error,"[ws]","channel error",e),null===(t=b(n,ve,"f"))||void 0===t||t.call(n,e)})}return s(e,[{key:"readyState",get:function(){return"miniprogram"===b(this,le,"f")?b(this,ue,"f"):b(this,ce,"f").readyState}},{key:"onopen",set:function(e){_(this,fe,e,"f")}},{key:"onmessage",set:function(e){_(this,de,e,"f")}},{key:"onclose",set:function(e){_(this,he,e,"f")}},{key:"onerror",set:function(e){_(this,ve,e,"f")}},{key:"send",value:function(e){"miniprogram"===b(this,le,"f")?b(this,ce,"f").send({data:e}):b(this,ce,"f").send(e)}},{key:"close",value:function(e){var t,r,n,o;_(this,ue,se.CLOSING,"f"),_e.record(K.debug,"[ws]","close channel",null!==(t=null==e?void 0:e.code)&&void 0!==t?t:"",null!==(r=null==e?void 0:e.reason)&&void 0!==r?r:""),"miniprogram"===b(this,le,"f")?null===(n=b(this,ce,"f"))||void 0===n||n.close(e):null===(o=b(this,ce,"f"))||void 0===o||o.close(null==e?void 0:e.code,null==e?void 0:e.reason)}}]),e}();function ke(e,t){var r,n=!1;return{abort:function(){var e;n=!0,clearTimeout(undefined),r&&(r.onerror=null,r.onopen=null,r.onmessage=null,null===(e=r.close)||void 0===e||e.call(r))},instablishPromise:new Promise((function(o,i){try{var a=0;(r=new xe(e,t)).onopen=function(){r.onerror=null,r.onopen=null,a=setTimeout((function(){n?r.close():o(r)}),50)},r.onclose=function(e){clearTimeout(a),r.onerror=null,r.onopen=null,r.onclose=null,n||i(e)},r.onerror=function(e){clearTimeout(a),r.onerror=null,r.onopen=null,r.onclose=null,n||i(e)}}catch(e){i(e)}}))}}se=xe,ce=new WeakMap,ue=new WeakMap,le=new WeakMap,fe=new WeakMap,de=new WeakMap,ve=new WeakMap,he=new WeakMap,xe.CONNECTING=0,xe.OPEN=1,xe.CLOSING=2,xe.CLOSED=3;var je,Oe,Me,Se,Ee,Ae,Pe,We,Te,Ie,Ce,Le,Re,Ue,ze,Be,Fe,De,Ne=0,qe=function(){function e(t){var r,n=this;i(this,e),pe.set(this,Ne),ye.set(this,{}),me.set(this,[]),ge.set(this,(function(e,t,r){if("function"!=typeof t)throw TypeError("listener must be a function");-1===b(n,me,"f").indexOf(e)&&b(n,me,"f").push(e),b(n,ye,"f")[e]=b(n,ye,"f")[e]||[],b(n,ye,"f")[e].push({once:r||!1,fn:t})})),we.set(this,(function(e,t){var r=b(n,ye,"f")[e],o=[];null==r||r.forEach((function(e,r){e.fn.apply(null,t),e.once&&o.unshift(r)})),null==o||o.forEach((function(e){r.splice(e,1)}))})),_(this,pe,null!==(r=null==t?void 0:t.emitDelay)&&void 0!==r?r:Ne,"f")}return s(e,[{key:"on",value:function(e,t){return b(this,ge,"f").call(this,e,t,!1),this}},{key:"once",value:function(e,t){return b(this,ge,"f").call(this,e,t,!0),this}},{key:"off",value:function(e,t){var r=b(this,me,"f").indexOf(e);if(e&&-1!==r)if(t){var n=[],o=b(this,ye,"f")[e];null==o||o.forEach((function(e,r){e.fn===t&&n.unshift(r)})),null==n||n.forEach((function(e){o.splice(e,1)})),o.length||(b(this,me,"f").splice(r,1),delete b(this,ye,"f")[e])}else delete b(this,ye,"f")[e],b(this,me,"f").splice(r,1);return this}},{key:"removeAllListeners",value:function(){return _(this,ye,{},"f"),_(this,me,[],"f"),this}},{key:"emit",value:function(e){for(var t=this,r=arguments.length,n=new Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];b(this,pe,"f")?setTimeout((function(){b(t,we,"f").call(t,e,n)}),b(this,pe,"f")):b(this,we,"f").call(this,e,n)}},{key:"emitSync",value:function(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];b(this,we,"f").call(this,e,r)}},{key:"destroy",value:function(){_(this,ye,{},"f"),_(this,me,[],"f")}}]),e}();pe=new WeakMap,ye=new WeakMap,me=new WeakMap,ge=new WeakMap,we=new WeakMap,function(e){e.connected="connected",e.disconnected="disconnected",e.nlp="nlp",e.asr="asr",e.stream_start="stream_start",e.frame_start="frame_start",e.frame_stop="frame_stop",e.tts_duration="tts_duration",e.error="error"}(je||(je={})),function(e){e.play="play",e.waiting="waiting",e.playing="playing",e.stop="stop",e.playNotAllowed="not-allowed",e.error="error"}(Oe||(Oe={}));var Ve,He=function(t){function n(){var t;return i(this,n),t=e(this,n),Me.set(d(t),void 0),Se.set(d(t),"xrtc"),Ee.set(d(t),void 0),Ae.set(d(t),!1),Pe.set(d(t),1),We.set(d(t),"center"),Te.set(d(t),void 0),Ie.set(d(t),void 0),Ce.set(d(t),void 0),Le.set(d(t),void 0),Re.set(d(t),{width:1080,height:1920}),Ue.set(d(t),1),ze.set(d(t),(function(){return w(d(t),void 0,void 0,r().mark((function e(){var t,n,o,i,a,s,c=this;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(b(this,Me,"f")&&b(this,Me,"f").destroy(),n=void 0,e.prev=2,"xrtc"!==b(this,Se,"f")){e.next=11;break}return e.next=6,import("./xrtc-player-Bb6-V-Q4.js");case 6:o=e.sent,i=o.XRTCPlayer,_(this,Me,new i,"f"),e.next=17;break;case 11:if("webrtc"!==b(this,Se,"f")){e.next=17;break}return e.next=14,import("./webrtc-player-CiiaCA8k.js");case 14:a=e.sent,s=a.WebRTCPlayer,_(this,Me,new s,"f");case 17:e.next=22;break;case 19:e.prev=19,e.t0=e.catch(2),n=new A(Y.MissingPlayerLibsError.message,Y.MissingPlayerLibsError.code,G.MediaError);case 22:if(!n){e.next=24;break}return e.abrupt("return",Promise.reject(n));case 24:null===(t=b(this,Me,"f"))||void 0===t||t.on(Oe.play,(function(){c.emit(Oe.play)})).on(Oe.waiting,(function(){c.emit(Oe.waiting)})).on(Oe.playing,(function(){c.emit(Oe.playing)})).on(Oe.playNotAllowed,(function(){c.emit(Oe.playNotAllowed)})).on(Oe.stop,(function(){c.emit(Oe.stop)})).on(Oe.error,(function(e){c.emit(Oe.error,e)}));case 25:case"end":return e.stop()}}),e,this,[[2,19]])})))})),Be.set(d(t),(function(){var e,r;if(!b(d(t),Ce,"f")){var n=_(d(t),Ce,document.createElement("div"),"f");n.setAttribute("id","xvideo"),n.style.position="relative",n.style.width="100%",n.style.height="100%",n.style.minWidth="100%",n.style.minHeight="100%",n.style.pointerEvents="none",null===(e=b(d(t),Ie,"f"))||void 0===e||e.appendChild(n)}if(!b(d(t),Le,"f")){var o=_(d(t),Le,document.createElement("div"),"f");o.style.position="absolute",t.resize(),b(d(t),Ce,"f").appendChild(o),window.addEventListener("resize",b(d(t),Fe,"f"))}if(b(d(t),Ie,"f"))try{null===(r=b(d(t),Ee,"f"))||void 0===r||r.observe(b(d(t),Ie,"f"))}catch(e){}})),Fe.set(d(t),(function(){var e,r,n=b(d(t),Re,"f"),o=n.width,i=n.height,a=(null===(e=b(d(t),Ce,"f"))||void 0===e?void 0:e.offsetWidth)||0,s=(null===(r=b(d(t),Ce,"f"))||void 0===r?void 0:r.offsetHeight)||0;if(b(d(t),Le,"f")){var c=1;c=a/s>o/i?s/i:a/o,b(d(t),Le,"f").style.left="50%";var u="-50%";"bottom"===b(d(t),We,"f")?(u="0",b(d(t),Le,"f").style.bottom="0px",b(d(t),Le,"f").style.transformOrigin="center bottom"):(b(d(t),Le,"f").style.top="50%",b(d(t),Le,"f").style.transformOrigin="center center");var l=b(d(t),Ue,"f")*c;b(d(t),Le,"f").style.transform="translate3d(-50%,".concat(u,",0) scale(").concat(l,", ").concat(c,")")}})),De.set(d(t),(function(){var e;if(b(d(t),Ie,"f"))try{null===(e=b(d(t),Ee,"f"))||void 0===e||e.unobserve(b(d(t),Ie,"f"))}catch(e){}window.removeEventListener("resize",b(d(t),Fe,"f")),b(d(t),Le,"f")&&(b(d(t),Le,"f").remove(),_(d(t),Le,void 0,"f")),b(d(t),Ce,"f")&&(b(d(t),Ce,"f").remove(),_(d(t),Ce,void 0,"f"))})),void 0!==window.ResizeObserver&&_(d(t),Ee,new ResizeObserver((function(e){e.forEach((function(){var e;null===(e=b(d(t),Fe,"f"))||void 0===e||e.call(d(t))}))})),"f"),t}return c(n,qe),s(n,[{key:"renderAlign",set:function(e){_(this,We,e,"f")}},{key:"playerType",set:function(e){_(this,Se,e,"f")}},{key:"muted",get:function(){var e,t=b(this,Ae,"f");return b(this,Me,"f")&&(t=null===(e=b(this,Me,"f"))||void 0===e?void 0:e.muted),t},set:function(e){_(this,Ae,e,"f"),b(this,Me,"f")&&(e?b(this,Me,"f").muted=!0:(b(this,Me,"f").muted=!1,b(this,Me,"f").resume()))}},{key:"volume",get:function(){var e;return(null===(e=b(this,Me,"f"))||void 0===e?void 0:e.volume)||b(this,Pe,"f")},set:function(e){e>1&&(e=1),_(this,Pe,e,"f"),b(this,Me,"f")&&(b(this,Me,"f").volume=e)}},{key:"stream",set:function(e){_(this,Te,e,"f"),b(this,Me,"f")&&(b(this,Me,"f").stream=e)}},{key:"container",set:function(e){var t;if(b(this,Ie,"f"))try{null===(t=b(this,Ee,"f"))||void 0===t||t.unobserve(b(this,Ie,"f"))}catch(e){}_(this,Ie,e,"f")}},{key:"videoSize",set:function(e){_(this,Re,e,"f")}},{key:"playStream",value:function(e){return w(this,void 0,void 0,r().mark((function t(){var n;return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return _(this,Te,e,"f"),console.log(b(this,Te,"f")),b(this,Be,"f").call(this),t.next=5,b(this,ze,"f").call(this);case 5:return b(this,Me,"f")&&(b(this,Me,"f")&&(b(this,Me,"f").stream=e),b(this,Me,"f").videoWrapper=b(this,Le,"f")),t.prev=6,t.next=9,null===(n=b(this,Me,"f"))||void 0===n?void 0:n.play();case 9:t.next=14;break;case 11:t.prev=11,t.t0=t.catch(6),this.stop();case 14:case"end":return t.stop()}}),t,this,[[6,11]])})))}},{key:"resume",value:function(){return w(this,void 0,void 0,r().mark((function e(){var t=this;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!b(this,Me,"f")){e.next=2;break}return e.abrupt("return",b(this,Me,"f").resume().then((function(){b(t,Me,"f")&&(b(t,Me,"f").muted=!1)})));case 2:return e.abrupt("return",Promise.reject("player not found"));case 3:case"end":return e.stop()}}),e,this)})))}},{key:"stop",value:function(){var e;b(this,De,"f").call(this),null===(e=b(this,Me,"f"))||void 0===e||e.stop()}},{key:"scaleX",get:function(){return b(this,Ue,"f")||1},set:function(e){_(this,Ue,e,"f"),b(this,Fe,"f").call(this)}},{key:"setSinkId",value:function(e){return w(this,void 0,void 0,r().mark((function t(){var n;return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,null===(n=b(this,Me,"f"))||void 0===n?void 0:n.setSinkId(e);case 2:case"end":return t.stop()}}),t,this)})))}},{key:"getSinkId",value:function(){return w(this,void 0,void 0,r().mark((function e(){var t;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(null===(t=b(this,Me,"f"))||void 0===t?void 0:t.getSinkId())||"");case 1:case"end":return e.stop()}}),e,this)})))}},{key:"destroy",value:function(){var e;try{null===(e=b(this,Ee,"f"))||void 0===e||e.disconnect()}catch(e){}_(this,Ee,void 0,"f"),this.stop(),v(u(n.prototype),"destroy",this).call(this)}},{key:"resize",value:function(){var e;b(this,Le,"f")&&(b(this,Le,"f").style.width="".concat(b(this,Re,"f").width,"px"),b(this,Le,"f").style.height="".concat(b(this,Re,"f").height,"px")),null===(e=b(this,Me,"f"))||void 0===e||e.resize(),b(this,Fe,"f").call(this)}}],[{key:"getVersion",value:function(){return"3.0.3-1009"}},{key:"setLogLevel",value:function(e){_e.setLogLevel(e)}}]),n}();function Ge(){var e={transF32ToRawData:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:16e3,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:16e3,o=e.transSamplingRate(t,r,n);return e.transF32ToS16(o).buffer},transSamplingRate:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:44100,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:16e3;if(t===r)return e;var n=Math.round(e.length*(r/t)),o=new Float32Array(n),i=(e.length-1)/(n-1);o[0]=e[0];for(var a=1;a<n-1;a++){var s=a*i,c=Number(Math.floor(s).toFixed()),u=Number(Math.ceil(s).toFixed()),l=s-c;o[a]=e[c]+(e[u]-e[c])*l}return o[n-1]=e[e.length-1],o},transF32ToS16:function(e){for(var t=[],r=0;r<e.length;r++){var n=e[r]<0?32768*e[r]:32767*e[r];t.push(n)}return new Int16Array(t)}};self.onmessage=function(t){var r=t.data,n=r.audio,o=r.sampleRate,i=void 0===o?16e3:o,a=r.destSampleRate,s=void 0===a?16e3:a;try{var c=e.transF32ToRawData(n,i,s);self.postMessage({data:c})}catch(t){self.postMessage({error:{code:t.type,message:t.message}})}}}Me=new WeakMap,Se=new WeakMap,Ee=new WeakMap,Ae=new WeakMap,Pe=new WeakMap,We=new WeakMap,Te=new WeakMap,Ie=new WeakMap,Ce=new WeakMap,Le=new WeakMap,Re=new WeakMap,Ue=new WeakMap,ze=new WeakMap,Be=new WeakMap,Fe=new WeakMap,De=new WeakMap,function(e){e.recoder_audio="recoder_audio",e.ended="ended",e.mute="mute",e.unmute="unmute",e.error="error",e.deviceAutoSwitched="device-auto-switched"}(Ve||(Ve={}));var Ke,$e,Ze,Je,Xe,Ye,Qe,et,tt,rt,nt,ot,it,at,st,ct,ut,lt,ft,dt,vt,ht,pt,yt,mt,gt,wt,bt,_t,xt,kt,jt,Ot=function(){function e(){i(this,e)}return s(e,null,[{key:"requestPermissions",value:function(t){return w(this,void 0,void 0,r().mark((function n(){var o,i,a;return r().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:o="",r.t0=t,r.next="audioinput"===r.t0||"audiooutput"===r.t0?4:"videoinput"===r.t0?6:8;break;case 4:return o="microphone",r.abrupt("break",8);case 6:return o="camera",r.abrupt("break",8);case 8:if(!o){r.next=22;break}if(i="prompt",!navigator.permissions){r.next=17;break}return r.next=13,navigator.permissions.query({name:o});case 13:if(a=r.sent,"denied"!==(i=a.state)){r.next=17;break}return r.abrupt("return",Promise.reject(new A(te.message,te.code,G.UserMediaError)));case 17:if("prompt"!==i){r.next=22;break}return r.next=20,e.getUserMedia({video:"camera"===o,audio:"microphone"===o});case 20:r.sent.getTracks().forEach((function(e){e.stop()}));case 22:case"end":return r.stop()}}),n)})))}},{key:"getEnumerateDevices",value:function(t){return w(this,void 0,void 0,r().mark((function n(){var o;return r().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(navigator.mediaDevices&&navigator.mediaDevices.enumerateDevices){r.next=2;break}return r.abrupt("return",Promise.reject(new A(Q.message,Q.code,G.UserMediaError)));case 2:return r.next=4,e.requestPermissions(t);case 4:return r.next=6,navigator.mediaDevices.enumerateDevices().then((function(e){return e.filter((function(e){return e.kind===t&&e.deviceId}))})).catch((function(e){return Promise.reject(new A(e.message||e.name||ie.message,ie.code,G.UserMediaError))}));case 6:return o=r.sent,r.abrupt("return",o);case 8:case"end":return r.stop()}}),n)})))}},{key:"getUserMedia",value:function(e){return w(this,void 0,void 0,r().mark((function t(){return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,navigator.mediaDevices.getUserMedia(e).catch((function(e){var t=new A(oe.message,oe.code,G.UserMediaError);switch(null==e?void 0:e.name){case"NotAllowedError":t=new A(te.message,te.code,G.UserMediaError);break;case"SecurityError":t=new A(Q.message,Q.code,G.UserMediaError);break;case"NotReadableError":t=new A(re.message,re.code,G.UserMediaError);break;case"NotFoundError":t=new A(ee.message,ee.code,G.UserMediaError);break;case"OverconstrainedError":t=new A(ne.message,ne.code,G.UserMediaError)}return Promise.reject(t)}));case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t)})))}}]),e}(),Mt=function(t){function n(t){var o,a;return i(this,n),a=e(this,n),Ke.add(d(a)),$e.set(d(a),new window.AudioContext),Ze.set(d(a),void 0),Je.set(d(a),void 0),Xe.set(d(a),[]),Ye.set(d(a),{sampleRate:16e3,analyser:!1}),Qe.set(d(a),void 0),et.set(d(a),void 0),tt.set(d(a),void 0),rt.set(d(a),void 0),nt.set(d(a),void 0),ot.set(d(a),void 0),it.set(d(a),void 0),at.set(d(a),0),st.set(d(a),!1),ct.set(d(a),!1),ut.set(d(a),12e4),lt.set(d(a),void 0),ft.set(d(a),!1),dt.set(d(a),!1),vt.set(d(a),void 0),ht.set(d(a),void 0),pt.set(d(a),(function(){var e;if(!b(d(a),it,"f"))try{var t=URL.createObjectURL(new Blob([(null===(e=Ge.toLocaleString().match(/(?:\/\*[\s\S]*?\*\/|\/\/.*?\r?\n|[^{])+\{([\s\S]*)\}$/))||void 0===e?void 0:e[1])||""]));_(d(a),it,new Worker(t),"f"),URL.revokeObjectURL(t),b(d(a),it,"f").onmessage=function(e){var t,r;b(d(a),at,"f")>0&&_(d(a),at,(r=b(d(a),at,"f"),--r),"f");var i=e.data.data;v((o=d(a),u(n.prototype)),"emitSync",o).call(o,Ve.recoder_audio,{s16buffer:i,frameStatus:b(d(a),st,"f")?b(d(a),ft,"f")&&0===b(d(a),at,"f")?x.end:x.intermediate:x.start,fullDuplex:null!==(t=b(d(a),dt,"f"))&&void 0!==t&&t,extend:Object.assign({},T(b(d(a),vt,"f")||{},{}))}),b(d(a),st,"f")||b(d(a),ft,"f")||_(d(a),st,!0,"f")},b(d(a),it,"f").onerror=function(e){b(d(a),yt,"f").call(d(a)),_e.record(K.error,"[audioWorker]",e)}}catch(e){_e.record(K.error,"[prepareAudioWorker]",e)}if(!b(d(a),it,"f"))return Promise.reject(new A(oe.message,oe.code,G.UserMediaError))})),yt.set(d(a),(function(){var e,t;_(d(a),at,0,"f"),null===(t=null===(e=b(d(a),it,"f"))||void 0===e?void 0:e.terminate)||void 0===t||t.call(e),_(d(a),it,void 0,"f")})),mt.set(d(a),(function(){b(d(a),ot,"f")||(_(d(a),ot,b(d(a),$e,"f").createScriptProcessor(4096,1,1),"f"),b(d(a),ot,"f").onaudioprocess=function(e){var t,r=e.inputBuffer.getChannelData(0).slice(0);null===(t=b(d(a),it,"f"))||void 0===t||t.postMessage({audio:r,sampleRate:b(d(a),$e,"f").sampleRate,destSampleRate:b(d(a),Ye,"f").sampleRate||16e3}),_(d(a),at,b(d(a),at,"f")+1,"f")}),b(d(a),Ze,"f")||(_(d(a),Ze,b(d(a),$e,"f").createAnalyser(),"f"),b(d(a),Ze,"f").fftSize=2048,_(d(a),Je,new Uint8Array(b(d(a),Ze,"f").frequencyBinCount),"f"))})),gt.set(d(a),(function(){a.emitSync(Ve.mute)})),wt.set(d(a),(function(){a.emitSync(Ve.unmute)})),bt.set(d(a),(function(){b(d(a),_t,"f").call(d(a)),a.emitSync(Ve.ended),b(d(a),dt,"f")?(a.stopRecord(),a.startRecord(0,b(d(a),ht,"f"),b(d(a),vt,"f")).then((function(){a.emit(Ve.deviceAutoSwitched)})).catch((function(e){a.emitSync(Ve.error,e)}))):(a.stopRecord(),a.emitSync(Ve.error,new A(te.message,te.code,G.UserMediaError)))})),_t.set(d(a),(function(){var e,t,r;null===(e=b(d(a),tt,"f"))||void 0===e||e.removeEventListener("mute",b(d(a),gt,"f")),null===(t=b(d(a),tt,"f"))||void 0===t||t.removeEventListener("unmute",b(d(a),gt,"f")),null===(r=b(d(a),tt,"f"))||void 0===r||r.removeEventListener("ended",b(d(a),gt,"f"))})),xt.set(d(a),(function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return w(d(a),[].concat(t),void 0,(function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,n=arguments.length>1?arguments[1]:void 0,o=arguments.length>2?arguments[2]:void 0;return r().mark((function i(){var a,s,c,u,l;return r().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(s=t<=0,b(e,mt,"f").call(e),b(e,ot,"f")){r.next=5;break}return _e.record(K.warn,"none scriptProcessor"),r.abrupt("return");case 5:return _(e,rt,new Promise((function(t){_(e,nt,{resolve:t},"f")})),"f"),r.next=8,Ot.getUserMedia({audio:{noiseSuppression:!0,echoCancellation:!0,autoGainControl:!0},video:!1});case 8:c=r.sent,_(e,et,c,"f"),(u=_(e,tt,c.getAudioTracks()[0],"f")).addEventListener("mute",b(e,gt,"f")),u.addEventListener("unmute",b(e,wt,"f")),u.addEventListener("ended",b(e,bt,"f")),c.addEventListener("addtrack",(function(){_e.record(K.verbose,"addtrack")})),c.addEventListener("removetrack",(function(){_e.record(K.verbose,"removetrack")})),_(e,st,!1,"f"),_(e,ft,!1,"f"),_(e,Qe,b(e,$e,"f").createMediaStreamSource(c),"f"),l=[],(null===(a=b(e,Ye,"f"))||void 0===a?void 0:a.analyser)&&b(e,Ze,"f")&&l.push(b(e,Ze,"f")),b(e,ot,"f")&&l.push(b(e,ot,"f")),b(e,Ke,"m",kt).call(e,l),_(e,Xe,l,"f"),_(e,ct,!0,"f"),_(e,dt,s,"f"),_(e,vt,T({nlp:!0},o||{}),"f"),_(e,ht,n,"f"),s||_(e,lt,setTimeout((function(){e.stopRecord()}),t||b(e,ut,"f")),"f");case 29:case"end":return r.stop()}}),i)}))()}))})),_(d(a),Ye,Object.assign(Object.assign({},b(d(a),Ye,"f")),t),"f"),a}return c(n,qe),s(n,[{key:"recording",get:function(){return b(this,ct,"f")||!1}},{key:"byteTimeDomainData",get:function(){var e,t;if(b(this,ct,"f")){if(b(this,Ze,"f"))return b(this,Je,"f")||_(this,Je,new Uint8Array((null===(e=b(this,Ze,"f"))||void 0===e?void 0:e.frequencyBinCount)||0),"f"),null===(t=b(this,Ze,"f"))||void 0===t||t.getByteTimeDomainData(b(this,Je,"f")),b(this,Je,"f");_e.record(K.error,"none analyser inited")}}},{key:"startRecord",value:function(e,t,n){return w(this,void 0,void 0,r().mark((function o(){var i,a,s=this;return r().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(!b(this,ct,"f")){r.next=3;break}return _e.record(K.warn,"[recorder]","conflicted recorder start"),r.abrupt("return");case 3:if(window.isSecureContext){r.next=5;break}return r.abrupt("return",Promise.reject(new A(Q.message,Q.code,G.UserMediaError)));case 5:return r.next=7,new Promise((function(e,t){b(s,$e,"f").resume().then(e).catch((function(e){_e.record(K.error,"[resume]",e),t(new A(oe.message,oe.code,G.UserMediaError))})),setTimeout((function(){t(new A(ae.message,ae.code,G.UserMediaError))}),500)}));case 7:return b(this,pt,"f").call(this),r.prev=8,r.next=11,b(this,xt,"f").call(this,e,t,n);case 11:r.next=17;break;case 13:throw r.prev=13,r.t0=r.catch(8),_(this,ct,!1,"f"),r.t0;case 17:return r.prev=17,null===(a=null===(i=b(this,nt,"f"))||void 0===i?void 0:i.resolve)||void 0===a||a.call(i),r.finish(17);case 20:case"end":return r.stop()}}),o,this,[[8,13,17,20]])})))}},{key:"stopRecord",value:function(){var e=this,t=Object.create(null,{emitSync:{get:function(){return v(u(n.prototype),"emitSync",e)}}});return w(this,arguments,void 0,(function(){var e=this,n=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return r().mark((function o(){var i,a,s,c,u,l,f;return r().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,b(e,rt,"f");case 2:if(b(e,ct,"f")){r.next=4;break}return r.abrupt("return");case 4:for(clearTimeout(b(e,lt,"f")),_(e,ct,!1,"f"),b(e,_t,"f").call(e),u=null===(i=b(e,et,"f"))||void 0===i?void 0:i.getAudioTracks(),l=0,f=(null==u?void 0:u.length)||0;l<f;l++)null===(a=null==u?void 0:u[l])||void 0===a||a.stop();_(e,ft,!0,"f"),!0!==n&&0!==b(e,at,"f")||(t.emitSync.call(e,Ve.recoder_audio,{s16buffer:new ArrayBuffer(2),frameStatus:x.end,fullDuplex:null!==(s=b(e,dt,"f"))&&void 0!==s&&s,extend:Object.assign({},T(b(e,vt,"f")||{},{}))}),!0===n&&b(e,yt,"f").call(e));try{b(e,Ke,"m",jt).call(e,b(e,Xe,"f"))}catch(e){_e.record(K.warn,"[disconnect media]",e)}finally{_(e,Xe,[],"f")}null===(c=b(e,ht,"f"))||void 0===c||c.call(e),_(e,ht,void 0,"f");case 14:case"end":return r.stop()}}),o)}))()}))}},{key:"switchDevice",value:function(e){return w(this,void 0,void 0,r().mark((function t(){var n,o;return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(b(this,ot,"f")){t.next=2;break}return t.abrupt("return");case 2:return t.next=4,Ot.getUserMedia({audio:{deviceId:{exact:e},noiseSuppression:!0,echoCancellation:!0},video:!1});case 4:o=t.sent,b(this,Ke,"m",jt).call(this,b(this,Xe,"f")),b(this,_t,"f").call(this),null===(n=b(this,et,"f"))||void 0===n||n.getAudioTracks().forEach((function(e){return e.stop()})),_(this,et,o,"f"),_(this,Qe,b(this,$e,"f").createMediaStreamSource(o),"f"),b(this,Ke,"m",kt).call(this,b(this,Xe,"f"));case 11:case"end":return t.stop()}}),t,this)})))}},{key:"destroy",value:function(){this.stopRecord(),v(u(n.prototype),"destroy",this).call(this)}}],[{key:"getVersion",value:function(){return"3.0.3-1009"}},{key:"setLogLevel",value:function(e){_e.setLogLevel(e)}}]),n}();$e=new WeakMap,Ze=new WeakMap,Je=new WeakMap,Xe=new WeakMap,Ye=new WeakMap,Qe=new WeakMap,et=new WeakMap,tt=new WeakMap,rt=new WeakMap,nt=new WeakMap,ot=new WeakMap,it=new WeakMap,at=new WeakMap,st=new WeakMap,ct=new WeakMap,ut=new WeakMap,lt=new WeakMap,ft=new WeakMap,dt=new WeakMap,vt=new WeakMap,ht=new WeakMap,pt=new WeakMap,yt=new WeakMap,mt=new WeakMap,gt=new WeakMap,wt=new WeakMap,bt=new WeakMap,_t=new WeakMap,xt=new WeakMap,Ke=new WeakSet,kt=function(e){var t,r;if(!e.length)return _(this,Xe,[],"f"),null===(t=b(this,Qe,"f"))||void 0===t?void 0:t.connect(b(this,$e,"f").destination);null===(r=b(this,Qe,"f"))||void 0===r||r.connect(e[0]);for(var n=1;n<e.length;n++)e[n-1].connect(e[n]);e[e.length-1].connect(b(this,$e,"f").destination)},jt=function(e){var t,r;if(!(null==e?void 0:e.length))return null===(t=b(this,Qe,"f"))||void 0===t?void 0:t.disconnect(b(this,$e,"f").destination);null===(r=b(this,Qe,"f"))||void 0===r||r.disconnect(e[0]);for(var n=1;n<e.length;n++)e[n-1].disconnect(e[n]);e[e.length-1].disconnect(b(this,$e,"f").destination)};var St=["src","img","video","link","txt","action","cmd","options","h5_url"];function Et(e){var t=!1;return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e&&"[object String]"===Object.prototype.toString.call(e)?e.replace(/\[={0,1}([a-zA-Z_-])+\d*:?-?\d*\]/g,""):e||""}(e).replace(/(\[打招呼\])|(\[鞠躬\])|(\[左手点赞\])|(\[右手点赞\])|(\[双手比心\])|(\[拜拜\])|(\[看上边摄像头\])|(\[放交通卡\])|(\[左边出口\])|(\[右边出口\])|(\[左上内容-单手\])|(\[左中内容-单手\])|(\[左下内容-单手\])|(\[右上内容-单手\])|(\[右中内容-单手\])|(\[右下内容-单手\])|(\[左上内容-双手\])|(\[左中内容-双手\])|(\[左下内容-双手\])|(\[右上内容-双手\])|(\[右中内容-双手\])|(\[右下内容-双手\])|(\[展开双手\])|(\[聆听点头\])|(\[轻微摇头\])|(\[双手放下\])/g,"").replace(/\[\[(\w+)=(((?!\]\]).)+)\]\]/g,(function(e,t){return-1===St.indexOf(t)?(console.log("ignore tag",e),""):e})).replace(/(\[\[txt=[^\[\]]+\]\])|(\[\[cmd=[^\[\]]+\]\])|(\[\[action=[^\[\]]+\]\])|(\[\[txt=(((?!\]\]).)+)\]\])/g,"").replace(/\[\[link=([^\[\]]+)\]\]/g,(function(e,t){return console.log(e),'<a class="llm-content-link" target="_blank"  href="'.concat(encodeURI(t),'">').concat(t,"</a>")})).replace(/\[\[h5_url=([^\[\]]+)\]\]/g,(function(e,t){return console.log(e),'<div class="llm-content-iframe"><iframe class="content-iframe" src="'.concat(encodeURI(t),'" frameborder="no" border="0" marginwidth="0" marginheight="0" allowtransparency="yes"></iframe></div>')})).replace(/\[\[(src|img)=(((?!\]\]).)+)\]\]/g,(function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";console.log(e,t);var n=r.split(";");r=n[0].replace("ceph.xfyousheng.com","ossbj.xfinfr.com");for(var o={},i=1;i<n.length;i++){var a=h(n[i].split("="),2),s=a[0],c=a[1];o[s]=c}return'<div class="llm-content-img"  style="width:'.concat(o.width||100,"%;\" ><img src='").concat(r,'\' onload="globalImgLoad(this)" onerror="globalImgError(this)"></div>')})).replace(/\[\[video=(((?!\]\]).)+)\]\]/g,(function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=r.split(";");if(1===n.length)return t?"":(t=!0,"<div class=\"llm-content-video\">\n                        <video \n                          onloadstart='imVideoWaiting(this)'\n                          ontimeupdate='imVideoPlaying(this)'\n                          onwaiting='imVideoWaiting(this)'\n                          onended='imVideoEnded(this)' \n                          onerror='imVideoError(this)' \n                          onplay='imVideoPlay(this)' \n                          onplaying='imVideoPlaying(this)' \n                          webkit-playsinline \n                          playsinline \n                          x5-playsinline \n                          autoplay=\"autoplay\"  \n                          preload=\"auto\" \n                          src='".concat(r,'\' \n                          loop=\'loop\'\n                          controls\n                          class="content-video">\n                        </video>\n                        <div class="loading-icon">\n                          <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="60px" height="60px" viewBox="0 0 40 40" enable-background="new 0 0 40 40" xml:space="preserve">\n                            <path opacity="0.2" fill="#FF6700" d="M20.201,5.169c-8.254,0-14.946,6.692-14.946,14.946c0,8.255,6.692,14.946,14.946,14.946\n                                s14.946-6.691,14.946-14.946C35.146,11.861,28.455,5.169,20.201,5.169z M20.201,31.749c-6.425,0-11.634-5.208-11.634-11.634\n                                c0-6.425,5.209-11.634,11.634-11.634c6.425,0,11.633,5.209,11.633,11.634C31.834,26.541,26.626,31.749,20.201,31.749z"></path>\n                            <path fill="#FF6700" d="M26.013,10.047l1.654-2.866c-2.198-1.272-4.743-2.012-7.466-2.012h0v3.312h0\n                                C22.32,8.481,24.301,9.057,26.013,10.047z" transform="rotate(42.1171 20 20)">\n                                <animateTransform attributeType="xml" attributeName="transform" type="rotate" from="0 20 20" to="360 20 20" dur="0.5s" repeatCount="indefinite"></animateTransform>\n                            </path>\n                          </svg>\n                        </div>\n                      </div>'));if(2===n.length){if(t)return"";t=!0;var o=n[1]||"";return o=Number(o.split("=")[1]),r=n[0],1===o?"<div class=\"llm-content-video\">\n                          <video  \n                            onloadstart='imVideoWaiting(this)'\n                            ontimeupdate='imVideoPlaying(this)'\n                            onwaiting='imVideoWaiting(this)'\n                            onended='imVideoEnded(this)' \n                            onerror='imVideoError(this)' \n                            onplay='imVideoPlay(this)' \n                            onplaying='imVideoPlaying(this)' \n                            webkit-playsinline \n                            playsinline \n                            x5-playsinline \n                            autoplay=\"autoplay\"  \n                            preload=\"auto\" \n                            src='".concat(r,'\' \n                            muted \n                            loop=\'loop\'\n                            style="width:100%;" \n                            controls\n                            class="content-video">\n                          </video>\n                          <div class="loading-icon">\n                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="60px" height="60px" viewBox="0 0 40 40" enable-background="new 0 0 40 40" xml:space="preserve">\n                              <path opacity="0.2" fill="#FF6700" d="M20.201,5.169c-8.254,0-14.946,6.692-14.946,14.946c0,8.255,6.692,14.946,14.946,14.946\n                                  s14.946-6.691,14.946-14.946C35.146,11.861,28.455,5.169,20.201,5.169z M20.201,31.749c-6.425,0-11.634-5.208-11.634-11.634\n                                  c0-6.425,5.209-11.634,11.634-11.634c6.425,0,11.633,5.209,11.633,11.634C31.834,26.541,26.626,31.749,20.201,31.749z"></path>\n                              <path fill="#FF6700" d="M26.013,10.047l1.654-2.866c-2.198-1.272-4.743-2.012-7.466-2.012h0v3.312h0\n                                  C22.32,8.481,24.301,9.057,26.013,10.047z" transform="rotate(42.1171 20 20)">\n                                  <animateTransform attributeType="xml" attributeName="transform" type="rotate" from="0 20 20" to="360 20 20" dur="0.5s" repeatCount="indefinite"></animateTransform>\n                              </path>\n                            </svg>\n                          </div>\n                        </div>'):"<div class=\"llm-content-video\">\n                          <video \n                            onloadstart='imVideoWaiting(this)'\n                            ontimeupdate='imVideoPlaying(this)'\n                            onwaiting='imVideoWaiting(this)'\n                            onended='imVideoEnded(this)' \n                            onerror='imVideoError(this)' \n                            onplay='imVideoPlay(this)' \n                            onplaying='imVideoPlaying(this)' \n                            webkit-playsinline \n                            playsinline \n                            x5-playsinline \n                            autoplay=\"autoplay\"  \n                            preload=\"auto\" \n                            src='".concat(r,'\' \n                            loop=\'loop\'\n                            controls\n                            class="content-video">\n                          </video>\n                          <div class="loading-icon">\n                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="60px" height="60px" viewBox="0 0 40 40" enable-background="new 0 0 40 40" xml:space="preserve">\n                              <path opacity="0.2" fill="#FF6700" d="M20.201,5.169c-8.254,0-14.946,6.692-14.946,14.946c0,8.255,6.692,14.946,14.946,14.946\n                                  s14.946-6.691,14.946-14.946C35.146,11.861,28.455,5.169,20.201,5.169z M20.201,31.749c-6.425,0-11.634-5.208-11.634-11.634\n                                  c0-6.425,5.209-11.634,11.634-11.634c6.425,0,11.633,5.209,11.633,11.634C31.834,26.541,26.626,31.749,20.201,31.749z"></path>\n                              <path fill="#FF6700" d="M26.013,10.047l1.654-2.866c-2.198-1.272-4.743-2.012-7.466-2.012h0v3.312h0\n                                  C22.32,8.481,24.301,9.057,26.013,10.047z" transform="rotate(42.1171 20 20)">\n                                  <animateTransform attributeType="xml" attributeName="transform" type="rotate" from="0 20 20" to="360 20 20" dur="0.5s" repeatCount="indefinite"></animateTransform>\n                              </path>\n                            </svg>\n                          </div>\n                        </div>')}return e}))}var At="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function Pt(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function Wt(e){if(e.__esModule)return e;var t=e.default;if("function"==typeof t){var r=function e(){return this instanceof e?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};r.prototype=t.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(e).forEach((function(t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})})),r}var Tt={exports:{}};function It(e){throw new Error('Could not dynamically require "'+e+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var Ct,Lt={exports:{}},Rt=Wt(Object.freeze({__proto__:null,default:{}}));function Ut(){return Ct||(Ct=1,Lt.exports=(e=e||function(e,t){var r;if("undefined"!=typeof window&&window.crypto&&(r=window.crypto),"undefined"!=typeof self&&self.crypto&&(r=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(r=globalThis.crypto),!r&&"undefined"!=typeof window&&window.msCrypto&&(r=window.msCrypto),!r&&void 0!==At&&At.crypto&&(r=At.crypto),!r)try{r=Rt}catch(e){}var n=function(){if(r){if("function"==typeof r.getRandomValues)try{return r.getRandomValues(new Uint32Array(1))[0]}catch(e){}if("function"==typeof r.randomBytes)try{return r.randomBytes(4).readInt32LE()}catch(e){}}throw new Error("Native crypto module could not be used to get secure random number.")},o=Object.create||function(){function e(){}return function(t){var r;return e.prototype=t,r=new e,e.prototype=null,r}}(),i={},a=i.lib={},s=a.Base={extend:function(e){var t=o(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},c=a.WordArray=s.extend({init:function(e,r){e=this.words=e||[],this.sigBytes=r!=t?r:4*e.length},toString:function(e){return(e||l).stringify(this)},concat:function(e){var t=this.words,r=e.words,n=this.sigBytes,o=e.sigBytes;if(this.clamp(),n%4)for(var i=0;i<o;i++){var a=r[i>>>2]>>>24-i%4*8&255;t[n+i>>>2]|=a<<24-(n+i)%4*8}else for(var s=0;s<o;s+=4)t[n+s>>>2]=r[s>>>2];return this.sigBytes+=o,this},clamp:function(){var t=this.words,r=this.sigBytes;t[r>>>2]&=4294967295<<32-r%4*8,t.length=e.ceil(r/4)},clone:function(){var e=s.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],r=0;r<e;r+=4)t.push(n());return new c.init(t,e)}}),u=i.enc={},l=u.Hex={stringify:function(e){for(var t=e.words,r=e.sigBytes,n=[],o=0;o<r;o++){var i=t[o>>>2]>>>24-o%4*8&255;n.push((i>>>4).toString(16)),n.push((15&i).toString(16))}return n.join("")},parse:function(e){for(var t=e.length,r=[],n=0;n<t;n+=2)r[n>>>3]|=parseInt(e.substr(n,2),16)<<24-n%8*4;return new c.init(r,t/2)}},f=u.Latin1={stringify:function(e){for(var t=e.words,r=e.sigBytes,n=[],o=0;o<r;o++){var i=t[o>>>2]>>>24-o%4*8&255;n.push(String.fromCharCode(i))}return n.join("")},parse:function(e){for(var t=e.length,r=[],n=0;n<t;n++)r[n>>>2]|=(255&e.charCodeAt(n))<<24-n%4*8;return new c.init(r,t)}},d=u.Utf8={stringify:function(e){try{return decodeURIComponent(escape(f.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return f.parse(unescape(encodeURIComponent(e)))}},v=a.BufferedBlockAlgorithm=s.extend({reset:function(){this._data=new c.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=d.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var r,n=this._data,o=n.words,i=n.sigBytes,a=this.blockSize,s=i/(4*a),u=(s=t?e.ceil(s):e.max((0|s)-this._minBufferSize,0))*a,l=e.min(4*u,i);if(u){for(var f=0;f<u;f+=a)this._doProcessBlock(o,f);r=o.splice(0,u),n.sigBytes-=l}return new c.init(r,l)},clone:function(){var e=s.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});a.Hasher=v.extend({cfg:s.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){v.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,r){return new e.init(r).finalize(t)}},_createHmacHelper:function(e){return function(t,r){return new h.HMAC.init(e,r).finalize(t)}}});var h=i.algo={};return i}(Math),e)),Lt.exports;var e}var zt,Bt={exports:{}};function Ft(){return zt?Bt.exports:(zt=1,Bt.exports=(e=Ut(),function(t){var r=e,n=r.lib,o=n.WordArray,i=n.Hasher,a=r.algo,s=[],c=[];!function(){function e(e){for(var r=t.sqrt(e),n=2;n<=r;n++)if(!(e%n))return!1;return!0}function r(e){return 4294967296*(e-(0|e))|0}for(var n=2,o=0;o<64;)e(n)&&(o<8&&(s[o]=r(t.pow(n,.5))),c[o]=r(t.pow(n,1/3)),o++),n++}();var u=[],l=a.SHA256=i.extend({_doReset:function(){this._hash=new o.init(s.slice(0))},_doProcessBlock:function(e,t){for(var r=this._hash.words,n=r[0],o=r[1],i=r[2],a=r[3],s=r[4],l=r[5],f=r[6],d=r[7],v=0;v<64;v++){if(v<16)u[v]=0|e[t+v];else{var h=u[v-15],p=(h<<25|h>>>7)^(h<<14|h>>>18)^h>>>3,y=u[v-2],m=(y<<15|y>>>17)^(y<<13|y>>>19)^y>>>10;u[v]=p+u[v-7]+m+u[v-16]}var g=n&o^n&i^o&i,w=(n<<30|n>>>2)^(n<<19|n>>>13)^(n<<10|n>>>22),b=d+((s<<26|s>>>6)^(s<<21|s>>>11)^(s<<7|s>>>25))+(s&l^~s&f)+c[v]+u[v];d=f,f=l,l=s,s=a+b|0,a=i,i=o,o=n,n=b+(w+g)|0}r[0]=r[0]+n|0,r[1]=r[1]+o|0,r[2]=r[2]+i|0,r[3]=r[3]+a|0,r[4]=r[4]+s|0,r[5]=r[5]+l|0,r[6]=r[6]+f|0,r[7]=r[7]+d|0},_doFinalize:function(){var e=this._data,r=e.words,n=8*this._nDataBytes,o=8*e.sigBytes;return r[o>>>5]|=128<<24-o%32,r[14+(o+64>>>9<<4)]=t.floor(n/4294967296),r[15+(o+64>>>9<<4)]=n,e.sigBytes=4*r.length,this._process(),this._hash},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});r.SHA256=i._createHelper(l),r.HmacSHA256=i._createHmacHelper(l)}(Math),e.SHA256));var e}var Dt,Nt,qt={exports:{}};Tt.exports=function(e){return e.HmacSHA256}(Ut(),Ft(),Dt||(Dt=1,qt.exports=(Nt=Ut(),void function(){var e=Nt,t=e.lib.Base,r=e.enc.Utf8;e.algo.HMAC=t.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=r.parse(t));var n=e.blockSize,o=4*n;t.sigBytes>o&&(t=e.finalize(t)),t.clamp();for(var i=this._oKey=t.clone(),a=this._iKey=t.clone(),s=i.words,c=a.words,u=0;u<n;u++)s[u]^=1549556828,c[u]^=909522486;i.sigBytes=a.sigBytes=o,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,r=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(r))}})}())));var Vt=Pt(Tt.exports),Ht={exports:{}};Ht.exports=function(e){return function(){var t=e,r=t.lib.WordArray;function n(e,t,n){for(var o=[],i=0,a=0;a<t;a++)if(a%4){var s=n[e.charCodeAt(a-1)]<<a%4*2|n[e.charCodeAt(a)]>>>6-a%4*2;o[i>>>2]|=s<<24-i%4*8,i++}return r.create(o,i)}t.enc.Base64={stringify:function(e){var t=e.words,r=e.sigBytes,n=this._map;e.clamp();for(var o=[],i=0;i<r;i+=3)for(var a=(t[i>>>2]>>>24-i%4*8&255)<<16|(t[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|t[i+2>>>2]>>>24-(i+2)%4*8&255,s=0;s<4&&i+.75*s<r;s++)o.push(n.charAt(a>>>6*(3-s)&63));var c=n.charAt(64);if(c)for(;o.length%4;)o.push(c);return o.join("")},parse:function(e){var t=e.length,r=this._map,o=this._reverseMap;if(!o){o=this._reverseMap=[];for(var i=0;i<r.length;i++)o[r.charCodeAt(i)]=i}var a=r.charAt(64);if(a){var s=e.indexOf(a);-1!==s&&(t=s)}return n(e,t,o)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),e.enc.Base64}(Ut());var Gt=Pt(Ht.exports),Kt={exports:{}};Kt.exports=function(e){return e.enc.Utf8}(Ut());var $t=Pt(Kt.exports);function Zt(e,t,r){var n=h(e.match(/^wss?:\/\/([^\/]+)(\/.*)/)||[],3);n[0];var o=n[1],i=n[2],a=(new Date).toUTCString(),s="host: ".concat(o,"\ndate: ").concat(a,"\n").concat("GET"," ").concat(i," HTTP/1.1"),c=Vt(s,r),u=Gt.stringify(c),l='api_key="'.concat(t,'", algorithm="').concat("hmac-sha256",'", headers="').concat("host date request-line",'", signature="').concat(u,'"'),f=Gt.stringify($t.parse(l));return"".concat(e,"?authorization=").concat(f,"&date=").concat(a,"&host=").concat(o)}var Jt=function(){this.__data__=[],this.size=0};var Xt=function(e,t){return e===t||e!=e&&t!=t},Yt=Xt;var Qt=function(e,t){for(var r=e.length;r--;)if(Yt(e[r][0],t))return r;return-1},er=Qt,tr=Array.prototype.splice;var rr=Qt;var nr=Qt;var or=Qt;var ir=Jt,ar=function(e){var t=this.__data__,r=er(t,e);return!(r<0)&&(r==t.length-1?t.pop():tr.call(t,r,1),--this.size,!0)},sr=function(e){var t=this.__data__,r=rr(t,e);return r<0?void 0:t[r][1]},cr=function(e){return nr(this.__data__,e)>-1},ur=function(e,t){var r=this.__data__,n=or(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this};function lr(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}lr.prototype.clear=ir,lr.prototype.delete=ar,lr.prototype.get=sr,lr.prototype.has=cr,lr.prototype.set=ur;var fr=lr,dr=fr;var vr=function(){this.__data__=new dr,this.size=0};var hr=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r};var pr=function(e){return this.__data__.get(e)};var yr=function(e){return this.__data__.has(e)},mr="object"==typeof At&&At&&At.Object===Object&&At,gr=mr,wr="object"==typeof self&&self&&self.Object===Object&&self,br=gr||wr||Function("return this")(),_r=br.Symbol,xr=_r,kr=Object.prototype,jr=kr.hasOwnProperty,Or=kr.toString,Mr=xr?xr.toStringTag:void 0;var Sr=function(e){var t=jr.call(e,Mr),r=e[Mr];try{e[Mr]=void 0;var n=!0}catch(e){}var o=Or.call(e);return n&&(t?e[Mr]=r:delete e[Mr]),o},Er=Object.prototype.toString;var Ar=Sr,Pr=function(e){return Er.call(e)},Wr=_r?_r.toStringTag:void 0;var Tr=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Wr&&Wr in Object(e)?Ar(e):Pr(e)};var Ir=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)},Cr=Tr,Lr=Ir;var Rr,Ur=function(e){if(!Lr(e))return!1;var t=Cr(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t},zr=br["__core-js_shared__"],Br=(Rr=/[^.]+$/.exec(zr&&zr.keys&&zr.keys.IE_PROTO||""))?"Symbol(src)_1."+Rr:"";var Fr=function(e){return!!Br&&Br in e},Dr=Function.prototype.toString;var Nr=function(e){if(null!=e){try{return Dr.call(e)}catch(e){}try{return e+""}catch(e){}}return""},qr=Ur,Vr=Fr,Hr=Ir,Gr=Nr,Kr=/^\[object .+?Constructor\]$/,$r=Function.prototype,Zr=Object.prototype,Jr=$r.toString,Xr=Zr.hasOwnProperty,Yr=RegExp("^"+Jr.call(Xr).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");var Qr=function(e){return!(!Hr(e)||Vr(e))&&(qr(e)?Yr:Kr).test(Gr(e))},en=function(e,t){return null==e?void 0:e[t]};var tn=function(e,t){var r=en(e,t);return Qr(r)?r:void 0},rn=tn(br,"Map"),nn=tn(Object,"create"),on=nn;var an=function(){this.__data__=on?on(null):{},this.size=0};var sn=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},cn=nn,un=Object.prototype.hasOwnProperty;var ln=function(e){var t=this.__data__;if(cn){var r=t[e];return"__lodash_hash_undefined__"===r?void 0:r}return un.call(t,e)?t[e]:void 0},fn=nn,dn=Object.prototype.hasOwnProperty;var vn=nn;var hn=an,pn=sn,yn=ln,mn=function(e){var t=this.__data__;return fn?void 0!==t[e]:dn.call(t,e)},gn=function(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=vn&&void 0===t?"__lodash_hash_undefined__":t,this};function wn(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}wn.prototype.clear=hn,wn.prototype.delete=pn,wn.prototype.get=yn,wn.prototype.has=mn,wn.prototype.set=gn;var bn=wn,_n=fr,xn=rn;var kn=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e};var jn=function(e,t){var r=e.__data__;return kn(t)?r["string"==typeof t?"string":"hash"]:r.map},On=jn;var Mn=jn;var Sn=jn;var En=jn;var An=function(){this.size=0,this.__data__={hash:new bn,map:new(xn||_n),string:new bn}},Pn=function(e){var t=On(this,e).delete(e);return this.size-=t?1:0,t},Wn=function(e){return Mn(this,e).get(e)},Tn=function(e){return Sn(this,e).has(e)},In=function(e,t){var r=En(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this};function Cn(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}Cn.prototype.clear=An,Cn.prototype.delete=Pn,Cn.prototype.get=Wn,Cn.prototype.has=Tn,Cn.prototype.set=In;var Ln=fr,Rn=rn,Un=Cn;var zn=fr,Bn=vr,Fn=hr,Dn=pr,Nn=yr,qn=function(e,t){var r=this.__data__;if(r instanceof Ln){var n=r.__data__;if(!Rn||n.length<199)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new Un(n)}return r.set(e,t),this.size=r.size,this};function Vn(e){var t=this.__data__=new zn(e);this.size=t.size}Vn.prototype.clear=Bn,Vn.prototype.delete=Fn,Vn.prototype.get=Dn,Vn.prototype.has=Nn,Vn.prototype.set=qn;var Hn=Vn;var Gn=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n&&!1!==t(e[r],r,e););return e},Kn=tn,$n=function(){try{var e=Kn(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();var Zn=function(e,t,r){"__proto__"==t&&$n?$n(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r},Jn=Zn,Xn=Xt,Yn=Object.prototype.hasOwnProperty;var Qn=function(e,t,r){var n=e[t];Yn.call(e,t)&&Xn(n,r)&&(void 0!==r||t in e)||Jn(e,t,r)},eo=Qn,to=Zn;var ro=function(e,t,r,n){var o=!r;r||(r={});for(var i=-1,a=t.length;++i<a;){var s=t[i],c=n?n(r[s],e[s],s,r,e):void 0;void 0===c&&(c=e[s]),o?to(r,s,c):eo(r,s,c)}return r};var no=function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n};var oo=function(e){return null!=e&&"object"==typeof e},io=Tr,ao=oo;var so=function(e){return ao(e)&&"[object Arguments]"==io(e)},co=oo,uo=Object.prototype,lo=uo.hasOwnProperty,fo=uo.propertyIsEnumerable,vo=so(function(){return arguments}())?so:function(e){return co(e)&&lo.call(e,"callee")&&!fo.call(e,"callee")},ho=Array.isArray,po={exports:{}};var yo=function(){return!1};!function(e,t){var r=br,n=yo,o=t&&!t.nodeType&&t,i=o&&e&&!e.nodeType&&e,a=i&&i.exports===o?r.Buffer:void 0,s=(a?a.isBuffer:void 0)||n;e.exports=s}(po,po.exports);var mo=po.exports,go=/^(?:0|[1-9]\d*)$/;var wo=function(e,t){var r=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==r||"symbol"!=r&&go.test(e))&&e>-1&&e%1==0&&e<t};var bo=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991},_o=Tr,xo=bo,ko=oo,jo={};jo["[object Float32Array]"]=jo["[object Float64Array]"]=jo["[object Int8Array]"]=jo["[object Int16Array]"]=jo["[object Int32Array]"]=jo["[object Uint8Array]"]=jo["[object Uint8ClampedArray]"]=jo["[object Uint16Array]"]=jo["[object Uint32Array]"]=!0,jo["[object Arguments]"]=jo["[object Array]"]=jo["[object ArrayBuffer]"]=jo["[object Boolean]"]=jo["[object DataView]"]=jo["[object Date]"]=jo["[object Error]"]=jo["[object Function]"]=jo["[object Map]"]=jo["[object Number]"]=jo["[object Object]"]=jo["[object RegExp]"]=jo["[object Set]"]=jo["[object String]"]=jo["[object WeakMap]"]=!1;var Oo=function(e){return ko(e)&&xo(e.length)&&!!jo[_o(e)]};var Mo=function(e){return function(t){return e(t)}},So={exports:{}};!function(e,t){var r=mr,n=t&&!t.nodeType&&t,o=n&&e&&!e.nodeType&&e,i=o&&o.exports===n&&r.process,a=function(){try{var e=o&&o.require&&o.require("util").types;return e||i&&i.binding&&i.binding("util")}catch(e){}}();e.exports=a}(So,So.exports);var Eo=So.exports,Ao=Oo,Po=Mo,Wo=Eo&&Eo.isTypedArray,To=Wo?Po(Wo):Ao,Io=no,Co=vo,Lo=ho,Ro=mo,Uo=wo,zo=To,Bo=Object.prototype.hasOwnProperty;var Fo=function(e,t){var r=Lo(e),n=!r&&Co(e),o=!r&&!n&&Ro(e),i=!r&&!n&&!o&&zo(e),a=r||n||o||i,s=a?Io(e.length,String):[],c=s.length;for(var u in e)!t&&!Bo.call(e,u)||a&&("length"==u||o&&("offset"==u||"parent"==u)||i&&("buffer"==u||"byteLength"==u||"byteOffset"==u)||Uo(u,c))||s.push(u);return s},Do=Object.prototype;var No=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||Do)};var qo=function(e,t){return function(r){return e(t(r))}},Vo=qo(Object.keys,Object),Ho=No,Go=Vo,Ko=Object.prototype.hasOwnProperty;var $o=Ur,Zo=bo;var Jo=function(e){return null!=e&&Zo(e.length)&&!$o(e)},Xo=Fo,Yo=function(e){if(!Ho(e))return Go(e);var t=[];for(var r in Object(e))Ko.call(e,r)&&"constructor"!=r&&t.push(r);return t},Qo=Jo;var ei=function(e){return Qo(e)?Xo(e):Yo(e)},ti=ro,ri=ei;var ni=function(e,t){return e&&ti(t,ri(t),e)};var oi=Ir,ii=No,ai=function(e){var t=[];if(null!=e)for(var r in Object(e))t.push(r);return t},si=Object.prototype.hasOwnProperty;var ci=Fo,ui=function(e){if(!oi(e))return ai(e);var t=ii(e),r=[];for(var n in e)("constructor"!=n||!t&&si.call(e,n))&&r.push(n);return r},li=Jo;var fi=function(e){return li(e)?ci(e,!0):ui(e)},di=ro,vi=fi;var hi=function(e,t){return e&&di(t,vi(t),e)},pi={exports:{}};!function(e,t){var r=br,n=t&&!t.nodeType&&t,o=n&&e&&!e.nodeType&&e,i=o&&o.exports===n?r.Buffer:void 0,a=i?i.allocUnsafe:void 0;e.exports=function(e,t){if(t)return e.slice();var r=e.length,n=a?a(r):new e.constructor(r);return e.copy(n),n}}(pi,pi.exports);var yi=pi.exports;var mi=function(e,t){var r=-1,n=e.length;for(t||(t=Array(n));++r<n;)t[r]=e[r];return t};var gi=function(){return[]},wi=function(e,t){for(var r=-1,n=null==e?0:e.length,o=0,i=[];++r<n;){var a=e[r];t(a,r,e)&&(i[o++]=a)}return i},bi=gi,_i=Object.prototype.propertyIsEnumerable,xi=Object.getOwnPropertySymbols,ki=xi?function(e){return null==e?[]:(e=Object(e),wi(xi(e),(function(t){return _i.call(e,t)})))}:bi,ji=ro,Oi=ki;var Mi=function(e,t){return ji(e,Oi(e),t)};var Si=function(e,t){for(var r=-1,n=t.length,o=e.length;++r<n;)e[o+r]=t[r];return e},Ei=qo(Object.getPrototypeOf,Object),Ai=Si,Pi=Ei,Wi=ki,Ti=gi,Ii=Object.getOwnPropertySymbols?function(e){for(var t=[];e;)Ai(t,Wi(e)),e=Pi(e);return t}:Ti,Ci=ro,Li=Ii;var Ri=function(e,t){return Ci(e,Li(e),t)},Ui=Si,zi=ho;var Bi=function(e,t,r){var n=t(e);return zi(e)?n:Ui(n,r(e))},Fi=Bi,Di=ki,Ni=ei;var qi=function(e){return Fi(e,Ni,Di)},Vi=Bi,Hi=Ii,Gi=fi;var Ki=function(e){return Vi(e,Gi,Hi)},$i=tn(br,"DataView"),Zi=rn,Ji=tn(br,"Promise"),Xi=tn(br,"Set"),Yi=tn(br,"WeakMap"),Qi=Tr,ea=Nr,ta="[object Map]",ra="[object Promise]",na="[object Set]",oa="[object WeakMap]",ia="[object DataView]",aa=ea($i),sa=ea(Zi),ca=ea(Ji),ua=ea(Xi),la=ea(Yi),fa=Qi;($i&&fa(new $i(new ArrayBuffer(1)))!=ia||Zi&&fa(new Zi)!=ta||Ji&&fa(Ji.resolve())!=ra||Xi&&fa(new Xi)!=na||Yi&&fa(new Yi)!=oa)&&(fa=function(e){var t=Qi(e),r="[object Object]"==t?e.constructor:void 0,n=r?ea(r):"";if(n)switch(n){case aa:return ia;case sa:return ta;case ca:return ra;case ua:return na;case la:return oa}return t});var da=fa,va=Object.prototype.hasOwnProperty;var ha=function(e){var t=e.length,r=new e.constructor(t);return t&&"string"==typeof e[0]&&va.call(e,"index")&&(r.index=e.index,r.input=e.input),r},pa=br.Uint8Array;var ya=function(e){var t=new e.constructor(e.byteLength);return new pa(t).set(new pa(e)),t},ma=ya;var ga=function(e,t){var r=t?ma(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.byteLength)},wa=/\w*$/;var ba=function(e){var t=new e.constructor(e.source,wa.exec(e));return t.lastIndex=e.lastIndex,t},_a=_r?_r.prototype:void 0,xa=_a?_a.valueOf:void 0;var ka=ya;var ja=ya,Oa=ga,Ma=ba,Sa=function(e){return xa?Object(xa.call(e)):{}},Ea=function(e,t){var r=t?ka(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.length)};var Aa=function(e,t,r){var n=e.constructor;switch(t){case"[object ArrayBuffer]":return ja(e);case"[object Boolean]":case"[object Date]":return new n(+e);case"[object DataView]":return Oa(e,r);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return Ea(e,r);case"[object Map]":case"[object Set]":return new n;case"[object Number]":case"[object String]":return new n(e);case"[object RegExp]":return Ma(e);case"[object Symbol]":return Sa(e)}},Pa=Ir,Wa=Object.create,Ta=function(){function e(){}return function(t){if(!Pa(t))return{};if(Wa)return Wa(t);e.prototype=t;var r=new e;return e.prototype=void 0,r}}(),Ia=Ei,Ca=No;var La=function(e){return"function"!=typeof e.constructor||Ca(e)?{}:Ta(Ia(e))},Ra=da,Ua=oo;var za=function(e){return Ua(e)&&"[object Map]"==Ra(e)},Ba=Mo,Fa=Eo&&Eo.isMap,Da=Fa?Ba(Fa):za,Na=da,qa=oo;var Va=function(e){return qa(e)&&"[object Set]"==Na(e)},Ha=Mo,Ga=Eo&&Eo.isSet,Ka=Ga?Ha(Ga):Va,$a=Hn,Za=Gn,Ja=Qn,Xa=ni,Ya=hi,Qa=yi,es=mi,ts=Mi,rs=Ri,ns=qi,os=Ki,is=da,as=ha,ss=Aa,cs=La,us=ho,ls=mo,fs=Da,ds=Ir,vs=Ka,hs=ei,ps=fi,ys="[object Arguments]",ms="[object Function]",gs="[object Object]",ws={};ws[ys]=ws["[object Array]"]=ws["[object ArrayBuffer]"]=ws["[object DataView]"]=ws["[object Boolean]"]=ws["[object Date]"]=ws["[object Float32Array]"]=ws["[object Float64Array]"]=ws["[object Int8Array]"]=ws["[object Int16Array]"]=ws["[object Int32Array]"]=ws["[object Map]"]=ws["[object Number]"]=ws[gs]=ws["[object RegExp]"]=ws["[object Set]"]=ws["[object String]"]=ws["[object Symbol]"]=ws["[object Uint8Array]"]=ws["[object Uint8ClampedArray]"]=ws["[object Uint16Array]"]=ws["[object Uint32Array]"]=!0,ws["[object Error]"]=ws[ms]=ws["[object WeakMap]"]=!1;var bs=function e(t,r,n,o,i,a){var s,c=1&r,u=2&r,l=4&r;if(n&&(s=i?n(t,o,i,a):n(t)),void 0!==s)return s;if(!ds(t))return t;var f=us(t);if(f){if(s=as(t),!c)return es(t,s)}else{var d=is(t),v=d==ms||"[object GeneratorFunction]"==d;if(ls(t))return Qa(t,c);if(d==gs||d==ys||v&&!i){if(s=u||v?{}:cs(t),!c)return u?rs(t,Ya(s,t)):ts(t,Xa(s,t))}else{if(!ws[d])return i?t:{};s=ss(t,d,c)}}a||(a=new $a);var h=a.get(t);if(h)return h;a.set(t,s),vs(t)?t.forEach((function(o){s.add(e(o,r,n,o,t,a))})):fs(t)&&t.forEach((function(o,i){s.set(i,e(o,r,n,i,t,a))}));var p=f?void 0:(l?u?os:ns:u?ps:hs)(t);return Za(p||t,(function(o,i){p&&(o=t[i=o]),Ja(s,i,e(o,r,n,i,t,a))})),s},_s=bs;var xs,ks,js,Os,Ms,Ss,Es,As,Ps,Ws,Ts,Is,Cs,Ls,Rs,Us,zs,Bs,Fs,Ds,Ns,qs,Vs,Hs,Gs,Ks,$s,Zs,Js,Xs,Ys,Qs,ec=Pt((function(e){return _s(e,5)})),tc=function(t){function n(t){var o;return i(this,n),o=e(this,n),xs.add(d(o)),ks.set(d(o),{useInlinePlayer:!0}),js.set(d(o),k.disconnected),Os.set(d(o),void 0),Ms.set(d(o),void 0),Ss.set(d(o),!1),Es.set(d(o),{appId:"",apiKey:"",apiSecret:"",serverUrl:"wss://avatar.cn-huadong-1.xf-yun.com/v1/interact",sceneId:"",sceneVersion:""}),As.set(d(o),"avatar"),Ps.set(d(o),!1),Ws.set(d(o),{avatar_dispatch:{interactive_mode:O.break},avatar:{avatar_id:"",width:720,height:1280,audio_format:0},stream:{protocol:"xrtc",bitrate:1e6,fps:25,alpha:0},tts:{vcn:"",speed:50,pitch:50,volume:100},air:{air:0,add_nonsemantic:0}}),Ts.set(d(o),void 0),Is.set(d(o),void 0),Cs.set(d(o),void 0),Ls.set(d(o),void 0),Rs.set(d(o),void 0),Us.set(d(o),(function(){return w(d(o),void 0,void 0,r().mark((function e(){var t,n;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!b(this,Ls,"f")){e.next=2;break}return e.abrupt("return",b(this,Ls,"f"));case 2:return e.next=4,null===(t=b(this,Cs,"f"))||void 0===t?void 0:t.websocketPromise;case 4:return n=e.sent,e.abrupt("return",n);case 6:case"end":return e.stop()}}),e,this)})))})),zs.set(d(o),(function(e,t){var r,n,i=e;if("[object String]"!==Object.prototype.toString.call(e)){var a=e;(null==a?void 0:a.header)&&!a.header.request_id&&(a.header.request_id=P()),i=JSON.stringify(a)}t?_e.record(K.debug,"[ws]","[msg send]:ignore record audio data, req_id:",(null===(r=null==e?void 0:e.header)||void 0===r?void 0:r.request_id)||""):_e.record(K.debug,"[ws]","[msg send]",i),null===(n=b(d(o),Ls,"f"))||void 0===n||n.send(i)})),Bs.set(d(o),void 0),Fs.set(d(o),void 0),Ds.set(d(o),(function(e){_(d(o),Bs,e,"f")})),Ns.set(d(o),0),qs.set(d(o),void 0),Vs.set(d(o),(function(){clearTimeout(b(d(o),qs,"f"))})),Hs.set(d(o),(function(){return w(d(o),void 0,void 0,r().mark((function e(){var t,n,o,i,a,s,c,u,l,f;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return _(this,Bs,void 0,"f"),s=(null===(t=b(this,Es,"f"))||void 0===t?void 0:t.signedUrl)||Zt((null===(n=b(this,Es,"f"))||void 0===n?void 0:n.serverUrl)||"",(null===(o=b(this,Es,"f"))||void 0===o?void 0:o.apiKey)||"",(null===(i=b(this,Es,"f"))||void 0===i?void 0:i.apiSecret)||""),_(this,js,k.connecting,"f"),c=ke(s,{binaryData:null!==(a=b(this,ks,"f").binaryData)&&void 0!==a&&a}),u=c.instablishPromise,l=c.abort,_(this,Cs,{websocketPromise:u,abort:l},"f"),e.next=7,u;case 7:return f=e.sent,_(this,Ls,f,"f"),e.abrupt("return",f);case 10:case"end":return e.stop()}}),e,this)})))})),Ks.set(d(o),(function(e){var t,r,n,o,i,a,s,c,u,l,f,d,v=null,h=null;try{v=JSON.parse(e)}catch(e){}var p=!1;if(0!==(null===(t=null==v?void 0:v.header)||void 0===t?void 0:t.code))h={code:null===(r=null==v?void 0:v.header)||void 0===r?void 0:r.code,message:null===(n=null==v?void 0:v.header)||void 0===n?void 0:n.message,sid:(null===(o=null==v?void 0:v.header)||void 0===o?void 0:o.sid)||""},p=!0;else if(null===(i=null==v?void 0:v.payload)||void 0===i?void 0:i.nlp){var y=v.payload.nlp;0!==y.error_code&&(h={code:y.error_code,message:y.error_message,sid:(null===(a=null==v?void 0:v.header)||void 0===a?void 0:a.sid)||"",request_id:(null==y?void 0:y.request_id)||""})}else if(null===(s=null==v?void 0:v.payload)||void 0===s?void 0:s.asr){var m=v.payload.asr;0!==m.error_code&&(h={code:m.error_code,message:m.error_message,sid:(null===(c=null==v?void 0:v.header)||void 0===c?void 0:c.sid)||"",request_id:(null==m?void 0:m.request_id)||""})}else if(null===(u=null==v?void 0:v.payload)||void 0===u?void 0:u.tts){var g=v.payload.tts;0!==g.error_code&&(h={code:g.error_code,message:g.error_message,sid:(null===(l=null==v?void 0:v.header)||void 0===l?void 0:l.sid)||"",request_id:(null==g?void 0:g.request_id)||""})}else if(null===(f=null==v?void 0:v.payload)||void 0===f?void 0:f.avatar){var w=v.payload.avatar;0!==w.error_code&&(h={code:w.error_code||X.code,message:w.error_message||X.message,sid:(null===(d=null==v?void 0:v.header)||void 0===d?void 0:d.sid)||"",request_id:(null==w?void 0:w.request_id)||""})}return{data:v,error:h,is_socket_error:p}})),$s.set(d(o),(function(){return w(d(o),void 0,void 0,r().mark((function e(){var t,n,o=this;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=b(this,xs,"m",Gs).call(this),b(this,zs,"f").call(this,t),!(n=b(this,Ls,"f"))){e.next=5;break}return e.abrupt("return",new Promise((function(e,t){n.onmessage=function(r){var i,a,s,c,u,l,f,d,v,h,p,y,m,g,w=b(o,Ks,"f").call(o,r.data),_=w.data,x=w.error;n.onmessage=null;var k=void 0,j=void 0;if(x)j=new A(null!==(i=x.message)&&void 0!==i?i:X.message,null!==(a=x.code)&&void 0!==a?a:X.code,G.ConnectError);else{_e.record(K.debug,"[stream_url]",null===(c=null===(s=null==_?void 0:_.payload)||void 0===s?void 0:s.avatar)||void 0===c?void 0:c.stream_url);var O=(null===(l=null===(u=null==_?void 0:_.payload)||void 0===u?void 0:u.avatar)||void 0===l?void 0:l.stream_url)||"",M=(null===(f=null==_?void 0:_.header)||void 0===f?void 0:f.sid)||"";O?k={stream_url:O,sid:M,session:(null===(d=null==_?void 0:_.header)||void 0===d?void 0:d.session)||"",appid:(null===(p=null===(h=null===(v=null==_?void 0:_.payload)||void 0===v?void 0:v.avatar)||void 0===h?void 0:h.stream_extend)||void 0===p?void 0:p.appid)||"",user_sign:(null===(g=null===(m=null===(y=null==_?void 0:_.payload)||void 0===y?void 0:y.avatar)||void 0===m?void 0:m.stream_extend)||void 0===g?void 0:g.user_sign)||""}:j=new A(J.message,J.code,G.InvalidResponse)}!j&&k?e(k):(n.onerror=null,t(j),b(o,xs,"m",Qs).call(o,!0))}})));case 5:return e.abrupt("return",Promise.reject(new A(J.message,J.code,G.InvalidResponse)));case 6:case"end":return e.stop()}}),e,this)})))})),Zs.set(d(o),(function(){if(b(d(o),Ls,"f")){b(d(o),Ls,"f").onclose=function(){b(d(o),xs,"m",Qs).call(d(o))};var e=null;b(d(o),Ls,"f").onmessage=function(t){var r,n,i,a,s,c,u,l,f,v,h,p,y=b(d(o),Ks,"f").call(d(o),t.data),m=y.data,g=y.error,w=y.is_socket_error;if(_e.record(K.verbose,"[msg handler]",null===(r=null==m?void 0:m.header)||void 0===r?void 0:r.sid),g)_e.record(K.error,"[error]",g),w||"nlp"===b(d(o),As,"f")&&(null===(n=null==m?void 0:m.payload)||void 0===n?void 0:n.nlp)?(_(d(o),Ps,!1,"f"),b(d(o),Ds,"f").call(d(o),g)):o.emit(je.error,new A(null!==(i=g.message)&&void 0!==i?i:X.message,null!==(a=g.code)&&void 0!==a?a:X.code,G.ConnectError),m);else if(null===(s=null==m?void 0:m.payload)||void 0===s?void 0:s.nlp){var x=null===(c=null==m?void 0:m.payload)||void 0===c?void 0:c.nlp;(null==x?void 0:x.request_id)===(null==e?void 0:e.request_id)&&x.streamNlp?x.content="".concat((null==e?void 0:e.content)||"").concat((null===(u=null==x?void 0:x.answer)||void 0===u?void 0:u.text)||""):x.content=(null===(l=null==x?void 0:x.answer)||void 0===l?void 0:l.text)||"";var k=Object.assign(Object.assign({},x),{displayContent:Et(x.content)});"nlp"!==b(d(o),As,"f")||void 0!==(null==k?void 0:k.status)&&2!==(null==k?void 0:k.status)||_(d(o),Ps,!0,"f"),e=k,o.emit(je.nlp,ec(k),ec(m))}else if(null===(f=null==m?void 0:m.payload)||void 0===f?void 0:f.asr)o.emit(je.asr,null===(v=null==m?void 0:m.payload)||void 0===v?void 0:v.asr);else if(null===(h=null==m?void 0:m.payload)||void 0===h?void 0:h.avatar){var O=null===(p=null==m?void 0:m.payload)||void 0===p?void 0:p.avatar;switch(O.event_type){case"stream_start":o.emit(je.stream_start);break;case"driver_status":O.vmr_status===j.start?(o.emit(je.frame_start,O),clearTimeout(b(d(o),qs,"f"))):O.vmr_status===j.stop&&(o.emit(je.frame_stop,O),b(d(o),Vs,"f").call(d(o)));break;case"tts_duration":o.emit(je.tts_duration,O)}}}}})),Js.set(d(o),(function(){_(d(o),Ns,setInterval((function(){b(d(o),Us,"f").call(d(o)).then((function(){var e;b(d(o),zs,"f").call(d(o),{header:{request_id:P(),app_id:(null===(e=b(d(o),Es,"f"))||void 0===e?void 0:e.appId)||"",ctrl:"ping"}})})).catch((function(e){_e.record(K.error,"[heartbeat error]",e)}))}),4e3),"f")})),Xs.set(d(o),(function(e){var t,r,n,i=b(d(o),Bs,"f");(t=o).emit.apply(t,[je.disconnected].concat(p(e||"nlp"===b(d(o),As,"f")&&("nlp"!==b(d(o),As,"f")||b(d(o),Ps,"f"))?[]:[i?new A(null==i?void 0:i.message,null==i?void 0:i.code,null==i?void 0:i.name,(null==i?void 0:i.sid)||""):new A(Z.message,Z.code,G.NetworkError,(null===(r=b(d(o),Is,"f"))||void 0===r?void 0:r.sid)||"")]))),b(d(o),ks,"f").useInlinePlayer&&(null===(n=b(d(o),Os,"f"))||void 0===n||n.stop())})),Ys.set(d(o),(function(e,t){return w(d(o),void 0,void 0,r().mark((function n(){var o,i,a,s,c,u,l;return r().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(!b(this,ks,"f").useInlinePlayer||!["xrtc","webrtc"].includes(null===(o=b(this,Ws,"f").stream)||void 0===o?void 0:o.protocol)){r.next=14;break}if(_e.record(K.debug,"[player]:useInlinePlayer","inited"),b(this,ks,"f").useInlinePlayer&&this.createPlayer(),b(this,Os,"f")){r.next=5;break}return r.abrupt("return",Promise.reject(new A(Y.MissingPlayerLibsError.message,Y.MissingPlayerLibsError.code,G.MediaError)));case 5:return e.stream_url.startsWith("xrtc")?(l=null===(i=e.stream_url)||void 0===i?void 0:i.match(/^xrtc(s?):\/\/([^/]*)\/([^/]+)/),u={sid:e.sid,server:"http".concat(l[1],"://").concat(l[2]),auth:null!==(s=null===(a=e.user_sign)||void 0===a?void 0:a.replace(/^Bearer /,""))&&void 0!==s?s:"",appid:e.appid,timeStr:"".concat(Date.now()),userId:"c"+l[3],roomId:l[3]},b(this,Os,"f").playerType="xrtc"):(b(this,Os,"f").playerType="webrtc",u={sid:e.sid,streamUrl:e.stream_url}),_e.record(K.debug,"[player]: playerType",b(this,Os,"f").playerType),b(this,Os,"f").videoSize={width:b(this,Ws,"f").avatar.width,height:b(this,Ws,"f").avatar.height},b(this,Os,"f").container=t,_e.record(K.debug,"[player]","preset streamSize:",b(this,Os,"f").videoSize),r.next=12,b(this,Os,"f").playStream(u);case 12:r.next=15;break;case 14:_e.record(K.debug,"[player]: ingore; [inline]/[protocol]",b(this,ks,"f").useInlinePlayer,["xrtc","webrtc"].includes(null===(c=b(this,Ws,"f").stream)||void 0===c?void 0:c.protocol));case 15:case"end":return r.stop()}}),n,this)})))})),_(d(o),ks,Object.assign(Object.assign({},b(d(o),ks,"f")),t),"f"),b(d(o),ks,"f").useInlinePlayer&&o.createPlayer(),o}return c(n,qe),s(n,[{key:"player",get:function(){return b(this,Os,"f")}},{key:"setApiInfo",value:function(e){return _(this,Es,T(b(this,Es,"f"),e),"f"),this}},{key:"setGlobalParams",value:function(e){return _(this,Ws,T(b(this,Ws,"f"),e),"f"),this}},{key:"start",value:function(e){return w(this,void 0,void 0,r().mark((function t(){var n,o,i,a,s,c,u;return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(_(this,Bs,void 0,"f"),_(this,As,"avatar","f"),s=(a=e||{}).wrapper,c=a.preRes,b(this,Es,"f")&&(null===(n=b(this,Ws,"f").avatar)||void 0===n?void 0:n.avatar_id)&&(null===(o=b(this,Ws,"f").tts)||void 0===o?void 0:o.vcn)&&b(this,Ws,"f").avatar.width&&b(this,Ws,"f").avatar.height){t.next=5;break}return t.abrupt("return",Promise.reject(new A($.message,$.code,G.InvalidParam)));case 5:if(void 0!==s||!b(this,ks,"f").useInlinePlayer||!["xrtc","webrtc"].includes(null===(i=b(this,Ws,"f").stream)||void 0===i?void 0:i.protocol)){t.next=7;break}return t.abrupt("return",Promise.reject(new A("播放节点未指定",$.code,G.InvalidParam)));case 7:return b(this,xs,"m",Qs).call(this,!0),t.prev=8,_(this,Ts,c||void 0,"f"),t.next=12,b(this,Hs,"f").call(this);case 12:t.next=19;break;case 14:return t.prev=14,t.t0=t.catch(8),_e.record(K.error,"[ws]:connect failed",(null===t.t0||void 0===t.t0?void 0:t.t0.message)||""),_(this,js,k.disconnected,"f"),t.abrupt("return",Promise.reject(new A((null===t.t0||void 0===t.t0?void 0:t.t0.message)||Z.message,(null===t.t0||void 0===t.t0?void 0:t.t0.code)||Z.code,G.ConnectError)));case 19:return t.prev=19,t.next=22,b(this,$s,"f").call(this);case 22:return u=t.sent,b(this,Zs,"f").call(this),_(this,Is,ec(u),"f"),_(this,js,k.connected,"f"),this.emit(je.connected,u),_e.record(K.debug,"[interact]:success",u),b(this,Js,"f").call(this),b(this,Vs,"f").call(this),t.next=32,b(this,Ys,"f").call(this,u,s);case 32:t.next=39;break;case 34:throw t.prev=34,t.t1=t.catch(19),b(this,xs,"m",Qs).call(this,!0),_(this,js,k.disconnected,"f"),t.t1;case 39:case"end":return t.stop()}}),t,this,[[8,14],[19,34]])})))}},{key:"connectNlp",value:function(){return w(this,void 0,void 0,r().mark((function e(){return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(_(this,As,"nlp","f"),_(this,Ps,!1,"f"),b(this,Es,"f")){e.next=4;break}return e.abrupt("return",Promise.reject(new A($.message,$.code,G.InvalidParam)));case 4:return b(this,xs,"m",Qs).call(this,!0),e.prev=5,e.next=8,b(this,Hs,"f").call(this);case 8:e.next=15;break;case 10:return e.prev=10,e.t0=e.catch(5),_e.record(K.error,"[ws]:connect failed",(null===e.t0||void 0===e.t0?void 0:e.t0.message)||""),_(this,js,k.disconnected,"f"),e.abrupt("return",Promise.reject(new A((null===e.t0||void 0===e.t0?void 0:e.t0.message)||Z.message,(null===e.t0||void 0===e.t0?void 0:e.t0.code)||Z.code,G.ConnectError)));case 15:b(this,Zs,"f").call(this),_(this,js,k.connected,"f"),this.emit(je.connected),_e.record(K.debug,"[interact]:success");case 19:case"end":return e.stop()}}),e,this,[[5,10]])})))}},{key:"interrupt",value:function(){return w(this,void 0,void 0,r().mark((function e(){var t=this;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b(this,Us,"f").call(this).then((function(){var e;b(t,zs,"f").call(t,{header:{app_id:(null===(e=b(t,Es,"f"))||void 0===e?void 0:e.appId)||"",ctrl:"reset"}})}));case 2:case"end":return e.stop()}}),e,this)})))}},{key:"writeText",value:function(e,t){return w(this,void 0,void 0,r().mark((function n(){var o,i,a,s,c,u,l,f,d,v,h,p;return r().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return c=t.request_id,u=t.nlp,l=t.interactive_mode,f=t.air,d=t.paramerter,v=g(t,["request_id","nlp","interactive_mode","air","paramerter"]),c=c||P(),h=null!=f?f:b(this,Ws,"f").air,p={},h&&(p={air:T(b(this,Ws,"f").air,f)}),r.prev=5,r.next=8,b(this,Us,"f").call(this);case 8:r.next=14;break;case 10:return r.prev=10,r.t0=r.catch(5),_e.record(K.error,"[writeText]",r.t0),r.abrupt("return",Promise.reject(new A(Z.message,Z.code,G.InvalidConnect)));case 14:return b(this,zs,"f").call(this,{header:{app_id:(null===(o=b(this,Es,"f"))||void 0===o?void 0:o.appId)||"",request_id:c,ctrl:u?"text_interact":"text_driver",session:(null==t?void 0:t.session)||"",scene_id:(null===(i=b(this,Es,"f"))||void 0===i?void 0:i.sceneId)||"",scene_version:(null===(a=b(this,Es,"f"))||void 0===a?void 0:a.sceneVersion)||""},parameter:Object.assign(Object.assign({avatar_dispatch:{interactive_mode:null!=l?l:null===(s=b(this,Ws,"f").avatar_dispatch)||void 0===s?void 0:s.interactive_mode},tts:Object.assign({},T(b(this,Ws,"f").tts,v))},p),d),payload:{text:{content:e}}}),r.abrupt("return",c);case 16:case"end":return r.stop()}}),n,this,[[5,10]])})))}},{key:"writeJsonText",value:function(e,t,n){return w(this,void 0,void 0,r().mark((function o(){var i,a,s,c,u,l,f,d,v;return r().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return s=t.request_id,c=t.nlp,u=t.interactive_mode,l=t.air,f=g(t,["request_id","nlp","interactive_mode","air"]),s=s||P(),d=null!=l?l:b(this,Ws,"f").air,v={},d&&(v={air:T(b(this,Ws,"f").air,l)}),r.prev=5,r.next=8,b(this,Us,"f").call(this);case 8:r.next=14;break;case 10:return r.prev=10,r.t0=r.catch(5),_e.record(K.error,"[writeJsonText]",r.t0),r.abrupt("return",Promise.reject(new A(Z.message,Z.code,G.InvalidConnect)));case 14:return b(this,zs,"f").call(this,{header:{app_id:(null===(i=b(this,Es,"f"))||void 0===i?void 0:i.appId)||"",request_id:s,ctrl:c?"text_interact":"text_driver"},parameter:Object.assign({avatar_dispatch:{interactive_mode:null!=u?u:null===(a=b(this,Ws,"f").avatar_dispatch)||void 0===a?void 0:a.interactive_mode},tts:Object.assign({},T(b(this,Ws,"f").tts,f))},v),payload:{json_text:{text:e,cmd:n}}}),r.abrupt("return",s);case 16:case"end":return r.stop()}}),o,this,[[5,10]])})))}},{key:"writeAudio",value:function(e,t,n){return w(this,void 0,void 0,r().mark((function o(){var i,a,s,c,u,l,f,d,v,h,p,y,m,w,k,j,O,M,S,E,W,I,C,L;return r().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,b(this,Us,"f").call(this);case 3:r.next=9;break;case 5:return r.prev=5,r.t0=r.catch(0),_e.record(K.error,"[writeAudio]",r.t0),r.abrupt("return",Promise.reject(new A(Z.message,Z.code,G.InvalidConnect)));case 9:return"",u=!1,(t===x.start||!b(this,Ss,"f")&&t===x.end)&&(u=!0,_(this,Rs,P(),"f"),_e.record(K.info,"[writeAudio]","audio is first Frame, reset")),_(this,Rs,c=b(this,Rs,"f")||P(),"f"),t===x.end?_(this,Ss,!1,"f"):_(this,Ss,!0,"f"),f=(l=n||{}).nlp,d=l.audio_mode,v=void 0===d?0:d,h=l.full_duplex,p=l.encoding,y=void 0===p?"raw":p,m=l.channels,w=void 0===m?1:m,k=l.bit_depth,j=void 0===k?16:k,O=l.avatar,M=l.vc,S=l.interactive_mode,E=l.air,W=l.session,I=g(l,["nlp","audio_mode","full_duplex","encoding","channels","bit_depth","avatar","vc","interactive_mode","air","session"]),C=null!=E?E:b(this,Ws,"f").air,L={},C&&(L={air:T(b(this,Ws,"f").air,E)}),b(this,zs,"f").call(this,{header:{app_id:(null===(i=b(this,Es,"f"))||void 0===i?void 0:i.appId)||"",request_id:c,ctrl:f?"audio_interact":"audio_driver",session:W||""},parameter:Object.assign(Object.assign(Object.assign({avatar_dispatch:Object.assign(Object.assign({},!f&&u?{interactive_mode:null!=S?S:null===(a=b(this,Ws,"f").avatar_dispatch)||void 0===a?void 0:a.interactive_mode}:{}),{audio_mode:null!=v?v:0})},f?{asr:{full_duplex:h?1:0}}:{}),(null==M?void 0:M.vc)?{vc:{vc:null!==(s=null==M?void 0:M.vc)&&void 0!==s?s:0,voice_name:(null==M?void 0:M.voice_name)||""}}:{}),L),payload:{audio:Object.assign({encoding:y,channels:null!=w?w:1,bit_depth:null!=j?j:16,status:t,audio:H(new Uint8Array(e)),frame_size:e.byteLength},I),avatar:O||[]}},!0),r.abrupt("return",c);case 20:case"end":return r.stop()}}),o,this,[[0,5]])})))}},{key:"writeCmd",value:function(e,t){return w(this,void 0,void 0,r().mark((function n(){var o,i,a;return r().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return i=P(),r.prev=1,r.next=4,b(this,Us,"f").call(this);case 4:r.next=10;break;case 6:return r.prev=6,r.t0=r.catch(1),_e.record(K.error,"[writeCmd]",r.t0),r.abrupt("return",Promise.reject(new A(Z.message,Z.code,G.InvalidConnect)));case 10:a=null,r.prev=11,r.t1=e,r.next="action"===r.t1?15:17;break;case 15:return a={cmd_text:{avatar:[{type:e,value:t}]}},r.abrupt("break",17);case 17:r.next=21;break;case 19:r.prev=19,r.t2=r.catch(11);case 21:return a&&b(this,zs,"f").call(this,{header:{app_id:(null===(o=b(this,Es,"f"))||void 0===o?void 0:o.appId)||"",request_id:i,ctrl:"cmd"},payload:a}),r.abrupt("return",i);case 23:case"end":return r.stop()}}),n,this,[[1,6],[11,19]])})))}},{key:"recorder",get:function(){return b(this,Ms,"f")}},{key:"createRecorder",value:function(){var e,t=this;if(b(this,Ms,"f"))return b(this,Ms,"f");var r=-1;return _(this,Ms,new Mt({sampleRate:Number(1===(null===(e=b(this,Ws,"f").avatar)||void 0===e?void 0:e.audio_format)?24e3:16e3)}),"f").on(Ve.recoder_audio,(function(e){var n,o;if(b(t,js,"f")===k.connected){var i=e.frameStatus;-1===r&&e.frameStatus!==x.end&&(i=0,r=0),t.writeAudio(e.s16buffer,i,{nlp:null!==(o=null===(n=null==e?void 0:e.extend)||void 0===n?void 0:n.nlp)&&void 0!==o&&o,full_duplex:e.fullDuplex?1:0}).catch((function(e){_e.record(K.error,"[writeAudio]",e)}))}else _e.record(K.info,"[writeAudio]","channel disconnected, ignore audio data")})),b(this,Ms,"f")}},{key:"createPlayer",value:function(){return b(this,Os,"f")?b(this,Os,"f"):_(this,Os,new He,"f")}},{key:"stop",value:function(){b(this,xs,"m",Qs).call(this,!0)}},{key:"destroy",value:function(){var e,t;null===(e=b(this,Os,"f"))||void 0===e||e.destroy(),_(this,Os,void 0,"f"),b(this,xs,"m",Qs).call(this,!0),null===(t=b(this,Ms,"f"))||void 0===t||t.destroy(),_(this,Ms,void 0,"f"),v(u(n.prototype),"destroy",this).call(this)}}],[{key:"getVersion",value:function(){return"3.0.3-1009"}},{key:"setLogLevel",value:function(e){_e.setLogLevel(e)}}]),n}();ks=new WeakMap,js=new WeakMap,Os=new WeakMap,Ms=new WeakMap,Ss=new WeakMap,Es=new WeakMap,As=new WeakMap,Ps=new WeakMap,Ws=new WeakMap,Ts=new WeakMap,Is=new WeakMap,Cs=new WeakMap,Ls=new WeakMap,Rs=new WeakMap,Us=new WeakMap,zs=new WeakMap,Bs=new WeakMap,Fs=new WeakMap,Ds=new WeakMap,Ns=new WeakMap,qs=new WeakMap,Vs=new WeakMap,Hs=new WeakMap,Ks=new WeakMap,$s=new WeakMap,Zs=new WeakMap,Js=new WeakMap,Xs=new WeakMap,Ys=new WeakMap,xs=new WeakSet,Gs=function(){var e,t,r,n,o,i,a=null!==(e=b(this,Ws,"f").stream)&&void 0!==e?e:{},s=a.protocol,c=void 0===s?"xrtc":s,u=a.bitrate,l=void 0===u?1e6:u,f=g(a,["protocol","bitrate"]),d=b(this,Ws,"f").avatar,v=d.avatar_id,h=d.width,p=d.height,y=g(d,["avatar_id","width","height"]),m=b(this,Ws,"f").tts,w=m.vcn,_=m.speed,x=m.pitch,k=m.volume,j=g(m,["vcn","speed","pitch","volume"]),O=b(this,Ws,"f").subtitle||{},M=O.subtitle,S=O.font_color,E=g(O,["subtitle","font_color"]),A=b(this,Ws,"f").background,P=b(this,Ws,"f").air,W={};return P&&(W={air:P}),Object.assign({header:{app_id:(null===(t=b(this,Es,"f"))||void 0===t?void 0:t.appId)||"",ctrl:"start",scene_id:(null===(r=b(this,Es,"f"))||void 0===r?void 0:r.sceneId)||"",scene_version:(null===(n=b(this,Es,"f"))||void 0===n?void 0:n.sceneVersion)||""},parameter:Object.assign(Object.assign({avatar:Object.assign(Object.assign({stream:Object.assign(Object.assign({},f),{protocol:c,bitrate:Math.floor((l||1e6)/1024)}),avatar_id:v,width:h,height:p},y),{audio_format:(null===(i=null===(o=b(this,Ws,"f"))||void 0===o?void 0:o.avatar)||void 0===i?void 0:i.audio_format)||0}),tts:Object.assign({vcn:w,speed:null!=_?_:50,pitch:null!=x?x:50,volume:null!=k?k:100},j)},M?{subtitle:Object.assign({subtitle:M,font_color:null!=S?S:"#FFFFFF"},E)}:{}),W)},(null==A?void 0:A.data)||b(this,Ts,"f")?{payload:Object.assign({background:A},b(this,Ts,"f")?{preload_resources:b(this,Ts,"f")}:{})}:void 0)},Qs=function(){var e,t,r,n,o,i=arguments.length>0&&void 0!==arguments[0]&&arguments[0];_(this,Ss,!1,"f"),clearInterval(b(this,Ns,"f")),clearTimeout(b(this,qs,"f")),i&&_(this,Bs,void 0,"f"),clearTimeout(b(this,Fs,"f")),_(this,Ts,void 0,"f"),_(this,js,k.disconnected,"f");var a=b(this,Ls,"f");null===(e=b(this,Ms,"f"))||void 0===e||e.stopRecord(),null===(t=b(this,Os,"f"))||void 0===t||t.stop(),(null==a?void 0:a.readyState)===xe.OPEN?(a.onclose=null,a.onmessage=null,b(this,zs,"f").call(this,{header:{request_id:P(),app_id:(null===(r=b(this,Es,"f"))||void 0===r?void 0:r.appId)||"",ctrl:"stop"}}),a.close()):(null===(n=b(this,Cs,"f"))||void 0===n||n.abort(),null===(o=null==a?void 0:a.close)||void 0===o||o.call(a)),b(this,Ls,"f")&&(b(this,Xs,"f").call(this,i),_(this,Cs,void 0,"f"),_(this,Ls,void 0,"f"),_(this,Is,void 0,"f"))};export{tc as A,A as C,G as E,_e as L,Oe as P,Ve as R,je as S,Ot as U,c as _,i as a,e as b,d as c,K as d,b as e,_ as f,s as g,w as h,r as i,Y as j,W as k,v as l,u as m,qe as n,o,It as p};
