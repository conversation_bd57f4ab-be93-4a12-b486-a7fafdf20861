<!DOCTYPE html>
<html lang="zh-cmn-<PERSON>">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <title></title>
  <meta name="keywords" content="">
  <meta name="description" content="">
  <link rel="icon">
  <!-- DNS预解析 -->
  <!-- <% if (NODE_ENV==='production' ) { %>
    <link rel="dns-prefetch" href="//<%= LIB_CDN_DOMAIN %>">
    <link rel="dns-prefetch" href="//<%= CDN_DOMAIN %>">
  <%} %> -->

  <!-- 以下三个脚本为阿里云waf防火墙的场景化反爬脚本 -->
  <!-- <script src="//aeu.alicdn.com/waf/antidomxss_v640.js"></script>
  <script src="//aeu.alicdn.com/waf/jquery_221121.min.js"></script>
  <script src="//aeu.alicdn.com/waf/interfaceacting220819.js"></script> -->

  <!-- jd sdk 跳转收银台 -->
  <script src="https://static-ftcms.jd.com/p/files/sdk-1.0.0.js"></script>
  <!-- 添加日志上报代码 -->
  <script type="text/javascript" src="/js/cnzz-mini.1.0.1.js"></script>

  <style>
    :root {
      --colorPrimary: #366AF0;
    }

    body {
      background-color: #F6F6F6;
    }

    #loading-box {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      justify-content: center;
      flex-flow: column;
      align-items: center;
    }

    @-webkit-keyframes loadingRotate {
      0% {
        transform: rotate(0deg) scale(1);
      }

      50% {
        transform: rotate(180deg) scale(0.6);
      }

      100% {
        transform: rotate(360deg) scale(1);
      }
    }

    @keyframes loadingRotate {
      0% {
        transform: rotate(0deg) scale(1);
      }

      50% {
        transform: rotate(180deg) scale(0.6);
      }

      100% {
        transform: rotate(360deg) scale(1);
      }
    }

    .ball-clip-rotate-multiple {
      position: relative;
      width: 50px;
      height: 50px;
    }

    .ball-clip-rotate-multiple>div {
      position: absolute;
      left: 0px;
      top: 0px;
      /* 重复写是为了兼容IE浏览器 */
      border: 2px solid var(--colorPrimary);
      border-bottom-color: #0000;
      border-top-color: #0000;
      border-radius: 100%;
      height: 35px;
      width: 35px;
      -webkit-animation: loadingRotate 1s 0s ease-in-out infinite;
      animation: loadingRotate 1s 0s ease-in-out infinite;
    }

    .ball-clip-rotate-multiple>div:last-child {
      display: inline-block;
      top: 10px;
      left: 10px;
      width: 15px;
      height: 15px;
      -webkit-animation-duration: .5s;
      animation-duration: .5s;
      border-color: var(--colorPrimary) #0000;
      -webkit-animation-direction: reverse;
      animation-direction: reverse;
    }

    #loading-box .loading-text {
      font-weight: 600;
      font-size: 16px;
      line-height: 22px;
      color: var(--colorPrimary);
      width: 100px;
    }

    #loading-box .dot {
      display: inline-block;
      border-radius: 50%;
      width: 2px;
      height: 2px;
      animation: dotting .8s infinite step-start;
    }

    @keyframes dotting {
      25% {
        box-shadow: 2px 0 0 var(--colorPrimary);
      }

      50% {
        box-shadow: 2px 0 0 var(--colorPrimary), 6px 0 0 var(--colorPrimary);
      }

      75% {
        box-shadow: 2px 0 0 var(--colorPrimary), 6px 0 var(--colorPrimary), 10px 0 0 var(--colorPrimary);
      }
    }

    @-webkit-keyframes dotting {
      25% {
        box-shadow: 2px 0 0 var(--colorPrimary);
      }

      50% {
        box-shadow: 2px 0 0 var(--colorPrimary), 6px 0 0 var(--colorPrimary);
      }

      75% {
        box-shadow: 2px 0 0 var(--colorPrimary), 6px 0 var(--colorPrimary), 10px 0 0 var(--colorPrimary);
      }
    }
  </style>
</head>

<body>
  <noscript>
    <strong>很抱歉，当前网站内容在您的浏览器上不能正常呈现，请检查是否已启用 JavaScript。</strong>
  </noscript>
  <div id="app">
    <div id="loading-box">
      <div class="ball-clip-rotate-multiple">
        <div></div>
        <div></div>
      </div>
      <div class="loading-text">正在加载中<span class="dot"></span></div>
    </div>
  </div>

  <!-- 浏览器兼容性判断 -->
  <div id="bwarndiv"
    style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0;text-align: center;overflow: scroll;line-height: 0;">
  </div>
  <script>
    // 生产环境的 不是客户端环境直接重定向到下载页面
    // 以下仅针对 ie9 及以下版本做处理
    var isIE = navigator.appName === 'Microsoft Internet Explorer' // 判断是否是 ie
    try {
      // 获取 ie 版本号，ie 11 或非ie浏览器会报错走 catch
      var IEVersion = Number(navigator.appVersion.match(/MSIE (\S*);/)[1])
      if (isIE && IEVersion < 10) {
        document.body.style.overflow = 'hidden'
        document.getElementById("bwarndiv").style.display = 'block'
        document.getElementById("app").style.display = 'none'
        // 插入图片
        var img = document.createElement('img')
        img.src = '<%= BASE_URL %>imgs/bns.png'
        img.alt = '不支持的浏览器'
        img.style.width = '100%'
        document.getElementById("bwarndiv").appendChild(img)
        setTimeout(function () {
          alert('很抱歉，当前网站内容在您的浏览器上不能正常呈现，请更换浏览器为非IE且为非IE内核浏览器！')
        }, 0)
      }
    } catch (e) { /* ie11 或不是 ie */ }
      // 以下仅针对 ie9 及以下版本做处理
  </script>

  <!-- 承接 -->
  <script src="//at.alicdn.com/t/c/font_4472478_7ylzu5bbhem.js" async></script>
  <!-- built files will be auto injected -->
</body>
</html>
