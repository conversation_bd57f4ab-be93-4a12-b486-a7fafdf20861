/* eslint-disable */
!function(t,e){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=t.document?e(t):function(t){if(!t.document)throw new Error("initNECaptchaWithFallback requires a window with a document");return e(t)}:"function"==typeof define&&define.amd?define("initNECaptchaWithFallback",[],function(){return e(t)}):t.initNECaptchaWithFallback=e(t)}("undefined"!=typeof window?window:this,function(t){"use strict";var e=0,r="QjGAuvoHrcpuxlbw7cp4WnIbbjzG4rtSlpc7EDovNHQS._ujzPZpeCInSxIT4WunuDDh8dRZYF2GbBGWyHlC6q5uEi9x-TXT9j7J705vSsBXyTar7aqFYyUltKYJ7f4Y2TXm_1Mn6HFkb4M7URQ_rWtpxQ5D6hCgNJYC0HpRE7.2sttqYKLoi7yP1KHzK-PptdHHkVwb77cwS2EJW7Mj_PsOtnPBubTmTZLpnRECJR99dWTVC11xYG0sx8dJNLUxUFxEyzTfX4nSmQz_T5sXATRKHtVAz7nmV0De5unmflfAlUwMGKlCT1khBtewlgN5nHvyxeD8Z1_fPVzi9oznl-sbegj6lKfCWezmLcwft8.4yaVh6SlzXJq-FnSK.euq9OBd5jYc82ge2_hEca1fGU--SkPRzgwkzew4O4qjdS2utdPwFONnhKAIMJRPUmCV4lPHG1OeRDvyNV8sCnuFMw7leasxIhPoycl4pm5bNy70Z1laozEGJgItVNr3",n={"zh-CN":"前方拥堵，已自动跳过验证",en:"captcha error，Verified automatically"},o=6e4,a=502,i={};function l(t,e,r){if(e=e||"",r=r||"",(t=t||"")&&(t=t.replace(/:?\/{0,2}$/,"://")),e){var n=e.match(/^([-0-9a-zA-Z.:]*)(\/.*)?/);e=n[1],r=(n[2]||"")+"/"+r}return!e&&(t=""),t+e+r}function c(t,e){if(void 0!==e){var r=t.nodeType;1!==r&&11!==r&&9!==r||("string"==typeof t.textContent?t.textContent=e:t.innerText=e)}}function u(e){var r,n=function(){if(Object.assign)return Object.assign.apply(null,arguments);for(var t={},e=1;e<arguments.length;e++){var r=arguments[e];if(null!=r)for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}({},{protocol:"http"===t.location.protocol.replace(":","")?"http":"https",lang:"zh-CN",errorFallbackCount:3},e),o=n.errorFallbackCount;return function(t,e){if(!t)throw new Error("[NECaptcha] "+e)}(void 0===o||(r=o,(Number.isInteger?Number.isInteger(r):"number"==typeof r&&isFinite(r)&&Math.floor(r)===r)&&o>=1),"errorFallbackCount must be an integer, and it's value greater than or equal one"),n}function s(e,r){if(t.initNECaptcha)setTimeout(function(){r(null)},0);else{var n=function(t){var r,n=[];if(r=t,Array.isArray?Array.isArray(r):"[object Array]"===Object.prototype.toString.call(r))for(var o=0,a=t.length;o<a;o++)n.push(l(e.protocol,t[o],"load.min.js"));else{var i=l(e.protocol,t,"load.min.js");n=[i,i]}return n}(e.staticServer||["cstaticdun.126.net","cstaticdun1.126.net","cstatic.dun.163yun.com"]);!function e(a){var i,l=n[a]+"?v="+(i=(i=o)||0===i?1:i,parseInt((new Date).valueOf()/i,10));!function(t,e){var r=document.head||document.getElementsByTagName("head")[0],n=document.createElement("script");e=e||function(){},n.type="text/javascript",n.charset="utf8",n.async=!0,n.src=t,"onload"in n||(n.onreadystatechange=function(){"complete"!==this.readyState&&"loaded"!==this.readyState||(this.onreadystatechange=null,e(null,n))}),n.onload=function(){this.onerror=this.onload=null,e(null,n)},n.onerror=function(){this.onerror=this.onload=null,e(new Error("Failed to load "+this.src),n)},r.appendChild(n)}(l,function(o){return o||!t.initNECaptcha?(a+=1)===n.length?r(new Error("Failed to load script("+l+")."+(o?o.message:"unreliable script"))):e(a):r(null)})}(0)}}return function(o,l,f){var d=null,p=u(o),h=!1!==p.defaultFallback,m=n["zh-CN"===p.lang?p.lang:"en"],y=t.location.pathname+"_"+p.captchaId+"_NECAPTCHA_ERROR_COUNTS";try{e=parseInt(localStorage.getItem(y)||0,10)}catch(t){}var g=h?function(t){!function t(e){if(e&&(t(e._captchaIns),e.$el)){var r=function(t,e){if((e=e||document).querySelectorAll)return e.querySelectorAll(t);if(!/^\.[^.]+$/.test(t))return[];if(e.getElementsByClassName)return e.getElementsByClassName(t);for(var r,n=e.getElementsByTagName("*"),o=[],a=t.slice(1),i=0,l=n.length;i<l;i++)~(" "+(r=n[i]).className+" ").indexOf(" "+a+" ")&&o.push(r);return o}(".yidun-fallback__tip",e.$el);r.length&&setTimeout(function(){for(var t=0,e=r.length;t<e;t++)c(r[t],m)},0)}}(d),p.onVerify&&p.onVerify(null,{validate:t})}:p.onFallback||function(){},v=!h&&!p.onFallback,E=function(t){if(++e<p.errorFallbackCount){try{localStorage.setItem(y,e)}catch(t){}f(t)}else g(r),b(),v&&f(t)},b=function(){e=0;try{localStorage.setItem(y,0)}catch(t){}},C=function(t){N&&N.isError()?N.resetError():(N&&N.resetTimer(),v?f(t):E(t))};p.onError=function(t){N&&N.isError()&&N.resetError(),E(t)},p.onDidRefresh=function(){N&&N.isError()&&N.resetError(),b()};var N=o.initTimeoutError?o.initTimeoutError(E):null,w=function(){t.initNECaptcha(p,function(t){N&&N.isError()||(N&&N.resetTimer(),d=t,l&&l(t))},C)},T="load-queue";i[T]||(i[T]={rejects:[],resolves:[],status:"error"}),"error"===i[T].status?(i[T].status="pending",s(p,function(t){if(t){var e=new Error;e.code=a,e.message=p.staticServer+"/load.min.js error";for(var r=i[T].rejects,n=0,o=r.length;n<o;n++)r.pop()(e);i[T].status="error"}else{i[T].status="done";for(var l=i[T].resolves,c=0,u=l.length;c<u;c++)l.pop()()}})):"done"===i[T].status&&w(),"pending"===i[T].status&&(i[T].rejects.push(function(t){C(t)}),i[T].resolves.push(w))}});