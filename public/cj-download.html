<!DOCTYPE html>
<html lang="zh-cmn-<PERSON>">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <title>承接ERP客户端下载页面</title>
  <link rel="icon" href="./favicon1.ico">
  <script src="./js/axios.min.js"></script>

  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    html,
    body {
      height: 100%;
      background-color: #fafafc;
    }

    .cj-backgound {
      height: 100%;
      min-width: 1268px;
      position: relative;
    }

    .cj-download {
      background-image: url('https://oss.chengjie.red/web/imgs/download/cj-bg.png');
      background-position: center bottom;
      background-repeat: no-repeat;
      background-size: cover;
      height: 650px;
      /* background-color: linear-gradient(180deg, #fcfafb 0%, rgb(250 250 252 / 0%) 49%); */
     
    }

    .cj-head {
      padding: 27px 0;
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }

    .cj-head img{
      width: 162px;
    }

    .cj-head .split-line {
      display: inline-block;
      width: 1px;
      height: 28px;
      margin: 0 24px;
      background: #B3B3B3;
    }
    .cj-head-txt {
      font-size: 20px;
      color: #181A1D;
    }

    .cj-content {
      margin: 0 auto;
      width: 1268px;
      height: 100%;
    }

    .cj-content .title {
      font-size: 48px;
      font-weight: 500;
      color: #181A1D;
    }

    .cj-content .text {
      font-size: 24px;
      color: #181A1D;
      margin-top: 24px;
    }

    .cj-content .text ul {
      margin-left: 20px;
    }

    .cj-text {
      text-align: center;
      padding: 64px 0;
    }

    .btn {
      width: 370px;
      height: 84px;
      cursor: pointer;
    }

    .cj-down-btns {
      display: flex;
    }

    .cj-down-item {
      cursor: pointer;
      flex: 1;
      font-size: 16px;
      padding: 36px 0;
      text-align: center;
      border-radius: 8px;
      background: #FFFFFF;
    }

    .cj-down-item  {
      transition: all 0.3s ease;
    }

    .cj-down-item:hover {
      box-shadow: 0px 4px 20px 0px rgba(158, 162, 173, 0.3);
    }

    .cj-down-item:hover #icon-imgs {
      content: url('https://oss.chengjie.red/web/imgs/download/mac-m.png');
    }

    .cj-down-item:hover #win-txt::before {
      content: '点击下载 ';
    }
    .cj-down-item:hover #mac-txt::before {
      content: '点击下载 ';
    }
    .cj-down-item:hover #mac-m1-txt::before {
      content: '点击下载 ';
    }


    .win {
      margin-right: 16px;
    }

    .mac {
      margin-right: 16px;
    }


    .cj-down-item img {
      margin-bottom: 24px;
      width: 56px;
    }

    .cj-profile-box {
      text-align: center;
      padding-top: 64px;
      font-size: 28px; 
      color: #181A1D;
      font-weight: 500;
    }
    .cj-profile {
      color: #2D5CF7;
    }
    .cj-dot {
      display: inline-block;
      width: 5px;
      height: 5px;
      margin: 0 8px 8px 8px;
      background: #181A1D;
    }

    .cj-profile-box .txt {
      font-size: 16px;
      color: #7E848D;
    }

    .cj-profile-content {
      text-align: center;
      font-size: 16px;
      line-height: 26px;
      font-weight: 400;
      padding-bottom: 48px;
    } 

    .cj-profile-content img {
      width: 64px;
      margin: 48px 0 24px 0;
      margin-bottom: 24px;
    }

    .cj-bottom-box {
      display: flex;
    }

    .m-r24 {
      margin-right: 24px;
    }
    .p-b24 {
      padding-bottom: 24px;
    }


    .cj-bottom-box .item {
      background: #FFFFFF;
      flex: 1;
      font-size: 16px;
      text-align: center;
      padding: 40px;
      color: #7E848D;
      border-top: 1px solid #2D5CF7;
    }

    .cj-bottom-box .item img {
      width: 48px;
      margin-bottom: 24px;
    }

    .cj-bottom-box .item .title {
      font-size: 28px;
      font-weight: 500;
      padding-bottom: 4px;
    }

    .cj-bottom-box .item .txt {
      text-align: start;
    }

    .cj-author {
      padding: 40px;
      text-align: center;
      font-size: 14px;
      color: #969696;
    }


  </style>
</head>

<body>
  <div class="cj-backgound">
    <div class="cj-download">
      <div class="cj-content">
        <div class="cj-head">
          <img src="https://oss.chengjie.red/web/imgs/download/cj-logo.png" alt="">
          <span class="split-line"></span>
          <span class="cj-head-txt">客户端下载</span>
        </div>
        <div class="cj-text">
          <div class="title">下载 承接ERP 客户端</div>
          <div class="text">
            企业财资管理服务软件
          </div>
        </div>
        <div class="cj-down-btns">
          <div class="cj-down-item win"  onclick="onDownload(event, `承接ERP-${winVersion}.exe`)">
            <img id="icon-imgs" src="https://oss.chengjie.red/web/imgs/download/win.png" alt="">
            <div id="win-txt">Windows 客户端</div>
          </div>
          <div class="cj-down-item mac" onclick="onDownload(event, `承接ERP-x64-${macVersion}.dmg`)">
            <img id="icon-imgs" src="https://oss.chengjie.red/web/imgs/download/macOS.png" alt="">
            <div id="mac-txt">macOS 客户端（Inter芯片版本）</div>
          </div>
           <div class="cj-down-item mac-m1" onclick="onDownload(event, `承接ERP-arm64-${macVersion}.dmg`)">
            <img id="icon-imgs" src="https://oss.chengjie.red/web/imgs/download/macOS.png" alt="">
            <div id="mac-m1-txt">macOS 客户端（M芯片版本）</div>
          </div>
        </div>

        <div class="cj-profile-box">
          <div><span class="cj-name">承接ERP</span><span class="cj-dot"></span><span class="cj-profile">简介</span></div>
          <div class="txt">Company introduction</div>
        </div>

        <div class="cj-profile-content">
          <img src="https://oss.chengjie.red/web/imgs/download/logo.png" alt="">
          <div>海南海成源网络科技有限责任公司，成立于2024年，位于海南省海口市，是一家以从事软件和信息技术服务业为主的企业。</div>
          <div>企业注册资本 <span style="font-weight: 600;">300万</span> 元人民币，团队人员主要来自业内顶尖的科技公司和研究机构，拥有各类科技创新型人才。</div>
        </div>

        <div class="cj-bottom-box">
          <div class="item m-r24">
            <img src="https://oss.chengjie.red/web/imgs/download/icon1.png" alt="">
            <div class="title"><span class="cj-name">承接ERP</span><span class="cj-dot"></span><span class="cj-profile">主营业务</span></div>
            <div class="p-b24">Main business</div>
            <div class="txt p-b24">
              在国家数字经济发展战略下，运用大数据、区块链、人工智能等先进科技手段，以线上化、场景化、数据化的方式，有效解决小微企业融资难、融资贵、融资效率等难题。
            </div>
            <div class="txt">同时，为企业提供票据融资、商业保理、政策解读、市场营销、经营管理财税培训、法律科普、风险防范等企业服务，通过统筹规划、聚集各类中小企业优质服务资源，逐步形成功能完善的中小企业服务链，为广大中小企业提供全方位一体化专业服务</div>
          </div>
          <div class="item">
            <img src="https://oss.chengjie.red/web/imgs/download/icon2.png" alt="">
            <div class="title"><span class="cj-name">承接ERP</span><span class="cj-dot"></span><span class="cj-profile">经营范围</span></div>
            <div class="p-b24">Business scope</div>
            <div class="txt">
              许可项目：第二类增值电信业务（依法须经批准的项目，经相关部门批准后方可开展经营活动）一般项目：软件开发；网络技术服务；大数据服务；技术服务、技术开发、技术咨询、技术交流、技术转让、技术推广；信息技术咨询服务；供应链管理服务；互联网数据服务；信息咨询服务（不含许可类信息咨询服务）；数据处理和存储支持服务；信息系统集成服务；软件销售；互联网销售（除销售需要许可的商品）；计算机软硬件及辅助设备零售；市场营销策划；市场调查（不含涉外调查）（除许可业务外，可自主依法经营法律法规非禁止或限制的项目）。
            </div>
          </div>
        </div>

        <div class="cj-author">
          开发者：海南海成源网络科技有限责任公司
        </div>
    </div>
  </div>
  <script>
    var ossPath = 'https://oss.chengjie.red/app'  // 正式环境的
    var host = window.location.host
    // TODO 上线前要优化
    var domanList = {
      'localhost:8080': 'https://www.chengjie.red',
      'boe.chengjie.red': 'https://boe.chengjie.red',
    }

    console.log(domanList[host]);
    if (domanList[host] === 'https://boe.chengjie.red') { //  正式环境
      ossPath += '/prod'
    }

    // if (domanList[host] === 'https://staging-api.lianwuxian.cn') { //  预发环境
    //   ossPath += '-staging'
    // }
    var winVersion = '0.0.1'
    var macVersion = '0.0.1'
    var isIE = !!window.ActiveXObject || "ActiveXObject" in window // 判断是否是 ie
    // 获取window版本号
    function getCurrentWindowsVersion() {
      axios.get(`${ossPath}/latest.yml?v=${Date.now()}`).then(({ data }) => {
        let version = String(data).match(/^version:\s?(\d+\.\d+\.\d+)/)
        winVersion = version[1] || '0.0.1'
      })
    }

    // 获取Mac版本号
    function getCurrentMacVersion() {
      axios.get(`${ossPath}/latest-mac.yml?v=${Date.now()}`).then(({ data }) => {
        let version = String(data).match(/^version:\s?(\d+\.\d+\.\d+)/)
        macVersion = version[1] || '0.0.1'
      })
    }

    //设置下载文案
    function changeText(element, newText) {
      const textElement = element.querySelector('div');
      textElement.textContent = newText;
    }

    // 判断是不是微信环境打开
    function isWechat() {
      //获取user-agaent标识头
      var ua = window.navigator.userAgent.toLowerCase();
      //判断ua和微信浏览器的标识头是否匹配
      console.log(ua);
      return ua.match(/micromessenger/i) == 'micromessenger'
    }

    // 判断是不是PC端打开
    function IsPC() {
      let userAgentInfo = navigator.userAgent;
      let Agents = ['Android', 'iPhone', 'SymbianOS', 'Windows Phone', 'iPad', 'iPod'];
      let getArr = Agents.filter(i => userAgentInfo.includes(i));
      return getArr.length ? false : true;
    }

    // 模拟a标签执行下载任务
    function downloadClick(url, name) {
      let aDom = document.createElement('a');
      aDom.style.display = 'none';
      aDom.href = url;
      aDom.setAttribute('download', name);
      document.body.appendChild(aDom);
      aDom.click();
      document.body.removeChild(aDom);
    }

    if (isWechat() || !IsPC()) {
      alert("当前不支持移动端或者在微信中下载，请用PC端浏览器下载！")
    } else if (isIE) {
      alert("当前不支持IE浏览器下载，请改用其他浏览器，推荐使用谷歌浏览器下载安装包！")
    } else {
      getCurrentWindowsVersion()
      getCurrentMacVersion()
    }

    // 默认是测试环境的
    var baseUrl = domanList[host] || "https://api.lianwuxian.cn"
    console.log(baseUrl);
    // 执行后端下载操作
    function onDownload(e, fileName) {
      e.preventDefault()
      if (isWechat() || !IsPC()) {
        return alert("当前不支持移动端或者在微信中下载，请用PC端浏览器下载！")
      }
      if (isIE) {
        return alert("当前不支持IE浏览器下载，请改用其他浏览器，推荐使用谷歌浏览器下载安装包！")
      }
      var authCode = ""
      let href = `${ossPath}/${fileName}`
      downloadClick(href, fileName)
      // var authCode = prompt("请输入下载授权码", "")
      // if (authCode) {
        // axios.get(`${baseUrl}/api/broker/noauth/qs/checkQqlDownload?fileName=${fileName}&authCode=${authCode}`).then(({ data }) => {
        //   if (data.code === 200) {
        //     const href = `${baseUrl}/api/broker/noauth/qs/qqlDownload?fileName=${fileName}&authCode=${authCode}`
        //     downloadClick(href, fileName)
        //   } else {
        //     alert(data.msg)
        //   }
        // })
      // }
    }

    // 跳转到最下面
    // function toLoadBtn() {
    //   if (isWechat() || !IsPC()) {
    //     alert("当前不支持移动端或者在微信中下载，请用PC端浏览器下载！")
    //   } else if (isIE) {
    //     alert("当前不支持IE浏览器下载，请改用其他浏览器，推荐使用谷歌浏览器下载安装包！")
    //   } else {
    //     document.querySelector('#bottom-btn').scrollIntoView({
    //       behavior: 'smooth', // 平滑过渡
    //       block: 'start' // 上边框与视窗顶部平齐。默认值
    //     })
    //   }
    // }
  </script>
</body>

</html>