body,
div,
span,
header,
footer,
nav,
section,
aside,
article,
th,
td,
ul,
dl,
dt,
dd,
li,
a,
p,
h1,
h2,
h3,
h4,
h5,
h6,
i,
b,
textarea,
button,
input,
select,
figure,
figcaption {
  margin: 0;
  border: none;
  padding: 0;
  /* stylelint-disable-next-line max-line-length */
  font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  text-decoration: none;
  list-style: none;
  font-style: normal;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  -webkit-font-smoothing: antialiased;
}

img {
  border-style: none;
  font-size: 0;
}

body {
  padding: 20px 24px;
  font-size: 12pt;
}

table {
  width: 100% !important;
}

/* 滚动条整体部分 */
::-webkit-scrollbar {
  background: #f2f2f2;
  height: 6px;
  width: 6px;
}

::-webkit-scrollbar-track-piece {
  background-color: #fff;
}

::-webkit-scrollbar-thumb,
::-webkit-scrollbar-thumb:vertical {
  background-color: #b4bbc5;
  -webkit-border-radius: 10px;
  height: 6px;
}

::-webkit-scrollbar-thumb:hover,
::-webkit-scrollbar-thumb:vertical:hover {
  background-color: #909090;
}

#translate {
  display: none!important;
}
