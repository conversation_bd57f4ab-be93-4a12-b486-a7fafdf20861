<!DOCTYPE html>
<html lang="zh-cmn-Hans">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <title>小微企业简易开户</title>
    <!-- 设置更目录字体  放在最前面 避免页面闪动 -->
    <script>
      const zoom = window.screen.width / 375; // 设计稿和屏幕的比例
      const size = zoom * 100;
      document.documentElement.style.fontSize = size + "px";
    </script>
    <link rel="stylesheet" href="../css/zb-standard-account.css" />
    <style>
      body {
        margin: 0;
        font-size: 0.14rem;
        color: #666666;
      }

      div {
        box-sizing: border-box;
      }

      img ,input{
        display: block;
      }

      span {
        display: inline;
      }
    </style>
  </head>
    <!-- 当前页面地址示例： https://dev.lianwuxian.cn/h5/pages/zb-standard-account.html?env=dev&employeeId= -->
  </head>
  <body>
    <div class="zb-standard-account">
      <div class="f-c">  
        <img
          class="logo"
          src="https://oss.chengjie.red/web/imgs/new_erp/images/h5/login/logo.png"
        />
      </div>
      <div class="warp">
        <div class="g-title">小微企业简易开户</div>
        <div class="content">
           <div class="form">
            <div class="label"><span class="require">*</span>企业名称</div>
            <input type="text" id="corpName" placeholder="请输入企业名称" />
            <div class="major">银行在您报名成功后会与您联系，请务必确保信息准确!</div>
           </div>
         <div class="f-c">
          <button onclick="toZb()">立即报名</button>
         </div>

        </div>
      </div>
    </div>

    <script src="../js/utils.js"></script>
    <script src="../js/jsencrypt.js"></script>

    <script>
      var publicKey =
        "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCB7EZgG1Qiy4aEqSe1zpOJoKHoyB5QrME3iGIkc8k7V5v/7wN0kixm4PP7L6gPy71U0GHzEZewmtLOyj3SiCwh8DFcvdZxM8Q3FLKeUUt/w8HO8LRhUZ8box1+t1Kz8aziowvmHq4rY+ws11uP/q/Z1ezw7mJsGlQ8JiDXR19EawIDAQAB"; // 加密用的公钥 生产和测试目前是保持一致的
      // 通过浏览器地址参数env判断是生产还是测试 只要不是生产 都是测试的
      var host =
        getUrlCode().env === "prod"
          ? "https://cams.z-bank.com"
          : "https://cams-test.z-bank.com";
      // 加密
      function encrypt(txt) {
        const encryptor = new JSEncrypt();
        encryptor.setPublicKey(publicKey); // 设置公钥
        return encryptor.encrypt(txt); // 对数据进行加密
      }

      function toZb() {
        var corpName = document.getElementById("corpName").value;
        var employeeId = getUrlCode().employeeId;
        if (!corpName) return Toast("请输入企业名称");
        if (!employeeId) return Toast("访问链接有误，请联系客户经理！");
        ajax("/api/platform/operation/zbank/yiyuan/applyOpenAccount", "post", {
          corpName,
          employeeId,
        })
          .then((res) => {
            if (res.code !== 200) return Toast(res.msg);
            // 调用接口 那返回的id加密拼接后跳转
            var channel = encrypt("OPNZB_0019"); // 写死的 银行提供的
            var thirdId = encrypt(res.data.thirdId);
            window.location.href =
              host +
              "/acct-micro/#/pages/h5pages/account-open/explain?channel=" +
              channel +
              "&thirdId=" +
              thirdId;
          })
          .catch(() => {
            Toast("网络异常，请稍后再试或者联系客户经理！");
          });
      }
    </script>
  </body>
</html>
