/* eslint-disable no-unused-vars */

/* 全局的环境变量 env   测试： dev   预发布： staging   生产： prod */
const baseUrlList = {
  dev: 'https://dev-api.lianwuxian.cn', // 开发环境
  staging: 'https://staging-api.lianwuxian.cn', // 预发环境
  prod: 'https://platform.chengjie.red', // 生产环境
}

/**
 * 创建toast元素
 * 页面需要用到toast的  需要页面先调用创建函数
 *  @param {fun} cd 回调函数
 */
function createToast(cd) {
  const div = document.createElement('div')
  div.id = 'my-toast'
  const sytleMap = {
    position: 'fixed',
    maxWidth: '80%',
    borderRadius: '5px',
    background: 'rgba(0,0,0,.7)',
    color: '#FFFFFF',
    boxSizing: 'border-box',
    textAlign: 'center',
    zIndex: '1000',
    padding: '10px',
    transition: 'opacity .3s linear',
    left: '50%',
    fontSize: '0.14rem',
    top: '50%',
    transform: 'translate(-50%,-50%)',
    opacity: '0'
  }
  for (let key in sytleMap) {
    div.style[key] = `${sytleMap[key]}`
  }
  document.body.appendChild(div)
  if (cd) cd()
}

/**
 * Toast提示
 * 页面必须要有id为my-toast的div 且有样式
 * @param {String} text 要提示的文案
 * @param {fun} cd 提示之后的回调
 * @param {Number} time 提示时间 默认2秒
 */
function Toast(text, cd, time) {
  time = time || 2000
  let dome = document.getElementById('my-toast')
  // 检查有没有创建toast 没有的话先创建
  if (!dome) {
    createToast(() => {
      dome = document.getElementById('my-toast')
      dome.innerText = text
      dome.style.opacity = 1
    })
  } else {
    dome.innerText = text
    dome.style.opacity = 1
  }
  setTimeout(() => {
    dome.style.opacity = 0
    cd && cd()
  }, time)
}

/**
 *  截取url中的参数方法，
 * @returns {Object} 返回包含所有参数的对象
 */
function getUrlCode() {
  let url = window.location.search
  let theRequest = {}
  if (url.indexOf('?') !== -1) {
    let str = url.split('?')[1]
    let strs = str.split('&')
    for (let i = 0; i < strs.length; i++) {
      theRequest[strs[i].split('=')[0]] = strs[i].split('=')[1]
    }
  }
  return theRequest
}

/** 实现简单的ajax来和服务端通讯
 * @param {Number} url 请求地址
 * @param {Number} method 请求方法 默认post
 * @param {Number} data 请求体
 * @param {Number} headers 设置请求头
 * @returns {Object} 接口返回对象
 */
function ajax(url, method = 'post', data, headers = {}) {
  Object.assign(headers, {
    'Content-Type': 'application/json;charset=utf-8',
    accessToken: getUrlCode().token || '', // 设置请求头携带的token
    accept: 'application/json, text/plain',
  })
  return new Promise((resolve, reject) => {
    let request = new XMLHttpRequest()
    if (method === 'get') {
      url = `${url}?${new URLSearchParams(data).toString()}`
      data = null
    } else {
      data = JSON.stringify(data)
    }
    // 检测url 如果是绝对路径直接使用 如果是相对路径取字典的域名拼接
    const baseUrl = url.indexOf('http') === -1 ? `${baseUrlList[getUrlCode().env || 'dev']}${url}` : url
    request.open(method, baseUrl) // 初始化一个请求
    request.timeout = 10 * 1000 // 单位毫秒，超时时间
    for (let key in headers) {
      request.setRequestHeader(key, headers[key]) // 设置HTTP请求头部的方法，该方法必须在 open()之后，send() 之前调用
    }
    request.onreadystatechange = () => {
      if (request.readyState === 4) {
        if (request.status === 200 || request.status === 304) {
          resolve(JSON.parse(request.responseText)) // request.responseText 是一个字符串，需要 JSON.parse() 转换
        } else {
          reject(request)
        }
      }
    }
    request.send(data) // 发送http请求
  })
}
