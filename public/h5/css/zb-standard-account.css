/* stylelint-disable number-leading-zero */
/* stylelint-disable no-missing-end-of-source-newline */
/* stylelint-disable property-no-vendor-prefix */
.zb-standard-account {
  padding: .1rem .16rem .2rem;
}

.f-c {
  display: flex;
  justify-content: center;
}

.logo {
  display: flex;
  width: 1.4rem;
  height: 1.6rem;
}

.warp {
  position: relative;
}

.g-title {
  position: absolute;
  top: .06rem;
  border-radius: 8px;
  padding-left: .16rem;
  width: 1.7rem;
  height: .48rem;
  line-height: .44rem;
  font-size: 0.16rem;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #007AFE;
}

.label {
  font-size: .14rem;
}

input {
  margin: .16rem 0;
  border: none;
  border-bottom: 1px solid #D8D8D8;
  border-radius: 0;
  padding: 0 0 .08rem;
  width: 100%;
  font-size: .14rem;
  background-color: transparent;
  outline: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
  -webkit-tap-highlight-color: transparent;
}

.footer {
  display: flex;
  justify-content: center;
}

button {
  margin-top: .8rem;
  border: none;
  border-radius: .23rem;
  padding: .09rem .9rem;
  font-size: .18rem;
  color: #FFFFFF;
  background: #007AFE;
}

.require {
  font-weight: 600;
  color: #F22E00;
}

.major {
  font-size: .13rem;
  text-align: justify;
  color: #F51818;
}

.content {
  margin-top: .14rem;
  border-radius: .08rem;
  padding: .78rem 0 .74rem;
  background: rgb(239 244 255 / 80%);
  -webkit-clip-path: polygon(1.7rem 0%, 100% 0%, 100% 100%, 0% 100%, 0% .48rem, 1.53rem .48rem);
  clip-path: polygon(1.7rem 0%, 100% 0%, 100% 100%, 0% 100%, 0% .48rem, 1.53rem .48rem);
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
}

.form {
  margin: 0 .16rem;
  width: calc(100% - .32rem);
}

/* 修改表单 placeholder字体颜色 */
::-webkit-input-placeholder {
  font-size: .14rem;

  /* WebKit browsers，webkit内核浏览器 */
  color: #CCCCCC;
}

:-moz-placeholder {
  font-size: .14rem;

  /* Mozilla Firefox 4 to 18 */
  color: #CCCCCC;
}

::-moz-placeholder {
  font-size: .14rem;

  /* Mozilla Firefox 19+ */
  color: #CCCCCC;
}

:-ms-input-placeholder {
  font-size: .14rem;

  /* Internet Explorer 10+ */
  color: #CCCCCC;
}