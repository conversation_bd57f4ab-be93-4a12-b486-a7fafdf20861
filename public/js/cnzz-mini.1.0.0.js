/* eslint-disable */
function n(o){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},n(o)}window.shukeCnzz=function(){var o="",t="",e=!1,r="",c="",i=!1,u="",a="https://sdsk-log-dev.sdopen.cn";function s(n,o){o=o||"error",e&&window.console&&window.console[o]&&window.console[o](n)}function l(){return"prod"===o?"https://sdsk-log.sdopen.cn":a}function p(o){return Object.keys(o).map((function(t){var e=o[t],r="object"===n(e)&&null!==e?JSON.stringify(e):e;return"".concat(t,"=").concat(r)})).join("&")}function f(n){for(var o="",t=new Uint8Array(n),e=t.byteLength,r=0;r<e;r++)o+=String.fromCharCode(t[r]);return window.btoa(o)}function w(n,o,t){var e=(new TextEncoder).encode(n);try{(window.crypto.subtle||window.crypto.webkitSubtle||window.crypto.msSubtle).digest("SHA-256",e).then((function(n){var e=f(Array.from(new Int8Array(n)));o&&o(e,t)})).catch((function(){return o&&o(null)}))}catch(n){o&&o(null)}}function d(n,o,t){for(var e=[],r=0;r<n.length;r++){var c=n.charCodeAt(r);c<128?e.push(c):c<2048?(e.push(192|c>>6),e.push(128|63&c)):c<65536?(e.push(224|c>>12),e.push(128|c>>6&63),e.push(128|63&c)):(e.push(240|c>>18),e.push(128|c>>12&63),e.push(128|c>>6&63),e.push(128|63&c))}var i=window.msCrypto.subtle.digest("SHA-256",new Uint8Array(e));i.oncomplete=function(n){var e=n.target.result,r=window.btoa(String.fromCharCode.apply(null,new Uint8Array(e)));o&&o(r,t)},i.onerror=function(){o&&o(null)}}function x(n,o){var t=Date.parse(new Date)/1e3,e="sc=".concat(n,"&wm=").concat(t);void 0!==window.msCrypto?d(e,o,t):w(e,o,t)}function y(n,o){return!(!n||!o)&&n.split("&").some((function(n){var t=n.split("=");return t[0]===o&&t[1]}))}function v(n,o){if(t)if(c)if(n){var e="string"==typeof n?n:p(n);y(e,"levelTwoTopic")?x(c,(function(n,i){if(n){n=n.split("=")[0],e+="&t1=".concat(t,"&wd=").concat(r,"&sc=").concat(c,"&wm=").concat(i,"&ws=").concat(encodeURIComponent(n),"&ref=").concat(encodeURIComponent(window.location.pathname)),u&&(e+="&wu=".concat(u)),e=e.replace("levelTwoTopic","t2");var a="".concat(l(),"/api/risk/log?").concat(e),p=new Image(1,1);p.src=a,p.onload=function(){o&&o({code:200,data:"success!"})},p.onerror=function(n){o&&o({code:502,data:n.error})}}else s("生成签名失败")})):s("上报参数不存在二级主题levelTwoTopic")}else s("请检查上报参数是否正确");else s("请先初始化设置服务编码再调用上传接口");else s("请先初始化设置一级主题再调用上传接口")}function m(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=n.topicOne,l=n.serverCodeStr,p=n.devDomain;e=n.debug||e,u=n.userId||u,i=n.timeAxis||i,a=p||a,n.env&&o?s("环境变量只能设置一次"):(o=o||n.env,t&&r?s("一级主题只能设置一次"):(t=t||r,c&&l?s("服务编码只能设置一次"):(c=c||l,n.timeAxis&&v({levelTwoTopic:"open_win"}))))}return function(){function n(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(n){var o=16*Math.random()|0;return("x"===n?o:8|(o&&3)).toString(16)}))}r=localStorage.getItem("deviceID")||n(),localStorage.setItem("deviceID",r)}(),window.onbeforeunload=function(){i&&v({levelTwoTopic:"close_win"})},{setConfig:m,track:v}}();