/* eslint-disable no-unused-vars */
/** getUrlCode 截取url中的code方法
 * @returns {Object} 参数对象
 */
function getUrlCode() {
  let url = window.location.search
  // eslint-disable-next-line no-new-object
  let theRequest = new Object()
  if (url.indexOf('?') > -1) {
    let str = url.split('?')[1]
    let strs = str.split('&')
    for (let i = 0; i < strs.length; i++) {
      theRequest[strs[i].split('=')[0]] = strs[i].split('=')[1]
    }
  }
  return theRequest
}

// 打开toast提示 2秒后自动返回AP 页面要生成id为my-toast元素
function Toast(text, cd) {
  let dome = document.getElementById('my-toast')
  dome.innerText = text
  dome.style.opacity = 1
  setTimeout(() => {
    dome.style.opacity = 0
    cd && cd()
  }, 2000)
}

/**
 * 创建Form表单跳转亿联直融的网关页面
 * @param {String} config .url form的Action，提交的后台地址
 * @param {String} config.method 使用POST还是GET提交表单
 * @param {String} config.target 当前页面还是新页面 _self/_blank
 * @param {object} config.params 文件对象
 */
function createFormToYl(config) {
  config = config || {}
  let form = document.createElement('form')
  form.action = config.url
  form.method = config.method || 'POST'
  form.target = config.target || '_blank'

  let params = config.params || {}
  for (let param in params) {
    let value = params[param]
    let input = document.createElement('input')
    input.type = 'hidden'
    input.name = param
    input.value = value

    form.appendChild(input)
  }
  document.getElementById('body').appendChild(form)
  form.submit()

  // 跳转后再删除form
  setTimeout(() => {
    document.getElementById('body').removeChild(form)
  }, 2000)
}
