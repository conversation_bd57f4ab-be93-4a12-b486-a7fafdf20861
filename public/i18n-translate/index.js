;(function () {
  const locales = [
    {
      language: 'english',
      translate: {
        中国大集: 'Chinamarket',
        临沂商城: 'Linyi Trade City',
        大集: 'Market',
      },
    },
    {
      language: 'arabic',
      translate: {
        中国大集: 'سوق الصين الكبرى',
        临沂商城: 'مدينة ليني التجارية',
        大集: 'سوق الكبرى',
      },
    },
    {
      language: 'thai',
      translate: {
        // 中国大集: 'ตลาดใหญ่ของจีน',
        // 中国大集平台: 'แพลตฟอร์มตลาดใหญ่ของจีน',
        // 临沂商城: 'ร้านหลินอี๋',
        // 大集: 'ตลาดใหญ่ขอ',
      },
    },
    {
      language: 'indonesian',
      translate: {
        中国大集: 'Cina Market',
        临沂商城: 'Linyi Trade city',
        大集: 'Market',
      },
    },
  ]

  if (!window.translate) return

  // 获取链接上的语言 注意 后期增加语言 languages 需要维护
  function setDefaultLanguageKey(languages) {
    const params = new URLSearchParams(window.location.search)
    const lang = params.get('lang') || {}
    let toValue = lang && languages.indexOf(lang) > -1 ? lang : ''
    // 根据国家馆设置默认语言
    if (window.__ENV__.APP_NATIONAL_TYPE === 'uae' && !window.localStorage.getItem('to')) {
      toValue = 'english'
    } else if (window.__ENV__.APP_NATIONAL_TYPE === 'idn' && !window.localStorage.getItem('to')) {
      toValue = 'indonesian'
    }
    if (toValue) {
      // 根据国家馆添加默认语言
      const customLanguages = ['english', 'chinese_simplified', 'indonesian']
      const newToValue = customLanguages.includes(toValue) ? toValue : customLanguages[0]
      window.localStorage.setItem('to', newToValue)
      console.log(localStorage.getItem('to'), 'setDefaultLanguageKey')
    }
  }

  function _translateStr(obj) {
    return Object.keys(obj)
      .map((key) => `${key}=${obj[key]}`)
      .join('\n')
  }

  // 开始执行
  const languages = []
  for (const item of locales) {
    languages.push(item.language)

    // https://translate.zvo.cn/41555.html 文档地址
    window.translate.nomenclature.append('chinese_simplified', item.language, _translateStr(item.translate))
  }
  setDefaultLanguageKey(languages.concat(['chinese_simplified']))
  window.translate.language.setLocal('chinese_simplified') //设置本地语种（当前网页的语种）。如果不设置，默认就是 'chinese_simplified' 简体中文。 可填写如 'english'、'chinese_simplified' 等，具体参见文档下方关于此的说明
  window.translate.setDocuments(document.documentElement)
  window.translate.service.use('client.edge')
  // translate.listener.start() //开启html页面变化的监控，对变化部分会进行自动翻译。注意，这里变化区域，是指使用 translate.setDocuments(...) 设置的区域。如果未设置，那么为监控整个网页的变化
  window.translate.ignore.class.push('skip-translate')
  window.translate.selectLanguageTag.languages = languages.join(',')
  window.translate.selectLanguageTag.show = false
  // window.translate.execute() //执行翻译初始化操作，显示出select语言选择
  document.documentElement.setAttribute('dir', localStorage.getItem('to') === 'arabic' ? 'rtl' : 'ltr')
})()
