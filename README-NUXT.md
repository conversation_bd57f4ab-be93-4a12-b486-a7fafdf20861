# 🚀 中国大集 Nuxt.js 项目

## 📋 项目概述

这是中国大集项目的 Nuxt.js 版本，专门为 SEO 优化而设计的服务端渲染应用。

## ✨ 主要特性

- ✅ 服务端渲染（SSR）
- ✅ SEO 优化
- ✅ 多语言支持
- ✅ 响应式设计
- ✅ 性能优化

## 🏗️ 项目结构

```
├── assets/              # 静态资源
├── components/          # 组件
├── composables/         # 组合式函数
├── layouts/            # 布局
├── locales/            # 国际化文件
├── middleware/         # 中间件
├── pages/              # 页面（自动路由）
├── plugins/            # 插件
├── public/             # 公共文件
├── server/             # 服务端 API
├── stores/             # 状态管理
├── utils/              # 工具函数
├── nuxt.config.ts      # Nuxt 配置
└── package.json        # 依赖配置
```

## 🚀 快速开始

### 1. 安装依赖

```bash
# 使用 npm
npm install

# 或使用 pnpm（推荐）
pnpm install
```

### 2. 环境配置

创建环境变量文件：

```bash
# .env
VUE_APP_API_URL=https://api.chinadaji.com
VUE_APP_NATIONAL_TYPE=cn
VUE_APP_BASE_TYPE=new
```

### 3. 开发模式

```bash
# 启动开发服务器
npm run dev

# 指定国家馆
npm run dev-uae  # 阿联酋
npm run dev-idn  # 印尼
```

### 4. 构建部署

```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview

# 生成静态站点
npm run generate
```

## 📱 SEO 优化功能详解

### 1. 页面级 SEO

每个页面都可以配置独立的 SEO 信息：

```vue
<script setup>
// 基础 SEO
useSeoMeta({
  title: '页面标题',
  description: '页面描述',
  keywords: '关键词1,关键词2',
})

// 结构化数据
useJsonld({
  '@context': 'https://schema.org',
  '@type': 'WebPage',
  name: '页面名称',
})
</script>
```

### 2. 动态 SEO

根据内容动态生成 SEO 信息：

```vue
<script setup>
// 获取产品数据
const { data: product } = await useFetch(`/api/products/${route.params.id}`)

// 动态 SEO
useProductSeo(product.value)
</script>
```

### 3. 多语言 SEO

自动为不同语言生成对应的 SEO 标签：

```vue
<script setup>
const { locale } = useI18n()

useSeoMeta({
  title: locale.value === 'zh' ? '中文标题' : 'English Title',
  ogLocale: locale.value,
})
</script>
```

## 🌍 国际化配置

### 支持的语言

- 🇨🇳 中文 (zh)
- 🇺🇸 英文 (en)
- 🇸🇦 阿拉伯语 (ar)
- 🇮🇩 印尼语 (id)

### 路由结构

```
/                    # 中文（默认）
/en                  # 英文
/ar                  # 阿拉伯语
/id                  # 印尼语
```

## 🔧 性能优化

### 1. 图片优化

```vue
<template>
  <!-- 自动优化 -->
  <NuxtImg src="/images/product.jpg" alt="产品图片" width="300" height="200" loading="lazy" format="webp" />
</template>
```

### 2. 代码分割

```vue
<script setup>
// 动态导入重型组件
const HeavyComponent = defineAsyncComponent(() => import('~/components/Heavy.vue'))
</script>
```

### 3. 缓存策略

```javascript
// nuxt.config.ts
export default defineNuxtConfig({
  routeRules: {
    '/': { prerender: true },
    '/mall': { isr: 60 }, // 增量静态再生
    '/api/**': { cors: true, headers: { 'cache-control': 's-maxage=60' } },
  },
})
```

## 🐳 Docker 部署

### 1. 构建镜像

```bash
# 构建 Docker 镜像
docker build -f Dockerfile.nuxt -t trade-exhibition-nuxt .
```

### 2. 使用 Docker Compose

```bash
# 启动服务
docker-compose -f docker-compose.nuxt.yml up -d

# 查看日志
docker-compose -f docker-compose.nuxt.yml logs -f
```

### 3. 生产环境配置

```yaml
# docker-compose.prod.yml
services:
  nuxt-app:
    environment:
      - NODE_ENV=production
      - VUE_APP_API_URL=https://api.chinadaji.com
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
```

## 📊 监控和分析

### 1. 性能监控

- 内置健康检查接口：`/api/health`
- 内存使用监控
- 响应时间统计

### 2. SEO 分析

- 自动生成 Sitemap：`/sitemap.xml`
- Robots.txt：`/robots.txt`
- 结构化数据验证

### 3. 用户分析

- 百度统计集成
- 阿里云 RUM 监控
- 自定义事件追踪

## 🔍 调试和测试

### 1. SEO 测试

```bash
# 检查页面 SEO
curl -H "User-Agent: Googlebot" http://localhost:3000/

# 验证结构化数据
# 使用 Google 结构化数据测试工具
```

### 2. 性能测试

```bash
# Lighthouse 测试
npx lighthouse http://localhost:3000 --output html

# 性能分析
npm run analyze
```

## 📚 相关文档

- [迁移指南](./MIGRATION_GUIDE.md)
- [Nuxt.js 官方文档](https://nuxt.com)
- [SEO 最佳实践](https://developers.google.com/search/docs)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。
