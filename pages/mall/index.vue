<template>
  <div class="mall-page">
    <!-- 页面头部横幅 -->
    <section class="mall-hero">
      <div class="container">
        <h1 class="mall-title">{{ $t('mall.title') }}</h1>
        <p class="mall-description">{{ $t('mall.description') }}</p>
        
        <!-- 搜索框 -->
        <div class="search-box">
          <el-input
            v-model="searchQuery"
            :placeholder="$t('common.search')"
            size="large"
            @keyup.enter="handleSearch"
          >
            <template #append>
              <el-button @click="handleSearch">
                <Icon name="mdi:magnify" />
              </el-button>
            </template>
          </el-input>
        </div>
      </div>
    </section>

    <!-- 商品分类 -->
    <section class="categories-section">
      <div class="container">
        <h2 class="section-title">{{ $t('mall.categories') }}</h2>
        <div class="categories-grid">
          <NuxtLink
            v-for="category in categories"
            :key="category.id"
            :to="`/mall/category/${category.slug}`"
            class="category-card"
          >
            <div class="category-image">
              <img :src="category.image" :alt="category.name" loading="lazy">
            </div>
            <h3 class="category-name">{{ category.name }}</h3>
            <p class="category-count">{{ category.productCount }} {{ $t('mall.products') }}</p>
          </NuxtLink>
        </div>
      </div>
    </section>

    <!-- 热门商品 -->
    <section class="hot-products-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">{{ $t('mall.hotProducts') }}</h2>
          <NuxtLink to="/mall/products?sort=hot" class="view-more">
            {{ $t('common.more') }}
          </NuxtLink>
        </div>
        
        <div class="products-grid">
          <ProductCard
            v-for="product in hotProducts"
            :key="product.id"
            :product="product"
          />
        </div>
      </div>
    </section>

    <!-- 新品推荐 -->
    <section class="new-products-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">{{ $t('mall.newProducts') }}</h2>
          <NuxtLink to="/mall/products?sort=new" class="view-more">
            {{ $t('common.more') }}
          </NuxtLink>
        </div>
        
        <div class="products-grid">
          <ProductCard
            v-for="product in newProducts"
            :key="product.id"
            :product="product"
          />
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
// SEO 配置
useSeo({
  title: '全球商城',
  description: '中国大集全球商城，汇聚全球优质商品，提供一站式采购体验。海量商品，品质保证，批发零售，欢迎选购。',
  keywords: '全球商城,商品采购,批发零售,优质商品,一站式采购,中国大集商城'
})

// 结构化数据
const { useOrganizationSchema } = useStructuredData()
useOrganizationSchema()

// 页面数据
const searchQuery = ref('')

// 获取分类数据
const { data: categories } = await useFetch('/api/categories', {
  key: 'mall-categories',
  default: () => []
})

// 获取热门商品
const { data: hotProducts } = await useFetch('/api/products/hot', {
  key: 'hot-products',
  default: () => []
})

// 获取新品推荐
const { data: newProducts } = await useFetch('/api/products/new', {
  key: 'new-products',
  default: () => []
})

// 搜索处理
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    navigateTo(`/mall/search?q=${encodeURIComponent(searchQuery.value)}`)
  }
}

// 页面元数据
definePageMeta({
  layout: 'default'
})
</script>

<style scoped>
.mall-page {
  min-height: 100vh;
}

.mall-hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
  text-align: center;
}

.mall-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.mall-description {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.search-box {
  max-width: 600px;
  margin: 0 auto;
}

.categories-section,
.hot-products-section,
.new-products-section {
  padding: 60px 0;
}

.section-title {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 2rem;
  color: #2d3748;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.view-more {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.view-more:hover {
  text-decoration: underline;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.category-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0,0,0,0.05);
  transition: transform 0.3s ease;
  text-decoration: none;
  color: inherit;
}

.category-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.category-image {
  width: 80px;
  height: 80px;
  margin: 0 auto 1rem;
  border-radius: 50%;
  overflow: hidden;
}

.category-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.category-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #2d3748;
}

.category-count {
  color: #718096;
  font-size: 0.9rem;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (max-width: 768px) {
  .mall-title {
    font-size: 2rem;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .categories-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
  
  .products-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}
</style>
