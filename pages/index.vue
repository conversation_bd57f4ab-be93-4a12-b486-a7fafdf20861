<template>
  <div class="home-page">
    <!-- SEO 优化的首页内容 -->
    <section class="hero-section">
      <div class="container">
        <h1 class="hero-title">
          {{ $t('home.title') }}
        </h1>
        <p class="hero-description">
          {{ $t('home.description') }}
        </p>
        <div class="hero-actions">
          <NuxtLink to="/mall" class="btn btn-primary">
            {{ $t('home.enterMall') }}
          </NuxtLink>
          <NuxtLink to="/market" class="btn btn-secondary">
            {{ $t('home.exploreMarket') }}
          </NuxtLink>
        </div>
      </div>
    </section>

    <!-- 特色功能区域 -->
    <section class="features-section">
      <div class="container">
        <h2 class="section-title">{{ $t('home.features.title') }}</h2>
        <div class="features-grid">
          <div v-for="feature in features" :key="feature.id" class="feature-card">
            <div class="feature-icon">
              <Icon :name="feature.icon" />
            </div>
            <h3 class="feature-title">{{ $t(feature.title) }}</h3>
            <p class="feature-description">{{ $t(feature.description) }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 数据统计区域 -->
    <section class="stats-section">
      <div class="container">
        <div class="stats-grid">
          <div v-for="stat in stats" :key="stat.id" class="stat-item">
            <div class="stat-number">{{ stat.number }}</div>
            <div class="stat-label">{{ $t(stat.label) }}</div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
// 页面组件逻辑

// 页面数据
const features = ref([
  {
    id: 1,
    icon: 'mdi:store',
    title: 'home.features.mall.title',
    description: 'home.features.mall.description'
  },
  {
    id: 2,
    icon: 'mdi:chart-line',
    title: 'home.features.market.title',
    description: 'home.features.market.description'
  },
  {
    id: 3,
    icon: 'mdi:account-group',
    title: 'home.features.merchants.title',
    description: 'home.features.merchants.description'
  },
  {
    id: 4,
    icon: 'mdi:robot',
    title: 'home.features.ai.title',
    description: 'home.features.ai.description'
  }
])

const stats = ref([
  {
    id: 1,
    number: '100万+',
    label: 'home.stats.merchants'
  },
  {
    id: 2,
    number: '500万+',
    label: 'home.stats.products'
  },
  {
    id: 3,
    number: '200+',
    label: 'home.stats.countries'
  },
  {
    id: 4,
    number: '1000亿+',
    label: 'home.stats.transaction'
  }
])

// 页面加载时的数据获取（暂时注释，等API准备好后启用）
// const { data: homeData } = await useFetch('/api/home', {
//   key: 'home-data',
//   server: true,
//   default: () => ({})
// })
</script>

<style scoped>
.home-page {
  min-height: 100vh;
}

.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 120px 0 80px;
  text-align: center;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.hero-description {
  font-size: 1.25rem;
  margin-bottom: 2.5rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  opacity: 0.9;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 32px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  display: inline-block;
}

.btn-primary {
  background: #fff;
  color: #667eea;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn-secondary {
  background: transparent;
  color: #fff;
  border: 2px solid #fff;
}

.btn-secondary:hover {
  background: #fff;
  color: #667eea;
}

.features-section {
  padding: 80px 0;
  background: #f8fafc;
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 3rem;
  color: #2d3748;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-4px);
}

.feature-icon {
  font-size: 3rem;
  color: #667eea;
  margin-bottom: 1rem;
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #2d3748;
}

.feature-description {
  color: #718096;
  line-height: 1.6;
}

.stats-section {
  padding: 80px 0;
  background: #2d3748;
  color: white;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  max-width: 1000px;
  margin: 0 auto;
  text-align: center;
}

.stat-number {
  font-size: 3rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1.1rem;
  opacity: 0.9;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-description {
    font-size: 1.1rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 200px;
  }
}
</style>
