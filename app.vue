<template>
  <div>
    <!-- 页面内容 -->
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
  </div>
</template>

<script setup>
// 全局 SEO 配置
useSeoMeta({
  titleTemplate: '%s - 中国大集',
  description: '中国大集是临沂商城官方B2B数字商贸综合服务平台，提供一站式全域贸易服务，连接全球买家与卖家，助力中小企业数字化转型。',
  ogDescription: '中国大集是临沂商城官方B2B数字商贸综合服务平台，提供一站式全域贸易服务，连接全球买家与卖家，助力中小企业数字化转型。',
  ogImage: '/logo.png',
  twitterCard: 'summary_large_image'
})

// 全局错误处理
const handleError = (error) => {
  console.error('Global error:', error)
  // 可以在这里添加错误上报逻辑
}

// 监听全局错误
onErrorCaptured(handleError)

// 页面加载完成后的处理
onMounted(() => {
  // 初始化第三方脚本
  nextTick(() => {
    // 翻译插件初始化
    if (window.translate) {
      window.translate.execute()
    }
  })
})
</script>

<style>
/* 全局样式 */
html {
  scroll-behavior: smooth;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 页面切换动画 */
.page-enter-active,
.page-leave-active {
  transition: opacity 0.3s ease;
}

.page-enter-from,
.page-leave-to {
  opacity: 0;
}
</style>
