<template>
  <header class="app-header">
    <div class="container">
      <!-- Logo 和网站名称 -->
      <div class="header-brand">
        <NuxtLink to="/" class="brand-link">
          <img src="/logo.png" alt="中国大集" class="logo">
          <span class="brand-name">中国大集</span>
        </NuxtLink>
      </div>

      <!-- 主导航菜单 -->
      <nav class="main-nav" :class="{ 'nav-open': mobileMenuOpen }">
        <NuxtLink
          v-for="item in navItems"
          :key="item.path"
          :to="item.path"
          class="nav-link"
          @click="closeMobileMenu"
        >
          {{ $t(item.label) }}
        </NuxtLink>
      </nav>

      <!-- 右侧功能区 -->
      <div class="header-actions">
        <!-- 语言切换 -->
        <el-dropdown @command="changeLanguage" class="language-dropdown">
          <span class="language-trigger">
            <Icon name="mdi:translate" />
            {{ currentLanguage.name }}
            <Icon name="mdi:chevron-down" />
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="lang in languages"
                :key="lang.code"
                :command="lang.code"
                :class="{ active: locale === lang.code }"
              >
                {{ lang.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <!-- 用户菜单 -->
        <div v-if="isLoggedIn" class="user-menu">
          <el-dropdown @command="handleUserAction">
            <span class="user-trigger">
              <el-avatar :src="userInfo?.avatar" :size="32">
                {{ userInfo?.name?.charAt(0) }}
              </el-avatar>
              <span class="user-name">{{ userInfo?.name }}</span>
              <Icon name="mdi:chevron-down" />
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <Icon name="mdi:account" />
                  {{ $t('nav.profile') }}
                </el-dropdown-item>
                <el-dropdown-item command="orders">
                  <Icon name="mdi:package-variant" />
                  我的订单
                </el-dropdown-item>
                <el-dropdown-item command="favorites">
                  <Icon name="mdi:heart" />
                  我的收藏
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <Icon name="mdi:logout" />
                  {{ $t('nav.logout') }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>

        <!-- 登录/注册按钮 -->
        <div v-else class="auth-buttons">
          <NuxtLink to="/login" class="btn btn-outline">
            {{ $t('nav.login') }}
          </NuxtLink>
          <NuxtLink to="/register" class="btn btn-primary">
            {{ $t('nav.register') }}
          </NuxtLink>
        </div>

        <!-- 移动端菜单按钮 -->
        <button
          class="mobile-menu-btn"
          @click="toggleMobileMenu"
          :class="{ active: mobileMenuOpen }"
        >
          <span></span>
          <span></span>
          <span></span>
        </button>
      </div>
    </div>
  </header>
</template>

<script setup>
const { locale, setLocale } = useI18n()
const route = useRoute()

// 移动端菜单状态
const mobileMenuOpen = ref(false)

// 导航菜单项
const navItems = [
  { path: '/', label: 'nav.home' },
  { path: '/mall', label: 'nav.mall' },
  { path: '/market', label: 'nav.market' },
  { path: '/merchants', label: 'nav.merchants' },
  { path: '/ai-features', label: 'nav.ai' }
]

// 语言选项
const languages = [
  { code: 'zh', name: '中文' },
  { code: 'en', name: 'English' },
  { code: 'ar', name: 'العربية' },
  { code: 'id', name: 'Bahasa' }
]

// 当前语言
const currentLanguage = computed(() => {
  return languages.find(lang => lang.code === locale.value) || languages[0]
})

// 用户登录状态和信息
const isLoggedIn = ref(false)
const userInfo = ref(null)

// 检查登录状态
const checkLoginStatus = () => {
  const token = useCookie('access_token')
  isLoggedIn.value = !!token.value
  
  if (isLoggedIn.value) {
    // 获取用户信息
    // userInfo.value = await getUserInfo()
  }
}

// 语言切换
const changeLanguage = (langCode) => {
  setLocale(langCode)
  // 保存语言偏好到 cookie
  const languageCookie = useCookie('i18n_redirected')
  languageCookie.value = langCode
}

// 用户操作处理
const handleUserAction = (command) => {
  switch (command) {
    case 'profile':
      navigateTo('/user/profile')
      break
    case 'orders':
      navigateTo('/user/orders')
      break
    case 'favorites':
      navigateTo('/user/favorites')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 退出登录
const handleLogout = () => {
  const token = useCookie('access_token')
  token.value = null
  isLoggedIn.value = false
  userInfo.value = null
  
  // 跳转到首页
  navigateTo('/')
  
  // 显示退出成功提示
  ElMessage.success('退出登录成功')
}

// 移动端菜单控制
const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

const closeMobileMenu = () => {
  mobileMenuOpen.value = false
}

// 监听路由变化，关闭移动端菜单
watch(() => route.path, () => {
  closeMobileMenu()
})

// 页面加载时检查登录状态
onMounted(() => {
  checkLoginStatus()
})
</script>

<style scoped>
.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid #e2e8f0;
  z-index: 1000;
  transition: all 0.3s ease;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.header-brand {
  display: flex;
  align-items: center;
}

.brand-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #2d3748;
}

.logo {
  height: 40px;
  width: auto;
  margin-right: 0.5rem;
}

.brand-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: #667eea;
}

.main-nav {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-link {
  text-decoration: none;
  color: #4a5568;
  font-weight: 500;
  padding: 0.5rem 0;
  position: relative;
  transition: color 0.3s ease;
}

.nav-link:hover,
.nav-link.router-link-active {
  color: #667eea;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: #667eea;
  transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.router-link-active::after {
  width: 100%;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.language-dropdown {
  cursor: pointer;
}

.language-trigger {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem;
  color: #4a5568;
  font-size: 0.9rem;
}

.user-menu {
  cursor: pointer;
}

.user-trigger {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
}

.user-name {
  font-weight: 500;
  color: #2d3748;
}

.auth-buttons {
  display: flex;
  gap: 0.5rem;
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.btn-outline {
  color: #667eea;
  border-color: #667eea;
}

.btn-outline:hover {
  background: #667eea;
  color: white;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover {
  background: #5a67d8;
}

.mobile-menu-btn {
  display: none;
  flex-direction: column;
  justify-content: space-around;
  width: 24px;
  height: 24px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
}

.mobile-menu-btn span {
  width: 100%;
  height: 2px;
  background: #4a5568;
  transition: all 0.3s ease;
}

.mobile-menu-btn.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-btn.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-btn.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

@media (max-width: 768px) {
  .main-nav {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    background: white;
    flex-direction: column;
    padding: 1rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }

  .main-nav.nav-open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .mobile-menu-btn {
    display: flex;
  }

  .auth-buttons {
    display: none;
  }

  .language-dropdown {
    display: none;
  }
}
</style>
