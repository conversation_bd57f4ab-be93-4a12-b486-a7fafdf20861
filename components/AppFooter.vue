<template>
  <footer class="app-footer">
    <div class="container">
      <div class="footer-content">
        <!-- 公司信息 -->
        <div class="footer-section">
          <h3 class="footer-title">{{ $t('footer.company') }}</h3>
          <p class="footer-text">{{ $t('footer.address') }}</p>
          <p class="footer-text">{{ $t('footer.phone') }}: 400-xxx-xxxx</p>
          <p class="footer-text">{{ $t('footer.email') }}: <EMAIL></p>
        </div>

        <!-- 快速链接 -->
        <div class="footer-section">
          <h3 class="footer-title">快速链接</h3>
          <ul class="footer-links">
            <li><NuxtLink to="/about">{{ $t('footer.links.about') }}</NuxtLink></li>
            <li><NuxtLink to="/service">{{ $t('footer.links.service') }}</NuxtLink></li>
            <li><NuxtLink to="/privacy">{{ $t('footer.links.privacy') }}</NuxtLink></li>
            <li><NuxtLink to="/help">{{ $t('footer.links.help') }}</NuxtLink></li>
          </ul>
        </div>

        <!-- 产品服务 -->
        <div class="footer-section">
          <h3 class="footer-title">产品服务</h3>
          <ul class="footer-links">
            <li><NuxtLink to="/mall">全球商城</NuxtLink></li>
            <li><NuxtLink to="/market">市场行情</NuxtLink></li>
            <li><NuxtLink to="/merchants">商家服务</NuxtLink></li>
            <li><NuxtLink to="/ai-features">AI功能</NuxtLink></li>
          </ul>
        </div>

        <!-- 联系我们 -->
        <div class="footer-section">
          <h3 class="footer-title">关注我们</h3>
          <div class="social-links">
            <a href="#" class="social-link" aria-label="微博">
              <Icon name="mdi:sina-weibo" />
            </a>
            <a href="#" class="social-link" aria-label="微信">
              <Icon name="mdi:wechat" />
            </a>
            <a href="#" class="social-link" aria-label="Facebook">
              <Icon name="mdi:facebook" />
            </a>
            <a href="#" class="social-link" aria-label="Twitter">
              <Icon name="mdi:twitter" />
            </a>
          </div>
        </div>
      </div>

      <!-- 版权信息 -->
      <div class="footer-bottom">
        <p class="copyright">{{ $t('footer.copyright') }}</p>
        <div class="footer-meta">
          <span>ICP备案号: 鲁ICP备xxxxxxxx号</span>
          <span>公安备案号: 鲁公网安备xxxxxxxx号</span>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
// 页脚组件逻辑
</script>

<style scoped>
.app-footer {
  background: #2d3748;
  color: #e2e8f0;
  padding: 3rem 0 1rem;
  margin-top: auto;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section {
  display: flex;
  flex-direction: column;
}

.footer-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #f7fafc;
}

.footer-text {
  margin-bottom: 0.5rem;
  color: #cbd5e0;
  line-height: 1.6;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 0.5rem;
}

.footer-links a {
  color: #cbd5e0;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #667eea;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #4a5568;
  color: #e2e8f0;
  border-radius: 50%;
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-link:hover {
  background: #667eea;
  transform: translateY(-2px);
}

.footer-bottom {
  border-top: 1px solid #4a5568;
  padding-top: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.copyright {
  color: #a0aec0;
  margin: 0;
}

.footer-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: #718096;
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
  }
  
  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }
  
  .footer-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>
