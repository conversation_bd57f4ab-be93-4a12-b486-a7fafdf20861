{"name": "trade-exhibition", "version": "1.0.0", "private": true, "scripts": {"dev-pc": "cross-env PLATFORM=pc vite --host", "dev-pc-idn": "cross-env PLATFORM=pc vite --host --mode development-idn", "dev-pc-uae": "cross-env PLATFORM=pc vite --host --mode development-uae", "build-pc": "cross-env PLATFORM=pc vite build", "build-pc-uae": "cross-env PLATFORM=pc vite build --mode=production-uae", "build-pc-idn": "cross-env PLATFORM=pc vite build --mode=production-idn", "build-pc:staging": "cross-env PLATFORM=pc vite build --mode=staging", "build-pc:staging-uae": "cross-env PLATFORM=pc vite build --mode=staging-uae", "build-pc:staging-idn": "cross-env PLATFORM=pc vite build --mode=staging-idn", "build-pc:dev": "cross-env PLATFORM=pc vite build --mode=development", "build-pc:dev-uae": "cross-env PLATFORM=pc vite build --mode=development-uae", "build-pc:dev-idn": "cross-env PLATFORM=pc vite build --mode=development-idn", "preview": "vite preview", "prepare": "husky"}, "dependencies": {"@arms/rum-vite-plugin": "^0.0.16", "@element-plus/icons-vue": "^2.3.1", "@microsoft/fetch-event-source": "^2.0.1", "@vueuse/core": "^11.0.1", "@wangeditor/editor": "^0.15.20", "axios": "^1.7.2", "bignumber.js": "^9.1.0", "dayjs": "^1.11.13", "element-plus": "^2.8.4", "fabric": "^6.4.1", "marked": "^12.0.1", "normalize.css": "^8.0.1", "pinia": "^2.2.1", "qrcode": "^1.5.4", "swiper": "^10.3.1", "uuid": "^10.0.0", "vant": "^4.9.6", "vue": "^3.4.33", "vue-i18n": "9", "vue-router": "^4.4.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@rushstack/eslint-patch": "^1.10.4", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@unocss/preset-rem-to-px": "^0.62.3", "@unocss/preset-uno": "^0.61.5", "@vant/auto-import-resolver": "^1.2.1", "@vitejs/plugin-legacy": "^6.1.1", "@vitejs/plugin-vue": "^5.0.5", "@vitejs/plugin-vue-jsx": "^4.0.0", "@vue/eslint-config-prettier": "^9.0.0", "check-keywords": "^1.0.2", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.27.0", "husky": "^9.1.4", "lint-staged": "^15.2.7", "prettier": "^3.3.3", "sass": "^1.77.8", "terser": "^5.39.2", "unocss": "^0.61.5", "unplugin-auto-import": "^0.18.2", "unplugin-icons": "^0.19.2", "unplugin-vue-components": "^0.27.4", "vite": "^5.3.4", "vite-plugin-vue-devtools": "^7.3.6"}}