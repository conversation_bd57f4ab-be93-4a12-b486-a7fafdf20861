{"name": "trade-exhibition-nuxt", "version": "1.0.0", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev --host", "dev-idn": "nuxt dev --host --dotenv .env.development-idn", "dev-uae": "nuxt dev --host --dotenv .env.development-uae", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "build-staging": "nuxt build --dotenv .env.staging", "build-staging-uae": "nuxt build --dotenv .env.staging-uae", "build-staging-idn": "nuxt build --dotenv .env.staging-idn", "build-production": "nuxt build --dotenv .env.production", "build-production-uae": "nuxt build --dotenv .env.production-uae", "build-production-idn": "nuxt build --dotenv .env.production-idn", "analyze": "nuxt analyze", "lint": "eslint .", "lint:fix": "eslint . --fix", "prepare": "husky"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@microsoft/fetch-event-source": "^2.0.1", "@nuxt/ui": "^2.18.4", "@pinia/nuxt": "^0.5.4", "@vueuse/core": "^11.0.1", "@wangeditor/editor": "^0.15.20", "axios": "^1.7.2", "bignumber.js": "^9.1.0", "dayjs": "^1.11.13", "element-plus": "^2.8.4", "fabric": "^6.4.1", "marked": "^12.0.1", "normalize.css": "^8.0.1", "nuxt": "^3.13.0", "pinia": "^2.2.1", "qrcode": "^1.5.4", "swiper": "^10.3.1", "uuid": "^10.0.0", "vant": "^4.9.6", "vue": "^3.4.33", "vue-router": "^4.4.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@element-plus/nuxt": "^1.0.10", "@nuxt/devtools": "latest", "@nuxt/eslint": "^0.5.7", "@nuxtjs/i18n": "^8.5.5", "@rushstack/eslint-patch": "^1.10.4", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@unocss/nuxt": "^0.62.3", "@unocss/preset-rem-to-px": "^0.62.3", "@unocss/preset-uno": "^0.61.5", "@vant/nuxt": "^1.0.0", "@vue/eslint-config-prettier": "^9.0.0", "check-keywords": "^1.0.2", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.27.0", "husky": "^9.1.4", "lint-staged": "^15.2.7", "prettier": "^3.3.3", "sass": "^1.77.8", "terser": "^5.39.2", "typescript": "^5.6.2", "@types/node": "^20.0.0", "unocss": "^0.61.5", "unplugin-auto-import": "^0.18.2", "unplugin-icons": "^0.19.2", "unplugin-vue-components": "^0.27.4"}}