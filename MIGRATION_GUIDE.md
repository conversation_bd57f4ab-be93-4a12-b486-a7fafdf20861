# Nuxt.js 迁移指南

## 🎯 项目概述

本指南将帮助您将现有的 Vue 3 + Vite 项目迁移到 Nuxt.js，实现服务端渲染（SSR）和 SEO 优化。

## 📋 迁移步骤

### 1. 安装依赖

```bash
# 安装 Nuxt.js 相关依赖
npm install nuxt @nuxt/ui @pinia/nuxt @nuxtjs/i18n @unocss/nuxt @element-plus/nuxt @vant/nuxt

# 安装开发依赖
npm install -D @nuxt/devtools @nuxt/eslint typescript
```

### 2. 项目结构调整

#### 原项目结构 → Nuxt 结构
```
src/pc/views/pages/     → pages/
src/pc/components/      → components/
src/pc/stores/         → stores/
src/utils/             → utils/
src/assets/            → assets/
src/i18n/locales/      → locales/
src/pc/router/         → 自动路由（无需手动配置）
```

### 3. 路由迁移

#### 原 Vue Router 配置
```javascript
// src/pc/router/index.js
const routes = [
  { path: '/', component: Home },
  { path: '/mall', component: Mall }
]
```

#### Nuxt 自动路由
```
pages/
  index.vue          → /
  mall/
    index.vue        → /mall
    [id].vue         → /mall/:id
    category/
      [slug].vue     → /mall/category/:slug
```

### 4. 组件迁移

#### 页面组件迁移
```vue
<!-- 原组件 -->
<template>
  <div>页面内容</div>
</template>

<script setup>
import { ref } from 'vue'
// 组件逻辑
</script>

<!-- Nuxt 页面组件 -->
<template>
  <div>页面内容</div>
</template>

<script setup>
// SEO 配置
useSeoMeta({
  title: '页面标题',
  description: '页面描述'
})

// 页面数据获取
const { data } = await useFetch('/api/data')

// 页面元数据
definePageMeta({
  layout: 'default',
  middleware: 'auth' // 可选
})
</script>
```

### 5. 状态管理迁移

#### Pinia Store 迁移
```javascript
// stores/user.js
export const useUserStore = defineStore('user', () => {
  const userInfo = ref(null)
  
  const getUserInfo = async () => {
    const { data } = await $fetch('/api/user')
    userInfo.value = data
  }
  
  return {
    userInfo,
    getUserInfo
  }
})
```

### 6. API 请求迁移

#### 原 axios 配置
```javascript
// utils/axios.js
import axios from 'axios'
const api = axios.create({
  baseURL: process.env.VUE_APP_API_URL
})
```

#### Nuxt useFetch
```javascript
// composables/useApi.ts
export const useApi = (url, options = {}) => {
  const config = useRuntimeConfig()
  return $fetch(url, {
    baseURL: config.public.apiUrl,
    ...options
  })
}
```

### 7. 国际化迁移

#### 原 vue-i18n 配置
```javascript
// i18n/index.js
import { createI18n } from 'vue-i18n'
import zh from './locales/zh.json'
import en from './locales/en.json'

const i18n = createI18n({
  locale: 'zh',
  messages: { zh, en }
})
```

#### Nuxt i18n 配置
```javascript
// nuxt.config.ts
export default defineNuxtConfig({
  modules: ['@nuxtjs/i18n'],
  i18n: {
    locales: [
      { code: 'zh', name: '中文', file: 'zh.json' },
      { code: 'en', name: 'English', file: 'en.json' }
    ],
    defaultLocale: 'zh',
    lazy: true,
    langDir: 'locales/'
  }
})
```

## 🚀 SEO 优化功能

### 1. 自动 SEO Meta 标签
```vue
<script setup>
useSeoMeta({
  title: '页面标题',
  description: '页面描述',
  ogTitle: 'Open Graph 标题',
  ogDescription: 'Open Graph 描述',
  ogImage: '/images/og-image.jpg',
  twitterCard: 'summary_large_image'
})
</script>
```

### 2. 结构化数据
```vue
<script setup>
useJsonld({
  '@context': 'https://schema.org',
  '@type': 'Product',
  name: '产品名称',
  description: '产品描述',
  image: '产品图片'
})
</script>
```

### 3. 自动生成 Sitemap
```javascript
// server/api/sitemap.xml.ts
export default defineEventHandler(async (event) => {
  // 动态生成 sitemap
  const pages = await getPages()
  return generateSitemap(pages)
})
```

### 4. Robots.txt
```javascript
// server/api/robots.txt.ts
export default defineEventHandler(() => {
  return `User-agent: *
Allow: /
Sitemap: https://www.chinadaji.com/sitemap.xml`
})
```

## 📱 性能优化

### 1. 图片优化
```vue
<template>
  <!-- 自动优化图片 -->
  <NuxtImg
    src="/images/product.jpg"
    alt="产品图片"
    width="300"
    height="200"
    loading="lazy"
  />
</template>
```

### 2. 代码分割
```vue
<script setup>
// 动态导入组件
const LazyComponent = defineAsyncComponent(() => import('~/components/Heavy.vue'))
</script>
```

### 3. 预渲染配置
```javascript
// nuxt.config.ts
export default defineNuxtConfig({
  nitro: {
    prerender: {
      routes: ['/', '/mall', '/market', '/merchants']
    }
  }
})
```

## 🔧 环境配置

### 1. 环境变量
```bash
# .env
VUE_APP_API_URL=https://api.chinadaji.com
VUE_APP_NATIONAL_TYPE=cn
VUE_APP_BASE_TYPE=new
```

### 2. 运行时配置
```javascript
// nuxt.config.ts
export default defineNuxtConfig({
  runtimeConfig: {
    public: {
      apiUrl: process.env.VUE_APP_API_URL,
      nationalType: process.env.VUE_APP_NATIONAL_TYPE
    }
  }
})
```

## 📦 部署配置

### 1. 构建命令
```json
{
  "scripts": {
    "build": "nuxt build",
    "preview": "nuxt preview",
    "generate": "nuxt generate"
  }
}
```

### 2. 服务器配置
```javascript
// 服务端渲染
npm run build
npm run preview

// 静态生成
npm run generate
```

## ⚠️ 注意事项

1. **客户端专用代码**：使用 `<ClientOnly>` 包装
2. **生命周期变化**：`mounted` → `onMounted`
3. **路由守卫**：使用 middleware 替代
4. **全局组件**：放在 `components/` 目录自动注册
5. **插件系统**：使用 `plugins/` 目录

## 🔍 调试工具

1. **Nuxt DevTools**：自动启用开发工具
2. **Vue DevTools**：继续支持 Vue 调试
3. **网络面板**：检查 SSR 渲染结果

## 📚 参考资源

- [Nuxt.js 官方文档](https://nuxt.com)
- [Vue 3 迁移指南](https://v3-migration.vuejs.org)
- [SEO 最佳实践](https://developers.google.com/search/docs)
